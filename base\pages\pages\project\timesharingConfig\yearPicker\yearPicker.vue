<template>
  <div>
    <div class="box-tables cleardix">
      <div class="yearText fl">{{ yearText }}{{ !en ? "年" : "" }}</div>
      <div class="fr btnBox">
        <!-- 向前查询按钮 -->
        <CetButton
          class="fl mrJ1"
          v-bind="CetButton_prv"
          v-on="CetButton_prv.event"
        ></CetButton>
        <!-- 向后查询按钮 -->
        <CetButton
          class="fl"
          v-bind="CetButton_next"
          v-on="CetButton_next.event"
        ></CetButton>
      </div>
      <div :class="['radio', en ? 'en' : '']">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="sign">
              <span class="drop" :style="{ background: signColor }"></span>
              {{ $T("已关联") }}
            </div>
          </el-col>
          <el-col :span="12">
            <div class="without sign">
              <span class="drop"></span>
              {{ $T("未关联") }}
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div
      :id="id_in"
      :class="{
        yearPickerDisable: disable_in,
        yearPickerDisable: true,
        fullYearPicker: true,
        dark: currentTheme === 'dark'
      }"
    ></div>
  </div>
</template>
<script>
import "./css/font-awesome.min.css";
import "./css/calendar.css";
import "./css/timedropper.css";

import "./js/timedropper.js";
import calendar from "./js/calendar.js";
export default {
  name: "yearPicker",
  components: {},
  props: {
    id_in: {
      type: String
    },
    color_in: {
      type: String,
      default: "color1"
    },
    disable_in: {
      type: Boolean
    },
    dateChangeCheck: Boolean,
    saveFn: Function,
    timeSchemeColors: Array
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    yearText() {
      return this.$moment(this.CetDatePicker_1.val).format("YYYY");
    },
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    },
    currentTheme() {
      return localStorage.getItem("omega_theme");
    }
  },

  data() {
    if (this.disable_in) {
      calendar.disable_in(true);
    } else {
      calendar.disable_in(false);
    }
    return {
      signColor: "",
      CetDatePicker_1: {
        disable_in: false,
        val: this.$moment().add(0, "year").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "year",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          clearable: false,
          size: "small",
          style: {
            display: "inline-block"
          },
          placeholder: $T("选择日期")
        }
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        type: "primary",
        plain: true,
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      // 设置组件唯一识别字段组件
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: "",
        type: "primary",
        plain: true,
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      clickFlag: false
    };
  },
  watch: {
    color_in: {
      handler: function (val) {
        calendar.actionColor(val);
        this.setSignColor(val);
      },
      immediate: true
    },
    disable_in: function (val) {
      calendar.disable_in(val);
    },
    "CetDatePicker_1.val": {
      handler: function (val) {
        calendar.setActionYear(this.$moment(val).year());
        this.$emit("year_out", val);
      },
      deep: true
    }
  },

  methods: {
    setColor(val) {
      this.setSignColor(val);
      calendar.actionColor(val);
    },
    setSignColor(val) {
      const index = val.split("color")[1] || 1;
      this.signColor = this.timeSchemeColors[(index - 1) % 12];
    },
    getAll() {
      return calendar.getAll();
    },
    disable(val) {
      calendar.disable_in(val);
    },
    drap_select(startDate, endDate, color, deleteAll) {
      this.clickFlag = false;
      calendar.setAim_div(this.id_in);
      calendar.drap_select(startDate, endDate, color, false, deleteAll);
    },
    CetButton_prv_statusTrigger_out(val) {
      if (this.dateChangeCheck && this.clickFlag) {
        this.$confirm($T("年历关联已修改，是否保存?"), $T("提示"), {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        }).then(async () => {
          // 调用保存接口
          if (this.saveFn) {
            await this.saveFn();
          }
          this.clickFlag = false;
          let date = this.$moment(this.CetDatePicker_1.val);
          this.CetDatePicker_1.val = date.subtract(1, "year").valueOf();
        });
      } else {
        let date = this.$moment(this.CetDatePicker_1.val);
        this.CetDatePicker_1.val = date.subtract(1, "year").valueOf();
      }
    },
    CetButton_next_statusTrigger_out(val) {
      if (this.dateChangeCheck && this.clickFlag) {
        this.$confirm($T("年历关联已修改，是否保存?"), $T("提示"), {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        }).then(async () => {
          // 调用保存接口
          if (this.saveFn) {
            await this.saveFn();
          }
          // this.$nextTick();
          this.clickFlag = false;
          let date = this.$moment(this.CetDatePicker_1.val);
          this.CetDatePicker_1.val = date.add(1, "year").valueOf();
        });
      } else {
        let date = this.$moment(this.CetDatePicker_1.val);
        this.CetDatePicker_1.val = date.add(1, "year").valueOf();
      }
    },
    setYear(val) {
      this.CetDatePicker_1.val = this.$moment(val).valueOf();
    }
  },
  created: function () {},
  mounted: function () {
    if (window.localStorage.getItem("omega_language") === "en") {
      calendar.loading_calendar(this.id_in, "en");
    } else {
      calendar.loading_calendar(this.id_in, "cn");
    }
    this.clickFlag = false;
    calendar.$on(this.id_in, {
      callback: () => {
        // 点击了日关联
        this.clickFlag = true;
      },
      type: "click"
    });
    this.$emit("year_out", this.CetDatePicker_1.val);
    // 注册提示框架
    calendar.$on(`message`, {
      callback: val => {
        this.$message({
          type: "warning",
          message: val
        });
      },
      type: "message"
    });
    // 注册提示框架
    calendar.$on(`selectHandle`, {
      callback: val => {
        this.$emit("selectHandle");
      },
      type: "change"
    });
    // $("#timesharingconfig-yearPicker").fullYearPicker({
    //   disabledDay: "",
    //   value: [
    //     /* '2016-6-25', '2016-8-26'  */
    //   ],
    //   cellClick: function(dateStr, isDisabled) {
    //     /* console.log("单击日期:"+dateStr); */
    //     /* arguments[0] */
    //     console.log(dateStr, isDisabled)
    //   }
    // });
  },
  activated() {
    this.clickFlag = false;
  }
};
</script>
<style lang="scss" scoped>
.box-tables {
  height: 40px;
  line-height: 40px;
  position: relative;
  .radio {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 150px;
    &.en {
      width: 250px;
    }
    .sign {
      .drop {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        position: relative;
        top: 2px;
        left: -5px;
        border: 1px solid;
        @include border_color(B1);
      }
    }
    .success::before {
      background: #3e77fc;
    }
    .without.drop {
      background: #fff;
    }
  }
  .yearText {
    @include font_size(H);
    font-weight: 600;
  }
  .btnBox {
    .flleftw {
      margin-right: 8px;
    }
  }
}
.yearPickerDisable {
  margin: 0 -15px;
  :deep(> .picker) {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }
  :deep(.month-container) {
    padding: 0 15px;
    box-sizing: border-box;
  }
}
</style>
<style lang="scss">
.yearPickerDisable.fullYearPicker .picker table .able_day:hover {
  @include background_color(BG2);
}
// 要与index.vue中的timeSchemeColors对应起来，当前只给了12中配色
.yearPickerDisable.fullYearPicker table td {
  $j: 0;
  @while $j <= 20 {
    &.color#{$j*12 + 1} {
      background: #70e09e !important;
    }
    &.color#{$j*12 + 2} {
      background: #4ca6ff !important;
    }
    &.color#{$j*12 + 3} {
      background: #ffd12f !important;
    }
    &.color#{$j*12 + 4} {
      background: #7dd9ff !important;
    }
    &.color#{$j*12 + 5} {
      background: #3166ef !important;
    }
    &.color#{$j*12 + 6} {
      background: #ff9d09 !important;
    }
    &.color#{$j*12 + 7} {
      background: #b9e177 !important;
    }
    &.color#{$j*12 + 8} {
      background: #6fbe0b !important;
    }
    &.color#{$j*12 + 9} {
      background: #94c8fb !important;
    }
    &.color#{$j*12 + 10} {
      background: #ffe69b !important;
    }
    &.color#{$j*12 + 11} {
      background: #f184c4 !important;
    }
    &.color#{$j*12 + 12} {
      background: #b1f6ff !important;
    }
    $j: $j + 1;
  }
}
.yearPickerDisable.dark.fullYearPicker table td {
  $j: 0;
  @while $j <= 20 {
    &.color#{$j*12 + 1} {
      background: #0c82f8 !important;
    }
    &.color#{$j*12 + 2} {
      background: #4afda7 !important;
    }
    &.color#{$j*12 + 3} {
      background: #ffc531 !important;
    }
    &.color#{$j*12 + 4} {
      background: #41e0e4 !important;
    }
    &.color#{$j*12 + 5} {
      background: #193dff !important;
    }
    &.color#{$j*12 + 6} {
      background: #ff8f27 !important;
    }
    &.color#{$j*12 + 7} {
      background: #a4d738 !important;
    }
    &.color#{$j*12 + 8} {
      background: #6d990c !important;
    }
    &.color#{$j*12 + 9} {
      background: #69b4ff !important;
    }
    &.color#{$j*12 + 10} {
      background: #ffdd85 !important;
    }
    &.color#{$j*12 + 11} {
      background: #df7eb7 !important;
    }
    &.color#{$j*12 + 12} {
      background: #048c8e !important;
    }
    $j: $j + 1;
  }
}
.yearPickerDisable.fullYearPicker table td {
  cursor: auto;
}
.fullYearPicker table td,
.fullYearPicker table th.head {
  @include background_color(BG1);
}
.fullYearPicker .picker table {
  @include border_color(B1);
}
</style>
