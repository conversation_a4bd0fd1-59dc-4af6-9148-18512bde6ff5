<template>
  <div>
    <div class="card-head-title">
      <span>负荷预测</span>
    </div>
    <div class="custom-tabs-style" style="height: calc(100% - 30px)">
      <el-tabs
        tab-position="top"
        type="border-card"
        style="height: 100%; width: 100%"
      >
        <el-tab-pane label="需量预测" style="height: 100%">
          <div class="card-body">
            <ul class="eventStatus">
              <li>
                <div style="width: 40%; float: left; text-align: center">
                  <span class="icon-label mt-10">日</span>
                </div>
                <div
                  style="
                    width: 60%;
                    float: left;
                    text-align: center;
                    color: #0350da;
                  "
                >
                  <em>{{ this.eventNumList[0] }}</em>
                  <span>今日最大需量（kWh）</span>
                </div>
              </li>
              <li>
                <div style="width: 40%; float: left; text-align: center">
                  <span class="icon-label mt-10">月</span>
                </div>
                <div style="width: 60%; float: left; text-align: center">
                  <em class="processing">{{ this.eventNumList[1] }}</em>
                  <span class="processing">本月最大需量（kWh）</span>
                </div>
              </li>
              <li>
                <div style="width: 40%; float: left; text-align: center">
                  <span class="icon-label mt-10">季</span>
                </div>
                <div style="width: 60%; float: left; text-align: center">
                  <em class="processed">{{ this.eventNumList[2] }}</em>
                  <span class="processed">本季度最大需量（kWh）</span>
                </div>
              </li>
            </ul>
          </div>
        </el-tab-pane>
        <el-tab-pane label="电量预测" style="height: 100%">
          <div class="card-body">
            <ul class="eventStatus">
              <li>
                <div style="width: 40%; float: left; text-align: center">
                  <span class="icon-label mt-10">本月</span>
                </div>
                <div
                  style="
                    width: 60%;
                    float: left;
                    text-align: center;
                    color: #0350da;
                  "
                >
                  <em>{{ this.eventNumList[0] }}</em>
                  <span>本月用电量（kWh）</span>
                </div>
              </li>
              <li>
                <div style="width: 40%; float: left; text-align: center">
                  <span class="icon-label mt-10">下月</span>
                </div>
                <div style="width: 60%; float: left; text-align: center">
                  <em class="processing">{{ this.eventNumList[1] }}</em>
                  <span class="processing">下月用电量（kWh）</span>
                </div>
              </li>
              <li>
                <div style="width: 40%; float: left; text-align: center">
                  <span class="icon-label mt-10">本年</span>
                </div>
                <div style="width: 60%; float: left; text-align: center">
                  <em class="processed">{{ this.eventNumList[2] }}</em>
                  <span class="processed">本年用电量（kWh）</span>
                </div>
              </li>
            </ul>
          </div>
        </el-tab-pane>
        <el-tab-pane label="发电预测" style="height: 100%">
          <div class="card-body">
            <CetChart
              :inputData_in="CetChart_1.inputData_in"
              v-bind="CetChart_1.config"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
export default {
  name: "ProjectTab",
  props: {
    queryTrigger_in: {
      type: Number
    }
  },
  components: {},

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data() {
    return {
      eventNumList: [566.54, 5666.44, 4544.54],
      CetChart_1: {
        inputData_in: null,
        config: {
          options: {
            color: ["#3398DB"],
            tooltip: {
              trigger: "axis",
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
              }
            },
            grid: {
              top: "10px",
              left: "0%",
              right: "0%",
              bottom: "3%",
              containLabel: true
            },
            xAxis: [
              {
                type: "category",
                data: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"],
                axisTick: {
                  alignWithLabel: true
                },
                axisLabel: {
                  color: "#fff"
                }
              }
            ],
            yAxis: [
              {
                type: "value",
                axisLabel: {
                  color: "#fff"
                }
              }
            ],
            series: [
              {
                name: "",
                type: "bar",
                color: "#2654CC",
                data: [10, 52, 200, 334, 390, 330, 220]
              }
            ]
          }
        }
      }
    };
  },
  watch: {
    //查询条件触发
    queryTrigger_in() {}
  },

  methods: {},

  created: function () {},
  mounted: function () {
    this.time = new Date().getHours() + ":" + new Date().getSeconds();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.card-header {
  height: 34px;
  padding: 8px 0 0 10px;
  background: #fff;
  clear: both;
  text-align: center;
}

.card-title {
  display: inline-block;
  height: 34px;
  font-weight: bold;
  font-size: 13px;
  // color: #7a7a7a;
}
.card-head-title {
  height: 30px;
  line-height: 30px;
  text-align: center;
  span {
    font-size: 18px;
    color: rgba(128, 255, 255, 1);
  }
}
.card-body {
  height: 100%;
  overflow-y: auto;
}
.card-body .icon-label {
  display: inline-block;
  width: 32px;
  height: 32px;
  line-height: 32px;
  border-radius: 32px;
  background: #0350da;
  color: #fff;
}
.card-body .eventStatus {
  overflow: hidden;
  width: 100%;
}
.card-body ul {
  margin: 0px;
  padding: 0px;
  list-style: none;
}
.card-body ul li {
  list-style: none;
}
.card-body .eventStatus li {
  padding: 0px;
  height: 60px;
}
.card-body .eventStatus li em {
  display: block;
  font-size: 30px;
  font-style: normal;
}

.card-body .eventStatus li span {
  font-size: 13px;
  // color: #7a7a7a;
}

.card-body .eventStatus .unprocessed {
  color: #ff3c1c;
}

.card-body .eventStatus .processing {
  color: #ffba00;
}

.card-body .eventStatus .processed {
  color: #70b000;
}
</style>

<style lang="scss">
.custom-tabs-style {
  .el-tabs--border-card > .el-tabs__header .el-tabs__item {
    color: #fff !important;
  }
  .el-tabs--border-card {
    background: rgba(15, 26, 71, 0.5) !important;
    border: 1px solid #0358c2;
  }
  .el-tabs--border-card > .el-tabs__header {
    background-color: rgba(15, 26, 71, 0.5) !important;
  }
  .el-tabs--border-card > .el-tabs__header {
    border-bottom: 1px solid #1a6fd9;
  }
  .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
    background-color: rgba(15, 26, 71, 0.5) !important;
    border-right-color: #0358c2;
    border-left-color: #0358c2;
    color: #fff !important;
  }
  .el-tabs--border-card > .el-tabs__header .el-tabs__item:not(.is-disabled).is-active:hover{
    color: #fff !important;
  }
  .el-tabs__content {
    padding: 1px;
  }
  .el-tabs .el-tabs__content {
    background-color: rgba(15, 26, 71, 0.5) !important;
    height: calc(100% - 40px) !important;
  }
  .el-tabs__item {
    color: #fff !important;
  }
  div {
    color: #fff;
  }
  span {
    color: #fff;
  }
  .card-body::-webkit-scrollbar {
    //滚动条整体样式
    width: 8px; //高宽分别对应横竖滚动条的尺寸
    height: 8px;
    scrollbar-arrow-color: #000d55 !important;
  }
  .card-body::-webkit-scrollbar-thumb {
    //滚动条里面小方块
    border-radius: 8px;
    // -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.9);
    // background: rgba(0,0,0,0.9);
    background: rgba(0, 13, 85, 1);
    scrollbar-arrow-color: #000d55;
  }
  .card-body::-webkit-scrollbar-track {
    //滚动条里面轨道
    // -webkit-box-shadow: inset 0 0 5px rgba(0,13,85,1);
    border-radius: 0;
    background: rgba(0, 0, 0, 0.1);
  }
}
</style>
