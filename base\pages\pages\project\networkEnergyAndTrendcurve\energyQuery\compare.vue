<template>
  <div class="flex-column fullheight">
    <div class="text-right">
      <el-radio-group v-model="chartType" @change="chartTypeChange">
        <el-radio-button :label="1">
          <omega-icon class="cetIcon" symbolId="bar" />
        </el-radio-button>
        <el-radio-button :label="2">
          <omega-icon class="cetIcon" symbolId="line" />
        </el-radio-button>
      </el-radio-group>
    </div>
    <CetChart
      class="flex-auto"
      :inputData_in="CetChart_3.inputData_in"
      v-bind="CetChart_3.config"
    />
  </div>
</template>

<script>
import common from "eem-utils/common";
import { httping } from "@omega/http";
export default {
  props: {
    queryBody_in: {
      type: Object
    },
    energy_in: {
      type: Object
    },
    CustomDatePicker_1: {
      type: Object
    },
    clickNode_in: {
      type: Object
    },
    checkedNodes_in: {
      type: Array
    },
    queryTrigger_in: {
      type: Number
    }
  },
  data() {
    return {
      chartType: 1,
      CetChart_3: {
        inputData_in: null,
        config: {
          options: {
            legend: {
              data: []
            },
            tooltip: {},
            dataset: {
              source: []
            },
            xAxis: { type: "category" },
            yAxis: {},
            series: []
          }
        }
      }
    };
  },
  watch: {
    queryTrigger_in() {
      this.queryData();
    }
  },
  methods: {
    queryData() {
      let _this = this;
      if (!this.queryBody_in) {
        _this.filChartData3([]);
        return;
      }
      let queryBody = this.queryBody_in;
      this.CetChart_3.config.options = {};
      var queryOption = {
        url: `/eem-service/v1/pipeline/energyConsumption/node/compare`,
        method: "POST",
        data: queryBody
      };

      httping(queryOption).then(function (response) {
        if (response.code === 0) {
          //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
          var data = _this._.get(response, ["data"], []);

          _this.filChartData3(data);
        }
      });
    },
    filChartData3(pdata) {
      this.CetChart_3.config.options = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          },
          formatter: function (val) {
            let list = val || [];
            let formatterStr = "";
            for (let i = 0, len = list.length; i < len; i++) {
              if (i === 0) {
                formatterStr += `${val[i].name}`;
              }
              formatterStr += `<br/>${val[i].marker}${val[i].seriesName} : ${
                val[i].value || "--"
              }(${val[i].data.unit || "--"})`;
            }
            return formatterStr;
          }
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true
        },
        legend: {
          data: []
        },
        // dataset: {
        //   source: []
        // },
        xAxis: {
          type: "category",
          data: []
        },
        yAxis: {
          type: "value",
          nameTextStyle: {
            align: "left"
          }
        },
        series: []
      };
      var _this = this,
        data = pdata || [],
        id = 0,
        modelLabel = "",
        legend = [],
        series = [];
      let unit = (data.length > 0 && data[0].symbol) || "--";

      if (this.clickNode_in) {
        id = this.clickNode_in.id;
        modelLabel = this.clickNode_in.modelLabel;
      } else {
        this.$message.warning("请选择节点");
        return;
      }
      var list = this.checkedNodes_in || [];
      var isHasNode = false;
      list.forEach(item => {
        if (
          item.id === this.clickNode_in.id &&
          item.modelLabel === this.clickNode_in.modelLabel
        ) {
          isHasNode = true;
        }
      });
      if (!isHasNode && list.length > 0) {
        id = list[0].id;
        modelLabel = list[0].modelLabel;
      }

      var source = [];
      data.forEach(item => {
        var serie = {};
        if (item.id === id && item.modelLabel === modelLabel) {
          var itemData1 = item.data || [];
          let customIndex = this.getMarginType(
            itemData1[0].time,
            itemData1[itemData1.length - 1].time
          );
          itemData1.forEach(item1 => {
            var obj = {};
            obj.time = item1.time;
            obj.product = this.getAxixs(item1.time, customIndex);
            obj[`yAxis${item.modelLabel}${item.id}`] =
              common.formatNumberWithPrecision(item1.value, 2);
            source.push(obj);
          });
          serie = {
            name: item.name,
            type: "bar",
            smooth: true,
            encode: { x: "product", y: `yAxis${item.modelLabel}${item.id}` }
          };
        } else {
          serie = {
            name: item.name,
            type: "line",
            smooth: true,
            encode: { x: "product", y: `yAxis${item.modelLabel}${item.id}` }
          };
        }
        legend.push(item.name);
        series.push(serie);
      });
      data.forEach(item => {
        if (item.id != id || item.modelLabel !== modelLabel) {
          var itemData1 = item.data || [];
          itemData1.forEach(item1 => {
            source.forEach(item2 => {
              if (item2.time === item1.time) {
                item2[`yAxis${item.modelLabel}${item.id}`] =
                  common.formatNumberWithPrecision(item1.value, 2);
              }
            });
          });
        }
      });
      // var dataset = {
      //   source: source
      // };

      var xAxisData = [];
      var cloneLegend = [],
        cloneSeries = [];
      source.forEach(item => {
        xAxisData.push(item.product);
      });
      data.forEach(item => {
        var serie = {};
        var yAxisData = [];
        source.forEach(item11 => {
          // yAxisData.push(item11[`yAxis${item.modelLabel}${item.id}`]);
          yAxisData.push({
            name: item11.product,
            time: item11.time,
            unit: unit,
            value: item11[`yAxis${item.modelLabel}${item.id}`]
          });
        });
        if (item.id === id && item.modelLabel === modelLabel) {
          serie = {
            name: item.name,
            type: "bar",
            smooth: true,
            data: yAxisData
            // encode: { x: "product", y: `yAxis${item.modelLabel}${item.id}` }
          };
        } else {
          serie = {
            name: item.name,
            type: "line",
            smooth: true,
            data: yAxisData
            // encode: { x: "product", y: `yAxis${item.modelLabel}${item.id}` }
          };
        }
        cloneLegend.push(item.name);
        cloneSeries.push(serie);
      });
      if (cloneSeries.length > 0) {
        cloneSeries[0].data.forEach(item => {
          if (Number(item.value) < 0) {
            item.value = 0;
          }
        });
      }
      var ElOption_1Text;
      ElOption_1Text = _this.energy_in && _this.energy_in.text;
      _this.CetChart_3.config.options.yAxis.name = ElOption_1Text
        ? `${ElOption_1Text}（${unit})`
        : "";

      this.CetChart_3.config.options.xAxis.data = xAxisData;
      this.CetChart_3.config.options.series = cloneSeries;
      this.CetChart_3.config.options.legend.data = cloneLegend;
      this.chartTypeChange();
    },
    // 图表类型切换
    chartTypeChange() {
      this.CetChart_3.config.options.series.forEach(item => {
        item.type = this.chartType === 1 ? "bar" : "line";
      });
      this.CetChart_3.config.options.dataZoom =
        this.chartType === 1
          ? [
              {
                type: "inside",
                startValue: 0,
                endValue: 14,
                zoomOnMouseWheel: false
              },
              {
                startValue: 0,
                endValue: 14,
                zoomLock: true,
                brushSelect: false
              }
            ]
          : null;
      this.CetChart_3.config.options = this._.cloneDeep(
        this.CetChart_3.config.options
      );
    },
    getMarginType(iStartTime1, iEndTime) {
      let iMarginDay = this.$moment(iEndTime).subtract(2, "day");
      let iMarginMonth = this.$moment(iEndTime).subtract(3, "month");
      let iMarginYear = this.$moment(iEndTime).subtract(3, "year");
      if (iStartTime1 > iMarginDay) {
        return 1;
      } else if (iStartTime1 > iMarginMonth) {
        return 2;
      } else if (iStartTime1 > iMarginYear) {
        return 3;
      } else {
        return 4;
      }
    },
    //过滤获取图表x轴对应值
    getAxixs(pDate, customIndex) {
      let oDate = this.$moment(pDate);
      let type = this.CustomDatePicker_1.queryTime.cycle;
      if (type === 12) {
        if (oDate.format("HH:mm") == "00:00") {
          return oDate.format("DD");
        }
        return oDate.format("HH:mm");
      } else if (type === 14) {
        return oDate.format("DD");
      } else if (type === 17) {
        return oDate.format("MM");
      } else if (type === 20) {
        if (customIndex === 1) {
          if (oDate.format("HH:mm") == "00:00") {
            return oDate.format("DD");
          }
          return oDate.format("HH:mm");
        } else if (customIndex === 2) {
          return oDate.format("DD");
        } else if (customIndex === 3) {
          return oDate.format("MM");
        } else if (customIndex === 4) {
          return oDate.format("YYYY");
        }
      }
      return oDate.format("YYYY-MM-DD");
    }
  }
};
</script>

<style></style>
