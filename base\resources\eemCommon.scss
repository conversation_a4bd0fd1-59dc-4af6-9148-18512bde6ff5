body,
* {
  @include font_size(Aa);
}

//页面通用样式设置
.bg {
  @include background_color(BG);
}
.bg1 {
  @include background_color(BG1);
}
.bg2 {
  @include background_color(BG2);
}
.bgZS {
  @include background_color(ZS);
}
.fsH {
  @include font_size(H);
}
.fsH1 {
  @include font_size(H1);
}
.fsH2 {
  @include font_size(H2);
}
.fsH3 {
  @include font_size(H3);
}
.fcZS {
  @include font_color(ZS);
}
.fcT1 {
  @include font_color(T1);
}
.fcT2 {
  @include font_color(T2);
}
.fcT3 {
  @include font_color(T3);
}
.fcT4 {
  @include font_color(T4);
}
.fcT5 {
  @include font_color(T5);
}
.fcB1 {
  @include font_color(B1);
}
.pJ {
  @include padding(J);
}
.ptJ {
  @include padding_top(J);
}
.prJ {
  @include padding_right(J);
}
.pbJ {
  @include padding_bottom(J);
}
.plJ {
  @include padding_left(J);
}
.pJ1 {
  @include padding(J1);
}
.ptJ1 {
  @include padding_top(J1);
}
.prJ1 {
  @include padding_right(J1);
}
.pbJ1 {
  @include padding_bottom(J1);
}
.plJ1 {
  @include padding_left(J1);
}
.pJ2 {
  @include padding(J2);
}
.ptJ2 {
  @include padding_top(J2);
}
.prJ2 {
  @include padding_right(J2);
}
.pbJ2 {
  @include padding_bottom(J2);
}
.plJ2 {
  @include padding_left(J2);
}
.pJ3 {
  @include padding(J3);
}
.ptJ3 {
  @include padding_top(J3);
}
.prJ3 {
  @include padding_right(J3);
}
.pbJ3 {
  @include padding_bottom(J3);
}
.plJ3 {
  @include padding_left(J3);
}
.pJ4 {
  @include padding(J4);
}
.ptJ4 {
  @include padding_top(J4);
}
.prJ4 {
  @include padding_right(J4);
}
.pbJ4 {
  @include padding_bottom(J4);
}
.plJ4 {
  @include padding_left(J4);
}
.mJ {
  @include margin(J);
}
.mtJ {
  @include margin_top(J);
}
.mrJ {
  @include margin_right(J);
}
.mbJ {
  @include margin_bottom(J);
}
.mlJ {
  @include margin_left(J);
}
.mJ1 {
  @include margin(J1);
}
.mtJ1 {
  @include margin_top(J1);
}
.mrJ1 {
  @include margin_right(J1, !important);
}
.mbJ1 {
  @include margin_bottom(J1);
}
.mlJ1 {
  @include margin_left(J1, !important);
}
.mJ2 {
  @include margin(J2);
}
.mtJ2 {
  @include margin_top(J2);
}
.mrJ2 {
  @include margin_right(J2);
}
.mbJ2 {
  @include margin_bottom(J2);
}
.mlJ2 {
  @include margin_left(J2);
}
.mJ3 {
  @include margin(J3);
}
.mtJ3 {
  @include margin_top(J3);
}
.mrJ3 {
  @include margin_right(J3);
}
.mbJ3 {
  @include margin_bottom(J3);
}
.mlJ3 {
  @include margin_left(J3);
}
.mJ4 {
  @include margin(J4);
}
.mtJ4 {
  @include margin_top(J4);
}
.mrJ4 {
  @include margin_right(J4);
}
.mbJ4 {
  @include margin_bottom(J4);
}
.mlJ4 {
  @include margin_left(J4);
}
.pl-pr-J1 {
  padding: 0px;
  @include padding_left(J1);
  @include padding_right(J1);
}
.pl-pr-J2 {
  padding: 0px;
  @include padding_left(J2);
  @include padding_right(J2);
}
.ml-mr-J1 {
  padding: 0px;
  @include margin_left(J1);
  @include margin_right(J1);
}
.ml-mr-J2 {
  padding: 0px;
  @include margin_left(J2);
  @include margin_right(J2);
}

.brC {
  @include border_radius(C);
}

.brC1 {
  @include border_radius(C1);
}

.brC2 {
  @include border_radius(C2);
}

.brC3 {
  @include border_radius(C3);
}

$j: 15;
@while $j <= 60 {
  .lh#{$j} {
    line-height: #{$j}px !important;
  }
  $j: $j + 1;
}

$j: 15;
@while $j <= 60 {
  .h#{$j} {
    height: #{$j}px;
  }
  $j: $j + 1;
}

.common-title-H {
  @include font_size(H);
  display: inline-block;
  line-height: 36px;
  font-weight: bold;
}

.common-title-H1 {
  @include font_size(H1);
  display: inline-block;
  line-height: 30px;
  font-weight: bold;
}
.common-title-H2 {
  @include font_size(H2);
  display: inline-block;
  line-height: 26px;
  font-weight: bold;
}
.common-title-H3 {
  @include font_size(H3);
  display: inline-block;
  line-height: 24px;
  font-weight: bold;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-auto {
  flex: 1;
  min-height: 0px;
  min-width: 0px;
}
.minWH {
  min-height: 0px;
  min-width: 0px;
}
.justify-content-start {
  display: flex;
  justify-content: flex-start;
}
.justify-content-end {
  display: flex;
  justify-content: flex-end;
}
.eem-common {
  .eem-aside {
    box-sizing: border-box;
    @include padding(J4 J3);
    @include background_color(BG1);
    @include border_radius(C);
  }
  .eem-header {
    @include padding_top(J2);
    @include padding_bottom(J2);
  }
  .eem-card {
    border: 1px solid;
    @include border_color(B1);
    @include border_radius(C);
  }
  .eem-container {
    box-sizing: border-box;
    @include padding(J3 J4);
    @include background_color(BG1);
    @include border_radius(C);
  }
  .eem-table-custom > .el-footer {
    text-align: right;
  }
  .eem-table-custom .el-button {
    border: none !important;
    background-color: transparent;
  }
  .eem-tabs-custom {
    height: 32px;
    @include background_color(BG1);
    @include padding_left(J2);
    @include padding_right(J2);
    @include border_radius(C);
    .el-tabs__header {
      margin: 0px;
    }
    .el-tabs__item {
      height: 32px;
      line-height: 32px;
    }
    .el-tabs__active-bar {
      @include background_color(ZS);
    }
    .el-tabs__item.is-active {
      @include font_color(ZS);
    }
    .el-tabs__nav-wrap::after {
      @include background_color(BG1);
    }
    .el-tabs__nav-next,
    .el-tabs__nav-prev {
      line-height: 32px;
    }
  }
  .eem-group-list {
    overflow: auto;
    .group-item {
      @include padding_left(J2);
      @include padding_right(J2);
      border: 1px solid;
      @include border_color(B1);
      @include border_radius(C);
      @include margin_bottom(J1);
      line-height: 30px;
      height: 30px;
      cursor: pointer;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .group-item:hover {
      @include font_color(ZS);
      @include background_color(BG2);
    }
    .active {
      @include font_color(ZS);
      @include background_color(BG4);
    }
  }
  .eem-footer-btn {
    position: relative;
    top: -48px;
  }
  .eem-no-line > .line-bottom {
    border-width: 0px;
  }
}
.eem-cont {
  box-sizing: border-box;
  @include padding(J3 J4);
  @include background_color(BG1);
  @include border_radius(C);
}
.eem-cont-c1 {
  box-sizing: border-box;
  @include padding(J3 J4);
  @include background_color(BG1);
  @include border_radius(C1);
}
.eem-tabs {
  height: 100%;
  padding: 0px;
  & > .el-tabs__content {
    height: calc(100% - 55px);
    padding: 0px;
    overflow: auto;
  }

  .el-input__inner {
    width: 240px;
  }
}

.eem-cont-custom {
  @include padding(J2);
  padding-top: 0px;
}
// 设置表格操作列可选字体样式
.eem-row-handle {
  @include font_color(ZS);
  cursor: pointer;
}
// 设置表格操作列不可选样式
.eem-row-no-handle {
  cursor: not-allowed;
  @include font_color(T6);
}
// 设置表格操作列删除选样式
.eem-row-delete {
  cursor: pointer;
  @include font_color(Sta3);
}
//设置最小宽度
.eem-min-width {
  min-width: 1280px;
}

.eem-min-width-mini {
  min-width: 980px;
}

// 设置时间控件上下按钮为正方形
.eem-date-range-button {
  &.el-button,
  & .el-button {
    width: 32px;
    height: 32px;
    padding: 9px;
  }
}

// 自定义更多的icon效果
.more-custom {
  zoom: 1;
  &:after {
    content: " ";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
  &:hover > i {
    @include background_color(ZS);
  }
  & > i {
    display: inline-block;
    height: 0;
    width: 0;
    padding: 2px;
    border-radius: 50%;
    @include background_color(T3);
  }
}

/* 适配云平台中w100是百分百 */
.w100 {
  width: 100% !important;
}
.menu_group {
  text-align: center;
  padding: 5px 0;
}

.menu_group .el-radio-button__inner {
  padding: 5px 16px;
  line-height: 20px;
  font-size: 14px;
}
// 节点树横向滚动
.el-tree {
  & > .el-tree-node {
    // display: inline-block;
    min-width: 100%;
  }
}
//导航栏与右侧滚动条间距
.el-menu--popup-right-start {
  margin-right: 0px;
}
// 菜单在收起的状态因子菜单过多出现滚动条
.el-menu--vertical.frame-navmenu-submenu.default {
  max-height: 100%;
  // overflow: auto;
  .el-menu--vertical.frame-navmenu-submenu.default {
    min-height: 400px;
    max-height: 100%;
    overflow: auto;
  }
}
.el-date-editor--daterange.el-input,
.el-date-editor--daterange.el-input__inner,
.el-date-editor--timerange.el-input,
.el-date-editor--timerange.el-input__inner,
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
}
// 项目公共样式
// 输入框，下拉框，时间选择框前面label样式
.basic-box {
  // width: 100%;
  display: flex;
  // margin-top: 10px;
  .basic-box-label {
    display: block;
    text-align: center;
    line-height: 32px;
    height: 32px;
    box-sizing: border-box;
    border: 1px solid;
    border-right: 0px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    min-width: 80px;
    @include border_color("B1");
    @include background_color("BG1");
    @include font_size("Aa");
    @include padding_left(J1);
    @include padding_right(J1);
  }
  .el-input,
  .device-Input,
  .device-Select,
  .device-DatePicker {
    flex: 1;
    .el-input__inner {
      border-top-left-radius: 0px !important;
      border-bottom-left-radius: 0px !important;
    }
  }
  .el-date-editor--time-select .el-input__inner {
    border-top-left-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
  }
  .device-Button .el-button {
    padding: 12px;
    i {
      margin-right: -5px;
    }
  }
}
// 输入框靠左
.el-input-number .el-input__inner {
  text-align: left !important;
}
.paddingL-R-16 {
  padding: 0;
  @include padding_left(J2);
  @include padding_right(J2);
}
.paddingAll-16 {
  @include padding(J2);
}
// 公共字体样式
.font28 {
  @include font_size("H");
}
.font24 {
  @include font_size("J3");
}

.font22 {
  @include font_size("H1");
}

.font18 {
  @include font_size("H2");
}

.font16 {
  @include font_size("H3");
}

.font14 {
  @include font_size("Aa");
}

.font12 {
  @include font_size("Ab");
}
.font32 {
  @include font_size("J4");
}
.font40 {
  @include font_size("I3");
}
// 公共间距值
.marginTopJ1 {
  @include margin_top(J1);
}
.marginRightJ1 {
  @include margin_right(J1);
}
.marginBottomJ1 {
  @include margin_bottom(J1);
}
.marginLeftJ1 {
  @include margin_left(J1);
}
.marginTopJ2 {
  @include margin_top(J2);
}
.marginRightJ2 {
  @include margin_right(J2);
}
.marginBottomJ2 {
  @include margin_bottom(J2);
}
.marginLeftJ2 {
  @include margin_left(J2);
}
// form里面input字体大小为13的话，整个内容区域会变成33px导致布局有问题
.el-input--small {
  font-size: 14px;
}
//cet-table组件，设置表格和分支之间间距
.table-container .el-footer {
  height: 40px !important;
  padding: 0px;
  @include padding_top(J1, !important);
}
// 去掉tabs组件的轨道
.el-tabs__nav-wrap::after {
  display: none;
}
//处理el-button高度变为34px的问题；
.el-button--small > span {
  font-size: 12px;
}
.el-button--small > i {
  font-size: 12px;
}

//能耗报警配置和需量报警配置；限定高度32px
.general-table th {
  padding: 0px !important;
}
.general-table td {
  padding: 0px !important;
}
.general-table .cell {
  line-height: 31px !important;
}
// form内上下布局必填标志居右展示
.el-form.el-form--label-top {
  .el-form-item--small .el-form-item__label {
    position: relative;
    line-height: 20px;
    @include padding_bottom(J, !important);
  }
  .el-form-item.is-required:not(.is-no-asterisk)
    .el-form-item__label-wrap
    > .el-form-item__label:before,
  .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
    position: absolute;
    right: -10px;
    margin-right: 0;
  }
}
.el-dialog {
  &.is-fullscreen {
    width: 100% !important;
  }
  // 修改弹框圆角
  @include border_radius(C2);
  // 修改头部间距
  .el-dialog__header {
    @include padding(J3);
    // 标题加粗
    .el-dialog__title {
      font-weight: bold;
    }
  }
  // 修改footer间距
  .el-dialog__footer {
    padding: 12px 16px 14px 16px;
    // 修改按钮间距
    .device-Button {
      margin-left: 4px;
    }
  }
  .el-dialog__body {
    @include background_color(BG);
    @include padding(J1);
    box-sizing: border-box;
  }
}
// 表单内单位
.form-item-unit {
  line-height: 30px;
  position: absolute;
  @include background_color(BG);
  @include padding(0 J1);
  right: 1px;
  text-align: center;
  top: 1px;
  border-radius: 0 mh-get(C) mh-get(C) 0;
}
//鼠标手型
.pointer {
  cursor: pointer;
}
// 弹框尺寸
.min > .el-dialog {
  width: 320px !important;
}
.small > .el-dialog {
  width: 600px !important;
}
.medium > .el-dialog {
  width: 960px !important;
}
.max > .el-dialog {
  width: 1440px !important;
}
//设置checkin间距
.eem-checkbox {
  .el-checkbox {
    @include margin_right(J1);
  }
  .el-checkbox__label {
    @include padding_left(J);
  }
  .el-checkbox:last-child {
    margin-right: 0px !important;
  }
}
//设置checkin间距
.eem-radio {
  .el-radio {
    @include margin_right(J1);
  }
  .el-radio__label {
    @include margin_left(J);
    padding-left: 0px;
  }
  .el-radio:last-child {
    margin-right: 0px !important;
  }
}
// 解决table中增加了排序icon导致表头高度超出40
.el-table {
  .caret-wrapper {
    height: 19px;
  }
  .sort-caret.ascending {
    top: 0;
    border-width: 4px;
  }
  .sort-caret.descending {
    bottom: 1px;
    border-width: 4px;
  }
}
.el-dialog__headerbtn {
  .el-dialog__close {
    @include font_color(T2);
  }
  &:focus .el-dialog__close,
  &:hover .el-dialog__close {
    @include font_color(ZS);
  }
}
.el-input .el-input__count {
  @include font_color(T3);
}

/* 事故、报警、一般、预警、其他事件tag状态样式 */
.el-tag--gray {
  color: #bbbbbb;
  border-color: #bbbbbb;
}
.el-tag--dark.el-tag--gray {
  color: #ffffff;
  border-color: #28be44;
  background-color: #28be44;
}
.el-tag--red {
  color: #ff0000;
  // background: #fff1e7;
  border-color: #ff0000;
}
.el-tag--dark.el-tag--red {
  color: #ffffff;
  border-color: #ff0000;
  background-color: #ff0000;
}
.el-tag--orange {
  color: #f98526;
  // background: #ffe9c9;
  border-color: #f98526;
}
.el-tag--dark.el-tag--orange {
  color: #ffffff;
  border-color: rgb(219, 128, 35);
  background-color: rgb(219, 128, 35);
}
.el-tag--yellow {
  color: #ddcc00;
  // background: #fff3c6;
  border-color: #ddcc00;
}
.el-tag--dark.el-tag--yellow {
  color: #ffffff;
  border-color: #ddcc00;
  background-color: #ddcc00;
}
.el-tag--green {
  color: #3cd594;
  // background: #c4ffe6;
  border-color: #3cd594;
}
.el-tag--dark.el-tag--green {
  color: #ffffff;
  border-color: #18beb7;
  background-color: rgb(24, 190, 183);
}
.el-tag--blue {
  color: #0000ff;
  // background: #b8e1ff;
  border-color: #0000ff;
}
.el-tag--dark.el-tag--blue {
  color: #ffffff;
  border-color: #0000ff;
  background-color: #0000ff;
}

//处理cet-gianttree节点树滚动条问题
.eem-aside .gianttree {
  display: flex;
  flex-direction: column;
}
.eem-aside .gianttree .line-bottom .device-search {
  margin-top: 0px !important;
  @include margin_bottom(J3, !important);
}

.eem-aside .gianttree .ztree {
  flex: 1;
  min-height: 0px;
  min-width: 0px;
}
//处理small类型el-button按钮高度为34px问题
.el-button--small {
  font-size: 12px;
}
//处理节点树组件搜索输入框下面一条横线
.tree .line-bottom {
  border: none;
}
.gianttree .line-bottom {
  border: none;
}
