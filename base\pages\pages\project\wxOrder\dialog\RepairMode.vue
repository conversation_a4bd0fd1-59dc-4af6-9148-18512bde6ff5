<template>
  <div>
    <CetDialog v-bind="CetDialog_add" v-on="CetDialog_add.event">
      <!-- <el-container style="padding: 0px 16px 11px 12px">
        <CetForm ref="createPlanForm" :data.sync="CetForm_1.data" v-bind="CetForm_1" v-on="CetForm_1.event">
          <el-row :gutter="20" style="width: 90%; margin-left: 5%">
            <el-col :span="24">
              <el-form-item label="维修方式：" prop="repairtype">
                <template>
                  <el-radio v-model="repairtype" label="2">内协维修</el-radio>
                  <el-radio v-model="repairtype" label="3">委外维修</el-radio>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="预计耗时：" prop="fillformtype">
                <template>
                  <el-radio v-model="fillformtype" label="1">线上</el-radio>
                  <el-radio v-model="fillformtype" label="2">线下</el-radio>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="预计耗时：" prop="timeconsumeplan">
                <ElInputNumber v-model="timeconsumeplan" v-bind="ElInputNumber_1" v-on="ElInputNumber_1.event"></ElInputNumber>
                <span class="el-input__icon eem-timeconsume-icon">h</span>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="inputData_in && inputData_in.sourcetype !== 5">
              <el-form-item label="责任班组：" prop="teamid">
                <ElSelect v-model="teamid" v-bind="ElSelect_team" v-on="ElSelect_team.event">
                  <ElOption
                    v-for="item in ElOption_team.options_in"
                    :key="item[ElOption_team.key]"
                    :label="item[ElOption_team.label]"
                    :value="item[ElOption_team.value]"
                    :disabled="item[ElOption_team.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="运值确认班组：" prop="inspectteamid">
                <ElSelect v-model="inspectteamid" v-bind="ElSelect_team" v-on="ElSelect_team.event">
                  <ElOption
                    v-for="item in ElOption_team.options_in"
                    :key="item[ElOption_team.key]"
                    :label="item[ElOption_team.label]"
                    :value="item[ElOption_team.value]"
                    :disabled="item[ElOption_team.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="意见：" prop="faultdescription">
                <el-input
                  type="textarea"
                  rows="5"
                  resize="none"
                  placeholder="请输入内容"
                  v-model="description"
                  onKeypress="javascript:if(event.keyCode == 32)event.returnValue = false;"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </CetForm>
      </el-container> -->
      <div class="eem-cont-c1">
        <div class="mbJ3">
          <template>
            <el-radio v-model="repairtype" label="2">
              {{ $T("内协维修") }}
            </el-radio>
            <el-radio v-model="repairtype" label="3">
              {{ $T("委外维修") }}
            </el-radio>
          </template>
        </div>
        <div class="mbJ3">
          <div class="mbJ1">{{ $T("填报方式") }}：</div>
          <template>
            <el-radio v-model="fillformtype" label="1">
              {{ $T("线上") }}
            </el-radio>
            <el-radio v-model="fillformtype" label="2">
              {{ $T("线下") }}
            </el-radio>
          </template>
        </div>
        <!-- <div v-if="inputData_in && inputData_in.sourcetype !== 5">
          <div class="mb-10">责任班组：</div>
          <ElSelect v-model="teamid" v-bind="ElSelect_team" v-on="ElSelect_team.event">
            <ElOption
              v-for="item in ElOption_team.options_in"
              :key="item[ElOption_team.key]"
              :label="item[ElOption_team.label]"
              :value="item[ElOption_team.value]"
              :disabled="item[ElOption_team.disabled]"
            ></ElOption>
          </ElSelect>
        </div>
        <div class="mt-10">
          <div class="mb-10">运值确认班组：</div>
          <ElSelect v-model="inspectteamid" v-bind="ElSelect_team" v-on="ElSelect_team.event">
            <ElOption
              v-for="item in ElOption_team.options_in"
              :key="item[ElOption_team.key]"
              :label="item[ElOption_team.label]"
              :value="item[ElOption_team.value]"
              :disabled="item[ElOption_team.disabled]"
            ></ElOption>
          </ElSelect>
        </div> -->
        <div class="mbJ3">
          <div class="mbJ1">{{ $T("预计耗时") }}：</div>
          <div class="countBox">
            <ElInputNumber
              v-model="timeconsumeplan"
              v-bind="ElInputNumber_1"
              v-on="ElInputNumber_1.event"
            ></ElInputNumber>
            <span class="form-item-unit">h</span>
          </div>
        </div>
        <div class="mbJ1">
          <span>{{ $T("意见") }}：</span>
        </div>
        <div style="height: 120px">
          <el-input
            type="textarea"
            rows="5"
            resize="none"
            :placeholder="$T('请输入内容')"
            v-model="description"
            onKeypress="javascript:if(event.keyCode == 32)event.returnValue = false;"
          ></el-input>
        </div>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_submit"
          v-on="CetButton_submit.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
export default {
  name: "RepairMode",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  data(vm) {
    return {
      isOk: true,
      CetDialog_add: {
        title: $T("选择维修方式"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_add_open_out,
          close_out: this.CetDialog_add_close_out
        },
        width: "540px",
        showClose: true
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        rules: {},
        event: {
          currentData_out: this.CetForm_1_currentData_out,
          saveData_out: this.CetForm_1_saveData_out,
          finishData_out: this.CetForm_1_finishData_out,
          finishTrigger_out: this.CetForm_1_finishTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: false,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      //责任班组
      ElSelect_team: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_team: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetButton_submit: {
        visible_in: true,
        disable_in: false,
        title: $T("提交"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_submit_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInputNumber_1: {
        // ...common.check_numberInt,
        min: 0.01,
        max: 999999999.99,
        step: 2,
        precision: 2,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      timeconsumeplan: null,
      repairtype: "2",
      fillformtype: "1",
      description: "",
      teamid: null,
      inspectteamid: null,
      copyStashMsg: {}
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      this.description = "";
      if (!this.inputData_in || !this.inputData_in.code) {
        this.CetButton_confirm.visible_in = false;
        vm.CetDialog_add.openTrigger_in = val;
      } else {
        this.CetButton_confirm.visible_in = true;
        Promise.all([
          // new Promise((resolve, reject) => {
          //   this.getTeam_out(resolve);
          // }),
          new Promise((resolve, reject) => {
            this.GetRepairCheckStashCode_out(resolve);
          })
        ]).then(data => {
          vm.CetDialog_add.openTrigger_in = val;
          // vm.teamid = this.inputData_in.teamid || null;
          // vm.inspectteamid = this.inputData_in.inspectteamid || null;
          if (this.inputData_in.timeconsumeplan) {
            vm.timeconsumeplan = (
              this.inputData_in.timeconsumeplan /
              (1000 * 60 * 60)
            ).toFixed2(2);
          }
          vm.description = this._.get(this.copyStashMsg, "remark", "") || "";
          let formdata =
            this._.get(this.copyStashMsg, "formdata", null) || null;
          if (formdata) {
            formdata = JSON.parse(formdata);
            vm.repairtype = String(formdata.repairtype);
            vm.fillformtype = String(formdata.fillformtype);
            vm.timeconsumeplan = (
              formdata.timeconsumeplan /
              (1000 * 60 * 60)
            ).toFixed2(2);
          }
        });
      }

      // this.reset();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    }
  },

  methods: {
    CetDialog_add_open_out(val) {},
    CetDialog_add_close_out(val) {},
    CetForm_1_currentData_out(val) {},
    CetForm_1_saveData_out(val) {
      this.$emit("confirm_out", this._.cloneDeep(val));
    },
    CetForm_1_finishData_out(val) {},
    CetForm_1_finishTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
      this.$emit("confirm_out", val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      const params = {
        code: this.inputData_in.code,
        handledescription: this.description,
        params: {
          formData: {
            repairtype: Number(this.repairtype),
            fillformtype: Number(this.fillformtype),
            timeconsumeplan: this.timeconsumeplan * 1000 * 60 * 60
          },
          remark: this.description
        }
      };
      this.queryRepairCheckStash_out(params);
    },
    CetButton_submit_statusTrigger_out(val) {
      const params = {
        code: this.inputData_in.code,
        handledescription: this.description,
        userTaskParams: {
          formData: {
            repairtype: Number(this.repairtype),
            id: this.inputData_in.id,
            modelLabel: "pmworksheet",
            fillformtype: Number(this.fillformtype),
            // teamid: this.teamid,
            // inspectteamid: this.inspectteamid,
            timeconsumeplan: this.timeconsumeplan * 1000 * 60 * 60
          },
          remark: this.description
        }
      };
      this.queryRepairCommitSubmit_out(params);
    },
    init() {},
    //处理维修工单
    queryRepairCommitSubmit_out(params) {
      const _this = this;
      if (!params || !params.code) {
        return;
      }

      customApi
        .queryRepairCommitSubmit(params)
        .then(res => {
          if (res.code === 0) {
            _this.CetDialog_add.closeTrigger_in = this._.cloneDeep(
              new Date().getTime()
            );
            _this.$emit("confirm_out", {});
            _this.$message.success($T("提交工单成功"));
          }
        })
        .catch(() => {});
    },
    //暂存维修信息
    queryRepairCheckStash_out(params) {
      const _this = this;
      if (!params || !params.code) {
        return;
      }

      customApi
        .queryRepairCheckStash(params)
        .then(res => {
          if (res.code === 0) {
            _this.CetDialog_add.closeTrigger_in = this._.cloneDeep(
              new Date().getTime()
            );
            _this.$emit("confirm_out", {});
            _this.$message.success($T("暂存审核信息成功"));
          }
        })
        .catch(() => {});
    },
    //查询暂存审核信息
    GetRepairCheckStashCode_out(callback) {
      if (!this.inputData_in || !this.inputData_in.code) {
        callback && callback();
        return;
      }
      const params = {
        code: this.inputData_in.code
      };

      customApi
        .GetRepairCheckStashCode(params)
        .then(res => {
          if (res.code === 0) {
            const resData = this._.get(res, "data", {});
            this.copyStashMsg = resData || {};
            callback && callback();
          } else {
            callback && callback();
          }
        })
        .catch(() => {});
    },
    // 获取班组列表信息
    getTeam_out(callback) {
      const _this = this;
      const params = {};
      customApi.queryInspectorTeamWithOutUserNoAuth(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          _this.ElOption_team.options_in = _this._.cloneDeep(data);
          callback && callback();
        }
      });
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.countBox {
  position: relative;
  width: 240px;
  :deep(.el-input__inner) {
    padding-right: 35px;
  }
  .count {
    position: absolute;
    top: 0;
    right: 8px;
  }
}
</style>
