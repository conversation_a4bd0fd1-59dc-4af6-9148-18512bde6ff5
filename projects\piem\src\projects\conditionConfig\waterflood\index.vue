<template>
  <div class="flex flex-col justify-between w-full h-full">
    <div>
      <el-input
        class="mrJ1"
        v-model="inputVal"
        placeholder="请输入方案名称"
        prefix-icon="el-icon-search"
        style="width: 240px"
        @input="onInput"
      ></el-input>
      <el-button type="primary" @click="onBatchNotification">
        批量通知
      </el-button>
    </div>
    <div
      class="h-[calc(100%-32px-24px)] w-full flex items-center justify-between mt-[24px]"
    >
      <div
        class="w-[calc(100%-32px)] h-full pr-[24px] box-border border-r border-B2 flex flex-col"
      >
        <div class="flex items-center justify-between">
          <div class="title">注水诊断方案</div>
          <el-button type="primary" @click="onAdd">新增</el-button>
        </div>
        <div class="h-[calc(100%-32px)] w-full mt-[16px]">
          <CetTable
            ref="table"
            class="mtJ3 piemTable"
            style="height: calc(100% - 48px)"
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
          >
            <el-table-column label="序号" width="70" type="index" />
            <el-table-column
              label="方案名称"
              prop="name"
              showOverflowTooltip
              :formatter="common.formatTextCol()"
            />
            <el-table-column label="报警等级">
              <template slot-scope="{ row }">
                <el-tag
                  v-if="row.alarmlevel"
                  :type="
                    row.alarmlevel === 1
                      ? 'danger'
                      : row.alarmlevel === 2
                      ? 'warning'
                      : row.alarmlevel === 3
                      ? 'primary'
                      : ''
                  "
                  :class="[row.alarmlevel === 3 ? 'primary' : '']"
                >
                  {{
                    (row.alarmlevel === 1
                      ? "一"
                      : row.alarmlevel === 2
                      ? "二"
                      : row.alarmlevel === 3
                      ? "三"
                      : "") + "级报警"
                  }}
                </el-tag>
                <span v-else>--</span>
              </template>
            </el-table-column>

            <el-table-column
              prop="waterfloodcrossing"
              :formatter="common.formatNumberCol(2)"
            >
              <template slot="header" slot-scope="{ row }">
                注水越线百分比(%）
                <el-tooltip
                  content="累注量和配注量差值和配注量的越限百分比"
                  placement="bottom"
                >
                  <i class="rotate-180 el-icon-warning"></i>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              prop="differencevalue"
              :formatter="common.formatNumberCol(2)"
            >
              <template slot="header" slot-scope="{ row }">
                油压干压差越线值（Mpa）
                <el-tooltip content="干压和油压差值的阈值" placement="bottom">
                  <i class="rotate-180 el-icon-warning"></i>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="是否启用方案" width="120">
              <template slot-scope="{ $index, row }">
                <el-switch
                  v-model="CetTable_1.data[$index].status"
                  @change="onSwitch(row, $index)"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="{ row }">
                <el-button
                  @click.stop="onEdit(row)"
                  type="text"
                  class="handleBtn"
                >
                  编辑
                </el-button>

                <el-button
                  type="text"
                  class="handleBtn delete"
                  @click.stop="onDelete(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </CetTable>
        </div>
      </div>
      <div class="w-[320px] h-full pl-[24px] box-border flex flex-col">
        <div class="flex items-center justify-between">
          <div class="title">关联管理节点</div>
          <el-button type="primary" @click="onSave">保存</el-button>
        </div>
        <div class="h-[calc(100%-48px)] w-full mt-[16px]">
          <customTree
            :dataMode="CetTree_1.dataMode"
            :inputData_in="CetTree_1.inputData_in"
            :selectNode.sync="CetTree_1.selectNode"
            :searchText_in="CetTree_1.searchText_in"
            :filterNodes_in="CetTree_1.filterNodes_in"
            :checkedArray.sync="CetTree_1.checkedArray"
            v-bind="CetTree_1.config"
          />
        </div>
      </div>
    </div>
    <AddOrEditWaterflood
      v-bind="addOrEditWaterflood"
      @updateTable="onUpdateTable"
    />
    <DeteleConfirm v-bind="deteleConfirm" @updateTable="onUpdateTable" />
    <BatchNoticeConfig v-bind="batchNoticeConfig"></BatchNoticeConfig>
  </div>
</template>

<script>
import common from "eem-utils/common.js";
import customTree from "@/components/customTree/index.vue";
import customApi from "@/api/custom.js";
import AddOrEditWaterflood from "./addOrEditWaterflood.vue";
import DeteleConfirm from "./deteleConfirm.vue";
import BatchNoticeConfig from "./batchNoticeConfig.vue";
export default {
  name: "ConditionConfigIndex",
  props: {},
  components: {
    customTree,
    AddOrEditWaterflood,
    DeteleConfirm,
    BatchNoticeConfig
  },
  data() {
    return {
      inputVal: "",
      timer: null,
      addOrEditWaterflood: {
        visibleTrigger_in: +new Date(),
        inputData_in: {}
      },
      deteleConfirm: {
        visibleTrigger_in: +new Date(),
        inputData_in: {}
      },
      batchNoticeConfig: {
        visibleTrigger_in: +new Date()
      },
      alarmlevelOptions: [
        { id: 1, text: "一级" },
        { id: 2, text: "二级" },
        { id: 3, text: "三级" }
      ],
      CetTable_1: {
        "empty-text": "列表暂无内容",
        queryMode: "",
        dataMode: "component",
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [],
          hasQueryNode: true
        },
        data: [],
        event: {
          record_out: this.onRowClick
        }
      },
      currentRow: {},
      CetTree_1: {
        dataMode: "static",
        inputData_in: [],
        searchText_in: "",
        filterNodes_in: "",
        selectNode: null,
        checkedArray: [],
        config: {
          highlightCurrent: true,
          ShowRootNode: false,
          expandMode: true,
          screen: true,
          initialFlag: false,
          showCheckbox: true,
          checkStrictly: false,
          filterModelLabel: "waterinjectionwell"
        }
      }
    };
  },
  computed: {
    common() {
      return common;
    }
  },
  watch: {},
  methods: {
    onBatchNotification() {
      this.batchNoticeConfig.visibleTrigger_in = +new Date();
    },
    onAdd() {
      this.addOrEditWaterflood.visibleTrigger_in = +new Date();
      this.addOrEditWaterflood.inputData_in = {};
    },
    onInput() {
      if (this.timer) clearTimeout(this.timer);
      this.timer = setTimeout(() => this.getTable(), 500);
    },
    onUpdateTable() {
      this.getTable();
    },

    async getTable() {
      const res = await customApi.querySchemeList({
        schemeName: this.inputVal
      });

      this.CetTable_1.data = res?.data || [];
    },

    async querySchemeById(row) {
      let data = _.cloneDeep(row?.waterflooddiagnosticschemetonode_model);
      if (data?.length > 0) {
        this.CetTree_1.checkedArray =
          data?.map(i => {
            return { modelLabel: "waterinjectionwell", id: i.nodeid };
          }) || [];
      } else {
        this.CetTree_1.checkedArray = [];
      }
    },

    onEdit(row) {
      this.addOrEditWaterflood.visibleTrigger_in = +new Date();
      this.addOrEditWaterflood.inputData_in = _.cloneDeep(row);
    },
    onDelete(row) {
      this.deteleConfirm.visibleTrigger_in = +new Date();
      this.deteleConfirm.inputData_in = _.cloneDeep(row);
    },
    async onSave() {
      const params = {
        schemeId: this.currentRow.id,
        objectLabel: "waterinjectionwell",
        nodeIdList: this.CetTree_1.checkedArray
          ?.filter(i => i.modelLabel === "waterinjectionwell")
          ?.map(i => i.id)
      };

      const res = await customApi.schemeRelevance(params);
      if (res.code === 0) {
        this.$message.success("保存成功");
        this.getTable();
      }
    },

    // 获取节点树
    async getTreeData() {
      const queryData = {
        rootID: 0,
        rootLabel: "oilcompany",
        subLayerConditions: [
          {
            modelLabel: "oilproductionplant"
          },
          {
            modelLabel: "operationarea"
          },
          {
            modelLabel: "oilproductioncrew"
          },
          {
            modelLabel: "platform"
          },
          {
            modelLabel: "waterinjectionstation"
          },
          {
            modelLabel: "waterinjectionplatform"
          },
          {
            modelLabel: "waterinjectionwell"
          }
        ],
        treeReturnEnable: true
      };

      const res = await customApi.getNodeTree(queryData);
      this.CetTree_1.inputData_in = res?.data || [];
    },
    async onSwitch(row) {
      const params = {
        name: row.name,
        alarmlevel: row.alarmlevel,
        waterfloodcrossing: row.waterfloodcrossing,
        differencevalue: row.differencevalue,
        id: row.id,
        status: !row.status
      };
      const res = await customApi.addUpdateScheme(params);
      if (res.code === 0) {
        this.$message.success("保存成功");
        this.getTable();
      }
    },

    onRowClick(row) {
      this.currentRow = _.cloneDeep(row);
      this.querySchemeById(row);
    }
  },
  created() {},
  mounted() {
    this.getTreeData();
    this.getTable();
  }
};
</script>

<style lang="scss" scoped>
.title {
  @apply font-semibold leading-8 text-H3 text-T2;
}
:deep(.el-tree) {
  overflow: auto;
}

.handleBtn {
  padding: 0;
  &:not(:first-of-type) {
    @include margin_left(J4);
  }
  &.delete {
    @include font-color(Sta3);
  }
  :deep(span) {
    @include font_size(Aa);
  }
}

.primary {
  color: #4ca6ff;
  background: rgba(76, 166, 255, 0.1);
}
</style>
