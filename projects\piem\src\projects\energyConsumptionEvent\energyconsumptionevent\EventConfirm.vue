<template>
  <!-- 1弹窗组件 -->
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="small">
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
    <div
      class="eem-cont-c1"
      style="overflow: auto; max-height: calc(84vh - 156px)"
    >
      <div class="confirm-opition clearfix">
        <span class="fl">{{ $T("确认意见") }}</span>
      </div>
      <ElInput
        class="confirm-input"
        v-model="ElInput_1.value"
        v-bind="ElInput_1"
        v-on="ElInput_1.event"
      ></ElInput>
      <div class="list clearfix">
        <span class="fl">{{ $T("确认时间") }}：</span>
        <span class="fl value-color">
          {{ this.$moment().format("YYYY-MM-DD HH:mm:ss") }}
        </span>
      </div>
      <div class="list clearfix">
        <span class="fl">{{ $T("操作人") }}：</span>
        <span class="fl value-color">{{ userInfo.nicName }}</span>
      </div>
    </div>
  </CetDialog>
</template>
<script>
import common from "eem-utils/common";
export default {
  name: "EventConfirm",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: [Array, Object]
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userInfo() {
      return this.$store.state.userInfo;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: $T("事件确认"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: true,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        style: {},
        rows: 8,
        type: "textarea",
        maxlength: 255,
        showWordLimit: true,
        required: true,
        placeholder: $T("请输入内容（必需）"),
        event: {
          change: this.ElInput_1_change_out,
          input: this.ElInput_1_input_out
        }
      },
      dialogWidth: "500px"
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      vm.ElInput_1.value = "";
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {
      var vm = this;
      // vm.CetDialog_1.queryId_in = val;
    },
    inputData_in(val) {
      // this.CetDialog_1.inputData_in = val;
    }
  },

  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      var eventList = this.inputData_in.eventlist;
      this.eventConfirm_out(eventList);
    },
    CetDialog_1_open_out(val) {},
    CetDialog_1_close_out(val) {},
    ElInput_1_change_out(val) {},
    ElInput_1_input_out(val) {
      this.CetButton_confirm.disable_in = !val.length;
    },

    getConfirmEventData_out(events) {
      var me = this;
      var list = [];
      if (!events || !events.length || events.length === 0) {
        return;
      }
      this._(events).forEach(function (item) {
        item.modelLabel = item.modelLabel ? item.modelLabel : "systemevent";
        item.confirmeventstatus = 3;
        item.operator = me.userInfo.nicName;
        item.operator_id = me.userInfo.id;
        item.remark = me.ElInput_1.value;
        item.updatetime = new Date().getTime();
      });
      return events;
    },

    eventConfirm_out(events) {
      var me = this,
        url,
        list;
      list = me.getConfirmEventData_out(events);
      if (!list || list.length === 0) {
        return;
      }
      url = `/eem-service/v1/alarmEvent/confirmEvents`;
      common.requestData(
        {
          url: url,
          data: list
        },
        res => {
          this.$message.success($T("操作成功"));
          me.CetDialog_1.closeTrigger_in = new Date().getTime();
          me.$emit("finishTrigger_out", new Date().getTime());
          me.$emit("confirmedTrigger_out", list);
        }
      );
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.list {
  height: 20px;
  margin: 8px 0 0;
}
.value-color {
  // color: #0066cc;
}
.confirm-opition {
  box-sizing: border-box;
  border: 1px solid;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  border-radius: 5px 5px 0 0;
  @include border_color(B1);
}
.confirm-input {
  border: solid;
  border-width: 0 1px 1px;
  border-radius: 0 0 5px 5px;
  box-sizing: border-box;
  @include border_color(B1);
}
</style>
<style>
.confirm-input .el-textarea__inner {
  border-width: 0;
  /* background: #1C2850; */
}
</style>
