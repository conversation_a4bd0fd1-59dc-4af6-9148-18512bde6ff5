<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="small"
    >
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <CetForm
        class="eem-cont-c1"
        :data.sync="CetForm_pagedialog.data"
        v-bind="CetForm_pagedialog"
        v-on="CetForm_pagedialog.event"
      >
        <el-form-item :label="$T('值班员')" prop="dutystaff">
          <ElSelect
            v-model="CetForm_pagedialog.data.dutystaff"
            v-bind="ElSelect_dutystaff"
            v-on="ElSelect_dutystaff.event"
          >
            <ElOption
              v-for="item in ElOption_dutystaff.options_in"
              :key="item[ElOption_dutystaff.key]"
              :label="item[ElOption_dutystaff.label]"
              :value="item"
              :disabled="item[ElOption_dutystaff.disabled]"
            ></ElOption>
          </ElSelect>
        </el-form-item>
        <el-form-item
          v-if="!CetForm_pagedialog.data.firstHandOver"
          :label="$T('密码')"
          prop="password"
        >
          <ElInput
            v-model="CetForm_pagedialog.data.password"
            v-bind="ElInput_password"
            v-on="ElInput_password.event"
          ></ElInput>
        </el-form-item>
        <el-form-item :label="$T('责任班组')" prop="">
          <span>{{ CetForm_pagedialog.data.teamName || "--" }}</span>
        </el-form-item>
        <!-- 第一次接班时不显示 -->
        <el-form-item
          :label="$T('交接事项')"
          prop="event"
          v-if="!CetForm_pagedialog.data.firstHandOver"
        >
          <el-tooltip
            effect="light"
            placement="top-start"
            :content="CetForm_pagedialog.data.handovermatter"
          >
            <div style="width: 100%" class="text-ellipsis">
              {{ CetForm_pagedialog.data.handovermatter || "--" }}
            </div>
          </el-tooltip>
        </el-form-item>
        <el-form-item :label="$T('值班长')" prop="">
          <span>{{ CetForm_pagedialog.data.dutyOfficerName || "--" }}</span>
        </el-form-item>
        <el-form-item :label="$T('正在值班的值班长')" prop="">
          <span>{{ CetForm_pagedialog.data.onDutyOfficerName || "--" }}</span>
        </el-form-item>
        <!-- <div style="height: 30px; line-height: 30px">
          <el-col :span="4">
            <el-tooltip content="正在值班的值班长:" effect="light">
              <div class="nowrap text-ellipsis fullwidth">
                正在值班的值班长:
              </div>
            </el-tooltip>
          </el-col>
          <el-col :span="20">
            <span>{{ CetForm_pagedialog.data.onDutyOfficerName || "--" }}</span>
          </el-col>
        </div> -->
        <span
          v-if="
            !CetForm_pagedialog.data.firstHandOver &&
            !CetForm_pagedialog.data.handovermatter
          "
          style="color: #f00"
        >
          {{ $T("上次值班无交接班注意事项，不允许接班！") }}
        </span>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";
import md5 from "crypto-js/md5";

export default {
  name: "takeoverDialog",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    userInfo() {
      return this.$store.state.userInfo;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      groupList: [],
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("接班"),
        "show-close": true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "component", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        labelPosition: "top",
        rules: {
          dutystaff: [
            {
              required: true,
              message: $T("请选择值班员"),
              trigger: ["blur", "change"]
            }
          ],
          password: [
            {
              required: true,
              message: $T("请输入密码"),
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_pagedialog_currentData_out,
          saveData_out: this.CetForm_pagedialog_saveData_out,
          finishData_out: this.CetForm_pagedialog_finishData_out,
          finishTrigger_out: this.CetForm_pagedialog_finishTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // 值班员
      ElSelect_dutystaff: {
        value: "",
        style: {
          width: "100%"
        },
        multiple: true,
        "collapse-tags": true,
        "value-key": "userId",
        event: {}
      },
      ElOption_dutystaff: {
        options_in: [],
        key: "userId",
        value: "userId",
        label: "userName",
        disabled: "disabled"
      },
      // password组件
      ElInput_password: {
        value: "",
        type: "password",
        placeholder: $T("请输入上一次值班长巡检密码"),
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInput_event: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in() {
      this.CetForm_pagedialog.resetTrigger_in = new Date().getTime();
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {
      this.CetForm_pagedialog.queryId_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      this.CetForm_pagedialog.data = this._.cloneDeep(val);
      this.ElSelect_dutystaff.value = "";
      this.ElOption_dutystaff.options_in = val.allowChooseUsers;
      this.CetDialog_pagedialog.openTrigger_in = new Date().getTime();
      this.CetForm_pagedialog.resetTrigger_in = new Date().getTime();
      if (val.firstHandOver) {
        // 第一次接班
        this.CetButton_preserve.disable_in = false;
      } else {
        this.CetButton_preserve.disable_in = !val.handovermatter;
      }
    }
  },
  methods: {
    CetForm_pagedialog_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_saveData_out(val) {
      this.$emit("saveData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishData_out(val) {
      this.$emit("finishData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
      this.$emit("finishTrigger_out", val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetForm_pagedialog.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      // 接班两人在场，输入交班人密码验证
      const nowTime = new Date().getTime();
      const param = {
        id: this.inputData_in.onDutyOfficer,
        password: this.encryptMD5(
          this.CetForm_pagedialog.data.password,
          nowTime
        ),
        timestamps: nowTime
      };
      // 第一次接班不用验证密码
      if (this.CetForm_pagedialog.data.firstHandOver) {
        this.CetForm_pagedialog.localSaveTrigger_in = this._.cloneDeep(val);
      } else {
        customApi.checkHandleOverPassword(param).then(res => {
          if (res.code === 0 && res.data) {
            this.CetForm_pagedialog.localSaveTrigger_in = this._.cloneDeep(val);
          } else {
            this.$message.error($T("输入巡检密码不正确!"));
          }
        });
      }
    },
    // MD5加密
    encryptMD5: function (value, nowTime) {
      var tr = "CET_Matterhorn#" + value + "#" + nowTime;

      if (md5) {
        return md5(tr).toString();
      } else {
        console.warn && console.warn("MD5 Function Not Found!");
        return value;
      }
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped></style>
