<template>
  <!-- 1弹窗组件 -->
  <div>
    <CetDialog
      class="CetDialog eem-common"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
    >
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
      <CetForm
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
      >
        <div class="title">{{ $T("角色信息") }}</div>
        <div class="cardBox eem-cont mtJ1 clearfix">
          <el-row :gutter="$J3">
            <el-col :span="8">
              <el-form-item :label="$T('角色名称')" prop="name">
                <ElInput
                  v-model.trim="CetForm_1.data.name"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="" prop="customConfig">
                <div slot="label">
                  {{ $T("角色首页") }}
                  <el-tooltip class="tooltip" effect="light">
                    <i class="el-icon-question"></i>
                    <div slot="content" class="tooltipContent">
                      {{ $T("关联页面访问权限后才能选择。") }}
                    </div>
                  </el-tooltip>
                </div>
                <ElSelect
                  v-model="ElSelect_Homepage.value"
                  v-bind="ElSelect_Homepage"
                  v-on="ElSelect_Homepage.event"
                >
                  <ElOption
                    v-for="item in ElOption_Homepage.options_in"
                    :key="item[ElOption_Homepage.key]"
                    :label="item[ElOption_Homepage.label]"
                    :value="item[ElOption_Homepage.value]"
                    :disabled="item[ElOption_Homepage.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <el-tabs v-model="activeName" class="eem-tabs-custom ptJ1 pbJ1">
          <el-tab-pane
            :label="$T('页面操作权限')"
            name="permissions"
          ></el-tab-pane>
          <el-tab-pane :label="$T('页面访问权限')" name="page"></el-tab-pane>
          <el-tab-pane
            :label="$T('移动端页面访问权限')"
            name="app"
            v-if="appMenuPermission"
          ></el-tab-pane>
        </el-tabs>
      </CetForm>
      <div class="treeBox bg1">
        <div v-show="activeName === 'permissions'" class="fullfilled">
          <CetTree
            class="CetTree"
            :selectNode.sync="CetTree_Permissions.selectNode"
            :checkedNodes.sync="CetTree_Permissions.checkedNodes"
            v-bind="CetTree_Permissions"
            v-on="CetTree_Permissions.event"
          ></CetTree>
        </div>
        <div v-show="activeName === 'page'" class="fullfilled">
          <CetTree
            class="CetTree"
            :selectNode.sync="CetTree_Pages.selectNode"
            :checkedNodes.sync="CetTree_Pages.checkedNodes"
            v-bind="CetTree_Pages"
            v-on="CetTree_Pages.event"
          ></CetTree>
        </div>
        <div v-show="activeName === 'app'" class="fullfilled">
          <CetTree
            class="CetTree"
            :selectNode.sync="CetTree_appMenus.selectNode"
            :checkedNodes.sync="CetTree_appMenus.checkedNodes"
            v-bind="CetTree_appMenus"
            v-on="CetTree_appMenus.event"
          ></CetTree>
        </div>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import {
  getPagePermissionTreeNodes,
  getOperatePermissionTreeNodes,
  getPagePermissionFilterNodes
} from "eem-utils/permission.js";
import common from "eem-utils/common";
import { find } from "eem-utils/util";
export default {
  components: {},
  props: {
    roleData_in: {
      type: Object,
      default: null
    },
    isEditMode: {
      type: Boolean,
      default: false
    },
    openTrigger_in: {
      type: Number,
      default: new Date().getTime()
    },
    appMenus: {
      type: Array
    },
    appMenuPermission: {
      type: Boolean
    }
  },
  computed: {
    projectTenantId() {
      if (this.$route.name === "platformurolemanage") {
        // 平台用户管理就取当前用户的租户id
        return this.$store.state.userInfo.tenantId;
      } else {
        return this.$store.state.projectTenantId;
      }
    },
    // 是否是默认角色
    ifDefaultRole() {
      let role = this.roleData_in;
      if (!role) {
        return false;
      }
      let customConfig = {};
      if (role.customConfig) {
        customConfig = JSON.parse(role.customConfig);
      }
      return customConfig.fixedrole;
    }
  },
  data(vm) {
    return {
      activeName: "permissions",
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        type: "primary",
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [
            "id",
            "name",
            "auths",
            "customConfig",
            "pageNodes",
            "tenantId",
            "modelNodes"
          ], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {
          id: null,
          name: "",
          auths: [],
          customConfig: "",
          tenantId: vm.projectTenantId,
          pageNodes: [],
          modelNodes: []
        },
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入角色名称")
            },
            common.pattern_name,
            common.check_stringLessThan50
          ]
        },
        event: {
          finishData_out: this.CetForm_1_finishData_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入"),
        style: {
          width: "100%"
        },
        event: {}
      },
      // 操作权限树组件
      CetTree_Permissions: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: false,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: true,
        checkStrictly: false,
        defaultExpandAll: true,
        event: {
          allCheckNodes_out: this.CetTree_Permissions_allCheckNodes_out
        }
      },
      // 首页下拉组件
      ElSelect_Homepage: {
        lastValue: "",
        value: "",
        clearable: true,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_Homepage_change_out
        }
      },
      // 首页下拉选项配置
      ElOption_Homepage: {
        options_in: [],
        key: "id",
        value: "id",
        label: "label",
        disabled: "disabled"
      },
      // 页面权限树组件
      CetTree_Pages: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: false,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "label",
          children: "subMenuList"
        },
        highlightCurrent: true,
        showCheckbox: true,
        checkStrictly: false,
        defaultExpandAll: true,
        event: {
          allCheckNodes_out: this.CetTree_Pages_allCheckNodes_out
        }
      },
      CetTree_appMenus: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: false,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "label",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: true,
        checkStrictly: false,
        defaultExpandAll: true,
        event: {}
      }
    };
  },
  watch: {
    openTrigger_in(val) {
      var vm = this;
      this.open();
      vm.CetDialog_1.openTrigger_in = val;
    }
  },

  methods: {
    // 打开弹窗
    open() {
      const vm = this;
      let permissionTreeData = [
        {
          id: "all_0",
          modelLabel: "all",
          name: $T("全选"),
          children: []
        }
      ];
      if (this.$route.name === "platformurolemanage") {
        permissionTreeData[0].children = getOperatePermissionTreeNodes(true);
      } else {
        permissionTreeData[0].children = getOperatePermissionTreeNodes(true, [
          "serviceprovider",
          "qy",
          "tenant"
        ]);
      }
      vm.CetTree_Permissions.inputData_in = permissionTreeData;
      if (this.$route.name === "platformurolemanage") {
        // 平台用户管理就取所有页面
        let pages = getPagePermissionTreeNodes(true, ["fw", "pf"], true);
        if (this.ifDefaultRole) {
          let platformusermanage = find(pages, "cloud_platformusermanage", {
            childKey: "subMenuList",
            valueKey: "permission"
          });
          let platformurolemanage = find(pages, "cloud_platformurolemanage", {
            childKey: "subMenuList",
            valueKey: "permission"
          });
          platformusermanage.disabled = true;
          platformurolemanage.disabled = true;
        }
        this.CetTree_Pages.inputData_in = pages;
      } else {
        this.CetTree_Pages.inputData_in = getPagePermissionTreeNodes(true, [
          "fw"
        ]);
      }
      vm.$nextTick(() => {
        vm.init();
      });
      const appMenuTreeData = [
        {
          id: "all_0",
          modelLabel: "all",
          label: $T("全选"),
          children: this.appMenus
        }
      ];
      this.CetTree_appMenus.inputData_in = appMenuTreeData;
    },
    init() {
      const vm = this;
      vm.activeName = "permissions";
      let fromData = {};
      if (vm.isEditMode) {
        vm.CetDialog_1.title = $T("编辑角色");
      } else {
        vm.CetDialog_1.title = $T("新增角色");
      }
      fromData.tenantId = vm.projectTenantId;
      if (vm.isEditMode) {
        fromData.id = vm.roleData_in.id;
        fromData.name = vm.roleData_in.name;
        fromData.auths = vm.roleData_in.auths || [];
        fromData.modelNodes = vm.roleData_in.modelNodes;
        fromData.customConfig = vm.roleData_in.customConfig || "";
        fromData.pageNodes = vm.roleData_in.pageNodes || [];
      } else {
        fromData.id = null;
        fromData.name = "";
        fromData.auths = [];
        fromData.customConfig = "";
        fromData.pageNodes = [];
        fromData.modelNodes = [];
      }

      const ownedPermission = [];
      fromData.auths.forEach(authId => {
        ownedPermission.push({
          id: authId
        });
      });
      vm.CetTree_Permissions.checkedNodes = ownedPermission;
      if (vm.$route.name === "platformurolemanage") {
        vm.CetTree_Pages.checkedNodes = getPagePermissionFilterNodes(
          fromData.pageNodes,
          getPagePermissionTreeNodes(false, ["fw", "pf"], true)
        );
      } else {
        vm.CetTree_Pages.checkedNodes = getPagePermissionFilterNodes(
          fromData.pageNodes
        );
      }
      // 移动端已经分配的页面
      const str = "eem_am_mobile";
      vm.CetTree_appMenus.checkedNodes = fromData.pageNodes.filter(item =>
        item.id.includes(str)
      );
      vm.ElSelect_Homepage.lastValue = vm.ElSelect_Homepage.value =
        vm.getHomepage(fromData);
      vm.CetForm_1.data = fromData;
      vm.CetForm_1.resetTrigger_in = new Date().getTime();
    },
    // 获取角色的首页
    getHomepage(role) {
      if (!role || !role.customConfig) {
        return null;
      }

      const pageObj = JSON.parse(role.customConfig);
      if (!pageObj) {
        return null;
      }

      return pageObj.homePagePageNodeId || pageObj.homepage;
    },
    // 操作权限树输出
    CetTree_Permissions_allCheckNodes_out(val) {
      const vm = this;
      const auths = val || [];
      vm.CetForm_1.data.auths = [];
      auths.forEach(auth => {
        if (auth.type === "permission") {
          vm.CetForm_1.data.auths.push(auth.id);
        }
      });
    },
    // 首页下拉组件
    ElSelect_Homepage_change_out(val) {
      this.ElSelect_Homepage.lastValue = val;
    },
    // 页面权限树输出
    CetTree_Pages_allCheckNodes_out(val) {
      const vm = this;
      const checkedNodes = val || [];
      const pageNodes = [];
      const homePageNodes = [];
      checkedNodes.forEach(checkedNode => {
        if (checkedNode.type === "menuItem") {
          pageNodes.push({
            id: checkedNode.permission,
            label: checkedNode.label,
            disabled: false,
            authIds: [],
            rule: ""
          });

          homePageNodes.push({
            id: checkedNode.permission,
            label: checkedNode.label
          });
        }
      });
      vm.CetForm_1.data.pageNodes = pageNodes;

      vm.ElOption_Homepage.options_in = homePageNodes;

      const homepage = vm._.find(vm.ElOption_Homepage.options_in, {
        id: vm.ElSelect_Homepage.lastValue
      });
      vm.ElSelect_Homepage.value = homepage ? homepage.id : "";
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      this.addOrEditRole();
    },
    CetForm_1_finishData_out() {
      this.CetDialog_1.closeTrigger_in = new Date().getTime();
      this.$emit("roleChanged_out");
    },
    // 增加或编辑用户
    addOrEditRole() {
      const vm = this;
      if (vm.isEditMode) {
        const customConfig = this.roleData_in.customConfig
          ? JSON.parse(this.roleData_in.customConfig)
          : {};
        // 删掉以前使用的homepage字段，改用homePagePageNodeId
        delete customConfig.homepage;
        vm.CetForm_1.data.customConfig = JSON.stringify({
          ...customConfig,
          homePagePageNodeId: vm.ElSelect_Homepage.value
        });
      } else {
        if (vm.ElSelect_Homepage.value) {
          if (this.$route.name === "platformurolemanage") {
            vm.CetForm_1.data.customConfig = JSON.stringify({
              homePagePageNodeId: vm.ElSelect_Homepage.value
            });
          } else {
            vm.CetForm_1.data.customConfig = JSON.stringify({
              homePagePageNodeId: vm.ElSelect_Homepage.value,
              roletype: 4 //这个后端要求传4，也不敢说也不敢问，听说是项目级用户就传4，否则用户里面查不到
            });
          }
        } else {
          if (this.$route.name !== "platformurolemanage") {
            vm.CetForm_1.data.customConfig = JSON.stringify({
              roletype: 4
            });
          }
        }
      }
      if (vm.isEditMode) {
        vm.CetForm_1.dataConfig.writeFunc = "editRole";
      } else {
        vm.CetForm_1.dataConfig.writeFunc = "addRole";
      }
      // 页面访问权限节点处理
      const pageNodes = [];
      vm.CetTree_appMenus.checkedNodes.forEach(item => {
        if (item.modelLabel !== "all") {
          pageNodes.push({
            id: item.id,
            label: item.label,
            disabled: false
          });
        }
      });
      vm.CetForm_1.data.pageNodes = [
        ...vm.CetForm_1.data.pageNodes,
        ...pageNodes
      ];
      if (!vm.CetForm_1.data.pageNodes || !vm.CetForm_1.data.pageNodes.length) {
        this.$message.warning($T("请先选择页面权限"));
        return;
      }
      vm.$nextTick(() => {
        vm.CetForm_1.saveTrigger_in = new Date().getTime();
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  .nbsp :deep(.el-form-item__label) {
    height: 42px;
  }

  .title {
    font-weight: bold;
    @include margin_left(J3);
  }
  .cardBox {
    @include border_radius(C1);
  }
  .eem-tabs-custom {
    @include background_color(BG);
    :deep(.el-tabs__nav-wrap::after) {
      @include background_color(BG);
    }
  }
  .treeBox {
    height: 500px;
    @include border_radius(C1);
    @include padding(J4 J3);
    box-sizing: border-box;
    flex: 1;
    min-height: 0;
    .CetTree {
      width: 100%;
    }
  }
}
</style>
