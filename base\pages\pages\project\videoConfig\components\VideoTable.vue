<template>
  <div class="page flex-column">
    <div class="vertical-middle mbJ3">
      <div class="fullwidth header">
        <div class="nodeName mrJ2 text-ellipsis">
          <el-tooltip :content="get(currentNode, 'name')" effect="light">
            <span class="common-title-H1">{{ get(currentNode, "name") }}</span>
          </el-tooltip>
        </div>
        <div class="fr">
          <div class="fl">
            <ElInput
              v-model="ElInput_search.value"
              v-bind="ElInput_search"
              v-on="ElInput_search.event"
              suffix-icon="el-icon-search"
            ></ElInput>
          </div>
          <div class="fl mlJ1">
            <customElSelect
              v-model="ElSelect_lineType.value"
              v-bind="ElSelect_lineType"
              :prefix_in="$T('连接类型')"
              v-on="ElSelect_lineType.event"
            >
              <ElOption
                v-for="item in ElOption_lineType.options_in"
                :key="item[ElOption_lineType.key]"
                :label="item[ElOption_lineType.label]"
                :value="item[ElOption_lineType.value]"
                :disabled="item[ElOption_lineType.disabled]"
              ></ElOption>
            </customElSelect>
          </div>
          <div class="fl" v-if="$checkPermission('videocamera_create')">
            <CetButton
              class="mlJ1 fl"
              v-bind="CetButton_import"
              v-on="CetButton_import.event"
            ></CetButton>
            <CetButton
              class="mlJ1"
              v-permission="'videocamera_create'"
              v-bind="CetButton_createVideo"
              v-on="CetButton_createVideo.event"
            ></CetButton>
            <el-dropdown class="mlJ3 more" @command="handleCommand">
              <span class="el-dropdown-link">
                {{ $T("更多") }}
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="export">
                  {{ $T("导出数据") }}
                </el-dropdown-item>
                <el-dropdown-item
                  v-permission="'videocamera_create'"
                  command="copy"
                  :disabled="currentRow.id === -1"
                >
                  {{ $T("复制") }}
                </el-dropdown-item>
                <el-dropdown-item
                  v-permission="'videocamera_create'"
                  command="mulDel"
                  class="mulDel"
                  :disabled="!Boolean(checkList.length)"
                >
                  {{ $T("批量删除") }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div v-else class="fl mlJ1">
            <el-button type="primary" plain @click="handleCommand('export')">
              {{ $T("导出数据") }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <main class="flex-auto padding0">
      <CetTable
        class="eem-table-custom"
        :data.sync="CetTable_videos.data"
        :dynamicInput.sync="CetTable_videos.dynamicInput"
        v-bind="CetTable_videos"
        v-on="CetTable_videos.event"
      >
        <template v-for="item in Columns_videos">
          <ElTableColumn
            v-if="item.prop === 'playUrl'"
            :key="item.label"
            v-bind="item"
          >
            <template slot-scope="{ row }">
              <span class="html" v-html="formatPlayUrl(row)"></span>
            </template>
          </ElTableColumn>
          <ElTableColumn v-else :key="item.label" v-bind="item"></ElTableColumn>
        </template>
        <ElTableColumn v-bind="ElTableColumn_operate">
          <template slot-scope="{ row }">
            <span class="mrJ2 row-handle" @click="handlePreview(row)">
              {{ $T("预览") }}
            </span>
            <span
              v-permission="'videocamera_create'"
              class="mlJ2 row-handle"
              @click="editVideo(row)"
            >
              {{ $T("编辑") }}
            </span>
          </template>
        </ElTableColumn>
      </CetTable>
    </main>

    <CreateVideoDialog
      v-bind="CreateVideoDialog"
      v-on="CreateVideoDialog.event"
    ></CreateVideoDialog>
    <!-- 导入数据 -->
    <ImportDataDialog
      v-bind="ImportDataDialog"
      v-on="ImportDataDialog.event"
    ></ImportDataDialog>
    <!-- 视频预览 -->
    <PreviewVideo
      v-bind="PreviewVideo"
      v-on="PreviewVideo.event"
    ></PreviewVideo>
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom";
import CreateVideoDialog from "./CreateVideoDialog";
import ImportDataDialog from "./ImportDataDialog.vue";
import PreviewVideo from "./PreviewVideo.vue";

const Linetypeenumeration = [
  { text: $T("全部"), propertyLabel: "all" },
  { text: $T("直连"), propertyLabel: "direct" },
  { text: $T("萤石云"), propertyLabel: "YSCloud" }
];
export default {
  name: "VideoTable",
  components: {
    CreateVideoDialog,
    ImportDataDialog,
    PreviewVideo
  },
  props: {
    currentNode: {
      type: Object
    }
  },
  data() {
    return {
      checkList: [],
      currentRow: {}, // 表格当前行
      ElInput_search: {
        value: "",
        placeholder: $T("请输入摄像头名称"),
        size: "small",
        style: {
          width: "240px"
        },
        event: {
          change: this.ElInput_search_change_out
        }
      },
      CetButton_import: {
        visible_in: true,
        disable_in: false,
        title: $T("导入数据"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_import_statusTrigger_out
        }
      },
      CetButton_createVideo: {
        visible_in: true,
        disable_in: true,
        title: $T("添加设备"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_createVideo_statusTrigger_out
        }
      },
      ElSelect_lineType: {
        value: "all",
        size: "small",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_lineType_change_out
        }
      },
      ElOption_lineType: {
        options_in: Linetypeenumeration,
        key: "propertyLabel",
        value: "propertyLabel",
        label: "text",
        disabled: "disabled"
      },
      CetTable_videos: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "videoQueryVideos",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "id_in", operator: "LIKE", prop: "foldId" },
            { name: "key_in", operator: "LIKE", prop: "keyword" },
            { name: "lineType_in", operator: "LIKE", prop: "joinType" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          id_in: "",
          key_in: "",
          lineType_in: ""
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        localBatchDeletionTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {
          pageSize: 20
        },
        exportFileName: "",
        event: {
          "selection-change": this.selection_change,
          record_out: this.CetTable_videos_record_out,
          totalNum_out: this.CetTable_videos_totalCount_out
        }
      },
      Columns_videos: [
        {
          type: "selection", // selection 勾选 index 序号
          align: "left",
          width: "50"
        },
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "50" //绝对宽度
        },
        {
          prop: "id", // 支持path a[0].b
          minWidth: "100",
          label: $T("记录id"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("摄像头名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "160", //绝对宽度
          formatter: common.formatTextCol()
        },
        {
          prop: "joinType", // 支持path a[0].b
          label: $T("连接类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "110",
          formatter: this.joinTypeFormat
        },
        {
          prop: "playUrl", // 支持path a[0].b
          label: $T("播放地址"), //列名
          headerAlign: "left",
          align: "left",
          minWidth: "400",
          showOverflowTooltip: true,
          formatter: this.formatPlayUrl
        },
        {
          prop: "originUrl", // 支持path a[0].b
          label: $T("视频流地址"), //列名
          headerAlign: "left",
          align: "left",
          minWidth: "250",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "appName", // 支持path a[0].b
          label: $T("云平台序列号"), //列名
          headerAlign: "left",
          align: "left",
          minWidth: "120",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "streamName", // 支持path a[0].b
          label: $T("云平台通道号"), //列名
          headerAlign: "left",
          align: "left",
          minWidth: "120",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "foldLevelInfo", // 支持path a[0].b
          label: $T("父层级路径"), //列名
          headerAlign: "left",
          align: "left",
          minWidth: "120",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "remark2", // 支持path a[0].b
          label: $T("摄像头型号"), //列名
          headerAlign: "left",
          align: "left",
          minWidth: "130",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "remark3", // 支持path a[0].b
          label: $T("厂商"), //列名
          headerAlign: "left",
          align: "left",
          minWidth: "130",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "deviceSerial", // 支持path a[0].b
          label: $T("设备序列号"), //列名
          headerAlign: "left",
          align: "left",
          minWidth: "120",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "remark", // 支持path a[0].b
          label: $T("备注信息"), //列名
          headerAlign: "left",
          align: "left",
          minWidth: "120",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ],
      ElTableColumn_operate: {
        prop: "", // 支持path a[0].b
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "120", //绝对宽度
        fixed: "right"
      },
      // CreateVideoDialog弹窗页面组件
      CreateVideoDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        editStatus: "",
        event: {
          finishData_out: this.CreateVideoDialog_saveData_out
        }
      },
      ImportDataDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        event: {
          refresh: this.refresh
        }
      },
      PreviewVideo: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      }
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  watch: {
    currentNode: {
      handler(val) {
        if (val && val.id) {
          this.CetButton_createVideo.disable_in = false;
          this.CetTable_videos.dynamicInput.id_in = val.id;
          this.CetTable_videos.queryTrigger_in = Date.now();
        } else {
          this.CetButton_createVideo.disable_in = true;
        }
      },
      deep: true
    }
  },
  methods: {
    get(...arg) {
      return this._.get(...arg);
    },
    ElInput_search_change_out(val) {
      this.CetTable_videos.dynamicInput.key_in = val;
      this.CetTable_videos.queryTrigger_in = Date.now();
    },
    ElSelect_lineType_change_out(val) {
      this.CetTable_videos.dynamicInput.lineType_in = val === "all" ? "" : val;
      this.CetTable_videos.queryTrigger_in = Date.now();
    },
    // 视频预览
    handlePreview(row) {
      this.PreviewVideo.inputData_in = this._.cloneDeep(row);
      this.PreviewVideo.openTrigger_in = new Date().getTime();
    },
    // 编辑摄像头
    editVideo(row) {
      this.CreateVideoDialog.editStatus = "edit";
      this.CreateVideoDialog.inputData_in = {
        id: row.id,
        name: row.name,
        joinType: row.joinType,
        originUrl: row.originUrl,
        unitType: row.unitType,
        manufacturer: row.manufacturer,
        deviceSerial: row.deviceSerial,
        remark: row.remark
      };
      if (row.joinType === "YSCloud") {
        this.CreateVideoDialog.inputData_in.appName = row.appName;
        this.CreateVideoDialog.inputData_in.streamName = row.streamName;
      }
      this.CreateVideoDialog.queryId_in = this.currentNode.id;
      this.CreateVideoDialog.openTrigger_in = Date.now();
    },
    handleCommand(command) {
      // 导出数据
      if (command === "export") {
        const url = `/eem-service/video/config/excel/export?templateFlag=false`;
        common.downExcelGET(url, {}, this.token, this.projectId);
      }
      // 复制
      if (command === "copy") {
        this.CreateVideoDialog.inputData_in = {
          name: this.currentRow.name + "副本",
          joinType: this.currentRow.joinType,
          originUrl: this.currentRow.originUrl,
          unitType: this.currentRow.unitType,
          manufacturer: this.currentRow.manufacturer,
          deviceSerial: this.currentRow.deviceSerial,
          remark: this.currentRow.deviceSerial
        };
        if (this.currentRow.joinType === "YSCloud") {
          this.CreateVideoDialog.inputData_in.appName = this.currentRow.appName;
          this.CreateVideoDialog.inputData_in.streamName =
            this.currentRow.streamName;
        }
        this.CreateVideoDialog.editStatus = "copy";
        this.CreateVideoDialog.queryId_in = this.currentNode.id;
        this.CreateVideoDialog.openTrigger_in = new Date().getTime();
      }
      // 删除
      if (command === "mulDel") {
        this.$confirm(`确定要删除所选项吗？`, "提示", {
          type: "warning",
          distinguishCancelAndClose: true,
          confirmButtonText: "确定",
          cancelButtonText: "取消"
        }).then(() => {
          const ids = this.checkList.map(item => item.id);
          customApi.videoDeleteVideo(ids).then(res => {
            if (res.code === 0) {
              this.$message.success("删除成功!");
              this.CetTable_videos.queryTrigger_in = Date.now();
            }
          });
        });
      }
    },
    CetButton_import_statusTrigger_out(val) {
      this.ImportDataDialog.openTrigger_in = new Date().getTime();
    },
    // 添加摄像头
    CetButton_createVideo_statusTrigger_out() {
      this.CreateVideoDialog.editStatus = "create";
      this.CreateVideoDialog.queryId_in = this.currentNode.id;
      this.CreateVideoDialog.openTrigger_in = Date.now();
    },
    selection_change(val) {
      this.checkList = val || [];
    },
    CetTable_videos_record_out(val) {
      this.currentRow = val;
    },
    CetTable_videos_totalCount_out(val) {
      this.$emit("getTotal", val);
    },
    CreateVideoDialog_saveData_out(val) {
      this.CetTable_videos.queryTrigger_in = Date.now();
    },
    refresh() {
      this.CetTable_videos.queryTrigger_in = new Date().getTime();
    },
    // 连接类型格式化
    joinTypeFormat(row) {
      if (!row.joinType) return "--";
      return (
        this._.find(Linetypeenumeration, ["propertyLabel", row.joinType])
          ?.text || "--"
      );
    },
    formatPlayUrl(row) {
      if (!row.playUrl) return "--";
      if (row.joinType === "YSCloud") {
        return row.playUrl;
      } else {
        const values = Object.values(JSON.parse(row.playUrl));
        const hostname = location.hostname;
        values.forEach((item, index) => {
          values[index] = item.replace(/\?/, `${hostname}`);
        });
        return values.join("<br />");
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .header {
    display: flex;
    .nodeName {
      flex: 1;
      @include line_height(J4);
    }
  }
}
.row-handle {
  cursor: pointer;
  @include font_color(ZS);
}
.more {
  @include line_height(Hm);
}
</style>
