<template>
  <div class="page eem-common flex-column">
    <div class="backPlatform" v-if="isJump" @click="jumpBack">
      <i class="el-icon-d-arrow-left"></i>
      {{ $T("返回") }}
    </div>
    <el-container class="fullheight">
      <el-aside width="315px" class="eem-aside flex-column">
        <customElSelect
          v-model="ElSelect_1.value"
          v-bind="ElSelect_1"
          v-on="ElSelect_1.event"
          class="mbJ1"
          :prefix_in="$T('能源类型')"
        >
          <ElOption
            v-for="item in ElOption_1.options_in"
            :key="item[ElOption_1.key]"
            :label="item[ElOption_1.label]"
            :value="item[ElOption_1.value]"
            :disabled="item[ElOption_1.disabled]"
          ></ElOption>
        </customElSelect>
        <el-input
          suffix-icon="el-icon-search"
          class="mbJ3"
          :placeholder="$T('输入关键字以检索')"
          v-model="filterText"
        ></el-input>
        <div class="flex-auto">
          <el-tree
            style="height: 100%"
            ref="tree"
            class="tree"
            :default-expanded-keys="expandkey"
            :data="dimTree.treeData"
            :props="dimTree.treeProps"
            :filter-node-method="filterNode"
            node-key="tree_id"
            :show-checkbox="dimTree.showCheckbox"
            :check-strictly="dimTree.checkStrictly"
            :highlight-current="true"
            :expand-on-click-node="false"
            @node-click="nodeClick_out"
            @check="CetTree_1_checkedNodes_out"
          ></el-tree>
        </div>
      </el-aside>
      <el-container class="padding0 fullheight mlJ3 flex-column">
        <div class="eem-container lh32" style="overflow: auto">
          <el-dropdown
            class="mrJ1 fl"
            placement="bottom-start"
            v-for="(item, i) in dropdownData"
            :key="i"
            trigger="click"
            @visible-change="handleVisible($event, i)"
          >
            <div class="dropdownBox clearfix">
              <div class="text text-ellipsis fl" :title="item.propertyText">
                {{ item.propertyText }}
              </div>
              <i class="el-icon-arrow-down fr"></i>
            </div>
            <el-dropdown-menu slot="dropdown">
              <div class="el-input el-input--small">
                <input
                  v-model="item.input"
                  type="text"
                  class="el-input__inner"
                  autocomplete="off"
                  placeholder="请输入搜索内容"
                  @input="filterLv(i)"
                  @compositionstart="inputStart"
                  @compositionend="inputEnd"
                />
              </div>
              <el-checkbox
                class="lh32"
                :indeterminate="item.isIndeterminate"
                v-model="item.checkAll"
                v-show="item.checkboxArr.length > 0"
                @change="handleCheckAll($event, i)"
              >
                全选
              </el-checkbox>
              <el-checkbox-group
                v-model="item.checkedArr"
                @change="checkedChange($event, i)"
              >
                <el-checkbox
                  class="lh32"
                  v-for="list in item.checkboxArr"
                  :label="list.id"
                  :key="list.id"
                >
                  {{ list.name }}
                </el-checkbox>
              </el-checkbox-group>
              <el-dropdown-item class="fr mlJ1" :disabled="btnIsDisabled(i)">
                <el-button
                  size="mini"
                  type="primary"
                  @click="filterFun(i)"
                  :disabled="btnIsDisabled(i)"
                >
                  确认
                </el-button>
              </el-dropdown-item>
              <el-dropdown-item class="fr">
                <el-button size="mini">取消</el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown
            placement="bottom-start"
            trigger="click"
            style="height: 32px; width: 32px; float: left"
            @visible-change="handleDimenVisible"
          >
            <!-- <div class="change-drop"></div> -->
            <omega-icon
              symbolId="add-lin"
              class="change-drop"
              @click="handleTemplate(currentTemplate)"
            />
            <el-dropdown-menu class="eem-gp-node-drop" slot="dropdown">
              <el-checkbox-group v-model="dimension.check" class="check-box">
                <el-checkbox
                  style="margin-left: 0"
                  v-for="item in dimension.options"
                  class="lh32"
                  :label="item.propertyId"
                  :key="item.propertyId"
                >
                  {{ item.propertyText }}
                </el-checkbox>
              </el-checkbox-group>
              <p class="tip">{{ dimensionText }}</p>
              <el-dropdown-item class="fr mlJ1">
                <el-button
                  size="mini"
                  type="primary"
                  :disabled="dimenDis"
                  @click="confirmDimen"
                >
                  确认
                </el-button>
              </el-dropdown-item>
              <el-dropdown-item class="fr">
                <el-button size="mini">取消</el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div class="flex-auto eem-container mtJ3" style="overflow: auto">
          <div class="fullheight flex-column eem-min-width-mini">
            <div class="mbJ3 flex-row">
              <div class="flex-auto text-ellipsis">
                <el-tooltip
                  effect="light"
                  :content="nodeName"
                  placement="top-start"
                >
                  <span class="fsH2 lh32">
                    {{ nodeName || "--" }}
                  </span>
                </el-tooltip>
              </div>
              <div class="fr">
                <div class="fl">
                  <customElSelect
                    v-model="ElSelect_type.value"
                    v-bind="ElSelect_type"
                    v-on="ElSelect_type.event"
                    class="fl mrJ1"
                    :prefix_in="$T('分析类型')"
                  >
                    <ElOption
                      v-for="item in ElOption_type.options_in"
                      :key="item[ElOption_type.key]"
                      :label="item[ElOption_type.label]"
                      :value="item[ElOption_type.value]"
                      :disabled="item[ElOption_type.disabled]"
                    ></ElOption>
                  </customElSelect>
                  <ElCheckboxGroup
                    v-show="ElSelect_type.value === 1"
                    class="eem-checkbox fl mrJ1 lh32"
                    style="height: 32px"
                    v-model="ElCheckboxGroup_1.value"
                    v-bind="ElCheckboxGroup_1"
                    v-on="ElCheckboxGroup_1.event"
                  >
                    <ElCheckbox
                      v-for="item in ElCheckboxList_1.options_in"
                      :key="item[ElCheckboxList_1.key]"
                      :label="item[ElCheckboxList_1.label]"
                      :disabled="item[ElCheckboxList_1.disabled]"
                    >
                      {{ item[ElCheckboxList_1.text] }}
                    </ElCheckbox>
                  </ElCheckboxGroup>
                  <ElCheckboxGroup
                    v-show="ElSelect_type.value === 2"
                    class="eem-checkbox fl mrJ1 lh32"
                    style="height: 32px"
                    v-model="ElCheckboxGroup_2.value"
                    v-bind="ElCheckboxGroup_2"
                    v-on="ElCheckboxGroup_2.event"
                  >
                    <ElCheckbox
                      v-for="item in ElCheckboxList_2.options_in"
                      :key="item[ElCheckboxList_2.key]"
                      :label="item[ElCheckboxList_2.label]"
                      :disabled="item[ElCheckboxList_2.disabled]"
                    >
                      {{ item[ElCheckboxList_2.text] }}
                    </ElCheckbox>
                  </ElCheckboxGroup>
                </div>
                <CustomDatePicker
                  class="fl"
                  @change="CustomDatePicker_1_change"
                  :val="CustomDatePicker_1.queryTime"
                  :dataConfig="CustomDatePicker_1.dataConfig"
                ></CustomDatePicker>
                <!-- 请填写组件含义按钮组件 -->
                <CetButton
                  class="fl"
                  v-bind="CetButton_export"
                  v-on="CetButton_export.event"
                  :disable_in="
                    ElSelect_type.value === 3 && !copySelectNodes.length
                  "
                ></CetButton>
              </div>
            </div>
            <div style="flex: 1" class="minWH">
              <div style="height: 100%" v-show="ElSelect_type.value === 1">
                <CetChart
                  :inputData_in="CetChart_1.inputData_in"
                  v-bind="CetChart_1.config"
                />
              </div>
              <div style="height: 100%" v-show="ElSelect_type.value === 2">
                <CetChart
                  @click="chartClick2"
                  ref="CetChart_2"
                  :inputData_in="CetChart_2.inputData_in"
                  v-bind="CetChart_2.config"
                />
              </div>
              <div
                style="height: 100%"
                v-show="ElSelect_type.value === 3"
                class="flex-column"
              >
                <div class="text-right">
                  <el-radio-group v-model="chartType" @change="chartTypeChange">
                    <el-radio-button :label="1">
                      <omega-icon class="cetIcon" symbolId="bar" />
                    </el-radio-button>
                    <el-radio-button :label="2">
                      <omega-icon class="cetIcon" symbolId="line" />
                    </el-radio-button>
                  </el-radio-group>
                </div>
                <CetChart
                  class="flex-auto"
                  :inputData_in="CetChart_3.inputData_in"
                  v-bind="CetChart_3.config"
                />
              </div>
            </div>
          </div>
        </div>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import common from "eem-utils/common";
import CustomDatePicker from "./CustomDatePicker.vue";
import { httping } from "@omega/http";
export default {
  name: "EnergyAttributionAnalysis",
  components: {
    CustomDatePicker
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    userInfo() {
      var vm = this;
      return vm.$store.state.userInfo;
    },
    // 选择框确认按钮状态
    btnIsDisabled: function () {
      return function (i) {
        return this.dropdownData[i].checkedArr.length === 0;
      };
    },
    // 维度选择框确认按钮状态
    dimenDis() {
      return this.dimension.check.length === 0;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    numberOfNodesCompared() {
      return this.$store.state.systemCfg.numberOfNodesCompared || 4;
    }
  },

  data() {
    return {
      chartType: 1,
      isJump: false,
      keepParams: {},
      parentNode: null,
      nodeName: "",
      expandkey: [], //初始化展开的节点树id
      filterText: "", //节点树过滤输入框
      dimension: {
        options: [],
        oldCheck: [],
        check: []
      },
      dimensionText: "", //维度文本
      dropdownData: [],
      filterData: [],
      allNum: 0,
      flag: true,
      dimTree: {
        treeData: [],
        showCheckbox: false,
        checkStrictly: true,
        treeProps: {
          children: "children",
          label: "nodeName"
        }
      },
      ElSelect_1: {
        value: null,
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // 本地存储
      storageDimAttribution: {
        dimension: null
      },
      // type组件
      ElSelect_type: {
        value: 1,
        style: {
          width: "220px"
        },
        event: {
          change: this.ElSelect_type_change_out
        }
      },
      // type组件
      ElOption_type: {
        options_in: [
          {
            id: 1,
            text: $T("同比环比")
          },
          {
            id: 2,
            text: $T("分时统计")
          },
          {
            id: 3,
            text: $T("节点对比")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElCheckboxGroup_1: {
        value: [],
        style: {},
        event: {
          change: this.ElCheckboxGroup_1_change_out
        }
      },
      ElCheckboxList_1: {
        options_in: [
          {
            id: 1,
            disabled: false,
            text: $T("同比")
          },
          {
            id: 2,
            disabled: false,
            text: $T("环比")
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      ElCheckboxGroup_2: {
        value: [0],
        style: {},
        event: {
          change: this.ElCheckboxGroup_2_change_out
        }
      },
      ElCheckboxList_2: {
        options_in: [
          {
            id: 0,
            text: $T("显示分时数据")
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      CustomDatePicker_1: {
        queryTime: {
          startTime: null,
          endTime: null,
          cycle: 14 //17年，14月，12日，20自定义
        },
        dataConfig: {
          time: null,
          cycle: 3,
          showPicker: true,
          showRange: true,
          type: [
            {
              id: 1,
              text: $T("日"),
              type: "date"
            },
            {
              id: 3,
              text: $T("月"),
              type: "month"
            },
            {
              id: 5,
              text: $T("年"),
              type: "year"
            },
            {
              id: 11,
              text: $T("自定义"),
              type: "date"
            }
          ]
        }
      },
      CetButton_export: {
        visible_in: true,
        // disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      CetChart_1: {
        inputData_in: null,
        config: {
          options: {}
        }
      },
      CetChart_2: {
        inputData_in: null,
        config: {
          options: {}
        }
      },
      CetChart_3: {
        inputData_in: null,
        config: {
          options: {}
        }
      },
      unit: "",
      copySelectNodes: [] //拷贝节点选中列表，按照顺序排序
    };
  },
  watch: {
    // 维度切换
    "dimension.check": {
      handler: function (value) {
        var str = [];
        for (var i = 0; i < value.length; i++) {
          for (var j = 0; j < this.dimension.options.length; j++) {
            if (value[i] === this.dimension.options[j].propertyId) {
              str.push(this.dimension.options[j].propertyText);
            }
          }
        }
        this.dimensionText = str.join(" - ");
      },
      immediate: true
    },

    "ElSelect_1.value": {
      handler: function (value) {
        this.unit = this.getUnit_out(value);
      }
    },
    //本地存储监听
    storageDimAttribution: {
      handler: function (newStorage) {
        if (!window.sessionStorage) {
          console.log("浏览器不支持localStorage");
        } else {
          var storage = window.sessionStorage;
          var str = JSON.stringify(newStorage);
          storage.setItem("storageDimAttribution", str);
        }
      },
      deep: true
    },
    // 监听节点树输入框
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },

  methods: {
    //返回查询页
    jumpBack() {
      this.$router.push({
        name: "electricityDataRank",
        params: {
          keepParams: this.keepParams
        }
      });
    },
    // 维度确认
    confirmDimen: function () {
      this.dimTree.treeData = [];
      this.dimension.oldCheck = this.dimension.check;
      this.dropdownData = [];
      this.filterData = [];
      var data = this.dimension.options;
      var check = this.dimension.check;
      var dropdownData = [];
      this.allNum = check.length;
      for (var i = 0; i < check.length; i++) {
        for (var j = 0; j < data.length; j++) {
          if (data[j].propertyId === check[i]) {
            var str = this.getAttrArr(data[j].dropDownList, "id");
            // 更新维度列表
            dropdownData.push(data[j]);
            this.filterData.push(str);
            break;
          }
        }
      }
      this.$nextTick(function () {
        this.dropdownData = dropdownData;
        this.notFilter();
      });
      // 本地存储维度数据
      this.storageDimAttribution.dimension = this.dimension.check;
      this.getNodeTree_out();
    },
    // 获取维度
    dimData() {
      var _this = this;
      httping({
        url: "/eem-service/v1/dim/setting/listWithDropDown",
        method: "get"
      }).then(res => {
        if (res.code === 0) {
          if (res.data && res.data.length > 0) {
            var data = res.data;
            var len = data.length;
            var str = _this.getAttrArr(data[0].dropDownList, "id"); //维度过滤文本
            _this.allNum = 1;
            // 将接口数据改为需要的数据结构
            for (var i = 0; i < len; i++) {
              data[i].dropDownList = data[i].dropDownList
                ? data[i].dropDownList
                : []; // 维度数据
              data[i].input = ""; // 维度搜索框
              data[i].checkAll = true; // 维度是否全选的按钮状态
              data[i].isIndeterminate = false; // 维度是否全选的按钮样式
              data[i].checkboxArr = data[i].dropDownList; // 一直显示的维度
              data[i].checkedArr = _this.getAttrArr(data[i].dropDownList, "id"); // 一直显示的维度id
              data[i].nofilterCheckboxArr = data[i].dropDownList; // 未过滤的维度
              data[i].nofilterCheckedArr = _this.getAttrArr(
                data[i].dropDownList,
                "id"
              ); // 未过滤的维度id
              data[i].filterCheckboxArr = data[i].dropDownList; // 过滤的维度
              data[i].filterCheckedArr = _this.getAttrArr(
                data[i].dropDownList,
                "id"
              ); // 过滤的维度id
            }
            // 维度赋值
            _this.dimension.options = data;
            // 判断session存储是否属于维度数据
            var localSelected = _this.storageDimAttribution.dimension;
            var hasSelect = false;
            var allIdArr = data.map(function (item) {
              return item.propertyId;
            });

            if (localSelected) {
              hasSelect = localSelected.every(function (item) {
                return allIdArr.indexOf(item) !== -1;
              });
            }
            // 跑if分支，本地存储有值，则选中本地存储，否则选中数据第一个
            if (_this.$route.params?.keepParams?.type === "jumpQueryParams") {
              _this.dimension.check =
                _this.$route.params.keepParams.dimensionCheck;
              _this.dimension.oldCheck =
                _this.$route.params.keepParams.dimensionCheck;
              _this.confirmDimen();
            } else if (hasSelect) {
              _this.dimension.check = localSelected;
              _this.dimension.oldCheck = localSelected;
              _this.confirmDimen();
            } else {
              _this.dimension.check = [data[0].propertyId];
              _this.dimension.oldCheck = [data[0].propertyId];
              _this.dropdownData = [data[0]];
              _this.filterData = [str];
              _this.storageDimAttribution.dimension = _this.dimension.check;
              _this.getNodeTree_out();
            }
          }
        }
      });
    },
    // 节点树过滤触发函数
    filterFun: function (i) {
      this.filterData[i] = this.dropdownData[i].checkedArr;
      this.dropdownData[i].nofilterCheckedArr = this.dropdownData[i].checkedArr;
      this.$refs.tree.filter(this.filterData);
    },
    // 取消过滤
    notFilter: function () {
      for (var i = 0; i < this.dropdownData.length; i++) {
        this.dropdownData[i].nofilterCheckedArr = this.getAttrArr(
          this.dropdownData[i].dropDownList,
          "id"
        );
        this.dropdownData[i].checkedArr = this.getAttrArr(
          this.dropdownData[i].dropDownList,
          "id"
        );
      }
      this.$refs.tree.filter([]);
    },
    // 节点树过滤逻辑函数 value:过滤参数，data:当前节点，node:当前节点所有信息
    filterNode: function (value, data, node) {
      var startNum = 0;
      if (value.length === 0) {
        return true;
      }
      if (node.isLeaf) {
        // 搜索框过滤 和 维度过滤分离
        if (typeof value === "string") {
          return this.strJudgeParentNode(node, value);
        } else {
          startNum = this.judgeParentNode(node.parent, 0);
          return startNum === this.allNum;
        }
      }
    },
    // 维度过滤：判断子节点是否属于过滤节点，即判断该子节点的各个父节点是否和fileterData一一对应(选择过滤)
    judgeParentNode: function (node, startNum) {
      var index = node.level - 1;
      if (index >= 0) {
        for (var i = 0; i < this.filterData.length; i++) {
          var str = this.filterData[i];
          if (str.indexOf(node.data.nodeId) !== -1) {
            startNum++;
          }
        }
        return this.judgeParentNode(node.parent, startNum);
      } else {
        return startNum;
      }
    },
    // 搜索框过滤：判断关键字是否出现在父节点上(搜索过滤)
    strJudgeParentNode: function (node, val) {
      if (node.level === 0) {
        return false;
      } else {
        if (node.label.indexOf(val) !== -1) {
          return true;
        } else {
          return this.strJudgeParentNode(node.parent, val);
        }
      }
    },
    // 层级关键字过滤
    filterLv: function (i) {
      if (this.flag) {
        var val = this.dropdownData[i].input;
        if (val) {
          this.dropdownData[i].filterCheckboxArr = [];
          this.dropdownData[i].filterCheckedArr = [];

          var checkArr = this.dropdownData[i].dropDownList;
          for (var j = 0; j < checkArr.length; j++) {
            // var name = checkArr[j]["name"];
            if (checkArr[j].name.indexOf(val) !== -1) {
              this.dropdownData[i].filterCheckboxArr.push(checkArr[j]);
              this.dropdownData[i].filterCheckedArr.push(checkArr[j].id);
            }
          }
          if (this.dropdownData[i].filterCheckboxArr.length !== 0) {
            //找到了
            this.dropdownData[i].checkboxArr =
              this.dropdownData[i].filterCheckboxArr;
            this.dropdownData[i].checkedArr =
              this.dropdownData[i].filterCheckedArr;
            this.dropdownData[i].checkAll = true;
            this.dropdownData[i].isIndeterminate = false;
          } else {
            //没找到
            this.dropdownData[i].checkboxArr = [];
            this.dropdownData[i].checkedArr = [];
          }
        } else {
          this.dropdownData[i].checkboxArr =
            this.dropdownData[i].nofilterCheckboxArr;
          this.dropdownData[i].checkedArr =
            this.dropdownData[i].nofilterCheckedArr;
          this.dropdownData[i].checkAll =
            this.dropdownData[i].checkedArr.length ===
            this.dropdownData[i].checkboxArr.length;
          this.dropdownData[i].isIndeterminate =
            this.dropdownData[i].checkedArr.length !==
            this.dropdownData[i].checkboxArr.length;
        }
      }
    },
    // 从数组的对象里取出某个属性组成新数组,data-数组参数,attr-对象属性
    getAttrArr: function (data, attr) {
      if (Array.isArray(data) && data.length > 0) {
        var arr = data.map(function (item) {
          return item[attr];
        });
        return arr;
      } else {
        return [];
      }
    },
    // 获取树节点
    getNodeTree_out() {
      if (this.dimension.check.length === 0) {
        return;
      }
      var queryTime = this.CustomDatePicker_1.queryTime;
      let startTime = queryTime.startTime;
      let endTime = queryTime.endTime;
      let measureLabelList = this.$store.state.systemCfg.measureLabelList || [
        "sectionarea",
        "building",
        "floor",
        "room",
        "manuequipment"
      ];
      var _this = this;
      if (this.systemCfg.hideHistoryBtn) {
        // 如果没有历史记录，入参固定为当月开始结束时间
        startTime = this.$moment().startOf("month").valueOf();
        endTime = this.$moment().endOf("month").valueOf() + 1;
      }
      var queryBody = {
        measureLabelList: measureLabelList,
        dimIds: this.dimension.check,
        measureLabel: "manuequipment",
        projectId: this.projectId,
        system: "cloud",
        endTime: endTime || this.$moment().endOf("day").valueOf() + 1,
        startTime: startTime || this.$moment("1900-01-01 00:00:00").valueOf(),
        energyType: this.ElSelect_1.value
      };
      httping({
        url: "/eem-service/v1/dim/setting/nodeTree/time",
        method: "post",
        data: queryBody
      }).then(res => {
        if (res.code === 0) {
          if (res.data && res.data.length > 0) {
            _this.dimTree.treeData = res.data;

            if (
              _this.$route.params?.keepParams?.dimensionType &&
              _this.jumpFilter
            ) {
              _this.filterData = [_this.$route.params.keepParams.dimensionType];
              _this.dropdownData[0].checkedArr =
                _this.$route.params.keepParams.dimensionType;
              _this.checkedChange(
                _this.$route.params.keepParams.dimensionType,
                0
              );
              _this.$nextTick(() => {
                _this.filterFun(0);
                let selectFirst = true;
                res.data.forEach(item => {
                  if (
                    _this.$refs.tree.getNode(item?.tree_id)?.visible &&
                    selectFirst
                  ) {
                    _this.$refs.tree.setCurrentKey(item.tree_id);
                    var node = _this.$refs.tree.getCurrentNode();
                    _this.nodeClick_out(node);
                    selectFirst = false;
                  }
                });
              });
              _this.jumpFilter = false;
            } else {
              //根据issue：51759；调整为默认选中第一个节点
              let key = _this._.get(res, "data[0].tree_id", null);
              let expandkey = _this._.get(res, "data[0].tree_id", null);
              //根据现场杨从戎反馈，切换时间查询节点树，需要默认选中之前选中节点；
              if (_this.clickNode) {
                key = _this.clickNode?.tree_id;
                expandkey = key;
              }
              if (!key) {
                return;
              }
              _this.$nextTick(function () {
                _this.$refs.tree.setCurrentKey(key);
                var node = _this.$refs.tree.getCurrentNode();
                _this.expandkey = [expandkey];
                _this.nodeClick_out(node);
              });
            }
          } else {
            _this.dimTree.treeData = [];
            _this.nodeName = null;
            _this.clickNode = null;
            _this.parentNode = null;
            _this.filChartData1([]);
            _this.filChartData2([]);
            _this.filChartData3([]);
          }
        }
      });
    },
    // 层级的全选
    handleCheckAll: function (val, i) {
      var allCheck = this.getAttrArr(this.dropdownData[i].dropDownList, "id");
      this.dropdownData[i].checkedArr = val ? allCheck : [];
      this.dropdownData[i].isIndeterminate = false;
    },
    // 层级的多选
    checkedChange: function (value, i) {
      var count = value.length;
      this.dropdownData[i].checkAll =
        count === this.dropdownData[i].checkboxArr.length;
      this.dropdownData[i].isIndeterminate =
        count > 0 && count < this.dropdownData[i].checkboxArr.length;
    },
    // 下拉框出现时，改变层级选择，即取消后选择框回复功能
    handleVisible: function (visible, i) {
      if (visible) {
        this.dropdownData[i].checkboxArr =
          this.dropdownData[i].nofilterCheckboxArr;
        this.dropdownData[i].checkedArr =
          this.dropdownData[i].nofilterCheckedArr;
        this.dropdownData[i].input = "";
        this.dropdownData[i].checkAll =
          this.dropdownData[i].checkedArr.length ===
          this.dropdownData[i].checkboxArr.length;
        this.dropdownData[i].isIndeterminate =
          this.dropdownData[i].checkedArr.length !==
          this.dropdownData[i].checkboxArr.length;
      }
    },
    // 维度下拉选择框出现时
    handleDimenVisible: function (visible) {
      if (!visible) {
        this.dimension.check = this.dimension.oldCheck;
      }
    },
    // 输入框防抖
    inputStart: function () {
      this.flag = false;
    },
    // 输入框防抖
    inputEnd: function () {
      this.flag = true;
    },
    // 获取localStorage的值
    getLocalStorage: function () {
      if (!window.sessionStorage) {
        return false;
      } else {
        var storage = window.sessionStorage;
        var json = storage.getItem("storageDimAttribution");
        if (json) {
          var jsonObj = JSON.parse(json);
          this.storageDimAttribution = jsonObj;
        }
      }
    },
    nodeClick_out(val) {
      if (val) {
        this.nodeName = val.nodeName;
        this.clickNode = val;
        var node = this.$refs.tree.getNode(val.tree_id);
        this.parentNode = node.parent.data;
        if (this.ElSelect_type.value === 3 && !this.systemCfg.hideHistoryBtn) {
          this.$refs.tree.setCheckedKeys([val.tree_id]);
        }
        if (this.ElSelect_type.value === 1) {
          this.getChartData1();
        } else if (this.ElSelect_type.value === 2) {
          this.getChartData2();
        } else if (this.ElSelect_type.value === 3) {
          if (!this.systemCfg.hideHistoryBtn) {
            this.copySelectNodes = [this.clickNode];
          }
          this.getChartData3();
        }
      } else {
        this.clickNode = {};
        this.$message.warning("请选择节点！");
      }
    },
    ElSelect_1_change_out(val) {
      if (!val) {
        return;
      }
      this.getNodeTree_out();
      this.unit = this.getUnit_out(val.id);
      // if (this.ElSelect_type.value === 1) {
      //   this.getChartData1();
      // } else if (this.ElSelect_type.value === 2) {
      //   this.getChartData2();
      // } else if (this.ElSelect_type.value === 3) {
      //   this.getChartData3();
      // }
    },
    ElSelect_type_change_out(val) {
      if (!val) {
        return;
      }
      const timenum = {
        12: 1,
        14: 3,
        17: 5,
        20: 11
      };
      if (val === 2) {
        this.CustomDatePicker_1.dataConfig.type = [
          {
            id: 3,
            text: $T("月"),
            type: "month"
          },
          {
            id: 5,
            text: $T("年"),
            type: "year"
          },
          {
            id: 11,
            text: $T("自定义"),
            type: "date"
          }
        ];
        if (timenum[this.CustomDatePicker_1.queryTime.cycle] === 1) {
          this.CustomDatePicker_1.dataConfig.cycle = 3;
          this.CustomDatePicker_1.dataConfig.time =
            this.CustomDatePicker_1.queryTime.startTime;
          return;
        } else {
          this.CustomDatePicker_1.dataConfig.cycle =
            timenum[this.CustomDatePicker_1.queryTime.cycle];
        }
      } else {
        this.CustomDatePicker_1.dataConfig.type = [
          {
            id: 1,
            text: $T("日"),
            type: "date"
          },
          {
            id: 3,
            text: $T("月"),
            type: "month"
          },
          {
            id: 5,
            text: $T("年"),
            type: "year"
          },
          {
            id: 11,
            text: $T("自定义"),
            type: "date"
          }
        ];
        this.CustomDatePicker_1.dataConfig.cycle =
          timenum[this.CustomDatePicker_1.queryTime.cycle];
      }
      this.CustomDatePicker_1.dataConfig.time =
        this.CustomDatePicker_1.dataConfig.cycle === 11
          ? [
              this.CustomDatePicker_1.queryTime.startTime,
              this.CustomDatePicker_1.queryTime.endTime
            ]
          : this.CustomDatePicker_1.queryTime.startTime;

      if (val === 3) {
        this.dimTree.showCheckbox = true;
      } else {
        this.dimTree.showCheckbox = false;
      }
      if (val === 1) {
        this.getChartData1();
      } else if (val === 2) {
        this.getChartData2();
      } else if (val === 3) {
        if (this.clickNode) {
          this.$refs.tree.setCheckedKeys([this.clickNode.tree_id]);
          this.copySelectNodes = [this.clickNode];
          this.getChartData3();
        }
      }
    },
    ElCheckboxGroup_1_change_out() {
      if (this.ElSelect_type.value === 1) {
        this.getChartData1();
      } else if (this.ElSelect_type.value === 2) {
        this.getChartData2();
      } else if (this.ElSelect_type.value === 3) {
        this.getChartData3();
      }
    },
    ElCheckboxGroup_2_change_out() {
      this.getChartData2();
    },
    //导出
    CetButton_export_statusTrigger_out() {
      var urlStr = "";
      var params = {};
      if (this.ElSelect_type.value === 1) {
        urlStr = "/eem-service/v1/dimEnergy/energy/export/1";
        params = this.getParams(1);
      } else if (this.ElSelect_type.value === 2) {
        urlStr = "/eem-service/v1/dimEnergy/energy/export/2";
        params = this.getParams(2);
      } else if (this.ElSelect_type.value === 3) {
        urlStr = "/eem-service/v1/dimEnergy/energy/export/3";
        params = this.getParams(3);
      }
      if (!params) {
        return;
      }
      common.downExcel(urlStr, params, this.token);
    },
    //同比环比
    getChartData1() {
      var _this = this;
      var queryBody = this.getParams(1);
      if (!queryBody || !queryBody.energyType) {
        _this.filChartData1([]);
        return;
      }
      this.CetChart_1.config.options = {};
      var queryOption = {
        url: `/eem-service/v1/dimEnergy/tbhb`,
        method: "POST",
        data: queryBody
      };

      httping(queryOption).then(function (response) {
        if (response.code === 0) {
          //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
          // var data = _this._.get(response, ["data","1"], []);

          _this.filChartData1(response.data);
        }
      });
    },
    filChartData1(data) {
      var _this = this;
      var cycle = this.CustomDatePicker_1.queryTime.cycle;
      this.CetChart_1.config.options = {
        toolbox: {
          top: 40,
          right: 30,
          feature: {
            saveAsImage: {}
          }
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          },
          formatter: function (val) {
            var list = val || [];
            var formatterStr = "";
            for (var i = 0, len = list.length; i < len; i++) {
              if (i === 0) {
                formatterStr += `${
                  val[i].name
                } <br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                  val[i].color
                };"></span>${
                  cycle === 20
                    ? val[i].seriesName
                    : common.formatSeriesName(val[i].data, cycle)
                } : ${val[i].value || "--"}(${val[i].data.unit || "--"})`;
              } else {
                formatterStr += `<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                  val[i].color
                };"></span>${
                  cycle === 20
                    ? val[i].seriesName
                    : common.formatSeriesName(val[i].data, cycle)
                } : ${val[i].value || "--"}(${val[i].data.unit || "--"})`;
              }
            }
            return formatterStr;
          }
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true
        },
        legend: {
          type: "scroll",
          data: []
        },
        xAxis: {
          type: "category",
          data: []
        },
        yAxis: {
          type: "value",
          min: 0,
          max: function (value) {
            if (value.max < 0) {
              return 0;
            }
            return null;
          }
        },
        series: []
      };
      var currentdata = data.currentdata || [];
      var tbdata = data.tbdata || [];
      var hbdata = data.hbdata || [];
      if (currentdata.length === 0) {
        return;
      }
      var xAxisData = [];
      var yAxisData1 = [];
      var yAxisData2 = [];
      var yAxisData3 = [];
      let unit = data.symbol || _this.unit;
      unit = common.formatSymbolStr(unit);
      var leng = currentdata.length;
      var customIndex = 1;
      var customTime = currentdata[leng - 1].time - currentdata[0].time;
      if (customTime < 1000 * 3600 * 24 * 3) {
        customIndex = 1;
      } else if (customTime < 1000 * 3600 * 24 * 30 * 3) {
        customIndex = 2;
      } else {
        customIndex = 3;
      }
      var source = [];
      for (var i = 0, len1 = currentdata.length; i < len1; i++) {
        var value1 = common.formatNumberWithPrecision(currentdata[i].value, 2);
        var value2 = tbdata[i]
          ? common.formatNumberWithPrecision(tbdata[i].value, 2)
          : null;
        var value3 = hbdata[i]
          ? common.formatNumberWithPrecision(hbdata[i].value, 2)
          : null;
        var product = this.getAxixs(currentdata[i].time, cycle, customIndex);
        var sour = {
          time: currentdata[i].time,
          unit: unit,
          product: product,
          yAxis1: value1,
          yAxis2: value2,
          yAxis3: value3
        };
        source.push(sour);
        xAxisData.push(product);
        yAxisData1.push({
          time: currentdata[i].time,
          unit: unit,
          name: product,
          value: value1
        });
        yAxisData2.push({
          time: currentdata[i].time,
          hb: true,
          unit: unit,
          name: product,
          value: value2
        });
        yAxisData3.push({
          time: currentdata[i].time,
          tb: true,
          unit: unit,
          name: product,
          value: value3
        });
      }

      var series = [];
      var legend = [];
      var currentName = "";
      var tbName = "";
      var hbName = "";
      var CheckedArray = this.ElCheckboxGroup_1.value || [];
      var len = CheckedArray.length;
      var queryType;
      if (len === 0) {
        queryType = 0;
      } else if (len === 1) {
        queryType = CheckedArray[0];
      } else if (len === 2) {
        queryType = 3;
      }
      if (cycle === 17 && len !== 0) {
        queryType = 1;
      } else if (cycle === 20) {
        queryType = 0;
      }
      let customLegendName = "";
      if (cycle === 20) {
        var queryTime = this.CustomDatePicker_1.queryTime;
        var startTime = queryTime.startTime;
        var endTime = queryTime.endTime;
        customLegendName =
          this.getLegend(startTime, cycle) +
          "~" +
          this.getLegend(endTime, cycle);
      }
      //1:同比；2：环比；3：同比环比
      if (queryType === 0) {
        currentName = this.getLegend(currentdata[0].time, cycle);
        if (cycle === 20) {
          currentName = customLegendName;
        }
        series = [
          {
            name: currentName,
            type: "bar",
            smooth: true,
            barWidth: "60%",
            data: yAxisData1
          }
        ];
        legend = [currentName];
      } else if (queryType === 1) {
        currentName = this.getLegend(currentdata[0].time, cycle);

        if (cycle === 12) {
          const xAxisTime = this.$moment(currentdata[0].time)
            .subtract(1, "M")
            .valueOf();
          tbName = this.getLegend(xAxisTime, cycle);
        } else if (cycle === 14) {
          const xAxisTime = this.$moment(currentdata[0].time)
            .subtract(1, "Y")
            .valueOf();
          tbName = this.getLegend(xAxisTime, cycle);
        } else if (cycle === 17) {
          const xAxisTime = this.$moment(currentdata[0].time)
            .subtract(1, "Y")
            .valueOf();
          tbName = this.getLegend(xAxisTime, cycle);
        }

        series = [
          {
            name: currentName,
            type: "bar",
            smooth: true,
            barWidth: "60%",
            data: yAxisData1
          },
          {
            name: tbName,
            type: "line",
            smooth: true,
            data: yAxisData2
          }
        ];
        legend = [currentName, tbName];
      } else if (queryType === 2) {
        currentName = this.getLegend(currentdata[0].time, cycle);
        if (cycle === 12) {
          const xAxisTime = this.$moment(currentdata[0].time)
            .subtract(1, "d")
            .valueOf();
          hbName = this.getLegend(xAxisTime, cycle);
        } else if (cycle === 14) {
          const xAxisTime = this.$moment(currentdata[0].time)
            .subtract(1, "M")
            .valueOf();
          hbName = this.getLegend(xAxisTime, cycle);
        } else if (cycle === 17) {
          const xAxisTime = this.$moment(currentdata[0].time)
            .subtract(1, "Y")
            .valueOf();
          hbName = this.getLegend(xAxisTime, cycle);
        }

        series = [
          {
            name: currentName,
            type: "bar",
            smooth: true,
            barWidth: "60%",
            data: yAxisData1
          },
          {
            name: hbName,
            type: "line",
            smooth: true,
            data: yAxisData3
          }
        ];
        legend = [currentName, hbName];
      } else if (queryType === 3) {
        currentName = this.getLegend(currentdata[0].time, cycle);
        if (cycle === 12) {
          const xAxisTime = this.$moment(currentdata[0].time)
            .subtract(1, "M")
            .valueOf();
          tbName = this.getLegend(xAxisTime, cycle);
        } else if (cycle === 14) {
          const xAxisTime = this.$moment(currentdata[0].time)
            .subtract(1, "Y")
            .valueOf();
          tbName = this.getLegend(xAxisTime, cycle);
        } else if (cycle === 17) {
          const xAxisTime = this.$moment(currentdata[0].time)
            .subtract(1, "Y")
            .valueOf();
          tbName = this.getLegend(xAxisTime, cycle);
        }

        if (cycle === 12) {
          const xAxisTime = this.$moment(currentdata[0].time)
            .subtract(1, "d")
            .valueOf();
          hbName = this.getLegend(xAxisTime, cycle);
        } else if (cycle === 14) {
          const xAxisTime = this.$moment(currentdata[0].time)
            .subtract(1, "M")
            .valueOf();
          hbName = this.getLegend(xAxisTime, cycle);
        } else if (cycle === 17) {
          const xAxisTime = this.$moment(currentdata[0].time)
            .subtract(1, "Y")
            .valueOf();
          hbName = this.getLegend(xAxisTime, cycle);
        }
        series = [
          {
            name: currentName,
            type: "bar",
            smooth: true,
            barWidth: "60%",
            data: yAxisData1
          },
          {
            name: tbName,
            type: "line",
            smooth: true,
            data: yAxisData2
          },
          {
            name: hbName,
            type: "line",
            smooth: true,
            data: yAxisData3
          }
        ];
        legend = [currentName, tbName, hbName];
      }
      //2022-9-5 根据何秋平反馈，不对负值转为0处理
      // series[0].data.forEach(item => {
      //   if (Number(item.value) < 0) {
      //     item.value = 0;
      //   }
      // });
      this.$nextTick(function () {
        var ElOption1Text;
        if (
          _this.ElOption_1.options_in.filter(
            item => item.id === _this.ElSelect_1.value
          ).length > 0
        ) {
          ElOption1Text = _this.ElOption_1.options_in.filter(
            item => item.id === _this.ElSelect_1.value
          )[0].text;
        }
        _this.CetChart_1.config.options.yAxis.name = ElOption1Text
          ? `${ElOption1Text}（${unit})`
          : "--";
        _this.CetChart_1.config.options.xAxis.data = xAxisData;
        _this.CetChart_1.config.options.series = series;
        _this.CetChart_1.config.options.legend.data = legend;
      });
    },
    //分时统计
    getChartData2() {
      var _this = this;
      var queryBody = this.getParams(2);
      if (!queryBody || !queryBody.energyType) {
        _this.filChartData2([]);
        return;
      }
      this.CetChart_2.config.options = {};
      var queryOption = {
        url: `/eem-service/v1/dimEnergy/timeshare`,
        method: "POST",
        data: queryBody
      };

      httping(queryOption).then(function (response) {
        if (response.code === 0) {
          //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
          var data = _this._.get(response, ["data", "0"], {});
          _this.filChartData2(data);
        }
      });
    },
    filChartData2(data) {
      var _this = this;
      var cycle = this.CustomDatePicker_1.queryTime.cycle;
      this.CetChart_2.config.options = {
        toolbox: {
          top: 40,
          right: 30,
          feature: {
            saveAsImage: {}
          }
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          },
          formatter: function (val) {
            var list = val || [];
            var formatterStr = "";
            for (var i = 0, len = list.length; i < len; i++) {
              if (i === 0) {
                formatterStr += `${
                  val[i].name
                } <br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                  val[i].color
                };"></span>${
                  cycle === 20 || _this.ElCheckboxGroup_2.value.length > 0
                    ? val[i].seriesName
                    : common.formatSeriesName(val[i].data, cycle)
                } : ${val[i].value || "--"}(${val[i].data.unit || "--"})`;
              } else {
                formatterStr += `<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                  val[i].color
                };"></span>${
                  cycle === 20 || _this.ElCheckboxGroup_2.value.length > 0
                    ? val[i].seriesName
                    : common.formatSeriesName(val[i].data, cycle)
                } : ${val[i].value || "--"}(${val[i].data.unit || "--"})`;
              }
            }
            return formatterStr;
          }
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true
        },
        legend: {
          type: "scroll",
          data: []
        },
        xAxis: {
          type: "category",
          data: []
        },
        yAxis: {
          type: "value",
          min: 0,
          max: function (value) {
            if (value.max < 0) {
              return 0;
            }
            return null;
          }
        },
        series: []
      };
      var energyDatas = data.energyDatas || [];
      var touDatas = data.touDatas || [];
      var legend = [];
      var series = [];
      if (energyDatas.length === 0) {
        return;
      }
      let unit = data.symbol || _this.unit;
      unit = common.formatSymbolStr(unit);
      var showTimeSharing = this.ElCheckboxGroup_2.value.length > 0;
      if (showTimeSharing) {
        var leng = energyDatas.length;
        var customIndex = 1;
        var customTime = energyDatas[leng - 1].time - energyDatas[0].time;
        if (customTime < 1000 * 3600 * 24 * 3) {
          customIndex = 1;
        } else if (customTime < 1000 * 3600 * 24 * 30 * 3) {
          customIndex = 2;
        } else {
          customIndex = 3;
        }
        var source = [];
        energyDatas.forEach(item => {
          var obj = {};
          obj.time = item.time;
          obj.product = this.getAxixs(item.time, cycle, customIndex);
          touDatas.forEach(item1 => {
            var tou = item1.tou || [];
            for (var i = 0, len = tou.length; i < len; i++) {
              if (item.time == tou[i].time) {
                obj[`yAxis${item1.segId}`] = common.formatNumberWithPrecision(
                  tou[i].value,
                  2
                );
              }
            }
          });
          source.push(obj);
        });

        var xAxisData = [];
        source.forEach(item => {
          xAxisData.push(item.product);
        });
        for (var i = 0, len = touDatas.length; i < len; i++) {
          var yAxisData = [];
          source.forEach(item => {
            // yAxisData.push(item[`yAxis${touDatas[i].segId}`]);
            yAxisData.push({
              name: item.product,
              time: item.time,
              unit: unit,
              value: item[`yAxis${touDatas[i].segId}`]
            });
          });
          var obj = {
            // name: touDatas[i].segName,
            name: touDatas[i].segId,
            type: "bar",
            stack: "总量",
            // encode: { x: "product", y: `yAxis${touDatas[i].segId}` }
            data: yAxisData
          };
          // legend.push(touDatas[i].segName);
          legend.push(touDatas[i].segId);
          series.push(obj);
        }
        if (series && series.length > 0) {
          series[0].data.forEach(item => {
            if (Number(item.value) < 0) {
              item.value = 0;
            }
          });
        }
        var ElOption1Text;
        if (
          _this.ElOption_1.options_in.filter(
            item => item.id === _this.ElSelect_1.value
          ).length > 0
        ) {
          ElOption1Text = _this.ElOption_1.options_in.filter(
            item => item.id === _this.ElSelect_1.value
          )[0].text;
        }
        this.CetChart_2.config.options.yAxis.name = ElOption1Text
          ? `${ElOption1Text}（${unit})`
          : "";
        this.CetChart_2.config.options.xAxis.data = xAxisData;
        this.CetChart_2.config.options.series = series;
        this.CetChart_2.config.options.legend.data = legend;
      } else {
        let xAxisData = [];
        let yAxisData1 = [];

        let leng = energyDatas.length;
        let customIndex = 1;
        let customTime = energyDatas[leng - 1].time - energyDatas[0].time;
        if (customTime < 1000 * 3600 * 24 * 3) {
          customIndex = 1;
        } else if (customTime < 1000 * 3600 * 24 * 30 * 3) {
          customIndex = 2;
        } else {
          customIndex = 3;
        }
        let source = [];
        energyDatas.forEach(item => {
          var product = this.getAxixs(item.time, cycle, customIndex);
          var value = common.formatNumberWithPrecision(item.value, 2);
          var obj = {};
          obj.time = item.time;
          obj.product = product;
          obj.yAxis = value;
          source.push(obj);
          xAxisData.push(product);
          yAxisData1.push({
            name: product,
            time: item.time,
            unit: unit,
            value: value
          });
        });
        var name = this.getLegend(energyDatas[0].time, cycle);
        if (cycle === 20) {
          let legendEnd = energyDatas[leng - 1].time;
          if (customIndex === 1) {
            legendEnd = legendEnd + 1000 * 3600;
          } else if (customIndex === 2) {
            legendEnd = legendEnd + 1000 * 3600 * 24;
          } else if (customIndex === 3) {
            legendEnd = legendEnd + 1000 * 3600 * 24 * 33;
          }
          name =
            this.getLegend(energyDatas[0].time, cycle) +
            "~" +
            this.getLegend(legendEnd, cycle);
        }
        let obj = {
          name: name,
          type: "bar",
          data: yAxisData1
        };
        legend.push(name);
        series.push(obj);
        series[0].data.forEach(item => {
          if (Number(item.value) < 0) {
            item.value = 0;
          }
        });
        let ElOption1Text;
        if (
          _this.ElOption_1.options_in.filter(
            item => item.id === _this.ElSelect_1.value
          ).length > 0
        ) {
          ElOption1Text = _this.ElOption_1.options_in.filter(
            item => item.id === _this.ElSelect_1.value
          )[0].text;
        }
        this.CetChart_2.config.options.yAxis.name = ElOption1Text
          ? `${ElOption1Text}（${unit})`
          : "";
        this.CetChart_2.config.options.xAxis.data = xAxisData;
        this.CetChart_2.config.options.series = series;
        this.CetChart_2.config.options.legend.data = legend;
      }
    },
    CetTree_1_checkedNodes_out(val, checkData) {
      const iNodeChecked = checkData.checkedNodes || [];
      if (iNodeChecked.length > this.numberOfNodesCompared) {
        this.$message.warning(
          $T("最多对比{0}个节点", this.numberOfNodesCompared)
        );
        let treeDatas = iNodeChecked.filter(
          item => item.tree_id !== val.tree_id
        );
        this.$refs.tree.setCheckedNodes(treeDatas);
        return;
      }
      if (this.ElSelect_type.value !== 3) {
        return;
      }
      if (iNodeChecked.length < 2) {
        this.copySelectNodes = this._.cloneDeep(iNodeChecked);
      } else {
        let isFindOk = iNodeChecked.find(item => item.tree_id === val.tree_id);
        if (isFindOk) {
          if (val.tree_id === this.clickNode.tree_id) {
            this.copySelectNodes.unshift(val);
          } else {
            this.copySelectNodes.push(val);
          }
        } else {
          this.copySelectNodes = this.copySelectNodes.filter(
            item => item.tree_id !== val.tree_id
          );
        }
      }
      this.getChartData3();
    },
    //节点对比
    getChartData3() {
      var _this = this;
      var queryBody = this.getParams(3);
      if (
        !queryBody ||
        queryBody.queryNodes.length == 0 ||
        !queryBody.energyType
      ) {
        _this.filChartData3([]);
        return;
      }
      this.CetChart_3.config.options = {};
      var queryOption = {
        url: `/eem-service/v1/dimEnergy/compare`,
        method: "POST",
        data: queryBody
      };

      httping(queryOption).then(function (response) {
        if (response.code === 0) {
          //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
          var data = _this._.get(response, ["data"], []);
          let sortData = [];
          _this.copySelectNodes.forEach(item => {
            let findObj = data.find(
              i => item.id === i.id && _this.getLabel(item) === i.modelLabel
            );
            if (findObj) {
              sortData.push(findObj);
            }
          });
          _this.filChartData3(sortData);
        }
      });
    },
    getLabel(node) {
      return node.nodeType === 1 ? node.levelNodeId : node.label;
    },
    //过滤节点对比图表数据
    filChartData3(data) {
      this.CetChart_3.config.options = {
        toolbox: {
          top: 40,
          right: 30,
          feature: {
            saveAsImage: {}
          }
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          },
          formatter: function (val) {
            var list = val || [];
            var formatterStr = "";
            for (var i = 0, len = list.length; i < len; i++) {
              if (i === 0) {
                formatterStr += `${
                  val[i].name
                } <br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                  val[i].color
                };"></span>${val[i].seriesName} : ${val[i].value || "--"}(${
                  val[i].data.unit || "--"
                })`;
              } else {
                formatterStr += `<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                  val[i].color
                };"></span>${val[i].seriesName} : ${val[i].value || "--"}(${
                  val[i].data.unit || "--"
                })`;
              }
            }
            return formatterStr;
          }
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true
        },
        legend: {
          type: "scroll",
          data: []
        },
        xAxis: {
          type: "category",
          data: []
        },
        yAxis: {
          type: "value",
          min: 0,
          max: function (value) {
            if (value.max < 0) {
              return 0;
            }
            return null;
          }
        },
        series: []
      };
      var _this = this;
      data = data || [];
      var id = 0;
      var label = "";
      var legend = [];
      var series = [];
      let unit = this._.get(data, "[0].symbol", "--") || _this.unit;
      unit = common.formatSymbolStr(unit);
      if (this.clickNode) {
        id = this.clickNode.id;
        label = this.getLabel(this.clickNode);
      } else {
        // this.$message.warning("请选择节点");
        return;
      }
      var list = _this.copySelectNodes || [];
      var isHasNode = false;
      list.forEach(item => {
        if (
          item.id === this.clickNode.id &&
          (item.label === label || this.getLabel(item) === label)
        ) {
          isHasNode = true;
        }
      });
      if (!isHasNode && list.length > 0) {
        id = list[0].id;
        label = this.getLabel(list[0]);
      }
      var cycle = this.CustomDatePicker_1.queryTime.cycle;
      var source = [];
      data.forEach(item => {
        var serie = {};
        if (item.id == id && item.modelLabel == label) {
          var itemData1 = item.data || [];
          var leng = itemData1.length;
          var customIndex = 1;
          var customTime = itemData1[leng - 1].time - itemData1[0].time;
          if (customTime < 1000 * 3600 * 24 * 3) {
            customIndex = 1;
          } else if (customTime < 1000 * 3600 * 24 * 30 * 3) {
            customIndex = 2;
          } else {
            customIndex = 3;
          }
          itemData1.forEach(item1 => {
            var obj = {};
            obj.time = item1.time;
            obj.product = this.getAxixs(item1.time, cycle, customIndex);
            obj[`yAxis${item.modelLabel}${item.id}`] =
              common.formatNumberWithPrecision(item1.value, 2);
            source.push(obj);
          });
          serie = {
            name: item.name,
            type: "bar",
            smooth: true,
            encode: { x: "product", y: `yAxis${item.modelLabel}${item.id}` }
          };
        } else {
          serie = {
            name: item.name,
            type: "line",
            smooth: true,
            encode: { x: "product", y: `yAxis${item.modelLabel}${item.id}` }
          };
        }
        legend.push(item.name);
        series.push(serie);
      });
      data.forEach(item => {
        if (item.modelLabel + item.id !== label + id) {
          var itemData1 = item.data || [];
          itemData1.forEach(item1 => {
            source.forEach(item2 => {
              if (item2.time == item1.time) {
                item2[`yAxis${item.modelLabel}${item.id}`] =
                  common.formatNumberWithPrecision(item1.value, 2);
              }
            });
          });
        }
      });

      var xAxisData = [];
      var cloneLegend = [];
      var cloneSeries = [];
      source.forEach(item => {
        xAxisData.push(item.product);
      });
      data.forEach(item => {
        var serie = {};
        var yAxisData = [];
        source.forEach(item11 => {
          // yAxisData.push(item11[`yAxis${item.modelLabel}${item.id}`]);
          yAxisData.push({
            name: item11.product,
            time: item11.time,
            unit: unit,
            value: item11[`yAxis${item.modelLabel}${item.id}`]
          });
        });
        if (item.id == id && item.modelLabel == label) {
          serie = {
            name: item.name,
            type: "bar",
            smooth: true,
            data: yAxisData
            // encode: { x: "product", y: `yAxis${item.modelLabel}${item.id}` }
          };
        } else {
          serie = {
            name: item.name,
            type: "line",
            smooth: true,
            data: yAxisData
            // encode: { x: "product", y: `yAxis${item.modelLabel}${item.id}` }
          };
        }
        cloneLegend.push(item.name);
        cloneSeries.push(serie);
      });

      // cloneSeries[0] &&
      //   cloneSeries[0].data.forEach(item => {
      //     if (Number(item.value) < 0) {
      //       item.value = 0;
      //     }
      //   });
      let ElOption1Text;
      if (
        _this.ElOption_1.options_in.filter(
          item => item.id === _this.ElSelect_1.value
        ).length > 0
      ) {
        ElOption1Text = _this.ElOption_1.options_in.filter(
          item => item.id === _this.ElSelect_1.value
        )[0].text;
      }
      this.CetChart_3.config.options.yAxis.name = ElOption1Text
        ? `${ElOption1Text}（${unit})`
        : "";
      this.CetChart_3.config.options.xAxis.data = xAxisData;
      this.CetChart_3.config.options.series = cloneSeries;
      this.CetChart_3.config.options.legend.data = cloneLegend;
      this.chartTypeChange();
    },
    // 图表类型切换
    chartTypeChange() {
      this.CetChart_3.config.options.series.forEach(item => {
        item.type = this.chartType === 1 ? "bar" : "line";
      });
      this.CetChart_3.config.options.dataZoom =
        this.chartType === 1
          ? [
              {
                type: "inside",
                startValue: 0,
                endValue: 14,
                zoomOnMouseWheel: false
              },
              {
                startValue: 0,
                endValue: 14,
                zoomLock: true,
                brushSelect: false
              }
            ]
          : null;
      this.CetChart_3.config.options = this._.cloneDeep(
        this.CetChart_3.config.options
      );
    },
    //获取图表参数
    getParams(type = 1) {
      if (!this.clickNode) {
        return null;
      }
      var queryTime = this.CustomDatePicker_1.queryTime;
      var startTime = queryTime.startTime;
      var endTime = queryTime.endTime;
      var cycle = queryTime.cycle;
      var energyType = this.ElSelect_1.value;
      const dimIds = this.dimension.check;
      if (type === 1) {
        var CheckedArray = this.ElCheckboxGroup_1.value || [];
        var len = CheckedArray.length;
        var queryType;
        if (len === 0) {
          queryType = 0;
        } else if (len === 1) {
          queryType = CheckedArray[0];
        } else if (len === 2) {
          queryType = 3;
        }
        if (cycle === 17) {
          queryType = 1;
        } else if (cycle === 20) {
          queryType = 0;
        }
        var params = {
          cycle: cycle,
          endTime: endTime,
          energyType: energyType,
          queryNodes: [
            {
              dimInfo: {
                id: this.clickNode.id,
                modelLabel: this.clickNode.levelNodeId,
                name: this.clickNode.nodeName,
                nodeType: this.clickNode.nodeType,
                singleDim: dimIds?.length === 1,
                tagIds: this.getParentTagIds(this.clickNode)
              },
              nodeInfos: [
                {
                  measureNodes: [],
                  tagIds: []
                }
              ]
            }
          ],
          projectId: this.projectId,
          queryType: queryType,
          startTime: startTime
        };
        if (this.clickNode.nodeType === 1) {
          let measureNodes = [];
          let tagIds = this.clickNode.parentIdList;
          this.getManufactureequipmenttemplate(this.clickNode, measureNodes);
          params.queryNodes[0].nodeInfos[0].measureNodes = measureNodes;
          params.queryNodes[0].nodeInfos[0].tagIds = tagIds;
        } else {
          params.queryNodes[0].nodeInfos[0].measureNodes = [
            {
              id: this.clickNode.id,
              name: this.clickNode.nodeName,
              modelLabel: this.clickNode.label
            }
          ];
          params.queryNodes[0].nodeInfos[0].tagIds =
            this.clickNode.parentIdList;
        }
        return params;
      } else if (type === 2) {
        let params = {
          cycle: cycle,
          endTime: endTime,
          energyType: energyType,
          queryNodes: [
            {
              dimInfo: {
                id: this.clickNode.id,
                modelLabel: this.clickNode.levelNodeId,
                name: this.clickNode.nodeName,
                nodeType: this.clickNode.nodeType,
                singleDim: dimIds?.length === 1,
                tagIds: this.getParentTagIds(this.clickNode)
              },
              nodeInfos: [
                {
                  measureNodes: [],
                  tagIds: []
                }
              ]
            }
          ],
          projectId: this.projectId,
          startTime: startTime
        };
        if (this.clickNode.nodeType === 1) {
          let measureNodes = [];
          let tagIds = this.clickNode.parentIdList;
          this.getManufactureequipmenttemplate(this.clickNode, measureNodes);
          params.queryNodes[0].nodeInfos[0].measureNodes = measureNodes;
          params.queryNodes[0].nodeInfos[0].tagIds = tagIds;
        } else {
          params.queryNodes[0].nodeInfos[0].measureNodes = [
            {
              id: this.clickNode.id,
              name: this.clickNode.nodeName,
              modelLabel: this.clickNode.label
            }
          ];
          params.queryNodes[0].nodeInfos[0].tagIds =
            this.clickNode.parentIdList;
        }
        return params;
      } else if (type === 3) {
        const params = {
          cycle: cycle,
          endTime: endTime,
          energyType: energyType,
          queryNodes: this.getSelectNode(),
          projectId: this.projectId,
          startTime: startTime
        };
        return params;
      }
      return {};
    },
    getParentTagIds(node) {
      const treeId = node?.tree_id;
      if (!treeId) return;
      let treeNode = this.$refs.tree.getNode(treeId);
      let ids = [treeNode.data.id];
      while (treeNode.level !== 1) {
        treeNode = treeNode.parent;
        ids.unshift(treeNode.data.id);
      }
      return ids.join("_");
    },
    //过滤选中节点和子节点列表
    getManufactureequipmenttemplate(node, arr) {
      const loop = loopNode => {
        if (loopNode.nodeType !== 1) {
          if (
            arr.filter(
              item =>
                item.modelLabel === loopNode.label && item.id === loopNode.id
            ).length === 0
          ) {
            arr.push({
              id: loopNode.id,
              modelLabel: loopNode.label,
              name: loopNode.nodeName
            });
          }
        }
        if (loopNode.children && loopNode.children.length > 0) {
          loopNode.children.forEach(item => {
            loop(item);
          });
        }
      };
      loop(node);
    },
    //节点对比中获取选中节点，进行分类
    getSelectNode() {
      var list = this.$refs.tree.getCheckedNodes() || [];
      const dimIds = this.dimension.check;
      var resArr = []; //存放结果数组
      list.forEach(item => {
        if (item.nodeType === 1) {
          var measureNodes = [];
          var tagIds = item.parentIdList;
          this.getManufactureequipmenttemplate(item, measureNodes);
          resArr.push({
            dimInfo: {
              id: item.id,
              modelLabel: item.levelNodeId,
              name: item.nodeName,
              nodeType: item.nodeType,
              singleDim: dimIds?.length === 1,
              tagIds: this.getParentTagIds(item)
            },
            nodeInfos: [
              {
                measureNodes: measureNodes,
                tagIds: tagIds
              }
            ]
          });
        } else {
          resArr.push({
            dimInfo: {
              id: item.id,
              modelLabel: item.label,
              name: item.nodeName,
              nodeType: item.nodeType,
              singleDim: dimIds?.length === 1,
              tagIds: this.getParentTagIds(item)
            },
            nodeInfos: [
              {
                measureNodes: [
                  {
                    id: item.id,
                    name: item.nodeName,
                    modelLabel: item.label
                  }
                ],
                tagIds: item.parentIdList
              }
            ]
          });
        }
      });
      return resArr;
    },
    //分时统计对应图表点击拽取
    chartClick2(val) {
      //17年，14月，12日，20自定义
      var queryTime = this.CustomDatePicker_1.queryTime;
      if (queryTime.cycle === 17) {
        this.CustomDatePicker_1.dataConfig.time = val.data.time;
        this.CustomDatePicker_1.dataConfig.cycle = 3;
      } else if (queryTime.cycle === 14) {
        this.CustomDatePicker_1.dataConfig.time = val.data.time;
        this.CustomDatePicker_1.dataConfig.cycle = 1;
      }
    },
    //时间组件变化
    CustomDatePicker_1_change(val) {
      if (
        val.cycle === this.CustomDatePicker_1.queryTime.cycle &&
        val.endTime === this.CustomDatePicker_1.queryTime.endTime &&
        val.startTime === this.CustomDatePicker_1.queryTime.startTime
      ) {
        return;
      }
      this.CustomDatePicker_1.queryTime = val;
      //根据张壮反馈，修改47382缺陷；
      this.$nextTick(() => {
        if (!this.systemCfg.hideHistoryBtn) {
          this.getNodeTree_out();
        } else {
          const node = this.$refs.tree.getCurrentNode();
          node && this.nodeClick_out(node);
        }
      });
      if (this.ElSelect_type.value === 1) {
        if (val.cycle === 17) {
          this.ElCheckboxList_1.options_in = [
            {
              id: 1,
              disabled: false,
              text: $T("同比")
            },
            {
              id: 2,
              disabled: true,
              text: $T("环比")
            }
          ];
        } else if (val.cycle === 20) {
          this.ElCheckboxList_1.options_in = [
            {
              id: 1,
              disabled: true,
              text: $T("同比")
            },
            {
              id: 2,
              disabled: true,
              text: $T("环比")
            }
          ];
        } else {
          this.ElCheckboxList_1.options_in = [
            {
              id: 1,
              disabled: false,
              text: $T("同比")
            },
            {
              id: 2,
              disabled: false,
              text: $T("环比")
            }
          ];
        }
      }
      return;
    },
    //获取图表图例
    getLegend(date, type, customIndex) {
      date = new Date(date);
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      var d = date.getDate();
      if (m < 10) {
        m = "0" + m;
      }
      if (d < 10) {
        d = "0" + d;
      }
      if (type === 12) {
        return y + "-" + m + "-" + d;
      } else if (type === 14) {
        return y + "-" + m;
      } else if (type === 17) {
        return y + "";
      } else if (type === 20) {
        if (customIndex === 1) {
          return y + "-" + m + "-" + d;
        } else if (customIndex === 2) {
          return y + "-" + m;
        } else if (customIndex === 3) {
          return y + "";
        } else {
          return y + "-" + m + "-" + d;
        }
      } else {
        return y + "-" + m + "-" + d;
      }
    },
    //过滤获取图表x轴对应值
    getAxixs(date, type, customIndex) {
      date = new Date(date);
      var y = date.getFullYear();
      var M = date.getMonth() + 1;
      var d = date.getDate();
      var h = date.getHours();
      var m = date.getMinutes();
      if (M < 10) {
        M = "0" + M;
      }
      if (d < 10) {
        d = "0" + d;
      }
      if (h < 10) {
        h = "0" + h;
      }
      if (m < 10) {
        m = "0" + m;
      }
      if (type === 12) {
        return h + ":" + m;
      } else if (type === 14) {
        return d + "";
      } else if (type === 17) {
        return M + "";
      } else if (type === 20) {
        if (customIndex === 1) {
          if (h === "00" && m === "00") {
            return d + "";
          }
          return h + ":" + m;
        } else if (customIndex === 2) {
          return M + "-" + d;
        } else if (customIndex === 3) {
          return y + "-" + M;
        } else if (customIndex === 4) {
          return y + "";
        } else {
          return y + "-" + M + "-" + d;
        }
      } else {
        return y + "-" + M + "-" + d;
      }
    },
    //过滤获取图表单位
    getUnit_out(energyType) {
      var isTon =
        (energyType >= 3 && energyType <= 7) ||
        (energyType >= 9 && energyType <= 12) ||
        (energyType >= 17 && energyType <= 20) ||
        energyType === 22;
      var isM3 =
        energyType === 8 ||
        (energyType >= 11 && energyType <= 16) ||
        energyType === 21;
      if (energyType === 1) {
        return "J";
      } else if (energyType === 2) {
        return "kWh";
      } else if (isTon) {
        return "t";
      } else if (energyType === 12 || energyType === 13) {
        return "kgce";
      } else if (isM3) {
        return "m3";
      } else {
        return "--";
      }
    },
    // 获取能源类型
    async grtCetNoEnumSelect_1() {
      await httping({
        url:
          "/eem-service/v1/project/projectEnergy?projectId=" + this.projectId,
        method: "GET"
      }).then(res => {
        if (res.code === 0 && res.data && res.data.length > 0) {
          var selectData = res.data.map(item => {
            return {
              id: item.energytype,
              text: item.name
            };
          });
          // var res = {};
          // res.id = 0;
          // res.text = "全部";
          // selectData.unshift(res);
          this.ElOption_1.options_in = this._.cloneDeep(selectData);
          if (selectData.filter(item => item.id === 2).length > 0) {
            this.ElSelect_1.value = 2;
          } else {
            this.ElSelect_1.value = selectData[0].id;
          }
        } else {
          this.ElOption_1.options_in = [];
          this.ElSelect_1.value = null;
        }
      });
    },
    async init() {
      this.chartType = 1;
      await this.grtCetNoEnumSelect_1();
      this.dimData();
      if (this.$route.params?.keepParams?.type === "jumpQueryParams") {
        this.jumpFilter = true;
        let {
          checkboxGroupValue,
          energyType,
          aggregationCycle,
          startTime,
          endTime,
          cycle
        } = this.$route.params.keepParams;
        this.ElSelect_1.value = energyType;
        this.ElCheckboxGroup_1.value = checkboxGroupValue;
        Object.assign(this.CustomDatePicker_1.dataConfig, {
          time: startTime,
          cycle: cycle
        });
        this.CustomDatePicker_1.queryTime = {
          cycle: aggregationCycle,
          endTime: endTime,
          startTime: startTime
        };
      }
    }
  },
  created: function () {
    this.getLocalStorage();
  },
  mounted() {
    if (this.systemCfg.cachePage) {
      this.init();
    }
  },
  activated: function () {
    if (!this.systemCfg.cachePage) {
      this.init();
    }
  },
  deactivated() {
    this.isJump = false;
  },
  beforeRouteEnter(to, from, next) {
    if (from.name === "electricityDataRank") {
      next(vm => {
        vm.isJump = true;
        vm.keepParams = vm.$route.params.keepParams;
      });
    } else {
      next();
    }
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}

.dropdownBox {
  width: 125px;
  padding: 0 6px;
  line-height: 30px;
  border: 1px solid;
  @include border_radius(C);
  @include border_color(B1);
  @include background_color(BG1);
  .text {
    width: calc(100% - 24px);
  }
}
.dropdownBox i {
  display: block;
  line-height: 30px;
  width: 30px;
  margin-right: -6px;
  text-align: center;
  float: right;
}
.el-dropdown-menu .el-checkbox {
  display: block;
}
.el-dropdown-menu__item {
  @include margin_top(J1);
  padding: 0;
  line-height: 32px;
}
.el-dropdown-menu {
  padding: 10px;
}
.change-drop {
  @include font_color(ZS);
  width: 30px;
  height: 30px;
  position: absolute;
  cursor: pointer;
}

.tree {
  overflow: auto;
  :deep(.el-tree-node__children) {
    overflow: inherit !important;
  }
}
.eem-checkbox {
  :deep(.el-checkbox) {
    @include margin_right(J1);
  }
}
.backPlatform {
  width: 50px;
  margin: -12px 0 8px 0;
  cursor: pointer;
  @include font_color(ZS);
}
</style>
