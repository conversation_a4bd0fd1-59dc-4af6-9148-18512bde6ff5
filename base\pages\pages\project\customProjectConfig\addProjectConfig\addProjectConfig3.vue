<template>
  <div>
    <!-- 1弹窗组件 -->
    <CetDialog
      class="CetDialog"
      ref="CetDialog"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
    >
      <CetForm
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
      >
        <div class="bg1 brC2 rowBox">
          <el-row :gutter="$J3">
            <el-col :span="8">
              <el-form-item
                label="节点类型"
                prop="nodeType"
                v-if="type_in !== 3"
              >
                <ElSelect
                  v-model="CetForm_1.data.nodeType"
                  v-bind="ElSelect_nodeType"
                  v-on="ElSelect_nodeType.event"
                >
                  <ElOption
                    v-for="item in ElOption_nodeType.options_in"
                    :key="item[ElOption_nodeType.key]"
                    :label="item[ElOption_nodeType.label]"
                    :value="item[ElOption_nodeType.value]"
                    :disabled="item[ElOption_nodeType.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="节点名称" prop="name">
                <ElInput
                  v-model="CetForm_1.data.name"
                  v-bind="ElInput_name"
                  v-on="ElInput_name.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('code')">
              <el-form-item label="编号" prop="code">
                <ElInput
                  v-model="CetForm_1.data.code"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('address')">
              <el-form-item label="地址" prop="address">
                <ElInput
                  v-model="CetForm_1.data.address"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('area')">
              <el-form-item label="面积（平方米）" prop="area">
                <ElInputNumber
                  v-model="CetForm_1.data.area"
                  v-bind="ElInputNumber_1"
                  v-on="ElInputNumber_1.event"
                ></ElInputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('population')">
              <el-form-item label="人数（人）" prop="population">
                <ElInputNumber
                  v-model="CetForm_1.data.population"
                  v-bind="ElInputNumber_2"
                  v-on="ElInputNumber_2.event"
                ></ElInputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('brand')">
              <el-form-item label="品牌" prop="brand">
                <ElInput
                  v-model="CetForm_1.data.brand"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('location')">
              <el-form-item label="安装位置" prop="location">
                <ElInput
                  v-model="CetForm_1.data.location"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('model')">
              <el-form-item label="型号" prop="model">
                <ElInput
                  v-model="CetForm_1.data.model"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('longitude')">
              <el-form-item label="经度" prop="longitude">
                <ElInputNumber
                  class="coordinate"
                  :disabled="true"
                  v-model="CetForm_1.data.longitude"
                  v-bind="ElInputNumber_3"
                  v-on="ElInputNumber_3.event"
                ></ElInputNumber>
                <i
                  class="el-icon-close el-input__icon eem-map-icon"
                  style="right: 35px"
                  @click="delteleMap"
                ></i>
                <i
                  class="el-icon-edit el-input__icon eem-map-icon"
                  @click="OpenBaiduMap"
                ></i>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('latitude')">
              <el-form-item label="纬度" prop="latitude">
                <ElInputNumber
                  class="coordinate"
                  :disabled="true"
                  v-model="CetForm_1.data.latitude"
                  v-bind="ElInputNumber_3"
                  v-on="ElInputNumber_3.event"
                ></ElInputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('manufacturedate')">
              <el-form-item label="出厂日期" prop="manufacturedate">
                <el-date-picker
                  v-model="CetForm_1.data.manufacturedate"
                  type="date"
                  :editable="false"
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('commissiondate')">
              <el-form-item label="投用日期" prop="commissiondate">
                <el-date-picker
                  v-model="CetForm_1.data.commissiondate"
                  type="date"
                  :editable="false"
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('manufactor')">
              <el-form-item label="生产厂家" prop="manufactor">
                <ElInput
                  v-model="CetForm_1.data.manufactor"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('voltagelevel')">
              <el-form-item label="电压等级" prop="voltagelevel">
                <ElSelect
                  v-model="CetForm_1.data.voltagelevel"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                >
                  <ElOption
                    v-for="item in ElOption_1.options_in"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="filNodePermissions_out('document')">
              <el-form-item label="相关文档" prop="test">
                <div class="upload mtJ2 mbJ2" style="float: left">
                  <el-upload
                    class="elupload11"
                    ref="DocElupload"
                    action="/eem-service/v1/common/uploadFile"
                    :headers="{ Authorization: this.token }"
                    :before-upload="handleBeforeUpload2"
                    :on-success="uploadSuccess2"
                    :multiple="false"
                    :limit="1"
                    :before-remove="beforeRemove"
                    :on-exceed="handleExceed"
                  >
                    <!-- <button ref="uploadBtn2"></button> -->
                    <el-button size="small" type="primary">
                      选择上传文件
                    </el-button>
                    <p>
                      只能上传xls/xlsx/docx/pdf格式文件,且不超过{{
                        systemCfg.uploadDocSize || "10"
                      }}M
                    </p>
                  </el-upload>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="bg1 brC2 rowBox mtJ1" v-if="filNodePermissions_out('pic')">
          <el-row :gutter="$J3">
            <el-col :span="24">
              <el-form-item>
                <div class="label" slot="label">
                  主图
                  <div class="box_tip2">
                    只能上传jpg/png图片，且不超过{{ systemCfg.uploadPicSize }}M
                  </div>
                </div>
                <div class="value">
                  <UploadImg
                    class="uploadImg"
                    :imgUrl.sync="CetForm_1.data.pic"
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </CetForm>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
    <MapLatOrLng
      :visibleTrigger_in="MapLatOrLng.visibleTrigger_in"
      :closeTrigger_in="MapLatOrLng.closeTrigger_in"
      :queryId_in="MapLatOrLng.queryId_in"
      :inputData_in="MapLatOrLng.inputData_in"
      :mapInfo="mapInfo"
      @finishTrigger_out="MapLatOrLng_finishTrigger_out"
      @finishData_out="MapLatOrLng_finishData_out"
      @saveData_out="MapLatOrLng_saveData_out"
      @currentData_out="MapLatOrLng_currentData_out"
    />
    <CetInterface
      :data.sync="CetInterface2_voltagelevel.data"
      :dynamicInput.sync="CetInterface2_voltagelevel.dynamicInput"
      v-bind="CetInterface2_voltagelevel"
      v-on="CetInterface2_voltagelevel.event"
    ></CetInterface>
  </div>
</template>
<script>
import common from "eem-utils/common";
import MapLatOrLng from "./MapLatOrLng.vue";
import ELECTRICAL_DEVICE_NODE from "@/store/electricaldevicenode.js";
import UploadImg from "eem-components/uploadImg.vue";
import { httping } from "@omega/http";
export default {
  name: "addProjectConfig3",
  components: {
    MapLatOrLng,
    UploadImg
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    treeData_in: {
      type: Object
    },
    currentTabItme_in: {
      type: Object
    },
    type_in: {
      type: Number
    },
    treeNameList_in: {
      type: Array
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    systemCfg() {
      return this.$store.state.systemCfg;
    }
  },

  data() {
    return {
      mapInfo: {
        areaJson: null,
        point: null
      },
      CetDialog_1: {
        title: "添加车间/用能设备",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "保存",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "关闭",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        labelPosition: "top",
        rules: {
          nodeType: [
            {
              required: true,
              message: "请选择节点类型",
              trigger: ["blur", "change"]
            }
          ],
          name: [
            {
              required: true,
              message: "请输入名称",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          address: [
            {
              required: false,
              message: "请输入地址",
              trigger: ["blur", "change"]
            },
            common.check_stringLessThan255
          ],
          area: [
            {
              required: false,
              message: "请输入面积",
              trigger: ["blur", "change"]
            }
          ],
          population: [
            {
              required: false,
              message: "请输入人数",
              trigger: ["blur", "change"]
            }
          ],

          code: [
            {
              required: false,
              message: "请输入编号",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          brand: [
            {
              required: false,
              message: "请输入品牌",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          location: [
            {
              required: false,
              message: "请输入安装位置",
              trigger: ["blur", "change"]
            },
            common.check_stringLessThan255,
            common.pattern_name
          ],
          model: [
            {
              required: false,
              message: "请输入型号",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          longitude: [
            {
              required: false,
              message: "请输入经度",
              trigger: ["blur", "change"]
            }
          ],
          latitude: [
            {
              required: false,
              message: "请输入纬度",
              trigger: ["blur", "change"]
            }
          ],
          manufacturedate: [
            {
              required: false,
              message: "请选择出厂日期",
              trigger: ["blur", "change"]
            }
          ],
          commissiondate: [
            {
              required: false,
              message: "请选择投用日期",
              trigger: ["blur", "change"]
            }
          ],
          manufactor: [
            {
              required: false,
              message: "请输入厂家",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          voltagelevel: [
            {
              required: false,
              message: "请选择电压等级",
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_1_currentData_out,
          saveData_out: this.CetForm_1_saveData_out,
          finishData_out: this.CetForm_1_finishData_out,
          finishTrigger_out: this.CetForm_1_finishTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        style: {},
        placeholder: "请输入内容",
        event: {
          change: this.ElInput_1_change_out,
          input: this.ElInput_1_input_out
        }
      },
      ElInputNumber_1: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_1_change_out
        }
      },
      ElInputNumber_2: {
        ...common.check_numberInt,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_2_change_out
        }
      },
      ElInputNumber_3: {
        ...common.check_numberFloat11,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        placeholder: "请选择经纬度",
        event: {
          change: this.ElInputNumber_3_change_out
        }
      },
      pickerOptions: common.pickerOptions_laterThanYesterd,
      pickerOptions11: common.pickerOptions_laterThanYesterd11,
      ElSelect_1: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetInterface2_voltagelevel: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "queryEnum",
          modelLabel: "voltagelevel",
          dataIndex: [],
          modelList: [],
          filters: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {},
        page_in: null, // exp:{ index: 1, limit: 20 }
        defaultSort: null, // { prop: "code"  order: "descending" },
        event: {
          result_out: this.CetInterface2_voltagelevel_result_out,
          finishTrigger_out: this.CetInterface2_voltagelevel_finishTrigger_out,
          failTrigger_out: this.CetInterface2_voltagelevel_failTrigger_out,
          totalNum_out: this.CetInterface2_voltagelevel_totalNum_out
        }
      },
      ElInput_name: {
        value: "",
        style: {},
        placeholder: "请输入内容",
        event: {
          change: this.ElInput_name_change_out,
          input: this.ElInput_name_input_out
        }
      },
      // nodeType组件
      ElSelect_nodeType: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_nodeType_change_out
        }
      },
      // nodeType组件
      ElOption_nodeType: {
        options_in: [
          {
            id: "floor",
            text: "车间/楼层"
          },
          {
            id: "manuequipment",
            text: "用能设备"
          },
          {
            id: "meteorologicalmonitor",
            text: "气象监测仪"
          },
          {
            id: "airconditioner",
            text: "空调"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      MapLatOrLng: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      uploadPath2: ""
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      vm.$nextTick(() => {
        vm.CetInterface2_voltagelevel.queryTrigger_in = new Date().getTime();
        $(this.$refs.CetDialog.$el).scrollTop(0);
        if (vm.$refs.DocElupload) {
          vm.$refs.DocElupload.clearFiles();
        }
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {
      var vm = this;
      // vm.CetDialog_1.queryId_in = val;
    },
    inputData_in(val) {
      // this.CetDialog_1.inputData_in = val;
      val.nodeType = "floor";
      this.CetForm_1.data = this._.cloneDeep(val);
      this.uploadPath2 = "";
      if (this.type_in === 3) {
        //删除设备通用字段信息，防止编辑保存报错；
        delete this.CetForm_1.data.devicecommoninfo_model;
        this.CetForm_1.data.nodeType = "floor";
        this.CetDialog_1.title = "编辑节点";
      } else {
        this.CetDialog_1.title = "添加节点";
      }
      this.CetForm_1.resetTrigger_in = new Date().getTime();
    }
  },

  methods: {
    filNodePermissions_out(key) {
      const nodeModelLabel = this.CetForm_1.data.nodeType || "";
      const modelLabelList = ELECTRICAL_DEVICE_NODE[nodeModelLabel] || [];
      let isOk = false;
      modelLabelList.forEach(item => {
        if (item.propertyLabel == key) {
          isOk = true;
        }
      });
      return isOk;
    },
    CetInterface2_voltagelevel_finishTrigger_out(val) {},
    CetInterface2_voltagelevel_failTrigger_out(val) {},
    CetInterface2_voltagelevel_totalNum_out(val) {},
    CetInterface2_voltagelevel_result_out(val) {
      this.ElOption_1.options_in = this._.cloneDeep(val);
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件`);
    },
    beforeRemove(file, fileList) {
      var _this = this;
      // return this.$confirm(`确定移除 ${ file.name }？`);
      return this.$confirm(`确定移除 ${file.name}？`, "提示").then(() => {
        _this.uploadPath2 = "";
      });
    },
    //    上传前
    handleBeforeUpload2: function (file) {
      this.loading = true;
      if (
        file.name.indexOf(".xls") != -1 ||
        file.name.indexOf(".xlsx") != -1 ||
        file.name.indexOf(".docx") != -1 ||
        file.name.indexOf(".pdf") != -1
      ) {
        var uploadDocSize = this.systemCfg.uploadDocSize || 10;
        const isLimit100M = file.size / 1024 / 1024 < uploadDocSize;
        if (!isLimit100M) {
          this.$message.error("上传文件超过规定的最大上传大小");
        }
        return isLimit100M;
      } else {
        this.$message({
          type: "warning",
          message: "只能上传xls/xlsx/docx/pdf格式文件"
        });
        return false;
      }
    },
    //   上传成功
    uploadSuccess2: function (response) {
      if (response.code === 0) {
        this.uploadPath2 = response.data;
        this.$message({
          message: "上传成功",
          type: "success"
        });
      } else if (response.code !== 0) {
        var tips = "";
        if (response.data) {
          for (var i = 0; i < response.data.length; i++) {
            tips = tips + response.data[i] + "<br/>";
          }
        } else {
          tips = response.msg;
        }
        this.$message({
          type: "error",
          message: tips
        });
      }
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
      this.$emit("confirm_out");
    },
    CetDialog_1_open_out(val) {},
    CetDialog_1_close_out(val) {},
    CetForm_1_currentData_out(val) {},
    CetForm_1_saveData_out(val) {
      var params = [];
      var model_data = this._.cloneDeep(this.CetForm_1.data);
      if (this.type_in == 1) {
        if (!this.treeData_in) {
          return;
        }
        model_data.modelLabel = this.CetForm_1.data.nodeType;
        if (this.CetForm_1.data.nodeType == "manuequipment") {
          var manufacturedate = model_data.manufacturedate
            ? new Date(model_data.manufacturedate).getTime()
            : null;
          var commissiondate = model_data.commissiondate
            ? new Date(model_data.commissiondate).getTime()
            : null;
          if (manufacturedate && commissiondate) {
            if (manufacturedate >= commissiondate) {
              this.$message.warning("投运时间不能小于等于出厂时间！");
              return;
            }
          }
          model_data.manufacturedate = manufacturedate;
          model_data.commissiondate = commissiondate;
          model_data.document = this.uploadPath2;
        }
        model_data.children = [
          {
            id: this.treeData_in.id,
            modelLabel: this.treeData_in.modelLabel
          }
        ];
        params[0] = model_data;
      } else if (this.type_in == 2) {
        if (!this.currentTabItme_in) {
          return;
        }
        model_data.modelLabel = this.CetForm_1.data.nodeType;
        if (this.CetForm_1.data.nodeType == "manuequipment") {
          model_data.modelLabel = "manuequipment";
          let manufacturedate = model_data.manufacturedate
            ? new Date(model_data.manufacturedate).getTime()
            : null;
          let commissiondate = model_data.commissiondate
            ? new Date(model_data.commissiondate).getTime()
            : null;
          if (manufacturedate && commissiondate) {
            if (manufacturedate >= commissiondate) {
              this.$message.warning("投运时间不能小于等于出厂时间！");
              return;
            }
          }
          model_data.manufacturedate = manufacturedate;
          model_data.commissiondate = commissiondate;
          model_data.document = this.uploadPath2;
        }
        model_data.area = Number(model_data.area);
        model_data.population = Number(model_data.population);
        model_data.children = [
          {
            id: this.currentTabItme_in.id,
            modelLabel: this.currentTabItme_in.modelLabel
          }
        ];
        params[0] = model_data;
      } else if (this.type_in == 3) {
        if (!this.inputData_in) {
          return;
        }
        model_data.modelLabel = "floor";
        model_data.children = [
          {
            id: this.treeData_in.id,
            modelLabel: this.treeData_in.modelLabel
          }
        ];
        params[0] = model_data;
      }
      var name = this.CetForm_1.data.name;
      var list = this.treeNameList_in || [];
      for (var i = 0, len = list.length; i < len; i++) {
        if (list[i] === name) {
          if (this.type_in === 3 && this.inputData_in.name === name) {
            continue;
          }
          this.$message.warning("节点名称重复！");
          return;
        }
      }
      this.Add_node(params);
    },
    CetForm_1_finishData_out(val) {},
    CetForm_1_finishTrigger_out(val) {},

    ElInput_1_change_out(val) {},
    ElInput_1_input_out(val) {},
    ElInput_name_change_out(val) {},
    ElInput_name_input_out(val) {},

    ElInputNumber_1_change_out(val) {},
    ElInputNumber_2_change_out(val) {},
    ElInputNumber_3_change_out(val) {},
    ElSelect_1_change_out(val) {},
    ElSelect_nodeType_change_out(val) {
      if (!val) {
        return;
      }
      this.CetForm_1.data = {
        nodeType: val
      };
    },
    MapLatOrLng_currentData_out(val) {},
    MapLatOrLng_finishData_out(val) {
      if (!val) {
        return;
      }
      this.$set(this.CetForm_1.data, "longitude", val.lng);
      this.$set(this.CetForm_1.data, "latitude", val.lat);
      this.CetForm_1.data.locationrange = val.areaJson;
    },
    MapLatOrLng_finishTrigger_out(val) {},
    MapLatOrLng_saveData_out(val) {},
    OpenBaiduMap() {
      this.mapInfo.areaJson = this.CetForm_1.data.locationrange;
      this.mapInfo.point = {
        latitude: this.CetForm_1.data.latitude,
        longitude: this.CetForm_1.data.longitude
      };
      this.MapLatOrLng.visibleTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    delteleMap() {
      this.CetForm_1.data.longitude = undefined;
      this.CetForm_1.data.latitude = undefined;
      this.CetForm_1.data.locationrange = "";
    },
    Add_node(params) {
      var _this = this;
      // 经纬度如果是undefined 则转成null
      if (params[0].longitude === undefined) {
        params[0].longitude = null;
      }
      if (params[0].latitude === undefined) {
        params[0].latitude = null;
      }
      httping({
        url: "/eem-service/v1/project/manageNode",
        data: params,
        method: "PUT"
      }).then(res => {
        if (res.code === 0) {
          _this.CetDialog_1.closeTrigger_in = _this._.cloneDeep(
            new Date().getTime()
          );
          _this.$emit("saveData_out", res.data[0]);
          if (_this.CetForm_1.data.nodeType !== 1 && _this.type_in !== 3) {
            _this.Edit_quantity(res.data);
          }
        }
      });
    },
    Edit_quantity(data) {
      var _this = this;
      var obj = this._.get(data, "[0]", {});
      var params = {};
      params = {
        dataSource: 1,
        energyType: null,
        nodes: [
          {
            id: obj.id,
            modelLabel: obj.modelLabel
          }
        ]
      };
      httping({
        url: "/eem-service/v1/quantity/quantityObjectSetting",
        data: params,
        method: "POST"
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
    box-sizing: border-box;
  }
  .rowBox {
    @include padding(J4 J4 J2 J4);
    padding-bottom: mh-get(J4) - 18px;
  }
}

.uploadImg {
  width: 80px;
  height: 80px;
}
.box_tip2 {
  display: inline-block;
  @include font_size(Ab);
  @include font_color(T3);
}
.upload,
.elupload11 {
  width: 100%;
  :deep(.el-upload) {
    width: 100%;
  }
}
.eem-map-icon {
  position: absolute;
  right: 10px;
  z-index: 1;
  top: -3px;
  cursor: pointer;
}
</style>
