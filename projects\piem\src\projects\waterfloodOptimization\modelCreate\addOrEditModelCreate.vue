<template>
  <div>
    <CetDialog
      class="CetDialog"
      ref="CetDialog"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
      :title="title"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="formData"
        label-width="120px"
        class="eem-cont-c1"
      >
        <div class="grid grid-cols-2 gap-x-[24px]">
          <el-form-item label="方案名称" prop="name">
            <ElInput v-model="formData.name" placeholder="请输入"></ElInput>
          </el-form-item>
          <el-form-item label="模型目标" prop="modelGoal">
            <ElSelect
              v-model="formData.modelGoal"
              style="width: 100%"
              placeholder="请选择"
              disabled
            >
              <ElOption
                v-for="item in modelGoalList"
                :key="item.id"
                :label="item.text"
                :value="item.id"
              ></ElOption>
            </ElSelect>
          </el-form-item>
          <el-form-item label="目标参数" prop="targetParameter">
            <ElSelect
              v-model="formData.targetParameter"
              style="width: 100%"
              placeholder="请选择"
              disabled
            >
              <ElOption
                v-for="item in targetParameterList"
                :key="item.id"
                :label="item.text"
                :value="item.id"
              ></ElOption>
            </ElSelect>
          </el-form-item>
          <el-form-item label="参数读取时间" prop="parameterPeriod">
            <ElSelect
              v-model="formData.parameterPeriod"
              style="width: 100%"
              placeholder="请选择"
              disabled
            >
              <ElOption
                v-for="item in parameterPeriodList"
                :key="item.id"
                :label="item.text"
                :value="item.id"
              ></ElOption>
            </ElSelect>
          </el-form-item>
          <el-form-item>
            <template #label>
              <span class="text-Sta3">*</span>
              汇管流量(m³)
            </template>
            <template>
              <div class="flex">
                <ElInputNumber
                  placeholder="请输入下限"
                  v-bind="inputNumber"
                  v-model="formData.parameterRange.minManifoldFlow"
                ></ElInputNumber>
                -
                <ElInputNumber
                  placeholder="请输入上限"
                  v-bind="inputNumber"
                  v-model="formData.parameterRange.maxManifoldFlow"
                ></ElInputNumber>
              </div>
            </template>
          </el-form-item>
          <el-form-item>
            <template #label>
              <span class="text-Sta3">*</span>
              汇管压力(Mpa）
            </template>
            <template>
              <div class="flex">
                <ElInputNumber
                  placeholder="请输入下限"
                  v-bind="inputNumber"
                  v-model="formData.parameterRange.minManifoldPressure"
                ></ElInputNumber>
                -
                <ElInputNumber
                  placeholder="请输入上限"
                  v-bind="inputNumber"
                  v-model="formData.parameterRange.maxManifoldPressure"
                ></ElInputNumber>
              </div>
            </template>
          </el-form-item>
        </div>
        <selectInputParameter
          :inputParameter="inputData_in.inputParameter"
          @inputParameter="onInputParameter"
          class="!w-full"
        />
      </el-form>

      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom";
import selectInputParameter from "./selectInputParameter.vue";

export default {
  name: "addOrEditModelCreate",
  props: {
    openTrigger_in: Number,
    inputData_in: {
      type: Object,
      default: () => {}
    }
  },
  components: { selectInputParameter },
  data() {
    return {
      CetDialog_1: {
        width: "880px",
        title: "",
        showClose: true,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        "append-to-body": true,
        event: {}
      },
      inputNumber: {
        style: {
          width: "100%"
        },
        ...common.check_numberFloat,
        controls: false
      },
      defaultParams: {
        name: "",
        modelGoal: 1,
        targetParameter: 1,
        parameterPeriod: 2,
        parameterRange: {
          minManifoldFlow: undefined,
          maxManifoldFlow: undefined,
          minManifoldPressure: undefined,
          maxManifoldPressure: undefined
        },
        inputParameter: []
      },
      formData: {
        name: "",
        modelGoal: 1,
        targetParameter: 1,
        parameterPeriod: 2,
        parameterRange: {
          minManifoldFlow: undefined,
          maxManifoldFlow: undefined,
          minManifoldPressure: undefined,
          maxManifoldPressure: undefined
        },
        inputParameter: []
      },
      rules: {
        manifoldFlow: [{ validator: this.validatePass, trigger: "blur" }],
        manifoldPressure: [{ validator: this.validatePass1, trigger: "blur" }],
        name: [
          {
            required: true,
            message: "请输入方案名称",
            trigger: ["blur", "change"]
          },
          common.check_name,
          common.pattern_name,
          common.check_space
        ],
        modelGoal: [
          {
            required: true,
            message: "请选择模型目标",
            trigger: ["blur", "change"]
          }
        ],
        targetParameter: [
          {
            required: true,
            message: "请输入目标参数",
            trigger: ["blur", "change"]
          }
        ],
        parameterPeriod: [
          {
            required: true,
            message: "请输入参数读取时间",
            trigger: ["blur", "change"]
          }
        ]
      },
      modelGoalList: [{ id: 1, text: "最小单耗" }],
      targetParameterList: [{ id: 1, text: "注水单耗" }],
      parameterPeriodList: [{ id: 2, text: "2小时" }],

      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    async openTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.init();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },
  computed: {
    title() {
      return `${this.inputData_in?.id ? "编辑" : "新增"}训练方案`;
    },
    isEdit() {
      return this.inputData_in?.id;
    }
  },
  methods: {
    init() {
      for (const key in this.formData) {
        if (this.isEdit) {
          this.formData[key] = this.inputData_in[key];
        } else {
          this.formData[key] = this.defaultParams[key];
        }
      }
    },
    validatePass(rule, value, callback) {
      if (!this.formData?.minManifoldFlow) {
        callback(new Error("请输入完整的汇管流量"));
      }
    },
    validatePass1(rule, value, callback) {
      if (!this.formData.minManifoldPressure) {
        callback(new Error("请输入完整的汇管压力"));
      }
    },

    onInputParameter(val) {
      this.formData.inputParameter = _.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      const params = {
        ...this.formData,
        status: this.isEdit ? this.inputData_in.status : true
      };
      if (this.isEdit) {
        params.id = this.inputData_in?.id;
      }
      this.$refs.formData.validate(async valid => {
        if (!valid) return;
        const res = await customApi.saveorupdate(params);
        if (res?.code === 0) {
          this.$message.success(this.isEdit ? "编辑成功" : "新增成功");
          this.$emit("updateTable");
          this.CetButton_cancel_statusTrigger_out();
        }
      });
    },
    CetButton_cancel_statusTrigger_out() {
      this.$refs.formData.resetFields();
      this.CetDialog_1.closeTrigger_in = +new Date();
    }
  },
  created() {},
  mounted() {}
};
</script>

<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
  }
  :deep(.el-form-item) {
    .el-form-item__label {
      line-height: 24px;
      text-align: left;
      width: 100% !important;
      float: none;
    }
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
  :deep(.el-dialog) {
    margin-top: 8vh !important;
  }
}
</style>
