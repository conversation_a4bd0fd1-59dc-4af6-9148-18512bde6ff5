<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="small"
    >
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
        </span>
      </template>
      <div class="eem-cont-c1">
        <CetForm
          :data.sync="CetForm_pagedialog.data"
          v-bind="CetForm_pagedialog"
          v-on="CetForm_pagedialog.event"
        >
          <el-form-item :label="$T('接班时间') + ':'" prop="">
            <span>
              {{
                $moment(CetForm_pagedialog.data.starttime).format(
                  "YYYY-MM-DD HH:mm:ss"
                ) || "--"
              }}
            </span>
          </el-form-item>
          <el-form-item :label="$T('交班时间') + ':'" prop="">
            <span>
              {{
                CetForm_pagedialog.data.endtime
                  ? $moment(CetForm_pagedialog.data.endtime).format(
                      "YYYY-MM-DD HH:mm:ss"
                    )
                  : "--"
              }}
            </span>
          </el-form-item>
          <el-form-item :label="$T('责任班组') + ':'" prop="duration">
            <span>{{ CetForm_pagedialog.data.teamName || "--" }}</span>
          </el-form-item>
          <el-form-item :label="$T('值班时长') + ':'" prop="duration">
            <span>
              {{
                secondsFormat(
                  CetForm_pagedialog.data.dutytime,
                  $T("hh小时mm分钟")
                )
              }}
            </span>
          </el-form-item>
          <el-form-item :label="$T('值班长') + ':'" prop="">
            <span>{{ CetForm_pagedialog.data.dutyOfficerName || "--" }}</span>
          </el-form-item>
          <el-form-item :label="$T('值班员') + ':'" prop="">
            <span>
              {{ handleDutystaff(CetForm_pagedialog.data.dutystaff) }}
            </span>
          </el-form-item>
          <el-form-item :label="$T('值班日志') + ':'" prop="">
            <el-tooltip
              effect="light"
              placement="top-start"
              :content="CetForm_pagedialog.data.dutylog"
            >
              <div style="width: 100%" class="text-ellipsis">
                {{ CetForm_pagedialog.data.dutylog || "--" }}
              </div>
            </el-tooltip>
          </el-form-item>
          <el-form-item :label="$T('交接事项') + ':'" prop="">
            <el-tooltip
              effect="light"
              placement="top-start"
              :content="CetForm_pagedialog.data.handovermatter"
            >
              <div style="width: 100%" class="text-ellipsis">
                {{ CetForm_pagedialog.data.handovermatter || "--" }}
              </div>
            </el-tooltip>
          </el-form-item>
        </CetForm>
      </div>
    </CetDialog>
  </div>
</template>
<script>
export default {
  name: "detailDialog",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    const enLanguage = window.localStorage.getItem("omega_language") === "en";
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("详情"),
        "show-close": true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "component", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: enLanguage ? "140px" : "80px",
        "label-position": "left",
        rules: {},
        event: {
          currentData_out: this.CetForm_pagedialog_currentData_out,
          saveData_out: this.CetForm_pagedialog_saveData_out,
          finishData_out: this.CetForm_pagedialog_finishData_out,
          finishTrigger_out: this.CetForm_pagedialog_finishTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInput_duration: {
        value: "",
        style: {
          width: "100%"
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
      this.CetForm_pagedialog.data = this.inputData_in;
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {
      this.CetForm_pagedialog.queryId_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      this.CetForm_pagedialog.inputData_in = this._.cloneDeep(val);
    }
  },
  methods: {
    CetForm_pagedialog_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_saveData_out(val) {
      this.$emit("saveData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishData_out(val) {
      this.$emit("finishData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishTrigger_out(val) {
      this.$emit("finishTrigger_out", val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetForm_pagedialog.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    // 毫秒数转化
    secondsFormat(sec, format) {
      let str = "-- h -- min";
      if (sec || sec === 0) {
        const hour = Math.floor(sec / 3600000);
        const minute = Math.floor((sec - hour * 3600000) / 60000);
        if (
          format.indexOf("hh") != -1 &&
          format.indexOf("mm") != -1 &&
          format.indexOf("ss") == -1
        ) {
          str = format.replace(
            /(.*)hh(.*)mm(.*)/,
            "$1" + hour + "$2" + minute + "$3"
          );
        }
      }
      return str;
    },
    handleDutystaff(val) {
      if (!val) return;
      const dutystaff = JSON.parse(val);
      const arr = [];
      if (dutystaff.length) {
        dutystaff.forEach(item => {
          arr.push(item.userName);
        });
      }
      return arr.join(",") || "--";
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.el-form {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}
</style>
