<template>
  <div>
    <CetDialog
      class="CetDialog"
      ref="CetDialog"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="formData"
        label-width="120px"
        class="eem-cont-c1"
      >
        <el-form-item label="评价结果" prop="valid">
          <el-radio-group v-model="formData.valid">
            <el-radio :label="true">有效</el-radio>
            <el-radio :label="false">无效</el-radio>
            <el-radio :label="null">忽略</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="评价描述">
          <el-input
            v-model="formData.strategyevaluation"
            :maxlength="100"
            :show-word-limit="true"
            :rows="5"
            resize="none"
            type="textarea"
            placeholder="请输入评价，最多100字"
          />
        </el-form-item>
      </el-form>

      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom";
export default {
  name: "strategyEvaluation",
  props: {
    openTrigger_in: Number,
    inputData_in: {
      type: Object,
      default: () => {}
    }
  },
  components: {},
  data() {
    return {
      CetDialog_1: {
        width: "480px",
        title: "策略评价",
        showClose: true,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        "append-to-body": true,
        event: {}
      },

      formData: {
        valid: true,
        strategyevaluation: ""
      },
      rules: {
        valid: [
          {
            required: true,
            message: "请选择评价结果",
            trigger: ["input"]
          }
        ]
      },

      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    async openTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.init();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },
  computed: {},
  methods: {
    init() {
      this.$nextTick(() => {
        for (const key in this.formData) {
          this.formData[key] = this.inputData_in[key];
        }
      });
    },

    async CetButton_confirm_statusTrigger_out() {
      const params = {
        ...this.formData,
        id: this.inputData_in?.id
      };

      const res = await customApi.updataAnalysisOfStrategies(params);
      if (res?.code === 0) {
        this.$message.success("修改成功");
        this.$emit("updateTable", res?.data[0]);
        this.CetButton_cancel_statusTrigger_out();
      }
    },
    CetButton_cancel_statusTrigger_out() {
      this.$refs.formData.resetFields();
      this.CetDialog_1.closeTrigger_in = +new Date();
    }
  }
};
</script>

<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
  }
  :deep(.el-form-item) {
    .el-form-item__label {
      line-height: 24px;
      text-align: left;
      width: 100% !important;
      float: none;
    }
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}
</style>
