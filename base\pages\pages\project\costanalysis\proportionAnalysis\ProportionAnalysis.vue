﻿<template>
  <div class="page eem-common">
    <el-container class="fullheight">
      <el-aside width="315px" class="eem-aside">
        <CetGiantTree
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
        ></CetGiantTree>
      </el-aside>
      <el-container class="padding0 fullheight mlJ3">
        <el-header height="48px" style="padding: 0px">
          <el-tooltip
            effect="light"
            :content="currentNode && currentNode.name"
            placement="top-start"
          >
            <span
              class="common-title-H1 text-ellipsis"
              style="display: inline-block; width: 160px"
            >
              {{ currentNode && currentNode.name }}
            </span>
          </el-tooltip>
          <!-- 向后查询按钮 -->
          <!-- <CetButton
            class="fr mr5"
            v-bind="CetButton_next"
            v-on="CetButton_next.event"
          ></CetButton>
          <div class="basic-box fr mr5">
            <span class="basic-box-label">
              {{ CetDatePicker_1.config.type === "year" ? "年份" : "月份" }}
            </span>
            <el-date-picker
              v-model="CetDatePicker_1.val"
              v-bind="CetDatePicker_1.config"
              size="small"
            ></el-date-picker>
          </div> -->
          <!-- 向前查询按钮 -->
          <!-- <CetButton
            class="fr mr5"
            v-bind="CetButton_prv"
            v-on="CetButton_prv.event"
          ></CetButton> -->
          <CustomElDatePicker
            class="fr"
            :prefix_in="$T('选择时间')"
            v-bind="CetDatePicker_1.config"
            v-on="CetDatePicker_1.event"
            v-model="CetDatePicker_1.val"
          />
          <div class="basic-box fr mrJ1">
            <customElSelect
              v-model="ElSelect_2.value"
              v-bind="ElSelect_2"
              v-on="ElSelect_2.event"
              :prefix_in="$T('查询时段')"
            >
              <ElOption
                v-for="item in ElOption_2.options_in"
                :key="item[ElOption_2.key]"
                :label="item[ElOption_2.label]"
                :value="item[ElOption_2.value]"
                :disabled="item[ElOption_2.disabled]"
              ></ElOption>
            </customElSelect>
          </div>
          <div class="basic-box fr mrJ1">
            <customElSelect
              v-model="ElSelect_1.value"
              v-bind="ElSelect_1"
              v-on="ElSelect_1.event"
              :prefix_in="$T('能源类型')"
            >
              <ElOption
                v-for="item in ElOption_1.options_in"
                :key="item[ElOption_1.key]"
                :label="item[ElOption_1.label]"
                :value="item[ElOption_1.value]"
                :disabled="item[ElOption_1.disabled]"
              ></ElOption>
            </customElSelect>
          </div>
        </el-header>
        <div class="flex-column flex-auto">
          <div class="chartsList flex-row">
            <div
              style="margin-left: 0"
              class="pJ3 bg1 pt0"
              v-if="!hideCostanalysisRegionEnergyProportion"
            >
              <el-header
                height="50px"
                class="bg1"
                style="padding: 0px; line-height: 50px"
              >
                <span class="mlJ1 common-title-H2">
                  {{ $T("区域用能占比核算") }}
                </span>
                <div class="fr chartsTitle">
                  {{ $T("成本合计") }} （{{ unitName }}）
                  <div class="fr">{{ total.section | formatNum2 }}</div>
                </div>
              </el-header>
              <div class="chartsBox">
                <CetChart
                  v-if="CetChart_1.config.options.series[0].data.length >= 1"
                  :inputData_in="CetChart_1.inputData_in"
                  v-bind="CetChart_1.config"
                />
                <div class="chartsBox-empty" v-else>暂无数据</div>
              </div>
            </div>
            <div class="pJ3 pt0 bg1">
              <el-header
                height="50px"
                class="bg1"
                style="padding: 0px; line-height: 50px"
              >
                <span class="ml8 common-title-H2">
                  {{ $T("分项用能占比核算") }}
                </span>
                <div class="fr chartsTitle">
                  {{ $T("成本合计") }} （{{ classUnitName }}）
                  <div class="fr">{{ total.class | formatNum2 }}</div>
                </div>
              </el-header>
              <div class="chartsBox">
                <CetChart
                  :inputData_in="CetChart_2.inputData_in"
                  v-bind="CetChart_2.config"
                />
              </div>
            </div>
            <div style="margin-right: 0" class="pJ3 bg1 pt0">
              <el-header
                height="50px"
                class="bg1"
                style="padding: 0px; line-height: 50px"
              >
                <span class="ml8 common-title-H2">
                  {{ $T("分类用能占比核算") }}
                </span>
                <div class="fr chartsTitle">
                  {{ $T("成本合计") }} （{{ typeUnitName }}）
                  <div class="fr">{{ total.type | formatNum2 }}</div>
                </div>
              </el-header>
              <div class="chartsBox">
                <CetChart
                  :inputData_in="CetChart_3.inputData_in"
                  v-bind="CetChart_3.config"
                />
              </div>
            </div>
          </div>
          <div class="tabBox flex-column mtJ3 bg1 eem-cont flex-auto">
            <div class="clearfix mbJ3">
              <span class="common-title-H2">{{ $T("能源流向核算占比") }}</span>
              <CetButton
                class="fr ml8"
                v-bind="CetButton_1"
                v-on="CetButton_1.event"
              ></CetButton>
              <CetButton
                class="fr ml8"
                v-bind="CetButton_2"
                v-on="CetButton_2.event"
              ></CetButton>
              <ElInput
                class="fr"
                suffix-icon="el-icon-search"
                v-model="ElInput_1.value"
                v-bind="ElInput_1"
                v-on="ElInput_1.event"
              ></ElInput>
              <!-- <div class="basic-box fr mr8">
                <customElSelect
                  v-model="ElSelect_3.value"
                  v-bind="ElSelect_3"
                  v-on="ElSelect_3.event"
                  :prefix_in="$T('查看')"
                >
                  <ElOption
                    v-for="item in ElOption_3.options_in"
                    :key="item[ElOption_3.key]"
                    :label="item[ElOption_3.label]"
                    :value="item[ElOption_3.value]"
                    :disabled="item[ElOption_3.disabled]"
                  ></ElOption>
                </customElSelect>
              </div> -->
            </div>
            <div class="flex-auto">
              <CetTable
                v-show="ElSelect_3.value == 1"
                :data.sync="CetTable_1.data"
                :dynamicInput.sync="CetTable_1.dynamicInput"
                v-bind="CetTable_1"
                v-on="CetTable_1.event"
              >
                <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
                <ElTableColumn
                  v-bind="ElTableColumn_objectName"
                ></ElTableColumn>
                <ElTableColumn
                  v-bind="ElTableColumn_objectPath"
                ></ElTableColumn>
                <ElTableColumn
                  v-bind="ElTableColumn_energyRate"
                ></ElTableColumn>
                <ElTableColumn
                  v-bind="ElTableColumn_energyValue"
                ></ElTableColumn>
                <ElTableColumn v-bind="ElTableColumn_cost"></ElTableColumn>
                <ElTableColumn v-bind="ElTableColumn_lossRate"></ElTableColumn>
                <ElTableColumn v-bind="ElTableColumn_detail">
                  <template slot-scope="scope">
                    <!-- <i
                    class="row-detail"
                    @click="handleDetail(scope.$index, scope.row)"
                  ></i> -->
                    <span
                      class="handel"
                      @click.stop="handleDetail(scope.$index, scope.row)"
                    >
                      {{ $T("查看") }}
                    </span>
                  </template>
                </ElTableColumn>
              </CetTable>
            </div>
            <div v-show="ElSelect_3.value == 2" style="height: 400px">
              <CetChart
                :inputData_in="CetChart_4.inputData_in"
                v-bind="CetChart_4.config"
              />
            </div>
          </div>
        </div>
      </el-container>
    </el-container>
    <ProportionAnalysisDetail
      :visibleTrigger_in="ProportionAnalysisDetail.visibleTrigger_in"
      :closeTrigger_in="ProportionAnalysisDetail.closeTrigger_in"
      :queryId_in="ProportionAnalysisDetail.queryId_in"
      :inputData_in="ProportionAnalysisDetail.inputData_in"
      :symbol_in="ProportionAnalysisDetail.symbol_in"
      @finishTrigger_out="ProportionAnalysisDetail_finishTrigger_out"
      @finishData_out="ProportionAnalysisDetail_finishData_out"
      @saveData_out="ProportionAnalysisDetail_saveData_out"
      @currentData_out="ProportionAnalysisDetail_currentData_out"
    />
  </div>
</template>
<script>
import ProportionAnalysisDetail from "./ProportionAnalysisDetail.vue";
import common from "eem-utils/common";
import costanalysisAPI from "../api/costanalysisAPI.js";
import TREE_PARAMS from "@/store/treeParams.js";
import {
  setValue,
  setPercent,
  setStartAndEndTime,
  colors,
  pieItemStyle
} from "../utils/utils.js";

export default {
  name: "ProportionAnalysis",
  components: {
    ProportionAnalysisDetail
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    timeRange() {
      const dateType = this.CetDatePicker_1.config.type;
      return setStartAndEndTime(this.CetDatePicker_1.val, dateType);
    },
    projectId() {
      return this.$store.state.projectId;
    },
    hideCostanalysisRegionEnergyProportion() {
      return this.$store.state.systemCfg.hideCostanalysisRegionEnergyProportion;
    }
  },

  data() {
    const language = window.localStorage.getItem("omega_language") === "en";
    return {
      unitName: "元",
      classUnitName: "元",
      typeUnitName: "元",
      currentTabItem: null,
      CetButton_1: {
        visible_in: false,
        disable_in: false,
        title: $T("钻取"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: false,
        disable_in: false,
        title: $T("返回上一层"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetChart_1: {
        inputData_in: {},
        config: {
          options: {
            color: colors,
            title: {
              text: "区域用能占比核算",
              show: false
            },
            legend: {
              type: "scroll",
              bottom: 10
            },
            tooltip: {
              trigger: "item",
              // formatter: "{a} <br/>{b} : {c} ({d}%)"
              formatter: function (val) {
                var value = common.formatNum(val.value);
                return `${val.seriesName}<br/>${val.name} : ${value} ${val.data.unit}(${val.percent}%)`;
              }
            },
            series: [
              {
                name: "区域用能占比核算",
                type: "pie",
                radius: "60%",
                center: ["50%", "40%"],
                itemStyle: pieItemStyle,
                data: [],
                bottom: 30
              }
            ]
          }
        }
      },
      CetChart_2: {
        inputData_in: {},
        config: {
          options: {
            color: colors,
            title: {
              text: "分项用电占比核算",
              show: false
            },
            legend: {
              type: "scroll",
              bottom: 10
            },
            tooltip: {
              trigger: "item",
              // formatter: "{a} <br/>{b} : {c} ({d}%)"
              formatter: function (val) {
                var value = common.formatNum(val.value);
                return `${val.seriesName}<br/>${val.name} : ${value} 
                ${val.data && val.data.unit ? val.data.unit : ""}
                (${val.percent}%)`;
              }
            },
            series: [
              {
                name: "分项用电占比核算",
                type: "pie",
                radius: "60%",
                center: ["50%", "40%"],
                itemStyle: pieItemStyle,
                data: [],
                bottom: 30
              }
            ]
          }
        }
      },
      CetChart_3: {
        inputData_in: {},
        config: {
          options: {
            color: colors,
            title: {
              text: "分类用电占比核算",
              show: false
            },
            legend: {
              type: "scroll",
              bottom: 10
            },
            tooltip: {
              trigger: "item",
              // formatter: "{a} <br/>{b} : {c} ({d}%)"
              formatter: function (val) {
                var value = common.formatNum(val.value);
                return `${val.seriesName}<br/>${val.name} : ${value} ${
                  val.data && val.data.unit ? val.data.unit : ""
                }(${val.percent}%)`;
              }
            },
            series: [
              {
                name: "分类用电占比核算",
                type: "pie",
                radius: "60%",
                center: ["50%", "40%"],
                itemStyle: pieItemStyle,
                data: [],
                bottom: 30
              }
            ]
          }
        }
      },
      CetChart_4: {
        inputData_in: {},
        config: {
          options: {
            title: {
              text: "中电园区管理维度能流图",
              left: "center"
            },
            series: [
              {
                left: 50.0,
                top: 20.0,
                right: 150.0,
                bottom: 25.0,
                type: "sankey",
                data: [
                  {
                    name: "a"
                  },
                  {
                    name: "b"
                  },
                  {
                    name: "a1"
                  },
                  {
                    name: "a2"
                  },
                  {
                    name: "b1"
                  },
                  {
                    name: "c"
                  }
                ],
                links: [
                  {
                    source: "a",
                    target: "a1",
                    value: 5
                  },
                  {
                    source: "a",
                    target: "a2",
                    value: 3
                  },
                  {
                    source: "b",
                    target: "b1",
                    value: 8
                  },
                  {
                    source: "a",
                    target: "b1",
                    value: 3
                  },
                  {
                    source: "b1",
                    target: "a1",
                    value: 1
                  },
                  {
                    source: "b1",
                    target: "c",
                    value: 2
                  }
                ],
                levels: [
                  {
                    depth: 0,
                    itemStyle: {
                      color: "#fbb4ae"
                    },
                    lineStyle: {
                      color: "source",
                      opacity: 0.6
                    }
                  },
                  {
                    depth: 1,
                    itemStyle: {
                      color: "#b3cde3"
                    },
                    lineStyle: {
                      color: "source",
                      opacity: 0.6
                    }
                  },
                  {
                    depth: 2,
                    itemStyle: {
                      color: "#ccebc5"
                    },
                    lineStyle: {
                      color: "source",
                      opacity: 0.6
                    }
                  },
                  {
                    depth: 3,
                    itemStyle: {
                      color: "#decbe4"
                    },
                    lineStyle: {
                      color: "source",
                      opacity: 0.6
                    }
                  }
                ]
              }
            ],
            tooltip: {
              trigger: "item",
              triggerOn: "mousemove"
            }
          }
        }
      },
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out
        }
      },
      ElSelect_1: {
        value: 2,
        style: {},
        size: "small",
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: 17,
        style: {},
        size: "small",
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [
          {
            id: 17,
            text: $T("按年")
          },
          {
            id: 14,
            text: $T("按月")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetDatePicker_1: {
        disable_in: false,
        val: this.$moment().startOf("year").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "year",
          rangeSeparator: "-",
          style: {
            display: "inline-block"
          },
          size: "small",
          clearable: false,
          pickerOptions: common.pickerOptions_earlierThanYesterd11
        }
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "mini",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: true,
        title: "",
        size: "mini",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      ElSelect_3: {
        value: 1,
        style: {},
        size: "small",
        event: {
          change: this.ElSelect_3_change_out
        }
      },
      ElOption_3: {
        options_in: [
          {
            id: 1,
            text: $T("表格")
          }
          // {
          //   id: 2,
          //   text: "能流关系图"
          // }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElInput_1: {
        value: "",
        style: {
          width: "200px"
        },
        size: "small",
        placeholder: $T("输入关键字以检索"),
        event: {
          change: this.ElInput_1_change_out,
          input: this.ElInput_1_input_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        // prop: "", // 支持path a[0].b
        label: "#", //列名
        headerAlign: "left",
        align: "left",
        fixed: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "65" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_objectName: {
        //type: "",      // selection 勾选 index 序号
        prop: "objectName", // 支持path a[0].b
        label: $T("对象节点"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "150px" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_objectPath: {
        //type: "",      // selection 勾选 index 序号
        prop: "objectPath", // 支持path a[0].b
        label: $T("所属层级"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "200px" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_energyRate: {
        //type: "",      // selection 勾选 index 序号
        prop: "energyRate", // 支持path a[0].b
        label: $T("能流占比"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "240px" : "150px" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_energyValue: {
        //type: "",      // selection 勾选 index 序号
        prop: "energyValue", // 支持path a[0].b
        label: $T("能耗统计（kWh）"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "300px" : "150px" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_cost: {
        //type: "",      // selection 勾选 index 序号
        prop: "cost", // 支持path a[0].b
        label: $T("成本合计（元）"), //列名
        headerAlign: "right",
        align: "right",
        showOverflowTooltip: true,
        minWidth: "200" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_lossRate: {
        //type: "",      // selection 勾选 index 序号
        prop: "lossRate", // 支持path a[0].b
        label: $T("损耗占比(%)"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "200" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_detail: {
        //type: "",      // selection 勾选 index 序号
        // prop: "", // 支持path a[0].b
        label: $T("详情"), //列名
        headerAlign: "left",
        align: "left",
        fixed: "right",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "80" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ProportionAnalysisDetail: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        symbol_in: "--"
      },
      firstLoad: true,
      currentNode: { children: [] },
      tableResult: [],
      total: {
        section: "",
        class: "",
        type: ""
      }
    };
  },
  watch: {
    "CetDatePicker_1.val": function (val) {
      if (this.ElSelect_2.value == 17) {
        let date = this.$moment(this.CetDatePicker_1.val);
        if (date.year() >= this.$moment().year()) {
          this.CetButton_next.disable_in = true;
        } else {
          this.CetButton_next.disable_in = false;
        }
      } else if (this.ElSelect_2.value == 14) {
        let date = this.$moment(this.CetDatePicker_1.val);
        if (
          date.startOf("month").valueOf() >=
          this.$moment().startOf("month").valueOf()
        ) {
          this.CetButton_next.disable_in = true;
        } else {
          this.CetButton_next.disable_in = false;
        }
      }

      if (val && !this.firstLoad && this.currentNode.id) {
        this.CetDatePicker_1.val = this._.clone(val);
        this.getSectionData();
        this.getClassData();
        this.getTypeData();
        this.getEnergyFlowTableRate();
      }
    },
    "ElInput_1.value"(newValue) {
      if (newValue) {
        this.CetTable_1.data = this.tableResult.filter(item =>
          item.objectName.includes(newValue)
        );
      } else {
        this.CetTable_1.data = this.tableResult;
      }
    }
  },

  methods: {
    getTreeData() {
      this.CetGiantTree_1.inputData_in = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      const data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.costanalysisTree,
        treeReturnEnable: true
      };
      costanalysisAPI.getNodeTree(data).then(result => {
        this.CetGiantTree_1.inputData_in = result;
        const queryItem = this.$store.state.costanalysis.shareQuery;
        console.log("queryItem", queryItem);
        if (queryItem.selectNode) {
          const selectNode = this._.cloneDeep(queryItem.selectNode);
          this.CetGiantTree_1.selectNode = selectNode;
          this.CetDatePicker_1.val = queryItem.selectYear;
          this.ElSelect_1.value = queryItem.energyId;
          this.currentNode = selectNode;
          this.ElSelect_2_change_out(queryItem.dateType);
          this.$store.commit("setShareQuery", {});
        } else {
          if (result.length > 0) {
            this.CetGiantTree_1.selectNode = result[0];
          }
        }
        this.firstLoad = false;
      });
    },
    getProjectEnergy() {
      costanalysisAPI.getProjectEnergy(this.projectId).then(result => {
        let options = [];
        result.forEach(item => {
          if (![18, 22].includes(item.energytype)) {
            options.push({
              id: item.energytype,
              name: item.name,
              symbol: common.formatSymbol(item.symbol) || "--"
            });
          }
        });
        this.ElOption_1.options_in = options;
      });
    },
    getSectionData() {
      const [startTime, endTime] = this.timeRange;
      let nodes = [];
      if (
        this.currentNode.children &&
        Array.isArray(this.currentNode.children)
      ) {
        nodes = this.currentNode.children.map(item => {
          return {
            id: item.id,
            modelLabel: item.modelLabel,
            name: item.name
          };
        });
      }
      if (nodes.length === 0) {
        this.CetChart_1.config.options.series[0].data = [];
        this.total.section = [];
        return;
      }
      const data = {
        startTime,
        endTime,
        nodes,
        cycle: this.ElSelect_2.value,
        energyType: this.ElSelect_1.value,
        queryType:
          this.ElSelect_2.value == 17 ? 1 : this.ElSelect_2.value == 14 ? 3 : 0,
        costKpiType: 0,
        projectId: this.projectId
      };
      costanalysisAPI.getCostByNode(data).then(result => {
        this.CetChart_1.config.options.series[0].data = result.map(item => {
          const itemData = item.data ? item.data[0] : {};
          return {
            value: setValue(itemData.value, 0),
            name: item.objectName,
            unit: item.unitName,
            label: {
              formatter: "{b}: {d}%"
            }
          };
        });
        this.total.section = result.reduce((acc, current) => {
          const itemData = current.data ? current.data[0] : {};
          if (itemData.value) {
            acc = acc + itemData.value * 1;
            acc = acc.toFixed(2) * 1;
          }
          return acc;
        }, 0);
        this.unitName = this._.get(result, "[0].unitName", "元") || "--";
      });
    },
    getClassData() {
      const dimId = 1; // 分项用能
      costanalysisAPI
        .getPropertysByDimId(dimId)
        .then(result => {
          const tags = result.map(item => {
            return {
              name: item.name,
              tag: item.id
            };
          });
          const [startTime, endTime] = this.timeRange;
          const node = {
            id: this.currentNode.id,
            modelLabel: this.currentNode.modelLabel,
            name: this.currentNode.name
          };
          const data = {
            startTime,
            endTime,
            node,
            cycle: this.ElSelect_2.value,
            energyType: this.ElSelect_1.value,
            queryType:
              this.ElSelect_2.value == 17
                ? 1
                : this.ElSelect_2.value == 14
                ? 3
                : 0,
            tags,
            queryTotal: false,
            projectId: this.projectId
          };
          if (tags.length === 0) {
            return [];
          }
          return costanalysisAPI.getCostByDimTagId(data);
        })
        .then(result => {
          this.CetChart_2.config.options.series[0].data = result.map(item => {
            const itemData = item.data ? item.data[0] : {};
            return {
              value: setValue(itemData.value, 0),
              name: item.dimTagName || "--",
              unit: item.unitName,
              label: {
                formatter: "{b}: {d}%"
              }
            };
          });
          this.total.class = result.reduce((acc, current) => {
            const itemData = current.data ? current.data[0] : {};
            if (itemData.value) {
              acc = acc + itemData.value * 1;
              acc = acc.toFixed(2) * 1;
            }
            return acc;
          }, 0);
          this.classUnitName = this._.get(result, "[0].unitName", "元") || "--";
        });
    },
    getTypeData() {
      const dimId = 2; // 分类用能
      costanalysisAPI
        .getPropertysByDimId(dimId)
        .then(result => {
          const tags = result.map(item => {
            return {
              name: item.name,
              tag: item.id
            };
          });
          const [startTime, endTime] = this.timeRange;
          const node = {
            id: this.currentNode.id,
            modelLabel: this.currentNode.modelLabel,
            name: this.currentNode.name
          };
          const data = {
            startTime,
            endTime,
            node,
            cycle: this.ElSelect_2.value,
            energyType: this.ElSelect_1.value,
            queryType:
              this.ElSelect_2.value == 17
                ? 1
                : this.ElSelect_2.value == 14
                ? 3
                : 0,
            tags,
            queryTotal: false,
            projectId: this.projectId
          };
          if (tags.length === 0) {
            return [];
          }
          return costanalysisAPI.getCostByDimTagId(data);
        })
        .then(result => {
          this.CetChart_3.config.options.series[0].data = result.map(item => {
            const itemData = item.data ? item.data[0] : {};
            return {
              value: setValue(itemData.value, 0),
              name: item.dimTagName || "--",
              unit: item.unitName,
              label: {
                formatter: "{b}: {d}%"
              }
            };
          });
          this.total.type = result.reduce((acc, current) => {
            const itemData = current.data ? current.data[0] : {};
            if (itemData.value) {
              acc = acc + itemData.value * 1;
              acc = acc.toFixed(2) * 1;
            }
            return acc;
          }, 0);
          this.typeUnitName = this._.get(result, "[0].unitName", "元") || "--";
        });
    },
    getEnergyFlowTableRate() {
      const vm = this;
      const [startTime, endTime] = vm.timeRange;
      const parentNode = {
        id: vm.currentNode.id,
        modelLabel: vm.currentNode.modelLabel,
        name: vm.currentNode.name
      };
      let subNodes = [];
      if (vm.currentNode.children && Array.isArray(vm.currentNode.children)) {
        subNodes = vm.currentNode.children.map(item => {
          return {
            id: item.id,
            modelLabel: item.modelLabel,
            name: item.name,
            path: item.path
          };
        });
      }
      if (subNodes.length === 0) {
        vm.tableResult = [];
        vm.CetTable_1.data = [];
        return;
      }
      const data = {
        parentNode,
        subNodes,
        startTime,
        endTime,
        cycle: vm.ElSelect_2.value,
        energyType: vm.ElSelect_1.value
      };
      vm.ElTableColumn_cost.label = $T("成本合计（元）");
      costanalysisAPI.getEnergyFlowTableRate(data).then(result => {
        vm.ElTableColumn_cost.label = `${$T("成本合计")}（${vm._.get(
          result,
          "[0].unitName",
          $T("元")
        )}）`;
        vm.tableResult = result.map(item => {
          return {
            objectName: item.objectName,
            objectLabel: item.objectLabel,
            objectId: item.objectId,
            objectPath: item.objectPath || "--",
            energyRate: setPercent(item.energyRate),
            energyValue: setValue(item.energyValue),
            cost: setValue(item.cost),
            lossRate: item.lossRate ? (item.lossRate * 100).toFixed2(2) : "--"
          };
        });
        vm.CetTable_1.data = vm.tableResult;
        const energtTypeItem = vm.ElOption_1.options_in.find(
          item => item.id === vm.ElSelect_1.value
        );
        vm.ElTableColumn_energyValue.label =
          vm.ElTableColumn_energyValue.label.replace(
            /\（.*\）/g,
            "（" + energtTypeItem.symbol + "）"
          );
      });
    },
    ElInput_1_change_out(val) {},
    ElInput_1_input_out(val) {},
    ElSelect_1_change_out(val) {
      if (val && !this.firstLoad) {
        console.log("ElSelect_1_change_out");
        this.ElSelect_1.value = this._.clone(val);
        this.getSectionData();
        this.getClassData();
        this.getTypeData();
        this.getEnergyFlowTableRate();
      }
    },
    ElSelect_2_change_out(val) {
      this.ElSelect_2.value = val;
      let date = this.$moment(this.CetDatePicker_1.val);
      if (val == 17) {
        this.CetDatePicker_1.config.type = "year";
        if (date.year() >= this.$moment().year()) {
          this.CetButton_next.disable_in = true;
        } else {
          this.CetButton_next.disable_in = false;
        }
      } else if (val == 14) {
        this.CetDatePicker_1.config.type = "month";
        if (date.month() >= this.$moment().month()) {
          this.CetButton_next.disable_in = true;
        } else {
          this.CetButton_next.disable_in = false;
        }
      }

      if (val && !this.firstLoad) {
        console.log("ElSelect_2_change_out");
        this.getSectionData();
        this.getClassData();
        this.getTypeData();
        this.getEnergyFlowTableRate();
      }
    },
    ElSelect_3_change_out(val) {},
    CetTable_1_outputData_out(val) {},
    CetTable_1_record_out(val) {
      this.currentTabItem = val;
    },
    handleDetail(index, row) {
      var symbol = this.ElOption_1.options_in.filter(
        item => item.id == this.ElSelect_1.value
      )[0].symbol;
      this.ProportionAnalysisDetail.symbol_in = symbol || "--";
      this.ProportionAnalysisDetail.inputData_in = this._.cloneDeep(row);
      this.ProportionAnalysisDetail.inputData_in.energyType$text =
        this.ElOption_1.options_in.filter(
          item => item.id == this.ElSelect_1.value
        )[0].name;
      this.ProportionAnalysisDetail.visibleTrigger_in = new Date().getTime();
    },
    CetGiantTree_1_currentNode_out(val) {
      if (val && !this.firstLoad) {
        // if (val.childSelectState == 2) {
        //   this.$message.warning("你没有此节点的全部权限！");
        //   return;
        // }
        this.currentNode = this._.cloneDeep(val);
        this.getSectionData();
        this.getClassData();
        this.getTypeData();
        this.getEnergyFlowTableRate();
        if (val.modelLabel == "project") {
          this.CetButton_2.visible_in = false;
        } else {
          this.CetButton_2.visible_in = true;
        }
        if (val.children && val.children.length > 0) {
          this.CetButton_1.visible_in = true;
        } else {
          this.CetButton_1.visible_in = false;
        }
      }
    },
    ProportionAnalysisDetail_currentData_out(val) {},
    ProportionAnalysisDetail_finishData_out(val) {},
    ProportionAnalysisDetail_finishTrigger_out(val) {},
    ProportionAnalysisDetail_saveData_out(val) {},
    CetButton_prv_statusTrigger_out(val) {
      if (this.ElSelect_2.value == 17) {
        let date = this.$moment(this.CetDatePicker_1.val);
        this.CetDatePicker_1.val = date.subtract(1, "year").valueOf();
      } else if (this.ElSelect_2.value == 14) {
        let date = this.$moment(this.CetDatePicker_1.val);
        this.CetDatePicker_1.val = date.subtract(1, "month").valueOf();
      }
    },
    CetButton_next_statusTrigger_out(val) {
      if (this.ElSelect_2.value == 17) {
        let date = this.$moment(this.CetDatePicker_1.val);
        this.CetDatePicker_1.val = date.add(1, "year").valueOf();
      } else if (this.ElSelect_2.value == 14) {
        let date = this.$moment(this.CetDatePicker_1.val);
        this.CetDatePicker_1.val = date.add(1, "month").valueOf();
      }
    },

    CetButton_1_statusTrigger_out(val) {
      if (this.currentNode.children && this.currentNode.children.length >= 1) {
        const selectNode = this.currentNode.children.find(
          item => item.id == this.currentTabItem.objectId
        );
        this.CetGiantTree_1.selectNode = selectNode;
      }
    },
    CetButton_2_statusTrigger_out() {
      this.CetGiantTree_1.selectNode = this._.cloneDeep(
        this.currentNode.getParentNode()
      );
    }
  },
  filters: {},
  created: function () {},
  mounted: function () {
    this.getTreeData();
    this.getProjectEnergy();
  },
  beforeDestroy() {
    this.$store.commit("setShareQuery", {});
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}
.headcolor {
  @include background_color("BG1");
}
.footcolor {
  @include background_color("BG1");
}
.fontcolor {
  @include font-color("Sta4");
}
.padding-B {
  border-bottom: 1px solid #fff;
  @include border_direction_color("B2", bottom);
}
.border-b {
  @include padding_right(J3);
}
.basic-box-label {
  height: 32px;
  line-height: 32px;
  margin-top: 14px;
}
.chartsList {
  & > div {
    flex: 1;
    box-sizing: border-box;
    @include margin(0 J1);
    @include border_radius(C);
    &:nth-child(1) {
      margin-left: 0;
    }
    .chartsTitle {
      box-shadow: none;
      @include font_size("Aa");
      color: rgb(0, 102, 204);
      background: linear-gradient(
        rgb(52, 143, 218) 0%,
        rgb(52, 143, 218) 0%,
        rgb(27, 61, 199) 100%,
        rgb(27, 61, 199) 100%
      );
      border-width: initial;
      border-style: none;
      border-color: initial;
      border-image: initial;
      @include border_radius(C);
      @include font_color(T5);
      line-height: 30px;
      margin: 5px 5px 0 0;
      padding: 0 5px;
      width: 280px;
    }
    .chartsBox {
      height: 260px;
      padding-top: 40px;
      @include background_color("BG1");
    }
    .chartsBox-empty {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      @include font_size("H3");
    }
  }
}
.tabBox {
  box-sizing: border-box;
  @include padding(J3 J4 J3 J4);
}
.row-detail {
  display: inline-block;
  width: 25px;
  height: 24px;
  cursor: pointer;
  background: url("../assets/details.png") no-repeat center center;
  border-radius: 50%;
  vertical-align: middle;
}
.handel {
  cursor: pointer;
  @include font_color(ZS);
  &.delete {
    @include font_color(Sta3);
  }
}
</style>
