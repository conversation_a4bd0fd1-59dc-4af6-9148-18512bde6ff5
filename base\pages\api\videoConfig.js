import fetch from "eem-utils/fetch";

function transConditions(data) {
  const conditions = _.get(data, "rootCondition.filter.expressions", []);
  if (conditions.length == 0) return data;
  const id = _.get(data, "rootCondition.treeNode.id");
  const modelLabel = _.get(data, "rootCondition.treeNode.modelLabel");
  const page = _.get(data, "rootCondition.page");
  const result = {};
  conditions.forEach(item => {
    result[item.prop] = item.limit;
  });
  result.page = page;
  return result;
}

function processResponse(response) {
  return response;
}

// 获取视频文件夹节点树
export function videoQueryFolders(params) {
  return fetch({
    url: `/eem-service/video/config/allCameraFolds`,
    method: "GET",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    params
  });
}

// 添加文件夹节点
export function videoCreateFolder(data) {
  return fetch({
    url: `/eem-service/video/config/cameraFold`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data
  });
}

// 重命名文件夹节点
export function videoRenameFolder(data) {
  return fetch({
    url: `/eem-service/video/config/cameraFold`,
    method: "PUT",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data
  });
}

// 删除文件夹节点
export function videoDeleteFolder(id) {
  return fetch({
    url: `/eem-service/video/config/cameraFold?id=${id}`,
    method: "DELETE",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

// 获取摄像头表格数据
export function videoQueryVideos(data) {
  return fetch({
    url: `/eem-service/video/config/camera/page`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: transConditions(data)
  });
}

// 新增摄像头
export function videoCreateVideo(data) {
  delete data.modelLabel;
  return fetch({
    url: `/eem-service/video/config/camera`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: transConditions(data)
  });
}

// 编辑摄像头
export function videoUpdateVideo(data) {
  delete data.modelLabel;
  return fetch({
    url: `/eem-service/video/config/camera`,
    method: "PUT",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: transConditions(data)
  });
}

// 删除摄像头
export function videoDeleteVideo(data) {
  return fetch({
    url: `/eem-service/video/config/cameras`,
    method: "DELETE",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data
  });
}

// 导入摄像头数据
export function importVideoConfig(data) {
  return fetch({
    url: `/eem-service/video/config/excel/import`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data
  });
}
