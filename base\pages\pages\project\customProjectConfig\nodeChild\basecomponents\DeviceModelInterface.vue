<template>
  <div class="not-in">
    <CetInterface
      :data.sync="CetInterface_DeviceModel.data"
      :dynamicInput.sync="CetInterface_DeviceModel.dynamicInput"
      v-bind="CetInterface_DeviceModel"
      v-on="CetInterface_DeviceModel.event"
    ></CetInterface>
  </div>
</template>
<script>
import modelEnums from "./modelenums";
const PRIMITIVE_TYPES = [
  "int4",
  "int8",
  "long",
  "float",
  "string",
  "jsonb",
  "boolean"
];
const customWeight = {
  devicetype: 98,
  name: 100,
  code: 99,
  pic: -99,
  picture: -99,
  document: -100
};
const customDeviceModels = {
  // battery: {
  //   propertyList: [
  //     {
  //       propertyLabel: "name",
  //       defaultValue: "你好，我是定制名称"
  //     }
  //   ]
  // }
};
const cachedDeviceModels = {};

export default {
  name: "DeviceModelInterface",
  props: {
    deviceLabel_in: {
      type: String,
      default: null
    },
    refreshTrigger_in: {
      type: Number,
      default: new Date().getTime()
    }
  },
  data() {
    return {
      CetInterface_DeviceModel: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "getDeviceModel",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {},
        page_in: null, // exp:{ index: 1, limit: 20 }
        defaultSort: null,
        event: {
          result_out: this.CetInterface_DeviceModel_result_out,
          finishTrigger_out: this.CetInterface_DeviceModel_finishTrigger_out,
          failTrigger_out: this.CetInterface_DeviceModel_failTrigger_out,
          totalNum_out: this.CetInterface_DeviceModel_totalNum_out
        }
      }
    };
  },
  watch: {
    refreshTrigger_in() {
      this.refresh();
    }
  },
  methods: {
    // 设备模型组件相关接口
    CetInterface_DeviceModel_finishTrigger_out(val) {},
    CetInterface_DeviceModel_failTrigger_out(val) {},
    CetInterface_DeviceModel_totalNum_out(val) {},
    CetInterface_DeviceModel_result_out(val) {
      let vm = this;
      vm.updateEnumerations(val.propertyList)
        .then(() => {
          cachedDeviceModels[vm.deviceLabel_in] =
            vm.processDeviceModelData(val);
          vm.outputModel(vm.deviceLabel_in);
        })
        .catch(e => {});
    },

    // 更新需要的枚举值
    updateEnumerations(propertyList) {
      let vm = this;
      let modelLabels = [];
      propertyList = propertyList || [];
      propertyList.forEach(propItem => {
        if (PRIMITIVE_TYPES.indexOf(propItem.dataType) === -1) {
          modelLabels.push(propItem.dataType);
        }
      });

      let p = new Promise((resolve, reject) => {
        modelEnums
          .updateEnums(modelLabels)
          .then(() => {
            resolve();
          })
          .catch(() => {
            reject();
          });
      });

      return p;
    },

    // 刷新数据
    refresh() {
      let vm = this;
      if (!vm.deviceLabel_in) {
        return;
      }

      if (cachedDeviceModels[vm.deviceLabel_in]) {
        vm.outputModel(vm.deviceLabel_in);
        return;
      }

      vm.CetInterface_DeviceModel.dataConfig.modelLabel = vm.deviceLabel_in;
      vm.$nextTick(() => {
        vm.CetInterface_DeviceModel.queryTrigger_in = new Date().getTime();
      });
    },

    // 输出模型
    outputModel(deviceLabel) {
      let vm = this;
      let mergedModel = vm.mergeModel(deviceLabel);
      vm.$emit("deviceModel_out", vm._.cloneDeep(mergedModel));
    },

    // 原始的设备模型较为简陋，需要进行处理
    processDeviceModelData(deviceModelData) {
      let vm = this;
      let mergedModel = vm._.cloneDeep(deviceModelData);
      if (!mergedModel) {
        return mergedModel;
      }

      // 根据权重进行排序
      let mergedPropertylist = mergedModel.propertyList || [];

      // 开始添加额外信息
      vm._.each(mergedPropertylist, propertyItem => {
        let propLabel = propertyItem.propertyLabel;
        let dataType = propertyItem.dataType;

        // 将单位组合到名称中
        propertyItem.name = propertyItem.alias;
        if (propertyItem.unitSymbol) {
          propertyItem.name += "（" + propertyItem.unitSymbol + "）";
        }

        // 精细化数据类型，并获取枚举列表
        let detailedDataType;
        let options = [];
        switch (dataType) {
          case "int4":
          case "long":
          case "int8":
            if (propLabel.slice(-4).toLowerCase() === "date") {
              detailedDataType = "date";
            } else if (
              ["busbarsegi", "busbarsegii", "linesegmentwithswitch_id"].indexOf(
                propLabel.toLowerCase()
              ) > -1
            ) {
              detailedDataType = propLabel;
            } else {
              detailedDataType = "number";
            }
            break;
          case "float":
            if (
              ["longitude", "latitude"].indexOf(propLabel.toLowerCase()) > -1
            ) {
              detailedDataType = "geography";
            } else {
              detailedDataType = "number";
            }
            break;
          case "jsonb":
          case "boolean":
            // case "devicetypeenum":
            detailedDataType = dataType;
            break;
          case "string":
            if (["picture", "pic"].indexOf(propLabel) > -1) {
              detailedDataType = "pic";
            } else if (propLabel.toLowerCase() === "document") {
              detailedDataType = propLabel;
            } else {
              detailedDataType = "string";
            }
            break;
          default:
            options = modelEnums.getEnum(dataType) || [];
            detailedDataType = "enum";
            break;
        }
        propertyItem.detailedDataType = detailedDataType;
        propertyItem.options = options;

        // 根据属性类型，限定小数位个数
        propertyItem.precision = dataType === "float" ? 2 : 0;

        // 限定最大长度，一般只用于字符串数据
        let maxLength = parseInt(propertyItem.maxLength);
        propertyItem.maxLength = isNaN(maxLength) ? 100 : maxLength;
        // 特殊设置，设备编号默认输入长度为20
        if (propLabel.toLowerCase() === "code") {
          propertyItem.maxLength = isNaN(maxLength) ? 20 : maxLength;
        }

        // 限定最大最小值，一般只用于数字数据
        let rangeMax = propertyItem.precision
          ? parseFloat(propertyItem.rangeMax)
          : parseInt(propertyItem.rangeMax);
        let rangeMin = propertyItem.precision
          ? parseFloat(propertyItem.rangeMin)
          : parseInt(propertyItem.rangeMin);
        if (isNaN(rangeMax) && isNaN(rangeMin)) {
          propertyItem.rangeMax = 999999999.99;
          propertyItem.rangeMin = 0;
        } else if (isNaN(rangeMax) && !isNaN(rangeMin)) {
          propertyItem.rangeMax =
            rangeMin >= 999999999.99 ? Infinity : 999999999.99;
          propertyItem.rangeMin = rangeMin;
        } else if (!isNaN(rangeMax) && isNaN(rangeMin)) {
          propertyItem.rangeMax = rangeMax;
          propertyItem.rangeMin = rangeMax > 0 ? 0 : -Infinity;
        } else {
          propertyItem.rangeMax = rangeMax;
          propertyItem.rangeMin = rangeMin;
        }

        // 是否必要
        propertyItem.required = !propertyItem.nullable;

        // 根据属性类型和各种参数，计算默认值
        let defaultValue = propertyItem.defaultValue;
        if (vm._.isNil(defaultValue)) {
          switch (propertyItem.detailedDataType) {
            case "date":
              defaultValue = vm.$moment().startOf("day").valueOf();
              break;
            case "number":
              if (propertyItem.rangeMin <= 0 && propertyItem.rangeMax > 0) {
                defaultValue = 0;
              } else {
                defaultValue = propertyItem.rangeMin;
              }
              break;
            case "string":
              defaultValue = "";
              break;
            case "enum":
              defaultValue = options.length ? options[0].id : null;
              break;
            case "devicetypeenum":
              defaultValue = 1;
              break;
            default:
              defaultValue = null;
          }
        }
        propertyItem.defaultValue = defaultValue;
      });
      return mergedModel;
    },

    // 合并自定义模型和默认模型
    mergeModel(deviceModel) {
      let vm = this;
      let cachedModel = cachedDeviceModels[deviceModel];
      let mergedModel = vm._.cloneDeep(cachedModel);

      if (!mergedModel) {
        return mergedModel;
      }

      // 根据权重进行排序
      let mergedPropertylist = mergedModel.propertyList || [];
      mergedPropertylist.sort((a, b) => {
        let aLabel = a.propertyLabel;
        let aWeight = customWeight.hasOwnProperty(aLabel)
          ? customWeight[aLabel]
          : 0;
        let bLabel = b.propertyLabel;
        let bWeight = customWeight.hasOwnProperty(bLabel)
          ? customWeight[bLabel]
          : 0;

        return bWeight - aWeight;
      });

      // 没有自定义模型，返回缓存模型
      let customModel = customDeviceModels[deviceModel];
      if (!customModel) {
        return mergedModel;
      }

      // 自定义模型或缓存模型里的属性数组为空时，返回缓存模型
      let customPropertylist = customModel.propertyList || [];
      if (!customPropertylist.length || !mergedPropertylist.length) {
        return mergedModel;
      }

      // 将自定义模型和缓存模型进行合并
      vm._.each(customPropertylist, customPropItem => {
        let label = customPropItem.propertyLabel;
        let remove = !!customPropItem.remove;

        let matchedProp = vm._.find(mergedPropertylist, {
          propertyLabel: label
        });
        if (!matchedProp) {
          return true;
        }

        if (remove) {
          vm._.pull(mergedPropertylist, matchedProp);
          return true;
        }

        // 只有这几个属性需要合并，其它都忽略
        vm._.each(
          [
            "alias",
            "dataType",
            "defaultValue",
            "maxLength",
            "nullable",
            "rangeMax",
            "rangeMin",
            "unitSymbol"
          ],
          propName => {
            if (customPropItem.hasOwnProperty(propName)) {
              matchedProp[propName] = customPropItem[propName];
            }
          }
        );
      });

      return mergedModel;
    }
  }
};
</script>
