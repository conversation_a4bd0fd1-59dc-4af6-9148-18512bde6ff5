<template>
  <!-- 1弹窗组件 -->
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="CetDialog">
    <el-container
      style="height: 100%; flex-direction: column"
      class="pJ3 plJ4 prJ4 bg1 brC1"
    >
      <el-container style="height: 100%">
        <el-table
          :data="tableData"
          height="500"
          border
          style="width: 100%"
          @selection-change="handleSelectionChange"
          stripe
        >
          <el-table-column
            label="#"
            type="index"
            align="center"
            width="50"
          ></el-table-column>
          <el-table-column
            :prop="item.prop"
            :label="item.label"
            align="left"
            headerAlign="left"
            v-for="(item, index) in tableColumns"
            :key="index"
          >
            <template slot-scope="scope">
              <!-- 满刻度值 -->
              <div v-if="item.prop == 'fullscalevalue'">
                <ElInputNumber
                  v-model="scope.row[item.prop]"
                  v-bind="ElInputNumber_num"
                  v-on="ElInputNumber_num.event"
                ></ElInputNumber>
              </div>
              <span v-else>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-container>
    </el-container>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import { httping } from "@omega/http";
export default {
  name: "shareRate",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    // 设备
    deviceNodes: {
      type: Array
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data(vm) {
    return {
      tableData: [
        // {
        //   text: "配电室设备",
        //   fullscalevalue: 0.5
        // }
      ],
      tableColumns: [
        {
          prop: "deviceName",
          label: $T("采集设备")
        },
        {
          prop: "fullscalevalue",
          label: $T("满刻度值")
        }
      ],
      // 多选数组
      checkedArray: [],
      CetDialog_1: {
        title: $T("满刻度值"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInputNumber_num: {
        value: "",
        style: {},
        controls: false,
        min: 0,
        max: 999999999999.99,
        step: 0.01,
        precision: 2,
        controlsPosition: "", //right 可选值
        event: {
          change: this.ElInputNumber_num_change_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.checkedArray = [];
      this.getPecdeviceextend(this.deviceNodes);
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {
      var vm = this;
      vm.CetDialog_1.queryId_in = val;
    },
    inputData_in(val) {
      this.CetDialog_1.inputData_in = val;
    }
  },

  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.addSupplyRelation_out();
    },
    CetDialog_1_open_out(val) {},
    CetDialog_1_close_out(val) {},
    ElInputNumber_num_change_out(val) {},
    // 获取满刻度值
    getPecdeviceextend(deviceNodes) {
      if (!deviceNodes || deviceNodes.length == 0) {
        return;
      }
      this.tableData = this._.cloneDeep(deviceNodes);
      var data = {
        rootCondition: {
          filter: {
            expressions: [
              {
                limit: deviceNodes.map(item => item.id),
                operator: "IN",
                prop: "deviceid",
                tagid: 0
              }
            ]
          },
          orders: [
            {
              orderType: "asc",
              priority: 0,
              propertyLabel: "deviceid"
            }
          ]
        },
        rootID: 0,
        rootLabel: "pecdeviceextend",
        treeReturnEnable: true
      };
      httping({
        url: `/eem-service/v1/common/query/oneLayer`,
        method: "POST",
        data
      }).then(
        res => {
          if (res.code == 0 && res.data && res.data.length > 0) {
            res.data.forEach(item => {
              if (
                this.tableData.filter(tabItem => tabItem.id == item.deviceid)
                  .length > 0
              ) {
                const fullscalevalue =
                  item.fullscalevalue || item.fullscalevalue === 0
                    ? item.fullscalevalue
                    : 999999999;
                this.$set(
                  this.tableData.filter(
                    tabItem => tabItem.id == item.deviceid
                  )[0],
                  "fullscalevalue",
                  fullscalevalue
                );
              }
            });
          }
        },
        function () {}
      );
    },
    // tab多选
    handleSelectionChange(val) {
      this.checkedArray = val;
    },
    // 保存
    addSupplyRelation_out() {
      var param = [];
      // 单个
      param = this.tableData.map(item => {
        return {
          // channelid: 0,
          // check: "string",
          deviceid: item.id,
          // devicestatus: 0,
          // energytype: 0,
          fullscalevalue: item.fullscalevalue
          // id: 0,
          // metertype: 0,
          // modelLabel: "string",
          // name: "string",
          // protocaltype: 0,
          // stationid: 0,
          // typeId: "string",
          // typeName: "string"
        };
      });
      httping({
        url: `/eem-service/v1/project/pecDeviceExtend`,
        data: param,
        method: "PUT"
      }).then(
        () => {
          this.$message({
            message: $T("保存成功"),
            type: "success"
          });
          this.CetDialog_1.closeTrigger_in = this._.cloneDeep(
            new Date().getTime()
          );
        },
        function () {}
      );
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep() {
    .el-dialog__body {
      @include background_color(BG);
      @include padding(J1);
    }
  }
}
</style>
