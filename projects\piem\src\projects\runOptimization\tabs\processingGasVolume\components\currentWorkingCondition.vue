<template>
  <div class="page">
    <div class="card">
      <div class="title mbJ1">{{ $T("当前工况") }}</div>
      <div class="content mbJ1">
        {{ fitAndOptimizeData.currentConditions || "--" }}
      </div>
      <div class="mbJ1 flex">
        <div class="title mrJ3">{{ $T("拟合结果") }}</div>
        <div class="mrJ3 flex1">
          <div class="mbJ1">{{ $T("效率-气量拟合公式") }}</div>
          <div class="text-ZS">
            <span class="value">
              {{ fitAndOptimizeData.efficiencyFormula || "--" }}
            </span>
            <span class="fs12">
              {{ fitAndOptimizeData.gasTransmissionVolumeRange || "--" }}
            </span>
          </div>
        </div>
        <div>
          <div class="mbJ1">{{ $T("单耗-气量拟合公式") }}</div>
          <div class="text-ZS">
            <span class="value">
              {{ fitAndOptimizeData.unitConsumptionFormula || "--" }}
            </span>
            <span class="fs12">
              {{ fitAndOptimizeData.gasTransmissionVolumeRange || "--" }}
            </span>
          </div>
        </div>
      </div>
      <div class="title mbJ1">{{ $T("气量优化策略") }}</div>
      <div class="content">{{ fitAndOptimizeData.strategy || "--" }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: "currentWorkingCondition",
  components: {},
  props: {
    fitAndOptimizeData: {
      type: Object
    }
  },
  data() {
    return {};
  },
  watch: {},
  methods: {}
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.card {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  border-radius: 8px;
  @include background_color(BG);
  .title {
    font-weight: bold;
  }
  .content {
    line-height: 28px;
  }
  .value {
    font-size: 14px;
    font-weight: bold;
  }
}
.flex1 {
  flex: 1;
  min-height: 0px;
  min-width: 0px;
}
</style>
