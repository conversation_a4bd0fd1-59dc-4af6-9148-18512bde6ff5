<template>
  <div>
    <CetDialog v-bind="CetDialog_scheme" v-on="CetDialog_scheme.event">
      <el-container class="eem-cont-c1">
        <el-header height="auto" class="padding0 mbJ3">
          <span style="float: left">{{$T("名称")}}：</span>
          <span class="title-text" :title="teamName">
            {{ teamName || "--" }}
          </span>
        </el-header>
        <el-main style="height: 100%; padding: 0px">
          <div class="mbJ3">
            <span>{{$T("模拟量")}}：</span>
          </div>
          <div style="height: 150px">
            <CetTable
              :data.sync="CetTable_simulation.data"
              :dynamicInput.sync="CetTable_simulation.dynamicInput"
              v-bind="CetTable_simulation"
              v-on="CetTable_simulation.event"
            >
              <template v-for="(column, index) in Columns_Simulation">
                <el-table-column
                  v-if="column.custom && column.custom === 'tag'"
                  v-bind="column"
                  :key="index"
                  class-name="font0 hand"
                  label-class-name="font14"
                >
                  <template slot-scope="scope">
                    <el-tag
                      size="small"
                      class="text-middle font14"
                      :effect="column.tagEffect"
                      :type="
                        column.tagTypeFormatter
                          ? column.tagTypeFormatter(scope.row, scope.column)
                          : 'primary'
                      "
                    >
                      {{
                        column.formatter
                          ? column.formatter(
                              scope.row,
                              scope.column,
                              scope.row[column.prop],
                              scope.$index
                            )
                          : scope.row[column.prop]
                      }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  v-else-if="column.custom && column.custom === 'button'"
                  v-bind="column"
                  :key="index"
                  class-name="font0"
                  label-class-name="font14"
                >
                  <template slot-scope="scope">
                    <span
                      class="clickformore"
                      @click.stop="
                        column.onButtonClick
                          ? column.onButtonClick(scope.row, scope.$index)
                          : void 0
                      "
                    >
                      {{
                        column.formatter
                          ? column.formatter(
                              scope.row,
                              scope.column,
                              scope.row[column.prop],
                              scope.$index
                            )
                          : column.text
                      }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-else
                  v-bind="column"
                  :key="index"
                  class-name="hand"
                ></el-table-column>
              </template>
            </CetTable>
          </div>
          <div class="mtJ3 mbJ3">
            <span>{{ $T("状态量") }}:</span>
          </div>
          <div style="height: 150px">
            <CetTable
              :data.sync="CetTable_state.data"
              :dynamicInput.sync="CetTable_state.dynamicInput"
              v-bind="CetTable_state"
              v-on="CetTable_state.event"
            >
              <template v-for="(column, index) in Columns_State">
                <el-table-column
                  v-if="column.custom && column.custom === 'tag'"
                  v-bind="column"
                  :key="index"
                  class-name="font0 hand"
                  label-class-name="font14"
                >
                  <template slot-scope="scope">
                    <el-tag
                      size="small"
                      class="text-middle font14"
                      :effect="column.tagEffect"
                      :type="
                        column.tagTypeFormatter
                          ? column.tagTypeFormatter(scope.row, scope.column)
                          : 'primary'
                      "
                    >
                      {{
                        column.formatter
                          ? column.formatter(
                              scope.row,
                              scope.column,
                              scope.row[column.prop],
                              scope.$index
                            )
                          : scope.row[column.prop]
                      }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  v-else-if="column.custom && column.custom === 'button'"
                  v-bind="column"
                  :key="index"
                  class-name="font0"
                  label-class-name="font14"
                >
                  <template slot-scope="scope">
                    <span
                      class="clickformore"
                      @click.stop="
                        column.onButtonClick
                          ? column.onButtonClick(scope.row, scope.$index)
                          : void 0
                      "
                    >
                      {{
                        column.formatter
                          ? column.formatter(
                              scope.row,
                              scope.column,
                              scope.row[column.prop],
                              scope.$index
                            )
                          : column.text
                      }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-else
                  v-bind="column"
                  :key="index"
                  class-name="hand"
                ></el-table-column>
              </template>
            </CetTable>
          </div>
          <div v-if="showTextQuantity">
            <div class="mtJ3 mbJ3">
              <span>{{ $T("文本量") }}:</span>
            </div>
            <div style="height: 150px">
              <CetTable :data.sync="CetTable_text.data" :dynamicInput.sync="CetTable_text.dynamicInput" v-bind="CetTable_text" v-on="CetTable_text.event">
                <template v-for="(column, index) in Columns_Text">
                  <el-table-column v-if="column.custom && column.custom === 'tag'" v-bind="column" :key="index" class-name="font0 hand" label-class-name="font14">
                    <template slot-scope="scope">
                      <el-tag
                        size="small"
                        class="text-middle font14"
                        :effect="column.tagEffect"
                        :type="column.tagTypeFormatter ? column.tagTypeFormatter(scope.row, scope.column) : 'primary'"
                      >
                        {{ column.formatter ? column.formatter(scope.row, scope.column, scope.row[column.prop], scope.$index) : scope.row[column.prop] }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column
                    v-else-if="column.custom && column.custom === 'button'"
                    v-bind="column"
                    :key="index"
                    class-name="font0"
                    label-class-name="font14"
                  >
                    <template slot-scope="scope">
                      <span class="clickformore" @click.stop="column.onButtonClick ? column.onButtonClick(scope.row, scope.$index) : void 0">{{
                        column.formatter ? column.formatter(scope.row, scope.column, scope.row[column.prop], scope.$index) : column.text
                      }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column v-else v-bind="column" :key="index" class-name="hand"></el-table-column>
                </template>
              </CetTable>
            </div>
          </div>
        </el-main>
      </el-container>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <!-- <CetButton v-bind="CetButton_confirm" v-on="CetButton_confirm.event"></CetButton> -->
      </span>
    </CetDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
export default {
  name: "Scheme",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    showTextQuantity() {
      return this.$store.state.systemCfg.showTextQuantity;
    }
  },
  data(vm) {
    return {
      CetDialog_scheme: {
        title: $T("巡检方案详情"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_scheme_open_out,
          close_out: this.CetDialog_scheme_close_out
        },
        width: "600px",
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // simulation表格组件
      CetTable_simulation: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      // state表格组件
      CetTable_state: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      // text表格组件
      CetTable_text: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      Columns_Simulation: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 60,
          label: "#",
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "name",
          minWidth: 100,
          width: "",
          label: $T("参数名"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "min",
          minWidth: 120,
          width: "",
          label: $T("下限"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "max",
          minWidth: 120,
          width: "",
          label: $T("上限"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        }
      ],
      Columns_State: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 60,
          label: "#",
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "name",
          minWidth: 100,
          width: "",
          label: $T("参数名"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        }
      ],
      Columns_Text: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 60,
          label: "#",
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "name",
          minWidth: 100,
          width: "",
          label: $T("参数名"),
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        }
      ],
      teamName: ""
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in() {
      var vm = this;
      new Promise((res, err) => {
        this.querySchemeDetailXJ_out(res);
      }).then(data => {
        if (data) {
          vm.CetDialog_scheme.openTrigger_in = new Date().getTime();
        } else {
          vm.$message.warning($T("查询巡检方案参数失败"));
        }
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_scheme.closeTrigger_in = val;
    }
  },

  methods: {
    CetDialog_scheme_open_out(val) {},
    CetDialog_scheme_close_out(val) {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_scheme.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_add.localSaveTrigger_in = this._.cloneDeep(val);
    },
    //获取巡检方案详情
    querySchemeDetailXJ_out(callback) {
      const _this = this;
      const params = {
        inspectionschemeid: _this.inputData_in.inspectionschemeid
      };
      if (!_this.inputData_in.inspectionschemeid) {
        callback && callback(false);
        return;
      }
      customApi.querySchemeDetailXJ(params).then(res => {
        if (res.code === 0) {
          const teamName = this._.get(res, "data.name", "");
          const statusQuantity = this._.get(res, "data.statusQuantity", []);
          const analogQuantity = this._.get(res, "data.analogQuantity", []);
          const textQuantity = this._.get(res, "data.textQuantity", []) || [];
          _this.CetTable_simulation.data = analogQuantity;
          _this.CetTable_state.data = statusQuantity;
          _this.CetTable_text.data = textQuantity;
          _this.teamName = teamName;
          callback && callback(true);
        } else {
          callback && callback(false);
        }
      });
    }
  },

  created: function () {},
  mounter: function () {}
};
</script>
<style lang="scss" scoped>
.title-text {
  display: inline-block;
  width: calc(100% - 50px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
