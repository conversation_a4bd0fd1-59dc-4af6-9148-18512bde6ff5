<template>
  <!-- 1弹窗组件 -->
  <div>
    <CetDialog
      class="CetDialog eem-common max"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
    >
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </span>
      <CetForm
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
      >
        <div class="title">{{ $T("基础信息") }}</div>
        <div class="mtJ1 clearfix eem-cont-c1">
          <el-row :gutter="$J3">
            <el-col :span="6">
              <el-form-item :label="$T('服务商名称')" prop="name">
                <ElInput
                  v-model.trim="CetForm_1.data.name"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$T('托管结束')" prop="endtime">
                <el-date-picker
                  v-model="CetForm_1.data.endtime"
                  v-bind="CetDatePicker_1.config"
                  :placeholder="$T('选择日期')"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$T('托管项目个数')" prop="projectNumber">
                <ElInputNumber
                  v-model="CetForm_1.data.projectNumber"
                  v-bind="ElInputNumber_projectNumber"
                  v-on="ElInputNumber_projectNumber.event"
                ></ElInputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <CetButton
                class="fr"
                v-bind="CetButton_infoSave"
                v-on="CetButton_infoSave.event"
              ></CetButton>
            </el-col>
          </el-row>
        </div>
        <el-tabs
          v-show="!authBtnDisable"
          v-model="activeName"
          @tab-click="handleClick"
          class="eem-tabs-custom ptJ2 pbJ1"
        >
          <el-tab-pane
            :label="$T('设备链路层节点')"
            name="pecCore"
          ></el-tab-pane>
          <el-tab-pane :label="$T('实时监控权限')" name="Graph"></el-tab-pane>
          <el-tab-pane
            :label="$T('pecReport报表权限')"
            name="pecReport"
            v-if="!hidePecReport"
          ></el-tab-pane>
          <el-tab-pane
            :label="$T('mReport报表权限')"
            name="mReport"
            v-if="!hideMReport"
          ></el-tab-pane>
          <el-tab-pane
            :label="$T('onlyReport报表权限')"
            name="onlyReport"
            v-if="!hideOnlyReport"
          ></el-tab-pane>
        </el-tabs>
      </CetForm>
      <div class="treeBox eem-cont-c1" v-show="!authBtnDisable">
        <div v-show="activeName === 'pecCore'" class="fullfilled">
          <CetButton
            class="fr mlJ2 saveBtn"
            :disable_in="authBtnDisable"
            v-bind="CetButton_pecCore"
            v-on="CetButton_pecCore.event"
          ></CetButton>
          <CetGiantTree
            ref="pecCoreTree"
            class="CetTree"
            v-bind="CetGiantTree_pecCore"
            v-on="CetGiantTree_pecCore.event"
          ></CetGiantTree>
        </div>
        <div v-show="activeName === 'Graph'" class="fullfilled">
          <CetButton
            class="fr mlJ2 saveBtn"
            :disable_in="authBtnDisable"
            v-bind="CetButton_graph"
            v-on="CetButton_graph.event"
          ></CetButton>
          <CetGiantTree
            ref="graphTree"
            class="CetTree"
            v-bind="CetGiantTree_graph"
            v-on="CetGiantTree_graph.event"
          ></CetGiantTree>
        </div>
        <div v-show="activeName === 'pecReport'" class="fullfilled">
          <CetButton
            class="fr mlJ2 saveBtn"
            :disable_in="authBtnDisable"
            v-bind="CetButton_pecReport"
            v-on="CetButton_pecReport.event"
          ></CetButton>
          <CetGiantTree
            ref="pecReportTree"
            class="CetTree"
            v-bind="CetGiantTree_pecReport"
            v-on="CetGiantTree_pecReport.event"
          ></CetGiantTree>
        </div>
        <div v-show="activeName === 'mReport'" class="fullfilled">
          <CetButton
            class="fr mlJ2 saveBtn"
            :disable_in="authBtnDisable"
            v-bind="CetButton_mReport"
            v-on="CetButton_mReport.event"
          ></CetButton>
          <CetGiantTree
            ref="mReportTree"
            class="CetTree"
            v-bind="CetGiantTree_mReport"
            v-on="CetGiantTree_mReport.event"
          ></CetGiantTree>
        </div>
        <div v-show="activeName === 'onlyReport'" class="fullfilled">
          <CetButton
            class="fr mlJ2 saveBtn"
            :disable_in="authBtnDisable"
            v-bind="CetButton_onlyReport"
            v-on="CetButton_onlyReport.event"
          ></CetButton>
          <CetGiantTree
            ref="onlyReportTree"
            class="CetTree"
            v-bind="CetGiantTree_onlyReport"
            v-on="CetGiantTree_onlyReport.event"
          ></CetGiantTree>
        </div>
      </div>
      <div class="treeBox eem-cont-c1 mtJ2" v-show="authBtnDisable">
        {{ $T("请先保存基本信息") }}
      </div>
    </CetDialog>
  </div>
</template>
<script>
import common from "eem-utils/common";
import customApi from "@/api/custom.js";
export default {
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    currentNode_in: {
      type: Object
    },
    inputData_in: {
      type: Object
    }
  },

  computed: {
    authBtnDisable() {
      return this.tenantId ? false : true;
    },
    serviceAdmin() {
      var vm = this;
      if (vm.$store.state.userInfo.customConfig) {
        let customConfig = JSON.parse(vm.$store.state.userInfo.customConfig);
        if (customConfig && customConfig.usertype) {
          return customConfig.usertype === 2;
        }
      }
      console.log("usertype不存在");
      return false;
    },
    isRoot() {
      return this.$store.state.userInfo.id === 1;
    },
    hidePecReport() {
      return this._.get(
        this.$store.state,
        "systemCfg.platformusermanageTabs.hidePecReport"
      );
    },
    hideMReport() {
      return this._.get(
        this.$store.state,
        "systemCfg.platformusermanageTabs.hideMReport"
      );
    },
    hideOnlyReport() {
      return this._.get(
        this.$store.state,
        "systemCfg.platformusermanageTabs.hideOnlyReport"
      );
    }
  },
  data(vm) {
    const addDiyDom = (treeId, treeNode) => {
        var aObj = $("#" + treeNode.tId + "_a");
        if (treeNode.level === 0 && vm.treeNodeTooltip) {
          vm.treeNodeTooltip = false;
          const dom = `
            <div class="inline-block relative tooltipBox">
              <i class="el-icon-question fcT2"></i>
              <div class="tooltip el-tooltip__popper is-light" x-placement="bottom">${$T(
                "置灰代表未拥有此节点全部权限"
              )}
                <div x-arrow="" class="popper__arrow" style="left: 49.5px;"></div>
              </div>
            </div>`;
          aObj.append(dom);
        }
      },
      setNodeClasses = (treeId, treeNode) => {
        return treeNode.childSelectState !== 1
          ? { add: ["halfSelectedNode"] }
          : { remove: ["halfSelectedNode"] };
      };
    return {
      treeNodeTooltip: true,
      tenantId: 0,
      lastName: "pecCore",
      activeName: "pecCore",
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          close: this.CetDialog_1_close_out
        }
      },
      CetButton_infoSave: {
        visible_in: true,
        disable_in: false,
        title: $T("保存基本信息"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_infoSave_statusTrigger_out
        }
      },
      CetButton_pecCore: {
        visible_in: true,
        // disable_in: false,
        title: $T("保存设备权限"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_pecCore_statusTrigger_out
        }
      },
      CetButton_graph: {
        visible_in: true,
        // disable_in: false,
        title: $T("保存实时监控权限"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_graph_statusTrigger_out
        }
      },
      CetButton_pecReport: {
        visible_in: true,
        // disable_in: false,
        title: $T("保存报表权限"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_pecReport_statusTrigger_out
        }
      },
      CetButton_mReport: {
        visible_in: true,
        // disable_in: false,
        title: $T("保存报表权限"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_mReport_statusTrigger_out
        }
      },
      CetButton_onlyReport: {
        visible_in: true,
        title: $T("保存报表权限"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_onlyReport_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: ["name", "tenantId", "parentId"], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入服务商名称")
            },
            common.pattern_name,
            common.check_stringLessThan50
          ],
          endtime: [
            {
              required: true,
              message: $T("请输入托管结束时间"),
              trigger: ["blur", "change"]
            }
          ],
          projectNumber: [
            {
              type: "number",
              required: true,
              message: $T("请输入负责项目个数"),
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      CetDatePicker_1: {
        disable_in: false,
        val: "2018-02-02",
        config: {
          valueFormat: "timestamp",
          type: "date",
          format: "yyyy-MM-dd",
          rangeSeparator: "-",
          pickerOptions: common.pickerOptions_laterThanYesterday,
          style: {
            width: "100%"
          }
        }
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入"),
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInputNumber_projectNumber: {
        ...common.check_numberInt,
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      CetGiantTree_pecCore: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "id"
            },
            key: {
              name: "text"
            }
          },
          callback: {
            onCheck: this.CetGiantTree_pecCore_checkedNodes_out
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {}
      },
      CetGiantTree_graph: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "text"
            }
          },
          callback: {
            onCheck: this.CetGiantTree_graph_checkedNodes_out
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {}
      },
      CetGiantTree_pecReport: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "nodeName"
            }
          },
          callback: {
            onCheck: this.CetGiantTree_pecReport_checkedNodes_out
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {}
      },
      CetGiantTree_mReport: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "nodeName"
            }
          },
          callback: {
            onCheck: this.CetGiantTree_mReport_checkedNodes_out
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {}
      },
      CetGiantTree_onlyReport: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "id"
            },
            key: {
              name: "name"
            }
          },
          callback: {
            onCheck: this.CetGiantTree_onlyReport_checkedNodes_out
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {}
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      this.init();
      vm.CetDialog_1.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    init() {
      this.initTree();
      this.tenantId = 0;
      if (this.inputData_in) {
        this.tenantId = this.inputData_in.id;
        this.CetDialog_1.title = $T("编辑服务商");
        this.getDetail();
      } else {
        this.CetDialog_1.title = $T("新增服务商");
        this.CetForm_1.data = {
          endtime: this.$moment().add(1, "month").valueOf()
        };
        this.CetForm_1.resetTrigger_in = new Date().getTime();
      }
      this.activeName = "pecCore";
      this.lastName = this.activeName;
      this.initTooltip();
      this.handleClick({
        name: "pecCore"
      });
    },
    initTooltip() {
      this.CetGiantTree_pecCore.confirmHint = false;
      this.CetGiantTree_graph.confirmHint = false;
      this.CetGiantTree_pecReport.confirmHint = false;
      this.CetGiantTree_mReport.confirmHint = false;
      this.CetGiantTree_onlyReport.confirmHint = false;
    },
    initTree() {
      this.CetGiantTree_pecCore.checkedNodes = [];
      this.CetGiantTree_pecCore.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_graph.checkedNodes = [];
      this.CetGiantTree_graph.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_pecReport.checkedNodes = [];
      this.CetGiantTree_pecReport.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_mReport.checkedNodes = [];
      this.CetGiantTree_mReport.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_onlyReport.checkedNodes = [];
      this.CetGiantTree_onlyReport.unCheckTrigger_in = new Date().getTime();
    },
    async getDetail() {
      const vm = this;
      if (!vm.tenantId) {
        vm.initTree();
        return;
      }
      let res = await customApi.authGetTenantInfo({
        tenantId: vm.tenantId
      });
      if (res.code === 0) {
        let data = vm._.get(res, "data");
        // 基础信息
        let endtimeObj = data.limits.find(i => i.decs === "deposittime"),
          depositnumberObj = data.limits.find(i => i.decs === "depositnumber");
        let fromData = {
          id: data.id,
          name: data.name,
          parentId: data.parentId,
          endtime: endtimeObj && endtimeObj.max,
          projectNumber: depositnumberObj && depositnumberObj.max
        };
        vm.CetForm_1.data = fromData;
        vm.CetForm_1.resetTrigger_in = new Date().getTime();
      }
    },
    // 展开节点
    expandNode(nodes, key, ztreeObj) {
      setTimeout(() => {
        nodes.forEach(item => {
          let node = ztreeObj.getNodeByParam(key, item[key]);
          let parentNodes = [],
            parentNode = node && node.getParentNode();
          while (parentNode) {
            parentNodes.push(parentNode);
            parentNode = parentNode.getParentNode();
          }
          parentNodes.forEach(i => {
            ztreeObj.expandNode(i, true);
          });
        });
      }, 0);
    },
    async handleClick(val) {
      let obj = {
        pecCore: this.CetGiantTree_pecCore.confirmHint,
        Graph: this.CetGiantTree_graph.confirmHint,
        pecReport: this.CetGiantTree_pecReport.confirmHint,
        mReport: this.CetGiantTree_mReport.confirmHint,
        onlyReport: this.CetGiantTree_onlyReport.confirmHint
      };
      if (obj[this.lastName]) {
        this.$confirm($T("节点权限已修改，是否放弃?"), $T("提示"), {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        })
          .then(async () => {
            await this.getTree(val);
          })
          .catch(() => {
            this.activeName = this.lastName;
          });
      } else {
        await this.getTree(val);
      }
    },
    async getTree(val) {
      this.treeNodeTooltip = true;
      this.lastName = val.name;
      this.initTooltip();
      switch (val.name) {
        case "pecCore":
          await this.getChannelTree();
          break;
        case "Graph":
          await this.getGraphTree();
          break;
        case "pecReport":
          await this.getPecReportTree_out();
          break;
        case "mReport":
          await this.getMReportTree_out();
          break;
        case "onlyReport":
          await this.getOnlyReportTree_out();
          break;
        default:
          break;
      }
    },
    // 获取设备
    async getChannelTree() {
      var tenantId,
        nodes = [],
        vm = this;
      this.CetGiantTree_pecCore.saveFlag = false;
      if (vm.tenantId) {
        let res = await customApi.tenantPecstarnodeQuery(
          this.$store.state.pecCoreTypes,
          { tenantId: vm.tenantId }
        );
        let data = vm._.get(res, "data", []) || [];
        data.forEach(i => {
          if (i.childSelectState === 1) {
            nodes.push({
              id: `${i.nodeType}_${i.nodeID}`,
              nodeId: i.nodeID,
              nodeType: i.nodeType,
              tree_id: `${i.nodeType}_${i.nodeID}`
            });
          }
        });
      }
      vm.CetGiantTree_pecCore.checkedNodes = nodes;
      if (!nodes.length) {
        vm.CetGiantTree_pecCore.unCheckTrigger_in = new Date().getTime();
      }
      tenantId = 1;
      // if (vm.inputData_in) {
      //   tenantId = vm.serviceAdmin
      //     ? vm.inputData_in.id
      //     : vm.inputData_in.parentId;
      // }

      var queryData = {
        loadDevice: false,
        nodeId: 0,
        nodeType: 0,
        async: false,
        tenantId: tenantId
      };
      await customApi.getPecCoreMeterTree(queryData).then(response => {
        if (response.code == 0) {
          let arr = vm._.get(response, "data", []);
          vm.CetGiantTree_pecCore.inputData_in = this.formatNodeTree(arr);
          setTimeout(() => {
            vm.expandNode(
              nodes,
              vm.CetGiantTree_pecCore.setting.data.simpleData.idKey,
              vm.$refs.pecCoreTree.ztreeObj
            );
          }, 0);
        }
        this.$nextTick(() => {
          this.CetGiantTree_pecCore.saveFlag = true;
        });
      });
    },
    formatNodeTree(rawNodeAry) {
      var me = this;
      rawNodeAry = rawNodeAry || [];
      me._(rawNodeAry).forEach(function (rawNode) {
        // if (rawNode.childSelectState !== 1) {
        //   rawNode.nocheck = true;
        // }
        if (!rawNode.children || !rawNode.children.length) {
          rawNode.children = null;
          rawNode.leaf = true;
        } else {
          rawNode.children = me.formatNodeTree(rawNode.children);
        }
      });
      return rawNodeAry;
    },
    // 获取图形节点权限
    async getGraphTree() {
      let vm = this,
        nodes = [];
      this.CetGiantTree_graph.saveFlag = false;
      if (vm.tenantId) {
        let res = await customApi.tenantGraphnodeQuery(
          this.$store.state.graphTypes,
          { tenantId: vm.tenantId }
        );
        let data = vm._.get(res, "data", []) || [];
        data.forEach(i => {
          if (i.childSelectState === 1) {
            nodes.push({
              nodeId: i.nodeID,
              nodeName: i.nodeName,
              text: i.text,
              nodeType: i.nodeType,
              tree_id: `${i.nodeType}_${i.nodeID}`
            });
          }
        });
      }
      vm.CetGiantTree_graph.checkedNodes = nodes;
      if (!nodes.length) {
        vm.CetGiantTree_graph.unCheckTrigger_in = new Date().getTime();
      }

      var queryData = {
        tenantId: 1
      };
      // if (this.inputData_in && this.serviceAdmin) {
      //   queryData.tenantId = this.inputData_in.id;
      // }
      await customApi.getGraphTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_graph.inputData_in = this.formatGraphNodeTree(data);
          setTimeout(() => {
            this.expandNode(
              nodes,
              this.CetGiantTree_graph.setting.data.simpleData.idKey,
              this.$refs.graphTree.ztreeObj
            );
          }, 0);
        } else {
          this.CetGiantTree_graph.inputData_in = [];
        }
        this.$nextTick(() => {
          this.CetGiantTree_graph.saveFlag = true;
        });
      });
    },
    formatGraphNodeTree(rawNodeAry) {
      var me = this;
      rawNodeAry = rawNodeAry || [];

      me._(rawNodeAry).forEach(function (rawNode) {
        rawNode.id = rawNode.nodeId;
        if (!rawNode.children || !rawNode.children.length) {
          rawNode.children = null;
          rawNode.leaf = true;
        } else {
          rawNode.leaf = false;
          rawNode.children = me.formatGraphNodeTree(rawNode.children);
        }
        rawNode.tree_id = `${rawNode.nodeType}_${rawNode.id}`;
      });
      return rawNodeAry;
    },
    // 获取报表节点权限
    async getPecReportTree_out() {
      let vm = this,
        nodes = [];
      this.CetGiantTree_pecReport.saveFlag = false;
      if (vm.tenantId) {
        let res = await customApi.tenantPecstarnodeQuery(
          this.$store.state.pReportTypes,
          { tenantId: vm.tenantId }
        );
        let data = vm._.get(res, "data", []) || [];
        data.forEach(i => {
          if (i.childSelectState === 1) {
            nodes.push({
              id: `${i.nodeType}_${i.nodeID}`,
              nodeId: i.nodeID,
              nodeType: i.nodeType,
              tree_id: `${i.nodeType}_${i.nodeID}`
            });
          }
        });
      }
      vm.CetGiantTree_pecReport.checkedNodes = nodes;
      if (!nodes.length) {
        vm.CetGiantTree_pecReport.unCheckTrigger_in = new Date().getTime();
      }

      var queryData = {
        tenantId: 1
      };
      // if (this.inputData_in && this.serviceAdmin) {
      //   queryData.tenantId = this.inputData_in.id;
      // }
      await customApi.getPecReportTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_pecReport.inputData_in =
            this.formatReporthNodeTree(data);
          setTimeout(() => {
            this.expandNode(
              nodes,
              this.CetGiantTree_pecReport.setting.data.simpleData.idKey,
              this.$refs.pecReportTree.ztreeObj
            );
          }, 0);
        } else {
          this.CetGiantTree_pecReport.inputData_in = [];
        }
        this.$nextTick(() => {
          this.CetGiantTree_pecReport.saveFlag = true;
        });
      });
    },
    // 获取报表节点权限
    async getMReportTree_out() {
      let vm = this,
        nodes = [];
      this.CetGiantTree_mReport.saveFlag = false;
      if (vm.tenantId) {
        let res = await customApi.tenantModelnodeQuery(
          this.$store.state.mReportTypes,
          { tenantId: vm.tenantId }
        );
        let data = vm._.get(res, "data", []) || [];
        data.forEach(i => {
          if (i.childSelectState === 1) {
            nodes.push({
              nodeId: i.id,
              nodeType: i.modelLabel,
              tree_id: `${i.modelLabel}_${i.id}`
            });
          }
        });
      }
      vm.CetGiantTree_mReport.checkedNodes = nodes;
      if (!nodes.length) {
        vm.CetGiantTree_mReport.unCheckTrigger_in = new Date().getTime();
      }

      var queryData = {
        tenantId: 1
      };
      // if (this.inputData_in && this.serviceAdmin) {
      //   queryData.tenantId = this.inputData_in.id;
      // }
      await customApi.getMReportTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_mReport.inputData_in =
            this.formatReporthNodeTree(data);
          setTimeout(() => {
            this.expandNode(
              nodes,
              this.CetGiantTree_mReport.setting.data.simpleData.idKey,
              this.$refs.mReportTree.ztreeObj
            );
          }, 0);
        } else {
          this.CetGiantTree_mReport.inputData_in = [];
        }
        this.$nextTick(() => {
          this.CetGiantTree_mReport.saveFlag = true;
        });
      });
    },
    // 获取onlyReport报表节点权限
    async getOnlyReportTree_out() {
      let vm = this,
        nodes = [];
      vm.CetGiantTree_onlyReport.saveFlag = false;
      if (vm.tenantId) {
        const modelLabels = this.$store.state.onlyReportTypes;
        let res = await customApi.tenantModelnodeQuery(modelLabels, {
          tenantId: vm.tenantId
        });
        let data = vm._.get(res, "data", []) || [];
        data.forEach(i => {
          if (i.childSelectState === 1) {
            nodes.push({
              id: i.id,
              nodeId: i.id,
              nodeType: i.modelLabel,
              tree_id: `${i.modelLabel}_${i.id}`
            });
          }
        });
      }
      vm.CetGiantTree_onlyReport.checkedNodes = nodes;
      if (!nodes.length) {
        vm.CetGiantTree_onlyReport.unCheckTrigger_in = new Date().getTime();
      }

      var queryData = {
        tenantId: 1
      };
      await customApi.getOnlyReportTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_onlyReport.inputData_in = this.formatNodeTree(data);
          setTimeout(() => {
            this.expandNode(
              nodes,
              this.CetGiantTree_onlyReport.setting.data.simpleData.idKey,
              this.$refs.onlyReportTree.ztreeObj
            );
          }, 0);
        } else {
          this.CetGiantTree_onlyReport.inputData_in = [];
        }
        this.$nextTick(() => {
          vm.CetGiantTree_onlyReport.saveFlag = true;
        });
      });
    },
    formatReporthNodeTree(rawNodeAry) {
      var me = this;
      rawNodeAry = rawNodeAry || [];

      me._(rawNodeAry).forEach(function (rawNode) {
        rawNode.id = rawNode.nodeId;
        if (!rawNode.children || !rawNode.children.length) {
          rawNode.children = null;
          rawNode.leaf = true;
        } else {
          rawNode.leaf = false;
          rawNode.children = me.formatReporthNodeTree(rawNode.children);
        }
        rawNode.tree_id = `${rawNode.nodeType}_${rawNode.id}`;
      });
      return rawNodeAry;
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetDialog_1_close_out() {
      if (!this.authBtnDisable) {
        this.$emit("finishTrigger_out");
      }
    },
    CetButton_infoSave_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_saveData_out(val) {
      const vm = this;
      let saveData = {
          businesses: [5],
          id: vm.tenantId,
          limits: [
            {
              decs: "deposittime",
              id: 1,
              max: val.endtime,
              min: 0
            },
            {
              decs: "depositnumber",
              id: 5,
              max: parseInt(vm._.cloneDeep(val.projectNumber)),
              min: 0
            }
          ],
          name: val.name,
          parentId: val.parentId,
          type: 0
        },
        fn = "editSimplifyTenant";
      if (!vm.tenantId) {
        saveData.parentId = vm.currentNode_in.id;
        fn = "addSimplifyTenant";
      }
      customApi[fn](saveData).then(response => {
        if (response.code === 0) {
          vm.$message.success($T("保存成功！"));
          vm.tenantId = response.data;
        }
      });
    },
    CetGiantTree_pecCore_checkedNodes_out() {
      if (!this.CetGiantTree_pecCore.saveFlag) {
        return;
      }
      this.CetGiantTree_pecCore.confirmHint = true;
    },
    CetGiantTree_graph_checkedNodes_out() {
      if (!this.CetGiantTree_graph.saveFlag) {
        return;
      }
      this.CetGiantTree_graph.confirmHint = true;
    },
    CetGiantTree_pecReport_checkedNodes_out() {
      if (!this.CetGiantTree_pecReport.saveFlag) {
        return;
      }
      this.CetGiantTree_pecReport.confirmHint = true;
    },
    CetGiantTree_mReport_checkedNodes_out() {
      if (!this.CetGiantTree_mReport.saveFlag) {
        return;
      }
      this.CetGiantTree_mReport.confirmHint = true;
    },
    CetGiantTree_onlyReport_checkedNodes_out() {
      if (!this.CetGiantTree_onlyReport.saveFlag) {
        return;
      }
      this.CetGiantTree_onlyReport.confirmHint = true;
    },
    CetButton_pecCore_statusTrigger_out() {
      const vm = this;
      let saveData = {
        id: vm.tenantId,
        nodeTypes: this.$store.state.pecCoreTypes,
        pecStarNodes: []
      };
      const nodes = vm.getCheckNodes(vm.$refs.pecCoreTree.ztreeObj);
      nodes.forEach(item => {
        saveData.pecStarNodes.push({
          authIds: [],
          disabled: false,
          nodeID: item.nodeId,
          nodeType: item.nodeType,
          childSelectState: item.saveChildSelectState
        });
      });
      customApi.editSimplifyTenantPecstarnode(saveData).then(response => {
        if (response.code === 0) {
          vm.CetGiantTree_pecCore.confirmHint = false;
          vm.CetGiantTree_graph.confirmHint = false;
          vm.CetGiantTree_pecReport.confirmHint = false;
          vm.CetGiantTree_mReport.confirmHint = false;
          vm.CetGiantTree_onlyReport.confirmHint = false;
          vm.$message.success($T("保存成功！"));
        }
      });
    },
    CetButton_graph_statusTrigger_out() {
      const vm = this;
      let saveData = {
        id: vm.tenantId,
        nodeTypes: this.$store.state.graphTypes,
        graphNodes: []
      };
      const nodes = vm.getCheckNodes(vm.$refs.graphTree.ztreeObj);
      nodes.forEach(item => {
        saveData.graphNodes.push({
          disabled: false,
          nodeID: item.nodeId,
          nodeName: item.nodeName,
          text: item.text,
          nodeType: item.nodeType,
          authIds: [],
          childSelectState: item.saveChildSelectState
        });
      });
      customApi.editSimplifyTenantGraphnode(saveData).then(response => {
        if (response.code === 0) {
          vm.CetGiantTree_pecCore.confirmHint = false;
          vm.CetGiantTree_graph.confirmHint = false;
          vm.CetGiantTree_pecReport.confirmHint = false;
          vm.CetGiantTree_mReport.confirmHint = false;
          vm.CetGiantTree_onlyReport.confirmHint = false;
          vm.$message.success($T("保存成功！"));
        }
      });
    },
    CetButton_pecReport_statusTrigger_out() {
      const vm = this;
      let saveData = {
        id: vm.tenantId,
        nodeTypes: this.$store.state.pReportTypes,
        pecStarNodes: []
      };
      const nodes = vm.getCheckNodes(vm.$refs.pecReportTree.ztreeObj);
      nodes.forEach(item => {
        if (saveData.nodeTypes.indexOf(item.nodeType) === -1) {
          saveData.nodeTypes.push(item.nodeType);
        }
        saveData.pecStarNodes.push({
          authIds: [],
          disabled: false,
          nodeID: item.nodeId,
          nodeType: item.nodeType,
          childSelectState: item.saveChildSelectState
        });
      });
      customApi.editSimplifyTenantPecstarnode(saveData).then(response => {
        if (response.code === 0) {
          vm.CetGiantTree_pecCore.confirmHint = false;
          vm.CetGiantTree_graph.confirmHint = false;
          vm.CetGiantTree_pecReport.confirmHint = false;
          vm.CetGiantTree_mReport.confirmHint = false;
          vm.CetGiantTree_onlyReport.confirmHint = false;
          vm.$message.success($T("保存成功！"));
        }
      });
    },
    CetButton_mReport_statusTrigger_out() {
      const vm = this;
      let saveData = {
        id: vm.tenantId,
        modelLabels: this.$store.state.mReportTypes,
        modelNodes: []
      };
      const nodes = vm.getCheckNodes(vm.$refs.mReportTree.ztreeObj);
      nodes.forEach(item => {
        saveData.modelNodes.push({
          authIds: [],
          disabled: false,
          childSelectState: item.saveChildSelectState,
          id: item.nodeId,
          modelLabel: item.nodeType,
          rule: ""
        });
      });
      customApi.editSimplifyTenantModelnode(saveData).then(response => {
        if (response.code === 0) {
          vm.CetGiantTree_pecCore.confirmHint = false;
          vm.CetGiantTree_graph.confirmHint = false;
          vm.CetGiantTree_pecReport.confirmHint = false;
          vm.CetGiantTree_mReport.confirmHint = false;
          vm.CetGiantTree_onlyReport.confirmHint = false;
          vm.$message.success($T("保存成功！"));
        }
      });
    },
    CetButton_onlyReport_statusTrigger_out() {
      const vm = this;
      let saveData = {
        id: vm.tenantId,
        modelLabels: this.$store.state.onlyReportTypes, // nodeType值，1-文件夹，2-报表节点,
        modelNodes: []
      };
      const nodes = vm.getCheckNodes(vm.$refs.onlyReportTree.ztreeObj);
      nodes.forEach(item => {
        saveData.modelNodes.push({
          authIds: [],
          disabled: false,
          childSelectState: item.saveChildSelectState,
          id: item.id,
          modelLabel: item.nodeType,
          rule: ""
        });
      });
      customApi.editSimplifyTenantModelnode(saveData).then(response => {
        if (response.code === 0) {
          vm.CetGiantTree_pecCore.confirmHint = false;
          vm.CetGiantTree_graph.confirmHint = false;
          vm.CetGiantTree_pecReport.confirmHint = false;
          vm.CetGiantTree_mReport.confirmHint = false;
          vm.CetGiantTree_onlyReport.confirmHint = false;
          vm.$message.success($T("保存成功！"));
        }
      });
    },
    // 获取节点
    getCheckNodes(ztreeObj) {
      const nodes = ztreeObj.getNodesByFilter(node => {
        const checkStatus = node.getCheckStatus();
        if (node.childSelectState === 1 && !checkStatus.half) {
          node.saveChildSelectState = 1;
        } else {
          node.saveChildSelectState = 2;
        }
        if (!node.checked) return false;

        const parentNode = node.getParentNode();
        if (!parentNode) return true;

        // eslint-disable-next-line no-unsafe-optional-chaining
        const { checked, half } = parentNode?.getCheckStatus();

        if (checked && !half && parentNode.childSelectState === 1) return false;
        return true;
      });
      return nodes;
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep() {
    .is-fullscreen {
      display: flex;
      flex-direction: column;
    }
    .el-dialog__body {
      @include background_color(BG, !important);
      @include padding(J2);
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }
  }
  .nbsp :deep(.el-form-item__label) {
    height: 42px;
  }

  .title {
    font-weight: bold;
    @include margin_left(J3);
  }
  .eem-tabs-custom {
    @include background_color(BG);
    :deep(.el-tabs__nav-wrap::after) {
      @include background_color(BG);
    }
  }
  .treeBox {
    height: 500px;
    .fullfilled {
      position: relative;
      .saveBtn {
        position: absolute;
        right: 0;
        top: 0;
      }
    }
    .CetTree {
      width: 100%;
      :deep(.device-search .el-input) {
        width: 240px;
      }
    }
  }
  :deep(.tooltipBox) {
    .tooltip {
      left: -44px;
      display: none;
    }
    &:hover .tooltip {
      display: block;
    }
  }
  :deep(.halfSelectedNode) {
    @include font_color(T6);
  }
}
</style>
