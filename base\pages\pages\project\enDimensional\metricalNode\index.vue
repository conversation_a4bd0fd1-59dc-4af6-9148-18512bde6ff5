<template>
  <div class="page eem-common">
    <div id="eem-metrical-node" class="eem-metrical-node">
      <div class="fullheight flex-row">
        <div class="fullheight eem-aside flex-column" style="width: 315px">
          <div class="mbJ2">
            <div class="select-box clearfix">
              <span class="label" style="width: 50px">模型：</span>
              <el-select
                v-model="district.id"
                size="mini"
                style="width: calc(100% - 52px)"
                multiple
                collapse-tags
                @remove-tag="removeTag($event, district)"
                @visible-change="handleTag($event, district)"
              >
                <el-option
                  v-for="item in district.options"
                  :key="item.label"
                  :label="item.name"
                  :value="item.label"
                ></el-option>
              </el-select>
            </div>
          </div>
          <div class="flex-auto">
            <el-tree
              class="fullheight"
              ref="tree"
              render-after-expand
              :expand-on-click-node="false"
              highlight-current
              show-checkbox
              check-strictly
              :default-expanded-keys="deKeys"
              :data="treeData"
              @node-click="clickNode"
              @check="handleCheck"
              :props="propsM"
              node-key="nodeId"
            ></el-tree>
          </div>
        </div>
        <div class="fullheight mlJ3 flex-auto flex-column">
          <div class="mbJ3">
            <el-button
              type="primary"
              plain
              class="fr mlJ1"
              size="mini"
              v-show="!isEdit"
              @click="handleExport"
            >
              导出
            </el-button>

            <el-button
              type="primary"
              plain
              class="fr mlJ1"
              size="mini"
              v-show="!isEdit"
              @click="configureNode"
            >
              配置节点
            </el-button>
            <el-button
              type="primary"
              class="fr mlJ1"
              size="mini"
              v-show="isEdit"
              @click="preserveTable"
            >
              保存
            </el-button>
            <el-button
              type="primary"
              plain
              class="fr mlJ1"
              size="mini"
              v-show="isEdit"
              @click="cancelEdit"
            >
              取消
            </el-button>
            <CustomElDatePicker
              v-show="isEdit && isShowDate"
              class="fr"
              style="width: 200px"
              prefix_in="生效时间"
              v-model="searchTime"
              type="date"
              :clearable="false"
              placeholder="选择日期"
              @change="timeChange"
            />
          </div>
          <div class="flex-auto eem-container" style="position: relative">
            <div class="eem-metrical-node-body" id="metrical_node_table"></div>
            <div class="window-table__empty-block" v-if="isTabEmpty">
              <div class="window-table__empty-text">暂无数据</div>
            </div>
          </div>
        </div>
      </div>

      <el-dialog
        class="medium"
        :title="metricalNodeName"
        :visible.sync="showMetricalNodeWind"
        :close-on-click-modal="false"
      >
        <MetricalNodeWindow
          @closeDialog="closeDialog"
          :nodeInfoM="nodeInfoM"
          :nodeInfoArr="nodeInfoArr"
          :dimIds="dimIds"
          :rowData="rowData"
        ></MetricalNodeWindow>
        <span slot="footer">
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
        </span>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import MetricalNodeWindow from "./dialog/MetricalNodeWindow";
import { httping } from "@omega/http";
export default {
  name: "MetricalNode",
  components: { MetricalNodeWindow },
  props: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    },
    hotLanguage() {
      if (window.localStorage.getItem("omega_language") === "en") {
        return "en-US";
      }
      return "zh-CN";
    }
  },

  data() {
    return {
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "关闭",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.closeDialog
        }
      },
      dimensionItems: [], //维度列表信息
      treeData: [], //左侧树的数据
      propsM: {
        children: "children",
        label: "node"
      },
      open: true, //控制左侧的侧边栏的伸缩
      userId: null, //用户的UserId
      nodeNameM: "", //节点名称
      nodeInfoM: null, //节点信息
      nodeInfoArr: [], // 左侧勾选的节点
      deKeys: [], //节点ID列表
      isClickChecked: false,
      searchTime: new Date(), //生效时间
      pickerOptions: {
        disabledDate: function (time) {
          return time > new Date();
        }
      },
      isEdit: false,
      //表格配置
      tableHeaders: [],
      tableColumns: [],
      tableColumns11: [],
      tableData: [],
      oldTableData: [],
      oldHeaders: [],
      oldRows: [],
      //计量节点弹框
      showMetricalNodeWind: false,
      rowData: null, //查看的数据
      dimIds: [],
      metricalNodeName: "计量节点名称",
      isTabEmpty: false,
      district: {
        options: [],
        id: [],
        oldId: []
      },
      isShowDate: true
    };
  },
  watch: {
    // 节点监听
    nodeInfoM: function (node) {},
    isactivate: function (val) {
      if (val) {
        this.district.id = [];
        this.district.oldId = [];
        this.isEdit = false;
        this.init();
      }
    },
    tableData: {
      handler: function (arr) {
        // this.isTabEmpty = arr.length === 0 ? true : false;
      },
      immediate: true
    }
  },

  methods: {
    // 节点点击事件
    clickNode: function (data, node, tree) {
      if (this.isEdit) {
        this.$message.warning("请先保存关联计量节点修改！");
        return;
      }
      if (this.hot) {
        this.hot.updateSettings({ filters: false });
      }
      this.nodeNameM = data.node;
      this.nodeInfoM = data;
      this.nodeInfoArr = [data];
      this.isClickChecked = true;
      this.$refs.tree.setCheckedKeys([data.nodeId], true);
      this.getMetricalNodeList();
    },

    // 复选框被点击
    handleCheck: function (data, datas) {
      if (!this.isClickChecked) {
        var checkedNode = this.$refs.tree.getCheckedNodes();

        var list = checkedNode;
        var len = list.length;
        var nodeStr = "";
        for (var i = 0; i < len; i++) {
          if (i === 0) {
            nodeStr = nodeStr + list[i].node;
          } else {
            nodeStr = nodeStr + "-" + list[i].node;
          }
        }
        this.nodeNameM = nodeStr;
        this.nodeInfoArr = datas.checkedNodes;
        this.getMetricalNodeList();
      }
    },
    //改变生效时间
    timeChange: function () {},
    //点击配置节点按钮
    configureNode: function () {
      var _this = this;
      _this.toEditTable();
      _this.isEdit = true;
    },

    toEditTable: function () {
      var _this = this;
      var dataObject = _this.tableData;
      var hotSettings = {
        data: dataObject,
        columns: _this.tableColumns,
        readOnly: false,
        stretchH: "all",
        autoWrapRow: true,
        maxRows: dataObject.length,
        // rowHeaders: true,
        colHeaders: _this.tableHeaders,
        dropdownMenu: true, //下拉式菜单
        filters: true, //过滤器
        // manualRowResize: true, //自定义行宽
        // manualColumnResize: true  //自定义列高
        fillHandle: "vertical", //设置自定填充垂直方向
        language: this.hotLanguage
      };
      _this.hot.updateSettings(hotSettings);
    },
    // 点击保存按钮
    preserveTable: function () {
      var _this = this;
      _this.addOrUpdateNode();
    },
    init: function () {
      this.loadDistrictNodes();
      // this.getDimensionMsg();
    },
    // 获取分区列表
    loadDistrictNodes: function () {
      let options = [
        {
          name: "用能设备",
          label: "manuequipment"
        }
      ];
      if (this.systemCfg.metricalNodeModelList) {
        options = this._.cloneDeep(this.systemCfg.metricalNodeModelList);
      }
      this.district.options = options;
      this.district.id = [this._.get(options, "[0].label", null)];
      this.getDimensionMsg();
    },
    // 移除tag
    removeTag: function (tag, obj) {
      this.getMetricalNodeList();
      obj.oldId = obj.id;
    },
    handleTag: function (bool, obj) {
      if (!bool) {
        var oldArr = obj.oldId;
        var newArr = obj.id;
        if (newArr.sort().join() !== oldArr.sort().join()) {
          this.getMetricalNodeList();
        }
        obj.oldId = obj.id;
      }
    },
    //获取维度列表信息
    getDimensionMsg: function () {
      var _this = this;
      var auth = _this.token; //身份验证
      httping({
        url: "/eem-service/v1/dim/setting/detailList",
        method: "GET",
        timeout: 10000
      })
        .then(res => {
          if (res.code === 0) {
            _this.dimensionItems = res.data;
            _this.addTreeData();
          }
        })
        .catch(res => {
          _this.$message.error("获取维度列表数据失败");
        });
    },
    //更新左侧节点树列表
    addTreeData: function () {
      var _this = this;
      var list = _this.dimensionItems;
      var len = list.length;
      var arr = [];
      for (var i = 0; i < len; i++) {
        var obj = {};
        obj.node = list[i].name;
        obj.nodeId = list[i].id;
        // obj.nodeType = list[i].dimensionId;
        obj.children = [];
        arr.push(obj);
      }
      _this.treeData = arr;
      _this.$nextTick(function () {
        _this.isClickChecked = true;
        _this.$refs.tree.setCheckedKeys([arr[0].nodeId], true);
        _this.nodeInfoM = arr[0];
        _this.nodeInfoArr = [arr[0]];
        _this.nodeNameM = arr[0] ? arr[0].node : "";
        _this.getMetricalNodeList();
      });
    },
    //获取计量节点列表信息
    getMetricalNodeList: function () {
      this.isEdit = false;
      var _this = this;
      var auth = _this.token;
      var checkedNode = this.$refs.tree.getCheckedNodes();
      var len = checkedNode.length;
      var demIdArr = [];
      for (var i = 0; i < len; i++) {
        var demId = checkedNode[i].nodeId;
        demIdArr.push(demId);
      }
      _this.dimIds = demIdArr;

      var disArr = _this.district.id || [];
      var disList = _this.district.options || [];
      var leng = disList.length;
      var labelsArr = [];
      for (let i = 0; i < leng; i++) {
        for (var j = 0; j < disArr.length; j++) {
          if (disList[i].label == disArr[j]) {
            labelsArr.push(disList[i].label);
            break;
          }
        }
      }
      var params = {
        dimIds: demIdArr,
        labels: labelsArr,
        projectId: 0,
        system: this.systemCfg.key
      };
      if (this.systemCfg.key == "cloud") {
        params.projectId = this.projectId;
      }
      _this.isClickChecked = true;
      httping({
        url: "/eem-service/v1/dim/setting/metrical/properties",
        data: params,
        method: "POST",
        timeout: 10000
      })
        .then(res => {
          _this.isClickChecked = false;
          if (res.code === 0) {
            _this.filTableMsg(res.data);
          }
        })
        .catch(res => {
          _this.isClickChecked = false;
          _this.$message.error("获取计量节点列表信息失败！");
        });
    },
    //过滤表格配置信息
    filTableMsg: function (data) {
      var _this = this;
      var headers = data.headers;
      var rows = data.rows;
      _this.oldHeaders = headers;
      _this.oldRows = rows;
      _this.tableHeaders = _this.filTableHeader(headers);
      _this.tableColumns = _this.filTableColumns(headers);
      _this.tableColumns11 = _this.filTableColumns11(headers);
      _this.tableData = _this.filTableData(_this.tableColumns, rows);
      _this.oldTableData = _this.filTableData(_this.tableColumns, rows);

      _this.getMetricalNode();
      var len = _this.tableData.length;
      if (len === 0) {
        this.isTabEmpty = true;
      } else {
        this.isTabEmpty = false;
      }
    },
    //过滤表格头部名称
    filTableHeader: function (list) {
      list = list || [];
      var len = list.length;
      var arr = [];
      for (var i = 0; i < len; i++) {
        var headerName = list[i].headerName;
        arr.push(headerName);
      }
      arr.push("操作");
      return arr;
    },
    // 过滤表格配置
    filTableColumns: function (list) {
      var len = list.length;
      var arr = [];
      for (var i = 0; i < len; i++) {
        var obj = {};
        if (i == 0) {
          obj = {
            data: "metricalnode",
            type: "text",
            className: "htLeft",
            readOnly: true
          };
        } else if (i == 1) {
          obj = {
            data: "id",
            type: "numeric",
            // width: 40,
            className: "htLeft",
            readOnly: true
          };
        } else if (i == 2) {
          obj = {
            data: "levelObj" + i,
            type: "text",
            className: "htLeft",
            readOnly: true
          };
        } else {
          obj = {
            // data: 'siteArea',
            type: "autocomplete",
            // source:['1F-中','2F','3F','4F','5F','...'],
            strict: true, //值为true，严格匹配
            allowInvalid: true
          };
          obj.data = "levelObj" + i;
          obj.source = this.filDropDownList(list[i].dropDownList);
        }
        arr.push(obj);
      }

      return arr;
    },
    // 过滤表格配置
    filTableColumns11: function (list) {
      list = list || [];
      var len = list.length;
      var arr = [];
      for (var i = 0; i < len; i++) {
        var obj = {};
        if (i == 0) {
          obj = {
            data: "metricalnode",
            type: "text",
            className: "htLeft",
            readOnly: true
          };
        } else if (i == 1) {
          obj = {
            data: "id",
            type: "numeric",
            // width: 40,
            className: "htLeft",
            readOnly: true
          };
        } else if (i == 2) {
          obj = {
            data: "levelObj" + i,
            type: "text",
            className: "htLeft",
            readOnly: true
          };
        } else {
          obj = {
            data: "levelObj" + i,
            type: "text",
            className: "htLeft"
          };
        }
        arr.push(obj);
      }
      return arr;
    },
    filDropDownList: function (list) {
      list = list || [];
      var len = list.length;
      var arr = [];
      for (var i = 0; i < len; i++) {
        var name = list[i].name;
        arr.push(name);
      }
      return arr;
    },
    // 过滤表格数据
    filTableData: function (list, data) {
      list = list || [];
      data = data || [];
      var len = data.length;
      var arr = [];
      for (var i = 0; i < len; i++) {
        var leng = data[i].length;
        var obj = {};
        for (var j = 0; j < leng; j++) {
          obj[list[j].data] = data[i][j] || "";
          if (j === leng - 1) {
            obj.nodeLabel = data[i][j] || "";
          }
        }
        obj.historyMsg = "";
        arr.push(obj);
      }
      return arr;
    },
    //保存表格数据
    addOrUpdateNode: function () {
      var _this = this;
      var auth = _this.token; //身份验证

      var params = _this.getPreserveParams();
      if (!params) {
        return;
      }
      httping({
        url: "/eem-service/v1/dim/setting/nodeProperty",
        data: params,
        method: "PUT",
        timeout: 10000
      })
        .then(res => {
          if (res.code === 0) {
            _this.searchTime = new Date();
            _this.isEdit = false;
            _this.$message.success("保存成功！");
            // 修改节点，更改全局状态，刷新首页
            _this.$store.commit("refreIndex", new Date());
            _this.getMetricalNodeList();
          }
        })
        .catch(res => {
          _this.$message.error("保存计量节点出错！");
        });
    },
    //获取保存表格数据接口参数
    getPreserveParams: function () {
      var _this = this;
      var tableColumns = _this.tableColumns;
      var oldHeaders = _this.oldHeaders;
      var list = _this.hot.getSourceDataArray();
      var oldList = _this.oldRows;
      var len = list.length;
      var params = [];
      var createTime = _this.getCreateTime();
      //   enableTime = _this.searchTime ? _this.getEnableTime(_this.searchTime) : null;
      // if(!enableTime){
      //   _this.$message.warning("请选择生效时间！");
      //   return;
      // }
      for (var i = 0; i < len; i++) {
        var arr = list[i];
        var nodeId = arr[1] || 0;
        var modelName = arr[2] || "";
        var oldArr = _this.getOldArr(nodeId, modelName, oldList);
        var leng = arr.length;
        var isOk = true;
        var num = 0;
        var num1 = 0;
        let objectLabel = "";
        const modeleList = this.district.options || [];
        modeleList.forEach(item => {
          if (item.name === modelName) {
            objectLabel = item.label;
          }
        });
        for (var j = 3; j < leng - 1; j++) {
          if (!isOk && arr[j]) {
            num1 += 1;
            isOk = true;
          }
          if (!arr[j]) {
            num += 1;
            isOk = false;
          }
          if (!nodeId) {
            continue;
          }
          if (!modelName) {
            continue;
          }
          if (arr[j] != oldArr[j]) {
            var isEdit = _this.filHasProperty(
              arr[j],
              oldHeaders[j].dropDownList
            );
            if (isEdit) {
              var obj = {};
              // obj.nodeId = Number(nodeId);
              // obj.oldNodeId = 0;
              // obj.createTime = createTime;
              // obj.enableTime = enableTime;
              // obj.propertyName = arr[j];
              // obj.propertyId = _this.filPropertyId(arr[j], oldHeaders[j].dropDownList);
              // obj.oldPropertyId = _this.filPropertyId(oldArr[j],oldHeaders[j].dropDownList);
              // obj.oldPropertyId = 0;
              // obj.levelId = oldHeaders[j].levelId;

              obj.label = modelName;
              obj.objectLabel = objectLabel;
              obj.id = 0;
              obj.modelid = Number(nodeId);
              obj.tagid = _this.filPropertyId(
                arr[j],
                oldHeaders[j].dropDownList
              );
              obj.levelid = oldHeaders[j].levelId;
              obj.enabletime = new Date("1900-01-01 00:00:00.000").getTime();
              obj.createtime = new Date().getTime();
              if (_this.isShowDate) {
                obj.enabletime = _this
                  .$moment(_this.searchTime)
                  .startOf("d")
                  .valueOf();
              }

              params.push(obj);
            } else {
              _this.$message.warning("请选择正确标签！");
              return false;
            }
          }
        }
        // if (num > 0 && (num != leng-3) && num1 > 0) {
        // 	_this.$message.warning('请按顺序进行绑定标签！');
        // 	return false
        // }
      }
      return params;
    },
    getCreateTime: function () {
      var date = new Date();
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      var d = date.getDate();
      if (m < 10) {
        m = "0" + m;
      }
      if (d < 10) {
        d = "0" + d;
      }
      // return y + '-' + m + '-' + d + ' 00:00:00.000'
      return "1900-01-01 00:00:00.000";
    },
    getEnableTime: function (date) {
      date = new Date(date);
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      var d = date.getDate();
      if (m < 10) {
        m = "0" + m;
      }
      if (d < 10) {
        d = "0" + d;
      }
      return y + "-" + m + "-" + d + " 00:00:00.000";
    },
    getOldArr: function (nodeId, modelName, list) {
      var len = list.length;
      var arr = [];
      for (var i = 0; i < len; i++) {
        var oldNodeId = list[i][1];
        var oldModelName = list[i][2];
        if (nodeId == oldNodeId && modelName == oldModelName) {
          arr = list[i];
          return arr;
        }
      }
      return [];
    },
    //判断输入是否是下拉框标签
    filHasProperty: function (fil, list) {
      list = list || [];
      var len = list.length;
      if (fil === "") {
        return true;
      }
      for (var i = 0; i < len; i++) {
        if (fil == list[i].name) {
          return true;
        }
      }
      return false;
    },
    //将标签名称转为Id
    filPropertyId: function (fil, list) {
      fil = fil || "";
      list = list || [];
      var len = list.length;
      for (var i = 0; i < len; i++) {
        if (fil == list[i].name) {
          return list[i].id;
        }
      }
      return 0;
    },
    //配置计量节点表格
    getMetricalNode: function () {
      var _this = this;
      var dataObject = _this.tableData;
      var tableBtnRender = function (
        instance,
        td,
        row,
        col,
        prop,
        value,
        cellProperties
      ) {
        //添加自定义的图片，并给图片的chick添加事件
        var escaped = Handsontable.helper.stringify(value);
        var text;

        text = document.createElement("span");
        text.innerHTML = "历史记录";
        text.className = "handsontableHandle";
        text.style = "cursor:pointer;width:100px;"; //鼠标移上去变手型
        Handsontable.dom.addEvent(text, "click", function (event) {
          var row_index = cellProperties.row;
          _this.metricalNodeName = _this.tableData[row_index].metricalnode;
          _this.rowData = _this.tableData[row_index];
          _this.showHierachyWind(row_index);
        });

        Handsontable.dom.empty(td);
        td.appendChild(text);
        td.style.textAlign = "left"; //图片居中对齐
        return td;
      };
      var hotElement = document.querySelector("#metrical_node_table");
      var historyMsg = {
        data: "historyMsg",
        renderer: tableBtnRender,
        readOnly: true
      };
      _this.isShowDate = false;
      //通过SystemCfg配置显示操作历史记录列显示
      if (!this.systemCfg.hideHistoryBtn) {
        _this.tableColumns11.push(historyMsg);
        _this.isShowDate = true;
      }
      var hotSettings = {
        data: dataObject,
        columns: _this.tableColumns11,
        readOnly: true,
        stretchH: "all",
        autoWrapRow: true,
        height: "100%",
        maxRows: dataObject.length,
        rowHeaders: true,
        colHeaders: _this.tableHeaders,
        dropdownMenu: true, //下拉式菜单
        filters: true, //过滤器
        fillHandle: false, //设置不能自定填充
        language: this.hotLanguage
      };
      if (_this.hot) {
        _this.toPreserveTable();
      } else {
        _this.hot = new Handsontable(hotElement, hotSettings);
      }
    },

    //配置表格不可编辑
    toPreserveTable: function () {
      var _this = this;
      var dataObject = _this.tableData;
      var hotSettings = {
        data: dataObject,
        columns: _this.tableColumns11,
        readOnly: true,
        stretchH: "all",
        autoWrapRow: true,
        maxRows: dataObject.length,
        // rowHeaders: true,
        colHeaders: _this.tableHeaders,
        dropdownMenu: true, //下拉式菜单
        filters: true, //过滤器
        fillHandle: false, //设置不能自定填充
        language: this.hotLanguage
      };
      _this.hot.updateSettings(hotSettings);
    },
    //点击导出按钮
    handleExport: function () {
      var _this = this;
      var name = _this.nodeNameM + "-关联计量节点";
      // _this.toExport(urlStr, name);
      var exportPlugin = _this.hot.getPlugin("exportFile");
      var len = _this.tableHeaders.length - 2;
      exportPlugin.downloadFile("csv", {
        filename: name,
        columnHeaders: true,
        range: [, 0, , len]
      });
    },

    toExport: function (urlStr, name) {
      var _this = this;
      var xhr = new XMLHttpRequest();
      var fileName = name + ".xls"; // 文件名称
      xhr.open("GET", urlStr, true);
      // xhr.responseType = 'arraybuffer'
      xhr.responseType = "blob";
      xhr.setRequestHeader("Authorization", "Basic a2VybWl0Omtlcm1pdA=="); // 请求头中的验证信息等（如果有）
      xhr.onload = function (res) {
        if (this.status === 200) {
          var type = xhr.getResponseHeader("Content-Type");
          var blob = new Blob([this.response], { type: type });
          if (typeof window.navigator.msSaveBlob !== "undefined") {
            /*
             * For IE
             * >=IE10
             */
            window.navigator.msSaveBlob(blob, fileName);
          } else {
            /*
             * For Non-IE (chrome, firefox)
             */
            var URL = window.URL || window.webkitURL;
            var objectUrl = URL.createObjectURL(blob);
            if (fileName) {
              var a = document.createElement("a");
              if (typeof a.download === "undefined") {
                window.location = objectUrl;
              } else {
                a.href = objectUrl;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                a.remove();
              }
            } else {
              window.location = objectUrl;
            }
          }
        } else {
          _this.$message.error("导出失败");
        }
      };
      xhr.send();
    },

    //点击取消编辑记录
    cancelEdit: function () {
      this.isEdit = false;
      this.searchTime = new Date();
      this.getMetricalNodeList();
    },
    //弹出计量节点历史弹框
    showHierachyWind: function (row) {
      this.showMetricalNodeWind = true;
    },
    // 关闭dialog
    closeDialog: function () {
      this.showMetricalNodeWind = false;
      this.getMetricalNodeList();
    }
  },
  created: function () {
    // 进入页面触发
    this.isEdit = false;
    var _this = this;
    setTimeout(function () {
      _this.district.id = [];
      _this.district.oldId = [];
      _this.init();
    }, 100);
  },
  mounted: function () {
    // var _this = this;
    // setTimeout(function() {
    //   _this.init();
    // }, 100);
  },
  activated: function () {},
  deactivated: function () {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.eem-metrical-node {
  overflow: auto;
  height: 100%;
}
#metrical_node_table {
  @include background_color(BG1);
  @include font_color(T1);
}
#metrical_node_table :deep(table thead tr th) {
  color: rgb(0, 0, 0);
  height: 30px;
  line-height: 30px;
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}

#metrical_node_table :deep(table tbody tr th) {
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}

#metrical_node_table :deep(table tbody tr td) {
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}
#metrical_node_table :deep(.handsontable th) {
  text-align: left;
}
#metrical_node_table :deep(table thead tr th .changeType) {
  margin-top: 8px;
}
#metrical_node_table :deep(table thead tr th .colHeader) {
  font-weight: 700;
}
#metrical_node_table :deep(.handsontable .changeType) {
  @include font_color(T1);
  @include background_color(BG1);
  @include border_color(B1);
}
#metrical_node_table :deep(.handsontable .changeType:hover) {
  @include font_color(T1);
  @include background_color(BG1);
  @include border_color(B1);
}
#metrical_node_table :deep(.handsontableHandle) {
  @include font_color(ZS);
}
.window-table__empty-block {
  position: absolute;
  overflow: hidden;
  display: block;
  top: 45%;
  left: 0;
  width: calc(100% - 32px);
}
.window-table__empty-text {
  text-align: center;
  width: 100%;
  height: 40px;
  line-height: 40px;
}
.eem-metrical-node-body {
  position: relative;
  overflow-y: auto;
  border: 1px solid;
  @include border_color(B1);
  border-top: none;
}
.eem-metrical-node-aside {
  @include background_color(BG1);
  width: 315px;
  height: 100%;
  @include padding(J1);
  @include margin_right(J2);
  overflow: hidden;
  box-sizing: border-box;
}

/*右侧css*/
.eem-metrical-node-right {
  position: relative;
  height: 100%;
  margin-left: 315px;
}
</style>

<style>
.eem-metrical-node #hot-display-license-info {
  display: none;
}

/*.htDropdownMenu .ht_master .wtHolder .wtSpreader .htCore tbody tr:nth-child(1) {
    display: none;
}*/
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(1) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(2) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(3) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(4) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(5) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(6) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(7) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(8) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(9) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(10) {
  display: none;
}

.eem-metrical-node-dialog {
  width: 100%;
  height: 360px;
}

.eem-metrical-node-dialog-header {
  border: 1px solid #ccc;
  border-bottom: none;
}

.eem-metrical-node .el-dialog__body {
  padding: 10px;
}

.node_history_btn {
  cursor: pointer;
}

.node_history_btn_delete {
  cursor: pointer;
  margin-left: 15px;
}
</style>

<style lang="scss">
.handsontable .ht_master table td.htCustomMenuRenderer {
  @include background_color(BG);
}
.htFiltersConditionsMenu table tbody tr td {
  @include background_color(BG1);
}
.htDropdownMenu table tbody tr td {
  @include background_color(BG1);
}
.handsontable .htUIMultipleSelect .handsontable .htCore td:hover {
  @include background_color(BG2);
}
.handsontable .htUISelectCaption {
  @include background_color(BG4);
}
</style>
