<template>
  <el-dialog
    title="自定义谐波次数"
    :visible.sync="visible"
    :show-close="true"
    @close="dialogClose"
    class="small"
  >
    <div class="eem-cont-c1">
      <el-collapse accordion>
        <el-collapse-item
          v-for="(item, index) in harmTimesGroup"
          :key="item.title"
          :title="item.title"
        >
          <div class="mb5">
            <el-checkbox
              :indeterminate="item.isIndeterminate"
              v-model="item.checkAll"
              @change="val => handleCheckAll(val, index)"
            >
              全选
            </el-checkbox>
          </div>
          <el-checkbox-group
            v-model="item.checked"
            @change="val => handleCheckedChange(val, index)"
          >
            <el-checkbox
              style="width: 60px"
              class="m0 mb5"
              v-for="it in item.enum"
              :label="it"
              :key="it"
            >
              {{ it }}
            </el-checkbox>
          </el-checkbox-group>
        </el-collapse-item>
      </el-collapse>
    </div>
    <span slot="footer">
      <span class="device-Button">
        <el-button size="small" type="primary" plain @click="queryDialogCancel">
          取消
        </el-button>
      </span>
      <span class="device-Button">
        <el-button size="small" type="primary" @click="queryDialogConfirm">
          确定
        </el-button>
      </span>
    </span>
  </el-dialog>
</template>
<script>
import common from "eem-utils/common";

export default {
  name: "HarmTimesGroup",
  props: {
    visible: Boolean,
    lastChecked: {
      type: Array,
      default: () => common.fileNumArr(2, 25)
    }
  },
  data() {
    return {
      harmTimesGroup: [
        {
          title: "2~25",
          checkAll: true,
          checked: [],
          enum: common.fileNumArr(2, 25),
          isIndeterminate: false
        },
        {
          title: "26~50",
          checkAll: false,
          checked: [],
          enum: common.fileNumArr(26, 50),
          isIndeterminate: false
        },
        {
          title: "51~63",
          checkAll: false,
          checked: [],
          enum: common.fileNumArr(51, 63),
          isIndeterminate: false
        }
      ]
    };
  },
  methods: {
    initChecked() {
      this.harmTimesGroup.forEach((item, index) => {
        const temp = [];
        item.enum.forEach(it => {
          this.lastChecked.includes(it) && temp.push(it);
        });
        item.checked = temp;
        this.handleCheckedChange(item.checked, index);
      });
    },
    queryDialogCancel() {
      this.initChecked();
      this.$emit("update:visible", false);
      this.$emit("cancel");
    },
    queryDialogConfirm() {
      const temp = this.harmTimesGroup.reduce(
        (accumulator, currentValue, index) => {
          accumulator.push(...currentValue.checked);
          return accumulator;
        },
        []
      );
      this.$emit("update:lastChecked", temp);
      this.$emit("update:visible", false);
      this.$emit("confirm");
    },
    dialogClose() {
      this.initChecked();
      this.$emit("update:visible", false);
      this.$emit("cancel");
    },
    handleCheckAll(val, index) {
      const group = this.harmTimesGroup[index];
      const arr = val ? group.enum : [];
      group.checked = arr;
      group.isIndeterminate = false;
    },
    handleCheckedChange(val, index) {
      const group = this.harmTimesGroup[index];
      const checkedCount = val.length;
      group.checkAll = checkedCount === group.enum.length;
      group.isIndeterminate =
        checkedCount > 0 && checkedCount < group.enum.length;
    }
  },
  created() {
    this.initChecked();
  }
};
</script>
