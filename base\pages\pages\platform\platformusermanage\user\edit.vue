<template>
  <!-- 1弹窗组件 -->
  <div>
    <CetDialog
      class="CetDialog eem-common max"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
    >
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </span>
      <CetForm
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
      >
        <div class="title">{{ $T("用户信息") }}</div>
        <div class="eem-cont-c1 clearfix mtJ1">
          <el-row :gutter="$J3">
            <el-col :span="6">
              <el-form-item :label="$T('账户名称')" prop="name">
                <ElInput
                  v-model.trim="CetForm_1.data.name"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$T('姓名')" prop="nicName">
                <ElInput
                  v-model.trim="CetForm_1.data.nicName"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$T('移动电话')" prop="mobilePhone">
                <ElInput
                  v-model.trim="CetForm_1.data.mobilePhone"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$T('电子信箱')" prop="email">
                <ElInput
                  v-model.trim="CetForm_1.data.email"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                :label="$T('账户密码')"
                prop="password"
                v-if="!userId"
              >
                <ElInput
                  v-model.trim="CetForm_1.data.password"
                  v-bind="ElInput_1"
                  showPassword
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                :label="$T('确认密码')"
                prop="confirmPassword"
                v-if="!userId"
              >
                <ElInput
                  v-model.trim="CetForm_1.data.confirmPassword"
                  v-bind="ElInput_1"
                  showPassword
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$T('角色')" prop="role">
                <ElSelect
                  v-model="CetForm_1.data.role"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                >
                  <ElOption
                    v-for="item in ElOption_1.options_in"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                v-if="$checkPermission('user_disable') && inputData_in"
                :label="$T('状态')"
                prop="state"
              >
                <ElSelect
                  v-model="CetForm_1.data.state"
                  v-bind="ElSelect_state"
                  v-on="ElSelect_state.event"
                >
                  <ElOption
                    v-for="item in ElOption_state.options_in"
                    :key="item[ElOption_state.key]"
                    :label="item[ElOption_state.label]"
                    :value="item[ElOption_state.value]"
                    :disabled="item[ElOption_state.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <CetButton
                class="fr"
                v-bind="CetButton_infoSave"
                v-on="CetButton_infoSave.event"
              ></CetButton>
            </el-col>
          </el-row>
        </div>
      </CetForm>
      <div class="bottomBox flex-row" v-show="!authBtnDisable">
        <div class="leftContent mrJ3">
          <div class="title ptJ2 pbJ1">{{ $T("通知类型") }}</div>
          <div class="noticeContent eem-cont-c1">
            <div class="label mbJ1">{{ $T("通知方式") }}</div>
            <ElCheckboxGroup
              v-model="ElCheckboxGroup_1.value"
              v-bind="ElCheckboxGroup_1"
              v-on="ElCheckboxGroup_1.event"
              @change="checkboxChange"
            >
              <ElCheckbox
                class="fullwidth"
                v-for="item in ElCheckboxList_1.options_in"
                :key="item[ElCheckboxList_1.key]"
                :label="item[ElCheckboxList_1.label]"
                :disabled="item[ElCheckboxList_1.disabled]"
              >
                {{ item[ElCheckboxList_1.text] }}
              </ElCheckbox>
            </ElCheckboxGroup>
          </div>
        </div>
        <div class="rightContent">
          <el-tabs
            v-model="activeName"
            @tab-click="handleClick"
            class="eem-tabs-custom ptJ2 pbJ1"
          >
            <el-tab-pane
              v-if="!platformUser && !ifDefaultRole"
              :label="$T('项目节点权限')"
              name="project"
            ></el-tab-pane>
            <el-tab-pane :label="$T('实时监控权限')" name="Graph"></el-tab-pane>
            <el-tab-pane
              :label="$T('pecReport报表权限')"
              name="pecReport"
              v-if="!hidePecReport"
            ></el-tab-pane>
            <el-tab-pane
              :label="$T('mReport报表权限')"
              name="mReport"
              v-if="!hideMReport"
            ></el-tab-pane>
            <el-tab-pane
              :label="$T('onlyReport报表权限')"
              name="onlyReport"
              v-if="!hideOnlyReport"
            ></el-tab-pane>
          </el-tabs>
          <div class="treeBox eem-cont-c1">
            <div v-show="activeName === 'project'" class="fullfilled">
              <CetButton
                class="fr mlJ2 saveBtn"
                :disable_in="authBtnDisable"
                v-bind="CetButton_project"
                v-on="CetButton_project.event"
              ></CetButton>
              <CetGiantTree
                ref="projectTree"
                class="CetTree"
                v-bind="CetGiantTree_project"
                v-on="CetGiantTree_project.event"
              ></CetGiantTree>
            </div>
            <div v-show="activeName === 'Graph'" class="fullfilled">
              <CetButton
                class="fr mlJ2 saveBtn"
                :disable_in="authBtnDisable"
                v-bind="CetButton_graph"
                v-on="CetButton_graph.event"
              ></CetButton>
              <CetGiantTree
                ref="graphTree"
                class="CetTree"
                v-bind="CetGiantTree_graph"
                v-on="CetGiantTree_graph.event"
              ></CetGiantTree>
            </div>
            <div v-show="activeName === 'pecReport'" class="fullfilled">
              <CetButton
                class="fr mlJ2 saveBtn"
                :disable_in="authBtnDisable"
                v-bind="CetButton_pecReport"
                v-on="CetButton_pecReport.event"
              ></CetButton>
              <CetGiantTree
                ref="pecReportTree"
                class="CetTree"
                v-bind="CetGiantTree_pecReport"
                v-on="CetGiantTree_pecReport.event"
              ></CetGiantTree>
            </div>
            <div v-show="activeName === 'mReport'" class="fullfilled">
              <CetButton
                class="fr mlJ2 saveBtn"
                :disable_in="authBtnDisable"
                v-bind="CetButton_mReport"
                v-on="CetButton_mReport.event"
              ></CetButton>
              <CetGiantTree
                ref="mReportTree"
                class="CetTree"
                v-bind="CetGiantTree_mReport"
                v-on="CetGiantTree_mReport.event"
              ></CetGiantTree>
            </div>
            <div v-show="activeName === 'onlyReport'" class="fullfilled">
              <CetButton
                class="fr mlJ2 saveBtn"
                :disable_in="authBtnDisable"
                v-bind="CetButton_onlyReport"
                v-on="CetButton_onlyReport.event"
              ></CetButton>
              <CetGiantTree
                ref="onlyReportTree"
                class="CetTree"
                v-bind="CetGiantTree_onlyReport"
                v-on="CetGiantTree_onlyReport.event"
              ></CetGiantTree>
            </div>
          </div>
        </div>
      </div>
      <div class="bottomBox bg1 mtJ2 brC2 pJ2" v-show="authBtnDisable">
        <span class="pJ2">{{ $T("请先保存基本信息") }}</span>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import common from "eem-utils/common";
import customApi from "@/api/custom.js";
export default {
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    currentNode_in: {
      type: Object
    },
    inputData_in: {
      type: Object
    },
    role_in: {
      type: Object
    }
  },

  computed: {
    platformUser() {
      if (!this.inputData_in && !this.currentNode_in) {
        return false;
      }
      if (this.inputData_in) {
        return this.inputData_in.tenantId === 1;
      } else {
        return this.currentNode_in.tenantId === 1;
      }
    },
    authBtnDisable() {
      return this.userId ? false : true;
    },
    isRoot() {
      return this.$store.state.userInfo.id === 1;
    },
    // 是否选择的是默认角色
    ifDefaultRole() {
      let role = this.ElOption_1.options_in.find(i => i.id === this.saveRoleId);
      if (!role) {
        return false;
      }
      let customConfig = {};
      if (role.customConfig) {
        customConfig = JSON.parse(role.customConfig);
      }
      return customConfig.fixedrole;
    },
    // 编辑的是否是默认角色
    isEditDefaultRole() {
      if (!this.role_in) {
        return false;
      }
      let role = this.role_in;
      if (!role) {
        return false;
      }
      let customConfig = {};
      if (role.customConfig) {
        customConfig = JSON.parse(role.customConfig);
      }
      return customConfig.fixedrole;
    },
    hidePecReport() {
      return this._.get(
        this.$store.state,
        "systemCfg.platformusermanageTabs.hidePecReport"
      );
    },
    hideMReport() {
      return this._.get(
        this.$store.state,
        "systemCfg.platformusermanageTabs.hideMReport"
      );
    },
    hideOnlyReport() {
      return this._.get(
        this.$store.state,
        "systemCfg.platformusermanageTabs.hideOnlyReport"
      );
    }
  },

  data(vm) {
    const addDiyDom = (treeId, treeNode) => {
        var aObj = $("#" + treeNode.tId + "_a");
        if (treeNode.level === 0 && vm.treeNodeTooltip) {
          vm.treeNodeTooltip = false;
          const dom = `
            <div class="inline-block relative tooltipBox">
              <i class="el-icon-question fcT2"></i>
              <div class="tooltip el-tooltip__popper is-light" x-placement="bottom">${$T(
                "置灰代表未拥有此节点全部权限"
              )}
                <div x-arrow="" class="popper__arrow" style="left: 49.5px;"></div>
              </div>
            </div>`;
          aObj.append(dom);
        }
      },
      setNodeClasses = (treeId, treeNode) => {
        return treeNode.childSelectState !== 1
          ? { add: ["halfSelectedNode"] }
          : { remove: ["halfSelectedNode"] };
      };
    return {
      treeNodeTooltip: true,
      userId: 0,
      saveRoleId: 0,
      lastName: "project",
      activeName: "project",
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          close: this.CetDialog_1_close_out
        }
      },
      CetButton_infoSave: {
        visible_in: true,
        disable_in: false,
        title: $T("保存基本信息"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_infoSave_statusTrigger_out
        }
      },
      CetButton_project: {
        visible_in: true,
        // disable_in: false,
        title: $T("保存项目权限"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_project_statusTrigger_out
        }
      },
      CetButton_graph: {
        visible_in: true,
        // disable_in: false,
        title: $T("保存实时监控权限"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_graph_statusTrigger_out
        }
      },
      CetButton_pecReport: {
        visible_in: true,
        // disable_in: false,
        title: $T("保存报表权限"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_pecReport_statusTrigger_out
        }
      },
      CetButton_mReport: {
        visible_in: true,
        // disable_in: false,
        title: $T("保存报表权限"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_mReport_statusTrigger_out
        }
      },
      CetButton_onlyReport: {
        visible_in: true,
        title: $T("保存报表权限"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_onlyReport_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: ["name", "tenantId", "parentId"], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入账户名称")
            },
            common.pattern_name,
            common.check_stringLessThan50
          ],
          nicName: [
            {
              required: true,
              message: $T("请输入账户姓名"),
              trigger: ["blur", "change"]
            },
            common.pattern_name,
            common.check_stringLessThan50
          ],
          password: [
            {
              required: true,
              message: $T("请输入账户密码"),
              trigger: ["blur", "change"]
            },
            {
              checkType: "password",
              validatorProp: "cetconfirmpassword",
              trigger: ["blur"]
            },
            common.check_strongPassword
          ],
          confirmPassword: [
            {
              required: true,
              message: $T("请输入确认密码"),
              trigger: ["blur", "change"]
            },
            {
              checkType: "checkPassword",
              relationProp: "password",
              trigger: "blur"
            }
          ],
          mobilePhone: [
            {
              required: true,
              message: $T("请输入手机号码"),
              trigger: ["blur", "change"]
            },
            common.check_phone
          ],
          role: [
            {
              required: true,
              message: $T("请选择角色"),
              trigger: ["blur", "change"]
            }
          ],
          state: [
            {
              required: true,
              message: $T("请选择状态"),
              trigger: ["blur", "change"]
            }
          ],
          email: [
            {
              type: "email",
              message: $T("请输入正确的邮箱格式"),
              trigger: "change"
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入"),
        style: {
          width: "100%"
        },
        event: {}
      },
      ElSelect_1: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_state: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_state: {
        options_in: [
          {
            id: 0,
            text: $T("正常")
          },
          {
            id: 1,
            text: $T("停用")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      //通知类型选择
      ElCheckboxGroup_1: {
        value: [],
        style: {},
        event: {}
      },
      ElCheckboxList_1: {
        options_in: vm.$store.state.systemCfg.alarmnotificationmethodList || [],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      CetGiantTree_project: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "id"
            },
            key: {
              name: "name"
            }
          },
          callback: {
            onCheck: this.CetGiantTree_project_checkedNodes_out
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {}
      },
      CetGiantTree_graph: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "text"
            }
          },
          callback: {
            onCheck: this.CetGiantTree_graph_checkedNodes_out
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {}
      },
      CetGiantTree_pecReport: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "nodeName"
            }
          },
          callback: {
            onCheck: this.CetGiantTree_pecReport_checkedNodes_out
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {}
      },
      CetGiantTree_mReport: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "nodeName"
            }
          },
          callback: {
            onCheck: this.CetGiantTree_mReport_checkedNodes_out
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {}
      },
      CetGiantTree_onlyReport: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "id"
            },
            key: {
              name: "name"
            }
          },
          callback: {
            onCheck: this.CetGiantTree_onlyReport_checkedNodes_out
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {}
      },
      userCustomConfig: "" //保存用户信息里面customConfig配置
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      this.init();
      vm.CetDialog_1.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    async init() {
      this.initTree();
      this.userId = 0;
      this.saveRoleId = 0;
      if (this.inputData_in) {
        this.userId = this.inputData_in.id;
        this.CetDialog_1.title = $T("编辑用户");
        await this.getDetail();
        this.saveRoleId = this.role_in && this.role_in.id;
      }
      await this.getRoleBytenantId();

      if (!this.inputData_in) {
        this.CetDialog_1.title = $T("新增用户");
        this.CetForm_1.data = {};
        this.ElCheckboxGroup_1.value = [];
        this.CetForm_1.resetTrigger_in = new Date().getTime();
      }
      if (this.platformUser || this.isEditDefaultRole) {
        this.activeName = "Graph";
      } else {
        this.activeName = "project";
      }
      this.lastName = this.activeName;
      this.initTooltip();
      this.handleClick({
        name: this.activeName
      });
    },
    initTooltip() {
      this.CetGiantTree_project.confirmHint = false;
      this.CetGiantTree_graph.confirmHint = false;
      this.CetGiantTree_pecReport.confirmHint = false;
      this.CetGiantTree_mReport.confirmHint = false;
      this.CetGiantTree_onlyReport.confirmHint = false;
    },
    initTree() {
      this.CetGiantTree_project.checkedNodes = [];
      this.CetGiantTree_project.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_graph.checkedNodes = [];
      this.CetGiantTree_graph.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_pecReport.checkedNodes = [];
      this.CetGiantTree_pecReport.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_mReport.checkedNodes = [];
      this.CetGiantTree_mReport.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_onlyReport.checkedNodes = [];
      this.CetGiantTree_onlyReport.unCheckTrigger_in = new Date().getTime();
    },
    // 获取角色
    async getRoleBytenantId() {
      var vm = this;
      var tenantId = vm.inputData_in
        ? vm.inputData_in.tenantId
        : vm.currentNode_in.tenantId;

      await customApi.getTenantRole(tenantId).then(response => {
        if (response.code === 0) {
          var data = vm._.get(response, "data", []);
          let isProjectUser = false;
          if (vm.userCustomConfig && JSON.parse(vm.userCustomConfig)) {
            isProjectUser = JSON.parse(vm.userCustomConfig).usertype === 4;
          }
          vm.ElOption_1.options_in = data.filter(item => {
            if (
              item.customConfig &&
              JSON.parse(item.customConfig) &&
              !isProjectUser
            ) {
              return JSON.parse(item.customConfig).roletype !== 4;
            } else {
              return true;
            }
          });
        }
      });
    },
    async getDetail() {
      const vm = this;
      if (!vm.userId) {
        vm.initTree();
        return;
      }
      let res = await customApi.queryUserInfoById(vm.userId);
      if (res.code === 0) {
        let data = vm._.get(res, "data");
        // 基础信息
        let fromData = {
          id: data.id,
          name: data.name,
          email: data.email,
          mobilePhone: data.mobilePhone,
          nicName: data.nicName,
          relativeUserGroup: data.relativeUserGroup,
          role: vm._.get(data, "roles[0].id"),
          state: data.state,
          tenantId: data.tenantId
        };
        vm.CetForm_1.data = fromData;
        vm.CetForm_1.resetTrigger_in = new Date().getTime();
        // 通知
        let notifytypeArr = [];
        if (data.customConfig) {
          notifytypeArr = JSON.parse(data.customConfig).notifytype || [];
          vm.userCustomConfig = data.customConfig;
        }
        vm.ElCheckboxGroup_1.value = notifytypeArr;
      }
    },
    // 展开节点
    expandNode(nodes, key, ztreeObj) {
      setTimeout(() => {
        nodes.forEach(item => {
          let node = ztreeObj.getNodeByParam(key, item[key]);
          let parentNodes = [],
            parentNode = node && node.getParentNode();
          while (parentNode) {
            parentNodes.push(parentNode);
            parentNode = parentNode.getParentNode();
          }
          parentNodes.forEach(i => {
            ztreeObj.expandNode(i, true);
          });
        });
      }, 0);
    },
    async handleClick(val) {
      let obj = {
        project: this.CetGiantTree_project.confirmHint,
        Graph: this.CetGiantTree_graph.confirmHint,
        pecReport: this.CetGiantTree_pecReport.confirmHint,
        mReport: this.CetGiantTree_mReport.confirmHint,
        onlyReport: this.CetGiantTree_onlyReport.confirmHint
      };
      if (obj[this.lastName]) {
        this.$confirm($T("节点权限已修改，是否放弃?"), $T("提示"), {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        })
          .then(async () => {
            await this.getTree(val);
          })
          .catch(() => {
            this.activeName = this.lastName;
          });
      } else {
        await this.getTree(val);
      }
    },
    async getTree(val) {
      this.treeNodeTooltip = true;
      this.lastName = val.name;
      this.initTooltip();
      switch (val.name) {
        case "project":
          await this.getProjectTree();
          break;
        case "Graph":
          await this.getGraphTree();
          break;
        case "pecReport":
          await this.getPecReportTree_out();
          break;
        case "mReport":
          await this.getMReportTree_out();
          break;
        case "onlyReport":
          await this.getOnlyReportTree_out();
          break;
        default:
          break;
      }
    },
    // 获取用户项目节点权限
    async getProjectTree() {
      let vm = this,
        nodes = [];
      vm.CetGiantTree_project.saveFlag = false;
      if (vm.userId) {
        let res = await customApi.userModelnodeQuery(["tenant"], {
          userId: vm.userId
        });
        let data = vm._.get(res, "data", []) || [];
        data.forEach(i => {
          if (i.childSelectState === 1) {
            nodes.push({
              id: i.id,
              nodeId: i.id,
              nodeType: i.modelLabel,
              tree_id: `${i.modelLabel}_${i.id}`
            });
          }
        });
      }
      vm.CetGiantTree_project.checkedNodes = nodes;
      if (!nodes.length) {
        vm.CetGiantTree_project.unCheckTrigger_in = new Date().getTime();
      }

      var queryData = {
        tenantId: 0
      };
      if (vm.inputData_in) {
        queryData.tenantId = vm.inputData_in.tenantId;
      } else {
        queryData.tenantId = vm.currentNode_in.tenantId || vm.currentNode_in.id;
      }
      await customApi.getEnterpriseTree(queryData).then(response => {
        if (response.code === 0) {
          var data = vm._.get(response, "data[0].children", []);
          vm.CetGiantTree_project.inputData_in = vm.formatNodeTree(data);
        } else {
          vm.CetGiantTree_project.inputData_in = [];
        }
        this.$nextTick(() => {
          vm.CetGiantTree_project.saveFlag = true;
        });
      });
    },
    formatNodeTree(rawNodeAry) {
      var me = this;
      rawNodeAry = rawNodeAry || [];
      me._(rawNodeAry).forEach(function (rawNode) {
        // if (rawNode.childSelectState !== 1) {
        //   rawNode.nocheck = true;
        // }
        if (!rawNode.children || !rawNode.children.length) {
          rawNode.children = null;
          rawNode.leaf = true;
        } else {
          rawNode.children = me.formatNodeTree(rawNode.children);
        }
      });
      return rawNodeAry;
    },
    // 获取图形节点权限
    async getGraphTree() {
      let vm = this,
        nodes = [];
      vm.CetGiantTree_graph.saveFlag = false;
      if (vm.userId) {
        let res = await customApi.userGraphnodeQuery(
          vm.$store.state.graphTypes,
          { userId: vm.userId }
        );
        let data = vm._.get(res, "data", []) || [];
        data.forEach(i => {
          if (i.childSelectState === 1) {
            nodes.push({
              nodeId: i.nodeID,
              nodeName: i.nodeName,
              text: i.text,
              nodeType: i.nodeType,
              tree_id: `${i.nodeType}_${i.nodeID}`
            });
          }
        });
      }
      vm.CetGiantTree_graph.checkedNodes = nodes;
      if (!nodes.length) {
        vm.CetGiantTree_graph.unCheckTrigger_in = new Date().getTime();
      }

      var queryData = {
        tenantId: 0
      };
      if (vm.inputData_in) {
        queryData.tenantId = vm.inputData_in.tenantId;
      } else {
        queryData.tenantId = vm.currentNode_in.tenantId || vm.currentNode_in.id;
      }
      await customApi.getGraphTree(queryData).then(response => {
        if (response.code === 0) {
          var data = vm._.get(response, "data", []);
          vm.CetGiantTree_graph.inputData_in = vm.formatGraphNodeTree(data);

          setTimeout(() => {
            vm.expandNode(
              nodes,
              vm.CetGiantTree_graph.setting.data.simpleData.idKey,
              vm.$refs.graphTree.ztreeObj
            );
          }, 0);
        } else {
          vm.CetGiantTree_graph.inputData_in = [];
        }
        this.$nextTick(() => {
          vm.CetGiantTree_graph.saveFlag = true;
        });
      });
    },
    formatGraphNodeTree(rawNodeAry) {
      var me = this;
      rawNodeAry = rawNodeAry || [];

      me._(rawNodeAry).forEach(function (rawNode) {
        rawNode.id = rawNode.nodeId;
        // if (rawNode.childSelectState !== 1) {
        //   rawNode.nocheck = true;
        // }
        if (!rawNode.children || !rawNode.children.length) {
          rawNode.children = null;
          rawNode.leaf = true;
        } else {
          rawNode.leaf = false;
          rawNode.children = me.formatGraphNodeTree(rawNode.children);
        }
        rawNode.tree_id = `${rawNode.nodeType}_${rawNode.id}`;
      });
      return rawNodeAry;
    },
    // 获取报表节点权限
    async getPecReportTree_out() {
      let vm = this,
        nodes = [];
      vm.CetGiantTree_pecReport.saveFlag = false;
      if (vm.userId) {
        let res = await customApi.userPecstarnodeQuery(
          vm.$store.state.pReportTypes,
          { userId: vm.userId }
        );
        let data = vm._.get(res, "data", []) || [];
        data.forEach(i => {
          if (i.childSelectState === 1) {
            nodes.push({
              id: `${i.nodeType}_${i.nodeID}`,
              nodeId: i.nodeID,
              nodeType: i.nodeType,
              tree_id: `${i.nodeType}_${i.nodeID}`
            });
          }
        });
      }
      vm.CetGiantTree_pecReport.checkedNodes = nodes;
      if (!nodes.length) {
        vm.CetGiantTree_pecReport.unCheckTrigger_in = new Date().getTime();
      }

      var queryData = {
        tenantId: 0
      };
      if (vm.inputData_in) {
        queryData.tenantId = vm.inputData_in.tenantId;
      } else {
        queryData.tenantId = vm.currentNode_in.tenantId || vm.currentNode_in.id;
      }
      await customApi.getPecReportTree(queryData).then(response => {
        if (response.code === 0) {
          var data = vm._.get(response, "data", []);
          vm.CetGiantTree_pecReport.inputData_in =
            vm.formatReporthNodeTree(data);
          setTimeout(() => {
            vm.expandNode(
              nodes,
              vm.CetGiantTree_pecReport.setting.data.simpleData.idKey,
              vm.$refs.pecReportTree.ztreeObj
            );
          }, 0);
        } else {
          vm.CetGiantTree_pecReport.inputData_in = [];
        }
        this.$nextTick(() => {
          vm.CetGiantTree_pecReport.saveFlag = true;
        });
      });
    },
    // 获取报表节点权限
    async getMReportTree_out() {
      let vm = this,
        nodes = [];
      vm.CetGiantTree_mReport.saveFlag = false;
      if (vm.userId) {
        let res = await customApi.userModelnodeQuery(
          vm.$store.state.mReportTypes,
          { userId: vm.userId }
        );
        let data = vm._.get(res, "data", []) || [];
        data.forEach(i => {
          if (i.childSelectState === 1) {
            nodes.push({
              nodeId: i.id,
              nodeType: i.modelLabel,
              tree_id: `${i.modelLabel}_${i.id}`
            });
          }
        });
      }
      vm.CetGiantTree_mReport.checkedNodes = nodes;
      if (!nodes.length) {
        vm.CetGiantTree_mReport.unCheckTrigger_in = new Date().getTime();
      }

      var queryData = {
        tenantId: 0
      };
      if (vm.inputData_in) {
        queryData.tenantId = vm.inputData_in.tenantId;
      } else {
        queryData.tenantId = vm.currentNode_in.tenantId || vm.currentNode_in.id;
      }
      await customApi.getMReportTree(queryData).then(response => {
        if (response.code === 0) {
          var data = vm._.get(response, "data", []);
          vm.CetGiantTree_mReport.inputData_in = vm.formatReporthNodeTree(data);
          setTimeout(() => {
            vm.expandNode(
              nodes,
              vm.CetGiantTree_mReport.setting.data.simpleData.idKey,
              vm.$refs.mReportTree.ztreeObj
            );
          }, 0);
        } else {
          vm.CetGiantTree_mReport.inputData_in = [];
        }
        this.$nextTick(() => {
          vm.CetGiantTree_mReport.saveFlag = true;
        });
      });
    },
    // 获取onlyReport报表节点权限
    async getOnlyReportTree_out() {
      let vm = this,
        nodes = [];
      vm.CetGiantTree_onlyReport.saveFlag = false;
      if (vm.userId) {
        const modelLabels = this.$store.state.onlyReportTypes;
        let res = await customApi.userModelnodeQuery(modelLabels, {
          userId: vm.userId
        });
        let data = vm._.get(res, "data", []) || [];
        data.forEach(i => {
          if (i.childSelectState === 1) {
            nodes.push({
              id: i.id,
              nodeId: i.id,
              nodeType: i.modelLabel,
              tree_id: `${i.modelLabel}_${i.id}`
            });
          }
        });
      }
      vm.CetGiantTree_onlyReport.checkedNodes = nodes;
      if (!nodes.length) {
        vm.CetGiantTree_onlyReport.unCheckTrigger_in = new Date().getTime();
      }

      var queryData = {
        tenantId: 0
      };
      if (vm.inputData_in) {
        queryData.tenantId = vm.inputData_in.tenantId;
      } else {
        queryData.tenantId = vm.currentNode_in.tenantId || vm.currentNode_in.id;
      }
      await customApi.getOnlyReportTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_onlyReport.inputData_in = this.formatNodeTree(data);
          setTimeout(() => {
            this.expandNode(
              nodes,
              this.CetGiantTree_onlyReport.setting.data.simpleData.idKey,
              this.$refs.onlyReportTree.ztreeObj
            );
          }, 0);
        } else {
          this.CetGiantTree_onlyReport.inputData_in = [];
        }
        this.$nextTick(() => {
          vm.CetGiantTree_onlyReport.saveFlag = true;
        });
      });
    },
    formatReporthNodeTree(rawNodeAry) {
      var me = this;
      rawNodeAry = rawNodeAry || [];

      me._(rawNodeAry).forEach(function (rawNode) {
        rawNode.id = rawNode.nodeId;
        // if (rawNode.childSelectState !== 1) {
        //   rawNode.nocheck = true;
        // }
        if (!rawNode.children || !rawNode.children.length) {
          rawNode.children = null;
          rawNode.leaf = true;
        } else {
          rawNode.leaf = false;
          rawNode.children = me.formatReporthNodeTree(rawNode.children);
        }
        rawNode.tree_id = `${rawNode.nodeType}_${rawNode.id}`;
      });
      return rawNodeAry;
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetDialog_1_close_out() {
      if (!this.authBtnDisable) {
        this.$emit("finishTrigger_out");
      }
    },
    CetButton_infoSave_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    checkboxChange() {
      this.CetForm_1.localSaveTrigger_in = new Date().getTime();
    },
    CetForm_1_saveData_out(val) {
      const vm = this;
      let saveData = {
          email: val.email || "",
          id: vm.userId,
          mobilePhone: val.mobilePhone,
          name: val.name,
          nicName: val.nicName,
          relativeUserGroup: [],
          roles: [
            {
              id: val.role
            }
          ],
          state: val.state,
          tenantId: val.tenantId,
          customConfig: ""
        },
        fn = "editSimplifyUser",
        cfg = {};
      if (vm.platformUser) {
        cfg.usertype = 1;
      } else if (vm.userCustomConfig) {
        cfg = JSON.parse(vm.userCustomConfig);
      } else {
        cfg.usertype = 2;
      }
      cfg.notifytype = vm.ElCheckboxGroup_1.value;
      saveData.customConfig = JSON.stringify(cfg);
      if (!vm.userId) {
        saveData.state = 0;
        fn = "addSimplifyUser";
        saveData.relativeUserGroup = [vm.currentNode_in.id];
        saveData.tenantId = vm.currentNode_in.tenantId;
        saveData.password = val.password;
      } else {
        saveData.relativeUserGroup = val.relativeUserGroup;
      }
      customApi[fn](saveData).then(response => {
        if (response.code === 0) {
          vm.$message.success($T("保存成功！"));
          vm.saveRoleId = val.role;
          vm.userId = response.data;
          vm.CetForm_1.data.relativeUserGroup = saveData.relativeUserGroup;
          vm.CetForm_1.data.state = saveData.state;
          vm.CetForm_1.data.tenantId = saveData.tenantId;
          // 目标为默认角色，且选择的是项目节点权限
          if (vm.ifDefaultRole && vm.activeName === "project") {
            vm.activeName = "Graph";
            vm.handleClick({
              name: vm.activeName
            });
          }
        }
      });
    },
    CetGiantTree_project_checkedNodes_out() {
      if (!this.CetGiantTree_project.saveFlag) {
        return;
      }
      this.CetGiantTree_project.confirmHint = true;
    },
    CetGiantTree_graph_checkedNodes_out() {
      if (!this.CetGiantTree_graph.saveFlag) {
        return;
      }
      this.CetGiantTree_graph.confirmHint = true;
    },
    CetGiantTree_pecReport_checkedNodes_out() {
      if (!this.CetGiantTree_pecReport.saveFlag) {
        return;
      }
      this.CetGiantTree_pecReport.confirmHint = true;
    },
    CetGiantTree_mReport_checkedNodes_out() {
      if (!this.CetGiantTree_mReport.saveFlag) {
        return;
      }
      this.CetGiantTree_mReport.confirmHint = true;
    },
    CetGiantTree_onlyReport_checkedNodes_out() {
      if (!this.CetGiantTree_onlyReport.saveFlag) {
        return;
      }
      this.CetGiantTree_onlyReport.confirmHint = true;
    },
    CetButton_project_statusTrigger_out() {
      const vm = this;
      let saveData = {
        id: vm.userId,
        modelLabels: ["tenant"],
        modelNodes: []
      };
      const nodes = vm.getCheckNodes(vm.$refs.projectTree.ztreeObj);
      nodes.forEach(item => {
        saveData.modelNodes.push({
          authIds: [],
          disabled: false,
          childSelectState: item.saveChildSelectState,
          id: item.id,
          modelLabel: "tenant",
          rule: ""
        });
      });
      if (!this.platformUser) {
        // 服务商用户补上当前服务商节点，半选
        saveData.modelNodes.push({
          authIds: [],
          disabled: false,
          childSelectState: 2,
          id: this.inputData_in
            ? this.inputData_in.tenantId
            : this.currentNode_in.tenantId,
          modelLabel: "tenant",
          rule: ""
        });
      }
      customApi.editSimplifyUserModelnode(saveData).then(response => {
        if (response.code === 0) {
          vm.CetGiantTree_project.confirmHint = false;
          vm.CetGiantTree_graph.confirmHint = false;
          vm.CetGiantTree_pecReport.confirmHint = false;
          vm.CetGiantTree_mReport.confirmHint = false;
          vm.CetGiantTree_onlyReport.confirmHint = false;
          vm.$message.success($T("保存成功！"));
        }
      });
    },
    CetButton_graph_statusTrigger_out() {
      const vm = this;
      let saveData = {
        id: vm.userId,
        nodeTypes: this.$store.state.graphTypes,
        graphNodes: []
      };
      const nodes = vm.getCheckNodes(vm.$refs.graphTree.ztreeObj);
      nodes.forEach(item => {
        saveData.graphNodes.push({
          disabled: false,
          nodeID: item.nodeId,
          nodeName: item.nodeName,
          text: item.text,
          nodeType: item.nodeType,
          childSelectState: item.saveChildSelectState,
          authIds: []
        });
      });
      customApi.editSimplifyUserGraphnode(saveData).then(response => {
        if (response.code === 0) {
          vm.CetGiantTree_project.confirmHint = false;
          vm.CetGiantTree_graph.confirmHint = false;
          vm.CetGiantTree_pecReport.confirmHint = false;
          vm.CetGiantTree_mReport.confirmHint = false;
          vm.CetGiantTree_onlyReport.confirmHint = false;
          vm.$message.success($T("保存成功！"));
        }
      });
    },
    CetButton_pecReport_statusTrigger_out() {
      const vm = this;
      let saveData = {
        id: vm.userId,
        nodeTypes: this.$store.state.pReportTypes,
        pecStarNodes: []
      };
      const nodes = vm.getCheckNodes(vm.$refs.pecReportTree.ztreeObj);
      nodes.forEach(item => {
        saveData.pecStarNodes.push({
          authIds: [],
          disabled: false,
          nodeID: item.nodeId,
          nodeType: item.nodeType,
          childSelectState: item.saveChildSelectState
        });
      });
      customApi.editSimplifyUserPecstarnode(saveData).then(response => {
        if (response.code === 0) {
          vm.CetGiantTree_project.confirmHint = false;
          vm.CetGiantTree_graph.confirmHint = false;
          vm.CetGiantTree_pecReport.confirmHint = false;
          vm.CetGiantTree_mReport.confirmHint = false;
          vm.CetGiantTree_onlyReport.confirmHint = false;
          vm.$message.success($T("保存成功！"));
        }
      });
    },
    CetButton_mReport_statusTrigger_out() {
      const vm = this;
      let saveData = {
        id: vm.userId,
        modelLabels: this.$store.state.mReportTypes,
        modelNodes: []
      };
      const nodes = vm.getCheckNodes(vm.$refs.mReportTree.ztreeObj);
      nodes.forEach(item => {
        saveData.modelNodes.push({
          authIds: [],
          disabled: false,
          childSelectState: item.saveChildSelectState,
          id: item.nodeId,
          modelLabel: item.nodeType,
          rule: ""
        });
      });
      customApi.editSimplifyUserModelnode(saveData).then(response => {
        if (response.code === 0) {
          vm.CetGiantTree_project.confirmHint = false;
          vm.CetGiantTree_graph.confirmHint = false;
          vm.CetGiantTree_pecReport.confirmHint = false;
          vm.CetGiantTree_mReport.confirmHint = false;
          vm.CetGiantTree_onlyReport.confirmHint = false;
          vm.$message.success($T("保存成功！"));
        }
      });
    },
    CetButton_onlyReport_statusTrigger_out() {
      const vm = this;
      let saveData = {
        id: vm.userId,
        modelLabels: this.$store.state.onlyReportTypes, // nodeType值，1-文件夹，2-报表节点,
        modelNodes: []
      };
      const nodes = vm.getCheckNodes(vm.$refs.onlyReportTree.ztreeObj);
      nodes.forEach(item => {
        saveData.modelNodes.push({
          authIds: [],
          disabled: false,
          childSelectState: item.saveChildSelectState,
          id: item.id,
          modelLabel: item.nodeType,
          rule: ""
        });
      });
      customApi.editSimplifyUserModelnode(saveData).then(response => {
        if (response.code === 0) {
          vm.CetGiantTree_project.confirmHint = false;
          vm.CetGiantTree_graph.confirmHint = false;
          vm.CetGiantTree_pecReport.confirmHint = false;
          vm.CetGiantTree_mReport.confirmHint = false;
          vm.CetGiantTree_onlyReport.confirmHint = false;
          vm.$message.success($T("保存成功！"));
        }
      });
    },
    // 获取节点
    getCheckNodes(ztreeObj) {
      const nodes = ztreeObj.getNodesByFilter(node => {
        const checkStatus = node.getCheckStatus();
        if (node.childSelectState === 1 && !checkStatus.half) {
          node.saveChildSelectState = 1;
        } else {
          node.saveChildSelectState = 2;
        }
        if (!node.checked) return false;

        const parentNode = node.getParentNode();
        if (!parentNode) return true;

        // eslint-disable-next-line no-unsafe-optional-chaining
        const { checked, half } = parentNode?.getCheckStatus();

        if (checked && !half && parentNode.childSelectState === 1) return false;
        return true;
      });
      return nodes;
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep() {
    .is-fullscreen {
      display: flex;
      flex-direction: column;
    }
    .el-dialog__body {
      @include background_color(BG, !important);
      @include padding(J2);
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }
  }
  .nbsp :deep(.el-form-item__label) {
    height: 42px;
  }

  .title {
    font-weight: bold;
    @include margin_left(J3);
  }
  .bottomBox {
    height: 500px;
    .leftContent {
      width: 300px;
      display: flex;
      flex-direction: column;
      .title {
        line-height: 32px;
      }
      .noticeContent {
        flex: 1;
        box-sizing: border-box;
        .label {
          @include font_color(T3);
        }
      }
    }
    .rightContent {
      display: flex;
      flex-direction: column;
      flex: 1;
      .eem-tabs-custom {
        @include background_color(BG);
        :deep(.el-tabs__nav-wrap::after) {
          @include background_color(BG);
        }
      }
      .treeBox {
        box-sizing: border-box;
        flex: 1;
        min-height: 0;
        .fullfilled {
          position: relative;
          .saveBtn {
            position: absolute;
            right: 0;
            top: 0;
          }
        }
        .CetTree {
          width: 100%;
          :deep(.device-search .el-input) {
            width: 240px;
          }
        }
      }
    }
  }
  :deep(.tooltipBox) {
    .tooltip {
      left: -44px;
      display: none;
    }
    &:hover .tooltip {
      display: block;
    }
  }
  :deep(.halfSelectedNode) {
    @include font_color(T6);
  }
}
</style>
