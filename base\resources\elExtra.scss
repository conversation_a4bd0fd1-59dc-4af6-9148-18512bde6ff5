@media print {
  body {
    height: auto !important;
  }
  .noprint-area {
    display: none;
  }
  .el-dialog {
    margin-top: 16px !important;
  }
  .noprint-area * {
    display: none;
  }
  .el-dialog__headerbtn * {
    display: none;
  }
  .receipt {
    // margin-top: 5px !important;
    zoom: 0.78; //a5纸缩放比例--0.78
    // transform-origin: left;
    // transform: scale(1.2, 1.3);
    margin-top: 65px !important;
    border: 2px solid #666666;
    table {
      height: 210px;
    }
    table tr th,
    table tr td {
      font-size: 20px;
    }
  }

  .settlebill {
    overflow: hidden;
    margin-top: 5px !important;
    border: 1px solid #666666;
  }

  .monthlybill {
    zoom: 0.65;
    overflow: hidden;
    .title_style div {
      font-size: 28px !important;
    }
    .el-form-item__label,
    .el-form-item__label div {
      font-weight: bold;
      font-size: 22px !important;
    }
    .el-table__body-wrapper {
      height: 2000px !important;
      width: 100%;
    }
    .el-table .cell {
      font-size: 20px;
      white-space: normal !important;
    }
    .el-table--border {
      border: 0px solid #666666 !important;
    }

    .el-table th.is-leaf,
    .el-table td {
      border: 2px solid #666666 !important;
      padding: 4px;
    }

    .el-table--border th,
    .el-table--border td {
      border: 2px solid #666666 !important;
      padding: 4px;
    }
  }

  .balanceflow {
    width: 1300px !important;
    height: 1000px !important;

    .el-table--border {
      border: 2px solid #666666 !important;
    }

    .el-table th.is-leaf,
    .el-table td {
      border-bottom: 2px solid #666666 !important;
      padding: 4px;
    }

    .el-table--border th,
    .el-table--border td {
      border-right: 2px solid #666666 !important;
      padding: 4px;
    }
  }
  .reminder {
    .el-form-item__label,
    .el-form-item__label div,
    .el-form-item__label span {
      font-size: 22px !important;
      color: #000000 !important;
    }
  }
  #reminder {
    .el-table th.is-leaf,
    .el-table td {
      border-bottom: 1px solid #666666;
    }

    .el-table--border th,
    .el-table--border td {
      border-right: 1px solid #666666;
    }

    .el-table--group,
    .el-table--border {
      border: 1px solid #666666;
    }

    .el-table--border .el-table__body tr.current-row > td {
      border-right: 1px solid #666666 !important;
    }

    .el-footer {
      height: 0 !important;
    }

    .el-pagination {
      display: none !important;
    }
  }
}

.loading-class {
  z-index: 10000 !important;
}

#CallFormContact {
  .el-table th.is-leaf,
  .el-table td {
    border-bottom: 1px solid #666666;
  }

  .el-table--border th,
  .el-table--border td {
    border-right: 1px solid #666666;
  }

  .el-table--group,
  .el-table--border {
    border: 1px solid #666666;
  }

  .el-table--border .el-table__body tr.current-row > td {
    border-right: 1px solid #666666 !important;
  }
}

// 催缴单字体大小的设置
#reminder .el-table .cell {
  font-size: 22px;
  white-space: normal !important;
  font-weight: bold;
}
// 框架tabs，hover标签时相关样式
.quickTabs {
  .el-tag.el-tag--info:hover {
    @include background_color(BG2);
    border-color: #cce0f5;
    color: #0066cc;
  }

  .el-tag .el-tag__close:hover {
    @include font_color(T5);
    // background-color：#0066cc;
    background-color: #0066cc;
  }
}

// 输入框，下拉框，时间选择框前面label样式
.basic-box {
  // width: 100%;
  display: flex;
  // margin-top: 10px;
  .basic-box-label {
    width: 86px;
    text-align: center;
    line-height: 32px;
    height: 32px;
    box-sizing: border-box;
    border: 1px solid;
    @include border_color(B1);
    border-right: 0px;
    @include background_color(BG1);
    @include font_color(ZS);
    @include font_size(Ab);
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }

  //能管
  .el-input,
  .device-Input,
  .device-Select,
  .device-DatePicker {
    flex: 1;
    .el-input__inner {
      @include font_size(Ab);
      border-top-left-radius: 0px !important;
      border-bottom-left-radius: 0px !important;
    }
  }

  .device-Input,
  .device-Select,
  .device-DatePicker {
    flex: 1;
    .el-input__inner {
      border-top-left-radius: 0px !important;
      border-bottom-left-radius: 0px !important;
    }
  }
  .el-date-editor--time-select .el-input__inner {
    border-top-left-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
  }
  .device-Button .el-button {
    padding: 8px;
    i {
      margin-right: -5px;
    }
  }
}

// 表格删除、详情、编辑图标样式---20200918凌英+
.row-detail {
  display: inline-block;
  width: 25px;
  height: 24px;
  cursor: pointer;
  background: url("/static/assets/tableicon/details.png") no-repeat center
    center !important;
  border-radius: 50%;
}

.row-delete {
  display: inline-block;
  width: 25px;
  height: 24px;
  cursor: pointer;
  background: url("/static/assets/tableicon/delete.png") no-repeat center center !important;
  border-radius: 50%;
}
.row-edit {
  display: inline-block;
  width: 25px;
  height: 24px;
  cursor: pointer;
  background: url("/static/assets/tableicon/edit.png") no-repeat center center !important;
  border-radius: 50%;
}

.page {
  // @include background_color(BG1);
}
.el-tabs .el-tabs__content {
  height: calc(100% - 70px) !important;
}
.el-aside {
  padding: 16px;
  // border-radius: 16px;
  @include background_color(BG1);
}

.tree .line-bottom + .el-tree {
  margin: 0;
}
.el-table {
  background-color: white;
}
.el-table--border {
  // border-bottom: 1px solid #f0f0f0;
}

.pd0,
.padding0 {
  padding: 0px !important;
}

.el-tabs--border-card > .el-tabs__content {
  padding: 16px !important;
}

.gianttree .device-search {
  margin: 0px !important;
  padding: 8px 0 !important;
}

.text-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.picker_one {
  .el-picker-panel__footer .el-button--text {
    display: none;
  }
}

.device-Button + .device-Button {
  @include margin_left(J);
}
