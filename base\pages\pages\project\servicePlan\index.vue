<template>
  <div class="page eem-common fullheight">
    <div class="fullheight flex-column eem-min-width">
      <div class="flex-auto eem-container flex-column">
        <div class="clearfix mbJ3">
          <!-- addOrder按钮组件 -->
          <CetButton
            class="fr mlJ1"
            v-bind="CetButton_add"
            v-on="CetButton_add.event"
          ></CetButton>
          <!-- refreshOrder按钮组件 -->
          <CetButton
            class="fr mlJ1"
            v-bind="CetButton_refresh"
            v-on="CetButton_refresh.event"
          ></CetButton>
          <!-- 批量启动按钮组件 -->
          <CetButton
            class="fr mlJ1"
            v-bind="CetButton_start"
            v-on="CetButton_start.event"
          ></CetButton>
          <!-- 批量禁用按钮组件 -->
          <CetButton
            class="fr mlJ1"
            v-bind="CetButton_disable"
            v-on="CetButton_disable.event"
          ></CetButton>
          <!-- 批量删除按钮组件 -->
          <CetButton
            class="fr mlJ1"
            v-bind="CetButton_delete"
            v-on="CetButton_delete.event"
          ></CetButton>
          <div class="fr">
            <el-input
              class="search-input fl"
              :placeholder="$T('输入计划名称')"
              suffix-icon="el-icon-search"
              v-model.trim="filterText"
              size="small"
              @change="serchByKeyword_out(filterText)"
            />
            <div class="eem-radio fl mlJ1 lh32">
              <ElRadioGroup
                v-model="ElRadioGroup_1.value"
                v-bind="ElRadioGroup_1"
                v-on="ElRadioGroup_1.event"
              >
                <ElRadio
                  v-for="item in ElRadioList_1.options_in"
                  :key="item[ElRadioList_1.key]"
                  :label="item[ElRadioList_1.label]"
                  :disabled="item[ElRadioList_1.disabled]"
                >
                  {{ item[ElRadioList_1.text] }}
                </ElRadio>
              </ElRadioGroup>
            </div>
            <div class="fl mlJ1 lh32">
              <ElCheckbox
                v-model="ElCheckbox_1.value"
                v-bind="ElCheckbox_1"
                v-on="ElCheckbox_1.event"
              >
                {{ ElCheckbox_1.text }}
              </ElCheckbox>
            </div>
            <div class="fl mlJ1">
              <customElSelect
                class="querytime-type fl"
                size="small"
                v-model="ElSelect_rank.value"
                v-bind="ElSelect_rank"
                v-on="ElSelect_rank.event"
                :prefix_in="$T('等级')"
              >
                <ElOption
                  v-for="item in ElOption_rank.options_in"
                  :key="item[ElOption_rank.key]"
                  :label="item[ElOption_rank.label]"
                  :value="item[ElOption_rank.value]"
                ></ElOption>
              </customElSelect>
            </div>
            <div class="fl mlJ1">
              <customElSelect
                class="querytime-type fl"
                size="small"
                v-model="ElSelect_team.value"
                v-bind="ElSelect_team"
                v-on="ElSelect_team.event"
                :prefix_in="$T('责任班组')"
              >
                <ElOption
                  v-for="item in ElOption_team.options_in"
                  :key="item[ElOption_team.key]"
                  :label="item[ElOption_team.label]"
                  :value="item[ElOption_team.value]"
                ></ElOption>
              </customElSelect>
            </div>
          </div>
        </div>
        <CetTable
          class="eem-table-custom flex-auto"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
          @selection-change="handleSelectionChange_out"
        >
          <ElTableColumn
            type="selection"
            width="50"
            align="left"
          ></ElTableColumn>
          <template v-for="(column, index) in Columns_Plan">
            <el-table-column
              v-if="column.custom && column.custom === 'tag'"
              v-bind="column"
              :key="index"
              class-name="font0 hand"
              label-class-name="font14"
            >
              <template slot-scope="scope">
                <el-tag
                  size="small"
                  class="text-middle font14"
                  :effect="column.tagEffect"
                  :type="
                    column.tagTypeFormatter
                      ? column.tagTypeFormatter(scope.row, scope.column)
                      : 'primary'
                  "
                >
                  {{
                    column.formatter
                      ? column.formatter(
                          scope.row,
                          scope.column,
                          scope.row[column.prop],
                          scope.$index
                        )
                      : scope.row[column.prop]
                  }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="column.custom && column.custom === 'button'"
              v-bind="column"
              :key="index"
              class-name="font0"
              label-class-name="font14"
            >
              <template slot-scope="scope">
                <span
                  class="clickformore"
                  @click.stop="
                    column.onButtonClick
                      ? column.onButtonClick(scope.row, scope.$index)
                      : void 0
                  "
                >
                  {{
                    column.formatter
                      ? column.formatter(
                          scope.row,
                          scope.column,
                          scope.row[column.prop],
                          scope.$index
                        )
                      : column.text
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              v-else
              v-bind="column"
              :key="index"
              class-name="hand"
            ></el-table-column>
          </template>
          <el-table-column
            align="left"
            :label="$T('操作')"
            :width="en ? 160 : 140"
          >
            <template slot-scope="{ row }">
              <span
                class="clickformore fl mrJ3"
                @click="handleClick_see_out(row)"
              >
                {{ $T("查看") }}
              </span>
              <span
                :class="{
                  clickdisable: !!row.enabled,
                  clickformore: !row.enabled
                }"
                class="fl mrJ3"
                @click="handleClick_edit_out(row)"
              >
                {{ $T("编辑") }}
              </span>
              <span
                :class="{
                  clickdisable: !!row.enabled,
                  clickformore: !row.enabled
                }"
                class="delete fl"
                @click="handleClick_delete_out(row)"
              >
                {{ $T("删除") }}
              </span>
            </template>
          </el-table-column>
        </CetTable>
      </div>
    </div>
    <CreatePlan v-bind="createPlan" v-on="createPlan.event" />
    <EditPlan v-bind="editPlan" v-on="editPlan.event" />
    <detail v-bind="detail" v-on="detail.event" />
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import detail from "./dialog/Detail";
import CreatePlan from "./dialog/CreatePlan";
import EditPlan from "./dialog/EditPlan";
const ONE_MINUTE_MILLISECONDS = 60 * 1000;
const ONE_HOUR_MILLISECONDS = 60 * ONE_MINUTE_MILLISECONDS;

export default {
  name: "servicePlan",
  components: { CreatePlan, detail, EditPlan },
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "name"
      },
      ElRadioList_1: {
        options_in: [
          {
            id: 1,
            text: $T("启用")
          },
          {
            id: 2,
            text: $T("禁用")
          },
          {
            id: 0,
            text: $T("全部")
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      ElRadioGroup_1: {
        value: 1,
        style: {},
        event: {
          change: this.ElRadioGroup_1_change_out
        }
      },
      // addOrder组件
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("新增"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      // refresh组件
      CetButton_refresh: {
        visible_in: true,
        disable_in: false,
        title: $T("刷新"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_refresh_statusTrigger_out
        }
      },
      // 批量删除组件
      CetButton_delete: {
        visible_in: false,
        disable_in: true,
        title: $T("批量删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_delete_statusTrigger_out
        }
      },
      // 批量禁用组件
      CetButton_disable: {
        visible_in: true,
        disable_in: true,
        title: $T("批量禁用"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_disable_statusTrigger_out
        }
      },
      // 批量启动组件inspectObj
      CetButton_start: {
        visible_in: false,
        disable_in: true,
        title: $T("批量启用"),
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_start_statusTrigger_out
        }
      },
      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryMaintenancePlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [
            { name: "enabled_in", operator: "EQ", prop: "enabled" },
            { name: "hide_in", operator: "EQ", prop: "hide" },
            { name: "name_in", operator: "LIKE", prop: "name" },
            { name: "teamId_in", operator: "EQ", prop: "teamid" },
            { name: "tenantId_in", operator: "EQ", prop: "tenantId" },
            {
              name: "worksheettasklevel_in",
              operator: "EQ",
              prop: "worksheettasklevel"
            }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {
          enabled_in: true,
          hide_in: false
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {},
        exportFileName: "",
        event: {},
        highlightCurrentRow: false
      },
      Columns_Plan: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 60,
          label: "#",
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "name",
          minWidth: 100,
          width: "",
          label: $T("计划名称"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formatter
        },
        {
          type: "",
          prop: "executestrategy",
          minWidth: 100,
          width: "cycle",
          label: $T("周期策略"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formatterExecutestrategy
        },
        {
          type: "",
          prop: "cycle",
          minWidth: 80,
          width: "cycle",
          label: $T("周期"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formatterInterval
        },
        {
          type: "",
          prop: "population",
          minWidth: 60,
          width: "",
          label: $T("人员数量"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formatter
        },
        {
          type: "",
          prop: "timeconsumeplan",
          minWidth: 60,
          width: "",
          label: $T("预计时长"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formatterTimeconsume
        },
        {
          type: "",
          prop: "deviceplanrelationship_model",
          minWidth: 150,
          width: "",
          label: $T("维保对象"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formatterRelationship
        },
        {
          type: "",
          prop: "teamName",
          minWidth: 80,
          width: "",
          label: $T("责任班组"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formatter
        },
        {
          type: "",
          prop: "worksheetTaskLevelName",
          minWidth: 40,
          width: "",
          label: $T("等级"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formatter
        },
        {
          type: "",
          prop: "enabled",
          minWidth: 40,
          width: "",
          label: $T("状态"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.filEnabled_out
        },
        {
          type: "",
          prop: "nextFireTime",
          minWidth: 90,
          width: "",
          label: $T("下次维保时间"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.filNextFireTime_out
        }
      ],
      ElCheckbox_1: {
        value: false,
        text: $T("隐藏已结束的计划"),
        disabled: false,
        event: {
          change: this.ElCheckbox_1_change_out
        }
      },
      // 等级下拉框
      ElSelect_rank: {
        value: "",
        style: {
          width: "120px"
        },
        event: {
          change: this.rankChange_out
        }
      },
      ElOption_rank: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text"
      },
      // 责任班组下拉框
      ElSelect_team: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.teamChange_out
        }
      },
      ElOption_team: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name"
      },
      showTopology: false,
      detail: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      createPlan: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        team_in: [], // 责任班组
        rankList: [], // 等级
        event: {
          saveData_out: this.saveCreatePlan
        }
      },
      editPlan: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        team_in: [], // 责任班组
        rankList: [], // 等级
        event: {
          saveData_out: this.saveEditPlan
        }
      },
      filterText: "",
      selectedData: []
    };
  },
  computed: {
    projectTenantId() {
      return this.$store.state.projectTenantId;
    },
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },
  watch: {},
  methods: {
    //过滤表格状态
    filEnabled_out(row, column, cellValue, index) {
      if (cellValue === true) {
        return $T("启用");
      } else if (cellValue === false) {
        return $T("禁用");
      } else {
        return "--";
      }
    },
    //过滤下次维保时间
    filNextFireTime_out(row, column, cellValue, index) {
      if (row.executestrategy === 2) {
        return "--";
      } else {
        if (cellValue) {
          return this.$moment(cellValue).format("YYYY-MM-DD HH:mm:ss");
        } else {
          return $T("已结束");
        }
      }
    },
    ElRadioGroup_1_change_out(val) {
      if (val === 1) {
        this.CetButton_delete.visible_in = false;
        this.CetButton_disable.visible_in = true;
        this.CetButton_start.visible_in = false;
      } else if (val === 2) {
        this.CetButton_delete.visible_in = true;
        this.CetButton_disable.visible_in = false;
        this.CetButton_start.visible_in = true;
      } else {
        this.CetButton_delete.visible_in = true;
        this.CetButton_disable.visible_in = true;
        this.CetButton_start.visible_in = true;
      }
      this.getMaintenancePlanList();
    },
    serchByKeyword_out() {
      this.getMaintenancePlanList();
    },

    ElCheckbox_1_change_out() {
      this.getMaintenancePlanList();
    },
    rankChange_out() {
      this.getMaintenancePlanList();
    },
    teamChange_out() {
      this.getMaintenancePlanList();
    },
    //表格点击多选框
    handleSelectionChange_out(val) {
      this.selectedData = val;
      if (val && val.length > 0) {
        this.CetButton_delete.disable_in = false;
        this.CetButton_disable.disable_in = false;
        this.CetButton_start.disable_in = false;
      } else {
        this.CetButton_delete.disable_in = true;
        this.CetButton_disable.disable_in = true;
        this.CetButton_start.disable_in = true;
      }
    },
    handleClick_see_out(val) {
      this.detail.inputData_in = val;
      this.detail.visibleTrigger_in = new Date().getTime();
    },
    handleClick_edit_out(val) {
      if (val && val.enabled) {
        return this.$message.warning($T("计划已启动，不可编辑"));
      }
      this.editPlan.inputData_in = val;
      this.editPlan.visibleTrigger_in = new Date().getTime();
    },
    handleClick_delete_out(val) {
      if (val && val.enabled) {
        return this.$message.warning($T("计划已启动，不可删除"));
      }
      this.$confirm($T("确定要删除所选项吗？"), $T("删除确认"), {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消")
      }).then(() => {
        customApi.deleteInspectPlan([val.id]).then(res => {
          if (res.code === 0) {
            this.$message.success($T("删除成功!"));
            this.getMaintenancePlanList();
          }
        });
      });
    },
    // addOrder输出
    CetButton_add_statusTrigger_out() {
      this.createPlan.inputData_in = {};
      this.createPlan.visibleTrigger_in = new Date().getTime();
    },
    CetButton_refresh_statusTrigger_out() {
      this.reset();
    },
    reset() {
      this.teamChange_out(this.ElSelect_team.value);
    },
    detail_confirm_out() {},
    createPlan_confirm_out() {},
    showtDetail(row) {
      console.log(row);
    },
    editPlan_confirm_out() {},
    formatter(row, column, cellValue) {
      return cellValue || "--";
    },
    formatterExecutestrategy(row, column, cellValue) {
      const target = this.$store.state.enumerations.executestrategytype.find(
        item => item.id === cellValue
      );
      row.executestrategy$text = target && target.text;
      return (target && target.text) || "--";
    },
    formatterInterval(row, column, cellValue) {
      if (row.aggregationcycle === 0 && cellValue) {
        const cycle = cellValue || "";
        let month = 0;
        let day = 0;
        let hour = 0;

        const cycleAry = cycle.split("T") || [];
        cycleAry.forEach(cycleItem => {
          let subTimeStrAry = [];
          if (cycleItem.indexOf("P") > -1) {
            subTimeStrAry = cycleItem.match(/\d+[YMD]/g) || [];
            subTimeStrAry.forEach(subTimeStr => {
              const num = parseInt(subTimeStr.slice(0, -1));
              const unit = subTimeStr.slice(-1);
              switch (unit) {
                case "Y":
                  month += 12 * num;
                  break;
                case "M":
                  month += num;
                  break;
                case "D":
                  day = num;
                  break;
              }
            });
          } else {
            subTimeStrAry = cycleItem.match(/\d+H/g) || [];
            subTimeStrAry.forEach(subTimeStr => {
              hour = parseInt(subTimeStr.slice(0, -1));
            });
          }
        });
        let cycle$text = "";
        if (row.executestrategy === 2) {
          cycle$text = `${hour}${$T("小时")}`;
        } else if (row.executestrategy === 1) {
          cycle$text = `${month}${$T("月")} ${day}${$T("天")} ${hour}${$T(
            "小时"
          )}`;
        }
        row.cycle$text = cycle$text;
        return cycle$text;
      } else {
        const list = [
          {
            value: 18,
            label: $T("只执行一次")
          },
          {
            value: 12,
            label: $T("1天")
          },
          {
            value: 13,
            label: $T("1周")
          },
          {
            value: 14,
            label: $T("1个月")
          },
          {
            value: 16,
            label: $T("半年")
          },
          {
            value: 17,
            label: $T("1年")
          }
        ];
        for (var i = 0, len = list.length; i < len; i++) {
          if (list[i].value === row.aggregationcycle) {
            row.cycle$text = list[i].label;
            return list[i].label;
          }
        }
        return "--";
      }
    },
    formatterTimeconsume(row, column, cellValue) {
      return cellValue === 0 || cellValue
        ? cellValue / ONE_HOUR_MILLISECONDS + "h"
        : "--";
    },
    formatterRelationship(row, column, cellValue) {
      if (cellValue && cellValue.length) {
        let inspectObject = "";
        cellValue.forEach((item, index) => {
          if (index) {
            inspectObject += " 、 ";
          }
          inspectObject += item.devicename;
        });
        return inspectObject;
      } else {
        return "--";
      }
    },
    // 等级枚举值
    getRankList() {
      // 等级枚举值
      const levelList = this.$store.state.enumerations.worksheettasklevel || [];
      this.ElOption_rank.options_in = [
        {
          id: 0,
          text: $T("全部")
        },
        ...levelList
      ];
      this.createPlan.rankList = this._.cloneDeep(levelList);
      this.editPlan.rankList = this._.cloneDeep(levelList);
      this.ElSelect_rank.value = this._.get(
        this.ElOption_rank.options_in,
        "[0].id",
        null
      );
    },
    // 获取班组列表
    getGroupList() {
      customApi.queryInspectorTeamWithOutUser({}).then(res => {
        if (res.code === 0) {
          this.ElOption_team.options_in = [
            {
              id: 0,
              name: $T("全部")
            },
            ...res.data
          ];
          this.ElSelect_team.value = this._.get(
            this.ElOption_team.options_in,
            "[0].id",
            null
          );
          this.createPlan.team_in = res.data;
          this.editPlan.team_in = res.data;
        }
      });
    },
    // 查询维保计划列表
    getMaintenancePlanList() {
      this.CetTable_1.dynamicInput = {
        enabled_in:
          this.ElRadioGroup_1.value === 1
            ? true
            : this.ElRadioGroup_1.value === 2
            ? false
            : "",
        hide_in: this.ElCheckbox_1.value,
        name_in: this.filterText,
        teamId_in: this.ElSelect_team.value ? this.ElSelect_team.value : "",
        tenantId_in: this.projectTenantId,
        worksheettasklevel_in: this.ElSelect_rank.value
          ? this.ElSelect_rank.value
          : ""
      };
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    saveCreatePlan(val) {
      customApi.addMaintenancePlan(val).then(res => {
        if (res.code === 0) {
          this.$message.success($T("保存成功!"));
          this.createPlan.closeTrigger_in = new Date().getTime();
          this.getMaintenancePlanList();
        }
      });
    },
    saveEditPlan(val) {
      customApi.editMaintenancePlan(val).then(res => {
        if (res.code === 0) {
          this.$message.success($T("保存成功!"));
          this.editPlan.closeTrigger_in = new Date().getTime();
          this.getMaintenancePlanList();
        }
      });
    },
    // 批量删除计划
    deleteInspectPlan_out(params) {
      if (!params) {
        return;
      }
      customApi.deleteInspectPlan(params).then(res => {
        if (res.code === 0) {
          this.$message.success($T("删除成功!"));
          this.reset();
        }
      });
    },
    // 批量禁用巡检计划
    setPlanDisable_out() {
      const _this = this;
      const idArr = [];
      if (!_this.selectedData || _this.selectedData.length === 0) {
        return;
      }
      _this.selectedData.forEach(item => {
        idArr.push(item.id);
      });
      customApi.setMaintenancePlanDisable(idArr).then(res => {
        if (res.code === 0) {
          this.reset();
        }
      });
    },
    // 批量启用巡检计划
    setPlanEnable_out() {
      const _this = this;
      if (!_this.selectedData || _this.selectedData.length === 0) {
        return;
      }
      const idArr = [];
      _this.selectedData.forEach(item => {
        idArr.push(item.id);
      });
      customApi.setMaintenancePlanEnable(idArr).then(res => {
        if (res.code === 0) {
          this.reset();
        }
      });
    },
    // 点击批量删除按钮
    CetButton_delete_statusTrigger_out() {
      var _this = this;
      let isOk = false;
      _this.selectedData.forEach(item => {
        if (item.enabled) {
          isOk = true;
        }
      });
      if (isOk) {
        this.$message.warning($T("计划已启动，不可批量删除"));
        return;
      }
      this.$confirm($T("是否批量删除计划？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: function (action, instance, done) {
          if (action === "confirm") {
            const idArr = [];
            _this.selectedData.forEach(item => {
              idArr.push(item.id);
            });
            _this.deleteInspectPlan_out(idArr);
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action !== "confirm") {
            _this.$message({
              type: "info",
              message: $T("取消删除!")
            });
          }
        }
      });
    },
    // 点击批量禁用按钮
    CetButton_disable_statusTrigger_out() {
      var _this = this;
      this.$confirm($T("是否批量禁用计划？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: function (action, instance, done) {
          if (action === "confirm") {
            _this.setPlanDisable_out();
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action !== "confirm") {
            _this.$message({
              type: "info",
              message: $T("取消禁用!")
            });
          }
        }
      });
    },
    // 点击批量启动按钮
    CetButton_start_statusTrigger_out() {
      var _this = this;
      if (!_this.selectedData || _this.selectedData.length === 0) {
        return;
      }
      let isOk = true;
      _this.selectedData.forEach(item => {
        if (item.finishtime && new Date(item.finishtime) < new Date()) {
          isOk = false;
        }
      });
      if (!isOk) {
        // this.$message.warning("部分计划由于已达到结束时间无法启动，请修改结束时间后重试。");
        this.$confirm(
          $T("部分计划由于已达到结束时间无法启动，请修改结束时间后重试。"),
          $T("提示"),
          {
            confirmButtonText: $T("确定"),
            cancelButtonText: $T("关闭"),
            type: "warning",
            closeOnClickModal: false,
            showClose: false,
            beforeClose: function (action, instance, done) {
              done();
            },
            callback: function (action) {}
          }
        );
        return;
      }
      this.$confirm($T("是否批量启动计划？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: function (action, instance, done) {
          if (action === "confirm") {
            _this.setPlanEnable_out();
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action !== "confirm") {
            _this.$message({
              type: "info",
              message: $T("取消启动!")
            });
          }
        }
      });
    }
  },
  activated: function () {
    // this.getTreeData();
    this.getRankList();
    this.getGroupList();
    this.getMaintenancePlanList();
    this.$nextTick(() => {
      // this.init();
    });
  }
};
</script>
<style lang="scss" scoped>
.search-input {
  width: 180px;
}
.device-Input {
  display: inline-block;
}
.clickformore {
  cursor: pointer;
  text-decoration: none;
  @include font_color(ZS);
  &.delete {
    @include font_color(Sta3);
  }
}
.clickdisable {
  @include font_color(T6);
  cursor: not-allowed;
}
</style>
