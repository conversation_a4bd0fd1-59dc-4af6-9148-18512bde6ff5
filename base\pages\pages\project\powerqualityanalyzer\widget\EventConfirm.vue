<template>
  <div class="eventComfirm">
    <!-- <el-row>
      <span class="time-title fsH1 mbJ2">事件确认</span>
    </el-row> -->
    <el-row :gutter="0">
      <el-row :gutter="0" class="mbJ1">
        <el-col :span="5">
          <span>确认意见：</span>
        </el-col>
        <el-col :span="5">
          {{ confirmConent.length }} / {{ contentLength }}
        </el-col>
      </el-row>
      <el-row :gutter="0">
        <el-col :span="24">
          <el-input
            :rows="10"
            type="textarea"
            placeholder="请输入确认意见。"
            v-model="confirmConent"
          ></el-input>
        </el-col>
      </el-row>
    </el-row>
    <el-row class="message-box">
      <el-col :span="12">确认时间：</el-col>
      <el-col :span="12" class="content">
        <span>
          {{
            (confirmData.updatetime ? confirmData.updatetime : new Date())
              | formatDate
          }}
        </span>
      </el-col>
    </el-row>
    <el-row class="message-box">
      <el-col :span="12">操作人：</el-col>
      <el-col class="content" :span="12">
        <span>{{ userInfo.nicName }}</span>
      </el-col>
    </el-row>
    <!-- <el-row style="text-align: right">
      <el-button size="small" plain @click="close">取消</el-button>
      <el-button size="small" type="primary" @click="saveConfirmData()">
        确定
      </el-button>
    </el-row> -->
  </div>
</template>
<script>
import common from "eem-utils/common";
import moment from "moment";

export default {
  name: "EventConfirm",
  components: {},
  data() {
    return {
      confirmConent: "",
      contentLength: 300
    };
  },
  props: {
    confirmData: {
      type: [Object],
      default: () => ({})
    }
  },
  watch: {
    confirmConent(val, oldVal) {
      if (val && val.length > this.contentLength) {
        this.confirmConent = oldVal;
      }
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  methods: {
    collectQueryParam() {
      const vm = this;
      var list = [];
      // 处理多条确认信息
      if (vm.confirmData.data instanceof Array) {
        for (const item of vm.confirmData.data) {
          if (!item.id) {
            continue;
          }

          const obj = {
            id: item.id,
            modelLabel: item.modelLabel,
            confirmeventstatus: 3,
            remark: vm.confirmConent,
            updatetime: new Date().getTime(),
            operator: vm.userInfo.id,
            projectId: this.projectId
          };
          list.push(obj);
        }

        // return { projectId: this.projectId, list };
        return list;
      }

      // 处理单条确认信息
      const item = vm.confirmData.data;
      const obj = {
        id: item.id,
        modelLabel: item.modelLabel,
        confirmeventstatus: 3,
        remark: vm.confirmConent,
        updatetime: new Date().getTime(),
        operator: vm.userInfo.id,
        projectId: this.projectId
      };
      list.push(obj);

      return list;
    },
    close(data) {
      this.confirmConent = "";
      this.$emit("afterEventConfirmed", data);
    },
    saveConfirmData() {
      const vm = this;
      if (!vm.confirmData || !vm.confirmData.data) {
        return vm.$message({
          message: "请先选择需要确认的事件节点！",
          type: "error"
        });
      }

      if (!vm.confirmConent || vm.confirmConent.length <= 0) {
        return vm.$message({
          message: "请输入确认内容"
        });
      }

      const data = vm.collectQueryParam();
      common.requestData(
        {
          url: "/eem-service/v1/pq/event/confirm?projectId=" + this.projectId,
          data: data
        },
        res => {
          vm.$message({
            message: "保存事件确认信息成功！",
            type: "success"
          });

          vm.close(data);
        }
      );
    }
  },
  filters: {
    formatDate(val, format) {
      if (!val) {
        return "--";
      }
      if (!format) format = "YYYY-MM-DD HH:mm:ss";
      return moment(val).format(format);
    }
  },
  created() {},
  mounted() {}
};
</script>
<style lang="scss" scope>
.time-title {
  display: inline-block;
}
.event-log-table {
  table-layout: fixed;
  width: 100%;
  border-collapse: collapse;
  border: none;
}
.event-log-table td {
  border: 1px solid #ccc;
  text-align: center;
  color: #333;
  padding: 10px 0;
}
.event-log-table tr:nth-child(2n + 1) {
  background-color: #ebfbff;
}
.message-box {
  @include margin_top(J1);
  @include margin_bottom(J1);
}
.message-box .content {
  text-align: right;
}
.eventComfirm {
  :deep(.el-input),
  .el-input--suffix input {
    height: 32px;
  }
}
</style>
