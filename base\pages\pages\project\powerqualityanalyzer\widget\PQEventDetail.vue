<template>
  <div>
    <el-container style="height: 100%; padding: 0px">
      <el-form>
        <div class="event-detail-header">
          <span>基本信息</span>
        </div>
        <div class="eem-cont-c1 mbJ1">
          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item class="form-item text-right" label="时间：">
                {{ eventLog.eventtime | formatDate("YYYY-MM-DD HH:mm:ss.SSS") }}
              </el-form-item>
              <el-form-item
                class="form-item text-right"
                label="设备名称："
                :title="eventLog.monitoredName"
              >
                {{ eventLog.monitoredName }}
              </el-form-item>
              <el-form-item
                class="form-item text-right"
                label="故障定位："
                :title="
                  formatEnum(
                    'transientfaultdirection',
                    eventLog.transientfaultdirection
                  )
                "
              >
                {{
                  formatEnum(
                    "transientfaultdirection",
                    eventLog.transientfaultdirection
                  )
                }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="form-item text-right" label="事件类型：">
                {{
                  formatEnum(
                    "pqvariationeventtype",
                    eventLog.pqvariationeventtype
                  )
                }}
              </el-form-item>
              <el-form-item
                class="form-item text-right"
                label="区域："
                :title="formatEnum('toleranceband', eventLog.toleranceband)"
              >
                {{ formatEnum("toleranceband", eventLog.toleranceband) }}
              </el-form-item>
              <el-form-item
                class="form-item text-right"
                label="状态："
                :class="{ danger: isConfirmed }"
              >
                {{
                  formatEnum("confirmeventstatus", eventLog.confirmeventstatus)
                }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!-- <div v-if="!isConfirmed"> -->
        <div class="event-detail-header">
          <span>确认信息</span>
        </div>
        <div class="eem-cont-c1">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                class="confirmContent form-item"
                label="确认意见："
                :title="eventLog.remark"
              >
                {{ eventLog.remark || "--" }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item class="form-item" label="确认人：">
                {{ operatorname || "--" }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                class="form-item"
                label="确认时间："
                v-if="!isConfirmed"
              >
                {{ eventLog.updatetime | formatDate("YYYY-MM-DD HH:mm:ss") }}
              </el-form-item>
              <el-form-item
                class="form-item"
                label="确认时间："
                v-if="isConfirmed"
              >
                --
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!-- <el-row :gutter="20">
          <el-col :span="24">
            <div style="text-align: right">
              <el-button
                size="small"
                class="is-plain"
                type="primary"
                @click="close"
              >
                关 闭
              </el-button>
            </div>
          </el-col>
        </el-row> -->
      </el-form>
    </el-container>
  </div>
</template>
<script>
import customApi from "@/api/custom";
export default {
  name: "PQEventDetail",
  props: {
    eventLog: {
      type: [Object],
      default: () => ({})
    },
    ENUM: {
      type: [Object],
      default: () => ({})
    }
  },
  computed: {
    isConfirmed() {
      if (!this.eventLog.confirmeventstatus) {
        return false;
      }

      return this.eventLog.confirmeventstatus === 1;
    }
  },
  data() {
    return {
      operatorname: ""
    };
  },
  watch: {
    eventLog: {
      deep: true,
      handler: function (val, oldVal) {
        this.GetUserName_out(null);
      }
    }
  },
  methods: {
    close() {
      this.$emit("closePQEventDetail", null);
    },
    formatEnum(modelLabel, val) {
      if (!modelLabel || !val) return "--";
      const text = this._.find(this.ENUM[modelLabel], ["id", val]).text;
      if (!text) {
        return "--";
      }

      return text;
    },
    GetUserName_out(val) {
      const vm = this;
      const user_id = vm.eventLog.operator;

      if (!user_id) {
        vm.operatorname = "";
        return;
      }

      customApi.queryUserInfoById(user_id).then(response => {
        if (response.code === 0) {
          vm.operatorname = response.data.nicName;
        }
      });
    }
  },
  filters: {
    formatDate(val, format) {
      if (!val) {
        return "--";
      }
      if (!format) format = "YYYY-MM-DD HH:mm:ss";
      return moment(val).format(format);
    }
  },
  mounted() {
    this.GetUserName_out();
  }
};
</script>
<style lang="scss" scoped>
.event-detail-header {
  width: 100%;
  font-weight: bold;
  @include margin_bottom(J1);
  @include margin_left(J3);
}

.el-form {
  width: 100%;
}

.el-form-item {
  margin: 0 !important;
  float: left;
  width: 100%;
}
.form-item {
  :deep(.el-form-item__content) {
    width: calc(100% - 100px);
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
