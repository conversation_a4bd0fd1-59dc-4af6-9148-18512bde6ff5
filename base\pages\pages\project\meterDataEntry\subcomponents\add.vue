<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="small">
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <CetForm
        class="eem-cont-c1"
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
      >
        <el-row :gutter="$J3">
          <el-col :span="12">
            <el-form-item class="custom-form-item" label="换表时间" prop="time">
              <el-date-picker
                v-model="CetForm_1.data.time"
                type="datetime"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                :picker-options="pickerOptions"
                placeholder="选择日期时间"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item
              class="custom-form-item"
              label="测试时间"
              prop="time2"
              v-if="false"
            >
              <el-date-picker
                v-model="CetForm_1.data.time2"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item
              class="custom-form-item"
              label="测试时间"
              prop="time3"
              v-if="false"
            >
              <TimePicker
                v-model="CetForm_1.data.time3"
                placeholder="选择时间"
                :minutesStep="5"
                :dateTime="CetForm_1.data.time2"
                :disabled="CetForm_1.data.time2 ? false : true"
              ></TimePicker>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-for="(item, index) in addColumnArr" :key="index">
            <el-form-item :label="item.label" :prop="item.prop">
              <ElInputNumber
                maxlength="30"
                :placeholder="$T('请输入')"
                v-bind="ElInputNumber_1"
                v-model.trim="CetForm_1.data[item.prop]"
              ></ElInputNumber>
            </el-form-item>
          </el-col>
        </el-row>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";
import TimePicker from "./time-picker.vue";
export default {
  name: "addMeter",
  components: { TimePicker },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    columnArr: {
      type: Array
    }
  },
  data() {
    return {
      CetDialog_1: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("新增"),
        showClose: true,
        event: {}
      },
      // ResetUserPassword表单组件

      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // 1表单组件
      CetForm_1: {
        dataMode: "static", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        labelPosition: "top",
        rules: {
          time: [
            {
              required: true,
              trigger: ["blur", "change"],
              validator: (rule, value, callback) => {
                if (!value) {
                  callback(new Error("请选择日期时间"));
                  return;
                }
                let min = this.$moment(value).minute();
                if (min === 0 || min % 5 === 0) {
                  callback();
                  return;
                } else {
                  callback(new Error("请选择5的倍数的时间"));
                  return;
                }
              }
            }
          ]
        },
        event: {
          //    currentData_out: this.CetForm_1_currentData_out,
          saveData_out: this.CetForm_1_saveData_out
          //    finishData_out: this.CetForm_1_finishData_out,
          //    finishTrigger_out: this.CetForm_1_finishTrigger_out
        }
      },
      pickerOptions: {
        disabledDate: time => {
          return new Date() < time.getTime();
        },
        selectableRange: "00:00:00 - 23:59:59"
      },
      ElInputNumber_1: {
        value: "",
        style: {
          width: "100%"
        },
        min: 0,
        max: 999999999.99,
        step: 2,
        precision: 2,
        controlsPosition: "",
        controls: false,
        event: {}
      },
      addColumnArr: []
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = this._.cloneDeep(val);
      this.CetForm_1.data = {};
      this.CetForm_1.resetTrigger_in = Date.now();
      if (this.columnArr.length === 0) {
        this.getLogic();
      } else {
        this.addColumnArr = this.columnArr;
      }
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      console.log(val);
    },
    "CetForm_1.data.time": {
      handler(newVal, oldVal) {
        if (newVal) {
          let date = new Date();
          let nowDate = this.$moment(new Date()).format("HH:mm:ss");
          let str = "";
          if (
            this.$moment(date).format("yyyy-MM-DD") ===
            this.$moment(newVal).format("yyyy-MM-DD")
          ) {
            let timeVal = this.$moment(newVal).format("HH:mm:ss");
            if (timeVal > nowDate) {
              this.CetForm_1.data.time = new Date();
            }
            str = nowDate;
          } else {
            str = "23:59:59";
          }
          this.pickerOptions.selectableRange = "00:00:00 - " + str;
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    CetButton_preserve_statusTrigger_out(val) {
      //由于接口太耗性能，不需要前端请求用户列表进行判断用户是否重名，后端那边进行处理；
      this.CetForm_1.localSaveTrigger_in = new Date().getTime();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_saveData_out(val) {
      let logtime = new Date(val.time).valueOf();
      let res = [];
      let bool = false;
      this.addColumnArr.forEach(item => {
        res.push({
          logicalid: Number(item.prop.split("_")[1]),
          value: val[item.prop] ? val[item.prop] : null
        });
      });
      this.addColumnArr.forEach(item => {
        if (val[item.prop]) {
          return (bool = true);
        }
      });
      let param = [
        {
          deviceid: this.inputData_in.deviceid,
          dataid: this.inputData_in.dataid,
          logtime: logtime,
          status: this.inputData_in.status,
          meterType: this.inputData_in.meterType,
          logicalValues: res
        }
      ];
      if (bool) {
        this.addData(param);
      } else {
        this.$message.error("请填入表码值");
      }
    },
    addData(val) {
      customApi.getFlinkEdit(val).then(response => {
        if (response.code === 0) {
          this.$message({
            type: "success",
            message: $T("保存成功")
          });
          this.$emit("updata_out");
          this.CetDialog_1.closeTrigger_in = Date.now();
        }
      });
    },
    // 当表头数据没有值的时候
    getLogic() {
      let param = {
        dataIds: [this.inputData_in.dataid],
        deviceIds: [this.inputData_in.deviceid]
      };
      customApi.getDeviceLogic(param).then(response => {
        if (response.code === 0) {
          let data = this._.get(
            response.data,
            "[0].dataIdLogics[0].logics",
            []
          );
          let columnArr = [];
          data.forEach(item => {
            columnArr.push({
              prop: "value_" + item,
              label: "回路" + item + "表码值"
            });
          });
          this.addColumnArr = columnArr;
        }
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped></style>
<!-- 注意：日期弹窗的样式是和vue根组件同级，所以页面额外再添加一个style样式 -->
<style>
.el-picker-panel__footer .el-picker-panel__link-btn.el-button--text {
  display: none;
}
</style>
