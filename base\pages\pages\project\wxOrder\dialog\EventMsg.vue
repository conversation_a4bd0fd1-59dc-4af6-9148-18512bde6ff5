<template>
  <div>
    <CetDialog v-bind="CetDialog_add" v-on="CetDialog_add.event">
      <div class="eem-cont-c1" style="line-height: 1.5">
        <el-row :gutter="24" v-for="(item, index) in eventList" :key="index">
          <el-col :span="7">
            <div>{{ item.name }}:</div>
          </el-col>
          <el-col :span="17">
            <el-tooltip
              :content="filData(eventMsg[item.key], item.type)"
              effect="light"
              placement="top"
            >
              <div class="text-overflow">
                {{ filData(eventMsg[item.key], item.type) }}
              </div>
            </el-tooltip>
          </el-col>
        </el-row>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
export default {
  name: "reservePlanDetail",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  data() {
    return {
      // add弹窗组件
      CetDialog_add: {
        title: $T("事件详情"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_add_open_out,
          close_out: this.CetDialog_add_close_out
        },
        width: "500px",
        showClose: true
      },
      CetButton_confirm: {
        visible_in: false,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      eventList: [
        {
          name: $T("设备名称"),
          key: "deviceName",
          type: "string"
        },
        {
          name: $T("开始时间"),
          key: "eventtime",
          type: "date"
        },
        {
          name: $T("恢复时间"),
          key: "endtime",
          type: "date"
        },
        {
          name: $T("持续时间"),
          key: "duration",
          type: "time"
        },
        {
          name: $T("类型"),
          key: "pecEventTypeName",
          type: "string"
        },
        {
          name: $T("描述"),
          key: "description",
          type: "string"
        }
      ],
      eventMsg: {}
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      new Promise((res, err) => {
        this.queryPeccoreEventLogById_out(res);
      }).then(data => {
        if (data) {
          vm.CetDialog_add.openTrigger_in = new Date().getTime();
        }
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    }
  },

  methods: {
    CetDialog_add_open_out(val) {},
    CetDialog_add_close_out(val) {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {},
    queryPeccoreEventLogById_out(callback) {
      if (!this.inputData_in.sourceid) {
        this.$message.warning($T("无关联事件信息"));
        callback && callback(false);
        return;
      }
      let params, fn;
      if (this.inputData_in?.repairSourceIndexVo) {
        params = {
          code1: this.inputData_in?.repairSourceIndexVo?.code1,
          code2: this.inputData_in?.repairSourceIndexVo?.code2,
          deviceid: this.inputData_in?.repairSourceIndexVo?.deviceid,
          eventbyte: this.inputData_in?.repairSourceIndexVo?.eventbyte,
          eventtime: this.inputData_in?.repairSourceIndexVo?.eventtime,
          peceventtype: this.inputData_in?.repairSourceIndexVo?.peceventtype
        };
        fn = "queryPeccoreEventLogByIndex";
      } else {
        params = {
          sourceid: this.inputData_in.sourceid
        };
        fn = "queryPeccoreEventLogById";
      }
      // 查班组下的人员
      customApi[fn](params).then(res => {
        if (res.code === 0) {
          const resData = this._.get(res, "data", {}) || {};
          this.eventMsg = this._.cloneDeep(resData);
          callback && callback(true);
        }
      });
    },
    filData(val, type = "string") {
      if ([undefined, NaN, null].includes(val)) {
        return "--";
      }
      if (type === "date") {
        return this.$moment(val).format("YYYY-MM-DD HH:mm:ss.SSS");
      } else if (type === "time") {
        var hours = parseInt((val % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        var minutes = parseInt((val % (1000 * 60 * 60)) / (1000 * 60));
        var seconds = parseInt((val % (1000 * 60)) / 1000);
        var milliseconds = parseInt(val % 1000);
        return $T(
          "{0}分钟{1}秒{2}毫秒",
          minutes + hours * 60,
          seconds,
          milliseconds
        );
      } else {
        return val;
      }
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.lh40 {
  height: 40px;
  line-height: 40px;
}
.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
