<template>
  <el-container class="fullfilled flex-column">
    <div class="title eem-cont mbJ3">
      <!-- <i class="icon text-middle"></i> -->
      <span class="common-title-H1">{{ $T("文档权限") }}</span>
      <div class="rfloat font0" v-show="!!userData_in">
        <template v-if="hasModifyAuth">
          <el-button
            class="text-middle"
            type="primary"
            size="small"
            @click.stop="editUser"
          >
            {{ $T("编辑") }}
          </el-button>
        </template>
      </div>
    </div>
    <div style="flex: 1" class="minWH">
      <div class="sub-container eem-cont">
        <div class="user-info-card brC">
          <div class="fullfilled user-info-card-inner">
            <div>
              <el-tooltip effect="light" :content="userDetail.name">
                <span class="fsH1 text-ellipsis fcT5" style="max-width: 100px">
                  {{ userDetail.name || "--" }}
                </span>
              </el-tooltip>
              <el-tooltip effect="light" :content="userDetail.roleName">
                <div class="text-ellipsis ptJ1 fcT5">
                  {{ userDetail.roleName || "--" }}
                </div>
              </el-tooltip>
            </div>

            <div class="mtJ3">
              <div class="flex-row">
                <label class="fcT5">{{ $T("移动电话") }}：</label>
                <el-tooltip effect="light" :content="userDetail.mobilePhone">
                  <span class="flex-auto text-ellipsis fcT5 text-right">
                    {{ userDetail.mobilePhone || "--" }}
                  </span>
                </el-tooltip>
              </div>
              <div class="flex-row">
                <label class="fcT5">{{ $T("电子邮箱") }}：</label>
                <el-tooltip effect="light" :content="userDetail.email">
                  <span class="flex-auto text-ellipsis fcT5 text-right">
                    {{ userDetail.email || "--" }}
                  </span>
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>
        <div class="mtJ2 flex-row">
          <label>{{ $T("用户组") }}：</label>
          <el-tooltip
            effect="light"
            :content="userDetail.relativeUserGroupName"
          >
            <span class="flex-auto text-ellipsis fcZS text-right">
              {{ userDetail.relativeUserGroupName || "--" }}
            </span>
          </el-tooltip>
        </div>
        <div class="fullwidth right-text mt10">
          <img
            v-show="!!userDetail.relativeUserGroupLogo"
            class="usergroup-logo"
            :src="userDetail.relativeUserGroupLogo"
            alt=""
          />
        </div>
      </div>
      <div class="sub-container eem-cont mlJ3">
        <el-container class="fullfilled flex-column">
          <div class="common-title-H2 mbJ3">{{ $T("文档范围") }}</div>
          <CetTree class="flex-auto" v-bind="CetTree_ModelNodes"></CetTree>
        </el-container>
      </div>
    </div>
  </el-container>
</template>
<script>
import commonApi from "@/api/custom";

export default {
  name: "UserDetailContainer",
  props: {
    userData_in: {
      type: Object,
      default: null
    },
    resetTrigger_in: {
      type: Number,
      default: new Date().getTime()
    }
  },
  data() {
    return {
      // 项目节点树组件
      CetTree_ModelNodes: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        defaultExpandAll: true,
        event: {}
      },
      userDetail: {
        name: "",
        mobilePhone: "",
        email: "",
        roleName: "",
        relativeUserGroupName: "",
        relativeUserGroupLogo: null
      },
      flag: true
    };
  },
  computed: {
    hasModifyAuth() {
      return true;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.localStorage) {
          return false;
        } else {
          var storage = window.localStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },
  watch: {
    resetTrigger_in() {
      if (!this.userData_in) {
        return;
      }
      if (!this.flag) {
        return;
      }
      this.flag = false;
      let params = {
        userId: this.userData_in.id,
        tenantId: this.userData_in.tenantId,
        projectId: this.projectId
      };
      commonApi
        .getVideoPermission(params)
        .then(res => {
          if (res.code == 0) {
            this.resetData(res.data);
          }
        })
        .finally(() => {
          this.flag = true;
        });
    }
  },
  methods: {
    // 根据名字查询用户信息
    queryUserInfoByName() {
      const vm = this;

      // 不是编辑模式不需要查询
      if (!vm.userData_in || !vm.userData_in.name) {
        return Promise.reject();
      }
      const p = new Promise((resolve, reject) => {
        commonApi
          .queryUserInfoByName(vm.userData_in.name)
          .then(response => {
            if (response.code !== 0) {
              reject();
              return;
            }
            resolve(response.data);
          })
          .catch(() => {
            reject();
          });
      });

      return p;
    },

    // 重置数据
    resetData(wholeModelNodes) {
      const vm = this;
      if (!vm.userData_in) {
        vm.userDetail.roleName = "";
        vm.userDetail.name = "";
        vm.userDetail.mobilePhone = "";
        vm.userDetail.email = "";
        vm.userDetail.relativeUserGroupName = "";
        vm.userDetail.relativeUserGroupLogo = null;

        vm.CetTree_ModelNodes.inputData_in = wholeModelNodes;
        // vm.CetTree_ModelNodes.filterNodes_in = [];
        return;
      }

      vm.queryUserInfoByName()
        .then(userInfo => {
          const roles = userInfo.roles || [];
          vm.userDetail.roleName = roles.length ? roles[0].name : "";
        })
        .catch(() => {
          vm.userDetail.roleName = "";
        })
        .finally(() => {
          vm.userDetail.name = vm.userData_in.name;
          vm.userDetail.mobilePhone = vm.userData_in.mobilePhone;
          vm.userDetail.email = vm.userData_in.email;
          vm.userDetail.relativeUserGroupName =
            vm.userData_in.relativeUserGroupName;
          vm.userDetail.relativeUserGroupLogo =
            vm.userData_in.relativeUserGroupLogo;

          // let filteredNodes = [];
          // let modelNodes = vm.userData_in.modelNodes || [];
          // modelNodes.forEach(node => {
          //   filteredNodes.push({
          //     tree_id: node.modelLabel + "_" + node.id
          //   });
          // });

          vm.CetTree_ModelNodes.inputData_in = wholeModelNodes;
          // vm.CetTree_ModelNodes.filterNodes_in = filteredNodes;
        });
    },
    // 点击编辑按钮
    editUser() {
      this.$emit(
        "editUser_out",
        this.userData_in,
        [],
        this.CetTree_ModelNodes.inputData_in
      );
    }
  }
};
</script>
<style lang="scss" scoped>
.title {
  overflow: hidden;
  position: relative;
}
.title .icon {
  display: inline-block;
  width: 30px;
  height: 30px;
  background: #fff url("../assets/icon-user.svg") no-repeat center center;
  border-radius: 50px;
}

.sub-container {
  float: left;
  box-sizing: border-box;
  height: 100%;
  width: 360px;
}
.sub-container :deep(.el-tree) {
  overflow: auto;
}

.user-info-card {
  background: -webkit-linear-gradient(top, #00aeca 0%, #4536cb 100%);
  background: -moz-linear-gradient(top, #00aeca 0%, #4536cb 100%);
  background: -ms-linear-gradient(top, #00aeca 0%, #4536cb 100%);
  background: -o-linear-gradient(top, #00aeca 0%, #4536cb 100%);
  background: linear-gradient(to bottom, #00aeca 0%, #4536cb 100%);
  box-sizing: border-box;
}
.user-info-card-inner {
  background: url("../assets/usr-info.png") no-repeat;
  box-sizing: border-box;
  padding: 20px;
}

.usergroup-logo {
  max-width: 100%;
  max-height: 330px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
}
.right-text {
  text-align: right;
}
</style>
