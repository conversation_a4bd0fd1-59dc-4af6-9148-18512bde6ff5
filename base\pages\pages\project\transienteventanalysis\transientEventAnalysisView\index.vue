<template>
  <div class="page eem-common">
    <div class="mbJ3 flex-row fr">
      <div class="headerCenter">
        <CetButton
          class="fl custom—square"
          v-bind="CetButton_prv"
          v-on="CetButton_prv.event"
        ></CetButton>
        <!-- <span class="lh32">{{ date }}</span> -->
        <span class="lh32">
          <el-date-picker
            v-model="elDate.value"
            v-bind="elDate"
            v-on="elDate.event"
          ></el-date-picker>
        </span>
        <CetButton
          class="fr custom—square"
          v-bind="CetButton_next"
          v-on="CetButton_next.event"
        ></CetButton>
      </div>
      <div class="mlJ1">
        <div class="fr mrJ1 eem-cascader" v-if="hasSectionarea">
          <span class="eem-cascader-label">分析对象</span>
          <el-cascader
            v-model="cascader_1.value"
            v-bind="cascader_1"
            v-on="cascader_1.event"
          ></el-cascader>
        </div>
        <div class="fr mrJ1 basic-box" v-if="!hasSectionarea">
          <customElSelect
            v-model="ElSelect_2.value"
            v-bind="ElSelect_2"
            v-on="ElSelect_2.event"
            prefix_in="分析对象"
          >
            <ElOption
              v-for="item in ElOption_2.options_in"
              :key="item[ElOption_2.key]"
              :label="item[ElOption_2.label]"
              :value="item[ElOption_2.value]"
              :disabled="item[ElOption_2.disabled]"
            ></ElOption>
          </customElSelect>
        </div>
      </div>
    </div>
    <div class="flex-column eem-min-width eem-container" style="width: 100%">
      <div class="flex-row">
        <div class="flex-auto common-title-H2">SEMI47曲线分析</div>
        <!-- <div class="headerCenter mrJ2">
          <CetButton
            class="fl"
            v-bind="CetButton_prv"
            v-on="CetButton_prv.event"
          ></CetButton>
          <span class="lh32">{{ date }}</span>
          <CetButton
            class="fr"
            v-bind="CetButton_next"
            v-on="CetButton_next.event"
          ></CetButton>
        </div> -->
        <div class="mlJ2">
          <!-- <div class="fl mrJ1 basic-box" v-if="hasSectionarea">
            <span class="basic-box-label">分析对象</span>
            <el-cascader
              v-model="cascader_1.value"
              v-bind="cascader_1"
              v-on="cascader_1.event"
            ></el-cascader>
          </div>
          <div class="fl mrJ1 basic-box" v-if="!hasSectionarea">
            <customElSelect
              v-model="ElSelect_2.value"
              v-bind="ElSelect_2"
              v-on="ElSelect_2.event"
              prefix_in="分析对象"
            >
              <ElOption
                v-for="item in ElOption_2.options_in"
                :key="item[ElOption_2.key]"
                :label="item[ElOption_2.label]"
                :value="item[ElOption_2.value]"
                :disabled="item[ElOption_2.disabled]"
              ></ElOption>
            </customElSelect>
          </div> -->
          <div class="fl mrJ1 basic-box">
            <customElSelect
              v-model="ElSelect_1.value"
              v-bind="ElSelect_1"
              v-on="ElSelect_1.event"
              prefix_in="采用标准"
            >
              <ElOption
                v-for="item in ElOption_1.options_in"
                :key="item[ElOption_1.key]"
                :label="item[ElOption_1.label]"
                :value="item[ElOption_1.value]"
                :disabled="item[ElOption_1.disabled]"
              ></ElOption>
            </customElSelect>
          </div>
          <CetButton
            class="fr mrJ1"
            v-bind="CetButton_add"
            v-on="CetButton_add.event"
          ></CetButton>
        </div>
      </div>
      <div class="flex-auto" style="padding: 0px">
        <div class="flex-row" style="width: 100%; height: 450px">
          <div class="flex-auto" style="height: 100%">
            <CetChart
              v-bind="CetChart_top"
              @click="CetChart_top_click"
              ref="CetChart_top"
            ></CetChart>
          </div>
          <div
            class="rightTab mrJ2"
            style="width: 450px; margin-top: 60px; height: 312px"
          >
            <el-row :gutter="24" style="margin: 0px">
              <el-col :span="8" class="thColor">序号</el-col>
              <el-col :span="16">
                {{
                  eventAnalysisData.length > 0
                    ? eventAnalysisData.sortCode
                    : "--"
                }}
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0px">
              <el-col :span="8" class="thColor">日期</el-col>
              <el-col :span="16">
                {{
                  eventAnalysisData.length > 0
                    ? $moment(eventAnalysisData[0].eventtime).format(
                        "YYYY年M月D日"
                      )
                    : "--"
                }}
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0px">
              <el-col :span="8" class="thColor">时间</el-col>
              <el-col :span="16">
                {{
                  eventAnalysisData.length > 0
                    ? $moment(eventAnalysisData[0].eventtime).format(
                        "HH:mm:ss.SSS"
                      )
                    : "--"
                }}
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0px">
              <el-col :span="8" class="thColor">线路</el-col>
              <el-col
                v-for="(item, index) in eventAnalysisData"
                :key="index"
                :span="Number((16 / eventAnalysisData.length).toFixed2(0))"
              >
                {{ item.monitoredName }}
              </el-col>
              <el-col v-if="eventAnalysisData.length == 0" :span="16">
                --
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0px">
              <el-col :span="8" class="thColor">压降幅度</el-col>
              <el-col
                v-for="(item, index) in eventAnalysisData"
                :key="index"
                :span="Number((16 / eventAnalysisData.length).toFixed2(0))"
              >
                {{ Number(item.magnitude).toFixed2(2) }}%
              </el-col>
              <el-col v-if="eventAnalysisData.length == 0" :span="16">
                --
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0px">
              <el-col :span="8" class="thColor">剩余电压</el-col>
              <el-col
                v-for="(item, index) in eventAnalysisData"
                :key="index"
                :span="Number((16 / eventAnalysisData.length).toFixed2(0))"
              >
                {{ (100 - Number(item.magnitude)).toFixed2(2) }}%
              </el-col>
              <el-col v-if="eventAnalysisData.length == 0" :span="16">
                --
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0px">
              <el-col :span="8" class="thColor">持续时间（ms）</el-col>
              <el-col
                v-for="(item, index) in eventAnalysisData"
                :key="index"
                :span="Number((16 / eventAnalysisData.length).toFixed2(0))"
              >
                {{ (item.duration / 1000).toFixed2(2) }}
              </el-col>
              <el-col v-if="eventAnalysisData.length == 0" :span="16">
                --
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0px">
              <el-col :span="8" class="thColor">波形</el-col>
              <el-col v-if="waveFormObj" :span="16" class="toWaveForm">
                <span @click="toWaveForm(waveFormObj)">查看波形</span>
              </el-col>
              <el-col v-else :span="16">--</el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>
    <div class="eem-container flex-auto flex-column eem-min-width mtJ3 mbJ3">
      <div>
        <div class="fl common-title-H2">压降事件年统计</div>
        <div class="clearfix mbJ3">
          <CetButton
            class="fr mlJ3"
            v-bind="CetButton_export"
            v-on="CetButton_export.event"
          ></CetButton>
          <div class="fr lh32">
            <el-radio class="mrJ2" v-model="showChart" :label="true">
              棒图
            </el-radio>
            <el-radio v-model="showChart" :label="false">表格</el-radio>
          </div>
        </div>
      </div>
      <div v-show="showChart" style="width: 100%; height: 400px">
        <CetChart v-bind="CetChart_bottom"></CetChart>
      </div>
      <div v-show="!showChart" style="width: 100%; height: 200px">
        <CetTable
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <ElTableColumn
            v-for="(item, index) in ElTableColumnArr"
            v-bind="item"
            :key="index"
          ></ElTableColumn>
        </CetTable>
      </div>
    </div>
    <customCurve
      :chartCurveAll_in="customCurve.chartCurveAll_in"
      :sarficurvetype_in="SARFICurveId"
      :visibleTrigger_in="customCurve.visibleTrigger_in"
      :closeTrigger_in="customCurve.closeTrigger_in"
      :inputData_in="customCurve.inputData_in"
      @confirm_out="customCurve_confirm_out"
    />
    <CetDialog v-bind="CetDialog_wave" v-on="CetDialog_wave.event">
      <div class="wave-view eem-cont-c1">
        <Wave :data="waveData" :title="waveData.title"></Wave>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import customCurve from "./customCurve";
import customApi from "@/api/custom";
import common from "eem-utils/common";
import Wave from "eem-components/Wave";
import { httping } from "@omega/http";
export default {
  name: "transientEventAnalysisView",
  components: {
    customCurve,
    Wave
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.localStorage) {
          return false;
        } else {
          var storage = window.localStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    },
    waveFormObj() {
      if (this.eventAnalysisData && this.eventAnalysisData.length > 0) {
        return this.eventAnalysisData.find(item => item.waveformlogtime);
      } else {
        return false;
      }
    },
    hasSectionarea() {
      return this.$store.state.systemCfg.hasSectionarea;
    }
  },
  data(vm) {
    // 当前主题颜色
    let themeActiveId = localStorage.getItem("omega_theme");
    var ElTableColumnArr = [];
    var dateArr = [
      { label: "", prop: "year" },
      { label: "1月", prop: "1Month" },
      { label: "2月", prop: "2Month" },
      { label: "3月", prop: "3Month" },
      { label: "4月", prop: "4Month" },
      { label: "5月", prop: "5Month" },
      { label: "6月", prop: "6Month" },
      { label: "7月", prop: "7Month" },
      { label: "8月", prop: "8Month" },
      { label: "9月", prop: "9Month" },
      { label: "10月", prop: "10Month" },
      { label: "11月", prop: "11Month" },
      { label: "12月", prop: "12Month" }
    ];
    ElTableColumnArr = dateArr.map(item => {
      return {
        //type: "",      // selection 勾选 index 序号
        prop: item.prop, // 支持path a[0].b
        label: item.label, //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      };
    });
    return {
      cachedWaveData: {},
      waveData: {}, // 波形数据
      SARFICurveId: 1, //曲线id
      date: vm.$moment().year(),
      showChart: true,
      ElTableColumnArr: ElTableColumnArr,
      eventAnalysisData: [], //某一时间节点的事件详情
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: "自定义曲线",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      CetButton_next: {
        visible_in: true,
        disable_in: true,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: "导出",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      CetChart_top: {
        //组件输入项
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "item"
          },
          grid: {
            left: "50px",
            right: "50px"
          },
          xAxis: {
            type: "log",
            min: 0.01,
            interval: 10,
            max: 100,
            splitLine: {
              show: true,
              lineStyle: {
                color: themeActiveId === "light" ? "#E0E4E8" : "#414B6E"
              }
            },
            axisLabel: {
              formatter: (val, i) => {
                return val + "s";
              }
            },
            minorTick: {
              show: true,
              splitNumber: 10
            }
          },
          yAxis: {
            type: "value",
            max: 100,
            interval: 10,
            min: 0,
            splitLine: {
              show: true,
              lineStyle: {
                color: themeActiveId === "light" ? "#E0E4E8" : "#414B6E"
              }
            },
            axisLabel: {
              formatter: "{value} %"
            }
          },
          series: [
            {
              data: [],
              type: "line",
              symbol: "none"
            },
            {
              symbolSize: 14,
              data: [],
              type: "scatter",
              itemStyle: {
                color: themeActiveId === "light" ? "#FFC24C" : "#FCB92C"
              }
            }
          ]
        }
      },
      CetChart_bottom: {
        //组件输入项
        inputData_in: null,
        options: {
          legend: {
            icon: "circle"
          },
          tooltip: {
            trigger: "item"
          },
          grid: {
            left: "50px",
            right: "10px"
          },
          xAxis: {
            type: "category",
            name: "数量",
            nameLocation: "start",
            nameGap: 30,
            nameRotate: 90,
            data: [
              "01",
              "02",
              "03",
              "04",
              "05",
              "06",
              "07",
              "08",
              "09",
              "10",
              "11",
              "12"
            ]
          },
          yAxis: {
            type: "value",
            name: "月份",
            nameLocation: "start",
            nameGap: 30
          },
          series: [
            {
              name: "",
              data: [],
              type: "bar",
              showBackground: true,
              itemStyle: {
                color: "#99DF21"
              }
            },
            {
              name: "",
              data: [],
              type: "bar",
              showBackground: true,
              itemStyle: {
                color: "#2E95FF"
              }
            },
            {
              name: "",
              data: [],
              type: "bar",
              showBackground: true,
              itemStyle: {
                color: "#F9CA1E"
              }
            }
          ]
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      },
      ElSelect_1: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: "",
        style: {
          width: "230px"
        },
        event: {
          change: this.cascader_1_change_out
        }
      },
      ElOption_2: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      cascader_1: {
        value: [],
        options: [],
        props: {
          expandTrigger: "hover",
          label: "name",
          value: "id"
        },
        showAllLevels: false,
        event: {
          change: this.cascader_1_change_out
        }
      },
      customCurve: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        chartCurveAll_in: null
      },
      CetDialog_wave: {
        title: "波形",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          openTrigger_out: this.updateWave,
          closeTrigger_out: this.clearWave
        },
        width: "80%",
        showClose: true
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: () => {
            this.CetDialog_wave.closeTrigger_in = Date.now();
          }
        }
      },
      elDate: {
        value: vm.$moment().startOf("year").valueOf(),
        "value-format": "timestamp",
        type: "year",
        placeholder: $T("请选择"),
        size: "small",
        clearable: false,
        style: {
          width: "150px"
        },
        pickerOptions: {
          disabledDate(time) {
            // return time.getTime() > vm.$moment(vm.startYearTime).add(1, "y").valueOf();
            return time.getTime() > vm.$moment().valueOf();
          }
        },
        event: {
          // change: this.elDate_change
        }
      }
    };
  },
  watch: {
    "elDate.value": {
      handler: function (val, oid) {
        let maxTime = this.$moment(Date.now())
          .add(0, "y")
          .startOf("year")
          .valueOf();
        if (
          this.$moment(this.elDate.value).startOf("year").valueOf() >= maxTime
        ) {
          this.CetButton_next.disable_in = true;
        } else {
          this.CetButton_next.disable_in = false;
        }
        this.getPQEvent();
        this.getPQEventCountByYear();
      },
      deep: true
    }
  },

  methods: {
    customCurve_confirm_out(val) {
      this.getChartCurveAll();
    },
    ElSelect_1_change_out(val) {
      if (!val) {
        this.CetChart_top.options.series[0].data = [];
        return;
      }
      var chartData = this.ElOption_1.options_in.filter(
        item => item.id == val
      )[0];
      if (
        chartData.sarfichartvalue_model &&
        chartData.sarfichartvalue_model.length > 0
      ) {
        var dataArr = [];
        chartData.sarfichartvalue_model.forEach((item, index) => {
          if (item.duration) {
            dataArr.push([
              item.duration / 1000,
              Number(item.magnitude).toFixed2(2)
            ]);
          }
        });
        this.CetChart_top.options.series[0].data = dataArr;
      } else {
        this.CetChart_top.options.series[0].data = [];
      }
    },
    cascader_1_change_out(val) {
      this.getPQEvent();
      this.getPQEventCountByYear();
    },
    CetButton_add_statusTrigger_out(val) {
      this.customCurve.chartCurveAll_in = this._.cloneDeep(
        this.ElOption_1.options_in
      );
      this.customCurve.visibleTrigger_in = this._.cloneDeep(val);
    },
    CetButton_prv_statusTrigger_out(val) {
      this.elDate.value = this.$moment(this.elDate.value)
        .subtract(1, "year")
        .valueOf();
    },
    CetButton_next_statusTrigger_out(val) {
      this.elDate.value = this.$moment(this.elDate.value)
        .add(1, "year")
        .valueOf();
    },
    CetButton_export_statusTrigger_out(val) {
      var queryData = {
        startTime: this.$moment(this.elDate.value)
          .add(-2, "year")
          .startOf("year")
          .valueOf(),
        endTime:
          this.$moment(this.elDate.value)
            .add(0, "year")
            .endOf("year")
            .valueOf() + 1,
        projectId: this.projectId,
        cycle: 14,
        node: {
          id: this.cascader_1.value[1],
          modelLabel: "room"
        }
      };
      if (this.hasSectionarea) {
        if (!this.cascader_1.value[1]) {
          return;
        }
        queryData.node.id = this.cascader_1.value[1];
      } else {
        if (!this.ElSelect_2.value) {
          return;
        }
        queryData.node.id = this.ElSelect_2.value;
      }
      common.downExcel(
        `/eem-service/v1/pq/event/exportPQEventCountByYear`,
        queryData,
        this.token
      );
    },
    toWaveForm(val) {
      this.cachedWaveData = {
        deviceId: val.srcdeviceid,
        waveTime: val.waveformlogtime,
        title: val.monitoredName
      };
      this.CetDialog_wave.openTrigger_in = new Date().getTime();
    },

    updateWave() {
      this.waveData = { ...this.cachedWaveData };
    },

    clearWave() {
      this.cachedWaveData = {};
      this.waveData = {};
    },

    CetChart_top_click(val) {
      this.waveData = {};
      if (!val) {
        this.eventAnalysisData = [];
        return;
      }
      // 设置当前点标红
      var data;
      this.CetChart_top.options.series[1].data.forEach(item => {
        item.itemStyle = {
          // color: "#000"
        };
        if (item.value[0] == val.value[0] && item.value[1] == val.value[1]) {
          data = item;
        }
      });
      data.itemStyle = {
        color: "red"
      };
      // 请求某点详细数据
      var data1;
      if (val.id) {
        // 接口请求的默认选中进入
        data1 = val;
      } else {
        data1 = val.data;
      }
      var queryData = {
        eventTime: data1.eventtime,
        nodeId: data1.monitoredid,
        nodeLabel: data1.monitoredlabel,
        projectId: this.projectId
      };
      customApi
        .transienteventanalysisEventAnalysis(queryData)
        .then(response => {
          if (
            response.code === 0 &&
            response.data &&
            response.data.length > 0
          ) {
            this.eventAnalysisData = response.data;
            this.eventAnalysisData.sortCode = data.sortCode;
          } else {
            this.eventAnalysisData = [];
          }
        });
    },
    // 查询所有曲线
    getChartCurveAll() {
      customApi.queryEnum({ rootLabel: "SARFICurveType" }).then(response => {
        var SARFICurveId = response.data.filter(item => item.text == "SEMI")[0]
          .id;
        this.SARFICurveId = SARFICurveId;
        customApi
          .transienteventanalysisChartCurve({
            curveType: SARFICurveId,
            projectId: this.projectId
          })
          .then(res => {
            if (res.code === 0 && res.data && res.data.length > 0) {
              this.ElOption_1.options_in = res.data;
              this.ElSelect_1.value = this.ElOption_1.options_in[0].id;
              this.ElSelect_1_change_out(this.ElOption_1.options_in[0].id);
            } else {
              this.ElOption_1.options_in = [];
              this.ElSelect_1.value = "";
              this.ElSelect_1_change_out();
            }
          });
      });
    },
    // 查询所有PQ打点
    getPQEvent() {
      this.CetChart_top.options.series[1].data = [];
      this.CetChart_top_click();
      let params = {
        endTime: this.$moment(this.elDate.value).endOf("year").valueOf() + 1,
        projectId: this.projectId,
        startTime: this.$moment(this.elDate.value).startOf("year").valueOf(),
        node: {
          id: null,
          modelLabel: "room"
        }
      };
      if (this.hasSectionarea) {
        if (!this.cascader_1.value[1]) {
          return;
        }
        params.node.id = this.cascader_1.value[1];
      } else {
        if (!this.ElSelect_2.value) {
          return;
        }
        params.node.id = this.ElSelect_2.value;
      }

      customApi.transienteventanalysisPQEvent(params).then(response => {
        if (response.code === 0 && response.data && response.data.length > 0) {
          var dataArr = [];
          // 对数轴x轴不能为0
          response.data.forEach((item, index) => {
            if (Number(item.duration)) {
              dataArr.push({
                value: [
                  Number(item.duration / 1000 / 1000),
                  Number(item.magnitude).toFixed2(2)
                ],
                itemStyle: {
                  color: "#000"
                },
                sortCode: index + 1,
                ...item
              });
            }
          });
          if (dataArr.length > 0) {
            dataArr[0].itemStyle = {
              color: "red"
            };
          }
          this.CetChart_top.options.series[1].data = dataArr;
          this.CetChart_top_click(dataArr[0]);
        } else {
          this.CetChart_top.options.series[1].data = [];
          this.CetChart_top_click();
        }
      });
    },
    // 根据年份查询PQ事件统计数据
    getPQEventCountByYear() {
      this.CetChart_bottom.options.series[0].name = "";
      this.CetChart_bottom.options.series[0].data = [];
      this.CetChart_bottom.options.series[1].name = "";
      this.CetChart_bottom.options.series[1].data = [];
      this.CetChart_bottom.options.series[2].name = "";
      this.CetChart_bottom.options.series[2].data = [];
      this.CetTable_1.data = [];

      var params = {
        startTime: this.$moment(this.elDate.value)
          .add(-2, "year")
          .startOf("year")
          .valueOf(),
        endTime:
          this.$moment(this.elDate.value)
            .add(0, "year")
            .endOf("year")
            .valueOf() + 1,
        projectId: this.projectId,
        cycle: 14,
        node: {
          id: null,
          modelLabel: "room"
        }
      };
      if (this.hasSectionarea) {
        if (!this.cascader_1.value[1]) {
          return;
        }
        params.node.id = this.cascader_1.value[1];
      } else {
        if (!this.ElSelect_2.value) {
          return;
        }
        params.node.id = this.ElSelect_2.value;
      }
      // 房间id cascader_1。value{1}
      customApi
        .ransienteventanalysisQueryPQEventCountByYear(params)
        .then(response => {
          if (
            response.code === 0 &&
            response.data &&
            response.data.length > 0
          ) {
            // data中返回三组数据，为近三年的数据，第一组数据为最近一年的数据，最后一组为最早一年数据
            var data = response.data.reverse();
            var tabData = [];
            data.forEach((item, index) => {
              var obj = {
                year: this.$moment(item[0].logTime).year() + "年"
              };
              this.CetChart_bottom.options.series[index].name = this.$moment(
                item[0].logTime
              ).year();
              this.CetChart_bottom.options.series[index].data = item.map(
                (i, d) => {
                  obj[d + 1 + "Month"] = i.count;
                  return Number(i.count);
                }
              );
              tabData.push(obj);
            });
            this.CetTable_1.data = tabData;
          }
        });
    },
    // 获取配电室
    getNode(fn) {
      let params = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [
          { modelLabel: "sectionarea" },
          {
            filter: {
              expressions: [
                { limit: 1, operator: "EQ", prop: "roomtype" },
                { limit: [4, 11], operator: "IN", prop: "voltagelevel" }
              ]
            },
            modelLabel: "room"
          }
        ],
        treeReturnEnable: true
      };

      httping({
        url: "/eem-service/v1/project/nodeTree",
        method: "POST",
        data: params
      }).then(res => {
        if (res.code === 0) {
          this.cascader_1.options = this._.get(res, "data[0].children", []);
          if (this._.get(res, "data[0].children[0].children[0].id")) {
            this.cascader_1.value = [
              this._.get(res, "data[0].children[0].id"),
              this._.get(res, "data[0].children[0].children[0].id")
            ];
          } else {
            this.cascader_1.value = [];
          }
          fn && fn();
        }
      });
    },
    // 获取配电室
    getNodeByRoom(fn) {
      let params = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [
          {
            filter: {
              expressions: [
                { limit: 1, operator: "EQ", prop: "roomtype" },
                { limit: [4, 11], operator: "IN", prop: "voltagelevel" }
              ]
            },
            modelLabel: "room"
          }
        ],
        treeReturnEnable: true
      };
      httping({
        url: "/eem-service/v1/project/nodeTree",
        method: "POST",
        data: params
      }).then(res => {
        if (res.code === 0) {
          this.ElOption_2.options_in = this._.get(res, "data[0].children", []);
          this.ElSelect_2.value = this._.get(res, "data[0].children[0].id");
          fn && fn();
        }
      });
    }
  },
  created: function () {},
  mounted: function () {
    this.elDate.value = this.$moment(Date.now()).startOf("year").valueOf();
    this.getChartCurveAll();
    if (this.hasSectionarea) {
      this.getNode(() => {
        this.getPQEventCountByYear();
        this.getPQEvent();
      });
    } else {
      this.getNodeByRoom(() => {
        this.getPQEventCountByYear();
        this.getPQEvent();
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.headerCenter {
  text-align: center;
  width: 220px;
}
.tabBtn {
  font-size: 16px;
  color: #2e95ff;
  cursor: pointer;
  &.action {
    color: #000;
  }
}
.rightTab {
  text-align: center;
  border-right: 1px solid;
  border-bottom: 1px solid;
  @include border_color(B2);
  :deep(.el-col) {
    border-top: 1px solid;
    border-left: 1px solid;
    @include border_color(B2);
    line-height: 38px;
  }
  .toWaveForm {
    color: rgb(46, 149, 255);
    cursor: pointer;
  }
}

.wave-view {
  width: 100%;
  height: 500px;
}

.thColor {
  @include background_color(BG);
}

.eem-cascader {
  position: relative;
  .eem-cascader-label {
    position: absolute;
    z-index: 1;
    height: 32px;
    line-height: 32px;
    left: 12px;
    @include font_color(ZS);
  }
  :deep(.el-cascader .el-input .el-input__inner) {
    padding-left: 75px;
  }
}
</style>
