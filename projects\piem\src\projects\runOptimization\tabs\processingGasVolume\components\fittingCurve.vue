<template>
  <div class="page">
    <CetChart
      class="fullheight"
      v-bind="CetChart_fittingCurve"
      :key="chartKey"
    ></CetChart>
  </div>
</template>

<script>
import common from "eem-utils/common";
export default {
  name: "fittingCurve",
  components: {},
  props: {
    fitAndOptimizeData: {
      type: Object
    },
    type: {
      type: String
    },
    queryTime: {
      type: Number
    }
  },
  data() {
    return {
      chartKey: 0,
      // fittingCurve组件
      CetChart_fittingCurve: {
        //组件输入项
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "item",
            confine: true,
            formatter: params => {
              if (params.componentSubType !== "scatter") return;
              const head = `<div style="color: ${params.color}">${
                params.data[2]
              }${params.seriesIndex === 3 ? $T("优化后") : $T("优化前")}</div>`;
              const name =
                this.type === "efficiency" ? $T("机组效率") : $T("单位消耗");
              const unit =
                this.type === "efficiency" ? $T("%") : $T("kWh/万立方米");
              const text = `<div style="margin-top: 4px"><span>${name}：</span><span>${params.data[1]}${unit}</span></div>`;
              const text1 = `<div style="margin-top: 4px"><span>输气量：</span><span>${params.data[0]}万立方米</span></div>`;
              return head + text + text1;
            }
          },
          grid: {
            top: 60,
            bottom: 20,
            left: 70,
            right: 80
          },
          legend: {
            show: true,
            data: [
              "单日优化前",
              "单日优化后",
              "有效样本范围",
              "效率-气量拟合公式"
            ]
          },
          xAxis: {
            type: "value",
            name: $T("输气量\n(万立方米)"),
            min: 0,
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            nameTextStyle: {
              lineHeight: 18
            }
          },
          yAxis: {
            name: $T("机组效率(%)"),
            type: "value",
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed"
              }
            }
          },
          series: [
            {
              name: $T("单日优化前"),
              type: "scatter",
              symbolSize: 15,
              data: []
            },
            {
              name: $T("当日优化前"),
              type: "scatter",
              symbolSize: 15,
              data: []
            },
            {
              name: $T("有效样本范围"),
              type: "bar",
              data: [],
              markArea: {
                itemStyle: { opacity: 0.2 },
                data: []
              }
            },
            {
              name: $T("单日优化后"),
              type: "scatter",
              symbolSize: 15,
              data: []
            },
            {
              name: $T("效率-气量拟合公式"),
              type: "line",
              symbolSize: 15,
              smooth: false,
              showSymbol: false,
              data: [],
              lineStyle: {
                type: [0, 0]
              }
            }
          ]
        }
      }
    };
  },
  watch: {
    fitAndOptimizeData(val) {
      this.renderCharts();
    },
    type(val) {
      this.renderCharts();
    }
  },
  methods: {
    renderCharts() {
      this.CetChart_fittingCurve.options.yAxis.name =
        this.type === "efficiency"
          ? $T("机组效率(%)")
          : $T("单位消耗(kWh/万立方米)");

      this.CetChart_fittingCurve.options.series[4].name =
        this.CetChart_fittingCurve.options.legend.data[3] =
          this.type === "efficiency"
            ? $T("效率-气量拟合公式")
            : $T("单耗-气量拟合公式");

      this.CetChart_fittingCurve.options.series[0].data = [];
      this.CetChart_fittingCurve.options.series[1].data = [];
      this.CetChart_fittingCurve.options.series[2].markArea.data = [];
      this.CetChart_fittingCurve.options.series[3].data = [];
      this.CetChart_fittingCurve.options.series[4].data = [];
      const fitAndOptimizeData = this.fitAndOptimizeData || {};
      const fitCurveVos = fitAndOptimizeData.fitCurveVos || [];

      this.CetChart_fittingCurve.options.xAxis.min =
        fitAndOptimizeData?.gasTransmissionVolumeReservedMin || 0;

      // 单日优化前
      const notTodayFitCurveVos =
        fitCurveVos.filter(
          item =>
            this.$moment(item.logTime).startOf("d").valueOf() !==
            this.$moment(this.queryTime).startOf("d").valueOf()
        ) || [];

      // 当日优化前
      const todayFitCurveVos =
        fitCurveVos.find(
          item =>
            this.$moment(item.logTime).startOf("d").valueOf() ===
            this.$moment(this.queryTime).startOf("d").valueOf()
        ) || {};

      // 单日优化后
      const optimizeValue = fitAndOptimizeData?.optimizeValue || {};

      const key = this.type === "efficiency" ? "efficiency" : "unitConsumption";

      const optimizeKey =
        this.type === "efficiency"
          ? "gasTransmissionVolumeReservedPoint"
          : "consumptionReservedPoint";

      this.CetChart_fittingCurve.options.series[0].data =
        notTodayFitCurveVos.map(item => [
          item.gasTransmissionVolume,
          common.formatNumberWithPrecision(item[key], 2),
          this.$moment(item.logTime).format("YYYY/MM/DD")
        ]);
      this.CetChart_fittingCurve.options.series[1].data = [
        [
          todayFitCurveVos.gasTransmissionVolume,
          common.formatNumberWithPrecision(todayFitCurveVos[key], 2),
          this.$moment(todayFitCurveVos.logTime).format("YYYY/MM/DD")
        ]
      ];
      this.CetChart_fittingCurve.options.series[3].data = [
        [
          optimizeValue.gasTransmissionVolume,
          common.formatNumberWithPrecision(optimizeValue[key], 2),
          this.$moment(optimizeValue.logTime).format("YYYY/MM/DD")
        ]
      ];
      this.CetChart_fittingCurve.options.series[2].markArea.data = [
        [
          {
            xAxis: fitAndOptimizeData?.gasTransmissionVolumeMin || 0
          },
          {
            xAxis: fitAndOptimizeData?.gasTransmissionVolumeMax || 0
          }
        ]
      ];

      const fitFormula = fitAndOptimizeData[optimizeKey] || [];
      this.CetChart_fittingCurve.options.series[4].data = fitFormula.map(
        item => [item.x, common.formatNumberWithPrecision(item.y, 2)]
      );

      this.chartKey++;
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
