<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog v-bind="CetDialog_pagedialog" v-on="CetDialog_pagedialog.event">
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <CetForm
        class="eem-cont-c1"
        :data.sync="CetForm_pagedialog.data"
        v-bind="CetForm_pagedialog"
        v-on="CetForm_pagedialog.event"
        ref="dialogForm"
      >
        <el-row :gutter="$J3">
          <el-col :span="isSigninByNFC_in ? 6 : 8">
            <el-form-item :label="$T('名称')" prop="name">
              <ElInput
                v-model.trim="CetForm_pagedialog.data.name"
                v-bind="ElInput_name"
                v-on="ElInput_name.event"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="isSigninByNFC_in">
            <el-form-item label="NFC" prop="nfc">
              <ElInput
                v-model="CetForm_pagedialog.data.nfc"
                v-bind="ElInput_NFC"
                v-on="ElInput_NFC.event"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="isSigninByNFC_in ? 6 : 8">
            <el-form-item :label="$T('分组')" prop="signInGroupIds">
              <ElSelect
                class="custom-select-tag"
                v-model="CetForm_pagedialog.data.signInGroupIds"
                v-bind="ElSelect_group"
                v-on="ElSelect_group.event"
              >
                <ElOption
                  v-for="item in ElOption_group.options_in"
                  :key="item[ElOption_group.key]"
                  :label="item[ElOption_group.label]"
                  :value="item[ElOption_group.value]"
                  :disabled="item[ElOption_group.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <el-col :span="isSigninByNFC_in ? 6 : 8">
            <el-form-item :label="$T('最短时间间隔')" prop="interval">
              <span slot="label">
                {{ $T("最短时间间隔") }}
                <el-popover trigger="hover">
                  <div>{{ $T("距离下一个签到点的最短时间间隔") }}</div>
                  <i slot="reference" class="el-icon-question fsH3"></i>
                </el-popover>
              </span>
              <el-input-number
                v-model.number="CetForm_pagedialog.data.interval"
                v-bind="ElInput_interval"
                v-on="ElInput_interval.event"
              ></el-input-number>
              <span class="form-item-unit">min</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="$J3">
          <el-col :span="isSigninByNFC_in ? 6 : 8">
            <el-form-item :label="$T('地址')" prop="address">
              <ElInput
                v-model="CetForm_pagedialog.data.address"
                v-bind="ElInput_address"
                v-on="ElInput_address.event"
              ></ElInput>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- add按钮组件 -->
        <CetButton
          v-bind="CetButton_add"
          v-on="CetButton_add.event"
        ></CetButton>
        <el-row class="mtJ3" :gutter="$J3" style="height: 400px">
          <el-col :span="16" class="fullheight">
            <CetTable
              :data.sync="CetTable_inspectObj.data"
              :dynamicInput.sync="CetTable_inspectObj.dynamicInput"
              v-bind="CetTable_inspectObj"
              v-on="CetTable_inspectObj.event"
            >
              <template v-for="item in Columns_inspectObj">
                <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
              </template>
              <ElTableColumn v-bind="ElTableColumn_operate">
                <template slot-scope="{ row }">
                  <span class="deleteHandle" @click="handleDelete(row)">
                    {{ $T("删除") }}
                  </span>
                </template>
              </ElTableColumn>
            </CetTable>
          </el-col>
          <el-col :span="8">
            <span>{{ $T("签到点图片") }}:</span>
            <el-upload
              ref="upload"
              class="avatar-uploader mtJ1"
              action="/eem-service/v1/common/uploadFile"
              :headers="{ Authorization: this.token }"
              accept="image/png,image/jpg,image/gif,image/bmp,image/jpeg"
              :file-list="fileList"
              :multiple="false"
              :show-file-list="false"
              :before-upload="beforeImgUpload"
              :on-change="handleChange"
              :on-success="handleUploadSuccess"
              :on-error="errorUpload"
            >
              <img
                v-if="imageUrl"
                :src="imageUrl"
                class="avatar"
                :alt="$T('签到点图片')"
              />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="tip" class="el-upload__tip">
                {{ $T("上传一张png/jpg/gif/bmp/jpeg格式图片") }}
              </div>
            </el-upload>
          </el-col>
        </el-row>
        <inspectObjDialog
          v-bind="inspectObjDialog"
          v-on="inspectObjDialog.event"
        ></inspectObjDialog>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import inspectObjDialog from "./inspectObjDialog";

export default {
  name: "addOrEditSigninDialog",
  components: {
    inspectObjDialog
  },
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    isAdd: {
      type: Boolean
    },
    // 选中的分组
    activeGroup: {
      type: Object
    },
    // 分组列表
    groupList: {
      type: Array
    },
    isSigninByNFC_in: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      imageUrl: "",
      fileList: [],
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: "",
        showClose: true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "component", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "60px",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入名称"),
              trigger: ["blur"]
            },
            {
              max: 20,
              message: $T("不得超过20个字符"),
              trigger: "blur"
            }
          ],
          nfc: [
            {
              required: true,
              message: $T("请输入NFC"),
              trigger: ["blur"]
            }
          ],
          signInGroupIds: [
            {
              required: true,
              message: $T("请选择分组"),
              trigger: ["blur", "change"]
            }
          ],
          address: [
            {
              required: false,
              message: $T("请输入地址"),
              trigger: ["blur"]
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_pagedialog_currentData_out,
          saveData_out: this.CetForm_pagedialog_saveData_out,
          finishData_out: this.CetForm_pagedialog_finishData_out,
          finishTrigger_out: this.CetForm_pagedialog_finishTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // name组件
      ElInput_name: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_name_change_out,
          input: this.ElInput_name_input_out
        }
      },
      // NFC组件
      ElInput_NFC: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {
          width: "100%"
        }
      },
      // address组件
      ElInput_address: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {
          width: "100%"
        }
      },
      // group组件
      ElSelect_group: {
        value: [],
        multiple: true,
        "collapse-tags": true,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_group_change_out
        }
      },
      // group组件
      ElOption_group: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // interval组件
      ElInput_interval: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {
          width: "100%"
        },
        size: "small",
        controls: false,
        min: 0,
        event: {
          change: this.ElInput_interval_change_out,
          input: this.ElInput_interval_input_out
        }
      },
      // add组件
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("添加巡检对象"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      CetTable_inspectObj: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [{ name: "测试" }],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {
          record_out: this.CetTable_inspectObj_record_out,
          outputData_out: this.CetTable_inspectObj_outputData_out
        }
      },
      Columns_inspectObj: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "60" //绝对宽度
        },
        {
          prop: "tree_id", // 支持path a[0].b
          label: "ID", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formatter
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formatter
        },
        {
          prop: "parentname", // 支持path a[0].b
          label: $T("父节点路径"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "200", //该宽度会自适应
          formatter: this.formatter
        }
      ],
      ElTableColumn_operate: {
        prop: "", // 支持path a[0].b
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      // 巡检对象弹窗
      inspectObjDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        tableData: null,
        event: {
          saveData_out: this.insepectObjDialog_saveData_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.ElOption_group.options_in = this.groupList;
      if (this.isAdd) {
        this.CetDialog_pagedialog.title = $T("新增签到点");
        this.init();
      } else {
        this.CetDialog_pagedialog.title = $T("编辑签到点");
        this.CetForm_pagedialog.data = this._.cloneDeep(this.inputData_in);
        this.CetTable_inspectObj.data = this.CetForm_pagedialog.data.inspectObj;
        this.inspectObjDialog.tableData = this.CetTable_inspectObj.data;
        this.imageUrl = this.getImgUrl(this.CetForm_pagedialog.data.image);
      }
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {
      this.CetForm_pagedialog.queryId_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      this.CetForm_pagedialog.inputData_in = this._.cloneDeep(val);
    }
  },
  methods: {
    // 新增初始化
    init() {
      this.CetForm_pagedialog.data = {};
      this.CetForm_pagedialog.resetTrigger_in = new Date().getTime();
      this.CetForm_pagedialog.data.signInGroupIds = [this.activeGroup.id];
      this.CetTable_inspectObj.data = [];
      this.inspectObjDialog.tableData = this.CetTable_inspectObj.data;
      this.fileList = [];
      this.imageUrl = "";
    },
    CetForm_pagedialog_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_saveData_out(val) {
      this.$emit("saveData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishData_out(val) {
      this.$emit("finishData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishTrigger_out(val) {
      this.$emit("finishTrigger_out", val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetForm_pagedialog.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      /* if (this.CetTable_inspectObj.data.length < 1) {
        return this.$message.warning("请添加巡检对象");
      } */
      let groupList = this.groupList || [];
      let isOk = true;
      let name = this.CetForm_pagedialog.data.name;
      groupList.forEach(item => {
        let list = item.registrationpoint_model || [];
        list.forEach(item1 => {
          if (item1.name === name) {
            if (this.isAdd) {
              isOk = false;
            } else if (item1.id !== this.inputData_in.id) {
              isOk = false;
            }
          }
        });
      });
      if (!isOk) {
        this.$message.warning($T("该项目下已存在同名签到点！"));
        return;
      }
      // 巡检对象存储时只需要objectid和objectlabel
      const arr = [];
      if (this.CetTable_inspectObj.data.length) {
        this.CetTable_inspectObj.data.forEach(item => {
          const obj = {
            objectid: item.objectid,
            objectlabel: item.objectlabel
          };
          arr.push(obj);
        });
      }
      this.CetForm_pagedialog.data = {
        ...this.CetForm_pagedialog.data,
        children: arr
      };
      const formData = {
        image: this.CetForm_pagedialog.data.image,
        interval: this.CetForm_pagedialog.data.interval,
        name: this.CetForm_pagedialog.data.name,
        nfc: this.CetForm_pagedialog.data.nfc,
        address: this.CetForm_pagedialog.data.address,
        signInGroupIds: this.CetForm_pagedialog.data.signInGroupIds,
        children: arr
      };
      this.$refs.dialogForm.$refs.cetForm.validate(valid => {
        if (valid) {
          if (this.isAdd) {
            this.saveSigninObj("POST", formData);
          } else {
            formData.id = this.CetForm_pagedialog.data.id;
            this.saveSigninObj("PATCH", formData);
          }
        }
      });
      // this.CetForm_pagedialog.saveTrigger_in = this._.cloneDeep(val);
    },
    // 保存签到点
    saveSigninObj(method, data) {
      customApi.signinManage(method, data).then(res => {
        if (res.code === 0) {
          this.$message.success($T("保存成功"));
          this.$emit("saveData_out");
          this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
        }
      });
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    no() {},
    // name输出,方法名要带_out后缀
    ElInput_name_change_out(val) {},
    ElInput_name_input_out(val) {},
    // group输出,方法名要带_out后缀
    ElSelect_group_change_out(val) {},
    // interval输出,方法名要带_out后缀
    ElInput_interval_change_out(val) {},
    ElInput_interval_input_out(val) {},
    // add输出
    CetButton_add_statusTrigger_out(val) {
      this.inspectObjDialog.openTrigger_in = new Date().getTime();
    },
    CetTable_inspectObj_record_out(val) {},
    CetTable_inspectObj_outputData_out(val) {},
    formatter(row, column, cellValue) {
      return cellValue || "--";
    },
    handleDelete(row) {
      this.$confirm($T("确定要删除所选项吗？"), $T("删除确认"), {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消")
      }).then(() => {
        const index = this._.findIndex(this.CetTable_inspectObj.data, row);
        this.CetTable_inspectObj.data.splice(index, 1);
        this.inspectObjDialog.tableData = this.CetTable_inspectObj.data;
      });
    },
    beforeImgUpload(file) {
      const types = [
        "image/png",
        "image/jpg",
        "image/gif",
        "image/bmp",
        "image/jpeg"
      ];
      if (!types.includes(file.type)) {
        this.$message.warning(
          $T("请上传格式为.png, .jpg, .gif, .bmp, .jpeg的图片")
        );
      }
    },
    handleChange(file, fileList) {
      this.fileList = [fileList[fileList.length - 1]];
    },
    handleUploadSuccess(res, file) {
      if (res.code === 0) {
        this.$message.success($T("上传成功"));
        this.getImgUrl(res.data);
        this.CetForm_pagedialog.data.image = res.data;
      } else if (res.code !== 0) {
        this.$message.error(res.msg);
      }
    },
    errorUpload() {
      this.$message.error($T("图片上传失败"));
    },
    // 获取图片
    getImgUrl(path) {
      var me = this;
      if (!path) {
        return;
      }
      var url = "/eem-service/v1/common/downloadFile?path=" + path;
      const xhr = new XMLHttpRequest();
      xhr.open("GET", url, true);
      xhr.responseType = "blob";
      xhr.setRequestHeader("Authorization", this.token);
      xhr.onload = () => {
        if (
          xhr.status === 200 &&
          xhr.response.type === "application/x-download"
        ) {
          //将图片信息放到Img中
          me.imageUrl = window.URL.createObjectURL(xhr.response);
        }
      };

      xhr.send();
    },
    // 巡检对象保存
    insepectObjDialog_saveData_out(val) {
      this.CetTable_inspectObj.data = val;
      this.inspectObjDialog.tableData = this.CetTable_inspectObj.data;
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.avatar-uploader :deep(.el-upload) {
  border: 1px dashed;
  @include border_color(B1);
  @include border_radius(C);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  @include border_color(ZS);
}
.avatar-uploader-icon {
  font-size: 28px;
  width: 150px;
  height: 150px;
  line-height: 150px;
  text-align: center;
}
.avatar {
  width: 150px;
  display: block;
}
.custom-select-tag :deep(.el-select__tags-text) {
  float: left;
  max-width: 90px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.deleteHandle {
  @include font_color(Sta3);
  cursor: pointer;
}
</style>
