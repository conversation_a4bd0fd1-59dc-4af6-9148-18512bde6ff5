<template>
  <div class="page eem-common">
    <div class="fullheight flex-column">
      <div class="header mbJ3 eem-cont">
        <div class="goBack fl" @click="goBack">
          <i class="el-icon-arrow-left"></i>
          返回
        </div>
        <div class="common-title-H3 fl mlJ1">
          {{ params.eventClassifiedName }}
        </div>
        <!-- <div class="line"></div> -->
      </div>
      <div class="flex-auto flex-row">
        <div
          class="fullheight flex-column eem-aside"
          style="width: 315px; box-sizing: border-box"
        >
          <div class="mtJ1 mbJ1 flex-row">
            <ElInput
              class="flex-auto"
              suffix-icon="el-icon-search"
              v-model="ElInput_1.value"
              v-bind="ElInput_1"
              v-on="ElInput_1.event"
            ></ElInput>
            <CetButton
              class="mlJ2"
              v-bind="CetButton_1"
              v-on="CetButton_1.event"
            ></CetButton>
          </div>
          <div class="flex-auto mbJ1" style="overflow: auto">
            <div
              v-for="(item, index) in listData"
              :key="index"
              class="flex-row clearfix listItem"
              :class="{ action: itemAction == index }"
              @click="listItemClick(item, index)"
            >
              <div
                class="flex-auto text-ellipsis"
                :title="`${item.name}(${item.eventPlanCount})`"
              >
                {{ item.name }}
              </div>
              <div class="mlJ1 mrJ1">
                ({{ item.eventPlanCount }})
                <span v-show="item.eventPlanCount == 0" style="color: red">
                  !
                </span>
              </div>
              <div class="">
                <span
                  class="el-icon-delete"
                  v-if="!item.eventPlanCount"
                  @click.stop="listHandleDelete(item, index)"
                ></span>
                <span
                  class="el-icon-info"
                  v-if="item.eventPlanCount"
                  @click.stop="handleTips()"
                ></span>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-auto fullheight flex-column mlJ3 eem-cont">
          <div class="mbJ3">
            <CetButton
              class="fr"
              v-bind="CetButton_2"
              v-on="CetButton_2.event"
            ></CetButton>
          </div>
          <CetTable
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
            ref="table"
            class="flex-auto"
          >
            <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_adoptRate"></ElTableColumn>
            <ElTableColumn
              label="操作"
              width="140"
              headerAlign="left"
              align="left"
              fixed="right"
            >
              <template slot-scope="scope">
                <span
                  class="handel fl mrJ3"
                  @click.stop="handleDetail(scope.$index, scope.row)"
                >
                  详情
                </span>
                <span
                  class="handel fl mrJ3"
                  @click.stop="handleEdit(scope.$index, scope.row)"
                >
                  编辑
                </span>
                <span
                  class="delete fl"
                  @click.stop="handleDelete(scope.$index, scope.row)"
                >
                  删除
                </span>
              </template>
            </ElTableColumn>
          </CetTable>
        </div>
      </div>
    </div>
    <addExpertKnowledge
      :visibleTrigger_in="addExpertKnowledge.visibleTrigger_in"
      :closeTrigger_in="addExpertKnowledge.closeTrigger_in"
      :inputData_in="addExpertKnowledge.inputData_in"
      :eventType_in="addExpertKnowledge.eventType_in"
      :scenariosList_in="addExpertKnowledge.scenariosList_in"
      @confirm_out="addExpertKnowledge_confirm_out"
    />
    <addReservePlan
      :visibleTrigger_in="addReservePlan.visibleTrigger_in"
      :closeTrigger_in="addReservePlan.closeTrigger_in"
      :inputData_in="addReservePlan.inputData_in"
      :scenarios_in="addReservePlan.scenarios_in"
      @confirm_out="addReservePlan_confirm_out"
    />
    <reservePlanDetail
      :visibleTrigger_in="reservePlanDetail.visibleTrigger_in"
      :closeTrigger_in="reservePlanDetail.closeTrigger_in"
      :inputData_in="reservePlanDetail.inputData_in"
    />
  </div>
</template>

<script>
import addExpertKnowledge from "../accidentHandle/addExpertKnowledge";
import addReservePlan from "./addReservePlan";
import reservePlanDetail from "./reservePlanDetail";
import custom from "@/api/custom";
export default {
  name: "classDetail",
  components: {
    addExpertKnowledge,
    addReservePlan,
    reservePlanDetail
  },
  props: {
    params: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  data() {
    return {
      listData: [
        // {
        //   name: "场景名称2",
        //   eventPlanCount: 0,
        //   id: 4
        // }
      ],
      copyListData: [],
      itemAction: 0,
      ElInput_1: {
        value: "",
        placeholder: "请输入关键字以检索",
        size: "small",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_1_change_out,
          input: this.ElInput_1_input_out
        }
      },
      CetButton_1: {
        visible_in: false, //应张壮提的需求，对所以产品线都屏蔽按钮
        disable_in: false,
        title: "新增场景",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: "新增预案",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },

      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryEventPlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [
          // {
          //   id: 1,
          //   name: "PT断线",
          //   solution: "第一步。。。↵第二步。。。↵第三部。。。",
          //   adoptnumber: 10,
          //   adoptRate: 0.25
          // }
        ],
        dynamicInput: {
          scenariosId_in: 0,
          limit_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 50
        },
        highlightCurrentRow: false,
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        },
        height: 50,
        style: {
          "text-align": "center"
        },
        showSelection: false
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        label: "#", //列名
        headerAlign: "left",
        align: "left",
        width: "65" //绝对宽度
      },
      ElTableColumn_name: {
        //type: "",      // selection 勾选 index 序号
        prop: "name", // 支持path a[0].b
        label: "预案名称", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "200", //该宽度会自适应
        // width: "100", //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (row, column, cellValue, index) {
          if (cellValue) {
            return cellValue;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_adoptRate: {
        //type: "",      // selection 勾选 index 序号
        prop: "adoptRate", // 支持path a[0].b
        label: "采纳率", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "100", //该宽度会自适应
        // width: "100", //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (row, column, cellValue, index) {
          if (typeof cellValue === "number") {
            return (cellValue * 100).toFixed2(2) + "%";
          } else {
            return "0.00%";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      addExpertKnowledge: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        eventType_in: null,
        scenariosList_in: null
      },
      addReservePlan: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        scenarios_in: 0
      },
      reservePlanDetail: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      }
    };
  },
  watch: {
    params: {
      handler: function (val, oldVal) {},
      deep: true,
      immediate: true
    }
  },

  methods: {
    addReservePlan_confirm_out() {
      this.CetTable_1.queryTrigger_in = new Date().getTime();
      this.getListData(true);
    },
    addExpertKnowledge_confirm_out(val) {
      this.CetTable_1.queryTrigger_in = new Date().getTime();
      this.getListData(true);
    },
    ElInput_1_change_out(val) {
      if (val) {
        var listData = this.copyListData.filter(item => {
          return item.name.indexOf(val) != -1;
        });
        if (listData.length > 0) {
          this.listData = this._.cloneDeep(listData);
          this.listItemClick(this.listData[0], 0);
        } else {
          this.listData = [];
        }
      } else {
        this.listData = this._.cloneDeep(this.copyListData);
        this.listItemClick(this.listData[0], 0);
      }
    },
    ElInput_1_input_out(val) {},
    CetButton_1_statusTrigger_out(val) {
      this.addExpertKnowledge.eventType_in = this._.cloneDeep(this.params);
      this.addExpertKnowledge.scenariosList_in = this._.cloneDeep(
        this.copyListData
      );
      this.addExpertKnowledge.visibleTrigger_in = new Date().getTime();
    },
    CetButton_2_statusTrigger_out(val) {
      this.addReservePlan.inputData_in = {};
      this.addReservePlan.visibleTrigger_in = new Date().getTime();
    },
    CetTable_1_record_out(val) {},
    CetTable_1_outputData_out(val) {},
    handleDetail(index, row) {
      this.reservePlanDetail.inputData_in = this._.cloneDeep({
        index: index + 1,
        ...row
      });
      this.reservePlanDetail.visibleTrigger_in = new Date().getTime();
    },
    handleEdit(index, row) {
      this.addReservePlan.inputData_in = {
        id: row.id,
        name: row.name,
        solution: row.solution,
        adoptnumber: row.adoptnumber
      };
      this.addReservePlan.visibleTrigger_in = new Date().getTime();
    },
    handleDelete(index, row) {
      if (row.adoptnumber) {
        this.$alert("此预案已被采纳，暂时不能进行删除操作。", "提示", {
          confirmButtonText: "我知道了",
          callback: action => {}
        });
      } else {
        this.$confirm("此操作将永久删除此预案，是否继续？", "删除", {
          confirmButtonText: "确定删除",
          cancelButtonText: "我再想想",
          cancelButtonClass: "btn-custom-cancel el-button--primary",
          confirmButtonClass: "project-btn-custom-confirm",
          type: "warning",
          closeOnClickModal: false,
          showClose: true,
          beforeClose: (action, instance, done) => {
            if (action == "confirm") {
              var deleteParams = {
                modelLabel: row.modelLabel,
                ids: [row.id]
              };
              custom.deleteKnowledge(deleteParams).then(res => {
                if (res.code == 0) {
                  this.$message({
                    message: "删除成功！",
                    type: "success"
                  });
                  this.CetTable_1.queryTrigger_in = new Date().getTime();
                  this.getListData(true);
                }
              });
            }
            instance.confirmButtonLoading = false;
            done();
          },
          callback: action => {
            if (action != "confirm") {
              this.$message({
                type: "info",
                message: "取消删除！"
              });
            }
          }
        });
      }
    },
    handleTips() {
      this.$alert(
        "此场景下依然有预案，暂时不能进行删除操作，请将预案清空后重试。",
        "提示",
        {
          confirmButtonText: "我知道了",
          callback: action => {}
        }
      );
    },
    listHandleDelete(item, index) {
      this.$confirm("此操作将永久删除场景及其下属预案，是否继续？", "删除", {
        confirmButtonText: "确定删除",
        cancelButtonText: "我再想想",
        cancelButtonClass: "btn-custom-cancel el-button--primary",
        confirmButtonClass: "project-btn-custom-confirm",
        type: "warning",
        closeOnClickModal: false,
        showClose: true,
        beforeClose: (action, instance, done) => {
          if (action == "confirm") {
            var deleteParams = {
              modelLabel: item.modelLabel,
              ids: [item.id]
            };
            custom.deleteKnowledge(deleteParams).then(res => {
              if (res.code == 0) {
                this.$message({
                  message: "删除成功！",
                  type: "success"
                });
                this.getListData();
              }
            });
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action != "confirm") {
            this.$message({
              type: "info",
              message: "取消删除！"
            });
          }
        }
      });
    },
    goBack() {
      // this.$router.go(-1);
      this.$emit("goBack", {});
    },
    // 列表点击
    listItemClick(item, index) {
      if (!this._.isEmpty(item)) {
        this.itemAction = index;
        this.CetTable_1.dynamicInput.scenariosId_in = item.id;
        this.CetTable_1.queryTrigger_in = new Date().getTime();
        // 如果是搜索进入需要去掉其他预案
        if (this.params.eventPlanId_in) {
          this.CetTable_1.dataConfig.filters = [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" },
            { name: "id_in", operator: "EQ", prop: "id" }
          ];
          this.CetTable_1.dynamicInput.id_in = this.params.eventPlanId_in;
        } else {
          this.CetTable_1.dataConfig.filters = [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ];
        }
        // 往新增、编辑弹框传入场景id
        this.addReservePlan.scenarios_in = item.id;
        this.CetButton_1.disable_in = false;
        this.CetButton_2.disable_in = false;
      } else {
        this.CetButton_1.disable_in = true;
        this.CetButton_2.disable_in = true;
      }
    },
    getListData(val) {
      // 传入true则是更新列表
      if (this.params.eventClassified) {
        this.CetButton_1.disable_in = false;
        this.CetButton_2.disable_in = false;
        custom
          .getEventPlanCount([this.params.eventClassified])
          .then(response => {
            if (
              response.code === 0 &&
              response.data &&
              response.data.length > 0
            ) {
              // 如果是搜索进入需要去掉其他场景
              if (this.params.faultScenariosId_in) {
                var listData = response.data.filter(
                  item => item.id == this.params.faultScenariosId_in
                );
                this.listData = listData;
              } else {
                this.listData = response.data;
              }
              this.copyListData = this._.cloneDeep(this.listData);
              if (!val) {
                this.listItemClick(this.listData[0], 0);
              }
            } else {
              this.listData = [];
              this.copyListData = [];
              this.CetTable_1.data = [];
            }
          });
      } else {
        this.CetButton_1.disable_in = true;
        this.CetButton_2.disable_in = true;
      }
    }
  },
  /*
    params = {
      eventClassified: 事件归类id
      eventClassifiedName： 事件归类名称
      faultScenariosId_in： 场景id，用于过滤出某个场景（用于外部搜索进入）
      eventPlanId_in： 预案id，用于过滤出某个预案（用于外部搜索进入）
    }
  */
  created: function () {},
  mounted: function () {
    this.getListData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  padding: 0 10px 0 20px;
  box-sizing: border-box;
}
.header {
  .goBack {
    display: inline-block;
    @include font_color(ZS);
    cursor: pointer;
  }
  .line {
    height: 1px;
    width: 100%;
    @include background_color(ZS);
  }
}
.handel {
  @include font_color(ZS);
  cursor: pointer;
}
.delete {
  @include font_color(Sta3);
  cursor: pointer;
}

.listItem {
  @include margin_top(J1);
  @include padding(0 J1);
  @include line_height(Hm);
  @include border_radius(C);
  box-sizing: border-box;
  cursor: pointer;
  &.action {
    @include background_color(BG4);
    // @include font_color(T5);
  }
}
</style>
