<template>
  <div class="page">
    <el-tabs v-model="currentTab" class="tab">
      <el-tab-pane
        v-for="item in tabList"
        :key="item.name"
        :name="item.name"
        :label="item.label"
      ></el-tab-pane>
    </el-tabs>
    <el-main class="main">
      <singleMachine v-if="currentTab === 'singleMachine'" />
      <multipleMachine v-if="currentTab === 'multipleMachine'" />
      <processingGasVolume v-if="currentTab === 'processingGasVolume'" />
    </el-main>
  </div>
</template>

<script>
import singleMachine from "./tabs/singleMachine/index.vue";
import multipleMachine from "./tabs/multipleMachine/index.vue";
import processingGasVolume from "./tabs/processingGasVolume/index.vue";

export default {
  name: "runOptimization",
  components: { singleMachine, multipleMachine, processingGasVolume },
  data() {
    return {
      currentTab: "singleMachine",
      tabList: [
        { name: "singleMachine", label: $T("压缩机单机运行调优") },
        { name: "multipleMachine", label: $T("压缩机多机运行调优") },
        { name: "processingGasVolume", label: $T("压缩机处理气量运行调优") }
      ]
    };
  },
  watch: {},
  methods: {}
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.tab {
  height: 56px;
  line-height: 56px;
  @include border_radius(C);
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  @include padding(0 J4);
  @include background_color(BG1);
  border-bottom: 1px solid;
  @include border_color(B2);
}
.main {
  height: calc(100% - 56px);
  padding: 0;
  @include background_color(BG1);
}
</style>
