<template>
  <el-container class="moveAndCopyDialog">
    <CetDialog
      class="CetDialog small eem-common"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
    >
      <el-main class="eem-cont-c1 h350 eem-group-list">
        <div
          v-for="folder in tabs"
          :key="folder.id"
          :class="current == folder.id ? 'group-item active' : 'group-item'"
          @click="folderChange(folder.id, folder.label)"
        >
          <i class="el-icon-notebook-1"></i>
          {{ folder.label }}
        </div>
      </el-main>
      <div slot="footer">
        <!-- cancel按钮组件 -->
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <!-- confirm按钮组件 -->
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </div>
    </CetDialog>
  </el-container>
</template>
<script>
import commonApi from "@/api/custom";
export default {
  name: "moveAndCopyDialog",
  components: {},
  computed: {},
  props: {
    openTrigger_in: {
      type: Number
    },
    order: {
      type: String
    },
    selectList: {
      type: Array
    },
    tabs: {
      type: Array
    }
  },
  data() {
    return {
      current: 0,
      label: "",
      // 设置组件唯一识别字段弹窗组件
      // 1弹窗组件
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        width: "380px",
        showClose: true,
        event: {}
      },

      // 确定按钮
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      // 取消按钮
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },

  watch: {
    openTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
    },
    order(val) {
      this.CetDialog_1.title = val;
    }
  },
  methods: {
    // 切换文件加
    folderChange(id, label) {
      this.current = id;
      this.label = label;
    },
    // dialog点击保存按钮
    CetButton_confirm_statusTrigger_out() {
      const newList = [];
      const selectList = this.selectList;
      selectList.forEach(item => {
        newList.push({
          filetype$text: item.filetype$text,
          filetype: item.filetype,
          level: item.level,
          fileformat$text: item.fileformat$text,
          modelLabel: item.modelLabel,
          fathernodename: item.fathernodename,
          size: item.size,
          fathernode_id: item.fathernode_id,
          name: item.name,
          storagepath: item.storagepath,
          id: item.id,
          fileformat: item.fileformat,
          fullname: item.fullname,
          logtime: item.logtime,
          newfathernodename: this.label,
          newfathernodeid: this.current
        });
      });
      if (this.current == 0) {
        this.$message({ message: $T("请选择目录") });
        return;
      }
      if (this.order === $T("移动到")) {
        commonApi.fileMove(newList).then(res => {
          if (res.code == 0) {
            this.$message({ type: "success", message: $T("移动成功") });
            this.$emit("refresh", 2);
            this.current = 0;
            this.CetDialog_1.closeTrigger_in = new Date().getTime();
          }
        });
      } else {
        commonApi.fileCopy(newList).then(res => {
          if (res.code == 0) {
            this.$message({ type: "success", message: $T("复制成功") });
            this.current = 0;
            this.CetDialog_1.closeTrigger_in = new Date().getTime();
          }
        });
      }
    },
    // dialog点击取消按钮
    CetButton_cancel_statusTrigger_out() {
      this.current = 0;
      this.CetDialog_1.closeTrigger_in = new Date().getTime();
    },
    // name输出,方法名要带_out后缀
    ElInput_name_change_out(val) {},
    ElInput_name_input_out(val) {}
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.h350 {
  height: 350px;
}
</style>
