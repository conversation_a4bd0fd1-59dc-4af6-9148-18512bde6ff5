<template>
  <div class="page eem-common">
    <el-container style="height: 100%">
      <el-aside width="315px" class="eem-aside brC3">
        <CetTree
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
        ></CetTree>
      </el-aside>
      <el-container class="padding0 marginLeftJ2 fullheight">
        <el-header
          height="36px"
          style="position: relative; padding: 0"
          class="mbJ3"
        >
          <el-tooltip :content="currentNode && currentNode.name" effect="light">
            <span class="common-title-H1 title text-ellipsis">
              {{ currentNode && currentNode.name }}
            </span>
          </el-tooltip>
          <div class="menu_group" style="padding: 0px">
            <el-radio-group v-model="selectedMenu" size="small">
              <el-radio-button
                v-for="menu in menus"
                :label="menu.id"
                :key="menu.id"
              >
                {{ menu.text }}
              </el-radio-button>
            </el-radio-group>
          </div>
        </el-header>
        <keep-alive>
          <component :is="selectedMenu" :currentNode="currentNode"></component>
        </keep-alive>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import DeclareProposal from "./DeclareProposal";
import DemandData from "./demanddata/DemandData";
import { httping } from "@omega/http";

export default {
  name: "declareproposal",
  components: {
    DeclareProposal,
    DemandData
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    }
  },
  data() {
    return {
      currentNode: null,
      menus: [
        {
          id: "DeclareProposal",
          text: $T("申报建议")
        },
        {
          id: "DemandData",
          text: $T("数据维护")
        }
      ],
      selectedMenu: "",
      copyTableData: [],
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out,
          parentList_out: this.CetTree_1_parentList_out,
          checkedNodes_out: this.CetTree_1_checkedNodes_out,
          halfCheckNodes_out: this.CetTree_1_halfCheckNodes_out,
          allCheckNodes_out: this.CetTree_1_allCheckNodes_out
        }
      }
    };
  },
  methods: {
    // 获取节点树
    getTree() {
      this.CetTree_1.inputData_in = [];
      this.copyTreeData = [];
      httping({
        url: "/eem-service/v1/demand/node/getDemandNodes",
        method: "POST",
        data: [this.projectId]
      }).then(response => {
        if (response.code == 0 && response.data) {
          if (this.$route.params.id) {
            // 需量检测进入选中传入的节点
            this.CetTree_1.selectNode = {
              id: this.$route.params.id,
              modelLabel: this.$route.params.modelLabel,
              tree_id:
                this.$route.params.modelLabel + "_" + this.$route.params.id
            };
          } else if (response.data[0].children) {
            this.CetTree_1.selectNode = {
              id: response.data[0].children[0].id,
              modelLabel: response.data[0].children[0].modelLabel,
              tree_id:
                response.data[0].children[0].modelLabel +
                "_" +
                response.data[0].children[0].id
            };
          }
          this.CetTree_1.inputData_in = this._.cloneDeep(response.data);
        }
      });
    },
    CetTree_1_currentNode_out(val) {
      this.currentNode = val;
    },
    CetTree_1_parentList_out(val) {},
    CetTree_1_checkedNodes_out(val) {},
    CetTree_1_halfCheckNodes_out(val) {},
    CetTree_1_allCheckNodes_out(val) {}
  },
  activated: function () {
    this.getTree();
    // path === "inletwiremanage" 进线管理跳转 id存在 需量监测跳转 展示需量数据维护
    if (
      this.$route.params.path === "inletwiremanage" ||
      this.$route.params.id
    ) {
      this.selectedMenu = "DemandData";
    } else {
      this.selectedMenu = "DeclareProposal";
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .title {
    max-width: 40%;
  }
  .menu_group {
    position: absolute;
    left: 50%;
    top: 0px;
    transform: translateX(-50%);
  }
}
</style>
