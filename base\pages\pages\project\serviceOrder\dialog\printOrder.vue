<template>
  <div class="page">
    <h1 style="text-align: center">{{$T("维保工单")}}</h1>
    <div style="display: flex">
      <div style="flex: 1.5; margin-bottom: 10px">
        <span class="title">{{$T("工单号")}}:</span>
        <span>{{ orderMsg.code }}</span>
      </div>
      <div style="flex: 1">
        <span>{{$T("预计耗时")}}:</span>
        <span>{{ secondsFormat(orderMsg.timeconsumeplan, "hh h mm min") }}</span>
      </div>
      <div style="flex: 1">
        <span>{{$T("等级")}}:</span>
        <span>{{ orderMsg.taskLevelName }}</span>
      </div>
    </div>
    <div style="display: flex; margin-bottom: 10px">
      <div style="flex: 1.5">
        <span>{{$T("维保对象")}}:</span>
        <span>{{ orderMsg.inspectObject }}</span>
      </div>
      <div style="flex: 1">
        <span>{{$T("工单来源")}}:</span>
        <span>{{ orderMsg.createType }}</span>
      </div>
      <div style="flex: 1">
        <span>{{$T("创建人")}}:</span>
        <span>{{ orderMsg.creatorname }}</span>
      </div>
    </div>
    <div style="margin-bottom: 30px">
      <span>{{$T("安全措施说明")}}:</span>
      <span>{{ orderMsg.safetymeasure }}</span>
    </div>
    <!-- 维保项 -->
    <h2>{{$T("维保项")}}:</h2>
    <table style="width: 100%; border: 1px solid #000; border-collapse: collapse; border-spacing: 0; table-layout：fixed">
      <tbody>
        <tr>
          <th
            :style="{ 'border-right': '1px solid #000', 'border-bottom': '1px solid #000', 'word-break': 'break-all', width: item.width }"
            v-for="item in thColumn"
            :key="item.prop"
          >
            {{ item.name }}
          </th>
        </tr>
        <tr v-for="item in tableData" :key="item.id">
          <td
            style="border-right: 1px solid #000; border-bottom: 1px solid #000; text-align: center; height: 30px; word-break: break-all"
            v-for="col in thColumn"
            :key="col.prop"
          >
            {{ item[col.prop] }}
          </td>
        </tr>
      </tbody>
    </table>
    <h2 style="margin-top: 30px">{{ $T("维保结果") }}:</h2>
    <table style="width: 100%; border: 1px solid #000; border-collapse: collapse; border-spacing: 0">
      <tbody>
        <tr style="display: flex; align-items: center" v-for="item in mainResult" :key="item.name">
          <td style="border-right: 1px solid #000; border-bottom: 1px solid #000; text-align: center; flex: 1; height: 30px">{{ item.name }}</td>
          <td style="text-align: center; border-right: 1px solid #000; border-bottom: 1px solid #000; flex: 2; height: 30px">
            {{ filData(orderMsg[item.prop], item.type) || "" }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  name: "printOrder",
  components: {},
  props: {
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      orderMsg: {},
      thColumn: [
        {
          prop: "index",
          name: "#",
          width: "5%"
        },
        {
          prop: "groupName",
          name: $T("分组"),
          width: "10%"
        },
        {
          prop: "maintenanceTypeName",
          name: $T("维保方式"),
          width: "10%"
        },
        {
          prop: "content",
          name: $T("维保内容"),
          width: "10%"
        },
        {
          prop: "sparePartName",
          name: $T("零部件名称"),
          width: "10%"
        },
        {
          prop: "number",
          name: $T("预计消耗数量"),
          width: "10%"
        },
        {
          prop: "number1",
          name: $T("实际消耗数量"),
          width: "10%"
        },
        {
          prop: "unit",
          name: $T("单位"),
          width: "10%"
        },
        {
          prop: "executorNames",
          name: $T("执行人"),
          width: "12.5%"
        },
        {
          prop: "maintenanceResult",
          name: $T("维保结果"),
          width: "12.5%"
        }
      ],
      tableData: [],
      mainResult: [
        {
          prop: "handledescription",
          name: $T("处理描述")
        },
        {
          prop: "",
          name: $T("执行人签字")
        },
        {
          prop: "executetime",
          name: $T("开始时间"),
          type: "date"
        },
        {
          prop: "finishtime",
          name: $T("结束时间"),
          type: "date"
        }
      ]
    };
  },
  watch: {
    inputData_in: {
      handler(val) {
        this.orderMsg = this._.cloneDeep(val);
        const maintenanceItem = this._.get(val, "maintenanceItemExtendVos", []);
        this.tableData = [];
        if (!maintenanceItem) return;
        maintenanceItem.forEach((item, i) => {
          item.index = i + 1;
          item.executorNames = item.executorName && item.executorName.length ? item.executorName.join("、") : "";
          this.tableData.push(item);
        });
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    //过滤耗时
    secondsFormat(sec, format) {
      let str = "--";
      if (sec || sec === 0) {
        const hour = Math.floor(sec / 3600000);
        const minute = Math.floor((sec - hour * 3600000) / 60000);
        if (format.indexOf("hh") !== -1 && format.indexOf("mm") !== -1 && format.indexOf("ss") === -1) {
          str = format.replace(/(.*)hh(.*)mm(.*)/, "$1" + hour + "$2" + minute + "$3");
        }
      }
      return str;
    },
    filData(val, type) {
      if ([null, undefined, NaN].includes(val)) {
        return "";
      } else if (type === "number") {
        return Number(val).toFixed(2);
      } else if (type === "date") {
        return this.$moment(val).format($T("YYYY 年 MM 月 DD 日  HH 时 mm 分"));
      } else {
        return val;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 20px;
}
</style>
