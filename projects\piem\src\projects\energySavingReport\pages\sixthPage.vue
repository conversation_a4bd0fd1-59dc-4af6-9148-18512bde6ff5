<template>
  <div class="page">
    <reportPage :pageNum="pagesNumber" :queryTime="queryTime">
      <div class="mbJ4">
        <div class="title mbJ2">{{ $T("3.污水回注系统节费分析") }}</div>
      </div>

      <div class="mbJ4">
        <div class="second-title mbJ2">{{ $T("3.1 节费概览") }}</div>
        <energyConsumptionOverview
          :reportData="reportData"
          :overviewList="costSavingOverviewList"
        />
      </div>

      <div class="mbJ4">
        <div class="second-title mbJ2">{{ $T("3.2 站场分析") }}</div>
        <stationAnalysis
          class="mbJ4"
          :stationAnalysisData="reportData.stationsCostVOList || []"
          :overviewList="stationAnalysisOverviewList"
        />
        <stationTable
          :tableData="reportData.stationsCostVOList || []"
          :columnList="stationColumnList"
        />
      </div>
    </reportPage>
  </div>
</template>

<script>
import reportPage from "../components/reportPage.vue";
import energyConsumptionOverview from "../components/energyConsumptionOverview.vue";
import stationAnalysis from "../components/stationAnalysis.vue";
import stationTable from "../components/stationTable.vue";
import common from "eem-utils/common";
export default {
  name: "sixthPage",
  components: {
    reportPage,
    energyConsumptionOverview, // 节费概览
    stationAnalysis, // 站场分析
    stationTable // 站场分析-表格
  },
  props: {
    reportData: {
      type: Object,
      default: () => {}
    },
    queryTime: {
      type: Number
    },
    pagesNumber: {
      type: String,
      default: () => {
        return "06";
      }
    }
  },
  data() {
    return {
      costSavingOverviewList: [
        {
          name: $T("月实际用电成本"),
          text: $T("月实际成本"),
          key: "recycleSysCost",
          unit: "万元"
        },
        {
          name: $T("优化后预计用电成本"),
          text: $T("优化后预计成本"),
          key: "recycleSysOptimizationCost",
          unit: "万元"
        },
        {
          name: $T("预计节费量"),
          text: $T("节费量"),
          key: "recycleSysCostSaving",
          unit: "万元"
        },
        {
          name: $T("预计节费率"),
          text: $T("节费率"),
          key: "costSavingRate",
          unit: "%"
        }
      ],
      stationAnalysisOverviewList: [
        {
          name: $T("月实际用电成本"),
          key: "cost",
          unit: "万元"
        },
        {
          name: $T("优化后预计用电成本"),
          key: "optimizationCost",
          unit: "万元"
        },
        {
          name: $T("节费"),
          key: "savingCost",
          unit: "万元"
        }
      ],
      stationColumnList: [
        {
          prop: "name",
          label: $T("分析对象"),
          align: "left",
          showOverflowTooltip: true
        },
        {
          prop: "cost",
          label: $T("月实际用电成本(万元)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 130
        },
        {
          prop: "optimizationCost",
          label: $T("优化后预计用电成本(万元)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 130
        },
        {
          prop: "savingCost",
          label: $T("预计节费量(万元)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 100
        },
        {
          prop: "savingCostRate",
          label: $T("预计节费率(%)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 110
        }
      ]
    };
  },
  watch: {},
  methods: {
    formatNumberWithPrecision(...args) {
      return common.formatNumberWithPrecision(...args);
    }
  }
};
</script>

<style lang="scss" scoped>
@media print {
  @page {
    margin: 0;
  }

  body {
    margin: 1.6cm;
    -webkit-print-color-adjust: exact !important;
    -moz-print-color-adjust: exact !important;
    -ms-print-color-adjust: exact !important;
  }
  button {
    display: none;
  }
}
.page {
  width: 100%;
  height: 100%;
  position: relative;
  background: #ffffff;
}
.title {
  color: #242424;
  font-weight: bold;
  line-height: 20px;
}
.second-title {
  color: #242424;
  line-height: 18px;
}
</style>
