<template>
  <div>
    <CetDialog v-bind="CetDialog_add" v-on="CetDialog_add.event" class="small">
      <CetForm
        class="eem-cont-c1"
        :data.sync="CetForm_add.data"
        v-bind="CetForm_add"
        v-on="CetForm_add.event"
      >
        <div class="mainTitle mbJ1">{{ $T("基础信息") }}</div>
        <el-row :gutter="$J3">
          <el-col :span="12">
            <div class="minTitle">{{ $T("预案名称") }}</div>
            <el-form-item prop="name">{{ CetForm_add.data.name }}</el-form-item>
          </el-col>
          <el-col :span="4">
            <div class="minTitle">{{ $T("排名") }}</div>
            <el-form-item prop="name">
              {{ CetForm_add.data.index }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <div class="minTitle">{{ $T("占比") }}</div>
            <el-form-item prop="name">
              {{
                typeof CetForm_add.data.adoptRate == "number"
                  ? (CetForm_add.data.adoptRate * 100).toFixed2(2)
                  : "0.00"
              }}%
            </el-form-item>
          </el-col>
        </el-row>
        <div class="mainTitle mbJ1">{{ $T("详细操作步骤") }}</div>
        <el-form-item
          prop="name"
          v-if="CetForm_add.data.solution"
          style="height: 200px; overflow-y: auto"
        >
          <div
            style="line-height: 1.5"
            v-for="(item, index) in CetForm_add.data.solution.split('\n')"
            :key="index"
          >
            {{ item }}
          </div>
        </el-form-item>
      </CetForm>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
export default {
  name: "reservePlanDetail",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  data() {
    return {
      // add弹窗组件
      CetDialog_add: {
        title: $T("预案详情"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_add_open_out,
          close_out: this.CetDialog_add_close_out
        },
        showClose: true
      },
      CetButton_confirm: {
        visible_in: false,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_add: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "0px",
        rules: {},
        event: {
          currentData_out: this.CetForm_add_currentData_out,
          saveData_out: this.CetForm_add_saveData_out,
          finishData_out: this.CetForm_add_finishData_out,
          finishTrigger_out: this.CetForm_add_finishTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    },
    inputData_in(val) {
      this.CetForm_add.data = val;
    }
  },

  methods: {
    CetDialog_add_open_out(val) {},
    CetDialog_add_close_out(val) {},
    CetForm_add_currentData_out(val) {},
    CetForm_add_saveData_out(val) {},
    CetForm_add_finishData_out(val) {},
    CetForm_add_finishTrigger_out(val) {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {}
  },

  created: function () {}
};
</script>
<style lang="scss" scoped></style>
