<template>
  <div style="height: 200px">
    <div class="card-head-title">
      <span>配电统计</span>
    </div>
    <div style="height: 30px; line-height: 30px">
      <span class="ml-10">配电室（个）：</span>
      <span class="mr-10" style="float: right; font-size: 20px">
        {{ roomNum }}
      </span>
    </div>
    <div style="height: calc(100% - 60px)">
      <CetChart
        :inputData_in="CetChart_1.inputData_in"
        v-bind="CetChart_1.config"
      />
    </div>
  </div>
</template>
<script>
import { httping } from "@omega/http";
export default {
  name: "ProjectTab",
  props: {
    queryTrigger_in: {
      type: Number
    }
  },
  components: {},

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data() {
    return {
      CetChart_1: {
        inputData_in: null,
        config: {
          options: {
            // backgroundColor: "gray",
            tooltip: {
              trigger: "item",
              formatter: "{a} <br/>{b} : {c} ({d}%)"
            },
            legend: {
              type: "scroll",
              orient: "vertical",
              right: 10,
              top: 10,
              data: [],
              textStyle: {
                fontFamily: "微软雅黑",
                fontSize: 14,
                color: "#fff"
              }
            },
            // visualMap: {
            //   show: false,
            //   min: 0,
            //   max: 600,
            //   inRange: {
            //     colorLightness: [0, 1]
            //   }
            // },
            color: ["#61A5E8", "#7ECF51", "#EECB5F", "#E3935D", "#E16757"],
            series: [
              {
                name: "配电统计",
                type: "pie",
                radius: "55%",
                center: ["40%", "50%"],
                data: [],
                roseType: "radius",
                label: {
                  normal: {
                    show: false,
                    position: "center"
                  },
                  emphasis: {
                    show: false,
                    textStyle: {
                      fontSize: "30",
                      fontWeight: "bold"
                    }
                  }
                }
              }
            ]
          }
        }
      },
      roomNum: 3646,
      deviceList: [
        {
          id: 1,
          modelLabel: "powertransformer",
          text: "变压器"
        },
        {
          id: 2,
          modelLabel: "capacitor",
          text: "电容柜"
        },
        {
          id: 3,
          modelLabel: "linesegmentwithswitch",
          text: "开关柜或一段线"
        },
        {
          id: 4,
          modelLabel: "busbarsection",
          text: "母线"
        },
        {
          id: 5,
          modelLabel: "generator",
          text: "发动机"
        },
        {
          id: 6,
          modelLabel: "busbarconnector",
          text: "母线"
        },
        {
          id: 7,
          modelLabel: "hdvc",
          text: "高压直流设备"
        },
        {
          id: 8,
          modelLabel: "battery",
          text: "蓄电池"
        }
      ]
    };
  },
  watch: {
    //查询条件触发
    queryTrigger_in() {
      this.getProjectNumData();
    }
  },

  methods: {
    //统计项目，配电室以及配电室设备数量
    getProjectNumData() {
      var _this = this,
        auth = _this.token; //身份验证

      var queryOption = {
        url: `/eem-service/v1/project/nodeNumber`,
        method: "GET"
      };

      httping(queryOption).then(function (response) {
        if (response.code === 0) {
          //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
          // console.log(response.data);
          var data = _this._.get(response, ["data"], {});
          // var data = {
          //   project: 2,
          //   deviceNumber: [
          //     {
          //       number: 30,
          //       label: "powertransformer"
          //     },
          //     {
          //       number: 73,
          //       label: "linesegmentwithswitch"
          //     }
          //   ],
          //   room: 25
          // };
          _this.filProjectNumData(data);
          _this.$emit("exportProjectNum", data);
        } else {
          _this.$emit("exportProjectNum", data);
        }
      });
    },
    filProjectNumData(data) {
      let deviceNumber = data.deviceNumber || [];
      let roomNum = this.filNum(data["room"]); //项目数量
      this.roomNum = roomNum;
      var chartData = [],
        legend = [];
      deviceNumber.forEach((item, i) => {
        var label = item.label,
          label$text = "",
          number = item.number || 0;

        this.deviceList.forEach(item1 => {
          if (label == item1.modelLabel) {
            label$text = item1.text;
          }
        });
        legend.push(label$text);
        var obj = {
          value: number,
          name: label$text
        };
        chartData.push(obj);
      });

      this.CetChart_1.config.options.legend.data = legend;
      this.CetChart_1.config.options.series[0].data = chartData;
    },
    filNum(data) {
      if ([NaN, undefined, null].includes(data)) {
        return "--";
      } else {
        return data;
      }
    }
  },

  created: function () {},
  mounted: function () {
    // this.getProjectNumData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.card-header {
  height: 34px;
  padding: 8px 0 0 10px;
  background: #fff;
  clear: both;
  text-align: center;
}

.card-title {
  display: inline-block;
  height: 34px;
  font-weight: bold;
  font-size: 13px;
  // color: #7a7a7a;
}
.card-head-title {
  height: 30px;
  line-height: 30px;
  text-align: center;
  span {
    font-size: 18px;
    color: rgba(128, 255, 255, 1);
  }
}
.card-body {
  height: 100%;
  overflow-y: auto;
}
</style>
