<template>
  <div class="page eem-common">
    <el-container class="padding0 fullheight">
      <el-aside width="315px" class="eem-aside">
        <leftTree
          selectNodeModel="device"
          @handleEnergyChange="handleEnergyChange"
          @handleNodeClick="handleNodeClick"
        ></leftTree>
      </el-aside>
      <el-main class="mlJ3 eem-container flex-column eem-min-width">
        <div class="mbJ3">
          <time-tool
            class="fr"
            :val.sync="startTime"
            :typeID="queryTime.cycle"
            :isNextDisabled="false"
            @change="changeQueryTime"
          ></time-tool>
        </div>
        <div class="treeTable flex-auto">
          <el-table
            height="true"
            style="height: 100%"
            :data="tableData"
            row-key="uniqueId"
            border
            :cell-class-name="tableCellClassName"
            tooltip-effect="light"
          >
            <el-table-column
              v-for="item in Columns_EnergyConsumpte"
              :key="item.label"
              v-bind="item"
            ></el-table-column>
          </el-table>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import customApi from "@/api/custom";
import common from "eem-utils/common";
import LeftTree from "./LeftTree.vue";
import TimeTool from "eem-components/TimeTool.vue";

export default {
  name: "transformerLoss",
  components: {
    LeftTree,
    TimeTool
  },
  computed: {
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    }
  },
  data() {
    const vm = this;
    const language = window.localStorage.getItem("omega_language") === "en";
    return {
      energyType: "",
      currentNode: null, // 当前点击的节点
      unit: "", // 能源基本单位
      startTime: new Date().getTime(),
      queryTime: {
        startTime: common.initDateRange("M")[0],
        endTime: common.initDateRange("M")[0],
        cycle: 14
      },
      tableData: [],
      Columns_EnergyConsumpte: [
        {
          prop: "circuitName",
          label: $T("设备名称"),
          headerAlign: "left",
          align: "left",
          minWidth: "240px",
          showOverflowTooltip: true
        },
        {
          prop: "roomName",
          label: $T("所属房间"),
          headerAlign: "left",
          align: "left",
          minWidth: "240px",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "current",
          label: $T("当前支路能耗") + "(kWh)",
          headerAlign: "left",
          align: "left",
          minWidth: language ? "350" : "150",
          showOverflowTooltip: true,
          formatter: common.formatNumberColumn
        },
        {
          prop: "output",
          label: $T("下级支路能耗") + "(kWh)",
          headerAlign: "left",
          align: "left",
          custom: "button",
          minWidth: language ? "350" : "150",
          showOverflowTooltip: true,
          onButtonClick: vm.openArea,
          formatter: common.formatNumberColumn
        },
        {
          prop: "loss",
          label: $T("损耗量") + "(kWh)",
          headerAlign: "left",
          align: "left",
          minWidth: "110px",
          showOverflowTooltip: true,
          formatter: common.formatNumberColumn
        },
        {
          prop: "lossRate",
          label: $T("损耗率") + "(%)",
          headerAlign: "left",
          align: "left",
          minWidth: "110px",
          showOverflowTooltip: true,
          formatter: this.formatNumberColumnA()
        }
      ]
    };
  },
  watch: {},
  methods: {
    formatNumberColumnA(precision = 2) {
      precision = precision || 0;
      return function (row, column, cellValue, index) {
        if (cellValue) {
          if (!_.isNumber(cellValue)) {
            //先转换成数字
            cellValue = parseFloat(cellValue);
          }

          const pow = Math.pow(10, precision);
          return (Math.round(cellValue * 100 * pow) / pow).toFixed(precision); //保留百分数两位小数
        } else if (cellValue === 0) {
          const val = cellValue.toFixed(2);
          return `${val}`;
        } else {
          return "--";
        }
      };
    },
    changeQueryTime({ val, timeOption }) {
      const date = this.$moment(val);
      this.queryTime = {
        cycle: timeOption.typeID,
        startTime: date.startOf(timeOption.unit).valueOf(),
        endTime: date.endOf(timeOption.unit).valueOf() + 1
      };
      this.querylossMonitor(this.nodeInfo);
    },
    // 查询损耗分析
    querylossMonitor() {
      if (this._.isEmpty(this.currentNode)) return;
      const param = {
        startTime: this.queryTime.startTime,
        endTime: this.queryTime.endTime,
        aggregationCycle: this.queryTime.cycle,
        projectId: this.projectId,
        modelLabel: this.currentNode.modelLabel,
        id: this.currentNode.id,
        energyType: this.energyType
      };
      // 查一段线和变压器
      const queryData = {
        keepTransformer: true,
        queryLineSegment: true
      };
      customApi.queryLossLine(param, queryData).then(res => {
        if (res.code === 0) {
          this.tableData = res.data;
        }
      });
    },
    // 能源类型改变
    handleEnergyChange(val) {
      this.tableData = [];
      this.energyType = val;
      this.getDefaultUnit(val);
    },
    // 节点树点击
    handleNodeClick(val) {
      if (val.modelLabel !== "project" && val.modelLabel !== "room") {
        this.currentNode = val;
        if (this.currentNode.modelLabel === "powertransformer") {
          this.Columns_EnergyConsumpte[2].label =
            $T("上级支路能耗") + `(${this.unit})`;
        } else {
          this.Columns_EnergyConsumpte[2].label =
            $T("当前支路能耗") + `(${this.unit})`;
        }
        this.querylossMonitor();
      } else {
        this.currentNode = null;
        this.$message.warning($T("请选择设备！"));
      }
    },
    // 查询基本单位
    getDefaultUnit(val) {
      if (!val) return;
      const params = {
        projectUnitClassify: 1,
        types: [val]
      };
      customApi.getDefaultUnitSetting(params).then(res => {
        if (res.code === 0) {
          const uniten = common.formatSymbol(this._.get(res, "data[0].uniten"));
          this.unit = uniten;
          this.Columns_EnergyConsumpte.forEach(item => {
            if (item.prop === "output") {
              item.label = $T("下级支路能耗") + `(${uniten})`;
            }
            if (item.prop === "loss") {
              item.label = $T("损耗量") + `(${uniten})`;
            }
          });
        }
      });
    },
    tableCellClassName({ row, column }) {
      if (
        (column.property === "loss" && row.loss && row.loss < 0) ||
        (column.property === "lossRate" && row.lossRate && row.lossRate < 0)
      ) {
        return "warning-row";
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  height: 100%;
}
.treeTable {
  :deep(.el-table__body-wrapper) {
    overflow: auto;
    max-height: 100%;
  }
}
.el-table {
  :deep(.el-table__expand-icon) {
    @include font_color(T3);
  }
  :deep(.warning-row) {
    .cell {
      @include font_color(Sta3);
    }
  }
  :deep(.el-table__placeholder) {
    margin-right: 3px;
  }
}
</style>
