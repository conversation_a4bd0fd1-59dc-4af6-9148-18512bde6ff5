<template>
  <div class="page fullfilled flex-row eem-common">
    <div class="eem-aside treeBox">
      <CetGiantTree
        class="fullheight"
        v-bind="CetGiantTree_1"
        v-on="CetGiantTree_1.event"
      ></CetGiantTree>
    </div>
    <div class="flex-auto flex-column mlJ3">
      <div class="mbJ3 text-ellipsis">
        <el-tooltip
          effect="light"
          :content="currentNode && currentNode.name"
          placement="bottom"
        >
          <span class="common-title-H1">
            {{ currentNode && currentNode.name }}
          </span>
        </el-tooltip>
      </div>
      <div class="flex-auto flex-row">
        <div class="flex-column flex-auto">
          <div class="eem-container flex-auto flex-column">
            <div class="mbJ3 clearfix">
              <span class="common-title-H2 contentTitle">
                {{ $T("已关联的采集设备") }}
              </span>
              <div class="fr">
                <CetButton
                  class="fl mrJ1"
                  v-bind="CetButton_del"
                  v-on="CetButton_del.event"
                  :disable_in="checkedDataIds.length ? false : true"
                ></CetButton>
                <CetButton
                  class="fl mrJ1"
                  v-bind="CetButton_fullScaleValue"
                  v-on="CetButton_fullScaleValue.event"
                ></CetButton>
                <CetButton
                  class="fl"
                  v-bind="CetButton_add"
                  v-on="CetButton_add.event"
                ></CetButton>
              </div>
            </div>
            <div style="flex: 1; overflow-y: auto" class="minWH">
              <el-checkbox
                :indeterminate="isIndeterminate"
                v-model="checkAll"
                @change="handleCheckAllChange"
                class="mbJ1 mlJ3"
              >
                全选
              </el-checkbox>
              <el-checkbox-group
                v-model="checkedDataIds"
                @change="handleCheckedDataIdsChange"
              >
                <div
                  v-for="(item, index) in deviceList"
                  :key="item.id"
                  :class="{
                    deviceItem: true,
                    mbJ1: true,
                    pJ3: true,
                    brC: true,
                    deviceItemAction: deviceItemActionIndex === index
                  }"
                >
                  <el-checkbox
                    :label="item.id"
                    :key="item.id"
                    class="checkboxItem"
                  ></el-checkbox>
                  <div
                    class="deviceItemName text-ellipsis"
                    @click="getMonitoredNode(item, index)"
                  >
                    {{ item.deviceName || "--" }}
                    <i
                      class="el-icon-delete deleteBtn"
                      @click.stop="delectSupplyRelation_out(item.id)"
                    ></i>
                  </div>
                </div>
              </el-checkbox-group>
            </div>
          </div>
        </div>
        <div class="mlJ3 eem-container flex-column flex-auto">
          <div class="mbJ3">
            <CetButton
              class="fr"
              v-bind="CetButton_save"
              v-on="CetButton_save.event"
            ></CetButton>
          </div>
          <div class="flex-auto">
            <el-table
              ref="multipleTable"
              :data="tableData"
              tooltip-effect="light"
              style="width: 100%"
              height="100%"
              border
              stripe
              highlight-current-row
              @selection-change="handleSelectionChange"
            >
              <ElTableColumn v-bind="ElTableColumn_selection"></ElTableColumn>
              <ElTableColumn v-bind="ElTableColumn_paraName"></ElTableColumn>
              <ElTableColumn v-bind="ElTableColumn_dataId"></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_logicalDeviceIndex"
              ></ElTableColumn>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <fullScaleValue
      :visibleTrigger_in="fullScaleValue.visibleTrigger_in"
      :closeTrigger_in="fullScaleValue.closeTrigger_in"
      :queryId_in="fullScaleValue.queryId_in"
      :inputData_in="fullScaleValue.inputData_in"
      :deviceNodes="deviceList"
    />
    <add
      :visibleTrigger_in="add.visibleTrigger_in"
      :closeTrigger_in="add.closeTrigger_in"
      :inputData_in="add.inputData_in"
      :currentNode="currentNode"
      :deviceList="deviceList"
      @updata_out="updataList"
      @deviceArr_out="deviceArr_out"
    />
  </div>
</template>

<script>
import common from "eem-utils/common";
import fullScaleValue from "./fullScaleValue.vue";
import add from "./add.vue";
import commonApi from "@/api/custom.js";
import TREE_PARAMS from "@/store/treeParams.js";
import { httping } from "@omega/http";
export default {
  name: "gatherRelation",
  components: {
    fullScaleValue,
    add
  },
  computed: {
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    }
  },
  data() {
    return {
      ajaxFlag: false,
      tableData: [],
      deviceItemActionIndex: -1,
      // 已关联采集的列表  每个采集设备的关联测点写在monitoredNodeArr内
      deviceList: [],
      currentNode: null,
      filterText1: "",
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out //选中单行输出
        }
      },

      CetButton_fullScaleValue: {
        visible_in: true,
        disable_in: true,
        title: $T("满刻度值"),
        plain: true,
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_fullScaleValue_statusTrigger_out
        }
      },
      CetButton_add: {
        visible_in: true,
        disable_in: true,
        title: $T("添加采集设备"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      fullScaleValue: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      add: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      ElTableColumn_selection: {
        type: "selection",
        width: "40px",
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      // 设置组件唯一识别字段组件
      ElTableColumn_paraName: {
        prop: "paraName",
        minWidth: "120px",
        label: $T("测点"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_dataId: {
        prop: "dataId",
        minWidth: "120px",
        label: $T("测点ID"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_logicalDeviceIndex: {
        prop: "logicalDeviceIndex",
        minWidth: "120px",
        label: $T("回路号"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      CetButton_save: {
        visible_in: true,
        disable_in: true,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_save_statusTrigger_out
        }
      },
      CetButton_del: {
        visible_in: true,
        // disable_in: false,
        title: $T("批量删除"),
        plain: true,
        type: "danger",
        event: {
          statusTrigger_out: this.CetButton_del_statusTrigger_out
        }
      },
      checkAll: false,
      checkedDataIds: [],
      isIndeterminate: false
    };
  },
  watch: {
    deviceList: {
      handler(val, old) {
        if (val && val.length > 0) {
          this.CetButton_fullScaleValue.disable_in = false;
        } else {
          this.CetButton_fullScaleValue.disable_in = true;
        }
      }
    }
  },
  methods: {
    handleSelectionChange(val) {
      if (!this.ajaxFlag && this.deviceList[this.deviceItemActionIndex]) {
        this.deviceList[this.deviceItemActionIndex].monitoredNodeArr = val;
      }
    },
    // 获取测点
    getMonitoredNode(item, index) {
      if (!item) {
        return;
      }
      const _this = this;
      this.deviceItemActionIndex = index;
      this.tableData = [];
      if (!item.id) {
        return;
      }
      var queryData = {
        deviceId: item.id,
        node: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel
        }
      };
      this.ajaxFlag = true;
      commonApi.getMonitoredNode(queryData).then(response => {
        if (response.code === 0) {
          var data = _this._.get(response, "data", []) || [];
          _this.tableData = data;
          var monitoredNodeArr = data.filter(item => {
            return !!item.selected;
          });
          // 设置选中
          // var monitoredNodeArr = _this.deviceList[_this.deviceItemActionIndex].monitoredNodeArr || [];
          _this.tableData.forEach(item => {
            monitoredNodeArr.forEach(item2 => {
              if (
                item.dataId === item2.dataId &&
                item.dataTypeId === item2.dataTypeId &&
                item.logicalDeviceIndex === item2.logicalDeviceIndex
              ) {
                _this.$nextTick(() => {
                  _this.$refs.multipleTable.toggleRowSelection(item);
                });
              }
            });
          });
        }
        _this.$nextTick(() => {
          _this.ajaxFlag = false;
        });
      });
    },
    // 保存关联测点
    CetButton_save_statusTrigger_out(val) {
      const deviceId = this.deviceList?.[this.deviceItemActionIndex]?.id;
      if (!this.deviceList || !deviceId) {
        return;
      }
      let params = {
        nodeId: this.currentNode.id,
        modelLabel: this.currentNode.modelLabel,
        deviceId: deviceId,
        measureInfos: []
      };
      const device = this.deviceList.find(
        (item, index) => index === this.deviceItemActionIndex
      );
      if (device && device.monitoredNodeArr && device.monitoredNodeArr.length) {
        device.monitoredNodeArr.forEach(i => {
          i.deviceId = deviceId;
        });
        params.measureInfos.push(...device.monitoredNodeArr);
      }
      const deviceIds = this.deviceList.map(i => i.id);
      var params2 = [
        {
          modelLabel: this.currentNode.modelLabel,
          nodes: [
            {
              deviceIds: deviceIds,
              id: this.currentNode.id
            }
          ]
        }
      ];
      // 数据源
      var dataSource = 6;
      if (deviceIds.length === 0) {
        dataSource = 6;
      } else {
        dataSource = 1;
      }
      httping({
        url: "/eem-service/v1/quantity/quantityMap/measureInfo",
        data: params,
        method: "POST",
        timeout: 10000
      }).then(res => {
        this.Edit_dataSource(dataSource, params2);
      });
    },
    getTreeData() {
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.gatherRelation,
        treeReturnEnable: true
      };
      httping({
        url: "/eem-service/v1/node/nodeTree",
        method: "POST",
        data
      }).then(res => {
        if (res.code === 0) {
          _this.CetGiantTree_1.inputData_in =
            _this._.get(res, "data[0].children", []) || [];
          _this.CetGiantTree_1.selectNode = _this._.get(
            res,
            "data[0].children[0]"
          );
        }
      });
    },
    // 查询关系
    querySupplyRelation_out(val) {
      if (!val) {
        return;
      }
      var me = this;
      var param;
      var url;
      param = [
        {
          modelLabel: val.modelLabel,
          id: val.id
        }
      ];
      url = `/eem-service/v1/connect/measureBy`;
      common.requestData(
        {
          url: url,
          data: param
        },
        (data, res) => {
          if (res.code === 0) {
            const checkedNodes = me._.get(res, "data", []).map(item => ({
              deviceName: item.deviceName,
              dataId: item.id,
              modelLabel: "269619472",
              id: item.measuredby,
              tree_id: "269619472_" + item.measuredby
            }));
            me.deviceList = this._.cloneDeep(checkedNodes);
            var index = -1;
            var item;
            if (me.deviceList && me.deviceList.length > 0) {
              index = 0;
              item = me.deviceList[0];
            }
            this.getMonitoredNode(item, index);
          }
        },
        () => {
          me.deviceList = [];
          me.getMonitoredNode(false, -1);
        }
      );
    },
    // 删除关系
    delectSupplyRelation_out(val) {
      var _this = this;
      _this
        .$confirm($T("确定要删除吗？"), $T("提示"), {
          distinguishCancelAndClose: true,
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        })
        .then(() => {
          var params = [];
          var deviceIds = [];
          var ids = _this._.isArray(val) ? val : [val];
          _this.deviceList.forEach(item => {
            if (!ids.includes(item.id)) {
              deviceIds.push(item.id);
            }
          });
          var obj = {
            modelLabel: _this.currentNode.modelLabel,
            nodes: [
              {
                deviceIds: deviceIds,
                id: _this.currentNode.id
              }
            ]
          };
          params.push(obj);
          // 数据源
          var dataSource = 6;
          if (deviceIds.length === 0) {
            dataSource = 6;
          } else {
            dataSource = 1;
          }
          httping({
            url: "/eem-service/v1/quantity/quantityMap",
            data: params,
            method: "PUT",
            timeout: 10000
          }).then(res => {
            _this.Edit_dataSource(dataSource, params);
          });
        })
        .catch(action => {
          if (action === "cancel") {
            _this.$message({
              type: "info",
              message: $T("已取消")
            });
          }
        });
    },
    // 更新物理量数据源
    Edit_dataSource(dataSource, params) {
      httping({
        url: "/eem-service/v1/quantity/quantityObject?dataSource=" + dataSource,
        method: "POST",
        data: params
      }).then(response => {
        if (response.code === 0) {
          this.$message({
            message: $T("保存成功"),
            type: "success"
          });
          this.$emit("saveData_out");
          this.CetGiantTree_1_currentNode_out(this.currentNode);
        }
      });
    },
    // 更新已关联的列表
    updataList() {
      this.CetGiantTree_1_currentNode_out(this.currentNode);
    },
    CetGiantTree_1_currentNode_out(val) {
      if (!val) {
        return;
      }
      this.deviceList = [];
      this.tableData = [];
      this.currentNode = this._.cloneDeep(val);
      if (
        ["sectionarea", "building", "floor"].includes(val.modelLabel) ||
        (val.modelLabel === "room" && !val.roomtype)
      ) {
        this.CetButton_add.disable_in = true;
        return;
      }
      this.CetButton_add.disable_in = false;
      this.CetButton_save.disable_in = false;
      this.checkAll = false;
      this.checkedDataIds = [];
      this.isIndeterminate = false;
      this.querySupplyRelation_out(val);
    },
    CetButton_fullScaleValue_statusTrigger_out(val) {
      this.fullScaleValue.inputData_in = this._.cloneDeep(this.currentNode);
      this.fullScaleValue.visibleTrigger_in = this._.cloneDeep(val);
    },
    CetButton_add_statusTrigger_out(val) {
      this.add.inputData_in = this._.cloneDeep(this.currentNode);
      this.add.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 添加弹框输出的采集设备
    deviceArr_out(deviceArr) {
      this.deviceList.push(...deviceArr);
    },
    CetButton_del_statusTrigger_out() {
      if (!this.checkedDataIds.length) {
        this.$message.warning($T("请选择节点"));
        return;
      }
      this.delectSupplyRelation_out(this.checkedDataIds);
    },
    handleCheckAllChange(val) {
      if (val) {
        this.checkedDataIds = this.deviceList.map(i => i.id);
      } else {
        this.checkedDataIds = [];
      }
      this.isIndeterminate = false;
    },
    handleCheckedDataIdsChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.deviceList.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.deviceList.length;
    }
  },
  mounted() {
    this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
    this.getTreeData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  .treeBox {
    width: 315px;
  }
  .contentTitle {
    @include line_height(Hm);
  }
  .deviceItem {
    position: relative;
    @include background_color(BG);
    cursor: pointer;
    .deviceItemName {
      width: 100%;
      padding-right: 20px;
      padding-left: 20px;
      box-sizing: border-box;
      @include font_size(H3);
      .deleteBtn {
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        @include font_color(Sta3);
      }
    }
  }
  .deviceItemAction {
    @include background_color(BG4);
  }
  .checkboxItem {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    :deep(.el-checkbox__label) {
      display: none;
    }
  }
}
</style>
