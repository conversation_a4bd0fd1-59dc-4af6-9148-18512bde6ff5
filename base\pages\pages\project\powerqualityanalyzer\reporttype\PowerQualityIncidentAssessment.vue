<template>
  <el-container style="height: 100%; flex-direction: column; overflow: auto">
    <!-- 电能质量事件统计 -->
    <template>
      <el-row class="mbJ3">
        <el-col :span="24" class="text-left common-title-H3">
          评估时间：{{
            queryParam.queryTime.startTime | formatDate("YYYY-MM-DD")
          }}
          至 {{ queryParam.queryTime.endTime | formatDate("YYYY-MM-DD") }}
        </el-col>
      </el-row>
      <div class="eem-cont mbJ3">
        <div>
          <headerSpot class="mbJ3">电能质量事件统计</headerSpot>
        </div>
        <el-row>
          <el-col>
            <table class="table-terse" cellspacing="0" cellpadding="0">
              <thead>
                <tr>
                  <th>事件分类</th>
                  <th>电压中断</th>
                  <th>电压暂降</th>
                  <th>电压暂升</th>
                  <th>瞬变事件</th>
                  <th>长时过电压</th>
                  <th>长时欠电压</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>事件数量</td>
                  <td>
                    {{
                      findDataByKey(eventCount, "interruption") +
                      findDataByKey(eventCount, "longinterruption")
                    }}
                  </td>
                  <td>{{ findDataByKey(eventCount, "dip") }}</td>
                  <td>{{ findDataByKey(eventCount, "swell") }}</td>
                  <td>{{ findDataByKey(eventCount, "transient") }}</td>
                  <td>{{ findDataByKey(eventCount, "longdip") }}</td>
                  <td>{{ findDataByKey(eventCount, "longswell") }}</td>
                </tr>
              </tbody>
            </table>
          </el-col>
        </el-row>
        <el-row class="ptJ3">
          <el-col :span="12">
            <div class="chart-box mrJ1">
              <!-- 事件按照故障方向统计饼图 -->
              <CetChart
                ref="eventFaultDirectionChart"
                v-bind="CetChart2_eventFaultDirection"
              ></CetChart>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="chart-box marginBottomJ2 mlJ1">
              <!-- 事件按照事件类型统计饼图 -->
              <CetChart
                ref="eventClassifyChart"
                v-bind="CetChart2_eventClassify"
              ></CetChart>
            </div>
          </el-col>
        </el-row>
      </div>
    </template>
    <!-- 事件统计 -->
    <template>
      <div class="eem-cont mbJ3">
        <div>
          <headerSpot class="mbJ3">事件统计</headerSpot>
        </div>
        <el-row>
          <el-col class="paddingL-R-16">
            <table class="table-terse" cellspacing="0" cellpadding="0">
              <thead>
                <tr>
                  <th>总点数</th>
                  <th>瞬变事件</th>
                  <th>暂降事件</th>
                  <th>暂升</th>
                  <th>中断</th>
                  <th>不确定类型</th>
                  <th>A区</th>
                  <th>B区</th>
                  <th>C区</th>
                  <th>区域外</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>{{ findDataByKey(eventCount, "total") }}</td>
                  <td>{{ findDataByKey(eventCount, "transient") }}</td>
                  <td>
                    {{
                      findDataByKey(eventCount, "dip", "-") +
                      findDataByKey(eventCount, "longdip", "-")
                    }}
                  </td>
                  <td>
                    {{
                      findDataByKey(eventCount, "swell", "-") +
                      findDataByKey(eventCount, "longswell", "-")
                    }}
                  </td>
                  <td>{{ findDataByKey(eventCount, "interruption") }}</td>
                  <td>{{ findDataByKey(eventCount, "undefined") }}</td>
                  <td>{{ findDataByKey(toleranceCount, "tolerance") }}</td>
                  <td>{{ findDataByKey(toleranceCount, "prohibited") }}</td>
                  <td>{{ findDataByKey(toleranceCount, "noDamage") }}</td>
                  <td>{{ findDataByKey(toleranceCount, "none") }}</td>
                </tr>
              </tbody>
            </table>
          </el-col>
        </el-row>
      </div>
    </template>
    <!-- 电能质量事件影响 -->
    <template>
      <el-row class="eem-cont mbJ3">
        <div>
          <headerSpot>电能质量事件影响</headerSpot>
        </div>
        <el-col style="height: 600px" class="mtJ3">
          <ITIC
            ref="ITICChart"
            key="PowerQualityIncidentAssessmentITICChart"
            :current-node="currentNode"
            :queryTime="queryParam.queryTime"
            :refreshTrigger_in="refreshTrigger_in"
          />
        </el-col>
      </el-row>
    </template>
    <!-- 电能质量事件趋势统计 -->
    <template>
      <el-row class="eem-cont">
        <div>
          <headerSpot>电能质量事件趋势统计</headerSpot>
        </div>
        <el-col style="height: 300px" class="mtJ3">
          <CetChart
            ref="eventTrendChart"
            v-bind="CetChart2_eventTrend"
          ></CetChart>
        </el-col>
      </el-row>
    </template>
  </el-container>
</template>
<script>
import common from "eem-utils/common";
import moment from "moment";
import ITIC from "../singlepointanalysis/ITIC";

export default {
  name: "PowerQualityIncidentAssessment",
  components: {
    ITIC
  },
  props: {
    queryParam: {
      type: [Object, Array],
      default: () => ({
        queryTime: []
      })
    }
  },
  computed: {
    currentNode() {
      return {
        data: {
          id: this._.get(this.queryParam, "params.id"),
          modelLabel: this._.get(this.queryParam, "params.modelLabel"),
          tree_id: `${this._.get(
            this.queryParam,
            "params.modelLabel"
          )}_${this._.get(this.queryParam, "params.id")}`,
          leaf:
            this._.get(this.queryParam, "params.modelLabel", "dcbase") !==
            "dcabse"
        }
      };
    },
    token() {
      return this.$store.state.token;
    }
  },
  watch: {
    queryParam: {
      deep: true,
      handler(val, oldVal) {
        this.queryReport();
      }
    }
  },
  data(vm) {
    return {
      // 事件区间统计
      CetChart2_eventFaultDirection: {
        //组件输入项
        inputData_in: null,
        options: {
          title: {
            text: "事件故障方向统计",
            left: "left"
          },
          color: ["#32C5E9", "#37A2DA"],
          tooltip: {
            trigger: "item",
            formatter: function (param) {
              return (
                `${param.data.name}：<br />` +
                `事件数量：${param.data.value}条<br />` +
                `占比：${param.percent}%`
              );
            }
          },
          legend: {
            // orient: 'vertical',
            // top: 'middle',
            bottom: 10,
            left: "center"
          },
          series: [
            {
              type: "pie",
              label: {
                normal: {
                  position: "inner",
                  show: false
                }
              },
              radius: "65%",
              center: ["50%", "50%"]
            }
          ]
        }
      },

      // 电能质量事件分类统计
      CetChart2_eventClassify: {
        //组件输入项
        inputData_in: null,
        options: {
          color: [
            "#37A2DA",
            "#32C5E9",
            "#9FE6B8",
            "#FFDB5C",
            "#FF9F7F",
            "#F598AF"
          ],
          title: {
            text: "事件类型统计",
            left: "left"
          },
          tooltip: {
            trigger: "item",
            formatter: function (param) {
              const val = param.data.value;
              return (
                `${param.data.name}：<br />` +
                `事件数量：${val}条<br />` +
                `占比：${common.roundNumber(
                  (val / vm.eventCount.total) * 100,
                  2,
                  "--"
                )}%`
              );
            }
          },
          legend: {
            // orient: 'vertical',
            // top: 'middle',
            bottom: 10,
            left: "center"
          },
          series: [
            {
              type: "pie",
              label: {
                normal: {
                  position: "inner",
                  show: false
                }
              },
              radius: "65%",
              center: ["50%", "50%"]
            }
          ]
        }
      },

      // 电能质量事件趋势统计
      CetChart2_eventTrend: {
        //组件输入项
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross"
            },
            formatter(params) {
              if (vm._.isEmpty(params)) return "--";
              let text =
                vm.$moment(params[0].axisValue).format("YYYY-MM-DD") + "<br />";
              params.forEach(item => {
                const key = item.dimensionNames[item.encode.y[0]];
                const val = common.roundNumber(item.data[key], 2, "--");
                text += `${item.marker} ${item.seriesName}：`;
                if (item.seriesType === "line") {
                  if (val > 0) text += `上涨 ${val}%<br />`;
                  else if (val < 0) text += `下降 ${Math.abs(val)}%<br />`;
                  else text += `${val}%<br />`;
                } else {
                  text += `${val}<br />`;
                }
              });
              return text;
            }
          },
          legend: {},
          grid: {
            top: 35,
            right: 35,
            bottom: 15,
            left: 15,
            containLabel: true
          },
          xAxis: [
            {
              splitLine: {
                show: false
              },
              type: "time",
              axisPointer: {
                type: "shadow"
              }
            }
          ],
          yAxis: [
            {
              type: "value",
              name: "事件数量",
              min: 0
            },
            {
              splitLine: {
                show: false
              },
              type: "value",
              name: "环比(%)"
            }
          ],
          series: [
            {
              type: "bar",
              name: "事件数量",
              encode: {
                x: "time",
                y: "current"
              }
            },
            {
              type: "line",
              yAxisIndex: 1,
              name: "环比",
              encode: {
                x: "time",
                y: "chain"
              }
            }
          ]
        }
      },

      // 容忍度事件统计
      eventCount: {},
      // 事件按照容忍度方向统计
      toleranceCount: {},
      refreshTrigger_in: new Date().getTime()
    };
  },
  methods: {
    findDataByKey(...arg) {
      return common.findDataByKey(...arg);
    },
    queryReport() {
      const vm = this;

      if (!vm.queryParam || !vm.queryParam.params) {
        return;
      }
      const builddata = vm.queryParam.params;
      const initparam = {
        cycle: builddata.aggregationCycle,
        endTime: builddata.endTime,
        node: {
          id: builddata.id,
          modelLabel: builddata.modelLabel
        },
        projectId: this.projectId,
        startTime: builddata.startTime
      };

      // 请求
      common.requestData(
        {
          url: "/eem-service/v1/pq/report/event",
          // data: vm.queryParam.params
          data: initparam
        },
        res => {
          // 处理事件按照事件类型统计
          vm.eventCount = res.eventTypeCount;
          const eventTypeCount = res.eventTypeCount;
          const eventClassify = [];
          eventClassify.push({
            id: "interruption",
            name: "电压中断",
            value: eventTypeCount.interruption + eventTypeCount.longinterruption
          });
          eventClassify.push({
            id: "swell",
            name: "电压暂升",
            value: eventTypeCount.swell
          });
          eventClassify.push({
            id: "longswell",
            name: "长时过电压",
            value: eventTypeCount.longswell
          });
          eventClassify.push({
            id: "dip",
            name: "电压暂降",
            value: eventTypeCount.dip
          });
          eventClassify.push({
            id: "longdip",
            name: "长时欠电压",
            value: eventTypeCount.longdip
          });
          eventClassify.push({
            id: "transient",
            name: "瞬变",
            value: eventTypeCount.transient
          });
          vm.CetChart2_eventClassify.inputData_in = eventClassify;

          // 处理事件按照负载类型统计
          const faultDirectionCount = res.faultDirectionCount;
          const faultDirectionClassify = [];
          faultDirectionClassify.push({
            id: "loadSide",
            name: "负载侧",
            value: faultDirectionCount.loadSide
          });
          faultDirectionClassify.push({
            id: "sourceSide",
            name: "电源侧",
            value: faultDirectionCount.sourceSide
          });
          vm.CetChart2_eventFaultDirection.inputData_in =
            faultDirectionClassify;

          // 处理事件按照容忍度区域统计
          vm.toleranceCount = res.toleranceCount;

          // 处理事件数量与时间的关系
          vm.CetChart2_eventTrend.inputData_in = res.eventCountByTime;
        }
      );
    },
    exportReport() {
      const vm = this;

      if (!vm.queryParam || !vm.queryParam.params) {
        return;
      }

      const params = this._.cloneDeep(vm.queryParam.params);
      params.pictures = [];
      params.pictures.push(this.$refs.eventFaultDirectionChart.getDataURL());
      params.pictures.push(this.$refs.eventClassifyChart.getDataURL());
      params.pictures.push(this.$refs.ITICChart.$refs.ITICChart.getDataURL());
      params.pictures.push(this.$refs.eventTrendChart.getDataURL());

      const initparam = {
        cycle: params.aggregationCycle,
        endTime: params.endTime,
        eventTypes: [],
        lineFunctionTypes: [0],
        node: {
          childSelectState: 0,
          children: [null],
          deviceIds: [0],
          endPoint: true,
          id: params.id,
          modelLabel: params.modelLabel,
          name: "电能质量事件评估报表",
          startPoint: true
        },
        pictures: params.pictures,
        projectId: this.projectId,
        startTime: params.startTime
      };

      common.downExcel(
        "/eem-service/v1/pq/report/event/export",
        initparam,
        this.token
      );
    }
  },
  filters: {
    //格式化日期
    formatDate(val, format = "YYYY-MM-DD HH:mm:ss") {
      if (!val) {
        return "--";
      }

      return moment(val).format(format);
    }
  },
  created() {
    this.queryReport();
  },
  activated() {
    this.queryReport();
  },
  mounted() {
    this.refreshTrigger_in = Date.now();
  }
};
</script>
<style lang="scss" scope>
.chart-box {
  padding: 10px;
  border: 1px solid;
  @include border_color(B2);
  @include border_radius(C);
  height: 300px;
}
</style>
