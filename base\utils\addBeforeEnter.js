import { getJsonFile } from "eem-utils/util";
import { store } from "@omega/app";
import { HttpBase } from "@omega/http";

// 平台级菜单增加项目id设置为0
function removeProjectId(to, from, next) {
  window.sessionStorage.setItem("projectId", 0);
  window.localStorage.setItem("projectId", 0);
  store.commit("setProjectId", 0);
  HttpBase.setHeadersOperate({ projectId: 0 });
  next();
}

// 通过路由独享守卫将平台菜单补上访问即删除项目id逻辑
export async function addBeforeEnter(appRoutes) {
  const navmenuJson = await getJsonFile(`/static/navmenu.json?t=${Date.now()}`);
  const allNavmenu = [...navmenuJson.main, ...navmenuJson.admin];
  const pfNavmenu = allNavmenu.filter(item => item.Module === "pf");

  let pathArr = [];
  function loop(list) {
    if (!list || !list.length) return;
    list.forEach(item => {
      if (item.type === "menuItem" && item.location) {
        pathArr.push(item.location);
      }
      if (item.subMenuList && item.subMenuList.length) {
        loop(item.subMenuList);
      }
    });
  }
  loop(pfNavmenu);
  const rootOption = appRoutes.find(i => i.path === "/");
  rootOption.children.forEach(item => {
    if (pathArr.includes(item.path)) {
      item.beforeEnter = removeProjectId;
    }
  });
}
