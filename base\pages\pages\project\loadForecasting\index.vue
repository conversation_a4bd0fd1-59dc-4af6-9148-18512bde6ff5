﻿<template>
  <div class="page projectOverview eem-common" style="min-width: 1350px">
    <el-container style="height: 100%">
      <el-aside width="315px" class="eem-aside">
        <CetGiantTree
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
        ></CetGiantTree>
      </el-aside>
      <el-main class="fullheight padding0 mlJ3 flex-column">
        <div class="eem-container mbJ3">
          <div class="fr">
            <customElSelect
              v-model="ElSelect_1.value"
              v-bind="ElSelect_1"
              v-on="ElSelect_1.event"
              class="fl"
              :prefix_in="$T('能耗类型')"
            >
              <ElOption
                v-for="item in ElOption_1.options_in"
                :key="item[ElOption_1.key]"
                :label="item[ElOption_1.label]"
                :value="item[ElOption_1.value]"
                :disabled="item[ElOption_1.disabled]"
              ></ElOption>
            </customElSelect>
            <customElSelect
              v-model="ElSelect_2.value"
              v-bind="ElSelect_2"
              v-on="ElSelect_2.event"
              class="fl mlJ1"
              :prefix_in="$T('查询时段')"
            >
              <ElOption
                v-for="item in ElOption_2.options_in"
                :key="item[ElOption_2.key]"
                :label="item[ElOption_2.label]"
                :value="item[ElOption_2.value]"
                :disabled="item[ElOption_2.disabled]"
              ></ElOption>
            </customElSelect>
            <div class="fl mlJ1" style="display: inline-block">
              <CetButton
                class="fl mrJ"
                v-bind="CetButton_prv"
                v-on="CetButton_prv.event"
              ></CetButton>
              <div class="basic-box fl">
                <el-date-picker
                  v-model="CetDatePicker_1.val"
                  v-bind="CetDatePicker_1.config"
                  :clearable="false"
                  :placeholder="$T('选择日期')"
                ></el-date-picker>
              </div>
              <CetButton
                class="fl mlJ"
                v-bind="CetButton_next"
                v-on="CetButton_next.event"
              ></CetButton>
            </div>
            <el-checkbox-group
              class="eem-checkbox fl mlJ1 lh32"
              style="height: 32px"
              v-model="chartTypeRadio"
              @change="chartTypeRadioChange_out"
            >
              <el-checkbox
                v-for="(item, index) in chartTypeList"
                :key="index"
                :label="item.label"
              >
                {{ item.name }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div class="flex-auto flex-column" style="overflow: auto">
          <div class="eem-container">
            <div class="mbJ3">
              <span class="common-title-H2">
                {{ $T("能耗预测与实际偏差") }}
              </span>
            </div>
            <div style="height: 300px" v-if="!showSingle">
              <CetChart
                ref="CetChart_1"
                :inputData_in="CetChart_1.inputData_in"
                v-bind="CetChart_1.config"
              />
            </div>
            <div style="height: 300px" v-if="showSingle">
              <CetChart
                ref="CetChart_2"
                :inputData_in="CetChart_2.inputData_in"
                v-bind="CetChart_2.config"
              />
            </div>
          </div>
          <div
            class="eem-container mtJ3 flex-auto flex-column"
            style="min-height: 300px"
          >
            <div class="mbJ3 flex-row">
              <div class="flex-auto text-ellipsis">
                <el-tooltip
                  effect="light"
                  :content="$T('能耗预测与实际能耗统计')"
                  placement="top-start"
                >
                  <span class="common-title-H2 lh32" style="display: inline">
                    {{ $T("能耗预测与实际能耗统计") }}
                  </span>
                </el-tooltip>
              </div>
              <div>
                <el-radio-group
                  v-show="productionPlan.tableType == 1 && isShowRadio"
                  class="mrJ1 eem-radio"
                  v-model="typeRadio"
                  @change="typeRadioChange_out"
                >
                  <el-radio
                    class="lh32"
                    v-for="(item, index) in typeList"
                    :key="index"
                    :label="item.label"
                  >
                    {{ item.name }}
                  </el-radio>
                </el-radio-group>
                <CetButton
                  v-permission="'energyplan_create'"
                  v-show="
                    isDisable && !showSingle && productionPlan.tableType == 0
                  "
                  class="fr mrJ1"
                  v-bind="CetButton_3"
                  v-on="CetButton_3.event"
                ></CetButton>
                <CetButton
                  class="fr mrJ1"
                  v-show="productionPlan.tableType == 0"
                  v-bind="CetButton_1"
                  v-on="CetButton_1.event"
                ></CetButton>
                <el-button
                  size="small"
                  v-show="productionPlan.tableType == 1"
                  class="fr"
                  type="primary"
                  @click="preservationAddProductionPlan_out"
                >
                  {{ $T("保存") }}
                </el-button>
                <el-button
                  size="small"
                  class="fr mrJ1"
                  style="margin-left: 0px"
                  v-show="productionPlan.tableType == 1"
                  type="primary"
                  @click="backAddProductionPlan_out"
                >
                  {{ $T("返回") }}
                </el-button>
                <CetButton
                  class="fr mrJ1"
                  v-show="productionPlan.tableType == 1"
                  v-bind="CetButton_import2"
                  v-on="CetButton_import2.event"
                ></CetButton>
                <CetButton
                  class="fr mrJ1"
                  v-show="productionPlan.tableType == 1"
                  v-bind="CetButton_import1"
                  v-on="CetButton_import1.event"
                ></CetButton>
                <el-upload
                  style="display: none"
                  :action="importUrl"
                  :headers="{
                    Authorization: this.token,
                    projectId: this.projectId
                  }"
                  :data="{}"
                  :before-upload="handleBeforeUpload"
                  :on-success="uploadSuccess"
                  :on-error="uploadError"
                  :multiple="false"
                >
                  <button ref="uploadBtn"></button>
                </el-upload>
                <CetButton
                  class="fr mrJ1"
                  v-show="productionPlan.tableType == 1"
                  v-bind="CetButton_export1"
                  v-on="CetButton_export1.event"
                ></CetButton>
                <CetButton
                  class="fr mrJ1"
                  v-show="productionPlan.tableType == 1"
                  v-bind="CetButton_export"
                  v-on="CetButton_export.event"
                ></CetButton>
              </div>
            </div>
            <div class="flex-auto">
              <div class="fullheight" v-if="showSingle">
                <CetTable
                  ref="elTab3"
                  tooltip-effect="light"
                  height="100%"
                  :data.sync="CetTable_single.data"
                  :dynamicInput.sync="CetTable_single.dynamicInput"
                  v-bind="CetTable_single"
                  v-on="CetTable_single.event"
                >
                  <ElTableColumn
                    headerAlign="center"
                    align="center"
                    :show-overflow-tooltip="true"
                    v-bind="ElTableColumn_index"
                  ></ElTableColumn>
                  <template v-for="(column, index) in Columns_single">
                    <el-table-column
                      v-if="column.custom && column.custom === 'tag'"
                      v-bind="column"
                      :key="index"
                      :show-overflow-tooltip="true"
                      class-name="font0 hand"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <span
                          size="small"
                          class="text-middle font14"
                          :style="{
                            color: column.colorTypeFormatter
                              ? column.colorTypeFormatter(
                                  scope.row,
                                  scope.column
                                )
                              : '#fff'
                          }"
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : scope.row[column.prop]
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    ></el-table-column>
                  </template>
                </CetTable>
              </div>
              <div
                class="fullheight"
                v-show="!showSingle && productionPlan.tableType == 0"
              >
                <el-table
                  ref="elTab1"
                  tooltip-effect="light"
                  :data="productionPlan.table.data"
                  :span-method="productionPlantableSpanMethod"
                  :header-cell-style="tabHeaderStyle"
                  :cell-style="tabCellStyle"
                  :cell-class-name="tabCellClass"
                  border
                  height="100%"
                >
                  <el-table-column
                    v-for="(item, index) in productionPlan.table.config.columns"
                    :width="
                      index == 0
                        ? '150px'
                        : index == 1
                        ? '150px'
                        : index == 2
                        ? '150px'
                        : null
                    "
                    :fixed="[0, 1, 2].includes(index)"
                    :minWidth="100"
                    :key="index"
                    :show-overflow-tooltip="true"
                    align="center"
                    :prop="item.id"
                    :label="item.name"
                  >
                    <template slot-scope="scope">
                      <span>{{ scope.row[item.id].val || "--" }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div
                class="fullheight"
                v-show="!showSingle && productionPlan.tableType == 1"
              >
                <el-table
                  ref="elTab2"
                  tooltip-effect="light"
                  :data="productionPlan.addProductionPlanTable.data"
                  :span-method="productionPlantableSpanMethod"
                  :header-cell-style="tabHeaderStyle"
                  :cell-style="tabCellStyle"
                  :cell-class-name="tabCellClass"
                  border
                  stripe
                  height="100%"
                >
                  <el-table-column
                    v-for="(item, index) in productionPlan.table.config.columns"
                    :width="
                      index == 0
                        ? '150px'
                        : index == 1
                        ? '150px'
                        : index == 2
                        ? '180px'
                        : null
                    "
                    :fixed="[0, 1, 2].includes(index)"
                    :minWidth="150"
                    :key="index"
                    :show-overflow-tooltip="true"
                    align="center"
                    :prop="item.id"
                    :label="item.name"
                  >
                    <template slot-scope="scope">
                      <span v-show="filTableRowCols1(index, scope.row)">
                        {{ scope.row[item.id].val || "--" }}
                      </span>
                      <el-input
                        size="small"
                        v-if="filTableRowCols2(index, scope.row)"
                        :class="{
                          projectOverview_tabModifyInput: true,
                          addProductionPlaTableInput: index == 2
                        }"
                        v-model="scope.row[item.id].val"
                        @keyup.native="handleNum(scope.row, item.id, 2)"
                        @blur="blurNum(scope.row, item.id)"
                      ></el-input>
                      <el-tooltip
                        v-if="filTableRowCols3(index, scope.row)"
                        :content="
                          typeRadio === 1 ? $T('按比例分摊') : $T('按比例合并')
                        "
                        effect="light"
                        placement="right-end"
                      >
                        <omega-icon
                          symbolId="chart-pie"
                          class="tabTextBlue"
                          @click="
                            brainpowerClick_out(
                              scope,
                              productionPlan.addProductionPlanTable.data[
                                scope.$index
                              ]
                            )
                          "
                        />
                      </el-tooltip>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import common from "eem-utils/common";
import commonApi from "@/api/custom.js";
import Vue from "vue";
import { FullScreenLoading } from "@omega/http/loading.js";
const loading = new FullScreenLoading();
import TREE_PARAMS from "@/store/treeParams.js";
import { httping } from "@omega/http";
import { themeMap } from "cet-chart";
export default {
  name: "projectOverview",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    systemCfg() {
      return this.$store.state.systemCfg;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },

  data(vm) {
    const language = window.localStorage.getItem("omega_language") === "en";
    return {
      themeColors: [],
      treeNode: null,
      parentNode: null,
      // 图表类型选择
      chartTypeRadio: [1, 2],
      chartTypeList: [],
      // 分摊模式、合并模型
      typeRadio: 1,
      isShowRadio: false,
      typeList: [
        {
          name: $T("分摊模式"),
          label: 1
        },
        {
          name: $T("合并模式"),
          label: 2
        }
      ],
      // 图表配置
      CetChart_1: {
        inputData_in: {},
        config: {
          options: {
            toolbox: {
              top: 0,
              right: 30,
              feature: {
                saveAsImage: {
                  title: $T("保存为图片")
                }
              }
            },
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "shadow"
              },
              formatter: params => {
                const energytypeData = this.ElOption_1.options_in.find(
                  item => item.id === this.ElSelect_1.value
                );
                const unit = this._.get(
                  this.unitMap,
                  energytypeData.energytype,
                  {}
                );
                let str = params[0].name + "<br/>";
                params.forEach(item => {
                  str +=
                    item.marker +
                    `<span style="
                      width: 100%;
                      display: inline-block;">
                      ${item.seriesName}(${unit.uniten || "--"})：
                      <span style="
                        float: right;
                        margin-right: 15px;">
                        ${vm.finite(item.value) ? item.value : "--"}
                      </span>
                    </span><br/>`;
                });
                return str;
              }
            },
            grid: {
              top: "40",
              left: "40",
              right: "30",
              bottom: "10",
              containLabel: true
            },
            legend: {
              data: []
            },
            xAxis: [
              {
                type: "category",
                axisTick: {
                  alignWithLabel: true
                },
                name: "",
                data: []
              }
            ],
            yAxis: [
              {
                type: "value",
                name: "",
                position: "left",
                nameLocation: "end"
              }
            ],
            series: []
          }
        }
      },
      // 图表配置
      CetChart_2: {
        inputData_in: {},
        config: {
          options: {
            toolbox: {
              top: 0,
              right: 120,
              feature: {
                saveAsImage: {
                  title: $T("保存为图片")
                }
              }
            },
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "shadow"
              },
              formatter: params => {
                const energytypeData = this.ElOption_1.options_in.find(
                  item => item.id === this.ElSelect_1.value
                );
                const unit = this._.get(
                  this.unitMap,
                  energytypeData.energytype,
                  {}
                );
                let uniten = unit.uniten;
                let str = params[0].name + "<br/>";
                params.forEach((item, index) => {
                  if ([3, 4].includes(index)) {
                    uniten = "%";
                  }
                  str +=
                    item.marker +
                    `<span style="
                      width: 100%;
                      display: inline-block;">
                      ${item.seriesName}(${uniten || "--"})：
                      <span style="
                        float: right;
                        margin-right: 15px;">
                        ${vm.finite(item.value) ? item.value : "--"}
                      </span>
                    </span><br/>`;
                });
                return str;
              }
            },
            grid: {
              top: "40",
              left: "40",
              right: "40",
              bottom: "10",
              containLabel: true
            },
            legend: {
              data: []
            },
            xAxis: [
              {
                type: "category",
                axisTick: {
                  alignWithLabel: true
                },
                name: "",
                data: []
              }
            ],
            yAxis: [
              {
                type: "value",
                name: "",
                position: "left",
                nameLocation: "end"
              },
              {
                type: "value",
                name: $T("偏差（%）"),
                position: "right",
                nameLocation: "end"
              }
            ],
            series: []
          }
        }
      },
      // 生产计划与实绩跟踪
      productionPlan: {
        date: new Date(),
        tableType: 0,
        table: {
          config: {
            columns: [
              {
                name: $T("统计项目"),
                id: "key1"
              },
              {
                name: $T("统计项目"),
                id: "key2"
              },
              {
                name: $T("全年"),
                id: "key3"
              },
              {
                name: "1月",
                id: "key4"
              },
              {
                name: "2月",
                id: "key5"
              },
              {
                name: "3月",
                id: "key6"
              },
              {
                name: "4月",
                id: "key7"
              },
              {
                name: "5月",
                id: "key8"
              },
              {
                name: "6月",
                id: "key9"
              },
              {
                name: "7月",
                id: "key10"
              },
              {
                name: "8月",
                id: "key11"
              },
              {
                name: "9月",
                id: "key12"
              },
              {
                name: "10月",
                id: "key13"
              },
              {
                name: "11月",
                id: "key14"
              },
              {
                name: "12月",
                id: "key15"
              }
            ],
            showTabInpt: false
          },
          data: [],
          copyData: []
        },
        addProductionPlanTable: {
          data: [],
          copyData: []
        },
        // 采油对的信息
        parentData: []
      },
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out
        }
      },
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: $T("导出模板"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      CetButton_export1: {
        visible_in: true,
        disable_in: false,
        title: $T("导出数据"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_export1_statusTrigger_out
        }
      },
      CetButton_import1: {
        visible_in: true,
        disable_in: false,
        title: $T("从Excel导入"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_import1_statusTrigger_out
        }
      },
      CetButton_import2: {
        visible_in: true,
        disable_in: false,
        title: $T("从预测值导入"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_import2_statusTrigger_out
        }
      },
      CetButton_3: {
        visible_in: true,
        disable_in: false,
        title: $T("录入计划"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      ElSelect_1: {
        value: 0,
        style: {
          width: language ? "320px" : "200px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: 17,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [
          {
            id: 14,
            text: $T("月")
          },
          {
            id: 17,
            text: $T("年")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetDatePicker_1: {
        disable_in: false,
        val: "",
        config: {
          type: "year",
          format: language ? "yyyy" : "yyyy年",
          rangeSeparator: "~",
          size: "small",
          style: {
            display: "inline-block",
            width: "150px"
          },
          pickerOptions: {
            disabledDate(time) {
              // return time.getTime() > Date.now();
            }
          }
        }
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      // single表格组件
      CetTable_single: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: $T("合计")
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      // index组件
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        //  prop: "",      // 支持path a[0].b
        label: $T("序号"), //列名
        headerAlign: "left",
        align: "lftt",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: language ? "100px" : "50px" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      Columns_single: [
        {
          label: $T("时间"),
          prop: "logtime",
          minWidth: 100,
          showOverflowTooltip: true,
          headerAlign: "center",
          align: "center"
        },
        {
          label: `${$T("实际值")}（--）`,
          prop: "energyConsumption",
          minWidth: 100,
          showOverflowTooltip: true,
          headerAlign: "center",
          align: "center",
          formatter: common.formatTextCol()
        },
        {
          label: `${$T("计划值")}（--）`,
          prop: "energyConsumptionPlan",
          minWidth: 100,
          showOverflowTooltip: true,
          headerAlign: "center",
          align: "center"
        },
        {
          label: `${$T("计划偏差")}（%）`,
          prop: "planDeviation",
          minWidth: 100,
          showOverflowTooltip: true,
          headerAlign: "center",
          align: "center",
          custom: "tag"
        },
        {
          label: `${$T("预测值")}（--）`,
          prop: "totalEnergyConsumptionPredict",
          minWidth: 100,
          showOverflowTooltip: true,
          headerAlign: "center"
        },
        {
          label: `${$T("预测偏差")}（%）`,
          prop: "predictionDeviation",
          minWidth: 100,
          showOverflowTooltip: true,
          headerAlign: "center",
          align: "center",
          custom: "tag"
        }
      ],
      importUrl: "",
      showSingle: false,
      isInit: true,
      singleTableData: {},
      totalEnergyType: null,
      totalEnergyTypeC: null,
      totalEnergyNum: 0,
      deviation: 0,
      isDisable: false,
      unitMap: {},
      unitData: {},
      standUnit: {},
      energyRelation: {},
      coefList: [],
      isHasEdit: false
    };
  },
  watch: {
    "CetDatePicker_1.val": {
      handler: function (val, oldVal) {
        if (val) {
          this.productionPlan.date = val;
          this.init();
          this.productionPlanTableTypeClick_out(0);
        }
      },
      deep: true
    }
  },
  methods: {
    //过滤录入计划表格是否显示文本
    filTableRowCols1(index, row) {
      if (
        index === 0 ||
        index === 1 ||
        ![$T("今年能源计划"), $T("当月能源计划")].includes(
          this._.get(row, "key1.val")
        )
      ) {
        return true;
      } else {
        if (
          this._.get(row, "key2.key") === this.totalEnergyType ||
          this._.get(row, "key2.key") === this.totalEnergyTypeC
        ) {
          return true;
        }
        let isNew = false;
        if (this.ElSelect_2.value === 17) {
          if (
            this.$moment(this.CetDatePicker_1.val).isSame(
              this.$moment(),
              "year"
            )
          ) {
            isNew = true;
          }
        } else if (this.ElSelect_2.value === 14) {
          if (
            this.$moment(this.CetDatePicker_1.val).isSame(
              this.$moment(),
              "month"
            )
          ) {
            isNew = true;
          }
        }
        if (index !== 2 && isNew) {
          if (this.ElSelect_2.value === 17) {
            const month = this.$moment().month();
            if (index < month + 3) {
              return true;
            }
          } else if (this.ElSelect_2.value === 14) {
            const date = this.$moment().date();
            if (index < date + 2) {
              return true;
            }
          }
        }
        return false;
      }
    },
    //过滤录入计划表格是否显示输入框
    filTableRowCols2(index, row) {
      if (
        index !== 0 &&
        index !== 1 &&
        [$T("今年能源计划"), $T("当月能源计划")].includes(
          this._.get(row, "key1.val")
        )
      ) {
        if (
          this._.get(row, "key2.key") === this.totalEnergyType ||
          this._.get(row, "key2.key") === this.totalEnergyTypeC
        ) {
          return false;
        }
        let isNew = false;
        if (this.ElSelect_2.value === 17) {
          if (
            this.$moment(this.CetDatePicker_1.val).isSame(
              this.$moment(),
              "year"
            )
          ) {
            isNew = true;
          }
        } else if (this.ElSelect_2.value === 14) {
          if (
            this.$moment(this.CetDatePicker_1.val).isSame(
              this.$moment(),
              "month"
            )
          ) {
            isNew = true;
          }
        }
        if (index !== 2 && isNew) {
          if (this.ElSelect_2.value === 17) {
            const month = this.$moment().month();
            if (index < month + 3) {
              return false;
            }
          } else if (this.ElSelect_2.value === 14) {
            const date = this.$moment().date();
            if (index < date + 2) {
              return false;
            }
          }
        }
        return true;
      } else {
        return false;
      }
    },
    //过滤录入计划表格是否显示分摊或合并图标
    filTableRowCols3(index, row) {
      if (
        index === 2 &&
        [$T("今年能源计划"), $T("当月能源计划")].includes(
          this._.get(row, "key1.val")
        )
      ) {
        // if (this.$moment(this.CetDatePicker_1.val).isSame(this.$moment(), 'month')) {
        //   return false;
        // }
        if (
          this._.get(row, "key2.key") === this.totalEnergyType ||
          this._.get(row, "key2.key") === this.totalEnergyTypeC
        ) {
          return false;
        }
        return true;
      } else {
        return false;
      }
    },
    // 获取节点树
    async getTree() {
      var me = this;
      me.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      const params = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.powerEquipment,
        treeReturnEnable: true
      };
      await commonApi.getNodeTree(params).then(res => {
        if (res.code === 0) {
          const resData = res.data || [];
          me.CetGiantTree_1.inputData_in = resData;
          me.CetGiantTree_1.selectNode = resData[0];
        } else {
          me.CetGiantTree_1.inputData_in = [];
          me.CetGiantTree_1.selectNode = null;
        }
      });
    },
    // 获取能耗类型
    async queryProjectEnergyList_out() {
      var _this = this;
      const params = {
        projectId: this.projectId
      };
      await commonApi.queryProjectEnergyList(params).then(res => {
        if (res.code === 0) {
          //energytype===13，就是综合能耗
          let resData = res.data || [];
          var obj = resData.find(
            item => item.energytype === this.totalEnergyType
          );
          var objC = resData.find(
            item => item.energytype === this.totalEnergyTypeC
          );
          this.totalEnergyNum = 0;
          resData = resData.filter(
            item =>
              item.energytype !== this.totalEnergyType &&
              item.energytype !== this.totalEnergyTypeC
          );
          if (obj && objC) {
            this.totalEnergyNum = 3;
            resData.unshift(objC);
            resData.unshift(obj);
          } else if (obj) {
            this.totalEnergyNum = 1;
            resData.unshift(obj);
          } else if (objC) {
            this.totalEnergyNum = 2;
            resData.unshift(objC);
          }
          _this.ElOption_1.options_in = resData;
          _this.ElSelect_1.value = obj ? obj.id : null;
        } else {
          _this.ElOption_1.options_in = [];
          _this.ElSelect_1.value = null;
        }
      });
    },
    // title选择
    titleRadioIndexChange_out(index) {
      if (!this.treeNode) {
        return;
      }
      // 根据选择的不同赋予不同的初始值
      if (index === 0) {
        this.CetDatePicker_1.val = this.productionPlan.date;
        this.CetButton_3.visible_in = true;
        this.productionPlan.tableType = 0;
        this.chartTypeList = [
          {
            name: $T("预测值"),
            label: 1
          },
          {
            name: $T("计划值"),
            label: 2
          }
        ];
        this.productionPlan.table.config.showTabInpt = false;
      }
      // 默认选中第一个图表类型
      this.chartTypeRadio = [1, 2];
      const seletVal = this._.get(this.ElOption_1.options_in, "[0].id", null);
      this.ElSelect_1.value = seletVal;
      this.ElSelect_1_change_out(seletVal);
    },
    //初始化
    init() {
      if (!this.treeNode) {
        return;
      }
      if (this.isInit) {
        return;
      }
      let nextDate = null;
      if (this.ElSelect_2.value === 17) {
        nextDate = this.$moment(this.CetDatePicker_1.val).add(1, "y").valueOf();
      } else if (this.ElSelect_2.value === 14) {
        nextDate = this.$moment(this.CetDatePicker_1.val).add(1, "M").valueOf();
      }
      if (nextDate >= this.$moment().valueOf()) {
        // this.CetButton_next.disable_in = true;
        this.isDisable = true;
      } else {
        this.isDisable = false;
        // this.CetButton_next.disable_in = false;
      }
      // 根据选择的不同赋予不同的初始值
      this.productionPlan.tableType = 0;
      const energytypeData = this.ElOption_1.options_in.find(
        item => item.id === this.ElSelect_1.value
      );
      if (
        energytypeData &&
        [this.totalEnergyType, this.totalEnergyTypeC].includes(
          energytypeData.energytype
        )
      ) {
        this.showSingle = false;
        this.$nextTick(() => {
          this.queryEnergyComposite_out();
        });
      } else {
        this.showSingle = true;
        this.getsingleEnergy();
      }
    },

    //过滤表格
    filTable(columnList) {
      if (!Array.isArray(columnList)) {
        return;
      }
      let columns = [];
      const tableData = [];
      const addProductionPlanTable = [];
      if (this.ElSelect_2.value === 14) {
        columns = [
          {
            name: $T("统计项目"),
            id: "key1"
          },
          {
            name: $T("统计项目"),
            id: "key2"
          },
          {
            name: `${this.$moment(this.CetDatePicker_1.val).format("M")}${$T(
              "月"
            )}`,
            id: "key3"
          }
        ];
        const dayNum = columnList.length;
        for (var i = 0; i < dayNum; i++) {
          columns.push({
            name: `${this.$moment(columnList[i].time).format("DD")}${$T("日")}`,
            id: `key${4 + i}`,
            dateName: this.$moment(columnList[i].time).format("DD")
          });
        }

        const totleType = [
          $T("当月实际能耗"),
          $T("当月能源计划"),
          $T("当月能源预测"),
          $T("上月同期能耗")
        ];
        const list = this.ElOption_1.options_in;
        totleType.forEach(key => {
          list.forEach(item => {
            const cols = {
              key1: {
                val: `${key}`
              },
              key2: {
                val: `${item.name}(${item.symbolCn})`,
                key: item.energytype
              },
              key3: {
                val: null,
                isBlue: false
              }
            };
            for (var j = 0; j < dayNum; j++) {
              cols[`key${4 + j}`] = {
                val: null,
                isBlue: false
              };
            }
            tableData.push(cols);
            if ([$T("当月能源计划"), $T("当月能源预测")].includes(key)) {
              addProductionPlanTable.push(cols);
            }
          });
        });
      } else {
        columns = [
          {
            name: $T("统计项目"),
            id: "key1"
          },
          {
            name: $T("统计项目"),
            id: "key2"
          },
          {
            name: `${this.$moment(this.CetDatePicker_1.val).format("YYYY")}${$T(
              "年"
            )}`,
            id: "key3"
          }
        ];
        const monthNum = columnList.length;
        for (var ii = 0; ii < monthNum; ii++) {
          columns.push({
            name: `${this.$moment(columnList[ii].time).format("MM")}${$T(
              "月"
            )}`,
            id: `key${4 + ii}`,
            dateName: this.$moment(columnList[ii].time).format("MM")
          });
        }

        const totleType = [
          $T("今年实际能耗"),
          $T("今年能源计划"),
          $T("今年能源预测"),
          $T("去年同期能耗")
        ];
        const list = this.ElOption_1.options_in;
        totleType.forEach(key => {
          list.forEach(item => {
            const cols = {
              key1: {
                val: `${key}`
              },
              key2: {
                val: `${item.name}(${item.symbol})`,
                key: item.energytype
              },
              key3: {
                val: null,
                isBlue: false
              }
            };
            for (var jj = 0; jj < monthNum; jj++) {
              cols[`key${4 + jj}`] = {
                val: null,
                isBlue: false
              };
            }
            tableData.push(cols);
            if ([$T("今年能源计划"), $T("今年能源预测")].includes(key)) {
              addProductionPlanTable.push(cols);
            }
          });
        });
      }
      this.productionPlan.table.config.columns = columns;
      this.productionPlan.table.data = tableData;
      this.productionPlan.addProductionPlanTable.data = this._.cloneDeep(
        addProductionPlanTable
      );
      this.productionPlan.addProductionPlanTable.copyData = this._.cloneDeep(
        addProductionPlanTable
      );
    },
    // 图表类型选择
    chartTypeRadioChange_out(val, selectNode) {
      const energytypeData = this.ElOption_1.options_in.find(
        item => item.id === this.ElSelect_1.value
      );
      if (
        energytypeData &&
        [this.totalEnergyType, this.totalEnergyTypeC].includes(
          energytypeData.energytype
        )
      ) {
        val = 0;
        if (
          this.totalEnergyNum === 3 &&
          energytypeData.energytype === this.totalEnergyTypeC
        ) {
          val = 1;
        }
        const list = this.ElOption_1.options_in;
        const len = list.length;
        this.initTabColor(
          this.productionPlan.table.data,
          [val, val + len, val + len * 2, val + len * 3],
          "isBlue"
        );
        this.initEchart(this.productionPlan.table.data, [
          val,
          val + len,
          val + len * 2,
          val + len * 3
        ]);
        this.exceedTheStandardPudge(this.productionPlan.table.data, val);
      } else {
        this.filSingleTable(this.singleTableData);
      }
    },
    // 分摊模式和合并模式切换
    typeRadioChange_out(val, selectNode) {},
    // 更新表格选中状态
    initTabColor(tableData, arr, color) {
      tableData.forEach(item => {
        Object.keys(item)
          .slice(1)
          .forEach(ite => {
            Vue.set(item[ite], color, false);
          });
      });
      if (arr.length > 0) {
        arr.forEach((item, index) => {
          Object.keys(tableData[item])
            .slice(1)
            .forEach(ite => {
              const colors = ["isBlue", "isBlue2", "isBlue3", "isBlue4"];
              Vue.set(tableData[item][ite], colors[index], true);
            });
        });
      }
    },
    // 生产计划更新图表内容
    initEchart(tableData, arr) {
      const energytypeData = this.ElOption_1.options_in.find(
        item => item.id === this.ElSelect_1.value
      );
      const unit = this._.get(this.unitMap, energytypeData.energytype, {});
      if (unit) {
        this.CetChart_1.config.options.yAxis[0].name = `${
          unit.typeName || "--"
        }(${unit.uniten || "--"})`;
      }
      let legendData = [];
      if (this.ElSelect_2.value === 17) {
        legendData = [
          $T("实际值"),
          $T("预测值"),
          $T("去年同期"),
          $T("今年计划")
        ];
      } else {
        legendData = [
          $T("实际值"),
          $T("预测值"),
          $T("上月同期"),
          $T("本月计划")
        ];
      }
      this.CetChart_1.config.options.legend.data = legendData;
      const columns = this.productionPlan.table.config.columns;
      this.CetChart_1.config.options.xAxis[0].data = columns
        .slice(3)
        .map(item => {
          return item.dateName;
        });
      this.CetChart_1.config.options.series = [
        {
          name: legendData[0],
          type: "bar",
          yAxisIndex: 0,
          data: Object.keys(tableData[arr[0]])
            .slice(3)
            .map(item => {
              return tableData[arr[0]][item].val;
            }),
          // itemStyle: {
          //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //     { offset: 0, color: "#1D78ED" },
          //     { offset: 1, color: "#1D78ED" }
          //   ])
          // },
          barWidth: "20%"
        },
        {
          name: legendData[1],
          type: "bar",
          yAxisIndex: 0,
          data: Object.keys(tableData[arr[2]])
            .slice(3)
            .map(item => {
              return tableData[arr[2]][item].val;
            }),
          // itemStyle: {
          //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //     { offset: 0, color: "#5DE27C" },
          //     { offset: 1, color: "#5DE27C" }
          //   ])
          // },
          barWidth: "20%"
        },
        {
          name: legendData[2],
          type: "bar",
          yAxisIndex: 0,
          data: Object.keys(tableData[arr[3]])
            .slice(3)
            .map(item => {
              return tableData[arr[3]][item].val;
            }),
          // itemStyle: {
          //   color: "#9E99F8"
          // },
          barWidth: "20%"
        },
        {
          name: legendData[3],
          type: "line",
          yAxisIndex: 0,
          data: Object.keys(tableData[arr[1]])
            .slice(3)
            .map(item => {
              return tableData[arr[1]][item].val;
            })
          // itemStyle: {
          //   color: "#FFA32B"
          // }
        }
      ];
      if (this.chartTypeRadio.length === 0) {
        this.CetChart_1.config.options.series.splice(3, 1);
        this.CetChart_1.config.options.series.splice(1, 1);
      } else if (this.chartTypeRadio.length === 1) {
        this.chartTypeRadio.forEach(item => {
          if (Number(item) === 1) {
            this.CetChart_1.config.options.series.splice(3, 1);
          } else if (Number(item) === 2) {
            this.CetChart_1.config.options.series.splice(1, 1);
          }
        });
      }
      this.$nextTick(() => {
        this.$refs.CetChart_1.mergeOptions(
          this.CetChart_1.config.options,
          true
        );
      });
    },
    // 能耗预测与实际能耗
    //获取全部数据
    queryEnergyComposite_out() {
      const _this = this;
      const list = _this.ElOption_1.options_in;
      const idArr = list.map(item => item.energytype);
      const len = list.length;
      const timeType = this.ElSelect_2.value;
      let startTime = null;
      let endTime = null;
      if (timeType === 17) {
        startTime = this.$moment(this.productionPlan.date)
          .startOf("year")
          .valueOf();
        endTime =
          this.$moment(this.productionPlan.date).endOf("year").valueOf() + 1;
      } else {
        startTime = this.$moment(this.productionPlan.date)
          .startOf("month")
          .valueOf();
        endTime =
          this.$moment(this.productionPlan.date).endOf("month").valueOf() + 1;
      }
      var params = [
        {
          startTime: startTime,
          endTime: endTime,
          aggregationCycle: this.ElSelect_2.value,
          energyTypeList: idArr,
          node: {
            id: this.treeNode.id,
            modelLabel: this.treeNode.modelLabel,
            name: this.treeNode.name
          }
        }
      ];
      commonApi.queryEnergyComposite(params).then(res => {
        // 处理今年计划
        if (res.code === 0) {
          const unitMap = _this._.get(res, "data.unitMap", {}) || {};
          const energyResultsPlan =
            _this._.get(res, "data.energyResultsPlan[0].datas[0].data", []) ||
            [];
          let energyResultsNew =
            _this._.get(res, "data.energyResults[0].datas[0].data", []) || [];
          let energyResultsOld =
            _this._.get(res, "data.energyResults[0].datas[1].data", []) || [];
          const energyResultsPredict =
            _this._.get(
              res,
              "data.energyResultsPredict[0].datas[0].data",
              []
            ) || [];
          if (
            _this._.get(res, "data.energyResults[0].datas[0].time", null) <
            _this._.get(res, "data.energyResults[0].datas[1].time", null)
          ) {
            energyResultsNew =
              _this._.get(res, "data.energyResults[0].datas[1].data", []) || [];
            energyResultsOld =
              _this._.get(res, "data.energyResults[0].datas[0].data", []) || [];
          }
          const columnList =
            _this._.get(energyResultsPlan, "[0].data", []) || [];
          this.filTable(columnList);
          this.unitMap = this._.cloneDeep(unitMap);
          const unitData = {};
          for (var i = 0; i < len; i++) {
            const energyDataPlan = energyResultsPlan.find(
              item => item.energyType === list[i].energytype
            );
            const energyDataNew = energyResultsNew.find(
              item => item.energyType === list[i].energytype
            );
            const energyDataOld = energyResultsOld.find(
              item => item.energyType === list[i].energytype
            );
            const energyDataPredict = energyResultsPredict.find(
              item => item.energyType === list[i].energytype
            );
            unitData[`${list[i].energytype}`] = this._.get(
              unitMap,
              `${list[i].energytype}.uniten`,
              null
            );
            // 今年能源计划
            if (energyDataPlan) {
              const unit = this._.get(unitMap, `${list[i].energytype}`, {});
              if (unit) {
                this.productionPlan.table.data[len + i].key2.val = `${
                  unit.typeName || "--"
                }(${unit.uniten || "--"})`;
              }
              let totalVal = _this._.get(
                energyDataPlan,
                "amountData.value",
                null
              );
              totalVal = this.filNumValue(totalVal);
              this.productionPlan.table.data[len + i].key3.val = totalVal;
              energyDataPlan.data &&
                energyDataPlan.data.forEach((item, index) => {
                  if (
                    _this.productionPlan.table.data[len + i][
                      "key" + (index + 4)
                    ]
                  ) {
                    _this.productionPlan.table.data[len + i][
                      "key" + (index + 4)
                    ].val = this.filNumValue(item.value);
                  }
                });
            }
            //今年实际能耗
            if (energyDataNew) {
              const unit = this._.get(unitMap, `${list[i].energytype}`, {});
              if (unit) {
                this.productionPlan.table.data[i].key2.val = `${
                  unit.typeName || "--"
                }(${unit.uniten || "--"})`;
              }
              let totalVal = _this._.get(
                energyDataNew,
                "amountData.value",
                null
              );
              totalVal = this.filNumValue(totalVal);
              this.productionPlan.table.data[i].key3.val = totalVal;
              energyDataNew.data &&
                energyDataNew.data.forEach((item, index) => {
                  if (_this.productionPlan.table.data[i]["key" + (index + 4)]) {
                    _this.productionPlan.table.data[i][
                      "key" + (index + 4)
                    ].val = this.filNumValue(item.value);
                  }
                });
            }
            //去年同期能耗
            if (energyDataOld) {
              const unit = this._.get(unitMap, `${list[i].energytype}`, {});
              if (unit) {
                this.productionPlan.table.data[3 * len + i].key2.val = `${
                  unit.typeName || "--"
                }(${unit.uniten || "--"})`;
              }
              let totalVal = _this._.get(
                energyDataOld,
                "amountData.value",
                null
              );
              totalVal = this.filNumValue(totalVal);
              this.productionPlan.table.data[3 * len + i].key3.val = totalVal;
              energyDataOld.data &&
                energyDataOld.data.forEach((item, index) => {
                  if (
                    _this.productionPlan.table.data[3 * len + i][
                      "key" + (index + 4)
                    ]
                  ) {
                    _this.productionPlan.table.data[3 * len + i][
                      "key" + (index + 4)
                    ].val = this.filNumValue(item.value);
                  }
                });
            }
            //今年能源预测
            if (energyDataPredict) {
              const unit = this._.get(unitMap, `${list[i].energytype}`);
              if (unit) {
                this.productionPlan.table.data[2 * len + i].key2.val = `${
                  unit.typeName || "--"
                }(${unit.uniten || "--"})`;
              }
              let totalVal = _this._.get(
                energyDataPredict,
                "amountData.value",
                null
              );
              totalVal = this.filNumValue(totalVal);
              this.productionPlan.table.data[2 * len + i].key3.val = totalVal;
              energyDataPredict.data &&
                energyDataPredict.data.forEach((item, index) => {
                  if (
                    _this.productionPlan.table.data[2 * len + i][
                      "key" + (index + 4)
                    ]
                  ) {
                    _this.productionPlan.table.data[2 * len + i][
                      "key" + (index + 4)
                    ].val = this.filNumValue(item.value);
                  }
                });
            }
          }
          this.unitData = this._.cloneDeep(unitData);
          this.chartTypeRadioChange_out(this.chartTypeRadio);
          this.$nextTick(() => {
            this.$refs.elTab1.doLayout();
            this.$refs.elTab2.doLayout();
          });
        }
      });
    },
    // 查询单一能耗
    getsingleEnergy() {
      const _this = this;
      const energytypeData = this.ElOption_1.options_in.find(
        item => item.id === this.ElSelect_1.value
      );
      if (!energytypeData) {
        return;
      }
      const timeType = this.ElSelect_2.value;
      let startTime = null;
      let endTime = null;
      if (timeType === 17) {
        startTime = this.$moment(this.productionPlan.date)
          .startOf("year")
          .valueOf();
        endTime =
          this.$moment(this.productionPlan.date).endOf("year").valueOf() + 1;
      } else {
        startTime = this.$moment(this.productionPlan.date)
          .startOf("month")
          .valueOf();
        endTime =
          this.$moment(this.productionPlan.date).endOf("month").valueOf() + 1;
      }
      var data = {
        startTime: startTime,
        endTime: endTime,
        aggregationCycle: this.ElSelect_2.value,
        energyType: energytypeData.energytype,
        id: this.treeNode.id,
        modelLabel: this.treeNode.modelLabel
      };
      httping({
        url: "/eem-service/v1/energyConsumptionPredict/single",
        method: "POST",
        data
      }).then(response => {
        if (response.code === 0) {
          const unit = _this._.get(response, "data.unit", {}) || {};
          this.unitMap = {};
          this.unitMap[data.energyType] = unit;
          const resData = response.data;
          _this.singleTableData = _this._.cloneDeep(resData);
          _this.filSingleTable(resData);
        }
      });
    },
    //过滤单一能耗表格，图表数据
    filSingleTable(data) {
      data = data || {};
      const times = data.times || [];
      const energyConsumptionPlans = data.energyConsumptionPlans || []; //计划能耗的list
      const energyConsumptions = data.energyConsumptions || []; //实际能耗的list
      const planDeviations = data.planDeviations || []; //计划偏差
      const predictionDeviations = data.predictionDeviations || []; //预测偏差
      const totalEnergyConsumptionPredicts =
        data.totalEnergyConsumptionPredicts || []; //预测能耗
      const tableData = [];
      const timeType = this.ElSelect_2.value;
      const xAxisData = [];
      const seriesData1 = []; //计划能耗的list
      const seriesData2 = []; //实际能耗的list
      const seriesData3 = []; //计划偏差
      const seriesData4 = []; //预测偏差
      const seriesData5 = []; //预测能耗
      times.forEach((item, index) => {
        const obj = {};
        obj.logtime = item;
        if (timeType === 17) {
          xAxisData.push(this.$moment(item).format("MM"));
        } else {
          xAxisData.push(this.$moment(item).format("DD"));
        }
        const obj1 = energyConsumptionPlans.find(
          item1 => item1 && item1.logtime === item
        );
        const obj2 = energyConsumptions.find(
          item2 => item2 && item2.logtime === item
        );
        const obj3Val = planDeviations[index];
        const obj4Val = predictionDeviations[index];
        const obj5 = totalEnergyConsumptionPredicts.find(
          item5 => item5 && item5.logtime === item
        );

        if (!obj1) {
          obj.energyConsumptionPlan = null;
          seriesData1.push(null);
        } else {
          const value1 = this.filNumValue(obj1.value);
          obj.energyConsumptionPlan = value1;
          seriesData1.push(value1);
        }
        if (!obj2) {
          obj.energyConsumption = null;
          seriesData2.push(null);
        } else {
          const value2 = this.filNumValue(obj2.total);
          obj.energyConsumption = value2;
          seriesData2.push(value2);
        }
        if ([null, NaN, undefined].includes(obj3Val)) {
          obj.planDeviation = null;
          seriesData3.push(null);
        } else {
          const value3 = this.filNumValue(obj3Val);
          obj.planDeviation = value3;
          seriesData3.push(value3);
        }
        if ([null, NaN, undefined].includes(obj4Val)) {
          obj.predictionDeviation = null;
          seriesData4.push(null);
        } else {
          const value4 = this.filNumValue(obj4Val);
          obj.predictionDeviation = value4;
          seriesData4.push(value4);
        }
        if (!obj5 || [null, NaN, undefined].includes(obj5.value)) {
          obj.totalEnergyConsumptionPredict = null;
          seriesData5.push(null);
        } else {
          const value5 = this.filNumValue(obj5.value);
          obj.totalEnergyConsumptionPredict = value5;
          seriesData5.push(value5);
        }
        tableData.push(obj);
      });
      console.log(tableData);
      this.Columns_single = [
        {
          label: $T("时间"),
          prop: "logtime",
          minWidth: 100,
          showOverflowTooltip: true,
          headerAlign: "center",
          align: "center",
          formatter:
            this.ElSelect_2.value === 17
              ? common.formatDateCol("YYYY-MM")
              : common.formatDateCol("YYYY-MM-DD")
        },
        {
          label: `${$T("实际值")}（${this._.get(data, "unit.uniten", "--")}）`,
          prop: "energyConsumption",
          minWidth: this.language ? "300px" : "100px",
          showOverflowTooltip: true,
          headerAlign: "center",
          align: "center",
          formatter: common.formatTextCol()
        },
        {
          label: `${$T("计划值")}（${this._.get(data, "unit.uniten", "--")}）`,
          prop: "energyConsumptionPlan",
          minWidth: this.language ? "340px" : "100px",
          showOverflowTooltip: true,
          headerAlign: "center",
          align: "center",
          formatter: common.formatTextCol()
        },
        {
          label: `${$T("计划偏差")}（%）`,
          prop: "planDeviation",
          minWidth: this.language ? "200px" : "100px",
          showOverflowTooltip: true,
          headerAlign: "center",
          align: "center",
          custom: "tag",
          formatter: common.formatTextCol(),
          colorTypeFormatter: this.statusColorTypeFormatter
        },
        {
          label: `${$T("预测值")}（${this._.get(data, "unit.uniten", "--")}）`,
          prop: "totalEnergyConsumptionPredict",
          minWidth: this.language ? "340px" : "100px",
          showOverflowTooltip: true,
          headerAlign: "center",
          align: "center",
          formatter: common.formatTextCol()
        },
        {
          label: `${$T("预测偏差")}（%）`,
          prop: "predictionDeviation",
          minWidth: this.language ? "200px" : "100px",
          showOverflowTooltip: true,
          headerAlign: "center",
          align: "center",
          custom: "tag",
          formatter: common.formatTextCol(),
          colorTypeFormatter: this.statusColorTypeFormatter
        }
      ];
      this.CetTable_single.data = this._.cloneDeep(tableData);

      this.CetChart_2.config.options.legend.data = [
        $T("实际值"),
        $T("预测值"),
        $T("计划值"),
        $T("预测偏差"),
        $T("计划偏差")
      ];
      this.CetChart_2.config.options.yAxis[0].name = `${this._.get(
        data,
        "unit.typeName",
        "--"
      )}(${this._.get(data, "unit.uniten", "--")})`;
      this.CetChart_2.config.options.xAxis[0].data = xAxisData;
      this.CetChart_2.config.options.series = [
        {
          name: $T("实际值"),
          type: "bar",
          yAxisIndex: 0,
          data: seriesData2
          // itemStyle: {
          //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //     { offset: 0, color: "#1D78ED" },
          //     { offset: 1, color: "#1D78ED" }
          //   ])
          // }
        },
        {
          name: $T("预测值"),
          type: "bar",
          yAxisIndex: 0,
          data: seriesData5
          // itemStyle: {
          //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //     { offset: 0, color: "#5DE27C" },
          //     { offset: 1, color: "#5DE27C" }
          //   ])
          // }
        },
        {
          name: $T("计划值"),
          type: "bar",
          yAxisIndex: 0,
          data: seriesData1
          // itemStyle: {
          //   color: "#F2E38C"
          // }
        },
        {
          name: $T("预测偏差"),
          type: "line",
          yAxisIndex: 1,
          data: seriesData4
          // itemStyle: {
          //   color: "#EC7DFF"
          // }
        },
        {
          name: $T("计划偏差"),
          type: "line",
          yAxisIndex: 1,
          data: seriesData3
          // itemStyle: {
          //   color: "#FF7272"
          // }
        }
      ];
      if (this.chartTypeRadio.length === 0) {
        this.CetChart_2.config.options.series.splice(1);
      } else if (this.chartTypeRadio.length === 1) {
        this.chartTypeRadio.forEach(item => {
          if (Number(item) === 1) {
            this.CetChart_2.config.options.series.splice(4, 1);
            this.CetChart_2.config.options.series.splice(2, 1);
          } else if (Number(item) === 2) {
            this.CetChart_2.config.options.series.splice(3, 1);
            this.CetChart_2.config.options.series.splice(1, 1);
          }
        });
      }
      this.$nextTick(() => {
        this.$refs.CetChart_2.mergeOptions(
          this.CetChart_2.config.options,
          true
        );
        this.$refs.elTab3.$refs.cetTable.doLayout();
      });
    },
    // 事件等级标签样式格式化
    statusColorTypeFormatter(row, column) {
      const cellValue = row[column.property];
      if (![null, NaN, undefined].includes(cellValue)) {
        if (Math.abs(cellValue) > this.deviation) {
          return "red";
        }
      }
      return null;
    },
    //返回计划录入
    backAddProductionPlan_out(val) {
      this.init();
      this.productionPlanTableTypeClick_out(0);
    },
    // 计划录入
    preservationAddProductionPlan_out(val) {
      var dataArr = this.productionPlan.addProductionPlanTable.data;
      const list = this.ElOption_1.options_in;
      const singleData = {};
      const totalData = {};
      list.forEach((item, index) => {
        singleData[list[index].energytype] = Object.keys(dataArr[index])
          .splice(3)
          .map(item => this.filNumValue11(dataArr[index][item].val, 1));
        totalData[list[index].energytype] = this.filNumValue11(
          dataArr[index].key3.val,
          1
        );
      });
      const timeType = this.ElSelect_2.value;
      let startTime = null;
      let endTime = null;
      if (timeType === 17) {
        startTime = this.$moment(this.productionPlan.date)
          .startOf("year")
          .valueOf();
        endTime =
          this.$moment(this.productionPlan.date).endOf("year").valueOf() + 1;
      } else {
        startTime = this.$moment(this.productionPlan.date)
          .startOf("month")
          .valueOf();
        endTime =
          this.$moment(this.productionPlan.date).endOf("month").valueOf() + 1;
      }
      const idArr = list.map(item => item.energytype);
      var params = {
        types: idArr,
        unitMap: this.standUnit,
        modelLabel: "energyconsumeplan",
        startTime: startTime,
        endTime: endTime,
        aggregationCycles: {
          totalData: this.ElSelect_2.value,
          singleData: this.ElSelect_2.value === 17 ? 14 : 12
        },
        node: {
          id: this.treeNode.id,
          modelLabel: this.treeNode.modelLabel
        },
        singleData: singleData,
        totalData: totalData
      };
      commonApi.queryEnergyPlan(params).then(res => {
        if (res.code === 0) {
          this.$message({
            type: "success",
            message: $T("保存成功")
          });
          this.productionPlan.tableType = 0;
          // this.CetDatePicker_1.val = this._.cloneDeep(this.CetDatePicker_1.val);
          this.init();
        }
      });
    },
    //切换录入计划还是显示计划
    productionPlanTableTypeClick_out(val) {
      // 在生产计划与实绩跟踪点击添加明年计划时如果今年计划的编辑状态是打开的则需要关闭，并数据还原
      if (this.productionPlan.table.config.showTabInpt) {
        this.productionPlan.table.config.showTabInpt = false;
        this.productionPlan.table.data = JSON.parse(
          JSON.stringify(this.productionPlan.table.copyData)
        );
        this.CetButton_3.visible_in = true;
      }
      this.productionPlan.tableType = val;
    },
    finite(val) {
      return isFinite(String(val) || parseInt(String(val)));
    },
    // 判断是否越限
    exceedTheStandardPudge(data, typeIndex = 0) {
      const monthKeys = Object.keys(data[0]).slice(2);
      const list = this.ElOption_1.options_in;
      const len = list.length;
      // 超标判断
      // if (let i = 0; i < len; i++) {
      //   const monthKeys = Object.keys(data[0]).slice(3);
      // }
      const standard = this._.cloneDeep(data.slice(len, 2 * len));
      data.forEach((item, index) => {
        monthKeys.forEach((ite, inde) => {
          if (index < len) {
            if (Number(item[ite].val) > Number(standard[index][ite].val)) {
              // 统计当前选中的能源类型数据
              if (index === typeIndex) {
                if (this.finite(standard[index][ite].val)) {
                  Vue.set(item[ite], "isRed", true);
                } else {
                  Vue.set(item[ite], "isRed", false);
                }
              } else {
                if (this.finite(standard[index][ite].val)) {
                  Vue.set(item[ite], "isRed1", true);
                } else {
                  Vue.set(item[ite], "isRed", false);
                }
              }
            } else {
              Vue.set(item[ite], "isRed", false);
            }
          }
        });
      });
    },
    calc(num1, num2, num3) {
      return common.setNumFixed(
        (num1 * 100 - num2 * 100 + num3 * 100) / 100,
        2
      );
    },
    //导出按钮点击
    CetButton_1_statusTrigger_out(val) {
      const list = this.ElOption_1.options_in;
      const idArr = list.map(item => item.energytype);
      const timeType = this.ElSelect_2.value;
      let startTime = null;
      let endTime = null;
      if (timeType === 17) {
        startTime = this.$moment(this.productionPlan.date)
          .startOf("year")
          .valueOf();
        endTime =
          this.$moment(this.productionPlan.date).endOf("year").valueOf() + 1;
      } else {
        startTime = this.$moment(this.productionPlan.date)
          .startOf("month")
          .valueOf();
        endTime =
          this.$moment(this.productionPlan.date).endOf("month").valueOf() + 1;
      }
      const energytypeData = this.ElOption_1.options_in.find(
        item => item.id === this.ElSelect_1.value
      );
      let params = null;
      let url = null;
      if (
        energytypeData &&
        [this.totalEnergyType, this.totalEnergyTypeC].includes(
          energytypeData.energytype
        )
      ) {
        params = [
          {
            startTime: startTime,
            endTime: endTime,
            aggregationCycle: this.ElSelect_2.value,
            energyTypeList: idArr,
            node: {
              id: this.treeNode.id,
              modelLabel: this.treeNode.modelLabel,
              name: this.treeNode.name
            }
          }
        ];
        url =
          "/eem-service/v1/energyConsumptionPredict/CompositeConsumption/export";
      } else {
        params = {
          startTime: startTime,
          endTime: endTime,
          aggregationCycle: this.ElSelect_2.value,
          energyType: energytypeData.energytype,
          id: this.treeNode.id,
          modelLabel: this.treeNode.modelLabel,
          colourNum: this.deviation
        };
        url =
          "/eem-service/v1/energyConsumptionPredict/exportSingleConsumption";
      }
      common.downExcel(url, params, this.token, this.projectId);
    },
    //导出模板
    CetButton_export_statusTrigger_out(val) {
      this.exportExcel(true);
    },
    //导出数据
    CetButton_export1_statusTrigger_out(val) {
      this.exportExcel(false);
    },
    //导出Excel
    exportExcel(type = true) {
      if (!this.treeNode) {
        this.$message.warning($T("暂无节点信息"));
        return;
      }
      const url = `/eem-service/v1/energy/plan/export?exportTemplate=${type}`;
      const timeType = this.ElSelect_2.value;
      let startTime = null;
      let endTime = null;
      if (timeType === 17) {
        startTime = this.$moment(this.productionPlan.date)
          .startOf("year")
          .valueOf();
        endTime =
          this.$moment(this.productionPlan.date).endOf("year").valueOf() + 1;
      } else {
        startTime = this.$moment(this.productionPlan.date)
          .startOf("month")
          .valueOf();
        endTime =
          this.$moment(this.productionPlan.date).endOf("month").valueOf() + 1;
      }
      const list = this.ElOption_1.options_in;
      const idArr = list.map(item => item.energytype);
      var params = {
        types: idArr,
        unitMap: this.standUnit,
        startTime: startTime,
        endTime: endTime,
        aggregationCycles: {
          totalData: timeType,
          singleData: timeType === 17 ? 14 : timeType === 14 ? 12 : null
        },
        node: {
          id: this.treeNode.id,
          name: this.treeNode.name,
          modelLabel: this.treeNode.modelLabel
        },
        modelLabel: "energyconsumeplan"
      };

      common.downExcel(url, params, this.token, this.projectId);
    },
    //从Excel导入
    CetButton_import1_statusTrigger_out(val) {
      this.importUrl = `/eem-service/v1/energy/plan/import?modelLable=energyconsumeplan`;
      this.$refs.uploadBtn.click();
    },
    //从预测值导入
    CetButton_import2_statusTrigger_out(val) {
      this.productionPlan.addProductionPlanTable.data = this._.cloneDeep(
        this.productionPlan.addProductionPlanTable.copyData
      );
    },
    // 查询今年能源计划，标准单位值
    queryEnergyData_out(callback) {
      const _this = this;
      const list = _this.ElOption_1.options_in;
      const idArr = list.map(item => item.energytype);
      const len = list.length;
      const timeType = this.ElSelect_2.value;
      let startTime = null;
      let endTime = null;
      if (timeType === 17) {
        startTime = this.$moment(this.productionPlan.date)
          .startOf("year")
          .valueOf();
        endTime =
          this.$moment(this.productionPlan.date).endOf("year").valueOf() + 1;
      } else {
        startTime = this.$moment(this.productionPlan.date)
          .startOf("month")
          .valueOf();
        endTime =
          this.$moment(this.productionPlan.date).endOf("month").valueOf() + 1;
      }
      var params = {
        unitMap: this.unitData,
        types: idArr,
        startTime: startTime,
        endTime: endTime,
        aggregationCycles: {
          singleData: timeType === 17 ? 14 : 12,
          totalData: timeType
        },
        node: {
          id: this.treeNode.id,
          modelLabel: this.treeNode.modelLabel
        },
        modelLabel: "energyconsumeplan"
      };
      commonApi.queryEnergyData(params).then(response => {
        // 处理今年计划
        if (response.code === 0) {
          const singleData = _this._.get(response, "data.singleData", {}) || {};
          const totalData = _this._.get(response, "data.totalData", {}) || {};
          _this.standUnit = _this._.get(response, "data.unitMap", {}) || {};
          _this.energyRelation =
            _this._.get(response, "data.energyRelation", {}) || {};
          for (var i = 0; i < len; i++) {
            const typeName = _this._.get(
              _this.energyRelation,
              `${list[i].energytype}`,
              null
            );
            const unit = _this._.get(
              _this.standUnit,
              `${list[i].energytype}`,
              null
            );
            _this.productionPlan.addProductionPlanTable.data[i].key2.val = `${
              typeName || "--"
            }(${unit || "--"})`;
            const totalVal =
              _this._.get(totalData, list[i].energytype, null) || null;
            this.productionPlan.addProductionPlanTable.data[i].key3.val =
              this.filNumValue(totalVal);
            const singleDataList =
              _this._.get(singleData, list[i].energytype, []) || [];
            singleDataList.forEach((item, index) => {
              if (
                _this.productionPlan.addProductionPlanTable.data[i][
                  "key" + (index + 4)
                ]
              ) {
                _this.productionPlan.addProductionPlanTable.data[i][
                  "key" + (index + 4)
                ].val = this.filNumValue(item);
                let isNew = false;
                if (this.ElSelect_2.value === 17) {
                  if (
                    this.$moment(this.CetDatePicker_1.val).isSame(
                      this.$moment(),
                      "year"
                    )
                  ) {
                    isNew = true;
                  }
                } else if (this.ElSelect_2.value === 14) {
                  if (
                    this.$moment(this.CetDatePicker_1.val).isSame(
                      this.$moment(),
                      "month"
                    )
                  ) {
                    isNew = true;
                  }
                }
                if (isNew && this.ElSelect_2.value === 17) {
                  const month = this.$moment().month();
                  if (index < month) {
                    _this.productionPlan.addProductionPlanTable.copyData[i][
                      "key" + (index + 4)
                    ].val = this.filNumValue(item);
                  }
                } else if (isNew && this.ElSelect_2.value === 14) {
                  const date = this.$moment().date();
                  if (index < date - 1) {
                    _this.productionPlan.addProductionPlanTable.copyData[i][
                      "key" + (index + 4)
                    ].val = this.filNumValue(item);
                  }
                }
              }
            });
          }
          const isOk = true;
          callback && callback(isOk);
        } else {
          const isOk = false;
          callback && callback(isOk);
        }
      });
    },
    // 查询能源预测值，标准单位值
    getEnergyplan() {
      const _this = this;
      const list = _this.ElOption_1.options_in;
      const idArr = list.map(item => item.energytype);
      const len = list.length;
      const timeType = this.ElSelect_2.value;
      let startTime = null;
      let endTime = null;
      if (timeType === 17) {
        startTime = this.$moment(this.productionPlan.date)
          .startOf("year")
          .valueOf();
        endTime =
          this.$moment(this.productionPlan.date).endOf("year").valueOf() + 1;
      } else {
        startTime = this.$moment(this.productionPlan.date)
          .startOf("month")
          .valueOf();
        endTime =
          this.$moment(this.productionPlan.date).endOf("month").valueOf() + 1;
      }
      var params = {
        unitMap: this.standUnit,
        startTime: startTime,
        endTime: endTime,
        aggregationCycles: {
          singleData: timeType === 17 ? 14 : 12,
          totalData: timeType
        },
        types: idArr,
        node: {
          id: this.treeNode.id,
          modelLabel: this.treeNode.modelLabel
        },
        modelLabel: "energyconsumeplan"
      };
      commonApi.queryEnergyPlanSynchronize(params).then(response => {
        // 处理今年计划
        if (response.code === 0) {
          const singleData = this._.get(response, "data.singleData", {}) || {};
          const totalData = this._.get(response, "data.totalData", {}) || {};

          for (var i = 0; i < len; i++) {
            const typeName = this._.get(
              this.energyRelation,
              `${list[i].energytype}`,
              null
            );
            const unit = this._.get(
              this.standUnit,
              `${list[i].energytype}`,
              null
            );
            this.productionPlan.addProductionPlanTable.data[i].key2.val = `${
              typeName || "--"
            }(${unit || "--"})`;
            if (unit) {
              this.productionPlan.addProductionPlanTable.data[
                len + i
              ].key2.val = `${typeName || "--"}(${unit || "--"})`;
              this.productionPlan.addProductionPlanTable.copyData[
                i
              ].key2.val = `${typeName || "--"}(${unit || "--"})`;
              this.productionPlan.addProductionPlanTable.copyData[
                len + i
              ].key2.val = `${typeName || "--"}(${unit || "--"})`;
            }
            const totalVal =
              _this._.get(totalData, list[i].energytype, null) || null;
            this.productionPlan.addProductionPlanTable.data[len + i].key3.val =
              this.filNumValue(totalVal);
            this.productionPlan.addProductionPlanTable.copyData[i].key3.val =
              this.filNumValue(totalVal);
            this.productionPlan.addProductionPlanTable.copyData[
              len + i
            ].key3.val = this.filNumValue(totalVal);
            const singleDataList =
              _this._.get(singleData, list[i].energytype, []) || [];
            singleDataList.forEach((item, index) => {
              if (
                _this.productionPlan.addProductionPlanTable.data[len + i][
                  "key" + (index + 4)
                ]
              ) {
                _this.productionPlan.addProductionPlanTable.data[len + i][
                  "key" + (index + 4)
                ].val = this.filNumValue(item);
                _this.productionPlan.addProductionPlanTable.copyData[len + i][
                  "key" + (index + 4)
                ].val = this.filNumValue(item);
                let isNew = false;
                if (this.ElSelect_2.value === 17) {
                  if (
                    this.$moment(this.CetDatePicker_1.val).isSame(
                      this.$moment(),
                      "year"
                    )
                  ) {
                    isNew = true;
                  }
                } else if (this.ElSelect_2.value === 14) {
                  if (
                    this.$moment(this.CetDatePicker_1.val).isSame(
                      this.$moment(),
                      "month"
                    )
                  ) {
                    isNew = true;
                  }
                }
                if (isNew && this.ElSelect_2.value === 17) {
                  const month = this.$moment().month();
                  if (index >= month) {
                    _this.productionPlan.addProductionPlanTable.copyData[i][
                      "key" + (index + 4)
                    ].val = this.filNumValue(item);
                  }
                } else if (isNew && this.ElSelect_2.value === 14) {
                  const date = this.$moment().date();
                  if (index >= date - 1) {
                    _this.productionPlan.addProductionPlanTable.copyData[i][
                      "key" + (index + 4)
                    ].val = this.filNumValue(item);
                  }
                } else {
                  _this.productionPlan.addProductionPlanTable.copyData[i][
                    "key" + (index + 4)
                  ].val = this.filNumValue(item);
                }
              }
            });
            this.$nextTick(() => {
              this.$refs.elTab2.doLayout();
            });
          }
        }
      });
    },
    // 获取转换系数列表
    getConvertedStandardCoalCoef_out() {
      var params = {
        projectId: this.projectId
      };
      commonApi.getConvertedStandardCoalCoef(params).then(res => {
        // 处理今年计划
        if (res.code === 0) {
          this.coefList = res.data || [];
        }
      });
    },
    // 导入前
    handleBeforeUpload: function (file) {
      loading.showLoading();
      if (
        file.name.indexOf(".xls") !== -1 ||
        file.name.indexOf(".xlsx") !== -1
      ) {
        var uploadDocSize = this.systemCfg.uploadDocSize || 0.5;
        const isLimit100M = file.size / 1024 / 1024 < uploadDocSize;
        if (!isLimit100M) {
          this.$message.error(
            `${$T("上传文件超过规定的最大上传大小{0}M", uploadDocSize)}`
          );
        }
        return isLimit100M;
      } else {
        loading.hideLoading();
        this.$message({
          type: "warning",
          message: $T("只能上传xls/xlsx格式文件")
        });
        return false;
      }
    },
    // 导入成功
    uploadSuccess: function (response) {
      loading.hideLoading();
      if (response.code === 0) {
        this.$message({
          message: $T("导入成功"),
          type: "success"
        });
        this.init();
      } else if (response.code !== 0) {
        this.$message({
          type: "error",
          message: response.msg
        });
      }
    },
    uploadError: function (response) {
      loading.hideLoading();
      this.$message({
        message: $T("导入失败"),
        type: "error"
      });
    },
    //点击录入计划
    CetButton_3_statusTrigger_out(val) {
      new Promise((resolve, reject) => {
        this.queryEnergyData_out(resolve);
      }).then(res => {
        if (res) {
          this.isHasEdit = false;
          this.typeRadio = 1;
          this.isShowRadio = true;
          this.CetButton_3.visible_in = false;
          this.productionPlan.table.copyData = JSON.parse(
            JSON.stringify(this.productionPlan.table.data)
          );
          this.productionPlan.table.config.showTabInpt = true;
          if (this.ElSelect_2.value === 17) {
            if (
              this.$moment(this.CetDatePicker_1.val).isSame(
                this.$moment(),
                "year"
              )
            ) {
              this.typeRadio = 2;
              this.isShowRadio = false;
            }
          } else if (this.ElSelect_2.value === 14) {
            if (
              this.$moment(this.CetDatePicker_1.val).isSame(
                this.$moment(),
                "month"
              )
            ) {
              this.typeRadio = 2;
              this.isShowRadio = false;
            }
          }
          this.productionPlanTableTypeClick_out(1);
          this.getEnergyplan();
          this.getConvertedStandardCoalCoef_out();
        }
      });
    },
    //切换能耗类型
    ElSelect_1_change_out(val) {
      this.init();
    },
    // 切换查询时段
    ElSelect_2_change_out(val) {
      if (val === 14) {
        this.CetDatePicker_1.config.type = "month";
        let formatStr = this.language ? "yyyy-MM" : "yyyy年MM月";
        this.CetDatePicker_1.config.format = formatStr;
      } else {
        this.CetDatePicker_1.config.type = "year";
        let formatStr = this.language ? "yyyy" : "yyyy年";
        this.CetDatePicker_1.config.format = formatStr;
      }
      this.init();
    },
    //点击节点树
    CetGiantTree_1_currentNode_out(val) {
      this.parentNode = val.getParentNode();
      this.treeNode = this._.cloneDeep(val);
      this.init();
    },
    //点击上一时段按钮
    CetButton_prv_statusTrigger_out(val) {
      if (this.CetDatePicker_1.val) {
        const date = this.$moment(this.CetDatePicker_1.val);
        if (this.ElSelect_2.value === 17) {
          this.CetDatePicker_1.val = date.subtract(1, "y")._d;
        } else if (this.ElSelect_2.value === 14) {
          this.CetDatePicker_1.val = date.subtract(1, "M")._d;
        }
      } else {
        this.CetDatePicker_1.val = new Date();
      }
    },
    //点击下一段时间按钮
    CetButton_next_statusTrigger_out(val) {
      if (this.CetDatePicker_1.val) {
        const date = this.$moment(this.CetDatePicker_1.val);
        if (this.ElSelect_2.value === 17) {
          this.CetDatePicker_1.val = date.add(1, "y")._d;
        } else if (this.ElSelect_2.value === 14) {
          this.CetDatePicker_1.val = date.add(1, "M")._d;
        }
      } else {
        this.CetDatePicker_1.val = new Date();
      }
    },
    // 智能平摊
    brainpowerClick_out(scope, data) {
      var value;
      var flag = 3;
      const list = this.productionPlan.table.config.columns;
      const len = list.length - 3;
      if (this.typeRadio === 1) {
        value = scope.row[scope.column.property].val;
        if (!value * 1) {
          return;
        }
        for (var item in scope.row) {
          if (flag === 0) {
            if (value * 1) {
              scope.row[item].val = ((1 / len) * value).toFixed2(2);
            } else {
              scope.row[item].val = 0;
            }
            this.blurNum(scope.row, item);
          } else {
            flag--;
          }
        }
      } else {
        let totalNum = 0;
        for (var item1 in scope.row) {
          if (flag === 0) {
            if (scope.row[item1].val) {
              totalNum += Number(scope.row[item1].val);
            }
          } else {
            flag--;
          }
        }
        scope.row.key3.val = Number(totalNum).toFixed2(2);
        this.blurNum(scope.row, "key3");
      }
    },
    //生产计划与实绩跟踪表格合并规则
    productionPlantableSpanMethod({ row, column, rowIndex, columnIndex }) {
      const list = this.ElOption_1.options_in;
      const len = list.length;
      if (!len) {
        return {
          rowspan: 1,
          colspan: 1
        };
      } else {
        if (columnIndex === 0) {
          if (rowIndex % len === 0) {
            return {
              rowspan: len,
              colspan: 1
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
      }
    },
    //表头合并规则
    tabHeaderStyle({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) {
        return { display: "none" };
      } else {
        return { "text-align": "center" };
      }
    },
    // 表格单元格样式
    tabCellStyle({ row, column, rowIndex, columnIndex }) {
      let colors = this.themeColors;
      if (!row[column.property]) {
        return;
      }
      if (row[column.property].isRed) {
        return { background: "red", color: "#fff" };
      } else if (row[column.property].isRed1) {
        return { color: "red" };
      } else if (row[column.property].isBlue) {
        return { background: colors[0], color: "#fff" };
      } else if (row[column.property].isBlue2) {
        return { background: colors[3], color: "#fff" };
      } else if (row[column.property].isBlue3) {
        return { background: colors[1], color: "#fff" };
      } else if (row[column.property].isBlue4) {
        return { background: colors[2], color: "#fff" };
      }
    },
    // 表格单元格样式类名
    tabCellClass({ row, column, rowIndex, columnIndex }) {
      if (!row[column.property]) {
        return;
      }
      const list = this.ElOption_1.options_in || [];
      const len = list.length;
      if (rowIndex % len === 0 && columnIndex === 0) {
        if ((rowIndex / len) % 2 === 0) {
          return "table-cell-deep";
        } else {
          return "table-cell-shallow";
        }
      }

      if (row[column.property].isRed) {
        return "table-cell-red";
      } else if (row[column.property].isRed1) {
        return "table-cell-red1";
      }
    },
    // 输入控制
    handleNum(row, key, num) {
      var value;
      if (typeof row[key] === "object" && row[key]) {
        value = row[key].val;
      } else {
        value = row[key];
      }
      var reg = new RegExp("^(\\-)*(\\d+)\\.(\\d{0," + num + "}).*$");
      var val = String(value);
      if (val) {
        val = val.replace(/[^\d.]/g, ""); //清除数字和'.'以外的字符
        val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", "."); //只保留顺位第一的'.'
        val = val.replace(reg, "$1$2.$3"); //只能输入两位位小数
      }
      if (val.length >= 2 && val.indexOf(".") !== 1 && val[0] === "0") {
        //在非小数的时候清除前导0
        val = val.replace(/0/, "");
      }
      if (val.length >= 2 && val.indexOf(".") === 0) {
        // 在先输入小数点时补0
        val = Number(val);
      }
      if (typeof row[key] === "object" && row[key]) {
        row[key].val = val;
      } else {
        row[key] = val;
      }
      this.isHasEdit = true;
    },
    //输入框失去焦点时触发
    blurNum(row, key, num) {
      if (typeof row[key] !== "object") {
        return;
      }
      const data = this.productionPlan.addProductionPlanTable.data || [];
      const list = this.ElOption_1.options_in;
      const len = list.length;
      let totalVal1 = 0;
      let totalVal2 = 0;
      if (this.totalEnergyNum === 3) {
        data.forEach((item, index) => {
          if (index > 1 && index < len) {
            if (typeof item[key] === "object" && item[key].val) {
              let coef1 = null;
              let coef2 = null;
              this.coefList.forEach(coef => {
                if (
                  coef.sourceenergytype ===
                    this._.get(item, "key2.key", null) &&
                  coef.targetenergytype === this.totalEnergyType
                ) {
                  coef1 = coef.coef;
                }
                if (
                  coef.sourceenergytype ===
                    this._.get(item, "key2.key", null) &&
                  coef.targetenergytype === this.totalEnergyTypeC
                ) {
                  coef2 = coef.coef;
                }
              });
              if (![null, undefined, NaN].includes(coef1)) {
                totalVal1 += item[key].val * coef1;
              }
              if (![null, undefined, NaN].includes(coef2)) {
                totalVal2 += item[key].val * coef2;
              }
            }
          }
        });
        data[0][key].val = Number(totalVal1).toFixed2(2);
        data[1][key].val = Number(totalVal2).toFixed2(2);
      } else if (this.totalEnergyNum === 2) {
        data.forEach((item, index) => {
          if (index > 0 && index < len) {
            if (typeof item[key] === "object" && item[key].val) {
              let coef2 = null;
              this.coefList.forEach(coef => {
                if (
                  coef.sourceenergytype ===
                    this._.get(item, "key2.key", null) &&
                  coef.targetenergytype === this.totalEnergyTypeC
                ) {
                  coef2 = coef.coef;
                }
              });
              if (![null, undefined, NaN].includes(coef2)) {
                totalVal2 += item[key].val * coef2;
              }
            }
          }
        });
        data[0][key].val = Number(totalVal2).toFixed2(2);
      } else if (this.totalEnergyNum === 1) {
        data.forEach((item, index) => {
          if (index > 0 && index < len) {
            if (typeof item[key] === "object" && item[key].val) {
              let coef1 = null;
              this.coefList.forEach(coef => {
                if (
                  coef.sourceenergytype ===
                    this._.get(item, "key2.key", null) &&
                  coef.targetenergytype === this.totalEnergyType
                ) {
                  coef1 = coef.coef;
                }
              });
              if (![null, undefined, NaN].includes(coef1)) {
                totalVal1 += item[key].val * coef1;
              }
            }
          }
        });
        data[0][key].val = Number(totalVal1).toFixed2(2);
      }
    },
    //过滤表格显示数据
    filNumValue(val, unit = 1) {
      if (["", "NaN", null, NaN, undefined].includes(val)) {
        return null;
      } else if (Number(val) === 0) {
        return "0.00";
      } else {
        return Number(val * unit).toFixed2(2);
      }
    },
    //过滤录入计划数据
    filNumValue11(val, unit = 1) {
      if (["", "NaN", null, NaN, undefined].includes(val)) {
        return null;
      } else if (Number(val) === 0) {
        return 0;
      } else {
        return Number((val * unit).toFixed2(2));
      }
    }
  },
  created: function () {
    const currentTheme = localStorage.getItem("omega_theme") || "blue";
    let themeColors = ["#0D86FF", "#F77234", "#FCB92C", "#172FFF", "#D79CB4"];
    const themeMapConfig = themeMap.get(currentTheme);
    if (themeMapConfig) {
      themeColors = themeMapConfig.color.slice(0, 5);
    }
    this.themeColors = themeColors;
  },
  mounted: function () {
    // tab表头合并
    this.$nextTick(() => {
      var dom = document
        .getElementsByClassName("projectOverview")[0]
        .getElementsByClassName("el-table__header");
      for (var index = 0; index < dom.length; index++) {
        dom[index].rows[0].cells[0].colSpan = 2;
      }
    });
  },
  activated: async function () {
    this.isInit = true;
    this.totalEnergyType = this.systemCfg && this.systemCfg.totalEnergyType;
    this.totalEnergyTypeC = this.systemCfg && this.systemCfg.totalEnergyTypeC;
    this.deviation = this.systemCfg && this.systemCfg.deviation;
    await this.getTree();
    await this.queryProjectEnergyList_out();
    setTimeout(() => {
      this.isInit = false;
      this.titleRadioIndexChange_out(0);
    }, 0);
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.projectOverview {
  :deep(.table-cell-red1 span) {
    color: red;
  }
}
.projectOverviewHeaderLine {
  height: 5px;
  margin: 0;
  background: url("./assets/u9803.png");
}
.tabTextBlue {
  cursor: pointer;
  @include font_size(I1);
  @include margin_left(J1);
  vertical-align: middle;
}
.projectOverviewTips {
  margin-top: 12px;
  color: #ff0000;
  display: inline-block;
  padding: 5px 10px;
  border-radius: 5px;
  background: #ffff00;
  height: 26px;
  line-height: 26px;
}
.energyConservationTableTitleBox {
  border: 1px solid #d7d7d7;
  padding: 5px 16px;
  .img {
    display: inline-block;
    height: 20px;
    width: 20px;
    background: url("./assets/u12409.png");
    background-size: 20px 20px;
    position: relative;
    top: 4px;
  }
}
.energyPlanTips {
  float: right;
  background: #ffff99;
  height: 100%;
  padding: 10px;
  color: #ff0000;
  border-radius: 5px;
  margin-right: 100px;
}
</style>
<style lang="scss">
.projectOverview_tabModifyInput > input {
  text-align: center;
}

.projectOverview_tabModifyInput.addProductionPlaTableInput {
  width: calc(100% - 60px);
}
.projectOverview_tabModifyInput.addProductionPlaTableInput.minWifth {
  width: calc(100% - 80px);
}
.projectOverviewTabNoHeader > .el-table__header-wrapper {
  display: none;
}
</style>
