<template>
  <CetDialog v-bind="CetDialog_qrCode" v-on="CetDialog_qrCode.event">
    <template v-slot:footer>
      <span>
        <!-- cancel按钮组件 -->
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </span>
    </template>
    <div class="eem-cont-c1 text-center">
      <img :src="imgSrc" alt="" />
      <p>
        {{ $T("设备名称") }}:
        <span>{{ inputData_in && inputData_in.name }}</span>
      </p>
      <!-- download按钮组件 -->
      <CetButton
        v-bind="CetButton_download"
        v-on="CetButton_download.event"
      ></CetButton>
    </div>
  </CetDialog>
</template>

<script>
import common from "eem-utils/common";

export default {
  name: "qrCode",
  components: {},
  props: {
    openTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    parentList: {
      type: Array
    }
  },
  computed: {},
  data() {
    return {
      // qrCode弹窗组件
      CetDialog_qrCode: {
        title: $T("查看二维码"),
        width: "500px",
        "show-close": true,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_qrCode_open_out,
          close_out: this.CetDialog_qrCode_close_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // download组件
      CetButton_download: {
        visible_in: true,
        disable_in: false,
        title: $T("下载"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_download_statusTrigger_out
        }
      },
      imgSrc: "" // 二维码地址
    };
  },
  watch: {
    openTrigger_in(val) {
      this.CetDialog_qrCode.openTrigger_in = this._.cloneDeep(val);
      this.getQrCodeImg();
    }
  },
  methods: {
    CetDialog_qrCode_open_out(val) {},
    CetDialog_qrCode_close_out(val) {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_qrCode.closeTrigger_in = this._.cloneDeep(val);
    },
    // download输出
    CetButton_download_statusTrigger_out(val) {
      if (!this.imgSrc) {
        return;
      }
      const a = document.createElement("a");
      a.href = this.imgSrc;
      a.download = this.inputData_in.name + $T("二维码"); // 设置下载图片名称
      a.click();
      a.remove();
    },
    // 二维码信息图片
    getQrCodeImg() {
      this.CetButton_download.disable_in = true;
      if (!this.inputData_in) {
        return;
      }
      const urlStr = `/eem-service/v1/signin/downloadQrCode/signPoint?signPointId=${this.inputData_in.id}`;
      const params = {};
      common.generateImg(urlStr, params, "GET").then(res => {
        if (res.status === 200 && res.data.type === "image/jpeg") {
          //将二维码图片转为路径
          this.imgSrc = window.URL.createObjectURL(res.data);
          this.CetButton_download.disable_in = false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped></style>
