<template>
  <div class="login" :style="loginBackgroundStyle">
    <div class="login-main">
      <div class="login-main-right">
        <div class="login-logo">
          <div class="login-logo-main">
            <div class="login-logo-img" :style="logoBackgroundStyle" />
            <div class="login-logo-text" :style="logoTextSize">
              {{ projectName }}
            </div>
          </div>
        </div>
        <div class="login-form">
          <LoginNormal />
        </div>
      </div>
      <div class="login-main-left" :style="mainLeftBackgroundStyle"></div>
    </div>
  </div>
</template>

<script>
import LoginNormal from "./components/loginNormal.vue";
// import LoginMobile from "./components/loginMobile.vue";
// import LoginQrcode from "./components/loginQrcode.vue";
import { conf } from "@omega/app";
import $ from "jquery";
import { getProjectIco } from "eem-utils/otherSetting/judgmentIcon.js";
import store from "@/store";
import { getAuthorize } from "eem-base/omega/afterAppLogin.js";

export default {
  name: "Login",
  beforeRouteEnter(to, from, next) {
    const retainLogin = _.get(store, "state.systemCfg.retainLogin"),
      singleSignOn = _.get(store, "state.systemCfg.singleSignOn");
    if (singleSignOn && !retainLogin) {
      getAuthorize();
    } else {
      next();
    }
  },
  components: {
    LoginNormal
    // LoginMobile,
    // LoginQrcode,
  },
  props: {
    setting: {
      type: Object
    }
  },
  computed: {
    mainLeftBackgroundStyle() {
      return conf.state.resource.login_background_image_url
        ? `background-image: url(${conf.state.resource.login_background_image_url})`
        : "";
    },
    logoBackgroundStyle() {
      return conf.state.resource.login_logo_image_url
        ? `background-image: url(${conf.state.resource.login_logo_image_url})`
        : "";
    },
    projectName() {
      if (window.localStorage.getItem("omega_language") === "en") {
        return store.state.systemCfg.enProjectName;
      } else {
        return store.state.systemCfg.projectName;
      }
    },
    logoTextSize() {
      let login_logo_title_size;
      if (this.setting) {
        login_logo_title_size = this._.get(
          this.setting,
          "login_logo_title_size"
        );
      } else {
        login_logo_title_size = this._.get(
          conf.state,
          "otherSertting.login_logo_title_size"
        );
      }
      return login_logo_title_size
        ? `font-size: ${login_logo_title_size}px`
        : "";
    },
    loginBackgroundStyle() {
      let login_background;
      if (this.setting) {
        login_background = this._.get(this.setting, "login_background");
      } else {
        login_background = this._.get(
          conf.state,
          "otherSertting.login_background"
        );
      }
      return login_background ? `background: ${login_background}` : "";
    }
  },
  mounted() {
    getProjectIco();
    const cb = evt => {
      if (evt.key === "Enter") {
        $(this.$el).find(".login-form .login-btn").click();
      }
    };

    const $document = $(window.document);
    $document.on("keyup", cb);
    this.$on("hook:beforeDestroy", () => $document.off("keyup", cb));
  }
};
</script>

<style lang="scss" scoped>
.login {
  width: 100%;
  height: 100%;
  @include background_static(LOGIN_BG);
  &-main {
    position: absolute;
    top: 50%;
    margin-top: -350px;
    left: 50%;
    margin-left: -700px;
    width: 1400px;
    height: 700px;
    &-right {
      position: absolute;
      width: 400px;
      height: 100%;
      top: 0px;
      right: 0px;
      background-repeat: no-repeat;
      border-radius: 4px;

      @include background_color(BG1);
      @include box_shadow(S1);
      background-size: 100% 100%;
      z-index: 1000;
    }
    &-left {
      position: absolute;
      height: 100%;
      top: 0px;
      right: 400px;
      left: 0px;
      background-size: 100% 100%;
      @include background_image_static(LOGIN_CAROUSEL_IMG);
    }
  }
}

.login-logo {
  position: relative;
  height: 280px;
  display: flex;
  justify-content: center;
  align-items: center;
  &-main {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  &-img {
    background-size: contain;
    background-repeat: no-repeat;
    width: 400px;
    height: 30px;
    @include background_image_static(LOGIN_LOGO_IMG);
    background-position: center center;
    @include margin_bottom(J3);
  }
  &-text {
    @include font_size(H1);
    @include line_height(H1);
    @include font_color(T3);
  }
}
.login-form {
  height: calc(100% - 280px);
  @include padding(J2);
}
.login-form :deep(.el-tabs) {
  .el-tabs__nav {
    width: 100%;
  }
  .el-tabs__item {
    width: 33%;
    text-align: center;
    padding-right: 0;
    &:not(.is-active) {
      @include font_color(T3);
    }
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
}
</style>
