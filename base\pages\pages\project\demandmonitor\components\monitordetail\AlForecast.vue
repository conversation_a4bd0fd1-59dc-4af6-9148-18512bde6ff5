<template>
  <div class="page">
    <el-main style="height: 100%; padding: 0px" class="flex-column">
      <el-header height="auto" style="padding: 0px" class="clearfix mbJ3">
        <CetButton
          class="fr"
          v-bind="CetButton_1"
          v-on="CetButton_1.event"
        ></CetButton>
        <div class="fr mrJ1 clearfix">
          <!-- 向前查询按钮 -->
          <CetButton
            class="fl custom—square"
            v-bind="CetButton_prv"
            v-on="CetButton_prv.event"
          ></CetButton>
          <!-- <div class="basic-box fl ml-5 mr-5">
            <div
              class="basic-box-label"
              style="height: 32px; line-height: 32px"
            >
              年份
            </div>
            <el-date-picker
              class="device-DatePicker"
              v-model="CetDatePicker_1.val"
              v-bind="CetDatePicker_1.config"
              @change="CetDatePicker_1_dateVal_out"
            ></el-date-picker>
          </div> -->
          <CustomElDatePicker
            class="fl mlJ mrJ"
            :prefix_in="$T('选择年份')"
            v-bind="CetDatePicker_1.config"
            v-model="CetDatePicker_1.val"
            @change="CetDatePicker_1_dateVal_out"
          />
          <!-- 向后查询按钮 -->
          <CetButton
            class="fl custom—square"
            v-bind="CetButton_next"
            v-on="CetButton_next.event"
          ></CetButton>
        </div>
      </el-header>
      <div class="mbJ3 flex-row">
        <div class="nextForecast mrJ3 bg1 eem-container">
          <div>
            <div>{{ $T("下月最大需量预测") }}（kW）</div>
            <img
              src="../../assets/curve.png"
              class="mt30"
              style="width: 70%"
              alt=""
            />
            <el-tooltip :content="declarevalue">
              <div class="text-ellipsis">{{ declarevalue }}</div>
            </el-tooltip>
            <div>
              {{ $moment().add(1, "Month").format($T("YYYY年MM月")) }}
            </div>
          </div>
        </div>
        <div class="chartBox eem-container" style="flex: 1">
          <CetChart
            :inputData_in="CetChart_1.inputData_in"
            v-bind="CetChart_1.config"
          />
        </div>
      </div>
      <div class="eem-cont flex-column flex-auto">
        <el-popover placement="top-start" trigger="hover" class="mbJ3">
          <div>
            <div>{{ $T("预测值：通过AI算法预测出的月最大需量值。") }}</div>
            <div>{{ $T("表计值：表计测量的月最大需量值。") }}</div>
            <div>{{ $T("账单值：供电局账单上的月最大需量值。") }}</div>
          </div>
          <i slot="reference" class="el-icon-question fsH3"></i>
        </el-popover>
        <CetTable
          class="flex-auto"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <ElTableColumn v-bind="ElTableColumn_timeText"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_predictionValue"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_meterValue"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_billingValue"></ElTableColumn>
          <ElTableColumn
            v-bind="ElTableColumn_meterBillDeviation"
          ></ElTableColumn>
          <ElTableColumn
            v-bind="ElTableColumn_preBillDeviation"
          ></ElTableColumn>
        </CetTable>
      </div>
    </el-main>
  </div>
</template>
<script>
import { httping } from "@omega/http";
export default {
  name: "AlForecast",
  components: {},
  props: {
    currentNode: {
      type: Object
    },
    exportFileName: {
      type: String
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data() {
    return {
      declarevalue: "--",
      CetChart_1: {
        inputData_in: {},
        config: {
          options: {
            legend: {
              top: 10,
              textStyle: {
                // color: "#fff"
              }
            },
            title: {
              text: $T("Al预测"),
              show: false
            },
            toolbox: {
              feature: {
                saveAsImage: {}
              },
              right: 30
            },
            grid: {
              left: 40,
              right: 40,
              bottom: 30
            },
            tooltip: {
              trigger: "axis",
              formatter: function (value) {
                if (value.length > 0) {
                  var text = `${value[0].axisValueLabel}<br />`;
                  value.forEach(item => {
                    if (item.seriesIndex !== 3) {
                      text += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                        item.color.colorStops
                          ? item.color.colorStops[1].color
                          : item.color
                      };"></span>${item.seriesName}: ${item.data || 0}<br />`;
                    } else {
                      text += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                        item.color.colorStops
                          ? item.color.colorStops[1].color
                          : item.color
                      };"></span>${item.seriesName}: ${item.data || 0}%<br />`;
                    }
                  });
                  return text;
                }
              }
            },
            xAxis: {
              type: "category",
              data: [],
              axisLabel: {
                // color: "#fff"
              },
              axisLine: {
                lineStyle: {
                  // color: "#fff",
                  opacity: 0.6
                }
              },
              splitLine: {
                lineStyle: {
                  // color: "#fff",
                  opacity: 0.6
                }
              }
            },
            yAxis: [
              {
                name: $T("需量") + "（kW）",
                nameLocation: "end",
                nameGap: 10,
                // max: 11000000,
                type: "value",
                nameTextStyle: {
                  align: "left"
                },
                axisLabel: {
                  // color: "#fff"
                },
                axisLine: {
                  lineStyle: {
                    // color: "#fff",
                    opacity: 0.6
                  }
                },
                splitLine: {
                  lineStyle: {
                    color: "#fff",
                    opacity: 0.6
                  }
                },
                splitNumber: 5
                // interval: 11000000 / 5
              },
              {
                name: $T("偏差率") + "（%）",
                nameLocation: "end",
                nameGap: 10,
                // max: 1111,
                type: "value",
                nameTextStyle: {
                  align: "right"
                },
                axisLabel: {
                  // color: "#fff"
                },
                axisLine: {
                  lineStyle: {
                    // color: "#fff",
                    opacity: 0.6
                  }
                },
                splitLine: {
                  lineStyle: {
                    // color: "#fff",
                    opacity: 0.6
                  }
                },
                splitNumber: 5
                // interval: 1111 / 5
              }
            ],
            series: [
              {
                name: $T("预测值") + "（kW）",
                data: [],
                type: "bar",
                itemStyle: {
                  /* color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                    { offset: 0, color: "rgba(37, 115, 237, 1)" },
                    { offset: 1, color: "rgba(50, 20, 220, 1)" }
                  ]) */
                  color: "#B4633E"
                },
                barGap: 0
              },
              {
                name: $T("账单值") + "（kW）",
                data: [],
                type: "bar",
                itemStyle: {
                  /* color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                    { offset: 0, color: "rgba(50, 191, 252, 1)" },
                    { offset: 1, color: "rgba(32, 87, 232, 1)" }
                  ]) */
                  color: "#80A0CF"
                },
                barGap: 0
              },
              {
                name: $T("表计值") + "（kW）",
                data: [],
                type: "bar",
                itemStyle: {
                  /* color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                    { offset: 0, color: "rgba(38, 223, 145, 1)" },
                    { offset: 1, color: "rgba(3, 152, 216, 1)" }
                  ]) */
                  color: "#175FC3"
                },
                barGap: 0
              },
              {
                name: $T("偏差率") + "（%）",
                data: [],
                type: "line",
                itemStyle: {
                  // color: "#F56C54"
                  color: "#5CE478"
                },
                yAxisIndex: 1
              }
            ]
          }
        }
      },
      CetDatePicker_1: {
        disable_in: false,
        val: this.$moment().add(0, "year").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "year",
          format: $T("yyyy 年"),
          rangeSeparator: "-",
          clearable: false,
          size: "small",
          style: {
            display: "inline-block"
          }
          // pickerOptions: {
          //   disabledDate(time) {
          //     return time.getTime() > Date.now();
          //   }
          // }
        }
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        // defaultSort: null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      ElTableColumn_timeText: {
        //type: "",      // selection 勾选 index 序号
        prop: "timeText", // 支持path a[0].b
        label: $T("月份"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "100", //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (val) {
          if (val.timeText && val.timeText !== 0) {
            return val.timeText;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_predictionValue: {
        //type: "",      // selection 勾选 index 序号
        prop: "predictionValue", // 支持path a[0].b
        label: $T("预测值") + "（kW）", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "200", //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (val) {
          if (val.predictionValue && val.predictionValue !== 0) {
            return val.predictionValue;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_meterValue: {
        //type: "",      // selection 勾选 index 序号
        prop: "meterValue", // 支持path a[0].b
        label: $T("表计值") + "（kW）", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "180", //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (val) {
          if (val.meterValue && val.meterValue !== 0) {
            return val.meterValue;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_billingValue: {
        //type: "",      // selection 勾选 index 序号
        prop: "billingValue", // 支持path a[0].b
        label: $T("账单值") + "（kW）", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "160", //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (val) {
          if (val.billingValue && val.billingValue !== 0) {
            return val.billingValue;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_meterBillDeviation: {
        //type: "",      // selection 勾选 index 序号
        prop: "meterBillDeviation", // 支持path a[0].b
        label: $T("表计与账单偏差") + "（%）", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "280", //该宽度会自适应
        //width: "100",     //绝对宽度
        sortable: true, //true 前端排序  "custom" 后端排序
        formatter: function (val) {
          if (val.meterBillDeviation && val.meterBillDeviation !== 0) {
            return val.meterBillDeviation;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_preBillDeviation: {
        //type: "",      // selection 勾选 index 序号
        prop: "preBillDeviation", // 支持path a[0].b
        label: $T("预测与账单偏差") + "（%）", //列名
        headerAlign: "left",
        align: "left",
        //width: "240",
        showOverflowTooltip: true,
        minWidth: "300", //该宽度会自适应
        //width: "100",     //绝对宽度
        sortable: true, //true 前端排序  "custom" 后端排序
        formatter: function (val) {
          if (val.preBillDeviation && val.preBillDeviation !== 0) {
            return val.preBillDeviation;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      }
    };
  },
  watch: {
    "CetDatePicker_1.val": function (val) {
      this.getAIPredict();
    },
    currentNode: {
      handler: function (val) {
        if (val) {
          this.getMaxPredict();
          this.getAIPredict();
        }
      },
      deep: true
    }
  },

  methods: {
    // 获取下月最大需量预测
    getMaxPredict() {
      if (!this.currentNode) {
        return;
      }
      this.declarevalue = "--";
      httping({
        url: "/eem-service/v1/common/query/oneLayer",
        data: {
          rootID: 0,
          rootLabel: "demandpredictdata",
          rootCondition: {
            filter: {
              expressions: [
                {
                  limit: this.currentNode.id,
                  operator: "EQ",
                  prop: "demandaccount_id"
                },
                {
                  limit: this.$moment()
                    .add(1, "month")
                    .startOf("month")
                    .valueOf(),
                  operator: "GE",
                  prop: "logtime"
                },
                {
                  limit: this.$moment()
                    .add(1, "month")
                    .endOf("month")
                    .valueOf(),
                  operator: "LE",
                  prop: "logtime"
                }
              ]
            }
          }
        },
        method: "POST",
        timeout: 10000
      }).then(res => {
        if (res.code === 0 && res.data && res.data.length > 0) {
          this.declarevalue = res.data[0].value.toFixed2(2);
        }
      });
    },
    // 查询预测值
    getAIPredict() {
      if (!this.currentNode) {
        return;
      }
      this.CetTable_1.data = [];
      this.CetChart_1.config.options.xAxis.data = [];
      this.CetChart_1.config.options.series[0].data = [];
      this.CetChart_1.config.options.series[1].data = [];
      this.CetChart_1.config.options.series[2].data = [];
      this.CetChart_1.config.options.series[3].data = [];
      httping({
        url: `/eem-service/v1/demand/monitor/details/AIPredict`,
        method: "POST",
        data: {
          accountIds: [this.currentNode.id],
          startTime: this.$moment(this.CetDatePicker_1.val)
            .startOf("year")
            .valueOf(),
          endTime: this.$moment(this.CetDatePicker_1.val)
            .add(1, "year")
            .startOf("year")
            .valueOf()
        }
      }).then(response => {
        if (response.code === 0) {
          var xAxisArr = [];
          var data0 = [];
          var data1 = [];
          var data2 = [];
          var data3 = [];
          response.data[0].data.forEach(item => {
            item.predictionValue =
              item.predictionValue && item.predictionValue.toFixed(2);
            item.meterValue = item.meterValue && item.meterValue.toFixed(2);
            item.billingValue =
              item.billingValue && item.billingValue.toFixed(2);
            item.meterBillDeviation =
              item.meterBillDeviation &&
              Number((item.meterBillDeviation * 100).toFixed(2));
            item.preBillDeviation =
              item.preBillDeviation &&
              Number((item.preBillDeviation * 100).toFixed(2));
            item.timeText = this.$moment(item.time).format("YYYY/MM");
            xAxisArr.push(this.$moment(item.time).format("YYYY/MM"));
            data0.push(item.predictionValue || "--");
            data1.push(item.billingValue || "--");
            data2.push(item.meterValue || "--");
            data3.push(item.preBillDeviation || "--");
          });
          // 表格
          this.CetTable_1.data = response.data[0].data;
          // 表格排序
          var wrap;
          for (var i = 0; i < this.CetTable_1.data.length; i++) {
            for (var k = i + 1; k < this.CetTable_1.data.length; k++) {
              if (this.CetTable_1.data[i].time < this.CetTable_1.data[k].time) {
                wrap = this.CetTable_1.data[k];
                this.CetTable_1.data[k] = this.CetTable_1.data[i];
                this.CetTable_1.data[i] = wrap;
              }
            }
          }
          // 图表
          this.CetChart_1.config.options.xAxis.data = xAxisArr;
          this.CetChart_1.config.options.series[0].data = data0;
          this.CetChart_1.config.options.series[1].data = data1;
          this.CetChart_1.config.options.series[2].data = data2;
          this.CetChart_1.config.options.series[3].data = data3;
          // 处理双轴刻度线不重合情况
          var Y1Max = Math.floor(
            Math.max(this.getMax(data0), this.getMax(data1), this.getMax(data2))
          );
          var Y2Max = Math.floor(this.getMax(data3));
          var interval1;
          var interval2;
          interval1 = Math.ceil(Y1Max / 5);
          if (interval1 > 10 && interval1 < 100) {
            interval1 = Math.ceil(interval1 / 10) * 10;
          } else if (interval1 > 100) {
            interval1 = Math.ceil(interval1 / 100) * 100;
          }
          Y1Max = interval1 * 5;
          interval2 = Math.ceil(Y2Max / 5);
          if (interval2 > 10 && interval2 < 100) {
            interval2 = Math.ceil(interval2 / 10) * 10;
          } else if (interval2 > 100) {
            interval2 = Math.ceil(interval2 / 100) * 100;
          }
          Y2Max = interval2 * 5;
          this.CetChart_1.config.options.yAxis[0].max = Y1Max;
          this.CetChart_1.config.options.yAxis[0].interval = interval1;
          this.CetChart_1.config.options.yAxis[1].max = Y2Max;
          this.CetChart_1.config.options.yAxis[1].interval = interval2;
        }
      });
    },
    //获取最大值
    getMax(arr) {
      //假设最大值max 为arr[0]
      let max;
      if (arr[0] && arr[0] !== "--") {
        max = Number(arr[0]);
      } else {
        max = 0;
      }
      //遍历对比
      for (var i = 0; i < arr.length; i++) {
        //若max小于当前项 说明不是最大值 将当前项的值赋予max
        // 继续遍历对比找到最大的值
        if (arr[i] && arr[i] !== "--") {
          if (max < Number(arr[i])) {
            max = Number(arr[i]);
          }
        }
      }
      return max;
    },
    // 导出
    CetButton_1_statusTrigger_out(val) {
      this.CetTable_1.exportFileName = this.exportFileName + "_" + $T("Al预测");
      this.CetTable_1.exportTrigger_in = this._.cloneDeep(val);
    },
    CetTable_1_record_out(val) {},
    CetTable_1_outputData_out(val) {},
    CetButton_prv_statusTrigger_out(val) {
      const date = this.$moment(this.CetDatePicker_1.val);
      this.CetDatePicker_1.val = date.subtract(1, "year").valueOf();
    },
    CetButton_next_statusTrigger_out(val) {
      const date = this.$moment(this.CetDatePicker_1.val);
      this.CetDatePicker_1.val = date.add(1, "year").valueOf();
    },
    CetDatePicker_1_dateVal_out(val) {
      this.getAIPredict();
    }
  },

  created: function () {},
  mounted: function () {
    this.getAIPredict();
    this.getMaxPredict();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.chartBox {
  height: 300px;
  box-sizing: border-box;
  @include background_color(BG1);
}
.nextForecast {
  width: 250px;
  height: 300px;
  box-sizing: border-box;
  & > div {
    @include background_color(BG1);
    @include border_radius(C3);
    height: 100%;
    width: 100%;
    text-align: center;
    & > div:nth-of-type(1) {
      // padding-top: 20px;
      font-size: 18px;
    }
    & > div:nth-of-type(2) {
      padding-top: 10px;
      font-size: 36px;
      @include font_color(T1);
    }
    & > div:nth-of-type(3) {
      padding-top: 10px;
      @include font_color(T3);
    }
  }
}
.cycle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  text-align: center;
  @include background_color(ZS);
}
</style>
