<template>
  <!-- 1弹窗组件 -->
  <CetDialog
    v-bind="CetDialog_1"
    v-on="CetDialog_1.event"
    class="CetDialog small"
  >
    <CetGiantTree
      class="tree eem-cont-c1"
      ref="CetGiantTree"
      v-bind="CetGiantTree_1"
      v-on="CetGiantTree_1.event"
    ></CetGiantTree>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import TREE_PARAMS from "@/store/treeParams.js";
import { httping } from "@omega/http";
export default {
  name: "NodeSelect",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    checkedNodes_in: {
      type: Array
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    }
  },

  data() {
    return {
      currentNode: null,
      CetDialog_1: {
        title: $T("选择被分摊对象"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        saveNodes: [],
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.getTreeData();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    // 获取节点树
    getTreeData() {
      var _this = this;
      _this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      _this.CetGiantTree_1.inputData_in = [];
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.powerEquipment,
        treeReturnEnable: true
      };
      httping({
        url: "/eem-service/v1/node/nodeTree",
        method: "POST",
        data
      }).then(res => {
        if (res.code === 0 && res.data.length > 0) {
          // 过滤掉项目下的开关柜或一段线
          var data = [];
          res.data.forEach((item, index) => {
            var obj = _this._.cloneDeep(item);
            obj.children = [];
            data.push(obj);
            if (item.children && item.children.length > 0) {
              item.children.forEach(ite => {
                if (ite.modelLabel != "linesegmentwithswitch") {
                  data[index].children.push(ite);
                }
              });
            }
          });
          if (_this.checkedNodes_in && _this.checkedNodes_in.length) {
            let checkedNodes = _this._.cloneDeep(_this.checkedNodes_in);
            setTimeout(() => {
              _this.CetGiantTree_1.checkedNodes = checkedNodes;
              _this.CetGiantTree_1.saveNodes = _this._.cloneDeep(checkedNodes);
              _this.expandNode(
                checkedNodes,
                "tree_id",
                _this.$refs.CetGiantTree.ztreeObj
              );
            }, 0);
          } else {
            _this.CetGiantTree_1.checkedNodes = [];
            _this.CetGiantTree_1.saveNodes = [];
          }
          _this.CetGiantTree_1.inputData_in = data;
        }
      });
    },
    // 展开节点
    expandNode(nodes, key, ztreeObj) {
      setTimeout(() => {
        nodes.forEach(item => {
          let node = ztreeObj.getNodeByParam(key, item[key]);
          let parentNodes = [],
            parentNode = node.getParentNode();
          while (parentNode) {
            parentNodes.push(parentNode);
            parentNode = parentNode.getParentNode();
          }
          parentNodes.forEach(i => {
            ztreeObj.expandNode(i, true);
          });
        });
      }, 0);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.$emit(
        "confirm_out",
        this._.cloneDeep(this.CetGiantTree_1.saveNodes)
      );
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetGiantTree_1_checkedNodes_out(val) {
      this.CetGiantTree_1.saveNodes = this._.cloneDeep(val);
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
    box-sizing: border-box;
  }
}
.tree {
  height: 400px;
  :deep(.el-tree) {
    overflow: auto;
  }
}
</style>
