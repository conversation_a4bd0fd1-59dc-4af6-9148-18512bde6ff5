<template>
  <el-container class="page">
    <el-aside width="320px" class="fullheight bg1 pJ3 border-right">
      <!-- left组件 -->
      <CetGiantTree
        v-bind="CetGiantTree_left"
        v-on="CetGiantTree_left.event"
        class="treeStyle"
      ></CetGiantTree>
    </el-aside>
    <el-main class="fullheight bg1 pJ3">
      <div style="height: calc(50% - 8px)" class="mbJ3">
        <div class="head flex">
          <div class="title">{{ $T("运行参数分析") }}</div>
          <div class="input-box flex-end">
            <div class="input-prefix">{{ $T("稳定性标准") }}</div>
            <ElInputNumber
              v-model="ElInput_standard.value"
              v-bind="ElInput_standard"
              v-on="ElInput_standard.event"
            ></ElInputNumber>
            <div class="input-suffix">%</div>
          </div>
          <customElSelect
            class="mlJ3 multipleSelect"
            v-model="ElSelect_parameter.value"
            v-bind="ElSelect_parameter"
            v-on="ElSelect_parameter.event"
            :prefix_in="$T('分析参数')"
          >
            <ElOption
              v-for="item in ElOption_parameter.options_in"
              :key="item[ElOption_parameter.key]"
              :label="item[ElOption_parameter.label]"
              :value="item[ElOption_parameter.value]"
              :disabled="item[ElOption_parameter.disabled]"
            ></ElOption>
          </customElSelect>
          <airIntervalInput
            v-bind="airIntervalInput"
            v-on="airIntervalInput.event"
            :currentNode="currentNode"
          >
            <el-button
              slot="reference"
              type="primary"
              @click="handleAirIntervalInput"
            >
              {{ $T("气量区间录入") }}
            </el-button>
          </airIntervalInput>
          <CetDateSelect
            class="mlJ3 date-select"
            v-bind="CetDateSelect_time"
            v-on="CetDateSelect_time.event"
          ></CetDateSelect>
        </div>
        <runParamaterAnalysis
          style="height: calc(100% - 40px)"
          :chartData="chartData"
        />
      </div>
      <div style="height: calc(50% - 8px)">
        <el-row class="fullfilled" :gutter="16">
          <el-col :span="12" class="fullheight">
            <runningDays :runningDaysData="runningDaysData" />
          </el-col>
          <el-col :span="12" class="fullheight">
            <div style="height: 35%" class="mbJ3">
              <optimizationStrategy
                :optimizationStrategyData="optimizationStrategyData"
                :defaultInterval="airInterval.defaultInterval"
                :machineLength="machineLength || 0"
              />
            </div>
            <div style="height: calc(65% - 16px)" class="flex">
              <div style="width: 35%" class="fullheight mrJ3">
                <efficiencyRatio :efficiencyRatioData="efficiencyRatioData" />
              </div>
              <div style="width: calc(65% - 16px)" class="fullheight">
                <suggestionOutput
                  :suggestionData="suggestionData"
                  :isMultiple="true"
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-main>
  </el-container>
</template>

<script>
import customApi from "@/api/custom.js";
import airIntervalInput from "./dialogs/airIntervalInput.vue";
import runParamaterAnalysis from "../singleMachine/components/runParamaterAnalysis.vue";
import runningDays from "./components/runningDays.vue";
import optimizationStrategy from "./components/optimizationStrategy.vue";
import efficiencyRatio from "./components/efficiencyRatio.vue";
import suggestionOutput from "../singleMachine/components/suggestionOutput.vue";
export default {
  name: "multipleMachine",
  components: {
    airIntervalInput,
    runParamaterAnalysis,
    runningDays,
    optimizationStrategy,
    efficiencyRatio,
    suggestionOutput
  },
  data() {
    return {
      currentNode: {},
      queryTime: {
        startTime: this.$moment().startOf("M").valueOf(),
        endTime: this.$moment().endOf("M").valueOf() + 1
      },
      chartData: [],
      runningDaysData: [],
      optimizationStrategyData: {},
      efficiencyRatioData: {},
      suggestionData: "",
      ElInput_standard: {
        value: "",
        clearable: false,
        min: 0,
        max: 100,
        precision: 2,
        controls: false,
        style: {
          width: "170px"
        },
        event: {
          change: this.ElInput_standard_change_out
        }
      },
      airInterval: {},
      airIntervalInput: {
        openTrigger_in: Date.now(),
        inputData_in: {},
        event: {
          finishTrigger_out: this.airIntervalInput_finishTrigger_out
        }
      },
      // parameter组件
      ElSelect_parameter: {
        value: [12],
        multiple: true,
        "collapse-tags": true,
        type: "number",
        style: {
          width: "250px"
        },
        event: {
          change: this.ElSelect_parameter_change_out
        }
      },
      // parameter组件
      ElOption_parameter: {
        options_in: this.$store.state.enumerations.compressoradjustparameter,
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // time组件
      CetDateSelect_time: {
        value: {
          dateType: "3",
          value: new Date().getTime()
        }, //设置日期值, dateType 1 日 2 周 3月 4季 5年 6自定义
        typeList: ["week", "month", "season"],
        align: "right",
        //自定义选项 typeList: ["day", "week", "month", "season",  "year", "daterange"]
        event: {
          date_out: this.CetDateSelect_time_date_out
        }
      },
      // left组件
      CetGiantTree_left: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: this.setNodeClasses
          },
          callback: {
            beforeClick: this.beforeClick
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_left_currentNode_out //选中单行输出
        }
      },
      machineLength: 0
    };
  },
  watch: {},
  methods: {
    /**
     * 获取节点树
     */
    getTreeData() {
      const params = {
        rootID: 0,
        rootLabel: "project",
        subLayerConditions: [
          {
            modelLabel: "gasgatheringstation"
          },
          {
            modelLabel: "dehydratingstation"
          }
        ],
        treeReturnEnable: true
      };
      customApi.getNodeTree(params).then(res => {
        const data = res.data || [];
        this.handleDisablingTree(data);
        this.CetGiantTree_left.inputData_in = data;
        const flatData = this.flatTreeData(data);
        const modelLabels = ["gasgatheringstation", "dehydratingstation"];
        const node = flatData?.find(item =>
          modelLabels.includes(item.modelLabel)
        );
        this.CetGiantTree_left.selectNode = node || {};
      });
    },

    /**
     * 拍平节点树数据
     */
    flatTreeData(treeData) {
      const cloneData = this._.cloneDeep(treeData);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },

    /**
     * 节点是否可选择
     */
    beforeClick(treeId, treeNode) {
      const modelLabels = ["gasgatheringstation", "dehydratingstation"];
      if (!modelLabels.includes(treeNode.modelLabel)) {
        return false;
      }
      return true;
    },

    /**
     * 设置节点禁用
     */
    handleDisablingTree(nodes) {
      const modelLabels = ["gasgatheringstation", "dehydratingstation"];
      nodes.forEach(item => {
        if (!modelLabels.includes(item.modelLabel)) {
          item.disableNode = true;
        } else {
          item.disableNode = false;
        }
        if (item.children && item.children.length > 0) {
          this.handleDisablingTree(item.children);
        }
      });
    },
    setNodeClasses(treeId, treeNode) {
      return treeNode.disableNode
        ? { add: ["disableNode"] }
        : { remove: ["disableNode"] };
    },

    /**
     * 节点树选择
     */
    CetGiantTree_left_currentNode_out(val) {
      this.currentNode = _.cloneDeep(val);
      this.ElSelect_parameter.value = [12];
      this.queryAirInterval();
      this.queryStability();
      this.queryChartData();
      this.queryOtherData();
    },

    /**
     * 查询气象区间参数
     */
    queryAirInterval() {
      const params = {
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel,
        startTime: this.queryTime.startTime,
        endTime: this.queryTime.endTime
      };
      customApi.queryAirInterval(params).then(res => {
        const data = res.data || {};
        this.airInterval = _.cloneDeep(data);
      });
    },

    /**
     * 查询稳定性标准
     */
    queryStability() {
      const params = {
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel
      };
      customApi.queryStability(params).then(res => {
        const data = res.data || {};
        this.ElInput_standard.value = data.stability || "";
      });
    },

    /**
     * 设置稳定性标准
     */
    setStability() {
      const params = {
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel,
        stability: this.ElInput_standard.value
      };
      customApi.setStability(params).then(res => {
        if (res.code === 0) {
          this.$message.success($T("设置成功"));
          this.queryStability();
          this.queryOtherData();
        }
      });
    },

    /**
     * 修改稳定性标准
     */
    ElInput_standard_change_out(val) {
      this.setStability();
    },

    /**
     * 修改分析参数
     */
    ElSelect_parameter_change_out(val) {
      const childrenLength = this.currentNode.children?.length || 1;
      if (childrenLength * val.length > 6) {
        this.$message.warning($T("最多只可选择{0}个参数", val.length - 1));
        this.ElSelect_parameter.value.pop();
        return;
      }
      this.queryChartData();
    },

    /**
     * 打开气量区间弹窗
     */
    handleAirIntervalInput() {
      this.airIntervalInput.inputData_in = this.airInterval;
      this.airIntervalInput.openTrigger_in = Date.now();
    },

    /**
     * 保存气量区间成功
     */
    airIntervalInput_finishTrigger_out() {
      this.queryAirInterval();
      this.queryOtherData();
    },

    /**
     * 时间切换
     */
    CetDateSelect_time_date_out(val) {
      const queryTime = {
        startTime: val[0],
        endTime: val[1] + 1
      };
      this.queryTime = queryTime;
      this.queryAirInterval();
      this.queryChartData();
      this.queryOtherData();
    },

    /**
     * 获取曲线数据
     */
    queryChartData() {
      const params = {
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel,
        params: this.ElSelect_parameter.value,
        startTime: this.queryTime.startTime,
        endTime: this.queryTime.endTime
      };
      customApi.queryMultipleMachineParameterAnalysis(params).then(res => {
        const data = res.data || [];
        this.chartData = data;
      });
    },

    /**
     * 查询页面其他数据
     */
    queryOtherData() {
      const params = {
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel,
        objectName: this.currentNode.name,
        startTime: this.queryTime.startTime,
        endTime: this.queryTime.endTime
      };
      customApi.queryRunningDaysAndStrategy(params).then(res => {
        const data = res.data || {};
        this.runningDaysData = data.runningDayList || [];
        this.optimizationStrategyData = data;
        this.efficiencyRatioData = {
          efficiency: data.efficiency || 0,
          inefficiency: data.inefficiency || 0
        };
        this.suggestionData = data.suggestion;
        this.machineLength = data.nodeCount || 0;
      });
    }
  },
  mounted() {
    this.getTreeData();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.border-right {
  border-right: 1px solid;
  @include border_color(B1);
}
.head {
  height: 32px;
  line-height: 32px;
  margin-bottom: 8px;
  .title {
    font-size: 16px;
    font-weight: bold;
  }
}
.flex-end {
  margin-left: auto;
  justify-content: flex-end;
}
.input-box {
  position: relative;
  :deep(.el-input__inner) {
    padding-left: 94px;
    padding-right: 32px;
  }
}
.input-suffix {
  position: absolute;
  height: 32px;
  line-height: 32px;
  right: 12px;
  top: 0;
}
.input-prefix {
  position: absolute;
  height: 32px;
  line-height: 32px;
  left: 12px;
  top: 0;
  z-index: 1;
  @include font_color(ZS);
}
.card {
  width: 100%;
  height: 250px;
  border: 1px solid;
  @include border_color(B1);
  border-radius: 8px;
  padding: 16px;
  box-sizing: border-box;
}
.row-center {
  align-items: center;
}
.multipleSelect {
  :deep(.el-select__tags) {
    width: calc(100% - 96px) !important;
  }
}
.date-select {
  width: 350px;
  :deep(.el-date-editor) {
    width: 150px !important;
  }
}
.treeStyle {
  :deep(.disableNode) {
    @include font_color(T6 !important);
    cursor: not-allowed !important;
    opacity: 0.6;
  }
}
</style>
