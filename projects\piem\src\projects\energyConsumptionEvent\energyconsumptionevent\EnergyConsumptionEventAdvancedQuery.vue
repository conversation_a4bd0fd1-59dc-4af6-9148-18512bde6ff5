﻿<template>
  <!-- 1弹窗组件 -->
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="CetDialog">
    <div class="eem-cont-c1">
      <el-row
        :gutter="$J3"
        width="100%"
        style="overflow: auto; max-height: calc(84vh - 156px)"
      >
        <el-col :span="12">
          <CetGiantTree
            class="tree"
            v-bind="CetGiantTree_1"
            v-on="CetGiantTree_1.event"
          ></CetGiantTree>
        </el-col>
        <el-col :span="12">
          <div>
            <el-input
              :placeholder="$T('输入事件描述关键字')"
              suffix-icon="el-icon-search"
              v-model="CetDialog_1.data.description"
            ></el-input>
          </div>
          <div class="event-box mtJ3">
            <span class="event-box-head">{{ $T("事件类型") }}</span>
            <div class="event-box-content">
              <ElCheckboxGroup
                v-model="CetDialog_1.data.types"
                v-bind="ElCheckboxGroup_1"
                v-on="ElCheckboxGroup_1.event"
              >
                <ElCheckbox
                  v-for="item in ElCheckboxList_1.options_in"
                  :key="item[ElCheckboxList_1.key]"
                  :label="item[ElCheckboxList_1.label]"
                  :disabled="item[ElCheckboxList_1.disabled]"
                >
                  {{ item[ElCheckboxList_1.text] }}
                </ElCheckbox>
              </ElCheckboxGroup>
            </div>
          </div>
          <div class="event-box mtJ3">
            <span class="event-box-head">{{ $T("事件等级") }}</span>
            <div class="event-box-content">
              <ElCheckboxGroup
                v-model="CetDialog_1.data.levels"
                v-bind="ElCheckboxGroup_2"
                v-on="ElCheckboxGroup_2.event"
              >
                <ElCheckbox
                  v-for="item in ElCheckboxList_2.options_in"
                  :key="item[ElCheckboxList_2.key]"
                  :label="item[ElCheckboxList_2.label]"
                  :disabled="item[ElCheckboxList_2.disabled]"
                >
                  {{ item[ElCheckboxList_2.text] }}
                </ElCheckbox>
              </ElCheckboxGroup>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import moment from "moment";
export default {
  name: "EnergyConsumptionEventAdvancedQuery",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    keyWord_in: {
      type: String
    },
    clickNode_in: {
      type: Object
    },
    treeData_in: {
      type: Array
    },
    energyConsumption: Array,
    energyEfficiecy: Array,
    energyAll: Array
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: $T("事件-高级查询"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        },
        data: {
          checkbox: [0], //收敛事件
          children: [
            {
              modelLabel: "",
              nodeId: 0,
              name: ""
            }
          ], //查询收敛事件
          cycle: 0, //年17 月14 日12 小时7
          description: "", //事件描述
          id: 0,
          name: "",
          index: 0,
          limit: 20,
          modelLabel: "",
          status: 0, //事件状态
          types: [], //事件类型
          levels: [] //事件等级
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetLabel_2: {
        dataConfig: {
          edition: "v1",
          queryUrl: "",
          type: "post",
          modelLabel: "",
          dropItemText: "",
          displayTextLeft: "",
          displayTextRight: "",
          width: "150px",
          textAlign: "left",
          fontSize: "14px"
        },
        dataMode: "static",
        val: $T("时段"),
        id_in: "",
        queryNode_in: {},
        queryTime_in: {
          timeType: 1,
          time: null
        },
        fontColor_in: "#606266",
        queryTrigger_in: new Date().getTime(),
        config: {}
      },
      ElCheckboxGroup_1: {
        value: [],
        style: {
          display: "inline-block",
          width: "100%"
        },
        showCheckAll: true,
        event: {
          change: this.ElCheckboxGroup_1_change_out
        }
      },
      ElCheckboxList_1: {
        options_in: [],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      ElCheckboxGroup_2: {
        value: [],
        style: {
          display: "inline-block",
          width: "100%"
        },
        showCheckAll: true,
        event: {
          change: this.ElCheckboxGroup_2_change_out
        }
      },
      ElCheckboxList_2: {
        options_in: [],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      eventKeyWord: "",

      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      CetCheckboxGroup_3: {
        dataConfig: {
          edition: "v1",
          queryUrl: "/data-center/v1/common/queryEnumerations",
          type: "",
          modelLabel: "transientfaultdirection",
          dropItemId: "id",
          dropItemText: "text"
        },
        dataMode: "static",
        inputData_in: [],
        CheckedArray: [],
        config: {
          style: {
            display: "inline-block",
            width: "100%"
          },
          showCheckAll: true
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.initTree();
      if (vm.clickNode_in) {
        vm.CetGiantTree_1.selectNode = vm.clickNode_in;
      }
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {
      var vm = this;
      vm.CetDialog_1.queryId_in = val;
    },
    inputData_in(val) {
      this.CetDialog_1.inputData_in = val;
    },
    keyWord_in(val) {
      this.CetDialog_1.data.description = val;
    },
    clickNode_in(val) {
      if (!val) {
        return;
      }
      this.CetGiantTree_1.selectNode = val;
    },
    $route(to, from) {
      if (
        to.name == "energyConsumptionEvent" ||
        to.name == "energyEfficiencyEvent"
      ) {
        if (
          from.name == "energyConsumptionEvent" ||
          from.name == "energyEfficiencyEvent"
        ) {
          this.init();
        }
      }
    }
  },

  methods: {
    init() {
      let vm = this;
      vm.loadEnumrations_out();
      vm.initTree();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
      this.$emit("saveData_out", this.CetDialog_1.data);
    },
    CetCheckboxGroup_1_resulttext_out(val) {},
    CetCheckboxGroup_2_resulttext_out(val) {},
    CetCheckboxGroup_3_resulttext_out(val) {},
    // 1输出,方法名要带_out后缀
    ElCheckboxGroup_1_change_out(val) {
      if (!val || val.length === 0) {
        this.CetDialog_1.data.types = [-1];
      }
    },
    ElCheckboxGroup_2_change_out(val) {
      if (!val || val.length === 0) {
        this.CetDialog_1.data.levels = [-1];
      }
    },

    CetDialog_1_open_out(val) {},
    CetDialog_1_close_out(val) {},
    CetLabel_2_currenttext_out(val) {},
    CetTree_1_currentNode_out(val) {
      if (!val) {
        return;
      }
      this.CetDialog_1.data.id = val.id;
      this.CetDialog_1.data.name = val.name;
      this.CetDialog_1.data.modelLabel = val.modelLabel;
      var children = val.children || [];
      function fn(children) {
        var arr = [];
        if (children && children.length > 0) {
          children.forEach(item => {
            var obj = {};
            if (item.children && item.children.length > 0) {
              obj = {
                nodeId: item.id,
                name: item.name,
                modelLabel: item.modelLabel,
                children: fn(item.children)
              };
            } else {
              obj = {
                nodeId: item.id,
                name: item.name,
                modelLabel: item.modelLabel,
                children: []
              };
            }
            arr.push(obj);
          });
        }
        return arr;
      }
      this.CetDialog_1.data.children = fn(children);
    },
    loadEnumrations_out(val) {
      let _this = this;

      var data = this.$store.state.enumerations.eventtype || [];
      data = data.filter(item => {
        if (this.$route.name == "energyConsumptionEvent") {
          // 能耗超标
          return this.energyConsumption.indexOf(item.id) != -1;
        } else if (this.$route.name == "energyEfficiencyEvent") {
          // 能效超标
          return this.energyEfficiecy.indexOf(item.id) != -1;
        } else {
          return this.energyAll.indexOf(item.id) != -1;
        }
      });
      _this.ElCheckboxList_1.options_in = data;
      // _this.CetCheckboxGroup_1.inputData_in = data;
      // 事件类型默认全选
      _this.CetDialog_1.data.types = data.map(item => {
        return item.id;
      });

      let levelsGroup = [
        {
          id: 1,
          text: $T("一级")
        },
        {
          id: 2,
          text: $T("二级")
        },
        {
          id: 3,
          text: $T("三级")
        },
        {
          id: 4,
          text: $T("其他")
        }
      ];
      _this.ElCheckboxList_2.options_in = levelsGroup;

      // 区域默认全选
      if (_this.$route.params?.keepParams) {
        let level = _.cloneDeep(
          _this.$route.params.keepParams.queryParams.level
        );
        if (_this.$route.name === "energyConsumptionEvent") {
          //能耗超标报警中，预警 就是 其他，其他的id=4，但是预警的level是0。因此这里如果level中含0，就把0改成4。例：[0,1,2] => [4,1,2]
          const index = level.indexOf(0);
          if (index !== -1) {
            level.splice(index, 1, 4);
          }
        }
        _this.CetDialog_1.data.levels = level;
      } else {
        _this.CetDialog_1.data.levels = levelsGroup.map(item => {
          return item.id;
        });
      }
    },
    initTree() {
      this.CetGiantTree_1.inputData_in = this._.cloneDeep(this.treeData_in);
    }
  },

  activated: function () {
    this.init();
  },
  mounted() {
    if (
      !["energyConsumptionEvent", "energyEfficiencyEvent"].includes(
        this.$route.name
      )
    ) {
      this.init();
    }
  }
};
</script>
<style lang="scss" scoped>
.tree {
  height: 500px;
}
.event-box {
  border: 1px solid;
  @include border_color(B1);
  @include border_radius(C);
}

.event-box-head {
  display: inline-block;
  width: 100%;
  height: 30px;
  line-height: 30px;
  box-sizing: border-box;
  border-width: 0 0 1px;
  border-style: solid;
  border-radius: 4px 4px 0 0;
  padding: 0 20px;
  @include border_color(B1);
  @include background_color(B1);
}

.event-box-content {
  padding: 20px;
}

.device-DatePicker {
  width: 280px;
  margin-left: 15px;
}

.device-left-tree {
  height: 520px !important;
  overflow: hidden;
}

.time {
  width: 100%;
  display: flex;
  height: 30px;
  @include margin_top(J3);
}
.time-label {
  width: 50px;
  line-height: 30px;
}
.time-range {
  flex: 1;
}
.CetDialog {
  :deep(.el-dialog__body) {
    @include padding(J1);
    @include background_color(BG);
  }
}
</style>
<style lang="scss">
.event-box-content .el-checkbox {
  min-width: 49%;
  margin-right: 0;
  input {
    @include background_color(ZS);
  }
}
</style>
