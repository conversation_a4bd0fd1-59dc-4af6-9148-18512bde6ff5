<template>
  <!-- 1弹窗组件 -->
  <CetDialog
    class="CetDialog small"
    v-bind="CetDialog_1"
    v-on="CetDialog_1.event"
  >
    <el-container class="fullheight flex-column bg1 brC1 pJ3 plJ4 prJ4">
      <div class="clearfix mbJ1">
        <!-- <el-checkbox class="fl lhHm" v-model="checkStrictly">
          默认选中子节点
        </el-checkbox> -->
        <customElSelect
          class="fr"
          :prefix_in="$T('能源类型')"
          v-model="ElSelect_energytype.value"
          v-bind="ElSelect_energytype"
          v-on="ElSelect_energytype.event"
        >
          <ElOption
            v-for="item in ElOption_energytype.options_in"
            :key="item[ElOption_energytype.key]"
            :label="item[ElOption_energytype.label]"
            :value="item[ElOption_energytype.value]"
            :disabled="item[ElOption_energytype.disabled]"
          ></ElOption>
        </customElSelect>
      </div>
      <!-- 父子关联 -->
      <CetGiantTree
        v-if="false"
        v-show="checkStrictly"
        class="switch-tree"
        v-bind="CetGiantTree_1"
        v-on="CetGiantTree_1.event"
      ></CetGiantTree>
      <!-- 父子不关联 -->
      <CetGiantTree
        v-show="!checkStrictly"
        class="switch-tree separate"
        v-bind="CetGiantTree_2"
        v-on="CetGiantTree_2.event"
      ></CetGiantTree>
      <div class="tagBox">
        <el-tag
          :key="index"
          v-for="(tag, index) in allTreeCheckedNodes"
          :closable="tag.closable"
          :disable-transitions="false"
          @close="handleClose(index)"
        >
          {{ tag.name }}
        </el-tag>
      </div>
    </el-container>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import { httping } from "@omega/http";
import commonApi from "@/api/custom.js";
import ELECTRICAL_DEVICE from "@/store/electricaldevice.js";
//能源类型-电
const ENERGY_TYPE_ELECTRIC = 2;
export default {
  name: "shareRate",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    // 配电设备模型列表
    roomDeviceList: {
      type: Array
    },
    currentNode: {
      type: Object
    },
    // 已关联管网设备列表
    deviceList: {
      type: Array,
      default() {
        return [];
      }
    },
    energyType_in: {
      type: Number
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data() {
    return {
      allTreeCheckedNodes: [],
      flag: false,
      checkStrictly: false,
      CetDialog_1: {
        title: $T("添加管网设备"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // energytype组件
      ElSelect_energytype: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_energytype_change_out
        }
      },
      // energytype组件
      ElOption_energytype: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      checkedNodes1: [],
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: this.setNodeClasses
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out //勾选节点输出
        }
      },
      checkedNodes2: [],
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: this.setNodeClasses
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_2_checkedNodes_out //勾选节点输出
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.flag = false;
      vm.checkStrictly = false;
      vm.CetDialog_1.openTrigger_in = val;
      this.allTreeCheckedNodes = this.deviceList.map(item => {
        return {
          ...item,
          closable: false
        };
      });
      this.queryProjectEnergyList_out();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    inputData_in(val) {
      this.CetDialog_1.inputData_in = val;
    },
    checkStrictly(val) {
      if (val) {
        this.CetGiantTree_1.checkedNodes = this._.cloneDeep(this.checkedNodes2);
      } else {
        this.CetGiantTree_2.checkedNodes = this._.cloneDeep(this.checkedNodes1);
      }
    }
  },

  methods: {
    handleClose(index) {
      this.allTreeCheckedNodes.splice(index, 1);
      const checkNodes = this.allTreeCheckedNodes.filter(item => item.closable);
      if (this.checkStrictly) {
        this.CetGiantTree_1.checkedNodes = checkNodes;
        if (!checkNodes.length) {
          this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
        }
      } else {
        this.CetGiantTree_2.checkedNodes = checkNodes;
        if (!checkNodes.length) {
          this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
        }
      }
    },
    setNodeClasses(treeId, treeNode) {
      return treeNode.disabledClass
        ? { add: ["disabledClass"] }
        : { remove: ["disabledClass"] };
    },
    CetGiantTree_1_checkedNodes_out(val) {
      this.allTreeCheckedNodes = (val || [])
        .map(item => {
          return {
            ...item,
            closable: true
          };
        })
        .concat(this.deviceList);
      this.checkedNodes1 = this._.cloneDeep(val);
    },
    CetGiantTree_2_checkedNodes_out(val) {
      this.allTreeCheckedNodes = (val || [])
        .map(item => {
          return {
            ...item,
            closable: true
          };
        })
        .concat(this.deviceList);
      this.checkedNodes2 = this._.cloneDeep(val);
    },
    getTreeData() {
      let data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [],
        treeReturnEnable: true
      };

      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.checkedNodes1 = [];
      this.CetGiantTree_1.checkedNodes = [];
      this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
      this.checkedNodes2 = [];
      this.CetGiantTree_2.checkedNodes = [];
      const energytype = this.ElSelect_energytype.value;
      //判断选择电能源类型，如果是则显示配电房和管道房，其他类型只显示管道房
      if (energytype === ENERGY_TYPE_ELECTRIC) {
        data.subLayerConditions = [
          {
            filter: {
              expressions: [{ limit: [1, 6], operator: "IN", prop: "roomtype" }]
            },
            modelLabel: "room"
          },
          {
            filter: {
              expressions: [
                { limit: energytype, operator: "EQ", prop: "energytype" }
              ]
            },
            modelLabel: "pipeline"
          },
          {
            filter: null,
            modelLabel: "linesegment",
            props: []
          },
          {
            filter: null,
            modelLabel: "breaker",
            props: []
          }
        ];
        let roomDeviceList = [];
        ELECTRICAL_DEVICE.forEach(item => {
          var obj = {
            filter: null,
            modelLabel: item.value,
            props: []
          };
          roomDeviceList.push(obj);
        });

        data.subLayerConditions =
          data.subLayerConditions.concat(roomDeviceList);
      } else {
        data.subLayerConditions = [
          {
            filter: {
              expressions: [{ limit: [6], operator: "IN", prop: "roomtype" }]
            },
            modelLabel: "room"
          },
          {
            filter: {
              expressions: [
                { limit: energytype, operator: "EQ", prop: "energytype" }
              ]
            },
            modelLabel: "pipeline"
          }
        ];
      }
      httping({
        url: "/eem-service/v1/node/nodeTree/simple",
        method: "POST",
        data
      }).then(res => {
        if (res.code === 0) {
          var data = this._.get(res, "data[0].children", []) || [];
          this.setTreeLeaf_filterText1Change(data);
          this.CetGiantTree_1.inputData_in = data;
          this.CetGiantTree_2.inputData_in = data;
        }
      });
    },
    setTreeLeaf_filterText1Change(nodes) {
      if (!nodes) {
        return;
      }
      nodes.forEach(item => {
        if (
          !["project", "room", "powerdiscabinet", "arraycabinet"].includes(
            item.modelLabel
          )
        ) {
          item.leaf = true;
        }
        if (
          this.deviceList.filter(
            i => i.id === item.id && i.modelLabel === item.modelLabel
          ).length === 0
        ) {
          item.disabledClass = false;
        } else {
          item.disabledClass = true;
        }
        this.setTreeLeaf_filterText1Change(this._.get(item, "children", []));
      });
    },
    // 保存
    async addSupplyRelation_out() {
      var _this = this;
      var deviceArr = [];
      let checkedNodes = (this.checkedNodes2 || []).concat(this.deviceList);
      // checkedNodes.map(item => {
      //   if (item.nodeType !== "room") {
      //     deviceArr.push({
      //       deviceName: item.text,
      //       modelLabel: "269619472",
      //       id: item.nodeId,
      //       tree_id: "269619472_" + item.nodeId
      //     });
      //   }
      // });
      if (!checkedNodes.length) {
        _this.$message({
          message: $T("请选择管网设备"),
          type: "warning"
        });
        return;
      }
      let deviceList = this._.cloneDeep(this.deviceList) || [];
      deviceList.push(...deviceArr);
      const params = checkedNodes
        .map(item => {
          return {
            measureInfos: [],
            node: {
              deviceIds: [this.currentNode.nodeId],
              id: item.id,
              modelLabel: item.modelLabel
            }
          };
        })
        .filter(item => item.node.id);
      // if (deviceList.length > 0) {
      //   deviceList.forEach(item => {
      //     if (item.monitoredNodeArr && item.monitoredNodeArr.length > 0) {
      //       item.monitoredNodeArr.forEach(i => {
      //         i.deviceId = item.id;
      //       });
      //       params[0].measureInfos.push(...item.monitoredNodeArr);
      //     }
      //     params[0].node.deviceIds.push(item.id);
      //   });
      // }

      const params2 = checkedNodes.map(item => {
        return {
          modelLabel: item.modelLabel,
          nodes: [
            {
              deviceIds: [this.currentNode.nodeId],
              id: item.id
            }
          ]
        };
      });

      // let a = [
      //   {
      //     modelLabel: this.currentNode.modelLabel,
      //     nodes: [
      //       {
      //         deviceIds: params[0].node.deviceIds,
      //         id: this.currentNode.id
      //       }
      //     ]
      //   }
      // ];
      // 数据源
      var dataSource = 6;
      if (params[0].node.deviceIds.length === 0) {
        dataSource = 6;
      } else {
        dataSource = 1;
      }
      httping({
        url: "/eem-service/v1/relationship/quantityMap/measureInfo",
        method: "PUT",
        data: params
      }).then(res => {
        this.Edit_dataSource(dataSource, params2);
      });
    },
    // 更新物理量数据源
    Edit_dataSource(dataSource, params) {
      httping({
        url: "/eem-service/v1/quantity/quantityObject?dataSource=" + dataSource,
        method: "POST",
        data: params
      }).then(response => {
        if (response.code === 0) {
          this.$emit("updata_out");
          this.$message({
            message: $T("保存成功"),
            type: "success"
          });
          this.CetDialog_1.closeTrigger_in = new Date().getTime();
        }
      });
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      this.addSupplyRelation_out();
    },
    ElSelect_energytype_change_out() {
      this.getTreeData();
    },
    // 获取能耗类型
    queryProjectEnergyList_out() {
      var _this = this;
      const params = {
        projectId: this.projectId
      };
      commonApi.queryProjectEnergyList(params).then(res => {
        if (res.code === 0) {
          const energytypeList = res.data || [];
          _this.ElOption_energytype.options_in = energytypeList;
          let energyVal =
            _this._.get(energytypeList, "[0].energytype", null) || null;
          if (_this.ergyType_in) {
            energyVal = _this.ergyType_in;
          }
          _this.ElSelect_energytype.value = this._.cloneDeep(energyVal);
        } else {
          _this.ElOption_energytype.options_in = [];
          _this.ElOption_energytype.value = null;
        }
        this.flag = true;
        this.ElSelect_energytype_change_out();
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep() {
    .el-dialog__body {
      @include background_color(BG);
      @include padding(J1);
    }
  }
  .tagBox {
    min-height: 100px;
    border-top: 1px solid;
    border-bottom: 1px solid;
    @include border_color(B1);
    @include padding(J3 0);
    :deep(.el-tag) {
      @include margin_right(J1);
      @include margin_top(J1);
    }
  }
  .lhHm {
    @include line_height(Hm);
  }
}
.switch-tree {
  height: 500px;
  // :deep(& > .el-tree > .el-tree-node > .el-tree-node__content) {
  //   .el-tree-node__expand-icon + .el-checkbox {
  //     display: none;
  //   }
  // }
  // 模拟打钩
  :deep(.el-checkbox.is-disabled .el-checkbox__inner) {
    background: url("../assets/check.png") no-repeat center center;
  }
  :deep(.disabledClass) {
    position: relative;
    &::after {
      content: "";
      position: absolute;
      background: url("../assets/check.png") no-repeat center center;
      background-size: 100% 100%;
      height: 14px;
      left: -22px;
      top: 4px;
      width: 14px;
    }
    .node_name {
      position: relative;
      &::before {
        content: "";
        position: absolute;
        background: url("../assets/check.png") no-repeat center center;
        background-size: 100% 100%;
        height: 14px;
        left: -22px;
        top: 5px;
        width: 14px;
        cursor: no-drop;
      }
    }
  }
  &.separate {
    :deep(.ztree > div > li > span.button.chk) {
      display: none !important;
    }
  }
}
</style>
