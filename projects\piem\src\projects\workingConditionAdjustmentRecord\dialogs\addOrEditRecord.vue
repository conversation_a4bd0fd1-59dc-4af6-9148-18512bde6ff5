<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="CetDialog"
    >
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
            :disable_in="
              CetForm_pagedialog.data.adjustmentType === 1 &&
              schemeData.clearanceRegulationMethod === 0
            "
          ></CetButton>
        </span>
      </template>
      <CetForm
        :data.sync="CetForm_pagedialog.data"
        v-bind="CetForm_pagedialog"
        v-on="CetForm_pagedialog.event"
      >
        <div class="card-box">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item prop="gascompressorId" :label="$T('选择设备')">
                <el-select
                  style="width: 100%"
                  v-model="CetForm_pagedialog.data.gascompressorId"
                  filterable
                  :placeholder="$T('请选择')"
                  @change="changeGasCompressor"
                  :disabled="isEdit"
                >
                  <el-option
                    v-for="item in gascompressorList"
                    :key="item.id"
                    :value="item.id"
                    :label="item.name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="adjustmentType" :label="$T('气量调节方式')">
                <el-select
                  style="width: 100%"
                  v-model="CetForm_pagedialog.data.adjustmentType"
                  :placeholder="$T('请选择')"
                  @change="changeAdjustmentType"
                  :disabled="isEdit"
                >
                  <el-option
                    v-for="item in adjustmentTypeOption"
                    :disabled="
                      item.id === 1 &&
                      schemeData.clearanceRegulationMethod === 0
                    "
                    :key="item.id"
                    :value="item.id"
                    :label="item.text"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <div
            class="scheme-box mbJ3"
            v-if="_.isNumber(CetForm_pagedialog.data.adjustmentType)"
          >
            <div class="title mbJ1">{{ $T("调节方案") }}</div>
            <el-row :gutter="16">
              <!-- 回流调节 -->
              <template v-if="CetForm_pagedialog.data.adjustmentType === 0">
                <el-col :span="12">
                  <span>{{ $T("可调范围") }}</span>
                  <span class="ml4">
                    {{ formatterNumber(schemeData.refluxMinimum) }}%-{{
                      formatterNumber(schemeData.refluxMaximum)
                    }}%
                  </span>
                </el-col>
                <el-col :span="12">
                  <span>{{ $T("基准偏离容忍度") }}</span>
                  <span class="ml4">
                    {{
                      formatterNumber(schemeData.refluxBaseDeviationTolerance)
                    }}%
                  </span>
                </el-col>
              </template>

              <!-- 余隙调节 -->
              <template v-if="CetForm_pagedialog.data.adjustmentType === 1">
                <el-col :span="8">
                  <span>{{ $T("调节方式") }}</span>
                  <span class="ml4">
                    {{
                      formatterClearanceRegulationMethod(
                        schemeData.clearanceRegulationMethod
                      )
                    }}
                  </span>
                </el-col>
                <el-col
                  :span="8"
                  v-if="
                    schemeData.clearanceRegulationMethod === 1 ||
                    schemeData.clearanceRegulationMethod === 2
                  "
                >
                  <span>{{ $T("可调范围") }}</span>
                  <span class="ml4">
                    {{ formatterNumber(schemeData.clearanceRegulationMin) }}%-{{
                      formatterNumber(schemeData.clearanceRegulationMax)
                    }}%
                  </span>
                </el-col>
                <el-col
                  :span="8"
                  v-if="
                    schemeData.clearanceRegulationMethod === 1 ||
                    schemeData.clearanceRegulationMethod === 2
                  "
                >
                  <span>{{ $T("基准偏离容忍度") }}</span>
                  <span class="ml4">
                    {{
                      formatterNumber(
                        schemeData.clearanceRegulationBaseDeviationTolerance
                      )
                    }}%
                  </span>
                </el-col>
              </template>

              <!-- 气缸调节 -->
              <template v-if="CetForm_pagedialog.data.adjustmentType === 2">
                <el-col :span="8">
                  <span>{{ $T("气缸组数") }}</span>
                  <span class="ml4">
                    {{
                      formatterCylinderBanksNumber(
                        schemeData.cylinderBanksNumber
                      )
                    }}
                  </span>
                </el-col>
                <el-col :span="8">
                  <span>{{ $T("调节方式") }}</span>
                  <span class="ml4">
                    {{
                      formatterCylinderOperationMode(
                        schemeData.cylinderOperationMode
                      )
                    }}
                  </span>
                </el-col>
                <el-col :span="8">
                  <span>{{ $T("基准偏离容忍度") }}</span>
                  <span class="ml4">
                    {{
                      formatterNumber(
                        schemeData.cylinderBaseDeviationTolerance
                      )
                    }}%
                  </span>
                </el-col>
              </template>
            </el-row>
          </div>
          <el-row
            :gutter="16"
            v-if="
              !(
                CetForm_pagedialog.data.adjustmentType === 1 &&
                schemeData.clearanceRegulationMethod === 0
              )
            "
          >
            <el-col :span="12">
              <el-form-item prop="adjustmentResult" :label="$T('调节结果')">
                <!-- 气量调节 -->
                <el-select
                  v-if="CetForm_pagedialog.data.adjustmentType === 2"
                  style="width: 100%"
                  v-model="CetForm_pagedialog.data.adjustmentResult"
                  :placeholder="$T('请选择')"
                >
                  <el-option
                    v-for="item in adjustmentResultOption"
                    :key="item.id"
                    :value="item.id"
                    :label="item.text"
                  />
                </el-select>
                <!-- 回流/余隙调节 -->
                <div class="input-number" v-else>
                  <el-input-number
                    style="width: 100%"
                    :placeholder="$T('请输入')"
                    :min="getAdjustmentResultMin()"
                    :max="getAdjustmentResultMax()"
                    :precision="2"
                    :controls="false"
                    v-model="CetForm_pagedialog.data.adjustmentResult"
                  ></el-input-number>
                  <div class="input-number-suffix">%</div>
                </div>
                <div
                  v-if="
                    CetForm_pagedialog.data.gascompressorId &&
                    _.isNumber(CetForm_pagedialog.data.adjustmentType)
                  "
                >
                  <span>{{ $T("调节前") }}</span>
                  <span class="ml4 text-ZS">
                    {{
                      isEdit
                        ? formatterAdjustedCondition(
                            inputData_in,
                            inputData_in.preRegulationCondition
                          )
                        : formatterAdjustedCondition(
                            workingCondition,
                            workingCondition.adjustmentResult
                          )
                    }}
                  </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="effectiveTime" :label="$T('工况生效时间')">
                <el-date-picker
                  v-model="CetForm_pagedialog.data.effectiveTime"
                  :clearable="false"
                  type="date"
                  value-format="timestamp"
                  placeholder="请选择"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";
import common from "eem-utils/common";
export default {
  name: "addOrEditRecord",
  components: {},
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    isEdit: {
      type: Boolean
    }
  },
  computed: {
    adjustmentResultOption() {
      const list = [
        {
          id: 1,
          text: $T("1组"),
          children: [
            {
              id: 0,
              text: $T("单作用")
            },
            {
              id: 1,
              text: $T("双作用")
            }
          ]
        },
        {
          id: 2,
          text: $T("2组"),
          children: [
            {
              id: 2,
              text: $T("单双可调")
            },
            {
              id: 3,
              text: $T("单单作用")
            },
            {
              id: 4,
              text: $T("双双作用")
            }
          ]
        }
      ];
      const cylinderBanksNumber = this.schemeData.cylinderBanksNumber;
      const obj = list.find(item => item.id === cylinderBanksNumber);
      return obj?.children || [];
    }
  },
  data() {
    return {
      gascompressorList: [],
      schemeData: {},
      workingCondition: {},
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("新增调整记录"),
        width: "640px",
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      adjustmentTypeOption:
        this.$store.state.enumerations.gascompressoradjustablemode?.filter(
          item => [0, 1, 2].includes(item.id)
        ),
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "static", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [
            "id",
            "gascompressorId",
            "adjustmentType",
            "adjustmentResult",
            "effectiveTime"
          ], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {
          id: null,
          gascompressorId: null,
          adjustmentType: null,
          adjustmentResult: null,
          effectiveTime: null
        },
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        labelPosition: "top",
        rules: {
          gascompressorId: [
            {
              required: true,
              message: $T("请选择设备"),
              trigger: "blur"
            }
          ],
          adjustmentType: [
            {
              required: true,
              message: $T("请选择气量调节方式"),
              trigger: "blur"
            }
          ],
          adjustmentResult: [
            {
              required: true,
              message: $T("请填写调节结果"),
              trigger: "blur"
            }
          ],
          effectiveTime: [
            {
              required: true,
              message: $T("请选择生效时间"),
              trigger: "blur"
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_pagedialog_saveData_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.queryGasCompressorBySheme();
      this.CetDialog_pagedialog.title = this.isEdit
        ? $T("编辑调整记录")
        : $T("新增调整记录");
      if (this.isEdit) {
        this.queryWorkingConditionDetailsById();
      } else {
        this.schemeData = {};
        this.workingCondition = {};
        this.CetForm_pagedialog.data = {
          id: null,
          gascompressorId: null,
          adjustmentType: null,
          adjustmentResult: null,
          effectiveTime: null
        };
      }
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      this.CetForm_pagedialog.inputData_in = this._.cloneDeep(val);
    }
  },
  methods: {
    /**
     * 查询已有方案的压缩机
     */
    queryGasCompressorBySheme() {
      customApi.queryGasCompressorBySheme().then(res => {
        this.gascompressorList = res.data || [];
      });
    },

    /**
     * 查询工况调整记录详情
     */
    queryWorkingConditionDetailsById() {
      customApi
        .queryWorkingConditionDetailsById(this.inputData_in.id)
        .then(res => {
          this.CetForm_pagedialog.data = res.data || {};
          this.queryDeviceOperationSchemeDetailsByGasCompressorId();
        });
    },

    /**
     * 获取工况详情-通过压缩机id
     */
    queryWorkingConditionDetails() {
      if (
        !this.CetForm_pagedialog.data.gascompressorId ||
        !_.isNumber(this.CetForm_pagedialog.data.adjustmentType)
      )
        return;
      const params = {
        gascompressorId: this.CetForm_pagedialog.data.gascompressorId,
        adjustmentTypeList: [this.CetForm_pagedialog.data.adjustmentType]
      };
      customApi.queryWorkingConditionDetails(params).then(res => {
        const data = res.data || [];
        this.workingCondition =
          data?.find(
            item =>
              item.adjustmentType ===
              this.CetForm_pagedialog.data.adjustmentType
          ) || {};
      });
    },

    /**
     * 查询运行方案详情-通过压缩机id
     */
    queryDeviceOperationSchemeDetailsByGasCompressorId() {
      customApi
        .queryDeviceOperationSchemeDetailsByGasCompressorId(
          this.CetForm_pagedialog.data.gascompressorId
        )
        .then(res => {
          this.schemeData = res.data || {};
          if (
            !this.isEdit &&
            this.CetForm_pagedialog.data.adjustmentType === 1 &&
            this.schemeData.clearanceRegulationMethod === 0
          ) {
            this.CetForm_pagedialog.data.adjustmentType = null;
          }
        });
    },

    /**
     * 切换设备
     */
    changeGasCompressor() {
      this.queryDeviceOperationSchemeDetailsByGasCompressorId();
      this.queryWorkingConditionDetails();
    },

    /**
     * 切换调节方式
     */
    changeAdjustmentType() {
      this.queryWorkingConditionDetails();
    },

    /**
     * 保存
     */
    CetForm_pagedialog_saveData_out(val) {
      customApi
        .saveWorkingConditionAdjustmentRecord([this.CetForm_pagedialog.data])
        .then(res => {
          if (res.code === 0) {
            this.$message.success($T("保存成功"));
            this.$emit("saveData_out", val);
            this.CetDialog_pagedialog.closeTrigger_in = Date.now();
          }
        });
    },

    /**
     * 打开弹窗
     */
    CetDialog_pagedialog_openTrigger_out(val) {
      this.$emit("openTrigger_out", val);
    },

    /**
     * 关闭弹窗
     */
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },

    /**
     * 确认
     */
    CetButton_preserve_statusTrigger_out(val) {
      this.CetForm_pagedialog.localSaveTrigger_in = this._.cloneDeep(val);
    },

    /**
     * 取消
     */
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },

    /**
     * 调节结果最小值
     */
    getAdjustmentResultMin() {
      if (this.CetForm_pagedialog.data.adjustmentType === 0) {
        return _.isNumber(this.schemeData.refluxMinimum)
          ? this.schemeData.refluxMinimum
          : 0;
      } else if (this.CetForm_pagedialog.data.adjustmentType === 1) {
        return _.isNumber(this.schemeData.clearanceRegulationMin)
          ? this.schemeData.clearanceRegulationMin
          : 0;
      } else {
        return 0;
      }
    },

    /**
     * 调节结果最大值
     */
    getAdjustmentResultMax() {
      if (this.CetForm_pagedialog.data.adjustmentType === 0) {
        return _.isNumber(this.schemeData.refluxMaximum)
          ? this.schemeData.refluxMaximum
          : 999999999999999;
      } else if (this.CetForm_pagedialog.data.adjustmentType === 1) {
        return _.isNumber(this.schemeData.clearanceRegulationMax)
          ? this.schemeData.clearanceRegulationMax
          : 999999999999999;
      } else {
        return 999999999999999;
      }
    },

    /**
     * 调节前工况转换
     */
    formatterAdjustedCondition(row, cellValue) {
      let result;
      if (row.adjustmentType === 2) {
        result = this.formatterCylinderOperationMode(cellValue);
      } else {
        let text = row.adjustmentType === 0 ? $T("回流开度") : $T("余隙开度");
        result = cellValue || cellValue === 0 ? text + cellValue + "%" : "--";
      }
      return result;
    },

    /**
     * 数字类型展示
     */
    formatterNumber(val) {
      return _.isNumber(val) ? val : "--";
    },

    /**
     * 余隙调节方式转换
     */
    formatterClearanceRegulationMethod(val) {
      const list = [
        {
          id: 0,
          text: $T("不可调节")
        },
        {
          id: 1,
          text: $T("手动调节")
        },
        {
          id: 2,
          text: $T("自动调节")
        }
      ];
      const obj = list.find(item => item.id === val);
      return obj?.text || "--";
    },

    /**
     * 气缸组数转换
     */
    formatterCylinderBanksNumber(val) {
      const list = [
        {
          id: 1,
          text: $T("1组")
        },
        {
          id: 2,
          text: $T("2组")
        }
      ];
      const obj = list.find(item => item.id === val);
      return obj?.text || "--";
    },

    /**
     * 气缸作用方式
     */
    formatterCylinderOperationMode(val) {
      const list = [
        {
          id: 0,
          text: $T("单作用")
        },
        {
          id: 1,
          text: $T("双作用")
        },
        {
          id: 2,
          text: $T("单双可调")
        },
        {
          id: 3,
          text: $T("单单作用")
        },
        {
          id: 4,
          text: $T("双双作用")
        }
      ];
      const obj = list.find(item => item.id === val);
      return obj?.text || "--";
    }
  }
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
  }
}
.card-box {
  padding: 16px;
  border-radius: 8px;
  @include background_color(BG1);
}
.input-number {
  display: flex;
  position: relative;
  &-suffix {
    position: absolute;
    right: 1px;
    top: 1px;
    width: 40px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 0px 4px 4px 0px;
    @include background_color(BG12);
  }
  :deep(.el-input__inner) {
    padding-right: 48px;
  }
}
.scheme-box {
  padding: 8px 16px;
  box-sizing: border-box;
  border-radius: 4px;
  @include background_color(BG);
  .title {
    font-weight: bold;
  }
}
.ml4 {
  margin-left: 4px;
}
</style>
