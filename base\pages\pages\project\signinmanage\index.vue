<template>
  <div class="page eem-common">
    <div class="fullheight flex-row">
      <!-- 分组 -->
      <div class="fullheight" style="width: 370px">
        <div class="fullfilled eem-container flex-column">
          <div class="mbJ3">
            <span class="common-title-H2">{{ $T("分组") }}</span>
          </div>
          <div class="eem-group-list flex-auto">
            <div
              v-for="(item, key) in groupList"
              :key="key"
              :class="[
                'group-item',
                { active: (activeGroup && activeGroup.id) === item.id }
              ]"
              @click="activeGroup = item"
            >
              <el-tooltip :content="item.name" effect="light">
                <span>
                  {{ item.name }}
                </span>
              </el-tooltip>
            </div>
          </div>
          <div class="mtJ3">
            <el-dropdown class="fr more mlJ3" @command="handleCommand_group">
              <span class="el-dropdown-link">
                {{ $T("更多") }}
                <i class="el-icon-arrow-up el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="edit"
                  :disabled="groupList.length ? false : true"
                >
                  {{ $T("编辑") }}
                </el-dropdown-item>
                <el-dropdown-item
                  command="delete"
                  class="delete"
                  :disabled="groupList.length ? false : true"
                >
                  {{ $T("删除") }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <!-- add按钮组件 -->
            <CetButton
              class="fr mlJ1"
              v-bind="CetButton_add"
              v-on="CetButton_add.event"
            ></CetButton>
            <!--批量导出按钮组件 -->
            <CetButton
              v-if="!isSigninByNFC"
              class="fr mlJ1"
              v-bind="CetButton_batchExport"
              v-on="CetButton_batchExport.event"
              :disable_in="groupList.length ? false : true"
            ></CetButton>
            <!-- edit按钮组件 -->
            <!-- <CetButton
              class="fr mlJ1 mtJ3"
              v-bind="CetButton_edit"
              v-on="CetButton_edit.event"
            ></CetButton> -->
            <!-- delete按钮组件 -->
            <!-- <CetButton
              class="fr mlJ1 mtJ3"
              v-bind="CetButton_delete"
              v-on="CetButton_delete.event"
            ></CetButton> -->
          </div>
        </div>
      </div>
      <!-- 签到点 -->
      <div class="fullheight flex-auto mlJ3 mrJ3" style="min-width: 560px">
        <div class="fullfilled eem-container flex-column">
          <div class="mbJ3">
            <span class="common-title-H2">{{ $T("签到点") }}</span>
          </div>
          <div class="flex-auto">
            <CetTable
              :data.sync="CetTable_signin.data"
              :dynamicInput.sync="CetTable_signin.dynamicInput"
              v-bind="CetTable_signin"
              v-on="CetTable_signin.event"
              ref="dragTable"
              row-key="id"
              @selection-change="handleSelectionChange_out"
            >
              <ElTableColumn
                type="selection"
                width="50"
                align="center"
              ></ElTableColumn>
              <el-table-column
                :label="$T('排序')"
                width="55px"
                headerAlign="left"
                align="left"
              >
                <template>
                  <i class="el-icon-s-operation"></i>
                </template>
              </el-table-column>
              <el-table-column
                headerAlign="left"
                align="left"
                type="index"
                label="#"
                width="60px"
              ></el-table-column>
              <el-table-column
                headerAlign="left"
                align="left"
                prop="name"
                :label="$T('名称')"
                :show-overflow-tooltip="true"
              ></el-table-column>
              <el-table-column
                headerAlign="left"
                align="left"
                width="60px"
                prop="id"
                label="ID"
                :show-overflow-tooltip="true"
              ></el-table-column>
              <ElTableColumn
                v-bind="ElTableColumn_operate"
                width="180px"
                headerAlign="left"
                align="left"
                fixed="right"
              >
                <template slot-scope="{ row }">
                  <div @click.stop>
                    <span
                      @click="CetButton_edit1_statusTrigger_out(row)"
                      class="eem-row-handle fl mrJ3"
                    >
                      {{ $T("编辑") }}
                    </span>
                    <span
                      @click="handleCommand_signin({ type: 'object', row })"
                      class="eem-row-handle fl mrJ3 text-ellipsis"
                      style="width: 85px"
                    >
                      {{ $T("关联巡检对象") }}
                    </span>
                    <el-dropdown class="fl" @command="handleCommand_signin">
                      <span class="el-icon-more"></span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item
                          :command="{ type: 'export', row }"
                          v-if="!isSigninByNFC"
                        >
                          {{ $T("查看二维码") }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          class="delete"
                          :command="{ type: 'delete', row }"
                        >
                          {{ $T("删除") }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                    <!-- <span
                    v-if="!isSigninByNFC"
                    @click="CetButton_export_statusTrigger_out(row)"
                    class="eem-row-handle fl mrJ3"
                  >
                    {{ $T("查看二维码") }}
                  </span>
                  <span @click="handleDeleteSignin(row)" class="delete fl">
                    {{ $T("删除") }}
                  </span> -->
                  </div>
                </template>
              </ElTableColumn>
            </CetTable>
          </div>
          <div class="mtJ3">
            <div class="fr">
              <el-dropdown class="fr more mlJ3" @command="handleCommand2">
                <span class="el-dropdown-link">
                  {{ $T("更多") }}
                  <i class="el-icon-arrow-up el-icon--right"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="batchExport1"
                    v-if="!isSigninByNFC"
                  >
                    {{ $T("导出二维码") }}
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" class="delete">
                    {{ $T("批量删除") }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <!-- delete按钮组件 -->
              <!-- <CetButton
                v-bind="CetButton_delete1"
                v-on="CetButton_delete1.event"
                class="fl mrJ1"
              ></CetButton> -->
              <!-- edit按钮组件 -->
              <!-- <CetButton v-bind="CetButton_edit1" v-on="CetButton_edit1.event"></CetButton> -->
              <!-- add按钮组件 -->
              <CetButton
                class="fl mrJ1"
                v-bind="CetButton_batchAdd"
                v-on="CetButton_batchAdd.event"
              ></CetButton>
              <CetButton
                class="fl"
                v-bind="CetButton_add1"
                v-on="CetButton_add1.event"
              ></CetButton>
              <!--批量导出按钮组件 -->
              <!-- <CetButton
                class="fl mlJ1"
                v-if="!isSigninByNFC"
                v-bind="CetButton_batchExport1"
                v-on="CetButton_batchExport1.event"
              ></CetButton> -->
            </div>
          </div>
        </div>
      </div>

      <!-- 巡检对象 -->
      <div class="fullheight flex-auto" style="flex: 2">
        <div class="fullfilled eem-container flex-column">
          <div class="mbJ3">
            <span class="common-title-H2">{{ $T("巡检对象") }}</span>
          </div>
          <div class="mbJ3">
            {{ $T("共{0}个巡检对象", CetTable_inspectObj.data.length) }}
          </div>
          <div class="flex-auto">
            <CetTable
              :data.sync="CetTable_inspectObj.data"
              :dynamicInput.sync="CetTable_inspectObj.dynamicInput"
              v-bind="CetTable_inspectObj"
              v-on="CetTable_inspectObj.event"
              @selection-change="handleObjChange_out"
            >
              <ElTableColumn
                type="selection"
                width="50"
                align="center"
              ></ElTableColumn>
              <template v-for="item in Columns_inspectObj">
                <ElTableColumn
                  :key="item.label"
                  v-bind="item"
                  headerAlign="left"
                  align="left"
                ></ElTableColumn>
              </template>
              <ElTableColumn
                v-bind="ElTableColumn_operate"
                headerAlign="left"
                align="left"
                fixed="right"
              >
                <template slot-scope="{ row }">
                  <span @click="handleDelete(row)" class="delete">
                    {{ $T("删除") }}
                  </span>
                </template>
              </ElTableColumn>
            </CetTable>
          </div>
          <div class="mtJ3">
            <!-- 巡检对象批量删除按钮组件 -->
            <CetButton
              class="fr"
              v-bind="CetButton_muldelete"
              v-on="CetButton_muldelete.event"
            ></CetButton>
          </div>
        </div>
      </div>
    </div>
    <!-- 新建/编辑分组 -->
    <addOrEditGroupDialog
      v-bind="addOrEditGroupDialog"
      v-on="addOrEditGroupDialog.event"
    ></addOrEditGroupDialog>
    <!-- 新建/编辑签到点 -->
    <addOrEditSigninDialog
      v-bind="addOrEditSigninDialog"
      :isSigninByNFC_in="isSigninByNFC"
      v-on="addOrEditSigninDialog.event"
    ></addOrEditSigninDialog>
    <!-- 二维码弹窗 -->
    <qrCode v-bind="qrCode"></qrCode>
    <!-- 关联对象弹窗 -->
    <inspectObjDialog
      v-bind="inspectObjDialog"
      v-on="inspectObjDialog.event"
    ></inspectObjDialog>
    <!-- 批量新建签到点弹窗 -->
    <batchAddSingin
      v-bind="batchAddSingin"
      :isSigninByNFC_in="isSigninByNFC"
      v-on="batchAddSingin.event"
    ></batchAddSingin>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import Sortable from "sortablejs";
import addOrEditGroupDialog from "./dialogs/addOrEditGroupDialog";
import addOrEditSigninDialog from "./dialogs/addOrEditSigninDialog";
import qrCode from "./dialogs/qrCode";
import inspectObjDialog from "./dialogs/inspectObjDialog.vue";
import batchAddSingin from "./dialogs/batchAddSignin.vue";
import common from "eem-utils/common";

export default {
  name: "signinmanage",
  components: {
    addOrEditGroupDialog,
    addOrEditSigninDialog,
    qrCode,
    inspectObjDialog,
    batchAddSingin
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.localStorage) {
          return false;
        } else {
          var storage = window.localStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },
  data() {
    return {
      groupList: [], // 分组列表
      qrCode: {
        openTrigger_in: new Date().getTime(),
        inputData_in: null,
        parentList: [] // 设备的父节点
      },
      CetButton_batchExport: {
        visible_in: true,
        title: $T("导出二维码"),
        type: "primary",
        plain: true,
        size: "mini",
        event: {
          statusTrigger_out: this.CetButton_batchExport_statusTrigger_out
        }
      },
      CetButton_batchExport1: {
        visible_in: true,
        disable_in: false,
        title: $T("导出二维码"),
        type: "primary",
        plain: true,
        size: "mini",
        event: {
          statusTrigger_out: this.CetButton_batchExport1_statusTrigger_out
        }
      },
      // delete组件
      CetButton_delete: {
        visible_in: true,
        disable_in: false,
        title: $T("删除"),
        type: "danger",
        plain: true,
        size: "mini",
        event: {
          statusTrigger_out: this.CetButton_delete_statusTrigger_out
        }
      },
      // edit组件
      CetButton_edit: {
        visible_in: true,
        disable_in: false,
        title: $T("编辑"),
        type: "primary",
        plain: true,
        size: "mini",
        event: {
          statusTrigger_out: this.CetButton_edit_statusTrigger_out
        }
      },
      // add组件
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("新建"),
        type: "primary",
        plain: true,
        size: "mini",
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      // delete组件
      CetButton_delete1: {
        visible_in: true,
        disable_in: true,
        title: $T("批量删除"),
        type: "danger",
        plain: true,
        size: "mini",
        event: {
          statusTrigger_out: this.CetButton_delete1_statusTrigger_out
        }
      },
      // muldelete组件
      CetButton_muldelete: {
        visible_in: true,
        disable_in: true,
        title: $T("批量删除"),
        type: "danger",
        plain: true,
        size: "mini",
        event: {
          statusTrigger_out: this.CetButton_muldelete_statusTrigger_out
        }
      },
      // edit组件
      CetButton_edit1: {
        visible_in: true,
        disable_in: false,
        title: $T("编辑"),
        type: "primary",
        plain: true,
        size: "mini",
        event: {
          statusTrigger_out: this.CetButton_edit1_statusTrigger_out
        }
      },
      // add组件
      CetButton_add1: {
        visible_in: true,
        disable_in: false,
        title: $T("新建"),
        type: "primary",
        plain: true,
        size: "mini",
        event: {
          statusTrigger_out: this.CetButton_add1_statusTrigger_out
        }
      },
      CetButton_batchAdd: {
        visible_in: true,
        disable_in: false,
        title: $T("批量新建"),
        type: "primary",
        plain: true,
        size: "mini",
        event: {
          statusTrigger_out: this.CetButton_batchAdd_statusTrigger_out
        }
      },
      activeGroup: null,
      currentSignIn: null, // 当前选中的签到点
      // inspectObj表格组件
      CetTable_inspectObj: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [{}],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {
          record_out: this.CetTable_inspectObj_record_out,
          outputData_out: this.CetTable_inspectObj_outputData_out
        }
      },
      Columns_inspectObj: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          showOverflowTooltip: true,
          //minWidth: "200",  //该宽度会自适应
          width: "60" //绝对宽度
          //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("名称"), //列名
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        },
        {
          prop: "parentname", // 支持path a[0].b
          label: $T("父节点路径"), //列名
          showOverflowTooltip: true,
          minWidth: "200", //该宽度会自适应
          // width: "160" //绝对宽度
          formatter: this.formatter
        },
        {
          prop: "tree_id", // 支持path a[0].b
          label: "ID", //列名
          showOverflowTooltip: true,
          formatter: this.formatter
          // width: "160" //绝对宽度
        }
      ],
      ElTableColumn_operate: {
        //type: "",      // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: $T("操作"), //列名
        showOverflowTooltip: true
        // width: "60" //绝对宽度
      },
      // 编辑分组弹窗
      editGroupDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      // 新建/编辑分组弹窗
      addOrEditGroupDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        isAdd: null,
        event: {
          saveData_out: this.addGroupData_out
        }
      },
      // 新建签到点弹窗
      addOrEditSigninDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        isAdd: null,
        groupList: [],
        activeGroup: {},
        event: {
          saveData_out: this.addSigninData_out
        }
      },
      // signin表格组件
      CetTable_signin: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {
          record_out: this.CetTable_signin_record_out,
          outputData_out: this.CetTable_signin_outputData_out
        }
      },
      signinId: null,
      selectedData: [], // 签到点多选
      selectedData1: [], // 巡检对象多选
      isSigninByNFC: false,
      // 巡检对象弹窗
      inspectObjDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        tableData: null,
        event: {
          saveData_out: this.insepectObjDialog_saveData_out
        }
      },
      // 批量新建签到点弹窗
      batchAddSingin: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        tableData: null,
        groupList: [],
        activeGroup: {},
        event: {
          saveData_out: this.batchAddSingin_saveData_out
        }
      }
    };
  },
  watch: {
    activeGroup: {
      deep: true,
      handler(val) {
        if (val) {
          this.CetButton_batchExport1.disable_in = false;
          this.CetButton_delete.disable_in = false;
          this.CetButton_edit.disable_in = false;
          this.CetButton_add1.disable_in = false;
          // 查当前分组下的签到点
          if (
            val.registrationpoint_model &&
            val.registrationpoint_model.length
          ) {
            this.getSigninList(val.registrationpoint_model);
          } else {
            this.CetTable_signin.data = [];
          }
        } else {
          this.CetButton_batchExport1.disable_in = true;
          this.CetButton_delete.disable_in = true;
          this.CetButton_edit.disable_in = true;
          this.CetButton_add1.disable_in = true;
        }
        this.addOrEditSigninDialog.activeGroup = val;
        this.batchAddSingin.activeGroup = val;
      }
    },
    currentSignIn(val) {
      if (val && val.id !== -1) {
        // 获取签到点下的设备
        this.getSigninPoint(val.id);
      } else {
        this.CetTable_inspectObj.data = [];
      }
    },
    groupList(val) {
      this.addOrEditSigninDialog.groupList = val;
      this.batchAddSingin.groupList = val;
    }
  },
  methods: {
    //获取移动端系统配置信息-巡检签到方式：10-nfc签到，20-二维码扫码签到
    getAppConfigSystemInfo_out() {
      this.isSigninByNFC = false;
      customApi.getAppConfigSystemInfo().then(res => {
        if (res.code === 0) {
          let inspectSignMode = this._.get(res, "data.inspectSignMode", 20);
          this.isSigninByNFC = inspectSignMode === 10 ? true : false;
        }
      });
    },
    //根据项目下载签到点二维码
    CetButton_batchExport_statusTrigger_out() {
      if (!this.projectId) {
        return;
      }
      const urlStr = `/eem-service/v1/signin/downloadQrCode/project?projectId=${this.projectId}`;
      common.downExcelGET(urlStr, {}, this.token, this.projectId);
    },
    //根据签到点分组下载签到点二维码
    CetButton_batchExport1_statusTrigger_out(val) {
      if (!this.activeGroup) {
        return;
      }
      const urlStr = `/eem-service/v1/signin/downloadQrCode/signGroup?signGroupId=${this.activeGroup.id}`;
      common.downExcelGET(urlStr, {}, this.token, this.projectId);
    },
    // 根据签到点下载签到点二维码
    CetButton_export_statusTrigger_out(val) {
      if (!val) {
        return;
      }
      this.qrCode.inputData_in = this._.cloneDeep(val);
      this.qrCode.openTrigger_in = new Date().getTime();
    },
    // 删除组
    CetButton_delete_statusTrigger_out(val) {
      this.$confirm($T("确定要删除所选项吗？"), $T("删除确认"), {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消")
      }).then(() => {
        customApi.groupManage("DELETE", [this.activeGroup.id]).then(res => {
          if (res.code === 0) {
            this.$message.info($T("删除成功"));
            this.activeGroup = null;
            this.getGroupList();
          }
        });
      });
    },
    // 编辑分组
    CetButton_edit_statusTrigger_out(val) {
      this.addOrEditGroupDialog.isAdd = false;
      this.addOrEditGroupDialog.inputData_in = this.activeGroup;
      this.addOrEditGroupDialog.openTrigger_in = new Date().getTime();
    },
    // 新建分组
    CetButton_add_statusTrigger_out(val) {
      this.addOrEditGroupDialog.isAdd = true;
      this.addOrEditGroupDialog.openTrigger_in = new Date().getTime();
    },
    deleteSignin_out(params) {
      this.$confirm($T("确定要删除所选项吗？"), $T("删除确认"), {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消")
      }).then(() => {
        customApi.deleteSignin(this.activeGroup.id, params).then(res => {
          if (res.code === 0) {
            this.$message.info($T("删除成功"));
            this.getGroupList();
          }
        });
      });
    },
    // 签到点表格点击多选框
    handleSelectionChange_out(val) {
      this.selectedData = val;
      if (val && val.length > 0) {
        this.CetButton_delete1.disable_in = false;
      } else {
        this.CetButton_delete1.disable_in = true;
      }
    },
    // 巡检对象表格点击多选框
    handleObjChange_out(val) {
      this.selectedData1 = val;
      if (val && val.length > 0) {
        this.CetButton_muldelete.disable_in = false;
      } else {
        this.CetButton_muldelete.disable_in = true;
      }
    },
    // 删除签到点
    handleDeleteSignin(row) {
      this.deleteSignin_out([row.id]);
    },
    // 批量删除签到点
    CetButton_delete1_statusTrigger_out(val) {
      const idArr = this.selectedData.map(item => item.id);
      this.deleteSignin_out(idArr);
    },
    // 编辑签到点
    CetButton_edit1_statusTrigger_out(row) {
      let arr = this._.cloneDeep(this.CetTable_inspectObj.data);
      const callback = () => {
        this.addOrEditSigninDialog.isAdd = false;
        this.addOrEditSigninDialog.inputData_in = {
          ...row,
          inspectObj: arr
        };
        this.addOrEditSigninDialog.openTrigger_in = new Date().getTime();
      };
      if (this.currentSignIn.id !== row.id) {
        customApi.getSigninList(row.id).then(res => {
          if (res.code === 0) {
            arr = res.data;
            callback();
          }
        });
      } else {
        callback();
      }
    },
    // 新建签到点
    CetButton_add1_statusTrigger_out(val) {
      this.addOrEditSigninDialog.isAdd = true;
      this.addOrEditSigninDialog.openTrigger_in = new Date().getTime();
    },
    // position表格输出
    CetTable_inspectObj_record_out(val) {},
    CetTable_inspectObj_outputData_out(val) {},
    formatter(row, column, cellValue) {
      return cellValue || "--";
    },
    // 删除巡检对象
    deleteSigninPoint_out(params) {
      this.$confirm($T("确定要删除所选项吗？"), $T("删除确认"), {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消")
      }).then(() => {
        customApi.deleteSigninPoint(this.currentSignIn.id, params).then(res => {
          if (res.code === 0) {
            this.$message.info($T("删除成功"));
            this.getSigninPoint(this.currentSignIn.id);
          }
        });
      });
    },
    handleDelete(row) {
      this.deleteSigninPoint_out([{ id: row.id, modelLabel: row.modelLabel }]);
    },
    // 批量删除巡检对象
    CetButton_muldelete_statusTrigger_out() {
      const delArr = this.selectedData1.map(item => {
        return {
          id: item.id,
          modelLabel: item.modelLabel
        };
      });
      this.deleteSigninPoint_out(delArr);
    },
    // 获取分组列表
    getGroupList() {
      customApi.groupManage("GET").then(res => {
        if (res.code === 0) {
          this.groupList = res.data || [];
          if (this.signinId) {
            this.groupList.forEach(item => {
              if (Array.isArray(item.registrationpoint_model)) {
                item.registrationpoint_model.forEach(item1 => {
                  if (item1.id === this.signinId) {
                    this.activeGroup = this._.cloneDeep(item);
                    this.currentSignIn = this._.cloneDeep(item1);
                  }
                });
              }
            });
          }
          if (!this.activeGroup) {
            this.activeGroup = this.groupList[0]; // 选中组
          }
          this.activeGroup = this.groupList.find(
            item => item.id === this.activeGroup.id
          );
          // 获取签到点
          this.activeGroup &&
            this.getSigninList(this.activeGroup.registrationpoint_model);
        }
      });
    },
    // 获取签到点列表
    getSigninList(list) {
      this.CetTable_signin.data = list;
    },
    // 获取签到点下的查询设备
    getSigninPoint(id) {
      customApi.getSigninList(id).then(res => {
        if (res.code === 0) {
          this.CetTable_inspectObj.data = res.data;
        }
      });
    },
    // 表格拖拽
    setSort() {
      const el = this.$refs.dragTable.$refs.cetTable.$el.querySelectorAll(
        ".el-table__body-wrapper > table > tbody"
      )[0];
      Sortable.create(el, {
        ghostClass: "sortable-ghost",
        onEnd: evt => {
          const targetRow = this.CetTable_signin.data.splice(
            evt.oldIndex,
            1
          )[0];
          this.CetTable_signin.data.splice(evt.newIndex, 0, targetRow);
          // 排序
          const sortTableData = [];
          this.CetTable_signin.data.forEach((item, index) => {
            const obj = {
              id: item.id,
              sort: index + 1
            };
            sortTableData.push(obj);
          });
          customApi.sortSignin(this.activeGroup.id, sortTableData).then(res => {
            if (res.code === 0) {
              this.$message.info($T("排序成功"));
              // 获取签到点
              this.getGroupList();
            }
          });
        }
      });
    },
    addGroupData_out(val) {
      if (this.addOrEditGroupDialog.isAdd) {
        customApi.groupManage("POST", val).then(res => {
          if (res.code === 0) {
            this.getGroupList();
            this.activeGroup = res.data;
          }
        });
      } else {
        customApi.groupManage("PATCH", val).then(res => {
          if (res.code === 0) {
            this.getGroupList();
          }
        });
      }
      if (!this.activeGroup) {
        this.activeGroup = this.groupList[0];
      }
    },
    addSigninData_out() {
      this.getGroupList();
      const obj = this.groupList.find(item => item.id === this.activeGroup.id);
      this.getSigninList(obj.registrationpoint_model);
    },
    // signin表格输出
    CetTable_signin_record_out(val) {
      if (this.signinId && this.currentSignIn) {
        this.signinId = null;
        this.$refs.dragTable.$refs.cetTable.setCurrentRow(this.currentSignIn);
      } else {
        this.currentSignIn = val;
      }
    },
    CetTable_signin_outputData_out(val) {},
    init() {
      this.signinId = null;
      if (!this._.isEmpty(this.$route.params)) {
        const params = this.$route.params;
        this.signinId = params.id || null;
      }
      this.getGroupList();
      this.getAppConfigSystemInfo_out();
      this.$nextTick(() => {
        this.setSort();
      });
    },
    //分组中更多选择
    handleCommand_group(command) {
      if (command === "edit") {
        this.CetButton_edit_statusTrigger_out();
      } else if (command === "delete") {
        this.CetButton_delete_statusTrigger_out();
      }
    },
    // 签到点更多选择
    handleCommand_signin({ type, row }) {
      if (type === "export") {
        this.CetButton_export_statusTrigger_out(row);
      } else if (type === "delete") {
        this.handleDeleteSignin(row);
      } else if (type === "object") {
        let arr = this._.cloneDeep(this.CetTable_inspectObj.data);
        const callback = () => {
          this.inspectObjDialog.tableData = arr;
          this.inspectObjDialog.inputData_in = row;
          this.inspectObjDialog.openTrigger_in = new Date().getTime();
        };
        if (this.currentSignIn.id !== row.id) {
          customApi.getSigninList(row.id).then(res => {
            if (res.code === 0) {
              arr = res.data;
              callback();
            }
          });
        } else {
          callback();
        }
      }
    },
    // 关联巡检对象保存
    insepectObjDialog_saveData_out(object, row) {
      const arr = [];
      if (object.length) {
        object.forEach(item => {
          const obj = {
            objectid: item.objectid,
            objectlabel: item.objectlabel
          };
          arr.push(obj);
        });
      }
      const formData = {
        id: row.id,
        image: row.image,
        interval: row.interval,
        name: row.name,
        nfc: row.nfc,
        address: row.address,
        signInGroupIds: row.signInGroupIds,
        children: arr
      };
      customApi.signinManage("PATCH", formData).then(res => {
        if (res.code === 0) {
          this.$message.success($T("保存成功"));
          this.addSigninData_out();
        }
      });
    },
    // 签到点下面按钮
    handleCommand2(command) {
      if (command === "batchExport1") {
        this.CetButton_batchExport1_statusTrigger_out();
      } else if (command === "delete") {
        this.CetButton_delete1_statusTrigger_out();
      }
    },
    // 批量新建
    CetButton_batchAdd_statusTrigger_out() {
      this.batchAddSingin.openTrigger_in = Date.now();
    },
    // 签到点保密
    batchAddSingin_saveData_out() {
      this.addSigninData_out();
    }
  },
  activated() {
    this.init();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .sortable-ghost {
    background: #0e1b47;
  }
  .delete {
    @include font_color(Sta3);
    cursor: pointer;
  }
  .more {
    @include line_height(Hm);
  }
}
.delete {
  @include font_color(Sta3);
}
</style>
