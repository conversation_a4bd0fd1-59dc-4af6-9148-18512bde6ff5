﻿<template>
  <!-- 1弹窗组件 -->
  <CetDialog
    v-bind="CetDialog_1"
    v-on="CetDialog_1.event"
    class="CetDialog small"
  >
    <div class="clearfix eem-cont-c1">
      <el-row :gutter="$J3">
        <el-col :span="12">
          <div class="mbJ1">
            {{ $T("时段名称") }}
            <span style="color: red">*</span>
          </div>
          <ElSelect
            v-model="ElSelect_1.value"
            v-bind="ElSelect_1"
            v-on="ElSelect_1.event"
          >
            <ElOption
              v-for="item in ElOption_1.options_in"
              :key="item[ElOption_1.key]"
              :label="item[ElOption_1.label]"
              :value="item[ElOption_1.value]"
              :disabled="item[ElOption_1.disabled]"
            ></ElOption>
          </ElSelect>
        </el-col>
        <el-col :span="12">
          <div class="mbJ1" v-show="ElInput_1.visible_in">
            {{ $T("自定义名称") }}
            <span style="color: red">*</span>
          </div>
          <ElInput
            v-show="ElInput_1.visible_in"
            v-model="ElInput_1.value"
            v-bind="ElInput_1"
            v-on="ElInput_1.event"
          ></ElInput>
        </el-col>
      </el-row>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import { httping } from "@omega/http";
export default {
  name: "AddTime",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },

  computed: {
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: $T("新增时段"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElSelect_1: {
        value: 1,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入内容"),
        visible_in: true,
        style: {
          width: "100%"
        },
        event: {}
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.getTimeperiodlabel();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    //获取时段名称
    getTimeperiodlabel() {
      var vm = this;
      vm.ElOption_1.options_in = [];
      var data = {
        rootLabel: "timeperiodlabel"
      };
      httping({
        url: `/eem-service/v1/common/query/oneLayer`,
        method: "POST",
        data
      }).then(function (response) {
        var selectData = [];
        if (response.code === 0 && response.data.length) {
          selectData = response.data.map(item => {
            return {
              id: item.id,
              text: item.name
            };
          });
        }
        var res = {};
        res.id = 0;
        res.text = $T("自定义");
        selectData.push(res);
        vm.ElOption_1.options_in = vm._.cloneDeep(selectData);
        vm.ElSelect_1.value = selectData[0].id;
        vm.ElSelect_1_change_out(selectData[0].id);
      });
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      var text = "";
      if (this.ElSelect_1.value !== 0) {
        text = this.ElOption_1.options_in.filter(
          item => item.id == this.ElSelect_1.value
        )[0].text;
      } else {
        if (!this.ElInput_1.value) {
          this.$message({
            message: $T("请填写时段名称"),
            type: "warning"
          });
          return;
        } else {
          text = this.ElInput_1.value;
        }
      }
      this.$emit("confirm_out", text);
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    ElSelect_1_change_out(val) {
      if (val === 0) {
        this.ElInput_1.value = "";
        this.ElInput_1.visible_in = true;
      } else {
        this.ElInput_1.visible_in = false;
      }
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped></style>
