// 定制的 elment-ui 样式
@import "./elementui/elementui-custom.scss";

// elemnt修复的样式
@import "./elementui/elementui-fix.scss";
// 通用样式类
@import "./common.scss";

//eem通用样式
@import "./eemCommon.scss";

//自定义主题样式调整；对element-UI样式配置进行调整
@import "./custemThen.scss";

/* body布局, 字体颜色等样式*/
body {
  @include font_color(T1);
  @include background(BG);
  background-size: cover;
  scroll-behavior: smooth;
}

body {
  // 滚动条样式
  ::-webkit-scrollbar {
    width: 12px !important;
    height: 12px !important;
  }
  //任务栏滚动条宽度
  .frame-navbars {
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
  }
  // 滚动槽
  ::-webkit-scrollbar-track {
    // background: transparent;
    @include background_color(BG12);
  }
  //滚动条交汇处样式
  ::-webkit-scrollbar-corner {
    @include background_color(BG12);
  }
  // 滚动条滑块
  ::-webkit-scrollbar-thumb {
    border-radius: 0px !important;
    right: 4px !important;
    cursor: pointer !important;
    @include background_color(B2);
    &:hover {
      @include background_color(B1);
      cursor: pointer !important;
    }
    &:active {
      @include background_color(BG14);
    }
  }
  //element-ui滚动条样式
  .el-scrollbar__bar.is-vertical {
    width: 12px !important;
  }
  .el-scrollbar__bar.is-horizontal {
    height: 12px !important;
  }
  .el-scrollbar__thumb {
    border-radius: 0px !important;
    @include background_color(B2);
    &:hover {
      @include background_color(B1);
      cursor: pointer !important;
    }
    &:active {
      @include background_color(BG14);
    }
  }
  //悬浮框样式
  .el-tooltip__popper {
    @include background_color(BG12);
    @include font_color(T1);
    @include border_color(BG12);
    @include box_shadow(S2);
    max-width: calc(100% - 40px);
  }
  .el-tooltip__popper[x-placement^="bottom"] .popper__arrow:after {
    @include border_direction_color(BG11, "bottom", !important);
  }
  /* 朝下 */
  .el-tooltip__popper[x-placement^="top"] .popper__arrow:after {
    @include border_direction_color(BG11, "top", !important);
  }
  // 改变三角形外边框
  // 三角形朝下的时候
  .el-tooltip__popper[x-placement^="bottom"] .popper__arrow {
    border: transparent;
  }
  // 三角形朝上的时候
  .el-tooltip__popper[x-placement^="top"] .popper__arrow {
    border: transparent;
  }
}
