<template>
  <div class="page">
    <el-container class="fullheight flex-column eem-container">
      <div class="clearfix mbJ3 lh32">
        <div class="fl">
          <span class="common-title-H2">
            {{ $moment(CetDatePicker_1.val).format($T("YYYY年MM月"))
            }}{{ $T("最大需量申报节省费用表") }}
          </span>
        </div>
        <el-tooltip
          effect="light"
          :content="
            `${$T('偏差费用')}` +
            `=（${$T('实际最大需量')}` +
            `-${$T('申报需量')}` +
            `*${$T('上限比例')}` +
            `）*${$T('需量电价')}` +
            `*${$T('惩罚系数')}` +
            `，${$T('不计算负偏差时')}` +
            `，${$T('偏差费用为0。')}`
          "
        >
          <i class="el-icon-question fl mtJ1 mlJ1 fsH3"></i>
        </el-tooltip>
        <CetButton
          class="fr mlJ1"
          v-bind="CetButton_1"
          v-on="CetButton_1.event"
        ></CetButton>
        <!-- 向后查询按钮 -->
        <CetButton
          class="fr custom—square"
          v-bind="CetButton_3"
          v-on="CetButton_3.event"
        ></CetButton>
        <CustomElDatePicker
          class="fr mlJ mrJ"
          :prefix_in="$T('选择月份')"
          v-bind="CetDatePicker_1.config"
          v-model="CetDatePicker_1.val"
        />
        <!-- <div class="basic-box fr ml5 mr5">
          <div class="basic-box-label" style="height: 32px">时间</div>
          <el-date-picker
            class="fr"
            v-model="CetDatePicker_1.val"
            v-bind="CetDatePicker_1.config"
          ></el-date-picker>
        </div> -->
        <!-- 向前查询按钮 -->
        <CetButton
          class="fr custom—square"
          v-bind="CetButton_2"
          v-on="CetButton_2.event"
        ></CetButton>
      </div>
      <div style="flex: 1" class="minWH">
        <div class="fullheight flex-column">
          <div style="flex: 1" class="minWH">
            <CetTable
              :data.sync="CetTable_1.data"
              :dynamicInput.sync="CetTable_1.dynamicInput"
              v-bind="CetTable_1"
              v-on="CetTable_1.event"
            >
              <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
              <ElTableColumn v-bind="ElTableColumn_accountName"></ElTableColumn>
              <ElTableColumn v-bind="ElTableColumn_accountNo"></ElTableColumn>
              <ElTableColumn v-bind="ElTableColumn_volume"></ElTableColumn>
              <ElTableColumn v-bind="ElTableColumn_volumeValue"></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_declareValue"
              ></ElTableColumn>
              <ElTableColumn v-bind="ElTableColumn_demandValue"></ElTableColumn>
              <ElTableColumn v-bind="ElTableColumn_savedValue"></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_billingValue"
              ></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_deviationCost"
              ></ElTableColumn>
            </CetTable>
          </div>
          <div class="mtJ1">
            {{ $T("节省总费用") }}：
            <span class="fsH1">{{ totalCount }}</span>
            {{ $T("元") }}
          </div>
        </div>
      </div>
    </el-container>
  </div>
</template>
<script>
import { httping } from "@omega/http";
export default {
  name: "DemandReportForm",
  components: {},

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },
  props: {
    currentNode: {
      type: Object
    }
  },

  data() {
    const language = window.localStorage.getItem("omega_language") === "en";
    return {
      totalCount: 0, // 总节省费用
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: "",
        plain: true,
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetButton_3: {
        visible_in: true,
        disable_in: true,
        title: "",
        plain: true,
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      CetDatePicker_1: {
        disable_in: false,
        val: this.$moment().add(0, "d").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "month",
          rangeSeparator: "-",
          clearable: false,
          size: "small",
          style: {
            display: "inline-block",
            width: "200px"
          },
          pickerOptions: {
            disabledDate(time) {
              return time.getTime() > Date.now();
            }
          }
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        // defaultSort: null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: "#", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        // fixed: "left",
        //minWidth: "200",  //该宽度会自适应
        width: "50" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_accountName: {
        //type: "",      // selection 勾选 index 序号
        prop: "accountName", // 支持path a[0].b
        label: $T("进线名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "190" : "160" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_accountNo: {
        //type: "",      // selection 勾选 index 序号
        prop: "accountNo", // 支持path a[0].b
        label: $T("户号"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "160" : "150" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_volume: {
        //type: "",      // selection 勾选 index 序号
        prop: "volume", // 支持path a[0].b
        label: $T("容量") + "（kVA）", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "160" : "150" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_volumeValue: {
        //type: "",      // selection 勾选 index 序号
        prop: "volumeValue", // 支持path a[0].b
        label: $T("容量费用（元）"), //列名
        headerAlign: "right",
        align: "right",
        showOverflowTooltip: true,
        minWidth: language ? "170" : "150" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_declareValue: {
        //type: "",      // selection 勾选 index 序号
        prop: "declareValue", // 支持path a[0].b
        label: $T("申报需量") + "（kW）", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "280" : "150" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_demandValue: {
        //type: "",      // selection 勾选 index 序号
        prop: "demandValue", // 支持path a[0].b
        label: $T("需量费用（元）"), //列名
        headerAlign: "right",
        align: "right",
        showOverflowTooltip: true,
        minWidth: language ? "210" : "150" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_savedValue: {
        //type: "",      // selection 勾选 index 序号
        prop: "savedValue", // 支持path a[0].b
        label: $T("预计节省费用（元）"), //列名
        headerAlign: "right",
        align: "right",
        showOverflowTooltip: true,
        minWidth: language ? "220" : "150" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_billingValue: {
        //type: "",      // selection 勾选 index 序号
        prop: "billingValue", // 支持path a[0].b
        label: $T("供电局账单值") + "（kW）", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "320" : "200" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_deviationCost: {
        //type: "",      // selection 勾选 index 序号
        prop: "deviationCost", // 支持path a[0].b
        label: $T("偏差费用（元）"), //列名
        headerAlign: "right",
        align: "right",
        showOverflowTooltip: true,
        minWidth: language ? "190" : "150" //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      }
    };
  },
  watch: {
    "CetDatePicker_1.val": function (val) {
      if (
        this.$moment(val).startOf("month").valueOf() >=
        this.$moment().startOf("month").valueOf()
      ) {
        this.CetButton_3.disable_in = true;
      } else {
        this.CetButton_3.disable_in = false;
      }
      this.getTabDate();
    },
    currentNode() {
      this.getTabDate();
    }
  },

  methods: {
    // 获取列表数据
    getTabDate() {
      if (!this.currentNode) {
        return;
      }
      this.CetTable_1.data = [];
      this.totalCount = 0;
      var data = {
        startTime: this.$moment(this.CetDatePicker_1.val)
          .startOf("month")
          .valueOf(),
        endTime: this.$moment(this.CetDatePicker_1.val)
          .endOf("month")
          .valueOf(),
        node: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          name: this.currentNode.name
        },
        projectId: this.projectId
      };
      httping({
        url: "/eem-service/v1/demand/manage/demandStatement",
        method: "POST",
        data
      }).then(response => {
        if (response.code == 0 && response.data && response.data.length > 0) {
          this.CetTable_1.data = this._.cloneDeep(response.data);
          this.CetTable_1.data.forEach((item, index) => {
            item.index = index + 1;
            item.volume =
              item.volume == "null" ||
              item.volume == "Infinity" ||
              item.volume == "NaN"
                ? "--"
                : item.volume && item.volume.toFixed(2);
            item.volumeValue =
              item.volumeValue == "null" ||
              item.volumeValue == "Infinity" ||
              item.volumeValue == "NaN"
                ? "--"
                : item.volumeValue && item.volumeValue.toFixed(2);
            item.declareValue =
              item.declareValue == "null" ||
              item.declareValue == "Infinity" ||
              item.declareValue == "NaN"
                ? "--"
                : item.declareValue && item.declareValue.toFixed(2);
            item.demandValue =
              item.demandValue == "null" ||
              item.demandValue == "Infinity" ||
              item.demandValue == "NaN"
                ? "--"
                : item.demandValue && item.demandValue.toFixed(2);
            item.savedValue =
              item.savedValue == "null" ||
              item.savedValue == "Infinity" ||
              item.savedValue == "NaN"
                ? "--"
                : item.savedValue && item.savedValue.toFixed(2);
            item.billingValue =
              item.billingValue == "null" ||
              item.billingValue == "Infinity" ||
              item.billingValue == "NaN"
                ? "--"
                : item.billingValue && item.billingValue.toFixed(2);
            item.deviationCost =
              item.deviationCost == "null" ||
              item.deviationCost == "Infinity" ||
              item.deviationCost == "NaN"
                ? "--"
                : item.deviationCost && item.deviationCost.toFixed(2);
          });
          this.CetTable_1.data[this.CetTable_1.data.length - 1].accountName =
            $T("合计");
          this.totalCount = (
            Number(
              this.CetTable_1.data[this.CetTable_1.data.length - 1].savedValue
            ) +
            Number(
              this.CetTable_1.data[this.CetTable_1.data.length - 1]
                .deviationCost
            )
          ).toFixed(2);
        }
      });
    },
    // 导出
    CetButton_1_statusTrigger_out(val) {
      this.CetTable_1.exportFileName = `${this.$moment(
        this.CetDatePicker_1.val
      ).format($T("YYYY年MM月"))}${$T("最大需量申报节省费用表")}`;
      this.CetTable_1.exportTrigger_in = this._.cloneDeep(val);
    },
    CetButton_2_statusTrigger_out(val) {
      const date = this.$moment(this.CetDatePicker_1.val);
      this.CetDatePicker_1.val = date.subtract(1, "month").valueOf();
    },
    CetButton_3_statusTrigger_out(val) {
      const date = this.$moment(this.CetDatePicker_1.val);
      if (
        date.startOf("month").valueOf() !=
        this.$moment().startOf("month").valueOf()
      ) {
        this.CetDatePicker_1.val = date.add(1, "month").valueOf();
      }
    },
    CetTable_1_outputData_out(val) {},
    CetTable_1_record_out(val) {}
  },

  created: function () {},
  activated: function () {
    this.getTabDate();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.DemandReportFormTitle {
  font-style: normal;
  font-size: 20px;
  & > div {
    display: inline-block;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #0152d9;
    position: relative;
    top: 10px;
    & > i {
      color: #fff;
      position: relative;
      top: -13px;
      left: 8px;
    }
  }
}
.circle {
  display: inline-block;
  width: 16px;
  height: 16px;
  font-size: 14px;
  line-height: 20px;
  border-radius: 50%;
  text-align: center;
  @include background_color(ZS);
  color: #fff;
}
</style>
