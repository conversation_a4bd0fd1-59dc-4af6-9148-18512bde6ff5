<template>
  <div class="page eem-common">
    <el-container class="fullheight flex-column eem-container">
      <div class="padding0 mbJ3">
        <el-col :span="8">
          <div class="text-ellipsis">
            <el-tooltip
              effect="light"
              :content="`${$moment(CetDatePicker_time.val).format(
                $T('YYYY年MM月')
              )}${$T('申报建议')}`"
            >
              <span class="common-title-H2">
                {{ $moment(CetDatePicker_time.val).format($T("YYYY年MM月"))
                }}{{ $T("申报建议") }}
              </span>
            </el-tooltip>
          </div>
        </el-col>
        <el-col :span="16">
          <CetButton
            class="fr"
            v-bind="CetButton_1"
            v-on="CetButton_1.event"
          ></CetButton>
          <CetButton
            class="mrJ1 fr"
            v-bind="CetButton_2"
            v-on="CetButton_2.event"
          ></CetButton>
          <div class="fr mrJ1">
            <!-- 向前查询按钮 -->
            <CetButton
              class="fr custom—square"
              v-bind="CetButton_next"
              v-on="CetButton_next.event"
            ></CetButton>
            <CustomElDatePicker
              style="width: 220px"
              class="fr mlJ mrJ"
              :prefix_in="$T('选择月份')"
              v-bind="CetDatePicker_time.config"
              v-model="CetDatePicker_time.val"
            />
            <!-- <el-date-picker
              class="fr ml5 mr5"
              style="width: 150px"
              v-model="CetDatePicker_time.val"
              v-bind="CetDatePicker_time.config"
            ></el-date-picker> -->
            <!-- 向后查询按钮 -->
            <CetButton
              class="fr custom—square"
              v-bind="CetButton_prv"
              v-on="CetButton_prv.event"
            ></CetButton>
          </div>
        </el-col>
      </div>
      <div class="minWH flex-column" style="flex: 1">
        <div class="minWH" style="flex: 1; padding: 0px">
          <CetTable
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
          >
            <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_demandAdvice"></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_lastDemandValue"
            ></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_lastFactDemandValue"
            ></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_demandSaveCost"
              prop="demandSaveCost"
            >
              <template slot="header">
                <div>
                  {{ $T("可节省费用(元)") }}
                  <el-tooltip width="200" :content="titleMsg" effect="light">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                </div>
              </template>
            </ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_chargyWayAdviceText"
            ></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_lastSessionChargyWayText"
            ></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_volumnPreCost">
              <template slot-scope="scope">
                <span>
                  {{ scope.row[ElTableColumn_volumnPreCost.prop] || "--" }}
                  <i :class="IconClass_out(scope)"></i>
                </span>
              </template>
            </ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_demandPreCost">
              <template slot-scope="scope">
                <span>
                  {{ scope.row[ElTableColumn_demandPreCost.prop] || "--" }}
                  <i :class="IconClass_out2(scope)"></i>
                </span>
              </template>
            </ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_chargWaySaveCost"
              prop="chargWaySaveCost"
            >
              <template slot="header">
                <div>
                  {{ $T("可节省费用(元)") }}
                  <el-tooltip width="200" :content="titleMsg1" effect="light">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                </div>
              </template>
            </ElTableColumn>
          </CetTable>
        </div>
        <div class="padding0 lh32 mtJ3" style="overflow: hidden">
          <div class="flex-row">
            <div class="lh32 minWH" style="flex: 1">
              <div class="basic-box-label text-ellipsis">
                <el-popover
                  placement="top-start"
                  trigger="hover"
                  :content="subContent"
                >
                  <span slot="reference">{{ subContent }}</span>
                </el-popover>
              </div>
            </div>
            <div style="min-width: 400px" class="plJ3">
              <el-pagination
                style="text-align: right"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalCount"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
    </el-container>
  </div>
</template>
<script>
import moment from "moment";
import { httping } from "@omega/http";
export default {
  name: "DeclareProposal",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    // 实际对标考核下一时段按钮禁点状态
    backToTimeBtnDis1() {
      var actTime = null; //控件时间
      var maxTime = null; //当前时间
      var cycle = 3;
      var time = this.CetDatePicker_time.val;

      if (cycle === 1) {
        actTime = moment(time).startOf("day").valueOf();
        maxTime = moment().startOf("day").subtract(0, "d").valueOf();
      } else if (cycle === 3) {
        actTime = moment(time).startOf("month").valueOf();
        maxTime = moment().add(1, "month").startOf("month").valueOf();
      } else if (cycle === 5) {
        actTime = moment(time).startOf("year").valueOf();
        maxTime = moment().startOf("year").valueOf();
      }
      return actTime >= maxTime;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    },
    subContent() {
      $T("申报需量调整预计节省合计");
      return `${$T("进线总数")}：${this.totalCount}${$T("条")}， ${$T(
        "需调整计费方式"
      )}：${this.chargyWayAdviceTotal}${$T("条")} ， ${$T("保持不变")}：${
        this.chargyWayKeepTotal
      }${$T("条")} ， ${$T("申报需量调整预计节省合计")}：${
        this.demandSaveCostTotal
      } ， ${$T("申报需量调整预计节省合计")}：${this.chargWaySaveCostTotal}`;
    }
  },
  props: {
    currentNode: {
      type: Object
    }
  },

  data() {
    const language = window.localStorage.getItem("omega_language") === "en";
    return {
      copyTableData: [],
      unnaturalmonthbegin: 1, //年起始月
      demandSaveCostTotal: 0,
      chargWaySaveCostTotal: 0,
      chargyWayAdviceTotal: 0,
      chargyWayKeepTotal: 0,
      currentTabelItem: null,
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      CetDatePicker_time: {
        disable_in: false,
        val: this.$moment().add(0, "M").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "month",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          clearable: false,
          // pickerOptions: {
          //   disabledDate(time) {
          //     return time.getTime() > new Date().setMonth(new Date().getMonth() + 1);
          //   }
          // },
          size: "small",
          style: {
            display: "inline-block"
          }
        }
      },
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: false,
        size: "small",
        style: {},
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: true,
        title: $T("Al预测"),
        type: "primary",
        plain: false,
        size: "small",
        style: {},
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        // defaultSort: null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      ElTableColumn_name: {
        prop: "name", // 支持path a[0].b
        label: $T("进线名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "185" : "100" //该宽度会自适应
      },
      ElTableColumn_demandAdvice: {
        prop: "demandAdvice", // 支持path a[0].b
        label: $T("需量申报建议值") + "(kW)", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "380" : "180" //该宽度会自适应
      },
      ElTableColumn_lastDemandValue: {
        prop: "lastDemandValue", // 支持path a[0].b
        label: $T("上月申报需量") + "(kW)", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "280" : "150" //该宽度会自适应
      },
      ElTableColumn_lastFactDemandValue: {
        prop: "lastFactDemandValue", // 支持path a[0].b
        label: $T("上月实际需量") + "(kW)", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "325" : "150" //该宽度会自适应
      },
      ElTableColumn_demandSaveCost: {
        prop: "demandSaveCost", // 支持path a[0].b
        label: $T("可节省费用(元)"), //列名
        headerAlign: "right",
        align: "right",
        showOverflowTooltip: true,
        minWidth: language ? "190" : "150" //该宽度会自适应
      },
      ElTableColumn_chargyWayAdviceText: {
        prop: "chargyWayAdviceText", // 支持path a[0].b
        label: $T("计费方式调整建议") + "(2020-01~2020-03)", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "420" : "280" //该宽度会自适应
      },
      ElTableColumn_lastSessionChargyWayText: {
        prop: "lastSessionChargyWayText", // 支持path a[0].b
        label: $T("上季度计费方式") + "(2019-10~2019-12)", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "360" : "280" //该宽度会自适应
      },
      ElTableColumn_volumnPreCost: {
        prop: "volumnPreCost", // 支持path a[0].b
        label: $T("按容量预计电费(元)"), //列名
        headerAlign: "right",
        align: "right",
        showOverflowTooltip: true,
        minWidth: language ? "340" : "200" //该宽度会自适应
      },
      ElTableColumn_demandPreCost: {
        prop: "demandPreCost", // 支持path a[0].b
        label: $T("按需量预计电费(元)"), //列名
        headerAlign: "right",
        align: "right",
        showOverflowTooltip: true,
        minWidth: language ? "330" : "200" //该宽度会自适应
      },
      ElTableColumn_chargWaySaveCost: {
        prop: "chargWaySaveCost", // 支持path a[0].b
        label: $T("可节省费用(元)"), //列名
        headerAlign: "right",
        align: "right",
        showOverflowTooltip: true,
        minWidth: language ? "180" : "200" //该宽度会自适应
      },
      currentPage: 1,
      pageSize: 20,
      totalCount: 0,
      titleMsg: $T("可节省费用=|需量申报建议值-上月申报需量|*需量单价"),
      titleMsg1: $T("容量和需量费用预计差值的绝对值")
    };
  },
  watch: {
    "CetDatePicker_time.val": function (val) {
      this.getTabDate();
    },
    currentNode: {
      handler() {
        this.getTabDate();
      }
    }
  },

  methods: {
    // 获取非自然年起始月
    getQueryUnnaturalSet() {
      this.unnaturalmonthbegin = 1;
      httping({
        url: `/eem-service/v1/costcaculating/queryUnnaturalSet/${this.projectId}`,
        method: "GET"
      }).then(response => {
        if (response.code == 0 && response.data && response.data.length > 0) {
          var data = response.data.filter(item => item.aggregationcycle == 17);
          if (data.length > 0) {
            this.unnaturalmonthbegin = data[0].unnaturalmonthbegin;
          }
        }
      });
    },
    // 获取列表数据
    getTabDate() {
      if (!this.currentNode) {
        return;
      }
      this.CetTable_1.data = [];
      this.totalCount = 0;
      var data = {
        starttime: this.$moment(this.CetDatePicker_time.val)
          .startOf("M")
          .valueOf(),
        endtime:
          this.$moment(this.CetDatePicker_time.val).endOf("M").valueOf() + 1,
        ids: []
      };
      var obj = {};
      this.getChildrenDevice(this.currentNode, obj);
      data.ids = obj.demandaccount;
      // 获取季度区间
      var JD1, JD2, JD3, JD4;
      if (this.unnaturalmonthbegin + 2 > 12) {
        JD1 = [this.unnaturalmonthbegin, this.unnaturalmonthbegin + 2 - 12];
      } else {
        JD1 = [this.unnaturalmonthbegin, this.unnaturalmonthbegin + 2];
      }
      if (this.unnaturalmonthbegin + 5 > 12) {
        if (this.unnaturalmonthbegin + 3 > 12) {
          JD2 = [
            this.unnaturalmonthbegin + 3 - 12,
            this.unnaturalmonthbegin + 5 - 12
          ];
        } else {
          JD2 = [
            this.unnaturalmonthbegin + 3,
            this.unnaturalmonthbegin + 5 - 12
          ];
        }
      } else {
        JD2 = [this.unnaturalmonthbegin + 3, this.unnaturalmonthbegin + 5];
      }
      if (this.unnaturalmonthbegin + 8 > 12) {
        if (this.unnaturalmonthbegin + 6 > 12) {
          JD3 = [
            this.unnaturalmonthbegin + 6 - 12,
            this.unnaturalmonthbegin + 8 - 12
          ];
        } else {
          JD3 = [
            this.unnaturalmonthbegin + 6,
            this.unnaturalmonthbegin + 8 - 12
          ];
        }
      } else {
        JD3 = [this.unnaturalmonthbegin + 6, this.unnaturalmonthbegin + 8];
      }
      if (this.unnaturalmonthbegin + 11 > 12) {
        if (this.unnaturalmonthbegin + 9 > 12) {
          JD4 = [
            this.unnaturalmonthbegin + 9 - 12,
            this.unnaturalmonthbegin + 11 - 12
          ];
        } else {
          JD4 = [
            this.unnaturalmonthbegin + 9,
            this.unnaturalmonthbegin + 11 - 12
          ];
        }
      } else {
        JD4 = [this.unnaturalmonthbegin + 9, this.unnaturalmonthbegin + 11];
      }
      var JDArr = [JD1, JD2, JD3, JD4];
      // 设置表格头
      var chargyWayAdviceTitle;
      var lastSessionChargyWayTitle;
      var year = this.$moment(this.CetDatePicker_time.val).year();
      JDArr.forEach((item, index) => {
        if (item[0] < item[1]) {
          if (
            this.$moment(this.CetDatePicker_time.val).month() + 1 >= item[0] &&
            this.$moment(this.CetDatePicker_time.val).month() + 1 <= item[1]
          ) {
            // 找到所属季度
            chargyWayAdviceTitle = `${$T("计费方式调整建议")}(${this.$moment(
              `${year}-${item[0]}`
            ).format("YYYY-MM")}~${this.$moment(`${year}-${item[1]}`).format(
              "YYYY-MM"
            )})`;
            lastSessionChargyWayTitle = `${$T("上季度计费方式")}(${this.$moment(
              `${year}-${item[0]}`
            )
              .month(this.$moment(`${year}-${item[0]}`).month() - 3)
              .format("YYYY-MM")}~${this.$moment(`${year}-${item[1]}`)
              .month(this.$moment(`${year}-${item[1]}`).month() - 3)
              .format("YYYY-MM")})`;

            data.nowsessionstart = this.$moment(`${year}-${item[0]}`)
              .startOf("month")
              .valueOf();
            data.nowsessionend =
              this.$moment(`${year}-${item[1]}`).endOf("month").valueOf() + 1;
            data.lastsessionstart = this.$moment(`${year}-${item[0]}`)
              .month(this.$moment(`${year}-${item[0]}`).month() - 3)
              .startOf("month")
              .valueOf();
            data.lastsessionend =
              this.$moment(`${year}-${item[1]}`)
                .month(this.$moment(`${year}-${item[1]}`).month() - 3)
                .endOf("month")
                .valueOf() + 1;
          }
        } else {
          // 跨年
          if (
            this.$moment(this.CetDatePicker_time.val).month() >= item[0] ||
            this.$moment(this.CetDatePicker_time.val).month() < item[1]
          ) {
            // 找到所属季度
            chargyWayAdviceTitle = `${$T("计费方式调整建议")}(${this.$moment(
              `${year}-${item[0]}`
            ).format("YYYY-MM")}~${this.$moment(`${year}-${item[1]}`).format(
              "YYYY-MM"
            )})`;
            lastSessionChargyWayTitle = `${$T("上季度计费方式")}(${this.$moment(
              `${year}-${item[0]}`
            )
              .month(this.$moment(`${year}-${item[0]}`).month() - 3)
              .format("YYYY-MM")}~${this.$moment(`${year}-${item[1]}`)
              .month(this.$moment(`${year}-${item[1]}`).month() - 3)
              .format("YYYY-MM")})`;

            data.nowsessionstart = this.$moment(`${year}-${item[0]}`)
              .startOf("month")
              .valueOf();
            data.nowsessionend =
              this.$moment(`${year}-${item[1]}`).endOf("month").valueOf() + 1;
            data.lastsessionstart = this.$moment(`${year}-${item[0]}`)
              .month(this.$moment(`${year}-${item[0]}`).month() - 3)
              .startOf("month")
              .valueOf();
            data.lastsessionend =
              this.$moment(`${year}-${item[1]}`)
                .month(this.$moment(`${year}-${item[1]}`).month() - 3)
                .endOf("month")
                .valueOf() + 1;
          }
        }
      });
      this.ElTableColumn_chargyWayAdviceText.label = chargyWayAdviceTitle;
      this.ElTableColumn_lastSessionChargyWayText.label =
        lastSessionChargyWayTitle;
      data.projectid = this.projectId;
      httping({
        url: "/eem-service/v1/demand/maintain/getDeclareRecords",
        method: "POST",
        data
      }).then(
        response => {
          if (response.code == 0 && response.data) {
            response.data.counts.forEach(item => {
              if (item.chargyWayAdvice == item.lastSessionChargyWay) {
                item.chargyWayAdviceText = $T("不变");
              } else {
                item.chargyWayAdviceText =
                  item.chargyWayAdvice == 1
                    ? $T("容量计费")
                    : item.chargyWayAdvice == 2
                    ? $T("需量计费")
                    : "--";
              }
              item.lastSessionChargyWayText =
                item.lastSessionChargyWay == 1
                  ? $T("容量计费")
                  : item.lastSessionChargyWay == 2
                  ? $T("需量计费")
                  : "--";
              item.demandAdvice = item.demandAdvice
                ? item.demandAdvice.toFixed2(2)
                : "--";
              item.lastDemandValue = item.lastDemandValue
                ? item.lastDemandValue.toFixed2(2)
                : "--";
              item.lastFactDemandValue = item.lastFactDemandValue
                ? item.lastFactDemandValue.toFixed2(2)
                : "--";
              item.demandSaveCost = item.demandSaveCost
                ? (item.demandSaveCost / 100000).toFixed2(2)
                : "--";
              item.volumnPreCost = item.volumnPreCost
                ? (item.volumnPreCost / 100000).toFixed2(2)
                : "--";
              item.demandPreCost = item.demandPreCost
                ? (item.demandPreCost / 100000).toFixed2(2)
                : "--";
              item.chargWaySaveCost = item.chargWaySaveCost
                ? (item.chargWaySaveCost / 100000).toFixed2(2)
                : "--";
            });
            // 保存数据，用于分页
            this.copyTableData = this._.cloneDeep(response.data.counts);
            // 处理表格
            this.handleCurrentChange(1);

            this.totalCount = response.data.countTotal;
            this.demandSaveCostTotal = response.data.demandSaveCostTotal
              ? (response.data.demandSaveCostTotal / 100000).toFixed2(2) + "元"
              : "--";
            this.chargWaySaveCostTotal = response.data.chargWaySaveCostTotal
              ? (response.data.chargWaySaveCostTotal / 100000).toFixed2(2) +
                $T("元")
              : "--";
            this.chargyWayAdviceTotal = response.data.chargyWayAdviceTotal
              ? response.data.chargyWayAdviceTotal.toFixed2(0)
              : "--";
            this.chargyWayKeepTotal = response.data.chargyWayKeepTotal
              ? response.data.chargyWayKeepTotal.toFixed2(0)
              : "--";
          }
        },
        error => {}
      );
    },
    //分页大小变化
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.handleCurrentChange(1);
    },
    //分页当前页变化
    handleCurrentChange(val) {
      this.currentPage = val;
      this.CetTable_1.data = this.copyTableData.slice(
        (this.currentPage - 1) * this.pageSize,
        this.currentPage * this.pageSize
      );
    },
    // 获取节点下的所有进线id
    getChildrenDevice(node, obj) {
      if (node.modelLabel == "demandaccount") {
        // 找到设备
        if (obj[node.modelLabel]) {
          if (obj[node.modelLabel].indexOf(node.id) == -1) {
            obj[node.modelLabel].push(node.id);
          }
        } else {
          obj[node.modelLabel] = [node.id];
        }
      } else {
        if (node.children && node.children.length > 0) {
          node.children.forEach(item => {
            this.getChildrenDevice(item, obj);
          });
        }
      }
    },
    CetButton_1_statusTrigger_out(val) {
      this.CetTable_1.exportFileName = `${$T("需量申报建议")}_${this.$moment(
        this.CetDatePicker_time.val
      ).format($T("YYYY年MM月"))}`;
      this.CetTable_1.exportTrigger_in = this._.cloneDeep(val);
    },
    CetButton_2_statusTrigger_out(val) {
      this.$router.push({
        name: "demandmonitor",
        params: Object.assign(this.currentTabelItem, { toAlForecast: true })
      });
    },
    CetButton_prv_statusTrigger_out(val) {
      // let date = this.$moment(this.CetDatePicker_time.val);
      // this.CetDatePicker_time.val = date.subtract(1, "d")._d;
      var time = this.CetDatePicker_time.val;
      this.CetDatePicker_time.val = this.$moment(time)
        .subtract(1, "M")
        .valueOf();
    },
    CetButton_next_statusTrigger_out(val) {
      // let date = this.$moment(this.CetDatePicker_time.val);
      // this.CetDatePicker_time.val = date.add(1, "d")._d;
      var time = this.CetDatePicker_time.val;
      this.CetDatePicker_time.val = this.$moment(time).add(1, "M").valueOf();
    },
    CetTable_1_record_out(val) {
      this.currentTabelItem = val;
      if (val.id != -1) {
        this.CetButton_1.disable_in = false;
        this.CetButton_2.disable_in = false;
      } else {
        this.CetButton_1.disable_in = true;
        this.CetButton_2.disable_in = true;
      }
    },
    CetTable_1_outputData_out(val) {},
    setCellClass({ row, column, rowIndex, columnIndex }) {
      if (columnIndex > 1 && columnIndex < 6) {
        return "cell-row-class11";
      } else if (columnIndex > 5) {
        return "cell-row-class12";
      }
    },
    setHeaderRowClass({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === 0) {
        if (columnIndex === 1) {
          return "header-row-class11";
        } else if (columnIndex === 2) {
          return "header-row-class12";
        }
      } else if (rowIndex === 1) {
        if (columnIndex > 1 && columnIndex < 6) {
          return "header-row-class21";
        } else if (columnIndex > 5) {
          return "header-row-class22";
        }
      }
    },
    AnalysisValueType_out(val) {
      return {};
    },
    CetTable_1_headclassname_out({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 1) {
        return "cell-row-class13";
      }
      if (columnIndex > 1 && columnIndex < 6) {
        return "header-row-class21";
      } else if (columnIndex >= 6) {
        return "header-row-class22";
      }
    },
    CetTable_1_classname_out({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 1) {
        return "cell-row-class13";
      }
      if (columnIndex > 1 && columnIndex < 6) {
        return "cell-row-class11";
      } else if (columnIndex >= 6) {
        return "cell-row-class12";
      }
    },
    IconClass_out(val) {
      if (val.row.volumnPreCost !== null && val.row.volumnPreCost !== "--") {
        if (val.row.demandPreCost !== null && val.row.demandPreCost !== "--") {
          if (Number(val.row.volumnPreCost) <= Number(val.row.demandPreCost)) {
            return ["eem-declareproposal-table-label"];
          }
        } else {
          return ["eem-declareproposal-table-label"];
        }
      }
    },
    IconClass_out2(val) {
      if (val.row.demandPreCost !== null && val.row.demandPreCost !== "--") {
        if (val.row.volumnPreCost !== null && val.row.volumnPreCost !== "--") {
          if (Number(val.row.volumnPreCost) > Number(val.row.demandPreCost)) {
            return ["eem-declareproposal-table-label"];
          }
        } else {
          return ["eem-declareproposal-table-label"];
        }
      }
    }
  },
  created: function () {},
  activated: function () {
    this.getQueryUnnaturalSet();
    this.getTabDate();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: calc(100% - 50px);
  position: relative;
}
.el-header {
  :deep(.el-button) {
    &:hover {
      color: #fff;
    }
    &:active {
      color: #fff;
    }
    &:focus {
      color: #fff;
    }
  }
}

.head-label-detail {
  display: block;
  float: right;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  color: #fff;
  background-color: #0152d9;
  margin-top: 6px;
  line-height: 16px;
  text-align: center;
  cursor: pointer;
}

.basic-box-label {
  line-height: 30px;
  height: 30px;
}
</style>
<style>
.header-row-class11 {
  background-color: #0152d9 !important;
  color: #fff;
  border-color: #0152d9 !important;
  border-radius: 15px 15px 0px 0px;
}
.header-row-class12 {
  background-color: #11c07b !important;
  color: #fff;
  border-color: #11c07b !important;
  border-radius: 15px 15px 0px 0px;
}
.header-row-class21 {
  background-color: #80a9ec !important;
  color: #333333;
  border-color: #0152d9 !important;
  border-top: 1px solid #0152d9 !important;
}
.header-row-class22 {
  background-color: #88e0bd !important;
  color: #333333;
  border-color: #11c07b !important;
  border-top: 1px solid #11c07b !important;
}
.el-table--border .el-table__body tr.current-row .cell-row-class11 {
  border-right: 1px solid #0152d9 !important;
}
.el-table--border .el-table__body tr.current-row .cell-row-class12 {
  border-right: 1px solid #11c07b !important;
}
.el-table--border .el-table__body tr.current-row .cell-row-class13 {
  border-right: 1px solid #0152d9 !important;
}
.cell-row-class11 {
  background-color: #e6eefb !important;
  border-color: #0152d9 !important;
}
.cell-row-class12 {
  background-color: #e8f9f2 !important;
  border-color: #11c07b !important;
}
.cell-row-class13 {
  border-right: 1px solid #0152d9 !important;
}
.eem-declareproposal-table-label {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 16px;
  cursor: pointer;
  background: url(./assets/u6690.png) no-repeat center;
  background-size: contain;
  position: relative;
  top: 4px;
  left: 5px;
}
</style>
