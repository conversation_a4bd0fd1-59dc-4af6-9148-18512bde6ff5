<template>
  <!-- 1弹窗组件 -->
  <div>
    <CetDialog
      :class="['CetDialog', !en ? 'small' : '']"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
      :title="
        activeTab_in === 'ActiveEvent'
          ? $T('编辑能效KPI指标')
          : $T('编辑转换效率指标')
      "
    >
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
      <div class="eem-cont-c1">
        <CetForm
          :data.sync="CetForm_1.data"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
        >
          <el-row :gutter="$J3">
            <el-col :span="12">
              <el-form-item :label="$T('指标名称')" prop="name">
                <ElInput
                  v-model.trim="CetForm_1.data.name"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('单位')" prop="symbol">
                <ElInput
                  v-model.trim="CetForm_1.data.symbol"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <template v-if="activeTab_in === 'ActiveEvent'">
              <el-col :span="12">
                <el-form-item :label="$T('能源类型')" prop="energytype">
                  <ElSelect
                    v-model="CetForm_1.data.energytype"
                    v-bind="ElSelect_energytype"
                    v-on="ElSelect_energytype.event"
                  >
                    <ElOption
                      v-for="item in ElOption_energytype.options_in"
                      :key="item[ElOption_energytype.key]"
                      :label="item[ElOption_energytype.label]"
                      :value="item[ElOption_energytype.value]"
                      :disabled="item[ElOption_energytype.disabled]"
                    ></ElOption>
                  </ElSelect>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('指标属性')" prop="unittype">
                  <ElSelect
                    v-model="CetForm_1.data.unittype"
                    v-bind="ElSelect_unittype"
                    v-on="ElSelect_unittype.event"
                  >
                    <ElOption
                      v-for="item in ElOption_unittype.options_in"
                      :key="item[ElOption_unittype.key]"
                      :label="item[ElOption_unittype.label]"
                      :value="item[ElOption_unittype.value]"
                      :disabled="item[ElOption_unittype.disabled]"
                    ></ElOption>
                  </ElSelect>
                </el-form-item>
              </el-col>
            </template>
            <template v-else>
              <el-col :span="12">
                <el-form-item :label="$T('消耗能源类型')" prop="energytype">
                  <ElSelect
                    v-model="CetForm_1.data.energytype"
                    v-bind="ElSelect_inenergytype"
                    v-on="ElSelect_inenergytype.event"
                  >
                    <ElOption
                      v-for="item in ElOption_inenergytype.options_in"
                      :key="item[ElOption_inenergytype.key]"
                      :label="item[ElOption_inenergytype.label]"
                      :value="item[ElOption_inenergytype.value]"
                      :disabled="item[ElOption_inenergytype.disabled]"
                    ></ElOption>
                  </ElSelect>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('产出载能工质')" prop="prodenergytype">
                  <ElSelect
                    v-model="CetForm_1.data.prodenergytype"
                    v-bind="ElSelect_prodenergytype"
                    v-on="ElSelect_prodenergytype.event"
                  >
                    <ElOption
                      v-for="item in ElOption_prodenergytype.options_in"
                      :key="item[ElOption_prodenergytype.key]"
                      :label="item[ElOption_prodenergytype.label]"
                      :value="item[ElOption_prodenergytype.value]"
                      :disabled="item[ElOption_prodenergytype.disabled]"
                    ></ElOption>
                  </ElSelect>
                </el-form-item>
              </el-col>
            </template>
            <el-col :span="12">
              <el-form-item :label="$T('分析周期')" prop="aggregationcycle">
                <!-- <ElSelect
                  v-model="CetForm_1.data.aggregationcycle"
                  v-bind="ElSelect_aggregationcycle"
                  v-on="ElSelect_aggregationcycle.event"
                >
                  <ElOption
                    v-for="item in ElOption_aggregationcycle.options_in"
                    :key="item[ElOption_aggregationcycle.key]"
                    :label="item[ElOption_aggregationcycle.label]"
                    :value="item[ElOption_aggregationcycle.value]"
                    :disabled="item[ElOption_aggregationcycle.disabled]"
                  ></ElOption>
                </ElSelect> -->
                <el-checkbox-group
                  v-model="CetForm_1.data.aggregationcycle"
                  :min="1"
                  :max="5"
                  disabled
                >
                  <el-checkbox
                    v-for="item in ElOption_aggregationcycle.options_in"
                    :key="item[ElOption_aggregationcycle.key]"
                    :label="item[ElOption_aggregationcycle.value]"
                    class="mrJ1"
                  >
                    {{ item[ElOption_aggregationcycle.label] }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :label="$T('选择产品')"
                prop="producttype"
                v-if="CetForm_1.data.unittype == 1"
              >
                <ElSelect
                  v-model="CetForm_1.data.producttype"
                  v-bind="ElSelect_producttype"
                  v-on="ElSelect_producttype.event"
                >
                  <ElOption
                    v-for="item in ElOption_producttype.options_in"
                    :key="item[ElOption_producttype.key]"
                    :label="item[ElOption_producttype.label]"
                    :value="item[ElOption_producttype.value]"
                    :disabled="item[ElOption_producttype.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :label="$T('产出载能工质')"
                prop="prodenergytype"
                v-if="CetForm_1.data.unittype == 7"
              >
                <ElSelect
                  v-model="CetForm_1.data.prodenergytype"
                  v-bind="ElSelect_prodenergytype"
                  v-on="ElSelect_prodenergytype.event"
                >
                  <ElOption
                    v-for="item in ElOption_prodenergytype.options_in"
                    :key="item[ElOption_prodenergytype.key]"
                    :label="item[ElOption_prodenergytype.label]"
                    :value="item[ElOption_prodenergytype.value]"
                    :disabled="item[ElOption_prodenergytype.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :label="$T('系数')"
                prop="coef"
                v-if="CetForm_1.data.unittype == 4"
              >
                <ElInputNumber
                  v-model="CetForm_1.data.coef"
                  v-bind="ElInputNumber_1"
                  v-on="ElInputNumber_1.event"
                ></ElInputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-collapse v-model="activeNames">
                <el-collapse-item :title="$T('高级选项')" name="1">
                  <el-row :gutter="$J3">
                    <el-col :span="12">
                      <el-form-item
                        :label="$T('维度选择')"
                        prop="dimension"
                        v-if="activeNames.length > 0"
                      >
                        <ElSelect
                          v-model="CetForm_1.data.dimension"
                          v-bind="ElSelect_dimension"
                          v-on="ElSelect_dimension.event"
                        >
                          <ElOption
                            v-for="item in ElOption_dimension.options_in"
                            :key="item[ElOption_dimension.key]"
                            :label="item[ElOption_dimension.label]"
                            :value="item[ElOption_dimension.value]"
                            :disabled="item[ElOption_dimension.disabled]"
                          ></ElOption>
                        </ElSelect>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item
                        :label="$T('用能对象')"
                        prop="object"
                        v-if="activeNames.length > 0"
                      >
                        <ElSelect
                          v-model="ElSelect_energyUsers.value"
                          v-bind="ElSelect_energyUsers"
                          v-on="ElSelect_energyUsers.event"
                        >
                          <!-- <ElSelect v-model="CetForm_1.data.energyUsers" v-bind="ElSelect_energyUsers" v-on="ElSelect_energyUsers.event"> -->
                          <ElOption
                            v-for="item in ElOption_energyUsers.options_in"
                            :key="item[ElOption_energyUsers.key]"
                            :label="item[ElOption_energyUsers.label]"
                            :value="item[ElOption_energyUsers.value]"
                            :disabled="item[ElOption_energyUsers.disabled]"
                          ></ElOption>
                        </ElSelect>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-collapse-item>
              </el-collapse>
            </el-col>
          </el-row>
        </CetForm>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import common from "eem-utils/common";
import commonApi from "@/api/custom.js";
import { httping } from "@omega/http";
export default {
  name: "EditEnergyefficiencyIndexes",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    energyefficiencyset_in: {
      type: Object
    },
    activeTab_in: {
      type: String
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },

  data() {
    return {
      activeNames: [],
      ElInputNumber_1: {
        ...common.check_numberFloat,
        value: "",
        style: {
          width: "100%"
        },
        controls: false,
        event: {}
      },
      CetDialog_1: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        labelPosition: "top",
        rules: {
          object: [
            {
              required: true,
              message: $T("请选择用能对象"),
              trigger: ["blur", "change"]
            }
          ],
          name: [
            {
              required: true,
              message: $T("请输入指标名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          energytype: [
            {
              required: true,
              message: $T("请选择能源类型"),
              trigger: ["blur", "change"]
            }
          ],
          symbol: [
            {
              required: true,
              message: $T("请输入单位"),
              trigger: ["blur", "change"]
            },
            common.check_name
          ],
          prodenergytype: [
            {
              required: true,
              message: $T("请选择产出载能工质"),
              trigger: ["blur", "change"]
            }
          ],
          unittype: [
            {
              required: true,
              message: $T("请选择能效单位类型"),
              trigger: ["blur", "change"]
            }
          ],
          aggregationcycle: [
            {
              required: true,
              message: $T("请选择分析周期"),
              trigger: ["blur", "change"]
            }
          ],
          producttype: [
            {
              required: true,
              message: $T("请选择产品"),
              trigger: ["blur", "change"]
            }
          ],
          coef: [
            {
              required: true,
              message: $T("请填写系数"),
              trigger: ["blur", "change"]
            }
          ],
          dimension: [
            {
              required: true,
              message: $T("请选择维度"),
              trigger: ["blur", "change"]
            }
          ]
          // energyUsers: [
          //   {
          //     required: true,
          //     message: "请选择用能对象",
          //     trigger: ["blur", "change"]
          //   }
          // ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {
          width: "100%"
        },
        event: {}
      },
      ElSelect_energytype: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_energytype: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // 消耗能源类型下拉框
      ElSelect_inenergytype: {
        value: null,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_inenergytype_change_out
        }
      },
      ElOption_inenergytype: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // 产出能源类型下拉框
      ElSelect_prodenergytype: {
        value: null,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_prodenergytype_change_out
        }
      },
      ElOption_prodenergytype: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_unittype: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_unittype_change_out
        }
      },
      ElOption_unittype: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_aggregationcycle: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_aggregationcycle: {
        options_in: [
          {
            id: 7,
            text: $T("小时"),
            type: "h"
          },
          {
            id: 12,
            text: $T("日"),
            type: "date"
          },
          {
            id: 13,
            text: $T("周"),
            type: "week"
          },
          {
            id: 14,
            text: $T("月"),
            type: "month"
          },
          {
            id: 17,
            text: $T("年"),
            type: "year"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_producttype: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_producttype: {
        options_in: [],
        key: "producttype",
        value: "producttype",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_dimension: {
        value: 1,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_dimension_change_out
        }
      },
      ElOption_dimension: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_energyUsers: {
        value: null,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_energyUsers: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      projectList: [],
      cycleList: []
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.grtEnergyefficiencyunittype();
      this.getProjectEnergy();
      this.getDimensionMsg();
      this.getProduct_out();
      // this.ElOption_producttype.options_in = this.$store.state.enumerations.producttype;
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    inputData_in(val) {
      // this.CetDialog_1.inputData_in = val;
      this.CetForm_1.data = val;
    },
    energyefficiencyset_in(val) {
      if (!val) {
        return;
      }
      var obj = {
        name: val.name,
        id: val.id,
        modelLabel: "energyefficiencyset",
        unittype: Number(val.unittype),
        energytype: Number(val.energytype),
        aggregationcycle: val.aggregationcycle,
        prodenergytype:
          val.prodenergytype !== null ? Number(val.prodenergytype) : null,
        symbol: val.symbol,
        dimension: 1
      };
      if (Number(val.unittype) === 1) {
        obj.producttype = Number(val.producttype);
      }
      if (Number(val.unittype) === 4) {
        obj.coef = Number(val.coef);
      }
      this.cycleList = this._.cloneDeep(val.mergeDisplayAdds);
      this.$nextTick(() => {
        this.CetForm_1.data = obj;
        this.CetForm_1.resetTrigger_in = new Date().getTime();
      });
    },
    "ElSelect_energyUsers.value": {
      handler(val) {
        this.$set(this.CetForm_1.data, "object", val);
      },
      deep: true
    }
  },

  methods: {
    grtEnergyefficiencyunittype() {
      var energyefficiencyunittypeArr =
        this.$store.state.enumerations.energyefficiencyunittype;
      var data = [];
      energyefficiencyunittypeArr.forEach(item => {
        if (item.id <= 4 || item.id === 7) {
          data.push(item);
        }
      });
      this.ElOption_unittype.options_in = data;
    },
    //获取维度列表信息
    getDimensionMsg: function () {
      var _this = this;
      var auth = _this.token; //身份验证
      _this.loading = true;
      httping({
        url: "/eem-service/v1/dim/setting/detailList",
        method: "GET",
        timeout: 10000
      }).then(res => {
        if (res.code !== 0) return;
        let dimData = res.data || [];
        dimData = dimData.filter(item => {
          return item.fixdim;
        });
        this.ElOption_dimension.options_in = dimData;
        if (this.energyefficiencyset_in.dimtagids) {
          this.activeNames = ["1"];
          dimData.forEach(item1 => {
            item1.children.forEach(item2 => {
              item2.children.forEach(item3 => {
                if (item3.id == this.energyefficiencyset_in.dimtagids) {
                  this.CetForm_1.data.dimension = item1.id;
                  // this.ElSelect_energyUsers.value = this.energyefficiencyset_in.dimtagids;
                  this.ElSelect_dimension_change_out(
                    item1.id,
                    this.energyefficiencyset_in.dimtagids * 1
                  );
                }
              });
            });
          });
        } else {
          this.activeNames = [];
          this.CetForm_1.data.dimension = null;
          this.ElSelect_energyUsers.value = null;
        }
      });
    },
    close() {
      this.ElOption_energyUsers.options_in = [];
      this.ElSelect_energyUsers.value = null;
      this.CetDialog_1.closeTrigger_in = new Date().getTime();
    },
    CetButton_cancel_statusTrigger_out() {
      this.close();
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_saveData_out(val) {
      var params = [];
      var modelData = this._.cloneDeep(this.CetForm_1.data);
      // 选择高级选项时 写入用能对象id
      if (this.activeNames.length > 0) {
        if (!this.ElSelect_energyUsers.value) {
          this.$message($T("请选择用能对象"));
          return;
        }
        modelData.energyefftype = 2;
        modelData.dimtagids = this.ElSelect_energyUsers.value;
      } else {
        modelData.energyefftype = 1;
        modelData.dimtagids = null;
      }
      modelData.modelLabel = "energyefficiencyset";
      modelData.efType = 1;
      delete modelData.dimension;
      // if (this.activeTab_in === "HistoricalEvent") {
      //   modelData.efType = 2;
      // } else {
      //   modelData.efType = 1;
      // }
      let str = modelData.name;
      this.cycleList.forEach(item => {
        if (this.cycleList.length > 1) {
          modelData.name = str + item.cycleName;
        }
        modelData.id = item.efSetId;
        modelData.aggregationcycle = item.cycle;
        let temp = this._.cloneDeep(modelData);
        params.push(temp);
      });
      this.Add_energy_efficiency(params);
    },
    ElSelect_inenergytype_change_out(val) {
      const list = this.ElOption_energytype.options_in.filter(item => {
        return item.id !== val;
      });
      this.ElOption_prodenergytype.options_in = list;
    },
    ElSelect_prodenergytype_change_out(val) {
      const list = this.ElOption_energytype.options_in.filter(item => {
        return item.id !== val;
      });
      this.ElOption_inenergytype.options_in = list;
    },
    ElSelect_unittype_change_out() {
      this.$set(this.CetForm_1.data, "coef", null);
      this.$set(this.CetForm_1.data, "producttype", null);
      this.$set(this.CetForm_1.data, "prodenergytype", null);
    },
    ElSelect_dimension_change_out(val, dimtagids) {
      const item = this.ElOption_dimension.options_in.filter(item => {
        return item.id === val;
      })[0];

      const energyUsers = [];
      if (item.children) {
        item.children.forEach(item => {
          item.children.forEach(item => {
            energyUsers.push(item);
          });
        });
        this.ElOption_energyUsers.options_in = this._.cloneDeep(energyUsers);
      }
      if (energyUsers.length > 0) {
        // this.CetForm_1.data.energyUsers = energyUsers[0].id;
        this.ElSelect_energyUsers.value = dimtagids || energyUsers[0].id;
      } else {
        this.ElSelect_energyUsers.value = null;
      }
    },
    Add_energy_efficiency(params) {
      var _this = this;
      commonApi
        .writeEnergyefficiencyset(params, _this.projectId)
        .then(response => {
          if (response.code === 0) {
            _this.$emit("saveData_out", new Date().getTime());
            _this.close();
            _this.$message({
              type: "success",
              message: $T("保存成功！")
            });
          }
        });
    },
    // 获取项目能源类型
    getProjectEnergy() {
      httping({
        url:
          "/eem-service/v1/project/projectEnergy?projectId=" + this.projectId,
        method: "GET"
      }).then(res => {
        if (res.code === 0 && res.data && res.data.length > 0) {
          var selectData = res.data.map(item => {
            return {
              id: item.energytype,
              text: item.name
            };
          });
          this.ElOption_energytype.options_in = this._.cloneDeep(selectData);
          this.ElOption_inenergytype.options_in = this._.cloneDeep(
            selectData
          ).filter(item => {
            return item.id !== this.CetForm_1.data.prodenergytype;
          });
          this.ElOption_prodenergytype.options_in = this._.cloneDeep(
            selectData
          ).filter(item => {
            return item.id !== this.CetForm_1.data.energytype;
          });
          if (selectData.filter(item => item.id === 2).length > 0) {
            this.ElSelect_energytype.value = 2;
          } else {
            this.ElSelect_energytype.value = selectData[0].id;
          }
        } else {
          this.ElOption_energytype.options_in = [];
          this.ElSelect_energytype.value = null;
        }
      });
    },
    // 获取产品列表
    getProduct_out() {
      const _this = this;
      const params = {
        rootLabel: "project",
        rootCondition: {
          filter: {
            expressions: [{ prop: "id", operator: "EQ", limit: this.projectId }]
          },
          include_submodel: true
        },
        subLayerConditions: [
          {
            filter: null,
            include_relations: [],
            modelLabel: "product",
            props: []
          }
        ]
      };
      httping({
        url: "/model/v1/query",
        method: "POST",
        data: params
      }).then(res => {
        if (res.code === 0 && res.data && res.data.length > 0) {
          const list = _this._.get(res, "data[0].product_model", []) || [];
          this.ElOption_producttype.options_in = list;
        } else {
          this.ElOption_producttype.options_in = [];
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-collapse-item__content) {
    padding-bottom: 0;
  }
}
:deep(.el-collapse) {
  border: none !important;
  :deep(.el-collapse-item__header) {
    border: none !important;
    text-align: right;
    display: block;
  }
  :deep(.el-collapse-item__wrap) {
    border: none !important;
  }
}
:deep(.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after) {
  border-color: #fff;
}
</style>
