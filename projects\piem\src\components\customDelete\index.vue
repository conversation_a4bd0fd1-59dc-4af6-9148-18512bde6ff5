<template>
  <div>
    <CetDialog class="CetDialog" v-bind="CetDialog_1" v-on="CetDialog_1.event">
      <div class="flex items-center eem-cont-c1">
        <omega-icon
          symbolId="delete"
          class="w-[33px] h-[33px] text-Sta3"
        ></omega-icon>
        <div class="flex flex-col ml-4 font-normal">
          <div class="text-[14px] text-T3">
            <slot></slot>
          </div>
          <div class="mt-[8px] text-T4 text-[12px]">此操作一经删除不可撤回</div>
        </div>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
export default {
  name: "customDelete",
  props: {
    openTrigger_in: Number,
    inputData_in: Object
  },
  data() {
    return {
      CetDialog_1: {
        width: "480px",
        title: "删除确认",
        showClose: true,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {}
      },

      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    async openTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
    }
  },
  computed: {},
  methods: {
    async CetButton_confirm_statusTrigger_out() {
      this.$emit("delete", this.inputData_in);
      this.CetDialog_1.closeTrigger_in = +new Date();
    },
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = +new Date();
    }
  }
};
</script>

<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
  }
  :deep(.el-dialog) {
    margin-top: 30vh !important;
  }
}
</style>
