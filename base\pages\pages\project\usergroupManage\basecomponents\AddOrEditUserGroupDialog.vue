<template>
  <div>
    <CetDialog
      class="CetDialog small"
      v-bind="CetDialog_UserGroup"
      v-on="CetDialog_UserGroup.event"
    >
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
      <CetForm
        class="bg1 CetForm"
        :data.sync="CetForm_UserGroup.data"
        v-bind="CetForm_UserGroup"
        v-on="CetForm_UserGroup.event"
      >
        <el-row :gutter="$J3">
          <el-col :span="12">
            <el-form-item :label="$T('用户组名称')" prop="name">
              <ElInput
                v-model.trim="CetForm_UserGroup.data.name"
                v-bind="ElInput_1"
                v-on="ElInput_1.event"
              ></ElInput>
            </el-form-item>
            <!-- <el-form-item
              class="custom-form-item gray-label"
              label="Logo"
              prop="customConfig"
            >
              <CustomUploader
                placeholder="请选择Logo图片"
                accept=".png,.jpeg,.bmp,.gif"
                infoText_in="支持 JPEG,BMP,GIF,PNG 文件，大小限制为512K"
                :maxSize="512"
                :defaultFileName_in="CustomUploader_UserGroupLogo.logoName"
                :resetTrigger_in="CustomUploader_UserGroupLogo.resetTrigger_in"
                @fileChanged_out="CustomUploader_UserGroupLogo_fileChanged_out"
              ></CustomUploader>
            </el-form-item> -->
          </el-col>
        </el-row>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
import commonApi from "@/api/custom";
import common from "eem-utils/common";
// import CustomUploader from "eem-components/customUploader";
import customApi from "@/api/custom.js";
export default {
  name: "AddOrEditUserGroupDialog",
  // components: { CustomUploader },
  props: {
    userGroupData_in: {
      type: Object,
      default: null
    },
    isEditMode: {
      type: Boolean,
      default: false
    },
    openTrigger_in: {
      type: Number,
      default: new Date().getTime()
    }
  },
  data() {
    return {
      // 用户组弹窗组件
      CetDialog_UserGroup: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },

      CetForm_UserGroup: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "addUserGroup",
          modelLabel: "usergroup",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入用户组名称")
            },
            common.pattern_name,
            common.check_stringLessThan50
          ]
          //customConfig: [{ required: true, message: "Logo不能为空", trigger: "blur" }]
        },
        event: {
          saveData_out: this.CetForm_UserGroup_saveData_out,
          finishData_out: this.CetForm_UserGroup_finishData_out
        }
      },
      // 用户组Logo上传组件
      CustomUploader_UserGroupLogo: {
        logoName: null,
        currentFile: null,
        resetTrigger_in: new Date().getTime()
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入"),
        style: {},
        event: {}
      }
    };
  },
  watch: {
    openTrigger_in() {
      this.open();
    }
  },
  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_UserGroup.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      this.addOrEditUserGroup();
    },
    // 用户组表单输出
    CetForm_UserGroup_currentData_out() {},
    CetForm_UserGroup_saveData_out() {
      this.close();
    },
    CetForm_UserGroup_finishData_out() {
      this.close();
      this.$emit("userGroupsChanged_out");
    },

    CustomUploader_UserGroupLogo_fileChanged_out(file) {
      let vm = this;
      vm.CustomUploader_UserGroupLogo.currentFile = file;
      vm.CetForm_UserGroup.inputData_in.customConfig = null;
    },

    // 上传用户组logo
    uploadLogo() {
      let vm = this;

      // 如果已经有了，说明是已经上传过用户组图片了
      if (vm.CetForm_UserGroup.inputData_in.customConfig) {
        return Promise.resolve(vm.CetForm_UserGroup.inputData_in.customConfig);
      }

      let currentFile = vm.CustomUploader_UserGroupLogo.currentFile;
      if (!currentFile) {
        return Promise.resolve("");
      }

      let p = new Promise(resolve => {
        commonApi["uploadUserGroupImg"](currentFile)
          .then(response => {
            if (response.code === 0) {
              resolve(
                JSON.stringify({
                  fileName: response.data
                })
              );
            } else {
              resolve("");
            }
          })
          .catch(() => {
            resolve("");
          });
      });

      return p;
    },

    // 新增或编辑用户组
    addOrEditUserGroup() {
      let vm = this;
      // vm.uploadLogo().then(customConfig => {
      // vm.CetForm_UserGroup.inputData_in.customConfig = customConfig;
      if (vm.isEditMode) {
        vm.CetForm_UserGroup.dataConfig.writeFunc = "editUserGroup";
      } else {
        vm.CetForm_UserGroup.dataConfig.writeFunc = "addUserGroup";
      }

      vm.$nextTick(() => {
        vm.CetForm_UserGroup.saveTrigger_in = new Date().getTime();
      });
      // });
    },

    // 获取Logo名称
    getLogoName(customConfig) {
      return new Promise((resolve, reject) => {
        if (!customConfig) {
          reject();
          return;
        }

        let logoData = JSON.parse(customConfig);
        let logoName = logoData.fileName;

        if (!logoName) {
          reject();
          return;
        }

        resolve(logoName);
      });
    },

    resetData() {
      let vm = this;
      let inputData = {};
      vm.CustomUploader_UserGroupLogo.currentFile = null;
      if (vm.isEditMode) {
        inputData.id = vm.userGroupData_in.id;
        inputData.name = vm.userGroupData_in.name;
        inputData.customConfig = vm.userGroupData_in.customConfig;
        inputData.parentId = 0;
        this.getRoleBytenantId();
      } else {
        inputData.id = null;
        inputData.name = "";
        inputData.customConfig = "";
        inputData.parentId = 0;
      }
      vm.CetForm_UserGroup.data = inputData;
      vm.CetForm_UserGroup.resetTrigger_in = new Date().getTime();
      // vm.getLogoName(inputData.customConfig)
      //   .then(logoName => {
      //     vm.CustomUploader_UserGroupLogo.logoName = logoName;
      //   })
      //   .catch(() => {
      //     vm.CustomUploader_UserGroupLogo.logoName = "";
      //   })
      //   .finally(() => {
      //     vm.CustomUploader_UserGroupLogo.resetTrigger_in =
      //       new Date().getTime();
      //   });
    },
    // 获取用户组信息
    getRoleBytenantId() {
      var vm = this;
      let params = {
        userGroupId: this.userGroupData_in.id
      };

      customApi.authGetUsergroupInfo(params).then(response => {
        if (response.code === 0) {
          vm.CetForm_UserGroup.data.customConfig = vm._.get(
            response,
            "data.customConfig",
            "{}"
          );
        }
      });
    },

    open() {
      let vm = this;
      vm.isEditMode
        ? (vm.CetDialog_UserGroup.title = $T("编辑用户组"))
        : (vm.CetDialog_UserGroup.title = $T("新增用户组"));
      vm.resetData();
      vm.CetDialog_UserGroup.openTrigger_in = new Date().getTime();
    },
    close() {
      this.CetDialog_UserGroup.closeTrigger_in = new Date().getTime();
    }
  }
};
</script>
<style lang="scss" scoped>
.CetForm {
  @include padding(J3 J4);
  @include border_radius(C1);
}
</style>
