<template>
  <div class="page eem-common">
    <el-main class="fullheight padding0 flex-column">
      <div class="mbJ3">
        <CetButton
          class="fr mlJ1"
          v-bind="CetButton_1"
          v-on="CetButton_1.event"
        ></CetButton>
        <!-- 向后查询按钮 -->
        <CetButton
          class="fr custom—square"
          v-bind="CetButton_3"
          v-on="CetButton_3.event"
        ></CetButton>
        <!-- <div class="basic-box fr ml-5 mr-5">
          <div class="basic-box-label" style="height: 32px; line-height: 32px">
            时段
          </div>
          <el-date-picker
            class="fr mr5 eem-date-picker-custom"
            style="width: 240px; border-radius: 0 4px 4px 0"
            v-model="CetDatePicker_1.val"
            format="yyyy年MM月"
            type="monthrange"
            size="small"
            :clearable="false"
            :pickerOptions="CetDatePicker_1.config.pickerOptions"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
          ></el-date-picker>
        </div> -->
        <CustomElDatePicker
          class="fr mlJ mrJ"
          style="width: 280px"
          :prefix_in="$T('选择时段')"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          v-bind="CetDatePicker_1.config"
          v-model="CetDatePicker_1.val"
        />
        <!-- 向前查询按钮 -->
        <CetButton
          class="fr custom—square"
          v-bind="CetButton_2"
          v-on="CetButton_2.event"
        ></CetButton>
        <div class="basic-box fr mrJ1">
          <customElSelect
            v-model="ElSelect_1.value"
            v-bind="ElSelect_1"
            v-on="ElSelect_1.event"
            :prefix_in="$T('展示方式')"
          >
            <ElOption
              v-for="item in ElOption_1.options_in"
              :key="item[ElOption_1.key]"
              :label="item[ElOption_1.label]"
              :value="item[ElOption_1.value]"
              :disabled="item[ElOption_1.disabled]"
            ></ElOption>
          </customElSelect>
        </div>
      </div>
      <div class="flex-auto eem-container mbJ3">
        <CetChart
          :inputData_in="CetChart_1.inputData_in"
          v-bind="CetChart_1.config"
          ref="CetChart"
          @datazoom="datazoomEvent"
          @restore="restoreEvent"
        />
      </div>
      <div
        class="flex-auto eem-container flex-column"
        style="min-height: 300px"
      >
        <el-popover placement="top-start" trigger="hover" class="mbJ3">
          <div>
            <div
              v-text="
                `${$T('容需计费临界值')}：` +
                `${$T('为容量单价')}` +
                `/${$T('需量单价')}` +
                `*${$T('容量。')}` +
                `${$T('若最大需量')}` +
                `<${$T('临界值')}` +
                `，${$T('则需量计费更经济')}` +
                `；${$T('若最大需量')}` +
                `>${$T('临界值')}` +
                `，${$T('则容量计费更经济。')}`
              "
            ></div>
            <div>
              {{
                `${$T("偏差费用")}` +
                `=（${$T("实际最大需量")}` +
                `-${$T("申报需量")}` +
                `*${$T("上限比例")}` +
                `）*${$T("需量电价")}` +
                `*${$T("惩罚系数")}` +
                `，${$T("不计算负偏差时")}` +
                `，${$T("偏差费用为0。")}`
              }}
            </div>
          </div>
          <i slot="reference" class="el-icon-question fsH3"></i>
        </el-popover>
        <CetTable
          class="flex-auto"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <ElTableColumn v-bind="ElTableColumn_timeText"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_maxDemand"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_maxTimeText"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_declareDemand"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_maxMonthDeclareDemand">
            <template slot-scope="scope">
              <span
                :style="{
                  color: ElTableColumn_maxMonthDeclareDemand.formatterType(
                    scope.row
                  )
                }"
              >
                {{
                  scope.row[ElTableColumn_maxMonthDeclareDemand.prop] || "--"
                }}
              </span>
            </template>
          </ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_deviationCost"></ElTableColumn>
        </CetTable>
      </div>
    </el-main>
  </div>
</template>
<script>
import common from "eem-utils/common";
import * as echarts from "echarts";
import { httping } from "@omega/http";
export default {
  name: "HistoricalTrend",
  components: {},
  props: {
    currentNode: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },

  data(vm) {
    const language = window.localStorage.getItem("omega_language") === "en";
    return {
      echartsData: null,
      schemeLevel: [], //预警等级
      copyTabData: [],
      CetChart_1: {
        inputData_in: {},
        config: {
          options: {
            title: {
              left: "left",
              text: $T("需量报告月度统计"),
              textStyle: {
                // color: "#fff"
              },
              padding: 20
            },
            grid: {
              left: 35,
              bottom: 10,
              right: 35,
              containLabel: true
            },
            legend: {
              top: 0,
              textStyle: {
                // color: "#fff"
              }
            },
            toolbox: {
              feature: {
                dataZoom: {
                  yAxisIndex: "none"
                },
                // restore: {},
                saveAsImage: {}
              },
              right: 30
            },
            tooltip: {
              trigger: "axis",
              formatter: function (value) {
                if (value.length > 0) {
                  var text = `<span>${value[0].axisValueLabel}</span> <br />`;
                  value.forEach(item => {
                    if (item.data !== "null") {
                      text += `<span>${item.seriesName}:</span> <span>${item.data}</span><br />`;
                    }
                  });
                  return text;
                }
              }
            },
            xAxis: {
              type: "time",
              boundaryGap: false,
              splitLine: {
                show: false
              },
              axisLine: {
                lineStyle: {
                  opacity: 0.6
                }
              },
              axisLabel: {
                rotate: 35,
                formatter: function (value, index) {
                  return vm.$moment(value).format("MM-DD HH:mm");
                }
              },
              splitNumber: 10
            },
            yAxis: {
              type: "value",
              axisLine: {
                lineStyle: {
                  opacity: 0.6
                }
              },
              min: function (value) {
                return Math.floor(value.min - 50);
              },
              max: function (value) {
                return Math.ceil(value.max + 50);
              }
            },
            series: [
              {
                name: $T("需量"),
                data: [],
                type: "line",
                itemStyle: {
                  color: "#5a95fb"
                },
                symbol: "none",
                // smooth: true,
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: "#99ccf4" },
                    { offset: 1, color: "#091545" }
                  ])
                }
              },
              {
                name: $T("容量"),
                data: [],
                type: "line",
                itemStyle: {
                  color: "#f2e38c"
                },
                symbol: "none"
              },
              {
                name: $T("容需计费临界值"),
                data: [],
                type: "line",
                itemStyle: {
                  color: "#ec3b3b"
                },
                symbol: "none"
              },
              {
                name: $T("账单值"),
                data: [],
                type: "line",
                itemStyle: {
                  color: "#e786d7"
                },
                symbol: "none"
              },
              {
                name: $T("月申报需量"),
                data: [],
                type: "line",
                itemStyle: {
                  color: "#99ccf4"
                },
                symbol: "none"
              }
            ]
          }
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: "",
        plain: true,
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetButton_3: {
        visible_in: true,
        disable_in: true,
        title: "",
        plain: true,
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      datePickerStart: 0,
      datePickerEnd: Date.now(),
      CetDatePicker_1: {
        disable_in: false,
        val: [
          this.$moment().subtract(1, "month").startOf("month").valueOf(),
          this.$moment().add(0, "month").startOf("month").valueOf()
        ],
        config: {
          valueFormat: "timestamp",
          type: "monthrange",
          rangeSeparator: $T("至"),
          clearable: false,
          size: "small",
          pickerOptions: {
            disabledDate(time) {
              return (
                time.getTime() > vm.datePickerEnd ||
                time.getTime() < vm.datePickerStart
              );
            },
            onPick({ maxDate, minDate }) {
              if (!maxDate && minDate) {
                // 第一次选择前后取12个月，不超过当月
                let startTime = vm.$moment(minDate).add(-11, "month").valueOf(),
                  endTime = vm.$moment(minDate).add(11, "month").valueOf();
                if (endTime > Date.now()) {
                  endTime = Date.now();
                }
                vm.datePickerEnd = endTime;
                vm.datePickerStart = startTime;
              } else if (maxDate && minDate) {
                vm.datePickerEnd = Date.now();
                vm.datePickerStart = 0;
              }
            }
          }
        }
      },
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: true,
        size: "small",
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      ElSelect_1: {
        value: 1,
        style: {},
        size: "small",
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [
          {
            id: 1,
            text: $T("按需量展示")
          },
          {
            id: 2,
            text: $T("按百分比展示")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        // defaultSort: null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      ElTableColumn_timeText: {
        prop: "timeText", // 支持path a[0].b
        label: $T("月份"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "100", //该宽度会自适应
        formatter: function (val) {
          if (val.timeText && val.timeText !== 0) {
            return val.timeText;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_maxDemand: {
        prop: "maxDemand", // 支持path a[0].b
        label: $T("月最大需量") + "（kW）", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "270" : "120", //该宽度会自适应
        formatter: function (val) {
          if ((val.maxDemand && val.maxDemand !== 0) || val.maxDemand === 0) {
            return val.maxDemand;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_maxTimeText: {
        prop: "maxTimeText", // 支持path a[0].b
        label: $T("最大需量发生时间"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "300" : "120", //该宽度会自适应
        formatter: function (val) {
          if (val.maxTimeText && val.maxTimeText !== 0) {
            return val.maxTimeText;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_declareDemand: {
        prop: "declareDemand", // 支持path a[0].b
        label: $T("本月申报需量") + "（kW）", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "340" : "150", //该宽度会自适应
        formatter: function (val) {
          if (
            (val.declareDemand && val.declareDemand !== 0) ||
            val.declareDemand === 0
          ) {
            return val.declareDemand;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_maxMonthDeclareDemand: {
        prop: "maxMonthDeclareDemand", // 支持path a[0].b
        label: $T("月最大需量") + "/" + $T("本月申报需量") + "%", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "510" : "170", //该宽度会自适应
        formatter: function (val) {
          if (
            (val.maxMonthDeclareDemand && val.maxMonthDeclareDemand !== 0) ||
            val.maxMonthDeclareDemand === 0
          ) {
            return val.maxMonthDeclareDemand;
          } else {
            return "--";
          }
        },
        formatterType: null
      },
      ElTableColumn_deviationCost: {
        //type: "",      // selection 勾选 index 序号
        prop: "deviationCost", // 支持path a[0].b
        label: $T("偏差费用（元）"), //列名
        headerAlign: "right",
        align: "right",
        showOverflowTooltip: true,
        minWidth: language ? "180" : "120", //该宽度会自适应
        formatter: function (val) {
          if (
            (val.deviationCost && val.deviationCost !== 0) ||
            val.deviationCost === 0
          ) {
            return val.deviationCost;
          } else {
            return "--";
          }
        }
      }
    };
  },
  watch: {
    "CetDatePicker_1.val": {
      handler: function (val) {
        if (
          this.$moment(val[1]).startOf("month").valueOf() >=
          this.$moment().startOf("month").valueOf()
        ) {
          this.CetButton_3.disable_in = true;
        } else {
          this.CetButton_3.disable_in = false;
        }
        this.getChartHistoryList();
        this.getHistoryList();
      },
      deep: true
    },
    currentNode: {
      handler: function (val) {
        if (val) {
          this.getChartHistoryList();
          this.getHistoryList();
          this.getProjectDetail();
        }
      },
      deep: true
    }
  },

  methods: {
    datazoomEvent(val) {
      if (val.batch[0].endValue && val.batch[0].startValue) {
        this.CetChart_1.config.options.xAxis.interval = null;
      } else {
        this.CetChart_1.config.options.xAxis.interval =
          this.CetChart_1.initDate;
      }
      this.$refs.CetChart.chart.setOption(this.CetChart_1.config.options);
    },
    restoreEvent() {
      var options = this.$refs.CetChart.chart.getOption();
      options.xAxis[0].interval = this.CetChart_1.initDate;
      this.$refs.CetChart.chart.setOption(options);
    },
    // 加载图表
    initEcharts() {
      // var xAxisArr = [];
      var data0 = [];
      var data1 = [];
      var data2 = [];
      var data3 = [];
      var data4 = [];
      var _this = this;
      if (!this.echartsData || !this.echartsData.monthDemandList) {
        return;
      }

      this.echartsData.monthDemandList.forEach(item => {
        // xAxisArr.push(this.$moment(item.time).format("MM-DD HH:mm"));
        if (this.ElSelect_1.value === 1) {
          data0.push(
            common.isEffectiveValue(item.demand)
              ? { value: [item.time, item.demand.toFixed(2)] }
              : { value: [item.time, null] }
          );
          data1.push(
            common.isEffectiveValue(item.volume)
              ? { value: [item.time, item.volume.toFixed(2)] }
              : { value: [item.time, null] }
          );
          data2.push(
            common.isEffectiveValue(item.rateVolume)
              ? { value: [item.time, item.rateVolume.toFixed(2)] }
              : { value: [item.time, null] }
          );
          data3.push(
            common.isEffectiveValue(item.billValue)
              ? { value: [item.time, item.billValue.toFixed(2)] }
              : { value: [item.time, null] }
          );
          data4.push(
            common.isEffectiveValue(item.declaredemand)
              ? { value: [item.time, item.declaredemand.toFixed(2)] }
              : { value: [item.time, null] }
          );
        } else if (this.ElSelect_1.value === 2) {
          data0.push(
            item.demand && item.volume
              ? {
                  value: [
                    item.time,
                    ((item.demand / item.volume) * 100).toFixed(2)
                  ]
                }
              : { value: [item.time, null] }
          );
          data1.push(
            item.volume
              ? { value: [item.time, "100.00"] }
              : { value: [item.time, null] }
          );
          data2.push(
            item.demandRate && item.volumeRate
              ? {
                  value: [
                    item.time,
                    ((item.volumeRate / item.demandRate) * 100).toFixed(2)
                  ]
                }
              : { value: [item.time, null] }
          );
          data3.push(
            item.billValue && item.volume
              ? {
                  value: [
                    item.time,
                    ((item.billValue / item.volume) * 100).toFixed(2)
                  ]
                }
              : { value: [item.time, null] }
          );
          data4.push(
            item.declaredemand && item.volume
              ? {
                  value: [
                    item.time,
                    ((item.declaredemand / item.volume) * 100).toFixed(2)
                  ]
                }
              : { value: [item.time, null] }
          );
        }
      });
      if (this.ElSelect_1.value === 1) {
        // 按需量展示
        this.CetChart_1.config.options.title.text = $T("需量报告月度统计");
        this.CetChart_1.config.options.tooltip.formatter = function (value) {
          if (value.length > 0) {
            var text = `<span>${_this
              .$moment(value[0].data.value[0])
              .format("MM-DD HH:mm")}</span> <br />`;
            value.forEach(item => {
              if (item.data) {
                var unit = item.seriesName === "容量" ? "kVA" : "kW";
                text += `<span>${item.seriesName}（${unit}）:</span> <span>${
                  item.data.value[1] || "--"
                }</span><br />`;
              }
            });
            return text;
          }
        };
        // this.CetChart_1.config.options.yAxis.name = "单位（KW）";
        this.CetChart_1.config.options.series[0].name = $T("需量");
        this.CetChart_1.config.options.series[1].name = $T("容量");
        this.CetChart_1.config.options.series[2].name = $T("容需计费临界值");
        this.CetChart_1.config.options.series[3].name = $T("账单值");
        this.CetChart_1.config.options.series[4].name = $T("月申报需量");
      } else if (this.ElSelect_1.value === 2) {
        // 按百分比展示
        this.CetChart_1.config.options.title.text =
          $T("需量报告月度统计百分比数据");
        this.CetChart_1.config.options.tooltip.formatter = function (value) {
          if (value.length > 0) {
            var text = `<span>${_this
              .$moment(value[0].data.value[0])
              .format("MM-DD HH:mm")}</span> <br />`;
            value.forEach(item => {
              if (item.data) {
                text += `<span>${item.seriesName}:</span> <span>${
                  item.data.value[1] || "--"
                }%</span><br />`;
              }
            });
            return text;
          }
        };
        // this.CetChart_1.config.options.yAxis.name = "百分比（%）";
        this.CetChart_1.config.options.series[0].name = `${$T("需量")}/${$T(
          "容量"
        )}`;
        this.CetChart_1.config.options.series[1].name = `${$T("容量")}/${$T(
          "需量"
        )}`;
        this.CetChart_1.config.options.series[2].name = `${$T("容量单价")}/${$T(
          "需量单价"
        )}`;
        this.CetChart_1.config.options.series[3].name = `${$T("账单值")}/${$T(
          "容量"
        )}`;
        this.CetChart_1.config.options.series[4].name = `${$T(
          "月申报需量"
        )}/${$T("容量")}`;
      }
      // this.CetChart_1.config.options.xAxis.data = xAxisArr;

      var date =
        (this.echartsData.monthDemandList[
          this.echartsData.monthDemandList.length - 1
        ].time -
          this.echartsData.monthDemandList[0].time) /
        10;
      date = Math.floor(date / (24 * 60 * 60 * 1000)) * (24 * 60 * 60 * 1000);
      this.CetChart_1.initDate = date;
      this.CetChart_1.config.options.xAxis.interval = date;

      this.CetChart_1.config.options.series[0].data = data0;
      this.CetChart_1.config.options.series[1].data = data1;
      this.CetChart_1.config.options.series[2].data = data2;
      this.CetChart_1.config.options.series[3].data = data3;
      this.CetChart_1.config.options.series[4].data = data4;
    },
    // 获取图表部分
    getChartHistoryList() {
      if (!this.currentNode) {
        return;
      }
      this.echartsData = null;
      this.CetChart_1.config.options.xAxis.data = [];
      this.CetChart_1.config.options.series[0].data = [];
      this.CetChart_1.config.options.series[1].data = [];
      this.CetChart_1.config.options.series[2].data = [];
      this.CetChart_1.config.options.series[3].data = [];
      this.CetChart_1.config.options.series[4].data = [];
      var data = {
        aggregationCycle: 14,
        startTime: this.$moment(this.CetDatePicker_1.val[0])
          .startOf("month")
          .valueOf(),
        endTime: this.$moment(this.CetDatePicker_1.val[1])
          .add(1, "month")
          .startOf("month")
          .valueOf(),
        node: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          name: this.currentNode.name
        },
        projectId: this.projectId
      };
      httping({
        url: "/eem-service/v1/demand/manage/chartHistoryList",
        method: "POST",
        data
      }).then(response => {
        if (response.code === 0 && response.data) {
          this.echartsData = this._.cloneDeep(response.data);
          this.initEcharts();
        }
      });
    },
    // 获取表格部分
    getHistoryList() {
      if (!this.currentNode) {
        return;
      }
      var data = {
        aggregationCycle: 14,
        startTime: this.$moment(this.CetDatePicker_1.val[0])
          .startOf("month")
          .valueOf(),
        endTime: this.$moment(this.CetDatePicker_1.val[1])
          .add(1, "month")
          .startOf("month")
          .valueOf(),
        node: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          name: this.currentNode.name
        },
        projectId: this.projectId
      };
      this.copyTabData = [];
      httping({
        url: "/eem-service/v1/demand/manage/historyList",
        method: "POST",
        data
      }).then(response => {
        if (response.code === 0 && response.data) {
          this.copyTabData = this._.cloneDeep(response.data);
          this.initTable();
        }
      });
    },
    // 渲染表格
    initTable() {
      var _this = this;
      // 判断字体颜色
      this.ElTableColumn_maxMonthDeclareDemand.formatterType = function (val) {
        if (_this.schemeLevel.length !== 0 && val.maxMonthDeclareDemand) {
          if (
            _this.schemeLevel.filter(item => item.alarmColor === 1)[0] &&
            val.maxMonthDeclareDemand >=
              _this.schemeLevel.filter(item => item.alarmColor === 1)[0].rate
          ) {
            return "#ff0000";
          } else if (
            _this.schemeLevel.filter(item => item.alarmColor === 1)[0] &&
            val.maxMonthDeclareDemand >=
              _this.schemeLevel.filter(item => item.alarmColor === 2)[0].rate
          ) {
            return "#ff9900";
          } else if (
            _this.schemeLevel.filter(item => item.alarmColor === 2)[0] &&
            val.maxMonthDeclareDemand >=
              _this.schemeLevel.filter(item => item.alarmColor === 3)[0].rate
          ) {
            return "#fbe100";
          }
        }
      };
      this.CetTable_1.data = this._.cloneDeep(this.copyTabData);
      this.CetTable_1.data.forEach(item => {
        item.timeText = item.time && this.$moment(item.time).format("YYYY-MM");
        item.maxTimeText =
          item.maxTime && this.$moment(item.maxTime).format("YYYY-MM-DD HH:mm");
        item.maxDemand =
          item.maxDemand &&
          item.maxDemand !== "NaN" &&
          Number(item.maxDemand).toFixed(2);
        item.declareDemand =
          item.declareDemand &&
          item.declareDemand !== "NaN" &&
          Number(item.declareDemand).toFixed(2);
        item.maxMonthDeclareDemand =
          item.maxMonthDeclareDemand &&
          item.maxMonthDeclareDemand !== "NaN" &&
          (item.maxMonthDeclareDemand * 100).toFixed(2);
        item.deviationCost =
          item.deviationCost &&
          item.deviationCost !== "NaN" &&
          Number(item.deviationCost).toFixed(2);
      });
    },
    // 获取方案
    getProjectDetail() {
      this.schemeLevel = [];
      httping({
        url:
          "/eem-service/v1/alarm/getSchemes/" +
          this.currentNode.id +
          "/" +
          this.currentNode.modelLabel +
          "/3/-1/0",
        method: "GET"
      }).then(response => {
        if (response.code === 0 && response.data) {
          this.getSchemeLevel(
            response.data[response.data.length - 1].id,
            response.data[response.data.length - 1].modelLabel
          );
        }
      });
    },
    // 获取预警等级设置
    getSchemeLevel(id, modelLabel) {
      // var me = this;
      httping({
        url: "/eem-service/v1/alarm/getSchemeLevel/" + id + "/" + modelLabel,
        method: "GET"
      }).then(response => {
        if (response.code === 0 && response.data) {
          this.schemeLevel = response.data;
          this.initTable();
        }
      });
    },
    // 导出
    exportHistoryList() {
      this.$confirm($T("确定要导出吗？"), $T("提示"), {
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          var data = {
            aggregationCycle: 14,
            startTime: this.$moment(this.CetDatePicker_1.val[0])
              .startOf("month")
              .valueOf(),
            endTime: this.$moment(this.CetDatePicker_1.val[1])
              .add(1, "month")
              .startOf("month")
              .valueOf(),
            node: {
              id: this.currentNode.id,
              modelLabel: this.currentNode.modelLabel,
              name: this.currentNode.name
            },
            projectId: this.projectId,
            showType: this.ElSelect_1.value
          };
          common.downExcel(
            `/eem-service/v1/demand/manage/exportHistoryList`,
            data,
            this.token
          );
        })
        .catch(action => {
          if (action === "cancel") {
            this.$message({
              type: "info",
              message: $T("已取消")
            });
          }
        });
    },
    CetButton_2_statusTrigger_out(val) {
      const date1 = this.$moment(this.CetDatePicker_1.val[0]);
      const date2 = this.$moment(this.CetDatePicker_1.val[1]);
      this.CetDatePicker_1.val = [
        date1.subtract(1, "month").startOf("month").valueOf(),
        date2.subtract(1, "month").startOf("month").valueOf()
      ];
    },
    CetButton_3_statusTrigger_out(val) {
      const date1 = this.$moment(this.CetDatePicker_1.val[0]);
      const date2 = this.$moment(this.CetDatePicker_1.val[1]);
      if (
        date2.startOf("month").valueOf() !==
        this.$moment().startOf("month").valueOf()
      ) {
        this.CetDatePicker_1.val = [
          date1.add(1, "month").startOf("month").valueOf(),
          date2.add(1, "month").startOf("month").valueOf()
        ];
      }
    },
    CetButton_1_statusTrigger_out(val) {
      this.exportHistoryList();
    },
    // 1输出,方法名要带_out后缀
    ElSelect_1_change_out(val) {
      this.initEcharts();
    },
    CetTable_1_record_out(val) {},
    CetTable_1_outputData_out(val) {}
  },
  created: function () {
    this.getProjectDetail();
  },
  mounted: function () {
    this.getChartHistoryList();
    this.getHistoryList();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.basic-box-label {
  height: 32px;
  line-height: 32px;
}
.cycle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  text-align: center;
  @include background_color(ZS);
}
</style>
