<template>
  <div class="page">
    <previewLogin ref="previewLogin" v-bind="$attrs"></previewLogin>
  </div>
</template>

<script>
import previewLogin from "eem-pages/pages/login/index.vue";
export default {
  components: {
    previewLogin
  },
  data() {
    return {};
  }
};
</script>

<style lang="scss" scoped>
.page {
  height: 750px;
  width: 1500px;
  :deep(
      .login-main
        .login-main-right
        .login-form
        .login-normal
        .el-form
        .login-btn
    ) {
    cursor: default;
    pointer-events: none; // 禁止点击事件
  }
}
</style>
