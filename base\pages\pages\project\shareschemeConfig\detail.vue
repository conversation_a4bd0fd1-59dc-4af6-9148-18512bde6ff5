<template>
  <div>
    <el-drawer
      class="detailDrawer"
      :title="$T('详情')"
      :visible.sync="drawer"
      size="1000px"
    >
      <div slot="title" class="clearfix">
        <span class="common-title-H1">{{ $T("详情") }}</span>
        <!-- <CetButton
          class="fr mlJ1"
          v-bind="CetButton_1"
          v-on="CetButton_1.event"
        ></CetButton>
        <CetButton
          class="fr mlJ1"
          v-bind="CetButton_2"
          v-on="CetButton_2.event"
        ></CetButton>
        <CetButton
          class="fr mlJ1"
          v-bind="CetButton_3"
          v-on="CetButton_3.event"
        ></CetButton> -->
      </div>
      <div class="head bg1 pJ3">
        <div class="fsH1 mbJ1">{{ detailData.name || "--" }}</div>
        <div class="headList">
          <div class="listItem">
            <div class="label">{{ $T("被分摊对象") }}</div>
            <div class="value">
              {{ (currentNode_in && currentNode_in.name) || "--" }}
            </div>
          </div>
          <div class="listItem">
            <div class="label">{{ $T("分摊方式") }}</div>
            <div class="value">
              {{ detailData.energysharemethodText || "--" }}
            </div>
          </div>
          <div class="listItem">
            <div class="label">{{ $T("生效日期") }}</div>
            <div class="value">
              {{
                detailData.starttime
                  ? $moment(detailData.starttime).format("YYYY/MM/DD")
                  : "--"
              }}
            </div>
          </div>
        </div>
      </div>
      <div class="pJ3 flex-auto">
        <div class="tableBox eem-cont-c1">
          <CetTable
            :data.sync="CetTable_2.data"
            :dynamicInput.sync="CetTable_2.dynamicInput"
            v-bind="CetTable_2"
            v-on="CetTable_2.event"
          >
            <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_objectname"></ElTableColumn>
            <ElTableColumn
              v-if="detailData && detailData.energysharemethod == 1"
              v-bind="ElTableColumn_rate"
            ></ElTableColumn>
            <ElTableColumn
              v-if="detailData && detailData.energysharemethod == 2"
              v-bind="ElTableColumn_energytypeText"
            ></ElTableColumn>
          </CetTable>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import { httping } from "@omega/http";
export default {
  components: {},
  props: {
    openTrigger_in: {
      type: Number
    },
    resetTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    table_in: {
      type: Array
    },
    currentNode_in: {
      type: Object
    }
  },
  filters: {},
  computed: {},
  data() {
    return {
      drawer: false,
      detailData: {},
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: "删除",
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: "编辑",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetButton_3: {
        visible_in: true,
        disable_in: false,
        title: "复制方案",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      CetTable_2: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {}
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        // prop: "", // 支持path a[0].b
        label: "#", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "41" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_objectname: {
        //type: "",      // selection 勾选 index 序号
        prop: "objectname", // 支持path a[0].b
        label: $T("分摊对象"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (val) {
          if (val.objectname) {
            return val.objectname;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_rate: {
        //type: "",      // selection 勾选 index 序号
        prop: "rate", // 支持path a[0].b
        label: $T("分摊系数"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (val) {
          if (val.rate || val.rate === 0) {
            return val.rate;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_energytypeText: {
        //type: "",      // selection 勾选 index 序号
        prop: "energytypeText", // 支持path a[0].b
        label: $T("分摊的能源类型"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (val) {
          if (val.energytypeText || val.energytypeText === 0) {
            return val.energytypeText;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      }
    };
  },
  watch: {
    openTrigger_in() {
      this.drawer = true;
      this.getDetail();
    },
    resetTrigger_in() {
      if (this.drawer) {
        this.getDetail();
      }
    }
  },
  methods: {
    getDetail() {
      this.detailData = this._.cloneDeep(this.inputData_in);
      this.CetTable_2.data = this._.cloneDeep(this.table_in);
    },
    CetButton_1_statusTrigger_out() {
      this.$confirm("确定要删除所选项吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: "btn-custom-cancel",
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: (action, instance, done) => {
          if (action == "confirm") {
            httping({
              url:
                "/eem-service/v1/schemeConfig/delEnergyShareConfigSchemeById?id=" +
                this.inputData_in.id,
              method: "DELETE"
            }).then(response => {
              if (response.code == 0) {
                this.$message({
                  message: "删除成功",
                  type: "success"
                });
                this.drawer = false;
                this.$emit("updateSchemeList_out");
              }
            });
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action != "confirm") {
            this.$message({
              type: "info",
              message: "取消删除！"
            });
          }
        }
      });
    },
    CetButton_2_statusTrigger_out() {
      this.$emit("edit_out");
    },
    CetButton_3_statusTrigger_out() {
      this.$emit("copy_out");
    }
  }
};
</script>
<style lang="scss" scoped>
.detailDrawer {
  :deep(.el-drawer__body) {
    @include background_color(BG);
    display: flex;
    flex-direction: column;
  }
  .head {
    border-top: 1px solid;
    @include border_color(B1);
    .headList {
      display: flex;
      .listItem {
        min-width: 100px;
        margin-right: 100px;
        &:last-child {
          margin-right: 0;
        }
        .label,
        .value {
          @include font_size(Aa);
        }
        .label {
          @include font_color(T3);
        }
        .value {
          @include margin_bottom(J1);
          line-height: 1.5;
        }
      }
    }
  }
  .tableBox {
    height: 100%;
  }
}
</style>
