<template>
  <div class="page eem-common flex-column">
    <el-header height="auto" class="header eem-container mbJ3">
      <div class="text lhHm">
        <div class="goBack" @click="goBack">
          <i class="el-icon-arrow-left"></i>
          {{ $T("返回") }}
        </div>
        <span class="common-title-H2 mlJ1">{{ $T("工单详情") }}</span>
        <div style="float: right" v-show="!isShow_in">
          <template v-if="orderMsg && orderMsg.worksheetstatus == 1">
            <!-- <CetButton class="fr" v-bind="CetButton_return" v-on="CetButton_return.event"></CetButton>
            <CetButton class="fr mr-10" v-bind="CetButton_confirm" v-on="CetButton_confirm.event"></CetButton> -->
            <CetButton
              class="fr mrJ1"
              v-bind="CetButton_mode"
              v-on="CetButton_mode.event"
            ></CetButton>
          </template>
          <template v-if="orderMsg && orderMsg.worksheetstatus == 2">
            <CetButton
              class="fr"
              v-bind="CetButton_input"
              v-on="CetButton_input.event"
            ></CetButton>
            <CetButton
              class="fr mrJ1"
              v-bind="CetButton_print"
              v-on="CetButton_print.event"
            ></CetButton>
            <CetButton
              class="fr mrJ1"
              v-bind="CetButton_export"
              v-on="CetButton_export.event"
            ></CetButton>
          </template>
        </div>
      </div>
    </el-header>
    <el-container class="flex-auto">
      <el-main style="height: 100%; padding: 0px">
        <div class="eem-cont mbJ3">
          <el-row :gutter="$J3">
            <el-col :span="24">
              <div class="mbJ3">
                <span>{{ $T("基础信息") }}</span>
              </div>
              <div style="height: 60px">
                <div
                  v-for="(item, index) in listData"
                  :key="index"
                  class="listItem fl"
                  :style="{ width: itemWidth }"
                >
                  <div class="ellipsis" style="" :title="item.name">
                    {{ item.name }}:
                  </div>
                  <el-tooltip
                    :content="filData(orderMsg[item.key], item.type)"
                    effect="light"
                    placement="top"
                  >
                    <div
                      v-show="item.type != 'button'"
                      class="text-overflow"
                      style=""
                    >
                      {{ filData(orderMsg[item.key], item.type, item.unit) }}
                    </div>
                  </el-tooltip>
                  <div v-show="item.type == 'button'" class="fl text-overflow">
                    <span
                      v-if="orderMsg && orderMsg.sourceid"
                      @click="showEventMsg"
                      class="clickformore"
                    >
                      {{ filData(orderMsg[item.key], item.type, item.unit) }}
                    </span>
                    <span v-else>
                      {{ filData(orderMsg[item.key], item.type, item.unit) }}
                    </span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="eem-cont mbJ3">
          <el-row :gutter="$J3">
            <el-col :span="8">
              <div class="mbJ3 lhHm">
                <span>{{ $T("故障描述") }}：</span>
              </div>
              <div class="description-icon">
                <div
                  style="line-height: 20px"
                  v-for="(item, index) in faultdescription.split('\n')"
                  :key="index"
                >
                  {{ item }}
                </div>
              </div>
            </el-col>
            <el-col :span="16">
              <div class="mbJ3 lhHm">
                <span>{{ $T("附件") }}：</span>
                <CetButton
                  class="fr"
                  v-bind="CetButton_2"
                  v-on="CetButton_2.event"
                ></CetButton>
              </div>
              <div style="height: 125px; overflow-y: auto">
                <div
                  class="img-item fl mbJ3"
                  v-for="(item, index) in imgList"
                  :key="index"
                >
                  <video
                    v-if="videoList.includes(item.url.split('.')[1])"
                    style="height: 100px; width: 100px"
                    :src="item.imgSrc"
                  ></video>
                  <img
                    v-else
                    style="height: 100px; width: 100px"
                    :src="item.imgSrc"
                    :alt="$T('获取附件失败')"
                  />
                  <div class="img-icon">
                    <span
                      class="clickformore"
                      @click="handleClick_see_out(item, index)"
                    >
                      {{ $T("预览") }}
                    </span>
                    <span
                      class="clickformore"
                      @click="handleClick_download_out(item, index)"
                    >
                      {{ $T("下载") }}
                    </span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="eem-cont">
          <el-row :gutter="$J3">
            <el-col :span="8">
              <div class="mbJ3">
                <span>{{ $T("专家库") }}:</span>
              </div>
              <div class="mbJ3 clearfix">
                <div
                  v-for="(item, index) in expertsListData"
                  :key="index"
                  class="listItem fl"
                  style="width: 33%"
                >
                  <el-tooltip :content="item.name + ':'" effect="light">
                    <div
                      class="ellipsis fl text-overflow"
                      style="width: 66px"
                      :title="item.name"
                    >
                      <span>{{ item.name }}:</span>
                    </div>
                  </el-tooltip>
                  <el-tooltip
                    :content="filData(orderMsg[item.key], item.type)"
                    effect="light"
                  >
                    <div
                      class="text-overflow fl"
                      style="width: calc(100% - 66px)"
                    >
                      {{ filData(orderMsg[item.key], item.type) }}
                    </div>
                  </el-tooltip>
                </div>
              </div>
              <div class="mbJ3">
                <span>{{ $T("预案推荐") }}:</span>
              </div>
              <div style="height: 340px">
                <CetTable
                  :data.sync="CetTable_1.data"
                  :dynamicInput.sync="CetTable_1.dynamicInput"
                  v-bind="CetTable_1"
                  v-on="CetTable_1.event"
                >
                  <template v-for="(column, index) in Columns_Simulation">
                    <el-table-column
                      v-if="column.custom && column.custom === 'tag'"
                      v-bind="column"
                      :key="index"
                      class-name="font0 hand"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <el-tag
                          size="small"
                          class="text-middle font14"
                          :effect="column.tagEffect"
                          :type="
                            column.tagTypeFormatter
                              ? column.tagTypeFormatter(scope.row, scope.column)
                              : 'primary'
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : scope.row[column.prop]
                          }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else-if="column.custom && column.custom === 'button'"
                      v-bind="column"
                      :key="index"
                      class-name="font0"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <span
                          class="clickformore"
                          @click.stop="
                            column.onButtonClick
                              ? column.onButtonClick(scope.row, scope.$index)
                              : void 0
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : column.text
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    ></el-table-column>
                  </template>
                </CetTable>
              </div>
            </el-col>
            <el-col :span="16">
              <div class="mbJ3">
                <span>{{ $T("流程状态") }}</span>
                <div style="float: right">
                  <span
                    :class="['btn_item', { active: isTab === 1 }]"
                    @click="handTab_out(1)"
                  >
                    {{ $T("流程表") }}
                  </span>
                  <span class="btnline"></span>
                  <span
                    :class="['btn_item', { active: isTab === 2 }]"
                    @click="handTab_out(2)"
                  >
                    {{ $T("流程图") }}
                  </span>
                </div>
              </div>
              <div style="height: 420px" v-show="isTab === 1">
                <CetTable
                  :data.sync="CetTable_2.data"
                  :dynamicInput.sync="CetTable_2.dynamicInput"
                  v-bind="CetTable_2"
                  v-on="CetTable_2.event"
                >
                  <template v-for="(column, index) in Columns_Process">
                    <el-table-column
                      v-if="column.custom && column.custom === 'tag'"
                      v-bind="column"
                      :key="index"
                      class-name="font0 hand"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <el-tag
                          size="small"
                          class="text-middle font14"
                          :effect="column.tagEffect"
                          :type="
                            column.tagTypeFormatter
                              ? column.tagTypeFormatter(scope.row, scope.column)
                              : 'primary'
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : scope.row[column.prop]
                          }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else-if="column.custom && column.custom === 'button'"
                      v-bind="column"
                      :key="index"
                      class-name="font0"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <span
                          class="clickformore"
                          @click.stop="
                            column.onButtonClick
                              ? column.onButtonClick(scope.row, scope.$index)
                              : void 0
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : column.text
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    ></el-table-column>
                  </template>
                </CetTable>
              </div>
              <div
                style="height: 420px; overflow: auto"
                class="bg1"
                v-show="isTab === 2"
              >
                <img :src="imgSrc" :alt="$T('暂无流程图')" />
              </div>
            </el-col>
          </el-row>
        </div>
      </el-main>
    </el-container>
    <!-- 维修工单打印 -->
    <div id="printContent" style="display: none">
      <printOrder
        :inputData_in="printOrder.inputData_in"
        :printOrderList_in="printOrder.printOrderList_in"
      ></printOrder>
    </div>
    <toExamine
      :visibleTrigger_in="toExamine.visibleTrigger_in"
      :closeTrigger_in="toExamine.closeTrigger_in"
      :inputData_in="toExamine.inputData_in"
      :codes_in="toExamine.codes_in"
      @confirm_out="toExamine_confirm_out"
    />
    <RepairMode
      :visibleTrigger_in="repairMode.visibleTrigger_in"
      :closeTrigger_in="repairMode.closeTrigger_in"
      :inputData_in="repairMode.inputData_in"
      @confirm_out="repairMode_confirm_out"
    />
    <FlowChart
      :visibleTrigger_in="flowChart.visibleTrigger_in"
      :closeTrigger_in="flowChart.closeTrigger_in"
      :inputData_in="flowChart.inputData_in"
      @confirm_out="flowChart_confirm_out"
    />
    <InputOrder
      :visibleTrigger_in="inputOrder.visibleTrigger_in"
      :closeTrigger_in="inputOrder.closeTrigger_in"
      :inputData_in="inputOrder.inputData_in"
      @confirm_out="inputOrder_confirm_out"
    />
    <ReservePlanMsg
      v-bind="ReservePlanMsg"
      v-on="ReservePlanMsg.event"
    ></ReservePlanMsg>
    <EventMsg v-bind="EventMsg" v-on="EventMsg.event"></EventMsg>
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom.js";
import toExamine from "./toExamine";
import RepairMode from "./RepairMode";
import FlowChart from "./FlowChart";
import InputOrder from "./InputOrder";
import ReservePlanMsg from "./ReservePlanMsg";
import EventMsg from "./EventMsg";
import printOrder from "./printOrder.vue";
var FileSaver = require("file-saver");
export default {
  name: "classDetail",
  components: {
    toExamine,
    RepairMode,
    FlowChart,
    InputOrder,
    ReservePlanMsg,
    EventMsg,
    printOrder
  },
  props: {
    inputData_in: {
      type: Object
    },
    isShow_in: {
      type: Boolean
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  data() {
    return {
      listData: [],
      itemWidth: "14.2%",
      expertsListData: [
        {
          name: $T("设备归类"),
          key: "deviceClassificationName",
          type: "string"
        },
        {
          name: $T("故障类型"),
          key: "eventClassificationName",
          type: "string"
        },
        {
          name: $T("故障"),
          key: "faultScenariosName",
          type: "string"
        }
      ],
      orderMsg: {},
      itemAction: 0,
      CetButton_return: {
        visible_in: true,
        disable_in: false,
        title: $T("退回"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_return_statusTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_mode: {
        visible_in: false,
        disable_in: false,
        title: $T("选择维修方式"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_mode_statusTrigger_out
        }
      },
      CetButton_input: {
        visible_in: false,
        disable_in: false,
        title: $T("录入"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_input_statusTrigger_out
        }
      },
      CetButton_print: {
        visible_in: true,
        disable_in: false,
        title: $T("打印"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_print_statusTrigger_out
        }
      },
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: $T("导出Excel"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: $T("全部下载"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryEventPlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          scenariosId_in: 0,
          limit_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 50
        },
        highlightCurrentRow: false,
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        },
        height: 50,
        style: {
          "text-align": "center"
        },
        showSelection: false
      },
      Columns_Simulation: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 60,
          label: "#",
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "name",
          minWidth: 100,
          width: "",
          label: $T("预案名称"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "adoptRate",
          minWidth: 100,
          width: "",
          label: $T("采纳率"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue, index) {
            if (typeof cellValue === "number") {
              return (cellValue * 100).toFixed2(2) + "%";
            } else {
              return "0.00%";
            }
          }
        },
        {
          type: "",
          prop: "",
          minWidth: 60,
          width: "",
          label: $T("操作"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null,
          custom: "button",
          text: $T("查看"),
          buttonFormatter: null,
          onButtonClick: this.showReservePlanDetail
        }
      ],
      CetTable_2: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          scenariosId_in: 0,
          limit_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 50
        },
        highlightCurrentRow: false,
        exportFileName: "",
        event: {},
        height: 50,
        style: {
          "text-align": "center"
        },
        showSelection: false
      },
      Columns_Process: [
        {
          type: "",
          prop: "nodename",
          minWidth: "",
          width: 100,
          label: $T("节点"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "operator",
          minWidth: 100,
          width: "",
          label: $T("操作人"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "detail",
          minWidth: "",
          width: "",
          label: $T("描述"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "logtime",
          minWidth: "",
          width: "",
          label: $T("日期时间"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatDateCol("YYYY-MM-DD HH:mm:ss")
        },
        {
          type: "",
          prop: "remark",
          minWidth: "",
          width: "",
          label: $T("内容"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ],
      toExamine: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        codes_in: []
      },
      repairMode: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      flowChart: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      inputOrder: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      printOrder: {
        inputData_in: {},
        printOrderList_in: []
      },
      isTab: 1,
      imgSrc: null,
      attachmentList: [],
      imgList: [],
      videoList: ["mp4", "3gp", "mkv", "avi", "mepg", "mpg"], // 视频支持的格式
      faultdescription: "",
      // 预案详情弹窗
      ReservePlanMsg: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        event: {
          saveData_out: this.reservePlanMsg_saveData_out
        }
      },
      // 事件详情弹窗
      EventMsg: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        event: {}
      }
    };
  },
  watch: {
    inputData_in: {
      handler: function (val, oldVal) {
        if ([3, 4, 6].includes(val.worksheetstatus)) {
          return;
        }
        this.listData = [
          {
            name: $T("工单号"),
            key: "code",
            type: "string"
          },
          {
            name: $T("创建时间"),
            key: "createtime",
            type: "date"
          },
          {
            name: $T("工单来源"),
            key: "sourceTypeName",
            type: "button"
          },
          {
            name: $T("维修目标"),
            key: "deviceplanrelationship_model",
            type: "array"
          },
          {
            name: $T("等级"),
            key: "taskLevelName",
            type: "string"
          },
          {
            name: $T("预计耗时"),
            key: "timeconsumeplan",
            unit: "h",
            type: "ms"
          },
          {
            name: $T("责任班组"),
            key: "teamName",
            type: "string"
          }
        ];
        if (val.worksheetstatus === 2) {
          this.itemWidth = "11.10%";
          this.listData = this.listData.concat([
            {
              name: $T("维修方式"),
              key: "repairTypeName",
              type: "string"
            },
            {
              name: $T("填报方式"),
              key: "fillFormTypeName",
              type: "string"
            }
          ]);
        }
        this.CetTable_1.data = [];
        this.queryExpertEventPlan_out();
        this.orderMsg = this._.cloneDeep(val);
        this.CetTable_2.data = this._.get(val, "processFlowUnits", []) || [];
        this.attachmentList = this._.get(val, "attachmentList", []) || [];
        this.faultdescription = val.faultdescription || "--";
        this.isTab = 1;
        this.imgSrc = null;
        this.imgList = [];
        this.getImgUrl(val.code);
        if (this.attachmentList && this.attachmentList.length > 0) {
          this.getAttachmentListImgUrl();
        }
        // if (val.worksheetstatus === 1) {
        //   this.getRepairConfigCode_out();
        // }
        this.checkAuth(val.code);
      },
      deep: true,
      immediate: true
    }
  },

  methods: {
    // 是否具有工单权限信息
    checkAuth(code) {
      if (!code) return;
      customApi.checkWorkorderAuth(code).then(res => {
        if (res.code === 0) {
          if (this.inputData_in.worksheetstatus === 1) {
            this.CetButton_mode.visible_in = res.data;
          } else {
            this.CetButton_mode.visible_in = false;
          }
          if (
            this.inputData_in.worksheetstatus === 2 ||
            this.inputData_in.worksheetstatus === 4
          ) {
            this.CetButton_input.visible_in = res.data;
          } else {
            this.CetButton_input.visible_in = false;
          }
        }
      });
    },
    toExamine_confirm_out(val) {
      this.$emit("goBack", true);
    },
    repairMode_confirm_out(val) {
      this.$emit("goBack", true);
    },
    flowChart_confirm_out(val) {},
    inputOrder_confirm_out(val) {
      this.$emit("goBack", true);
    },
    CetButton_return_statusTrigger_out(val) {
      this.toExamine.inputData_in = this._.cloneDeep(this.inputData_in);
      this.toExamine.codes_in = this._.cloneDeep([this.inputData_in.code]);
      this.toExamine.visibleTrigger_in = new Date().getTime();
    },
    CetButton_confirm_statusTrigger_out(val) {
      const params = {
        code: this.inputData_in.code,
        formData: { repairtype: 1 },
        userTaskParams: {
          taskResult: 1
        }
      };
      this.queryRepairWorkOrderSubmit_out(params);
    },
    CetButton_mode_statusTrigger_out(val) {
      this.repairMode.inputData_in = this._.cloneDeep(this.inputData_in);
      this.repairMode.visibleTrigger_in = new Date().getTime();
    },
    CetButton_input_statusTrigger_out(val) {
      this.inputOrder.inputData_in = this._.cloneDeep(this.inputData_in);
      this.inputOrder.visibleTrigger_in = new Date().getTime();
    },
    CetButton_print_statusTrigger_out(val) {
      this.printOrder.inputData_in = this._.cloneDeep(this.orderMsg);
      let sparePartsName = "";
      const spareParts =
        this._.get(
          this.orderMsg,
          "maintenanceContentObj.sparePartsReplaceRecords",
          []
        ) || [];
      spareParts.forEach((item, index) => {
        if (index) {
          sparePartsName += " , ";
        }
        sparePartsName += item.name + $T("备件") + item.number + item.unit;
      });
      if (sparePartsName) {
        sparePartsName = "(" + sparePartsName + ")";
      }

      this.orderMsg.sparePartsName = sparePartsName;
      const tableData = this.CetTable_1.data;
      const eventPlanTable = [];
      tableData.forEach((item, index) => {
        item.solution = item.solution || "";
        eventPlanTable.push({
          id: index,
          groupName: item.name,
          groupList: item.solution.split("\n")
        });
      });
      this.printOrder.printOrderList_in = [
        {
          orderMsg: this._.cloneDeep(this.orderMsg),
          eventPlanTable_in: this._.cloneDeep(eventPlanTable)
        }
      ];
      setTimeout(() => {
        const newStr = document.getElementById("printContent").innerHTML;
        var wind = window.open(
          "",
          "newwindow",
          "top=0, left=0, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no"
        );
        wind.document.body.innerHTML = newStr;
        wind.print();
        // wind.close();
      }, 100);
    },
    CetButton_export_statusTrigger_out(val) {
      if (!this.inputData_in.code) {
        this.$message.warning($T("获取不到工单编号"));
        return;
      }
      const url = `/eem-service/v1/workorder/repair/check/import/${this.inputData_in.code}`;
      const params = {
        code: this.inputData_in.code
      };

      common.downExcelGET(url, params, this.token, this.projectId);
    },
    CetButton_2_statusTrigger_out(val) {
      const imgList = this.attachmentList || [];
      if (imgList.length === 0) {
        this.$message.warning($T("无附件图片"));
        return;
      }

      imgList.forEach(item => {
        var downloadPath = item.url;
        var name = downloadPath.split("/").splice(-1)[0];
        this.downImg(downloadPath, name);
      });
    },
    CetTable_1_record_out(val) {},
    CetTable_1_outputData_out(val) {},
    handleDetail(index, row) {},
    handleEdit(index, row) {},
    goBack() {
      // this.$router.go(-1);
      this.$emit("goBack", false);
    },
    filData(val, type, unit = "") {
      if ([undefined, null, NaN].includes(val)) {
        return "--" + unit;
      } else if (type === "date") {
        return this.$moment(val).format("YYYY-MM-DD HH:mm:ss");
      } else if (type === "minute") {
        if (val === 5) {
          return $T("执行前5分钟");
        } else if (val === 24 * 60) {
          return $T("执行前1天");
        } else if (val === 2 * 24 * 60) {
          return $T("执行前2天");
        } else {
          return "--";
        }
      } else if (type === "ms") {
        let str = "--";
        const format = "hh h mm min";
        if (val || val === 0) {
          const hour = Math.floor(val / 3600000);
          const minute = Math.floor((val - hour * 3600000) / 60000);
          if (
            format.indexOf("hh") !== -1 &&
            format.indexOf("mm") !== -1 &&
            format.indexOf("ss") === -1
          ) {
            str = format.replace(
              /(.*)hh(.*)mm(.*)/,
              "$1" + hour + "$2" + minute + "$3"
            );
          }
        }
        return str;
      } else if (type === "array") {
        const list = val || [];
        let inspectObject = "";
        list.forEach((item, index) => {
          if (index) {
            inspectObject += " , ";
          }
          inspectObject += item.devicename;
        });
        return inspectObject;
      } else {
        return val + unit;
      }
    },
    handleClick_see_out(data) {
      this.flowChart.inputData_in = this._.cloneDeep(data);
      this.flowChart.visibleTrigger_in = new Date().getTime();
    },
    handleClick_download_out(data) {
      var downloadPath = data.url;
      var name = downloadPath.split("/").splice(-1)[0];
      this.downImg(downloadPath, name);
    },
    downImg: function (downloadPath, name) {
      if (!downloadPath) {
        return;
      }
      var url =
        "/eem-service/v1/common/downloadFile?path=" +
        encodeURIComponent(downloadPath);
      const params = {};
      common.generateImg(url, params, "GET").then(res => {
        if (res.status === 200 && res.data.type === "application/x-download") {
          //下载附件图片
          FileSaver.saveAs(res.data, name);
        }
      });
    },
    getAttachmentListImgUrl() {
      var imgList = this.attachmentList || [];
      imgList.forEach(item => {
        this.getAttachmentListUrl(item);
      });
    },
    getAttachmentListUrl: function (data) {
      const downloadPath = data.url;
      if (!downloadPath) {
        return;
      }
      var url =
        "/eem-service/v1/common/downloadFile?path=" +
        encodeURIComponent(downloadPath);
      const params = {};
      common.generateImg(url, params, "GET").then(res => {
        if (res.status === 200 && res.data.type === "application/x-download") {
          //将图片信息放到Img中
          const imgSrc = window.URL.createObjectURL(res.data);
          this.imgList.push({
            imgSrc: imgSrc,
            name: data.name,
            url: data.url
          });
        }
      });
    },
    //获取流程图
    getImgUrl(code) {
      var me = this;
      if (!code) {
        return;
      }
      // 主题色
      const currentTheme = localStorage.getItem("omega_theme");
      const isLightStyle = currentTheme === "light";
      var url = `/eem-service/v1/workorder/repair/workOrder/processDiagram/${code}?isLightStyle=${isLightStyle}`;
      const params = {};
      common.generateImg(url, params, "GET").then(res => {
        if (res.status === 200 && res.data.type === "image/png") {
          //将图片信息放到Img中
          me.imgSrc = window.URL.createObjectURL(res.data);
        }
      });
    },
    handTab_out(val) {
      this.isTab = val;
    },
    //过滤状态量样式
    statusStyleFormatter(row) {
      if (row.status) {
        return {};
      } else {
        return {
          color: "red"
        };
      }
    },
    //处理维修工单
    queryRepairWorkOrderSubmit_out(params) {
      const _this = this;
      if (!params || !params.code) {
        return;
      }
      customApi
        .queryRepairCommitSubmit(params)
        .then(res => {
          if (res.code === 0) {
            _this.CetDialog_add.closeTrigger_in = this._.cloneDeep(
              new Date().getTime()
            );
            _this.$emit("confirm_out", {});
            _this.$message.success($T("提交工单成功"));
          }
        })
        .catch(() => {});
    },
    //查询指定任务当前所处的节点,判断是否可以点击维修方式
    getRepairConfigCode_out() {
      if (!this.inputData_in.code) {
        return;
      }
      const params = {
        code: this.inputData_in.code
      };
      customApi
        .getRepairConfigCode(params)
        .then(res => {
          if (res.code === 0) {
            const nodeModel = this._.get(res, "data.nodeModel", "");
            if (nodeModel === "chooseRepairType") {
              this.CetButton_mode.disable_in = false;
            } else {
              this.CetButton_mode.disable_in = true;
            }
          }
        })
        .catch(() => {});
    },
    //弹出预案详情弹框
    showReservePlanDetail(row, index) {
      row.index = index + 1;
      this.ReservePlanMsg.inputData_in = this._.cloneDeep(row);
      this.ReservePlanMsg.visibleTrigger_in = new Date().getTime();
    },
    //弹出事件详情弹框
    showEventMsg() {
      if (!this.orderMsg.sourceid) {
        this.$message.warning($T("暂无关联事件信息"));
        return;
      }
      if (this.orderMsg.sourcetype === 3) {
        if (!this.$checkPermission("inspectorworkorder_browser")) {
          this.$message.warning($T("无巡检工单查看权限"));
        } else {
          this.$emit("goBack", 3);
        }
        return;
      }
      this.EventMsg.inputData_in = this._.cloneDeep(this.inputData_in);
      this.EventMsg.visibleTrigger_in = new Date().getTime();
    },
    // 获取工单详情信息
    getRepairWorkOrder_out(callback) {
      var _this = this;
      if (!this.inputData_in.code) {
        callback && callback(false);
        return;
      }
      var data = {
        code: this.inputData_in.code
      };
      customApi.getRepairWorkOrder(data).then(res => {
        if (res.code === 0) {
          const resData = _this._.get(res, "data", {}) || {};
          _this.orderMsg = this._.cloneDeep(resData);
          callback && callback(true);
        } else {
          callback && callback(false);
        }
      });
    },
    //获取获取预案列表
    queryExpertEventPlan_out() {
      var _this = this;
      if (!this.inputData_in.faultscenariosid) {
        return;
      }
      var data = {
        scenariosId: this.inputData_in.faultscenariosid,
        limit: 3
      };
      customApi.queryExpertEventPlan(data).then(res => {
        if (res.code === 0) {
          _this.CetTable_1.data = res.data || [];
        }
      });
    },
    reservePlanMsg_saveData_out() {}
  },
  created: function () {},
  mounted: function () {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;
}
.header {
  .goBack {
    display: inline-block;
    @include font_color(ZS);
    cursor: pointer;
    line-height: 20px;
  }
}
.lhHm {
  @include line_height(Hm);
}

.row-detail {
  cursor: pointer;
  color: #0066cc;
  &:hover {
    color: rgb(0, 58, 111);
  }
}

.row-edit {
  cursor: pointer;
  color: #0066cc;
  &:hover {
    color: rgb(0, 58, 111);
  }
}

.row-delete {
  cursor: pointer;
  color: red;
  &:hover {
    color: rgb(0, 58, 111);
  }
}
.listItem {
  margin-top: 5px;
  box-sizing: border-box;
  padding: 0 5px;
  line-height: 25px;
  border-radius: 5px;
  width: 14.2%;
}
.list-delete {
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: inline-block;
  //   background: url("../assets/22_u2309.png") no-repeat center 2px;
  background-size: 20px 20px;
}
.list-tips {
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: inline-block;
  //   background: url("../assets/u2319.png") no-repeat center 4px;
  background-size: 16px 16px;
}
.btnline {
  display: inline-block;
  position: relative;
  height: 14px;
  width: 1px;
  @include background_color(T1);
  top: 2px;
}
.btn_item {
  cursor: pointer;
  @include font_color(T1);
  &.active {
    @include font_color(ZS);
  }
}
.description-icon {
  height: 120px;
  overflow-y: auto;
  padding: 5px 10px;
  line-height: 1.5;
  box-sizing: border-box;
  width: 100%;
  @include background_color(BG1);
  background-image: none;
  border: 1px solid;
  @include border_color(B1);
  @include border_radius(C);
  word-break: break-all;
}
.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.clickformore {
  cursor: pointer;
  @include font_color(ZS);
}
.img-item {
  height: 100px;
  width: 100px;
  padding: 0px;
  .img-icon {
    display: none;
    text-align: center;
  }
  &:hover {
    @include background_color(BG);
    .img-icon {
      display: block;
    }
  }
}
</style>
