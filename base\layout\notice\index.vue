<template>
  <el-popover
    style="order: -1"
    placement="bottom-end"
    trigger="click"
    width="400"
    v-model="visible"
  >
    <el-badge :hidden="!total" :value="total" :max="99" slot="reference">
      <omega-icon class="icon-hover-normal" symbolId="layout-alarm-mode-lin" />
    </el-badge>
    <div class="layout-notice">
      <div class="layout-notice-header">
        <span class="title">
          <span class="fsH3 fl lh32">{{ $T("消息") }}</span>
          <VolumeCtrl ref="volumeCtrl" class="ml10" />
        </span>
        <span class="total">
          {{ $T("共 {0} 条", total) }}
        </span>
      </div>
      <div class="layout-notice-main">
        <div v-if="items.length === 0" class="layout-notice-main-empty">
          <span>{{ $T("暂无数据") }}</span>
        </div>
        <transition-group v-else name="el-zoom-in-top">
          <NoticeItem
            class="layout-notice-item"
            v-for="(item, index) in items"
            :key="item.id || index"
            :item="item"
            @removeItem="removeItem"
          />
        </transition-group>
      </div>
      <div class="layout-notice-footer">
        <el-link :underline="false" type="primary" @click="clearItems">
          {{ $T("全部已读") }}
        </el-link>
        <el-link type="primary" @click="evViewAllClick">
          {{ $T("查看全部") }}
        </el-link>
      </div>
      <!--消息中心弹窗页面 -->
      <MessageCenter
        v-bind="MessageCenter"
        v-on="MessageCenter.event"
      ></MessageCenter>
    </div>
  </el-popover>
</template>

<script>
import VolumeCtrl from "./VolumeCtrl";
import { WebSocketConnectClient } from "./modules/WebSocketClient";
import { http } from "@omega/http";
import omegaAuth from "@omega/auth";
import NoticeItem from "./NoticeItem";
import { guid } from "@omega/layout/utils/util";
import MessageCenter from "./MessageCenter.vue";
import _ from "lodash";

const client = new WebSocketConnectClient();

export default {
  name: "LayoutNotice",
  components: {
    VolumeCtrl,
    NoticeItem,
    MessageCenter
  },
  data() {
    return {
      items: [],
      visible: false,
      //请填写组件含义弹窗页面的data
      MessageCenter: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        event: {}
      },
      intervalMsgArr: null,
      msgInterval: false,
      cacheMsgArr: []
    };
  },
  created() {
    const userId = omegaAuth.user.getUserId();
    let tags = [];
    tags.push(String(userId));
    const isHTTPS = window.location.protocol === "https:";
    client.connect({
      url: `${isHTTPS ? "wss" : "ws"}://${
        window.location.host
      }/messageServer/websocket/${userId}`
    });
    client.on("open", () => {
      http({
        url: `/messageServer/v1/service/web/client`,
        method: "POST",
        data: {
          // 用户登录ID
          userId: userId,
          // 用户标签，用户给一组用户发送信息时用
          // 一个用户对应多个标签用“,”隔开，如巡检员,预试员,业主,项目1
          // tags: [`${userId}`]
          tags: tags
        }
      });
    });
    client.on("message", msg => {
      //高版本消息推送，会有空数据（空字符）推送过来，需要对其过滤处理；
      if (!msg) {
        return;
      }
      // 判断是否是导入消息信息
      if (msg.logType && msg.logType === 200) {
        this.handleNotice(msg);
        return;
      }
      //查看缓存列表里面是否存储有事件，如果有按照每0.5秒速度进行渲染
      if (this.cacheMsgArr.length && !this.intervalMsgArr) {
        this.intervalMsgArr = setInterval(() => {
          let newMsg = this.cacheMsgArr.shift();
          let message = this.handleMessage(newMsg);
          this.addItem(message);
          if (this.cacheMsgArr.length === 0) {
            clearInterval(this.intervalMsgArr);
            this.intervalMsgArr = null;
          }
        }, 200);
      }
      //将一秒内多余事件放到缓存列表里面，然后退出
      if (this.msgInterval) {
        if (this.cacheMsgArr.length > 50000) {
          this.cacheMsgArr = [];
        }
        this.cacheMsgArr.push(msg);
        return;
      }
      //判断缓存列表里面是否已经有事件
      if (this.cacheMsgArr.length && this.intervalMsgArr) {
        if (this.cacheMsgArr.length > 50000) {
          this.cacheMsgArr = [];
        }
        this.cacheMsgArr.push(msg);
      } else {
        this.$refs.volumeCtrl.rePlay();
        let message = this.handleMessage(msg);
        this.addItem(message);
      }
      this.msgInterval = true;
      //设置一秒延时，一秒内不重复发铃声，然后将一秒内多余事件放到缓存事件列表里面
      const timeoutLen = setTimeout(() => {
        this.msgInterval = false;
        clearTimeout(timeoutLen);
        //查看缓存列表里面是否存储有事件，如果有按照每0.5秒速度进行渲染
        if (this.cacheMsgArr.length === 1 && !this.intervalMsgArr) {
          let newMsg = this.cacheMsgArr.shift();
          let message = this.handleMessage(newMsg);
          this.addItem(message);
        } else if (this.cacheMsgArr.length) {
          this.$refs.volumeCtrl.rePlay();
        }
      }, 2000);
    });
  },
  computed: {
    total() {
      return this.items.length;
    }
  },
  methods: {
    handleMessage(message) {
      if ([702, 703, 704, 705, 706, 707, 708, 709].includes(message.logType)) {
        // 报警事件
        let isJSONStr = this.isJSONStr(message.content);
        if (isJSONStr) {
          const content = JSON.parse(message.content);
          message.content = content.message;
        }
        message.level = 2;
        return message;
      } else if ([701, 710].includes(message.logType)) {
        // 预警事件
        let isJSONStr = this.isJSONStr(message.content);
        if (isJSONStr) {
          const content = JSON.parse(message.content);
          message.content = content.message;
        }
        message.level = 4;
        return message;
      } else if ([711, 713, 714, 715].includes(message.logType)) {
        // 预警事件,711:巡检工单即将超时的事件
        message.level = 4;
        return message;
      } else {
        let description = message.description || "{}";
        let level = 5;
        let isJSONStr = this.isJSONStr(description);
        if (isJSONStr) {
          description = JSON.parse(description);
          level = parseInt(description.eventClass) || 5;
        }
        if (level > 5 || level < 1) level = 5;
        message.level = level;
        return message;
      }
    },
    addItem(item) {
      // this.items.unshift({
      //   id: guid("notice_"),
      //   ...item
      // });
      //2022-3-18处理消息推送过于频繁，防止内存泄漏；扩展运算符相当于是浅拷贝，会遍历对象的所有属性。
      item.id = guid("notice_");
      this.items.unshift(item);
      // 最大条目限制为100条
      if (this.items.length > 99) {
        this.items.pop();
      }
    },
    removeItem(item) {
      const index = _.findIndex(this.items, {
        id: item.id
      });
      this.items.splice(index, 1);
    },
    clearItems() {
      this.items = [];
    },
    evViewAllClick() {
      this.MessageCenter.openTrigger_in = new Date().getTime();
    },
    isJSONStr(str) {
      if (typeof str === "string") {
        try {
          var obj = JSON.parse(str);
          if (typeof obj === "object" && obj) {
            return true;
          }
        } catch (e) {
          return false;
        }
      }
      return false;
    },
    // 连接关系导入消息处理
    handleNotice(msg) {
      setTimeout(() => {
        this.$notify({
          title: "提示",
          message: msg.description,
          duration: 0
        });
      }, 3000);
    }
  },
  mounted() {},
  beforeDestroy() {
    client.close();
  }
};
</script>

<style lang="scss" scoped>
.layout-notice {
  margin: -12px;
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    box-sizing: border-box;
    @include padding(0 J3);
    @include font_color(T1);
    border-bottom: 1px solid;
    @include border_direction_color(B2, "bottom");
    & .title {
      @include font_size(H3);
    }
    & .total {
      @include font_size(Ab);
    }
  }
  &-main {
    height: 415px;
    overflow: auto;
    @include padding(0 J3);
    & .layout-notice-item:not(:last-child) {
      border-bottom: 1px solid;
      @include border_direction_color(B2, "bottom");
    }
    &-empty {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &-scroll {
      height: 415px !important;
    }
  }
  &-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    box-sizing: border-box;
    @include padding(0 J3);
    @include font_size(Aa);
    // @include font_color(ZS);
    border-top: 1px solid;
    @include border_direction_color(B2, "top");
  }
}
:deep() {
  .el-badge__content {
    z-index: 1;
  }
}
</style>
