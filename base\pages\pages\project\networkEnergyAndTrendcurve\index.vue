<template>
  <div class="page eem-common">
    <el-container style="height: 100%; min-width: 1324px">
      <el-aside width="315px" class="eem-aside flex-column">
        <customElSelect
          v-model="ElSelect_1.value"
          v-bind="ElSelect_1"
          v-on="ElSelect_1.event"
          class="mbJ1"
          prefix_in="能源类型"
        >
          <ElOption
            v-for="item in ElOption_1.options_in"
            :key="item[ElOption_1.key]"
            :label="item[ElOption_1.label]"
            :value="item[ElOption_1.value]"
            :disabled="item[ElOption_1.disabled]"
          ></ElOption>
        </customElSelect>
        <CetGiantTree
          class="bg1 flex-auto"
          v-show="!showCheckbox"
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
        ></CetGiantTree>
        <CetGiantTree
          v-show="showCheckbox"
          class="bg1 flex-auto"
          v-bind="CetGiantTree_2"
          v-on="CetGiantTree_2.event"
        ></CetGiantTree>
      </el-aside>
      <el-container class="mlJ3 fullheight" style="overflow: auto">
        <el-container class="fullheight eem-min-width">
          <el-header height="auto" class="mbJ3 padding0">
            <div class="head-label text-middle">
              {{ nodeName || "--" }}

              <el-radio-group
                size="small"
                class="radiogroup"
                v-model="selectedMenu"
              >
                <el-radio-button
                  v-for="menu in menus"
                  :label="menu"
                  :key="menu"
                >
                  {{ menu }}
                </el-radio-button>
              </el-radio-group>
            </div>
          </el-header>
          <EnergyQuery
            v-if="selectedMenu == '能耗查询'"
            v-bind="energyQuery"
            v-on="energyQuery.event"
            :energy="energy"
            style="height: 100%"
            class="eem-container"
            ref="EnergyQuery"
          />
          <TrendCurve ref="trendCurve" v-if="selectedMenu == '趋势曲线'" />
        </el-container>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import TREE_PARAMS from "@/store/treeParams.js";
import customApi from "@/api/custom";
import EnergyQuery from "./energyQuery/energyQuery.vue";
import TrendCurve from "./trendCurve/trendCurve.vue";

export default {
  components: {
    EnergyQuery,
    TrendCurve
  },

  computed: {
    energy() {
      return this.ElOption_1.options_in.find(
        item => this.ElSelect_1.value == item.id
      );
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    numberOfNodesCompared() {
      return this.$store.state.systemCfg.numberOfNodesCompared || 4;
    }
  },

  data() {
    return {
      menus: ["能耗查询", "趋势曲线"],
      selectedMenu: "能耗查询",
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.nodeClick_out //选中单行输出
        }
      },
      clickNode: null,
      checkedNodes: [],
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          callback: {
            beforeCheck: this.CetGiantTree_2_beforeCheck
          }
        },
        event: {
          currentNode_out: this.nodeClick2_out, //选中单行输出
          checkedNodes_out: this.checkboxClick //勾选节点输出
        }
      },
      nodeName: "",
      showCheckbox: false,
      ElSelect_1: {
        value: null,
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      energyQuery: {
        clickNode: null,
        checkedNodes: [],
        event: {
          setTree1Node: this.setTree1Node,
          setTree2Node: this.setTree2Node,
          changeCheckbox: this.changeCheckbox
        }
      }
    };
  },
  watch: {
    showCheckbox: {
      handler: function (val) {
        if (val) {
          this.CetGiantTree_2.selectNode = this._.cloneDeep(
            this.CetGiantTree_1.selectNode
          );
        } else {
          this.CetGiantTree_1.selectNode = this._.cloneDeep(
            this.CetGiantTree_2.selectNode
          );
        }
      }
    },
    selectedMenu: {
      handler: function (val, old) {
        if (val === "趋势曲线") {
          this.showCheckbox = false;
          this.$nextTick(() => {
            this.$refs.trendCurve.CetTree_1_currentNode_out(this.clickNode);
          });
        }
      }
    }
  },

  methods: {
    CetGiantTree_2_beforeCheck(treeId, treeNode) {
      if (["project", "room"].includes(treeNode.modelLabel)) {
        return false;
      }
    },
    setTree1Node(obj) {
      this.CetGiantTree_1.selectNode = obj;
    },
    setTree2Node(obj) {
      this.CetGiantTree_2.selectNode = obj;
      this.CetGiantTree_2.checkedNodes = [obj];
    },
    changeCheckbox(val) {
      this.showCheckbox = val;
    },
    async grtCetNoEnumSelect_1(id) {
      const res = await customApi.getProjectEnergy(id);
      if (res.code !== 0) {
        this.ElOption_1.options_in = [];
        this.ElSelect_1.value = null;
        return;
      }
      let selectData = [];
      res.data.forEach(item => {
        if (![22].includes(item.energytype)) {
          selectData.push({
            id: item.energytype,
            text: item.name
          });
        }
      });
      this.ElOption_1.options_in = selectData;
      if (selectData.find(item => item.id == 2)) {
        this.ElSelect_1.value = 2;
      } else {
        this.ElSelect_1.value = selectData[0].id;
      }
    },
    nodeClick_out(val) {
      if (["project", "room"].includes(val.modelLabel)) {
        this.$message.warning("请选择设备节点！");
        return;
      }
      if (this.showCheckbox) {
        return;
      }
      if (val) {
        this.nodeName = val.name;
        this.clickNode = val;
        this.energyQuery.clickNode = val;
        this.$nextTick(() => {
          if (this.selectedMenu === "能耗查询") {
            this.$refs.EnergyQuery.nodeClick_out(val);
          } else if (this.selectedMenu === "趋势曲线") {
            this.$refs.trendCurve.CetTree_1_currentNode_out(val);
          }
        });
      } else {
        this.energyQuery.clickNode = {};
        this.clickNode = {};
        this.$message.warning("请选择节点！");
      }
    },
    nodeClick2_out(val) {
      if (["project", "room"].includes(val.modelLabel)) {
        this.$message.warning("请选择设备节点！");
        return;
      }
      if (!this.showCheckbox) {
        return;
      }
      if (val) {
        this.nodeName = val.name;
        this.clickNode = val;
        this.energyQuery.clickNode = val;
        // if (!this.checkedNodes.length) {
        this.CetGiantTree_2.checkedNodes = [this._.cloneDeep(val)];
        // }
      } else {
        this.clickNode = {};
        this.energyQuery.clickNode = {};
        this.$message.warning("请选择节点！");
      }
    },
    checkboxClick(val) {
      if (val.length > this.numberOfNodesCompared) {
        this.$message.warning(
          $T("最多对比{0}个节点", this.numberOfNodesCompared)
        );
        this.CetGiantTree_2.checkedNodes = this._.cloneDeep(this.checkedNodes);
        return;
      }
      this.energyQuery.checkedNodes = val;
      this.checkedNodes = val;
      this.$nextTick(() => {
        this.$refs.EnergyQuery.getChartData3();
      });
    },
    ElSelect_1_change_out() {
      this.clickNode = null;
      this.energyQuery.clickNode = null;
      this.CetGiantTree_1.selectNode = null;
      this.checkedNodes = [];
      this.energyQuery.checkedNodes = [];
      this.CetGiantTree_2.selectNode = null;
      this.CetGiantTree_2.checkedNodes = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
      this.getTreeData(() => {
        this.CetGiantTree_1.selectNode = this._.get(
          this.CetGiantTree_1.inputData_in,
          "[0].children[0].children[0]"
        );
        this.CetGiantTree_2.selectNode = this._.get(
          this.CetGiantTree_2.inputData_in,
          "[0].children[0].children[0]"
        );
      });
    },
    async getTreeData(cb) {
      var _this = this;
      var data = {
        energyType: this.ElSelect_1.value,
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.projectConfigNetwork,
        treeReturnEnable: true
      };
      const res = await customApi.getNodeTreeSimple(data);
      if (res.code !== 0) {
        return;
      }
      const treeData = _this.setTreeDisabled(res.data);
      _this.CetGiantTree_1.inputData_in = treeData;
      _this.CetGiantTree_2.inputData_in = treeData;
      _this.CetGiantTree_1.selectNode = _this._.get(
        res,
        "data[0].children[0].children[0]"
      );
      cb && cb();
    },
    setTreeDisabled(treeData) {
      treeData.forEach(item => {
        item.chkDisabled = true;
        if (item.children) {
          item.children.forEach(i => {
            i.chkDisabled = true;
          });
        }
      });
      return treeData;
    },
    async init() {
      this.showCheckbox = false;
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
      this.selectedMenu = "";
      await this.$nextTick(() => {
        this.selectedMenu = "能耗查询";
      });
      await this.grtCetNoEnumSelect_1(this.projectId);
      this.getTreeData();
    }
  },

  mounted() {
    this.init();
  },
  activated() {
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}

.head-label {
  @include font_size(H2);
  @include font_weight(MD);
  line-height: 32px;
  position: relative;
  .radiogroup {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    z-index: 1;
  }
}
</style>
