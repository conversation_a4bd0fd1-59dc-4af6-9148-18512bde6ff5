<template>
  <div class="page">
    <div class="card">
      <div class="common-title-H3 mbJ1">{{ $T("效益评估") }}</div>
      <div class="mbJ1">
        <span class="mrJ3 text-T3">{{ $T("当日总能耗") }}</span>
        <span class="value">
          {{
            formatNumberWithPrecision(
              fitAndOptimizeData.dailyEnergyConsumption,
              2
            )
          }}
        </span>
        <span class="text-T3">kWh</span>
      </div>
      <el-progress
        class="mbJ1 oil1-progress"
        :stroke-width="9"
        :percentage="
          formatterPercentage(
            fitAndOptimizeData.dailyActualEnergyConsumption,
            fitAndOptimizeData.dailyEnergyConsumption
          )
        "
        :show-text="false"
      ></el-progress>
      <div class="mbJ1">
        <span class="box mrJ2 box-oil1"></span>
        <span class="mrJ3 text-T3">{{ $T("优化后能耗") }}</span>
        <span class="value">
          {{
            formatNumberWithPrecision(
              fitAndOptimizeData.dailyActualEnergyConsumption,
              2
            )
          }}
        </span>
        <span class="text-T3">kWh</span>
      </div>
      <div class="mbJ1">
        <span class="box mrJ2 box-oil3"></span>
        <span class="mrJ3 text-T3">{{ $T("节能潜力") }}</span>
        <span class="value">
          {{
            formatNumberWithPrecision(
              fitAndOptimizeData.dailyEnergyConsumptionSaving,
              2
            )
          }}
        </span>
        <span class="text-T3">kWh</span>
      </div>

      <div class="mbJ1">
        <span class="mrJ3 text-T3">{{ $T("近30日总能耗") }}</span>
        <span class="value">
          {{
            formatNumberWithPrecision(
              fitAndOptimizeData.totalEnergyConsumption,
              2
            )
          }}
        </span>
        <span class="text-T3">kWh</span>
      </div>
      <el-progress
        class="mbJ1 oil2-progress"
        :stroke-width="9"
        :percentage="
          formatterPercentage(
            fitAndOptimizeData.totalActualEnergyConsumption,
            fitAndOptimizeData.totalEnergyConsumption
          )
        "
        :show-text="false"
      ></el-progress>
      <div class="mbJ1">
        <span class="box mrJ2 box-oil2"></span>
        <span class="mrJ3 text-T3">{{ $T("优化后能耗") }}</span>
        <span class="value">
          {{
            formatNumberWithPrecision(
              fitAndOptimizeData.totalActualEnergyConsumption,
              2
            )
          }}
        </span>
        <span class="text-T3">kWh</span>
      </div>
      <div>
        <span class="box mrJ2 box-oil3"></span>
        <span class="mrJ3 text-T3">{{ $T("节能潜力") }}</span>
        <span class="value">
          {{
            formatNumberWithPrecision(
              fitAndOptimizeData.totalEnergyConsumptionSaving,
              2
            )
          }}
        </span>
        <span class="text-T3">kWh</span>
      </div>
    </div>
  </div>
</template>

<script>
import common from "eem-utils/common";
export default {
  name: "benefitEvaluation",
  components: {},
  props: {
    fitAndOptimizeData: {
      type: Object
    }
  },
  data() {
    return {};
  },
  watch: {},
  methods: {
    formatNumberWithPrecision(...args) {
      return common.formatNumberWithPrecision(...args);
    },

    formatterPercentage(val, total) {
      if (val && total) {
        return common.formatNumberWithPrecision((val / total) * 100, 2);
      } else {
        return 0;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;
}
.card {
  overflow: auto;
  width: 100%;
  height: 100%;
  background: url("../../../assets/bg-blue-block.png") no-repeat;
  background-size: cover;
  padding: 16px;
  box-sizing: border-box;
}
.value {
  font-size: 16px;
  font-weight: bold;
}
.box {
  display: inline-block;
  width: 8px;
  height: 8px;
}
.box-oil1 {
  @include background_color(oil1);
}
.box-oil2 {
  @include background_color(oil2);
}
.box-oil3 {
  @include background_color(oil3);
}
.el-progress :deep(.el-progress-bar__outer) {
  @include background_color(oil3, !important);
}
.oil1-progress {
  :deep(.el-progress-bar__inner) {
    @include background_color(oil1, !important);
  }
}
.oil2-progress {
  :deep(.el-progress-bar__inner) {
    @include background_color(oil2, !important);
  }
}
</style>
