<template>
  <div class="page eem-common">
    <div class="fullheight eem-container flex-column">
      <div class="pbJ3">
        <!-- add按钮组件 -->
        <CetButton
          class="fr mlJ1"
          v-bind="CetButton_add"
          v-on="CetButton_add.event"
        ></CetButton>
        <ElInput
          class="fr mlJ1"
          v-model="ElInput_keyword.value"
          v-bind="ElInput_keyword"
          v-on="ElInput_keyword.event"
        >
          <i class="el-icon-search el-input__icon" slot="suffix"></i>
        </ElInput>
      </div>
      <div class="flex-auto">
        <CetTable
          class="metrical-table-btn"
          :data.sync="CetTable_dimension.data"
          :dynamicInput.sync="CetTable_dimension.dynamicInput"
          v-bind="CetTable_dimension"
          v-on="CetTable_dimension.event"
        >
          <el-table-column
            prop="name"
            :label="$T('组织层级')"
          ></el-table-column>
          <el-table-column
            :label="$T('操作')"
            align="left"
            header-align="left"
            :width="en ? 200 : 170"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                class="fl mrJ3"
                :disabled="filTableRowCols1(scope.row)"
                @click="onClickAdd(scope.row)"
              >
                {{ filTableRowColsText1(scope.row) }}
              </el-button>
              <el-button
                type="text"
                class="fl mrJ3"
                :disabled="filTableRowCols2(scope.row)"
                @click="onClickEdit(scope.row)"
              >
                {{ $T("编辑") }}
              </el-button>
              <el-button
                type="text"
                class="fl"
                :class="{ delete: !filTableRowCols3(scope.row) }"
                :disabled="filTableRowCols3(scope.row)"
                @click="onClickDelete(scope.row)"
              >
                {{ $T("删除") }}
              </el-button>
            </template>
          </el-table-column>
        </CetTable>
      </div>
    </div>
    <addAndEditNode
      :visibleTrigger_in="addAndEditNode.visibleTrigger_in"
      :closeTrigger_in="addAndEditNode.closeTrigger_in"
      :inputData_in="addAndEditNode.inputData_in"
      :treeData_in="addAndEditNode.treeData_in"
      @saveData_out="addAndEditNode_saveData_out"
    />
  </div>
</template>
<script>
import customApi from "@/api/custom";
import addAndEditNode from "./dialog/addAndEditNode.vue";
const NODE_RELATION_TEXT = {
  all: $T("添加维度"),
  dimension: $T("添加层级"),
  level: $T("添加标签")
};
import { httping } from "@omega/http";
export default {
  name: "dimensionConfig",
  components: {
    addAndEditNode
  },
  props: { mountedTag: Number },
  computed: {
    token() {
      return this.$store.state.token;
    },
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },

  data() {
    return {
      // keyword组件
      ElInput_keyword: {
        value: "",
        placeholder: $T("请输入关键字"),
        style: {
          width: "284px"
        },
        event: {
          change: this.ElInput_keyword_change_out
        }
      },
      // add组件
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("添加维度"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      // dimension表格组件
      CetTable_dimension: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 10,
          layout: "sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        highlightCurrentRow: false,
        defaultExpandAll: true,
        rowKey: "tree_id",
        treeProps: { children: "children", hasChildren: "hasChildren" },
        event: {}
      },
      addAndEditNode: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        titleName: "",
        queryId_in: 0,
        inputData_in: null,
        treeData_in: null
      }
    };
  },
  watch: {},
  methods: {
    //控制添加子节点显示信息
    filTableRowColsText1(row) {
      return `${this._.get(
        NODE_RELATION_TEXT,
        row.modelLabel,
        $T("添加子节点")
      )}`;
    },
    //控制添加子节点按钮样式
    filTableRowCols1(row) {
      if (row.modelLabel === "tag") {
        return true;
      }
    },
    //控制编辑按钮样式
    //2022-6-29 根据40106缺陷，对固定维度设置可编辑
    filTableRowCols2(row) {
      if (row.modelLabel === "all") {
        return true;
      }
    },
    //控制删除按钮样式
    filTableRowCols3(row) {
      if (
        row.modelLabel === "all" ||
        (row.children && row.children.length > 0) ||
        row.fixdim
      ) {
        return true;
      }
    },
    // keyword输出,方法名要带_out后缀
    ElInput_keyword_change_out() {
      this.init();
    },
    // add输出
    CetButton_add_statusTrigger_out() {
      const fatherNode = {
        id: 1,
        modelLabel: "all",
        tree_id: "all_1"
      };
      this.addAndEditNode.treeData_in = this._.cloneDeep(fatherNode);
      this.addAndEditNode.inputData_in = {};
      this.addAndEditNode.visibleTrigger_in = new Date().getTime();
    },
    //点击添加子节点
    onClickAdd(val) {
      this.addAndEditNode.treeData_in = this._.cloneDeep(val);
      this.addAndEditNode.inputData_in = {};
      this.addAndEditNode.visibleTrigger_in = new Date().getTime();
    },
    //点击编辑节点
    onClickEdit(val) {
      let fatherNode = this.getFatherNode(this.CetTable_dimension.data, val);
      if (!fatherNode) {
        fatherNode = {
          id: 1,
          modelLabel: "all",
          tree_id: "all_1"
        };
      }
      this.addAndEditNode.treeData_in = this._.cloneDeep(fatherNode);
      this.addAndEditNode.inputData_in = this._.cloneDeep(val);
      this.addAndEditNode.visibleTrigger_in = new Date().getTime();
    },
    //点击删除节点
    onClickDelete(val) {
      const _this = this;
      if (!val) {
        return;
      }
      if (val.fixdim) {
        this.$message.warning("固定维度，不允许删除！");
        return;
      }
      this.$confirm($T("是否确认要删除该节点?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          const params = {
            modelLabel: val.modelLabel,
            id: val.id
          };
          _this.Delete_node(params);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已取消删除")
          });
        });
    },
    Delete_node(params) {
      customApi.deleteDimNode(params).then(response => {
        if (response.code === 0) {
          this.$message({
            type: "success",
            message: $T("删除成功!")
          });
          this.init();
        }
      });
    },
    //初始化
    init() {
      this.getDimensionMsg();
    },
    //获取维度列表信息
    getDimensionMsg: function () {
      var _this = this,
        auth = _this.token; //身份验证
      let urlStr = "/eem-service/v1/dim/setting/detailList";
      if (this.ElInput_keyword.value) {
        urlStr = `/eem-service/v1/dim/setting/detailList?keyword=${this.ElInput_keyword.value}`;
      }
      httping({
        url: urlStr,
        method: "GET",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          if (res.data) {
            _this.CetTable_dimension.data = res.data;
          }
        }
      });
    },
    //添加编辑节点成功后调用接口
    addAndEditNode_saveData_out() {
      this.init();
    },
    // 查找父级方法
    getFatherNode(nodeList, node) {
      const fatherList = this.findIndexArray(nodeList, node, []) || [];
      let index = -1;
      fatherList.forEach((item, i) => {
        if (item.tree_id === node.tree_id) {
          index = i;
        }
      });
      return fatherList[index - 1] || null;
    },
    /**
     * 通过选中的节点id逆推该节点的父级关系
     * @data 节点树数据
     * @tree_id 选中的节点id
     * @indexArray 存储父级关系
     */
    findIndexArray(data, node, indexArray) {
      let arr = Array.from(indexArray);
      for (let i = 0, len = data.length; i < len; i++) {
        arr.push(data[i]);
        if (data[i].tree_id === node.tree_id) {
          return arr;
        }
        let children = data[i].children;
        if (children && children.length) {
          let result = this.findIndexArray(children, node, arr);
          if (result) return result;
        }
        arr.pop();
      }
      return false;
    }
  },
  mounted() {
    if (this.mountedTag) {
      this.init();
    }
  },
  activated() {
    if (!this.mountedTag) {
      this.init();
    }
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.metrical-table-btn :deep(.el-button) {
  border: none !important;
  background-color: transparent !important;
}
.delete.el-button--text {
  @include font_color(Sta3);
}
:deep(.el-button + .el-button) {
  margin-left: 0;
}
</style>
