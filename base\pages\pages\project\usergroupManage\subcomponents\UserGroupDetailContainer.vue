<template>
  <el-container class="fullfilled eem-common">
    <el-header class="title lh58 bg1 brC3" height="58px">
      <span class="font24 text-middle">用户组管理{{ suffixTitle }}</span>
      <div class="fr">
        <template>
          <el-button
            class="text-middle"
            type="primary"
            size="small"
            plain
            @click.stop="editUserGroup"
          >
            编辑
          </el-button>
        </template>
        <template>
          <el-button
            class="text-middle"
            type="danger"
            size="small"
            plain
            @click.stop="deleteUserGroup"
          >
            删除
          </el-button>
        </template>
      </div>
    </el-header>
    <el-main class="mtJ2 padding0">
      <el-container class="fullfilled">
        <div class="box-preview eem-container">
          <div class="box-top">
            <el-header class="padding0 font18 mlJ2 mtJ2" height="24px">
              用户组
            </el-header>
            <el-main class="padding0 no-overflow">
              <div class="pt100">
                <div class="flex ptJ2">
                  <img
                    v-if="showLogo"
                    class="logo mt10"
                    :src="userGroupDetail.logoData"
                    alt=""
                  />
                  <div v-else class="logo none mt10 gray-font">
                    {{ $T("暂无图片") }}
                  </div>
                </div>
              </div>
            </el-main>
          </div>
          <div class="box-nopicture"></div>
        </div>
        <div class="box-picture eem-container flex-column">
          <div class="box-userlist">用户列表</div>
          <div class="mtJ2 flex-auto">
            <el-table
              ref="table"
              tooltip-effect="light"
              border
              :data="tableData"
              height="100%"
            >
              <el-table-column
                :index="tableIndex"
                type="index"
                header-align="center"
                align="center"
                show-overflow-tooltip
                label="序号"
                width="60"
              ></el-table-column>
              <ElTableColumn
                v-for="(item, index) in ElTableColumnArr"
                :key="index"
                v-bind="item"
              ></ElTableColumn>
            </el-table>
          </div>
          <el-pagination
            class="mtJ1"
            style="text-align: right"
            @size-change="handleSizeChange"
            @current-change="handleCurrentPageChange"
            :current-page.sync="currentPage"
            :page-size.sync="pageSize"
            :total="totalCount"
            layout="total,sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100]"
          ></el-pagination>
        </div>
      </el-container>
    </el-main>
  </el-container>
</template>
<script>
import commonApi from "@/api/custom";
export default {
  name: "UserGroupDetailContainer",
  props: {
    userGroupData_in: {
      type: Object,
      default: () => {}
    },
    refreshTrigger_in: {
      type: Number,
      default: new Date().getTime()
    }
  },
  computed: {
    suffixTitle() {
      let vm = this;
      let userGroup = vm.userGroupDetail;
      return userGroup.name ? "-" + userGroup.name : "";
    },
    showLogo() {
      return !!this.userGroupDetail.logoData;
    }
  },
  watch: {
    refreshTrigger_in() {
      this.refresh();
    },
    userGroupData_in(val) {
      this.totalTableData = this._.cloneDeep(val.users || []);
      this.totalCount = this.totalTableData.length;
      this.tableData = this.totalTableData.slice(
        (this.currentPage - 1) * this.pageSize,
        this.currentPage * this.pageSize
      );

      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    }
  },
  data() {
    return {
      ElTableColumnArr: [
        {
          label: "用户名称",
          prop: "name",
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: function (val) {
            if (val.name) {
              return val.name;
            } else {
              return "--";
            }
          }
        },
        {
          label: "移动电话",
          prop: "mobilePhone",
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: function (val) {
            if (val.mobilePhone) {
              return val.mobilePhone;
            } else {
              return "--";
            }
          }
        },
        {
          label: "电子邮箱",
          prop: "email",
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: function (val) {
            if (val.email) {
              return val.email;
            } else {
              return "--";
            }
          }
        }
      ],
      totalTableData: [],
      tableData: [],
      totalCount: 0,
      pageSize: 20,
      currentPage: 1,
      userGroupDetail: {
        name: "",
        customConfig: "",
        logoData: null
      }
    };
  },

  methods: {
    tableIndex(index) {
      return this.pageSize * (this.currentPage - 1) + index + 1;
    },
    handleSizeChange() {
      this.currentPage = 1;
      this.tableData = this.totalTableData.slice(0, this.pageSize);
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },
    handleCurrentPageChange(val) {
      this.currentPage = val;
      this.tableData = this.totalTableData.slice(
        (val - 1) * this.pageSize,
        val * this.pageSize
      );
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },

    // 获取logo数据
    getLogoData(userGroup) {
      if (!userGroup || !userGroup.customConfig) {
        return Promise.resolve("");
      }

      return new Promise(resolve => {
        let logoData = JSON.parse(userGroup.customConfig);
        let logoName = logoData.fileName;

        commonApi["downloadUserGroupImg"](logoName)
          .then(response => {
            resolve(response);
          })
          .catch(() => {
            resolve("");
          });
      });
    },

    refresh() {
      let vm = this;
      vm.getLogoData(vm.userGroupData_in).then(logoData => {
        if (vm.userGroupData_in) {
          vm.userGroupDetail.name = vm.userGroupData_in.name;
          vm.userGroupDetail.customConfig = vm.userGroupData_in.customConfig;
        } else {
          vm.userGroupDetail.name = "";
          vm.userGroupDetail.customConfig = "";
        }

        vm.userGroupDetail.logoData = logoData;
      });
    },

    // 删除用户组
    deleteUserGroup() {
      this.$emit("deleteUserGroup_out", this.userGroupData_in);
    },

    // 编辑用户组
    editUserGroup() {
      this.$emit("editUserGroup_out", this.userGroupData_in);
    }
  }
};
</script>
<style lang="scss" scoped>
.title {
  overflow: hidden;
  position: relative;
}

.logo {
  width: 142px;
  height: 142px;
  border: 1px solid;
  @include border_color(B1);
  border-radius: 4px;
  box-sizing: border-box;
}

.logo.none {
  line-height: 142px;
  text-align: center;
}
.box-preview {
  width: 360px;
  @include background_color(BG1);
}
.box-picture {
  width: calc(100% - 360px);
  @include background_color(BG1);
  @include margin_left(J2);
  min-width: 620px;
}
.flex {
  display: flex;
  justify-content: center;
}
.box-leflex {
  @include font_size(J2);
  @include font_color(T5);
}
.box-top {
  @include background_color(BG1);
}
.box-nopicture {
  @include background_color(BG1);
}
.box-el {
  height: calc(100% - 75px);
  overflow: auto;
}
.box-userlist {
  @include font_size(J2);
  @include font_color(T5);
}
.pt100 {
  padding-top: 100px;
}
</style>
