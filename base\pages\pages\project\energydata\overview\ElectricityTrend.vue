<template>
  <div class="page">
    <div class="clearfix mbJ2">
      <div class="clearfix plJ1 fr">
        <ElCheckboxGroup
          class="fl mrJ1 lh32"
          v-if="dataConfig.showType"
          v-model="ElCheckboxGroup_1.value"
          v-bind="ElCheckboxGroup_1"
          v-on="ElCheckboxGroup_1.event"
        >
          <ElCheckbox
            class="fl"
            v-for="item in ElCheckboxList_1.options_in"
            :key="item[ElCheckboxList_1.key]"
            :label="item[ElCheckboxList_1.label]"
            :disabled="item[ElCheckboxList_1.disabled]"
          >
            {{ item[ElCheckboxList_1.text] }}
          </ElCheckbox>
        </ElCheckboxGroup>
        <!-- 向前查询按钮 -->
        <CetButton
          class="fl"
          v-bind="CetButton_prv"
          v-on="CetButton_prv.event"
        ></CetButton>
        <CustomElDatePicker
          class="fl mlJ mrJ"
          :prefix_in="$T('选择月份')"
          v-bind="CetDatePicker_time.config"
          v-model="CetDatePicker_time.val"
          :picker-options="pickerOptions"
          @change="CetDatePicker_time_queryTime_out"
        />
        <!-- 向后查询按钮 -->
        <CetButton
          class="fl mrJ1"
          :disable_in="backToTimeBtnDis1"
          v-bind="CetButton_next"
          v-on="CetButton_next.event"
        ></CetButton>
        <!-- 请填写组件含义按钮组件 -->
        <CetButton
          :class="{
            fl: !dataConfig.showType,
            fr: dataConfig.showType
          }"
          v-bind="CetButton_export"
          v-on="CetButton_export.event"
        ></CetButton>
      </div>
      <div class="clearfix">
        <span class="common-title-H3 lh32 pointer fl" @click="clickTitle">
          {{ dataConfig.title || "--" }}
        </span>
        <div class="card-head-label" v-if="dataConfig.subtitle">
          <span></span>
          {{ dataConfig.subtitle || "--" }}
        </div>
      </div>
    </div>
    <div style="flex: 1">
      <CetChart
        :inputData_in="CetChart_1.inputData_in"
        v-bind="CetChart_1.config"
      />
    </div>
  </div>
</template>
<script>
import common from "eem-utils/common";
import { httping } from "@omega/http";
export default {
  name: "ElectricityTrend",
  components: {},
  props: {
    inputData_in: {
      type: Array
    },
    //数据绑定配置
    dataConfig: {
      type: Object
    },
    //查询节点输入
    queryNode_in: {
      type: Object
    },
    customParams_in: {
      type: Object
    },
    //查询按钮状态输入，状态变化执行查询
    visibleTrigger_in: {
      type: Number
    },
    refreshTrigger_in: {
      type: Number
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      let vm = this;
      return vm.$store.state.rootNode;
    },
    // 实际对标考核下一时段按钮禁点状态
    backToTimeBtnDis1() {
      let actTime = null, //控件时间
        maxTime = null, //当前时间
        cycle = 3,
        time = this.CetDatePicker_time.val;
      if (cycle === 1) {
        actTime = this.$moment(time).startOf("day").valueOf();
        maxTime = this.$moment()
          .add(1, "d")
          .startOf("day")
          .subtract(0, "d")
          .valueOf();
      } else if (cycle === 3) {
        actTime = this.$moment(time).startOf("month").valueOf();
        maxTime = this.$moment().add(1, "M").startOf("month").valueOf();
      } else if (cycle === 5) {
        actTime = this.$moment(time).startOf("year").valueOf();
        maxTime = this.$moment().add(1, "y").startOf("year").valueOf();
      }
      return actTime >= maxTime;
    },
    projectId() {
      let vm = this;
      let projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          let storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },

  data(vm) {
    return {
      unit: "--",
      ElCheckboxGroup_1: {
        value: [],
        style: {
          // width:"300px"
        },
        event: {
          change: this.ElCheckboxGroup_1_change_out
        }
      },
      ElCheckboxList_1: {
        options_in: [
          {
            id: 1,
            text: $T("同比")
          },
          {
            id: 2,
            text: $T("环比")
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      // 请填写组件含义组件
      CetDatePicker_time: {
        disable_in: false, //禁用
        val: new Date(),
        config: {
          clearable: false,
          valueFormat: "yyyy-MM",
          type: "month", //date daterange
          format: "yyyy-MM",
          rangeSeparator: "-",
          size: "small",
          style: {
            width: "200px"
          }
        }
      },
      pickerOptions: {
        disabledDate(time) {
          const cycle = 3;
          if (cycle === 1) {
            return time.getTime() > vm.$moment().add(1, "d").valueOf();
          } else if (cycle === 3) {
            return time.getTime() > vm.$moment().add(1, "M").valueOf();
          } else if (cycle === 5) {
            return time.getTime() > vm.$moment().add(1, "y").valueOf();
          }
        }
      },
      CetChart_1: {
        inputData_in: null,
        config: {
          options: {
            tooltip: {
              trigger: "axis",
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
              }
            },
            grid: {
              left: "0",
              right: "16",
              bottom: "0",
              containLabel: true
            },
            legend: {
              data: []
            },
            dataset: {
              source: []
            },
            xAxis: { type: "category" },
            yAxis: {},
            series: []
          }
        }
      },
      queryTime: {
        startTime: this.$moment().startOf("M").valueOf(),
        endTime: this.$moment().endOf("M").valueOf() + 1,
        cycle: 14 //17年，14月，12日，20自定义
      },
      isLoading: false
    };
  },
  watch: {
    //查询触发
    visibleTrigger_in() {
      this.getChartData();
    },
    //刷新触发
    refreshTrigger_in() {
      this.getChartData();
    },
    dataConfig: {
      deep: true,
      handler: function (val, oldVal) {}
    },
    queryNode_in: {
      deep: true,
      handler: function (val, oldVal) {
        if (!(this._.get(val, "id") && this._.get(val, "modelLabel"))) return;
        if (
          val.id === this._.get(oldVal, "id") &&
          val.modelLabel === this._.get(oldVal, "modelLabel")
        ) {
          return;
        }
        this.getChartData();
      }
    },
    "CetDatePicker_time.val": {
      handler: function (val) {
        this.CetDatePicker_time_queryTime_out(val);
      },
      deep: true
    }
  },

  methods: {
    ElCheckboxGroup_1_change_out(val) {
      this.getChartData();
    },

    CetButton_prv_statusTrigger_out(val) {
      let date = this.$moment(this.CetDatePicker_time.val);
      this.CetDatePicker_time.val = date.subtract(1, "M")._d;
    },
    CetButton_next_statusTrigger_out(val) {
      let date = this.$moment(this.CetDatePicker_time.val);
      this.CetDatePicker_time.val = date.add(1, "M")._d;
    },
    //导出
    CetButton_export_statusTrigger_out(val) {
      let urlStr = "/eem-service/v1/energy/energydata/export/1",
        params = this.getParams(1);
      if (!params) {
        return;
      }
      common.downExcel(urlStr, params, this.token);
    },
    CetDatePicker_time_queryTime_out(val) {
      let date = this.$moment(val);
      let type = 14;
      let value = {
        startTime: date.startOf("M").valueOf(),
        endTime: date.endOf("M").valueOf() + 1,
        cycle: 14
      };

      this.queryTime = this._.cloneDeep(value);
      this.getChartData();
      this.$emit("date_out", date);
    },
    // time输出,方法名要带_out后缀
    CetDateSelect_time_date_out(val) {
      let value = {
        startTime: val[0],
        endTime: val[1] + 1,
        cycle: 14
      };

      this.queryTime = this._.cloneDeep(value);
      this.getChartData();
      this.$emit("date_out", value);
    },
    getChartData() {
      let _this = this,
        auth = _this.token; //身份验证
      let queryBody = this.getParams(1);
      if (!queryBody) {
        return;
      }
      this.CetChart_1.config.options = {};
      queryBody.projectId = this.projectId;
      let queryOption = {
        url: `/eem-service/v1/energy/energydata/time/tbhb`,
        method: "POST",
        data: queryBody
      };
      if (_this.isLoading) {
        return;
      }
      _this.isLoading = true;
      httping(queryOption).then(function (response) {
        _this.isLoading = false;
        if (response.code === 0 && response.data) {
          //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
          let data = _this._.get(response, ["data"], {});
          _this.unit = response.data.symbol;
          _this.filChartData(data);
        }
      });
    },
    filChartData(data) {
      let vm = this;
      this.CetChart_1.config.options = {
        tooltip: {
          trigger: "item",
          formatter: function (value) {
            if (value) {
              let text = `${vm
                .$moment(value.data.time)
                .format("YYYY-MM")}<br />`;
              text += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                value.color.colorStops
                  ? value.color.colorStops[1].color
                  : value.color
              };"></span>${value.data.product}: ${
                value.data[value.dimensionNames[value.encode.y[0]]] + vm.unit
              }<br />`;
              return text;
            }
          }
        },
        grid: {
          left: "16",
          right: "16",
          bottom: "8",
          containLabel: true
        },
        legend: {
          data: []
        },

        dataset: {
          source: []
        },
        xAxis: { type: "category" },
        yAxis: {},
        series: []
      };
      if (!data.currentdata || data.currentdata.length == 0) {
        return;
      }
      let _this = this,
        energytype = data.energytype,
        currentdata = data.currentdata || [],
        tbdata = data.tbdata || [],
        hbdata = data.hbdata || [];
      let xAxisData = [],
        yAxisData1 = [],
        yAxisData2 = [],
        yAxisData3 = [];
      let cycle = this.queryTime.cycle;
      let source = [];
      for (let i = 0, len = currentdata.length; i < len; i++) {
        let value1 = common.formatNumberWithPrecision(currentdata[i].value, 2),
          value2 = tbdata[i]
            ? common.formatNumberWithPrecision(tbdata[i].value, 2)
            : null,
          value3 = hbdata[i]
            ? common.formatNumberWithPrecision(hbdata[i].value, 2)
            : null;
        let sour = {
          time: currentdata[i].time,
          product: this.getAxixs(currentdata[i].time, cycle),
          yAxis1: value1,
          yAxis2: value2,
          yAxis3: value3
        };
        source.push(sour);
      }

      let series = [],
        legend = [],
        currentName = "",
        tbName = "",
        hbName = "";
      let CheckedArray = this.ElCheckboxGroup_1.value || [],
        len = CheckedArray.length,
        queryType;
      if (len === 0) {
        queryType = 0;
      } else if (len === 1) {
        queryType = CheckedArray[0];
      } else if (len === 2) {
        queryType = 3;
      }

      //1:同比；2：环比；3：同比环比
      if (queryType === 0) {
        currentName = this.getLegend(this.queryTime.startTime, cycle);
        if (cycle === 20) {
          let leng = currentdata.length;
          currentName =
            this.getLegend(this.queryTime.startTime, cycle) +
            "~" +
            this.getLegend(currentdata[leng - 1].time, cycle);
        }
        series = [
          {
            name: currentName,
            type: "bar",
            smooth: true,
            encode: { x: "product", y: "yAxis1" }
          }
        ];
        legend = [currentName];
        // source = this._.cloneDeep(source1);
      } else if (queryType === 1) {
        currentName = this.getLegend(this.queryTime.startTime, cycle);

        if (cycle === 12) {
          let xAxisTime = this.$moment(this.queryTime.startTime)
            .subtract(1, "M")
            .valueOf();
          tbName = this.getLegend(xAxisTime, cycle);
        } else if (cycle === 14) {
          let xAxisTime = this.$moment(this.queryTime.startTime)
            .subtract(1, "Y")
            .valueOf();
          tbName = this.getLegend(xAxisTime, cycle);
        } else if (cycle === 17) {
          let xAxisTime = this.$moment(this.queryTime.startTime)
            .subtract(1, "Y")
            .valueOf();
          tbName = this.getLegend(xAxisTime, cycle);
        }

        if (cycle === 20) {
          let leng = currentdata.length;
          currentName =
            this.getLegend(this.queryTime.startTime, cycle) +
            "~" +
            this.getLegend(currentdata[leng - 1].time, cycle);
        }
        series = [
          {
            name: currentName,
            type: "bar",
            smooth: true,
            encode: { x: "product", y: "yAxis1" }
          },
          {
            name: tbName,
            type: "line",
            smooth: true,
            encode: { x: "product", y: "yAxis2" }
          }
        ];
        legend = [currentName, tbName];
        // source = this._.cloneDeep(source2);
      } else if (queryType === 2) {
        currentName = this.getLegend(this.queryTime.startTime, cycle);
        if (cycle === 12) {
          let xAxisTime = this.$moment(this.queryTime.startTime)
            .subtract(1, "d")
            .valueOf();
          hbName = this.getLegend(xAxisTime, cycle);
        } else if (cycle === 14) {
          let xAxisTime = this.$moment(this.queryTime.startTime)
            .subtract(1, "M")
            .valueOf();
          hbName = this.getLegend(xAxisTime, cycle);
        } else if (cycle === 17) {
          let xAxisTime = this.$moment(this.queryTime.startTime)
            .subtract(1, "Y")
            .valueOf();
          hbName = this.getLegend(xAxisTime, cycle);
        }
        if (cycle === 20) {
          let leng = currentdata.length;
          currentName =
            this.getLegend(this.queryTime.startTime, cycle) +
            "~" +
            this.getLegend(currentdata[leng - 1].time, cycle);
        }
        series = [
          {
            name: currentName,
            type: "bar",
            smooth: true,
            encode: { x: "product", y: "yAxis1" }
          },
          {
            name: hbName,
            type: "line",
            smooth: true,
            encode: { x: "product", y: "yAxis3" }
          }
        ];
        legend = [currentName, hbName];
        // source = this._.cloneDeep(source3);
      } else if (queryType === 3) {
        currentName = this.getLegend(this.queryTime.startTime, cycle);
        if (cycle === 12) {
          let xAxisTime = this.$moment(this.queryTime.startTime)
            .subtract(1, "M")
            .valueOf();
          tbName = this.getLegend(xAxisTime, cycle);
        } else if (cycle === 14) {
          let xAxisTime = this.$moment(this.queryTime.startTime)
            .subtract(1, "Y")
            .valueOf();
          tbName = this.getLegend(xAxisTime, cycle);
        } else if (cycle === 17) {
          let xAxisTime = this.$moment(this.queryTime.startTime)
            .subtract(1, "Y")
            .valueOf();
          tbName = this.getLegend(xAxisTime, cycle);
        }

        if (cycle === 12) {
          let xAxisTime = this.$moment(this.queryTime.startTime)
            .subtract(1, "d")
            .valueOf();
          hbName = this.getLegend(xAxisTime, cycle);
        } else if (cycle === 14) {
          let xAxisTime = this.$moment(this.queryTime.startTime)
            .subtract(1, "M")
            .valueOf();
          hbName = this.getLegend(xAxisTime, cycle);
        } else if (cycle === 17) {
          let xAxisTime = this.$moment(this.queryTime.startTime)
            .subtract(1, "Y")
            .valueOf();
          hbName = this.getLegend(xAxisTime, cycle);
        }
        if (cycle === 20) {
          let leng = currentdata.length;
          currentName =
            this.getLegend(this.queryTime.startTime, cycle) +
            "~" +
            this.getLegend(currentdata[leng - 1].time, cycle);
        }
        series = [
          {
            name: currentName,
            type: "bar",
            smooth: true,
            encode: { x: "product", y: "yAxis1" }
          },
          {
            name: tbName,
            type: "line",
            smooth: true,
            encode: { x: "product", y: "yAxis2" }
          },
          {
            name: hbName,
            type: "line",
            smooth: true,
            encode: { x: "product", y: "yAxis3" }
          }
        ];
        legend = [currentName, tbName, hbName];
        // source = this._.cloneDeep(source4);
      }

      let dataset = {
        source: source
      };
      this.$nextTick(function () {
        _this.CetChart_1.config.options.yAxis.name = this.unit;
        _this.CetChart_1.config.options.dataset = dataset;
        _this.CetChart_1.config.options.series = series;
        _this.CetChart_1.config.options.legend.data = legend;
      });
    },
    getParams(type = 1) {
      if (!this.queryNode_in) {
        return null;
      }
      let queryTime = this._.cloneDeep(this.queryTime),
        startTime = queryTime.startTime,
        endTime = queryTime.endTime,
        cycle = queryTime.cycle;

      if (type === 1) {
        let CheckedArray = this.ElCheckboxGroup_1.value || [],
          len = CheckedArray.length,
          queryType;
        if (len === 0) {
          queryType = 0;
        } else if (len === 1) {
          queryType = CheckedArray[0];
        } else if (len === 2) {
          queryType = 3;
        }
        let params = {
          cycle: cycle,
          endTime: endTime,
          energyType: 2,
          node: [
            {
              modelLabel: this.queryNode_in.modelLabel,
              nodes: [
                {
                  id: this.queryNode_in.id,
                  name: this.queryNode_in.name
                }
              ]
            }
          ],
          queryType: queryType,
          startTime: startTime
        };
        return params;
      }
      return {};
    },
    getLegend(date, type) {
      date = new Date(date);
      let y = date.getFullYear(),
        m = date.getMonth() + 1,
        d = date.getDate();
      if (m < 10) {
        m = "0" + m;
      }
      if (d < 10) {
        d = "0" + d;
      }
      if (type === 12) {
        return y + "-" + m + "-" + d;
      } else if (type === 14) {
        return y + "-" + m;
      } else if (type === 17) {
        return y;
      } else {
        return y + "-" + m + "-" + d;
      }
    },
    getAxixs(date, type, customIndex) {
      date = new Date(date);
      let y = date.getFullYear(),
        M = date.getMonth() + 1,
        d = date.getDate(),
        h = date.getHours(),
        m = date.getMinutes();
      if (M < 10) {
        M = "0" + M;
      }
      if (d < 10) {
        d = "0" + d;
      }
      if (h < 10) {
        h = "0" + h;
      }
      if (m < 10) {
        m = "0" + m;
      }
      if (type === 12) {
        return h + ":" + m;
      } else if (type === 14) {
        return d + "";
      } else if (type === 17) {
        return M + "";
      } else if (type === 20) {
        if (customIndex === 1) {
          return h + ":" + m;
        } else if (customIndex === 2) {
          return d + "";
        } else if (customIndex === 3) {
          return M + "";
        } else {
          return h + ":" + m;
        }
      } else {
        return y + "-" + M + "-" + d;
      }
    },
    //点击标题调转
    clickTitle() {
      this.$emit("clickChart", "用能查询");
    }
  },

  created: function () {},
  mounted: function () {
    this.getChartData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}
.card-head-label {
  line-height: 32px;
  @include font_size(H2);
  float: left;
  span {
    display: block;
    float: left;
    width: 32px;
    height: 32px;
    border-radius: 32px;
    @include margin_right(J1);
    background: url(./assets/u7103.png) no-repeat center;
  }
}

.card-body {
  height: 100%;
  overflow-y: auto;
}
.card-style {
  height: 100%;
  border: 1px solid rgb(235, 235, 235);
  padding: 10px;
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0px 5px 16px rgba(0, 0, 0, 0.149019607843137);
}

.card-cont {
  height: 80px;
  background: #f9f9f9;
  span {
    display: block;
    padding-left: 24px;
  }
}
</style>
