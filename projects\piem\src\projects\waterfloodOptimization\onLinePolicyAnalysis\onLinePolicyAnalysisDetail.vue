<template>
  <customDrawer v-bind="modelCreateDetail">
    <template v-slot:headerRight>
      <el-button @click="onEdit">编辑</el-button>
    </template>
    <div class="w-full h-full eem-cont-c1">
      <div class="w-full">
        <div class="title">基本信息</div>
        <div class="gap-[24px] grid grid-cols-2">
          <div v-for="(i, ix) in data" :key="ix">
            <div class="text-T3 mb-[4px]">{{ i.label }}</div>
            <div class="ov" :title="i.value">{{ i.value }}</div>
          </div>
        </div>
      </div>
      <div class="w-full mt-[24px]">
        <div class="title">策略评价</div>
        <div class="flex">
          <el-tag
            :type="
              newDetailObj.valid
                ? 'success'
                : newDetailObj.valid === false
                ? 'danger'
                : newDetailObj.valid === null
                ? 'gray'
                : ''
            "
          >
            {{
              newDetailObj.valid
                ? "有效"
                : newDetailObj.valid === false
                ? "无效"
                : newDetailObj.valid === null
                ? "忽略"
                : "--"
            }}
          </el-tag>
          <div class="ml-[24px]">
            {{ newDetailObj.strategyevaluation || "--" }}
          </div>
        </div>
      </div>
    </div>

    <StrategyEvaluation
      v-bind="strategyEvaluation"
      @updateTable="onUpdateTable"
    />
  </customDrawer>
</template>

<script>
import customDrawer from "@/components/customElDrawer";
import StrategyEvaluation from "./strategyEvaluation.vue";
export default {
  name: "onLinePolicyAnalysisDetail",
  components: { customDrawer, StrategyEvaluation },
  computed: {},
  props: {
    openTrigger_in: Number,
    inputData_in: Object
  },
  data() {
    return {
      modelCreateDetail: {
        title: "策略详情",
        size: "480px",
        openTrigger_in: +new Date(),
        closeTrigger_in: +new Date()
      },
      strategyEvaluation: {
        openTrigger_in: +new Date(),
        inputData_in: {}
      },
      data: [
        { label: "发生时间", value: "", id: "eventtime" },
        { label: "最优泵组合", value: "", id: "最优泵组合" },
        { label: "负荷率分配", value: "", id: "负荷率分配" },
        { label: "预计单耗下降", value: "", id: "预计单耗" }
      ],
      newDetailObj: {}
    };
  },
  watch: {
    openTrigger_in() {
      this.modelCreateDetail.openTrigger_in = +new Date();
    },
    inputData_in: {
      deep: true,
      handler(val) {
        this.newDetailObj = _.cloneDeep(val);
        const str = val?.strategydescription.replace(/】，/g, () => "】、");
        str?.split("、")?.forEach(i => {
          this.data?.map(it => {
            if (it.id == "eventtime")
              it.value = this.inputData_in.eventtime
                ? this.$moment(this.inputData_in.eventtime).format(
                    "YYYY-MM-DD HH:mm:ss"
                  )
                : "--";
            if (i.includes(it.id)) {
              const arr = i?.split("：");
              it.value = arr[1]?.replace(/【|】/g, () => "") || "--";
            }
          });
        });
      }
    }
  },
  methods: {
    onEdit() {
      this.strategyEvaluation.openTrigger_in = +new Date();
      this.strategyEvaluation.inputData_in = _.cloneDeep(this.inputData_in);
    },
    onUpdateTable(val) {
      this.newDetailObj = _.cloneDeep(val);
      this.$emit("updateTable");
    }
  }
};
</script>
<style lang="scss" scoped>
.title {
  @apply mb-[24px] font-semibold text-T2;
}

.ov {
  @apply overflow-hidden text-T2 text-ellipsis whitespace-nowrap;
}
.el-tag.el-tag--gray {
  @include font_color(T3, !important);
  border: none;
  background-color: rgba(152, 152, 152, 0.1);
}
</style>
