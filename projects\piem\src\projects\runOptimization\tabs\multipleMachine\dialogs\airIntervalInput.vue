<template>
  <el-popover
    placement="bottom"
    width="350"
    trigger="manual"
    v-model="showPopover"
    class="mlJ3"
  >
    <div class="mbJ3">
      <el-radio-group v-model="airInterval.defaultInterval" style="width: 100%">
        <el-radio :label="0">
          {{
            $T(
              "默认区间{0}m³-{1}m³",
              airInterval.intervalMinValue,
              airInterval.intervalMaxValue
            )
          }}
        </el-radio>
        <div class="card mtJ3">
          <el-radio :label="1" class="mbJ3">
            {{ $T("录入区间") }}
          </el-radio>
          <div class="mbJ1">{{ $T("单机运行区间") }}</div>
          <div class="flex row-center">
            <div class="input-number">
              <el-input-number
                :placeholder="$T('请输入')"
                :min="0"
                :precision="2"
                :controls="false"
                style="width: 150px"
                v-model="airInterval.singleIntervalMinValue"
              ></el-input-number>
              <div class="input-number-suffix">m³</div>
            </div>
            <span class="mlJ1 mrJ1">-</span>
            <div class="input-number">
              <el-input-number
                :placeholder="$T('请输入')"
                :min="0"
                :precision="2"
                :controls="false"
                style="width: 150px"
                v-model="airInterval.singleIntervalMaxValue"
              ></el-input-number>
              <div class="input-number-suffix">m³</div>
            </div>
          </div>
          <div class="mbJ1 mtJ1">{{ $T("双机运行区间") }}</div>
          <div class="flex row-center">
            <div class="input-number">
              <el-input-number
                :placeholder="$T('请输入')"
                :min="0"
                :precision="2"
                :controls="false"
                style="width: 150px"
                v-model="airInterval.twoIntervalMinValue"
              ></el-input-number>
              <div class="input-number-suffix">m³</div>
            </div>
            <span class="mlJ1 mrJ1">-</span>
            <div class="input-number">
              <el-input-number
                :placeholder="$T('请输入')"
                :min="0"
                :precision="2"
                :controls="false"
                style="width: 150px"
                v-model="airInterval.twoIntervalMaxValue"
              ></el-input-number>
              <div class="input-number-suffix">m³</div>
            </div>
          </div>
          <div class="mbJ1 mtJ1">{{ $T("三机运行区间") }}</div>
          <div class="flex row-center">
            <div class="input-number">
              <el-input-number
                :placeholder="$T('请输入')"
                :min="0"
                :precision="2"
                :controls="false"
                style="width: 150px"
                v-model="airInterval.threeIntervalMinValue"
              ></el-input-number>
              <div class="input-number-suffix">m³</div>
            </div>
            <span class="mlJ1 mrJ1">-</span>
            <div class="input-number">
              <el-input-number
                :placeholder="$T('请输入')"
                :min="0"
                :precision="2"
                :controls="false"
                style="width: 150px"
                v-model="airInterval.threeIntervalMaxValue"
              ></el-input-number>
              <div class="input-number-suffix">m³</div>
            </div>
          </div>
        </div>
      </el-radio-group>
    </div>
    <div class="fr">
      <el-button @click="handleCancel">
        {{ $T("取消") }}
      </el-button>
      <el-button type="primary" @click="handleConfirm">
        {{ $T("确认") }}
      </el-button>
    </div>
    <div slot="reference">
      <slot name="reference"></slot>
    </div>
  </el-popover>
</template>

<script>
import customApi from "@/api/custom.js";

export default {
  name: "airIntervalInput",
  components: {},
  props: {
    inputData_in: {
      type: Object
    },
    openTrigger_in: {
      type: Number
    },
    currentNode: {
      type: Object
    }
  },
  data() {
    return {
      airInterval: {},
      showPopover: false
    };
  },
  watch: {
    openTrigger_in(val) {
      this.airInterval = _.cloneDeep(this.inputData_in);
      this.showPopover = true;
    }
  },
  methods: {
    /**
     * 取消气量区间弹窗
     */
    handleCancel() {
      this.showPopover = false;
    },

    /**
     * 确认气量区间弹窗
     */
    handleConfirm() {
      const keys = [
        "threeIntervalMaxValue",
        "threeIntervalMinValue",
        "twoIntervalMaxValue",
        "twoIntervalMinValue",
        "singleIntervalMaxValue",
        "singleIntervalMinValue"
      ];
      if (this.airInterval.defaultInterval === 1) {
        let flag = false;
        keys.forEach(item => {
          if (!_.isNumber(this.airInterval[item])) {
            flag = true;
          }
        });
        if (flag) return this.$message.warning("录入区间不可为空");

        console.log(
          this.airInterval.threeIntervalMaxValue,
          this.airInterval.threeIntervalMinValue,
          this.airInterval.twoIntervalMaxValue,
          this.airInterval.twoIntervalMinValue,
          this.airInterval.singleIntervalMaxValue,
          this.airInterval.singleIntervalMinValue
        );
        if (
          !(
            this.airInterval.threeIntervalMaxValue >
              this.airInterval.threeIntervalMinValue &&
            this.airInterval.threeIntervalMinValue >=
              this.airInterval.twoIntervalMaxValue &&
            this.airInterval.twoIntervalMaxValue >
              this.airInterval.twoIntervalMinValue &&
            this.airInterval.twoIntervalMinValue >=
              this.airInterval.singleIntervalMaxValue &&
            this.airInterval.singleIntervalMaxValue >
              this.airInterval.singleIntervalMinValue
          )
        ) {
          return this.$message.warning("请输入正确的区间");
        }
      }
      const params = {
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel,
        ...this.airInterval
      };
      customApi.setAirInterval(params).then(res => {
        if (res.code === 0) {
          this.$message.success($T("设置成功"));
          this.showPopover = false;
          this.$emit("finishTrigger_out");
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.card {
  width: 100%;
  height: 250px;
  border: 1px solid;
  @include border_color(B1);
  border-radius: 8px;
  padding: 16px;
  box-sizing: border-box;
}
.row-center {
  align-items: center;
}
.input-number {
  display: flex;
  position: relative;
  &-suffix {
    position: absolute;
    right: 1px;
    top: 1px;
    width: 40px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 0px 4px 4px 0px;
    @include background_color(BG12);
  }
  :deep(.el-input__inner) {
    padding-right: 48px;
  }
}
</style>
