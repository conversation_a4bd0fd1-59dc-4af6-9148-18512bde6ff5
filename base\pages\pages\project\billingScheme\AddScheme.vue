<template>
  <!-- 1弹窗组件 -->
  <CetDialog
    v-bind="CetDialog_1"
    v-on="CetDialog_1.event"
    class="el_dialog small"
  >
    <div class="mbJ1 mlJ1 el_title">容量计费</div>
    <CetForm
      :data.sync="CetForm_1.data"
      v-bind="CetForm_1"
      v-on="CetForm_1.event"
      class="eem-cont"
      label-position="top"
    >
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="方案名称" prop="name">
            <ElInput
              v-model="CetForm_1.data.name"
              v-bind="ElInput_1"
              v-on="ElInput_1.event"
            ></ElInput>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="费率类型" prop>
            <ElInput v-model="ElInput_3.value" v-bind="ElInput_3"></ElInput>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="费率值（元/kVA）" prop="feerate">
            <ElInputNumber
              v-model="CetForm_1.data.feerate"
              v-bind="ElInputNumber_1"
              v-on="ElInputNumber_1.event"
            ></ElInputNumber>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生效时间" prop="effectivedate">
            <el-date-picker
              style="width: 100%"
              v-model="CetForm_1.data.effectivedate"
              v-bind="CetDatePicker_1.config"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
    </CetForm>
    <div class="mtJ1 mbJ1 mlJ1 el_title">需量计费</div>
    <CetForm
      :data.sync="CetForm_2.data"
      v-bind="CetForm_2"
      v-on="CetForm_2.event"
      class="eem-cont"
      label-position="top"
    >
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="方案名称" prop="name">
            <ElInput
              v-model="CetForm_2.data.name"
              v-bind="ElInput_2"
              v-on="ElInput_2.event"
            ></ElInput>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="费率类型" prop>
            <ElInput v-model="ElInput_4.value" v-bind="ElInput_4"></ElInput>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="费率值（元/kW）" prop="feerate">
            <ElInputNumber
              v-model="CetForm_2.data.feerate"
              v-bind="ElInputNumber_4"
              v-on="ElInputNumber_4.event"
            ></ElInputNumber>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上限比例（%）" prop="highLimit">
            <ElInputNumber
              v-model="CetForm_2.data.highLimit"
              v-bind="ElInputNumber_2"
              v-on="ElInputNumber_2.event"
            ></ElInputNumber>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="tooltipInfo">
        偏差费用=（实际最大需量-申报需量*上限比例）*需量电价*惩罚系数
      </div>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="惩罚系数" prop="punishRate">
            <ElInputNumber
              v-model="CetForm_2.data.punishRate"
              v-bind="ElInputNumber_3"
              v-on="ElInputNumber_3.event"
            ></ElInputNumber>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生效时间" prop="effectivedate">
            <el-date-picker
              style="width: 100%"
              v-model="CetForm_2.data.effectivedate"
              v-bind="CetDatePicker_2.config"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label prop="calculatedeviation">
        <ElCheckbox
          style="display: inline-block"
          v-model="CetForm_2.data.calculatedeviation"
          v-bind="ElCheckbox_1"
          v-on="ElCheckbox_1.event"
        >
          {{ ElCheckbox_1.text }}
        </ElCheckbox>
        <el-popover placement="top-start" trigger="hover">
          <div>
            <div
              v-text="
                '实际最大需量<申报需量*上限比例时，是否计算负偏差，若不计算负偏差费用为0.'
              "
            ></div>
          </div>
          <div slot="reference" class="el-icon-question"></div>
        </el-popover>
      </el-form-item>
    </CetForm>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import common from "eem-utils/common";
import { httping } from "@omega/http";
export default {
  name: "AddScheme",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: "新建费率方案",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "确定",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "关闭",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "150px",
        rules: {
          name: [
            {
              required: true,
              message: "请输入方案名称",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          feerate: [
            {
              required: true,
              message: "请输入费率值",
              trigger: ["blur", "change"]
            }
          ],
          effectivedate: [
            {
              required: true,
              message: "请选择生效时间",
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      CetForm_2: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "150px",
        rules: {
          name: [
            {
              required: true,
              message: "请输入方案名称",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          feerate: [
            {
              required: true,
              message: "请输入费率值",
              trigger: ["blur", "change"]
            }
          ],
          effectivedate: [
            {
              required: true,
              message: "请选择生效时间",
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_2_saveData_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        disabled: false,
        event: {}
      },
      ElInput_2: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        disabled: false,
        event: {}
      },
      ElInput_3: {
        value: " 容量计费",
        style: {
          width: "100%"
        },
        disabled: true
      },
      ElInput_4: {
        value: "需量计费",
        style: {
          width: "100%"
        },
        disabled: true
      },
      ElInputNumber_1: {
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        min: 0,
        max: 999999999999.99999,
        step: 2,
        precision: 5,
        controlsPosition: "",
        disabled: false,
        event: {}
      },
      ElInputNumber_4: {
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        min: 0,
        max: 999999999999.99999,
        step: 2,
        precision: 5,
        controlsPosition: "",
        disabled: false,
        event: {}
      },

      ElInputNumber_2: {
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        min: 0,
        max: 999999999999.99,
        step: 2,
        precision: 2,
        controlsPosition: "",
        disabled: false,
        event: {}
      },
      ElInputNumber_3: {
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        min: 0,
        max: 999999999999.99,
        step: 2,
        precision: 2,
        controlsPosition: "",
        disabled: false,
        event: {}
      },
      ElCheckbox_1: {
        value: false,
        text: "计算负偏差",
        disabled: false,
        event: {}
      },
      CetDatePicker_1: {
        disable_in: false,
        val: this.$moment().add(1, "month").startOf("month").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "month",
          rangeSeparator: "-",
          style: {
            display: "inline-block"
          },
          pickerOptions: common.pickerOptions_laterThanYesterd11
        }
      },
      CetDatePicker_2: {
        disable_in: false,
        val: this.$moment().add(1, "month").startOf("month").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "month",
          rangeSeparator: "-",
          style: {
            display: "inline-block"
          },
          pickerOptions: common.pickerOptions_laterThanYesterd11
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.CetForm_1.resetTrigger_in = new Date().getTime();
      this.CetForm_2.resetTrigger_in = new Date().getTime();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    inputData_in(val) {
      this.CetForm_1.data = {};
      this.CetForm_2.data = {};
      this.ElInput_1.disabled = false;
      this.ElInputNumber_1.disabled = false;
      this.CetDatePicker_1.disable_in = false;
      this.ElInput_2.disabled = false;
      this.ElInputNumber_4.disabled = false;
      this.ElInputNumber_2.disabled = false;
      this.ElInputNumber_3.disabled = false;
      this.ElCheckbox_1.disabled = false;
      this.CetDatePicker_2.disable_in = false;
      if (val.id) {
        if (val.chargyWay == 1) {
          // 置灰容量计费
          this.CetForm_1.data = val;
          this.ElInput_1.disabled = true;
          this.ElInputNumber_1.disabled = true;
          this.CetDatePicker_1.disable_in = true;
        } else if (val.chargyWay == 2) {
          // 置灰需量计费
          this.CetForm_2.data = val;
          this.ElInput_2.disabled = true;
          this.ElInputNumber_4.disabled = true;
          this.ElInputNumber_2.disabled = true;
          this.ElInputNumber_3.disabled = true;
          this.ElCheckbox_1.disabled = true;
          this.CetDatePicker_2.disable_in = true;
        }
      }
    }
  },

  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.cetForm_obj = {
        ruleObj1: false,
        ruleObj2: false
      };
      this.CetForm_2.localSaveTrigger_in = this._.cloneDeep(val);
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_saveData_out() {
      this.cetForm_obj.ruleObj1 = true;
      if (!this.inputData_in) {
        this.CetForm_1_validateFlag = true;
        this.addScheme();
        return;
      }
      if (!this.cetForm_obj.ruleObj2) {
        // this.cetForm_obj.ruleObj1 = false;
        return;
      }
      this.addScheme1();
      this.addScheme2();
    },
    CetForm_2_saveData_out() {
      this.cetForm_obj.ruleObj2 = true;
      if (!this.inputData_in) {
        this.CetForm_2_validateFlag = true;
        this.addScheme();
        return;
      }
      if (!this.cetForm_obj.ruleObj1) {
        // this.cetForm_obj.ruleObj2 = false;
        return;
      }
      this.addScheme1();
      this.addScheme2();
    },
    addScheme() {
      if (this.CetForm_1_validateFlag && this.CetForm_2_validateFlag) {
        if (this.CetForm_1.data.name == this.CetForm_2.data.name) {
          this.$message({
            message: "方案名重名",
            type: "warning"
          });
        } else {
          this.addScheme1(true);
          this.addScheme2();
        }
      }
    },
    addScheme1(noMessage) {
      var data = {
        chargeway: 1,
        effectivedate:
          this.CetForm_1.data.effectivedate &&
          this.$moment(this.CetForm_1.data.effectivedate)
            .startOf("month")
            .valueOf(),
        fee: this.CetForm_1.data.feerate,
        name: this.CetForm_1.data.name,
        recordId: 0,
        schemeId: 0,
        modelId: this.projectId,
        modelLabel: "project"
      };
      httping({
        url: "/eem-service/v1/demand/maintain/saveVolumeFeeScheme",
        method: "POST",
        data
      }).then(response => {
        if (response.code == 0) {
          if (!noMessage) {
            this.$message({
              message: "保存成功",
              type: "success"
            });
            this.CetDialog_1.closeTrigger_in = this._.cloneDeep(
              new Date().getTime()
            );
            this.$emit("finishTrigger_out", new Date().getTime());
          }
        }
      });
    },
    addScheme2() {
      var data = {
        calculatedeviation: this.CetForm_2.data.calculatedeviation || false,
        chargeway: 2,
        effectivedate:
          this.CetForm_2.data.effectivedate &&
          this.$moment(this.CetForm_2.data.effectivedate)
            .startOf("month")
            .valueOf(),
        feeRate: this.CetForm_2.data.feerate,
        highLimit: this.CetForm_2.data.highLimit,
        name: this.CetForm_2.data.name,
        punishRate: this.CetForm_2.data.punishRate,
        recordId: 0,
        schemeId: 0,
        modelId: this.projectId,
        modelLabel: "project"
      };
      httping({
        url: "/eem-service/v1/demand/maintain/saveDemandFeeScheme",
        method: "POST",
        data
      }).then(response => {
        if (response.code == 0) {
          this.$message({
            message: "保存成功",
            type: "success"
          });
          this.CetDialog_1.closeTrigger_in = this._.cloneDeep(
            new Date().getTime()
          );
          this.$emit("finishTrigger_out", new Date().getTime());
        }
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.tooltipInfo {
  color: #999999;
  font-size: 13px;
  position: relative;
  top: -5px;
}
:deep(.el-dialog) {
  margin-top: 10vh !important;
}
.el_dialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
    @include border-radius(C1);
  }
}
.el_title {
  font-size: 14px;
  font-weight: bold;
}
</style>
