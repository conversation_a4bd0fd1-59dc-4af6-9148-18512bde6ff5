<template>
  <div class="page" style="height: 100%">
    <el-aside
      class="nodeBox mainBox leftTree"
      :class="{ 'aside-collapsed': collapsed }"
      :width="asideWidth"
    >
      <div v-show="!collapsed" class="title" style="display: inline-block">
        选择管理层级
      </div>
      <CetButton
        v-show="!collapsed"
        class="fr"
        v-bind="CetButton_shareRate"
        v-on="CetButton_shareRate.event"
      ></CetButton>
      <div v-show="!collapsed" style="padding: 0px; height: calc(100% - 40px)">
        <CetTree
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          :searchText_in.sync="CetTree_1.searchText_in"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
        ></CetTree>
      </div>
      <div class="collapse-btn" @click="onCollapseBtnClick">
        {{ collapseText }}
      </div>
    </el-aside>
    <el-aside class="deviceBox mainBox">
      <el-header style="height: 40px; padding: 0">
        <span class="title">关联管网设备</span>
        <ElSelect
          v-model="ElSelect_energyType.value"
          v-bind="ElSelect_energyType"
          v-on="ElSelect_energyType.event"
        >
          <ElOption
            v-for="item in ElOption_energyType.options_in"
            :key="item[ElOption_energyType.key]"
            :label="item[ElOption_energyType.label]"
            :value="item[ElOption_energyType.value]"
            :disabled="item[ElOption_energyType.disabled]"
          ></ElOption>
        </ElSelect>
      </el-header>
      <el-main style="padding: 0px; height: calc(100% - 40px)">
        <CetTree
          class="cetTree2"
          ref="cetTree2"
          :selectNode.sync="CetTree_2.selectNode"
          :checkedNodes.sync="CetTree_2.checkedNodes"
          :searchText_in.sync="CetTree_2.searchText_in"
          v-bind="CetTree_2"
          v-on="CetTree_2.event"
        ></CetTree>
      </el-main>
    </el-aside>
    <el-main class="relatedBox mainBox">
      <div style="display: inline-block" class="title">已关联管网设备列表</div>
      <CetButton
        class="fr"
        v-bind="CetButton_save"
        v-on="CetButton_save.event"
      ></CetButton>
      <div style="padding: 0px; height: calc(100% - 40px)">
        <CetTable
          stripe
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <template v-for="item in Columns_1">
            <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
          </template>
          <el-table-column label="操作" header-align="center" width="80">
            <template slot-scope="scope">
              <el-button
                class="deleteBtn"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
              ></el-button>
            </template>
          </el-table-column>
        </CetTable>
      </div>
    </el-main>
    <shareRate
      :visibleTrigger_in="shareRate.visibleTrigger_in"
      :closeTrigger_in="shareRate.closeTrigger_in"
      :queryId_in="shareRate.queryId_in"
      :inputData_in="shareRate.inputData_in"
      :roomDeviceNodes="energySupplyToDataAll"
      :roomDeviceList="roomDeviceList"
    />
  </div>
</template>

<script>
import customApi from "@/api/custom";
import { httping } from "@omega/http";
import common from "eem-utils/common";
import shareRate from "./shareRate.vue";
import ELECTRICAL_DEVICE from "@/store/electricaldevice.js";
export default {
  name: "pipeRelation",
  components: { shareRate },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    asideWidth() {
      return this.collapsed ? "15px!important" : "500px";
    },
    collapseText() {
      return this.collapsed ? ">" : "<";
    }
  },
  data() {
    const roomDeviceList = [
      { filter: null, modelLabel: "linesegment", props: [] },
      { filter: null, modelLabel: "combinedstation", props: [] },
      { filter: null, modelLabel: "waterinjectionstation", props: [] },
      { filter: null, modelLabel: "oiltransferstation", props: [] }
    ];
    ELECTRICAL_DEVICE.forEach(item => {
      let obj = {
        filter: null,
        modelLabel: item.value,
        props: []
      };
      if (item.value === "linesegmentwithswitch" || item.value === "pipeline") {
        obj.depth = 1;
      }
      roomDeviceList.push(obj);
    });

    return {
      collapsed: false,
      // 配电室设备模型
      roomDeviceList: roomDeviceList,
      // 管网层级模型列表
      networkModelLabelList: ["linesegmentwithswitch", "pipeline"],
      // 已关联配电设备的列表
      energySupplyToDataAll: [],
      currentNode: null,
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children",
          isLeaf: "leaf"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        lazy: false,
        load: this.loadNode1,
        defaultExpandedKeys: [],
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      CetTree_2: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children",
          isLeaf: "leaf",
          disabled: "disabled"
        },
        highlightCurrent: true,
        showCheckbox: true,
        checkStrictly: false,
        event: { checkedNodes_out: this.CetTree_2_checkedNodes_out }
      },
      // energyType组件
      ElSelect_energyType: {
        value: "",
        style: {
          width: "200px",
          display: "inline-block",
          float: "right",
          "line-height": "40px"
        },
        event: {
          change: this.ElSelect_energyType_change_out
        }
      },
      // energyType组件
      ElOption_energyType: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        "highlight-current-row": false,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      // 1组件
      Columns_1: [
        {
          type: "index", // selection 勾选 index 序号
          label: "序号", //列名
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          width: "80" //绝对宽度
        },
        {
          prop: "name",
          label: "管网设备名称",
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true
        }
      ],
      shareRate: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      CetButton_shareRate: {
        visible_in: true,
        disable_in: true,
        title: "分摊系数",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_shareRate_statusTrigger_out
        }
      },
      CetButton_save: {
        visible_in: true,
        disable_in: true,
        title: "保存",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_save_statusTrigger_out
        }
      }
    };
  },
  watch: {
    energySupplyToDataAll: {
      handler(val) {
        if (val && val.length > 0) {
          this.CetButton_shareRate.disable_in = false;
        } else {
          this.CetButton_shareRate.disable_in = true;
        }
      }
    }
  },
  methods: {
    onCollapseBtnClick() {
      this.collapsed = !this.collapsed;
    },
    // 获取项目能源类型
    async getEnergyType() {
      const res = await customApi.getProjectEnergy(this.projectId);
      const resData = this._.get(res, "data", []) || [];
      this.ElOption_energyType.options_in = resData;
      if (resData.length) {
        resData.unshift({ name: "全部", energytype: 0 });
        this.ElSelect_energyType.value = resData[0].energytype;
      }
    },
    handleDelete(row) {
      const vm = this;
      vm.$confirm("确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let checkIndex = vm.CetTree_2.checkedNodes.findIndex(
            item => item.tree_id === row.tree_id
          );
          vm.CetTree_2.checkedNodes = vm.CetTree_2.checkedNodes.filter(
            (item, index) =>
              this.networkModelLabelList.includes(item.modelLabel) &&
              index !== checkIndex
          );
        })
        .catch(() => {
          vm.$message.info("已取消");
        });
    },
    // 获取管理层级节点树
    async getNodeTree() {
      const params = {
        subLayerConditions: [
          { modelLabel: "oilcompany" },
          { modelLabel: "oilproductionplant" },
          { modelLabel: "operationarea" },
          { modelLabel: "oilproductioncrew" },
          { modelLabel: "combinedstation" },
          { modelLabel: "platform" },
          { modelLabel: "oiltransferstation" },
          { modelLabel: "waterinjectionstation" },
          { modelLabel: "oilwell" },
          { modelLabel: "waterwell" },
          { modelLabel: "gasmine" },
          { modelLabel: "gasoperationarea" },
          { modelLabel: "gasplatform" },
          { modelLabel: "dehydratingstation" },
          { modelLabel: "gasgatheringstation" },
          { modelLabel: "purificationplant" },
          { modelLabel: "gaswell" },
          { modelLabel: "purificationcompany" },
          { modelLabel: "sectionarea" },
          { modelLabel: "building" },
          { modelLabel: "floor" },
          {
            modelLabel: "room",
            filter: {
              expressions: [{ limit: null, prop: "roomtype", operator: "EQ" }]
            }
          },
          { modelLabel: "manuequipment" },
          { modelLabel: "meteorologicalmonitor" },
          { modelLabel: "airconditioner" },
          { modelLabel: "coldwatermainengine" },
          { modelLabel: "windset" },
          { modelLabel: "coolingtower" },
          { modelLabel: "plateheatexchanger" },
          { modelLabel: "aircompressor" },
          { modelLabel: "colddryingmachine" },
          { modelLabel: "dryingmachine" },
          { modelLabel: "boiler" },
          { modelLabel: "civicpipe" },
          { modelLabel: "mechanicalminingmachine" },
          { modelLabel: "pump" },
          { modelLabel: "gascompressor" },
          { modelLabel: "heatingfurnace" },
          { modelLabel: "waterinjectionplatform" },
          { modelLabel: "waterinjectionwell" },
          { modelLabel: "oilcommonequipment" }
        ],
        treeReturnEnable: true,
        rootID: null,
        rootLabel: "project"
      };

      // const treeRes = await customApi.queryNodeTreeConfig({
      //   configtype: 1,
      //   functionkey: "395c29be-5279-ca83-4e9e-2cbbd1f5726f"
      // });
      // const queryData = treeRes.data[0]?.value
      //   ? JSON.parse(treeRes.data[0]?.value)
      //   : params;

      const res = await customApi.getNodeTree(params);
      let data = this._.get(res, "data", []) || [];
      this.CetTree_1.inputData_in = data;
      if (
        this.CetTree_1.inputData_in.length &&
        !this._.isEmpty(this.currentNode)
      ) {
        let flag = await this.nodeIsExist(this.currentNode.tree_id);
        if (flag) {
          this.CetTree_1_currentNode_out(this.currentNode);
        } else {
          this.CetTree_2.checkedNodes = [];
        }
      }
    },
    // 获取关联管网设备节点树
    async getDeviceTreeData() {
      const vm = this;
      const params = {
        rootID: 1,
        rootLabel: "project",
        subLayerConditions: [
          {
            modelLabel: "room",
            filter: {
              expressions: [
                {
                  limit: [1, 2, 5, 6, 7, 8, 9],
                  operator: "IN",
                  prop: "roomtype"
                }
              ]
            }
          },
          { modelLabel: "pipeline" },
          { modelLabel: "plateheatexchanger" },
          { modelLabel: "colddryingmachine" },
          { modelLabel: "dryingmachine" },
          { modelLabel: "boiler" },
          { modelLabel: "linesegment" },
          { modelLabel: "powertransformer" },
          { modelLabel: "capacitor" },
          { modelLabel: "linesegmentwithswitch" },
          { modelLabel: "busbarsection" },
          { modelLabel: "meteringcabinet" },
          { modelLabel: "busbarconnector" },
          { modelLabel: "ptcabinet" },
          { modelLabel: "powerdiscabinet" },
          { modelLabel: "switchcabinet" },
          { modelLabel: "arraycabinet" },
          { modelLabel: "ups" },
          { modelLabel: "battery" },
          { modelLabel: "hvdc" },
          { modelLabel: "interchanger" },
          { modelLabel: "dcpanel" },
          { modelLabel: "generator" },
          { modelLabel: "itcabinet" },
          { modelLabel: "ats" },
          { modelLabel: "avc" }
        ],
        treeReturnEnable: true
      };
      const treeRes = await customApi.queryNodeTreeConfig({
        configtype: 1,
        functionkey: "7444157d-d0ed-4fb3-b61a-e8a64a111143"
      });
      const queryData = treeRes.data[0]?.value
        ? JSON.parse(treeRes.data[0]?.value)
        : params;

      const res = await customApi.getNodeTree(queryData);
      const data = vm._.get(res, "data", []) || [];
      vm.setTreeCheckStatus(data, true);
      vm.CetTree_2.inputData_in = vm.filterTreeNode(data);
    },
    // 将管道类型pipetype不为1的过滤掉
    filterTreeNode(data) {
      data.forEach(item => {
        if (item.children?.length) {
          item.children = item.children.filter(
            item =>
              (item.modelLabel === "pipeline" && item.pipetype === 1) ||
              item.modelLabel !== "pipeline"
          );
          this.filterTreeNode(item.children);
        }
      });
      return data;
    },
    // 查询关系
    async querySupplyRelation_out(val) {
      if (!val || !val.id || !val.modelLabel) {
        return;
      }
      const me = this,
        url = `/eem-service/v1/connect/energySupplyTo/measuredBy?time=${+new Date()}`,
        data = [
          {
            id: val.id,
            modelLabel: val.modelLabel
          }
        ];
      common.requestData(
        { url, data },
        (data, res) => {
          if (res.code === 0) {
            var relationData = me._.get(res, "data", []) || [];
            // 过滤出对应的关联关系
            let checkedNodes = [];
            relationData.forEach(item => {
              if (me.networkModelLabelList.includes(item.objectlabel)) {
                checkedNodes.push({
                  dataId: item.id,
                  modelLabel: item.objectlabel,
                  name: item.objectName,
                  id: item.objectid,
                  tree_id: item.objectlabel + "_" + item.objectid,
                  measuredBys: item.measuredBys || []
                });
              }
            });
            me.energySupplyToDataAll = me._.cloneDeep(checkedNodes);
            me.CetTree_2.checkedNodes = me._.cloneDeep(checkedNodes);
          }
        },
        () => {
          me.energySupplyToDataAll = [];
          me.CetTree_2.checkedNodes = [];
        }
      );
    },
    // 设置节点树是否禁选
    setTreeCheckStatus(treeData, disable) {
      if (treeData.length === 0) {
        return;
      }
      treeData.forEach(node => {
        const flag =
          !this.networkModelLabelList.includes(node.modelLabel) &&
          this._.isNil(node.children);
        this.$set(node, "disabled", flag || disable);
        this.setTreeCheckStatus(this._.get(node, "children", []), disable);
      });
    },
    // 过滤掉modelLabelArr中的模型，并获取过滤之后的节点树节点
    filterTreeNodeByModelLabel(treeData, modelLabelArr, filterNodes = []) {
      treeData.forEach(node => {
        const flag = !modelLabelArr.includes(node.modelLabel);
        flag && filterNodes.push(node);
        const electricalNode = ["room", "linesegmentwithswitch"];
        const flag1 =
          !electricalNode.includes(node.modelLabel) &&
          (!this._.isNil(node.children) || node.energytype === 2);
        flag1 && filterNodes.push(node);
        const children = this._.get(node, "children", []) || [];
        this.filterTreeNodeByModelLabel(children, modelLabelArr, filterNodes);
      });
      return filterNodes;
    },
    // energytype获取过滤之后的节点树节点
    filterTreeNodeByEnergyType(treeData, energytype, filterNodes = []) {
      treeData.forEach(node => {
        const electricalNode = ["room", "linesegmentwithswitch"];
        const flag =
          !electricalNode.includes(node.modelLabel) &&
          (!this._.isNil(node.children) || node.energytype === energytype);
        flag && filterNodes.push(node);
        const children = this._.get(node, "children", []) || [];
        this.filterTreeNodeByEnergyType(children, energytype, filterNodes);
      });
      return filterNodes;
    },

    ElSelect_energyType_change_out(val) {
      let filterNodes = null;
      const treeData = this.CetTree_2.inputData_in;
      if (val === 2) {
        filterNodes = this.filterTreeNodeByModelLabel(treeData, ["pipeline"]);
      } else if (val) {
        filterNodes = this.filterTreeNodeByEnergyType(treeData, val);
      }
      this.CetTree_2.filterNodes_in = filterNodes;
    },
    CetTree_1_currentNode_out(val) {
      if (this._.isEmpty(val)) {
        this.CetButton_save.disable_in = true;
        this.setTreeCheckStatus(this.CetTree_2.inputData_in, true);
        return;
      }
      if (val.modelLabel === "pump") {
        // 泵只能关联电能设备
        this.ElSelect_energyType.value = 2;
        this.ElOption_energyType.options_in.forEach(item => {
          item.disabled = item.energytype !== 2;
        });
        this.ElSelect_energyType_change_out(2);
      } else {
        this.ElSelect_energyType.value = 0;
        this.ElOption_energyType.options_in.forEach(item => {
          item.disabled = false;
        });
        this.ElSelect_energyType_change_out(0);
      }
      this.setTreeCheckStatus(this.CetTree_2.inputData_in, false);
      this.CetButton_save.disable_in = false;
      this.currentNode = this._.cloneDeep(val);
      this.querySupplyRelation_out(val);
    },
    CetTree_2_checkedNodes_out(val) {
      if (val && val.length > 0) {
        this.CetTable_1.data = this._.cloneDeep(
          val.filter(item =>
            this.networkModelLabelList.includes(item.modelLabel)
          )
        );
      } else {
        this.CetTable_1.data = [];
      }
    },
    CetButton_shareRate_statusTrigger_out(val) {
      this.shareRate.inputData_in = this._.cloneDeep(this.currentNode);
      this.shareRate.visibleTrigger_in = this._.cloneDeep(val);
    },
    async CetButton_save_statusTrigger_out() {
      const me = this,
        param = [];
      if (!me.currentNode) {
        return;
      }
      await this.querySupplyRelation_out(me.currentNode);
      me.CetTree_2.checkedNodes.forEach(function (value) {
        if (me.networkModelLabelList.includes(value.modelLabel)) {
          param.push({
            createtime: new Date().getTime(),
            objectlabel: value.modelLabel,
            objectid: value.id,
            supplytoid: me.currentNode.id,
            supplytolabel: me.currentNode.modelLabel,
            modelLabel: "energysupplyto",
            starttime: 0
          });
        }
      });
      const deleteEnergySupply = [];

      me.energySupplyToDataAll.length > 0 &&
        me.energySupplyToDataAll.forEach(item => {
          if (param.findIndex(ite => ite.objectid === item.id) === -1) {
            deleteEnergySupply.push(item);
          }
        });
      // 删除关联设备
      if (deleteEnergySupply.length > 0) {
        let queryData = { idRange: [], modelLabel: "energysupplyto" };
        me.energySupplyToDataAll.forEach(energySupplyTo => {
          if (
            param.findIndex(
              newEnergySupplyTo =>
                energySupplyTo.id === newEnergySupplyTo.objectid
            ) === -1
          ) {
            queryData.idRange.push(energySupplyTo.dataId);
          }
        });
        await httping({
          url: "/eem-service/v1/connect/relationship",
          data: queryData,
          method: "DELETE"
        });
      }
      let addEnergySupply = param.filter(item => {
        return !me.energySupplyToDataAll.find(
          it => it.id === item.objectid && it.modelLabel === item.objectlabel
        );
      });
      // 修改关联设备
      if (addEnergySupply.length > 0) {
        await httping({
          url: "/eem-service/v1/connect/relationship",
          data: addEnergySupply,
          method: "PUT"
        }).then(res => {
          if (res.code === 0) {
            me.$message.success("保存成功");
          }
        });
      }
      this.querySupplyRelation_out(me.currentNode);
    },
    // 判断节点是否在节点树上
    async nodeIsExist(treeNodeId) {
      const [rootLabel, rootID] = treeNodeId.split("_");
      const data = {
        rootID,
        rootLabel,
        subLayerConditions: [],
        treeReturnEnable: true
      };
      const res = await customApi.getNodeTree(data);
      const resData = this._.get(res, "data", []) || [];
      return resData.length > 0;
    }
  },
  mounted() {
    this.getNodeTree();
    this.getDeviceTreeData();
    this.getEnergyType();
  },
  activated() {
    this.getNodeTree();
    this.getDeviceTreeData();
    this.getEnergyType();
  }
};
</script>
<style lang="scss" scoped>
.page {
  height: 100%;
  width: 100%;
  display: flex;
}
.mainBox {
  height: 100%;
  @include padding(J3);
  @include background_color(BG1);
  display: inline-block;
}
.nodeBox {
  width: 500px !important;
}
.deviceBox {
  @include margin_left(J3);
  width: 500px !important;
}
.relatedBox {
  @include margin_left(J3);
  width: calc(100% - 1041px);
}
.title {
  height: 40px;
  @include font_size(H1);
  font-weight: 600;
}
.cetTree2 :deep(.el-table__row--striped) {
  background: none;
}
.deleteBtn {
  width: 40px;
  height: 40px;
  border: none;
  background: none;
}
.deleteBtn :deep(.el-button:hover) {
  border: none;
  background: none;
}
.deleteBtn :deep(.el-icon-delete) {
  @include font_size(H1);
  @include font_color(T3);
}
.deleteBtn :deep(.el-icon-delete:hover) {
  @include font_color(Sta3);
}
.deleteBtn :deep(.is-disabled .el-icon-delete:hover) {
  @include font_color(T3);
}
.page :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  @include background_color(BG2, !important);
}
.aside-collapsed {
  padding: 0 !important;
}
.leftTree {
  position: relative;
  transition: width 0.3s;
}
.collapse-btn {
  cursor: pointer;
  position: absolute;
  top: 0;
  right: 0px;
  bottom: 0;
  margin: auto;
  width: 14px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  vertical-align: middle;
  @include background_color(ZS);
  @include font_color(T5);
  border-radius: 3px;
}
</style>
