<template>
  <div class="stationTable">
    <el-table :data="tableData" :border="false" :cell-style="cellStyle">
      <template v-for="(item, index) in columnList">
        <el-table-column :key="index" v-bind="item"></el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script>
import common from "eem-utils/common";
export default {
  name: "stationTable",
  components: {},
  props: {
    tableData: {
      type: Array
    },
    columnList: {
      type: Array
    }
  },
  data() {
    return {};
  },
  methods: {
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (column.property === "name" || column.property === "order") return;
      if (
        column.property === "savingRate" ||
        column.property === "savingCostRate"
      )
        return "color:#29B061;font-weight:bold;";
      else {
        return "color:#242424";
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.stationTable {
  :deep(.el-table__header-wrapper thead th) {
    background-color: #f8fafb !important;
    color: #798492;
  }
  :deep(.el-table__body-wrapper .el-table__row),
  :deep(.el-table__body-wrapper .el-table__body tr:hover > td),
  :deep(.el-table__empty-block) {
    background-color: #ffffff !important;
  }
  :deep(.el-table__empty-text) {
    color: #798492;
  }
  :deep(.el-table__cell) {
    border-color: #f3f5f7 !important;
    color: #798492;
  }
  :deep(.el-table::before) {
    background-color: #f3f5f7 !important;
  }
}
</style>
