<template>
  <div class="page eem-common">
    <div
      class="menu_group"
      style="padding: 0px; height: 0px; position: relative"
    >
      <el-radio-group
        size="mini"
        v-model="selectedMenu"
        style="position: absolute; z-index: 1; top: 0px"
        @change="menuChange"
      >
        <el-radio-button
          v-for="menu in menus"
          :label="menu.name"
          :key="menu.name"
        >
          {{ menu.label }}
        </el-radio-button>
      </el-radio-group>
    </div>
    <el-container class="fullheight" style="min-width: 1350px">
      <el-aside width="315px" class="eem-aside flex-column">
        <div class="mbJ1 common-title-H2">{{ $T("选择核算范围") }}</div>
        <div class="flex-auto">
          <CetGiantTree
            v-show="selectedMenu !== '2'"
            v-bind="CetGiantTree_1"
            v-on="CetGiantTree_1.event"
          ></CetGiantTree>
          <CetGiantTree
            v-show="selectedMenu === '2'"
            v-bind="CetGiantTree_2"
            v-on="CetGiantTree_2.event"
          ></CetGiantTree>
        </div>
      </el-aside>
      <el-container class="padding0 fullheight mlJ3">
        <EneryCost
          :selectedMenu="selectedMenu"
          :obj="obj1"
          :title="title"
          :energyList="energyList"
          :energySelect="energySelect"
          v-show="selectedMenu === '1'"
        />
        <UnitCost
          ref="UnitCost"
          :selectedMenu="selectedMenu"
          :obj="obj2"
          :energyList="energyList"
          :energySelect="energySelect"
          v-show="selectedMenu === '2'"
        />
      </el-container>
    </el-container>
  </div>
</template>
<script>
import EneryCost from "./EneryCost.vue";
import UnitCost from "./UnitCost.vue";
import TREE_PARAMS from "@/store/treeParams.js";
import { httping } from "@omega/http";
export default {
  name: "Costaccounting",
  components: {
    EneryCost,
    UnitCost
  },
  computed: {
    token() {
      return this.$store.state.token;
    }
  },

  data() {
    return {
      energyList: [],
      energySelect: "",
      title: "", // 选中节点的名称
      data: [],
      obj1: {},
      obj2: [],
      menus: [
        {
          label: $T("能源成本核算"),
          name: "1"
        },
        {
          label: $T("单位成本核算"),
          name: "2"
        }
      ],
      selectedMenu: "1",
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out //选中单行输出
        }
      },
      checkedNodes: [],
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_2_checkedNodes_out //勾选节点输出
        }
      }
    };
  },
  watch: {},

  methods: {
    menuChange(val) {
      if (val === "1") {
        this.obj1 = this._.cloneDeep(this.obj1);
      } else {
        this.obj2 = this._.cloneDeep(this.obj2);
      }
    },
    // 接口 获取节点树
    getTree() {
      const _this = this;
      _this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      _this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
      this.obj1 = {};
      this.obj2 = [];
      const obj = {
        rootID: sessionStorage.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.costanalysisTree,
        treeReturnEnable: true
      };
      const auth = _this.token; //身份验证
      httping({
        url: "/eem-service/v1/node/nodeTree",
        data: obj,
        method: "POST"
      }).then(res => {
        if (res.code == 0) {
          const list = res.data;
          this.handleTree(list, list[0].id);

          const obj = list[0];
          const projectId = obj.id;
          obj.projectId = projectId;
          obj.label = obj.name;
          this.CetGiantTree_1.inputData_in = this._.cloneDeep([obj]);
          this.CetGiantTree_2.inputData_in = this._.cloneDeep([obj]);
          this.CetGiantTree_1.selectNode = obj;
          this.CetGiantTree_1.checkedNodes = [];
          this.projectEnergy(projectId);
          this.setTreeLeaf(this.data);
        }
      });
    },
    // 设置节点禁用
    setTreeLeaf(nodesAll) {
      if (nodesAll && nodesAll.length > 0) {
        nodesAll.forEach(item => {
          if (item.childSelectState == 2) {
            this.$set(item, "disabled", true);
          } else if (item.childSelectState == 1) {
            this.$set(item, "disabled", false);
          }
          this.setTreeLeaf(this._.get(item, "children", []));
        });
      }
    },
    // 处理节点树
    handleTree(array, projectId) {
      const expanded = datas => {
        if (datas && datas.length > 0) {
          datas.forEach(e => {
            e.label = e.name;
            e.projectId = projectId;
            expanded(e.children);
          });
        }
      };
      expanded(array);
      return array;
    },
    // 接口 查询项目的能源类型
    projectEnergy(projectId) {
      httping({
        url: "eem-service/v1/project/projectEnergy?projectId=" + projectId,
        method: "GET"
      }).then(res => {
        if (res.code == 0) {
          if (res.data && res.data.length != 0) {
            let list = res.data || [];
            list.map(item => {
              item.text = item.name;
              item.id = item.energytype;
            });
            list = list.filter(item => {
              return ![13, 18, 22].includes(item.id);
            });
            list.push({
              text: $T("全部"),
              id: 13
            });
            this.energyList = list;
            this.energySelect = 13;
          }
        }
      });
    },
    CetGiantTree_1_currentNode_out(val) {
      if (this.selectedMenu === "2") {
        return;
      }
      // if (val.childSelectState == 2) {
      //   this.$message.warning("你没有此节点的全部权限！");
      //   return;
      // }
      this.obj1 = val;
      this.title = val.label;
    },
    CetGiantTree_2_checkedNodes_out(val) {
      const list = val;
      if (list.length > 6) {
        this.$alert($T("最多勾选6个节点"), "", {
          confirmButtonText: $T("确定"),
          callback: action => {}
        });
        this.CetGiantTree_2.checkedNodes = this._.cloneDeep(this.obj2);
        return;
      }
      this.obj2 = list;
    }
  },
  activated: function () {
    this.selectedMenu = this.menus[0].name;
    this.getTree();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.logo {
  width: 40px;
  height: 34px;
  margin: 0 10px 12px 14px;
}
.menu_group {
  text-align: center;
  padding: 5px 0;
}
.menu_group .el-radio-button__inner {
  padding: 7px 20px;
  // border: 1px solid #0066ff;
}
.silde {
  margin: 10% 0;
  height: 45% !important;
}
.filter-tree {
  height: 100%;
  overflow: auto;
}
</style>
