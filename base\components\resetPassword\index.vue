﻿<template>
  <!-- 重命名费率方案弹窗组件 -->
  <div>
    <CetDialog v-bind="CetDialog_add" v-on="CetDialog_add.event" class="min">
      <span slot="footer" class="dialog-footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_preserve"
          v-on="CetButton_preserve.event"
        ></CetButton>
      </span>
      <div class="eem-cont-c1">
        <CetForm
          :data.sync="CetForm_editfe32.data"
          v-bind="CetForm_editfe32"
          v-on="CetForm_editfe32.event"
        >
          <el-form-item :label="$T('新密码')" prop="password">
            <ElInput
              v-model="CetForm_editfe32.data.password"
              v-bind="ElInput_password"
              v-on="ElInput_password.event"
            ></ElInput>
          </el-form-item>
          <el-form-item :label="$T('密码确认')" prop="cetconfirmpassword">
            <ElInput
              v-model="CetForm_editfe32.data.cetconfirmpassword"
              v-bind="ElInput_cetconfirmpassword"
              v-on="ElInput_cetconfirmpassword.event"
            ></ElInput>
          </el-form-item>
          <el-form-item :label="$T('操作密码')" prop="oldpassword">
            <ElInput
              v-model="CetForm_editfe32.data.oldpassword"
              v-bind="ElInput_oldpassword"
              v-on="ElInput_oldpassword.event"
            ></ElInput>
          </el-form-item>
        </CetForm>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import common from "eem-utils/common";
import customApi from "@/api/custom";

export default {
  name: "ResetPassword",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },

  computed: {
    token() {
      return this.store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    userInfo() {
      var vm = this;
      return vm.$store.state.userInfo;
    }
  },

  data() {
    return {
      CetDialog_add: {
        title: $T("修改密码"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          open_out: this.CetDialog_add_open_out,
          close_out: this.CetDialog_add_close_out
        }
      },
      // 设置组件唯一识别字段组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // 设置组件唯一识别字段组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },

      // 设置组件唯一识别字段表单组件
      CetForm_editfe32: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        labelPosition: "top",
        rules: {
          oldpassword: [
            {
              required: true,
              message: $T("请输入操作密码"),
              trigger: ["blur", "change"]
            },
            common.check_strongPassword
          ],
          password: [
            {
              checkType: "password",
              validatorProp: "cetconfirmpassword",
              trigger: ["blur"]
            },
            {
              required: true,
              trigger: ["blur", "change"],
              message: $T("请输入密码")
            },
            common.check_strongPassword
          ],
          cetconfirmpassword: [
            {
              checkType: "checkPassword",
              relationProp: "password",
              trigger: "blur"
            },
            {
              required: true,
              trigger: ["blur", "change"],
              message: $T("请输入确认密码")
            },
            common.check_strongPassword
          ]
        },
        event: {
          currentData_out: this.CetForm_editfe32_currentData_out,
          saveData_out: this.CetForm_editfe32_saveData_out,
          finishData_out: this.CetForm_editfe32_finishData_out,
          finishTrigger_out: this.CetForm_editfe32_finishTrigger_out
        }
      },
      ElInput_password: {
        value: "",
        placeholder: $T("请输入"),
        showPassword: true,
        style: {},
        event: {
          change: this.ElInput_password_change_out,
          input: this.ElInput_password_input_out
        }
      },
      ElInput_oldpassword: {
        value: "",
        placeholder: $T("请输入"),
        showPassword: true,
        style: {},
        event: {
          change: this.ElInput_oldpassword_change_out,
          input: this.ElInput_oldpassword_input_out
        }
      },
      ElInput_cetconfirmpassword: {
        value: "",
        placeholder: $T("请输入"),
        showPassword: true,
        style: {},
        event: {
          change: this.ElInput_cetconfirmpassword_change_out,
          input: this.ElInput_cetconfirmpassword_input_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.openTrigger_in = val;
      vm.CetForm_editfe32.data = {};
      vm.CetForm_editfe32.resetTrigger_in = new Date().getTime();
    },

    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    }
  },

  methods: {
    CetDialog_add_open_out() {},
    CetDialog_add_close_out() {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      this.CetForm_editfe32.localSaveTrigger_in = this._.cloneDeep(val);
    },
    // 设置组件唯一识别字段表单输出
    CetForm_editfe32_currentData_out() {},
    CetForm_editfe32_saveData_out(val) {
      this.changePassword(this._.cloneDeep(val));
    },
    CetForm_editfe32_finishData_out() {},
    CetForm_editfe32_finishTrigger_out() {},

    ElInput_password_change_out() {},
    ElInput_password_input_out() {},
    ElInput_cetconfirmpassword_change_out() {},
    ElInput_cetconfirmpassword_input_out() {},
    ElInput_oldpassword_change_out() {},
    ElInput_oldpassword_input_out() {},

    //endregion
    async changePassword(val) {
      console.log(this.inputData_in);
      // 重置密码接口
      var vm = this;

      var obj = {
        rootId: vm.userInfo.userId,
        rootName: vm.userInfo.userName,
        rootPassword: val.oldpassword, //登录用户的操作密码
        userName: vm.inputData_in.name, //选填，权限服务缺陷(不应该是数值类型)
        userId: vm.inputData_in.id,
        newPassword: val.password
      };

      const respose = await customApi.updatePasswordByRoot(obj);

      if (respose.code === 0) {
        // 调用接口，关联用户与用户组的关系
        vm.$message.success($T("修改密码成功！"));
        vm.$emit("updatePassword", val.password);
        vm.CetDialog_add.closeTrigger_in = Date.now();
      }
    },

    //占位函数，无实际用处
    nothing() {}
  },
  created: function () {}
};
</script>
<style lang="scss" scoped></style>
