<template>
  <div class="w-full h-full">
    <div class="h-[32px] w-full mt-[16px]">
      <CustomElDatePicker
        prefix_in="选择日期"
        style="width: 187px"
        v-bind="CetDatePicker_1.config"
        v-model="CetDatePicker_1.val"
        @change="CetDatePicker_1_change_out"
      ></CustomElDatePicker>
      <el-button type="primary" class="ml-[8px]" @click="onExport">
        导出
      </el-button>
    </div>
    <div
      class="h-[78px] w-full my-[16px] bg-BG flex items-center justify-between p-[16px] box-border"
    >
      <div class="text-T2" v-for="i in dataList" :key="i.id">
        <div class="text-[12px]">
          {{ i.label }}
          <el-tooltip
            content="注水井欠注量超过3m³之和"
            placement="top-start"
            v-if="i.isICon"
          >
            <omega-icon
              symbolId="question-lin"
              class="text-[12px]"
            ></omega-icon>
          </el-tooltip>
        </div>
        <div class="text-[12px] my-[4px]">({{ i.unit }})</div>
        <div class="flex items-center">
          <div
            class="text-Sta4 text-[16px] font-medium overflow-hidden text-ellipsis whitespace-nowrap"
            :title="i.value + i.unit"
          >
            {{ i.value || i.value === 0 ? i.value : "--" }}
          </div>
        </div>
      </div>
    </div>
    <div class="h-[calc(100%-64px-78px)] w-full">
      <el-table height="100%" :data="tableData" :highlight-current-row="true">
        <el-table-column
          v-for="(item, index) in tableCol"
          :key="index"
          v-bind="item"
        />
      </el-table>
    </div>
    <ExportTime v-bind="exportTime" :selectNode="selectNode" />
  </div>
</template>

<script>
import { getTableColWater } from "../component/tableCol.jsx";
import ExportTime from "./exportTime.vue";
import customApi from "@/api/custom.js";
import common from "eem-utils/common";
export default {
  name: "WaterfloodProfile",
  props: { selectNode: Object },
  components: { ExportTime },
  data() {
    return {
      CetDatePicker_1: {
        val: this.$moment().startOf("d").subtract(1, "days").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "date",
          clearable: false,
          pickerOptions: {
            disabledDate: time => {
              return time.getTime() >= this.$moment().startOf("d").valueOf();
            }
          }
        }
      },
      exportTime: {
        openTrigger_in: +new Date()
      },
      dataList: [
        {
          model: "totalManifoldFlow",
          id: 1,
          label: "日汇管总流量",
          value: null,
          unit: "m³/日"
        },
        {
          model: "electricityConsumption",
          id: 2,
          label: "日用电量",
          value: null,
          unit: "kwh/日"
        },
        {
          model: "unitConsumption",
          id: 3,
          label: "日均单耗",
          value: null,
          unit: "kwh/m³"
        },
        {
          model: "shortTotal",
          id: 4,
          label: "注水井欠注量",
          isICon: true,
          value: null,
          unit: "m³"
        }
      ],
      tableData: []
    };
  },
  computed: {
    tableCol() {
      return getTableColWater();
    }
  },
  watch: {
    selectNode: {
      deep: true,
      handler(val) {
        if (!_.isObject(val)) return;
        this.getTableData();
      }
    }
  },
  methods: {
    CetDatePicker_1_change_out() {
      this.getTableData();
    },
    onExport() {
      this.exportTime.openTrigger_in = +new Date();
    },
    async getTableData() {
      const params = {
        objectId: this.selectNode?.id,
        objectLabel: this.selectNode?.modelLabel,
        logTime: this.CetDatePicker_1.val
      };
      this.tableData = [];
      this.dataList?.map(i => {
        i.value = null;
        return {
          ...i
        };
      });
      if (params.objectLabel !== "waterinjectionstation") return;
      const res = await customApi.onlineSituation(params);
      this.tableData = res?.data?.shortWellList || [];
      for (const key in res?.data) {
        this.dataList?.map(i => {
          if (i.model == key) {
            i.value = common.formatNumberWithPrecision(res?.data[key], 1);
          }
        });
      }
    }
  },
  created() {},
  mounted() {
    this.getTableData();
  }
};
</script>

<style lang="scss" scoped></style>
