<template>
  <div class="page eem-common">
    <el-container class="fullheight">
      <el-aside width="315px" class="eem-aside flex-column">
        <div class="mbJ3">
          <el-input
            suffix-icon="el-icon-search"
            placeholder="输入关键字以检索"
            class="device-search"
            v-model="filterText"
          ></el-input>
        </div>
        <div class="flex-auto" style="overflow: auto">
          <el-tree
            ref="powerTree"
            style="width: 100%; height: 100%"
            :data="treeData"
            node-key="tree_id"
            :props="treeProps"
            :filter-node-method="filterNode"
            :highlight-current="true"
            :default-expanded-keys="expandedKeys"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
          ></el-tree>
        </div>
      </el-aside>
      <el-container
        v-show="modelLabels.includes(_.get(currentNode, 'data.modelLabel'))"
        style="overflow: auto; min-height: 720px"
        class="fullheight mlJ3 flex-column"
      >
        <div class="clearfix mbJ3 eem-cont">
          <time-tool
            class="fr"
            :val.sync="startTime"
            :timeType_in="timeType_in"
            :typeID="queryTime.cycle"
            @change="changeQueryTime"
          ></time-tool>
        </div>
        <div style="flex: 1; overflow: auto" class="minWH flex-column">
          <div class="flex-auto flex-row mbJ3">
            <div class="flex-auto eem-cont flex-column">
              <div class="card-header mbJ3">
                <headerSpot>事件列表</headerSpot>
                <span
                  class="btn"
                  type="text"
                  @click="jumpPage('voltageVariation')"
                >
                  更多
                </span>
              </div>
              <EventList
                class="flex-auto"
                :current-node="currentNode"
                :queryTime="queryTime"
                :refreshTrigger_in="refreshTrigger_in"
                @handleEventRow="handleEventRow"
              />
            </div>
            <div class="flex-auto eem-cont mlJ3 flex-column">
              <div class="card-header mbJ3">
                <headerSpot>趋势图</headerSpot>
                <span class="btn" type="text" @click="jumpPage('trendcurve')">
                  更多
                </span>
              </div>
              <PowerTrend
                class="flex-auto"
                :current-node="currentNode"
                :queryTime="queryTime"
                :refreshTrigger_in="refreshTrigger_in"
              />
            </div>
          </div>
          <div class="flex-auto flex-row">
            <div class="flex-auto eem-cont flex-column">
              <div class="card-header mbJ3">
                <headerSpot>容忍度曲线</headerSpot>
                <span class="btn" type="text" @click="jumpPage(ITIC_value)">
                  更多
                </span>
              </div>
              <ITIC
                :current-node="currentNode"
                :queryTime="queryTime"
                :refreshTrigger_in="refreshTrigger_in"
                :handleRow_in="handleRow"
                @ElSelect_value="select_value"
                class="flex-auto"
              />
            </div>
            <div class="flex-auto eem-cont mlJ3 flex-column">
              <div class="card-header mbJ3">
                <headerSpot>电能质量指标</headerSpot>
                <!-- <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button> -->
              </div>
              <PowerQualityIndex
                :current-node="currentNode"
                :queryTime="queryTime"
                :refreshTrigger_in="refreshTrigger_in"
                class="flex-auto"
              />
            </div>
          </div>
        </div>
      </el-container>
      <el-container
        v-show="!modelLabels.includes(_.get(currentNode, 'data.modelLabel'))"
      >
        <p class="text-center w100 fs20 info">请选择管网设备</p>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import commonApi from "@/api/custom.js";
import common from "eem-utils/common";
import TimeTool from "eem-components/TimeTool.vue";
import EventList from "./singlepointanalysis/EventList";
import ITIC from "./singlepointanalysis/ITIC";
import PowerQualityIndex from "./singlepointanalysis/PowerQualityIndex";
import PowerTrend from "./singlepointanalysis/PowerTrend";
import ELECTRICAL_DEVICE from "@/store/electricaldevice.js";

export default {
  name: "SinglePointAnalysis",
  components: { TimeTool, EventList, ITIC, PowerQualityIndex, PowerTrend },
  props: {
    selectedMenu: {
      type: String
    }
  },

  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    modelLabels() {
      return ELECTRICAL_DEVICE.map(i => i.value);
    }
  },

  data() {
    return {
      treeData: [],
      expandedKeys: [], // 初始展开
      treeProps: {
        children: "children",
        label: "name",
        isLeaf: "leaf"
      },
      filterText: "",
      currentNode: null,
      startTime: Date.now(),
      refreshTrigger_in: new Date().getTime(),
      queryTime: {
        startTime: common.initDateRange("M")[0],
        endTime: common.initDateRange("M")[1],
        cycle: 14
      },
      timeType_in: [
        {
          type: "date",
          text: "日",
          typeID: 12,
          number: 1,
          unit: "d"
        },
        {
          type: "month",
          text: "月",
          typeID: 14,
          number: 1,
          unit: "M"
        }
      ],
      handleRow: {},
      // 容忍度切换
      ITIC_value: "tolerance"
    };
  },
  watch: {
    currentNode: {
      deep: true,
      handler: function (val, oldVal) {
        // this.refreshTrigger_in = new Date().getTime();
      }
    },

    filterText(val) {
      this.$refs.powerTree.filter(val);
    }
    /* selectedMenu: {
      deep: true,
      handler: function(val, oldVal) {
        if (val === "单点分析" && this._.get(this.currentNode, "data.leaf")) {
          this.refreshTrigger_in = new Date().getTime();
        }
      }
    } */
  },

  methods: {
    jumpPage(val) {
      this.$router.push({ path: val });
    },
    jumpTab(tab) {
      this.$emit("jump", tab);
    },
    changeQueryTime({ val, timeOption }) {
      var date = this.$moment(val);
      this.queryTime.startTime = date.startOf(timeOption.unit).valueOf();
      this.queryTime.endTime = date.endOf(timeOption.unit).valueOf() + 1;
      this.queryTime.cycle = timeOption.typeID;

      this.$emit("changeQueryTime", this.queryTime);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1;
    },
    handleNodeClick(obj, node) {
      this.currentNode = node;
    },
    getTreeData() {
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [
          {
            filter: {
              expressions: [{ limit: 1, operator: "EQ", prop: "roomtype" }]
            },
            modelLabel: "room"
          }
        ],
        treeReturnEnable: true
      };
      ELECTRICAL_DEVICE.forEach(item => {
        data.subLayerConditions.push({ modelLabel: item.value });
      });
      commonApi.getPqNodeTree(data, this.projectId, true).then(res => {
        if (res.code === 0) {
          _this.treeData = res.data;
          if (_this._.get(res.data, "[0]")) {
            let currentNode = _this
              .dataTransform(res.data)
              .find(item => this.modelLabels.includes(item.modelLabel));
            _this.$nextTick(() => {
              _this.$refs.powerTree.setCurrentKey(currentNode.tree_id);
              _this.currentNode = _this.$refs.powerTree.getNode(
                currentNode.tree_id
              );
              _this.expandedKeys = [currentNode.tree_id];
            });
          }
        }
      });
    },
    // 节点树数据平铺，用于选中第一个管网设备
    dataTransform(array) {
      const cloneData = this._.cloneDeep(array);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },
    handleEventRow(row) {
      this.handleRow = this._.cloneDeep(row);
    },
    select_value(val) {
      this.ITIC_value = val === 1 ? "transienteventanalysis" : "tolerance";
    }
  },
  created: function () {},
  activated() {
    this.currentNode = {};
    this.getTreeData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: static;
}
.time-tool {
  position: absolute;
  right: 17px;
  top: 20px;
  z-index: 10;
}
.card-header {
  position: relative;
  .btn {
    @include background_color(BG1);
    @include font_color(ZS);
    cursor: pointer;
    position: absolute;
    right: 0px;
    top: 0px;
    line-height: 24px;
    @include padding_left(J3);
  }
}
</style>
