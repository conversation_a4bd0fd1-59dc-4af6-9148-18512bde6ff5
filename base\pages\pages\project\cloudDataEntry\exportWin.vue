<template>
  <!-- 1弹窗组件 -->
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <div class="fullheight eem-cont-c1">
      <!-- 父子关联 -->
      <CetGiantTree
        class="switch-tree"
        v-bind="CetGiantTree_1"
        v-on="CetGiantTree_1.event"
      ></CetGiantTree>
    </div>
    <span slot="footer">
      <span class="fl mlJ1">
        {{ `${$T("最多选中{0}个节点", this.maxExportNodeNumber)}` }}
      </span>
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import common from "eem-utils/common.js";
import customApi from "@/api/custom";
import TREE_PARAMS from "@/store/treeParams.js";
export default {
  name: "shareRate",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    // 已关联管网设备列表
    deviceList: {
      type: Array,
      default() {
        return [];
      }
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: $T("选择节点"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        width: "600px",
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      checkedNodes: [],
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {}
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out //勾选节点输出
        }
      },
      maxExportNodeNumber: 100
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.CetGiantTree_1.checkedNodes = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.checkedNodes = [];
      this.getConfig();
      this.getTreeData();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    inputData_in(val) {
      this.CetDialog_1.inputData_in = val;
    }
  },

  methods: {
    //选中节点树节点
    CetGiantTree_1_checkedNodes_out(val) {
      val = val || [];
      if (val.length > this.maxExportNodeNumber) {
        this.$message.warning(
          `${$T("最多选中{0}个节点", this.maxExportNodeNumber)}`
        );
        val.pop();
        this.CetGiantTree_1.checkedNodes = this._.cloneDeep(val);
        return;
      }
      this.checkedNodes = val;
    },
    //获取导出节点数量配置
    getConfig() {
      let queryData = {};
      customApi.getDataEntryConfig(queryData).then(response => {
        if (response.code === 0) {
          this.maxExportNodeNumber =
            this._.get(response, "data.maxExportNodeNumber", 100) || 100;
        }
      });
    },
    //获取节点树数据
    getTreeData() {
      let queryData = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.energyEntry,
        treeReturnEnable: true
      };
      customApi.getNodeTreeSimple(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_1.inputData_in = data;
          this.CetGiantTree_1.selectNode = this._.get(response, "data[0]");
        }
      });
    },
    // 保存
    addSupplyRelation_out() {
      var me = this;
      if (me.checkedNodes.length === 0) {
        this.$message({
          message: $T("请选择导出节点"),
          type: "warning"
        });
        return;
      }
      const nodes = me.checkedNodes.map(item => {
        return {
          id: item.id,
          modelLabel: item.modelLabel,
          tree_id: item.tree_id
        };
      });
      const params = {
        ...this.inputData_in,
        nodes: nodes
      };
      const urlStr = "/eem-service/v1/system/data/export/multiNodes";
      common.downExcel(urlStr, params, this.token, this.projectId, function () {
        me.$emit("updata_out");
        me.CetDialog_1.closeTrigger_in = new Date().getTime();
      });
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      this.addSupplyRelation_out();
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.switch-tree {
  height: 400px;
}
</style>
