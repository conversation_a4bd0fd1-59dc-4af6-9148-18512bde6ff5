﻿<template>
  <div class="page eem-common">
    <div
      class="menu_group"
      style="padding: 0px; height: 0px; position: relative"
    >
      <el-radio-group
        size="small"
        class="radiogroup"
        v-model="selectedMenu"
        style="position: absolute; z-index: 1; left: calc(50% - 200px)"
      >
        <el-radio-button v-for="menu in menus" :label="menu.id" :key="menu.id">
          {{ menu.label }}
        </el-radio-button>
      </el-radio-group>
    </div>
    <el-container class="fullheight">
      <template v-if="selectedMenu !== 3">
        <el-aside width="315px" class="eem-aside">
          <CetGiantTree
            v-bind="CetGiantTree_1"
            v-on="CetGiantTree_1.event"
          ></CetGiantTree>
        </el-aside>
        <el-container class="mlJ3 padding0 fullheight">
          <EnergyData
            :selectedMenu="selectedMenu"
            v-if="selectedMenu === 1"
            :clickNode="clickNode"
            @showTab="showTab"
            @treeDrilling="treeDrilling_out"
            @pageJump="pageJump_out"
          ></EnergyData>
          <ElectricityConsumption
            :selectedMenu="selectedMenu"
            v-if="selectedMenu === 2"
            :clickNode="clickNode"
          ></ElectricityConsumption>
          <EnergyWarning
            v-if="selectedMenu === 4"
            :clickNode="clickNode"
            @setClickNode="setClickNode_out"
            :treeData_in="energyWarningTree"
          ></EnergyWarning>
          <EnergyEfficiencyIndexes
            :selectedMenu="selectedMenu"
            v-if="selectedMenu === 5"
            :clickNode="clickNode"
            :clickProjectNode_in="clickProjectNode"
          ></EnergyEfficiencyIndexes>
        </el-container>
      </template>
      <EnergyQuery
        v-if="selectedMenu === 3"
        :selectedMenu="selectedMenu"
        :clickNode_in="clickNode"
        @setNode="setNode"
      ></EnergyQuery>
    </el-container>
    <Slide v-if="selectedMenu !== 3"></Slide>
  </div>
</template>
<script>
import EnergyData from "./overview/Overview";
import ElectricityConsumption from "./electricityconsumption/ElectricityConsumption";
import EnergyQuery from "./energyquery/EnergyQuery";
import EnergyWarning from "../eventCenter/energyconsumptionevent/EnergyConsumptionEvent";
import EnergyEfficiencyIndexes from "./energyefficiencyindexes/EnergyEfficiencyIndexes";
import Slide from "eem-components/slide/Slide.vue";
import TREE_PARAMS from "@/store/treeParams.js";
import { httping } from "@omega/http";

export default {
  // 电能质量分析
  name: "PowerQualityAnalyzer",
  components: {
    Slide,
    EnergyData,
    ElectricityConsumption,
    EnergyQuery,
    EnergyWarning,
    EnergyEfficiencyIndexes
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    defaultExpandedKeys() {
      return [];
      // return [this.$store.state.powerQualityNode.tree_id];
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    menus() {
      const initList = [
        {
          label: $T("项目概览"),
          id: 1
        },
        {
          label: $T("分项用能"),
          id: 2
        },
        {
          label: $T("用能查询"),
          id: 3
        },
        {
          label: $T("能源事件"),
          id: 4
        },
        {
          label: $T("能效指标"),
          id: 5
        }
      ];
      const energydataTabs =
        this.$store.state.systemCfg.energydataTabs || initList;
      const keyArr = [
        "",
        $T("项目概览"),
        $T("分项用能"),
        $T("用能查询"),
        $T("能源事件"),
        $T("能效指标")
      ];
      energydataTabs.forEach(item => {
        item.label = keyArr[String(item.id)];
      });
      return energydataTabs;
    }
  },

  data() {
    return {
      energyWarningTree: [],
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetTree_1_currentNode_out //选中单行输出
        }
      },
      selectedMenu: 1,
      currentNode: null,
      clickNode: null,
      clickProjectNode: null,
      parames: [
        {
          modelLabel: "city",
          name: "深圳1",
          province_model: [{ id: 1, modelLabel: "province", move_from: null }]
        }
      ],
      params1: {
        queryCondition: [
          { filter: null, modelLabel: "building", props: [] },
          { filter: null, modelLabel: "floor", props: [] }
        ],
        id: 0,
        modelLabel: "project",
        queryID: 0
      }
    };
  },
  watch: {},

  methods: {
    CetTree_1_currentNode_out(val) {
      if (!val) {
        return;
      }
      if (val.modelLabel === "manuequipment") {
        if (this.selectedMenu === 1 && this.menus.find(i => i.id === 2)) {
          this.selectedMenu = 2;
        }
      }
      this.clickNode = this._.cloneDeep(val);
    },
    showTab() {
      if (this.menus.find(i => i.id === 2)) {
        this.selectedMenu = 2;
      }
    },
    getTreeData() {
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.management,
        treeReturnEnable: true
      };
      httping({
        url: "/eem-service/v1/node/nodeTree/simple",
        method: "POST",
        data
      }).then(res => {
        if (res.code === 0) {
          _this.energyWarningTree = this._.cloneDeep(res.data);
          _this.CetGiantTree_1.inputData_in = res.data;
          _this.CetGiantTree_1.selectNode = res.data[0];
          _this.clickProjectNode = res.data[0];
        }
      });
    },
    treeDrilling_out(val) {
      console.log(val);
      if (!val) {
        return;
      }
      if (this.selectedMenu === 1) {
        var clickNode = this.clickNode,
          childNode = clickNode.children || [];
        childNode.forEach(item => {
          if (item.name === val) {
            this.CetGiantTree_1.selectNode = {
              id: item.id,
              modelLabel: item.modelLabel,
              tree_id: item.modelLabel + "_" + item.id
            };
            if (val.modelLabel === "manuequipment") {
              if (this.selectedMenu === 1 && this.menus.find(i => i.id === 2)) {
                this.selectedMenu = 2;
              }
            }
            this.clickNode = item;
          }
        });
      }
    },
    pageJump_out(val) {
      if (!val || !this.menus.find(i => i.id === val)) {
        return;
      }
      this.selectedMenu = val;
    },
    setClickNode_out(val) {
      if (!val) {
        return;
      }
      this.CetGiantTree_1.selectNode = val;
      this.clickNode = val;
      if (val.modelLabel === "manuequipment") {
        if (this.selectedMenu === 1 && this.menus.find(i => i.id === 2)) {
          this.selectedMenu = 2;
        }
      }
    },
    setNode(val) {
      this.clickNode = val;
      this.CetGiantTree_1.selectNode = val;
    }
  },

  mounted() {},
  activated() {
    this.getTreeData();
    this.selectedMenu = "";
    this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
    this.CetGiantTree_1.selectNode = this.clickNode = null;
    var params = this.$route.params;
    if (params && params.id && params.modelLabel) {
      this.$nextTick(() => {
        this.selectedMenu = this._.get(this.menus, "[0].id");
        this.CetTree_1.selectNode = {
          id: params.id,
          modelLabel: params.modelLabel,
          tree_id: params.modelLabel + "_" + params.id
        };
      });
    } else if (
      params &&
      params.selectedMenu &&
      this.menus.find(i => i.id === params.selectedMenu)
    ) {
      this.$nextTick(() => {
        this.selectedMenu = params.selectedMenu;
      });
    } else {
      this.$nextTick(() => {
        this.selectedMenu = this._.get(this.menus, "[0].id");
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}

.menu_group {
  text-align: center;
  .radiogroup {
    top: 0px;
  }
}
</style>
