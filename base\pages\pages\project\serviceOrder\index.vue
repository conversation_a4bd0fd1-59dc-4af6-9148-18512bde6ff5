<template>
  <div class="page eem-common fullheight">
    <div class="fullheight" v-show="!showDetail">
      <div class="fullheight flex-row">
        <div class="eem-aside fullheight flex-column" style="width: 240px">
          <div class="mbJ1">
            <span class="common-title-H2">{{ $T("工单状态统计") }}</span>
          </div>
          <div style="height: 200px; position: relative">
            <CetChart v-bind="CetChart_order"></CetChart>
            <div
              style="
                text-align: center;
                position: absolute;
                width: 100%;
                top: 80px;
              "
            >
              <div>
                <span class="chartLine">
                  <span class="text-overflow" style="font-size: 20px">
                    {{ totleNum }}
                  </span>
                  {{ $T("单") }}
                </span>
              </div>
              <div>
                <span>{{ $T("工单总数") }}</span>
              </div>
            </div>
          </div>
          <div class="flex-auto">
            <el-row
              :gutter="10"
              style="margin: 0px; line-height: 34px"
              v-for="(item, index) in orderList"
              :key="index"
            >
              <el-col :span="10" class="text-overflow">
                <i
                  class="echar-icon1 mrJ"
                  :style="{ background: item.itemStyle.color }"
                ></i>
                <el-tooltip :content="item.name" effect="light" placement="top">
                  <span>{{ item.name }}</span>
                </el-tooltip>
              </el-col>
              <el-col :span="7">
                <span>{{ filNumTotle(item.value, item.totle) }}%</span>
              </el-col>
              <el-col :span="7">
                <el-tooltip
                  :content="filNum(item.value) + $T('个')"
                  effect="light"
                >
                  <div class="nowrap text-ellipsis fullwidth">
                    {{ filNum(item.value) }}{{ $T("个") }}
                  </div>
                </el-tooltip>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="flex-auto fullheight mlJ2 flex-column">
          <div>
            <el-tabs
              v-model="selectedMenu"
              @tab-click="handleTabClick_out(selectedMenu)"
              class="eem-tabs-custom"
            >
              <el-tab-pane
                v-for="menu in menus"
                :label="menu"
                :name="menu"
                :key="menu"
              ></el-tab-pane>
            </el-tabs>
          </div>
          <div class="flex-auto mtJ3" style="overflow: auto">
            <div class="fullheight flex-column eem-min-width eem-cont">
              <div class="flex-row">
                <div class="flex-auto">
                  <div class="clearfix time fr mlJ1">
                    <!-- <div class="time-label">预计开始时间:</div> -->
                    <div class="time-range">
                      <TimeRange
                        @change="handleTime_out"
                        :val.sync="queryTime"
                      ></TimeRange>
                    </div>
                  </div>
                  <div class="mlJ1 fr">
                    <customElSelect
                      class="fl"
                      size="small"
                      default-first-option
                      v-model="ElSelect_rank.value"
                      v-bind="ElSelect_rank"
                      v-on="ElSelect_rank.event"
                      :prefix_in="$T('等级')"
                    >
                      <ElOption
                        v-for="item in ElOption_rank.options_in"
                        :key="item[ElOption_rank.key]"
                        :label="item[ElOption_rank.label]"
                        :value="item[ElOption_rank.value]"
                      ></ElOption>
                    </customElSelect>
                  </div>
                  <div class="mlJ1 fr">
                    <customElSelect
                      class="fl"
                      size="small"
                      default-first-option
                      v-model="ElSelect_team.value"
                      v-bind="ElSelect_team"
                      v-on="ElSelect_team.event"
                      :prefix_in="$T('责任班组')"
                    >
                      <ElOption
                        v-for="item in ElOption_team.options_in"
                        :key="item[ElOption_team.key]"
                        :label="item[ElOption_team.label]"
                        :value="item[ElOption_team.value]"
                      ></ElOption>
                    </customElSelect>
                  </div>
                  <ElInput
                    size="small"
                    class="search-input fr"
                    suffix-icon="el-icon-search"
                    :placeholder="$T('输入工单号以检索')"
                    v-model="filterText"
                    v-bind="ElInput_keyword"
                    v-on="ElInput_keyword.event"
                  ></ElInput>
                  <!-- 高级搜索按钮组件 -->
                  <!-- <CetButton v-bind="CetButton_search" v-on="CetButton_search.event" class="fl ml10"></CetButton> -->
                </div>
                <div class="text-right">
                  <!-- addOrder按钮组件 -->
                  <CetButton
                    v-permission="'maintenanceworkorder_create'"
                    class="fr mlJ1"
                    v-bind="CetButton_addOrder"
                    v-on="CetButton_addOrder.event"
                  ></CetButton>
                  <!-- refreshOrder按钮组件 -->
                  <CetButton
                    class="fr mlJ1"
                    v-bind="CetButton_export"
                    v-on="CetButton_export.event"
                  ></CetButton>
                  <CetButton
                    class="fr mlJ1"
                    v-if="CetButton_examine.visible_in"
                    v-bind="CetButton_examine"
                    v-on="CetButton_examine.event"
                  ></CetButton>
                  <!-- refreshOrder按钮组件 -->
                  <CetButton
                    class="fr mlJ1"
                    v-bind="CetButton_refresh"
                    v-on="CetButton_refresh.event"
                  ></CetButton>
                </div>
              </div>
              <div class="flex-auto mtJ3">
                <CetTable
                  ref="CetTable"
                  class="eem-table-custom"
                  style="height: 100%; text-align: right"
                  :data.sync="CetTable_1.data"
                  :dynamicInput.sync="CetTable_1.dynamicInput"
                  v-bind="CetTable_1"
                  v-on="CetTable_1.event"
                  row-key="id"
                  @selection-change="handleSelectionChange"
                >
                  <el-table-column
                    v-if="selectedMenu == $T('待审核')"
                    type="selection"
                    width="40"
                    align="left"
                    :selectable="selectable"
                  ></el-table-column>
                  <template v-for="(column, index) in Columns_Order">
                    <el-table-column
                      v-if="column.custom && column.custom === 'tag'"
                      v-bind="column"
                      :key="index"
                      class-name="font0 hand"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <el-tag
                          size="small"
                          class="text-middle font14"
                          :effect="column.tagEffect"
                          :type="
                            column.tagTypeFormatter
                              ? column.tagTypeFormatter(scope.row, scope.column)
                              : 'primary'
                          "
                          :color="
                            column.colorTypeFormatter
                              ? column.colorTypeFormatter(
                                  scope.row,
                                  scope.column
                                )
                              : '#fff'
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : scope.row[column.prop]
                          }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else-if="column.custom && column.custom === 'button'"
                      v-bind="column"
                      :key="index"
                      class-name="font0"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <span
                          class="clickformore"
                          @click.stop="
                            column.onButtonClick
                              ? column.onButtonClick(scope.row, scope.$index)
                              : void 0
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : column.text
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    ></el-table-column>
                  </template>
                </CetTable>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <orderDetail
      v-show="showDetail"
      @goBack="goBack"
      :inputData_in="orderMsg_in"
    ></orderDetail>
    <CreateOrder
      @confirm_out="createOrder_confirm_out"
      :visibleTrigger_in="createOrder.visibleTrigger_in"
      :closeTrigger_in="createOrder.closeTrigger_in"
      :inputData_in="createOrder.inputData_in"
    />
    <toExamine
      :visibleTrigger_in="toExamine.visibleTrigger_in"
      :closeTrigger_in="toExamine.closeTrigger_in"
      :inputData_in="toExamine.inputData_in"
      :codes_in="toExamine.codes_in"
      @confirm_out="toExamine_confirm_out"
    />
    <DeviceList v-bind="DeviceList" v-on="DeviceList.event"></DeviceList>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import common from "eem-utils/common.js";
import CreateOrder from "./dialog/CreateOrder";
import orderDetail from "./dialog/Detail";
import toExamine from "./dialog/toExamine";
import TimeRange from "eem-components/TimeRange";
import DeviceList from "./dialog/DeviceList.vue";
import { measureTextWidth } from "eem-utils/measure.js";
const WORKSHEET_STATUS_TAG_COLORS = [
  "#fff",
  "#ff5a5a",
  "#ff5a5a",
  "#1b89b7",
  "#ffa32b",
  "#FF8A45",
  "#16a66c",
  "#58D9F9",
  "#1E66B7"
];
export default {
  name: "metrics",
  components: { CreateOrder, orderDetail, toExamine, TimeRange, DeviceList },
  data() {
    return {
      totleNum: "",
      orderList: [],
      menus: [
        $T("待维保"),
        $T("待审核"),
        $T("已退回"),
        $T("已完成"),
        $T("全部工单")
      ],
      selectedMenu: "",
      CetButton_search: {
        visible_in: true,
        disable_in: false,
        title: $T("高级搜索"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_search_statusTrigger_out
        }
      },
      // addOrder组件
      CetButton_addOrder: {
        visible_in: true,
        disable_in: false,
        title: $T("新建"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_addOrder_statusTrigger_out
        }
      },
      // refresh组件
      CetButton_refresh: {
        visible_in: true,
        disable_in: false,
        title: $T("刷新"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_refresh_statusTrigger_out
        }
      },
      // refresh组件
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      // examine组件
      CetButton_examine: {
        visible_in: false,
        disable_in: true,
        title: $T("批量审核"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_examine_statusTrigger_out
        }
      },
      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryMaintenanceOrder",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [
            { name: "startTime_in", operator: "EQ", prop: "startTime" },
            { name: "endTime_in", operator: "EQ", prop: "endTime" },
            { name: "tasklevel_in", operator: "EQ", prop: "tasklevel" },
            { name: "teamid_in", operator: "EQ", prop: "teamid" },
            { name: "tenantId_in", operator: "EQ", prop: "tenantId" },
            { name: "workOrder_in", operator: "EQ", prop: "workOrder" },
            {
              name: "workSheetStatus_in",
              operator: "EQ",
              prop: "workSheetStatus"
            }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {
          endTime_in: 0,
          startTime_in: 0,
          tasklevel_in: "",
          teamid_in: "",
          tenantId_in: 0,
          workOrder_in: "",
          workSheetStatus_in: 1
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {},
        exportFileName: "",
        event: {},
        highlightCurrentRow: false
      },
      Columns_Order: [],
      // order组件
      CetChart_order: {
        //组件输入项
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "item"
          },
          legend: {
            show: false
          },
          series: [
            {
              name: "",
              type: "pie",
              radius: ["70%", "85%"],
              label: {
                show: false
              },
              data: []
            }
          ]
        }
      },
      // 关键字组件
      ElInput_keyword: {
        value: "",
        event: {
          change: this.ElInput_keyword_change_out
        }
      },
      filterText: "",
      // 责任班组下拉框
      ElSelect_team: {
        value: null,
        style: {
          width: "200px"
        },
        filterable: true,
        event: {
          change: this.teamChange_out
        }
      },
      ElOption_team: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name"
      },
      // 等级下拉框
      ElSelect_rank: {
        value: null,
        style: {
          width: "130px"
        },
        event: {
          change: this.signinChange_out
        }
      },
      ElOption_rank: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text"
      },
      queryTime: [
        this.$moment().startOf("month").valueOf(),
        this.$moment().endOf("month").valueOf() + 1
      ],
      createOrder: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      toExamine: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        codes_in: []
      },
      showDetail: false,
      orderMsg_in: {},
      selecteOrders: [],
      // 巡检对象弹窗
      DeviceList: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        tableData: null,
        dialogTitle: "选择查看节点",
        event: {}
      }
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    projectTenantId() {
      return this.$store.state.projectTenantId;
    }
  },
  watch: {},
  methods: {
    // 批量审核，不属于当前阶段的不允许勾选
    selectable(row) {
      return row.userTaskConfig?.authorized;
    },
    //关键字搜索
    ElInput_keyword_change_out(val) {
      this.CetTable_1.dynamicInput.workOrder_in = val
        ? this._.cloneDeep(val)
        : "";
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    // 根据对象搜索工单
    CetButton_search_statusTrigger_out() {
      this.DeviceList.openTrigger_in = new Date().getTime();
    },
    //班组下拉框
    teamChange_out(val) {
      this.CetTable_1.dynamicInput.teamid_in = val ? this._.cloneDeep(val) : "";
      this.CetTable_1.queryTrigger_in = new Date().getTime();
      this.queryWorkOrderCount_out();
    },
    // 等级下拉框
    signinChange_out(val) {
      this.CetTable_1.dynamicInput.tasklevel_in = val
        ? this._.cloneDeep(val)
        : "";
      this.CetTable_1.queryTrigger_in = new Date().getTime();
      this.queryWorkOrderCount_out();
    },
    //时间控件
    handleTime_out(val) {
      this.CetTable_1.dynamicInput.startTime_in = val[0]
        ? this._.cloneDeep(val[0])
        : "";
      this.CetTable_1.dynamicInput.endTime_in = val[1]
        ? this._.cloneDeep(val[1])
        : "";
      if (
        [null, NaN, undefined].includes(
          this.CetTable_1.dynamicInput.teamid_in
        ) ||
        [null, NaN, undefined].includes(
          this.CetTable_1.dynamicInput.tasklevel_in
        )
      ) {
        return;
      }
      this.CetTable_1.queryTrigger_in = new Date().getTime();
      this.queryWorkOrderCount_out();
    },
    //点击表格多选框
    handleSelectionChange(val) {
      this.selecteOrders = this._.cloneDeep(val);
      if (val && val.length > 0) {
        this.CetButton_examine.disable_in = false;
      } else {
        this.CetButton_examine.disable_in = true;
      }
    },
    //切换tabs列表
    handleTabClick_out(val) {
      const ColumnsOrder1 = [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 60,
          label: "#",
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "code",
          minWidth: 100,
          width: "",
          label: $T("工单号"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "executetimeplan",
          minWidth: 120,
          width: "",
          label: $T("预计开始时间"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatDateCol("YYYY-MM-DD HH:mm:ss")
        },
        {
          type: "",
          prop: "timeconsumeplan",
          minWidth: "",
          width: "",
          label: $T("预计耗时"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: (row, column, cellValue, index) =>
            this.secondsFormat(cellValue, "hh h mm min")
        }
      ];
      const ColumnsOrder2 = [
        {
          type: "",
          prop: "deviceplanrelationship_model",
          minWidth: "",
          width: "",
          label: $T("维保对象"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.filDevicelist_out
        },
        {
          type: "",
          prop: "teamName",
          minWidth: "",
          width: "",
          label: $T("责任班组"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "personnumber",
          minWidth: "",
          width: "",
          label: $T("人员数量"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "taskLevelName",
          minWidth: "",
          width: "",
          label: $T("等级"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ];
      if (val === $T("待审核")) {
        this.CetTable_1.dynamicInput.workSheetStatus_in = 3;
        this.Columns_Order = [
          ...ColumnsOrder1,
          {
            type: "",
            prop: "timeconsume",
            minWidth: "",
            width: "",
            label: $T("实际耗时"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: (row, column, cellValue, index) =>
              this.secondsFormat(cellValue, "hh h mm min")
          },
          ...ColumnsOrder2,
          {
            type: "",
            prop: "",
            minWidth: 60,
            width: "",
            label: $T("操作"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: (row, column, cellValue, index) =>
              row.userTaskConfig && row.userTaskConfig.authorized
                ? $T("查看并审核")
                : $T("查看"),
            custom: "button",
            text: $T("查看并审核"),
            buttonFormatter: null,
            onButtonClick: this.showOrderDetail
          }
        ];
      } else if (val === $T("待维保")) {
        this.CetTable_1.dynamicInput.workSheetStatus_in = 1;
        this.Columns_Order = [
          ...ColumnsOrder1,
          ...ColumnsOrder2,
          {
            type: "",
            prop: "",
            minWidth: 60,
            width: "",
            label: $T("操作"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: (row, column, cellValue, index) =>
              row.userTaskConfig && row.userTaskConfig.authorized
                ? $T("查看并处理")
                : $T("查看"),
            custom: "button",
            text: $T("查看并处理"),
            buttonFormatter: null,
            onButtonClick: this.showOrderDetail
          }
        ];
      } else if (val === $T("已退回")) {
        this.CetTable_1.dynamicInput.workSheetStatus_in = 4;
        this.Columns_Order = [
          ...ColumnsOrder1,
          ...ColumnsOrder2,
          {
            type: "",
            prop: "",
            minWidth: 60,
            width: "",
            label: $T("操作"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: (row, column, cellValue, index) =>
              row.userTaskConfig && row.userTaskConfig.authorized
                ? $T("查看并处理")
                : $T("查看"),
            custom: "button",
            text: $T("查看并处理"),
            buttonFormatter: null,
            onButtonClick: this.showOrderDetail
          }
        ];
      } else if (val === $T("已完成")) {
        this.CetTable_1.dynamicInput.workSheetStatus_in = 6;
        this.Columns_Order = [
          ...ColumnsOrder1,
          {
            type: "",
            prop: "timeconsume",
            minWidth: "",
            width: "",
            label: $T("实际耗时"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: (row, column, cellValue, index) =>
              this.secondsFormat(cellValue, "hh h mm min")
          },
          ...ColumnsOrder2,
          {
            type: "",
            prop: "",
            minWidth: 60,
            width: "",
            label: $T("操作"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: null,
            custom: "button",
            text: $T("查看"),
            buttonFormatter: null,
            onButtonClick: this.showOrderDetail
          }
        ];
      } else if (val === $T("全部工单")) {
        this.CetTable_1.dynamicInput.workSheetStatus_in = "";
        this.Columns_Order = [
          ...ColumnsOrder1,
          {
            type: "",
            prop: "timeconsume",
            minWidth: "",
            width: "",
            label: $T("实际耗时"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: (row, column, cellValue, index) =>
              this.secondsFormat(cellValue, "hh h mm min")
          },
          ...ColumnsOrder2,
          {
            type: "",
            prop: "workSheetStatusName",
            // minWidth: 80,
            width: measureTextWidth($T("待维保"), "14px") + 46,
            label: $T("工单状态"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            custom: "tag",
            tagEffect: "light",
            colorTypeFormatter: this.statusColorTypeFormatter,
            formatter: common.formatTextCol()
          },
          {
            type: "",
            prop: "",
            minWidth: 60,
            width: "",
            label: $T("操作"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: null,
            custom: "button",
            text: $T("查看"),
            buttonFormatter: null,
            onButtonClick: this.showOrderDetail
          }
        ];
      }
      if (val === $T("待审核")) {
        this.CetButton_examine.visible_in = true;
        this.CetButton_examine.disable_in = true;
      } else {
        this.CetButton_examine.visible_in = false;
      }

      this.queryWorkOrderCount_out();
      this.CetTable_1.queryTrigger_in = new Date().getTime();
      this.$refs.CetTable.$refs.cetTable.doLayout();
    },
    //初始化入口
    init() {
      this.selectedMenu = $T("待维保");

      // 等级枚举值
      const levelList = this.$store.state.enumerations.worksheettasklevel || [];
      this.ElOption_rank.options_in = [
        {
          id: 0,
          text: $T("全部")
        },
        ...levelList
      ];
      this.ElSelect_rank.value = this._.get(
        this.ElOption_rank.options_in,
        "[0].id",
        null
      );

      Promise.all([
        new Promise((resolve, reject) => {
          this.getTeam_out(resolve);
        })
      ]).then(data => {
        this.handleTabClick_out(this.selectedMenu);
      });
    },
    //重新刷新页面
    reset() {
      if (this.selectedMenu) {
        this.handleTabClick_out(this.selectedMenu);
      }
    },
    // 获取班组列表信息
    getTeam_out(callback) {
      const _this = this;
      const params = {};
      customApi.queryInspectorTeamWithOutUser(params).then(res => {
        if (res.code === 0) {
          callback && callback();
          let arr = [
            {
              id: 0,
              name: $T("全部")
            }
          ];
          const data = this._.get(res, "data", []);
          arr = arr.concat(data);
          const teamId = this._.get(arr, "[0].id", null);
          _this.ElOption_team.options_in = _this._.cloneDeep(arr);
          _this.ElSelect_team.value = teamId;
        }
      });
    },
    //数组去重
    unique(arr) {
      //定义常量 res,值为一个Map对象实例
      const res = new Map();

      //返回arr数组过滤后的结果，结果为一个数组
      //过滤条件是，如果res中没有某个键，就设置这个键的值为1
      return arr.filter(a => !res.has(a.id) && res.set(a.id, 1));
    },
    //查询工单数量
    queryWorkOrderCount_out() {
      if (
        [null, NaN, undefined].includes(this.ElSelect_team.value) ||
        [null, NaN, undefined].includes(this.ElSelect_rank.value)
      ) {
        return;
      }
      const params = {
        endTime: this.$moment(this.queryTime[1]).valueOf(),
        startTime: this.$moment(this.queryTime[0]).valueOf(),
        /* workSheetStatuses: [1, 2, 3, 4, 5, 6, 7, 8],
        taskType: 2, */
        teamid: this.ElSelect_team.value,
        tasklevel: this.ElSelect_rank.value
      };
      customApi.queryMaintenanceCount(params).then(res => {
        if (res.code === 0) {
          const list = res.data || [];
          let num1 = null;
          let num2 = null;
          let num3 = null;
          let num4 = null;
          let totle = null;
          list.forEach(item => {
            if ([3].includes(item.workOrderStatus)) {
              num1 = item.count;
            } else if ([1].includes(item.workOrderStatus)) {
              num2 = item.count;
            } else if ([4].includes(item.workOrderStatus)) {
              num3 = item.count;
            } else if ([6].includes(item.workOrderStatus)) {
              num4 = item.count;
            }
            if ([1, 3, 4, 6].includes(item.workOrderStatus) && item.count) {
              totle += item.count;
            }
          });
          this.totleNum = totle;
          const dataList = [
            {
              value: num2,
              totle: totle,
              name: $T("待维保"),
              itemStyle: { color: "#ff5a5a" }
            },
            {
              value: num1,
              totle: totle,
              name: $T("待审核"),
              itemStyle: { color: "#1b89b7" }
            },
            {
              value: num3,
              totle: totle,
              name: $T("已退回"),
              itemStyle: { color: "#ffa32b" }
            },
            {
              value: num4,
              totle: totle,
              name: $T("已完成"),
              itemStyle: { color: "#16a66c" }
            }
          ];
          this.orderList = this._.cloneDeep(dataList);
          this.CetChart_order.options.series[0].data =
            this._.cloneDeep(dataList);
        }
      });
    },
    // addOrder输出
    CetButton_addOrder_statusTrigger_out() {
      this.createOrder.inputData_in = {};
      this.createOrder.visibleTrigger_in = new Date().getTime();
    },
    // 点击刷新工单列表按钮
    CetButton_refresh_statusTrigger_out() {
      this.reset();
    },
    // 点击导出按钮
    CetButton_export_statusTrigger_out() {
      const url = "/eem-service/v1/workorder/maintenance/export";
      const data = this.CetTable_1.dynamicInput;
      const currentPage = this.$refs.CetTable.currentPage || 1;
      const pageSize = this.$refs.CetTable.pageSize || 100;
      const page = {
        index: (currentPage - 1) * pageSize,
        limit: pageSize
      };
      const params = {
        teamid: data.teamid_in,
        tasklevel: data.tasklevel_in,
        workOrder: data.workOrder_in,
        endTime: data.endTime_in,
        startTime: data.startTime_in,
        // workSheetStatus: data.workSheetStatus_in,
        page: page,
        tenantId: this.projectTenantId
      };
      if (data.workSheetStatus_in) {
        params.workSheetStatus = data.workSheetStatus_in;
      }

      common.downExcel(url, params, this.token, this.projectId);
    },
    CetButton_examine_statusTrigger_out() {
      const list = this.selecteOrders || [];
      const codeArr = [];
      list.forEach(item => {
        codeArr.push(item.code);
      });

      this.toExamine.inputData_in = this._.cloneDeep({});
      this.toExamine.codes_in = this._.cloneDeep(codeArr);
      this.toExamine.visibleTrigger_in = new Date().getTime();
    },
    toExamine_confirm_out() {
      this.reset();
    },
    createOrder_confirm_out() {
      this.queryWorkOrderCount_out();
      this.reset();
    },
    // 事件等级标签样式格式化
    statusColorTypeFormatter(row, column) {
      const cellValue = row.worksheetstatus || 0;
      return WORKSHEET_STATUS_TAG_COLORS[cellValue];
    },
    //展示详情页面
    showOrderDetail(row) {
      this.orderMsg_in = this._.cloneDeep(row);
      this.showDetail = true;
    },
    //详情页面返回
    goBack(val) {
      this.showDetail = false;
      if (val) {
        this.reset();
      }
    },
    //过滤维保对象
    filDevicelist_out(row, column, cellValue, index) {
      if (
        row.deviceplanrelationship_model &&
        row.deviceplanrelationship_model.length
      ) {
        const list = row.deviceplanrelationship_model;
        let inspectObject = "";
        list.forEach((item, index) => {
          if (index) {
            inspectObject += " , ";
          }
          inspectObject += item.devicename;
        });
        row.inspectObject = inspectObject;
        return inspectObject;
      } else {
        return "--";
      }
    },
    //过滤耗时
    formTimeconsumeplan_out(row, column, cellValue, index) {
      if (cellValue) {
        if (!this._.isNumber(cellValue)) {
          //先转换成数字
          cellValue = parseFloat(cellValue);
        }
        return (cellValue / (1000 * 60 * 60)).toFixed(0) + "h"; //保留两位小数
      } else if (cellValue === 0) {
        return cellValue;
      } else {
        return "--";
      }
    },
    secondsFormat(sec, format) {
      let str = "--";
      if (sec || sec === 0) {
        const hour = Math.floor(sec / 3600000);
        const minute = Math.floor((sec - hour * 3600000) / 60000);
        if (
          format.indexOf("hh") !== -1 &&
          format.indexOf("mm") !== -1 &&
          format.indexOf("ss") === -1
        ) {
          str = format.replace(
            /(.*)hh(.*)mm(.*)/,
            "$1" + hour + "$2" + minute + "$3"
          );
        }
      }
      return str;
    },
    //过滤数字
    filNum(val) {
      if ([undefined, null, NaN].includes(val)) {
        return "--";
      } else {
        return val;
      }
    },
    //过滤百分比
    filNumTotle(val, totle = 1) {
      if ([undefined, null, NaN].includes(val) || !totle) {
        return "--";
      } else {
        return Number((val / totle) * 100).toFixed2(2);
      }
    }
  },
  activated: function () {
    this.CetTable_1.dynamicInput.tenantId_in = this.projectTenantId;
    this.showDetail = false;
    this.$nextTick(() => {
      this.init();
    });
  }
};
</script>
<style lang="scss" scoped>
.search-input {
  width: 180px;
}
.device-Input {
  display: inline-block;
}
.check-box {
  display: inline-block;
}
.clickformore {
  cursor: pointer;
  @include font_color(ZS);
}
.querytime-type {
  margin-top: 12px;
  width: 260px;
}
.echar-icon1 {
  display: inline-block;
  width: 12px;
  height: 12px;
  // background-color: #ec3b3b;
}

.time {
  width: 380px;
  display: flex;
  height: 32px;
}
.time-label {
  width: 100px;
  line-height: 32px;
}
.time-range {
  flex: 1;
}
.text-overflow {
  display: inline-block;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.eem-table-custom :deep(.el-tag) {
  @include font_color(T5, !important);
  border: none !important;
}
.chartLine {
  border-bottom: 1px solid;
  @include border_color(B1);
}
</style>
<style>
.btn-custom-confirm-display {
  display: none;
}

.custom-date-picker-inspect .el-picker-panel__footer .el-button--text {
  display: none !important;
}
</style>
