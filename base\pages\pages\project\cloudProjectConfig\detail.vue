<template>
  <CetDialog
    ref="CetDialog"
    v-bind="CetDialog_1"
    v-on="CetDialog_1.event"
    class="CetDialog"
  >
    <div style="max-height: 800px; overflow-y: auto" ref="detailWrap">
      <div class="cont-title">{{ $T("基本信息") }}</div>
      <div style="overflow: hidden" class="bg1 brC1 mtJ1 rowBox">
        <el-row :gutter="$J3">
          <template v-for="item in showFieldList">
            <el-col
              v-if="
                !['pic', 'document', 'nodeType', 'rangeSelection'].includes(
                  item.type
                ) &&
                (!item.show || item.show(inputData_in))
              "
              :span="8"
              :key="item.propertyLabel"
              class="mtJ3"
            >
              <div class="detail-label mbJ1">
                {{ item.name }} {{ item.unit ? `(${item.unit})` : "" }}
              </div>
              <div class="value">{{ findDataByField(item) }}</div>
            </el-col>
            <!-- 区间选择在详情里面将最大最小分开展示 -->
            <template v-if="item.type === 'rangeSelection'">
              <el-col :span="8" :key="item.propertyLabel" class="mtJ3">
                <div class="detail-label mbJ1">
                  {{ item.detailMinName }}
                </div>
                <div class="value">
                  {{ formatNumFloat(inputData_in[item.propertyLabel]) }}
                </div>
              </el-col>
              <el-col :span="8" :key="item.relatedLabel[0]" class="mtJ3">
                <div class="detail-label mbJ1">
                  {{ item.detailMaxName }}
                </div>
                <div class="value">
                  {{ formatNumFloat(inputData_in[item.relatedLabel[0]]) }}
                </div>
              </el-col>
            </template>
          </template>

          <el-col :span="8" v-if="documentField" class="mtJ3">
            <div class="detail-label mbJ1">{{ documentField.name }}</div>
            <div class="value">
              <span class="dcm-btn-class-label" :title="documentName">
                {{ documentName || "--" }}
              </span>
              <el-button
                size="mini"
                style="position: absolute"
                :disabled="!documentName"
                type="primary"
                @click="importDocument()"
              >
                {{ $T("导出文档") }}
              </el-button>
            </div>
          </el-col>

          <template v-for="item in picFields">
            <el-col :span="24" class="mtJ3" :key="item.propertyLabel">
              <div class="detail-label mbJ1">
                {{ item.name }}
              </div>
              <div class="value">
                <UploadImg
                  class="img"
                  :static_in="true"
                  :imgUrl.sync="inputData_in[item.propertyLabel]"
                />
              </div>
            </el-col>
          </template>
        </el-row>
      </div>
      <div
        class="cont-title mtJ1"
        v-show="inputData_in?.modelLabel === 'building'"
      >
        {{ $T("层级信息") }}
      </div>
      <div
        style="overflow: hidden"
        v-show="inputData_in?.modelLabel === 'building'"
        class="bg1 brC1 mtJ1 rowBox ptJ3"
      >
        <el-row :gutter="$J3">
          <el-col :span="8">
            <div class="detail-label mbJ1">{{ $T("层级对象") }}</div>
            <div class="value">
              {{
                `${
                  inputData_in?.project || inputData_in?.sectionarea || "--"
                } > ${inputData_in?.name || "--"}`
              }}
            </div>
          </el-col>
        </el-row>
      </div>
      <div
        class="cont-title mtJ1"
        v-show="inputData_in?.modelLabel === 'building'"
      >
        {{ $T("关联的设备") }}
      </div>
      <div
        style="overflow: hidden"
        v-show="inputData_in?.modelLabel === 'building'"
      >
        <div class="ptJ3 pbJ3 plJ4 prJ4 bg1 brC1 mtJ1" style="height: 300px">
          <CetTable
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
          >
            <template v-for="item in Columns_1">
              <ElTableColumn
                headerAlign="left"
                align="left"
                showOverflowTooltip
                :key="item.label"
                v-bind="item"
              ></ElTableColumn>
            </template>
          </CetTable>
        </div>
      </div>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import common from "eem-utils/common";
import UploadImg from "eem-components/uploadImg.vue";
import customApi from "@/api/custom";
import { getFields } from "eem-utils/projectTreeField.js";
export default {
  components: {
    UploadImg
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    Fields() {
      return getFields();
    },
    enumerations() {
      return this.$store.state.enumerations;
    },
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    fieldList() {
      const modelLabel = this._.get(this.inputData_in, "modelLabel");
      if (!modelLabel) return;
      const item = this.Fields.find(item => {
        if (modelLabel === "room") {
          let roomType = this._.get(this.inputData_in, "roomtype", null);
          if (roomType) {
            roomType = Number(roomType);
          } else {
            roomType = null;
          }
          return item.modelLabel === modelLabel && item.roomType === roomType;
        } else {
          return item.modelLabel === modelLabel;
        }
      });
      const node_fields = item ? item.node_fields : [];
      let addFields = [
        {
          name: $T("模型ID"),
          propertyLabel: "id",
          type: "string"
        },
        {
          name: $T("模型名称"),
          propertyLabel: "modelLabel",
          type: "string"
        }
      ];
      if (modelLabel === "room") {
        addFields.push({
          name: $T("房间类型"),
          propertyLabel: "roomtype$text",
          type: "string"
        });
      }
      return [...node_fields, ...addFields];
    },
    picFields() {
      if (!this._.isArray(this.fieldList)) return [];
      return this.fieldList.filter(item => item.type === "pic");
    },
    documentField() {
      if (!this._.isArray(this.fieldList)) return [];
      return this.fieldList.find(item => item.type === "document");
    },
    documentName() {
      if (!this.documentField) return;
      const documentPath = this._.get(
        this.inputData_in,
        this.documentField.propertyLabel
      );
      if (!documentPath) return;
      return (
        documentPath.split("/")[3].split("_")[0] +
        "." +
        documentPath.split("/")[3].split("_")[1].split(".")[1]
      );
    }
  },
  data() {
    return {
      customEnums: {},
      showFieldList: [],
      CetDialog_1: {
        title: $T("详情"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        // defaultSort: null, // { prop: "code"  order: "descending" },
        event: {},
        style: "text-align: center;height:100%;"
      },
      Columns_1: [
        {
          prop: "name", // 支持path a[0].b
          label: $T("设备名称"), //列名
          minWidth: "120",
          formatter: common.formatTextCol()
        },
        {
          prop: "code", // 支持path a[0].b
          label: $T("编号"), //列名
          minWidth: "120",
          formatter: common.formatTextCol()
        },
        {
          prop: "location", // 支持path a[0].b
          label: $T("安装位置"), //列名
          minWidth: "180",
          formatter: common.formatTextCol()
        },
        {
          prop: "brand", // 支持path a[0].b
          label: $T("品牌"), //列名
          minWidth: "120",
          formatter: common.formatTextCol()
        },
        {
          prop: "model", // 支持path a[0].b
          label: $T("型号"), //列名
          minWidth: "120",
          formatter: common.formatTextCol()
        },
        {
          prop: "commissiondate", // 支持path a[0].b
          label: $T("投运时间"), //列名
          minWidth: "150",
          formatter: common.formatDateCol("YYYY-MM-DD")
        },
        {
          prop: "voltagelevel$text", // 支持path a[0].b
          label: $T("电压等级"), //列名
          minWidth: "120",
          formatter: common.formatTextCol()
        }
      ]
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in() {
      this.CetDialog_1.openTrigger_in = Date.now();
      this.init();
      this.$nextTick(() => {
        $(this.$refs.detailWrap).scrollTop(0);
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },
  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    async init() {
      if (this._.get(this.inputData_in, "modelLabel") === "building") {
        this.getDeviceList();
      }
      this.showFieldList = [];
      this.customEnums = {};
      await this.customAnalysis();
      this.showFieldList = this.fieldList;
    },
    // 自定义字段需要通过后端接口返回解析
    async customAnalysis() {
      if (this.fieldList.find(i => i.type === "energytype")) {
        this.customEnums.energytype = await this.projectEnergy();
      }
      if (this.fieldList.find(i => i.type === "deviceclassification")) {
        this.customEnums.deviceclassification =
          await this.getDeviceclassification();
      }
      if (this.fieldList.find(i => i.type === "busbarseg")) {
        this.customEnums.busbarseg = await this.getBusbarseg();
      }
      if (this.fieldList.find(i => i.type === "product")) {
        this.customEnums.product = await this.getProduct();
      }
    },
    async projectEnergy() {
      const res = await customApi.getProjectEnergy(this.projectId);
      if (res.code !== 0) return [];
      return res.data || [];
    },
    async getDeviceclassification() {
      const res = await customApi.getEventClassification({
        rootLabel: "deviceclassification"
      });
      if (res.code !== 0) return [];
      return res.data || [];
    },
    async getBusbarseg() {
      const params = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [{ modelLabel: "busbarsection" }],
        treeReturnEnable: true
      };
      const res = await customApi.getNodeTreeSimple(params);
      if (res.code !== 0) return [];
      return this._.get(res.data, "[0].children", []);
    },
    async getProduct() {
      const params = {
        projectId: this.projectId
      };
      const res = await customApi.queryProductList(params);
      if (res.code !== 0) return [];
      return this._.get(res, "data", []);
    },
    findDataByField(field) {
      const { propertyLabel, type, enumLabel } = field;
      let showText,
        value = this.inputData_in[propertyLabel],
        obj,
        enumsArr;
      if (value || value == 0) {
        switch (type) {
          case "string":
          case "textarea":
            showText = value;
            break;
          case "numberInt":
            showText = value.toFixed2(0);
            break;
          case "numberFloat":
            showText = value.toFixed2(2);
            break;
          case "latitude":
          case "longitude":
            showText = value;
            break;
          case "datePicker":
            showText = this.$moment(value).format("YYYY-MM-DD");
            break;
          case "boolean":
            showText = value ? $T("是") : $T("否");
            break;
          case "enums":
            enumsArr = this.enumerations[enumLabel] || [];
            obj = enumsArr.find(i => i.id === value);
            showText = obj?.text;
            break;
          case "rangeSelection":
            showText = "rangeSelection";
            break;
          case "energytype":
            obj = this.customEnums.energytype.find(i => i.energytype === value);
            showText = obj?.name;
            break;
          case "deviceclassification":
            obj = this.customEnums.deviceclassification.find(
              i => i.id === value
            );
            showText = obj?.name;
            break;
          case "busbarseg":
            obj = this.customEnums.busbarseg.find(i => i.id === value);
            showText = obj?.name;
            break;
          case "product":
            obj = this.customEnums.product.find(i => i.producttype === value);
            showText = obj?.productTypeName;
            break;

          default:
            break;
        }
      }
      if (!showText && showText !== 0) {
        return "--";
      } else {
        return showText;
      }
    },
    importDocument: function () {
      const documentPath = this._.get(
        this.inputData_in,
        this.documentField.propertyLabel
      );
      if (!documentPath) {
        return;
      }
      var url = "/eem-service/v1/common/downloadFile?path=" + documentPath;
      common.downExcelGET(url, {}, this.token, this.projectId);
    },
    //获取关联用能设备列表
    async getDeviceList() {
      const params = {
        subLayerConditions: [
          {
            modelLabel: "manuequipment"
          }
        ],
        rootLabel: "building",
        rootID: this.inputData_in.id,
        treeReturnEnable: true
      };
      const res = await customApi.getNodeTree(params);
      this.CetTable_1.data = this._.get(res, "data[0].children", []) || [];
    },
    formatNumFloat(val) {
      if (!val && val !== 0) return "--";
      return val.toFixed2(2);
    }
  }
};
</script>
<style lang="scss" scoped>
.CetDialog {
  .cont-title {
    font-weight: bold;
    @include margin_left(J3);
  }
  .rowBox {
    @include padding_left(J4);
    @include padding_right(J4);
    @include padding_bottom(J3);
  }
  .detail-label {
    @include font_color(T3);
    line-height: 1;
  }
  .value {
    line-height: 1.5;
  }
  .img {
    height: 80px;
    width: 80px;
  }
  .dcm-btn-class-label {
    padding-right: 0px;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 150px;
    display: inline-block;
  }
}
</style>
