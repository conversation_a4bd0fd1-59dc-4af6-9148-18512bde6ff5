<template>
  <div class="fullheight">
    <div class="head">
      <div class="title">{{ $T("多机运行天数分析") }}</div>
    </div>
    <div style="height: calc(100% - 40px)" class="flex-column">
      <div class="legend-box mbJ1">
        <div class="legend-item">
          <div class="legend-line" :style="{ background: colorList[8] }"></div>
          <div class="fs14">{{ $T("综合单耗") }}</div>
        </div>
        <div
          v-for="(item, index) in legendData"
          :key="index"
          class="legend-item"
        >
          <div class="legend-bar" :style="{ background: item.color }"></div>
          <div class="fs14">{{ item.name }}</div>
        </div>
      </div>
      <CetChart class="flex1" v-bind="CetChart_analysis"></CetChart>
    </div>
  </div>
</template>

<script>
const colorLight = [
  "#29b061",
  "#ffc24c",
  "#7eb2ee",
  "#2b71c3",
  "#7fd0a0",
  "#ffe7b7",
  "#b38eca",
  "#d8c2e5",
  "#6bc1c3"
];
const colorDark = [
  "#0d86ff",
  "#19e9e9",
  "#26ec86",
  "#2485c0",
  "#8680f6",
  "#5cbbeb",
  "#659ded",
  "#e8e392",
  "#ffc83a"
];
import omegaTheme from "@omega/theme";
const colorList = omegaTheme.theme === "light" ? colorLight : colorDark;
export default {
  name: "runningDays",
  components: {},
  props: {
    runningDaysData: {
      type: Array
    }
  },
  data() {
    return {
      legendData: [],
      colorList: omegaTheme.theme === "light" ? colorLight : colorDark,
      CetChart_analysis: {
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            },
            confine: true
          },
          grid: {
            top: 30,
            bottom: 10,
            left: 40,
            right: 70,
            containLabel: true
          },
          legend: {
            show: false
          },
          xAxis: {
            type: "category",
            axisTick: { show: false },
            axisLabel: { show: false }
          },
          yAxis: [
            {
              name: $T("单位(天)"),
              type: "value"
            },
            {
              name: $T("综合单耗(kWh/万立方米)"),
              type: "value"
            }
          ],
          series: [
            {
              name: $T("综合单耗"),
              type: "line",
              yAxisIndex: 1,
              smooth: false,
              showSymbol: true,
              color: colorList[8],
              encode: {
                x: "compressorUnitName",
                y: "unitConsumption"
              }
            },
            {
              name: $T("运行天数"),
              type: "bar",
              yAxisIndex: 0,
              barMaxWidth: "20",
              colorBy: "data",
              encode: {
                x: "compressorUnitName",
                y: "runningDays"
              },
              color: colorList,
              label: {
                show: true,
                position: "top",
                lineHeight: 25,
                backgroundColor: "inherit",
                width: 25,
                height: 25,
                borderRadius: 50
              }
            }
          ]
        }
      }
    };
  },
  watch: {
    runningDaysData(val) {
      this.renderChart();
    }
  },
  methods: {
    renderChart() {
      const list = this.runningDaysData || [];
      this.CetChart_analysis.inputData_in = _.cloneDeep(list);
      const legendData = list.map((item, index) => {
        return {
          name: item.compressorUnitName,
          color: colorList[index]
        };
      });
      this.legendData = legendData;
    }
  },
  activated() {
    this.renderChart();
  }
};
</script>

<style lang="scss" scoped>
.head {
  height: 32px;
  line-height: 32px;
  margin-bottom: 8px;
  .title {
    font-size: 16px;
    font-weight: bold;
  }
}
.legend-box {
  width: 100%;
  padding: 0 40px;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  z-index: 11;
  gap: 8px;
  justify-content: center;
  .legend-item {
    display: flex;
    height: 16px;
    align-items: center;
  }
  .legend-bar {
    width: 25px;
    height: 14px;
    border-radius: 3px;
    margin-right: 4px;
  }
  .legend-line {
    height: 3px;
    width: 25px;
    margin-right: 4px;
  }
}
.flex1 {
  flex: 1;
}
</style>
