<template>
  <div class="page">
    <omega-player
      controls
      ref="play"
      style="height: 100%; width: 100%"
    ></omega-player>
  </div>
</template>

<script>
export default {
  name: "videoPlayers",
  components: {},
  props: {
    videoInfo: {
      type: Object
    }
  },
  data() {
    return {};
  },
  watch: {
    videoInfo: {
      deep: true,
      immediate: true,
      handler(val, old) {
        setTimeout(() => {
          this.$refs.play.loadSourceOrID(val.id);
        }, 0);
      }
    }
  },
  methods: {
    destroyVideo() {
      this.$refs.play.clear();
    }
  },
  deactivated() {
    console.log("deactivated");
    this.destroyVideo();
  },
  //离开页面时，销毁播放器，防止一直请求视频流
  beforeDestroy() {
    console.log("beforeDestroy");
    this.destroyVideo();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
