<template>
  <div>
    <CetDialog
      v-bind="CetDialog_add"
      v-on="CetDialog_add.event"
      class="el_dialog small"
    >
      <div>
        <CetForm
          ref="createPlanForm"
          :data.sync="CetForm_1.inputData_in"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
          class="eem-cont"
          label-position="top"
        >
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="$T('维保目标类型')" prop="nodelabel">
                <ElSelect
                  v-model="CetForm_1.inputData_in.nodelabel"
                  v-bind="ElSelect_nodelabel"
                  v-on="ElSelect_nodelabel.event"
                >
                  <ElOption
                    v-for="item in objectLabelIdList"
                    :key="item[ElOption_nodelabel.key]"
                    :label="item[ElOption_nodelabel.label]"
                    :value="item[ElOption_nodelabel.value]"
                    :disabled="item[ElOption_nodelabel.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :label="$T('型号')"
                prop="model"
                v-if="
                  CetForm_1.inputData_in.nodelabel !== 'room' &&
                  CetForm_1.inputData_in.nodelabel !== 'manuequipment'
                "
              >
                <ElSelect
                  v-model="CetForm_1.inputData_in.model"
                  v-bind="ElSelect_model"
                  v-on="ElSelect_model.event"
                >
                  <ElOption
                    v-for="item in ElOption_model.options_in"
                    :key="item[ElOption_model.key]"
                    :label="item[ElOption_model.label]"
                    :value="item[ElOption_model.value]"
                    :disabled="item[ElOption_model.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('维保目标')" prop="deviceListName">
                <div class="custom-btn" @click="OpenwbDevice">
                  <span>
                    {{ CetForm_1.inputData_in.deviceListName }}
                  </span>
                  <span class="toConnect">
                    <omega-icon symbolId="link-lin" />
                  </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('维保项目')" prop="project">
                <div class="custom-btn" @click="handleProject">
                  <span>
                    {{ CetForm_1.inputData_in.project }}
                  </span>
                  <span class="toConnect">
                    <omega-icon symbolId="link-lin" />
                  </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('计划维保时间')" prop="executetimeplan">
                <el-date-picker
                  popper-class="custom-date-picker-inspect"
                  v-model="CetForm_1.inputData_in.executetimeplan"
                  type="datetime"
                  value-format="timestamp"
                  :editable="true"
                  :pickerOptions="pickerOptions11"
                  :placeholder="$T('选择日期')"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('预计耗时')" prop="timeconsume">
                <ElInputNumber
                  v-model.trim="CetForm_1.inputData_in.timeconsume"
                  v-bind="ElInputNumber_2"
                  v-on="ElInputNumber_2.event"
                ></ElInputNumber>
                <span class="form-item-unit">h</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('责任班组')" prop="teamid">
                <ElSelect
                  v-model="CetForm_1.inputData_in.teamid"
                  v-bind="ElSelect_team"
                  v-on="ElSelect_team.event"
                >
                  <ElOption
                    v-for="item in ElOption_team.options_in"
                    :key="item[ElOption_team.key]"
                    :label="item[ElOption_team.label]"
                    :value="item[ElOption_team.value]"
                    :disabled="item[ElOption_team.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('等级')" prop="tasklevel">
                <ElSelect
                  v-model="CetForm_1.inputData_in.tasklevel"
                  v-bind="ElSelect_tasklevel"
                  v-on="ElSelect_tasklevel.event"
                >
                  <ElOption
                    v-for="item in ElOption_tasklevel.options_in"
                    :key="item[ElOption_tasklevel.key]"
                    :label="item[ElOption_tasklevel.label]"
                    :value="item[ElOption_tasklevel.value]"
                    :disabled="item[ElOption_tasklevel.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('人员数量')" prop="personNumber">
                <ElInputNumber
                  v-model.trim="CetForm_1.inputData_in.personNumber"
                  v-bind="ElInputNumber_1"
                  v-on="ElInputNumber_1.event"
                ></ElInputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$T('安全措施说明')" prop="safetymeasure">
                <ElInput
                  v-model.trim="CetForm_1.inputData_in.safetymeasure"
                  v-bind="ElInput_describe"
                  v-on="ElInput_describe.event"
                ></ElInput>
              </el-form-item>
            </el-col>
          </el-row>
        </CetForm>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
    <!-- 维保项目弹窗 -->
    <projectDialog v-bind="projectDialog" v-on="projectDialog.event" />
    <DeviceList v-bind="DeviceList" v-on="DeviceList.event"></DeviceList>
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom.js";
import projectDialog from "./projectDailog";
import DeviceList from "./DeviceList";

const ONE_MINUTE_MILLISECONDS = 60 * 1000;
const ONE_HOUR_MILLISECONDS = 60 * ONE_MINUTE_MILLISECONDS;

export default {
  name: "createPlan",
  components: {
    projectDialog,
    DeviceList
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.localStorage) {
          return false;
        } else {
          var storage = window.localStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    },
    userInfo() {
      var vm = this;
      return vm.$store.state.userInfo;
    }
  },
  data() {
    return {
      objectLabelIdList: [], // 维保目标类型列表
      CetDialog_add: {
        title: $T("新增维保工单"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        width: "550px",
        showClose: true,
        "modal-append-to-body": false
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "backendInterface", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "createMaintenanceWorkOrder",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        rules: {
          nodelabel: [
            {
              required: true,
              message: $T("请选择维保目标类型"),
              trigger: ["blur", "change"]
            }
          ],
          model: [
            {
              required: true,
              message: $T("请选择型号"),
              trigger: ["blur", "change"]
            }
          ],
          project: [
            {
              required: true,
              message: $T("请选择维保项目"),
              trigger: ["blur", "change"]
            }
          ],
          personNumber: [
            {
              required: true,
              message: $T("请输入人员数量"),
              trigger: ["blur"]
            }
          ],
          executetimeplan: [
            {
              required: true,
              message: $T("请选择执行时间"),
              trigger: ["blur", "change"]
            },
            {
              required: true,
              trigger: "blur",
              validator: (rule, value, callback) => {
                const now = new Date().getTime();
                if (value < now + 15 * ONE_MINUTE_MILLISECONDS) {
                  callback(
                    new Error($T("首次执行时间不能小于当前时间+15分钟"))
                  );
                  return;
                }
                callback();
              }
            }
          ],
          timeconsume: [
            {
              required: true,
              message: $T("请输入预计耗时"),
              trigger: ["blur"]
            }
          ],
          teamid: [
            {
              required: true,
              message: $T("请选择责任班组"),
              trigger: ["blur", "change"]
            }
          ],
          deviceListName: [
            {
              required: true,
              message: $T("请选择维保目标"),
              trigger: ["blur", "change"]
            }
          ],
          tasklevel: [
            {
              required: true,
              message: $T("请选择等级"),
              trigger: ["blur", "change"]
            }
          ],
          safetymeasure: [
            {
              required: true,
              message: $T("请输入安全措施说明"),
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out,
          finishTrigger_out: this.CetForm_1_finishTrigger_out
        }
      },
      // 维保目标类型
      ElSelect_nodelabel: {
        value: "",
        style: {
          width: "100%"
        },
        filterable: true,
        event: {
          change: this.ElSelect_nodelabel_change_out
        }
      },
      ElOption_nodelabel: {
        options_in: [],
        key: "propertyLabel",
        value: "propertyLabel",
        label: "text",
        disabled: "disabled"
      },
      // 型号组件
      ElSelect_model: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_model_change_out
        }
      },
      ElOption_model: {
        options_in: [],
        key: "label",
        value: "label",
        label: "label",
        disabled: "disabled"
      },
      // project组件
      ElInput_project: {
        value: "",
        readonly: true,
        style: {
          width: "100%"
        }
      },
      ElInput_1: {
        value: "",
        style: {},
        placeholder: $T("请输入内容"),
        event: {}
      },
      ElInput_describe: {
        value: "",
        placeholder: $T("请输入内容"),
        type: "textarea",
        resize: "none",
        rows: 5,
        style: {
          width: "100%"
        }
      },
      ElInputNumber_1: {
        ...common.check_numberInt,
        value: null,
        placeholder: $T("请输入内容"),
        style: {
          width: "100%"
        },
        controls: false,
        event: {}
      },
      ElInputNumber_2: {
        ...common.check_numberFloat,
        min: 0.01,
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      pickerOptions11: common.pickerOptions_laterThanYesterd11,
      //责任班组
      ElSelect_team: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_team: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // tasklevel组件
      ElSelect_tasklevel: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      // tasklevel组件
      ElOption_tasklevel: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // 维保项目
      projectDialog: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        tableData: [],
        event: {
          saveData_out: this.saveMaintenanceItems,
          closeDialog: this.CetButton_cancel_statusTrigger_out
        }
      },
      // 巡检对象弹窗
      DeviceList: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        tableData: null,
        dialogTitle: $T("选择查看节点"),
        event: {
          saveData_out: this.deviceList_saveData_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;

      this.ElOption_tasklevel.options_in =
        this.$store.state.enumerations.worksheettasklevel || [];
      this.objectLabelIdList = [
        ...this.$store.state.enumerations.deviceclass,
        {
          propertyLabel: "manuequipment",
          text: $T("用能设备")
        },
        {
          propertyLabel: "room",
          text: $T("房间")
        }
      ];

      Promise.all([
        new Promise((resolve, reject) => {
          this.getTeam_out(resolve);
        })
      ]).then(res => {
        vm.CetDialog_add.openTrigger_in = this._.cloneDeep(val);
        this.$nextTick(() => {
          this.CetForm_1.inputData_in = {};
          this.projectDialog.tableData = [];
          this.DeviceList.tableData = [];
          this.CetForm_1.resetTrigger_in = new Date().getTime();
          this.ElOption_model.options_in = [];
          this.$set(this.CetForm_1.inputData_in, "model", "");
        });
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    }
  },

  methods: {
    ElSelect_nodelabel_change_out(val) {
      // 类型改变，置空型号、维保目标、维保项目
      this.CetForm_1.inputData_in.model = "";
      this.$set(this.CetForm_1.inputData_in, "deviceListName", "");
      this.$set(this.CetForm_1.inputData_in, "project", "");
      this.DeviceList.tableData = [];
      this.projectDialog.tableData = [];
      if (val === "room" || val === "manuequipment") return;
      customApi.querySparePartsModel(val).then(res => {
        if (res.code === 0) {
          const arr = [];
          res.data.forEach(item => {
            arr.push({ label: item });
          });
          this.ElOption_model.options_in = arr;
        }
      });
    },
    // 型号下拉选择
    ElSelect_model_change_out() {
      this.$set(this.CetForm_1.inputData_in, "deviceListName", "");
      this.$set(this.CetForm_1.inputData_in, "project", "");
      this.DeviceList.tableData = [];
      this.projectDialog.tableData = [];
    },
    // 维保项目
    handleProject() {
      if (!this.CetForm_1.inputData_in.deviceListName) {
        return this.$message.warning($T("请先选择维保目标!"));
      }
      this.projectDialog.inputData_in = {
        nodelabel: this.CetForm_1.inputData_in.nodelabel, // 设备类型
        model: this.CetForm_1.inputData_in.model // 型号
      };
      this.projectDialog.openTrigger_in = new Date().getTime();
    },
    // 保存维保项目
    saveMaintenanceItems(val, num) {
      if (val && val.length) {
        this.$set(
          this.CetForm_1.inputData_in,
          "project",
          $T("已选择{0}项", num)
        );
      } else {
        this.$set(this.CetForm_1.inputData_in, "project", "");
      }
      this.CetForm_1.inputData_in.maintenanceextend = {
        maintenanceItems: this._.cloneDeep(val)
      };
      this.projectDialog.tableData = this._.cloneDeep(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.inputData_in.timeconsumeplan =
        this.CetForm_1.inputData_in.timeconsume * ONE_HOUR_MILLISECONDS;
      // this.CetForm_1.inputData_in.tenantId = this.userInfo.tenantId;
      this.CetForm_1.saveTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_saveData_out(val) {
      this.$emit("confirm_out", this._.cloneDeep(val));
    },
    CetForm_1_finishTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
      this.$emit("confirm_out", val);
    },
    OpenwbDevice() {
      const nodelabel = this.CetForm_1.inputData_in.nodelabel;
      if (!nodelabel) {
        return this.$message.warning($T("请选择维保目标类型!"));
      }
      // 房间、用能设备不需要型号
      if (
        nodelabel !== "room" &&
        nodelabel !== "manuequipment" &&
        !this.CetForm_1.inputData_in.model
      ) {
        return this.$message.warning($T("请选择型号!"));
      }
      this.DeviceList.inputData_in = {
        signInGroup: this.CetForm_1.inputData_in.signgroupid,
        nodelabel, // 设备类型
        model: this.CetForm_1.inputData_in.model // 型号
      };
      this.DeviceList.openTrigger_in = new Date().getTime();
    },
    init() {},
    // 巡检对象保存
    deviceList_saveData_out(val) {
      if (val && val.length) {
        this.$set(
          this.CetForm_1.inputData_in,
          "deviceListName",
          $T("已选择{0}个目标", val.length)
        );
        this.$refs.createPlanForm.$refs.cetForm.clearValidate("deviceListName");
      } else {
        this.$set(this.CetForm_1.inputData_in, "deviceListName", null);
      }

      /* this.CetForm_1.inputData_in.objectid = this._.get(val, "[0].id", null);
      this.CetForm_1.inputData_in.objectlabel = this._.get(val, "[0].modelLabel", null); */
      this.CetForm_1.inputData_in.objects = this._.cloneDeep(val);
      this.DeviceList.tableData = this._.cloneDeep(val);
    },
    // 获取班组列表信息
    getTeam_out(callback) {
      const _this = this;
      const params = {};
      customApi.queryInspectorTeam(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          _this.ElOption_team.options_in = _this._.cloneDeep(data);
          callback && callback();
        }
      });
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.custom-btn {
  position: relative;
  padding-left: 15px;
  height: 32px;
  line-height: 32px;
  border-radius: 4px;
  border: 1px solid;
  @include border_color(B1);
  box-sizing: border-box;
  @include font_color(T2);
  cursor: pointer;
  @include background_color(BG4);
}
.toConnect {
  position: absolute;
  right: 2px;
  z-index: 999;
  display: inline-block;
  width: 30px;
  height: 30px;
  cursor: pointer;
  @include font_color(ZS);
  text-align: center;
}
.el_dialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
    @include border-radius(C1);
  }
}
</style>
