<template>
  <div class="eem-common fullfilled">
    <div class="fullheight eem-aside flex-column treeBox">
      <div class="common-title-H1">{{ $T("角色管理") }}</div>
      <CetTree
        class="CetTree mtJ3 flex-auto eem-no-line"
        :selectNode.sync="CetTree_Roles.selectNode"
        :checkedNodes.sync="CetTree_Roles.checkedNodes"
        :searchText_in.sync="CetTree_Roles.searchText_in"
        v-bind="CetTree_Roles"
        v-on="CetTree_Roles.event"
      ></CetTree>
      <div class="clearfix">
        <CetButton
          class="mtJ3 fr"
          v-permission="'role_create'"
          v-bind="CetButton_add"
          v-on="CetButton_add.event"
        ></CetButton>
      </div>
    </div>
    <div class="contentBox mlJ3">
      <div class="mainContent bg1">
        <div class="title clearfix">
          <div class="icon fl mtJ3"></div>
          <div class="roleInfo fl">
            <div class="roleName">
              {{ roleData_in && roleData_in.name }}
            </div>
            <div class="flex-row">
              <div :class="['userNum mtJ1 bg plJ3 prJ3 ptJ1 pbJ1', en && 'en']">
                <div class="label">{{ $T("包含的用户数(个)") }}</div>
                <el-tooltip
                  effect="light"
                  :content="userNum.toString()"
                  placement="bottom"
                >
                  <div class="value text-ellipsis">
                    {{ userNum }}
                  </div>
                </el-tooltip>
              </div>
              <div
                :class="[
                  'userNum mtJ1 bg plJ3 prJ3 ptJ1 pbJ1 mlJ3',
                  en && 'en'
                ]"
              >
                <div class="label">{{ $T("角色首页") }}</div>
                <el-tooltip
                  effect="light"
                  :content="homepageName"
                  placement="bottom"
                >
                  <div class="value text-ellipsis">
                    {{ homepageName }}
                  </div>
                </el-tooltip>
              </div>
            </div>
          </div>
          <CetButton
            class="mlJ1 fr"
            v-permission="'role_update'"
            v-bind="CetButton_edit"
            @click.stop="editRole"
          ></CetButton>
          <CetButton
            class="mlJ1 fr delete"
            v-permission="'role_delete'"
            v-bind="CetButton_del"
            @click.stop="deleteRole(roleData_in)"
          ></CetButton>
        </div>
      </div>
      <div class="tabs bg1">
        <el-tabs v-model="activeName" class="eem-tabs-custom mlJ1">
          <el-tab-pane
            :label="$T('页面操作权限')"
            name="permissions"
          ></el-tab-pane>
          <el-tab-pane :label="$T('页面访问权限')" name="pages"></el-tab-pane>
          <el-tab-pane
            v-if="appMenuPermission"
            :label="$T('移动端页面访问权限')"
            name="app"
          ></el-tab-pane>
          <el-tab-pane :label="$T('角色包含的用户')" name="users"></el-tab-pane>
        </el-tabs>
      </div>

      <div class="detailContent mtJ3 eem-cont flex-auto" v-if="roleData_in">
        <div class="fullfilled">
          <RoleDetailContainer
            :activeName_in="activeName"
            :roleData_in="RoleDetailContainer.roleData_in"
            :appMenus="RoleDetailContainer.appMenus"
            :refreshTrigger_in="RoleDetailContainer.refreshTrigger_in"
            :appMenuPermission="appMenuPermission"
            @userNum_out="detail_userNum_out"
          ></RoleDetailContainer>
        </div>
      </div>
    </div>
    <AddOrEditRoleDialog
      :roleData_in="AddOrEditRoleDialog.roleData_in"
      :appMenus="AddOrEditRoleDialog.appMenus"
      :isEditMode="AddOrEditRoleDialog.isEditMode"
      :openTrigger_in="AddOrEditRoleDialog.openTrigger_in"
      :appMenuPermission="appMenuPermission"
      @roleChanged_out="refresh"
    ></AddOrEditRoleDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import AddOrEditRoleDialog from "./basecomponents/AddOrEditRoleDialog";
import RoleDetailContainer from "./subcomponents/RoleDetailContainer.vue";
import { find } from "eem-utils/util";
export default {
  components: { AddOrEditRoleDialog, RoleDetailContainer },
  data() {
    return {
      userNum: 0,
      roleData_in: {},
      CetTree_Roles: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        nodeKey: "id",
        filterNodesKey: "filterNode_roles",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        event: {
          currentNode_out: this.CetTree_Roles_currentNode_out
        }
      },

      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("新增角色"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      CetButton_edit: {
        visible_in: true,
        disable_in: true,
        title: $T("修改"),
        type: "primary",
        plain: false,
        event: {}
      },
      CetButton_del: {
        visible_in: true,
        disable_in: true,
        title: $T("删除"),
        plain: true,
        type: "danger",
        event: {}
      },
      activeName: "permissions",
      // 新增或编辑用户组弹窗
      AddOrEditRoleDialog: {
        appMenus: [], // app所有菜单
        isEditMode: false,
        roleData_in: null,
        openTrigger_in: new Date().getTime()
      },
      RoleDetailContainer: {
        appMenus: [], // app所有菜单
        roleData_in: null,
        refreshTrigger_in: new Date().getTime()
      },
      appMenuPermission: false // 是否需要移动端页面访问权限
    };
  },
  computed: {
    tenantId() {
      if (this.$route.name === "platformurolemanage") {
        // 平台用户管理就取当前用户的租户id
        return this.$store.state.userInfo.tenantId;
      } else {
        return this.$store.state.projectTenantId;
      }
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    homepageName() {
      if (!this.roleData_in || !this.roleData_in.customConfig) {
        return "--";
      }
      const pageObj = JSON.parse(this.roleData_in.customConfig);
      const homePagePageNodeId =
        this._.get(pageObj, "homePagePageNodeId") ||
        this._.get(pageObj, "homepage");
      if (!homePagePageNodeId) {
        return "--";
      }

      const allNavmenu = this.navmenu;
      let item = find(allNavmenu, homePagePageNodeId, {
        childKey: "subMenuList",
        valueKey: "permission"
      });
      return item ? item.label : "--";
    },
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    },
    navmenu() {
      return this.$store.state.initialInfo.menuList;
    }
  },
  methods: {
    // 角色树组件输出
    CetTree_Roles_currentNode_out(val) {
      this.activeName = "permissions";
      this.RoleDetailContainer.roleData_in = val;
      this.roleData_in = val;
      this.RoleDetailContainer.refreshTrigger_in = new Date().getTime();
      this.CetButton_edit.disable_in = val ? false : true;
    },
    CetButton_add_statusTrigger_out() {
      this.AddOrEditRoleDialog.roleData_in = null;
      this.AddOrEditRoleDialog.isEditMode = false;
      this.AddOrEditRoleDialog.openTrigger_in = new Date().getTime();
    },

    initRoleList(val) {
      const treeData = val || [];
      treeData.sort((a, b) => {
        return a.id - b.id;
      });

      this.CetTree_Roles.inputData_in = treeData;
      if (!this._.get(this.CetTree_Roles, "selectNode.id")) {
        this.CetTree_Roles.selectNode = this._.get(treeData, "[0]", null);
      }
    },
    // 编辑用户
    editRole(roleData) {
      if (!roleData) {
        return;
      }
      this.AddOrEditRoleDialog.roleData_in = this._.cloneDeep(this.roleData_in);
      this.AddOrEditRoleDialog.isEditMode = true;
      this.AddOrEditRoleDialog.openTrigger_in = new Date().getTime();
    },
    // 删除用户
    deleteRole(roleData) {
      if (!roleData) {
        return;
      }
      this.$confirm($T("确定要删除所选角色吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          customApi
            .deleteRole({
              id: roleData.id,
              name: roleData.name,
              tenantId: roleData.tenantId
            })
            .then(() => {
              this.refresh();
            });
        })
        .catch(() => {});
    },
    // 刷新数据
    async refresh() {
      this.getAppMenus();
      this.activeName = "permissions";
      let res;
      if (this.$route.name === "platformurolemanage") {
        res = await customApi.getRoles({ tenantId: this.tenantId });
      } else {
        const params = {
          projectId: this.projectId,
          tenantId: this.tenantId
        };
        res = await customApi.getRolesList(params);
      }
      if (res.code === 0) {
        this.initRoleList(res.data);
      }
    },
    detail_userNum_out(num) {
      this.userNum = num;
      this.CetButton_del.disable_in = !!num;
    },
    // 获取app菜单列表
    getAppMenus() {
      customApi.queryAppMenus().then(res => {
        if (res.code === 0) {
          const menus = res.data.children || [];
          // 返回enable为true的才显示菜单
          const appMenus = menus.filter(item => item.enable);
          this.AddOrEditRoleDialog.appMenus = appMenus;
          this.RoleDetailContainer.appMenus = appMenus;
          this.appMenuPermission = res.data.filter;
        }
      });
    }
  },
  activated() {
    this.CetTree_Roles.selectNode = null;
    this.refresh();
  }
};
</script>
<style lang="scss" scoped>
.eem-common {
  display: flex;
  .treeBox {
    width: 316px;
    box-sizing: border-box;
  }
  .contentBox {
    flex: 1;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    min-width: 0;
    .mainContent {
      border-radius: mh-get(C) mh-get(C) 0 0;
      @include padding(J3 J3 J4 J4);
      .title {
        .icon {
          height: mh-get(I4);
          width: mh-get(I4);
          background: url("./assets/avatar.png") no-repeat center center;
        }
        .roleInfo {
          @include margin_left(J3);
          .roleName {
            @include font_size(H1);
            @include line_height(Hm);
            font-weight: bold;
          }
          .userNum {
            @include border_radius(C);
            width: 160px;
            &.en {
              width: 180px;
            }
            .label {
              @include font_color(T3);
            }
            .value {
              @include font_size(H3);
              font-weight: bold;
              max-width: 100%;
              display: inline-block;
            }
          }
        }
      }
      &.borderRadiusC {
        @include border_radius(C);
      }
    }
    .tabs {
      border-radius: 0 0 mh-get(C) mh-get(C);
    }
    .detailContent {
      box-sizing: border-box;
      .CetTree {
        width: 300px;
      }
    }
  }
}
</style>
