<template>
  <div class="page eem-common">
    <el-container class="fullheight">
      <el-aside width="315px" class="eem-aside flex-column">
        <div class="mbJ3">
          <el-input
            placeholder="输入关键字以检索"
            suffix-icon="el-icon-search"
            class="device-search"
            v-model="filterText"
          ></el-input>
        </div>
        <div class="flex-auto" style="overflow: auto">
          <el-tree
            ref="powerTree"
            style="width: 100%; height: 100%"
            :data="treeData"
            node-key="tree_id"
            :props="treeProps"
            :filter-node-method="filterNode"
            :highlight-current="true"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
          ></el-tree>
        </div>
      </el-aside>
      <el-container class="fullheight mlJ3 flex-column">
        <div class="mbJ3 eem-cont">
          <CetButton
            class="fr"
            style="line-height: normal"
            v-bind="CetButton2_2"
            v-on="CetButton2_2.event"
          ></CetButton>
          <CetButton
            class="fr mrJ1"
            style="line-height: normal"
            v-bind="CetButton2_1"
            v-on="CetButton2_1.event"
          ></CetButton>
          <!-- 查询 -->
          <!-- <CetButton
                :visible_in="CetButton_query.visible_in"
                :disable_in="CetButton_query.disable_in"
                v-bind="CetButton_query.config"
                @statusTrigger_out="queryEventLog"
          ></CetButton>-->

          <div class="fr mrJ1 clearfix" style="line-height: normal">
            <div class="fr" style="width: 420px">
              <time-range :val.sync="queryTime"></time-range>
            </div>
            <div class="fr mrJ1" style="width: 200px">
              <ElInput
                v-model="ElInput_keyword.value"
                suffix-icon="el-icon-search"
                v-bind="ElInput_keyword"
                v-on="ElInput_keyword.event"
              ></ElInput>
            </div>
          </div>
        </div>
        <div class="eem-cont minWH flex-column" style="flex: 1">
          <div class="clearfix mbJ3">
            <el-button
              class="fr"
              :disabled="isBatchConfirm"
              type="primary"
              @click="showConfirmWindow(null)"
            >
              批量确认
            </el-button>
          </div>
          <div style="flex: 1" class="minWH">
            <el-table
              stripe
              :border="true"
              ref="multipleTable"
              :data="eventTableData"
              tooltip-effect="light"
              height="100%"
              style="width: 100%"
              row-key="id"
              @selection-change="handleSelectionChange"
            >
              <el-table-column
                type="selection"
                width="40"
                align="left"
                reserve-selection
                :selectable="selectInit"
              ></el-table-column>
              <el-table-column
                label="#"
                type="index"
                :index="indexMethod"
                align="left"
                width="60"
                show-overflow-tooltip
              />
              <el-table-column
                align="left"
                :formatter="formatDate"
                prop="eventtime"
                label="触发时间"
                width="190"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                align="left"
                prop="monitoredName"
                label="设备名称"
                width="250"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                align="left"
                :formatter="formatEnum"
                prop="pqvariationeventtype"
                label="事件类型"
                width="120"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                align="left"
                :formatter="formatEnum"
                prop="transientfaultdirection"
                label="故障定位"
                width="80"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                align="left"
                prop="description"
                label="事件描述"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column align="left" label="区域" width="100">
                <template slot-scope="scope">
                  <el-button
                    @click="showToleranceWindow(scope.row)"
                    v-if="
                      scope.row.toleranceband &&
                      scope.row.toleranceband >= 2 &&
                      scope.row.toleranceband <= 4
                    "
                    type="text"
                    size="small"
                  >
                    {{
                      formatEnum(
                        scope.row,
                        { property: "toleranceband" },
                        scope.row.toleranceband
                      )
                    }}
                  </el-button>
                  <span v-else>
                    {{
                      formatEnum(
                        scope.row,
                        { property: "toleranceband" },
                        scope.row.toleranceband
                      )
                    }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column align="left" label="关联波形" width="80">
                <template slot-scope="scope">
                  <span
                    class="clickformore"
                    v-if="scope.row.waveformlogtime"
                    @click="showWaveWindow(scope.row)"
                  >
                    查看
                  </span>
                  <span v-else>无</span>
                </template>
              </el-table-column>
              <el-table-column
                align="left"
                prop="confirmeventstatus"
                label="状态"
                width="100"
              >
                <template slot-scope="scope">
                  <span
                    :class="getEventStatus(scope.row.confirmeventstatus)"
                    @click="
                      showConfirmWindow(scope.row, scope.row.confirmeventstatus)
                    "
                  >
                    {{
                      formatEnum(
                        scope.row,
                        { property: "confirmeventstatus" },
                        scope.row.confirmeventstatus
                      )
                    }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column align="left" label="操作" width="80">
                <template slot-scope="scope">
                  <span
                    class="clickformore"
                    @click="showEventDetail(scope.row)"
                  >
                    详情
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="mtJ3">
            <div class="block fr mrJ3">
              <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="currentPage"
                :page-sizes="[20, 50, 100, 200]"
                :page-size.sync="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="eventTotal"
              ></el-pagination>
            </div>
          </div>
        </div>
      </el-container>
    </el-container>
    <!-- 事件详情 -->
    <el-dialog
      :show-close="true"
      title="事件详情"
      class="detail-dialog"
      :visible.sync="isShowEventDetailWindow"
    >
      <PQEventDetail
        :ENUM="ENUM"
        @closePQEventDetail="closePQEventDetail"
        :eventLog="selectedLog"
      ></PQEventDetail>
      <span slot="footer">
        <el-button
          size="small"
          plain
          type="primary"
          @click="closePQEventDetail"
        >
          关闭
        </el-button>
      </span>
    </el-dialog>
    <!-- 容忍度详情 -->
    <el-dialog
      title="ITIC曲线"
      :show-close="true"
      :visible.sync="isShowToleranceWindow"
      width="80%"
    >
      <tolerance-widget
        class="eem-cont-c1"
        :ENUM="ENUM"
        @closeToleranceWindow="closeToleranceWindow"
        :eventLog="selectedLog"
      ></tolerance-widget>
      <span slot="footer">
        <el-button
          size="small"
          plain
          type="primary"
          @click="closeToleranceWindow"
        >
          关闭
        </el-button>
      </span>
    </el-dialog>
    <!-- 波形展示弹窗 -->
    <el-dialog
      :visible.sync="isShowWaveWindow"
      title="波形"
      width="80%"
      @opened="updateWave"
      @closed="clearWave"
    >
      <Wave
        class="eem-cont-c1"
        :showTitle="true"
        :title="_.get(clickedEventLog, 'monitoredName', '--')"
        :showTable="true"
        :data="clickedEventLog"
      ></Wave>
      <span slot="footer">
        <el-button
          size="small"
          plain
          type="primary"
          @click="
            () => {
              this.isShowWaveWindow = false;
            }
          "
        >
          关闭
        </el-button>
      </span>
    </el-dialog>
    <!-- 事件确认弹窗 -->
    <el-dialog
      :show-close="true"
      title="事件确认"
      :close-on-click-modal="false"
      :visible.sync="isShowConfirmWindow"
    >
      <event-confirm
        class="eem-cont-c1"
        ref="eventConfirm"
        @afterEventConfirmed="afterEventConfirmed"
        :confirmData="confirmData"
      ></event-confirm>
      <span slot="footer">
        <span class="device-Button">
          <el-button
            size="small"
            plain
            type="primary"
            @click="afterEventConfirmed"
          >
            关闭
          </el-button>
        </span>

        <span class="device-Button">
          <el-button
            size="small"
            type="primary"
            @click="
              () => {
                this.$refs.eventConfirm.saveConfirmData();
              }
            "
          >
            确定
          </el-button>
        </span>
      </span>
    </el-dialog>
    <!-- 高级查询弹窗 -->
    <el-dialog
      title="高级查询"
      :visible.sync="isShowAdvancedQueryWindow"
      :show-close="true"
    >
      <el-card class="mbJ1" shadow="never">
        <div slot="header" class="clearfix">
          <span>事件类型</span>
          <el-checkbox
            class="fr"
            :indeterminate="eventTypeGroup.isIndeterminate"
            v-model="eventTypeGroup.checkAll"
            @change="val => handleCheckAll(val, 'eventTypeGroup')"
          >
            全选
          </el-checkbox>
        </div>
        <el-checkbox-group
          v-model="eventTypeGroup.checked"
          @change="val => handleCheckedChange(val, 'eventTypeGroup')"
        >
          <el-checkbox
            style="width: 50%"
            class="m0 mb5"
            v-for="item in eventTypeGroup.enum"
            :label="item.id"
            :key="item.id"
          >
            {{ item.text }}
          </el-checkbox>
        </el-checkbox-group>
      </el-card>
      <el-card class="mbJ1" shadow="never">
        <div slot="header" class="clearfix">
          <span>区域</span>
          <el-checkbox
            class="fr"
            :indeterminate="tolerancebandGroup.isIndeterminate"
            v-model="tolerancebandGroup.checkAll"
            @change="val => handleCheckAll(val, 'tolerancebandGroup')"
          >
            全选
          </el-checkbox>
        </div>
        <el-checkbox-group
          v-model="tolerancebandGroup.checked"
          @change="val => handleCheckedChange(val, 'tolerancebandGroup')"
        >
          <el-checkbox
            style="width: 50%"
            class="m0 mb5"
            v-for="item in tolerancebandGroup.enum"
            :label="item.id"
            :key="item.id"
          >
            {{ item.text }}
          </el-checkbox>
        </el-checkbox-group>
      </el-card>
      <el-card shadow="never">
        <div slot="header" class="clearfix">
          <span>故障方向</span>
          <el-checkbox
            class="fr"
            :indeterminate="faultDirectionGroup.isIndeterminate"
            v-model="faultDirectionGroup.checkAll"
            @change="val => handleCheckAll(val, 'faultDirectionGroup')"
          >
            全选
          </el-checkbox>
        </div>
        <el-checkbox-group
          v-model="faultDirectionGroup.checked"
          @change="val => handleCheckedChange(val, 'faultDirectionGroup')"
        >
          <el-checkbox
            style="width: 50%"
            class="m0 mb5"
            v-for="item in faultDirectionGroup.enum"
            :label="item.id"
            :key="item.id"
          >
            {{ item.text }}
          </el-checkbox>
        </el-checkbox-group>
      </el-card>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </el-dialog>
    <!-- <el-container
        v-show="_.get(currentNode,'level',1)===1"
        style="height:100%;padding:50px 16px 11px 12px"
      >
        <p class="text-center w100 fs20 info">请选择监测设备节点或被监测设备节点</p>
    </el-container>-->
  </div>
</template>
<script>
import commonApi from "@/api/custom.js";
import common from "eem-utils/common";
import ELECTRICAL_DEVICE from "@/store/electricaldevice";
import TimeRange from "eem-components/TimeRange";
import { COMMON } from "@/store/constQuantity";

import Wave from "eem-components/Wave.vue";
import EventConfirm from "./widget/EventConfirm";
import ToleranceWidget from "./widget/ToleranceWidget";
import PQEventDetail from "./widget/PQEventDetail.vue";

export default {
  name: "VoltageVariation",
  components: {
    Wave,
    TimeRange,
    PQEventDetail,
    EventConfirm,
    ToleranceWidget
  },
  props: {
    selectedMenu: {
      type: String
    },
    wrapTime: {
      type: Object
    }
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    token() {
      return this.$store.state.token;
    }
  },

  data() {
    return {
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "确定",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.queryDialogConfirm
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        plain: true,
        type: "primary",
        event: {
          statusTrigger_out: this.queryDialogCancel
        }
      },
      treeData: [],
      treeProps: {
        children: "children",
        label: "name",
        isLeaf: "leaf"
      },
      filterText: "",
      currentNode: null,
      // keyword组件
      ElInput_keyword: {
        value: "",
        placeholder: "请输入内容",
        type: "text",
        size: "small",
        rows: 2,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElInput_keyword_change_out,
          input: this.ElInput_keyword_input_out
        }
      },
      // 1组件
      CetButton2_1: {
        visible_in: true,
        disable_in: false,
        title: "高级查询",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton2_1_statusTrigger_out
        }
      },
      // 2组件
      CetButton2_2: {
        visible_in: true,
        disable_in: false,
        title: "导出",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton2_2_statusTrigger_out
        }
      },
      selectedLog: {},
      firstVisited: true,
      // 查询按钮组件
      // CetButton_query: {
      //   visible_in: true,
      //   disable_in: false,
      //   config: {
      //     title: "查询",
      //     type: "primary",
      //     plain: true
      //   }
      // },
      eventTypeGroup: {
        checkAll: false,
        checked: [],
        enum: [],
        isIndeterminate: false
      },
      tolerancebandGroup: {
        checkAll: false,
        checked: [],
        enum: [],
        isIndeterminate: false
      },
      faultDirectionGroup: {
        checkAll: false,
        checked: [],
        enum: [],
        isIndeterminate: false
      },
      queryTime: this.wrapTime
        ? [this.wrapTime.startTime, this.wrapTime.endTime]
        : common.initDateRange(),
      isShowWaveWindow: false,
      isShowConfirmWindow: false, // 显示事件确认弹框
      isShowAdvancedQueryWindow: false,
      isShowToleranceWindow: false,
      isShowEventDetailWindow: false,
      data: [],
      eventTotal: 0,
      currentPage: 1,
      pageSize: 20,
      eventTableData: [],
      defaultProps: {
        children: "children",
        label(data) {
          return data.name;
        }
      },
      ENUM: {},
      EventDetail: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      isBatchConfirm: true,
      cachedWaveData: {},
      clickedEventLog: {},
      pqVarEventType: {},
      faultDirection: {},
      toleranceBand: {},
      dateRange: {},
      queryEventParam: {},
      selectedData: [],
      confirmData: {},
      // 用于记录高级查询中确定勾选的选项
      lastChecked: {
        eventTypeGroup: [0],
        tolerancebandGroup: [0],
        faultDirectionGroup: [0]
      }
    };
  },
  watch: {
    currentNode: {
      deep: true,
      handler: function (val, oldVal) {
        if (
          this._.get(val, "data.tree_id", -1) ===
          this._.get(oldVal, "data.tree_id", -2)
        )
          return;
        // if (this._.get(val, "level", 1) !== 1)
        this.$refs.multipleTable.clearSelection();
        this.currentPage = 1;
        this.queryEventLog();
      }
    },
    wrapTime: {
      deep: true,
      immediate: true,
      handler: function (val, oldVal) {
        if (!val) return;
        this.queryTime = [val.startTime, val.endTime];
      }
    },
    queryTime: {
      deep: true,
      handler: function (val, oldVal) {
        // if (this.selectedMenu !== "故障诊断") return;
        this.$refs.multipleTable.clearSelection();
        this.currentPage = 1;
        this.queryEventLog();
      }
    },
    filterText(val) {
      this.$refs.powerTree.filter(val);
    },
    selectedMenu: {
      deep: true,
      handler: function (val, oldVal) {
        if (val === "故障诊断") {
          this.$refs.multipleTable.clearSelection();
          this.currentPage = 1;
          this.queryEventLog();
        }
      }
    }
  },

  methods: {
    // 表格序号格式化
    indexMethod(i) {
      return (this.currentPage - 1) * this.pageSize + i + 1;
    },
    selectInit(row, index) {
      if (row.confirmeventstatus === 1) {
        return true;
      } else {
        return false;
      }
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1;
    },
    getTreeData() {
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [
          {
            filter: {
              expressions: [{ limit: 1, operator: "EQ", prop: "roomtype" }]
            },
            modelLabel: "room"
          }
        ],
        treeReturnEnable: true
      };
      ELECTRICAL_DEVICE.forEach(item => {
        data.subLayerConditions.push({ modelLabel: item.value });
      });
      commonApi.getPqNodeTree(data, this.projectId, true).then(res => {
        if (res.code === 0) {
          _this.treeData = res.data;
          if (_this._.get(res.data, "[0]")) {
            var currentNode = _this._.get(res.data, "[0]");
            _this.$nextTick(() => {
              if (
                _this._.get(_this.currentNode, "data.tree_id", 1) ===
                _this._.get(currentNode, "tree_id", 2)
              ) {
                _this.$refs.multipleTable.clearSelection();
                _this.currentPage = 1;
                _this.queryEventLog();
              }
              _this.$refs.powerTree.setCurrentKey(currentNode.tree_id);
              _this.currentNode = _this.$refs.powerTree.getNode(
                currentNode.tree_id
              );
              _this.$refs.powerTree.getNode(currentNode.tree_id).expand();
            });
          }
        }
      });
    },
    handleNodeClick(obj, node) {
      this.currentNode = node;
    },

    showEventDetail(val) {
      this.selectedLog = val;
      this.isShowEventDetailWindow = true;
    },
    closePQEventDetail(data) {
      this.isShowEventDetailWindow = false;
    },
    closeToleranceWindow(data) {
      this.isShowToleranceWindow = false;
    },
    // 点击取消按钮，还原勾选状态
    queryDialogCancel() {
      this.eventTypeGroup.checked = [...this.lastChecked.eventTypeGroup];
      this.tolerancebandGroup.checked = [
        ...this.lastChecked.tolerancebandGroup
      ];
      this.faultDirectionGroup.checked = [
        ...this.lastChecked.faultDirectionGroup
      ];
      this.handleCheckedChange(this.eventTypeGroup.checked, "eventTypeGroup");
      this.handleCheckedChange(
        this.tolerancebandGroup.checked,
        "tolerancebandGroup"
      );
      this.handleCheckedChange(
        this.faultDirectionGroup.checked,
        "faultDirectionGroup"
      );
      this.isShowAdvancedQueryWindow = false;
    },
    // 点击确定按钮，保存勾选状态，进行查询
    queryDialogConfirm() {
      this.lastChecked.eventTypeGroup = [...this.eventTypeGroup.checked];
      this.lastChecked.tolerancebandGroup = [
        ...this.tolerancebandGroup.checked
      ];
      this.lastChecked.faultDirectionGroup = [
        ...this.faultDirectionGroup.checked
      ];
      this.handleGroupCheckStatus();
      this.isShowAdvancedQueryWindow = false;
      this.queryEventLog();
    },
    // 初始化每一个复选组的勾选状态
    handleGroupCheckStatus() {
      this.handleCheckedChange(this.eventTypeGroup.checked, "eventTypeGroup");
      this.handleCheckedChange(
        this.faultDirectionGroup.checked,
        "faultDirectionGroup"
      );
      this.handleCheckedChange(
        this.tolerancebandGroup.checked,
        "tolerancebandGroup"
      );
    },
    // 是否全选
    handleCheckAll(val, type) {
      var arr = val ? this[type].enum.map(item => item.id) : [];
      this[type].checked = arr;
      this[type].isIndeterminate = false;
    },
    // 选中checkbox时，修改全选的状态 （半选或者选中或者不选），
    handleCheckedChange(val, type) {
      var checkedCount = val.length;
      this[type].checkAll = checkedCount === this[type].enum.length;
      this[type].isIndeterminate =
        checkedCount > 0 && checkedCount < this[type].enum.length;
    },
    formatDate(row, column, cellValue, index) {
      return common.formatDate(cellValue, "YYYY-MM-DD HH:mm:ss.SSS");
    },

    queryEventLog() {
      var vm = this;

      vm.collectQueryEventParam();
      common.requestData(
        {
          url: "/eem-service/v1/pq/event",
          data: vm.queryEventParam
        },
        (data, res) => {
          if (res.code === 0) {
            vm.eventTableData = data || [];
            vm.eventTotal = res.total;
          } else {
            vm.eventTableData = [];
            vm.eventTotal = 0;
          }
        }
      );
    },
    CetButton2_2_statusTrigger_out() {
      var vm = this;

      vm.collectQueryEventParam();
      common.downExcel(
        "/eem-service/v1/pq/event/export",
        vm.queryEventParam,
        vm.token
      );
    },

    // 收集事件查询的参数
    collectQueryEventParam() {
      var vm = this;

      var node = null;
      if (vm.currentNode && vm.currentNode.data) {
        node = {
          id: vm.currentNode.data.id,
          modelLabel: vm.currentNode.data.modelLabel
        };
      }

      vm.queryEventParam = {
        startTime: vm.queryTime[0],
        endTime: vm.queryTime[1],
        // 被检测设备类型
        // deviceTypes: ["linesegmentwithswitch"],
        // 事件等级
        // "eventClasses": [],
        // 高级查询中的事件类型
        eventTypes:
          vm.lastChecked.eventTypeGroup.length === 0
            ? [-1]
            : [...vm.lastChecked.eventTypeGroup],
        // 搜索关键字
        keyWord: vm.ElInput_keyword.value,
        // 电能质量类型的监测设备 必传，目前只有 9  // 2019年11月20日17:07:06
        // meterTypes: [9],
        // 选中节点树中的测点 monitoredid
        node: node,
        page: {
          index: vm.pageSize * (vm.currentPage - 1),
          limit: vm.pageSize
        },
        // 高级查询中的区域
        toleranceBands:
          vm.lastChecked.tolerancebandGroup.length === 0
            ? [-1]
            : [...vm.lastChecked.tolerancebandGroup],
        transientFaultDirections:
          vm.lastChecked.faultDirectionGroup.length === 0
            ? [-1]
            : [...vm.lastChecked.faultDirectionGroup],
        projectId: vm.projectId
      };
    },
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.queryEventLog();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.queryEventLog();
    },
    formatEnum(row, column, cellValue) {
      var vm = this;
      var data = [];
      switch (column.property) {
        case "pqvariationeventtype":
          data = vm.ENUM.pqvariationeventtype;
          break;
        case "transientfaultdirection":
          data = vm.ENUM.transientfaultdirection;
          break;
        case "toleranceband":
          data = vm.ENUM.toleranceband;
          break;
        case "confirmeventstatus":
          data = vm.ENUM.confirmeventstatus;
          break;
      }

      var text = vm._.find(data, ["id", cellValue]).text;
      if (text) return text;

      return "--";
    },
    showToleranceWindow(eventlog) {
      this.selectedLog = eventlog;
      this.isShowToleranceWindow = true;
    },
    showWaveWindow(obj) {
      var wavedata = this._.cloneDeep(obj);
      wavedata.deviceId = wavedata.srcdeviceid;
      wavedata.waveTime = wavedata.waveformlogtime;
      this.cachedWaveData = wavedata;
      this.isShowWaveWindow = true;
    },

    updateWave() {
      this.clickedEventLog = { ...this.cachedWaveData };
    },

    clearWave() {
      this.cachedWaveData = {};
      this.clickedEventLog = {};
    },

    showConfirmWindow(obj) {
      if (obj && obj.confirmeventstatus !== 1) {
        return;
      }
      this.confirmData = {
        updatetime: new Date(),
        data: obj || this.selectedData
      };
      this.isShowConfirmWindow = true;
    },
    handleSelectionChange(val) {
      this.selectedData = val;
      if (val && val.length > 0) {
        this.isBatchConfirm = false;
      } else {
        this.isBatchConfirm = true;
      }
    },
    // keyword输出,方法名要带_out后缀
    ElInput_keyword_change_out(val) {
      if (!this.currentNode) return;
      this.queryEventLog();
    },
    ElInput_keyword_input_out(val) {},
    afterEventConfirmed(data) {
      var vm = this;
      vm.isShowConfirmWindow = false;
      if (!data || !Array.isArray(data)) {
        return;
      }
      //取消表格多选
      this.selectedData.forEach(row => {
        this.$refs.multipleTable.toggleRowSelection(row);
      });
      // TODO: 此处的更新数据逻辑需要调整，暂时不清楚有什么更高效的方法
      for (const item of data) {
        for (var log of vm.eventTableData) {
          if (log.id === item.id) {
            log.confirmeventstatus = item.confirmeventstatus;
            log.remark = item.remark;
            log.updatetime = item.updatetime;
            log.operator = item.operator;
            break;
          }
        }
      }
    },
    // 1输出
    CetButton2_1_statusTrigger_out(val) {
      this.isShowAdvancedQueryWindow = true;
    },
    loadEnumrations(...args) {
      this.ENUM = {};
      args.forEach(item => {
        this.ENUM[item] = this.$store.state.enumerations[item];
      });
      this.eventTypeGroup.enum = this.ENUM.pqvariationeventtype;
      this.tolerancebandGroup.enum = this.ENUM.toleranceband;
      this.faultDirectionGroup.enum = this.ENUM.transientfaultdirection;

      if (this.firstVisited) {
        this.eventTypeGroup.checked = this.ENUM.pqvariationeventtype.map(
          it => it.id
        );
        this.tolerancebandGroup.checked = this.ENUM.toleranceband.map(
          it => it.id
        );
        this.faultDirectionGroup.checked =
          this.ENUM.transientfaultdirection.map(it => it.id);
        this.lastChecked.eventTypeGroup = [...this.eventTypeGroup.checked];
        this.lastChecked.tolerancebandGroup = [
          ...this.tolerancebandGroup.checked
        ];
        this.lastChecked.faultDirectionGroup = [
          ...this.faultDirectionGroup.checked
        ];
        this.handleGroupCheckStatus();
        this.firstVisited = false;
      }
    },
    getEventStatus(cellValue) {
      // if (!cellValue) {
      //   return "unconfirm";
      // }
      // if (cellValue === 1) {
      //   return "unconfirm";
      // } else
      // return cellValue === 3 ? "confirm" : "unconfirm";
      if (cellValue === 3) {
        return "confirm";
      } else {
        return "unconfirm";
      }
    }
  },
  created: function () {
    this.loadEnumrations(
      COMMON.PQ_VARIATION_EVENTTYPE,
      COMMON.TRANSIENT_FALUT_DIRECTION,
      COMMON.TOLERANCE_BAND,
      COMMON.CONFIRM_EVENT_STATUS
    );
  },
  mounted() {
    // this.$nextTick(() => {
    //   this.queryEventLog();
    // });
  },
  activated: function () {
    this.getTreeData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.clickformore {
  cursor: pointer;
  @include font_size("Ab");
  @include font_color(ZS);
}
.confirm {
  cursor: not-allowed;
}
.unconfirm {
  @include font_color(Sta3);
  cursor: pointer;
}
</style>
