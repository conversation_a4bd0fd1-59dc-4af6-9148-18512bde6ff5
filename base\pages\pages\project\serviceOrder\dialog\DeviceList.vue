<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="el_dialog small"
    >
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <CetForm
        class="eem-cont-c1"
        :data.sync="CetForm_pagedialog.data"
        v-bind="CetForm_pagedialog"
        v-on="CetForm_pagedialog.event"
      >
        <CetTree
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
          style="height: 500px"
          ref="cetTree"
          class="nodeTree"
        ></CetTree>
        <div class="selected mtJ3 ptJ3">
          <span>
            {{
              $T("已选择{0}个", (checkedNodes && checkedNodes.length) || 0)
            }}:&nbsp;
            <el-tag
              class="mrJ mbJ"
              v-for="(item, index) in checkedNodes"
              :key="index"
              @close="handleClose(item)"
              closable
            >
              {{ item && item.name }}
            </el-tag>
          </span>
        </div>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";

export default {
  name: "inspectObjDialog",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    tableData: {
      type: Array
    },
    dialogTitle: {
      type: String
    }
  },
  data(vm) {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: vm.dialogTitle || $T("维保目标"),
        "append-to-body": true,
        width: "500px",
        showClose: true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "static", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        rules: {},
        event: {
          currentData_out: this.CetForm_pagedialog_currentData_out,
          saveData_out: this.CetForm_pagedialog_saveData_out,
          finishData_out: this.CetForm_pagedialog_finishData_out,
          finishTrigger_out: this.CetForm_pagedialog_finishTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: true,
        checkStrictly: true,
        "default-expand-all": true,
        event: {
          checkedNodes_out: this.CetTree_1_checkedNodes_out
        }
      },
      checkedNodes: [] // 选中节点
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      new Promise((resolve, reject) => {
        this.getTreeData();
        resolve();
      }).then(res => {
        this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
      });
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {
      this.CetForm_pagedialog.queryId_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      this.CetForm_pagedialog.inputData_in = this._.cloneDeep(val);
    }
  },
  methods: {
    CetForm_pagedialog_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_saveData_out() {
      this.$emit("saveData_out", this.checkedNodes);
      this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
    },
    CetForm_pagedialog_finishData_out(val) {
      this.$emit("finishData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishTrigger_out(val) {
      this.$emit("finishTrigger_out", val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetForm_pagedialog.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      this.CetForm_pagedialog.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    no() {},
    CetTree_1_checkedNodes_out(val) {
      this.checkedNodes = val;
    },
    getTreeData(callback) {
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [
          { modelLabel: "building" },
          { modelLabel: "floor" },
          { modelLabel: "room" }
        ],
        treeReturnEnable: true
      };
      const nodelabel = this.inputData_in.nodelabel;
      if (nodelabel !== "room" && nodelabel !== "manuequipment") {
        data.subLayerConditions.push({
          modelLabel: this.inputData_in.nodelabel,
          // depth: nodelabel === "linesegment" ? 3 : 2, // 添加配电柜和列头柜下面一段线设备 depth=3 其余depth=2
          filter: {
            composemethod: false,
            expressions: [
              {
                limit: this.inputData_in.model,
                operator: "EQ",
                prop: "model"
              }
            ]
          }
        });
      }
      if (this.inputData_in.nodelabel === "manuequipment") {
        data.subLayerConditions.push({
          modelLabel: this.inputData_in.nodelabel
        });
      }
      customApi.getNodeTree(data).then(res => {
        if (res.code === 0) {
          const treeData = this._.cloneDeep(res.data);
          let filterArr = _this.loop(treeData);
          _this.CetTree_1.inputData_in = treeData;
          _this.CetTree_1.selectNode = treeData[0];
          _this.checkedNodes = _this._.cloneDeep(this.tableData);
          _this.CetTree_1.checkedNodes = _this._.cloneDeep(this.tableData);
          // 不展示不可勾选的节点
          _this.CetTree_1.filterNodes_in = _this._.cloneDeep(filterArr);
          callback && callback();
        }
      });
    },
    loop(data, arr = []) {
      data.forEach(item => {
        // modelLabel = room 以及room下的用能设备、配电设备、管道设备可选, civicpipe也不可选
        if (this.inputData_in.nodelabel === item.modelLabel) {
          this.$set(item, "disabled", false);
          arr.push(item);
        } else {
          this.$set(item, "disabled", true);
        }
        if (item.children) {
          this.loop(item.children, arr);
        }
      });
      return arr;
    },
    // 点击删除tag标签
    handleClose(tag) {
      this.checkedNodes.splice(this.checkedNodes.indexOf(tag), 1);
      this.CetTree_1.checkedNodes = this._.cloneDeep(this.checkedNodes);
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.selected {
  height: 50px;
  border-top: 1px solid;
  @include border_color(B1);
  overflow: auto;
}
.nodeTree {
  :deep(.el-checkbox.is-disabled) {
    display: none !important;
  }
}
.el_dialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
    @include border-radius(C1);
  }
}
</style>
