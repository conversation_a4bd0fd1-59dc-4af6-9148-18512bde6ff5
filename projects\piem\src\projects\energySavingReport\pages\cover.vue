<template>
  <div class="coverPage">
    <div class="centerBox">
      <div class="title">
        {{ $T("浙江油田西南采气厂月节能评估报告") }}
      </div>
      <div class="time">
        <span class="month">{{ momth }}</span>
        <div class="mlJ2">
          <div class="year mbJ1 fs24">{{ year }}</div>
          <div class="fs24">{{ $T("月月报") }}</div>
        </div>
      </div>
    </div>
    <div class="date">
      <span class="fs16">{{ $T("生成日期：") }}</span>
      <span class="fs16">
        {{ generationDate || "--" }}
      </span>
    </div>
    <img src="../assets/BG.png" alt="" />
  </div>
</template>

<script>
import OmegaI18n from "@omega/i18n";
export default {
  name: "coverPage",
  components: {},
  data() {
    return {};
  },
  props: {
    queryTime: {
      type: Number
    },
    generationDate: {
      type: String
    }
  },
  computed: {
    momth() {
      return this.$moment(this.queryTime).format("MM");
    },
    year() {
      return this.$moment(this.queryTime).format("YYYY");
    },
    locale() {
      return OmegaI18n.locale;
    }
  },
  watch: {},
  methods: {
    formatTime(time, format) {
      return this.$moment(time).format(format);
    }
  }
};
</script>

<style lang="scss" scoped>
@media print {
  @page {
    margin: 0;
  }

  body {
    margin: 1.6cm;
  }
}
.coverPage {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  img {
    width: 100%;
    height: 100%;
  }
  .centerBox {
    position: absolute;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    width: 90%;

    .title {
      font-weight: bold;
      font-size: 28px;
      text-align: center;
    }

    .time {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #54c081;
      .month {
        font-size: 128px;
        font-weight: bold;
        padding: 0;
        margin: 0;
      }
      .year {
        padding-bottom: 8px;
        border-bottom: 1px solid #a3a8ad;
      }
    }
  }
  .date {
    position: absolute;
    bottom: 20%;
    left: 50%;
    translate: -50% -50%;
    color: #666666;
  }
}
</style>
