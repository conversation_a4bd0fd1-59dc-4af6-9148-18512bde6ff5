<template>
  <div class="page eem-common">
    <el-container style="height: 100%; min-width: 1350px">
      <el-aside class="mrJ3 eem-aside" width="315px">
        <div style="padding: 0px" class="common-title-H1 mbJ3">
          选择项目对象
        </div>
        <CetTree
          style="height: calc(100% - 58px)"
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
        ></CetTree>
      </el-aside>
      <el-container
        class="eem-container"
        style="height: 100%; flex-direction: column"
      >
        <div style="padding: 0px" class="common-title-H1 mbJ3">
          配置需量方案
        </div>
        <el-container>
          <div class="basic-box">
            <div class="basic-box-label" style="width: 100px">
              方案名称
              <span style="color: red">*</span>
            </div>
            <ElInput
              v-model="schemeData.name"
              v-bind="ElInput_1"
              v-on="ElInput_1.event"
            ></ElInput>
          </div>
          <div class="mlJ1 el_checkbox">
            通知方式：
            <ElCheckboxGroup
              v-model="ElCheckboxGroup_1.value"
              v-bind="ElCheckboxGroup_1"
              v-on="ElCheckboxGroup_1.event"
            >
              <ElCheckbox
                v-for="item in ElCheckboxList_1.options_in"
                :key="item[ElCheckboxList_1.key]"
                :label="item[ElCheckboxList_1.label]"
                :disabled="item[ElCheckboxList_1.disabled]"
              >
                {{ item[ElCheckboxList_1.text] }}
              </ElCheckbox>
            </ElCheckboxGroup>
          </div>
        </el-container>
        <el-main class="general" style="height: 100%; padding: 0px">
          <el-table class="mtJ3 mbJ3" :data="tableData" border>
            <el-table-column
              type="index"
              label="#"
              align="left"
              width="50"
            ></el-table-column>
            <el-table-column
              prop="key1"
              label="启用预警"
              align="center"
              width="80"
            >
              <template slot-scope="scope">
                <el-checkbox
                  @change="checkboxChange_out"
                  v-model="scope.row[scope.column.property].val"
                ></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column prop="key2" label="预警等级标签" align="left">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row[scope.column.property].val"
                  placeholder="请选择"
                  @change="
                    selectChange(
                      scope.row,
                      scope.row[scope.column.property].val
                    )
                  "
                  size="small"
                >
                  <el-option
                    v-for="item in scope.row[scope.column.property].options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="key3" label="预警等级" align="left">
              <template slot-scope="scope">
                <span
                  :class="[
                    'grade',
                    scope.row.key1.val ? 'gradeColor' + scope.row.key2.val : ''
                  ]"
                >
                  {{ scope.row[scope.column.property].val }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="key4" label="预警名称" align="left">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row[scope.column.property].val"
                  placeholder="请输入内容"
                  size="small"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column
              prop="key5"
              label="自动计算比例"
              align="left"
              width="160"
            >
              <template slot-scope="scope">
                <ElInputNumber
                  v-model="scope.row.key5.val"
                  v-bind="ElInputNumber_1"
                  v-on="ElInputNumber_1.event"
                ></ElInputNumber>
                <span class="el_span">%</span>
              </template>
            </el-table-column>
          </el-table>
        </el-main>
        <div class="mtJ3">
          <CetButton
            class="fr"
            v-bind="CetButton_1"
            v-on="CetButton_1.event"
          ></CetButton>
        </div>
      </el-container>
      <el-aside
        class="mlJ3 eem-container el-right_aside"
        width="300px"
        style="border: none"
      >
        <div class="common-title-H1 mbJ3">通知联系人</div>
        <div class="el-right-commom">
          <div
            v-for="(item, index) in users"
            :key="index"
            :class="{
              pl5: true,
              pt5: true,
              pb5: true,
              isAction: currentUserIndex == index
            }"
            style="cursor: pointer"
            @click="userClick(index)"
          >
            {{ item.name }}
          </div>
        </div>
        <div class="mtJ3">
          <CetButton
            class="fr mlJ1"
            v-bind="CetButton_3"
            v-on="CetButton_3.event"
          ></CetButton>
          <CetButton
            class="fr"
            v-bind="CetButton_2"
            v-on="CetButton_2.event"
          ></CetButton>
        </div>
      </el-aside>
    </el-container>
    <ContactsLibrary
      :visibleTrigger_in="ContactsLibrary.visibleTrigger_in"
      :closeTrigger_in="ContactsLibrary.closeTrigger_in"
      :queryId_in="ContactsLibrary.queryId_in"
      :inputData_in="ContactsLibrary.inputData_in"
      :currentNode="currentNode"
      :currentUsers="currentUsers"
      @addUsers_out="ContactsLibrary_addUsers_out"
    />
  </div>
</template>
<script>
import ContactsLibrary from "./ContactsLibrary";
import { httping } from "@omega/http";
export default {
  name: "demandearlywarning",
  components: {
    ContactsLibrary
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },

  data() {
    return {
      // 节点关联的联系人
      users: [],
      // 当前选中的联系人index
      currentUserIndex: null,
      currentNode: null,
      tableData: [],
      testTableData: [
        {
          key1: {
            val: false
          },
          key2: {
            val: 1,
            options: [
              {
                value: 1,
                label: "红色预警"
              }
              // {
              //   value: 2,
              //   label: "橙色预警"
              // },
              // {
              //   value: 3,
              //   label: "蓝色预警"
              // }
            ]
          },
          key3: {
            val: "预警等级一"
          },
          key4: {
            val: "红色预警"
          },
          key5: {
            val: 90
          }
        },
        {
          key1: {
            val: false
          },
          key2: {
            val: 2,
            options: [
              // {
              //   value: 1,
              //   label: "红色预警"
              // },
              {
                value: 2,
                label: "橙色预警"
              }
              // {
              //   value: 3,
              //   label: "蓝色预警"
              // }
            ]
          },
          key3: {
            val: "预警等级二"
          },
          key4: {
            val: "橙色预警"
          },
          key5: {
            val: 80
          }
        },
        {
          key1: {
            val: false
          },
          key2: {
            val: 3,
            options: [
              // {
              //   value: 1,
              //   label: "红色预警"
              // },
              // {
              //   value: 2,
              //   label: "橙色预警"
              // },
              {
                value: 3,
                label: "黄色预警"
              }
            ]
          },
          key3: {
            val: "预警等级三"
          },
          key4: {
            val: "黄色预警"
          },
          key5: {
            val: 70
          }
        }
      ],
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      // 方案配置信息
      schemeData: {
        id: 0,
        name: ""
      },
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: "保存",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        style: {},
        showPassword: false,
        type: "text",
        rows: 2,
        placeholder: "输入方案名称",
        event: {}
      },
      ElCheckboxGroup_1: {
        value: [],
        style: {
          display: "inline-block"
        },
        event: {}
      },
      ElCheckboxList_1: {
        options_in: [],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      ElInputNumber_1: {
        value: "",
        style: {
          width: "100px"
        },
        size: "small",
        controls: false,
        min: 0,
        step: 2,
        precision: 0,
        controlsPosition: "",
        event: {}
      },
      ContactsLibrary: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      CetButton_2: {
        visible_in: true,
        disable_in: true,
        title: "移除",
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetButton_3: {
        visible_in: true,
        disable_in: false,
        title: "联系人信息库",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      currentUsers: []
    };
  },
  watch: {
    currentUserIndex: function (val) {
      if (typeof val === "number") {
        this.CetButton_2.disable_in = false;
      } else {
        this.CetButton_2.disable_in = true;
      }
    }
  },

  methods: {
    // 联系人的点击
    userClick(index) {
      this.currentUserIndex = index;
    },
    // 获取联系人
    getUsers() {
      httping({
        url:
          "/eem-service/v1/alarm/getSchemeUsers/" +
          this.currentNode.id +
          "/" +
          this.currentNode.modelLabel,
        method: "GET"
      }).then(response => {
        if (response.code == 0 && response.data && response.data.length > 0) {
          this.users = response.data;
        } else {
          this.users = [];
          this.currentUserIndex = null;
        }
      });
    },
    // 移除联系人
    CetButton_2_statusTrigger_out() {
      this.$confirm("确定要删除所选项吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: "btn-custom-cancel",
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: (action, instance, done) => {
          if (action == "confirm") {
            httping({
              url: `/eem-service/v1/alarm/removeSchemeUsers?modelId=${
                this.currentNode.id
              }&modelLabel=${this.currentNode.modelLabel}&userId=${
                this.users[this.currentUserIndex].id
              }`,
              method: "POST"
            }).then(response => {
              if (response.code == 0) {
                this.$message({
                  message: "移除成功",
                  type: "success"
                });
                this.getUsers();
              }
            });
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action != "confirm") {
            this.$message({
              type: "info",
              message: "取消删除！"
            });
          }
        }
      });
    },
    CetButton_3_statusTrigger_out(val) {
      this.currentUsers = this.users;
      this.ContactsLibrary.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 初始化
    init() {
      this.schemeData.id = 0;
      this.schemeData.name = "";
      this.ElCheckboxGroup_1.value = [];
      let alarmnotificationmethodList =
        this.systemCfg.alarmnotificationmethodList || [];
      alarmnotificationmethodList = alarmnotificationmethodList.map(item => {
        return {
          id: String(item.id),
          text: item.text
        };
      });
      this.ElCheckboxList_1.options_in = alarmnotificationmethodList;
      this.tableData = JSON.parse(JSON.stringify(this.testTableData));
    },
    // 获取节点树
    getTree() {
      httping({
        url: "/eem-service/v1/demand/node/demandNodes",
        method: "POST",
        data: [this.projectId]
      }).then(response => {
        if (response.code == 0 && response.data) {
          me.CetTree_1.inputData_in = response.data;
          me.CetTree_1.selectNode = response.data[0];
        }
      });
      var me = this;
      var data = {
        rootID: 0,
        rootLabel: "project",
        subLayerConditions: [
          {
            modelLabel: "demandaccount"
          }
        ],
        treeReturnEnable: true
      };
    },
    // 获取预警方案
    getProjectDetail() {
      // 初始化
      var me = this;
      this.init();
      this.getUsers();
      if (!me.currentNode) {
        return;
      }
      me.getProjectId(
        me.currentNode.id,
        me.currentNode.modelLabel,
        function (projectId) {
          httping({
            url: "/eem-service/v1/alarm/alarmScheme",
            method: "POST",
            data: {
              alarmType: 3,
              energyType: -1,
              indexId: 0,
              nodeId: me.currentNode.id,
              nodeLabel: me.currentNode.modelLabel,
              projectId: projectId
            }
          }).then(function (response) {
            if (
              response.code === 0 &&
              response.data &&
              response.data.length > 0
            ) {
              me.schemeData.id = response.data[0].id;
              me.schemeData.name = response.data[0].name;
              me.$nextTick(() => {
                me.ElCheckboxGroup_1.value = response.data[0].notificationmethod
                  ? response.data[0].notificationmethod.split(",")
                  : [];
              });
              me.getSchemeLevel(response.data[0].alarmlevelconfig_model);
            }
          });
        }
      );
    },
    // 获取预警等级设置
    getSchemeLevel(data) {
      var arr = [];
      data.forEach(item => {
        arr.push({
          key1: {
            val: item.isactive
          },
          key2: {
            val: item.alarmcolorset_id,
            options:
              item.alarmcolorset_id == 1
                ? [
                    {
                      value: 1,
                      label: "红色预警"
                    }
                  ]
                : item.alarmcolorset_id == 2
                ? [
                    {
                      value: 2,
                      label: "橙色预警"
                    }
                  ]
                : item.alarmcolorset_id == 3
                ? [
                    {
                      value: 3,
                      label: "黄色预警"
                    }
                  ]
                : null
          },
          key3: {
            val: item.alarmlevel
          },
          key4: {
            val: item.name
          },
          key5: {
            val: item.rate
          },
          id: item.id
        });
      });
      this.tableData = arr;
    },
    checkboxChange_out() {},
    // 选择器变化
    selectChange(data, val) {
      if (val == 1) {
        data.key3.val = "预警等级一";
      } else if (val == 2) {
        data.key3.val = "预警等级二";
      } else if (val == 3) {
        data.key3.val = "预警等级三";
      }
    },
    // 保存方案
    CetButton_1_statusTrigger_out() {
      // 判断一级、二级、三级比例是否合理
      if (!this.schemeData.name.trim()) {
        this.$message({
          message: "请填写方案名称",
          type: "warning"
        });
        return;
      } else if (
        !common.check_pattern_name.pattern.test(this.schemeData.name.trim())
      ) {
        this.$message({
          message: "请不要输入特殊字符",
          type: "warning"
        });
        return;
      } else if (this.schemeData.name.trim().length > 20) {
        this.$message({
          message: "方案名称长度不超过20个字符",
          type: "warning"
        });
        return;
      }
      var arr = this.tableData;
      arr.forEach(item => {
        if (item.key1.val && !item.key4.val.trim()) {
          this.$message({
            message: "启用的预警名称不能为空",
            type: "warning"
          });
          return;
        }
        if (item.key4.val.trim().length > 255) {
          this.$message({
            message: "预警名称长度不能大于255个字符",
            type: "warning"
          });
        }
      });
      if (
        Number(arr[0].key5.val) <= arr[1].key5.val ||
        arr[1].key5.val <= arr[2].key5.val
      ) {
        this.$message({
          message: "比例填写不合理",
          type: "warning"
        });
        return;
      }
      this.addAlarmscheme();
    },
    addAlarmscheme() {
      var data = {
        scheme: {
          id: this.schemeData.id,
          modelLabel: "alarmscheme",
          isAuto: 2,
          name: this.schemeData.name.trim(),
          noticeTypes: this.ElCheckboxGroup_1.value.join(),
          type: 3,
          energyType: -1,
          alarm: true
        },
        users: [],
        nodes: [
          {
            modelLabel: this.currentNode.modelLabel,
            nodeId: this.currentNode.id
          }
        ],
        items: []
      };

      var data = {
        alarmlevelconfig_model: [],
        alarmtype: 3,
        benchmarkset_model: [],
        // convergence: true,
        efId: 0,
        energytype: -1,
        isalarm: true,
        name: this.schemeData.name.trim(),
        notificationmethod: this.ElCheckboxGroup_1.value.join(),
        project_id: 0,
        relatedNodes: [
          {
            id: this.currentNode.id,
            modelLabel: this.currentNode.modelLabel,
            name: this.currentNode.name
          }
        ],
        thresholdgeneratemethod: 2,
        id: this.schemeData.id
      };
      // 等级
      data.alarmlevelconfig_model = this.tableData.map(item => {
        return {
          isactive: item.key1.val,
          alarmcolorset_id: item.key2.val,
          alarmlevel: item.key3.val,
          id: item.id,
          modelLabel: "alarmlevelconfig",
          name: item.key4.val.trim(),
          rate: item.key5.val
        };
      });
      new Promise((res, err) => {
        this.getProjectId(
          this.currentNode.id,
          this.currentNode.modelLabel,
          res
        );
      }).then(projectId => {
        data.project_id = projectId;
        httping({
          url: "/eem-service/v1/alarm/saveAlarmSchemes",
          method: "POST",
          data
        }).then(response => {
          if (response.code == 0) {
            this.$message({
              message: "保存成功",
              type: "success"
            });
            this.getProjectDetail();
          }
        });
      });
    },
    // 获取节点所属项目id
    getProjectId(id, modelLabel, res) {
      if (this.systemCfg.key == "cloud") {
        res(this.projectId);
        return;
      }
      if (modelLabel == "project") {
        res(id);
      } else if (
        modelLabel == "mechanicalminingmachine" ||
        modelLabel == "pump" ||
        modelLabel == "heatingfurnace"
      ) {
        httping({
          url: "/eem-service/v1/common/query/nodes", // 反查
          method: "POST",
          data: {
            rootID: id,
            rootLabel: modelLabel,
            subLayerConditions: [
              {
                modelLabel: "operationarea"
              }
            ],
            treeReturnEnable: true
          }
        }).then(response => {
          if (
            response.code == 0 &&
            response.data &&
            response.data.length > 0 &&
            response.data[0].children
          ) {
            httping({
              url: "/eem-service/v1/common/query/nodes", // 反查
              method: "POST",
              data: {
                rootID: response.data[0].children[0].id,
                rootLabel: response.data[0].children[0].modelLabel,
                subLayerConditions: [
                  {
                    modelLabel: "project"
                  }
                ],
                treeReturnEnable: true
              }
            }).then(response2 => {
              if (
                response2.code == 0 &&
                response2.data &&
                response2.data.length > 0 &&
                response2.data[0].children
              ) {
                res(response.data[0].children[0].id);
              }
            });
          }
        });
      } else {
        httping({
          url: "/eem-service/v1/common/query/nodes", // 反查
          method: "POST",
          data: {
            rootID: id,
            rootLabel: modelLabel,
            subLayerConditions: [
              {
                modelLabel: "project"
              }
            ],
            treeReturnEnable: true
          }
        }).then(response => {
          if (
            response.code == 0 &&
            response.data &&
            response.data.length > 0 &&
            response.data[0].children
          ) {
            res(response.data[0].children[0].id);
          }
        });
      }
    },
    CetTree_1_currentNode_out(val) {
      if (val) {
        // if (val.modelLabel === "demandaccount") {
        this.currentNode = val;
        this.getProjectDetail();
        // } else {
        //   this.$message({
        //     message: "请选择进线",
        //     type: "warning"
        //   });
        // }
      } else {
        this.currentNode = null;
      }
    },
    ContactsLibrary_addUsers_out() {
      // 更新关联用户
      this.getUsers();
    }
  },
  created: function () {},
  mounted: function () {
    this.getTree();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}

.grade {
  display: block;
}
.gradeColor1 {
  color: #fff;
  background: #ff0000;
}
.gradeColor2 {
  color: #fff;
  background: #ff9900;
}
.gradeColor3 {
  color: #000;
  background: #fbe100;
}
.isAction {
  @include background_color(BG4);
}
.el-footer {
  background: transparent !important;
}
.basic-box {
  :deep(.el-input__inner) {
    border-radius: 0 4px 4px 0;
  }
}
.el_checkbox {
  @include line_height(Hm);
}
.el_span {
  line-height: 32px;
}
.el-right_aside {
  display: flex;
  flex-direction: column;
}
.el-right-commom {
  flex: 1;
  overflow: auto;
}
</style>
