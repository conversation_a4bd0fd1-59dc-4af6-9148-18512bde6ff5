<template>
  <div class="page eem-common">
    <div class="fullheight flex-column">
      <div class="mbJ3">
        <el-tabs v-model="activeName" class="eem-tabs-custom">
          <el-tab-pane :label="$T('按备件统计')" name="spareStatistic"></el-tab-pane>
          <el-tab-pane :label="$T('按设备统计')" name="equipStatistic"></el-tab-pane>
        </el-tabs>
      </div>
      <div class="flex-auto">
        <sparepart v-if="activeName === 'spareStatistic'"></sparepart>
        <equipStatistic v-if="activeName === 'equipStatistic'"></equipStatistic>
      </div>
    </div>
  </div>
</template>

<script>
import sparepart from "./sparepart";
import equipStatistic from "./equipStatistic";

export default {
  name: "sparepartStatistic",
  components: {
    sparepart,
    equipStatistic
  },
  data() {
    return {
      activeName: "spareStatistic"
    };
  },
  watch: {},
  methods: {},
  activated() {
    this.activeName = "spareStatistic";
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
