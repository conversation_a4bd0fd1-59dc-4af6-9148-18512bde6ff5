<template>
  <!-- 1弹窗组件 -->
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="CetDialog">
    <CetForm
      :data.sync="CetForm_pagedialog.data"
      v-bind="CetForm_pagedialog"
      v-on="CetForm_pagedialog.event"
      class="eem-cont-c1"
    >
      <el-container style="height: 100%">
        <el-header style="padding: 0px; height: 96px">
          <el-row :gutter="$J3">
            <el-col :span="8">
              <customElSelect
                v-model="ElSelect_1.value"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
                :prefix_in="$T('能源类型')"
              >
                <ElOption
                  v-for="item in ElOption_1.options_in"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </customElSelect>
            </el-col>
          </el-row>
          <el-row :gutter="$J3" class="mtJ3">
            <el-col :span="8">
              <customElSelect
                v-model="ElSelect_2.value"
                v-bind="ElSelect_2"
                v-on="ElSelect_2.event"
                :prefix_in="$T('费用类型')"
              >
                <ElOption
                  v-for="item in ElOption_2.options_in"
                  :key="item[ElOption_2.key]"
                  :label="item[ElOption_2.label]"
                  :value="item[ElOption_2.value]"
                  :disabled="item[ElOption_2.disabled]"
                ></ElOption>
              </customElSelect>
            </el-col>
            <el-col :span="8">
              <el-form-item class="constName" label="" prop="constName">
                <customElInput
                  v-model.trim="CetForm_pagedialog.data.constName"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                  :prefix_in="$T('成本项名称')"
                ></customElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <ElInput
                style="width: 100%"
                v-model="ElInput_2.value"
                v-bind="ElInput_2"
                v-on="ElInput_2.event"
              ></ElInput>
            </el-col>
          </el-row>
        </el-header>
        <el-main class="content-bg" style="padding: 0">
          <el-table
            ref="CetTable"
            :data="schemeData"
            tooltip-effect="light"
            style="width: 100%"
            height="400px"
            border
            stripe
            @select="handleSelect"
          >
            <template v-for="item in Columns_scheme">
              <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
            </template>
          </el-table>
          <div v-if="showCheckboxGroup_1" class="mtJ1">
            {{ $T("选择计算项") }}：
            <ElCheckboxGroup
              v-model="ElCheckboxGroup_1.value"
              v-bind="ElCheckboxGroup_1"
              v-on="ElCheckboxGroup_1.event"
            >
              <ElCheckbox
                v-for="item in ElCheckboxList_1.options_in"
                :key="item[ElCheckboxList_1.key]"
                :label="item[ElCheckboxList_1.label]"
                :disabled="item[ElCheckboxList_1.disabled]"
              >
                {{ item[ElCheckboxList_1.text] }}
              </ElCheckbox>
            </ElCheckboxGroup>
          </div>
        </el-main>
      </el-container>
    </CetForm>
    <template v-slot:footer>
      <span>
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="mlJ"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </template>
  </CetDialog>
</template>
<script>
import common from "eem-utils/common";
import commonApi from "@/api/custom";
import { httping } from "@omega/http";
import customElInput from "./customElInput.vue";
export default {
  name: "AddConstComposition",
  components: { customElInput },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    basicfeerateFlag: {
      type: Boolean
    },
    powertarifffeerateFlag: {
      type: Boolean
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },

  data() {
    return {
      tableData: [],
      currentFeescheme: null,
      showCheckboxGroup_1: false,
      CetDialog_1: {
        title: "",
        "show-close": true,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          /* open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out, */
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "component", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        rules: {
          constName: [
            {
              required: true,
              message: $T("请输入成本项名称"),
              trigger: ["blur"]
            },
            common.check_name,
            common.pattern_name
          ]
        },
        event: {
          currentData_out: this.CetForm_pagedialog_currentData_out,
          saveData_out: this.CetForm_pagedialog_saveData_out,
          finishData_out: this.CetForm_pagedialog_finishData_out,
          finishTrigger_out: this.CetForm_pagedialog_finishTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElSelect_1: {
        value: null,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [
          {
            id: 2,
            text: $T("电能")
          },
          {
            id: 3,
            text: $T("水")
          },
          {
            id: 15,
            text: $T("天然气")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: null,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [],
        key: "feeRateType",
        value: "feeRateType",
        label: "feeRateTypeName",
        disabled: "disabled"
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {
          width: "100%"
        },
        size: "small",
        event: {
          change: this.ElInput_1_change_out,
          input: this.ElInput_1_input_out
        }
      },
      ElInput_2: {
        value: "",
        "suffix-icon": "el-icon-search",
        placeholder: $T("请输入费率方案以检索"),
        style: {
          // width:"200px"
        },
        event: {
          change: this.ElInput_2_change_out,
          input: this.ElInput_2_input_out
        }
      },
      ElCheckboxGroup_1: {
        value: [],
        style: {
          display: "inline-block"
          // width:"300px"
        },
        disabled: false,
        event: {
          change: this.ElCheckboxGroup_1_change_out
        }
      },
      ElCheckboxList_1: {
        options_in: [
          {
            id: 1,
            text: $T("能耗")
          },
          {
            id: 2,
            text: $T("损耗")
          },
          {
            id: 3,
            text: $T("分摊")
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      schemeData: [], // 费率表格数据
      Columns_scheme: [
        {
          type: "selection", // selection 勾选 index 序号
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "60",
          selectable: this.checkSelectable
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("费率方案"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "feesubratetype$text", // 支持path a[0].b
          label: $T("费率类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ],
      multipleSelection: [], // 费率方案勾选项
      // 基本电费的费率类型列表
      rateTypeList1: [
        {
          id: 1,
          text: $T("容量计费")
        },
        {
          id: 2,
          text: $T("需量计费")
        }
      ],
      // 电度电费和附加费的费率类型列表
      rateTypeList2: [
        {
          id: 1,
          text: $T("单一费率")
        },
        {
          id: 2,
          text: $T("分时费率")
        },
        {
          id: 3,
          text: $T("阶梯费率")
        }
      ],
      allTypeList: [] // 所有能源类型、费用类型、费率类型的列表
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.getEnergytype();
      this.CetDialog_1.title = this.inputData_in.edit
        ? $T("编辑成本构成")
        : $T("添加成本构成");
      this.CetForm_pagedialog.resetTrigger_in = new Date().getTime();
      if (!this.inputData_in.edit) {
        this.multipleSelection = [];
      }
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {},
    inputData_in(val) {
      this.ElSelect_2.value = null;
      this.ElInput_2.value = "";
      this.ElCheckboxGroup_1.value = [];
      this.showCheckboxGroup_1 = false;
      if (val.edit) {
        // 编辑
        this.ElSelect_1.disabled = true;
        this.ElSelect_2.disabled = true;
        this.CetForm_pagedialog.data = this._.cloneDeep(val);
        if (val.feeratetype === 2 || val.feeratetype === 4) {
          this.showCheckboxGroup_1 = true;
          this.$nextTick(() => {
            if (val.feeratesubtype === 3) {
              this.ElCheckboxList_1.options_in = [
                {
                  id: 1,
                  text: $T("能耗"),
                  disabled: true
                },
                {
                  id: 2,
                  text: $T("损耗"),
                  disabled: true
                },
                {
                  id: 3,
                  text: $T("分摊"),
                  disabled: true
                }
              ];
            } else {
              this.ElCheckboxGroup_1.disabled = false;
              this.ElCheckboxList_1.options_in = [
                {
                  id: 1,
                  text: $T("能耗"),
                  disabled: false
                },
                {
                  id: 2,
                  text: $T("损耗"),
                  disabled: false
                },
                {
                  id: 3,
                  text: $T("分摊"),
                  disabled: false
                }
              ];
            }
            this.ElCheckboxGroup_1.value = JSON.parse(
              val.costcheckitem
            ).calculateitem;
          });
        }
      } else {
        this.CetForm_pagedialog.data = {};
        this.ElSelect_1.disabled = false;
        this.ElSelect_2.disabled = false;
      }
    }
  },

  methods: {
    //获取能源类型
    getEnergytype() {
      var vm = this;
      vm.ElOption_1.options_in = [];
      httping({
        url:
          "/eem-service/v1/project/projectEnergy?projectId=" + this.projectId,
        method: "GET"
      }).then(function (response) {
        if (response.code === 0 && response.data && response.data.length > 0) {
          // 过滤掉能源类型为13和22
          const energyList =
            response.data.filter(
              item => ![13, 18, 22].includes(item.energytype)
            ) || [];
          var selectData = energyList.map(item => {
            return {
              id: item.energytype,
              text: item.name,
              symbol: common.formatSymbol(item.symbol) || "--"
            };
          });
          // 查询所有能源类型、费用类型、费率类型
          const energytypes = energyList.map(item => {
            return item.energytype;
          });
          commonApi.queryEnergySchemeConfig(energytypes).then(res => {
            if (res.code === 0) {
              vm.allTypeList = vm._.cloneDeep(res.data);
              vm.ElOption_1.options_in = vm._.cloneDeep(selectData);
              if (vm.inputData_in.edit) {
                // 编辑
                vm.ElSelect_1.value = vm.inputData_in.energytype;
                vm.ElSelect_1_change_out(vm.inputData_in.energytype);
              } else {
                vm.ElSelect_1.value = selectData[0] && selectData[0].id;
                vm.ElSelect_1_change_out(selectData[0] && selectData[0].id);
              }
            }
          });
        }
      });
    },
    // 获取费率列表
    getFeerateList() {
      if (!this.ElSelect_1.value || !this.ElSelect_2.value) {
        return;
      }
      // 成本项名称 ElInput_1.value
      var vm = this;
      if (vm.AjaxFlag) {
        return;
      }
      vm.AjaxFlag = true;
      vm.schemeData = [];
      httping({
        url: `/eem-service/v1/schemeConfig/feeScheme/${this.projectId}?energyType=${this.ElSelect_1.value}`,
        method: "GET"
      }).then(function (response) {
        vm.AjaxFlag = false;
        if (response.code === 0 && response.data && response.data.length > 0) {
          response.data.forEach(item => {
            const feeratetypeTarget = vm.ElOption_2.options_in.find(
              item => item.feeRateType === vm.ElSelect_2.value
            );
            item.feeratetype$text =
              feeratetypeTarget && feeratetypeTarget.feeRateTypeName;
            // 费率类型显示
            let target;
            if (item.feeratetype === 1) {
              // 基本电费
              target = vm._.find(vm.rateTypeList1, ["id", item.feeratesubtype]);
            } else if ([2, 4].includes(item.feeratetype)) {
              // 电度电费、附加费
              target = vm._.find(vm.rateTypeList2, ["id", item.feeratesubtype]);
            }
            // 费率类型
            item.feesubratetype$text = target && target.text;
          });
          vm.tableData = response.data;
          vm.ElInput_2.value = "";
          vm.ElInput_2_input_out();
          if (vm.inputData_in && vm.inputData_in.edit) {
            var data = response.data.find(
              item => item.id === vm.inputData_in.feescheme_id
            );
            vm.$nextTick(() => {
              vm.$refs.CetTable.setCurrentRow(data);
              vm.$refs.CetTable.toggleRowSelection(data, true);
              this.multipleSelection = [data];
            });
          }
        }
      });
    },
    filterTab() {
      var _this = this;
      if (_this.tableData.length === 0 || !this.ElSelect_2.value) {
        _this.schemeData = [];
        return;
      }
      var tableData = _this.tableData.filter(item => {
        return item.feeratetype === _this.ElSelect_2.value;
      });

      if (_this.ElInput_2.value) {
        tableData = tableData.filter(item => {
          return item.name.indexOf(_this.ElInput_2.value) !== -1;
        });
      }
      tableData = _this._.sortBy(tableData, ["feeratesubtype", "id"]);
      _this.schemeData = tableData;
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_pagedialog.localSaveTrigger_in = this._.cloneDeep(val);
    },
    ElCheckboxGroup_1_change_out(val) {},
    CetDialog_1_open_out(val) {},
    CetDialog_1_close_out(val) {},
    ElInput_1_change_out(val) {},
    ElInput_1_input_out(val) {},
    ElInput_2_change_out(val) {},
    ElInput_2_input_out(val) {
      this.ElInput_2.value = val;
      this.filterTab();
    },
    ElSelect_1_change_out(val) {
      if (!val) {
        return;
      }
      const target = this.allTypeList.find(item => item.energyType === val);
      if (!target) return;
      if (val === 2) {
        //电
        if (this.basicfeerateFlag && this.powertarifffeerateFlag) {
          this.ElOption_2.options_in = target.feeRateTypesList.filter(
            item => item.feeRateType === 2
          );
          this.ElSelect_2.value = 2;
        } else if (this.basicfeerateFlag && !this.powertarifffeerateFlag) {
          this.ElOption_2.options_in = target.feeRateTypesList.filter(item =>
            [2, 3].includes(item.feeRateType)
          );
          this.ElSelect_2.value = 2;
        } else if (this.powertarifffeerateFlag && !this.basicfeerateFlag) {
          this.ElOption_2.options_in = target.feeRateTypesList.filter(item =>
            [1, 2].includes(item.feeRateType)
          );
          this.ElSelect_2.value = 1;
        } else {
          this.ElOption_2.options_in = this._.cloneDeep(
            target.feeRateTypesList
          );
          this.ElSelect_2.value = 1;
        }
      } else {
        this.ElOption_2.options_in = this._.cloneDeep(target.feeRateTypesList);
        this.ElSelect_2.value = 2;
      }
      if (this.inputData_in.edit) {
        // 编辑
        if (
          this.inputData_in.feeratetype === 1 &&
          this.powertarifffeerateFlag
        ) {
          this.ElOption_2.options_in = target.feeRateTypesList.filter(item =>
            [1, 2].includes(item.feeRateType)
          );
        } else if (
          this.inputData_in.feeratetype === 3 &&
          this.basicfeerateFlag
        ) {
          this.ElOption_2.options_in = target.feeRateTypesList.filter(item =>
            [2, 3].includes(item.feeRateType)
          );
        } else if (
          (this.inputData_in.feeratetype === 1 &&
            !this.powertarifffeerateFlag) ||
          (this.inputData_in.feeratetype === 3 && !this.basicfeerateFlag)
        ) {
          this.ElOption_2.options_in = this._.cloneDeep(
            target.feeRateTypesList
          );
        }
        this.ElSelect_2.value = this.inputData_in.feeratetype;
      }
      this.ElSelect_2_change_out(this.ElSelect_2.value);
    },
    ElSelect_2_change_out(val) {
      if (!val) {
        return;
      }
      if (val === 2 || val === 4) {
        this.showCheckboxGroup_1 = true;
        if (!this.inputData_in.edit) {
          this.ElCheckboxGroup_1.value = [];
        }
      } else {
        if (!this.inputData_in.edit) {
          this.ElCheckboxGroup_1.value = [1];
        }
        this.showCheckboxGroup_1 = false;
      }
      this.getFeerateList();
    },
    CetTable_1_record_out(val) {
      if (val.id !== -1) {
        this.currentFeescheme = val;
        // 选择阶梯费率分摊置灰
        if (
          (val.feeratetype === 2 && val.feeratesubtype === 3) ||
          val.feeratetype === 4
        ) {
          if (!this.inputData_in.edit) {
            this.ElCheckboxGroup_1.value = [1];
          }
          this.ElCheckboxGroup_1.disabled = true;
          this.ElCheckboxList_1.options_in = [
            {
              id: 1,
              text: $T("能耗"),
              disabled: true
            },
            {
              id: 2,
              text: $T("损耗"),
              disabled: true
            },
            {
              id: 3,
              text: $T("分摊"),
              disabled: true
            }
          ];
        } else {
          if (!this.inputData_in.edit) {
            this.ElCheckboxGroup_1.value = [];
          }
          this.ElCheckboxGroup_1.disabled = false;
          this.ElCheckboxList_1.options_in = [
            {
              id: 1,
              text: $T("能耗"),
              disabled: false
            },
            {
              id: 2,
              text: $T("损耗"),
              disabled: false
            },
            {
              id: 3,
              text: $T("分摊"),
              disabled: false
            }
          ];
        }
        // 修改时保持选择当前方案
        if (this.inputData_in && this.inputData_in.edit) {
          var data = this.tableData.filter(
            item => item.id === this.inputData_in.feescheme_id
          )[0];
          this.$nextTick(() => {
            this.$refs.CetTable.setCurrentRow(data);
          });
        }
      } else {
        this.currentFeescheme = null;
      }
    },
    //勾选单行操作
    handleSelect(selection, row) {
      this.multipleSelection = []; //清空已选
      // 选择项大于1时
      if (selection.length > 1) {
        const delRow = selection.shift();
        this.$refs.CetTable.toggleRowSelection(delRow, false);
      }
      this.multipleSelection.push(selection[0]);
      if (this.multipleSelection[0]) {
        this.CetTable_1_record_out(this.multipleSelection[0]);
      }
    },
    // 编辑时保持选择当前方案
    checkSelectable(row, index) {
      if (this.inputData_in.edit) {
        const target = this.tableData.find(
          item => item.id === this.inputData_in.feescheme_id
        );
        this.multipleSelection = [target];
      }
      return !this.inputData_in.edit;
    },
    CetForm_pagedialog_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_saveData_out(val) {
      if (!this.multipleSelection[0]) {
        return this.$message({
          message: $T("请选择费率方案"),
          type: "warning"
        });
      }
      // 必须选择一项计费项
      if (
        this.showCheckboxGroup_1 &&
        this.ElCheckboxGroup_1.value.length === 0
      ) {
        return this.$message({
          message: $T("至少选择一项计算项"),
          type: "warning"
        });
      }
      var obj = {
        calculateitem: this.ElCheckboxGroup_1.value
      };
      if (this.inputData_in.edit) {
        // 编辑
        this.$emit("finishData_out", {
          edit: true,
          id: this.inputData_in.id,
          energytype: this.ElSelect_1.value,
          feeratetype: this.ElSelect_2.value,
          feescheme_id: this.multipleSelection[0].id,
          name: this.multipleSelection[0].name,
          costcheckitem: JSON.stringify(obj),
          costcheckitem$text:
            this.ElCheckboxGroup_1.value
              .join("、")
              .replace(1, "能耗")
              .replace(2, "损耗")
              .replace(3, "分摊") || "--",
          constName: this.CetForm_pagedialog.data.constName
        });
      } else {
        // 新增
        this.$emit("finishData_out", {
          edit: false,
          energytype: this.ElSelect_1.value,
          feeratetype: this.ElSelect_2.value,
          feescheme_id: this.multipleSelection[0].id,
          name: this.multipleSelection[0].name,
          costcheckitem: JSON.stringify(obj),
          costcheckitem$text:
            this.ElCheckboxGroup_1.value
              .join("、")
              .replace(1, "能耗")
              .replace(2, "损耗")
              .replace(3, "分摊") || "--",
          constName: this.CetForm_pagedialog.data.constName
        });
      }
    },
    CetForm_pagedialog_finishData_out(val) {},
    CetForm_pagedialog_finishTrigger_out(val) {
      this.$emit("finishTrigger_out", val);
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetForm_pagedialog.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.content-bg {
  :deep(.el-table__header-wrapper .el-checkbox) {
    display: none;
  }
}
.el-header {
  .constName {
    margin: 0px;
    :deep(.el-form-item__content) {
      margin-left: 0 !important;
    }
  }
  :deep(.is-error .el-input__inner) {
    @include border_color(B1);
  }
  :deep(.el-input__inner:focus) {
    @include border_color(B1);
  }
}
.CetDialog {
  :deep(.el-dialog__body) {
    @include padding(J1);
    @include background_color(BG);
  }
}
</style>
