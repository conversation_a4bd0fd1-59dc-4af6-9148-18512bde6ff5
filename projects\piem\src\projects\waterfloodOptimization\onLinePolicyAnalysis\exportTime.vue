<template>
  <div>
    <CetDialog
      class="CetDialog"
      ref="CetDialog"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
    >
      <el-form ref="formData" label-width="120px" class="eem-cont-c1">
        <el-form-item label="选择时间区间">
          <CustomElDatePicker
            range-separator="至"
            v-bind="CetDatePicker_1.config"
            v-model="CetDatePicker_1.val"
          ></CustomElDatePicker>
        </el-form-item>
      </el-form>

      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import common from "eem-utils/common.js";
export default {
  name: "addOrEditModelCreate",
  props: {
    openTrigger_in: Number,
    selectNode: Object
  },
  components: {},
  data() {
    return {
      CetDialog_1: {
        width: "320px",
        title: "导出时间选择",
        showClose: true,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        "append-to-body": true,
        event: {}
      },
      CetDatePicker_1: {
        val: [
          this.$moment().subtract(10, "d").startOf("d").valueOf(),
          this.$moment().endOf("d").valueOf()
        ],
        config: {
          valueFormat: "timestamp",
          type: "daterange",
          clearable: false,
          pickerOptions: {
            disabledDate: time => {
              return time.getTime() > Date.now();
            }
          }
        }
      },

      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    async openTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },
  computed: {},
  methods: {
    async CetButton_confirm_statusTrigger_out() {
      const params = {
        startTime: this.CetDatePicker_1.val[0],
        endTime: this.$moment(this.CetDatePicker_1.val[1]).endOf("d").valueOf(),
        objectId: this.selectNode?.id,
        objectLabel: this.selectNode?.modelLabel
      };

      common.downExcel(
        "/piem/v1/waterinjectionoptimization/online/waterinjection/situation/export",
        params,
        this.$store.state.token,
        this.$store.state.projectId
      );
    },
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = +new Date();
    }
  },
  created() {},
  mounted() {}
};
</script>

<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
  }
  :deep(.el-form-item) {
    .el-form-item__label {
      line-height: 24px;
      text-align: left;
      width: 100% !important;
      float: none;
    }
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}

:deep(.el-dialog) {
  margin-top: 28vh !important;
}
</style>
