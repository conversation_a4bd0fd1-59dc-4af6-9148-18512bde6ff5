<template>
  <div class="page eem-common flex-column eem-cont">
    <div class="justify-content-end mbJ3">
      <ElInput
        class="mlJ1"
        v-model="ElInput_search.value"
        suffix-icon="el-icon-search"
        v-bind="ElInput_search"
        v-on="ElInput_search.event"
      ></ElInput>
      <div class="mlJ1 inline-block">
        <customElSelect
          v-model="ElSelect_system.value"
          v-bind="ElSelect_system"
          v-on="ElSelect_system.event"
          :prefix_in="$T('所属系统')"
        >
          <ElOption
            v-for="item in ElOption_system.options_in"
            :key="item[ElOption_system.key]"
            :label="item[ElOption_system.label]"
            :value="item[ElOption_system.value]"
            :disabled="item[ElOption_system.disabled]"
          ></ElOption>
        </customElSelect>
      </div>
      <div class="mlJ1 inline-block">
        <customElSelect
          v-model="ElSelect_device.value"
          v-bind="ElSelect_device"
          v-on="ElSelect_device.event"
          :prefix_in="$T('所属设备类型')"
        >
          <ElOption
            v-for="item in ElOption_device.options_in"
            :key="item[ElOption_device.key]"
            :label="item[ElOption_device.label]"
            :value="item[ElOption_device.value]"
            :disabled="item[ElOption_device.disabled]"
          ></ElOption>
        </customElSelect>
      </div>
      <time-tool
        class="mlJ1"
        :val.sync="startTime"
        @change="changeQueryTime"
      ></time-tool>
      <!-- export按钮组件 -->
      <CetButton
        class="mlJ1"
        v-bind="CetButton_export"
        v-on="CetButton_export.event"
      ></CetButton>
    </div>
    <div class="flex-auto">
      <CetTable
        :data.sync="CetTable_1.data"
        :dynamicInput.sync="CetTable_1.dynamicInput"
        v-bind="CetTable_1"
        v-on="CetTable_1.event"
        class="eem-table-custom"
      >
        <template v-for="item in Columns_sparepart">
          <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
        </template>
      </CetTable>
    </div>
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom.js";
import TimeTool from "eem-components/TimeTool.vue";

export default {
  name: "sparepart",
  props: {},
  components: {
    TimeTool
  },
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  data() {
    const en = window.localStorage.getItem("omega_language") === "en";
    return {
      // search组件
      ElInput_search: {
        value: "",
        style: {
          width: en ? "210px" : "180px"
        },
        size: "small",
        placeholder: $T("请输入备件名称"),
        event: {
          change: this.ElInput_search_change_out,
          input: this.ElInput_search_input_out
        }
      },
      // system组件
      ElSelect_system: {
        value: 0,
        style: {
          width: en ? "210px" : "160px"
        },
        size: "small",
        filterable: true,
        event: {
          change: this.ElSelect_system_change_out
        }
      },
      // system组件
      ElOption_system: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // device组件
      ElSelect_device: {
        value: "",
        style: {
          width: en ? "210px" : "180px"
        },
        size: "small",
        filterable: true,
        event: {
          change: this.ElSelect_device_change_out
        }
      },
      // device组件
      ElOption_device: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      startTime: new Date().getTime(),
      endTime: null,
      // export组件
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "querySparePartsByOther",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [
            { name: "keyWord_in", operator: "LIKE", prop: "keyWord" },
            { name: "startTime_in", operator: "EQ", prop: "startTime" },
            { name: "endTime_in", operator: "EQ", prop: "endTime" },
            { name: "objectLabelId_in", operator: "EQ", prop: "objectLabelId" },
            { name: "systemId_in", operator: "EQ", prop: "systemId" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      Columns_sparepart: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          //minWidth: "200",  //该宽度会自适应
          width: "60" //绝对宽度
        },
        {
          prop: "sparePartsName", // 支持path a[0].b
          label: $T("备件名称"), //列名
          headerAlign: "leftr",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formatter
          // width: "160" //绝对宽度
        },
        {
          prop: "model", // 支持path a[0].b
          label: $T("规格型号"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        },
        {
          prop: "deviceSystemName", // 支持path a[0].b
          label: $T("所属系统"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        },
        {
          prop: "objectLabelText", // 支持path a[0].b
          label: $T("所属设备类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        },
        {
          prop: "number", // 支持path a[0].b
          label: $T("消耗量"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter,
          sortable: true
        },
        {
          prop: "unit", // 支持path a[0].b
          label: $T("单位"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        }
      ]
    };
  },
  methods: {
    // export输出
    CetButton_export_statusTrigger_out() {
      const param = {
        keyWord: this.ElInput_search.value,
        startTime: this.startTime,
        endTime: this.endTime,
        objectLabelId: this.ElSelect_device.value
          ? this.ElSelect_device.value
          : "",
        systemId: this.ElSelect_system.value ? this.ElSelect_system.value : "",
        page: {
          index: 0,
          limit: 100
        }
      };
      common.downExcel(
        "/eem-service/v1/device/spareParts/exportSparePartsRepalceRecord",
        param,
        this.token
      );
    },
    // search输出,方法名要带_out后缀
    ElInput_search_change_out() {
      this.getSparePartList();
    },
    ElInput_search_input_out() {},
    // system输出,方法名要带_out后缀
    ElSelect_system_change_out() {
      this.getSparePartList();
    },
    ElSelect_device_change_out() {
      this.getSparePartList();
    },
    changeQueryTime({ val, timeOption }) {
      const date = this.$moment(val);
      this.startTime = date.startOf(timeOption.unit).valueOf();
      this.endTime = date.endOf(timeOption.unit).valueOf() + 1;
      this.getSparePartList();
    },
    formatter(row, column, cellValue) {
      return cellValue || "--";
    },
    getDeviceSystem() {
      // 获取系统和设备节点树
      customApi.queryDeviceSystem().then(res => {
        if (res.code === 0) {
          const list = res.data || [];
          this.ElOption_system.options_in = [
            {
              id: 0,
              name: $T("全部")
            },
            ...list
          ];
        }
      });
    },
    // 获取设备类型
    getDevice() {
      const list = this.$store.state.enumerations.deviceclass || [];
      this.ElOption_device.options_in = [
        {
          id: 0,
          text: $T("全部")
        },
        ...list
      ];
      this.ElSelect_device.value = 0;
    },
    // 查备件列表
    getSparePartList() {
      this.CetTable_1.dynamicInput = {
        keyWord_in: this.ElInput_search.value,
        startTime_in: this.startTime,
        endTime_in: this.endTime,
        objectLabelId_in: this.ElSelect_device.value
          ? this.ElSelect_device.value
          : "",
        systemId_in: this.ElSelect_system.value
          ? this.ElSelect_system.value
          : ""
      };
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    }
  },
  created() {
    this.getDeviceSystem();
    this.getDevice();
    this.getSparePartList();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
