<template>
  <div class="page">
    <reportPage pageNum="04" :queryTime="queryTime">
      <div
        class="mbJ4"
        v-if="deviceTypeTableData.length > deviceTypeTableMaxLength"
      >
        <efficiencyAssessmentTable
          :tableData="
            deviceTypeTableData.slice(
              deviceTypeTableMaxLength,
              deviceTypeTableData.length
            )
          "
          type="deviceType"
        />
      </div>

      <div class="mbJ4">
        <div class="third-title mbJ2">
          {{ $T("分电机负载率天然气压缩机能效分析表") }}
        </div>
        <efficiencyAssessmentTable
          :tableData="
            electricMachineryTableData.slice(0, electricMachineryTableMaxLength)
          "
          type="electricMachinery"
        />
      </div>
    </reportPage>
  </div>
</template>

<script>
import reportPage from "../components/reportPage.vue";
import efficiencyAssessmentTable from "../components/efficiencyAssessmentTable.vue";

import common from "eem-utils/common";
export default {
  name: "fourthPage",
  components: {
    reportPage,
    efficiencyAssessmentTable // 分设备型号天然气压缩机能效分析
  },
  props: {
    reportData: {
      type: Object,
      default: () => {}
    },
    queryTime: {
      type: Number
    }
  },
  computed: {
    deviceTypeTableData() {
      const list = this.reportData.typeCompressorEffAssessmentList || [];
      let result = [];
      list.forEach(item => {
        item.compressorEffAssessmentList?.forEach(v => {
          result.push({
            ...v,
            typeName: item.typeName
          });
        });
      });
      return result;
    },
    electricMachineryTableData() {
      const list = this.reportData.loadRateCompressorEffAssessmentList || [];
      let result = [];
      list.forEach(item => {
        item.compressorEffAssessmentList?.forEach(v => {
          result.push({
            ...v,
            typeName: item.typeName
          });
        });
      });
      return result;
    },
    deviceTypeTableMaxLength() {
      const list = this.reportData.compressorEnergySavingVOList || [];
      const compressorList =
        this.reportData.stationsCompressorEffAssessmentList || [];
      const length = 15; // 节能分析表格和能效评估表表格都为空时，分设备型号天然气压缩机能效分析表格所能展示下的最大行数
      const num = compressorList.length + list.length - 8;
      return num > 0 ? length - num : length;
    },
    electricMachineryTableMaxLength() {
      const list = this.deviceTypeTableData || [];
      const length = 20; //分设备型号天然气压缩机能效分析表格为空时，分电机天然气压缩机能效分析表格所能展示下的最大行数
      const num = list.length - this.deviceTypeTableMaxLength;
      return num > 0 ? length - num : length;
    }
  },
  data() {
    return {};
  },
  watch: {},
  methods: {
    formatNumberWithPrecision(...args) {
      return common.formatNumberWithPrecision(...args);
    }
  }
};
</script>

<style lang="scss" scoped>
@media print {
  @page {
    margin: 0;
  }

  body {
    margin: 1.6cm;
    -webkit-print-color-adjust: exact !important;
    -moz-print-color-adjust: exact !important;
    -ms-print-color-adjust: exact !important;
  }
  button {
    display: none;
  }
}
.page {
  width: 100%;
  height: 100%;
  position: relative;
  background: #ffffff;
}
.title {
  color: #242424;
  font-weight: bold;
  line-height: 20px;
}
.second-title {
  color: #242424;
  line-height: 18px;
}
.third-title {
  color: #242424;
  line-height: 18px;
  font-size: 12px;
}
</style>
