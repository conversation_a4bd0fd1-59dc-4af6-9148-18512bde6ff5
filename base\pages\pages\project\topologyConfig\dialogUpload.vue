<template>
  <div>
    <CetDialog
      v-bind="CetDialog_add"
      v-on="CetDialog_add.event"
      class="CetDialog"
    >
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
      <el-main class="bg1 brC1 ptJ3 pbJ3 plJ4 prJ4" style="height: 100%">
        <ElInput
          v-model="ElInput_1.value"
          v-bind="ElInput_1"
          v-on="ElInput_1.event"
        >
          <div slot="suffix" class="uploadBtnClick" @click="uploadBtnClick">
            <i class="el-icon-more"></i>
          </div>
        </ElInput>
        <div class="uploadTitle">
          <span @click="download" :class="!nodeTypes ? 'click-text' : ''">
            {{ download_text_in }}
          </span>
          <el-popover
            placement="bottom"
            width="400"
            trigger="hover"
            v-if="tipsData_in && tipsData_in.length > 0"
          >
            <div>
              <div v-for="(item, index) in tipsData_in" :key="index">
                {{ item }}
              </div>
            </div>
            <i class="el-icon-question" slot="reference"></i>
          </el-popover>
          <div v-if="nodeTypes" class="flex-row mtJ">
            <ElSelect
              class="flex-auto"
              v-model="ElSelect_1.value"
              v-bind="ElSelect_1"
              v-on="ElSelect_1.event"
            >
              <ElOption
                v-for="item in nodeTypes"
                :key="item.modelLabel"
                :label="item.name"
                :value="item.modelLabel"
              ></ElOption>
            </ElSelect>
            <CetButton
              class="mlJ3"
              v-bind="CetButton_download"
              v-on="CetButton_download.event"
            ></CetButton>
          </div>
        </div>
      </el-main>
      <el-upload
        class="elUpload"
        ref="upload"
        action=""
        :file-list="fileList"
        :accept="accept_in"
        :auto-upload="false"
        :multiple="false"
        :on-change="uploadChange"
        :limit="1"
        :before-upload="handleBeforeUpload"
        :http-request="httpRequest"
      >
        <el-button
          slot="trigger"
          style="display: none"
          size="small"
          type="primary"
          ref="uploadBtn"
        >
          选取文件
        </el-button>
      </el-upload>
    </CetDialog>
  </div>
</template>

<script>
export default {
  name: "dialogUpload",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    title_in: {
      type: String,
      default: ""
    },
    download_text_in: {
      type: String,
      default: ""
    },
    tipsData_in: {
      type: Array,
      default: () => []
    },
    accept_in: {
      type: String,
      default: ""
    },
    nodeTypes: Array
  },
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  data(vm) {
    return {
      fileList: [],
      CetDialog_add: {
        title: vm.title_in,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_add_open_out,
          close_out: this.CetDialog_add_close_out
        },
        width: "500px",
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: true,
        title: $T("上传"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请上传"),
        style: {
          width: "100%"
        },
        disabled: true,
        event: {}
      },
      ElSelect_1: {
        value: "",
        filterable: true,
        clearable: true,
        multiple: true,
        "collapse-tags": true,
        style: {},
        event: {}
      },
      CetButton_download: {
        visible_in: true,
        disable_in: false,
        title: $T("下载"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_download_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.openTrigger_in = val;
      this.ElSelect_1.value = "";
      this.ElInput_1.value = "";
      this.CetButton_confirm.disable_in = true;
      this.$nextTick(() => {
        this.$refs.upload.clearFiles();
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    },
    inputData_in(val) {},
    title_in(val) {
      this.CetDialog_add.title = val;
    }
  },

  methods: {
    // 文件上传前
    handleBeforeUpload(file) {
      var acceptArr = this.accept_in.split(",");
      var flag = true;
      if (acceptArr.length) {
        acceptArr.forEach(item => {
          if (file.name.indexOf(item) != -1) {
            flag = false;
          }
        });
      }
      if (flag) {
        this.$message({
          type: "error",
          message: $T("只能上传{0}格式文件", this.accept_in)
        });
        return false;
      }
    },
    CetDialog_add_open_out(val) {},
    CetDialog_add_close_out(val) {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.$refs.upload.submit();
    },
    uploadBtnClick() {
      this.$refs.upload.clearFiles();
      this.ElInput_1.value = "";
      this.CetButton_confirm.disable_in = true;
      this.$refs.uploadBtn.$el.click();
    },
    uploadChange(file, fileList) {
      if (fileList && fileList.length > 0) {
        this.ElInput_1.value = fileList[0].name;
        this.CetButton_confirm.disable_in = false;
      } else {
        this.ElInput_1.value = "";
        this.CetButton_confirm.disable_in = true;
      }
    },
    httpRequest(val) {
      this.$emit("uploadFile", val);
    },
    download() {
      if (this.nodeTypes) return;
      this.$emit("download");
    },
    CetButton_download_statusTrigger_out() {
      this.$emit("download", this.ElSelect_1.value);
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
  }
}
.elUpload {
  display: none;
}
.uploadBtnClick {
  height: 100%;
  width: 20px;
  line-height: 40px;
  cursor: pointer;
}
.uploadTitle {
  @include margin_top(J3);
  .click-text {
    cursor: pointer;
    text-decoration: underline;
    @include font_color(ZS);
  }
}
</style>
