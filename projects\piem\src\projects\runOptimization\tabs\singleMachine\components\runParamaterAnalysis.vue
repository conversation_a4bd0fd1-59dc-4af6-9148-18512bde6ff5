<template>
  <div class="page">
    <CetChart
      class="fullheight"
      v-bind="CetChart_parameter"
      :key="chartKey"
    ></CetChart>
  </div>
</template>

<script>
import common from "eem-utils/common";
export default {
  name: "runParameterAnalysis",
  components: {},
  props: {
    chartData: {
      type: Array
    }
  },
  data() {
    return {
      chartKey: 0,
      CetChart_parameter: {
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            },
            confine: true,
            formatter: val => {
              if (_.isEmpty(val)) return;
              const head = `<div>${val[0]?.axisValue}</div>`;
              const conent = val.map(item => {
                const unit =
                  this.CetChart_parameter.options.series[item.seriesIndex].unit;
                return `<div style='margin-top: 4px'>
                    <span>${item.marker}</span>
                    <span>${item.seriesName}</span>
                    <span style="float: right; margin-left: 4px">${unit}</span>
                    <span style="font-size: 14px; font-weight: 600; float: right; margin-left: 24px">${item.data[1]}</span>
                    </div>`;
              });
              return head + conent.join("");
            }
          },
          grid: {
            top: 60,
            bottom: 80,
            left: 60,
            right: 20
          },
          legend: {
            show: true
          },
          xAxis: {
            type: "category",
            axisTick: { show: false }
          },
          dataZoom: {
            type: "slider",
            handleIcon:
              "path://M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z",
            handleSize: "80%",
            left: 150,
            right: 150
          },
          yAxis: [],
          series: []
        }
      }
    };
  },
  watch: {
    chartData(val) {
      let series = [];
      let yAxis = [];
      val.forEach(item => {
        let index = 0;
        const yIndex = yAxis.findIndex(v => v.name === item.unit);
        if (yIndex === -1) {
          yAxis.push({
            name: item.unit,
            type: "value",
            offset: yAxis.length > 1 ? (yAxis.length - 1) * 80 : 0
          });
          index = yAxis.length - 1;
        } else {
          index = yIndex;
        }
        series.push({
          name: item.param,
          type: "line",
          yAxisIndex: index,
          smooth: true,
          showSymbol: false,
          unit: item.unit,
          data: item.dataList?.map(item => [
            this.$moment(item.time).format("MM/DD HH:mm"),
            common.formatNumberWithPrecision(item.value, 2)
          ])
        });
      });
      this.CetChart_parameter.options.yAxis = yAxis;
      this.CetChart_parameter.options.grid.right =
        yAxis.length > 1 ? (yAxis.length - 1) * 80 : 80;
      this.CetChart_parameter.options.series = series;
      this.CetChart_parameter.options.dataZoom.right =
        yAxis.length > 2 ? (yAxis.length - 2) * 80 + 150 : 150;
      this.chartKey++;
    }
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
