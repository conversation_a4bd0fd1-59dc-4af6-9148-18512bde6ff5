<template>
  <div class="page">
    <div v-if="defaultInterval === 0" class="flex fullheight">
      <div class="rank-row title mrJ3">
        <span>{{ $T("默认优化策略") }}</span>
        <span class="text-Sta3">{{ $T("TOP3") }}</span>
      </div>
      <div class="flex1">
        <template
          v-for="(
            item, index
          ) in optimizationStrategyData.optimizationListRanking"
        >
          <div :key="index" v-if="index < 3" class="rank-row">
            <img
              :src="require(`../../../assets/rank-${index + 1}.png`)"
              alt=""
              class="mrJ2"
            />
            <span>{{ item.compressorUnitName }}</span>
          </div>
        </template>
      </div>
      <div class="rank-row title mlJ3">
        <span class="text-Sta1 pointer" @click="handleDetails">
          {{ $T("查看更多排名") }}
        </span>
      </div>
    </div>
    <div v-if="defaultInterval === 1" class="flex fullheight">
      <div class="flex1">
        <template v-for="(item, index) in rankList">
          <div :key="index" class="rank-row" v-if="machineLength > index">
            <div class="title mrJ3">
              <span>{{ item.name }}</span>
              <span class="text-Sta3">{{ $T("TOP1") }}</span>
            </div>
            <img src="../../../assets/rank-1.png" alt="" class="mrJ2" />
            <span>
              {{ formatterValue(item.key) }}
            </span>
          </div>
        </template>
      </div>
      <div class="rank-row title mlJ3">
        <span class="text-Sta1 pointer" @click="handleDetails">
          {{ $T("查看更多排名") }}
        </span>
      </div>
    </div>
    <optimizationStrategyDetails
      :defaultInterval="defaultInterval"
      :machineLength="machineLength"
      :optimizationStrategyData="optimizationStrategyData"
      :openTrigger_in="optimizationStrategyDetails.openTrigger_in"
    ></optimizationStrategyDetails>
  </div>
</template>

<script>
import optimizationStrategyDetails from "../dialogs/optimizationStrategyDetails.vue";
export default {
  name: "optimizationStrategy",
  components: { optimizationStrategyDetails },
  props: {
    optimizationStrategyData: {
      type: Object
    },
    defaultInterval: {
      type: Number
    },
    machineLength: {
      type: Number
    }
  },
  data() {
    return {
      rankList: [
        {
          name: $T("单机优化策略"),
          key: "singleOptimizationRanking"
        },
        {
          name: $T("双机优化策略"),
          key: "twoOptimizationRanking"
        },
        {
          name: $T("三机优化策略"),
          key: "threeOptimizationRanking"
        }
      ],
      optimizationStrategyDetails: {
        openTrigger_in: Date.now()
      }
    };
  },
  watch: {},
  methods: {
    /**
     * 多机排名-名称转换
     */
    formatterValue(val) {
      const list = _.get(this.optimizationStrategyData, val, []) || [];
      const name = _.get(list, "[0].compressorUnitName", "--") || "--";
      return name;
    },

    /**
     * 打开详情弹窗
     */
    handleDetails() {
      this.optimizationStrategyDetails.openTrigger_in = Date.now();
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  padding: 16px;
  box-sizing: border-box;
  border-radius: 8px;
  background: url("../../../assets/bg-blue-row.png") no-repeat;
  background-size: cover;
}
.flex1 {
  flex: 1;
}
.title {
  font-weight: bold;
}
.rank-row {
  height: 32px;
  line-height: 32px;
  display: flex;
  align-items: center;
}
</style>
