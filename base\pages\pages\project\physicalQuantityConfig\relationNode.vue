<template>
  <div>
    <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
      <div class="eem-cont-c1 treeBox">
        <ElInput
          class="mbJ3"
          v-model.trim="ElInput_search.value"
          v-bind="ElInput_search"
          v-on="ElInput_search.event"
        ></ElInput>
        <CetVirtualTree
          ref="cetVirtualTree"
          class="cetVirtualTree"
          v-bind="CetVirtualTree_left"
          v-on="CetVirtualTree_left.event"
        ></CetVirtualTree>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_clear"
          v-on="CetButton_clear.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom";
export default {
  props: {
    openTrigger_in: Number,
    inputData_in: Object
  },
  computed: {
    objectlabel() {
      return this.inputData_in?.objectlabel;
    },
    schemeId() {
      return this.inputData_in?.id;
    }
  },
  data() {
    return {
      CetDialog_1: {
        title: "关联节点",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        top: "10vh",
        width: "640px",
        showClose: true,
        "destroy-on-close": true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "保存",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_clear: {
        visible_in: true,
        disable_in: false,
        title: "一键清除",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_clear_statusTrigger_out
        }
      },
      ElInput_search: {
        value: "",
        placeholder: "请输入内容",
        "suffix-icon": "el-icon-search",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_search_change_out
        }
      },
      TreeV2: null,
      CetVirtualTree_left: {
        attribute: {
          data: [],
          props: {
            value: "tree_id",
            label: "name",
            children: "children",
            disabled: "disabled"
          },
          nodeKey: "tree_id",
          defaultCheckedKeys: [],
          showCheckbox: true,
          highlightCurrent: true,
          // checkStrictly: true,
          // height: 638,
          height: 590,
          filterMethod: this.filterNode
        },
        event: {
          onCreate: this.onCreate
        }
      }
    };
  },
  watch: {
    openTrigger_in() {
      this.init();
      this.CetDialog_1.openTrigger_in = Date.now();
    }
  },
  methods: {
    async init() {
      this.ElInput_search.value = "";
      let queryData = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [
          {
            modelLabel: "room"
          },
          {
            modelLabel: this.objectlabel
          }
        ],
        treeReturnEnable: true
      };
      if (this.objectlabel === "linesegment") {
        // 如果是一段线模型，查询节点树的时候加上列头柜和配电柜
        queryData.subLayerConditions.push(
          ...[
            {
              modelLabel: "arraycabinet"
            },
            {
              modelLabel: "powerdiscabinet"
            }
          ]
        );
      }
      const response = await customApi.getNodeTreeSimple(queryData);
      if (response.code !== 0) return;
      const treeData = this._.get(response, "data", []);
      // 过滤掉没有设备的节点
      const filterNodes = this.loop(treeData, this.objectlabel);
      this.TreeV2.setData(filterNodes);
      // 设置已关联节点
      this.setChecked();
    },
    loop(data, modelLabel) {
      let newData = [];
      data.forEach(item => {
        if (item.children && item.children.length) {
          const newChildren = this.loop(item.children, modelLabel);
          if (newChildren && newChildren.length) {
            newData.push({
              ...item,
              children: newChildren
            });
          }
        } else if (item.modelLabel === modelLabel) {
          newData.push(item);
        }
      });
      return newData;
    },
    async setChecked() {
      const res = await customApi.queryCalculateRelationship({
        schemeId: this.schemeId
      });
      const data = this._.get(res, "data", []) || [];
      const keys = data.map(item => {
        return `${this.objectlabel}_${item.objectid}`;
      });
      this.TreeV2.setCheckedKeys(keys);
    },
    async CetButton_confirm_statusTrigger_out() {
      const nodes = this.TreeV2.getCheckedNodes();
      let saveNodes = [];
      nodes.forEach(item => {
        if (item.modelLabel === this.objectlabel) {
          saveNodes.push({
            id: item.id,
            modelLabel: item.modelLabel
          });
        }
      });
      const res = await customApi.calculateRelationship(saveNodes, {
        schemeId: this.schemeId
      });
      if (res.code !== 0) return;
      this.$message({
        message: "保存成功",
        type: "success"
      });
      this.$emit("reloadTable");
      this.CetDialog_1.closeTrigger_in = Date.now();
    },
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    },
    CetButton_clear_statusTrigger_out() {
      this.TreeV2.setCheckedKeys();
    },
    ElInput_search_change_out(val) {
      this.TreeV2.filter(val);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.includes(value);
    },
    onCreate(event) {
      this.TreeV2 = event;
    }
  }
};
</script>

<style lang="scss" scoped>
.treeBox {
  height: 670px;
  .cetVirtualTree {
    ::v-deep .el-vl__window.el-tree-virtual-list::-webkit-scrollbar-track {
      background-color: transparent;
    }
    ::v-deep .el-virtual-scrollbar {
      display: none;
    }
  }
}
</style>
