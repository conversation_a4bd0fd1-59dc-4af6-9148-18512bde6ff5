<template>
  <div class="page eem-common">
    <el-container class="fullheight">
      <el-aside width="315px" class="eem-aside flex-column">
        <customElSelect
          class="fullwidth"
          :prefix_in="$T('能源类型')"
          v-model="ElSelect_1.value"
          v-bind="ElSelect_1"
          v-on="ElSelect_1.event"
        >
          <ElOption
            v-for="item in ElOption_1.options_in"
            :key="item[ElOption_1.key]"
            :label="item[ElOption_1.label]"
            :value="item[ElOption_1.value]"
            :disabled="item[ElOption_1.disabled]"
          ></ElOption>
        </customElSelect>
        <CetTree
          class="flex-auto"
          ref="tree"
          :selectNode.sync="CetTree_folder.selectNode"
          :checkedNodes.sync="CetTree_folder.checkedNodes"
          v-bind="CetTree_folder"
          v-on="CetTree_folder.event"
        >
          <span
            class="el-tree-node__label"
            slot-scope="{ node }"
            :level="node.level"
          >
            <span
              :style="{
                color: filNodeColor(node)
              }"
            >
              {{ node.label }}
            </span>
          </span>
        </CetTree>
      </el-aside>
      <el-container class="fullheight mlJ3 flex-column">
        <div class="mbJ2">
          <div class="fl mrJ2 text-ellipsis">
            <el-tooltip :content="currentNode.name" effect="light">
              <span class="common-title-H1">{{ currentNode.name }}</span>
            </el-tooltip>
          </div>
          <CetButton
            class="fr"
            v-bind="CetButton_edit"
            v-on="CetButton_edit.event"
          ></CetButton>
          <CetButton
            class="fr mrJ1"
            v-bind="CetButton_clear"
            v-on="CetButton_clear.event"
          ></CetButton>
          <CetButton
            class="fr"
            v-bind="CetButton_confirm"
            v-on="CetButton_confirm.event"
          ></CetButton>
          <CetButton
            class="fr mrJ1"
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
        </div>
        <div
          class="fullheight eem-container"
          style="height: calc(100% - 42px); overflow: auto"
        >
          <div class="eem-container mbJ2 bg_color">
            <div class="mbJ1">
              <span class="common-title-H2">计算方式</span>
            </div>
            <div>
              <el-radio-group v-model="radio" @change="radioChange">
                <el-radio label="up" border>向上相加</el-radio>
                <el-radio label="down" border>向下相减</el-radio>
              </el-radio-group>
            </div>
          </div>
          <div class="eem-container mbJ2 bg_color">
            <div class="mbJ1">
              <span class="common-title-H2">能耗计算公式</span>
            </div>
            <div style="font-size: 20px" class="text-ellipsis">
              <el-tooltip content="baidu" effect="light">
                <!-- <span style="color: #29b061; font-size: 20px">
                  拓扑1.1的能耗 =
                </span> -->
                <div slot="content">{{ calculateText }}</div>
                <span style="font-size: 20px">{{ calculateText }}</span>
              </el-tooltip>
            </div>
          </div>
          <div class="eem-container bg_color" style="min-height: 560px">
            <div>
              <span class="common-title-H2">拓扑配置结构</span>
            </div>
            <div v-if="!showVirtual" class="virtualText">
              <span>暂无数据，请选择正确的虚拟表计进行配置</span>
            </div>
            <VirtualChartConfig
              v-bind="VirtualChartConfig"
              :currentNode="currentNode"
              v-else
            />
          </div>
        </div>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import customApi from "@/api/custom";
import VirtualChartConfig from "./VirtualChartConfig.vue";

export default {
  name: "virtualMeter",
  components: { VirtualChartConfig },
  computed: {
    UserID() {
      return this.$store.state.userInfo.id;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  data() {
    return {
      radio: "up",
      number: 0,
      // 用于判断该页面是否是第一次加载
      firstNumber: 0,
      // 计算公式
      calculateText: "暂无数据，请配置虚拟表计!",
      oldCalculateText: "",
      oldradio: "",
      CetTree_folder: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_folder_currentNode_out
        }
      },
      currentNode: {},
      ElSelect_1: {
        value: "",
        style: {},
        event: {}
      },
      ElOption_1: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      // 配置图像
      VirtualChartConfig: {
        inputData_in: null
      },
      // 是否展示拓扑图
      showVirtual: false,
      CetButton_edit: {
        visible_in: true,
        disable_in: false,
        title: $T("编辑"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_edit_statusTrigger_out
        }
      },
      CetButton_clear: {
        visible_in: true,
        disable_in: false,
        title: $T("清除"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_clear_statusTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: false,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: false,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    "ElSelect_1.value": function (val, old) {
      if (old) {
        const callback = () => {
          this.init();
          this.getNodeTree();
        };
        if (
          this.CetButton_cancel.visible_in &&
          this.CetButton_confirm.visible_in
        ) {
          this.CetButton_confirm_statusTrigger_out(callback);
        } else {
          callback();
        }
      }
    }
  },
  methods: {
    // 进行页面初始化
    init() {
      this.number = 0;
      this.currentNode = {};
      this.showVirtual = false;
      this.radio = "up";
      this.CetButton_edit.visible_in = true;
      this.CetButton_clear.visible_in = true;
      this.CetButton_cancel.visible_in = false;
      this.CetButton_confirm.visible_in = false;
    },
    //通过节点childSelectState属性，调整字体颜色
    filNodeColor(node) {
      const state = this._.get(node, "data.nodeStatus", null);
      if (state === 1) {
        return "#989898";
      }
      return;
    },
    CetTree_folder_currentNode_out(val) {
      if (++this.number === 1 && ++this.firstNumber > 1) {
        return;
      }
      const callback = () => {
        this.currentNode = val;
        if (val.nodeStatus === 1) {
          this.calculateText = "暂无数据，请配置虚拟表计!";
          this.showVirtual = false;
          this.CetButton_edit.visible_in = false;
          this.CetButton_clear.visible_in = false;
          this.CetButton_cancel.visible_in = false;
          this.CetButton_confirm.visible_in = false;
          return this.$message.warning(
            val.name + "已关联表计，请选择虚拟表计进行配置"
          );
        }
        this.CetButton_edit.visible_in = true;
        this.CetButton_clear.visible_in = true;
        this.CetButton_cancel.visible_in = false;
        this.CetButton_confirm.visible_in = false;
        // this.saveJudgment();
        setTimeout(() => {
          this.checktype();
        }, 0);
      };
      if (
        this.CetButton_cancel.visible_in &&
        this.CetButton_confirm.visible_in
      ) {
        this.CetButton_confirm_statusTrigger_out(callback);
      } else {
        callback();
      }
    },
    getNodeTree() {
      customApi.getPeTree(this.ElSelect_1.value).then(res => {
        if (res.code !== 0) {
          return;
        }
        this.CetTree_folder.inputData_in = this._.cloneDeep(res.data);
        this.CetTree_folder.selectNode = this._.cloneDeep(res.data[0]);
        this.CetButton_edit.disable_in = false;
        if (res.data.length === 0) {
          this.currentNode = {};
          this.CetButton_edit.disable_in = true;
          this.CetButton_clear.disable_in = true;
          this.calculateText = "暂无数据，请配置虚拟表计!";
          this.showVirtual = false;
        }
      });
    },
    // 获取所有的能源类型
    async getProjectEnergy() {
      const res = await customApi.getProjectEnergy(this.projectId);
      if (res.code !== 0) {
        return;
      }
      if (res.data && res.data.length) {
        const list = res.data;
        this.ElOption_1.options_in = list.filter(
          item => ![13, 18, 22].includes(item.energytype)
        );
        this.ElSelect_1.value = this.ElOption_1.options_in[0].energytype;
      } else {
        this.ElOption_1.options_in = [];
        this.ElSelect_1.value = null;
      }
    },
    // 获取配置图像的数据
    getVirtualChart_out() {
      let params = {
        baseVo: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel
        },
        statWithOutMeter: this.radio === "up" ? 2 : 3
      };
      customApi.getPoInfo(params).then(res => {
        const dataInfoList = this._.get(res, "data.pointNodes", []) || [];
        const dataLink = this._.get(res, "data.linkNodes", []) || [];
        if (res.code === 0 && dataInfoList.length > 0 && dataLink.length > 0) {
          this.showVirtual = true;
          const dataInfo = dataInfoList.map(item => {
            return {
              id: item.name,
              label: item.nodeName,
              style: {
                fill: this.formatEventColor(item)
              },
              nodeLabel: item.nodeLabel
            };
          });
          this.VirtualChartConfig.inputData_in = {
            nodes: dataInfo,
            edges: dataLink
          };
        } else {
          this.showVirtual = false;
        }
      });
    },
    // 固定颜色
    formatEventColor(item) {
      return "#ADEFF7";
    },
    CetButton_confirm_statusTrigger_out(callback) {
      let params = {
        baseVo: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel
        },
        statWithOutMeter: this.radio === "up" ? 2 : 3
      };
      this.$confirm($T("是否保存该配置?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          customApi.saveQuantityConfig(params).then(async res => {
            if (res.code === 0) {
              this.$message({
                message: "保存成功",
                type: "success"
              });
              this.CetButton_edit.visible_in = true;
              this.CetButton_clear.visible_in = true;
              this.CetButton_cancel.visible_in = false;
              this.CetButton_confirm.visible_in = false;
              await this.getCalculateText();
            }
            typeof callback === "function" && callback();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已取消保存")
          });
          typeof callback === "function" && callback();
        });
    },
    CetButton_cancel_statusTrigger_out() {
      this.$confirm($T("是否取消编辑?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          this.$message({
            type: "success",
            message: $T("取消成功！")
          });
          this.CetButton_edit.visible_in = true;
          this.CetButton_clear.visible_in = true;
          this.CetButton_cancel.visible_in = false;
          this.CetButton_confirm.visible_in = false;
          this.radio = this.oldradio;
          this.calculateText = this.oldCalculateText;
          this.CetButton_clear.disable_in =
            this.calculateText === "暂无数据，请配置虚拟表计!" ? true : false;
          this.getVirtualChart_out();
          // this.checktype();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已放弃取消")
          });
        });
    },
    radioChange(val) {
      if (!this.currentNode.id) return;
      if (this.currentNode.nodeStatus === 1) return;
      this.getVirtualChart_out();
      this.getCalculateText();
    },
    CetButton_edit_statusTrigger_out() {
      this.oldCalculateText = this.calculateText;
      this.oldradio = this.radio;
      this.CetButton_edit.visible_in = false;
      this.CetButton_clear.visible_in = false;
      this.CetButton_cancel.visible_in = true;
      this.CetButton_confirm.visible_in = true;
      this.getCalculateText();
    },
    // 获取计算公式数据
    async getCalculateText() {
      let params = {
        baseVo: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel
        },
        ifQuery: this.CetButton_edit.visible_in,
        statWithOutMeter: this.radio === "up" ? 2 : 3
      };
      await customApi.getIsRelation(params).then(res => {
        if (res.code === 0) {
          let data = this._.get(res, "data", {});
          if (data.message) {
            this.calculateText = "暂无数据，请配置虚拟表计!";
            this.CetButton_confirm.disable_in = true;
            this.CetButton_clear.disable_in = true;
            return this.$message.error(data.message);
          }
          if (
            (data.nodes && data.nodes.length) ||
            (data.parent && data.parent.length)
          ) {
            this.CetButton_confirm.disable_in = false;
            this.CetButton_clear.disable_in = false;
            let str = this.currentNode.name + "的能耗 = ";
            const len = "的能耗 +";
            if (this.radio === "up") {
              data.nodes.forEach(item => {
                str = str + item.name + len;
              });
              str = str.slice(0, -1);
              this.calculateText = str;
            } else if (this.radio === "down") {
              data.parent.forEach(item => {
                str = str + item.name + len;
              });
              str = str.slice(0, -1);
              data.nodes.forEach(item => {
                str = str + "-" + item.name + "的能耗";
              });
              this.calculateText = str;
            }
          } else {
            this.calculateText = "暂无数据，请配置虚拟表计!";
          }
        }
      });
    },
    // 清除配置
    CetButton_clear_statusTrigger_out() {
      let params = {
        id: this.currentNode.id,
        modelLabel: this.currentNode.modelLabel
      };
      this.$confirm($T("是否清除该节点的配置?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          customApi.removeConfig(params).then(res => {
            if (res.code === 0) {
              this.$message({
                message: "清除成功",
                type: "success"
              });
              this.CetButton_edit.visible_in = true;
              this.CetButton_clear.visible_in = true;
              this.CetButton_cancel.visible_in = false;
              this.CetButton_confirm.visible_in = false;
              this.CetButton_clear.disable_in = true;
              this.calculateText = "暂无数据，请配置虚拟表计!";
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已取消清除")
          });
        });
    },
    // 获取已经配置好的计算方式
    async checktype() {
      let params = {
        id: this.currentNode.id,
        modelLabel: this.currentNode.modelLabel
      };
      await customApi.checktypeConfig(params).then(res => {
        if (res.data === 3) {
          this.radio = "down";
        } else {
          this.radio = "up";
        }
        this.getCalculateText();
        this.getVirtualChart_out();
      });
    }
  },
  async activated() {
    this.init();
    await this.getProjectEnergy();
    this.getNodeTree();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}

.bg_color {
  @include background_color(BG, !important);
}

.virtualText {
  text-align: center;
  line-height: 400px;
  span {
    font-size: 20px;
  }
}
</style>
