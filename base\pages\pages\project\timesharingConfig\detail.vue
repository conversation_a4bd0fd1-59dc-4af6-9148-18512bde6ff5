<template>
  <div>
    <el-drawer class="detailDrawer" :visible.sync="drawer" size="1050px">
      <div slot="title" class="clearfix">
        <span class="common-title-H1">
          {{ $T("详情") }}
        </span>
      </div>
      <div class="head bg1 pJ3">
        <div class="fsH1 mbJ1">{{ detailData.name || "--" }}</div>
        <div class="headList">
          <div class="listItem">
            <div class="label">{{ $T("能源类型") }}</div>
            <div class="value">
              {{ detailData.energytype$text || "--" }}
            </div>
          </div>
        </div>
      </div>
      <el-tabs
        v-model="activeTabsName"
        class="eem-tabs-custom"
        @tab-click="tabChange"
      >
        <el-tab-pane :label="$T('时段方案')" name="1"></el-tab-pane>
        <el-tab-pane name="2">
          <span slot="label">
            {{ $T("关联年历") }}
            <el-tooltip
              effect="light"
              :content="$T('已关联年历时段方案出现点标记')"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
        </el-tab-pane>
        <!-- <el-tab-pane label="关联节点" name="关联节点"></el-tab-pane> -->
      </el-tabs>
      <div class="content pJ3">
        <div v-show="activeTabsName === '1'" class="fullfilled">
          <div class="flex-row fullfilled">
            <div class="eem-cont-c1 flex-column" style="width: 250px">
              <div class="daySchemeArr mbJ1 flex-auto">
                <div
                  v-for="(item, index) in daySchemeArr"
                  :key="index"
                  :class="{
                    action: actionDayScheme === index ? true : false,
                    'flex-row': true
                  }"
                  @click="daySchemeClick(index)"
                >
                  <span
                    :style="{
                      background: getTimeSchemeColor(index)
                    }"
                  ></span>
                  <el-tooltip effect="light" :content="item.name">
                    <span class="flex-auto mlJ1 text-ellipsis">
                      {{ item.name }}
                    </span>
                  </el-tooltip>
                </div>
              </div>
            </div>
            <div class="flex-auto eem-cont-c1 mlJ3">
              <CetTable
                :data.sync="CetTable_1.data"
                :dynamicInput.sync="CetTable_1.dynamicInput"
                v-bind="CetTable_1"
                v-on="CetTable_1.event"
              >
                <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
                <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
                <ElTableColumn
                  v-bind="ElTableColumn_timeInterval"
                ></ElTableColumn>
              </CetTable>
            </div>
          </div>
        </div>
        <div v-show="activeTabsName === '2'">
          <div class="flex-row">
            <div class="eem-cont-c1 flex-column" style="width: 250px">
              <div class="daySchemeArr mbJ1 flex-auto">
                <div
                  v-for="(item, index) in daySchemeArr"
                  :key="index"
                  :class="{
                    action: actionDayScheme === index ? true : false,
                    'flex-row': true
                  }"
                  @click="daySchemeClick(index)"
                >
                  <span
                    :style="{
                      background: getTimeSchemeColor(index)
                    }"
                  ></span>
                  <el-tooltip effect="light" :content="item.name">
                    <span class="flex-auto mlJ1 text-ellipsis">
                      {{ item.name }}
                    </span>
                  </el-tooltip>
                  <span
                    class="mlJ1 drop"
                    v-if="alreadyConfigIndexs.includes(index)"
                  ></span>
                </div>
              </div>
            </div>
            <div class="flex-auto eem-cont-c1 mlJ3">
              <div style="height: 100%">
                <yearPicker
                  style="height: 100%"
                  :id_in="'AddTimeSharingSchemeYearPickerDetail'"
                  :color_in="yearPicker.color_in"
                  :disable_in="yearPicker.disable_in"
                  ref="yearPicker"
                  :timeSchemeColors="timeSchemeColors"
                  @year_out="yearPicker_year_out"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- <div
          v-show="activeTabsName === '关联节点'"
          class="pJ2 brC3 fullfilled giantTree bg1"
        >
          <CetGiantTree
            v-bind="CetGiantTree_1"
            v-on="CetGiantTree_1.event"
          ></CetGiantTree>
        </div> -->
      </div>
    </el-drawer>
  </div>
</template>
<script>
import yearPicker from "./yearPicker/yearPicker.vue";
import { httping } from "@omega/http";
export default {
  components: {
    yearPicker
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    currentNode_in: {
      type: Object
    },
    daySchemeDetail_in: {
      type: Object
    },
    timeSchemeColors: Array
  },
  filters: {},
  computed: {
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    }
  },
  data() {
    return {
      drawer: false,
      detailData: {},
      activeTabsName: "1",
      daySchemeArr: [],
      yearPicker: {
        disable_in: true,
        color_in: "color1",
        date: null
      },
      actionDayScheme: 0,
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {}
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        //  prop: "",      // 支持path a[0].b
        label: "#", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "50" //绝对宽度
      },
      ElTableColumn_name: {
        //type: "",      // selection 勾选 index 序号
        prop: "name", // 支持path a[0].b
        label: $T("时段名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "200", //绝对宽度
        formatter: function (val) {
          if (val.name) {
            return val.name;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_timeInterval: {
        //type: "",      // selection 勾选 index 序号
        prop: "timeInterval", // 支持path a[0].b
        label: $T("时段"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.timeInterval) {
            return val.timeInterval;
          } else {
            return "--";
          }
        }
      },
      oneQuery: false,
      alreadyConfigIndexs: []
      // treeRender: true,
      // CetGiantTree_1: {
      //   inputData_in: [],
      //   checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
      //   selectNode: {}, //设置选中某一行
      //   unCheckTrigger_in: new Date().getTime(),
      //   setting: {
      //     check: {
      //       chkboxType: { Y: "", N: "" },
      //       enable: true //多选，不配置则默认单选
      //     },
      //     data: {
      //       simpleData: {
      //         enable: true,
      //         idKey: "tree_id"
      //       }
      //     }
      //   },
      //   event: {}
      // }
    };
  },
  watch: {
    async openTrigger_in() {
      this.drawer = true;
      this.oneQuery = 1;
      this.alreadyConfigIndexs = [];
      await this.getDetail();
      this.$nextTick(() => {
        this.$refs.yearPicker.disable(true);
        this.$refs.yearPicker.setYear(this.$moment());
        this.setAlreadyConfigIndexs();
        setTimeout(() => {
          this.oneQuery = false;
        }, 500);
      });
      // this.treeRender = true;
      // this.CetGiantTree_1.inputData_in = [];
    }
  },
  methods: {
    async getDetail() {
      this.detailData = this._.cloneDeep(this.inputData_in);
      this.actionDayScheme = 0;
      this.activeTabsName = "1";
      await this.getTimeShareSchemeDetail();
      this.daySchemeClick(0);
    },
    async getTimeShareSchemeDetail(params_in) {
      if (typeof this.oneQuery === "number") {
        if (this.oneQuery < 1) return;
        this.oneQuery -= 1;
      }
      let params = {
        startTime: this.$moment().startOf("year").valueOf(),
        endTime: this.$moment().endOf("year").valueOf() + 1
      };

      this.daySchemeArr = [];
      await httping({
        url: `/eem-service/v1/schemeConfig/timeShareScheme/${this.inputData_in.id}`,
        method: "GET",
        params: params_in || params
      }).then(res => {
        if (res.code === 0) {
          if (
            res.data[0].timeshareperiod_model &&
            res.data[0].timeshareperiod_model.length > 0
          ) {
            res.data[0].timeshareperiod_model.forEach(item => {
              if (item.dayset_model && item.dayset_model.length > 0) {
                item.dayset_model.forEach(ite => {
                  if (!item.daysetObj) {
                    item.daysetObj = {};
                  }
                  if (!item.daysetObj[this.$moment(ite.day).year()]) {
                    item.daysetObj[this.$moment(ite.day).year()] = [ite];
                  } else {
                    item.daysetObj[this.$moment(ite.day).year()].push(ite);
                  }
                });
              }
              let daysharesetText = [];
              if (item.dayshareset_model && item.dayshareset_model.length > 0) {
                item.dayshareset_model.forEach(ite => {
                  if (Number(ite.beginhour) < 10) {
                    ite.beginhour = "0" + Number(ite.beginhour);
                  }
                  if (Number(ite.beginminute) < 10) {
                    ite.beginminute = "0" + Number(ite.beginminute);
                  }
                  if (Number(ite.endhour) < 10) {
                    ite.endhour = "0" + Number(ite.endhour);
                  }
                  if (Number(ite.endminute) < 10) {
                    ite.endminute = "0" + Number(ite.endminute);
                  }
                  daysharesetText.push(
                    `${ite.beginhour}:${ite.beginminute}~${ite.endhour}:${ite.endminute}`
                  );
                });
              }
              item.daysharesetText = daysharesetText.join(";");
            });
          }
          // 过滤出日时段方案
          let dayArr = [];
          if (
            res.data[0].timeshareperiod_model &&
            res.data[0].timeshareperiod_model.length > 0
          ) {
            res.data[0].timeshareperiod_model.forEach(item => {
              const dataSource = dayArr.find(i => i.name === item.name);
              if (!dataSource) {
                dayArr.push({
                  name: item.name,
                  data: [item]
                });
              } else {
                dataSource.data.push(item);
              }
            });
          }
          res.data[0].timeshareperiodArr = dayArr;
          this.daySchemeArr = res.data[0].timeshareperiodArr;
          this.daySchemeArr.forEach(item => {
            item.data.forEach(ite => {
              ite.copyName = ite.name;
              ite.name = ite.identification;
              ite.timeInterval = ite.daysharesetText;
            });
            this.$set(item, "timeInterval", item.data);
          });
        }
      });
    },
    // 日方案点击
    daySchemeClick(index) {
      this.actionDayScheme = index;
      if (this.daySchemeArr.length > 0) {
        this.CetTable_1.data = this._.cloneDeep(
          this.daySchemeArr[this.actionDayScheme].timeInterval
        );
      } else {
        this.CetTable_1.data = [];
      }
      this.$nextTick(() => {
        this.$refs.yearPicker.setColor("color" + (index + 1));
      });
    },
    // 日历输出时间
    async yearPicker_year_out(val) {
      await this.getTimeShareSchemeDetail({
        startTime: this.$moment(val).startOf("year").valueOf(),
        endTime: this.$moment(val).endOf("year").valueOf() + 1
      });
      this.renderYear(this.$moment(val).year());
    },
    // 渲染年历
    renderYear(year) {
      this.$refs.yearPicker.setYear(this.$moment(year + ".1.1").valueOf());
      // 清空
      this.$refs.yearPicker.drap_select(
        this.$moment().startOf("year").format("MM-DD"),
        this.$moment().endOf("year").format("MM-DD"),
        "",
        true
      );
      if (this.actionDayScheme != -1) {
        if (this.daySchemeArr && this.daySchemeArr.length > 0) {
          this.daySchemeArr.forEach((item, index) => {
            if (item.data && item.data.length > 0) {
              if (item.data[0].daysetObj && item.data[0].daysetObj[year]) {
                item.data[0].daysetObj[year].forEach(ite => {
                  this.$refs.yearPicker.drap_select(
                    this.$moment(ite.day).format("MM-DD"),
                    this.$moment(ite.day).format("MM-DD"),
                    "color" + (index + 1)
                  );
                });
              }
            }
          });
        }
      }
      this.setAlreadyConfigIndexs();
    },
    tabChange() {},
    getTimeSchemeColor(index) {
      return this.timeSchemeColors[index % 12];
    },
    setAlreadyConfigIndexs() {
      let alreadyConfigIndexs = [];
      const yearPickerAll = this.$refs.yearPicker.getAll() || {};
      for (const key in yearPickerAll) {
        if (Object.hasOwnProperty.call(yearPickerAll, key)) {
          alreadyConfigIndexs.push(key.split("color")[1] - 1);
        }
      }
      this.alreadyConfigIndexs = alreadyConfigIndexs;
    }
  }
};
</script>
<style lang="scss" scoped>
.detailDrawer {
  :deep(.el-drawer__body) {
    @include background_color(BG);
    display: flex;
    flex-direction: column;
    min-height: 0;
  }
  .head {
    border-top: 1px solid;
    @include border_color(B1);
    .headList {
      display: flex;
      .listItem {
        min-width: 100px;
        margin-right: 100px;
        &:last-child {
          margin-right: 0;
        }
        .label,
        .value {
          @include font_size(Aa);
        }
        .label {
          @include font_color(T3);
        }
        .value {
          @include margin_bottom(J1);
          line-height: 1.5;
        }
      }
    }
  }
  .eem-tabs-custom {
    :deep() {
      .el-tabs__header.is-top {
        margin-bottom: 0;
      }
      .el-tabs__nav-wrap {
        @include background_color(BG1);
        @include padding_left(J3);
      }
      .el-tabs__nav-wrap::after {
        display: none;
      }
    }
  }
  .content {
    flex: 1;
    min-height: 0;
    overflow: auto;
    .giantTree {
      box-sizing: border-box;
    }
  }
  .daySchemeArr {
    text-align: left;
    overflow: auto;
    & > div {
      height: 16px;
      cursor: pointer;
      @include border_color(B1);
      @include padding_left(J3);
      @include padding_right(J3);
      padding-top: 8px;
      padding-bottom: 8px;
      & > span {
        display: inline-block;
        height: 16px;
        width: 16px;
        line-height: 16px;
      }
      .drop {
        display: inline-block;
        margin-top: 5px;
        height: 6px;
        width: 6px;
        border-radius: 2px;
        @include background_color(ZS);
        line-height: 16px;
      }
    }
    & > div.action {
      @include background_color(BG4);
    }
    & > div:hover {
      @include background_color(BG2);
    }
  }
}
</style>
