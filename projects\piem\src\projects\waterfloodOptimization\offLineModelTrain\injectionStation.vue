<template>
  <div class="flex items-center justify-center w-full h-full">
    <div class="flex justify-between w-full h-full" v-if="isData">
      <div class="w-2/5 h-full">
        <div class="flex items-center h-[32px] text-T2">
          <span class="mr-[16px] text-[14px] font-medium">
            参数相关性热力图
          </span>
          <omega-icon
            symbolId="frame-fullscreen-lin"
            class="w-6 h-6 cursor-pointer"
            @click="onDialog"
          ></omega-icon>
        </div>
        <div class="w-full h-[calc(100%-32px)]">
          <CetChart v-bind="CetChart_pic"></CetChart>
        </div>
      </div>
      <div class="w-3/5 h-full ml-[16px]">
        <div class="w-full h-[32px] mb-[16px]">
          <customElSelect
            v-model="selectParams"
            size="small"
            :style="{ width: '272px' }"
            :prefix_in="$T('选择参数')"
            @change="onChange"
          >
            <el-option
              v-for="item in selectParamsList"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            ></el-option>
          </customElSelect>
        </div>
        <div
          class="w-full h-[calc(100%-32px-16px)] grid gap-[16px]"
          :class="[isDouble ? 'grid-cols-2' : 'grid-cols-1']"
        >
          <el-table
            v-for="(it, ix) in tableDatas"
            :key="ix"
            height="100%"
            :data="it"
            :highlight-current-row="true"
          >
            <el-table-column
              v-for="(item, index) in tableCol"
              :key="index"
              v-bind="item"
            />
          </el-table>
        </div>
      </div>
    </div>
    <div class="flex flex-col items-center empty" v-else>
      <img v-if="isLight" src="../../../resources/assets/light.png" alt="" />
      <img v-else src="../../../resources/assets/dark.png" alt="" />
      <div class="text-[16px] text-T3 mt-[16px]">暂无数据</div>
    </div>
    <DeviceParameter
      v-bind="deviceParameter"
      @updateLabel="isShowLabel = false"
    />
  </div>
</template>

<script>
import omegaTheme from "@omega/theme";
import { getTableColStation } from "../component/tableCol.jsx";
import customApi from "@/api/custom.js";
import common from "eem-utils/common.js";
import DeviceParameter from "./deviceParameter.vue";
export default {
  name: "injectionStation",
  props: {
    selectNode: Object,
    paramsTime: Array,
    activeName: String
  },
  components: { DeviceParameter },
  data() {
    return {
      CetChart_pic: {
        inputData_in: null,
        options: {
          tooltip: {
            position: "top"
            // formatter: params => {
            //   const arr = this.tooltipData?.find(
            //     it => it[0] === params.value[0] && it[1] == params.value[1]
            //   );
            //   return arr?.length === 5
            //     ? `${arr[3]}——${arr[4]}： ${arr[2]}`
            //     : `${params.name}：${params.value[2]}`;
            // }
          },
          grid: {
            height: "82%",
            top: "2%",
            left: "22%",
            right: "12%",
            bottom: "45%"
          },
          xAxis: {
            type: "category",
            interval: 0,
            axisLabel: {
              rotate: 45,
              margin: 10,
              formatter: value => {
                if (this.isShowLabel) return value;
                if (value.length > 4) {
                  return `${value.slice(0, 4)}...`;
                }
                return value;
              }
            },
            data: [],
            splitArea: {
              show: true
            }
          },
          yAxis: {
            type: "category",
            data: [],
            splitArea: {
              show: true
            },
            axisLabel: {
              show: true,
              formatter: value => {
                if (this.isShowLabel) return value;

                if (value.length > 8) {
                  return `${value.slice(0, 8)}...`;
                }
                return value;
              }
            }
          },
          visualMap: {
            color: [],
            min: -1,
            max: 1,
            precision: 1,
            itemHeight: 276,
            calculable: true,
            right: "-6%",
            bottom: "10%",
            top: 0,
            align: "left"
          },
          series: [
            {
              type: "heatmap",
              data: [],
              label: {
                show: true
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowColor: "rgba(0, 0, 0, 0.5)"
                }
              }
            }
          ]
        }
      },
      selectParams: null,
      selectParamsList: [],
      tableDatas: [],
      resData: [],
      tooltipData: [],
      deviceParameter: {
        openTrigger_in: +new Date(),
        inputData_in: {
          chartData: {},
          tableCol: [],
          tableData: []
        }
      },
      isShowLabel: false
    };
  },
  computed: {
    tableCol() {
      return getTableColStation();
    },
    isLight() {
      return omegaTheme.theme === "light";
    },
    isData() {
      return this.resData?.length > 0;
    },
    isDouble() {
      return this.tableDatas?.length === 2;
    }
  },
  watch: {
    selectNode: {
      deep: true,
      handler(val) {
        if (!_.isObject(val)) return;
        this.getData();
      }
    },
    paramsTime: {
      deep: true,
      handler() {
        this.getData();
      }
    }
  },
  methods: {
    async getData() {
      const params = {
        endTime: this.paramsTime[1],
        objectId: this.selectNode?.id,
        objectLabel: this.selectNode?.modelLabel,
        startTime: this.paramsTime[0]
      };
      this.CetChart_pic.options.yAxis.data = [];
      this.CetChart_pic.options.series[0].data = [];
      this.CetChart_pic.options.xAxis.data = [];
      this.tableDatas = [];
      this.tooltipData = [];
      this.selectParamsList = [];
      this.selectParams = null;
      this.resData = [];
      if (!params.endTime || params.objectLabel !== "waterinjectionstation")
        return;

      const res = await customApi.waterParamcorrelation(params);

      this.resData = _.cloneDeep(res?.data);
      const yData = res?.data[0]?.map(it => it.y),
        xData = [],
        seriesData = [],
        tooltipData = [];
      res?.data?.forEach(item => {
        xData.push(item[0]?.x);
        item.forEach(it => {
          const xIndex = xData.findIndex(x => x === it.x),
            yIndex = yData.findIndex(y => y === it.y);
          seriesData.push([xIndex, yIndex, common.filNum(it.value, 2)]);
          tooltipData.push([
            xIndex,
            yIndex,
            common.filNum(it.value, 2),
            it.x,
            it.y
          ]);
        });
      });
      this.selectParamsList = xData?.map((i, ix) => {
        return {
          text: i,
          id: ix
        };
      });

      this.selectParams = this.selectParamsList[0]?.id;

      this.CetChart_pic.options.yAxis.data = yData;
      this.CetChart_pic.options.series[0].data = seriesData;
      this.CetChart_pic.options.xAxis.data = xData;

      this.tooltipData = _.cloneDeep(tooltipData);
      this.getTableData(res?.data);
    },

    onChange() {
      this.getTableData(this.resData);
    },
    getTableData(data) {
      let newData = data[this.selectParams];
      const arr = newData?.length > 6 ? [[], []] : [[]];
      this.tableDatas = _.isArray(data)
        ? arr?.map((it, ix) =>
            ix === 0 ? newData?.slice(0, 6) : newData?.slice(6)
          )
        : [];
    },
    onDialog() {
      this.isShowLabel = true;
      this.deviceParameter.openTrigger_in = +new Date();
      this.deviceParameter.inputData_in = {
        chartData: this.CetChart_pic.options,
        tableCol: this.tableCol,
        tableDatas: this.tableDatas,
        selectParamsList: this.selectParamsList,
        resData: this.resData
      };
    }
  },
  created() {
    this.CetChart_pic.options.visualMap.color = this.isLight
      ? ["#f95e5a", "#f0f2f6", "#4ca6ff"]
      : ["#ff3f3f", "#39476b ", "#0d86ff"];
    this.CetChart_pic.options.visualMap.textStyle.color = this.isLight
      ? "#333333"
      : "#e6e8ea";
  },
  mounted() {
    if (this.activeName === "injectionStation") this.getData();
  }
};
</script>

<style lang="scss" scoped>
.empty {
  img {
    width: 220px;
    height: 135px;
  }
}
</style>
