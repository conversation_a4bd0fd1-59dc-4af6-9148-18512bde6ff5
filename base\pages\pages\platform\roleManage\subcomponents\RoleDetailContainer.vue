<template>
  <div class="fullfilled">
    <div class="fullfilled treeBox" v-show="activeName_in === 'pages'">
      <CetTree v-bind="CetTree_Pages" class="eem-no-line"></CetTree>
    </div>
    <div class="fullfilled treeBox" v-show="activeName_in === 'permissions'">
      <CetTree v-bind="CetTree_Permissions" class="eem-no-line"></CetTree>
    </div>
    <div class="fullfilled" v-show="activeName_in === 'users'">
      <CetTable
        :data.sync="CetTable_Users.data"
        :dynamicInput.sync="CetTable_Users.dynamicInput"
        v-bind="CetTable_Users"
      >
        <el-table-column
          v-for="(column, index) in Columns_Users"
          v-bind="column"
          :key="index"
        ></el-table-column>
      </CetTable>
    </div>
    <div
      class="fullfilled treeBox"
      v-show="activeName_in === 'app'"
      v-if="appMenuPermission"
    >
      <CetTree
        class="eem-no-line"
        :selectNode.sync="CetTree_appPages.selectNode"
        :checkedNodes.sync="CetTree_appPages.checkedNodes"
        v-bind="CetTree_appPages"
        v-on="CetTree_appPages.event"
      ></CetTree>
    </div>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import {
  getPagePermissionTreeNodes,
  getPagePermissionFilterNodes,
  getOperatePermissionTreeNodes
} from "eem-utils/permission.js";
import common from "eem-utils/common";
export default {
  name: "RoleDetailContainer",
  props: {
    roleData_in: {
      type: Object,
      default: null
    },
    refreshTrigger_in: {
      type: Number,
      default: new Date().getTime()
    },
    activeName_in: {
      type: String
    },
    appMenus: {
      type: Array
    },
    appMenuPermission: {
      type: Boolean
    }
  },
  data() {
    return {
      // 页面树组件
      CetTree_Pages: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        filterNodesKey: "filterNode_pages",
        props: {
          label: "label",
          children: "subMenuList"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {}
      },

      // 操作权限树组件
      CetTree_Permissions: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        filterNodesKey: "filterNode_permissions",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {}
      },

      // 用户表格组件
      CetTable_Users: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        defaultSort: {
          order: "descending",
          prop: "id"
        },
        event: {}
      },
      // app页面节点树
      CetTree_appPages: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        filterNodesKey: "filterNode_appPages",
        props: {
          label: "label",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {}
      },

      // 用户列设置
      Columns_Users: [],

      // 角色详情
      roleDetail: {
        name: "",
        auths: [],
        pageNodes: [],
        homepage: ""
      },
      flag: true
    };
  },
  computed: {},
  watch: {
    refreshTrigger_in() {
      this.refresh();
    }
  },
  methods: {
    // 过滤节点树
    filterPageTree(filterNodes) {
      const vm = this;
      vm.CetTree_Pages.filterNodes_in = [];
      vm.$nextTick(() => {
        vm.CetTree_Pages.filterNodes_in = filterNodes;
      });
    },

    // 按角色过滤页面树
    filterPageTreeByRole(role) {
      const vm = this;
      if (!role) {
        vm.filterPageTree([]);
        return;
      }

      const pageNodes = role.pageNodes || [];
      if (!pageNodes.length) {
        vm.filterPageTree([]);
        return;
      }
      let filterNodes = [];
      if (this.$route.name === "platformurolemanage") {
        filterNodes = getPagePermissionFilterNodes(
          pageNodes,
          getPagePermissionTreeNodes(false, ["fw", "pf"], true)
        );
      } else {
        filterNodes = getPagePermissionFilterNodes(pageNodes);
      }
      vm.filterPageTree(filterNodes);
    },
    // 获取app权限节点树
    getAppMenusTree(pageNodes) {
      const str = "eem_am_mobile";
      this.CetTree_appPages.inputData_in = this.appMenus;
      // 角色拥有的app页面权限
      const appPageNodes = pageNodes.filter(item => item.id.includes(str));
      this.CetTree_appPages.filterNodes_in = appPageNodes.map(item => {
        return {
          id: item.id
        };
      });
    },

    // 获取角色的首页
    getHomepage(role) {
      if (!role || !role.customConfig) {
        return null;
      }

      const pageObj = JSON.parse(role.customConfig);
      if (!pageObj) {
        return null;
      }

      return pageObj.homePagePageNodeId || pageObj.homepage;
    },

    // 按角色过滤角色操作权限树
    filterOperationAuths(role) {
      const vm = this;
      const filtedAuths = [];
      (role.auths || []).forEach(val => {
        filtedAuths.push({
          id: val
        });
      });
      vm.CetTree_Permissions.filterNodes_in = [];
      vm.$nextTick(() => {
        vm.CetTree_Permissions.filterNodes_in = filtedAuths;
      });
    },

    // 获取用户
    getUsers(roleId) {
      const vm = this;

      if (vm._.isNil(roleId)) {
        return Promise.reject();
      }

      const p = new Promise((resolve, reject) => {
        customApi
          .getUsersByRole(roleId)
          .then(response => {
            if (response.code === 0) {
              resolve(vm._.cloneDeep(response.data));
            } else {
              reject();
            }
          })
          .catch(() => {
            reject();
          });
      });

      return p;
    },

    // 刷新数据
    refresh() {
      const vm = this;

      let roleId = null;
      if (vm.roleData_in) {
        roleId = vm.roleData_in.id;
      }
      if (!this.flag) {
        return;
      }
      this.flag = false;
      vm.getUsers(roleId)
        .then(users => {
          if (!vm.roleData_in) {
            vm.roleDetail.name = "";
            vm.roleDetail.auths = [];
            vm.roleDetail.pageNodes = [];
            vm.roleDetail.homepage = null;
          } else {
            vm.roleDetail.name = vm.roleData_in.name;
            vm.roleDetail.auths = vm._.cloneDeep(vm.roleData_in.auths);
            vm.roleDetail.pageNodes = vm._.cloneDeep(vm.roleData_in.pageNodes);
            vm.roleDetail.homepage = vm.getHomepage(vm.roleData_in);
          }
          if (this.$route.name === "platformurolemanage") {
            vm.CetTree_Permissions.inputData_in =
              getOperatePermissionTreeNodes(true);
            vm.CetTree_Pages.inputData_in = getPagePermissionTreeNodes(
              false,
              ["fw", "pf"],
              true
            );
          } else {
            vm.CetTree_Permissions.inputData_in =
              getOperatePermissionTreeNodes();
            vm.CetTree_Pages.inputData_in = getPagePermissionTreeNodes(false, [
              "fw"
            ]);
          }

          vm.CetTable_Users.data = users;
          this.$emit("userNum_out", users ? users.length : 0);
          setTimeout(() => {
            vm.filterOperationAuths(vm.roleDetail);
            vm.filterPageTreeByRole(vm.roleDetail);
            if (this.appMenuPermission) {
              this.getAppMenusTree(vm.roleDetail.pageNodes);
            }
          }, 0);
        })
        .finally(() => {
          this.flag = true;
        });
    }
  },
  created() {
    this.Columns_Users = [
      {
        type: "index",
        prop: "id",
        width: 60,
        label: "#",
        sortable: false,
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: null
      },
      {
        prop: "name",
        minWidth: 120,
        label: $T("用户名"),
        sortable: false,
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: common.formatTextCol()
      },
      {
        prop: "mobilePhone",
        minWidth: 150,
        label: $T("移动电话"),
        sortable: false,
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: common.formatTextCol()
      },
      {
        prop: "email",
        minWidth: 100,
        label: $T("电子邮箱"),
        sortable: false,
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: common.formatTextCol()
      }
    ];
  },
  activated() {
    this.flag = true;
  }
};
</script>
<style lang="scss" scoped>
.treeBox {
  width: 100%;
  :deep(.device-search) {
    width: 300px;
  }
}
</style>
