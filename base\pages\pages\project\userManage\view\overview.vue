<template>
  <div class="wrap">
    <div class="rowBox">
      <el-row :gutter="20">
        <el-col :span="8" class="mtJ3 flex-row">
          <div class="label">{{ $T("用户名称") }}</div>

          <div class="value flex-auto text-ellipsis">
            <el-tooltip
              effect="light"
              :content="currentNode_in && currentNode_in.name"
              placement="bottom"
            >
              <div class="text text-ellipsis fl">
                {{ currentNode_in && currentNode_in.name }}
              </div>
            </el-tooltip>
            <span class="role mlJ1">{{ roleName }}</span>
          </div>
        </el-col>
        <el-col :span="8" class="mtJ3 flex-row">
          <div class="label">{{ $T("姓名") }}</div>
          <div class="value flex-auto">
            <el-tooltip effect="light" :content="nicName" placement="bottom">
              <span class="text text-ellipsis">
                {{ nicName }}
              </span>
            </el-tooltip>
          </div>
        </el-col>
        <el-col :span="8" class="mtJ3 flex-row">
          <div class="label">{{ $T("所属用户组") }}</div>
          <div class="value flex-auto">
            <el-tooltip
              effect="light"
              :content="userGroupName"
              placement="bottom"
            >
              <div class="text text-ellipsis">
                {{ userGroupName }}
              </div>
            </el-tooltip>
          </div>
        </el-col>
        <el-col :span="24" class="mtJ3 flex-row">
          <div class="label">{{ $T("移动电话") }}</div>
          <div class="value flex-auto">
            <el-tooltip
              effect="light"
              :content="mobilePhone"
              placement="bottom"
            >
              <div class="text text-ellipsis">
                {{ mobilePhone }}
              </div>
            </el-tooltip>
          </div>
        </el-col>
        <el-col :span="24" class="mtJ3 flex-row">
          <div class="label">{{ $T("邮箱地址") }}</div>
          <div class="value flex-auto">
            <el-tooltip effect="light" :content="email" placement="bottom">
              <div class="text text-ellipsis">
                {{ email }}
              </div>
            </el-tooltip>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";
export default {
  props: {
    currentNode_in: {
      type: Object
    }
  },
  data() {
    return {
      nicName: "--",
      mobilePhone: "--",
      email: "--",
      userGroupName: "--",
      roleName: "--"
    };
  },
  watch: {
    currentNode_in: {
      handler: function () {
        this.getDetail();
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async getDetail() {
      const vm = this;
      if (!vm.currentNode_in) {
        return;
      }
      vm.userGroupName = vm.currentNode_in.userGroupName || "--";
      let res = await customApi.queryUserInfoById(vm.currentNode_in.id);
      if (res.code === 0) {
        let data = vm._.get(res, "data");
        vm.nicName = data.nicName || "--";
        vm.mobilePhone = data.mobilePhone || "--";
        vm.email = data.email || "--";
        vm.roleName = vm._.get(data, "roles[0].name", "--") || "--";
        vm.$emit("role_out", vm._.get(data, "roles[0]"));
        vm.$emit("userInfo_out", data);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.rowBox {
  :deep(.el-col) {
    display: flex;
  }
  .label {
    float: left;
    @include font_color(T3);
    @include margin_right(J3);
  }
  .value {
    float: left;
    .text {
      display: inline-block;
      max-width: 100%;
    }
  }
  .role {
    float: left;
    @include font_color(Sta3);
    padding: 4px 6px;
    @include font_size(Ab);
    @include line_height(Ab);
    @include border_radius(C);
    @include themeify {
      background-color: rgba(themed(Sta3), mh-get(O1));
    }
  }
}
</style>
