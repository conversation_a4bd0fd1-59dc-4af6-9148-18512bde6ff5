<template>
  <div class="treeBox flex-column">
    <customElSelect
      v-show="dimId_in !== 4"
      v-model="ElSelect_1.value"
      v-bind="ElSelect_1"
      v-on="ElSelect_1.event"
      class="mbJ1"
      :prefix_in="$T('能源类型')"
    >
      <ElOption
        v-for="item in ElOption_1.options_in"
        :key="item[ElOption_1.key]"
        :label="item[ElOption_1.label]"
        :value="item[ElOption_1.value]"
        :disabled="item[ElOption_1.disabled]"
      ></ElOption>
    </customElSelect>
    <CetGiantTree
      class="CetGiantTree flex-auto"
      v-bind="CetGiantTree_1"
      v-on="CetGiantTree_1.event"
    ></CetGiantTree>
  </div>
</template>

<script>
//项目级逻辑组件
import customApi from "@/api/custom";
export default {
  props: {
    refreshTrigger: {
      type: Number
    },
    dimIdChangeTrigger: {
      type: Number
    },
    dimId_in: {
      type: Number
    },
    energyOptions_in: Array
  },

  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    projectTenantId() {
      return this.$store.state.projectTenantId;
    }
  },

  watch: {
    refreshTrigger() {
      this.CetGiantTree_1.inputData_in = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.init();
      this.getTreeData();
      this.getProjectEnergy();
    },
    dimIdChangeTrigger() {
      this.dimIdChange();
    }
  },
  data() {
    return {
      ElSelect_1: {
        value: 0,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "name"
            }
          }
        },
        event: {
          currentNode_out: this.CetTree_1_currentNode_out //选中单行输出
        }
      }
    };
  },
  methods: {
    init() {
      const params = this.$route.params;
      this.ElSelect_1.value = this._.get(
        this.energyOptions_in,
        "[0].energytype"
      );
      if (params && params.currentNode && this.dimId_in === 3) {
        this.ElSelect_1.value = params.energyType;
        this.setNode = () => {
          this.CetGiantTree_1.selectNode = params.currentNode;
        };
      }
    },
    dimIdChange() {
      this.ElSelect_1.value = this._.get(
        this.energyOptions_in,
        "[0].energytype"
      );
      this.getTreeData();
    },
    ElSelect_1_change_out() {
      this.getTreeData(this.ElSelect_1.value);
    },
    async getProjectEnergy() {
      this.ElOption_1.options_in = this._.cloneDeep(this.energyOptions_in);
    },
    async getTreeData() {
      if (this.dimId_in === 3) {
        this.getTopologyTree();
      }
    },
    // 获取拓扑树
    async getTopologyTree() {
      const data = {
          projectId: this.projectId,
          energyType: this.ElSelect_1.value
        },
        params = {
          keepEndEquipment: true
        };

      const res = await customApi.powerManageConfigTree(data, params);
      if (res.code !== 0) {
        return;
      }
      this.treeDataHandle([res.data]);
    },
    treeDataHandle(resData) {
      if (!resData) {
        return;
      }
      this.CetGiantTree_1.inputData_in = resData;
      this.CetGiantTree_1.selectNode = resData[0];
      this.$emit("clearChart_out");
    },
    CetTree_1_currentNode_out(val) {
      this.$emit("currentNode_out", val);
    }
  }
};
</script>

<style lang="scss" scoped>
.treeBox {
  height: 100%;
}
</style>
