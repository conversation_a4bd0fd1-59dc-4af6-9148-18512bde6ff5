<template>
  <div class="page clearfix flex-column eem-cont-c1">
    <div class="mbJ3">
      <div class="fr">
        <div class="select-box fl">
          <el-date-picker
            type="date"
            class="selectbox"
            v-model="startTime"
            :picker-options="pickerOptions1"
            :editable="false"
            :clearable="false"
          ></el-date-picker>
          <span class="mlJ mrJ">至</span>
          <el-date-picker
            type="date"
            class="selectbox"
            v-model="endTime"
            :picker-options="pickerOptions2"
            :editable="false"
            :clearable="false"
          ></el-date-picker>
        </div>
        <div class="select-box fl mlJ1">
          <customElSelect
            prefix_in="层级"
            class="selectbox"
            v-model="levelOptions.value"
            placeholder="请选择"
            popper-class="green"
            @change="levelChange"
          >
            <el-option
              v-for="item in levelOptions.options"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </customElSelect>
        </div>
        <div class="select-box fl mlJ1">
          <customElSelect
            prefix_in="标签"
            class="selectbox"
            v-model="tagOptions.value"
            placeholder="请选择"
            popper-class="green"
            @change="tagChange"
          >
            <el-option
              v-for="item in tagOptions.options"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </customElSelect>
        </div>
        <el-button class="fl mlJ1" :disabled="isEdit" @click="handSearchBtn">
          查询
        </el-button>
        <el-button
          class="fl mlJ1"
          :disabled="isEdit"
          @click="toHandSearchBtn"
          type="primary"
          title="重置查询时间"
        >
          快速查询
        </el-button>
      </div>
    </div>
    <div class="eem-metrical-node-dialog order-box">
      <div
        id="metrical_node_window_table"
        class="hide_handsontable_operation"
      ></div>
      <div class="window-table__empty-block" v-if="isTabEmpty">
        <div class="window-table__empty-text">暂无数据</div>
      </div>
    </div>
  </div>
</template>
<script>
import { httping } from "@omega/http";
export default {
  name: "MetricalNodeWindow",
  components: {},

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data(vm) {
    return {
      startTime: "", //开始时间
      endTime: "", //结束时间
      pickerOptions1: {
        disabledDate(time) {
          return time.getTime() >= vm.$moment(vm.endTime).valueOf();
        }
      },
      pickerOptions2: {
        disabledDate(time) {
          return time.getTime() <= vm.$moment(vm.startTime).valueOf();
        }
      },
      levelOptions: {
        options: [],
        value: 0
      },
      tagOptions: {
        options: [],
        value: 0
      },
      isEdit: false,
      //表格配置
      tableHeaders: [],
      tableColumns: [],
      tableColumns11: [],
      tableData: [],
      oldHeaders: [],
      oldRows: [],
      oldTableData: [],
      tableHeight: 0, //表格高度
      nodeId: null, //节点ID
      nodeName: null, //节点名称
      nodeLabel: null, //节点modelLabel
      levelId: 0, //层级ID
      isTabEmpty: false //表格数据是否为空
    };
  },
  props: {
    dimIds: {
      type: Array,
      default: null
    },
    rowData: {
      type: Object,
      default: null
    }
  },
  watch: {
    dimIds: {
      handler: function (obj) {
        this.getLevelByDimId();
      },
      immediate: true
    },
    rowData: {
      handler: function (obj) {
        this.nodeId = obj.id;
        this.nodeName = obj.levelObj2;
        this.nodeLabel = obj.nodeLabel;
        this.startTime = new Date("1900-01-01 00:00:00");
        this.endTime = new Date();
        this.isEdit = false;
        var type = this.levelOptions.options[0]
          ? this.levelOptions.options[0].id
          : 0;
        if (type) {
          this.levelOptions.value = type;
          this.levelChange(type);
          this.getTableData();
        }
      },
      immediate: true
    },
    tableData: {
      handler: function (arr) {},
      immediate: true
    }
  },

  methods: {
    //点击修改层级
    levelChange: function (value) {
      this.levelId = value;
      const levelObj = this.levelOptions.options.find(
        item => item.id === value
      );
      var tagArr = levelObj ? levelObj.children : [];
      tagArr = this._.cloneDeep(tagArr);
      tagArr.unshift({
        id: 0,
        name: "全部"
      });
      this.tagOptions.options = tagArr;
      this.tagOptions.value = this._.get(tagArr, "[0].id", 0);
    },
    //点击修改标签
    tagChange: function (value) {},
    handSearchBtn: function () {
      var _this = this;
      var startTime = this.startTime ? this.startTime.getTime() : "";
      var endTime = this.endTime ? this.endTime.getTime() : "";
      if (!startTime || !endTime) {
        this.$message.warning("请选择查询时间！");
        return;
      }
      if (startTime >= endTime) {
        this.$message.warning("结束时间不能早于或等于开始时间！");
        return;
      }
      this.getTableData();
    },
    toHandSearchBtn: function () {
      this.startTime = new Date("1900-01-01 00:00:00");
      this.endTime = new Date();
      this.getTableData();
    },
    // 设置编辑表格
    editTableData: function () {
      this.isEdit = true;
      this.toEditTable();
    },
    // 保存表格修改
    saveTableData: function () {
      this.isEdit = false;
      this.toNoEditTable();
    },
    // 取消loading
    cancelLoad: function () {
      var _this = this;
      var time = null;
      window.clearTimeout(time);
    },
    init: function () {
      this.startTime = new Date("1900-01-01 00:00:00");
      this.endTime = new Date();
    },

    getMetricalNode: function () {
      var _this = this;
      var dataObject = _this.tableData;
      var tableBtnRender = function (
        instance,
        td,
        row,
        col,
        prop,
        value,
        cellProperties
      ) {
        //添加自定义的图片，并给图片的chick添加事件
        var escaped = Handsontable.helper.stringify(value);
        var text;
        var textDle;
        var text1;
        var text2;
        var row_index = cellProperties.row;
        text = document.createElement("span");
        text.innerHTML = "编辑";
        text.className = "handsontableHandle";
        text.style = "cursor:pointer;width:16px;"; //鼠标移上去变手型
        Handsontable.dom.addEvent(text, "click", function (event) {
          _this.isEdit = true;
          _this.handleHotSettings(row_index, col, value);
        });

        text1 = document.createElement("span");
        text1.innerHTML = "保存";
        text.className = "handsontableHandle";
        text1.style = "cursor:pointer;width:20px;"; //鼠标移上去变手型
        Handsontable.dom.addEvent(text1, "click", function (event) {
          _this.handleHotSettings(row_index, col, value);
        });

        text2 = document.createElement("span");
        text2.innerHTML = "取消";
        text.className = "handsontableHandle";
        text2.style = "cursor:pointer;margin-left:15px;width:16px;"; //鼠标移上去变手型
        Handsontable.dom.addEvent(text2, "click", function (event) {
          _this.isEdit = false;
          _this.handleHotSettings(row_index, col, 3);
        });

        textDle = document.createElement("span");
        textDle.innerHTML = "删除";
        text.className = "handsontableHandle";
        textDle.style = "cursor:pointer;margin-left:15px;width:16px;"; //鼠标移上去变手型
        Handsontable.dom.addEvent(textDle, "click", function (event) {
          _this.isEdit = false;
          _this.handleDelete(row_index, col, 3);
        });

        Handsontable.dom.empty(td);
        if (value == 1) {
          td.appendChild(text);
          td.appendChild(textDle);
        } else {
          td.appendChild(text1);
          td.appendChild(text2);
        }
        td.style.textAlign = "left"; //图片居中对齐
        return td;
      };
      var hotElement = document.querySelector("#metrical_node_window_table");
      // var hotElementContainer = hotElement.parentNode;
      var historyMsg = {
        data: "isEdit",
        renderer: tableBtnRender,
        readOnly: true
      };
      _this.tableColumns.push(historyMsg);
      _this.tableColumns11.push(historyMsg);

      var hotSettings = {
        data: dataObject,
        hiddenColumns: {
          columns: [5, 6]
        },
        columns: _this.tableColumns11,
        readOnly: true,
        stretchH: "all",
        autoWrapRow: false,
        height: "100%",
        // maxRows: 22,
        rowHeaders: true,
        colHeaders: _this.tableHeaders,
        dropdownMenu: true, //下拉式菜单
        filters: true, //过滤器
        fillHandle: false //设置不能自定填充
      };
      // _this.hot = new Handsontable(hotElement, hotSettings);
      if (_this.hot) {
        _this.toNoEditTable();
      } else {
        _this.hot = new Handsontable(hotElement, hotSettings);
      }
    },
    //配置表格编辑
    toEditTable: function () {
      var _this = this;
      var dataObject = _this.tableData;
      var hotSettings = {
        data: dataObject,
        columns: _this.tableColumns,
        readOnly: false,
        stretchH: "all",
        autoWrapRow: true,
        // rowHeaders: true,
        colHeaders: _this.tableHeaders,
        dropdownMenu: true, //下拉式菜单
        filters: true //过滤器
      };
      _this.hot.updateSettings(hotSettings);
    },
    //配置表格不可编辑
    toNoEditTable: function () {
      var _this = this;
      var dataObject = _this.tableData;
      var hotSettings = {
        data: dataObject,
        columns: _this.tableColumns11,
        readOnly: true,
        stretchH: "all",
        autoWrapRow: true,
        // rowHeaders: true,
        colHeaders: _this.tableHeaders
      };
      _this.hot.updateSettings(hotSettings);
    },
    handleHotSettings: function (row, col, value) {
      var _this = this;
      var rowNum = row;
      var dataObject = _this.tableData;

      if (value == 1) {
        var list = dataObject;
        var len = list.length;
        var isOk = true;
        for (var i = 0; i < len; i++) {
          if (list[i].isEdit == 2) {
            isOk = false;
          }
        }
        if (!isOk) {
          _this.$message.warning("只能进行单个历史记录修改！");
          return;
        }
        dataObject[row].isEdit = 2;
        let hotSettings = {
          data: dataObject,
          columns: _this.tableColumns,
          readOnly: true,
          cells: function (row, col, prop) {
            var cellMeta = {};
            if (row == rowNum && (col == 1 || col == 2 || col == 3)) {
              cellMeta.readOnly = false;
            }
            return cellMeta;
          }
        };
        _this.hot.updateSettings(hotSettings);
      } else if (value == 3) {
        dataObject[row].isEdit = 1;
        let hotSettings = {
          data: dataObject,
          columns: _this.tableColumns11,
          readOnly: true,
          cells: function (row, col, prop) {
            var cellMeta = {};
            if (row == rowNum && (col == 1 || col == 2 || col == 3)) {
              cellMeta.readOnly = true;
            }
            return cellMeta;
          }
        };
        _this.hot.updateSettings(hotSettings);
        _this.handSearchBtn();
      } else {
        var params = _this.getPreserveParams(row);
        if (params) {
          dataObject[row].isEdit = 1;
          let hotSettings = {
            data: dataObject,
            columns: _this.tableColumns11,
            readOnly: true,
            cells: function (row, col, prop) {
              var cellMeta = {};
              if (row == rowNum && (col == 1 || col == 2 || col == 3)) {
                cellMeta.readOnly = true;
              }
              return cellMeta;
            }
          };
          _this.isEdit = false;
          _this.addOrUpdateNode(params);
          _this.hot.updateSettings(hotSettings);
        }
      }
    },
    handleDelete(row, col, value) {
      var _this = this;
      this.$confirm("是否删除此记录?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          var list = this.hot.getData();
          var rowData = list[row];
          var obj = {
            tagid: 0,
            label: this.nodeName,
            modelid: Number(this.nodeId),
            id: Number(rowData[6]),
            levelid: this.levelId,
            enabletime: new Date(this.getEnableTime(rowData[2])).getTime(),
            disabletime: this.$moment(rowData[3]).startOf("d").valueOf(),
            createtime: new Date(rowData[4]).getTime()
          };
          httping({
            url: "/eem-service/v1/dim/setting/nodeProperty",
            data: [obj],
            method: "PUT"
          }).then(res => {
            if (res.code === 0) {
              _this.$message.success("删除成功！");
              _this.handSearchBtn();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    //保存表格数据
    addOrUpdateNode: function (params) {
      var _this = this;
      var auth = _this.token;

      // var params = _this.getPreserveParams(row);
      if (!params) {
        return;
      }
      httping({
        url: "/eem-service/v1/dim/setting/nodeProperty",
        data: params,
        method: "PUT",
        
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          _this.isEdit = false;
          _this.$message.success("保存成功！");
          _this.handSearchBtn();
        }
      }).catch(res => {
        _this.$message.error("保存计量节点历史记录出错！");
      });
    },
    //获取保存表格数据接口参数
    getPreserveParams: function (row) {
      var _this = this;
      var oldHeaders = _this.oldHeaders;
      var list = _this.hot.getData();
      var oldList = _this.oldRows;
      var len = list.length;
      var oldArr = oldList[row];
      var rowData = _this.getOldArr(oldArr[4], list);
      var isOk = true;
      var params = [];
      for (var i = 0; i < len; i++) {
        var arr = oldList[i];
        var filDate1 = rowData[2].split(" ")[0];
        var filDate2 = arr[2].split(" ")[0];
        if (
          rowData[3] == arr[3] &&
          rowData[1] == arr[1] &&
          filDate1 == filDate2
        ) {
          isOk = false;
        }
      }
      if (!rowData[1] || !rowData[2]) {
        _this.$message.warning("标签和生效时间不能为空！");
        return false;
      }
      if (isOk) {
        var isEdit = _this.filHasProperty(
          rowData[1],
          oldHeaders[1].dropDownList
        );
        if (isEdit) {
          var levelObj = _this.levelOptions.options.find(
            item => item.name === rowData[0]
          );
          var tagArr = levelObj ? levelObj.children : [];
          var tagObj = tagArr.find(item => item.name === rowData[1]);
          var obj = {
            tagid: tagObj ? tagObj.id : Number(rowData[5]),
            label: _this.nodeName,
            modelid: Number(_this.nodeId),
            id: Number(rowData[6]),
            levelid: _this.levelId,
            enabletime: new Date(_this.getEnableTime(rowData[2])).getTime(),
            disabletime: this.$moment(rowData[3]).startOf("d").valueOf(),
            createtime: new Date(rowData[4]).getTime()
          };
          params.push(obj);
        } else {
          _this.$message.warning("请选择正确标签！");
          return false;
        }
      } else {
        _this.$message.warning("你未修改任何信息，不需要进行保存！");
        return false;
      }
      return params;
    },
    getCreateTime: function () {
      var date = new Date();
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      var d = date.getDate();
      if (m < 10) {
        m = "0" + m;
      }
      if (d < 10) {
        d = "0" + d;
      }
      return y + "-" + m + "-" + d + " 00:00:00.000";
    },
    getEnableTime: function (date) {
      date = new Date(date);
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      var d = date.getDate();
      if (m < 10) {
        m = "0" + m;
      }
      if (d < 10) {
        d = "0" + d;
      }
      return y + "-" + m + "-" + d + " 00:00:00.000";
    },
    getOldArr: function (nodeId, list) {
      list = list || [];
      var len = list.length;
      var arr = [];
      for (var i = 0; i < len; i++) {
        var oldNodeId = list[i][4];
        if (nodeId == oldNodeId) {
          arr = list[i];
          return arr;
        }
      }
      return [];
    },
    //判断输入是否是下拉框标签
    filHasProperty: function (fil, list) {
      fil = fil || "";
      list = list || [];
      var len = list.length;
      if (fil === "") {
        return true;
      }
      for (var i = 0; i < len; i++) {
        if (fil == list[i].name) {
          return true;
        }
      }
      return false;
    },
    //将标签名称转为Id
    filPropertyId: function (fil, list) {
      fil = fil || "";
      list = list || [];
      var len = list.length;
      for (var i = 0; i < len; i++) {
        if (fil == list[i].name) {
          return list[i].id;
        }
      }
      return 0;
    },
    //将层级名称转为Id
    filLevelId: function (fil) {
      var _this = this;
      fil = fil || "";
      var list = _this.levelOptions.options || [];
      var len = list.length;
      for (var i = 0; i < len; i++) {
        if (fil == list[i].label) {
          return list[i].id;
        }
      }
      return 0;
    },

    //获取层级列表信息
    getLevelByDimId: function () {
      var _this = this;
      var auth = _this.token; //身份验证

      httping({
        url: "/eem-service/v1/dim/setting/levels",
        data: this.dimIds,
        method: "POST",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          if (res.data && res.data.length > 0) {
            _this.filLevelList(res.data);
          }
        }
      }).catch(res => {
        _this.$message.error("获取层级列表数据失败");
      });
    },
    //过滤层级列表
    filLevelList: function (list) {
      this.levelOptions.options = list;
      const val = this._.get(list, "[0].id", 0);
      this.levelOptions.value = val;
      this.levelChange(val);
      this.getTableData();
    },
    // 获取表格数据
    getTableData: function () {
      var _this = this;
      var auth = _this.token; //身份验证
      const params = {
        levelId: _this.levelId,
        tagId: _this.tagOptions.value,
        startTime: this.startTime.getTime(),
        endTime: this.endTime.getTime(),
        node: {
          modelLabel: this.nodeLabel,
          id: Number(this.nodeId)
        }
      };
      httping({
        url: "/eem-service/v1/dim/setting/history/nodeProperty",
        method: "POST",
        data: params,
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          _this.filTableMsg(res.data);
        }
      }).catch(res => {
        _this.$message.error("获取关联计量节点历史记录失败！");
      });
    },
    //过滤表格配置信息
    filTableMsg: function (data) {
      if (!data) {
        return;
      }
      var _this = this;
      var headers = data.headers;
      var rows = data.rows;
      _this.oldHeaders = headers;
      _this.oldRows = rows;
      _this.tableHeaders = _this.filTableHeader(headers);
      _this.tableColumns = _this.filTableColumns(headers);
      _this.tableColumns11 = _this.filTableColumns11(headers);
      _this.tableData = _this.filTableData(_this.tableColumns, rows);
      _this.oldTableData = _this.filTableData(_this.tableColumns, rows);
      this.getMetricalNode();
      var len = _this.tableData.length;
      if (len === 0) {
        this.isTabEmpty = true;
      } else {
        this.isTabEmpty = false;
      }
    },
    //过滤表格头部名称
    filTableHeader: function (list) {
      list = list || [];
      var len = list.length;
      var arr = [];
      for (var i = 0; i < len; i++) {
        var headerName = list[i].headerName;
        arr.push(headerName);
      }
      arr.push("操作");
      return arr;
    },
    // 过滤表格配置
    filTableColumns: function (list) {
      var _this = this;
      list = list || [];
      var arr = [];
      arr = [
        {
          data: "hierachy",
          type: "text",
          className: "htLeft",
          readOnly: true
        },
        {
          data: "property",
          type: "autocomplete",
          source: _this.filDropDownList(list[1].dropDownList),
          strict: true, //值为true，严格匹配
          allowInvalid: true
        },
        {
          data: "enableTime",
          type: "date",
          dateFormat: "YYYY-MM-DD"
        },
        {
          data: "disabletime",
          type: "date",
          dateFormat: "YYYY-MM-DD"
        },
        {
          data: "createTime",
          type: "date",
          dateFormat: "YYYY-MM-DD",
          readOnly: true
        },
        {
          data: "tagId",
          type: "text",
          className: "htLeft",
          readOnly: true
        },
        {
          data: "id",
          type: "text",
          className: "htLeft",
          readOnly: true
        }
      ];
      return arr;
    },
    // 过滤表格配置
    filTableColumns11: function (list) {
      var arr = [];
      arr = [
        {
          data: "hierachy",
          type: "text",
          className: "htLeft",
          readOnly: true
        },
        {
          data: "property",
          type: "text",
          className: "htLeft"
        },
        {
          data: "enableTime",
          type: "text",
          className: "htLeft"
        },
        {
          data: "disabletime",
          type: "text",
          className: "htLeft"
        },
        {
          data: "createTime",
          type: "text",
          className: "htLeft",
          readOnly: true
        },
        {
          data: "tagId",
          type: "text",
          className: "htLeft",
          readOnly: true
        },
        {
          data: "id",
          type: "text",
          className: "htLeft",
          readOnly: true
        }
      ];
      return arr;
    },
    filDropDownList: function (list) {
      list = list || [];
      var len = list.length;
      var arr = [];
      for (var i = 0; i < len; i++) {
        var name = list[i].name;
        arr.push(name);
      }
      return arr;
    },
    // 过滤表格数据
    filTableData: function (list, data) {
      list = list || [];
      data = data || [];
      var len = data.length;
      var arr = [];
      for (var i = 0; i < len; i++) {
        var leng = list.length;
        var obj = {};
        for (var j = 0; j < leng; j++) {
          if (j == 2 || j == 3) {
            if (data[i][j]) {
              obj[list[j].data] = data[i][j].split(" ")[0];
            } else {
              obj[list[j].data] = "1900-01-01";
            }
          } else {
            obj[list[j].data] = data[i][j];
          }
        }
        obj.isEdit = 1;
        arr.push(obj);
      }

      return arr;
    },

    // 监听窗口大小，修改表格滚动条，控制表格高度
    handleTableHeight: function () {
      var _this = this;
      window.onresize = function () {
        var t1 = null;
        window.clearTimeout(t1);
        t1 = setTimeout(function () {
          var height = $(".eem-metrical-node-dialog").height();
          _this.tableHeight = height - 47;
        }, 100);
      };
      window.onresize();
    }
  },
  created: function () {},
  mounted: function () {
    var _this = this;
    setTimeout(function () {
      _this.init();
    }, 100);
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .selectbox {
    width: 150px !important;
    :deep(.el-input__inner) {
      @include padding_right(J1);
    }
  }
}

.eem-metrical-node-dialog {
  width: 100%;
  height: 360px;
  border: 1px solid;
  @include border_color(B1);
  border-top: none;
}

.window-table__empty-block {
  position: absolute;
  top: 145px;
  overflow: hidden;
  display: block;
  width: calc(100% - 20px);
}

.window-table__empty-text {
  text-align: center;
  width: 100%;
  height: 40px;
  line-height: 40px;
}

#metrical_node_window_table :deep(table thead tr th) {
  height: 30px;
  line-height: 30px;
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}
#metrical_node_window_table :deep(.handsontable th) {
  text-align: left;
}
#metrical_node_window_table :deep(table tbody tr th) {
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}

#metrical_node_window_table :deep(table tbody tr td) {
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}

#metrical_node_window_table :deep(table thead tr th .changeType) {
  margin-top: 8px;
}
#metrical_node_window_table :deep(table thead tr th .colHeader) {
  font-weight: 700;
}
#metrical_node_window_table :deep(.handsontable .changeType) {
  @include font_color(T1);
  @include background_color(BG1);
  @include border_color(B1);
}
#metrical_node_window_table :deep(.handsontable .changeType:hover) {
  @include font_color(T1);
  @include background_color(BG1);
  @include border_color(B1);
}
#metrical_node_window_table :deep(.handsontableHandle) {
  @include font_color(ZS);
}
</style>
