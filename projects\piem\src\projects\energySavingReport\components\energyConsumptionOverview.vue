<template>
  <div class="energy-overview-box">
    <div class="left-box mrJ4">
      <CetChart
        style="height: 100%; width: 100%"
        v-bind="CetChart_energyConsumption"
        :initOptions="rendererType"
      ></CetChart>
    </div>
    <div class="right-box">
      <div v-for="(item, index) in overviewList" :key="index" class="item">
        <div class="name mbJ3">{{ item.name }}</div>
        <div>
          <span
            class="value"
            :class="item.key === 'gatherSysSavingRate' ? 'text-ZS' : ''"
          >
            {{ formatNumberWithPrecision(reportData[item.key], 2) }}
          </span>
          <span class="unit">{{ item.unit }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import common from "eem-utils/common";
export default {
  name: "energyConsumptionOverview",
  components: {},
  props: {
    reportData: {
      type: Object
    },
    overviewList: {
      type: Array
    }
  },
  data() {
    return {
      rendererType: { renderer: "svg" },
      // energyConsumption组件
      CetChart_energyConsumption: {
        //组件输入项
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            },
            confine: true
          },
          grid: {
            top: 40,
            bottom: 10,
            left: 20,
            right: 0,
            containLabel: true
          },
          legend: {
            show: false
          },
          xAxis: {
            type: "category",
            axisTick: { show: false },
            axisLabel: {
              show: true,
              fontSize: 12,
              interval: 0,
              color: "#798492"
            }
          },
          yAxis: {
            name: $T("单位(kWh)"),
            type: "value",
            axisLabel: {
              color: "#798492"
            },
            axisLine: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: "#E0E4E8",
                type: "dashed"
              }
            },
            nameTextStyle: {
              color: "#798492",
              padding: [0, 0, 10, 0]
            }
          },
          series: [
            {
              name: $T("能耗"),
              type: "bar",
              barWidth: 18,
              smooth: false,
              showSymbol: true,
              colorBy: "data",
              color: ["#29B061", "#FFC168"],
              data: [],
              markLine: {
                symbol: "none",
                label: {
                  position: "middle",
                  color: "#13171F",
                  fontWeight: "bold",
                  fontSize: 12,
                  formatter: params => {
                    return "";
                  }
                },
                lineStyle: {
                  width: 1,
                  color: "#F95E5A",
                  type: [3, 2]
                },
                data: []
              }
            }
          ]
        }
      }
    };
  },
  watch: {
    reportData(val) {
      const overview = this.overviewList;
      const gatherSysEnergy = common.formatNumberWithPrecision(
        val[overview[0].key],
        2
      );
      const gatherSysOptimizationEnergy = common.formatNumberWithPrecision(
        val[overview[1].key],
        2
      );
      this.CetChart_energyConsumption.options.series[0].data = [
        [overview[0].text, gatherSysEnergy],
        [overview[1].text, gatherSysOptimizationEnergy]
      ];
      this.CetChart_energyConsumption.options.series[0].markLine.data = [
        {
          yAxis: gatherSysEnergy
        },
        {
          yAxis: gatherSysOptimizationEnergy
        }
      ];
      this.CetChart_energyConsumption.options.series[0].markLine.label.formatter =
        params => {
          if (params.dataIndex !== 0) return "";
          return (
            overview[2].text +
            "：" +
            common.formatNumberWithPrecision(val[overview[2].key], 2) +
            overview[2].unit
          );
        };

      this.CetChart_energyConsumption.options.yAxis.name =
        "单位(" + overview[0].unit + ")";
    }
  },
  methods: {
    formatNumberWithPrecision(...args) {
      return common.formatNumberWithPrecision(...args);
    }
  }
};
</script>

<style lang="scss" scoped>
.energy-overview-box {
  height: 200px;
  display: flex;
  .left-box {
    width: 230px;
    height: 100%;
  }
  .right-box {
    flex: 1;
    height: 100%;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    .item {
      background: #f8fafb;
      width: calc(50% - 4px);
      height: calc(50% - 4px);
      padding: 8px 12px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .name {
        font-size: 12px;
      }
      .value {
        font-size: 14px;
        color: #13171f;
        font-weight: 500;
      }
      .unit {
        font-size: 12px;
        color: #989898;
      }
    }
  }
}
.text-ZS {
  color: #29b061 !important;
}
</style>
