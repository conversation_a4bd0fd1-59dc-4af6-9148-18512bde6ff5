<template>
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" append-to-body>
    <div class="eem-cont-c1">
      <div class="mbJ3">
        <CetTree
          class="tree"
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          :searchText_in.sync="CetTree_1.searchText_in"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
        ></CetTree>
      </div>
      <div class="basic-box">
        <div class="basic-box-label">{{ $T("已选择") }}</div>
        <ElInput
          v-model="ElInput_1.value"
          disabled
          v-bind="ElInput_1"
          v-on="ElInput_1.event"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-error"
            style="cursor: pointer"
            @click="deleteCurrentNode"
          ></i>
        </ElInput>
      </div>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import customApi from "@/api/custom.js";
import ELECTRICAL_DEVICE from "@/store/electricaldevice.js";
import TREE_PARAMS from "@/store/treeParams.js";
export default {
  name: "measureObj",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    modelLabelArr() {
      var arr = ELECTRICAL_DEVICE.map(item => item.value);
      const arr1 = [
        "civicpipe",
        "pipeline",
        "pump",
        "coolingtower",
        "windset",
        "coldwatermainengine",
        "aircompressor",
        "colddryingmachine",
        "dryingmachine",
        "boiler",
        "steamboiler",
        "linesegment"
      ];
      arr = arr.concat(arr1);
      return arr;
    }
  },

  data() {
    return {
      currentNode: null,
      CetDialog_1: {
        title: $T("选择测量对象"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请选择测量对象"),
        style: {
          width: "100%"
        },
        event: {}
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.CetTree_1.searchText_in = "";
      this.getNodeTree();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    getNodeTree() {
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.sandingBok,
        treeReturnEnable: true
      };
      if (!TREE_PARAMS.sandingBok) {
        data.subLayerConditions = [
          {
            modelLabel: "room",
            depth: 1,
            filter: {
              composemethod: false,
              expressions: [
                {
                  limit: null,
                  operator: "NE",
                  prop: "roomtype"
                }
              ]
            }
          },
          { modelLabel: "civicpipe", depth: 2 },
          { modelLabel: "pipeline", depth: 2 },
          { modelLabel: "pump", depth: 2 },
          { modelLabel: "coolingtower", depth: 2 },
          { modelLabel: "windset", depth: 2 },
          { modelLabel: "coldwatermainengine", depth: 2 },
          { modelLabel: "aircompressor", depth: 2 },
          { modelLabel: "colddryingmachine", depth: 2 },
          { modelLabel: "dryingmachine", depth: 2 },
          { modelLabel: "boiler", depth: 2 },
          { modelLabel: "steamboiler", depth: 2 },
          { modelLabel: "linesegment", depth: 3 }
        ];
        ELECTRICAL_DEVICE.forEach(item => {
          data.subLayerConditions.push({
            modelLabel: item.value,
            depth: 2
          });
        });
      }
      customApi.getNodeTree(data).then(res => {
        if (res.code === 0) {
          const resDate = res.data || [];
          _this.CetTree_1.inputData_in = resDate;
          _this.CetTree_1.selectNode = resDate[0];
          setTimeout(() => {
            if (
              _this.inputData_in.monitorid &&
              _this.inputData_in.monitorlabel
            ) {
              // 进行复选
              _this.CetTree_1.selectNode = {
                id: _this.inputData_in.monitorid,
                modelLabel: _this.inputData_in.monitorlabel,
                tree_id:
                  _this.inputData_in.monitorlabel +
                  "_" +
                  _this.inputData_in.monitorid
              };
              _this.CetTree_1_currentNode_out({
                id: _this.inputData_in.monitorid,
                modelLabel: _this.inputData_in.monitorlabel,
                name: _this.inputData_in.name
              });
            } else {
              _this.CetTree_1.selectNode = _this.CetTree_1.inputData_in[0];
              _this.CetTree_1_currentNode_out(_this.CetTree_1.selectNode);
            }
          }, 0);
        }
      });
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      // if (this.currentNode) {
      this.$emit("CetButton_confirm_out", this.currentNode);
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
      // } else {
      //   this.$message({
      //     type: "warning",
      //     message: "请选择设备节点"
      //   });
      // }
    },
    CetTree_1_currentNode_out(val) {
      if (val) {
        if (this.modelLabelArr.indexOf(val.modelLabel) !== -1) {
          this.ElInput_1.value = val.name;
          this.currentNode = val;
        } else {
          this.ElInput_1.value = "";
          this.currentNode = null;
        }
      }
    },
    deleteCurrentNode() {
      this.ElInput_1.value = "";
      this.currentNode = null;
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.tree {
  height: 500px;
  :deep(.el-tree) {
    overflow: auto;
  }
}
</style>
