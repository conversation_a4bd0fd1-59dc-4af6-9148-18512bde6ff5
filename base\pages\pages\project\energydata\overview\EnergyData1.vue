<template>
  <div class="page eem-common">
    <el-row :gutter="$J3" :style="{ height: language ? '250px' : '194px' }">
      <el-col :span="6" style="height: 100%">
        <div class="cont11-bg" style="height: 100%">
          <ContImg v-if="projectMsg.pic" :imgPath_in="projectMsg.pic"></ContImg>
          <img
            v-if="!projectMsg.pic"
            src="./assets/u3131.png"
            style="height: 100%; width: 100%"
            alt="暂无上传图片"
          />
          <div class="cont11-label">
            <span>{{ $T("项目概览") }}：{{ projectMsg.name || "--" }}</span>
            <span>
              {{ $T("联系电话") }}：{{ projectMsg.contactnumber || "--" }}
            </span>
            <span>
              {{ $T("规模") }}：{{
                projectMsg.annualoutputvalue | annualoutputvalueFormat
              }}
            </span>
            <span>{{ $T("地址") }}：{{ projectMsg.address || "--" }}</span>
          </div>
        </div>
      </el-col>
      <el-col :span="6" style="height: 100%">
        <div class="eem-container fullheight">
          <div class="common-title-H3">{{ $T("折标总能耗") }}</div>
          <div class="card-cont mtJ2">
            <span class="label">{{ $T("当日折标总能耗") }}（tce）</span>
            <span
              class="card-cont-num-danger text-ellipsis"
              :title="
                projectConsumption.sce
                  ? (
                      Number(projectConsumption.sce.dayConsumption) / 1000
                    ).toFixed(2)
                  : '--'
              "
            >
              {{
                projectConsumption.sce
                  ? (
                      Number(projectConsumption.sce.dayConsumption) / 1000
                    ).toFixed(2)
                  : "--"
              }}
            </span>
          </div>
          <div class="card-cont mtJ2">
            <span class="label">{{ $T("当月折标总能耗") }}（tce）</span>
            <span
              class="card-cont-num-success text-ellipsis"
              :title="
                projectConsumption.sce
                  ? (
                      Number(projectConsumption.sce.monthConsumption) / 1000
                    ).toFixed(2)
                  : '--'
              "
            >
              {{
                projectConsumption.sce
                  ? (
                      Number(projectConsumption.sce.monthConsumption) / 1000
                    ).toFixed(2)
                  : "--"
              }}
            </span>
          </div>
        </div>
      </el-col>
      <el-col :span="6" style="height: 100%">
        <div class="eem-container fullheight">
          <div class="common-title-H3">{{ $T("总用电") }}</div>
          <div class="card-cont mtJ2">
            <span class="label">{{ $T("当日用电量") }}（kWh）</span>
            <span
              class="card-cont-num-danger text-ellipsis"
              :title="
                projectConsumption.electric
                  ? Number(projectConsumption.electric.dayConsumption).toFixed(
                      2
                    )
                  : '--'
              "
            >
              {{
                projectConsumption.electric
                  ? Number(projectConsumption.electric.dayConsumption).toFixed(
                      2
                    )
                  : "--"
              }}
            </span>
          </div>
          <div class="card-cont mtJ2">
            <span class="label">{{ $T("当月用电量") }}（kWh）</span>
            <span
              class="card-cont-num-success text-ellipsis"
              :title="
                projectConsumption.electric
                  ? Number(
                      projectConsumption.electric.monthConsumption
                    ).toFixed(2)
                  : '--'
              "
            >
              {{
                projectConsumption.electric
                  ? Number(
                      projectConsumption.electric.monthConsumption
                    ).toFixed(2)
                  : "--"
              }}
            </span>
          </div>
        </div>
      </el-col>
      <el-col :span="6" style="height: 100%">
        <div class="eem-container fullheight">
          <div class="common-title-H3">
            {{ $T("总用水") }}
            <el-tooltip effect="light" placement="bottom-start">
              <i class="el-icon-question"></i>
              <div slot="content" class="pricingContent">
                总水表更新时间为当日凌晨0点，每日更新一次。
                <br />
                {{
                  `所以当日总用水量实际为 ${$moment()
                    .add(-1, "d")
                    .format("YYYY/MM/DD")} 00:00~${$moment().format(
                    "YYYY/MM/DD"
                  )} 00:00 期间用水量。`
                }}
              </div>
            </el-tooltip>
          </div>
          <div class="card-cont mtJ2">
            <span class="label">{{ $T("当日用水量") }}（T）</span>
            <span
              class="card-cont-num-danger text-ellipsis"
              :title="
                projectConsumption.water
                  ? Number(projectConsumption.water.dayConsumption).toFixed(2)
                  : '--'
              "
            >
              {{
                projectConsumption.water
                  ? Number(projectConsumption.water.dayConsumption).toFixed(2)
                  : "--"
              }}
            </span>
          </div>
          <div class="card-cont mtJ2">
            <span class="label">{{ $T("当月用水量") }}（T）</span>
            <span
              class="card-cont-num-success text-ellipsis"
              :title="
                projectConsumption.water
                  ? Number(projectConsumption.water.monthConsumption).toFixed(2)
                  : '--'
              "
            >
              {{
                projectConsumption.water
                  ? Number(projectConsumption.water.monthConsumption).toFixed(2)
                  : "--"
              }}
            </span>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="0" style="height: 360px" class="gapJ2">
      <el-col :span="24" style="height: 360px">
        <div class="eem-container fullheight">
          <ElectricityTrend
            :queryNode_in="clickNode"
            :inputData_in="ElectricityTrend_1.inputData_in"
            :dataConfig="ElectricityTrend_1.dataConfig"
            :customParams_in="ElectricityTrend_1.customParams_in"
            :visibleTrigger_in="ElectricityTrend_1.visibleTrigger_in"
            :refreshTrigger_in="ElectricityTrend_1.refreshTrigger_in"
            @clickChart="ElectricityTrend_1_clickChart"
          ></ElectricityTrend>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="0" style="display: flex" class="gapJ2">
      <el-col :span="24">
        <div class="eem-container fullheight">
          <ItemizedByNode
            :queryNode_in="clickNode"
            :inputData_in="ItemizedByNode_1.inputData_in"
            :dataConfig="ItemizedByNode_1.dataConfig"
            :customParams_in="ItemizedByNode_1.customParams_in"
            :visibleTrigger_in="ItemizedByNode_1.visibleTrigger_in"
            :refreshTrigger_in="ItemizedByNode_1.refreshTrigger_in"
            @clickChart="ItemizedByNode_1_clickChart"
          ></ItemizedByNode>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="0" style="height: 300px" class="gapJ2">
      <el-col :span="24" style="height: 100%">
        <div class="eem-container fullheight column">
          <div class="clearfix mbJ2">
            <span class="common-title-H3">{{ $T("月报警趋势曲线") }}</span>
          </div>
          <div style="flex: 1">
            <CetChart
              :inputData_in="CetChart_5.inputData_in"
              v-bind="CetChart_5.config"
            />
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="$J3" style="height: 290px" class="gapJ2">
      <el-col :span="12" style="height: 100%">
        <div class="eem-container fullheight column">
          <div class="clearfix mbJ2">
            <span class="common-title-H3">{{ $T("月报警趋势曲线") }}</span>
          </div>
          <div style="flex: 1">
            <CetChart
              :inputData_in="CetChart_2.inputData_in"
              v-bind="CetChart_2.config"
            />
          </div>
        </div>
      </el-col>
      <el-col :span="12" style="height: 100%">
        <div class="eem-container fullheight column">
          <div class="clearfix mbJ2">
            <span class="common-title-H3">{{ $T("月能耗事件占比") }}</span>
          </div>
          <div style="flex: 1">
            <CetChart
              :inputData_in="CetChart_6.inputData_in"
              v-bind="CetChart_6.config"
            />
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="$J3" style="height: 250px" class="gapJ2">
      <el-col :span="24" style="height: 100%">
        <div class="eem-container fullheight">
          <EnergyEfficiency
            :queryNode_in="clickNode"
            :inputData_in="EnergyEfficiency_1.inputData_in"
            :dataConfig="EnergyEfficiency_1.dataConfig"
            :customParams_in="EnergyEfficiency_1.customParams_in"
            :visibleTrigger_in="EnergyEfficiency_1.visibleTrigger_in"
            :refreshTrigger_in="EnergyEfficiency_1.refreshTrigger_in"
            @clickChart="EnergyEfficiency_1_clickChart"
          ></EnergyEfficiency>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import ElectricityTrend from "./ElectricityTrend";
import ItemizedByNode from "./ItemizedByNode";
import EnergyEfficiency from "./EnergyEfficiency";
import ContImg from "./ContImg";
import { httping } from "@omega/http";
export default {
  name: "EnergyData1",
  components: {
    ElectricityTrend,
    ItemizedByNode,
    EnergyEfficiency,
    ContImg
  },
  props: {
    clickNode: {
      type: Object
    },
    nodeIsLeaf: {
      type: String
    },
    projectInfo_in: {
      type: Object
    }
  },
  filters: {
    annualoutputvalueFormat(val) {
      if (val || val === 0) {
        if (val < 100000) {
          return val + "万元";
        } else {
          return (val / 10000).toFixed(4) + "亿元";
        }
      }
      return "--";
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectInfo() {
      var vm = this;
      return vm.$store.state.projectInfo;
    },
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },

  data() {
    return {
      ElectricityTrend_1: {
        visibleTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        currentNode_in: null,
        inputData_in: null,
        customParams_in: null,
        dataConfig: {
          title: $T("用电趋势"),
          // subtitle: "稳定性：本月良好",
          queryUrl: "/eem-service/v1/energy/consumption",
          modelLabel: "",
          type: "POST",
          showType: false,
          showTime: true,
          showExport: true
        }
      },
      CetChart_2: {
        inputData_in: null,
        config: {
          options: {
            title: {
              text: "",
              left: "center"
            },
            legend: {
              bottom: 0
            },
            tooltip: {
              trigger: "item",
              formatter: "{a} <br/>{b}"
            },
            series: [
              {
                name: "报警统计",
                type: "pie",
                radius: "60%",
                center: ["50%", "40%"],
                data: [],
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: "rgba(0, 0, 0, 0.5)"
                  }
                }
              }
            ]
          }
        }
      },
      ItemizedByNode_1: {
        visibleTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        currentNode_in: null,
        inputData_in: null,
        customParams_in: null,
        dataConfig: {
          title: $T("分项用能统计"),
          // subtitle: "节能性高：E栋",
          queryUrl: "/eem-service/v1/energy/consumption",
          modelLabel: "",
          type: "POST",
          showType: false,
          showTime: true,
          showExport: true
        }
      },
      CetChart_5: {
        inputData_in: null,
        config: {
          options: {
            legend: {
              data: [$T("能耗报警")]
            },
            tooltip: {
              trigger: "axis"
            },
            grid: {
              left: "16",
              right: "16",
              bottom: "8",
              containLabel: true
            },
            dataset: {
              source: []
            },
            xAxis: { type: "category" },
            yAxis: {
              minInterval: 1
            },
            series: [
              {
                type: "line",
                name: $T("能耗报警"),
                smooth: true,
                encode: { x: "product", y: "value" }
              }
            ]
          }
        }
      },
      CetChart_6: {
        inputData_in: null,
        config: {
          options: {
            legend: {
              bottom: 0
            },
            tooltip: {
              trigger: "item",
              formatter: "{a} <br/>{b}"
            },
            series: [
              {
                name: "能耗事件占比",
                type: "pie",
                radius: "60%",
                center: ["50%", "40%"],
                avoidLabelOverlap: false,
                data: []
              }
            ]
          }
        }
      },
      projectMsg: {
        pic: "",
        name: "",
        contactnumber: "",
        address: "",
        annualoutputvalue: ""
      },
      projectConsumption: {
        sce: {
          itemId: "sce",
          monthConsumption: 0,
          dayConsumption: 0
        },
        water: {
          itemId: "water",
          monthConsumption: 0,
          dayConsumption: 0
        },
        electric: {
          itemId: "electric",
          monthConsumption: 0,
          dayConsumption: 0
        }
      },
      EnergyEfficiency_1: {
        visibleTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        currentNode_in: null,
        inputData_in: null,
        customParams_in: null,
        dataConfig: {
          title: $T("月能效对标统计"),
          // subtitle: "人均用能超过其他标准",
          queryUrl: "/eem-service/v1/energy/consumption",
          modelLabel: "",
          type: "POST",
          showType: false
        }
      }
    };
  },
  watch: {
    clickNode: {
      deep: true,
      handler: function (val, oldVal) {
        if (!(this._.get(val, "id") && this._.get(val, "modelLabel"))) return;
        if (
          val.id === this._.get(oldVal, "id") &&
          val.modelLabel === this._.get(oldVal, "modelLabel")
        ) {
          return;
        }
        if (val.modelLabel === "project") {
          this.getProjectMsg();
          this.getProjectConsumption();
          this.getOverViewEventAnalysis();
          this.getEventAnalysisNum();
        }
      }
    }
  },

  methods: {
    //获取项目详情
    getProjectMsg() {
      this.projectMsg = {
        pic: this._.get(this.projectInfo, "pic", ""),
        name: this._.get(this.projectInfo, "name", ""),
        contactnumber: this._.get(this.projectInfo, "contactnumber", ""),
        address: this._.get(this.projectInfo, "address", ""),
        annualoutputvalue: this._.get(this.projectInfo, "annualoutput", "")
      };
    },
    //获取折标总能耗，总用电，总用水
    getProjectConsumption() {
      var _this = this,
        auth = _this.token; //身份验证
      var params = {
        modelLabel: this.clickNode.modelLabel,
        id: this.clickNode.id
      };
      httping({
        url: "/eem-service/v1/energy/projectConsumption",
        data: params,
        method: "POST",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          _this.projectConsumption = res.data;
        }
      });
    },
    //点击用电趋势
    ElectricityTrend_1_clickChart(val) {
      console.log(val);
      this.$emit("pageJump", val);
    },
    //点击分项用电统计
    ItemizedByNode_1_clickChart(val) {
      console.log(val);
      this.$emit("pageJump", val);
    },
    //点击能效对标统计组件
    EnergyEfficiency_1_clickChart(val) {
      console.log(val);
      this.$emit("pageJump", val);
    },
    // 获取事件类型个数统计
    getOverViewEventAnalysis() {
      var _this = this;
      // var queryBody = this._.cloneDeep(this.queryData);
      var clickNode = this.clickNode;
      if (!clickNode) {
        return;
      }
      var params = {
        endTime: this.$moment().endOf("M").valueOf() + 1,
        projectId: clickNode.id,
        // modelLabel: clickNode.modelLabel,
        eventTypes: [702, 708, 701], //701:能耗报警；702：能耗报警；708：能效报警
        // types:[16,17,20,23],
        startTime: this.$moment().startOf("M").valueOf()
      };

      var queryOption = {
        url: "/eem-service/v1/alarmEvent/queryEventCount",
        method: "POST",
        data: params
      };

      httping(queryOption).then(function (response) {
        if (response.code === 0) {
          // console.log(response.data);
          var data = _this._.get(response, ["data"], {});
          _this.filEventAnalysis(data);
        }
      });
    },
    filEventAnalysis(data) {
      var _this = this;
      var eventType_17 = data[702] || 0, //能耗报警事件个数
        eventType_20 = data[708] || 0, // 能效报警
        eventType_23 = data[701] || 0; // 能耗报警
      var eventType1 = eventType_23, //预警事件个数
        eventType2 = eventType_17, //告警事件个数
        eventType3 = eventType_23; //能耗预警事件
      var num1 =
        ((eventType1 + eventType3) / (eventType1 + eventType2 + eventType3)) *
          100 || 0;
      num1 = num1.toFixed(2);
      var num2 =
        (eventType2 / (eventType1 + eventType2 + eventType3)) * 100 || 0;
      num2 = num2.toFixed(2);
      this.CetChart_2.config.options.series[0].data = [
        { value: eventType_20, name: `${$T("能效报警")}(${eventType_20})` },
        { value: eventType_17, name: `${$T("能耗报警")}(${eventType_17})` },
        { value: eventType_23, name: `${$T("能耗预警")}(${eventType_23})` }
      ];
      this.CetChart_6.config.options.series[0].data = [
        { value: num1, name: `${$T("报警事件")}(${num1}%)` },
        { value: num2, name: `${$T("告警事件")}(${num2}%)` }
      ];
    },
    // 获取报警趋势曲线图表数据
    getEventAnalysisNum() {
      var _this = this;
      // var queryBody = this._.cloneDeep(this.queryData);
      var clickNode = this.clickNode;
      if (!clickNode) {
        return;
      }
      var params = {
        endTime: this.$moment().endOf("M").valueOf() + 1,
        projectId: this.clickNode.id,
        // id: 24,
        // modelLabel: clickNode.modelLabel,
        eventTypes: [702],
        startTime: this.$moment().startOf("M").valueOf(),
        cycle: 12
      };

      var queryOption = {
        url: "/eem-service/v1/alarmEvent/queryEventTrend",
        method: "POST",
        headers: { projectId: this.clickNode.id },
        data: params
      };

      httping(queryOption).then(function (response) {
        if (response.code === 0) {
          // console.log(response.data);
          var data = _this._.get(response, ["data"], []);
          _this.filEventAnalysisNum(data);
        }
      });
    },
    filEventAnalysisNum(oldData) {
      var data = oldData[0].dataList || [];
      var source = this._.cloneDeep(data);
      source.forEach(item => {
        item.product = this.getAxixs(item.key, 14);
      });
      var dataset = {
        source: source
      };
      this.CetChart_5.config.options.dataset = dataset;
    },
    getAxixs(val, type) {
      var date = new Date(val),
        y = date.getFullYear(),
        M = date.getMonth() + 1,
        d = date.getDate(),
        h = date.getHours(),
        m = date.getMinutes();
      if (M < 10) {
        M = "0" + M;
      }
      if (d < 10) {
        d = "0" + d;
      }
      if (h < 10) {
        h = "0" + h;
      }
      if (m < 10) {
        m = "0" + m;
      }
      if (type === 12) {
        return h + ":" + m;
      } else if (type === 14) {
        return M + "-" + d;
      } else if (type === 17) {
        return M + "";
      } else {
        return y + "-" + M + "-" + d;
      }
    }
  },
  created: function () {},
  mounted: function () {
    if (!this.clickNode) {
      return;
    }
    if (this.clickNode.modelLabel === "project") {
      this.getProjectMsg();
      this.getProjectConsumption();
      this.getOverViewEventAnalysis();
      this.getEventAnalysisNum();
    }
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
}
.cont11-bg {
  position: relative;
  @include background_color("BG1");
  @include border_radius(C);
  overflow: hidden;
}
.cont11-bg:hover .cont11-label {
  display: block;
}
.cont11-label {
  display: none;
  position: absolute;
  width: 100%;
  bottom: 0px;
  background: rgba(#1c1f2e, 0.55);
  color: #fff;
  padding-bottom: mh-get(J);
  span {
    @include font_size(Ab);
    line-height: 1.5;
    display: block;
    padding: mh-get(J) mh-get(J6);
  }
}
.card-cont {
  span {
    display: block;
  }
  .label {
    @include font_color("T3");
    @include margin_bottom(J1);
  }
}
.card-cont-num-danger {
  @include font_color(Sta3);
  @include font_size(H);
  @include line_height(H);
  @include font_weight(Bold);
}

.card-cont-num-success {
  @include font_color(Sta1);
  @include font_size(H);
  @include line_height(H);
  @include font_weight(Bold);
}
.gapJ2 {
  @include margin_top(J3);
}
.column {
  display: flex;
  flex-direction: column;
  height: 100%;
}
</style>
