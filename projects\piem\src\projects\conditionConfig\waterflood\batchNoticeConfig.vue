<template>
  <CetDialog class="CetDialog" v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <div
      class="eem-cont-c1 flex-column"
      style="padding-bottom: 1px; width: 320px"
    >
      <span style="font-weight: bold">用户组</span>
      <el-tabs
        class="mtJ3"
        v-model="userGroupType"
        @tab-click="handleTabClick(userGroupType)"
      >
        <el-tab-pane
          label="即时通架构"
          name="architecture"
          disabled
        ></el-tab-pane>
        <el-tab-pane label="自建用户组" name="personal"></el-tab-pane>
      </el-tabs>
      <el-input
        prefix-icon="el-icon-search"
        v-model="userGroupInput"
        @input="userGroupInputChange"
        placeholder="请输入关键字"
      ></el-input>
      <div
        class="mtJ1"
        v-if="userGroupList.length"
        style="flex: 1; text-align: center; overflow: auto; white-space: nowrap"
      >
        <div
          class="subLevelItem"
          :class="{
            subLevelItemAction: currentUserGroup.id == item.id
          }"
          v-for="(item, index) in userGroupList"
          :key="index"
          @click="changeUserGroup(item)"
        >
          <span :title="item.chatname" class="name">
            {{ item.chatname }}
          </span>
        </div>
      </div>
      <el-empty
        v-else
        class="text-center flex-column piemEmpty"
        style="flex: 1; justify-content: center"
        description="暂无内容"
        image="emptypng"
      ></el-empty>
    </div>
    <div
      class="eem-cont-c1 mlJ1"
      style="padding-bottom: 1px; width: 320px; overflow: auto"
    >
      <span style="font-weight: bold">井号选择</span>
      <CetTree
        class="mtJ3"
        style="height: calc(100% - 52px)"
        :selectNode.sync="CetTree_1.selectNode"
        :checkedNodes.sync="CetTree_1.checkedNodes"
        :searchText_in.sync="CetTree_1.searchText_in"
        v-bind="CetTree_1"
        v-on="CetTree_1.event"
      ></CetTree>
    </div>

    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>

<script>
import customApi from "@/api/custom";
export default {
  name: "batchNoticeConfig",
  props: {
    visibleTrigger_in: Number,
    closeTrigger_in: Number
  },
  data() {
    return {
      userGroupType: "personal", // tab 选中的用户组类型
      userGroupInput: "", // 用户组搜索框的值
      userGroupList: [], // 用户组列表
      copyUserGroupList: [], // 拷贝用户组列表，用于还原搜索
      currentUserGroup: {}, // 当前选中用户组
      CetDialog_1: {
        width: "640px",
        title: "批量通知配置",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {}
      },
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        showCheckbox: true,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.handleTabClick(this.userGroupType);
      this.getTreeData();
      this.CetTree_1.searchText_in = "";
      this.CetTree_1.checkedNodes = [];
      this.userGroupInput = "";
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },
  methods: {
    async getTreeData() {
      const queryData = {
        rootID: 0,
        rootLabel: "oilcompany",
        subLayerConditions: [
          {
            modelLabel: "oilproductionplant"
          },
          {
            modelLabel: "operationarea"
          },
          {
            modelLabel: "oilproductioncrew"
          },
          {
            modelLabel: "platform"
          },
          {
            modelLabel: "waterinjectionstation"
          },
          {
            modelLabel: "waterinjectionplatform"
          },
          {
            modelLabel: "waterinjectionwell"
          }
        ],
        treeReturnEnable: true
      };
      const res = await customApi.getNodeTree(queryData);
      this.CetTree_1.inputData_in = res?.data || [];

      this.CetTree_1.filterNodes_in = this.flatTreeData(
        this.CetTree_1.inputData_in
      ).filter(item => ["waterinjectionwell"].includes(item.modelLabel));
      this.CetTree_1.checkedNodes = [];
    },
    // 拍平节点树数据
    flatTreeData(treeData) {
      const cloneData = this._.cloneDeep(treeData);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },
    handleTabClick(val) {
      this.userGroupInput = "";
      val === "personal" && this.queryPersonalChatList();
      val === "architecture" && this.queryArchitectureChatList();
    },
    userGroupInputChange(name) {
      this.userGroupList = this.copyUserGroupList.filter(item =>
        item.chatname.includes(name)
      );
      this.currentUserGroup = this.userGroupList[0] || {};
      this.CetButton_confirm.disable_in = this.userGroupList.length === 0;
      this.getCurrentUserGroupMesConfig();
    },
    changeUserGroup(userGroup) {
      this.currentUserGroup = userGroup;
      this.getCurrentUserGroupMesConfig();
    },
    /**
     * 查询自建用户组列表
     */
    async queryPersonalChatList() {
      const res = await customApi.queryChatList();
      this.userGroupList = res?.data || [];
      this.copyUserGroupList = this._.cloneDeep(this.userGroupList);
      this.currentUserGroup = res?.data?.[0] || {};
      this.CetButton_confirm.disable_in = this.userGroupList.length === 0;
      this.getCurrentUserGroupMesConfig();
    },
    /**
     * 查询即时通架构列表
     */
    async queryArchitectureChatList() {
      this.userGroupList = [];
      this.copyUserGroupList = this._.cloneDeep(this.userGroupList);
      this.currentUserGroup = {};
      this.getCurrentUserGroupMesConfig();
    },
    /**
     * 查询当前用户组消息通知配置
     */
    async getCurrentUserGroupMesConfig() {
      if (!this.currentUserGroup?.id) {
        this.CetTree_1.checkedNodes = [];
        return;
      }
      const res = await customApi.queryChatIdById(this.currentUserGroup);
      this.CetTree_1.checkedNodes = (res?.data || []).map(i => {
        return {
          tree_id: `${i.objectlabel}_${i.objectid}`
        };
      });
    },
    async CetButton_confirm_statusTrigger_out() {
      if (this._.isNil(this.currentUserGroup?.chatcode)) {
        this.$message.warning("请选择用户组");
        return;
      }
      const queryData = {
        chatId: this.currentUserGroup.id,
        nodeList: this.CetTree_1.checkedNodes
          .filter(i => ["waterinjectionwell"].includes(i.modelLabel))
          .map(i => {
            return {
              modelLabel: i.modelLabel,
              id: i.id
            };
          })
      };

      const res = await customApi.waterFloodDiagnosticSave(queryData);

      if (res?.code === 0) {
        this.$message.success("保存成功！");
        this.$emit("finishTrigger_out");
      }
      this.CetDialog_1.closeTrigger_in = +new Date();
    },
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = +new Date();
    }
  }
};
</script>

<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
  }
  :deep(.el-dialog) {
    margin-top: 7vh !important;
  }
  :deep(.el-dialog__body) {
    display: flex;
    height: 688px;
  }
  .subLevelItem {
    width: fit-content;
    min-width: 260px;
    height: 48px;
    line-height: 48px;
    text-align: left;
    cursor: pointer;
    .name {
      width: 248px;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: middle;
    }
  }
  .subLevelItemAction {
    @include background_color(BG4);
  }
}
</style>
