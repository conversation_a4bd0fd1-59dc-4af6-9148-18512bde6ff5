<template>
  <div class="fullheight">
    <div class="head">
      <div class="title">{{ $T("参数相关性分析") }}</div>
    </div>
    <div style="height: calc(100% - 40px)">
      <CetChart v-bind="CetChart_analysis"></CetChart>
    </div>
  </div>
</template>

<script>
import common from "eem-utils/common";
import omegaTheme from "@omega/theme";
export default {
  name: "parameterCorrelationAnalysis",
  components: {},
  props: {
    heatMapData: {
      type: Array
    }
  },
  data() {
    return {
      CetChart_analysis: {
        inputData_in: null,
        options: {
          tooltip: {
            position: "top"
          },
          grid: {
            height: "82%",
            top: "2%",
            left: "22%",
            right: "20%",
            bottom: "45%"
          },
          xAxis: {
            type: "category",
            interval: 0,
            axisLabel: {
              rotate: 45,
              margin: 10,
              formatter: value => {
                if (value.length > 4) {
                  return `${value.slice(0, 4)}...`;
                }
                return value;
              }
            },
            data: [],
            splitArea: {
              show: true
            }
          },
          yAxis: {
            type: "category",
            data: [],
            splitArea: {
              show: true
            },
            axisLabel: {
              show: true,
              formatter: value => {
                if (value.length > 8) {
                  return `${value.slice(0, 8)}...`;
                }
                return value;
              }
            }
          },
          visualMap: {
            color:
              omegaTheme.theme === "light"
                ? ["#f95e5a", "#f0f2f6", "#4ca6ff"]
                : ["#ff3f3f", "#39476b ", "#0d86ff"],
            min: -1,
            max: 1,
            precision: 1,
            itemHeight: 276,
            calculable: true,
            right: 0,
            bottom: "20%",
            top: 0,
            align: "left"
          },
          series: [
            {
              type: "heatmap",
              data: [],
              label: {
                show: true
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowColor: "rgba(0, 0, 0, 0.5)"
                }
              }
            }
          ]
        }
      }
    };
  },
  watch: {
    heatMapData(val) {
      const yData = val[0]?.map(it => it.y);
      let xData = [];
      let seriesData = [];
      val.forEach(item => {
        xData.push(item[0]?.x);
        item.forEach(it => {
          const xIndex = xData.findIndex(x => x === it.x);
          const yIndex = yData.findIndex(y => y === it.y);
          seriesData.push([xIndex, yIndex, common.filNum(it.value, 2)]);
        });
      });

      this.CetChart_analysis.options.yAxis.data = yData;
      this.CetChart_analysis.options.series[0].data = seriesData;
      this.CetChart_analysis.options.xAxis.data = xData;
    }
  },
  methods: {},
  mounted() {}
};
</script>

<style lang="scss" scoped>
.head {
  height: 32px;
  line-height: 32px;
  margin-bottom: 8px;
  .title {
    font-size: 16px;
    font-weight: bold;
  }
}
.flex-end {
  margin-left: auto;
  justify-content: flex-end;
}
.flex1 {
  flex: 1;
}
</style>
