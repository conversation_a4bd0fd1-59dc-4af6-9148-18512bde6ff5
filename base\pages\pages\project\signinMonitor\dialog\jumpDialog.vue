<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="small"
    >
      <template v-slot:footer>
        <span>
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
        </span>
      </template>
      <el-container class="eem-cont-c1" style="height: 100%">
        <!-- <el-header height="58px" style="padding:0px;line-height:58px;">
					请选择跳转到对应页面：
				</el-header> -->
        <el-main style="height: 100%; text-align: center">
          <!-- cancel按钮组件 -->
          <CetButton
            class="mrJ1 custom-btn-width"
            v-bind="CetButton_signin"
            v-on="CetButton_signin.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_order"
            v-on="CetButton_order.event"
          ></CetButton>
        </el-main>
      </el-container>
    </CetDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";

export default {
  name: "graphTreeDialog",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    userInfo() {
      var vm = this;
      return vm.$store.state.userInfo;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    tableData: {
      type: Array
    }
  },
  data() {
    return {
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "关闭",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: "请选择跳转到对应页面",
        "append-to-body": true,
        showClose: true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // preserve组件
      CetButton_order: {
        visible_in: true,
        disable_in: false,
        title: "巡检工单管理",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_order_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_signin: {
        visible_in: true,
        disable_in: false,
        title: "签到点管理",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_signin_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    inputData_in(val) {}
  },
  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_order_statusTrigger_out(val) {
      this.$emit("saveData_out", {
        jumpName: "inspectorder",
        id: this.inputData_in.id
      });
      this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
    },
    CetButton_signin_statusTrigger_out(val) {
      this.$emit("saveData_out", {
        jumpName: "signinmanage",
        id: this.inputData_in.id
      });
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    no() {}
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.custom-btn-width {
  :deep(.el-button) {
    width: 102px !important;
  }
}
</style>
