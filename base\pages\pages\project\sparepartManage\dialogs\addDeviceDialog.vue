<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="small"
    >
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <CetForm
        :data.sync="CetForm_pagedialog.data"
        v-bind="CetForm_pagedialog"
        v-on="CetForm_pagedialog.event"
        class="eem-cont-c1"
        label-position="top"
      >
        <el-row :gutter="$J3">
          <el-col :span="12">
            <el-form-item :label="$T('名称')" prop="name">
              <ElInput
                v-model.trim="CetForm_pagedialog.data.name"
                v-bind="ElInput_name"
                v-on="ElInput_name.event"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$T('设备类型')" prop="objectLabelId">
              <ElSelect
                v-model="CetForm_pagedialog.data.objectLabelId"
                v-bind="ElSelect_objectLabel"
                v-on="ElSelect_objectLabel.event"
              >
                <ElOption
                  v-for="item in ElOption_objectLabel.options_in"
                  :key="item[ElOption_objectLabel.key]"
                  :label="item[ElOption_objectLabel.label]"
                  :value="item[ElOption_objectLabel.value]"
                  :disabled="item[ElOption_objectLabel.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item :label="$T('型号')" prop="model">
              <ElSelect
                v-model="CetForm_pagedialog.data.model"
                v-bind="ElSelect_model"
                v-on="ElSelect_model.event"
              >
                <ElOption
                  v-for="item in ElOption_model.options_in"
                  :key="item[ElOption_model.key]"
                  :label="item[ElOption_model.label]"
                  :value="item[ElOption_model.value]"
                  :disabled="item[ElOption_model.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
        </el-row>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";
import common from "eem-utils/common";

export default {
  name: "addDeviceDialog",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    groupList: {
      type: Array
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("新增备件分类"),
        showClose: true,
        "append-to-body": true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "static", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入备件分类名称"),
              trigger: ["blur"]
            },
            common.check_name
          ],
          objectLabelId: [
            {
              required: true,
              message: $T("请选择设备类型"),
              trigger: ["blur", "change"]
            }
          ],
          model: [
            {
              required: true,
              message: $T("请选择或者输入设备型号"),
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_pagedialog_currentData_out,
          saveData_out: this.CetForm_pagedialog_saveData_out,
          finishData_out: this.CetForm_pagedialog_finishData_out,
          finishTrigger_out: this.CetForm_pagedialog_finishTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // name组件
      ElInput_name: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {
          width: "100%"
        },
        event: {}
      },
      // 设备类型组件
      ElSelect_objectLabel: {
        value: "",
        style: {
          width: "100%"
        },
        filterable: true,
        event: {
          change: this.ElSelect_objectLabel_change_out
        }
      },
      ElOption_objectLabel: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // 型号组件
      ElSelect_model: {
        value: "",
        filterable: true,
        "allow-create": true,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_model: {
        options_in: [],
        key: "label",
        value: "label",
        label: "label",
        disabled: "disabled"
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.getDevice();
      this.CetForm_pagedialog.data = {};
      this.CetForm_pagedialog.resetTrigger_in = new Date().getTime();
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {
      this.CetForm_pagedialog.queryId_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      this.CetForm_pagedialog.inputData_in = this._.cloneDeep(val);
    }
  },
  methods: {
    ElSelect_objectLabel_change_out() {
      this.$set(this.CetForm_pagedialog.data, "model", "");
      this.getModel();
    },
    CetForm_pagedialog_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_saveData_out(val) {
      this.$emit("saveData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishData_out(val) {
      this.$emit("finishData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishTrigger_out(val) {
      this.$emit("finishTrigger_out", val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetForm_pagedialog.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out() {
      this.CetForm_pagedialog.localSaveTrigger_in = new Date().getTime();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    getDevice() {
      customApi.queryEnumerations("deviceclass").then(res => {
        if (res.code === 0) {
          this.ElOption_objectLabel.options_in = res.data;
        }
      });
    },
    getModel() {
      const objectLabelId = this.CetForm_pagedialog.data.objectLabelId;
      const modelLabel = this.ElOption_objectLabel.options_in.find(
        item => item.id === objectLabelId
      ).propertyLabel;
      customApi.querySparePartsModel(modelLabel).then(res => {
        if (res.code === 0) {
          const arr = [];
          res.data.forEach(item => {
            arr.push({ label: item });
          });
          this.ElOption_model.options_in = arr;
        }
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped></style>
