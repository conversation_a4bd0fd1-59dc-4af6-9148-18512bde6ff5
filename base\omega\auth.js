import { httping } from "@omega/http";
import { decryptAttributes } from "eem-utils/crypto.js";
import customApi from "@/api/custom";
const decryptUserInfo = data => {
  if (typeof data === "string") {
    data = JSON.parse(data);
  }
  let response = _.get(data, "data");
  response = decryptAttributes(response, [
    "name",
    "nicName",
    "email",
    "mobilePhone"
  ]);
  return {
    ...data,
    data: response
  };
};
const apiProxy = {
  async getCurrentUser(userId) {
    const res = await httping({
      url: `/eem-service/v2/user/getUserByHeaderUserId`,
      method: "get",
      headers: { "User-ID": userId },
      mhconf: {
        hideNotice: true
      },
      transformResponse: [decryptUserInfo]
    });
    return res?.data;
  },
  async getPermission() {
    const res = await customApi.getPermissionTree();
    let permission = [];
    function loop(arr) {
      (arr || []).forEach(item => {
        permission.push({
          id: item.id,
          name: item.onlyKey
        });
        if (item.parentId === 0) {
          loop(item.children);
        }
      });
    }
    loop(res?.data || []);
    return permission;
  }
};

export default apiProxy;
