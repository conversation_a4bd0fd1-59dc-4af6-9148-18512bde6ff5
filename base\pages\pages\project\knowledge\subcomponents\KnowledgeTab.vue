<template>
  <div class="fullheight eem-group-list">
    <ul>
      <li
        v-for="tab in tabs"
        :key="tab.id"
        :class="current == tab.id ? 'group-item active' : 'group-item'"
        @click="tabChange(tab.id, tab.label)"
        :title="tab.label"
      >
        <i class="el-icon-notebook-1"></i>
        {{ tab.label }}
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  name: "KnowledgeTab",
  components: {},
  computed: {},
  data() {
    return {
      activeId: "0",
      current: 1
    };
  },
  props: {
    tabs: Array,
    selectObj: Object
  },
  watch: {
    selectObj(val) {
      this.current = val.id;
    }
  },
  methods: {
    // tab切换
    tabChange(id, label) {
      const tabs = this.tabs;
      this.current = id;
      this.$emit("tabChange", { id, label, tabs });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
ul,
li {
  list-style: none;
  margin: 0;
  padding: 0;
}

ul {
  height: 100%;
  width: 100%;
  overflow-x: scroll;
}
</style>
