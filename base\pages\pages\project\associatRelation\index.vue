<template>
  <div class="pageWrap flex-column">
    <el-tabs v-model="activeName" class="eem-tabs-custom">
      <el-tab-pane
        v-for="item in tabList"
        :label="item.label"
        :name="item.name"
        :key="item.name"
      ></el-tab-pane>
    </el-tabs>
    <div class="flex-auto">
      <PipeRelation v-show="activeName == '1'" ref="PipeRelation" />
      <GatherRelation v-show="activeName == '2'" ref="GatherRelation" />
      <CheckRelation v-show="activeName == '3'" ref="CheckRelation" />
    </div>
  </div>
</template>

<script>
import PipeRelation from "./pipeRelation";
import GatherRelation from "./gatherRelation";
import CheckRelation from "./checkRelation";
export default {
  name: "deviceRelation",
  components: {
    PipeRelation,
    GatherRelation,
    CheckRelation
  },
  computed: {
    tabList() {
      const labels = {
        1: $T("管网设备关联"),
        2: $T("采集设备关联"),
        3: $T("设备关联核对")
      };
      if (!this.$store.state.systemCfg.associatRelationTabs) {
        return [
          {
            label: $T("管网设备关联"),
            name: "1"
          },
          {
            label: $T("采集设备关联"),
            name: "2"
          },
          {
            label: $T("设备关联核对"),
            name: "3"
          }
        ];
      }
      let associatRelationTabs = this._.cloneDeep(
        this.$store.state.systemCfg.associatRelationTabs
      );
      associatRelationTabs.forEach(item => {
        item.label = labels[item.name];
      });
      return associatRelationTabs;
    }
  },
  data() {
    return {
      activeName: "1",
      activatedNum: 0
    };
  },
  watch: {
    activeName(val, oldval) {
      const refObj = {
        1: "PipeRelation",
        2: "GatherRelation",
        3: "CheckRelation"
      };
      if (val === "3") {
        this.$refs[refObj[val]]?.callImportRatio();
      }
      if (oldval === "3") {
        this.$refs[refObj[oldval]]?.closeImportRatio();
      }
      this.$refs[refObj[val]]?.getTreeData();
    },
    deep: true
  },

  methods: {},

  created: function () {},
  activated: function () {
    if (this.activatedNum > 0) {
      this.activeName = "";
      this.$nextTick(() => {
        this.activeName = "1";
      });
    }
    var params = this.$route.params;
    if (params && params.deviceId && params.deviceModelLabel) {
      this.$nextTick(() => {
        this.activeName = "2";
      });
    }
    this.activatedNum++;
  }
};
</script>
<style lang="scss" scoped>
.pageWrap {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  :deep() {
    .el-tabs__nav-wrap::after {
      display: none;
    }
    .el-tabs__nav-scroll {
      @include padding_left(J2);
      @include background_color(BG1);
    }
  }
}
</style>
