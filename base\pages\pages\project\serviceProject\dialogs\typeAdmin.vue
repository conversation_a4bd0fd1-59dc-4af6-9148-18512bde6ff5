<template>
  <div>
    <CetDialog v-bind="CetDialog_add" v-on="CetDialog_add.event" class="min">
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <!-- <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton> -->
      </span>
      <el-main style="max-height: 500px" class="eem-cont-c1">
        <div
          v-for="(item, index) in listData"
          :key="index"
          :class="{
            clearfix: true,
            listItem: true,
            action: itemAction == index
          }"
          @click="itemAction = index"
        >
          <div v-show="!item.showInput">
            <el-tooltip :content="item.name" effect="light">
              <div class="fl ellipsis" style="width: 150px">
                {{ item.name }}
              </div>
            </el-tooltip>
            <div class="fr">
              <i
                class="el-icon-edit cursor fcZS mrJ"
                @click="handleEdit(item, index)"
                v-permission="'maintenancetype_update'"
              ></i>
              <i
                class="el-icon-delete cursor delete"
                @click="handleDelete(item, index)"
                v-permission="'maintenancetype_delete'"
              ></i>
            </div>
          </div>
          <ElInput
            :ref="`addInput${index}`"
            v-show="item.showInput"
            class="mt5"
            v-model.trim="item.name"
            v-bind="ElInput_1"
            v-on="ElInput_1.event"
            @blur="elInputBlur(item)"
          ></ElInput>
        </div>
        <div
          class="addBtn"
          @click="handleAdd"
          v-permission="'maintenancetype_create'"
        >
          {{ $T("点击添加维保方式") }}
        </div>
      </el-main>
    </CetDialog>
    <CetDialog v-bind="CetDialog_edit" v-on="CetDialog_edit.event" class="min">
      <div class="eem-cont-c1">
        <div class="mbJ3">
          {{ $T("维保方式名称") }}
          <span style="color: red">*</span>
        </div>
        <ElInput
          v-model="ElInput_edit.value"
          v-bind="ElInput_edit"
          v-on="ElInput_edit.event"
        ></ElInput>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_edit_cancel"
          v-on="CetButton_edit_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_edit_confirm"
          v-on="CetButton_edit_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
    <CetInterface
      :data.sync="CetInterface_query.data"
      :dynamicInput.sync="CetInterface_query.dynamicInput"
      v-bind="CetInterface_query"
      v-on="CetInterface_query.event"
    ></CetInterface>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";

export default {
  name: "typeAdmin",
  components: {},
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.localStorage) {
          return false;
        } else {
          var storage = window.localStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },
  data() {
    return {
      listData: [],
      itemAction: 0,
      // add弹窗组件
      CetDialog_add: {
        title: $T("维保方式管理"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        showClose: true
      },
      CetButton_confirm: {
        visible_in: false,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        size: "small",
        style: {
          width: "200px"
        },
        maxlength: 20,
        event: {}
      },

      CetDialog_edit: {
        title: $T("编辑维保方式"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        showClose: true
      },
      ElInput_edit: {
        value: "",
        style: {
          width: "100%"
        },
        maxlength: 20,
        event: {}
      },
      CetButton_edit_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_edit_confirm_statusTrigger_out
        }
      },
      CetButton_edit_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_edit_cancel_statusTrigger_out
        }
      },
      CetInterface_query: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "queryMaintenanceType",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {},
        page_in: null, // exp:{ index: 1, limit: 20 }
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          result_out: this.CetInterface_query_result_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.openTrigger_in = val;
      this.CetInterface_query.queryTrigger_in = new Date().getTime();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    }
  },

  methods: {
    CetInterface_query_result_out(val) {
      if (val && val.length > 0) {
        this.listData = val;
        this.listData.forEach(item => {
          this.$set(item, "showInput", false);
        });
      } else {
        this.listData = [];
      }
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_edit_cancel_statusTrigger_out(val) {
      this.ElInput_edit.value = "";
      this.CetDialog_edit.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_edit_confirm_statusTrigger_out(val) {
      if (!this.ElInput_edit.value) {
        this.$message({
          message: $T("类型名称不能为空"),
          type: "warning"
        });
        return;
      } else if (this.ElInput_edit.value.length > 20) {
        this.$message({
          message: $T("类型名称支持20个字符"),
          type: "warning"
        });
        return;
      }
      const addParams = [
        {
          id: this.listData[this.itemAction].id,
          name: this.ElInput_edit.value
        }
      ];
      customApi.editMaintenanceType(addParams).then(res => {
        if (res.code === 0) {
          this.$message({
            message: $T("保存成功！"),
            type: "success"
          });
          this.$emit("confirm_out");
        }
        this.CetInterface_query.queryTrigger_in = new Date().getTime();
        this.CetDialog_edit.closeTrigger_in = this._.cloneDeep(val);
      });
    },
    handleEdit(item, index) {
      this.ElInput_edit.value = item.name;
      this.CetDialog_edit.openTrigger_in = new Date().getTime();
    },
    handleDelete(item, index) {
      this.$confirm($T("确定要删除所选项吗？"), $T("删除确认"), {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消")
      }).then(() => {
        customApi.deleteMaintenanceType([item.id]).then(res => {
          if (res.code === 0) {
            this.$message.info($T("删除成功"));
            this.CetInterface_query.queryTrigger_in = new Date().getTime();
          }
        });
      });
    },
    handleAdd() {
      this.listData.push({
        name: $T("新增维保方式"),
        showInput: true
      });
      this.$nextTick(() => {
        this.$refs[`addInput${this.listData.length - 1}`][0].focus();
      });
    },
    elInputBlur(item) {
      if (!item.name) {
        this.$message({
          message: $T("类型名称不能为空"),
          type: "warning"
        });
        return;
      } else if (item.name.length > 20) {
        this.$message({
          message: $T("类型名称支持20个字符"),
          type: "warning"
        });
        return;
      }
      this.addServer(item);
    },
    addServer(row) {
      var addParams = [
        {
          name: row.name,
          id: row.id
        }
      ];
      customApi.addMaintenanceType(addParams).then(res => {
        if (res.code === 0) {
          this.$message({
            message: $T("保存成功！"),
            type: "success"
          });
          row.showInput = false;
          this.$emit("confirm_out");
        }
        this.CetInterface_query.queryTrigger_in = new Date().getTime();
      });
    },
    deleteServer(row) {
      var deleteParams = {
        modelLabel: "eventclassification",
        ids: [row.id]
      };
      customApi.deleteKnowledge(deleteParams).then(res => {
        if (res.code === 0) {
          this.$message({
            message: $T("保存成功！"),
            type: "success"
          });
          this.$emit("confirm_out");
          this.CetInterface_query.queryTrigger_in = new Date().getTime();
        }
      });
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.cursor {
  cursor: pointer;
}
.addBtn {
  @include margin_bottom(J1);
  cursor: pointer;
  @include font_color(ZS);
  display: inline-block;
}
.listItem {
  @include margin_bottom(J1);
  box-sizing: border-box;
  @include padding(0 J1);
  cursor: pointer;
  line-height: 2;
  @include border_radius(C);
  box-sizing: border-box;
  &.action {
    @include background_color(BG4);
  }
}
.delete {
  @include font_color(Sta3);
}
</style>
