import omegaTheme from "@omega/theme";
const eventTextColor = "#FFFFFF";
const eventTypeColor = {
    light: {
      1: "#FF3F3F", // 事故事件
      2: "#FF842B", // 报警事件
      3: "#28B061", // 一般事件
      4: "#FFC531", // 预警事件
      5: "#0D86FF" // 其他事件
    },
    dark: {
      1: "#F95E5A", // 事故事件
      2: "#FF9D09", // 报警事件
      3: "#28B061", // 一般事件
      4: "#FFD12F", // 预警事件
      5: "#0D86FF" // 其他事件
    }
  },
  eventGradeColor = {
    light: {
      1: "#FF3F3F", // 一级
      2: "#FF842B", // 二级
      3: "#FFC531", // 三级
      4: "#0D86FF" // 其他
    },
    dark: {
      1: "#F95E5A", // 一级
      2: "#FF9D09", // 二级
      3: "#FFD12F", // 三级
      4: "#0D86FF" // 其他
    }
  };

// 获取事件类型颜色
export const getEventTypeColor = value => {
  const themeName = omegaTheme.theme === "dark" ? "dark" : "light";
  return { bgColor: eventTypeColor[themeName][value], color: eventTextColor };
};
// 获取事件等级颜色
export const getEventGradeColor = value => {
  const themeName = omegaTheme.theme === "dark" ? "dark" : "light";
  return { bgColor: eventGradeColor[themeName][value], color: eventTextColor };
};
