<template>
  <div
    class="page fullheight"
    style="padding-top: 32px; box-sizing: border-box"
  >
    <div
      class="fullheight flex-column"
      style="overflow-y: auto; min-width: 1290px"
    >
      <div class="tip1 eem-container mtJ3 flex-column">
        <div class="mbJ3" style="height: 32px">
          <CetButton
            class="fr custom—square"
            v-bind="CetButton_8"
            v-on="CetButton_8.event"
          ></CetButton>
          <div class="basic-box fr mlJ mrJ">
            <div class="block" v-show="status !== 14">
              <CustomElDatePicker
                class="fr"
                :prefix_in="$T('选择年份')"
                style="width: 200px"
                v-model="value3"
                type="year"
                :placeholder="$T('选择年份')"
                @change="yearChange"
              />
            </div>
            <div class="block" v-show="status === 14">
              <CustomElDatePicker
                class="fr"
                style="width: 256px"
                :prefix_in="$T('选择日期')"
                v-model="value1"
                type="monthrange"
                :range-separator="$T('至')"
                @change="monthChange"
              />
            </div>
          </div>
          <CetButton
            class="fr custom—square"
            v-bind="CetButton_7"
            v-on="CetButton_7.event"
          ></CetButton>
          <div class="basic-box mlJ1 fr">
            <customElSelect
              class="fr mrJ1"
              v-model="ElSelect_1.value"
              v-bind="ElSelect_1"
              v-on="ElSelect_1.event"
              :prefix_in="$T('能源类型')"
            >
              <ElOption
                v-for="item in ElOption_1.options_in"
                :key="item[ElOption_1.key]"
                :label="item[ElOption_1.label]"
                :value="item[ElOption_1.value]"
                :disabled="item[ElOption_1.disabled]"
              ></ElOption>
            </customElSelect>
            <customElSelect
              class="fr mrJ1"
              v-model="ElSelect_2.value"
              v-bind="ElSelect_2"
              v-on="ElSelect_2.event"
              :prefix_in="$T('核算周期')"
            >
              <ElOption
                v-for="item in ElOption_2.options_in"
                :key="item[ElOption_2.key]"
                :label="item[ElOption_2.label]"
                :value="item[ElOption_2.value]"
                :disabled="item[ElOption_2.disabled]"
              ></ElOption>
            </customElSelect>
          </div>
          <div class="fr basic-box">
            <el-radio v-model="radio" label="1" class="lh32">
              {{ type == 1 ? $T("单位产量") : $T("单位面积") }}
            </el-radio>
            <el-radio v-model="radio" label="2" class="lh32">
              {{ type == 1 ? $T("单位产值") : $T("单位人员") }}
            </el-radio>
            <customElSelect
              class="fr"
              v-model="ElSelect_3.value"
              v-bind="ElSelect_3"
              v-on="ElSelect_3.event"
              :prefix_in="$T('指标类型')"
            >
              <ElOption
                v-for="item in ElOption_3.options_in"
                :key="item[ElOption_3.key]"
                :label="item[ElOption_3.label]"
                :value="item[ElOption_3.value]"
                :disabled="item[ElOption_3.disabled]"
              ></ElOption>
            </customElSelect>
          </div>
        </div>
        <div class="flex-auto" style="width: 100%">
          <CetChart v-bind="CetChart_1"></CetChart>
        </div>
      </div>
      <div class="flex-auto mtJ3 eem-container" style="min-height: 200px">
        <CetTable
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
          <ElTableColumn
            v-for="(item, index) in ElTableColumnArr"
            :key="index"
            v-bind="item"
          ></ElTableColumn>
        </CetTable>
      </div>
    </div>
  </div>
</template>
<script>
import { httping } from "@omega/http";
export default {
  name: "UnitCost",
  components: {},

  computed: {
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },

  data(vm) {
    const language = window.localStorage.getItem("omega_language") === "en";
    return {
      unitTransition: false, // 是否需要进行单位转换
      selectObj: {},
      value3: vm.$moment().startOf("year").toDate(),
      value1: [
        this.$moment().toDate(),
        this.$moment().add("month", 1).toDate()
      ],
      type: 0,
      status: 17,
      radio: "1", // 切换单位产量1和单位产值2
      ElSelect_1: {
        value: 13,
        style: { width: language ? "240px" : "200px" },
        event: {
          change: this.energyChange
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: 17,
        style: { width: language ? "250px" : "200px" },
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [
          { id: 17, text: $T("按年") },
          { id: 14, text: $T("自定义") }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_3: {
        value: 0,
        style: { width: language ? "230px" : "200px" },
        event: {
          change: this.ElSelect_3_change_out
        }
      },
      ElOption_3: {
        options_in: [{ id: 0, text: $T("建筑") }],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {
          // record_out: this.CetTable_1_record_out,
          // outputData_out: this.CetTable_1_outputData_out
        }
      },
      // index组件
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        //  prop: "",      // 支持path a[0].b
        label: $T("序号"), //列名
        headerAlign: "left",
        align: "lftt",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: language ? "100px" : "50px" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumnArr: [{}],
      CetButton_7: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        plain: true,
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_7_statusTrigger_out
        }
      },
      CetButton_8: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        plain: true,
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_8_statusTrigger_out
        }
      },
      CetChart_1: {
        //组件输入项
        inputData_in: null,
        options: {}
      }
    };
  },
  props: ["obj", "energyList", "energySelect"],
  watch: {
    obj(val) {
      const selectObj = this.selectObj || {};
      if (val.length === 0) {
        this.ElTableColumnArr = [{}];
        this.CetTable_1.data = [];
        this.getEcharts([], [], []);
        return;
      }
      const nodes = [];
      val.map(item => {
        const obj = {};
        obj.id = item.id;
        obj.modelLabel = item.modelLabel;
        obj.name = item.name;
        nodes.push(obj);
      });
      const startTime = this.$moment().startOf("year").valueOf();
      const endTime = this.$moment().add(1, "year").startOf("year").valueOf();
      this.value3 = this.$moment().startOf("year").toDate();
      this.value1 = [
        this.$moment().toDate(),
        this.$moment().add("month", 1).toDate()
      ];
      const data = {
        cycle: selectObj.cycle ? selectObj.cycle : 17, // 周期
        energyType: selectObj.energyType ? selectObj.energyType : 13, // 能源类型
        endTime, //
        nodes,
        startTime,
        projectId: val[0].projectId,
        quotaType: selectObj.quotaType ? selectObj.quotaType : 1
      };
      this.selectObj = data;
      this.getList(data);
    },
    radio(val) {
      const selectObj = this.selectObj;
      selectObj.quotaType = val;
      this.getList(selectObj);
    },
    energyList(val) {
      this.ElOption_1.options_in = val;
    },
    energySelect(val) {
      this.ElSelect_1.value = val;
    }
  },

  methods: {
    ElSelect_2_change_out(val) {
      this.status = val;
      if (this.status === 17) {
        this.yearChange(new Date(this.value3));
      } else if (this.status === 14) {
        this.monthChange([new Date(this.value1[0]), new Date(this.value1[1])]);
      }
    },
    ElSelect_3_change_out(val) {
      this.type = val;
    },
    // 获取单位成本核算的列表
    getList(obj) {
      this.unitTransition = false;
      if (obj.nodes == null) return;
      httping({
        url: "eem-service/v1/costcaculating/unitcostcheck",
        method: "post",
        data: obj
      }).then(res => {
        if (res.code == 0) {
          const head = [
            {
              label: $T("核算周期"),
              prop: "cycle",
              minWidth: 100,
              showOverflowTooltip: true,
              headerAlign: "left",
              align: "left"
            }
          ];
          const list = [];
          const data = this._.get(res, "data.datas", []) || [];
          const count = 0;
          const seriesList = [];
          const legendList = [];
          const source = [["product"]];
          // 先判断是否需要进行单位转换
          for (const item in data) {
            if (item != 0) {
              data[item].forEach(item1 => {
                if (item1.value && item1.value * 1 > 10000) {
                  this.unitTransition = true;
                }
              });
            }
          }
          const headers = this._.get(res, "data.header", []) || [];
          headers.map((item, index) => {
            const obj = {
              label: this.unitTransition
                ? item.name + `(${$T("万元")})`
                : item.name + `(${$T("元")})`,
              prop: "node" + index,
              minWidth: 100,
              showOverflowTooltip: true,
              headerAlign: "right",
              align: "right",
              formatter: function (val) {
                if (val["node" + index] || val["node" + index] === 0) {
                  return val["node" + index];
                } else {
                  return "--";
                }
              }
            };
            source[0].push(item.name);
            seriesList.push({ type: "bar" });
            legendList.push({
              name: item.name
            });
            head.push(obj);
          });
          for (const item in data) {
            // let time = new Date(item * 1).getMonth() + 1 + "月";
            let formatStr = this.language ? "YYYY-MM" : "YYYY年MM月";
            const time = this.$moment(item * 1).format(formatStr);
            const item2 = [time];
            const obj = {
              cycle: time
            };
            data[item].map((item1, i) => {
              if (this.unitTransition && (item1.value || item.value === 0)) {
                item1.value = item1.value / 10000;
              }
              const name = "node" + i;
              var value = item1.value !== null ? item1.value.toFixed2(5) : null;
              obj[name] = value;
              item2.push(value);
            });
            list.push(obj);
            source.push(item2);
          }
          this.ElTableColumnArr = head;
          this.CetTable_1.data = list;
          this.getEcharts(source, legendList, seriesList);
        } else if (res.code == -1) {
          this.CetTable_1.data = [];
          this.getEcharts([]);
        }
      });
    },
    getEcharts(source, legendList, seriesList) {
      this.CetChart_1.options = {};
      this.CetChart_1.options = {
        toolbox: {
          top: 0,
          right: 0,
          feature: {
            saveAsImage: {
              title: $T("保存为图片")
            }
          }
        },
        legend: { data: legendList },
        tooltip: {},
        grid: {
          left: "3%",
          right: "1%",
          bottom: "2%",
          top: "50px",
          containLabel: true
        },
        dataset: {
          source
        },
        xAxis: { type: "category" },
        yAxis: {
          name: this.unitTransition ? $T("成本（万元）") : $T("成本（元）")
        },
        series: seriesList
      };
    },
    energyChange(val) {
      const selectObj = this.selectObj;
      selectObj.energyType = val;
      this.selectObj = selectObj;
      this.getList(selectObj);
    },
    yearChange(val) {
      let time = val.getTime();
      time = new Date(time * 1).getFullYear();
      const time1 = time + "-1-1";
      const time2 = time * 1 + 1 + "-1-1";
      const startTime = new Date(time1).getTime();
      const endTime = new Date(time2).getTime();
      const selectObj = this.selectObj;
      selectObj.startTime = startTime;
      selectObj.endTime = endTime;
      selectObj.cycle = 17;
      this.getList(selectObj);
    },
    monthChange(val) {
      const startTime = this.$moment(val[0]).startOf("month").valueOf();
      const endTime = this.$moment(val[1]).endOf("month").valueOf() + 1;
      const selectObj = this.selectObj;
      selectObj.startTime = startTime;
      selectObj.endTime = endTime;
      selectObj.cycle = 17;
      this.getList(selectObj);
    },
    // 向前按钮
    CetButton_7_statusTrigger_out() {
      if (this.status === 17) {
        const selectObj = this.selectObj;
        let time = this.value3;
        if (time > 1000000000) {
          time = new Date(time * 1).getFullYear();
        }
        time--;
        this.value3 = time.toString();
        const time1 = time + "-1-1";
        const time2 = time * 1 + 1 + "-1-1";
        const startTime = new Date(time1).getTime();
        const endTime = new Date(time2).getTime();
        selectObj.startTime = startTime;
        selectObj.endTime = endTime;
        this.selectObj.cycle = 17;
        this.selectObj = selectObj;
        this.getList(selectObj);
      } else if (this.status == 14) {
        var startTime = this.$moment(this.value1[0])
          .add("month", -1)
          .startOf("month")
          .valueOf();
        var endTime = this.$moment(this.value1[1])
          .add("month", -1)
          .endOf("month")
          .valueOf();
        this.value1 = [new Date(startTime), new Date(endTime)];
        this.selectObj.startTime = startTime;
        this.selectObj.endTime = endTime + 1;
        this.getList(this.selectObj);
      }
    },
    // 向后按钮
    CetButton_8_statusTrigger_out() {
      if (this.status === 17) {
        const selectObj = this.selectObj;
        let time = this.value3;
        if (time > 1000000000) {
          time = new Date(time * 1).getFullYear();
        }
        time++;
        this.value3 = time.toString();
        const time1 = time + "-1-1";
        const time2 = time * 1 + 1 + "-1-1";
        const startTime = new Date(time1).getTime();
        const endTime = new Date(time2).getTime();
        selectObj.startTime = startTime;
        selectObj.endTime = endTime;
        this.selectObj = selectObj;
        this.getList(selectObj);
      } else if (this.status == 14) {
        var startTime = this.$moment(this.value1[0])
          .add("month", 1)
          .startOf("month")
          .valueOf();
        var endTime = this.$moment(this.value1[1])
          .add("month", 1)
          .endOf("month")
          .valueOf();
        this.value1 = [new Date(startTime), new Date(endTime)];
        this.selectObj.startTime = startTime;
        this.selectObj.endTime = endTime + 1;
        this.getList(this.selectObj);
      }
    }
  },
  created: function () {},
  mounted: function () {}
};
</script>
<style lang="scss" scoped>
.page {
  :deep(.el-radio) {
    @include margin_right(J1);
  }
  :deep(.el-radio .el-radio__label) {
    @include padding_left(J);
  }
}
.left {
  float: left;
  display: flex;
  align-items: center;
}
.top {
  width: 900px;
  position: absolute;
  right: 0;
  display: flex;
  align-items: center;
}
.type {
  height: 38px;
  line-height: 38px;
  border: 1px solid #0358c2;
  border-right: none;
  padding: 0 12px;
  border-radius: 6px 0 0 6px;
  text-align: center;
}
.tip1 {
  height: 400px;
  overflow: hidden;
  position: relative;
}
.tip2 {
  height: calc(100% - 480px);
  min-height: 200px;
}
canvas {
  width: 1000px !important;
}
</style>
