<template>
  <div class="page">
    <CetTable
      ref="table"
      :data.sync="CetTable_1.data"
      :dynamicInput.sync="CetTable_1.dynamicInput"
      v-bind="CetTable_1"
      v-on="CetTable_1.event"
    >
      <ElTableColumn type="selection" width="39"></ElTableColumn>
      <ElTableColumn
        v-for="(item, index) in columnArr"
        :key="index"
        v-bind="item"
      ></ElTableColumn>
      <ElTableColumn v-bind="ElTableColumn_handele">
        <template slot-scope="scope">
          <span class="eem-row-handle fl mrJ3" @click="handelDetail(scope.row)">
            {{ $T("查看") }}
          </span>
          <span class="eem-row-handle fl mrJ3" @click="handelEdit(scope.row)">
            {{ $T("编辑") }}
          </span>
          <span class="delete fl" @click="handelDelete(scope.row)">
            {{ $T("删除") }}
          </span>
        </template>
      </ElTableColumn>
    </CetTable>
    <detail
      :actionTitle_in="actionTitle_in"
      v-bind="detail"
      v-on="detail.event"
      :template_in="this.deviceTemplate"
    />
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import detail from "./detail.vue";
export default {
  name: "sandingBoxTable",
  props: {
    resetTrigger_in: {
      type: Number
    },
    queryCondition_in: {
      type: Object,
      default() {
        return {};
      }
    },
    actionTitle_in: {
      //1 仪表台账  2检定记录
      type: Number
    },
    // 只更新列表数据，不更新表头
    tableResetTrigger_in: {
      type: Number
    }
  },
  components: {
    detail
  },
  data(vm) {
    return {
      deviceTemplate: [],
      detail: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        data_in: null,
        event: {}
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "getDeviceTableDate", //getRecordTableDate
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [{ name: "projectId", operator: "EQ", prop: "projectId" }], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: $T("合计")
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {
          projectId: null,
          id_in: 12
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {
          pageSize: 20,
          style: {
            float: "right"
          }
        },
        exportFileName: "",
        event: {
          outputData_out: this.CetTable_1_outputData_out,
          "selection-change": this.CetTable_1_selectionChange
        }
      },
      columnArr: [
        {
          type: "index", // selection 勾选 index 序号
          width: 65,
          prop: "", // 支持path a[0].b
          label: "#",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true
        },
        {
          prop: "test", // 支持path a[0].b
          label: ""
        }
      ],
      ElTableColumn_handele: {
        width: "160", //绝对宽度
        minWidth: "160",
        label: $T("操作"),
        fixed: "right",
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      }
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  watch: {
    resetTrigger_in: function (val) {
      this.getData();
    },
    tableResetTrigger_in: function (val) {
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    actionTitle_in: function () {
      this.$refs.table.$refs.cetTable.doLayout();
    }
  },
  methods: {
    getData() {
      // 判断当前选中的tab
      if (this.actionTitle_in === 1) {
        this.CetTable_1.dataConfig.queryFunc = "getDeviceTableDate";
      } else if (this.actionTitle_in === 2) {
        this.CetTable_1.dataConfig.queryFunc = "getRecordTableDate";
      }
      this.columnArr = [
        {
          type: "index", // selection 勾选 index 序号
          width: 65,
          prop: "", // 支持path a[0].b
          label: "#",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true
        },
        {
          prop: "test", // 支持path a[0].b
          label: ""
        }
      ];
      this.getTemplate(() => {
        this.CetTable_1.dataConfig.filters = [
          { name: "projectId", operator: "EQ", prop: "projectId" }
        ];
        this.CetTable_1.dynamicInput = { projectId: this.projectId };
        // 在表头中判断入参条件时间是区间，枚举是并且，文本是存在   赋值入参条件
        Object.keys(this.queryCondition_in).forEach(key => {
          if (
            this.queryCondition_in[key] ||
            this.queryCondition_in[key] === false
          ) {
            var obj = this.deviceTemplate.find(i => i.name == key);
            console.log(obj, "4");
            if (!obj || obj.alias == $T("测量对象")) {
              this.CetTable_1.dataConfig.filters.push({
                name: key,
                operator: "LIKE",
                prop: key
              });
              this.CetTable_1.dynamicInput[key] = this.queryCondition_in[key];
            } else if (obj.datatype == "string") {
              this.CetTable_1.dataConfig.filters.push({
                name: key,
                operator: "LIKE",
                prop: key
              });
              this.CetTable_1.dynamicInput[key] = this.queryCondition_in[key];
            } else if (obj.datatype == "int4") {
              this.CetTable_1.dataConfig.filters.push({
                name: key,
                operator: "EQ",
                prop: key
              });
              this.CetTable_1.dynamicInput[key] = this.queryCondition_in[key];
            } else if (obj.datatype == "int8") {
              this.CetTable_1.dataConfig.filters.push({
                name: key,
                operator: "EQ",
                prop: key
              });
              this.CetTable_1.dynamicInput[key] = this.queryCondition_in[key];
            } else if (obj.datatype == "float") {
              this.CetTable_1.dataConfig.filters.push({
                name: key,
                operator: "EQ",
                prop: key
              });
              this.CetTable_1.dynamicInput[key] = this.queryCondition_in[key];
            } else if (
              obj.datatype === "enum" ||
              obj.datatype === "boolean" ||
              (obj.enumerationvalue && obj.enumerationvalue.length)
            ) {
              this.CetTable_1.dataConfig.filters.push({
                name: key,
                operator: "EQ",
                prop: key
              });
              this.CetTable_1.dynamicInput[key] = this.queryCondition_in[key];
            } else if (obj.datatype == "date") {
              this.CetTable_1.dataConfig.filters.push({
                name: key + "start",
                operator: "GE",
                prop: key
              });
              this.CetTable_1.dynamicInput[key + "start"] =
                this.queryCondition_in[key][0];

              this.CetTable_1.dataConfig.filters.push({
                name: key + "end",
                operator: "LT",
                prop: key
              });
              this.CetTable_1.dynamicInput[key + "end"] =
                this.$moment(this.queryCondition_in[key][1])
                  .endOf("date")
                  .valueOf() + 1;
            }
          }
        });
        this.CetTable_1.queryTrigger_in = new Date().getTime();
        this.$nextTick(() => {
          this.$refs.table.$refs.cetTable.doLayout();
        });
      });
    },
    // 获取表格头
    getTemplate(callback) {
      // 检定记录是固定字段
      if (this.actionTitle_in === 2) {
        this.deviceTemplate = [
          {
            name: "code",
            active: true,
            alias: $T("仪表编号"),
            allowdeactivation: false,
            datatype: "string",
            display: true,
            enumerationvalue: null
          },
          {
            name: "name",
            active: true,
            alias: $T("仪表名称"),
            allowdeactivation: false,
            datatype: "string",
            display: true,
            enumerationvalue: null
          },
          {
            name: "model",
            active: true,
            alias: $T("型号规格"),
            allowdeactivation: false,
            datatype: "string",
            display: true,
            enumerationvalue: null
          },
          {
            name: "location",
            active: true,
            alias: $T("安装使用地点"),
            allowdeactivation: false,
            datatype: "string",
            display: true,
            enumerationvalue: null
          },
          {
            name: "verificationagency",
            active: true,
            alias: $T("检定/校准机构"),
            allowdeactivation: false,
            datatype: "string",
            display: true,
            enumerationvalue: null
          },
          {
            name: "lastoverhauldate",
            active: true,
            alias: $T("检定日期"),
            allowdeactivation: false,
            datatype: "date",
            display: true,
            enumerationvalue: null
          },
          {
            name: "nextoverhauldate",
            active: true,
            alias: $T("下次检定日期"),
            allowdeactivation: false,
            datatype: "date",
            display: true,
            enumerationvalue: null
          },
          {
            name: "certiication",
            active: true,
            alias: $T("检定证明证书"),
            allowdeactivation: false,
            datatype: "img",
            display: false,
            enumerationvalue: null
          }
        ];
        this.rendererColumn(callback);
        this.$emit("columnArr_out", this.deviceTemplate);
        return;
      }
      customApi.getDeviceTemplate(this.projectId).then(response => {
        if (response.code === 0) {
          this.deviceTemplate = this._.get(response, "data", []);
          this.$emit("columnArr_out", this.deviceTemplate);
          this.rendererColumn(callback);
        }
      });
    },
    rendererColumn(callback) {
      const _this = this;
      const data = this.deviceTemplate.filter(item => {
        if (!item.allowdeactivation) {
          return item.display;
        } else {
          return item.display && item.active;
        }
      });
      var columnArr = [
        {
          type: "index", // selection 勾选 index 序号
          width: 65,
          prop: "", // 支持path a[0].b
          label: "#",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true
        }
      ];
      var arr = data.map(item => {
        return {
          prop: item.name, // 支持path a[0].b
          label: item.alias,
          headerAlign: "left",
          minWidth: "120px",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue, index) {
            if (!cellValue && cellValue !== 0 && cellValue !== false) {
              return "--";
            }
            var value;
            if (item.datatype === "enum") {
              var obj = item.enumerationvalue.find(
                i => i.id === Number(cellValue)
              );
              value = obj && obj.text;
            } else if (item.datatype === "string") {
              value = cellValue;
            } else if (item.datatype === "date") {
              if (cellValue) {
                value = _this.$moment(Number(cellValue)).format("YYYY-MM-DD");
              }
            } else if (item.datatype === "int4") {
              if (cellValue || cellValue === 0) {
                value = Number(cellValue).toFixed2(0);
              }
            } else if (item.datatype === "int8") {
              if (cellValue || cellValue === 0) {
                value = Number(cellValue).toFixed2(0);
              }
            } else if (item.datatype === "float") {
              if (cellValue || cellValue === 0) {
                value = Number(cellValue).toFixed2(2);
              }
            } else if (item.datatype === "boolean") {
              if (cellValue === true) {
                value = $T("是");
              } else if (cellValue === false) {
                value = $T("否");
              }
            } else if (
              item.enumerationvalue &&
              item.enumerationvalue.length > 0
            ) {
              const obj = item.enumerationvalue.find(
                i => i.id === Number(cellValue)
              );
              value = obj && obj.text;
            }

            if (value || value === 0) {
              return value;
            } else {
              return "--";
            }
          }
        };
      });
      if (arr.length) {
        _this.columnArr = columnArr.concat(...arr);
      } else {
        _this.columnArr = columnArr.push({
          prop: "test",
          label: ""
        });
      }
      this.$nextTick(() => {
        this.$refs.table.$refs.cetTable.doLayout();
      });

      callback && callback();
    },
    handelDetail(row) {
      this.detail.data_in = this._.cloneDeep(row);
      this.detail.visibleTrigger_in = new Date().getTime();
    },
    handelEdit(row) {
      this.$emit("tableEdit_out", row);
    },
    handelDelete(row) {
      var deleteFn;
      if (this.actionTitle_in === 1) {
        deleteFn = "deleteDashboard";
      } else if (this.actionTitle_in === 2) {
        deleteFn = "deleteRecord";
      }
      this.$confirm($T("是否删除此台账?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          customApi[deleteFn]([row.id]).then(response => {
            if (response.code === 0) {
              this.$message({
                type: "success",
                message: $T("删除成功！")
              });
              this.getData();
              this.$emit("upStatistics_out");
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已取消删除")
          });
        });
    },
    CetTable_1_outputData_out(val) {
      // 导出按钮状态
      this.$emit("exportDisableStatus_out", !val || !val.length);
    },
    CetTable_1_selectionChange(val) {
      this.CetTable_1.selectionAll = val;
      let ids = this.CetTable_1.selectionAll.map(item => item.id);
      this.$emit("selectionAll", ids);
    }
  },
  activated: function () {}
};
</script>
<style lang="scss" scoped>
.delete {
  @include font_color(Sta3);
}
:deep(.el-table__fixed-right .el-table__row td:last-child > div) {
  width: 160px !important;
}
:deep(.el-table__body-wrapper .el-table__row td:last-child > div) {
  width: 160px !important;
}
</style>
