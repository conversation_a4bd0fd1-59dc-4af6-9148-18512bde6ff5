<template>
  <div class="page eem-common">
    <el-container class="fullheight eem-container flex-column">
      <div height="auto" class="padding0 mbJ3">
        <div class="fl lh32">
          {{ energyTypeObj && energyTypeObj.name }}{{ $T("计量网络图") }}
        </div>
        <CetButton
          class="fr mrJ1"
          v-bind="CetButton_export"
          v-on="CetButton_export.event"
        ></CetButton>
        <CetButton
          class="fr mrJ1"
          v-bind="CetButton_import"
          v-on="CetButton_import.event"
        ></CetButton>
        <CetButton
          class="fr mrJ1"
          v-bind="CetButton_All"
          v-on="CetButton_All.event"
        ></CetButton>
        <CetButton
          class="fr mrJ1"
          v-bind="CetButton_next"
          v-on="CetButton_next.event"
        ></CetButton>
        <customElSelect
          v-model="ElSelect_energyType.value"
          v-bind="ElSelect_energyType"
          v-on="ElSelect_energyType.event"
          class="fr mrJ1"
          :prefix_in="$T('能源类型')"
        >
          <ElOption
            v-for="item in ElOption_energyType.options_in"
            :key="item[ElOption_energyType.key]"
            :label="item[ElOption_energyType.label]"
            :value="item[ElOption_energyType.value]"
            :disabled="item[ElOption_energyType.disabled]"
          ></ElOption>
        </customElSelect>
      </div>
      <div class="padding0 flex-auto">
        <div class="Tooltip">
          <span>{{ $T("在线") }}</span>
          <span>{{ $T("离线") }}</span>
          <span>
            {{ $T("未关联") }}
            <el-popover
              class="popover"
              placement="top-start"
              trigger="hover"
              width="160"
              :content="$T('管网设备未配置实际设备或未配置仪表')"
            >
              <i slot="reference" class="el-icon-question"></i>
            </el-popover>
          </span>
        </div>
        <measureTopologyChart
          v-if="showTopology"
          v-bind="measureTopologyChartConfig"
        />
      </div>
      <div class="padding0 mtJ3" style="height: 250px">
        <CetTable
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <ElTableColumn v-bind="ElTableColumn_codename"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
          <ElTableColumn
            v-bind="ElTableColumn_modelspecification"
          ></ElTableColumn>
          <ElTableColumn
            v-bind="ElTableColumn_managementnumber"
          ></ElTableColumn>
          <ElTableColumn
            v-bind="ElTableColumn_installationlocation"
          ></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_monitorstatus"></ElTableColumn>
        </CetTable>
      </div>
    </el-container>
    <el-upload
      v-show="false"
      ref="upload"
      action=""
      :auto-upload="true"
      :multiple="false"
      :limit="1"
      :http-request="httpRequest"
    >
      <el-button
        slot="trigger"
        v-show="false"
        size="small"
        type="primary"
        ref="uploadBtn"
      >
        {{ $T("选取文件") }}
      </el-button>
    </el-upload>
  </div>
</template>

<script>
import commonApi from "@/api/custom.js";
import common from "eem-utils/common.js";
import measureTopologyChart from "./measureTopologyChart.vue";
export default {
  name: "metrics",
  components: { measureTopologyChart },
  data() {
    const language = window.localStorage.getItem("omega_language") === "en";
    return {
      hierarchy: 2,
      showTopology: false,
      measureTopologyChartConfig: {
        inputData_in: null
      },
      // energyType组件
      ElSelect_energyType: {
        value: "",
        style: {
          width: language ? "260px" : "200px"
        },
        event: {
          change: this.ElSelect_energyType_change_out
        }
      },
      // energyType组件
      ElOption_energyType: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      CetButton_import: {
        visible_in: true,
        disable_in: false,
        title: $T("导入数据"),
        type: "primary",
        size: "",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_import_statusTrigger_out
        }
      },
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: $T("导出模板"),
        type: "primary",
        size: "",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: $T("加载下一层"),
        type: "primary",
        size: "",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      CetButton_All: {
        visible_in: true,
        disable_in: false,
        title: $T("加载全部"),
        type: "primary",
        size: "",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_All_statusTrigger_out
        }
      },
      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "getMeasureTable",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [
            { name: "energytype", operator: "EQ", prop: "energytype" },
            { name: "hierarchy", operator: "EQ", prop: "hierarchy" },
            { name: "projectId", operator: "EQ", prop: "projectId" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: $T("合计")
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {
          energytype: null,
          projectId: null,
          hierarchy: 2
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {},
        highlightCurrentRow: false,
        spanMethod: this.spanMethod
      },
      ElTableColumn_codename: {
        prop: "codename", // 支持path a[0].b
        label: $T("能源计量器具代号"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_name: {
        prop: "name", // 支持path a[0].b
        label: $T("名称"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_modelspecification: {
        prop: "model", // 支持path a[0].b
        label: $T("型号规格"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_managementnumber: {
        prop: "managementnumber", // 支持path a[0].b
        label: $T("管理编号"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_installationlocation: {
        prop: "location", // 支持path a[0].b
        label: $T("安装使用地点"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_monitorstatus: {
        prop: "monitorproperty", // 支持path a[0].b
        label: $T("测量对象属性"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      }
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    energyTypeObj() {
      return this.ElOption_energyType.options_in.find(
        item => item.energytype == this.ElSelect_energyType.value
      );
    },
    mergeArr() {
      if (!this.CetTable_1.data.length) {
        return [];
      }
      var wrap = this.CetTable_1.data[0][this.ElTableColumn_codename.prop];
      var arr = [];
      this.CetTable_1.data.forEach((item, index) => {
        if (item[this.ElTableColumn_codename.prop] !== wrap) {
          wrap = item[this.ElTableColumn_codename.prop];
          arr.push(index);
        }
      });
      return arr;
    } // 合并的行数下标
  },
  watch: {},
  methods: {
    init() {
      this.reset();
      commonApi.getProjectEnergy(this.projectId).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []).filter(
            item =>
              item.energytype !== 13 &&
              item.energytype !== 18 &&
              item.energytype !== 22
          );
          this.ElOption_energyType.options_in = data;
          this.ElSelect_energyType.value =
            this.ElOption_energyType.options_in[0].energytype;
          this.getData();
        }
      });
    },
    reset() {
      this.$nextTick(() => {
        this.$refs.upload.clearFiles();
      });
    },
    getData() {
      if (!this.ElSelect_energyType.value) {
        return;
      }
      this.hierarchy = 2;
      this.CetTable_1.dynamicInput.energytype = this.ElSelect_energyType.value;
      this.CetTable_1.dynamicInput.projectId = this.projectId;
      this.CetTable_1.dynamicInput.hierarchy = this.hierarchy;
      this.measureTopologyChartConfig.inputData_in = null;
      this.getTopologyData();
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    CetButton_next_statusTrigger_out(val) {
      if (!this.hierarchy) {
        this.measureTopologyChartConfig.inputData_in = null;
        this.hierarchy = 1;
      }
      this.hierarchy++;
      this.CetTable_1.dynamicInput.hierarchy = this.hierarchy;
      this.getTopologyData();
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    CetButton_All_statusTrigger_out(val) {
      this.hierarchy = 0;
      this.CetTable_1.dynamicInput.hierarchy = this.hierarchy;
      this.getTopologyData();
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    getTopologyData() {
      commonApi
        .getMeasureTopology({
          energytype: this.ElSelect_energyType.value,
          projectId: this.projectId,
          n: this.hierarchy,
          nodes: []
        })
        .then(response => {
          if (response.code === 0) {
            var val = this._.get(response, "data", {});
            if (val && val.dataInfo && val.dataInfo.length > 0) {
              this.showTopology = true;
              const dataInfo = val.dataInfo.map(item => {
                var obj = {
                  id: item.name,
                  label: item.nodeName || "",
                  communicationStatus: item.communicationStatus,
                  accuracylevel: " ",
                  codename: " "
                };
                // 有实际设备、表计才展示状态，其他归为虚拟表计
                if (
                  !item.deviceId ||
                  !item.instruments ||
                  item.instruments.length === 0
                ) {
                  if (item.instruments && item.instruments.length > 0) {
                    obj.codename = item.instruments[0].codename || "--";
                  }
                  obj.virtual = true;
                } else {
                  if (item.instruments && item.instruments.length > 0) {
                    obj.accuracylevel =
                      item.instruments[0].accuracylevel || "--";
                    obj.codename = item.instruments[0].codename || "--";
                  }
                }
                return obj;
              });
              const dataLink = val.dataLink || [];
              // if (!this.measureTopologyChartConfig.inputData_in || !this.measureTopologyChartConfig.inputData_in.nodes.length) {
              this.measureTopologyChartConfig.inputData_in = {
                nodes: dataInfo,
                edges: dataLink
              };
              // } else {
              //   this.measureTopologyChartConfig.inputData_in.nodes.push(...dataInfo);
              //   this.measureTopologyChartConfig.inputData_in.edges.push(...dataLink);
              // }
            } else if (this.hierarchy === 0 || this.hierarchy === 2) {
              this.showTopology = false;
            }
          } else {
            this.showTopology = false;
          }
        });
    },
    httpRequest(val) {
      const formData = new FormData();
      formData.append("file", val.file);
      var queryData = {
        projectId: this.projectId
      };
      commonApi
        .networkImportNodeAndConnection(formData, queryData)
        .then(response => {
          this.$refs.upload.clearFiles();
          if (response.code === 0) {
            this.$message({
              type: "success",
              message: $T("导入成功！")
            });
            // 刷新
            this.getData();
          }
        });
    },
    CetButton_import_statusTrigger_out(val) {
      this.$refs.uploadBtn.$el.click();
    },
    CetButton_export_statusTrigger_out(val) {
      if (!this.ElSelect_energyType.value) {
        return;
      }

      common.downExcelGET(
        `/eem-service/v1/topology/network/exportNodeAndConnection/${this.projectId}`,
        {},
        this.token
      );
    },
    ElSelect_energyType_change_out(val) {
      this.getData();
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 4 || columnIndex === 5) {
        if (!this.mergeArr.length) {
          return {
            rowspan: 1,
            colspan: 1
          };
        }
        if (!rowIndex) {
          return {
            rowspan: this.mergeArr[0],
            colspan: 1
          };
        }
        var index = this.mergeArr.indexOf(rowIndex);
        if (index !== -1) {
          if (this.mergeArr[index + 1]) {
            var indexLength = this.mergeArr[index + 1] - this.mergeArr[index];
            return {
              rowspan: indexLength,
              colspan: 1
            };
          } else {
            return {
              rowspan: this.CetTable_1.data.length - this.mergeArr[index],
              colspan: 1
            };
          }
        }
        return {
          rowspan: 0,
          colspan: 0
        };
      }
    }
  },
  activated: function () {
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.page {
  height: 100%;
}
.Tooltip {
  position: absolute;
  height: 50px;
  line-height: 50px;
  z-index: 1;
  & > span {
    display: inline-block;
    padding-left: 40px;
    margin-right: 30px;
  }
  & > span:nth-child(1) {
    background: url("./assets/img1.png") no-repeat left center;
  }
  & > span:nth-child(2) {
    background: url("./assets/img2.png") no-repeat left center;
  }
  & > span:nth-child(3) {
    background: url("./assets/img3.png") no-repeat left center;
  }
}
</style>
