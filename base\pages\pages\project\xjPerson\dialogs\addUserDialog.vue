<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="small"
    >
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <CetForm
        class="eem-cont-c1"
        :data.sync="CetForm_User.data"
        v-bind="CetForm_User"
        v-on="CetForm_User.event"
      >
        <el-row :gutter="$J3">
          <el-col :span="12">
            <el-form-item
              class="custom-form-item"
              :label="$T('成员名称')"
              prop="name"
            >
              <ElInput
                maxlength="30"
                :placeholder="$T('请输入成员名称')"
                v-model.trim="CetForm_User.data.name"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$T('角色')" prop="roleId">
              <ElSelect
                v-model="CetForm_User.data.roleId"
                v-bind="ElSelect_Role"
                v-on="ElSelect_Role.event"
              >
                <ElOption
                  v-for="item in ElOption_Roles.options_in"
                  :key="item[ElOption_Roles.key]"
                  :label="item[ElOption_Roles.label]"
                  :value="item[ElOption_Roles.value]"
                  :disabled="item[ElOption_Roles.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              class="custom-form-item"
              :label="$T('巡检确认密码')"
              prop="checkPassword"
            >
              <ElInput
                clearable
                type="password"
                maxlength="18"
                :placeholder="$T('请输入巡检确认密码')"
                v-model.trim="CetForm_User.data.checkPassword"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              class="custom-form-item"
              :label="$T('设置登录密码')"
              prop="password"
            >
              <ElInput
                clearable
                type="password"
                maxlength="18"
                :placeholder="$T('请输入新的密码')"
                v-model.trim="CetForm_User.data.password"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              class="custom-form-item"
              :label="$T('确认登录密码')"
              prop="_checkPassword"
            >
              <ElInput
                clearable
                type="password"
                maxlength="18"
                :placeholder="$T('请输入确认密码')"
                v-model.trim="CetForm_User.data._checkPassword"
              ></ElInput>
            </el-form-item>
          </el-col>
        </el-row>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";
import common from "eem-utils/common.js";
import AES from "crypto-js/aes";
import Hex from "crypto-js/enc-hex";

export default {
  name: "addUserDialog",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("新增成员"),
        showClose: true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // ResetUserPassword表单组件
      CetForm_User: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [
            "name",
            "roleId",
            "password",
            "_checkPassword",
            "checkPassword"
          ], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {
          name: "",
          roleId: null,
          password: "",
          _checkPassword: "",
          checkPassword: ""
        },
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "110px",
        labelPosition: "top",
        rules: {
          name: [
            common.pattern_name,
            { required: true, message: $T("用户名不能为空"), trigger: "blur" }
          ],
          roleId: [
            { required: true, message: $T("角色不能为空"), trigger: "blur" }
          ],
          checkPassword: [
            // common.check_strongPassword,
            {
              min: 1,
              max: 18,
              message: $T("长度在 1 到 {0} 个字符", 18),
              trigger: ["blur", "change"]
            },
            {
              required: true,
              type: "string",
              message: $T("巡检确认密码不能为空"),
              trigger: "blur",
              validator: (rule, value, callback) => {
                if (this._.isNil(value) || value === "") {
                  callback(new Error());
                  return;
                }
                callback();
              }
            }
          ],
          password: [
            common.check_strongPassword,
            {
              required: true,
              type: "string",
              message: $T("巡检登录密码不能为空"),
              trigger: "blur",
              validator: (rule, value, callback) => {
                if (this._.isNil(value) || value === "") {
                  callback(new Error());
                  return;
                }

                callback();
              }
            }
          ],
          _checkPassword: [
            {
              required: true,
              type: "string",
              message: $T("密码不一致"),
              trigger: "blur",
              validator: (rule, value, callback) => {
                if (value !== this.CetForm_User.data.password) {
                  callback(new Error());
                  return;
                }

                callback();
              }
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_User_currentData_out,
          saveData_out: this.CetForm_User_saveData_out,
          finishData_out: this.CetForm_User_finishData_out,
          finishTrigger_out: this.CetForm_User_finishTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // name组件
      ElInput_name: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_name_change_out,
          input: this.ElInput_name_input_out
        }
      },
      ElSelect_Role: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_Role_change_out
        }
      },
      ElOption_Roles: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
      this.CetForm_User.data = {
        name: "",
        roleId: null,
        checkPassword: "",
        password: "",
        _checkPassword: ""
      };
      this.CetForm_User.resetTrigger_in = this._.cloneDeep(val);
      this.getRoles_out();
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {
      this.CetForm_User.queryId_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      this.CetForm_User.inputData_in = this._.cloneDeep(val);
    }
  },
  methods: {
    CetForm_User_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_User_saveData_out(val) {
      this.addInspectorRegister_out(val);
      this.$emit("saveData_out", this._.cloneDeep(val));
    },
    CetForm_User_finishData_out(val) {
      this.$emit("finishData_out", this._.cloneDeep(val));
    },
    CetForm_User_finishTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
      this.$emit("finishTrigger_out", val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetForm_User.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      //由于接口太耗性能，不需要前端请求用户列表进行判断用户是否重名，后端那边进行处理；
      this.CetForm_User.localSaveTrigger_in = new Date().getTime();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    no() {},
    // name输出,方法名要带_out后缀
    ElInput_name_change_out(val) {},
    ElInput_name_input_out(val) {},
    ElSelect_Role_change_out(val) {
      const _this = this;
      if (_this._.isNil(val)) {
        _this.CetForm_User.data.role = {};
        return;
      }

      const role = _this._.find(_this.ElOption_Roles.options_in, { id: val });
      if (!role) {
        _this.CetForm_User.data.role = {};
        return;
      }

      _this.CetForm_User.data.role = _this._.cloneDeep(role);
    },
    //获取角色列表
    getRoles_out() {
      const _this = this;
      customApi
        .getInspectorRoles()
        .then(response => {
          if (response.code !== 0) {
            return;
          }
          const list = response.data || [];
          _this.ElOption_Roles.options_in = list;
        })
        .catch(() => {});
    },
    // 获取所有用户信息
    getUsers_out(callback) {
      const params = {};
      customApi.getAllUsers(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          callback && callback(data);
        }
      });
    },
    // 新增成员,密码需要加密
    addInspectorRegister_out(data) {
      if (!data) {
        return;
      }

      const params = data;

      params.password = data.password;
      params.checkPassword = data.checkPassword;

      customApi.addInspectorRegisterSecurity(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", {});
          this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
          this.$emit("finishTrigger_out", data);
        }
      });
    },
    stringToHex: function (str) {
      var val = "";
      for (var i = 0; i < str.length; i++) {
        if (val === "") {
          val = str.charCodeAt(i).toString(16);
        } else {
          val += str.charCodeAt(i).toString(16);
        }
      }
      return val;
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped></style>
