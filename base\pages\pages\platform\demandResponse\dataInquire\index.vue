<template>
  <div class="page flex-column">
    <div class="Toback handel" v-if="isinnerdetail">
      <i class="el-icon-arrow-left icon" @click="back"></i>
      <!-- <span class="title" @click="back">返回</span> -->
    </div>
    <el-tabs v-model="activeName" @tab-click="tabsClick" class="tabs SZWtabs">
      <el-tab-pane name="负荷聚合商" label="负荷聚合商"></el-tab-pane>
      <el-tab-pane name="园区虚拟电厂" label="园区虚拟电厂"></el-tab-pane>
    </el-tabs>
    <el-container
      class="contentBox"
      v-if="!isdetail && !isVirtualpowerplantdetail"
    >
      <el-main style="height: 100%; padding: 0px">
        <el-row>
          <el-col :span="24" class="box-col">
            <div class="el-form-item__label" style="line-height: inherit">
              <ElInput
                class="searchInput"
                v-model="ElInput_search.value"
                v-bind="ElInput_search"
                v-on="ElInput_search.event"
                @change="isinnerdetail ? getinnerDetailData() : gettableData()"
                placeholder="请输入关键字搜索"
              >
                <i slot="suffix" class="el-icon-search"></i>
              </ElInput>
            </div>
            <div class="el-form-item__label" style="line-height: inherit">
              <customElSelect
                class="customElSelect"
                v-model="ElSelect_1.value"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
                clearable
                prefix_in="用户状态"
                v-if="!isinnerdetail"
                @change="gettableData"
              >
                <ElOption
                  v-for="item in ElOption_1.options_in"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </customElSelect>
            </div>
            <div class="basic-box fl" v-if="!isinnerdetail">
              <span class="basic-box-label">响应区域</span>
              <div class="el-form-item__label" style="line-height: inherit">
                <!-- 响应区域 -->
                <el-cascader
                  class="cascader"
                  clearable
                  v-model="address"
                  :props="treeProps"
                  :options="areaOptions"
                  @change="handleChange"
                ></el-cascader>
              </div>
            </div>
            <div class="fl">
              <customElSelect
                v-model="ElSelect_3.value"
                v-bind="ElSelect_3"
                v-on="ElSelect_3.event"
                prefix_in="黑名单"
                clearable
                @change="gettableData"
              >
                <ElOption
                  v-for="item in ElOption_3.options_in"
                  :key="item[ElOption_3.key]"
                  :label="item[ElOption_3.label]"
                  :value="item[ElOption_3.value]"
                  :disabled="item[ElOption_3.disabled]"
                ></ElOption>
              </customElSelect>
            </div>
          </el-col>
        </el-row>
        <div class="tableTop clearfix">
          <div class="fl marginLeftJ2">
            共
            <span class="sum">{{ totalCount }}</span>
            条
          </div>
        </div>
        <div class="tableBody" v-if="!isinnerdetail">
          <CetTable
            ref="CetTable"
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
            @cell-click="tableRow_Click"
            @sort-change="sortChange"
          >
            <ElTableColumn label="#" type="index" width="41"></ElTableColumn>
            <ElTableColumn
              label="用户名称"
              prop="name"
              max-width="120px"
              header-align="left"
              align="left"
              show-overflow-tooltip
              :formatter="val => (val.name || val.name == 0 ? val.name : '--')"
            ></ElTableColumn>
            <ElTableColumn
              label="响应区域"
              prop="address"
              header-align="left"
              align="left"
              min-width="80px"
              show-overflow-tooltip
              :formatter="
                val => (val.address || val.address == 0 ? val.address : '--')
              "
            ></ElTableColumn>
            <template v-for="(item, index) in Columns_1">
              <ElTableColumn :key="index" v-bind="item"></ElTableColumn>
            </template>
            <!-- <ElTableColumn
              label="有效响应总次数"
              prop="effectiveResponseCount"
              v-if="isinnerdetail"
            ></ElTableColumn> -->
            <ElTableColumn
              label="用户状态"
              min-width="100"
              header-align="left"
              align="left"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span style="color: #08c673" v-if="scope.row.status === 2">
                  合作中
                </span>
                <span style="color: #f65d68" v-else>已过期</span>
              </template>
            </ElTableColumn>
            <ElTableColumn
              label="加入原因"
              prop="memo"
              max-width="120px"
              header-align="left"
              align="left"
              show-overflow-tooltip
              :formatter="val => (val.memo || val.memo == 0 ? val.memo : '--')"
            ></ElTableColumn>
            <ElTableColumn
              label="黑名单"
              min-width="100"
              header-align="left"
              align="left"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span style="color: #f65d68" v-if="scope.row.isBlacklist">
                  是
                </span>
                <span style="color: #08c673" v-else>否</span>
              </template>
            </ElTableColumn>
            <ElTableColumn
              label="操作"
              width="136"
              header-align="center"
              align="center"
            >
              <!-- <template slot-scope="scope">
                <div @click.stop>
                  <span
                    @click="handleCommand('detail', scope.row)"
                    class="handel highLight"
                  >
                    详情
                  </span>
                </div>
              </template> -->
              <template slot-scope="scope">
                <div @click.stop>
                  <span
                    class="handle delete highLight"
                    @click="deleteHandle(scope.row.id)"
                    v-if="scope.row.isBlacklist"
                    style="cursor: pointer"
                  >
                    移除黑名单
                  </span>
                  <span
                    class="handle highLight"
                    style="cursor: pointer"
                    @click="editHandle(scope.row)"
                    v-else
                  >
                    加入黑名单
                  </span>
                  <span class="line fcB1">|</span>
                  <span
                    class="handle highLight"
                    style="cursor: pointer"
                    @click="handleCommand('detail', scope.row)"
                  >
                    详情
                  </span>
                </div>
              </template>
            </ElTableColumn>
          </CetTable>
        </div>
        <div class="tableBody" v-if="isinnerdetail">
          <CetTable
            ref="CetTable"
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
            @cell-click="tableRow_Clickdetail"
            @sort-change="sortChange"
          >
            <ElTableColumn label="#" type="index" width="41"></ElTableColumn>
            <ElTableColumn
              label="用电户号"
              prop="name"
              header-align="left"
              align="left"
              max-width="296px"
              show-overflow-tooltip
              :formatter="val => (val.name || val.name == 0 ? val.name : '--')"
            ></ElTableColumn>
            <template v-for="(item, index) in Columns_1">
              <ElTableColumn :key="index" v-bind="item"></ElTableColumn>
            </template>
            <ElTableColumn
              label="操作"
              width="52"
              header-align="center"
              align="center"
            >
              <template slot-scope="scope">
                <div @click.stop>
                  <span
                    @click="handleCommand('innerdetail', scope.row)"
                    class="handel highLight"
                  >
                    详情
                  </span>
                </div>
              </template>
            </ElTableColumn>
          </CetTable>
        </div>
        <div class="tableFooter">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-sizes="pageSizes"
            :page-size.sync="pageSize"
            layout=" sizes, prev, pager, next, jumper"
            :total="totalCount"
          ></el-pagination>
        </div>
      </el-main>
    </el-container>
    <el-container
      class="contentBox"
      v-if="isdetail && !isVirtualpowerplantdetail"
    >
      <el-main style="height: 100%; padding: 0px">
        <el-row>
          <el-col :span="24" class="box-col searchBox">
            <ElInput
              class="searchInput fl marginRightJ1"
              v-model="ElInput_search.value"
              v-bind="ElInput_search"
              v-on="ElInput_search.event"
              placeholder="请输入关键字搜索"
              @change="getDetailData"
            >
              <i slot="suffix" class="el-icon-search"></i>
            </ElInput>
            <customElSelect
              class="customElSelect fl marginRightJ1"
              v-model="ElSelect_2.value"
              v-bind="ElSelect_2"
              v-on="ElSelect_2.event"
              prefix_in="响应结果"
              clearable
              @change="getDetailData"
            >
              <ElOption
                v-for="item in ElOption_2.options_in"
                :key="item[ElOption_2.key]"
                :label="item[ElOption_2.label]"
                :value="item[ElOption_2.value]"
                :disabled="item[ElOption_2.disabled]"
              ></ElOption>
            </customElSelect>
            <div class="basic-box fl">
              <span class="basic-box-label">开始时段</span>
              <el-date-picker
                class="datePicker fl szwDatePickertime"
                style="width: 400px"
                v-model="CetDatePicker_1.val"
                size="small"
                value-format="timestamp"
                type="datetimerange"
                align="right"
                :clearable="false"
                unlink-panels
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                @change="getDetailData"
              ></el-date-picker>
            </div>
          </el-col>
        </el-row>
        <div class="tableTop clearfix">
          <div class="fl marginLeftJ2">
            共
            <span class="sum">{{ totalCount }}</span>
            条
          </div>
        </div>
        <div class="tableBody">
          <CetTable
            ref="CetTable"
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
          >
            <template v-for="item in Columns_2">
              <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
            </template>
            <ElTableColumn
              label="响应结果"
              min-width="120px"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span style="color: #08c673" v-if="scope.row.validstate === 1">
                  有效响应
                </span>
                <span
                  style="color: #f65d68"
                  v-else-if="scope.row.validstate === 2"
                >
                  无效
                </span>
                <span v-else>--</span>
              </template>
            </ElTableColumn>
            <ElTableColumn
              label="操作"
              header-align="center"
              align="center"
              width="80"
            >
              <template slot-scope="scope">
                <!-- <span @click="handleCommand('innerdetail',scope.row)" v-if="!isinnerdetail">详情</span> -->

                <span
                  @click="Toeditdialog(scope.row)"
                  v-if="isinnerdetail"
                  class="handel highLight colorZS"
                >
                  负荷监测
                </span>
              </template>
            </ElTableColumn>
          </CetTable>
        </div>
        <div class="tableFooter">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-sizes="pageSizes"
            :page-size.sync="pageSize"
            layout=" sizes, prev, pager, next, jumper"
            :total="totalCount"
          ></el-pagination>
        </div>
      </el-main>
    </el-container>

    <Virtualpowerplantdetail v-if="isVirtualpowerplantdetail" />
    <blacklist
      v-bind="blacklist"
      v-on="blacklist.event"
      v-if="!isVirtualpowerplantdetail"
    />
  </div>
</template>
<script>
import Virtualpowerplantdetail from "./Virtualpowerplantdetail.vue";
import blacklist from "./dialog/blacklist.vue";
import customApi from "@/api/custom";
import moment from "moment";
export default {
  components: {
    Virtualpowerplantdetail,
    blacklist
  },
  props: {},
  computed: {},
  data() {
    return {
      detailrowdata: {},
      isinnerdetail: false,
      isSelfOperate: false,
      address: [],
      treeProps: {
        expandTrigger: "hover",
        children: "children",
        label: "name",
        value: "name"
      },
      areaOptions: [],
      isdetail: false,
      isVirtualpowerplantdetail: false,
      totalCount: 0,
      currentPage: 1,
      pageSizes: [10, 20, 50, 100],
      pageSize: 10,
      pageTotal: 0,
      activeName: "负荷聚合商",
      sum: 0,
      showDetail: false,
      thisYearEffectiveResponseCount: "",
      responseCount: "",
      thisYearEffectiveResponseRate: "",
      effectiveResponseRate: "",
      ElInput_search: {
        value: "",
        style: {},
        event: {}
      },
      blacklist: {
        openTrigger_in: new Date().getTime(),
        inputData_in: null,
        event: {
          refersh_out: this.gettableData
        }
      },
      CetDatePicker_1: {
        disable_in: false,
        val: [
          this.$moment() + 5 * 24 * 60 * 60 * 1000 - 24 * 60 * 60 * 1000 * 365,
          this.$moment() + 5 * 24 * 60 * 60 * 1000
        ],
        config: {
          valueFormat: "timestamp",
          rangeSeparator: "至",
          clearable: false,
          size: "small",
          pickerOptions: {}
        }
      },
      CetDialog_1: {
        title: "导入",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        width: "656px",
        showClose: true
      },

      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        "highlight-current-row": false,
        paginationCfg: {
          pageSize: 10,
          textAlign: "center",
          layout: "sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          "selection-change": this.handleSelectionChange
          // "cell-click": this.tableRow_Click,
        }
      },
      Columns_1: [],
      Columns_2: [],
      Columns_inletWire: [
        {
          prop: "percentageInvalidTimes", // 支持path a[0].b
          label: "无效响应存在次数占比", //列名
          headerAlign: "left",
          align: "left",
          width: "250",
          sortable: true,
          showOverflowTooltip: true,
          formatter: function (val) {
            return val.percentageInvalidTimes || val.percentageInvalidTimes == 0
              ? (val.percentageInvalidTimes * 100).toFixed(2) + "%"
              : "--";
          }
        },
        {
          prop: "totalTimes", // 支持path a[0].b
          label: "响应总次数", //列名
          headerAlign: "left",
          sortable: true,
          width: "180",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (val) {
            return val.totalTimes || val.totalTimes == 0
              ? val.totalTimes
              : "--";
          }
        },

        {
          prop: "yearPercentageInvalidTimes", // 支持path a[0].b
          label: "本年无效响应存在次数占比", //列名
          headerAlign: "left",
          sortable: true,
          width: "250",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (val) {
            return val.yearPercentageInvalidTimes ||
              val.yearPercentageInvalidTimes == 0
              ? (val.yearPercentageInvalidTimes * 100).toFixed(2) + "%"
              : "--";
          }
        },
        {
          prop: "yearTotalTimes", // 支持path a[0].b
          label: "本年响应次数", //列名
          sortable: true,
          width: "180",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (val) {
            return val.yearTotalTimes || val.yearTotalTimes == 0
              ? val.yearTotalTimes
              : "--";
          }
        }
      ],
      Columns_user: [
        {
          prop: "percentageInvalidTimes", // 支持path a[0].b
          label: "总有效响应率", //列名
          headerAlign: "left",
          align: "left",
          width: "200",
          sortable: true,
          showOverflowTooltip: true,
          formatter: function (val) {
            return val.percentageInvalidTimes || val.percentageInvalidTimes == 0
              ? (val.percentageInvalidTimes * 100).toFixed(2) + "%"
              : "--";
          }
        },
        {
          prop: "totalTimes", // 支持path a[0].b
          label: "响应总次数", //列名
          headerAlign: "left",
          align: "left",
          width: "200",
          sortable: true,
          showOverflowTooltip: true,
          formatter: function (val) {
            return val.totalTimes || val.totalTimes == 0
              ? val.totalTimes
              : "--";
          }
        },
        {
          prop: "percentageTimes", // 支持path a[0].b
          label: "有效响应总次数", //列名
          headerAlign: "left",
          align: "left",
          width: "200",
          sortable: true,
          showOverflowTooltip: true,
          formatter: function (val) {
            return val.percentageTimes || val.percentageTimes == 0
              ? val.percentageTimes
              : "--";
          }
        },
        {
          prop: "yearPercentageInvalidTimes", // 支持path a[0].b
          label: "本年有效响应率", //列名
          headerAlign: "left",
          width: "250",
          align: "left",
          sortable: true,
          showOverflowTooltip: true,
          formatter: function (val) {
            return val.yearPercentageInvalidTimes ||
              val.yearPercentageInvalidTimes == 0
              ? (val.yearPercentageInvalidTimes * 100).toFixed(2) + "%"
              : "--";
          }
        },
        {
          prop: "yearTotalTimes", // 支持path a[0].b
          label: "本年响应次数", //列名
          headerAlign: "left",
          align: "left",
          width: "250",
          sortable: true,
          showOverflowTooltip: true,
          formatter: function (val) {
            return val.yearTotalTimes || val.yearTotalTimes == 0
              ? val.yearTotalTimes
              : "--";
          }
        }
      ],
      Columns_resource: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          width: "41" //绝对宽度
        },
        {
          prop: "name", // 支持path a[0].b
          label: "计划名称", //列名
          headerAlign: "left",
          align: "left",
          width: "250",
          showOverflowTooltip: true,
          formatter: function (val) {
            return val.name || val.name == 0 ? val.name : "--";
          }
        },
        {
          prop: "starttime", // 支持path a[0].b
          label: "响应时间", //列名
          headerAlign: "left",
          align: "left",
          width: "320",
          showOverflowTooltip: true,
          formatter: function (val) {
            return (
              moment(val.starttime).format("YYYY-MM-DD HH:mm:ss") +
              "至" +
              moment(val.endtime).format("YYYY-MM-DD HH:mm:ss")
            );
          }
        },
        {
          prop: "planvalue", // 支持path a[0].b
          label: "申报响应量（MW）", //列名
          headerAlign: "left",
          align: "left",
          width: "250",
          showOverflowTooltip: true,
          formatter: function (val) {
            return val.planvalue || val.planvalue == 0
              ? val.planvalue.toFixed(4)
              : "--";
          }
        },
        {
          prop: "calcvalue", // 支持path a[0].b
          label: "实际响应量（MW)", //列名
          headerAlign: "left",
          align: "left",
          width: "250",
          showOverflowTooltip: true,
          formatter: function (val) {
            return val.calcvalue || val.calcvalue == 0
              ? val.calcvalue.toFixed(4)
              : "--";
          }
        }
      ],
      ElSelect_1: {
        value: "",
        style: {
          width: "200px"
        },
        event: {}
      },
      ElOption_1: {
        options_in: [
          { id: 2, text: "合作中" },
          { id: 3, text: "已过期" }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: "",
        style: {
          width: "200px"
        },
        event: {}
      },
      ElOption_2: {
        options_in: this.$store.state.enumerations.responsevalidstate || [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_3: {
        value: "",
        style: {
          width: "200px"
        },
        event: {}
      },
      ElOption_3: {
        options_in: [
          { id: true, text: "是" },
          { id: false, text: "否" }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      editInletWire: {
        openTrigger_in: new Date().getTime(),
        inputData_in: null,
        isVirtualpowerplant: false
      },
      editResource: {
        openTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      relevanceDetail: {
        openTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      addUser: {
        openTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      editlog: {
        openTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      echartdialog: {
        openTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      isVirtualpowerplant: false,
      fileList: []
    };
  },
  watch: {
    isdetail: {
      handler(val) {
        {
          if (val) {
            this.Columns_2 = this._.cloneDeep(this.Columns_resource);
          } else {
            if (this.isinnerdetail) {
              this.Columns_1 = this._.cloneDeep(this.Columns_user);
            } else {
              this.Columns_1 = this._.cloneDeep(this.Columns_inletWire);
            }
          }
        }
      },
      immediate: true
    },
    isinnerdetail(val) {
      if (this.isinnerdetail) {
        this.Columns_1 = this._.cloneDeep(this.Columns_user);
      } else {
        this.Columns_1 = this._.cloneDeep(this.Columns_inletWire);
      }
    }
  },
  methods: {
    init() {
      this.sum = 0;
      // this.CetTable_1.data = [];
      this.ElInput_search.value = "";
      this.ElSelect_1.value = "";
      // 清除列表勾选
      // this.$refs.CetTable.$refs.cetTable.clearSelection();
    },
    tabsClick(val) {
      console.log(val);
      if (val.name == "园区虚拟电厂") {
        this.isdetail = false;
        this.isVirtualpowerplantdetail = true;
        this.isSelfOperate = true;
        this.isinnerdetail = false;
      } else {
        this.isVirtualpowerplantdetail = false;
        this.isSelfOperate = false;
        this.isinnerdetail = false;
        this.gettableData();
      }
      this.editInletWire.isVirtualpowerplant =
        !this.editInletWire.isVirtualpowerplant;
      console.log(this.editInletWire.isVirtualpowerplant);
      this.init();
    },
    Toeditdialog(row) {
      row.isselfoperate = false;
      row.isquery = true;
      this.$router.push({ path: "/realTimeMonitoring", query: row });
    },
    handleSelectionChange(val) {
      this.sum = val.length;
    },
    tableRow_Click(row) {
      if (!this.isSelfOperate) {
        this.handleCommand("detail", row);
      }
    },
    editHandle(row) {
      this.blacklist.openTrigger_in = new Date().getTime();
      this.blacklist.inputData_in = row;
    },
    tableRow_Clickdetail(row) {
      if (!this.isSelfOperate) {
        if (row.modelLabel === "demandsideresponsedetails") {
          console.log(row);
        } else {
          this.handleCommand("innerdetail", row);
        }
      }
    },
    deleteHandle(id) {
      this.$confirm("确定将该客户移除黑名单吗？", "移除黑名单", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          customApi.statisticsDatasRemove([id]).then(response => {
            if (response.code === 0) {
              this.currentPage = 1;
              this.gettableData();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消移除黑名单"
          });
        });
    },
    handleChange() {
      this.gettableData();
    },
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      if (this.isinnerdetail) {
        this.isdetail ? this.getDetailData() : this.getinnerDetailData();
      } else {
        this.gettableData();
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      if (this.isinnerdetail) {
        this.isdetail ? this.getDetailData() : this.getinnerDetailData();
      } else {
        this.gettableData();
      }
    },
    back() {
      this.isdetail = false;
      this.isinnerdetail = false;
      this.gettableData();
    },
    //表格列排序条件变化
    sortChange(val) {
      console.log(val);

      switch (val.prop) {
        case "effectiveResponseRate":
          this.effectiveResponseRate = val.order === "ascending" ? true : false;
          break;
        case "thisYearEffectiveResponseRate":
          this.thisYearEffectiveResponseRate =
            val.order === "ascending" ? true : false;
          break;
        case "responseCount":
          this.responseCount = val.order === "ascending" ? true : false;
          break;
        case "thisYearEffectiveResponseCount":
          this.thisYearEffectiveResponseCount =
            val.order === "ascending" ? true : false;
          break;
      }
      this.currentPage = 1; //分页置为第一页
      this.gettableData();
    },
    getDistrictTree() {
      customApi.demandSideResponsedetailsdistrict().then(response => {
        this.areaOptions = this._.get(response, "data", []);
      });
    },
    gettableData() {
      let projectId = this.$store.state.projectId;
      customApi.commonqueryPowersellingcompany(projectId).then(response => {
        if (response.code == 0) {
          let data = {
            // id: response.data[0].id,
            powersellingcompanyid: this.$store.state.sellCompanyId,
            keyword: this.ElInput_search.value,
            status: this.ElSelect_1.value,
            address: this.address ? this.address.join("/") : "",
            isselfoperate: false,
            isBlacklist: this.ElSelect_3.value,
            // thisYearEffectiveResponseCount: this.thisYearEffectiveResponseCount,
            // responseCount: this.responseCount,
            // thisYearEffectiveResponseRate: this.thisYearEffectiveResponseRate,
            // effectiveResponseRate: this.effectiveResponseRate,
            page: {
              index: this.currentPage - 1,
              limit: this.pageSize
            }
          };
          customApi.statisticsDatasqueryDatas(data).then(response => {
            if (response.code == 0) {
              this.CetTable_1.data = this._.get(response, "data", []);
              this.totalCount = response.total;
            }
          });
        }
      });
    },
    getinnerDetailData() {
      let data = {
        id: this.detailrowdata.id,
        isselfoperate: false,
        keyword: this.ElInput_search.value,
        // planState: this.ElSelect_1.value,

        // province: this.address[0],
        // city: this.address[1],

        page: {
          index: this.currentPage - 1,
          limit: this.pageSize
        }
      };
      customApi.statisticsDatasqueryLoadAccountInfos(data).then(response => {
        if (response.code == 0) {
          this.CetTable_1.data = this._.get(response, "data", []);
          this.totalCount = response.total;
        }
      });
    },
    getDetailData() {
      console.log(this.CetDatePicker_1.val);
      let data = {
        // isSelfOperate: this.isSelfOperate,
        keyword: this.ElInput_search.value,
        page: {
          index: this.currentPage - 1,
          limit: this.pageSize
        },
        id: this.detailrowdata.id,
        starttime: this.CetDatePicker_1.val ? this.CetDatePicker_1.val[0] : "",
        endtime: this.CetDatePicker_1.val ? this.CetDatePicker_1.val[1] : "",
        status: this.ElSelect_2.value
      };
      customApi.statisticsDatasloadAccountDetails(data).then(response => {
        if (response.code == 0) {
          this.CetTable_1.data = this._.get(response, "data", []);
          this.totalCount = response.total;
        }
      });
    },
    handleCommand(val, row) {
      if (val === "innerdetail") {
        this.isdetail = true;
        this.init();
        this.detailrowdata = row;
        this.getDetailData();
      } else if (val === "detail") {
        console.log(222);
        this.isdetail = false;
        this.isinnerdetail = true;
        this.init();
        this.detailrowdata = row;
        this.getinnerDetailData();
      }
    }
  },
  created() {
    this.getDistrictTree();
    this.gettableData();
  },
  mounted() {},
  activated() {
    this.showDetail = false;
  }
};
</script>
<style lang="scss" scoped>
.page {
  margin: 0 !important;
  height: 100% !important;
  width: 100% !important;
  position: relative;
  .tabs {
    padding: 0 19px;
    :deep(.el-tabs__header) {
      margin: 0;
    }
    @include background_color(BG1);
  }
  .Dialogbox {
    @include background_color(BG1);
    height: 478px;
    .title {
      @include font_size("Aa");

      font-weight: 400;
      color: #808797;
      line-height: 36px;
    }
    :deep(.el-button) {
      width: 100px;
      height: 36px;
      background: #f6f6f7;

      color: #808797;
      border: none;
    }
  }
  .noticebox {
    .noticetitle {
      height: 20px;
      @include font_size("H3");
      color: #1c2538;
      font-weight: 500;
      line-height: 20px;
      margin: 10px 0;
      img {
        margin-right: 16px;
      }
    }
    p {
      margin: 0 0 0 34px;
      font-weight: 400;
      color: #808797;
      @include font_size("Aa");
    }
  }
  .searchInput {
    width: 298px;
    :deep(.el-input__inner) {
      padding-right: 60px;
    }
  }
  .customElSelect {
    // margin-left: 8px;
  }
  .contentBox {
    @include padding_top(J2);
    flex: 1;
    min-height: 0;
    box-sizing: border-box;
    .aside {
      height: 100%;
      border-top-left-radius: mh-get(C3);
      border-bottom-left-radius: mh-get(C3);
    }
    .tableTop {
      @include padding_top(J2);
      @include margin_top(J2);
      @include background_color(BG1);
      border-top-left-radius: mh-get(C3);
      border-top-right-radius: mh-get(C3);
      line-height: 34px;
      .sum {
        @include font_color(ZS);
      }
    }
    .tableBody {
      @include background_color(BG1);
      padding-top: 12px;
      @include padding_left(J2);
      @include padding_right(J2);
      position: relative;
      height: calc(100% - 174px);
      :deep(.el-footer) {
        position: absolute;
        right: 36px;
        bottom: -48px;
      }
    }
    .tableFooter {
      text-align: right;
      @include padding_right(J2);
      @include padding_top(J2);
      @include padding_bottom(J2);
      height: 30px;
      @include background_color(BG1);
      border-bottom-left-radius: mh-get(C3);
      border-bottom-right-radius: mh-get(C3);
    }
  }
  .delete:hover {
    @include font_color(F2);
  }
  .el-icon-search {
    @include font_size("Aa");
    margin: 10px 7px 0 0;
  }
  .el-dropdown-link {
    cursor: pointer;
  }
}
.handel {
  cursor: pointer;
}
.cascader {
  width: 200px;
}
.Toback {
  height: 14px;
  position: absolute;
  top: 9px;
  left: 4px;
  z-index: 999;
  .title {
    @include font_size("Ab");
    line-height: 20px;
  }
  .icon {
    @include font_size("Aa");
    line-height: 20px;
  }
}
.el-form-item__label {
  @include padding_right(J1);
}
</style>
