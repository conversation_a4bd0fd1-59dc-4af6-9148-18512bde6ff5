<template>
  <div class="station-list plJ1 prJ1">
    <el-input
      class="mtJ3"
      size="small"
      placeholder="输入关键字以检索"
      v-model="searchVal"
    >
      <i slot="suffix" class="el-input__icon el-icon-search"></i>
    </el-input>
    <div style="max-height: 400px; overflow-y: auto">
      <el-checkbox-group class="p10" v-model="checked" @change="changeStation">
        <el-checkbox
          style="display: block"
          v-for="point in copyPoints"
          :label="point"
          :key="
            point.paraName +
            point.dataId +
            point.logicalDeviceIndex +
            point.dataTypeId
          "
        >
          {{ point.paraName }}-{{ pecDataType[point.dataTypeId] }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <div style="text-align: center">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[100, 500, 1000]"
        :page-size="pageSize"
        :pager-count="5"
        layout="total, sizes, prev, pager, next"
        :total="pageTotal"
      ></el-pagination>
    </div>
  </div>
</template>
<script>
import { PEC_DATA_TYPE } from "@/store/constQuantity";
import { httping } from "@omega/http";
export default {
  name: "StationList",
  props: {
    stations: {
      type: Array,
      default: () => []
    },
    deviceID: {
      type: Number,
      default: () => []
    },
    checkedStations: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    // cities() {
    //   if (!this.searchVal) return this.options;
    //   return this.options.filter(item => {
    //     return item.indexOf(this.searchVal) !== -1;
    //   });
    // }
  },
  data() {
    return {
      searchVal: "",
      points: [],
      copyPoints: [],
      pecDataType: PEC_DATA_TYPE,
      checked: this.checkedStations,
      currentPage: 1,
      pageSize: 100,
      pageTotal: 0
    };
  },
  watch: {
    stations(val) {
      this.points = val;
      var arr = [];
      this.checkedStations.forEach(item => {
        var hasStations = this.points.find(item1 => {
          return (
            item1.dataId === item.dataId &&
            item1.dataTypeId === item.dataTypeId &&
            item1.deviceId === item.deviceId
          );
        });
        if (hasStations) {
          arr.push(hasStations);
        }
      });
      this.$set(this, "checked", arr);
    },
    checked(val) {
      this.$emit("update:checkedStations", val);
    },
    checkedStations(val) {
      this.checked = val;
    },
    searchVal(val) {
      if (!val) return (this.points = this.stations);
      this.points = this.stations.filter(item => {
        return (
          `${item.paraName}-${this.pecDataType[item.dataTypeId]}`.indexOf(
            val
          ) !== -1
        );
      });
    },
    points(val) {
      const len = val.length;
      this.pageTotal = len;
      this.currentPage = 1;
      this.pageSize = 100;
      this.copyPoints = val.slice(
        (this.currentPage - 1) * this.pageSize,
        this.currentPage * this.pageSize
      );
    }
  },
  methods: {
    // /api/comm/v1/device/datalog/points 获取设备的定时记录测点列表
    changeStation(val) {
      this.$emit("changeStation", val);
    },
    getPoints() {
      const params = [this.deviceID];
      httping({
        url: `/device-data-service/api/comm/v1/device/datalog/points`,
        method: "POST",
        data: params
      }).then(res => {
        this.points = res.data[this.deviceID];
      });
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.pageSize = val;
      this.copyPoints = this.points.slice(
        (this.currentPage - 1) * this.pageSize,
        this.currentPage * this.pageSize
      );
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.currentPage = val;
      this.copyPoints = this.points.slice(
        (this.currentPage - 1) * this.pageSize,
        this.currentPage * this.pageSize
      );
    }
  },
  created() {
    // this.getPoints()
  },
  activated() {
    this.searchVal = "";
  }
};
</script>
<style lang="scss" scoped>
.mt10 {
  margin-top: 10px !important;
}
.p10 {
  padding: 10px !important;
}
</style>
