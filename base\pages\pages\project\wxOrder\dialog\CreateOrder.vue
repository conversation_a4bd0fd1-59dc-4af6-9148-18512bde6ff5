<template>
  <div>
    <CetDialog v-bind="CetDialog_add" v-on="CetDialog_add.event" class="small">
      <div style="padding: 0">
        <CetForm
          ref="createPlanForm"
          :data.sync="CetForm_1.inputData_in"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
          class="eem-cont-c1"
          label-position="top"
        >
          <el-row :gutter="$J3">
            <el-col :span="12">
              <el-form-item :label="$T('维修目标')" prop="deviceListName">
                <div class="custom-btn" @click="OpenxjDevice">
                  <span>
                    {{ CetForm_1.inputData_in.deviceListName }}
                  </span>
                  <span class="toConnect">
                    <omega-icon symbolId="link-lin" />
                  </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('等级')" prop="tasklevel">
                <ElSelect
                  v-model="CetForm_1.inputData_in.tasklevel"
                  v-bind="ElSelect_tasklevel"
                  v-on="ElSelect_tasklevel.event"
                >
                  <ElOption
                    v-for="item in ElOption_tasklevel.options_in"
                    :key="item[ElOption_tasklevel.key]"
                    :label="item[ElOption_tasklevel.label]"
                    :value="item[ElOption_tasklevel.value]"
                    :disabled="item[ElOption_tasklevel.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="$J3">
            <el-col :span="12">
              <el-form-item :label="$T('责任班组')" prop="teamid">
                <ElSelect
                  v-model="CetForm_1.inputData_in.teamid"
                  v-bind="ElSelect_team"
                  v-on="ElSelect_team.event"
                >
                  <ElOption
                    v-for="item in ElOption_team.options_in"
                    :key="item[ElOption_team.key]"
                    :label="item[ElOption_team.label]"
                    :value="item[ElOption_team.value]"
                    :disabled="item[ElOption_team.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('运值确认班组')" prop="inspectteamid">
                <ElSelect
                  v-model="CetForm_1.inputData_in.inspectteamid"
                  v-bind="ElSelect_team"
                  v-on="ElSelect_team.event"
                >
                  <ElOption
                    v-for="item in ElOption_team.options_in"
                    :key="item[ElOption_team.key]"
                    :label="item[ElOption_team.label]"
                    :value="item[ElOption_team.value]"
                    :disabled="item[ElOption_team.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="$J3">
            <el-col :span="12">
              <el-form-item :label="$T('预计耗时')" prop="timeconsumeplan">
                <ElInputNumber
                  v-model="CetForm_1.inputData_in.timeconsumeplan"
                  v-bind="ElInputNumber_4"
                  v-on="ElInputNumber_4.event"
                ></ElInputNumber>
                <span class="form-item-unit">h</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :label="$T('设备归类')"
                prop="deviceclassificationid"
              >
                <div class="custom-btn isDisable">
                  <span>
                    {{ CetForm_1.inputData_in.deviceTypeName }}
                  </span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="$J3">
            <el-col :span="12">
              <el-form-item
                :label="$T('故障类型')"
                prop="eventclassificationid"
              >
                <ElSelect
                  v-model="CetForm_1.inputData_in.eventclassificationid"
                  v-bind="ElSelect_eventType"
                  v-on="ElSelect_eventType.event"
                >
                  <ElOption
                    v-for="item in ElOption_eventType.options_in"
                    :key="item[ElOption_eventType.key]"
                    :label="item[ElOption_eventType.label]"
                    :value="item[ElOption_eventType.value]"
                    :disabled="item[ElOption_eventType.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :label="$T('故障选择')"
                prop="faultscenariosid"
                class="faultscenariosid"
              >
                <ElSelect
                  class="flex-auto mrJ1"
                  v-model="CetForm_1.inputData_in.faultscenariosid"
                  v-bind="ElSelect_scene"
                  v-on="ElSelect_scene.event"
                >
                  <ElOption
                    v-for="item in ElOption_scene.options_in"
                    :key="item[ElOption_scene.key]"
                    :label="item[ElOption_scene.label]"
                    :value="item[ElOption_scene.value]"
                    :disabled="item[ElOption_scene.disabled]"
                  ></ElOption>
                </ElSelect>
                <div class="advanced-setting fcZS" @click="OpenReservePlanList">
                  <span>{{ $T("查看预案") }}</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="$J3">
            <el-col :span="24">
              <el-form-item :label="$T('故障描述')" prop="faultdescription">
                <el-input
                  type="textarea"
                  rows="3"
                  resize="none"
                  :placeholder="$T('请输入内容')"
                  v-model="CetForm_1.inputData_in.faultdescription"
                  onKeypress="javascript:if(event.keyCode == 32)event.returnValue = false;"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </CetForm>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
    <DeviceList v-bind="DeviceList" v-on="DeviceList.event"></DeviceList>
    <ReservePlanList
      v-bind="ReservePlanList"
      v-on="ReservePlanList.event"
    ></ReservePlanList>
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom.js";
import DeviceList from "./DeviceList";
import ReservePlanList from "./ReservePlanList";
export default {
  name: "createPlan",
  components: { DeviceList, ReservePlanList },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    userInfo() {
      var vm = this;
      return vm.$store.state.userInfo;
    }
  },
  data(vm) {
    return {
      CetDialog_add: {
        title: $T("新增维修工单"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_add_open_out,
          close_out: this.CetDialog_add_close_out
        },
        width: "550px",
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "backendInterface", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "createRepairWorkOrder",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        rules: {
          planname: [
            {
              required: true,
              message: $T("请输入名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          sourcetype: [
            {
              required: true,
              message: $T("请选择关联对象"),
              trigger: ["blur", "change"]
            }
          ],
          tasklevel: [
            {
              required: true,
              message: $T("请选择事件等级"),
              trigger: ["blur", "change"]
            }
          ],
          timeconsumeplan: [
            {
              required: true,
              message: $T("请输入预计耗时"),
              trigger: ["blur", "change"]
            }
          ],
          faultdescription: [
            {
              required: true,
              message: $T("请输入故障描述"),
              trigger: ["blur", "change"]
            }
          ],
          teamid: [
            {
              required: true,
              message: $T("请选择责任班组"),
              trigger: ["blur", "change"]
            }
          ],
          inspectteamid: [
            {
              required: true,
              message: $T("请选择运值确认班组"),
              trigger: ["blur", "change"]
            }
          ],
          deviceListName: [
            {
              required: true,
              message: $T("请选择维修目标"),
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_1_currentData_out,
          saveData_out: this.CetForm_1_saveData_out,
          finishData_out: this.CetForm_1_finishData_out,
          finishTrigger_out: this.CetForm_1_finishTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        style: {},
        placeholder: $T("请输入内容"),
        event: {}
      },
      // 月，设置范围0-99
      ElInputNumber_1: {
        min: 0,
        max: 99,
        step: 2,
        precision: 0,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      // 天，设置范围0-29
      ElInputNumber_2: {
        min: 0,
        max: 29,
        step: 2,
        precision: 0,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      // 小时，设置范围0-23
      ElInputNumber_3: {
        min: 0,
        max: 23,
        step: 2,
        precision: 0,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInputNumber_4: {
        // ...common.check_numberInt,
        min: 0.01,
        max: 999999999.99,
        step: 2,
        precision: 2,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      pickerOptions: common.pickerOptions_laterThanYesterd,
      pickerOptions11: common.pickerOptions_laterThanYesterd11,
      //关联
      ElSelect_relation: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_relation: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      //事件类型
      ElSelect_eventType: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_eventType_change_out
        }
      },
      ElOption_eventType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      //巡检路线，签到组
      ElSelect_tasklevel: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_tasklevel_change_out
        }
      },
      ElOption_tasklevel: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      //责任班组
      ElSelect_team: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_team: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // 场景列表
      ElSelect_scene: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_scene: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      //设备类型
      ElSelect_deviceclass: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_deviceclass_change_out
        }
      },
      ElOption_deviceclass: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // 维修对象弹窗
      DeviceList: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        tableData: null,
        event: {
          saveData_out: this.deviceList_saveData_out
        }
      },
      // 预案列表弹窗
      ReservePlanList: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        event: {
          saveData_out: this.reservePlanList_saveData_out
        }
      },
      showSet: false,
      deviceTypeObj: null,
      deviceclassList: []
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.CetForm_1.resetTrigger_in = this._.cloneDeep(val);
      this.showSet = false;

      this.ElOption_tasklevel.options_in =
        this.$store.state.enumerations.worksheettasklevel || [];
      // this.ElOption_deviceclass.options_in = this.$store.state.enumerations.deviceclass || [];

      Promise.all([
        new Promise((resolve, reject) => {
          this.getTeam_out(resolve);
        }),
        new Promise((resolve, reject) => {
          this.queryEventclassification_out(resolve);
        })
      ]).then(res => {
        this.CetDialog_add.openTrigger_in = this._.cloneDeep(val);
        this.$nextTick(() => {
          this.CetForm_1.inputData_in = {};
          this.DeviceList.tableData = [];

          // let data = this.$store.state.enumerations.workordersourcetype || [];
          // this.ElOption_relation.options_in = data;
          // let sourcetype = this._.get(data, "[0].id", null);
          // if (sourcetype) {
          //   this.$set(this.CetForm_1.inputData_in, "sourcetype", sourcetype);
          // }
          this.$set(
            this.CetForm_1.inputData_in,
            "sourceid",
            this.inputData_in.id
          );
          this.$set(
            this.CetForm_1.inputData_in,
            "sourcemodel",
            this.inputData_in.modelLabel
          );
          this.$set(this.CetForm_1.inputData_in, "sourcetype", 5); //手动创建
          const eventclassificationid = this._.get(
            this.ElOption_eventType.options_in,
            "[0].id",
            null
          );
          this.$set(
            this.CetForm_1.inputData_in,
            "eventclassificationid",
            eventclassificationid
          );
          this.queryFaultScenarios_out();
        });
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    }
  },

  methods: {
    CetDialog_add_open_out(val) {},
    CetDialog_add_close_out(val) {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.$set(this.CetForm_1.inputData_in, "worksheetstatus", 1);
      this.CetForm_1.saveTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_currentData_out(val) {},
    CetForm_1_saveData_out(val) {
      this.$emit("confirm_out", this._.cloneDeep(val));
    },
    CetForm_1_finishData_out(val) {},
    CetForm_1_finishTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
      this.$emit("confirm_out", val);
    },
    ElSelect_tasklevel_change_out(val) {},
    ElSelect_eventType_change_out(val) {
      this.queryFaultScenarios_out();
    },
    ElSelect_deviceclass_change_out(val) {
      this.queryFaultScenarios_out();
    },
    OpenxjDevice(val) {
      this.DeviceList.inputData_in = {};
      this.DeviceList.openTrigger_in = new Date().getTime();
    },
    OpenReservePlanList(val) {
      if (!this.CetForm_1.inputData_in.faultscenariosid) {
        this.$message.warning($T("请先选择故障信息"));
        return;
      }
      this.ReservePlanList.inputData_in = {
        scenariosId: this.CetForm_1.inputData_in.faultscenariosid
      };
      this.ReservePlanList.openTrigger_in = new Date().getTime();
    },
    init() {},
    // 巡检对象保存
    deviceList_saveData_out(val) {
      if (val && val.length) {
        this.$set(
          this.CetForm_1.inputData_in,
          "deviceListName",
          $T("已选择{0}个目标", val.length)
        );
        this.$refs.createPlanForm.$refs.cetForm.clearValidate("deviceListName");
      } else {
        this.$set(this.CetForm_1.inputData_in, "deviceListName", null);
      }

      this.CetForm_1.inputData_in.objectid = this._.get(val, "[0].id", null);
      this.CetForm_1.inputData_in.objectlabel = this._.get(
        val,
        "[0].modelLabel",
        null
      );
      this.DeviceList.tableData = this._.cloneDeep(val);
      this.queryDeviceClassificationNode_out();
    },
    //预案列表返回
    reservePlanList_saveData_out(val) {},
    // 获取班组列表信息
    getTeam_out(callback) {
      const _this = this;
      const params = {};
      customApi.queryInspectorTeamWithOutUserNoAuth(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          _this.ElOption_team.options_in = _this._.cloneDeep(data);
          callback && callback();
        }
      });
    },
    // 根据节点查询所关联的设备归类信息
    queryDeviceClassificationNode_out() {
      const _this = this;
      if (!this.CetForm_1.inputData_in.objectid) {
        return;
      }
      const params = {
        id: this.CetForm_1.inputData_in.objectid,
        modelLabel: this.CetForm_1.inputData_in.objectlabel
      };
      customApi.queryDeviceClassificationNode(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", {}) || {};
          _this.$set(_this.CetForm_1.inputData_in, "deviceTypeName", data.name);
          _this.$set(
            _this.CetForm_1.inputData_in,
            "deviceclassificationid",
            data.id
          );
          _this.queryFaultScenarios_out();
        }
      });
    },
    // 获取事件类型
    queryEventclassification_out(callback) {
      const _this = this;
      const params = {
        projectId: this.projectId
      };
      customApi.queryEventclassification(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          _this.ElOption_eventType.options_in = _this._.cloneDeep(data);
          callback && callback();
        }
      });
    },
    // 获取场景列表信息
    queryFaultScenarios_out() {
      const _this = this;
      const params = {
        deviceClassified:
          _this.CetForm_1.inputData_in.deviceclassificationid || 0,
        eventClassified:
          _this.CetForm_1.inputData_in.eventclassificationid || 0,
        keyWord: "",
        // page: { index: 0, limit: 100 },
        projectId: this.projectId
      };
      customApi.queryFaultScenarios(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          _this.ElOption_scene.options_in = _this._.cloneDeep(data);
          const faultscenariosid = this._.get(res, "data[0].id", null);
          this.$set(
            this.CetForm_1.inputData_in,
            "faultscenariosid",
            faultscenariosid
          );
        }
      });
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.custom-btn {
  position: relative;
  padding-left: 15px;
  height: 32px;
  line-height: 32px;
  border-radius: 4px;
  border: 1px solid;
  @include border_color(B1);
  box-sizing: border-box;
  @include font_color(T2);
  cursor: pointer;
  @include background_color(BG4);
}
.toConnect {
  position: absolute;
  right: 2px;
  z-index: 999;
  display: inline-block;
  width: 30px;
  height: 30px;
  cursor: pointer;
  @include font_color(ZS);
  text-align: center;
}
.faultscenariosid {
  :deep(.el-form-item__content) {
    display: flex;
  }
}
.advanced-setting {
  cursor: pointer;
}
.isDisable {
  @include background_color(BG4);
  cursor: no-drop;
}
</style>
