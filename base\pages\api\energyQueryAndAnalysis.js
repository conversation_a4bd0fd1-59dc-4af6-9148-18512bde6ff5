import fetch from "eem-utils/fetch";
const version = "v1";

// 获取分时方案
export function getTimeShareScheme(data, energyType) {
  return fetch({
    url: `/eem-service/${version}/schemeConfig/timeShareRelationship/node?energyType=${energyType}`,
    method: "POST",
    data: data
  });
}
// 查询折标能耗，传入父层级聚合周期，查询子层级聚合周期数据
export function getSynthesizeConsume(data) {
  return fetch({
    url: `/eem-service/${version}/energy/standard/consumption/child`,
    method: "POST",
    data: data
  });
}
// 获取项目能源类型
export function getProjectEnergy(projectId) {
  return fetch({
    url: `/eem-service/${version}/project/projectEnergy`,
    method: "GET",
    params: {
      projectId
    }
  });
}
// 获取同比环比数据
export function getEnergyTbhb(data) {
  return fetch({
    url: `/eem-service/${version}/energy/consumption/tbhb`,
    method: "POST",
    data: data
  });
}
// 聚合同比环比数据
export function getPolymerizationEnergyTbhb(data) {
  return fetch({
    url: `/eem-service/${version}/energy/energydata/time/tbhb`,
    method: "POST",
    data: data
  });
}
// 获取top5排名
export function getConsumptionTop5(data) {
  return fetch({
    url: `/eem-service/${version}/energy/consumption`,
    method: "POST",
    data: data
  });
}
// 查询子聚合周期分时能耗
export function getTimeChartData(data) {
  return fetch({
    url: `/eem-service/${version}/energy/timeshare/consumption/child/cycle`,
    method: "POST",
    data: data
  });
}
// 查询分时能耗
export function getTimeshareConsumption(data) {
  return fetch({
    url: `/eem-service/${version}/energy/timeshare/consumption`,
    method: "POST",
    data: data
  });
}

// 获取所有指标数据
export function queryEnergyefficiencyset(data) {
  return fetch({
    url: `/eem-service/${version}/energy/energyEfficiency/getEnergyefficiencyset`,
    method: "POST",
    data
  });
}

// 根据指标类型获取项目节点树
export function queryEnergyTree(data, energyefficiencysetId) {
  return fetch({
    url: `/eem-service/${version}/energy/nodeTree?energyefficiencysetId=${energyefficiencysetId}`,
    method: "POST",
    data
  });
}

// 通过能效配置获取能效数据
export function queryEnergyEfficiencyData(data) {
  return fetch({
    url: `/eem-service/${version}/energy/energyEfficiency/query`,
    method: "POST",
    data
  });
}

// 能效最值数据
export function queryMaxMinAvgEnergyEfficiency(data) {
  return fetch({
    url: `/eem-service/${version}/energy/energyEfficiency/getMaxMinAvgEnergyEfficiency`,
    method: "POST",
    data
  });
}

// 历史最优值数据
export function queryHistoryOptimumValue(data) {
  return fetch({
    url: `/eem-service/${version}/energy/energyEfficiency/getHistoryOptimumValue`,
    method: "POST",
    data
  });
}

// 能效对标数据
export function queryEnergyEfficiencyBenchmark(data) {
  return fetch({
    url: `/eem-service/${version}/energy/energyEfficiency/getEnergyEfficiencyBenchmark`,
    method: "POST",
    data
  });
}

// 能源流向
export function queryEnergyFlow(data) {
  return fetch({
    url: `/eem-service/${version}/energy/flow/query`,
    method: "POST",
    data
  });
}

// 能源损耗分析
export function queryEnergyLoss(data) {
  return fetch({
    url: `/eem-service/${version}/loss/analysis`,
    method: "POST",
    data
  });
}

// 能源损耗分析
export function queryLossLine(data, params) {
  return fetch({
    url: `/eem-service/${version}/loss/lineLoss`,
    method: "POST",
    data,
    params
  });
}

// 根据管网拓扑结构查询第一层节点
export function queryEnergyLossStartNodes(data) {
  return fetch({
    url: `/eem-service/${version}/loss/startNodes`,
    method: "POST",
    params: data
  });
}

// 获取节点对比数据
export function queryNodeCompare(data) {
  return fetch({
    url: `/eem-service/${version}/group/platform/query/ef/data`,
    method: "POST",
    data
  });
}
// 能耗对比数据
export function energyContrast(data) {
  return fetch({
    url: `/eem-service/${version}/energy/contrast`,
    method: "POST",
    data
  });
}
