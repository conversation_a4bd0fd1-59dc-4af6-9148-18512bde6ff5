<template>
  <div class="fullfilled eem-common flex-row">
    <el-aside width="315px" class="eem-aside flex-column">
      <customElSelect
        class="mbJ1"
        v-model="ElSelect_energyType.value"
        v-bind="ElSelect_energyType"
        v-on="ElSelect_energyType.event"
        :prefix_in="$T('能源类型')"
      >
        <ElOption
          v-for="item in ElOption_energyType.options_in"
          :key="item[ElOption_energyType.key]"
          :label="item[ElOption_energyType.label]"
          :value="item[ElOption_energyType.value]"
          :disabled="item[ElOption_energyType.disabled]"
        ></ElOption>
      </customElSelect>
      <CetTree
        class="flex-auto"
        ref="cetTree"
        :selectNode.sync="CetTree_1.selectNode"
        :checkedNodes.sync="CetTree_1.checkedNodes"
        :searchText_in.sync="CetTree_1.searchText_in"
        v-bind="CetTree_1"
        v-on="CetTree_1.event"
      ></CetTree>
    </el-aside>
    <div class="flex-auto eem-cont mlJ3 flex-column">
      <div class="mbJ3 flex-row">
        <el-tooltip
          effect="light"
          :content="currentNode && currentNode.name"
          placement="top-start"
        >
          <span class="fsH1 text-ellipsis lh28 mrJ2" style="flex: 1">
            {{ currentNode && currentNode.name }}
          </span>
        </el-tooltip>
        <div class="lh32">
          <el-checkbox v-model="maximum" @change="checksChange">
            最值
          </el-checkbox>
          <el-checkbox v-model="averageValue" @change="checksChange">
            平均值
          </el-checkbox>
          <customElSelect
            v-model="ElSelect_dateType.value"
            v-bind="ElSelect_dateType"
            v-on="ElSelect_dateType.event"
            class="mlJ1"
            prefix_in="查询周期"
          >
            <ElOption
              v-for="item in ElOption_dateType.options_in"
              :key="item[ElOption_dateType.key]"
              :label="item[ElOption_dateType.label]"
              :value="item[ElOption_dateType.value]"
              :disabled="item[ElOption_dateType.disabled]"
            ></ElOption>
          </customElSelect>
          <DatePointPicker
            class="mlJ1"
            style="width: 300px"
            v-bind="datePointPicker"
            v-on="datePointPicker.event"
            :value.sync="datePointPicker.value"
          />
          <CetButton
            class="mlJ1"
            :disable_in="
              CetChart_1.options.series[0].data.length ? false : true
            "
            v-bind="CetButton_export"
            v-on="CetButton_export.event"
          ></CetButton>
        </div>
      </div>
      <div class="flex-auto">
        <CetChart v-bind="CetChart_1"></CetChart>
      </div>
    </div>
  </div>
</template>

<script>
import commonApi from "@/api/custom.js";
import TREE_PARAMS from "@/store/treeParams.js";
import common from "eem-utils/common.js";
import DatePointPicker from "./datePointPicker.vue";
export default {
  name: "energyComparison",
  components: {
    DatePointPicker
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    token() {
      return this.$store.state.token;
    },
    energyTypeObj() {
      return this.ElOption_energyType.options_in.find(
        item => item.energytype === this.ElSelect_energyType.value
      );
    }
  },
  data() {
    return {
      maximum: false,
      averageValue: false,
      currentNode: null,
      ElSelect_energyType: {
        value: "",
        style: {},
        event: {
          change: this.ElSelect_energyType_change_out
        }
      },
      ElOption_energyType: {
        options_in: [],
        key: "id",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: true,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      ElSelect_dateType: {
        value: 13,
        size: "small",
        style: {
          width: "130px"
        },
        event: {
          change: this.ElSelect_dateType_change_out
        }
      },
      ElOption_dateType: {
        options_in: [
          {
            id: 17,
            text: "年"
          },
          {
            id: 14,
            text: "月"
          },
          {
            id: 13,
            text: "周"
          },
          {
            id: 12,
            text: "日"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetChart_1: {
        inputData_in: null,
        options: {
          toolbox: {
            top: 40,
            right: 30,
            feature: {
              saveAsImage: {
                title: $T("保存为图片")
              }
            }
          },
          legend: {},
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            }
          },
          grid: {
            top: "80",
            left: "16",
            right: "16",
            bottom: "8",
            containLabel: true
          },
          xAxis: [
            {
              type: "category",
              data: [],
              splitLine: {
                show: false
              }
            }
          ],
          yAxis: [
            {
              type: "value",
              name: "",
              min: 0,
              max: function (value) {
                if (value.max < 0) {
                  return 0;
                }
                return null;
              },
              splitLine: {
                show: false
              },
              nameTextStyle: {
                align: "left"
              }
            }
          ],
          series: [
            {
              name: "",
              type: "bar",
              data: [],
              barWidth: "16"
            }
          ]
        }
      },
      datePointPicker: {
        type: "dates",
        value: [],
        event: {
          change: this.datePointPicker_change
        }
      },
      CetButton_export: {
        visible_in: true,
        // disable_in: false,
        title: "导出",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      }
    };
  },
  watch: {},
  methods: {
    async init() {
      this.maximum = false;
      this.averageValue = false;
      this.ElSelect_dateType.value = 13;
      this.CetTree_1.searchText_in = "";
      this.setPointPickerType();
      this.initChart();
      await commonApi.getProjectEnergy(this.projectId).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          this.ElOption_energyType.options_in = data;
          let obj = data.find(i => i.energytype === 2);
          this.ElSelect_energyType.value = obj
            ? obj.energytype
            : this._.get(this.ElOption_energyType, "options_in[0].energytype");
        }
      });
      await this.getTreeData();
    },
    initChart() {
      this.CetChart_1.options.xAxis[0].data = [];
      this.CetChart_1.options.series[0].name = "";
      this.CetChart_1.options.series[0].data = [];
      this.CetChart_1.options.series[0].markPoint = null;
      this.CetChart_1.options.series[0].markLine = null;
      this.CetChart_1.options.yAxis[0].name = "";
      this.CetChart_1.options = this._.cloneDeep(this.CetChart_1.options);
    },
    setChart(data) {
      const currentdata = this._.get(data, "currentdata", []) || [];
      let xAxisData = [],
        seriesData = [],
        dateFormat = "yyyy-MM-DD";
      switch (this.ElSelect_dateType.value) {
        case 17:
          dateFormat = "yyyy";
          break;
        case 14:
          dateFormat = "yyyy-MM";
          break;
        case 13:
          dateFormat = "yyyy年第WW周(M.D-M.D)";
          break;
        default:
          dateFormat = "yyyy-MM-DD";
          break;
      }
      currentdata.forEach(item => {
        if (item.value || item.value === 0) {
          seriesData.push(item.value.toFixed(2));
        } else {
          seriesData.push(null);
        }
        if (this.ElSelect_dateType.value === 13) {
          const start = this.$moment(item.time);
          const month = start.month();
          const week = this.$moment(start).isoWeek();
          let year;
          if (week === 1 && month === 11) {
            year = start.add(1, "year").format("YYYY");
          } else {
            year = start.format("YYYY");
          }
          xAxisData.push(
            `${year}年第${week}周(${start.format("M.D")}-${start
              .add(6, "d")
              .format("M.D")})`
          );
        } else {
          xAxisData.push(this.$moment(item.time).format(dateFormat));
        }
      });
      this.CetChart_1.options.xAxis[0].data = xAxisData;
      this.CetChart_1.options.series[0].data = seriesData;
      this.CetChart_1.options.series[0].name = this.currentNode.name;
      const symbol = data.symbol || "--";
      this.CetChart_1.options.yAxis[0].name = `用${this.energyTypeObj.name}量（${symbol}）`;
      this.checksChange();
    },
    async ElSelect_energyType_change_out() {
      this.currentNode = null;
      this.initChart();
      await this.getTreeData();
    },
    async getTreeData() {
      var data = {
        energyType: this.ElSelect_energyType.value,
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.autoManagement,
        treeReturnEnable: true
      };
      const res = await commonApi.getNodeTreeSimple(data);
      if (res.code !== 0) return;
      this.CetTree_1.inputData_in = res.data;
      this.CetTree_1.selectNode = res.data[0];
    },
    CetTree_1_currentNode_out(val) {
      this.currentNode = val;
      this.getData();
    },
    ElSelect_dateType_change_out() {
      this.setPointPickerType();
      this.getData();
    },
    datePointPicker_change() {
      this.getData();
    },
    getParams() {
      return {
        aggregationCycle: this.ElSelect_dateType.value,
        energyType: this.ElSelect_energyType.value,
        logTimes: this.datePointPicker.value,
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel,
        projectId: this.projectId
      };
    },
    async getData() {
      if (!this.datePointPicker.value || !this.datePointPicker.value.length) {
        this.initChart();
        return;
      }
      const queryData = this.getParams();
      const res = await commonApi.energyContrast(queryData);
      if (res.code !== 0) {
        this.initChart();
        return;
      }
      this.setChart(res.data);
    },
    CetButton_export_statusTrigger_out() {
      const queryData = this.getParams();
      common.downExcel(
        "eem-service/v1/energy/contrast/export",
        queryData,
        this.token,
        this.projectId
      );
    },
    checksChange() {
      const currentTheme = localStorage.getItem("omega_theme");
      let bgColor = "";
      let borderColor = "";
      if (["dark", "blue"].includes(currentTheme)) {
        bgColor = "rgba(38, 41, 56, 0.7)";
        borderColor = "#414b6e";
      } else {
        bgColor = " rgba(255, 255, 255, 0.7)";
        borderColor = "#e0e4e8";
      }
      if (this.maximum) {
        this.CetChart_1.options.series[0].markPoint = {
          data: [
            {
              type: "max",
              symbolSize: 30,
              name: $T("最大值"),
              label: {
                formatter(params) {
                  return (
                    params.name +
                    ": " +
                    common.formatNumberWithPrecision(params.value, 2)
                  );
                },
                position: "top",
                padding: 8,
                backgroundColor: bgColor,
                borderType: "solid",
                borderWidth: 1,
                borderColor: borderColor,
                borderRadius: 4
              }
            },
            {
              type: "min",
              symbolSize: 30,
              name: $T("最小值"),
              label: {
                formatter(params) {
                  return (
                    params.name +
                    ": " +
                    common.formatNumberWithPrecision(params.value, 2)
                  );
                },
                position: "top",
                padding: 8,
                backgroundColor: bgColor,
                borderType: "solid",
                borderWidth: 1,
                borderColor: borderColor,
                borderRadius: 4
              }
            }
          ]
        };
      } else {
        this.CetChart_1.options.series[0].markPoint = null;
      }
      if (this.averageValue) {
        this.CetChart_1.options.series[0].markLine = {
          data: [
            {
              type: "average",
              name: "平均值",
              label: {
                show: true,
                position: "insideEndTop",
                formatter(val) {
                  return `${Number(val.value).toFixed(2)}(平均值)`;
                }
              }
            }
          ]
        };
      } else {
        this.CetChart_1.options.series[0].markLine = null;
      }
      this.CetChart_1.options = this._.cloneDeep(this.CetChart_1.options);
    },
    setPointPickerType() {
      const dataTypes = {
        17: "years",
        14: "months",
        13: "weeks",
        12: "dates"
      };
      this.datePointPicker.type = dataTypes[this.ElSelect_dateType.value];
      let value = [];
      // switch (this.ElSelect_dateType.value) {
      //   case 17:
      //     value = [this.$moment().startOf("year").valueOf()];
      //     break;
      //   case "months":
      //     value = [this.$moment().startOf("month").valueOf()];
      //     break;
      //   case "weeks":
      //     value = [this.$moment().startOf("week").day(1).valueOf()];
      //     break;
      //   default:
      //     value = [this.$moment().startOf("date").valueOf()];
      //     break;
      // }
      this.datePointPicker.value = value;
    }
  },
  activated() {
    this.init();
  }
};
</script>

<style lang="scss" scoped></style>
