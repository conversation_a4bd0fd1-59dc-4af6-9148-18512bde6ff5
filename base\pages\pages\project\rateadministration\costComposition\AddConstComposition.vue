<template>
  <!-- 1弹窗组件 -->
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <el-container style="height: 100%; padding: 0px 16px 11px 12px;">
      <el-header :height="showCheckboxGroup_1 ? '170px' : '120px'" style="padding: 0px; line-height: 58px;">
        <div class="clearfix">
          <div class="basic-box fl">
            <span class="basic-box-label" style="margin-top: 9px">能源类型</span>
            <ElSelect v-model="ElSelect_1.value" v-bind="ElSelect_1" v-on="ElSelect_1.event">
              <ElOption
                v-for="item in ElOption_1.options_in"
                :key="item[ElOption_1.key]"
                :label="item[ElOption_1.label]"
                :value="item[ElOption_1.value]"
                :disabled="item[ElOption_1.disabled]"
              ></ElOption>
            </ElSelect>
          </div>
        </div>
        <div style="display: flex;">
          <div class="basic-box" style="flex: 1;">
            <span class="basic-box-label" style="margin-top: 9px">费用类型</span>
            <ElSelect v-model="ElSelect_2.value" v-bind="ElSelect_2" v-on="ElSelect_2.event">
              <ElOption
                v-for="item in ElOption_2.options_in"
                :key="item[ElOption_2.key]"
                :label="item[ElOption_2.label]"
                :value="item[ElOption_2.value]"
                :disabled="item[ElOption_2.disabled]"
              ></ElOption>
            </ElSelect>
          </div>
          <div class="basic-box pl10 pr10" style="flex: 1; box-sizing: border-box;">
            <span class="basic-box-label" style="width: 130px; margin-top: 9px">成本项名称<span style="color: red;">*</span></span>
            <ElInput v-model="ElInput_1.value" v-bind="ElInput_1" v-on="ElInput_1.event"></ElInput>
          </div>
          <ElInput style="flex: 1;" v-model="ElInput_2.value" v-bind="ElInput_2" v-on="ElInput_2.event"></ElInput>
        </div>
        <div v-if="showCheckboxGroup_1">
          选择计算项：
          <ElCheckboxGroup v-model="ElCheckboxGroup_1.value" v-bind="ElCheckboxGroup_1" v-on="ElCheckboxGroup_1.event">
            <ElCheckbox
              v-for="item in ElCheckboxList_1.options_in"
              :key="item[ElCheckboxList_1.key]"
              :label="item[ElCheckboxList_1.label]"
              :disabled="item[ElCheckboxList_1.disabled]"
              >{{ item[ElCheckboxList_1.text] }}</ElCheckbox
            >
          </ElCheckboxGroup>
        </div>
      </el-header>
      <el-main class="content-bg" style="height: 100%; padding: 0px 16px 11px 12px;">
        <CetTable
          ref="CetTable"
          style="height: 400px;"
          :queryMode="CetTable_1.queryMode"
          :dataMode="CetTable_1.dataMode"
          :dataConfig="CetTable_1.dataConfig"
          :data.sync="CetTable_1.data"
          :queryNode_in="CetTable_1.queryNode_in"
          :queryTime_in="CetTable_1.queryTime_in"
          :queryTrigger_in="CetTable_1.queryTrigger_in"
          :exportTrigger_in="CetTable_1.exportTrigger_in"
          :deleteTrigger_in="CetTable_1.deleteTrigger_in"
          :refreshTrigger_in="CetTable_1.refreshTrigger_in"
          :addData_in="CetTable_1.addData_in"
          :editData_in="CetTable_1.editData_in"
          :dynamicInput="CetTable_1.dynamicInput"
          v-bind="CetTable_1.config"
          @record_out="CetTable_1_record_out"
          @editTrigger_out="CetTable_1_editTrigger_out"
          @detailTrigger_out="CetTable_1_detailTrigger_out"
          @outputData_out="CetTable_1_outputData_out"
        />
      </el-main>
    </el-container>
    <span slot="footer">
      <CetButton v-bind="CetButton_confirm" v-on="CetButton_confirm.event"></CetButton>
      <CetButton v-bind="CetButton_cancel" v-on="CetButton_cancel.event"></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import CetTable from "../cet-table/index.vue";
import { httping } from "@omega/http";
export default {
  name: "AddConstComposition",
  components: {
    CetTable
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    basicfeerateFlag: {
      type: Boolean
    },
    powertarifffeerateFlag: {
      type: Boolean
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },

  data() {
    return {
      tableData: [],
      currentFeescheme: null,
      showCheckboxGroup_1: false,
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "确认",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElSelect_1: {
        value: 2,
        style: {
          // width:"200px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [
          {
            id: 2,
            text: "电能"
          },
          {
            id: 3,
            text: "水"
          },
          {
            id: 15,
            text: "天然气"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: null,
        style: {
          // width:"200px"
        },
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElInput_1: {
        value: "",
        placeholder: "请输入内容",
        style: {
          // width:"200px"
        },
        event: {
          change: this.ElInput_1_change_out,
          input: this.ElInput_1_input_out
        }
      },
      ElInput_2: {
        value: "",
        placeholder: "请输入内容",
        style: {
          // width:"200px"
        },
        event: {
          change: this.ElInput_2_change_out,
          input: this.ElInput_2_input_out
        }
      },
      ElCheckboxGroup_1: {
        value: [],
        style: {
          display: "inline-block"
          // width:"300px"
        },
        disabled: true,
        event: {
          change: this.ElCheckboxGroup_1_change_out
        }
      },
      ElCheckboxList_1: {
        options_in: [
          {
            id: 1,
            text: "能耗"
          },
          {
            id: 2,
            text: "损耗"
          },
          {
            id: 3,
            text: "分摊"
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      CetTable_1: {
        queryMode: "trigger",
        dataMode: "component",
        dataConfig: {
          queryUrl: "",
          exportUrl: "",
          deleteUrl: "",
          modelLabel: "",
          type: "POST",
          dataIndex: [],
          modelList: []
        },
        data: [],
        queryNode_in: {},
        queryTime_in: {
          timeType: 1,
          time: null
        },
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        dynamicInput: {},
        config: {
          showEdit: false,
          showDelete: false,
          showDetail: false,
          showCheckBox: false,
          showIndex: false,
          showPagination: false,
          columns: [
            {
              title: "费率方案",
              key: "name",
              width: 100
            },
            {
              title: "费用类型",
              key: "feeratetype$text",
              width: 100
            }
          ],
          style: ""
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.getEnergytype();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {
      var vm = this;
    },
    inputData_in(val) {
      this.ElSelect_2.value = null;
      this.ElInput_1.value = "";
      this.ElInput_2.value = "";
      this.ElCheckboxGroup_1.value = [];
      this.showCheckboxGroup_1 = false;
      if (val.edit) {
        // 编辑
        this.ElSelect_1.disabled = true;
        this.ElSelect_2.disabled = true;
        this.ElInput_1.value = val.constName;
        if (val.feeratetype == 2 || val.feeratetype == 4) {
          this.showCheckboxGroup_1 = true;
          this.$nextTick(() => {
            this.ElCheckboxGroup_1.value = JSON.parse(val.costcheckitem).calculateitem;
          });
        }
      } else {
        this.ElSelect_1.disabled = false;
        this.ElSelect_2.disabled = false;
      }
    }
  },

  methods: {
    //获取能源类型
    getEnergytype() {
      var vm = this;
      vm.ElOption_1.options_in = [];
      httping({
        url:
          "/eem-service/v1/project/projectEnergy?projectId=" + this.projectId,
        method: "GET"
      }).then(function (response) {
        if (response.code === 0 && response.data && response.data.length > 0) {
          var selectData = response.data.map(item => {
            return {
              id: item.energytype,
              text: item.name
            };
          });
          // var res = {};
          // res.id = 0;
          // res.text = "全部";
          // selectData.unshift(res);
          vm.ElOption_1.options_in = vm._.cloneDeep(selectData);
          if (vm.inputData_in.edit) {
            // 编辑
            vm.ElSelect_1.value = vm.inputData_in.energytype;
            vm.ElSelect_1_change_out(vm.inputData_in.energytype);
          } else {
            vm.ElSelect_1.value = selectData[0].id;
            vm.ElSelect_1_change_out(selectData[0].id);
          }
        }
      });
    },
    // 获取费率列表
    getFeerateList() {
      if (!this.ElSelect_1.value || !this.ElSelect_2.value) {
        return;
      }
      // 成本项名称 ElInput_1.value
      var vm = this;
      if (vm.AjaxFlag) {
        return;
      }
      vm.AjaxFlag = true;
      vm.CetTable_1.data = [];
      httping({
        url: `/eem-service/v1/schemeConfig/feeScheme/${this.projectId}?energyType=${this.ElSelect_1.value}`,
        method: "GET"
      }).then(function (response) {
        vm.AjaxFlag = false;
        if (response.code === 0 && response.data && response.data.length > 0) {
          response.data.forEach(item => {
            item.feeratetype$text = vm.ElOption_2.options_in.filter(item => item.id == vm.ElSelect_2.value)[0].text;
          });
          vm.tableData = response.data;
          vm.ElInput_2.value = "";
          vm.ElInput_2_input_out();
          if (vm.inputData_in && vm.inputData_in.edit) {
            var data = response.data.filter(item => item.id == vm.inputData_in.feescheme_id)[0];
            vm.$nextTick(() => {
              vm.$refs.CetTable.setCurrentRow(data);
            });
          }
        }
      });
    },
    filterTab() {
      var _this = this;
      if (_this.tableData.length == 0 || !this.ElSelect_2.value) {
        _this.CetTable_1.data = [];
        return;
      }
      var tableData = _this.tableData.filter(item => {
        return item.feeratetype == _this.ElSelect_2.value;
      });

      if (_this.ElInput_2.value) {
        tableData = tableData.filter(item => {
          return item.name.indexOf(_this.ElInput_2.value) != -1;
        });
      }
      _this.CetTable_1.data = tableData;
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      var obj = {
        calculateitem: this.ElCheckboxGroup_1.value
      };
      // 必须选择一项计费项
      if (this.showCheckboxGroup_1) {
        if (this.ElCheckboxGroup_1.value.length == 0) {
          this.$message({
            message: "至少选择一项计算项",
            type: "warning"
          });
          return;
        }
      }
      if (!this.currentFeescheme) {
        this.$message({
          message: "请选择费率方案",
          type: "warning"
        });
        return;
      } else if (!this.ElInput_1.value) {
        this.$message({
          message: "请输入成本项名称",
          type: "warning"
        });
        return;
      } else if (this.ElInput_1.value.length > 20) {
        this.$message({
          message: "成本项名称长度在 1 到 20 个字符",
          type: "warning"
        });
        return false;
      } else if (!/^((?![`~!@$%^&*()+=\[\]{}\\|;:\'"<,>.?\/]).)*$/.test(this.ElInput_1.value)) {
        this.$message({
          message: "成本项名称不能输入特殊字符",
          type: "warning"
        });
        return false;
      }
      if (this.inputData_in.edit) {
        // 编辑

        this.$emit("finishData_out", {
          edit: true,
          energytype: this.ElSelect_1.value,
          feeratetype: this.ElSelect_2.value,
          feescheme_id: this.currentFeescheme.id,
          name: this.currentFeescheme.name,
          costcheckitem: JSON.stringify(obj),
          costcheckitem$text: this.ElCheckboxGroup_1.value.join("、").replace(1, "能耗").replace(2, "损耗").replace(3, "分摊") || "--",
          constName: this.ElInput_1.value
        });
      } else {
        // 新增
        this.$emit("finishData_out", {
          edit: false,
          energytype: this.ElSelect_1.value,
          feeratetype: this.ElSelect_2.value,
          feescheme_id: this.currentFeescheme.id,
          name: this.currentFeescheme.name,
          costcheckitem: JSON.stringify(obj),
          costcheckitem$text: this.ElCheckboxGroup_1.value.join("、").replace(1, "能耗").replace(2, "损耗").replace(3, "分摊") || "--",
          constName: this.ElInput_1.value
        });
      }
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    ElCheckboxGroup_1_change_out(val) {},
    CetDialog_1_open_out(val) {},
    CetDialog_1_close_out(val) {},
    ElInput_1_change_out(val) {},
    ElInput_1_input_out(val) {},
    ElInput_2_change_out(val) {},
    ElInput_2_input_out(val) {
      this.ElInput_2.value = val;
      this.filterTab();
    },
    ElSelect_1_change_out(val) {
      if (!val) {
        return;
      }
      if (val == 2) {
        //电
        if (this.basicfeerateFlag && this.powertarifffeerateFlag) {
          this.ElOption_2.options_in = [
            // {
            //   id: 1,
            //   text: "基本电费",
            //   propertyLabel: "basicfeerate"
            // },
            {
              id: 2,
              text: "电度电费",
              propertyLabel: "electricitychargerate"
            },
            // {
            //   id: 3,
            //   text: "力调电费",
            //   propertyLabel: "powertarifffeerate"
            // },
            {
              id: 4,
              text: "附加费",
              propertyLabel: "surchargefeerate"
            }
          ];
          this.ElSelect_2.value = 2;
        } else if (this.basicfeerateFlag && !this.powertarifffeerateFlag) {
          this.ElOption_2.options_in = [
            // {
            //   id: 1,
            //   text: "基本电费",
            //   propertyLabel: "basicfeerate"
            // },
            {
              id: 2,
              text: "电度电费",
              propertyLabel: "electricitychargerate"
            },
            {
              id: 3,
              text: "力调电费",
              propertyLabel: "powertarifffeerate"
            },
            {
              id: 4,
              text: "附加费",
              propertyLabel: "surchargefeerate"
            }
          ];
          this.ElSelect_2.value = 2;
        } else if (this.powertarifffeerateFlag && !this.basicfeerateFlag) {
          this.ElOption_2.options_in = [
            {
              id: 1,
              text: "基本电费",
              propertyLabel: "basicfeerate"
            },
            {
              id: 2,
              text: "电度电费",
              propertyLabel: "electricitychargerate"
            },
            // {
            //   id: 3,
            //   text: "力调电费",
            //   propertyLabel: "powertarifffeerate"
            // },
            {
              id: 4,
              text: "附加费",
              propertyLabel: "surchargefeerate"
            }
          ];
          this.ElSelect_2.value = 1;
        } else {
          this.ElOption_2.options_in = [
            {
              id: 1,
              text: "基本电费",
              propertyLabel: "basicfeerate"
            },
            {
              id: 2,
              text: "电度电费",
              propertyLabel: "electricitychargerate"
            },
            {
              id: 3,
              text: "力调电费",
              propertyLabel: "powertarifffeerate"
            },
            {
              id: 4,
              text: "附加费",
              propertyLabel: "surchargefeerate"
            }
          ];
          this.ElSelect_2.value = 1;
        }
      } else if (val == 3) {
        //水
        this.ElOption_2.options_in = [
          {
            id: 2,
            text: "水费",
            propertyLabel: ""
          },
          {
            id: 4,
            text: "附加费",
            propertyLabel: "surchargefeerate"
          }
        ];
        this.ElSelect_2.value = 2;
      } else if (val == 15) {
        //天然气
        this.ElOption_2.options_in = [
          {
            id: 2,
            text: "天然气费",
            propertyLabel: ""
          },
          {
            id: 4,
            text: "附加费",
            propertyLabel: "surchargefeerate"
          }
        ];
        this.ElSelect_2.value = 2;
      } else {
        var str = this.ElOption_1.options_in.find(item => item.id === val).text;
        //天然气
        this.ElOption_2.options_in = [
          {
            id: 2,
            text: `${str}费`,
            propertyLabel: ""
          },
          {
            id: 4,
            text: "附加费",
            propertyLabel: "surchargefeerate"
          }
        ];
        this.ElSelect_2.value = 2;
      }
      if (this.inputData_in.edit) {
        // 编辑
        if (this.inputData_in.feeratetype == 1 && this.powertarifffeerateFlag) {
          this.ElOption_2.options_in = [
            {
              id: 1,
              text: "基本电费",
              propertyLabel: "basicfeerate"
            },
            {
              id: 2,
              text: "电度电费",
              propertyLabel: "electricitychargerate"
            },
            // {
            //   id: 3,
            //   text: "力调电费",
            //   propertyLabel: "powertarifffeerate"
            // },
            {
              id: 4,
              text: "附加费",
              propertyLabel: "surchargefeerate"
            }
          ];
        } else if (this.inputData_in.feeratetype == 3 && this.basicfeerateFlag) {
          this.ElOption_2.options_in = [
            // {
            //   id: 1,
            //   text: "基本电费",
            //   propertyLabel: "basicfeerate"
            // },
            {
              id: 2,
              text: "电度电费",
              propertyLabel: "electricitychargerate"
            },
            {
              id: 3,
              text: "力调电费",
              propertyLabel: "powertarifffeerate"
            },
            {
              id: 4,
              text: "附加费",
              propertyLabel: "surchargefeerate"
            }
          ];
        } else if ((this.inputData_in.feeratetype == 1 && !this.powertarifffeerateFlag) || (this.inputData_in.feeratetype == 3 && !this.basicfeerateFlag)) {
          this.ElOption_2.options_in = [
            {
              id: 1,
              text: "基本电费",
              propertyLabel: "basicfeerate"
            },
            {
              id: 2,
              text: "电度电费",
              propertyLabel: "electricitychargerate"
            },
            {
              id: 3,
              text: "力调电费",
              propertyLabel: "powertarifffeerate"
            },
            {
              id: 4,
              text: "附加费",
              propertyLabel: "surchargefeerate"
            }
          ];
        }
        this.ElSelect_2.value = this.inputData_in.feeratetype;
      }
      this.ElSelect_2_change_out(this.ElSelect_2.value);
    },
    ElSelect_2_change_out(val) {
      if (!val) {
        return;
      }
      this.ElSelect_2.dropItemText = this.ElOption_2.options_in.filter(item => item.id == val)[0].text;
      if (val == 2 || val == 4) {
        this.showCheckboxGroup_1 = true;
      } else {
        if (!this.inputData_in.edit) {
          this.ElCheckboxGroup_1.value = [1];
        }
        this.showCheckboxGroup_1 = false;
      }
      this.getFeerateList();
    },
    CetTable_1_detailTrigger_out(val) {},
    CetTable_1_editTrigger_out(val) {},
    CetTable_1_outputData_out(val) {},
    CetTable_1_record_out(val) {
      if (val.id != -1) {
        this.currentFeescheme = val;
        // 选择阶梯费率分摊置灰
        if ((val.feeratetype == 2 && val.feeratesubtype == 3) || val.feeratetype == 4) {
          if (!this.inputData_in.edit) {
            this.ElCheckboxGroup_1.value = [1];
          }
          this.ElCheckboxGroup_1.disabled = true;
          this.ElCheckboxList_1.options_in = [
            {
              id: 1,
              text: "能耗",
              disabled: true
            },
            {
              id: 2,
              text: "损耗",
              disabled: true
            },
            {
              id: 3,
              text: "分摊",
              disabled: true
            }
          ];
        } else {
          if (!this.inputData_in.edit) {
            this.ElCheckboxGroup_1.value = [];
          }
          this.ElCheckboxGroup_1.disabled = false;
          this.ElCheckboxList_1.options_in = [
            {
              id: 1,
              text: "能耗",
              disabled: false
            },
            {
              id: 2,
              text: "损耗",
              disabled: false
            },
            {
              id: 3,
              text: "分摊",
              disabled: false
            }
          ];
        }
        // 修改时保持选择当前方案
        if (this.inputData_in && this.inputData_in.edit) {
          var data = this.tableData.filter(item => item.id == this.inputData_in.feescheme_id)[0];
          this.$nextTick(() => {
            this.$refs.CetTable.setCurrentRow(data);
          });
        }
      } else {
        this.currentFeescheme = null;
      }
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
</style>
