<template>
  <div>
    <CetDialog v-bind="CetDialog_add" v-on="CetDialog_add.event" class="min">
      <CetForm
        class="eem-cont-c1"
        :data.sync="CetForm_add.data"
        v-bind="CetForm_add"
        v-on="CetForm_add.event"
      >
        <el-form-item label="预案名称" prop="name">
          <ElInput
            v-model.trim="CetForm_add.data.name"
            v-bind="ElInput_1"
            v-on="ElInput_1.event"
          ></ElInput>
        </el-form-item>
        <el-form-item label="操作步骤" prop="solution">
          <ElInput
            v-model="CetForm_add.data.solution"
            v-bind="ElInput_2"
            v-on="ElInput_2.event"
          ></ElInput>
        </el-form-item>
      </CetForm>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import common from "eem-utils/common";
import custom from "@/api/custom";
export default {
  name: "addReservePlan",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    scenarios_in: {
      type: Number
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.localStorage) {
          return false;
        } else {
          var storage = window.localStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },
  data() {
    return {
      // add弹窗组件
      CetDialog_add: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_add_open_out,
          close_out: this.CetDialog_add_close_out
        },
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "保存",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_add: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: "预案名称不能为空",
              trigger: ["blur", "change"]
            },
            common.check_name
          ],
          solution: [
            {
              required: true,
              message: "操作步骤不能为空",
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_add_currentData_out,
          saveData_out: this.CetForm_add_saveData_out,
          finishData_out: this.CetForm_add_finishData_out,
          finishTrigger_out: this.CetForm_add_finishTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: "请输入预案名称",
        style: {
          width: "100%"
        },
        maxlength: 20,
        event: {}
      },
      ElInput_2: {
        value: "",
        placeholder: "请输入详细操作步骤（必填）",
        style: {
          width: "100%"
        },
        type: "textarea",
        rows: "4",
        event: {}
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    },
    inputData_in(val) {
      if (val && val.id) {
        this.CetDialog_add.title = "编辑预案";
        this.CetForm_add.data = val;
        this.CetForm_add.data.solution = this.CetForm_add.data.solution.replace(
          /↵/g,
          "\r\n"
        );
      } else {
        this.CetDialog_add.title = "新增预案";
        this.CetForm_add.data = {};
      }
      this.CetForm_add.resetTrigger_in = new Date().getTime();
    }
  },

  methods: {
    CetDialog_add_open_out(val) {},
    CetDialog_add_close_out(val) {},
    CetForm_add_currentData_out(val) {},
    CetForm_add_saveData_out(val) {
      var data = [
        {
          id: val.id,
          name: val.name,
          solution: val.solution,
          adoptnumber: val.adoptnumber,
          projectid: this.projectId
        }
      ];
      custom.addEventPlan(data, this.scenarios_in).then(response => {
        if (response.code === 0) {
          this.$message({
            message: "保存成功！",
            type: "success"
          });
          this.$emit("confirm_out");
          this.CetDialog_add.closeTrigger_in = new Date().getTime();
        }
      });
    },
    CetForm_add_finishData_out(val) {},
    CetForm_add_finishTrigger_out(val) {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_add.localSaveTrigger_in = this._.cloneDeep(val);
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped></style>
