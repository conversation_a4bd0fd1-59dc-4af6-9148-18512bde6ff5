<template>
  <div class="efficiencyAssessmentTable">
    <el-table
      :data="tableData"
      :border="false"
      :cell-style="cellStyle"
      :span-method="arraySpanMethod"
    >
      <template v-for="(item, index) in ColumnList">
        <el-table-column :key="index" v-bind="item"></el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script>
import common from "eem-utils/common";
export default {
  name: "deviceTypeTable",
  components: {},
  props: {
    tableData: {
      type: Array
    },
    type: {
      type: String,
      default: "deviceType"
    }
  },
  data() {
    return {
      ColumnList: [
        {
          prop: "name",
          label: $T("分析对象"),
          align: "left",
          showOverflowTooltip: true
        },
        {
          prop: "typeName",
          label: $T("设备型号"),
          align: "left",
          showOverflowTooltip: true,
          width: 200
        },
        {
          prop: "unitEfficiency",
          label: $T("机组效率(%)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 80
        },
        {
          prop: "gasTransmissionVolume",
          label: $T("日输气量(10⁴m³)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 80
        },
        {
          prop: "dischargeCoefficient",
          label: $T("排量系数"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 80
        },
        {
          prop: "loadRate",
          label: $T("电机负载率(%)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 100
        }
      ]
    };
  },
  watch: {
    type: {
      immediate: true,
      handler(val) {
        this.ColumnList[1].label =
          val === "electricMachinery"
            ? $T("电机负载率x范围(%)")
            : $T("设备型号");
      }
    }
  },
  methods: {
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (column.property === "name" || column.property === "typeName") return;
      if (row.name === "平均值") return "color:#29B061;font-weight:bold;";
      else {
        return "color:#242424";
      }
    },

    /**
     * 合并单元格
     */
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      const table = _.cloneDeep(this.tableData);
      if (column.property === "typeName") {
        let rowspan = 1;
        const currentValue = row[column.property];
        const list = table
          .filter((item, index) => index < rowIndex)
          ?.map(item => item[column.property]);
        if (list.includes(currentValue)) return [0, 0];
        for (let i = rowIndex + 1; i < table.length; i++) {
          if (table[i][column.property] === currentValue) {
            rowspan++;
          } else {
            break;
          }
        }
        if (rowspan > 1) {
          return [rowspan, 1];
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.efficiencyAssessmentTable {
  :deep(.el-table__header-wrapper thead th) {
    background-color: #f8fafb !important;
    color: #798492;
  }
  :deep(.el-table__body-wrapper .el-table__row),
  :deep(.el-table__body-wrapper .el-table__body tr:hover > td),
  :deep(.el-table__empty-block) {
    background-color: #ffffff !important;
  }
  :deep(.el-table__empty-text) {
    color: #798492;
  }
  :deep(.el-table__cell) {
    border-color: #f3f5f7 !important;
    color: #798492;
  }
  :deep(.el-table::before) {
    background-color: #f3f5f7 !important;
  }
}
</style>
