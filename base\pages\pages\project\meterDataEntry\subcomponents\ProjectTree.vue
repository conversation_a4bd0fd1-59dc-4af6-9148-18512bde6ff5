<template>
  <div class="treeBox">
    <CetGiantTree
      class="CetGiantTree"
      v-bind="CetGiantTree_1"
      v-on="CetGiantTree_1.event"
    ></CetGiantTree>
  </div>
</template>

<script>
//项目级逻辑组件
import customApi from "@/api/custom";
export default {
  props: {
    refreshTrigger: {
      type: Number
    }
  },

  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    projectTenantId() {
      return this.$store.state.projectTenantId;
    }
  },

  watch: {
    refreshTrigger() {
      this.CetGiantTree_1.inputData_in = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.getTreeData();
    }
  },
  data() {
    return {
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "id"
            },
            key: {
              name: "text"
            }
          }
        },
        event: {
          currentNode_out: this.CetTree_1_currentNode_out //选中单行输出
        }
      }
    };
  },
  methods: {
    getTreeData() {
      let queryData = {
        loadDevice: true,
        nodeId: 0,
        nodeType: 0,
        async: false,
        tenantId: this.projectTenantId
      };
      customApi.getPecCoreMeterTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_1.inputData_in = data;
          this.$emit("project_node", data);
          // this.CetGiantTree_1.selectNode = this._.get(data, "[0].children[0].children[0]");
        }
      });
    },
    CetTree_1_currentNode_out(val) {
      this.$emit("currentNode_out", val);
    }
  }
};
</script>

<style lang="scss" scoped>
.treeBox {
  height: 100%;
}
</style>
