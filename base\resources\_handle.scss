// 该文件已再 vue.config.js 全局性的引入，使用时无需额外引入
$theme-map: null;
//遍历主题map
@mixin themeify {
  @each $theme-name, $theme-map in $themes {
    //!global 把局部变量强升为全局变量
    $theme-map: $theme-map !global;
    //判断html的data-theme的属性值  #{}是sass的插值表达式
    //& sass嵌套里的父容器标识   @content是混合器插槽，像vue的slot
    [data-theme="#{$theme-name}"] & {
      @content;
    }
  }
}

//声明一个根据Key获取颜色的function
@function themed($key) {
  @if type-of($key) != "number" {
    $key: map-get($theme-map, $key);
  }
  @return $key;
}

//获取背景颜色
@mixin background_color($color, $important: null) {
  @include themeify {
    background-color: themed($color) $important;
  }
}

//获取背景颜色
@mixin background_image($color, $important: null) {
  @include themeify {
    background-image: themed($color) $important;
  }
}

//获取背景
@mixin background($background, $important: null) {
  @include themeify {
    background: themed($background) $important;
  }
}

//获取字体颜色
@mixin font_color($color, $important: null) {
  @include themeify {
    color: themed($color) $important;
  }
}

//获取边框颜色
@mixin border_color($color, $important: null) {
  @include themeify {
    border-color: themed($color) $important;
  }
}
//获取圆角
@mixin border_radius($key, $important: null) {
  @include themeify {
    border-radius: mh-get($key) $important;
  }
}
//获取某一个方向边框颜色
@mixin border_direction_color($color, $direction, $important: null) {
  @include themeify {
    border-#{$direction}-color: themed($color) $important;
  }
}

//获取svgicon的fill颜色
@mixin fill_color($color, $important: null) {
  @include themeify {
    fill: themed($color) $important;
  }
}

@mixin outline_color($color, $important: null) {
  @include themeify {
    outline-color: themed($color) $important;
  }
}
//获取圆角
@mixin border_radius($key, $important: null) {
  @include themeify {
    border-radius: mh-get($key) $important;
  }
}
//获取透明度
@mixin opacity($key, $important: null) {
  @include themeify {
    opacity: mh-get($key) $important;
  }
}
//获取加粗
@mixin font_weight($key, $important: null) {
  @include themeify {
    font-weight: mh-get($key) $important;
  }
}

@function mh-get($key) {
  @if type-of($key) != "number" {
    $key: map-get($mh-vars, $key);
  }
  @return $key;
}

@function mh-list($keys) {
  $list: ();

  @each $key in $keys {
    $list: append($list, mh-get($key));
  }
  @return $list;
}

@mixin font_size($key, $important: null) {
  font-size: mh-get($key) $important;
}

@mixin line_height($key, $important: null) {
  line-height: mh-get($key) $important;
}

@mixin padding($keys, $important: null) {
  padding: mh-list($keys) $important;
}
@mixin padding_left($key, $important: null) {
  padding-left: mh-get($key) $important;
}
@mixin padding_top($key, $important: null) {
  padding-top: mh-get($key) $important;
}
@mixin padding_right($key, $important: null) {
  padding-right: mh-get($key) $important;
}
@mixin padding_bottom($key, $important: null) {
  padding-bottom: mh-get($key) $important;
}

@mixin margin($keys, $important: null) {
  margin: mh-list($keys) $important;
}
@mixin margin_left($key, $important: null) {
  margin-left: mh-get($key) $important;
}
@mixin margin_top($key, $important: null) {
  margin-top: mh-get($key) $important;
}
@mixin margin_right($key, $important: null) {
  margin-right: mh-get($key) $important;
}
@mixin margin_bottom($key, $important: null) {
  margin-bottom: mh-get($key) $important;
}

@mixin box_shadow($key, $important: null) {
  box-shadow: mh-get($key) $important;
}

%J1 {
  & > *:not(:last-child) {
    margin-right: mh-get(J1);
  }
}
%J2 {
  & > *:not(:last-child) {
    margin-right: mh-get(J2);
  }
}
%J3 {
  & > *:not(:last-child) {
    margin-right: mh-get(J3);
  }
}
%J4 {
  & > *:not(:last-child) {
    margin-right: mh-get(J4);
  }
}

%hover {
  cursor: pointer;
  &:hover {
    @include background_color(BG2);
  }
  &:active {
    @include background_color(BG3);
  }
}

//获取加粗
@mixin font_weight($key, $important: null) {
  @include themeify {
    font-weight: mh-get($key) $important;
  }
}
