﻿<template>
  <div class="page eem-common">
    <el-container class="fullheight">
      <el-aside width="315px" class="eem-aside flex-column">
        <div class="basic-box mbJ1" style="display: flex">
          <customElSelect
            v-model="ElSelect_type.value"
            v-bind="ElSelect_type"
            v-on="ElSelect_type.event"
            :prefix_in="$T('查询对象')"
          >
            <ElOption
              v-for="item in ElOption_type.options_in"
              :key="item[ElOption_type.key]"
              :label="item[ElOption_type.label]"
              :value="item[ElOption_type.value]"
              :disabled="item[ElOption_type.disabled]"
            ></ElOption>
          </customElSelect>
        </div>
        <CetGiantTree
          class="flex-auto"
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
        ></CetGiantTree>
      </el-aside>
      <el-container class="padding0 mlJ3 fullheight">
        <el-header height="auto" class="eem-cont">
          <customElSelect
            class="fr mlJ1"
            v-model="ElSelect_1.value"
            v-bind="ElSelect_1"
            v-on="ElSelect_1.event"
            :prefix_in="$T('能源类型')"
          >
            <ElOption
              v-for="item in ElOption_1.options_in"
              :key="item[ElOption_1.key]"
              :label="item[ElOption_1.label]"
              :value="item[ElOption_1.value]"
              :disabled="item[ElOption_1.disabled]"
            ></ElOption>
          </customElSelect>
          <CetButton
            class="fr custom—square"
            v-bind="CetButton_8"
            v-on="CetButton_8.event"
          ></CetButton>
          <CustomElDatePicker
            class="fr mlJ mrJ"
            :prefix_in="$T('选择年份')"
            v-model="value3"
            type="year"
            style="width: 200px"
            :placeholder="$T('选择年份')"
            @change="yearChange"
            :picker-options="pickerOptions0"
            :clearable="false"
          />
          <CetButton
            class="fr custom—square"
            v-bind="CetButton_7"
            v-on="CetButton_7.event"
          ></CetButton>
        </el-header>
        <el-main class="padding0 mtJ3">
          <div class="tip1 mbJ3">
            <div class="bg1 eem-container" style="height: 100%">
              <div class="mbJ3 common-title-H1">{{ $T("分摊成本") }}</div>
              <CetChart
                style="height: calc(100% - 50px)"
                :inputData_in="CetChart_1.inputData_in"
                v-bind="CetChart_1.config"
              ></CetChart>
            </div>
          </div>
          <div class="tip2">
            <div style="height: 100%" class="eem-container">
              <div class="common-title-H1 mbJ3">
                {{
                  ElSelect_type.value == 1 ? $T("被分摊对象") : $T("分摊对象")
                }}
              </div>
              <div class="mbJ1">
                <el-radio-group
                  v-model="tabIndex"
                  size="small"
                  @change="handleClick"
                >
                  <el-radio-button
                    :label="item.index"
                    v-for="item in tabs"
                    :key="item.index"
                  >
                    {{ item.label }}
                  </el-radio-button>
                </el-radio-group>
              </div>
              <CetTable
                :data.sync="CetTable_1.data"
                :dynamicInput.sync="CetTable_1.dynamicInput"
                v-bind="CetTable_1"
                v-on="CetTable_1.event"
                style="height: calc(100% - 106px)"
              >
                <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
                <ElTableColumn
                  v-for="(item, index) in ElTableColumnArr"
                  :key="index"
                  v-bind="item"
                ></ElTableColumn>
              </CetTable>
            </div>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import TREE_PARAMS from "@/store/treeParams.js";
import { httping } from "@omega/http";

export default {
  name: "Equalsharecomparison",
  components: {},

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data(vm) {
    return {
      pickerOptions0: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6; //如果没有后面的-8.64e6就是不可以选择今天的
        }
      },
      tabIndex: 0, // tabs当前选中的下标
      tabs: [], // 分摊和被分摊对象的列表
      projectId: 0, // 项目id
      checkedNodes: [],
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out //勾选节点输出
        }
      },
      value3: String(vm.$moment().year()),
      activeName: "first",
      props: {
        label: "name",
        children: "zones"
      },
      count: 1,
      ElSelect_1: {
        value: 0,
        style: { width: "200px" },
        event: {
          change: this.energyChange
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_type: {
        value: 2,
        style: {},
        event: {
          change: this.typeChange
        }
      },
      ElOption_type: {
        options_in: [
          { id: 2, text: $T("分摊对象") },
          { id: 1, text: $T("被分摊对象") }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      // index组件
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        //  prop: "",      // 支持path a[0].b
        label: "#", //列名
        headerAlign: "left",
        align: "lftt",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "50" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumnArr: [
        {
          label: $T("统计周期"),
          prop: "time",
          minWidth: 100,
          showOverflowTooltip: true,
          headerAlign: "left",
          align: "left"
        },
        {
          label: $T("分摊成本合计(元)"),
          prop: "node1",
          minWidth: 100,
          showOverflowTooltip: true,
          headerAlign: "right",
          align: "right"
        }
      ],
      CetButton_7: {
        visible_in: true,
        disable_in: false,
        title: "",
        plain: true,
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_7_statusTrigger_out
        }
      },
      CetButton_8: {
        visible_in: true,
        disable_in: true,
        title: "",
        plain: true,
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_8_statusTrigger_out
        }
      },
      // 请填写组件含义组件
      CetChart_1: {
        //组件输入项
        inputData_in: null,
        config: {
          options: {
            tooltip: {
              trigger: "axis",
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
              }
            },
            barWidth: 300,
            legend: {
              data: []
            },
            grid: {
              left: "1%",
              right: "1%",
              top: "1%",
              bottom: "1%",
              containLabel: true
            },
            xAxis: [
              {
                type: "category",
                data: []
              }
            ],
            yAxis: [
              {
                type: "value"
              }
            ],
            series: []
          }
        }
      }
    };
  },

  methods: {
    CetGiantTree_1_checkedNodes_out(val) {
      if (val.length > 6) {
        this.$alert($T("最多勾选6个节点"), "", {
          confirmButtonText: $T("确定"),
          callback: action => {}
        });
        this.CetGiantTree_1.checkedNodes = this.checkedNodes;
        return;
      }
      this.checkedNodes = val;
      this.checkedNodes.map((item, index) => {
        item.index = index.toString();
      });
      this.tabs = this.checkedNodes;
      this.tabIndex = 0;
      this.request();
    },
    // 接口 获取节点树
    getTree(obj) {
      let _this = this;
      _this.CetGiantTree_1.checkedNodes = _this.checkedNodes = [];
      this.tabs = [];
      this.tabIndex = 0;
      this.request();
      _this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      let auth = _this.token; //身份验证
      httping({
        url: "/eem-service/v1/node/nodeTree",
        data: obj,
        method: "POST"
      }).then(res => {
        if (res.code == 0) {
          var data = [];
          res.data.forEach((item, index) => {
            var obj = _this._.cloneDeep(item);
            obj.children = [];
            data.push(obj);
            if (item.children && item.children.length > 0) {
              item.children.forEach((ite, inde) => {
                if (ite.modelLabel != "linesegmentwithswitch") {
                  data[index].children.push(ite);
                }
              });
            }
          });
          let list = data;
          this.handleTree(list);
          this.projectId = list[0].id;
          this.nodes = list[0].children;
          _this.CetGiantTree_1.inputData_in = list;
          this.projectEnergy(list[0].id); // 查询项目的能源类型
          this.setTreeLeaf(this.data);
        }
      });
    },
    // 设置节点禁用
    setTreeLeaf(nodesAll) {
      if (nodesAll && nodesAll.length > 0) {
        nodesAll.forEach(item => {
          if (item.childSelectState == 2) {
            this.$set(item, "disabled", true);
          } else if (item.childSelectState == 1) {
            this.$set(item, "disabled", false);
          }
          this.setTreeLeaf(this._.get(item, "children", []));
        });
      }
    },
    // 处理节点树
    handleTree(array) {
      const expanded = datas => {
        if (datas && datas.length > 0) {
          datas.forEach(e => {
            e.label = e.name;
            expanded(e.children);
          });
        }
      };
      expanded(array);
      return array;
    },
    // 接口 查询项目的能源类型
    projectEnergy(projectId) {
      httping({
        url: "eem-service/v1/project/projectEnergy?projectId=" + projectId,
        method: "GET"
      }).then(res => {
        if (res.code == 0) {
          if (res.data && res.data.length != 0) {
            let list = res.data;
            list.map(item => {
              item.text = item.name;
              item.id = item.energytype;
            });
            this.ElOption_1.options_in = list.filter(
              i => ![18, 22].includes(i.id)
            );
            this.ElSelect_1.value = list[0].energytype;
          }
        }
      });
    },
    // 接口 分摊成本
    cost(projectId, nodes) {
      let list = [];
      let time = this.value3 * 1;
      let startTime = new Date(time + "-1-1").getTime();
      let endTime = new Date(time + 1 + "-1-1").getTime();
      nodes.map(item => {
        let obj = {};
        obj.id = item.id;
        obj.modelLabel = item.modelLabel;
        obj.name = item.name;
        list.push(obj);
      });
      let obj = {
        startTime,
        endTime,
        nodes: list,
        cycle: 17, // 只能按年 17
        energyType: this.ElSelect_1.value,
        shareNodeType: this.ElSelect_type.value,
        projectId
      };
      httping({
        url: "eem-service/v1/costanalysis/share/cost",
        method: "POST",
        data: obj
      }).then(res => {
        if (res.code == 0) {
          let list = res.data;
          let legendList = [];
          let seriesList = [];
          let xAxis = [];
          let colorList = ["#0099FF", "#00CC66", "#FFCC33", "#FF6666"];
          nodes.map((item, index) => {
            legendList.push(item.name);
          });
          list.map((item, index1) => {
            let obj = {
              name: nodes[index1].name,
              type: "bar",
              stack: $T("被分摊对象"),
              data: [],
              barWidth: 30,
              color: colorList[index1]
            };
            item.data.map((item, index) => {
              let value = item.value ? item.value : 0;
              obj.data.push(value.toFixed(2));
              if (index1 == 0) {
                let month = new Date(item.logTime).getMonth() * 1 + 1;
                xAxis.push(month + $T("月"));
              }
            });
            seriesList.push(obj);
          });
          this.createEchart(legendList, seriesList, xAxis);
        }
      });
    },
    // 接口 分摊数据
    allocation(projectId) {
      let node = {};
      node.id = this.tabs[this.tabIndex].id;
      node.modelLabel = this.tabs[this.tabIndex].modelLabel;
      node.name = this.tabs[this.tabIndex].name;
      let time = this.value3 * 1;
      let startTime = new Date(time + "-1-1").getTime();
      let endTime = new Date(time + 1 + "-1-1").getTime();
      let obj = {
        startTime,
        endTime,
        node,
        cycle: 17, // 只能查年17
        energyType: this.ElSelect_1.value,
        shareNodeType: this.ElSelect_type.value,
        projectId
      };
      httping({
        url: "eem-service/v1/costanalysis/share/data",
        method: "POST",
        data: obj
      }).then(res => {
        if (res.code == 0) {
          let head = res.data.header;
          let list = res.data.data;
          let dataList = [];
          let headList = [
            {
              label: $T("统计周期"),
              prop: "time",
              minWidth: 100,
              showOverflowTooltip: true,
              headerAlign: "left",
              align: "left"
            }
          ];
          head.map((item, index) => {
            let obj = {
              label: item.name + `(${$T("元")})`,
              prop: "node" + index,
              minWidth: 100,
              showOverflowTooltip: true,
              headerAlign: "right",
              align: "right",
              formatter: function (val) {
                if (val["node" + index] || val["node" + index] === 0) {
                  return val["node" + index];
                } else {
                  return "--";
                }
              }
            };
            headList.push(obj);
          });
          for (let item in list) {
            let year = new Date(item * 1).getFullYear() + $T("年");
            let month = new Date(item * 1).getMonth() + 1 + $T("月");
            let obj = {
              time: year + month,
              logTime: item
            };
            list[item].map((item, index) => {
              let nodeName = "node" + index;
              if (item.value === null) {
                obj[nodeName] = 0;
              } else {
                obj[nodeName] = item.value.toFixed(2);
              }
            });
            dataList.push(obj);
          }
          dataList = this._.orderBy(dataList, ["logTime"], ["asc"]);
          this.ElTableColumnArr = headList;
          this.CetTable_1.data = dataList;
        }
      });
    },
    // 创建柱状图
    createEchart(legendList, seriesList, xAxis) {
      this.CetChart_1 = {
        //组件输入项
        inputData_in: null,
        config: {
          options: {
            toolbox: {
              top: 40,
              right: 30,
              feature: {
                saveAsImage: {
                  title: $T("保存为图片")
                }
              }
            },
            tooltip: {
              trigger: "axis",
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
              }
            },
            barWidth: 300,
            legend: {
              data: legendList
            },
            grid: {
              left: "3%",
              right: "4%",
              containLabel: true
            },
            xAxis: [
              {
                type: "category",
                data: xAxis
              }
            ],
            yAxis: [
              {
                name: "成本（元）",
                type: "value"
              }
            ],
            series: seriesList
          }
        }
      };
    },
    // 每次切换之后重新调用接口
    request() {
      if (this.tabs.length == 0) {
        // 没有节点的时候初始化页面
        this.CetChart_1.config.options.legend.data = [];
        this.CetChart_1.config.options.series = [
          {
            name: "",
            type: "bar",
            stack: "",
            data: [],
            barWidth: 30
          }
        ];
        this.ElTableColumnArr = [
          {
            label: $T("统计周期"),
            prop: "time",
            minWidth: 100,
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left"
          },
          {
            label: $T("分摊成本合计(元)"),
            prop: "node1",
            minWidth: 100,
            showOverflowTooltip: true,
            headerAlign: "right",
            align: "right"
          }
        ];

        this.CetTable_1.data = [];
        return;
      }
      setTimeout(() => {
        this.cost(this.projectId, this.tabs);
        this.allocation(this.projectId);
      }, 0);
    },
    // 切换分摊对象和被分摊对象
    typeChange(val) {
      let obj = {};
      if (val == 1) {
        obj = {
          rootID: sessionStorage.projectId,
          rootLabel: "project",
          subLayerConditions: TREE_PARAMS.EqualShareComparisonTree,
          treeReturnEnable: true
        };
      } else {
        obj = {
          rootID: sessionStorage.projectId,
          rootLabel: "project",
          subLayerConditions: TREE_PARAMS.EqualShareComparisonTree,
          treeReturnEnable: true
        };
      }
      this.getTree(obj);
      this.request();
    },
    // 切换能源类型
    energyChange(val) {
      this.request();
    },
    // 年切换
    yearChange(val) {
      let yearNow = new Date().getFullYear();
      let time = val.getTime();
      time = new Date(time * 1).getFullYear();
      if (yearNow == time) {
        this.CetButton_8.disable_in = true;
      } else {
        this.CetButton_8.disable_in = false;
      }
      this.value3 = time.toString();
      this.request();
    },
    // 向前按钮
    CetButton_7_statusTrigger_out(val) {
      this.CetButton_8.disable_in = false;
      let time = this.value3;
      console.log(time);
      time--;
      this.value3 = time.toString();
      this.request();
    },
    // 向后按钮
    CetButton_8_statusTrigger_out(val) {
      let yearNow = new Date().getFullYear();
      let time = this.value3;
      time++;
      if (time == yearNow) {
        this.CetButton_8.disable_in = true;
      }
      this.value3 = time.toString();
      this.request();
    },
    // 切换分摊对象（被分摊对象）
    handleClick(val) {
      this.tabIndex = val;
      this.allocation(this.projectId);
    },
    CetTable_1_record_out(val) {},
    CetTable_1_outputData_out(val) {}
  },
  created: function () {
    let obj = {
      rootID: sessionStorage.projectId,
      rootLabel: "project",
      subLayerConditions: TREE_PARAMS.EqualShareComparisonTree,
      treeReturnEnable: true
    };
    this.getTree(obj);
  },
  mounted() {}
};
</script>
<style lang="scss" scoped>
.filter-tree {
  height: calc(100% - 94px);
  overflow: auto;
}
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.title {
  width: 200px;
}
.top {
  width: 700px;
  // position: absolute;
  // right: 0;
  // display: flex;
  // align-items: center;
}
.tip1 {
  height: 450px;
}
.tip2 {
  height: calc(100% - 400px);
  min-height: 400px;
}
.container {
  width: 100%;
  height: calc(100% - 58px);
  display: block;
}
.a {
  width: 500px;
  height: 1000px;
}
</style>
