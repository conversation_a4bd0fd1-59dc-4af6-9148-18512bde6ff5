<template>
  <div class="clearfix">
    <customElSelect
      v-show="showOption"
      class="fl"
      value-key="text"
      v-model="currentTimeOption"
      :disabled="selectDisable"
      @change="changTimeType"
      size="small"
      style="width: 160px"
      :prefix_in="showOption ? $T('分析周期') : $T('查询时段')"
    >
      <el-option
        v-for="(item, index) in timeOptions"
        :key="`${index}_${item.typeID}`"
        :label="item.text"
        :value="item"
      ></el-option>
    </customElSelect>

    <el-button
      class="fl mlJ1 mrJ"
      plain
      icon="el-icon-arrow-left"
      :disabled="disable_in"
      @click="queryPrv"
    ></el-button>
    <el-date-picker
      size="small"
      value-format="timestamp"
      :clearable="false"
      class="fl"
      style="width: 200px !important"
      v-model="value"
      :picker-options="pickerOptions"
      :type="currentTimeOption.type"
      :disabled="disable_in"
      :placeholder="$T('选择日期')"
    ></el-date-picker>
    <el-time-select
      v-if="showTimeSelect"
      class="timeSelect fl mlJ"
      v-model="timeSelectValue"
      :disabled="disable_in"
      :clearable="false"
      :placeholder="$T('请选择时间')"
      :picker-options="{
        start: '00:00',
        step: '01:00',
        end: '23:00',
        format: 'HH'
      }"
    ></el-time-select>
    <el-button
      class="fl mlJ"
      plain
      icon="el-icon-arrow-right"
      :disabled="nextDisabled || disable_in"
      @click="queryNext"
    ></el-button>
  </div>
</template>
<script>
import { TIME_TYPE } from "@/store/constQuantity";
export default {
  name: "TimeTool",
  props: {
    time: {
      type: [Number, Object],
      default: +new Date()
    },
    val: {
      type: [Number, Object, String]
    },
    typeID: {
      type: Number,
      default: 14
    },
    showOption: {
      type: Boolean,
      default: true
    },
    selectDisable: {
      type: Boolean,
      default: false
    },
    timeType_in: {
      type: Array,
      default: function () {
        return [];
      }
    },
    isNextDisabled: {
      type: Boolean,
      default: false
    },
    disable_in: {
      type: Boolean,
      default: false
    }
  },

  watch: {
    val: {
      deep: true,
      handler: function (val, oldVal) {
        this.value = val;
      }
    },
    value: {
      deep: true,
      handler: function (val, oldVal) {
        if (val === oldVal) return;
        this.$emit("update:val", this.judgeHourSelect(val));
        this.$emit("change", {
          val: this.judgeHourSelect(val),
          timeOption: this._.find(this.timeOptions, {
            typeID: this.currentTimeOption.typeID
          })
        });
      }
    },
    typeID: {
      deep: true,
      handler: function (val, oldVal) {
        if (val === oldVal) return;
        this.$set(
          this,
          "currentTimeOption",
          this._.find(this.timeOptions, { typeID: val })
        );
      }
    },
    showOption: {
      deep: true,
      handler: function (val, oldVal) {
        if (val === oldVal) return;
        this.$emit("change", {
          val: this.judgeHourSelect(this.value),
          timeOption: this._.find(this.timeOptions, {
            typeID: this.currentTimeOption.typeID
          })
        });
      }
    },
    timeSelectValue: {
      deep: true,
      handler: function (val) {
        this.$emit("update:val", this.judgeHourSelect(this.value));
        this.$emit("change", {
          time: this.judgeHourSelect(this.value),
          timeOption: this._.find(this.timeOptions, {
            typeID: this.currentTimeOption.typeID
          })
        });
      }
    },
  },
  computed: {
    nextDisabled() {
      return this.isNextDisabled
        ? this.$moment(this.value)
            .startOf(this.currentTimeOption.unit)
            .valueOf() >=
            this.$moment().startOf(this.currentTimeOption.unit).valueOf()
        : false;
    },
    showTimeSelect(){
      return this._.get(this.currentTimeOption,"showTimeSelect")
    }
  },
  data(vm) {
    return {
      pickerOptions: {
        disabledDate(time) {
          return vm.isNextDisabled ? time.getTime() > Date.now() : false;
        }
      },
      timeOptions: TIME_TYPE,
      value: +new Date(),
      currentTimeOption: this._.find(TIME_TYPE, { typeID: this.typeID }),
      timeSelectValue: "00:00"
    };
  },
  methods: {
    // 通过匹配key与value返回timeType对应对象
    findTimeType(key, value) {
      return this.timeOptions.find(item => {
        return item[key] === value;
      });
    },
    queryPrv() {
      var date = this.$moment(this.value);
      this.value = date.subtract(1, this.currentTimeOption.unit).valueOf();
    },
    queryNext() {
      var date = this.$moment(this.value);
      this.value = date.add(1, this.currentTimeOption.unit).valueOf();
    },
    changDate(val) {
      this.value = val;
      this.$emit("update:val", this.judgeHourSelect(val));
      this.$emit("change", {
        val: this.judgeHourSelect(val),
        timeOption: this._.find(this.timeOptions, {
          typeID: this.currentTimeOption.typeID
        })
      });
    },
    changTimeType(val) {
      this.timeSelectValue = "00:00";
      if (this.value > new Date().getTime()) {
        this.value = new Date().getTime();
      }
      this.$emit("change", { val: this.judgeHourSelect(this.value), timeOption: val });
    },
    judgeHourSelect(val){
      if(this._.get(this.currentTimeOption,"showTimeSelect")){
        const time = this.$moment(val).hour(this.timeSelectValue.split(":")[0]).valueOf();
        return time;
      }else{
        return val;
      }
    }
  },
  mounted() {
    this.value = this.val;
    this.timeOptions = this.timeType_in;
  }
};
</script>
<style lang="scss" scoped>
.timeSelect{
  width: 150px;
}
</style>
