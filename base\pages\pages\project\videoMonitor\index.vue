<template>
  <div class="page eem-common">
    <el-container class="fullheight">
      <el-aside width="315px" class="eem-aside flex-column">
        <CetTree
          class="flex-auto"
          :selectNode.sync="CetTree_folder.selectNode"
          :checkedNodes.sync="CetTree_folder.checkedNodes"
          v-bind="CetTree_folder"
          v-on="CetTree_folder.event"
        ></CetTree>
      </el-aside>
      <el-container
        class="eem-container fullheight mlJ3 flex-column"
        id="screen_container"
      >
        <div class="header_screen">
          <div class="mrJ2 mbJ2 text-ellipsis">
            <el-tooltip :content="currentNode.name" effect="light">
              <span class="common-title-H1">{{ currentNode.name }}</span>
            </el-tooltip>
          </div>
          <div class="screenHandle" @click.stop="screenHandle($event)">
            <omega-icon
              class="cetIcon"
              :symbolId="screenModel ? 'frame-fullscreen-lin' : 'lessen-lin'"
            />
          </div>
        </div>
        <div v-if="!isVedioBool" class="video_tip">
          <span>该节点下没有添加摄像头，请前往视频配置页面进行视频添加</span>
        </div>
        <div :class="`view-list view-list-${currentPanel}`" v-else>
          <div
            v-for="(item, index) in vedioList"
            :key="item + index"
            class="video-box mbJ2"
          >
            <div class="mbJ text-ellipsis">
              <el-tooltip :content="item.name" effect="light">
                <span class="common-title-H3">{{ item.name }}</span>
              </el-tooltip>
            </div>
            <div style="height: calc(100% - 32px)">
              <players :videoInfo="item"></players>
            </div>
          </div>
        </div>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import customApi from "@/api/custom";
import players from "./players.vue";

export default {
  name: "videoMonitor",
  components: { players },
  computed: {
    UserID() {
      return this.$store.state.userInfo.id;
    }
  },
  data() {
    return {
      CetTree_folder: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_folder_currentNode_out
        }
      },
      currentNode: {},
      vedioList: [],
      currentPanel: "1",
      isVedioBool: true,
      screenModel: true
    };
  },
  watch: {},
  methods: {
    CetTree_folder_currentNode_out(val) {
      this.currentNode = val;
      this.getVideosInfo(val);
    },
    // 对获取视频数据进行处理
    getSourceIN(videoInfo) {
      if (!videoInfo) return "";
      let url = videoInfo.playUrl;
      // 是否为直连
      if (videoInfo.joinType === "direct") {
        let playUrl = JSON.parse(videoInfo.playUrl);
        url = _.get(playUrl, "webSocket");
      }
      return url;
    },
    // 获取视频文件夹
    async getTreeData() {
      const data = {
        userId: this.UserID,
        tenantId: this.$store.state.projectTenantId
      };
      const res = await customApi.videoQueryFolders(data);
      if (res.code !== 0) {
        return;
      }
      this.CetTree_folder.inputData_in = res.data;
      this.CetTree_folder.selectNode = this._.isEmpty(this.currentNode)
        ? res.data[0]
        : this._.cloneDeep(this.currentNode);
    },
    // 获取文件夹下所有的视频数据
    async getVideosInfo(val) {
      const data = {
        foldId: val.id,
        page: {
          index: 0,
          limit: 100
        }
      };
      const res = await customApi.videoQueryVideos(data);
      if (res.code !== 0) {
        this.vedioList = [];
        return;
      }
      if (res.data && res.data.length) {
        this.vedioList = res.data.map((v, index) => {
          return { id: v.id, source_in: this.getSourceIN(v), name: v.name };
        });
        this.currentPanel = this.vedioList.length > 1 ? 4 : 1;
        this.isVedioBool = true;
      } else {
        this.vedioList = [];
        this.isVedioBool = false;
      }
    },
    // 全屏放大
    screenHandle(e) {
      // 取消点击事件的默认操作
      e.preventDefault();
      this.screenModel = !this.screenModel;
      // 获取想要放大的元素
      let screenBody = document.getElementById("screen_container");
      if (!this.screenModel) {
        if (screenBody.requestFullscreen) {
          screenBody.requestFullscreen();
        } else if (screenBody.mozRequestFullScreen) {
          screenBody.mozRequestFullScreen();
        } else if (screenBody.webkitRequestFullScreen) {
          screenBody.webkitRequestFullScreen();
        } else if (screenBody.msRequestFullscreen) {
          screenBody.msRequestFullscreen();
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }
    }
  },
  activated() {
    this.currentNode = {};
    this.vedioList = [];
    this.getTreeData();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.view-list {
  height: 100%;
  width: 100%;
  overflow: auto;
  &-1 {
    height: 100%;
    width: 100%;
    .video-box {
      margin: 0;
      padding: 8px 12px;
      height: calc(100% - 16px);
      width: calc(100% - 24px);
      @include background_color(BG);
    }
  }
  &-2,
  &-3,
  &-4 {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-content: space-between;
    .video-box {
      padding: 8px 12px;
      height: calc(50% - 4px);
      width: calc(50% - 26px);
      @include background_color(BG);
    }
  }
}
.video_tip {
  text-align: center;
  line-height: 300px;
  span {
    font-size: 18px;
  }
}
.header_screen {
  display: flex;
  justify-content: space-between;
}
.screenHandle {
  font-size: 26px;
  font-weight: 500;
  @include margin_right(J2);
  @include margin_left(J2);
  cursor: pointer;
  transform: scale(1.5);
  .cetIcon {
    height: 22px;
    width: 22px;
  }
}
.screenHandle:hover {
  @include font_color(ZS);
}
</style>
