<template>
  <div class="energyEfficiencyAssessment">
    <span>
      {{ $T("依据标准") }}
    </span>
    <span class="value">
      {{ $T("《QSY_09821-2021_油气田用往复式天然气压缩机组节能监测方法》") }}
    </span>
    <span>
      {{ $T("规定的油气田压缩机组节能监测指标，分体式电驱压缩机机组效率") }}
    </span>
    <span class="value">
      {{ $T("大于60%") }}
    </span>
    <span>
      {{ $T("为合格设备，机组效率") }}
    </span>
    <span class="value">
      {{ $T("大于65%") }}
    </span>
    <span>{{ $T("为节能设备。对浙江油田西南采气厂的") }}</span>
    <span class="value">
      {{ formatNumberWithPrecision(reportData.compressorCount, 0) }}台
    </span>
    <span>
      {{ $T("电驱往复式压缩机在本月的机组效率平均值进行评估，合格设备") }}
    </span>
    <span class="value">
      {{ formatNumberWithPrecision(reportData.compressorQualified, 0) }}台
    </span>
    <span>{{ $T("，节能设备") }}</span>
    <span class="value">
      {{ formatNumberWithPrecision(reportData.compressorSaving, 0) }}台
    </span>
    <span>{{ $T("，合格率为") }}</span>
    <span class="value">
      {{ formatNumberWithPrecision(reportData.compressorQualifiedRate, 2) }}%
    </span>
    <span>{{ $T("，节能率为") }}</span>
    <span class="value">
      {{ formatNumberWithPrecision(reportData.compressorSavingRate, 2) }}%
    </span>
    <span>。</span>
  </div>
</template>

<script>
import common from "eem-utils/common";
export default {
  name: "energyEfficiencyAssessment",
  components: {},
  props: {
    reportData: {
      type: Object
    }
  },
  data() {
    return {};
  },
  watch: {},
  methods: {
    formatNumberWithPrecision(...args) {
      return common.formatNumberWithPrecision(...args);
    }
  }
};
</script>

<style lang="scss" scoped>
.energyEfficiencyAssessment {
  background: #f8fafb;
  padding: 8px 16px;
  box-sizing: border-box;
  line-height: 20px;
  .value {
    font-weight: bold;
    color: #00b45e;
  }
  text-indent: 24px;
  * {
    word-break: break-all;
    font-size: 12px;
  }
}
</style>
