<template>
  <div class="wrap">
    <el-date-picker
      v-if="type === 'times'"
      value-format="timestamp"
      :format="dateFormat"
      v-model="dateValue"
      type="datetimerange"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      :picker-options="timeOptions"
      @change="timePickerChange"
      @focus="timePickerFocus"
      popper-class="customCycleEnergy_timePicker"
    ></el-date-picker>
    <customDatePointPicker
      v-else
      value-format="timestamp"
      :format="dateFormat"
      :type="dateType"
      :clearable="true"
      v-model="dateValue"
      placeholder="请选择"
      :picker-options="pickerOptions"
      @change="pickerChange"
      :weeksModel="type === 'weeks'"
      :selectNumMax="selectNumMax"
    ></customDatePointPicker>
  </div>
</template>

<script>
import Vue from "vue";
import customDatePointPicker from "./date-picker/index.js";
Vue.component("customDatePointPicker", customDatePointPicker);
const TYPES = ["dates", "weeks", "months", "years", "times"];
/*
  times单独处理
*/
export default {
  props: {
    value: Array,
    type: String,
    selectNumMax: {
      type: Number,
      default: () => 20
    }
  },
  data(vm) {
    return {
      dateValue: [],
      dateType: "dates",
      pickerOptions: {
        firstDayOfWeek: 1
      },
      dateFormat: "",
      timePickerStart: null,
      timePickerEnd: null,
      timeOptions: {
        firstDayOfWeek: 1,
        disabledDate: time => {
          if (!vm.timePickerEnd && !vm.timePickerStart) {
            return false;
          }
          return (
            time.getTime() > vm.timePickerEnd ||
            time.getTime() < vm.timePickerStart
          );
        },
        onPick({ maxDate, minDate }) {
          if (!maxDate && minDate) {
            // 第一次选择前后取两天
            let startTime = vm.$moment(minDate).add(-2, "d").valueOf(),
              endTime = vm.$moment(minDate).add(2, "d").valueOf();
            vm.timePickerEnd = endTime;
            vm.timePickerStart = startTime;
          } else if (maxDate && minDate) {
            vm.timePickerEnd = null;
            vm.timePickerStart = null;
          }
        }
      }
    };
  },
  watch: {
    type: {
      handler(val) {
        if (TYPES.includes(val)) {
          if (val === "weeks") {
            this.weeksHandle();
          } else {
            this.dateType = val;
          }
        } else {
          console.log("type配置错误，重置为dates");
          this.dateType = "dates";
        }
        this.setDateFormat(val);
      },
      immediate: true
    },
    value: {
      handler(val) {
        this.dateValue = val;
      },
      deep: true
    }
  },
  methods: {
    // 周单独处理，换成日
    weeksHandle() {
      this.dateType = "dates";
    },
    setDateFormat(val) {
      switch (val) {
        case "times":
          this.dateFormat = "yyyy-MM-dd HH:mm";
          break;
        case "dates":
          this.dateFormat = "yyyy-MM-DD";
          break;
        case "weeks":
          this.dateFormat = ""; //周在内部进行处理
          break;
        case "months":
          this.dateFormat = "yyyy-MM";
          break;
        case "years":
          this.dateFormat = "yyyy";
          break;

        default:
          this.dateFormat = "yyyy-MM-DD";
          break;
      }
    },
    pickerChange() {
      this.dateOut();
    },
    dateOut() {
      if (!this.dateValue) {
        this.$emit("update:value", []);
        this.$emit("change", []);
        return;
      }
      if (this.dateValue.length > this.selectNumMax) {
        this.$message({
          type: "warning",
          message: `选择时间个数超过限制${this.selectNumMax}个`
        });
        this.dateValue = this.value;
        return;
      }
      // 排序
      this.dateValue = this.dateValue.sort();
      this.$emit("update:value", this.dateValue);
      this.$emit("change", this.dateValue);
    },
    timePickerChange() {
      if (
        this.$moment(this.dateValue[0]).add(72, "H").valueOf() <
        this.$moment(this.dateValue[1]).valueOf()
      ) {
        this.$message({
          type: "warning",
          message: `选择时间个数超过限制`
        });
        this.dateValue = this.value;
        return;
      }
      this.dateOut();
    },
    timePickerFocus() {
      this.timePickerEnd = null;
      this.timePickerStart = null;
    }
  }
};
</script>

<style lang="scss" scoped>
.wrap {
  display: inline-block;
}
</style>
<style lang="scss">
.customCycleEnergy_timePicker .el-time-spinner {
  .el-time-spinner__wrapper:nth-child(1) {
    width: 100%;
  }
  .el-time-spinner__wrapper:nth-child(2) {
    display: none;
  }
}
</style>
