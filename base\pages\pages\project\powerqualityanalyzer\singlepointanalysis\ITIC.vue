<template>
  <div style="height: 100%; position: relative" class="flex-column">
    <div class="text-right" v-show="showDataType">
      <ElSelect
        class="trend-select"
        v-model="ElSelect_dataType.value"
        v-bind="ElSelect_dataType"
        v-on="ElSelect_dataType.event"
      >
        <ElOption
          v-for="item in ElOption_dataType.options_in"
          :key="item[ElOption_dataType.key]"
          :label="item[ElOption_dataType.label]"
          :value="item[ElOption_dataType.value]"
          :disabled="item[ElOption_dataType.disabled]"
        ></ElOption>
      </ElSelect>
    </div>
    <div class="flex-auto">
      <div class="fullheight" v-if="ElSelect_dataType.value == 2">
        <CetChart
          style="height: 100%"
          ref="ITICChart"
          :inputData_in="CetChart2_ITIC.inputData_in"
          v-bind="CetChart2_ITIC.config"
        ></CetChart>
      </div>
      <div class="fullheight" v-else>
        <CetChart
          v-bind="CetChart_top"
          @click="CetChart_top_click"
          ref="CetChart_top"
        ></CetChart>
      </div>
    </div>
  </div>
</template>
<script>
import common from "eem-utils/common";
import customApi from "@/api/custom";
import ELECTRICAL_DEVICE from "@/store/electricaldevice";
import { find } from "eem-utils/util.js";
export default {
  name: "ITIC",
  props: {
    queryTime: Object,
    currentNode: Object,
    refreshTrigger_in: {
      type: [Number]
    },
    isShow: {
      type: Boolean,
      default: false
    },
    handleRow_in: Object
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    modelLabels() {
      return ELECTRICAL_DEVICE.map(i => i.value);
    }
  },
  watch: {
    queryTime: {
      deep: true,
      handler: function (val, oldVal) {
        if (
          this.modelLabels.includes(
            this._.get(this.currentNode, "data.modelLabel")
          )
        )
          this.init();
      }
    },
    refreshTrigger_in: {
      deep: true,
      handler: function (val, oldVal) {
        this.init();
      }
    },
    currentNode: {
      deep: true,
      handler: function (val, oldVal) {
        if (
          this._.get(val, "data.tree_id", -1) ===
          this._.get(oldVal, "data.tree_id", -2)
        )
          return;
        if (this.modelLabels.includes(this._.get(val, "data.modelLabel")))
          this.init();
      }
    },
    handleRow_in: {
      deep: true,
      handler: function (val, oldVal) {
        this.CetChart_top_click_red(val);
      }
    }
  },
  data(vm) {
    // 当前主题颜色
    let themeActiveId = localStorage.getItem("omega_theme");
    return {
      CetChart2_ITIC: {
        //组件输入项
        inputData_in: null,
        config: {
          options: {
            title: {
              show: false,
              text: "",
              x: "center",
              textStyle: {
                fontSize: 16,
                fontWeight: "normal",
                color: "#999"
              }
            },
            graphic: [
              {
                type: "text",
                z: 100,
                left: "12%",
                top: "middle",
                style: {
                  fill: themeActiveId === "light" ? "#242424" : "#F0F1F2",
                  text: "A区",
                  font: "bolder 14px Microsoft YaHei"
                }
              },
              {
                type: "text",
                z: 100,
                left: "60%",
                top: "40%",
                style: {
                  fill: themeActiveId === "light" ? "#242424" : "#F0F1F2",
                  text: "B区",
                  font: "bolder 14px Microsoft YaHei"
                }
              },
              {
                type: "text",
                z: 100,
                left: "75%",
                bottom: "15%",
                style: {
                  fill: themeActiveId === "light" ? "#242424" : "#F0F1F2",
                  text: "C区",
                  font: "bolder 14px Microsoft YaHei"
                }
              }
            ],
            grid: {
              show: "true",
              backgroundColor:
                themeActiveId === "light"
                  ? "rgba(31,32,34,0.05)"
                  : "rgba(250,251,251,0.05)",
              top: 35,
              right: 30,
              bottom: 35,
              left: 43
            },
            legend: {
              x: "center",
              y: "top"
            },
            tooltip: {
              // trigger: "axis",
              appendToBody: true,
              formatter(params) {
                // console.log(params);

                const info = params.data[2];
                let txt = "";
                txt += "节点：" + info.monitoredName + "<br />";
                txt +=
                  "时间：" +
                  vm.$moment(info.eventtime).format("YYYY-MM-DD HH:mm:ss.SSS") +
                  "<br />";

                txt +=
                  "事件类型：" +
                  vm.formatEnum(info.pqvariationeventtype) +
                  "<br />";
                txt += "电压幅值（%）：" + params.data[1] + "<br />";
                txt += "持续时间（s）：" + params.data[0] + "<br />";
                return txt;
              }
            },

            xAxis: {
              type: "log",
              min: 0.000001,
              interval: 10,
              max: 100,
              splitLine: {
                show: false
              },
              axisLabel: {
                formatter: (val, i) => {
                  // console.log(val, i);
                  if (i == 0 || i == 1 || i == 2) {
                    return val * 1000000 + "μs";
                  } else if (i == 5 || i == 3 || i == 4) {
                    return val * 1000 + "ms";
                  } else {
                    return val + "s";
                  }
                }
              }
            },
            yAxis: {
              max: 500,
              name: "电压幅值(%)",
              interval: 50,
              splitLine: {
                show: false
              }
            },
            series: [
              {
                name: "A区",
                // symbolSize: 20,
                data: [],
                type: "scatter",
                symbol: "rect",
                markLine: {
                  symbol: "none",
                  silent: true,
                  data: [
                    {
                      yAxis: 100
                    }
                  ],
                  lineStyle: {
                    color: "red",
                    type: "solid",
                    width: 2.5
                  }
                },
                symbolSize: 12,
                zlevel: 10,
                itemStyle: {
                  color: "#008001"
                }
              },
              {
                name: "B区",
                // symbolSize: 20,
                data: [],
                type: "scatter",
                symbol: "triangle",
                symbolSize: 12,
                zlevel: 10,
                itemStyle: {
                  color: "#EB8308"
                }
              },
              {
                name: "C区",
                // symbolSize: 20,
                data: [],
                type: "scatter",
                symbol: "triangle",
                symbolRotate: 180,
                symbolSize: 12,
                zlevel: 10,
                itemStyle: {
                  color: "#D32A46"
                }
              },
              {
                data: [
                  [Math.pow(10, -0.398) / 1000, 500],
                  [Math.pow(10, 0) / 1000, 200],
                  [Math.pow(10, 0.477) / 1000, 140],
                  [Math.pow(10, 0.477) / 1000, 120],
                  [Math.pow(10, 1.3) / 1000, 120],
                  [Math.pow(10, 2.699) / 1000, 120],
                  [Math.pow(10, 2.699) / 1000, 110],
                  [Math.pow(10, 4) / 1000, 110],
                  [Math.pow(10, 5) / 1000, 110]
                ],
                type: "line",
                symbol: "none",
                areaStyle: {
                  color:
                    themeActiveId === "light"
                      ? "rgba(41,176,97,0.2)"
                      : "rgba(13,134,255,0.2)",
                  origin: "end"
                },
                lineStyle: {
                  color: themeActiveId === "light" ? "#29B061" : "#0D86FF"
                }
              },
              {
                data: [
                  [Math.pow(10, 1) / 1000, 0],
                  [Math.pow(10, 1) / 1000, 70],
                  [Math.pow(10, 2.699) / 1000, 70],
                  [Math.pow(10, 2.699) / 1000, 80],
                  [Math.pow(10, 4) / 1000, 80],
                  [Math.pow(10, 4) / 1000, 90],
                  [Math.pow(10, 5) / 1000, 90]
                ],
                type: "line",
                symbol: "none",
                areaStyle: {
                  color:
                    themeActiveId === "light"
                      ? "rgba(255,194,76,0.2)"
                      : "rgba(252,185,44,0.2)"
                },
                lineStyle: {
                  color: themeActiveId === "light" ? "#29B061" : "#0D86FF"
                }
              }
            ]
          }
        }
      },
      CetChart_top: {
        //组件输入项
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "item"
          },
          grid: {
            show: "true",
            top: 35,
            right: 30,
            bottom: 35,
            left: 43
          },
          xAxis: {
            type: "log",
            min: 0.01,
            interval: 10,
            max: 100,
            splitLine: {
              show: true,
              lineStyle: {
                color: themeActiveId === "light" ? "#E0E4E8" : "#414B6E"
              }
            },
            axisLabel: {
              formatter: (val, i) => {
                return val + "s";
              }
            },
            minorTick: {
              show: true,
              splitNumber: 10
            }
          },
          yAxis: {
            type: "value",
            max: 100,
            interval: 10,
            min: 0,
            splitLine: {
              show: true,
              lineStyle: {
                color: themeActiveId === "light" ? "#E0E4E8" : "#414B6E"
              }
            },
            axisLabel: {
              formatter: "{value} %"
            }
          },
          series: [
            {
              data: [
                // [0.01, 0],
                // [0.02, 0],
                // [0.02, 50],
                // [0.2, 50],
                // [0.2, 70],
                // [0.5, 70],
                // [0.5, 80],
                // [10, 80],
                // [10, 90],
                // [100, 90]
              ],
              type: "line",
              symbol: "none"
            },
            {
              symbolSize: 14,
              data: [],
              type: "scatter",
              itemStyle: {
                color: themeActiveId === "light" ? "#FFC24C" : "#FCB92C"
              }
            }
          ]
        }
      },
      eventTypeEnum: [],
      ElSelect_dataType: {
        value: 2,
        size: "mini",
        style: {
          width: "140px"
        },
        event: {
          change: this.ElSelect_dataType_change_out
        }
      },
      // dataId组件
      ElOption_dataType: {
        options_in: [
          {
            id: 1,
            text: "SEMI-F47曲线"
          },
          {
            id: 2,
            text: "ITIC曲线"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      showDataType: false
    };
  },
  methods: {
    // 查询曲线切换
    ElSelect_dataType_change_out(val) {
      this.init();
      this.$emit("ElSelect_value", this.ElSelect_dataType.value);
    },
    // 加载枚举数据
    loadEnumrations() {
      this.eventTypeEnum = this.$store.state.enumerations.pqvariationeventtype;
    },
    formatEnum(value) {
      const enumObj = this._.find(this.eventTypeEnum, ["id", value]);
      return enumObj.text || "--";
    },

    // 将数据分类 放到ITIC图表中
    setChartData(data) {
      const A = [];
      const B = [];
      const C = [];
      data.forEach(item => {
        if (item.toleranceband === 2)
          A.push([item.duration / 1000 / 1000, item.magnitude, item]);
        else if (item.toleranceband === 3)
          B.push([item.duration / 1000 / 1000, item.magnitude, item]);
        else if (item.toleranceband === 4)
          C.push([item.duration / 1000 / 1000, item.magnitude, item]);
      });

      this.CetChart2_ITIC.config.options.series[0].data = A;
      this.CetChart2_ITIC.config.options.series[1].data = B;
      this.CetChart2_ITIC.config.options.series[2].data = C;
    },
    getQueryParams(pic = []) {
      let node = null;
      if (this.currentNode && this.currentNode.data) {
        node = {
          id: this.currentNode.data.id,
          modelLabel: this.currentNode.data.modelLabel
        };
      }
      return {
        startTime: this.queryTime.startTime,
        endTime: this.queryTime.endTime,
        // 被检测设备类型
        // deviceTypes: ["linesegmentwithswitch"],
        // 事件等级
        // "eventClasses": [],
        // 高级查询中的事件类型
        eventTypes: [],
        // 搜索关键字
        keyWord: "",
        // 电能质量类型的监测设备 必传，目前只有 9  // 2019年11月20日17:07:06
        // meterTypes: [9],
        // pictures: pic,
        // 选中节点树中的测点 monitoredid
        node: node,
        page: {
          index: 0,
          limit: 9999
        },
        // 高级查询中的区域
        toleranceBands: [],
        transientFaultDirections: [],
        projectId: this.projectId
      };
    },
    //查询ITIC图表事件
    getVariationEvent() {
      common.requestData(
        {
          url: "/eem-service/v1/pq/event",
          data: this.getQueryParams()
        },
        (data, res) => {
          this.setChartData(data);
          // this.eventStatistics = res;
        }
      );
    },
    //查询SEMI-F47图表事件
    getSEMIEvent() {
      common.requestData(
        {
          url: "/eem-service/v1/pq/event/pq/event",
          data: this.getQueryParams()
        },
        data => {
          data = data || [];
          this.filSEMIData(data);
        },
        () => {
          this.CetChart_top.options.series[1].data = [];
        }
      );
    },
    filSEMIData(data) {
      var dataArr = [];
      // 对数轴x轴不能为0
      data.forEach((item, index) => {
        if (Number(item.duration)) {
          dataArr.push({
            value: [
              Number(item.duration / 1000 / 1000),
              Number(item.magnitude).toFixed2(2)
            ],
            itemStyle: {},
            sortCode: index + 1,
            ...item
          });
        }
      });
      if (dataArr.length > 0) {
        dataArr[0].itemStyle = {
          color: "red"
        };
      }
      this.CetChart_top.options.series[1].data = dataArr;
      this.CetChart_top_click_red(dataArr[0]);
    },
    CetChart_top_click(val) {
      const clickItem = this._.get(val, "data", {});
      this.CetChart_top_click_red(clickItem);
    },
    CetChart_top_click_red(val) {
      if (!val) {
        return;
      }
      // 设置当前点标红
      var data;
      this.CetChart_top.options.series[1].data.forEach(item => {
        item.itemStyle = {};
        if (item.id == val.id && item.duration == val.duration) {
          data = item;
        }
      });
      //如果SEMI-F47曲线没有找到对应点，不需要进行标红
      if (!data) {
        return;
      }
      data.itemStyle = {
        color: "red"
      };
    },
    // 查询所有曲线
    getChartCurveAll() {
      customApi.queryEnum({ rootLabel: "SARFICurveType" }).then(response => {
        var SARFICurveId = response.data.filter(item => item.text == "SEMI")[0]
          .id;
        customApi
          .transienteventanalysisChartCurve({
            curveType: SARFICurveId,
            projectId: this.projectId
          })
          .then(res => {
            if (res.code === 0 && res.data && res.data.length > 0) {
              var chartData = res.data[0];
              if (
                chartData.sarfichartvalue_model &&
                chartData.sarfichartvalue_model.length > 0
              ) {
                var dataArr = [];
                chartData.sarfichartvalue_model.forEach((item, index) => {
                  if (item.duration) {
                    dataArr.push([
                      item.duration / 1000,
                      Number(item.magnitude).toFixed2(2)
                    ]);
                  }
                });
                this.CetChart_top.options.series[0].data = dataArr;
              }
            }
          });
      });
    },
    init() {
      if (this.ElSelect_dataType.value == 1) {
        this.getSEMIEvent();
        this.getChartCurveAll();
      } else {
        this.getVariationEvent();
      }
    }
  },
  created() {
    this.loadEnumrations();
    const allNavmenu = this.$store.state.initialInfo.menuList;
    let item = find(allNavmenu, "cloud_transienteventanalysis", {
      childKey: "subMenuList",
      valueKey: "permission"
    });
    if (!item) {
      item = find(allNavmenu, "dfrc_transienteventanalysis", {
        childKey: "subMenuList",
        valueKey: "permission"
      });
    }
    if (!item) {
      item = find(allNavmenu, "oil_transienteventanalysis", {
        childKey: "subMenuList",
        valueKey: "permission"
      });
    }
    if (item) {
      this.showDataType = true;
    }
  }
};
</script>
<style lang="scss" scoped>
.square {
  height: 18px;
  width: 18px;
  display: inline-block;
  border-radius: 5px;
  line-height: 20px;
  font-size: 0;
  position: relative;
  top: 4px;
  margin-right: 10px;
}
</style>
