//自定义接口命名规范, 项目名_模块名_接口名 例:eem_device_getWaterDevice
//api内部的分组文件夹命名和自定义接口命名保持一致, 便于查找
//获取枚举

//关联关系
import * as deviceRelation from "./deviceRelation";
//拓扑配置页面
import * as topologyConfig from "./topologyConfig";
//单位转换配置
import * as unitTransition from "./unitTransition";
//用户管理
import * as userFunc from "./userFunc";
//用户组管理
import * as userGroupFunc from "./userGroupFunc";
//角色管理
import * as roleFunc from "./roleFunc";
//获取报表节点
import * as report from "./report";
//获取实时画面节点
import * as graph from "./graph";
//视频与文档权限
import * as videoAndDocPermission from "./videoAndDocPermission";
//对标管理
import * as benchmarking from "./benchmarking";
//能效实际与对标配置页面
import * as efficiencyBenchmarkingManage from "./efficiencyBenchmarkingManage";
//能量平衡表
import * as energyBalance from "./energyBalance";
//自定义项目配置页面
import * as cloudProjectConfig from "./cloudProjectConfig";
import * as deviceFunc from "./deviceFunc";
import * as knowledge from "./knowledge";

//文档在线管理
import * as knowledgeFunc from "./knowledgeFunc";

//电能质量部分
import * as nodeTree from "./nodeTree";
import * as Demandsideresponse from "./Demandsideresponse";
import * as IndicatorOverview from "./IndicatorOverview";

//能耗查询与分析
import * as energyQueryAndAnalysis from "./energyQueryAndAnalysis";
//计量网络图
import * as measureTopologyChart from "./measureTopologyChart";
//仪表台账信息
import * as sandingBok from "./sandingBok";
import * as file from "./file";
//能耗数据录入
import * as energyEntry from "./energyEntry";
import * as dataImport from "./dataImport";
//费率管理
import * as costAnalysis from "./costAnalysis";
//维度配置与属性关联
import * as dimFunc from "./dimFunc";

// 事件处理和事件查询相关接口
import * as eventFunc from "./eventFunc";
// 事件统计页面
import * as eventstatistics from "./eventstatistics";
//关联关系
import * as associatRelation from "./associatRelation";
//项目配置弹框
import * as cloudMonitor from "./cloudMonitorApi";
//项目配置页面
import * as projectConfig from "./projectConfig";
//设备管理页面
import * as equipManage from "./equipManage";
//告警实时诊断（收敛）
import * as eventDiagByConvergenceFunc from "./eventDiagByConvergenceFunc";

//交接班
import * as signinManage from "./signinManage";
import * as xjOrder from "./xjOrder";
//签到点平面图
import * as signinMonitor from "./signinMonitor";
//维保工单管理
import * as maintenance from "./maintenance";
//维修工单管理
import * as repairorder from "./repairorder";
//损耗管理
import * as energyLossManagement from "./energyLossManagement";
// 统计周期配置
import * as cycleconfig from "./cycleconfig";
//需量监测
import * as demandmonitor from "./demandmonitor.js";
//电费优化
import * as powerFeeSaving from "./powerFeeSaving";
// 视频配置
import * as videoConfig from "./videoConfig.js";
// 暂态事件分析
import * as transienteventanalysis from "./transienteventanalysis.js";
// 虚拟表计配置
import * as virtualMeter from "./virtualMeter.js";
import * as modelFun from "./baseApi/model.js";
import queryMessage from "./messageCenter/query.js";
// flink数据录入
import * as meterDataEntry from "./meterDataEntry.js";
// 聚合计算配置
import * as physicalQuantityConfig from "./physicalQuantityConfig.js";
// 生产计划
import * as productionPlan from "./productionPlan.js";

export default {
  ...deviceRelation,
  ...energyEntry,
  ...dataImport,
  ...topologyConfig,
  ...unitTransition,
  ...userFunc,
  ...userGroupFunc,
  ...roleFunc,
  ...report,
  ...graph,
  ...videoAndDocPermission,
  ...benchmarking,
  ...efficiencyBenchmarkingManage,
  ...energyBalance,
  ...cloudProjectConfig,
  ...deviceFunc,
  ...knowledge,
  ...nodeTree,
  ...Demandsideresponse,
  ...IndicatorOverview,
  ...knowledgeFunc,
  ...energyQueryAndAnalysis,
  ...measureTopologyChart,
  ...sandingBok,
  ...file,
  ...costAnalysis,
  ...dimFunc,
  ...eventFunc,
  ...eventstatistics,
  ...associatRelation,
  ...cloudMonitor,
  ...projectConfig,
  ...equipManage,
  ...eventDiagByConvergenceFunc,
  ...signinManage,
  ...xjOrder,
  ...signinMonitor,
  ...maintenance,
  ...repairorder,
  ...energyLossManagement,
  ...videoConfig,
  ...demandmonitor,
  ...cycleconfig,
  ...powerFeeSaving,
  ...transienteventanalysis,
  ...meterDataEntry,
  ...virtualMeter,
  ...modelFun,
  ...physicalQuantityConfig,
  ...productionPlan,
  queryMessage
};
