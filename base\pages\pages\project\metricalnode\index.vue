<template>
  <div class="page eem-common eem-metrical-node">
    <div class="fullheight flex-column">
      <div class="flex-auto flex-row">
        <div class="fullheight flex-column eem-aside" style="width: 315px">
          <div class="mbJ3">
            <customElSelect
              :prefix_in="$T('模型类型')"
              v-model="district.id"
              class="mbJ1 custom-select-tag"
              multiple
              collapse-tags
              @remove-tag="removeModel($event, district)"
              @visible-change="handleModel($event, district)"
            >
              <el-option
                v-for="item in district.options"
                :key="item.label"
                :label="item.name"
                :value="item.label"
              ></el-option>
            </customElSelect>
            <customElSelect
              :prefix_in="$T('维度属性')"
              v-model="detailList.id"
              :class="[
                'mbJ1',
                'custom-select-tag',
                en ? 'custom-select-tag-english' : ''
              ]"
              multiple
              collapse-tags
              @remove-tag="removeDim($event, detailList)"
              @visible-change="handleDim($event, detailList)"
            >
              <el-option
                v-for="item in detailList.options"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </customElSelect>
            <ElInput
              v-model="ElInput_keyword.value"
              v-bind="ElInput_keyword"
              v-on="ElInput_keyword.event"
            ></ElInput>
          </div>
          <div class="flex-auto">
            <CetGiantTree
              v-bind="CetGiantTree_1"
              v-on="CetGiantTree_1.event"
            ></CetGiantTree>
          </div>
        </div>
        <div class="flex-auto mlJ3 fullheight flex-column eem-container">
          <div class="mbJ2 clearfix">
            <el-tooltip effect="light" :content="nodeNameM" placement="bottom">
              <span class="common-title-H1 text-ellipsis title-w50">
                {{ nodeNameM }}
              </span>
            </el-tooltip>
            <div class="fr">
              <el-button
                type="primary"
                class="fr mlJ1"
                v-show="!isEdit"
                @click="handleExport"
              >
                {{ $T("导出") }}
              </el-button>
              <el-button
                plain
                type="primary"
                class="fr mlJ1"
                v-show="!isEdit"
                @click="configureNode"
              >
                {{ $T("配置节点") }}
              </el-button>
              <el-button
                type="primary"
                class="fr mlJ1"
                v-show="isEdit"
                @click="preserveTable"
              >
                {{ $T("保存") }}
              </el-button>
              <el-button
                plain
                type="primary"
                class="fr mlJ1"
                v-show="isEdit"
                @click="cancelEdit"
              >
                {{ $T("取消") }}
              </el-button>
              <CustomElDatePicker
                v-show="isEdit && isShowDate"
                class="fr"
                :style="{ width: en ? '300px' : '200px' }"
                :prefix_in="$T('生效时间')"
                v-model="searchTime"
                type="date"
                :clearable="false"
                :placeholder="$T('选择日期')"
                @change="timeChange"
              />
            </div>
          </div>
          <div class="flex-auto relative">
            <div class="fullheight table-border">
              <div
                id="metrical_node_table"
                :class="{ hide_handsontable_operation: !isEdit }"
              ></div>
            </div>
            <div class="window-table__empty-block" v-if="isTabEmpty">
              <div class="window-table__empty-text">{{ $T("暂无数据") }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      class="dialog medium"
      :title="metricalNodeName"
      :visible.sync="showMetricalNodeWind"
      :close-on-click-modal="false"
    >
      <MetricalNodeWindow
        @closeDialog="closeDialog"
        :dimIds="detailList.id"
        :rowData="rowData"
      ></MetricalNodeWindow>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import MetricalNodeWindow from "./dialog/MetricalNodeWindow";
import { httping } from "@omega/http";
export default {
  name: "MetricalNode",
  components: { MetricalNodeWindow },
  props: { mountedTag: Number },
  computed: {
    token() {
      return this.$store.state.token;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    },
    hotLanguage() {
      if (window.localStorage.getItem("omega_language") === "en") {
        return "en-US";
      }
      return "zh-CN";
    }
  },

  data() {
    return {
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.closeDialog
        }
      },
      loading: false,
      treeData: [], //左侧树的数据
      currentNode: null,
      // keyword组件
      ElInput_keyword: {
        value: "",
        "suffix-icon": "el-icon-search",
        placeholder: $T("输入关键字以检索"),
        style: {},
        event: {
          change: this.ElInput_keyword_change_out
        }
      },
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        showFilter: false,
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out
        }
      },
      nodeNameM: "", //维度属性列表名称
      searchTime: new Date(), //生效时间
      isEdit: false,
      //表格配置
      tableHeaders: [],
      tableColumns: [],
      tableColumns11: [],
      tableData: [],
      oldTableData: [],
      oldHeaders: [],
      oldRows: [],
      //计量节点弹框
      showMetricalNodeWind: false,
      rowData: null, //查看的数据
      metricalNodeName: "",
      isTabEmpty: false,
      district: {
        options: [],
        id: [],
        oldId: []
      },
      detailList: {
        options: [],
        id: [],
        oldId: []
      },
      isShowDate: true
    };
  },
  watch: {
    tableData: {
      handler: function () {
        // this.isTabEmpty = arr.length === 0 ? true : false;
      },
      immediate: true
    }
  },

  methods: {
    //输入关键字进行请求接口过滤节点树
    ElInput_keyword_change_out() {
      this.getTreeData();
    },
    // 节点点击事件
    CetGiantTree_1_currentNode_out(val) {
      if (this.isEdit) {
        this.$message.warning($T("请先保存关联计量节点修改！"));
        return;
      }
      if (this.hot) {
        this.hot.updateSettings({ filters: false });
      }
      this.currentNode = this._.cloneDeep(val);
      this.nodeNameM = val.name;
      this.getMetricalNodeList();
    },
    //改变生效时间
    timeChange: function () {},
    //点击配置节点按钮
    configureNode: function () {
      var _this = this;
      _this.toEditTable();
      _this.isEdit = true;
    },

    toEditTable: function () {
      var _this = this;
      var dataObject = _this.tableData;
      //屏蔽最后一列模型Label
      // _this.tableColumns.pop();
      var hotSettings = {
        data: dataObject,
        columns: _this.tableColumns,
        readOnly: false,
        stretchH: "all",
        autoWrapRow: true,
        maxRows: dataObject.length,
        // rowHeaders: true,
        colHeaders: _this.tableHeaders,
        dropdownMenu: true, //下拉式菜单
        filters: true, //过滤器
        // manualRowResize: true, //自定义行宽
        // manualColumnResize: true  //自定义列高
        fillHandle: "vertical", //设置自定填充垂直方向
        language: this.hotLanguage
      };
      _this.hot.updateSettings(hotSettings);
    },
    // 点击保存按钮
    preserveTable: function () {
      var _this = this;
      _this.addOrUpdateNode();
    },
    init() {
      // 进入页面触发
      this.isEdit = false;
      this.district.id = [];
      this.district.oldId = [];
      this.detailList.id = [];
      this.detailList.oldId = [];
      this.currentNode = null;
      this.loadDistrictNodes();
      this.getDimensionMsg();
    },
    // 获取分区列表
    loadDistrictNodes: function () {
      let options = [
        {
          name: $T("用能设备"),
          label: "manuequipment"
        }
      ];
      let nameObj = {
        manuequipment: $T("用能设备"),
        room: $T("房间"),
        floor: $T("楼层"),
        building: $T("楼栋"),
        sectionarea: $T("分区")
      };
      if (this.systemCfg.metricalNodeModelList) {
        options = this.systemCfg.metricalNodeModelList.map(item => {
          return {
            name: nameObj[item.label],
            label: item.label
          };
        });
      }
      this.district.options = options;
      this.district.id = [this._.get(options, "[0].label", null)];
      // _this.getDimensionMsg();
    },
    // 删除模型类型
    removeModel: function (tag, obj) {
      this.getTreeData();
      obj.oldId = obj.id;
    },
    //模型类型选择变化
    handleModel: function (bool, obj) {
      if (!bool) {
        var oldArr = obj.oldId;
        var newArr = obj.id;
        if (newArr.sort().join() !== oldArr.sort().join()) {
          this.getTreeData();
        }
        obj.oldId = obj.id;
      }
    },
    // 删除维度属性
    removeDim: function (tag, obj) {
      this.getMetricalNodeList();
      obj.oldId = obj.id;
    },
    //维度属性选项有变化
    handleDim: function (bool, obj) {
      if (!bool) {
        var oldArr = obj.oldId;
        var newArr = obj.id;
        if (newArr.sort().join() !== oldArr.sort().join()) {
          this.getMetricalNodeList();
        }
        obj.oldId = obj.id;
      }
    },
    //获取维度属性列表信息
    getDimensionMsg: function () {
      var _this = this;
      var auth = _this.token; //身份验证
      httping({
        url: "/eem-service/v1/dim/setting/detailList",
        method: "GET",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          const resData = res.data || [];
          _this.filDimensionData(resData);
        }
      });
    },
    //更新维度属性下拉框列表
    filDimensionData: function (val) {
      var _this = this;
      var list = val || [];
      this.detailList.options = list;
      this.detailList.id = [this._.get(list, "[0].id", "")];
      _this.$nextTick(function () {
        _this.getTreeData();
      });
    },
    //获取左侧节点树数据
    getTreeData() {
      var _this = this; //身份验证
      this.loading = false;
      const params = {
        dimIds: this.detailList.id,
        keyWord: this.ElInput_keyword.value,
        modelLabels: this.district.id
      };
      _this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      customApi.queryDimTree(params).then(res => {
        if (res.code === 0) {
          var resData = res.data || [];
          _this.CetGiantTree_1.inputData_in = resData;
          if (!_this.currentNode) {
            _this.CetGiantTree_1.selectNode =
              _this.CetGiantTree_1.inputData_in[0];
          } else {
            _this.CetGiantTree_1.selectNode = _this.currentNode;
          }
        }
      });
    },
    //获取计量节点列表信息
    getMetricalNodeList: function () {
      this.isEdit = false;
      var _this = this;
      if (this.loading) {
        return;
      }
      this.loading = true;
      let sectionarea = this.district.id.filter(
          i => !["sectionarea"].includes(i)
        ),
        building = this.district.id.filter(
          i => !["sectionarea", "building"].includes(i)
        ),
        floor = this.district.id.filter(
          i => !["sectionarea", "building", "floor"].includes(i)
        ),
        room = this.district.id.filter(
          i => !["sectionarea", "building", "floor", "room"].includes(i)
        ),
        children =
          this.currentNode.children && this.currentNode.children.length;
      let districtObj = {
        project: this.district.id,
        sectionarea:
          sectionarea.length && children ? sectionarea : ["sectionarea"],
        building: building.length && children ? building : ["building"],
        floor: floor.length && children ? floor : ["floor"],
        room: room.length && children ? room : ["room"],
        manuequipment: ["manuequipment"]
      };
      const params = {
        dimIds: this.detailList.id,
        modelLabels: districtObj[this.currentNode.modelLabel],
        id: this.currentNode.id,
        modelLabel: this.currentNode.modelLabel
      };
      customApi.queryDimMetricalproperties(params).then(res => {
        if (res.code === 0) {
          var resData = res.data || [];
          _this.filTableMsg(resData);
        }
        this.loading = false;
      });
    },
    //过滤表格配置信息
    filTableMsg: function (data) {
      var _this = this;
      var headers = data.headers;
      var rows = data.rows;
      _this.oldHeaders = headers;
      _this.oldRows = rows;
      _this.tableHeaders = _this.filTableHeader(headers);
      _this.tableColumns = _this.filTableColumns(headers);
      _this.tableColumns11 = _this.filTableColumns11(headers);
      _this.tableData = _this.filTableData(_this.tableColumns, rows);
      _this.oldTableData = _this.filTableData(_this.tableColumns, rows);

      _this.getMetricalNode();
      var len = _this.tableData.length;
      if (len === 0) {
        this.isTabEmpty = true;
      } else {
        this.isTabEmpty = false;
      }
    },
    //过滤表格头部名称
    filTableHeader: function (list) {
      list = list || [];
      var len = list.length;
      var arr = [];
      //屏蔽最后一列模型Label
      for (var i = 0; i < len; i++) {
        var headerName = list[i].headerName;
        arr.push(headerName);
      }
      arr.push($T("操作"));
      return arr;
    },
    // 过滤表格配置
    filTableColumns: function (list) {
      var _this = this;
      list = list || [];
      var len = list.length;
      var arr = [];
      for (var i = 0; i < len; i++) {
        var obj = {};
        if (i == 0) {
          obj = {
            data: "metricalnode",
            type: "text",
            className: "htLeft",
            readOnly: true
          };
        } else if (i == 1) {
          obj = {
            data: "id",
            type: "numeric",
            // width: 40,
            className: "htLeft",
            readOnly: true
          };
        } else if (i == 2) {
          obj = {
            data: "levelObj" + i,
            type: "text",
            className: "htLeft",
            readOnly: true
          };
        } else {
          obj = {
            // data: 'siteArea',
            type: "autocomplete",
            // source:['1F-中','2F','3F','4F','5F','...'],
            strict: true, //值为true，严格匹配
            allowInvalid: true
          };
          obj.data = "levelObj" + i;
          obj.source = _this.filDropDownList(list[i].dropDownList);
        }
        arr.push(obj);
      }

      return arr;
    },
    // 过滤表格配置
    filTableColumns11: function (list) {
      list = list || [];
      var len = list.length;
      var arr = [];
      for (var i = 0; i < len; i++) {
        var obj = {};
        if (i == 0) {
          obj = {
            data: "metricalnode",
            type: "text",
            className: "htLeft",
            readOnly: true
          };
        } else if (i == 1) {
          obj = {
            data: "id",
            type: "numeric",
            // width: 40,
            className: "htLeft",
            readOnly: true
          };
        } else if (i == 2) {
          obj = {
            data: "levelObj" + i,
            type: "text",
            className: "htLeft",
            readOnly: true
          };
        } else {
          obj = {
            data: "levelObj" + i,
            type: "text",
            className: "htLeft"
          };
        }
        arr.push(obj);
      }
      return arr;
    },
    filDropDownList: function (list) {
      list = list || [];
      var len = list.length;
      var arr = [];
      for (var i = 0; i < len; i++) {
        var name = list[i].name;
        arr.push(name);
      }
      return arr;
    },
    // 过滤表格数据
    filTableData: function (list, data) {
      list = list || [];
      data = data || [];
      var len = data.length;
      var arr = [];
      for (var i = 0; i < len; i++) {
        var leng = data[i].length;
        var obj = {};
        for (var j = 0; j < leng; j++) {
          obj[list[j].data] = data[i][j] || "";
          if (j === leng - 1) {
            obj.nodeLabel = data[i][j] || "";
          }
        }
        obj.historyMsg = "";
        arr.push(obj);
      }
      return arr;
    },
    //保存表格数据
    addOrUpdateNode: function () {
      var _this = this;
      var auth = _this.token; //身份验证

      var params = _this.getPreserveParams();
      if (!params) {
        return;
      }

      httping({
        url: "/eem-service/v1/dim/setting/nodeProperty",
        data: params,
        method: "PUT",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          _this.searchTime = new Date();
          _this.isEdit = false;
          _this.$message.success($T("保存成功！"));
          // 修改节点，更改全局状态，刷新首页
          _this.$store.commit("refreIndex", new Date());
          _this.getMetricalNodeList();
        }
      });
    },
    //获取保存表格数据接口参数
    getPreserveParams: function () {
      var _this = this;
      var oldHeaders = _this.oldHeaders;
      var list = _this.hot.getSourceDataArray();
      var oldList = _this.oldRows;
      var len = list.length;
      var params = [];
      //   enableTime = _this.searchTime ? _this.getEnableTime(_this.searchTime) : null;
      // if(!enableTime){
      //   _this.$message.warning("请选择生效时间！");
      //   return;
      // }
      for (var i = 0; i < len; i++) {
        var arr = list[i];
        var nodeId = arr[1] || 0;
        var modelName = arr[2] || "";
        var oldArr = _this.getOldArr(nodeId, modelName, oldList);
        var leng = arr.length;
        var isOk = true;
        var num = 0;
        var num1 = 0;
        let objectLabel = "";
        const modeleList = this.district.options || [];
        modeleList.forEach(item => {
          if (item.name === modelName) {
            objectLabel = item.label;
          }
        });
        for (var j = 3; j < leng - 1; j++) {
          if (!isOk && arr[j]) {
            num1 += 1;
            isOk = true;
          }
          if (!arr[j]) {
            num += 1;
            isOk = false;
          }
          if (!nodeId) {
            continue;
          }
          if (!modelName) {
            continue;
          }
          if (arr[j] != oldArr[j]) {
            var isEdit = _this.filHasProperty(
              arr[j],
              oldHeaders[j].dropDownList
            );
            if (isEdit) {
              var obj = {};
              obj.label = modelName;
              obj.objectLabel = objectLabel;
              obj.id = 0;
              obj.modelid = Number(nodeId);
              obj.tagid = _this.filPropertyId(
                arr[j],
                oldHeaders[j].dropDownList
              );
              obj.levelid = oldHeaders[j].levelId;
              obj.enabletime = new Date("1900-01-01 00:00:00.000").getTime();
              obj.createtime = new Date().getTime();
              if (_this.isShowDate) {
                obj.enabletime = _this
                  .$moment(_this.searchTime)
                  .startOf("d")
                  .valueOf();
              }

              params.push(obj);
              const editNode = {
                id: Number(nodeId),
                modelLabel: objectLabel,
                name: arr[0]
              };
              const isRepeatTag = this.isRepeatTag(editNode, j, arr[j]);
              if (isRepeatTag) {
                return false;
              }
            } else {
              _this.$message.warning($T("请选择正确标签！"));
              return false;
            }
          }
        }
        // if (num > 0 && (num != leng-3) && num1 > 0) {
        // 	_this.$message.warning('请按顺序进行绑定标签！');
        // 	return false
        // }
      }
      return params;
    },
    getCreateTime: function () {
      var date = new Date();
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      var d = date.getDate();
      if (m < 10) {
        m = "0" + m;
      }
      if (d < 10) {
        d = "0" + d;
      }
      // return y + '-' + m + '-' + d + ' 00:00:00.000'
      return "1900-01-01 00:00:00.000";
    },
    getEnableTime: function (date) {
      date = new Date(date);
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      var d = date.getDate();
      if (m < 10) {
        m = "0" + m;
      }
      if (d < 10) {
        d = "0" + d;
      }
      return y + "-" + m + "-" + d + " 00:00:00.000";
    },
    getOldArr: function (nodeId, modelName, list) {
      var len = list.length;
      var arr = [];
      for (var i = 0; i < len; i++) {
        var oldNodeId = list[i][1];
        var oldModelName = list[i][2];
        if (nodeId == oldNodeId && modelName == oldModelName) {
          arr = list[i];
          //屏蔽最后一列模型Label
          // arr.pop();
          return arr;
        }
      }
      return [];
    },
    //判断输入是否是下拉框标签
    filHasProperty: function (fil, list) {
      list = list || [];
      var len = list.length;
      if (fil === "") {
        return true;
      }
      for (var i = 0; i < len; i++) {
        if (fil == list[i].name) {
          return true;
        }
      }
      return false;
    },
    //将标签名称转为Id
    filPropertyId: function (fil, list) {
      fil = fil || "";
      list = list || [];
      var len = list.length;
      for (var i = 0; i < len; i++) {
        if (fil == list[i].name) {
          return list[i].id;
        }
      }
      return 0;
    },
    //配置计量节点表格
    getMetricalNode: function () {
      var _this = this;
      var dataObject = _this.tableData;
      var tableBtnRender = function (
        instance,
        td,
        row,
        col,
        prop,
        value,
        cellProperties
      ) {
        //添加自定义的图片，并给图片的chick添加事件
        var text;

        text = document.createElement("span");
        text.innerHTML = $T("历史记录");
        text.className = "handsontableHandle";
        // text.width = 20;
        text.style = "cursor:pointer;width:100px;"; //鼠标移上去变手型
        Handsontable.dom.addEvent(text, "click", function () {
          var row_index = cellProperties.row;
          _this.metricalNodeName = _this.tableData[row_index].metricalnode;
          _this.rowData = _this.tableData[row_index];
          _this.showHierachyWind();
        });

        Handsontable.dom.empty(td);
        td.appendChild(text);
        td.style.textAlign = "left"; //图片居中对齐
        return td;
      };
      var hotElement = document.querySelector("#metrical_node_table");
      //屏蔽最后一列模型Label
      // _this.tableColumns11.pop();
      var historyMsg = {
        data: "historyMsg",
        renderer: tableBtnRender,
        width: "100px",
        readOnly: true
      };
      _this.isShowDate = false;
      //通过SystemCfg配置显示操作历史记录列显示
      if (!this.systemCfg.hideHistoryBtn) {
        _this.tableColumns11.push(historyMsg);
        _this.isShowDate = true;
      }
      var hotSettings = {
        data: dataObject,
        columns: _this.tableColumns11,
        readOnly: true,
        stretchH: "all",
        autoWrapRow: true,
        height: "100%",
        maxRows: dataObject.length,
        rowHeaders: true,
        colHeaders: _this.tableHeaders,
        dropdownMenu: true, //下拉式菜单
        filters: true, //过滤器
        fillHandle: false, //设置不能自定填充
        language: this.hotLanguage
      };
      if (_this.hot) {
        _this.toPreserveTable();
      } else {
        _this.hot = new Handsontable(hotElement, hotSettings);
      }
    },
    //配置表格不可编辑
    toPreserveTable: function () {
      var _this = this;
      var dataObject = _this.tableData;
      var hotSettings = {
        data: dataObject,
        columns: _this.tableColumns11,
        readOnly: true,
        stretchH: "all",
        autoWrapRow: true,
        maxRows: dataObject.length,
        // rowHeaders: true,
        colHeaders: _this.tableHeaders,
        dropdownMenu: true, //下拉式菜单
        filters: true, //过滤器
        fillHandle: false, //设置不能自定填充
        language: this.hotLanguage
      };
      _this.hot.updateSettings(hotSettings);
    },
    //点击导出按钮
    handleExport: function () {
      var _this = this;
      var name = _this.nodeNameM + "-" + $T("关联计量节点");
      var exportPlugin = _this.hot.getPlugin("exportFile");
      var len = _this.tableHeaders.length - 2;
      exportPlugin.downloadFile("csv", {
        filename: name,
        columnHeaders: true,
        range: [, 0, , len]
      });
    },
    //点击取消编辑记录
    cancelEdit: function () {
      this.isEdit = false;
      this.searchTime = new Date();
      this.getMetricalNodeList();
    },
    //弹出计量节点历史弹框
    showHierachyWind: function () {
      this.showMetricalNodeWind = true;
    },
    // 关闭dialog
    closeDialog: function () {
      this.showMetricalNodeWind = false;
      this.getMetricalNodeList();
    },
    /**
     * 通过选中的节点id逆推该节点的父级关系
     * @data 节点树数据
     * @tree_id 选中的节点id
     * @indexArray 存储父级关系
     */
    findIndexArray(data, node, indexArray) {
      let arr = Array.from(indexArray);
      for (let i = 0, len = data.length; i < len; i++) {
        arr.push(data[i]);
        if (data[i].id === node.id && data[i].modelLabel === node.modelLabel) {
          this.getNodeChildren(data[i], arr);
          return arr;
        }
        let children = data[i].children;
        if (children && children.length) {
          let result = this.findIndexArray(children, node, arr);
          if (result) return result;
        }
        arr.pop();
      }
      return false;
    },
    //获取节点下面所以子节点列表，补充在列表后面
    getNodeChildren(node, arr) {
      const data = node.children || [];
      for (let i = 0, len = data.length; i < len; i++) {
        arr.push(data[i]);
        let children = data[i].children;
        if (children && children.length) {
          this.getNodeChildren(data[i], arr);
        }
      }
    },
    //判断保存标签是否存在父节点重复
    isRepeatTag(node, index, tagName) {
      let isOk = false;
      //如果是删除标签，不需要判断重复
      if (!tagName) {
        return isOk;
      }
      const oldList = this.oldRows;
      const lastIndex = oldList[0] ? oldList[0].length : 0;
      const fathNodeList = this.findIndexArray(
        this.CetGiantTree_1.inputData_in,
        node,
        []
      );
      var list = this.hot.getSourceDataArray();
      for (let i = 0; i < fathNodeList.length; i++) {
        const fathNode = fathNodeList[i];
        if (
          node.id === fathNode.id &&
          node.modelLabel === fathNode.modelLabel
        ) {
          continue;
        }
        for (let j = 0; j < list.length; j++) {
          if (
            Number(list[j][1]) === fathNode.id &&
            list[j][lastIndex - 1] === fathNode.modelLabel
          ) {
            if (list[j][index] === tagName) {
              isOk = true;
              this.$message.warning(
                $T(
                  "{0}节点和{1}节点设置重复[{2}]标签",
                  node.name,
                  list[j][0],
                  tagName
                )
              );
              return isOk;
            }
          }
        }
      }
      return isOk;
    }
  },
  created: function () {},
  mounted() {
    if (this.mountedTag) {
      this.init();
    }
  },
  activated() {
    if (!this.mountedTag) {
      this.init();
    }
  },
  deactivated: function () {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.eem-metrical-node {
  overflow: auto;
  height: 100%;
}
.custom-select-tag :deep(.el-select__tags-text) {
  float: left;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 80px;
}
.custom-select-tag-english :deep(.el-select__tags-text) {
  max-width: 28px;
}
#metrical_node_table {
  @include background_color(BG1);
  @include font_color(T1);
}
#metrical_node_table :deep(table thead tr th) {
  height: 30px;
  line-height: 30px;
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}
#metrical_node_table :deep(.handsontable th) {
  text-align: left;
}

#metrical_node_table :deep(table tbody tr th) {
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}

#metrical_node_table :deep(table tbody tr td) {
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}

#metrical_node_table :deep(table thead tr th .changeType) {
  margin-top: 8px;
}
#metrical_node_table :deep(table thead tr th .colHeader) {
  font-weight: 700;
}
#metrical_node_table :deep(.handsontable .changeType) {
  @include font_color(T1);
  @include background_color(BG1);
  @include border_color(B1);
}
#metrical_node_table :deep(.handsontable .changeType:hover) {
  @include font_color(T1);
  @include background_color(BG1);
  @include border_color(B1);
}
#metrical_node_table :deep(.handsontableHandle) {
  @include font_color(ZS);
}

.window-table__empty-block {
  position: absolute;
  top: 145px;
  overflow: hidden;
  display: block;
  width: calc(100% - 20px);
}
.window-table__empty-text {
  text-align: center;
  width: 100%;
  height: 40px;
  line-height: 40px;
}
.eem-metrical-node .table-border {
  border: 1px solid;
  @include border_color(B1);
  border-top: none;
}
.title-w50 {
  max-width: 50%;
}
.dialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
    box-sizing: border-box;
  }
}
</style>

<style>
.eem-metrical-node #hot-display-license-info {
  display: none;
}

.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(1) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(2) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(3) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(4) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(5) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(6) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(7) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(8) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(9) {
  display: none;
}
.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody
  > tr:nth-child(10) {
  display: none;
}
.hide_handsontable_operation
  .ht_clone_top
  .wtHolder
  .wtHider
  .wtSpreader
  .htCore
  thead
  tr
  th:nth-last-child(1)
  .changeType {
  display: none;
}
</style>

<style lang="scss">
.handsontable .ht_master table td.htCustomMenuRenderer {
  @include background_color(BG);
}
.htFiltersConditionsMenu table tbody tr td {
  @include background_color(BG1);
}
.htDropdownMenu table tbody tr td {
  @include background_color(BG1);
}
.handsontable .htUIMultipleSelect .handsontable .htCore td:hover {
  @include background_color(BG2);
}
.handsontable .htUISelectCaption {
  @include background_color(BG4);
}
</style>
