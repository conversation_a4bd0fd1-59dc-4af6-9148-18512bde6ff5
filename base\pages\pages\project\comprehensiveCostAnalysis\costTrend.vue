<template>
  <div class="page eem-common">
    <el-container
      style="padding: 0px; height: calc(100% - 2px); flex-direction: column"
    >
      <!-- 综合成本趋势 -->
      <div class="flex-column eem-container" style="flex: 5">
        <headerSpot class="mbJ3">
          {{ $T("综合成本趋势") }}
        </headerSpot>
        <div style="overflow-x: auto" class="flex-auto chartBox">
          <CetChart v-bind="CetChart_comprehensiveCost"></CetChart>
        </div>
      </div>
      <div style="flex: 4" class="proportion fullwidth">
        <div class="fullheight flex-row" style="min-height: 230px">
          <div class="flex-auto fullheight eem-container flex-column">
            <headerSpot class="mbJ3">
              {{ $T("综合成本概览") }}
            </headerSpot>
            <div class="proportion-item avg-cost flex-auto flex-column">
              <div class="cost-item mbJ1">
                <span>{{ $T("时间") }}:</span>
                &emsp;
                <span>{{ selectTime }}</span>
              </div>
              <div class="cost-item mbJ1">
                <span>{{ $T("总用能成本") }}:</span>
                &emsp;
                <span>
                  {{ formatNumberWithPrecision(totalCost.totalValue, 2) }}
                </span>
                <span>{{ totalCost.unit }}</span>
              </div>
              <div class="compare flex-auto">
                <el-row
                  :gutter="20"
                  class="fullheight"
                  style="
                    display: flex;
                    justify-content: center;
                    position: relative;
                  "
                >
                  <span class="fs16 preText tip">{{ $T("同比") }}</span>
                  <el-col
                    :span="12"
                    class="fullheight"
                    style="position: relative"
                  >
                    <CetChart
                      v-bind="CetChart_avgcost1"
                      style="position: absolute; top: 0; left: 0"
                    ></CetChart>
                    <div class="label">
                      <el-tooltip
                        :content="
                          _.isEmpty(tbLabel) ? '' : tbLabel.price + tbLabel.unit
                        "
                        effect="light"
                      >
                        <div class="price text-ellipsis">
                          <span class="fsH3">{{ tbLabel.price }}</span>
                        </div>
                      </el-tooltip>
                      <div class="percent">
                        <el-tooltip :content="tbLabel.percent" effect="light">
                          <span class="text-ellipsis font12">
                            {{ tbLabel.percent }}
                          </span>
                        </el-tooltip>
                        <img :src="tbLabel.src" v-if="tbLabel.src" alt="" />
                      </div>
                    </div>
                  </el-col>
                  <el-col
                    :span="12"
                    class="fullheight"
                    style="position: relative"
                    v-if="queryTime.cycle === 14"
                  >
                    <span class="fs16 lastText tip">{{ $T("环比") }}</span>
                    <CetChart
                      v-bind="CetChart_avgcost2"
                      style="position: absolute; top: 0; left: 0"
                    ></CetChart>
                    <div class="label">
                      <el-tooltip
                        :content="
                          _.isEmpty(hbLabel) ? '' : hbLabel.price + hbLabel.unit
                        "
                        effect="light"
                      >
                        <div class="price text-ellipsis">
                          <span class="fsH3">{{ hbLabel.price }}</span>
                        </div>
                      </el-tooltip>
                      <div class="percent">
                        <el-tooltip :content="hbLabel.percent" effect="light">
                          <span class="text-ellipsis font12">
                            {{ hbLabel.percent }}
                          </span>
                        </el-tooltip>
                        <img :src="hbLabel.src" v-if="hbLabel.src" alt="" />
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
          <div class="flex-auto mlJ3 mrJ3 fullheight eem-container flex-column">
            <headerSpot class="mbJ3">
              {{ $T("分类成本占比") }}
            </headerSpot>
            <div class="proportion-item flex-auto flex-column">
              <div class="cost-item mbJ1">
                <span>{{ $T("时间") }}:</span>
                &emsp;
                <span>{{ selectTime }}</span>
              </div>
              <div class="classify flex-auto">
                <CetChart
                  v-if="CetChart_energyCost.options.series[0].data.length"
                  v-bind="CetChart_energyCost"
                ></CetChart>
                <div class="itemBox-empty" v-else>{{ $T("暂无数据") }}</div>
              </div>
            </div>
          </div>
          <div class="flex-auto fullheight eem-container flex-column">
            <headerSpot class="mbJ3">
              {{ $T("区域成本占比") }}
            </headerSpot>
            <div
              class="proportion-item flex-auto flex-column"
              style="margin-right: 0"
            >
              <div class="cost-item mbJ1">
                <span>{{ $T("时间") }}:</span>
                &emsp;
                <span>{{ selectTime }}</span>
              </div>
              <div class="classify flex-auto">
                <CetChart
                  v-if="CetChart_areaCost.options.series[0].data.length"
                  v-bind="CetChart_areaCost"
                ></CetChart>
                <div class="itemBox-empty" v-else>{{ $T("暂无数据") }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-container>
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom";

export default {
  name: "costTrend",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },
  props: {
    selectTime: {
      type: String
    },
    currentNode: {
      type: Object
    },
    queryTime: {
      type: Object
    }
  },
  data() {
    return {
      energyList: [], // 过滤energytype=13后的能源类型列表
      // comprehensiveCost组件
      CetChart_comprehensiveCost: {
        //组件输入项
        inputData_in: null,
        options: {}
      },
      totalCost: {
        totalValue: null, // 总用能成本
        unit: "" // 单位
      },
      tbLabel: {}, // 同比数据
      hbLabel: {}, // 环比数据
      // 同比
      CetChart_avgcost1: {
        //组件输入项
        inputData_in: null,
        options: {
          series: [
            {
              name: $T("同比"),
              type: "pie",
              radius: ["65%", "85%"],
              avoidLabelOverlap: false,
              emphasis: {
                scale: false
              },
              label: {
                position: "center",
                rich: {
                  a: {
                    color: "#00fff9",
                    fontSize: 24
                  },
                  b: {
                    color: "#00fff9"
                  },
                  c: {
                    fontSize: 16,
                    color: "#fff"
                  },
                  d: {
                    width: 14,
                    backgroundColor: {
                      image: "/static/assets/icons/arrow_up.png"
                    }
                  }
                }
              },
              labelLine: {
                show: false
              },
              data: []
            }
          ]
        }
      },
      // 环比
      CetChart_avgcost2: {
        //组件输入项
        inputData_in: null,
        options: {
          series: [
            {
              name: $T("同比"),
              type: "pie",
              radius: ["65%", "85%"],
              avoidLabelOverlap: false,
              emphasis: {
                scale: false
              },
              label: {
                position: "center",
                rich: {
                  a: {
                    color: "#00fff9",
                    fontSize: 24
                  },
                  b: {
                    color: "#00fff9"
                  },
                  c: {
                    fontSize: 16,
                    color: "#fff"
                  },
                  d: {
                    width: 14,
                    backgroundColor: {
                      image: "/static/assets/icons/arrow_up.png"
                    }
                  }
                }
              },
              labelLine: {
                show: false
              },
              data: []
            }
          ]
        }
      },
      // 分类成本占比
      CetChart_energyCost: {
        //组件输入项
        inputData_in: null,
        options: {
          legend: {
            type: "scroll",
            left: 10,
            bottom: -5
          },
          tooltip: {
            trigger: "item",
            formatter(params) {
              return (
                params.name +
                ": " +
                Number(params.value).toFixed(2) +
                (params.data.unit || "") +
                "(" +
                Number(params.percent).toFixed(2) +
                "%)"
              );
            }
          },
          series: [
            {
              name: "",
              type: "pie",
              radius: "65%",
              center: ["50%", "40%"],
              label: {
                show: true,
                textStyle: {
                  fontSize: 12
                },
                formatter(params) {
                  return (
                    params.name + ": " + Number(params.percent).toFixed(2) + "%"
                  );
                }
              },
              data: []
            }
          ]
        }
      },
      // 区域成本占比
      CetChart_areaCost: {
        //组件输入项
        inputData_in: null,
        options: {
          legend: {
            type: "scroll",
            left: 10,
            bottom: -5
          },
          tooltip: {
            trigger: "item",
            formatter(params) {
              return (
                params.name +
                ": " +
                Number(params.value).toFixed(2) +
                (params.data.unit || "") +
                "(" +
                Number(params.percent).toFixed(2) +
                "%)"
              );
            }
          },
          series: [
            {
              name: "",
              type: "pie",
              radius: "65%",
              center: ["50%", "40%"],
              label: {
                show: true,
                textStyle: {
                  fontSize: 12
                },
                formatter(params) {
                  return (
                    params.name + ": " + Number(params.percent).toFixed(2) + "%"
                  );
                }
              },
              data: []
            }
          ]
        }
      }
    };
  },
  watch: {
    currentNode(val) {
      this.getAllData();
    },
    queryTime(val) {
      this.getAllData();
    }
  },
  methods: {
    formatNumberWithPrecision: common.formatNumberWithPrecision,
    async getProjectEnergy() {
      this.CetChart_comprehensiveCost.options = {};
      this.CetChart_energyCost.options.series[0].data = [];
      try {
        // 过滤折标煤
        await customApi.getProjectEnergy(this.projectId).then(res => {
          const list = res.data.filter(
            item => item.energytype !== 13 && item.energytype !== 22
          );
          this.energyList = this._.cloneDeep(list);
        });
        this.getAllData();
      } catch (err) {
        console.log(err);
      }
    },
    getAllData() {
      if (!this.currentNode || this._.isEmpty(this.queryTime)) return;
      this.getComprehensiveTrend();
      this.getComprehensiveCost();
      this.getClassifyCostPercent();
      this.getAreaCost();
    },
    // 综合成本趋势tooltip格式化
    formatTooltip(params, unit) {
      if (!params[0].data.logtime) return;
      const cycle = this.queryTime.cycle;
      const formatStr =
        cycle === 14 ? "YYYY-MM-DD" : cycle === 17 ? "YYYY-MM" : "";
      let str =
        this.$moment(params[0].data.logtime).format(formatStr) + "<br />";
      params.forEach(item => {
        str +=
          item.marker +
          item.seriesName +
          ": " +
          (item.data.value || item.data.value === 0
            ? Number(item.data.value).toFixed(2)
            : "--") +
          (unit || "") +
          "<br />";
      });
      return str;
    },
    //过滤获取图表x轴对应值
    getAxixs(date, type) {
      if (type === 14) {
        return this.$moment(date).format("DD");
      } else if (type === 17) {
        return this.$moment(date).format("YYYY/MM");
      }
    },
    formatterDate(cellValue, formatStr = "YYYY-MM-DD HH:mm:ss") {
      if (cellValue) {
        return this.$moment(cellValue).format(formatStr);
      } else if (cellValue === 0 || cellValue === "") {
        return cellValue;
      } else {
        return "--";
      }
    },
    // 综合成本图例综合成本、同比、环比增加悬浮提示
    formatLegend(name, startTime, endTime) {
      const cycle = this.queryTime.cycle;
      let str = name + ":";
      const monthStr = this.language ? "YYYY-MM" : "YYYY年MM月";
      const dayStr = this.language ? "YYYY-MM-DD" : "YYYY年MM月DD日";
      const formatStr = cycle === 17 ? monthStr : dayStr;
      const unit = cycle === 17 ? "M" : "d";
      if (name === $T("综合成本")) {
        str +=
          this.formatterDate(startTime, formatStr) +
          "~" +
          this.formatterDate(this.$moment(endTime).add(1, unit), formatStr);
      }
      if (name === $T("同比")) {
        str +=
          this.formatterDate(
            this.$moment(startTime).subtract(1, "Y"),
            formatStr
          ) +
          "~" +
          this.formatterDate(
            this.$moment(endTime).add(1, unit).subtract(1, "Y"),
            formatStr
          );
      }
      if (name === $T("环比")) {
        str +=
          this.formatterDate(
            this.$moment(startTime).subtract(1, "M"),
            formatStr
          ) +
          "~" +
          this.formatterDate(startTime, formatStr);
      }
      return str;
    },
    // 综合成本趋势
    getComprehensiveTrend() {
      const _this = this;
      const params = {
        ids: [this.currentNode.id],
        modelLabel: this.currentNode.modelLabel,
        energyTypes: [13],
        ...this.queryTime
      };
      customApi.queryComprehensiveTrend(params).then(res => {
        if (
          res.code === 0 &&
          res.data &&
          res.data.nowData &&
          res.data.nowData.length
        ) {
          // 处理x轴
          const xAxisData = [];
          // 自然周期和非自然周期返回的开始时间和结束时间
          const startTime = res.data.nowData[0].logtime;
          const endTime = res.data.nowData[res.data.nowData.length - 1].logtime;
          res.data.nowData.forEach(item => {
            xAxisData.push(this.getAxixs(item.logtime, this.queryTime.cycle));
          });
          if (this.queryTime.cycle === 14) {
            this.CetChart_comprehensiveCost.options = {
              toolbox: {
                top: 30,
                right: 30,
                feature: {
                  saveAsImage: {
                    title: $T("保存为图片")
                  }
                }
              },
              tooltip: {
                trigger: "axis",
                formatter(params) {
                  return _this.formatTooltip(params, res.data.unitName);
                }
              },
              legend: {
                top: 10,
                tooltip: {
                  show: true,
                  formatter(params) {
                    return _this.formatLegend(params.name, startTime, endTime);
                  }
                }
              },
              grid: {
                left: "3%",
                right: "4%",
                bottom: "3%",
                containLabel: true
              },
              xAxis: {
                type: "category",
                name: $T("天数"),
                nameLocation: "end",
                data: xAxisData,
                axisPointer: {
                  type: "shadow"
                }
              },
              yAxis: {
                type: "value",
                name: res.data.unitName,
                nameTextStyle: {
                  align: "right"
                }
              },
              series: [
                {
                  name: $T("综合成本"),
                  type: "bar",
                  barMaxWidth: 30,
                  data: res.data.nowData
                },
                {
                  name: $T("同比"),
                  type: "line",
                  data: res.data.yoyData,
                  smooth: true
                },
                {
                  name: $T("环比"),
                  type: "line",
                  data: res.data.chainData,
                  smooth: true
                }
              ]
            };
          } else {
            this.CetChart_comprehensiveCost.options = {
              tooltip: {
                trigger: "axis",
                formatter(params) {
                  return _this.formatTooltip(params, res.data.unitName);
                }
              },
              legend: {
                top: 10,
                tooltip: {
                  show: true,
                  formatter(params) {
                    return _this.formatLegend(params.name, startTime, endTime);
                  }
                }
              },
              grid: {
                left: "3%",
                right: "4%",
                bottom: "3%",
                containLabel: true
              },
              xAxis: {
                type: "category",
                name: $T("月份"),
                nameLocation: "end",
                data: xAxisData,
                axisPointer: {
                  type: "shadow"
                }
              },
              yAxis: {
                type: "value",
                name: res.data.unitName,
                nameTextStyle: {
                  align: "right"
                }
              },
              series: [
                {
                  name: $T("综合成本"),
                  type: "bar",
                  barMaxWidth: 30,
                  data: res.data.nowData
                },
                {
                  name: $T("同比"),
                  type: "line",
                  data: res.data.yoyData,
                  smooth: true
                }
              ]
            };
          }
        }
      });
    },
    // 综合成本概览
    getComprehensiveCost() {
      const nodes = [
        {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          name: this.currentNode.name
        }
      ];
      const params = {
        nodes,
        energyType: 13,
        queryType:
          this.queryTime.cycle === 17 ? 1 : this.queryTime.cycle === 14 ? 3 : 0,
        costKpiType: 0,
        projectId: this.projectId,
        ...this.queryTime
      };
      customApi.queryComprehensiveCost(params).then(res => {
        if (res.code === 0) {
          const mainData = res.data[0].data[0] || {};
          // 总用能成本
          this.totalCost = {
            totalValue: mainData.value,
            unit: res.data[0].unitName
          };
          // 同比
          const tbRate = Math.abs(mainData.yearOnYear);
          if (tbRate > 1) {
            this.CetChart_avgcost1.options.series[0].data = [1];
          } else {
            this.CetChart_avgcost1.options.series[0].data = [
              tbRate,
              1 - tbRate
            ];
          }
          this.tbLabel = {
            price:
              this.formatNumberWithPrecision(mainData.yearOnYearCost, 2) ||
              "--",
            percent:
              mainData.yearOnYear || mainData.yearOnYear === 0
                ? this.formatNumberWithPrecision(
                    Math.abs(mainData.yearOnYear) * 100,
                    2
                  ) + "%"
                : "--",
            src:
              mainData.yearOnYear > 0
                ? require("./assets/arrow_up.png")
                : mainData.yearOnYear < 0
                ? require("./assets/arrow_down.png")
                : "",
            unit: res.data[0].unitName
          };

          // 环比
          const hbRate = Math.abs(mainData.chain);
          if (hbRate > 1) {
            this.CetChart_avgcost2.options.series[0].data = [1];
          } else {
            this.CetChart_avgcost2.options.series[0].data = [
              hbRate,
              1 - hbRate
            ];
          }
          this.hbLabel = {
            price:
              this.formatNumberWithPrecision(mainData.chainCost, 2) || "--",
            percent:
              mainData.chain || mainData.chain === 0
                ? this.formatNumberWithPrecision(
                    Math.abs(mainData.chain) * 100,
                    2
                  ) + "%"
                : "--",
            src:
              mainData.chain > 0
                ? require("./assets/arrow_up.png")
                : mainData.chain < 0
                ? require("./assets/arrow_down.png")
                : "",
            unit: res.data[0].unitName
          };
        }
      });
    },
    // 分类成本占比
    getClassifyCostPercent() {
      const energyTypes = this.energyList.map(item => item.energytype);
      const data = {
        modelLabel: this.currentNode.modelLabel,
        ids: [this.currentNode.id],
        energyTypes,
        dimTagIds: null,
        ...this.queryTime
      };
      this.CetChart_energyCost.options.series[0].data = [];
      customApi.queryEnergyCostValue(data).then(res => {
        if (res.code === 0) {
          let seriesData = [];
          let resData = res.data || [];
          resData.forEach(energyItem => {
            const target = this.energyList.find(
              item => item.energytype === energyItem.energytype
            );
            if (energyItem.value !== null) {
              seriesData.push({
                value: this.formatNumberWithPrecision(energyItem.value, 2),
                name: target && target.name,
                energyId: energyItem.energytype,
                unit: energyItem.unitName
              });
            }
          });
          this.CetChart_energyCost.options.series[0].data =
            this._.cloneDeep(seriesData);
        }
      });
    },
    // 区域成本占比
    getAreaCost() {
      let nodes = [];
      if (
        this.currentNode.children &&
        Array.isArray(this.currentNode.children)
      ) {
        nodes = this.currentNode.children.map(item => {
          return {
            id: item.id,
            modelLabel: item.modelLabel,
            name: item.name
          };
        });
      } else {
        // 没有子节点时显示本身节点100%
        nodes = [
          {
            id: this.currentNode.id,
            modelLabel: this.currentNode.modelLabel,
            name: this.currentNode.name
          }
        ];
      }
      const params = {
        nodes,
        energyType: 13,
        queryType:
          this.queryTime.cycle === 17 ? 1 : this.queryTime.cycle === 14 ? 3 : 0,
        costKpiType: 0,
        projectId: this.projectId,
        ...this.queryTime
      };
      this.CetChart_areaCost.options.series[0].data = [];
      customApi.queryComprehensiveCost(params).then(res => {
        if (res.code === 0) {
          let seriesData = [];
          let resData = res.data || [];
          resData.forEach(item => {
            const itemData = item.data[0] || {};
            if (itemData.value !== null) {
              seriesData.push({
                value: this.formatNumberWithPrecision(itemData.value, 2),
                name: item.objectName,
                unit: item.unitName
              });
            }
          });
          this.CetChart_areaCost.options.series[0].data =
            this._.cloneDeep(seriesData);
        }
      });
    }
  },
  created() {
    this.getProjectEnergy();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .chartBox {
    min-height: 300px;
  }
  .proportion {
    @include margin_top(J3);
    min-height: 300px;
    .proportion-item {
      position: relative;
      .cost-item {
        text-align: center;
        // padding: 2px 0;
        height: 28px;
        line-height: 28px;
        @include font_color(T8);
        @include background(BG6);
        span {
          font-size: 16px;
        }
      }
    }
    .avg-cost {
      padding-bottom: 0;
      .compare {
        .tip {
          position: absolute;
          top: 10px;
          left: 10px;
          z-index: 99;
          padding: 2px 20px 2px 2px;
        }
        .preText {
          @include font_color(T8);
          @include background(BG6);
        }
        .lastText {
          @include font_color(T8);
          @include background(BG6);
        }
        .label {
          width: 50%;
          height: 50px;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;
          .price {
            // color: #00fff9;
          }
          .percent {
            span {
              display: inline-block;
              max-width: calc(100% - 20px);
            }
            img {
              width: 14px;
            }
          }
        }
      }
    }
    .classify {
      .itemBox-empty {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
      }
    }
  }
}
</style>
