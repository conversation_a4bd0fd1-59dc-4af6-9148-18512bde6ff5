<template>
  <div class="page eem-common">
    <el-container class="fullheight flex-column">
      <el-header height="auto" class="lh32 padding0 mbJ3">
        <el-tooltip effect="light" :content="nodeName" placement="top-start">
          <div class="common-title-H2 text-ellipsis" style="width: 250px">
            {{ nodeName || "--" }}
          </div>
        </el-tooltip>
      </el-header>
      <el-container class="flex-auto eem-container eem-min-width-mini">
        <el-header height="auto" class="lh32 padding0 mbJ3">
          <div class="fr">
            <customElSelect
              v-model="ElSelect_1.value"
              v-bind="ElSelect_1"
              v-on="ElSelect_1.event"
              class="fl"
              prefix_in="能源类型"
            >
              <ElOption
                v-for="item in ElOption_1.options_in"
                :key="item[ElOption_1.key]"
                :label="item[ElOption_1.label]"
                :value="item[ElOption_1.value]"
                :disabled="item[ElOption_1.disabled]"
              ></ElOption>
            </customElSelect>
            <CustomDatePicker
              class="fl mlJ1"
              @change="CustomDatePicker_1_change"
              :val="CustomDatePicker_1.queryTime"
              :dataConfig="CustomDatePicker_1.dataConfig"
            ></CustomDatePicker>
          </div>
        </el-header>
        <el-main class="padding0">
          <el-row :gutter="0" style="height: 100%; margin: 0px">
            <el-col
              :span="24"
              class="energyIndex"
              style="height: 100%"
              v-for="(item, index) in energyIndexList"
              :key="index"
            >
              <div class="card-style">
                <div class="mbJ3">
                  <span class="common-title-H3 fl">
                    {{ item.title || "--" }}
                  </span>
                  <!-- <el-button
                    class="fr mr-10"
                    type="danger"
                    plain
                    @click="CetButton_delete_statusTrigger_out(item.efSet)"
                  >
                    删除
                  </el-button>
                  <el-button
                    class="fr mr-10"
                    type="primary"
                    plain
                    @click="
                      CetButton_edit_statusTrigger_out(
                        item.efSet,
                        item.lineList
                      )
                    "
                  >
                    编辑对标
                  </el-button>
                  <el-button
                    class="fr mr-10"
                    type="primary"
                    plain
                    @click="
                      CetButton_edit_statusTrigger_out11(
                        item.efSet,
                        item.lineList
                      )
                    "
                  >
                    编辑能效指标
                  </el-button> -->
                </div>
                <div style="height: calc(100% - 50px)">
                  <CetChart
                    :inputData_in="item.chart.inputData_in"
                    v-bind="item.chart.config"
                  />
                </div>
              </div>
            </el-col>
          </el-row>
        </el-main>
      </el-container>
      <!-- </el-container> -->
    </el-container>
    <!-- <Slide></Slide> -->
    <BenchmarkingManage
      :visibleTrigger_in="BenchmarkingManage.visibleTrigger_in"
      :closeTrigger_in="BenchmarkingManage.closeTrigger_in"
      :queryId_in="BenchmarkingManage.queryId_in"
      :inputData_in="BenchmarkingManage.inputData_in"
      :treeData_in="BenchmarkingManage.treeData_in"
      :benchmarkset_in="benchmarkset"
      :energyefficiencyset_in="energyefficiencyset"
      :isEditBenchmark_in="isEditBenchmark"
      @finishTrigger_out="BenchmarkingManage_finishTrigger_out"
      @finishData_out="BenchmarkingManage_finishData_out"
      @saveData_out="BenchmarkingManage_saveData_out"
      @currentData_out="BenchmarkingManage_currentData_out"
    />
    <EditEnergyefficiencyIndexes
      :visibleTrigger_in="EditEnergyefficiencyIndexes.visibleTrigger_in"
      :closeTrigger_in="EditEnergyefficiencyIndexes.closeTrigger_in"
      :queryId_in="EditEnergyefficiencyIndexes.queryId_in"
      :inputData_in="EditEnergyefficiencyIndexes.inputData_in"
      :treeData_in="EditEnergyefficiencyIndexes.treeData_in"
      :benchmarkset_in="benchmarkset"
      :energyefficiencyset_in="energyefficiencyset"
      :isEditBenchmark_in="isEditBenchmark"
      @finishTrigger_out="EditEnergyefficiencyIndexes_finishTrigger_out"
      @finishData_out="EditEnergyefficiencyIndexes_finishData_out"
      @saveData_out="EditEnergyefficiencyIndexes_saveData_out"
      @currentData_out="EditEnergyefficiencyIndexes_currentData_out"
    />
  </div>
</template>
<script>
import common from "eem-utils/common";
import CustomDatePicker from "../CustomDatePicker2.vue";
import BenchmarkingManage from "./dialog/BenchmarkingManage.vue";
import EditEnergyefficiencyIndexes from "./dialog/EditEnergyefficiencyIndexes.vue";
import { httping } from "@omega/http";
export default {
  name: "EnergyEfficiencyIndexes",
  components: {
    CustomDatePicker,
    BenchmarkingManage,
    EditEnergyefficiencyIndexes
  },
  props: {
    clickNode: {
      type: Object
    },
    clickProjectNode_in: {
      type: Object
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      let vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      let vm = this;
      let projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          let storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },

  data() {
    return {
      BenchmarkingManage: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        treeData_in: null
      },
      EditEnergyefficiencyIndexes: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        treeData_in: null
      },

      CustomDatePicker_1: {
        queryTime: {
          startTime: null,
          endTime: null,
          cycle: 12 //17年，14月，12日，20自定义
        },
        dataConfig: {
          time: null,
          cycle: 1,
          showPicker: true,
          showRange: true,
          type: [
            {
              id: 1,
              text: "日",
              type: "date"
            },
            {
              id: 3,
              text: "月",
              type: "month"
            },
            {
              id: 5,
              text: "年",
              type: "year"
            }
          ]
        }
      },
      ElSelect_1: {
        value: null,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },

      CetButton_edit: {
        visible_in: true,
        disable_in: false,
        config: {
          title: "编辑",
          type: "primary",
          plain: true
        }
      },
      energyIndexList: [],
      benchmarkset: [],
      benchmarkset1: [],
      benchmarkset2: [],
      benchmarkset3: [],
      energyefficiencyset: {},
      isEditBenchmark: true,
      nodeName: "",
      efSetList: [],
      benchmarksetList: []
    };
  },
  watch: {
    clickNode: {
      deep: true,
      handler: function (val, oldVal) {
        if (!(this._.get(val, "id") && this._.get(val, "modelLabel"))) return;
        if (
          val.id === this._.get(oldVal, "id") &&
          val.modelLabel === this._.get(oldVal, "modelLabel")
        ) {
          return;
        }
        this.nodeName = val.name;
        this.getEfSet();
      }
    },
    clickProjectNode_in: {
      deep: true,
      handler: function (val, oldVal) {}
    }
  },

  methods: {
    BenchmarkingManage_currentData_out(val) {},
    BenchmarkingManage_finishData_out(val) {},
    BenchmarkingManage_finishTrigger_out(val) {},
    BenchmarkingManage_saveData_out(val) {
      this.getEfSet();
    },
    EditEnergyefficiencyIndexes_currentData_out(val) {},
    EditEnergyefficiencyIndexes_finishData_out(val) {},
    EditEnergyefficiencyIndexes_finishTrigger_out(val) {},
    EditEnergyefficiencyIndexes_saveData_out(val) {
      this.getEfSet();
    },
    // 编辑对标
    CetButton_edit_statusTrigger_out(val, list) {
      this.energyefficiencyset = this._.cloneDeep(val);
      this.benchmarkset = this._.cloneDeep(list);
      // if (!val || val.length === 0) {
      //   this.$message.warning("没有对标进行编辑！");
      //   return;
      // }
      this.BenchmarkingManage.inputData_in = {};
      this.BenchmarkingManage.visibleTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    // 编辑能效指标
    CetButton_edit_statusTrigger_out11(val, list) {
      this.energyefficiencyset = this._.cloneDeep(val);
      this.benchmarkset = this._.cloneDeep(list);
      this.EditEnergyefficiencyIndexes.visibleTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    // 删除指标
    CetButton_delete_statusTrigger_out(val) {
      let _this = this,
        auth = _this.token; //身份验证
      let arr = val || [],
        idRange = [];
      // arr.forEach(function(item) {
      // idRange.push(item.id);
      // });
      idRange.push(val.id);
      this.$confirm("是否删除此能效指标?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          httping({
            url: `/eem-service/v1/ef/efSet`,
            data: idRange,
            method: "DELETE",
            timeout: 10000
          }).then(res => {
            if (res.code === 0) {
              _this.$message({
                type: "success",
                message: "删除成功!"
              });
              _this.getEfSet();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },

    ElSelect_1_change_out(val) {
      this.getEfSet();
    },

    //时间控件改变触发
    CustomDatePicker_1_change(val) {
      this.CustomDatePicker_1.queryTime = val;
      // if (this.efSetList && this.efSetList.length === 0) {
      //   return;
      // }
      // this.getChartLine();
      this.getEfSet();
    },
    // 获取图表标准线数据
    getChartLine() {
      let _this = this,
        auth = _this.token; //身份验证
      if (!this.clickNode) {
        return;
      }
      let queryTime = this.CustomDatePicker_1.queryTime;
      let unittype = this._.map(this.efSetList, "unittype");
      let queryBody = {
        nodes: [
          {
            modelLabel: this.clickNode.modelLabel,
            nodes: [
              {
                id: this.clickNode.id,
                name: this.clickNode.name
              }
            ]
          }
        ],
        aggregationCycle: queryTime.cycle,
        energyTypes: [this.ElSelect_1.value],
        unittype: _this.uniq(unittype)
      };
      let queryOption = {
        url: `/eem-service/v1/energy/benchmarkSet`,
        method: "POST",
        data: queryBody,
        timeout: 10000
      };

      httping(queryOption).then(function (response) {
        if (response.code === 0) {
          //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
          console.log(response.data);
          let data = _this._.get(response, ["data"], []);
          _this.benchmarksetList = data;

          _this.getChartData();
        }
      });
    },
    // 获取图表数据
    getChartData() {
      let _this = this,
        auth = _this.token; //身份验证
      if (!this.clickNode) {
        return;
      }
      let queryTime = this.CustomDatePicker_1.queryTime;
      let unittype = this._.map(this.efSetList, "unittype");
      let producttype = this._.map(this.efSetList, "producttype");
      let queryBody = {
        nodes: [
          {
            modelLabel: this.clickNode.modelLabel,
            nodes: [
              {
                id: this.clickNode.id,
                name: this.clickNode.name
              }
            ]
          }
        ],
        startTime: queryTime.startTime,
        endTime: queryTime.endTime,
        aggregationCycle: queryTime.cycle,
        energyTypes: [this.ElSelect_1.value],
        unittype: _this.uniq(unittype),
        producttype: _this.uniq(producttype)
      };
      // if (queryBody.aggregationCycle == 12) {
      //   queryBody.startTime = this.$moment(queryBody.endTime)
      //     .subtract(30, "day")
      //     .valueOf();
      // } else if (queryBody.aggregationCycle == 14) {
      //   queryBody.startTime = this.$moment(queryBody.endTime)
      //     .subtract(12, "month")
      //     .valueOf();
      // } else if (queryBody.aggregationCycle == 17) {
      //   queryBody.startTime = this.$moment(queryBody.endTime)
      //     .subtract(3, "year")
      //     .valueOf();
      // }
      let queryOption = {
        url: `/eem-service/v1/energy/energyEfficiency`,
        method: "POST",
        data: queryBody,
        timeout: 10000
      };

      httping(queryOption).then(function (response) {
        if (response.code === 0) {
          //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
          // console.log(response.data);
          let data = _this._.get(response, ["data"], {});

          _this.filChartData(data);
        }
      });
    },
    //过滤图表数据
    filChartData(data) {
      let _this = this,
        efSetList = this.efSetList || [],
        benchmarksetList = _this.benchmarksetList || [];
      let arr = [];
      let cycle = this.CustomDatePicker_1.queryTime.cycle;
      let producttypeNum1 = 0,
        producttypeNum2 = 0,
        producttypeNum3 = 0;
      for (let item in data) {
        if (item == 1) {
          let clonelist = _this._.cloneDeep(this.efSetList);
          let clonefSetArr = [];

          let efSetArr = clonelist.filter(item11 => {
            return item11.unittype == 1;
          });
          let efSetArr1 = efSetArr.filter(item22 => {
            return item22.producttype == 1;
          });
          let efSetArr2 = efSetArr.filter(item33 => {
            return item33.producttype == 2;
          });
          let efSetArr3 = efSetArr.filter(item44 => {
            return item44.producttype == 3;
          });
          efSetArr1.sort(function (a, b) {
            return b.id - a.id;
          });
          efSetArr2.sort(function (a, b) {
            return b.id - a.id;
          });
          efSetArr3.sort(function (a, b) {
            return b.id - a.id;
          });

          if (efSetArr1[0]) {
            clonefSetArr.push(efSetArr1[0]);
          }
          if (efSetArr2[0]) {
            clonefSetArr.push(efSetArr2[0]);
          }
          if (efSetArr3[0]) {
            clonefSetArr.push(efSetArr3[0]);
          }

          for (let i = 0, len = clonefSetArr.length; i < len; i++) {
            if (clonefSetArr[i].unittype == 1) {
              let title = "";
              let efSet = {};
              title = clonefSetArr[i].name;
              efSet = clonefSetArr[i];

              let colors = ["#72DB72", "#0398D8", "#F0593C"];
              let lineList = [],
                lineList1 = [];
              benchmarksetList.forEach((item2, index) => {
                if (item2.unittype == item) {
                  let obj = {
                    lineStyle: {
                      color: colors[index % 2]
                    },
                    label: {
                      position: "end",
                      formatter: `${item2.name || "--"}(${
                        item2.limitvalue || "--"
                      })`,
                      fontSize: 14
                    },
                    yAxis: item2.limitvalue
                  };
                  lineList.push(obj);
                  lineList1.push(item2);
                }
              });

              let source = [];
              let list = _this._.get(data, [item, "0", "data"], []);
              let list11 = _this._.get(data, ["1"], []);

              list11.forEach(item111 => {
                if (item111.producttype == clonefSetArr[i].producttype) {
                  list = item111.data || [];
                }
              });
              list.forEach(item3 => {
                let value =
                  common.formatNumberWithPrecision(item3.value, 2) || "--";
                let time = this.getAxixs(item3.time, cycle);
                let obj = {
                  time: time,
                  yAxis1: value
                };
                // yAxisData1.push(value);
                // xAxis1.push(time);
                source.push(obj);
              });

              let lineValueList = this._.map(lineList1, "limitvalue"),
                sourValueList = this._.map(source, "yAxis1");
              let lineMaxValue = this._.max(
                  lineValueList.map(i => {
                    if (isNaN(Number(i))) {
                      return 0;
                    } else {
                      return Number(i);
                    }
                  })
                ),
                sourMaxValue = this._.max(
                  sourValueList.map(i => {
                    if (isNaN(Number(i))) {
                      return 0;
                    } else {
                      return Number(i);
                    }
                  })
                );

              let maxData = 0;
              if (lineMaxValue > 0 && sourMaxValue > 0) {
                maxData =
                  lineMaxValue - sourMaxValue > 0 ? lineMaxValue : sourMaxValue;
              } else if (lineMaxValue > 0) {
                maxData = lineMaxValue;
              } else if (sourMaxValue > 0) {
                maxData = sourMaxValue;
              }
              maxData = Math.ceil(maxData * 1.1);
              let dataset = {
                source: source
              };
              const symbol = _this._.get(data, [item, "0", "symbol"]) || "--";
              let obj = {
                title: title,
                efSet: efSet,
                unittype: item,
                lineList: lineList1,
                chart: {
                  inputData_in: null,
                  config: {
                    options: {
                      tooltip: {
                        trigger: "axis",
                        formatter: function (value) {
                          const data = value[0];
                          if (!data) return "";
                          return `${value[0].axisValue}
                              <br>
                              ${value[0].marker}${value[0].seriesName}
                              ${
                                data.data[value[0].dimensionNames[1]]
                              }(${symbol})`;
                        },
                        axisPointer: {
                          type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
                        }
                      },
                      grid: {
                        left: "5%",
                        right: "100",
                        bottom: "3%",
                        containLabel: true
                      },
                      dataset: dataset,
                      xAxis: { type: "category" },
                      yAxis: {
                        max: maxData
                      },
                      series: [
                        {
                          name: title,
                          type: "bar",
                          stack: "",
                          markLine: {
                            symbol: "none",
                            lineStyle: {
                              width: 1,
                              type: "dotted"
                            },
                            data: lineList
                          },
                          encode: { x: "time", y: "yAxis1" }
                        }
                      ]
                    }
                  }
                }
              };
              arr.push(obj);
            }
          }
        } else {
          let title = "";
          let efSet = {};
          efSetList.forEach(item1 => {
            if (item1.unittype == item) {
              title = item1.name;
              efSet = item1;
            }
          });

          let colors = ["#72DB72", "#0398D8", "#F0593C"];
          let lineList = [],
            lineList1 = [];
          benchmarksetList.forEach((item2, index) => {
            if (item2.unittype == item) {
              let obj = {
                lineStyle: {
                  color: colors[index % 2]
                },
                label: {
                  position: "end",
                  formatter: `${item2.name || "--"}(${
                    item2.limitvalue || "--"
                  })`,
                  fontSize: 14
                },
                yAxis: item2.limitvalue
              };
              lineList.push(obj);
              lineList1.push(item2);
            }
          });

          let source = [];
          let list = _this._.get(data, [item, "0", "data"], []);
          list.forEach(item3 => {
            let value =
              common.formatNumberWithPrecision(item3.value, 2) || "--";
            let time = this.getAxixs(item3.time, cycle);
            let obj = {
              time: time,
              yAxis1: value
            };
            // yAxisData1.push(value);
            // xAxis1.push(time);
            source.push(obj);
          });

          let lineValueList = this._.map(lineList1, "limitvalue"),
            sourValueList = this._.map(source, "yAxis1");
          let lineMaxValue = this._.max(
              lineValueList.map(i => {
                if (isNaN(Number(i))) {
                  return 0;
                } else {
                  return Number(i);
                }
              })
            ),
            sourMaxValue = this._.max(
              sourValueList.map(i => {
                if (isNaN(Number(i))) {
                  return 0;
                } else {
                  return Number(i);
                }
              })
            );

          let maxData = 0;
          if (lineMaxValue > 0 && sourMaxValue > 0) {
            maxData =
              lineMaxValue - sourMaxValue > 0 ? lineMaxValue : sourMaxValue;
          } else if (lineMaxValue > 0) {
            maxData = lineMaxValue;
          } else if (sourMaxValue > 0) {
            maxData = sourMaxValue;
          }
          maxData = Math.ceil(maxData * 1.1);
          let dataset = {
            source: source
          };

          const symbol = _this._.get(data, [item, "0", "symbol"]) || "--";
          let obj = {
            title: title,
            efSet: efSet,
            unittype: item,
            lineList: lineList1,
            chart: {
              inputData_in: null,
              config: {
                options: {
                  tooltip: {
                    trigger: "axis",
                    formatter: function (value) {
                      const data = value[0];
                      if (!data) return "";
                      return `${value[0].axisValue}
                              <br>
                              ${value[0].marker}${value[0].seriesName}
                              ${
                                data.data[value[0].dimensionNames[1]]
                              }(${symbol})`;
                    },
                    axisPointer: {
                      type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
                    }
                  },
                  grid: {
                    left: "5%",
                    right: "100",
                    bottom: "3%",
                    containLabel: true
                  },
                  dataset: dataset,
                  xAxis: { type: "category" },
                  yAxis: {
                    max: maxData
                  },
                  series: [
                    {
                      name: title,
                      type: "bar",
                      stack: "",
                      markLine: {
                        symbol: "none",
                        lineStyle: {
                          width: 1,
                          type: "dotted"
                        },
                        data: lineList
                      },
                      encode: { x: "time", y: "yAxis1" }
                    }
                  ]
                }
              }
            }
          };
          arr.push(obj);
        }
      }

      _this.energyIndexList = arr;
    },
    // 过滤x轴对应值
    getAxixs(pdate, type) {
      let date = new Date(pdate),
        y = date.getFullYear(),
        M = date.getMonth() + 1,
        d = date.getDate(),
        h = date.getHours(),
        m = date.getMinutes();
      if (M < 10) {
        M = "0" + M;
      }
      if (d < 10) {
        d = "0" + d;
      }
      if (h < 10) {
        h = "0" + h;
      }
      if (m < 10) {
        m = "0" + m;
      }
      if (type === 12) {
        return M + "-" + d;
        // return h + ":" + m;
      } else if (type === 14) {
        return y + "-" + M;
        // return M + "-" + d;
      } else if (type === 17) {
        return y;
        // return y + "-" + M;
      } else {
        return y + "-" + M + "-" + d;
      }
    },
    //获取能效指标列表
    getEfSet() {
      let _this = this,
        auth = _this.token; //身份验证
      if (!this.clickNode || !this.ElSelect_1.value) {
        return;
      }
      let queryBody = {
        nodes: [
          {
            modelLabel: this.clickNode.modelLabel,
            nodes: [
              {
                id: this.clickNode.id
              }
            ]
          }
        ],
        energyTypes: [this.ElSelect_1.value],
        aggregationCycle: this.CustomDatePicker_1.queryTime.cycle
      };
      let queryOption = {
        url: `/eem-service/v1/energy/efSet`,
        method: "POST",
        data: queryBody,
        timeout: 10000
      };

      httping(queryOption).then(function (response) {
        if (response.code === 0) {
          //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
          // console.log(response.data);
          let data = _this._.get(response, ["data"], []);
          _this.efSetList = data;
          _this.getChartLine();
          // _this.getChartData();
        }
      });
    },
    //对数组进行去重
    uniq(arr) {
      let temp = []; //一个新的临时数组
      for (let i = 0; i < arr.length; i++) {
        if (temp.indexOf(arr[i]) == -1) {
          if (arr[i]) {
            temp.push(arr[i]);
          }
        }
      }
      return temp;
    },
    getProjectEnergy() {
      let _this = this;
      _this.ElOption_1.options_in = [];
      httping({
        url:
          "/eem-service/v1/project/projectEnergy?projectId=" + this.projectId,
        method: "GET"
      }).then(res => {
        if (res.code === 0 && res.data && res.data.length > 0) {
          let selectData = res.data.map(item => {
            return {
              id: item.energytype,
              text: item.name
            };
          });
          let obj = selectData.find(i => i.id === 2);
          _this.ElOption_1.options_in = selectData;
          _this.ElSelect_1.value = obj ? obj.id : selectData[0].id;
          _this.ElSelect_1_change_out(_this.ElSelect_1.value);
        }
      });
    }
  },

  created: function () {
    this.getProjectEnergy();
  },
  mounted: function () {
    this.nodeName = this.clickNode ? this.clickNode.name : null;
    this.getEfSet();
  },
  activated() {
    this.getEfSet();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}

.card-style {
  height: 100%;
  border: 1px solid;
  padding: 10px;
  box-sizing: border-box;
  border-radius: 4px;
  @include border_color(B1);
}
.card-head-title {
  height: 50px;
  line-height: 50px;
  @include font_size(H1);
}

.energyIndex {
  @include margin_bottom(J2);
}
</style>
