<template>
  <div class="page eem-common">
    <div class="fullheight flex-column">
      <div class="flex-auto flex-row">
        <div class="fullheight eem-aside" style="width: 315px">
          <div class="fullfilled flex-column">
            <div class="eem-group-list flex-auto">
              <div
                v-for="(item, key) in groupList"
                :key="key"
                :class="[
                  'group-item',
                  { active: (activeGroup && activeGroup.id) === item.id }
                ]"
                @click="activeGroup = item"
              >
                <el-tooltip :content="item.name" effect="light">
                  <span>{{ item.name }}</span>
                </el-tooltip>
              </div>
            </div>
            <div class="mtJ1">
              <!-- add按钮组件 -->
              <CetButton
                class="fr mlJ1"
                v-permission="'maintenanceitemgroup_create'"
                v-bind="CetButton_add"
                v-on="CetButton_add.event"
              ></CetButton>
              <!-- delete按钮组件 -->
              <CetButton
                class="fr mlJ1"
                v-permission="'maintenanceitemgroup_delete'"
                v-bind="CetButton_delete"
                v-on="CetButton_delete.event"
              ></CetButton>
              <!-- edit按钮组件 -->
              <CetButton
                class="fr mlJ1"
                v-permission="'maintenanceitemgroup_update'"
                v-bind="CetButton_edit"
                v-on="CetButton_edit.event"
              ></CetButton>
            </div>
          </div>
        </div>
        <!-- 巡检对象 -->
        <div class="fullheight flex-auto mlJ3">
          <div class="fullfilled flex-column">
            <div class="mbJ3">
              <span class="common-title-H1">{{ $T("维保项目") }}</span>
            </div>

            <div class="flex-auto eem-container flex-column">
              <div class="mbJ3">
                <el-upload
                  class="elupload"
                  action="/eem-service/v1/maintenance/importItem"
                  :headers="{ Authorization: this.token, projectId: projectId }"
                  :on-success="uploadSuccess"
                  :multiple="false"
                >
                  <button ref="uploadBtn"></button>
                </el-upload>
                <div class="common-title-H2 lh32">
                  {{ $T("共{0}个维保项目", CetTable_inspectObj.data.length) }}
                </div>
                <!-- addProject按钮组件 -->
                <CetButton
                  class="fr"
                  v-bind="CetButton_addProject"
                  v-on="CetButton_addProject.event"
                  v-permission="'maintenanceitem_create'"
                ></CetButton>
                <!-- 批量删除按钮组件 -->
                <CetButton
                  class="fr mrJ1"
                  v-bind="CetButton_muldelete"
                  v-on="CetButton_muldelete.event"
                  v-permission="'maintenanceitem_delete'"
                ></CetButton>
                <CetButton
                  class="fr mrJ1"
                  v-bind="CetButton_import"
                  v-on="CetButton_import.event"
                  v-permission="'maintenanceitem_create'"
                ></CetButton>
                <!-- 导出组件 -->
                <CetButton
                  class="fr mrJ1"
                  v-bind="CetButton_export"
                  v-on="CetButton_export.event"
                ></CetButton>
                <!-- 维保方式管理按钮组件 -->
                <CetButton
                  class="fr mrJ1"
                  v-bind="CetButton_typeAdmin"
                  v-on="CetButton_typeAdmin.event"
                ></CetButton>
              </div>
              <CetTable
                class="flex-auto"
                :data.sync="CetTable_inspectObj.data"
                :dynamicInput.sync="CetTable_inspectObj.dynamicInput"
                v-bind="CetTable_inspectObj"
                v-on="CetTable_inspectObj.event"
                ref="dragTable"
                row-key="id"
                @selection-change="handleSelectionChange_out"
              >
                <ElTableColumn
                  type="selection"
                  width="50"
                  align="center"
                ></ElTableColumn>
                <el-table-column
                  :label="$T('排序')"
                  width="60px"
                  align="center"
                >
                  <template>
                    <i class="el-icon-s-operation"></i>
                  </template>
                </el-table-column>
                <template v-for="item in Columns_inspectObj">
                  <ElTableColumn
                    :key="item.label"
                    v-bind="item"
                  ></ElTableColumn>
                </template>
                <ElTableColumn v-bind="ElTableColumn_operate">
                  <template slot-scope="{ row }">
                    <span
                      class="fl mrJ3 handle"
                      v-permission="'maintenanceitem_update'"
                      @click="handleEdit(row)"
                    >
                      {{ $T("编辑") }}
                    </span>
                    <span
                      class="fl handle delete"
                      @click="handleDelete(row)"
                      v-permission="'maintenanceitem_delete'"
                    >
                      {{ $T("删除") }}
                    </span>
                  </template>
                </ElTableColumn>
              </CetTable>
            </div>
          </div>
        </div>
        <!-- </div> -->
        <!-- </el-container> -->
      </div>
    </div>
    <!-- 新建/编辑分组 -->
    <addGroupDialog
      v-bind="addGroupDialog"
      v-on="addGroupDialog.event"
    ></addGroupDialog>
    <editGroupDialog
      v-bind="editGroupDialog"
      v-on="editGroupDialog.event"
    ></editGroupDialog>
    <addProjectDialog
      v-bind="addProjectDialog"
      v-on="addProjectDialog.event"
    ></addProjectDialog>
    <editProjectDialog
      v-bind="editProjectDialog"
      v-on="editProjectDialog.event"
    ></editProjectDialog>
    <!-- 维保方式 -->
    <typeAdmin v-bind="typeAdmin" v-on="typeAdmin.event"></typeAdmin>
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom.js";
import Sortable from "sortablejs";
import addGroupDialog from "./dialogs/addGroupDialog";
import editGroupDialog from "./dialogs/editGroupDialog";
import addProjectDialog from "./dialogs/addProjectDialog";
import editProjectDialog from "./dialogs/editProjectDialog";
import typeAdmin from "./dialogs/typeAdmin";

export default {
  name: "signinmanage",
  components: {
    addGroupDialog,
    editGroupDialog,
    addProjectDialog,
    editProjectDialog,
    typeAdmin
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  data() {
    return {
      groupList: [], // 分组列表
      // delete组件
      CetButton_delete: {
        visible_in: true,
        disable_in: false,
        title: $T("删除"),
        type: "danger",
        plain: true,
        size: "mini",
        event: {
          statusTrigger_out: this.CetButton_delete_statusTrigger_out
        }
      },
      // edit组件
      CetButton_edit: {
        visible_in: true,
        disable_in: false,
        title: $T("编辑"),
        type: "primary",
        plain: true,
        size: "mini",
        event: {
          statusTrigger_out: this.CetButton_edit_statusTrigger_out
        }
      },
      // add组件
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("新建"),
        type: "primary",
        size: "mini",
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      activeGroup: null,
      // muldelete组件
      CetButton_muldelete: {
        visible_in: true,
        disable_in: true,
        title: $T("批量删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_muldelete_statusTrigger_out
        }
      },
      // inspectObj表格组件
      CetTable_inspectObj: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {
          record_out: this.CetTable_inspectObj_record_out,
          outputData_out: this.CetTable_inspectObj_outputData_out
        }
      },
      Columns_inspectObj: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          //minWidth: "200",  //该宽度会自适应
          width: "60" //绝对宽度
          //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
        },
        {
          prop: "maintenanceTypeName", // 支持path a[0].b
          label: $T("维保方式"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formatter
          // width: "160" //绝对宽度
        },
        {
          prop: "content", // 支持path a[0].b
          label: $T("维保内容"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        },
        {
          prop: "sparePartName", // 支持path a[0].b
          label: $T("零部件类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        },
        {
          prop: "number", // 支持path a[0].b
          label: $T("零部件数量"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: common.formatNumberCol()
        },
        {
          prop: "unit", // 支持path a[0].b
          label: $T("零部件单位"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        }
      ],
      ElTableColumn_operate: {
        //type: "",      // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        fixed: "right",
        showOverflowTooltip: true,
        width: "120" //绝对宽度
      },
      selectedData: [],
      // 新建分组弹窗
      addGroupDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        event: {
          saveData_out: this.addGroupData_out
        }
      },
      // 编辑分组弹窗
      editGroupDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        event: {
          saveData_out: this.editGroupData_out
        }
      },
      // 新建维保项目弹窗
      addProjectDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        groupList: null, // 分组列表
        maintenancetype: null, // 维保方式枚举
        maintenanceGroup: null, // 维保项目组
        event: {
          saveData_out: this.addProjectData_out
        }
      },
      // 编辑维保项目弹窗
      editProjectDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        groupList: null, // 分组列表
        maintenancetype: null, // 维保方式枚举
        maintenanceGroup: null, // 维保项目组
        event: {
          saveData_out: this.editProjectData_out
        }
      },
      // 维保方式新增、编辑、删除
      typeAdmin: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        event: {}
      },
      CetButton_addProject: {
        visible_in: true,
        disable_in: false,
        title: $T("新增"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_addProject_statusTrigger_out
        }
      },
      CetButton_typeAdmin: {
        visible_in: true,
        disable_in: false,
        title: $T("维保方式管理"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_typeAdmin_statusTrigger_out
        }
      },
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      CetButton_import: {
        visible_in: true,
        disable_in: false,
        title: $T("导入"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_import_statusTrigger_out
        }
      }
    };
  },
  watch: {
    activeGroup: {
      deep: true,
      handler(val) {
        if (val) {
          this.CetButton_export.disable_in = false;
          this.CetButton_import.disable_in = false;
          this.CetButton_delete.disable_in = false;
          this.CetButton_edit.disable_in = false;
          this.CetButton_addProject.disable_in = false;
          this.getMaintenanceItem();
          this.addProjectDialog.maintenanceGroup = val;
          this.editProjectDialog.maintenanceGroup = val;
        } else {
          this.CetButton_export.disable_in = true;
          this.CetButton_import.disable_in = true;
          this.CetButton_delete.disable_in = true;
          this.CetButton_edit.disable_in = true;
          this.CetButton_addProject.disable_in = true;
          this.CetTable_inspectObj.data = [];
        }
      }
    }
  },
  methods: {
    CetButton_export_statusTrigger_out() {
      common.downExcel(
        "/eem-service/v1/maintenance/exportItem",
        [this.activeGroup.id],
        this.token
      );
    },
    CetButton_import_statusTrigger_out() {
      this.$refs.uploadBtn.click();
    },
    uploadSuccess(res) {
      if (res.code === 0) {
        this.$message.success($T("导入成功!"));
        this.getGroupList();
        this.getMaintenanceItem();
      } else {
        this.$message.error(res.msg);
      }
    },
    // 删除组
    CetButton_delete_statusTrigger_out() {
      this.$confirm($T("确定要删除所选项吗？"), $T("删除确认"), {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消")
      }).then(() => {
        customApi.deleteMaintenanceGroup([this.activeGroup.id]).then(res => {
          if (res.code === 0) {
            this.$message.success($T("删除成功!"));
            this.activeGroup = null;
            this.getGroupList();
          }
        });
      });
    },
    // 编辑分组
    CetButton_edit_statusTrigger_out() {
      this.editGroupDialog.inputData_in = this.activeGroup;
      this.editGroupDialog.openTrigger_in = new Date().getTime();
    },
    // 新建分组
    CetButton_add_statusTrigger_out() {
      this.addGroupDialog.isAdd = true;
      this.addGroupDialog.openTrigger_in = new Date().getTime();
    },
    // position表格输出
    CetTable_inspectObj_record_out() {},
    CetTable_inspectObj_outputData_out() {},
    formatter(row, column, cellValue) {
      return cellValue || cellValue === 0 ? cellValue : "--";
    },
    formatterMaintenancetype(row, column, cellValue) {
      const target = this.addProjectDialog.maintenancetype.find(
        item => item.id === cellValue
      );
      if (target) {
        return target.text;
      }
    },
    // addProject输出
    CetButton_addProject_statusTrigger_out() {
      this.addProjectDialog.openTrigger_in = new Date().getTime();
    },
    // 维保方式管理
    CetButton_typeAdmin_statusTrigger_out() {
      this.typeAdmin.openTrigger_in = new Date().getTime();
    },
    //表格点击多选框
    handleSelectionChange_out(val) {
      this.selectedData = val;
      if (val && val.length > 0) {
        this.CetButton_muldelete.disable_in = false;
      } else {
        this.CetButton_muldelete.disable_in = true;
      }
    },
    CetButton_muldelete_statusTrigger_out() {
      const idArr = this.selectedData.map(item => item.id);
      this.deleteMaintenance_out(idArr);
    },
    handleEdit(row) {
      this.editProjectDialog.inputData_in = row;
      this.editProjectDialog.openTrigger_in = new Date().getTime();
    },
    // 删除维保项目
    deleteMaintenance_out(params) {
      this.$confirm($T("确定要删除所选项吗？"), $T("删除确认"), {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消")
      }).then(() => {
        customApi.deleteMaintenanceItem(params).then(res => {
          if (res.code === 0) {
            this.$message.success($T("删除成功!"));
            this.getMaintenanceItem();
          }
        });
      });
    },
    handleDelete(row) {
      this.deleteMaintenance_out([row.id]);
    },
    // 获取分组列表
    getGroupList() {
      customApi.queryMaintenanceGroup().then(res => {
        if (res.code === 0) {
          this.groupList = res.data;
          this.addProjectDialog.groupList = res.data;
          this.editProjectDialog.groupList = res.data;
          if (!this.activeGroup) {
            this.activeGroup = this.groupList[0];
          }
          this.activeGroup = this._.cloneDeep(this.activeGroup);
        }
      });
    },
    // 获取分组下的维保项目
    getMaintenanceItem() {
      customApi.queryMaintenanceItem(this.activeGroup.id).then(res => {
        if (res.code === 0) {
          this.CetTable_inspectObj.data = res.data;
        }
      });
    },
    addGroupData_out(val) {
      customApi.addMaintenanceGroup(val).then(res => {
        if (res.code === 0) {
          this.$message.success($T("保存成功!"));
          this.addGroupDialog.closeTrigger_in = new Date().getTime();
          this.getGroupList();
        }
      });
    },
    editGroupData_out(val) {
      customApi.editMaintenanceGroup(val).then(res => {
        if (res.code === 0) {
          this.$message.success($T("保存成功!"));
          this.editGroupDialog.closeTrigger_in = new Date().getTime();
          this.getGroupList();
        }
      });
    },
    addProjectData_out(val) {
      const params = {
        content: val.content,
        maintenanceGroupId: val.maintenanceGroupId,
        maintenancetype: val.maintenancetype
      };
      if (val.number === 0 || val.number) {
        params.number = val.number;
      }
      if (val.sparePartId) {
        params.sparePartId = val.sparePartId;
      }
      customApi.addMaintenanceItem(params).then(res => {
        if (res.code === 0) {
          this.$message.success($T("保存成功!"));
          this.addProjectDialog.closeTrigger_in = new Date().getTime();
          this.getMaintenanceItem();
        }
      });
    },
    editProjectData_out(val) {
      const params = {
        id: val.id,
        content: val.content,
        maintenanceGroupId: val.maintenanceGroupId,
        maintenancetype: val.maintenancetype
      };
      if (val.number === 0 || val.number) {
        params.number = val.number;
      }
      if (val.sparePartId) {
        params.sparePartId = val.sparePartId;
      }
      customApi.editMaintenanceItem(params).then(res => {
        if (res.code === 0) {
          this.$message.success($T("保存成功!"));
          this.editProjectDialog.closeTrigger_in = new Date().getTime();
          this.getMaintenanceItem();
        }
      });
    },
    // 获取维保方式枚举值
    getMaintenanceTypes() {
      this.addProjectDialog.maintenancetype =
        this.$store.state.enumerations.maintenancetype || [];
      this.editProjectDialog.maintenancetype =
        this.$store.state.enumerations.maintenancetype || [];
    },
    // 表格拖拽
    setSort() {
      const el = this.$refs.dragTable.$refs.cetTable.$el.querySelectorAll(
        ".el-table__body-wrapper > table > tbody"
      )[0];
      Sortable.create(el, {
        ghostClass: "sortable-ghost",
        onEnd: evt => {
          const targetRow = this.CetTable_inspectObj.data.splice(
            evt.oldIndex,
            1
          )[0];
          this.CetTable_inspectObj.data.splice(evt.newIndex, 0, targetRow);
          // 排序
          const sortTableData = [];
          this.CetTable_inspectObj.data.forEach((item, index) => {
            const obj = {
              id: item.id,
              sort: index + 1
            };
            sortTableData.push(obj);
          });
          customApi.sortMaintenanceItem(sortTableData).then(res => {
            if (res.code === 0) {
              this.$message.info($T("排序成功"));
            }
          });
          // this.getMaintenanceItem();
        }
      });
    }
  },
  activated() {
    this.getGroupList();
    this.getMaintenanceTypes();
    this.$nextTick(() => {
      this.setSort();
    });
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .elupload {
    display: none;
  }
  .sortable-ghost {
    background: #0e1b47;
  }
}
.handle {
  cursor: pointer;
  @include font_color(ZS);
  &.delete {
    @include font-color(Sta3);
  }
}
</style>
