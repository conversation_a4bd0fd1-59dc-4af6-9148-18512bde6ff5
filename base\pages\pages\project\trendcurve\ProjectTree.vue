<template>
  <div class="treeBox flex-column">
    <customElSelect
      v-show="dimId_in !== 4"
      v-model="ElSelect_1.value"
      v-bind="ElSelect_1"
      v-on="ElSelect_1.event"
      class="mbJ1"
      :prefix_in="$T('能源类型')"
    >
      <ElOption
        v-for="item in ElOption_1.options_in"
        :key="item[ElOption_1.key]"
        :label="item[ElOption_1.label]"
        :value="item[ElOption_1.value]"
        :disabled="item[ElOption_1.disabled]"
      ></ElOption>
    </customElSelect>
    <CetGiantTree
      class="CetGiantTree flex-auto"
      v-bind="CetGiantTree_1"
      v-on="CetGiantTree_1.event"
    ></CetGiantTree>
  </div>
</template>

<script>
//项目级逻辑组件
import customApi from "@/api/custom";
import TREE_PARAMS from "@/store/treeParams.js";
export default {
  props: {
    refreshTrigger: {
      type: Number
    },
    dimIdChangeTrigger: {
      type: Number
    },
    dimId_in: {
      type: Number
    },
    energyOptions_in: Array
  },

  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    projectTenantId() {
      return this.$store.state.projectTenantId;
    }
  },

  watch: {
    refreshTrigger() {
      this.CetGiantTree_1.inputData_in = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.init();
      this.getTreeData();
      this.getProjectEnergy();
    },
    dimIdChangeTrigger() {
      this.dimIdChange();
    }
  },
  data() {
    return {
      energyOptions: [],
      ElSelect_1: {
        value: 0,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "name"
            }
          }
        },
        event: {
          currentNode_out: this.CetTree_1_currentNode_out //选中单行输出
        }
      }
    };
  },
  methods: {
    init() {
      const params = this.$route.params;
      this.ElSelect_1.value = 0;
      if (params && params.currentNode && this.dimId_in !== 3) {
        this.ElSelect_1.value = params.energyType || 0;
        this.setNode = () => {
          this.CetGiantTree_1.selectNode = params.currentNode;
        };
      }
    },
    dimIdChange() {
      this.ElSelect_1.value = 0;
      this.getTreeData();
    },
    ElSelect_1_change_out() {
      this.getTreeData();
    },
    async getProjectEnergy() {
      const options = this._.cloneDeep(this.energyOptions_in);
      options.unshift({
        energytype: 0,
        name: $T("全部")
      });
      this.ElOption_1.options_in = options;
    },
    async getTreeData() {
      let queryData = {
          rootID: this.projectId,
          rootLabel: "project",
          subLayerConditions: TREE_PARAMS.projectConfig,
          treeReturnEnable: true
        },
        subLayerConditions;
      if (this.ElSelect_1.value) {
        queryData.energyType = this.ElSelect_1.value;
      }
      this.CetGiantTree_1.setting.data.simpleData.idKey = "tree_id";
      this.CetGiantTree_1.setting.data.key.name = "name";
      if (this.dimId_in === 1) {
        subLayerConditions = TREE_PARAMS.projectConfig;
      } else if (this.dimId_in === 2) {
        subLayerConditions = TREE_PARAMS.projectConfigNetwork;
      } else if (this.dimId_in === 3) {
        return;
      } else if (this.dimId_in === 4) {
        this.CetGiantTree_1.setting.data.simpleData.idKey = "id";
        this.CetGiantTree_1.setting.data.key.name = "text";
        this.getPecCoreMeterTree();
        return;
      }
      queryData.subLayerConditions = subLayerConditions;
      const res = await customApi.getNodeTreeSimple(queryData);
      if (res.code !== 0) {
        return;
      }
      this.treeDataHandle(res.data || []);
    },
    // 获取场站通道
    async getPecCoreMeterTree() {
      const data = {
        loadDevice: true,
        nodeId: 0,
        nodeType: 0,
        async: false,
        tenantId: this.projectTenantId
      };

      const res = await customApi.getPecCoreMeterTree(data);
      if (res.code !== 0) {
        return;
      }
      this.treeDataHandle(res.data);
    },
    treeDataHandle(resData) {
      if (!resData) {
        return;
      }
      this.CetGiantTree_1.inputData_in = resData;
      if (this.setNode) {
        this.setNode();
        this.setNode = null;
      } else {
        this.CetGiantTree_1.selectNode = resData[0];
      }
      if (this.dimId_in === 4) {
        // 场站通道设备
        if (this.setNode) {
          this.setNode();
          this.setNode = null;
        }
        {
          this.CetGiantTree_1.selectNode = this._.get(
            resData,
            "[0].children[0].children[0]"
          );
        }
      }
      this.$emit("clearChart_out");
    },
    CetTree_1_currentNode_out(val) {
      this.$emit("currentNode_out", val);
    }
  }
};
</script>

<style lang="scss" scoped>
.treeBox {
  height: 100%;
}
</style>
