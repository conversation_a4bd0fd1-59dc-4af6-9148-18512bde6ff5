<template>
  <div class="page flex-column">
    <el-header height="auto" class="header eem-container mbJ3">
      <div class="text lhHm">
        <div class="goBack" @click="goBack">
          <i class="el-icon-arrow-left"></i>
          {{ $T("返回") }}
        </div>
        <span class="common-title-H2 mlJ1">{{ $T("工单详情") }}</span>
        <CetButton
          v-show="!isShow_in"
          class="fr mrJ1"
          v-bind="CetButton_1"
          v-on="CetButton_1.event"
        ></CetButton>
      </div>
    </el-header>
    <el-container class="flex-auto">
      <el-aside width="315px" class="eem-aside flex-column">
        <div class="common-title-H3 mbJ3">{{ $T("基础信息") }}</div>
        <div class="flex-auto">
          <div
            v-for="(item, index) in listData"
            :key="index"
            class="clearfix listItem"
          >
            <div class="fl ellipsis" style="width: 120px">{{ item.name }}:</div>

            <el-tooltip
              :content="filData(orderMsg[item.key], item.type)"
              effect="light"
            >
              <div class="fl text-overflow" style="width: calc(100% - 120px)">
                {{ filData(orderMsg[item.key], item.type) }}
              </div>
            </el-tooltip>
          </div>
          <div v-show="!isShow_in" class="clearfix listItem">
            <div class="fl ellipsis" style="width: 120px">
              {{ $T("关联维修工单") }}:
            </div>
            <div class="fl text-overflow">
              <span
                v-if="orderMsg && orderMsg.relatedcode"
                @click="showOrderMsg"
                class="clickformore"
              >
                {{ $T("查看详情") }}
              </span>
              <span v-else>--</span>
            </div>
          </div>
        </div>
      </el-aside>
      <el-container class="eem-container mlJ3">
        <el-header height="auto" style="padding: 0px" class="mbJ3">
          <span class="common-title-H3">{{ $T("巡检内容") }}</span>
        </el-header>
        <el-main style="height: 100%; padding: 0px; overflow-x: hidden">
          <el-row :gutter="$J3" class="mbJ3">
            <el-col :span="showTextQuantity ? 8 : 12">
              <div class="mbJ3">
                <span>{{ $T("模拟量") }}：</span>
              </div>
              <div style="height: 200px">
                <CetTable
                  :data.sync="CetTable_1.data"
                  :dynamicInput.sync="CetTable_1.dynamicInput"
                  v-bind="CetTable_1"
                  v-on="CetTable_1.event"
                >
                  <template v-for="(column, index) in Columns_Simulation">
                    <el-table-column
                      v-if="column.custom && column.custom === 'tag'"
                      v-bind="column"
                      :key="index"
                      class-name="font0 hand"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <el-tag
                          size="small"
                          class="text-middle font14"
                          :effect="column.tagEffect"
                          :type="
                            column.tagTypeFormatter
                              ? column.tagTypeFormatter(scope.row, scope.column)
                              : 'primary'
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : scope.row[column.prop]
                          }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else-if="column.custom && column.custom === 'button'"
                      v-bind="column"
                      :key="index"
                      class-name="font0"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <span
                          class="clickformore"
                          @click.stop="
                            column.onButtonClick
                              ? column.onButtonClick(scope.row, scope.$index)
                              : void 0
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : column.text
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else-if="column.custom && column.custom === 'text'"
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    >
                      <template slot-scope="scope">
                        <span
                          v-bind:style="
                            column.textTypeFormatter
                              ? column.textTypeFormatter(
                                  scope.row,
                                  scope.column
                                )
                              : ''
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : column.text
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    ></el-table-column>
                  </template>
                </CetTable>
              </div>
            </el-col>
            <el-col :span="showTextQuantity ? 8 : 12">
              <div class="mbJ3">
                <span>{{ $T("状态量") }}：</span>
              </div>
              <div style="height: 200px">
                <CetTable
                  :data.sync="CetTable_2.data"
                  :dynamicInput.sync="CetTable_2.dynamicInput"
                  v-bind="CetTable_2"
                  v-on="CetTable_2.event"
                >
                  <template v-for="(column, index) in Columns_State">
                    <el-table-column
                      v-if="column.custom && column.custom === 'tag'"
                      v-bind="column"
                      :key="index"
                      class-name="font0 hand"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <el-tag
                          size="small"
                          class="text-middle font14"
                          :effect="column.tagEffect"
                          :type="
                            column.tagTypeFormatter
                              ? column.tagTypeFormatter(scope.row, scope.column)
                              : 'primary'
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : scope.row[column.prop]
                          }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else-if="column.custom && column.custom === 'button'"
                      v-bind="column"
                      :key="index"
                      class-name="font0"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <span
                          class="clickformore"
                          @click.stop="
                            column.onButtonClick
                              ? column.onButtonClick(scope.row, scope.$index)
                              : void 0
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : column.text
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else-if="column.custom && column.custom === 'text'"
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    >
                      <template slot-scope="scope">
                        <span
                          v-bind:style="
                            column.textTypeFormatter
                              ? column.textTypeFormatter(
                                  scope.row,
                                  scope.column
                                )
                              : ''
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : column.text
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    ></el-table-column>
                  </template>
                </CetTable>
              </div>
            </el-col>
            <el-col :span="8" v-if="showTextQuantity">
              <div class="mbJ3">
                <span>{{ $T("文本量") }}：</span>
              </div>
              <div style="height: 200px">
                <CetTable
                  :data.sync="CetTable_5.data"
                  :dynamicInput.sync="CetTable_5.dynamicInput"
                  v-bind="CetTable_5"
                  v-on="CetTable_5.event"
                >
                  <template v-for="(column, index) in Columns_Text">
                    <el-table-column
                      v-if="column.custom && column.custom === 'tag'"
                      v-bind="column"
                      :key="index"
                      class-name="font0 hand"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <el-tag
                          size="small"
                          class="text-middle font14"
                          :effect="column.tagEffect"
                          :type="
                            column.tagTypeFormatter
                              ? column.tagTypeFormatter(scope.row, scope.column)
                              : 'primary'
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : scope.row[column.prop]
                          }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else-if="column.custom && column.custom === 'button'"
                      v-bind="column"
                      :key="index"
                      class-name="font0"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <span
                          class="clickformore"
                          @click.stop="
                            column.onButtonClick
                              ? column.onButtonClick(scope.row, scope.$index)
                              : void 0
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : column.text
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else-if="column.custom && column.custom === 'text'"
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    >
                      <template slot-scope="scope">
                        <span
                          v-bind:style="
                            column.textTypeFormatter
                              ? column.textTypeFormatter(
                                  scope.row,
                                  scope.column
                                )
                              : ''
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : column.text
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    ></el-table-column>
                  </template>
                </CetTable>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="$J3" class="mbJ3">
            <el-col :span="24">
              <div class="mbJ3">
                <span>{{ $T("流程状态") }}</span>
                <div style="float: right">
                  <span
                    :class="['btn_item', { active: isTab === 1 }]"
                    @click="handTab_out(1)"
                  >
                    {{ $T("流程表") }}
                  </span>
                  <span class="btnline">|</span>
                  <span
                    :class="['btn_item', { active: isTab === 2 }]"
                    @click="handTab_out(2)"
                  >
                    {{ $T("流程图") }}
                  </span>
                </div>
              </div>
              <div style="height: 200px" v-show="isTab === 1">
                <CetTable
                  :data.sync="CetTable_3.data"
                  :dynamicInput.sync="CetTable_3.dynamicInput"
                  v-bind="CetTable_3"
                  v-on="CetTable_3.event"
                >
                  <template v-for="(column, index) in Columns_Process">
                    <el-table-column
                      v-if="column.custom && column.custom === 'tag'"
                      v-bind="column"
                      :key="index"
                      class-name="font0 hand"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <el-tag
                          size="small"
                          class="text-middle font14"
                          :effect="column.tagEffect"
                          :type="
                            column.tagTypeFormatter
                              ? column.tagTypeFormatter(scope.row, scope.column)
                              : 'primary'
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : scope.row[column.prop]
                          }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else-if="column.custom && column.custom === 'button'"
                      v-bind="column"
                      :key="index"
                      class-name="font0"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <span
                          class="clickformore"
                          @click.stop="
                            column.onButtonClick
                              ? column.onButtonClick(scope.row, scope.$index)
                              : void 0
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : column.text
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    ></el-table-column>
                  </template>
                </CetTable>
              </div>
              <div
                style="height: 400px; overflow: auto"
                class="bg1"
                v-show="isTab === 2"
              >
                <img :src="imgSrc" :alt="$T('暂无流程图')" />
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="$J3">
            <el-col :span="24">
              <div class="mbJ3">
                <span>{{ $T("巡检结果") }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="mbJ3">
                <span>{{ $T("处理描述") }}：</span>
              </div>
              <div class="description-icon">
                {{ handledescription || "--" }}
                <!-- <el-input
                  type="textarea"
                  rows="5"
                  resize= "none"
                  placeholder="请输入内容"
                  v-model="handledescription">
                </el-input> -->
              </div>
            </el-col>
            <el-col :span="12">
              <div class="mbJ3">
                <span>{{ $T("附件") }}：</span>
              </div>
              <div style="height: 125px">
                <CetTable
                  :data.sync="CetTable_4.data"
                  :dynamicInput.sync="CetTable_4.dynamicInput"
                  v-bind="CetTable_4"
                  v-on="CetTable_4.event"
                >
                  <template v-for="(column, index) in Columns_File">
                    <el-table-column
                      v-if="column.custom && column.custom === 'tag'"
                      v-bind="column"
                      :key="index"
                      class-name="font0 hand"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <el-tag
                          size="small"
                          class="text-middle font14"
                          :effect="column.tagEffect"
                          :type="
                            column.tagTypeFormatter
                              ? column.tagTypeFormatter(scope.row, scope.column)
                              : 'primary'
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : scope.row[column.prop]
                          }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else-if="column.custom && column.custom === 'button'"
                      v-bind="column"
                      :key="index"
                      class-name="font0"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <span
                          class="clickformore"
                          @click.stop="
                            column.onButtonClick
                              ? column.onButtonClick(scope.row, scope.$index)
                              : void 0
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : column.text
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    ></el-table-column>
                  </template>
                  <el-table-column :label="$T('操作')" width="140">
                    <template slot-scope="scope">
                      <span
                        class="clickformore fl mrJ3"
                        @click="handleClick_see_out(scope.row, scope.$index)"
                      >
                        {{ $T("预览") }}
                      </span>
                      <span
                        class="clickformore fl"
                        @click="
                          handleClick_download_out(scope.row, scope.$index)
                        "
                      >
                        {{ $T("下载") }}
                      </span>
                    </template>
                  </el-table-column>
                </CetTable>
              </div>
              <div>
                <CetButton
                  class="fr mtJ3"
                  v-bind="CetButton_2"
                  v-on="CetButton_2.event"
                ></CetButton>
              </div>
            </el-col>
          </el-row>
        </el-main>
      </el-container>
    </el-container>
    <toExamine
      :visibleTrigger_in="toExamine.visibleTrigger_in"
      :closeTrigger_in="toExamine.closeTrigger_in"
      :inputData_in="toExamine.inputData_in"
      :codes_in="toExamine.codes_in"
      @confirm_out="toExamine_confirm_out"
    />
    <FlowChart
      :visibleTrigger_in="flowChart.visibleTrigger_in"
      :closeTrigger_in="flowChart.closeTrigger_in"
      :inputData_in="flowChart.inputData_in"
      @confirm_out="flowChart_confirm_out"
    />
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom.js";
import toExamine from "./toExamine";
import FlowChart from "./FlowChart";
var FileSaver = require("file-saver");
export default {
  name: "classDetail",
  components: {
    toExamine,
    FlowChart
  },
  props: {
    inputData_in: {
      type: Object
    },
    isShow_in: {
      type: Boolean
    }
  },
  computed: {
    showTextQuantity() {
      return this.$store.state.systemCfg.showTextQuantity;
    }
  },
  data() {
    return {
      listData: [
        {
          name: $T("工单号"),
          key: "code",
          type: "string"
        },
        {
          name: $T("巡检目标"),
          key: "deviceplanrelationship_model",
          type: "array"
        },
        {
          name: $T("巡检结果"),
          key: "inspectResult",
          type: "string"
        },
        {
          name: $T("工单来源"),
          key: "createType",
          type: "string"
        },
        {
          name: $T("创建人"),
          key: "creatorname",
          type: "string"
        },
        {
          name: $T("预计开始时间"),
          key: "executetimeplan",
          type: "date"
        },
        {
          name: $T("预计结束时间"),
          key: "finishtimeplan",
          type: "date"
        },
        {
          name: $T("实际开始时间"),
          key: "executetime",
          type: "date"
        },
        {
          name: $T("实际结束时间"),
          key: "finishtime",
          type: "date"
        },
        {
          name: $T("执行人"),
          key: "executor",
          type: "string"
        },
        {
          name: $T("责任班组"),
          key: "teamName",
          type: "string"
        }
      ],
      orderMsg: {},
      itemAction: 0,
      CetButton_1: {
        visible_in: false,
        disable_in: false,
        title: $T("审核"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: $T("全部下载"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },

      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryEventPlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          scenariosId_in: 0,
          limit_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 50
        },
        highlightCurrentRow: false,
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        },
        height: 50,
        style: {
          "text-align": "center"
        },
        showSelection: false
      },
      Columns_Simulation: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 60,
          label: "#",
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "paraName",
          minWidth: 100,
          width: "",
          label: $T("参数名"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "value",
          minWidth: 100,
          width: "",
          label: $T("数值"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          custom: "text",
          formatter: common.formatTextCol(),
          textTypeFormatter: this.valueStyleFormatter
        },
        {
          type: "",
          prop: "min",
          minWidth: 120,
          width: "",
          label: $T("下限"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "max",
          minWidth: 120,
          width: "",
          label: $T("上限"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ],
      CetTable_2: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryEventPlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          scenariosId_in: 0,
          limit_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 50
        },
        highlightCurrentRow: false,
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        },
        height: 50,
        style: {
          "text-align": "center"
        },
        showSelection: false
      },
      Columns_State: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 60,
          label: "#",
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "paraName",
          minWidth: 100,
          width: "",
          label: $T("参数名"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "status",
          minWidth: 100,
          width: "",
          label: $T("状态"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          custom: "text",
          showOverflowTooltip: true,
          formatter: this.statusFormatter,
          textTypeFormatter: this.statusStyleFormatter
        }
      ],
      CetTable_3: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryEventPlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          scenariosId_in: 0,
          limit_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 50
        },
        highlightCurrentRow: false,
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        },
        height: 50,
        style: {
          "text-align": "center"
        },
        showSelection: false
      },
      Columns_Process: [
        {
          type: "",
          prop: "nodename",
          minWidth: "",
          width: 100,
          label: $T("节点"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "operator",
          minWidth: 100,
          width: "",
          label: $T("操作人"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "remark",
          minWidth: "",
          width: "",
          label: $T("描述"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "logtime",
          minWidth: "",
          width: "",
          label: $T("日期时间"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatDateCol("YYYY-MM-DD HH:mm:ss")
        },
        {
          type: "",
          prop: "detail",
          minWidth: "",
          width: "",
          label: $T("内容"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ],
      CetTable_4: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryEventPlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          scenariosId_in: 0,
          limit_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 50
        },
        highlightCurrentRow: false,
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        },
        height: 50,
        style: {
          "text-align": "center"
        },
        showSelection: false
      },
      Columns_File: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 60,
          label: "#",
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "name",
          minWidth: 100,
          width: "",
          label: $T("名称"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        }
      ],
      CetTable_5: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryEventPlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          scenariosId_in: 0,
          limit_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 50
        },
        highlightCurrentRow: false,
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        },
        height: 50,
        style: {
          "text-align": "center"
        },
        showSelection: false
      },
      Columns_Text: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 60,
          label: "序号",
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "paraName",
          minWidth: 100,
          width: "",
          label: "参数名",
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "textvalue",
          minWidth: 100,
          width: "",
          label: "参数值",
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ],
      toExamine: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        codes_in: []
      },
      flowChart: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      handledescription: "",
      isTab: 1,
      imgSrc: null
    };
  },
  watch: {
    inputData_in: {
      handler: function (val, oldVal) {
        this.orderMsg = this._.cloneDeep(val);
        const list = this._.get(val, "inspectionSchemeDetails", []) || [];
        const statusQuantity = [];
        const analogQuantity = [];
        let textQuantity = [];
        list.forEach(item => {
          if (item.type === 1) {
            statusQuantity.push(item);
          } else if (item.type === 2) {
            analogQuantity.push(item);
          } else if (item.type === 3) {
            textQuantity.push(item);
          }
        });
        this.CetTable_1.data = analogQuantity;
        this.CetTable_2.data = statusQuantity;
        this.CetTable_5.data = textQuantity;
        this.CetTable_3.data = this._.get(val, "processFlowUnits", []) || [];
        this.CetTable_4.data = this._.get(val, "attachmentList", []) || [];
        this.handledescription = val.handledescription;
        if (val.executetimeplan && val.timeconsumeplan) {
          this.orderMsg.finishtimeplan =
            val.executetimeplan + val.timeconsumeplan;
        }
        if (val.maintenancecontent) {
          const maintenancecontent = JSON.parse(val.maintenancecontent);
          const users = maintenancecontent.users || [];
          let executor = "";
          users.forEach((item, index) => {
            if (index) {
              executor += " , ";
            }
            executor += item.userName;
          });
          if (!executor) {
            executor = "--";
          }
          this.orderMsg.executor = executor;
        }
        this.isTab = 1;
        this.imgSrc = null;
        this.getImgUrl(val.code);
        this.checkAuth(val.code);
      },
      deep: true,
      immediate: true
    }
  },

  methods: {
    // 是否具有工单权限信息
    checkAuth(code) {
      if (!code) return;
      customApi.checkWorkorderAuth(code).then(res => {
        if (res.code === 0) {
          if (this.inputData_in.worksheetstatus === 3) {
            this.CetButton_1.visible_in = res.data;
          } else {
            this.CetButton_1.visible_in = false;
          }
        }
      });
    },
    toExamine_confirm_out(val) {
      // this.CetTable_1.queryTrigger_in = new Date().getTime();
      this.$emit("goBack", true);
    },
    flowChart_confirm_out(val) {},
    CetButton_1_statusTrigger_out(val) {
      this.toExamine.inputData_in = this._.cloneDeep(this.inputData_in);
      this.toExamine.codes_in = this._.cloneDeep([this.inputData_in.code]);
      this.toExamine.visibleTrigger_in = new Date().getTime();
    },
    CetButton_2_statusTrigger_out(val) {
      const imgList = this.CetTable_4.data || [];
      if (imgList.length === 0) {
        this.$message.warning($T("无附件图片"));
        return;
      }
      imgList.forEach(item => {
        var downloadPath = item.url;
        var name = downloadPath.split("/").splice(-1)[0];
        this.downImg(downloadPath, name);
      });
    },
    CetTable_1_record_out(val) {},
    CetTable_1_outputData_out(val) {},
    goBack() {
      // this.$router.go(-1);
      this.$emit("goBack", false);
    },
    //过滤数据格式
    filData(val, type, unit = "", keyPath = "") {
      if ([null, undefined, NaN].includes(val)) {
        return "--" + unit;
      } else if (type === "number") {
        return Number(val).toFixed(2) + unit;
      } else if (type === "date") {
        if (keyPath) {
          val = this._.get(val, keyPath, null);
        }
        return val ? this.$moment(val).format("YYYY-MM-DD HH:mm:ss") : "--";
      } else if (type === "ms") {
        let str = "--";
        const format = "hh h mm min";
        if (val || val === 0) {
          const hour = Math.floor(val / 3600000);
          const minute = Math.floor((val - hour * 3600000) / 60000);
          if (
            format.indexOf("hh") !== -1 &&
            format.indexOf("mm") !== -1 &&
            format.indexOf("ss") === -1
          ) {
            str = format.replace(
              /(.*)hh(.*)mm(.*)/,
              "$1" + hour + "$2" + minute + "$3"
            );
          }
        }
        return str;
      } else if (type === "array") {
        const list = val || [];
        let inspectObject = "";
        list.forEach((item, index) => {
          if (index) {
            inspectObject += " , ";
          }
          inspectObject += item.devicename;
        });
        return inspectObject || "--";
      } else if (type === "custom") {
        return this._.get(val, keyPath, "--") || "--";
      } else {
        return val + unit;
      }
    },
    handleClick_see_out(data) {
      this.flowChart.inputData_in = this._.cloneDeep(data);
      this.flowChart.visibleTrigger_in = new Date().getTime();
    },
    handleClick_download_out(data) {
      var downloadPath = data.url;
      var name = downloadPath.split("/").splice(-1)[0];

      this.downImg(downloadPath, name);
    },
    downImg: function (downloadPath, name) {
      if (!downloadPath) {
        return;
      }
      var url =
        "/eem-service/v1/common/downloadFile?path=" +
        encodeURIComponent(downloadPath);
      const params = {};
      common.generateImg(url, params, "GET").then(res => {
        if (res.status === 200 && res.data.type === "application/x-download") {
          //下载附件图片
          FileSaver.saveAs(res.data, name);
        }
      });
    },
    //获取流程图图片
    getImgUrl: function (code) {
      var me = this;
      if (!code) {
        return;
      }
      // 主题色
      const currentTheme = localStorage.getItem("omega_theme");
      const isLightStyle = currentTheme === "light";
      var url = `/eem-service/v1/workorder/inspector/workOrder/processDiagram/${code}?isLightStyle=${isLightStyle}`;

      const params = {};
      common.generateImg(url, params, "GET").then(res => {
        if (res.status === 200 && res.data.type === "image/png") {
          //将图片信息放到Img中
          me.imgSrc = window.URL.createObjectURL(res.data);
        }
      });
    },
    handTab_out(val) {
      this.isTab = val;
    },
    //过滤状态量状态
    statusFormatter(row) {
      if (row.status) {
        return $T("正常");
      } else if ([null, undefined, NaN].includes(row.status)) {
        return "--";
      } else {
        return $T("异常");
      }
    },
    //过滤状态量样式
    statusStyleFormatter(row) {
      if (row.status) {
        return {};
      } else if ([null, undefined, NaN].includes(row.status)) {
        return {};
      } else {
        return {
          color: "red"
        };
      }
    },
    //过滤模拟量数值样式
    valueStyleFormatter(row) {
      if (row.value && (row.value < row.min || row.value > row.max)) {
        return {
          color: "red"
        };
      } else {
        return {};
      }
    },
    //点击打开维修工单详情弹框
    showOrderMsg() {
      if (this.orderMsg.relatedcode) {
        if (!this.$checkPermission("repairworkorder_browser")) {
          this.$message.warning($T("无维修工单查看权限"));
        } else {
          this.$emit("goBack", 3);
        }
      }
    }
  },
  created: function () {},
  mounted: function () {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;
}
.header {
  .lhHm {
    @include line_height(Hm);
  }
  .goBack {
    display: inline-block;
    @include font_color(ZS);
    cursor: pointer;
  }
}
.listItem {
  box-sizing: border-box;
  @include padding(0 J1);
  @include margin_bottom(J1);
  line-height: 1.5;
}
.btn_item {
  cursor: pointer;
  @include font_color(T1);
  &.active {
    @include font_color(ZS);
  }
}
.description-icon {
  height: 124px;
  overflow-y: auto;
  padding: 5px 10px;
  line-height: 1.5;
  box-sizing: border-box;
  width: 100%;
  @include background_color(BG1);
  background-image: none;
  border: 1px solid;
  @include border_color(B2);
  @include border_radius(C);
}
.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.clickformore {
  cursor: pointer;
  @include font_color(ZS);
}
</style>
