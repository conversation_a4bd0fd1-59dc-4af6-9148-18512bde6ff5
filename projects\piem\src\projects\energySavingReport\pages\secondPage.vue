<template>
  <div class="page">
    <reportPage pageNum="02" :queryTime="queryTime">
      <div class="mbJ4">
        <div class="second-title mbJ2">
          {{ $T("2.3 集输增压系统预计节能量趋势") }}
        </div>
        <energySavingTrend
          :chartData="reportData.stationsEnergySavingVOList || []"
          :reportData="reportData"
        />
      </div>

      <div class="mbJ4">
        <div class="second-title mbJ2">
          {{ $T("2.4 天然气压缩机节能分析") }}
        </div>
        <energySavingAnalysis
          :chartData="reportData.compressorEnergySavingVOList || []"
          :reportData="reportData"
        />
      </div>

      <div class="mbJ4">
        <stationTable
          :columnList="compressorColumnList"
          :tableData="
            reportData.compressorEnergySavingVOList?.slice(0, 8) || []
          "
        />
      </div>
    </reportPage>
  </div>
</template>

<script>
import reportPage from "../components/reportPage.vue";
import energySavingTrend from "../components/energySavingTrend.vue";
import energySavingAnalysis from "../components/energySavingAnalysis.vue";
import stationTable from "../components/stationTable.vue";
import common from "eem-utils/common";
export default {
  name: "secondPage",
  components: {
    reportPage,
    energySavingTrend, // 集输增压系统预计节能量趋势
    energySavingAnalysis, // 天然气压缩机节能分析
    stationTable // 天然气压缩机节能分析
  },
  props: {
    reportData: {
      type: Object,
      default: () => {}
    },
    queryTime: {
      type: Number
    }
  },
  computed: {
    compressorColumnList() {
      const unit = this.reportData.compressorEnergyUnit;
      const list = [
        {
          prop: "order",
          label: $T("排名"),
          align: "left",
          showOverflowTooltip: true,
          width: 50
        },
        {
          prop: "name",
          label: $T("分析对象"),
          align: "left",
          showOverflowTooltip: true
        },
        {
          prop: "gasTransmissionVolume",
          label: $T("月实际输气量(10⁴m³)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 110
        },
        {
          prop: "energy",
          label: $T(`月实际能耗(${unit})`),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 100
        },
        {
          prop: "optimizationEnergy",
          label: $T(`优化后预计能耗(${unit})`),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 100
        },
        {
          prop: "energySaving",
          label: $T(`预计节能量(${unit})`),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 100
        },
        {
          prop: "savingRate",
          label: $T("预计节能率(%)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 80
        }
      ];
      return list;
    }
  },
  data() {
    return {};
  },
  watch: {},
  methods: {
    formatNumberWithPrecision(...args) {
      return common.formatNumberWithPrecision(...args);
    }
  }
};
</script>

<style lang="scss" scoped>
@media print {
  @page {
    margin: 0;
  }

  body {
    margin: 1.6cm;
    -webkit-print-color-adjust: exact !important;
    -moz-print-color-adjust: exact !important;
    -ms-print-color-adjust: exact !important;
  }
  button {
    display: none;
  }
}
.page {
  width: 100%;
  height: 100%;
  position: relative;
  background: #ffffff;
}
.title {
  color: #242424;
  font-weight: bold;
  line-height: 20px;
}
.second-title {
  color: #242424;
  line-height: 18px;
}
</style>
