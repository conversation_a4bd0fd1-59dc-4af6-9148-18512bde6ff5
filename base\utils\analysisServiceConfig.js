import _ from "lodash";
import fetch from "eem-utils/fetch";
let config = [
  // {
  //   modelLabel: "project",
  //   rootLabel: true,
  //   name: "项目",
  //   nodeTypes: [1],
  //   children: [
  //     {
  //       modelLabel: "sectionarea",
  //       rootLabel: false,
  //       name: "园区",
  //       nodeTypes: [1],
  //       children: [
  //         {
  //           modelLabel: "building",
  //           name: "楼栋",
  //           nodeTypes: [1],
  //           children: [
  //             {
  //               modelLabel: "floor",
  //               name: "楼层",
  //               nodeTypes: [1],
  //               children: [
  //                 {
  //                   modelLabel: "room",
  //                   name: "房间",
  //                   nodeTypes: [1],
  //                   roomType: null,
  //                   children: [
  //                     {
  //                       name: "用能设备",
  //                       modelLabel: "manuequipment",
  //                       nodeTypes: [1]
  //                     }
  //                   ]
  //                 },
  //                 {
  //                   modelLabel: "manuequipment",
  //                   name: "用能设备",
  //                   nodeTypes: [1]
  //                 },
  //                 {
  //                   modelLabel: "meteorologicalmonitor",
  //                   name: "气象监测仪",
  //                   nodeTypes: [1]
  //                 },
  //                 {
  //                   name: "空调",
  //                   modelLabel: "airconditioner",
  //                   nodeTypes: [1]
  //                 }
  //               ]
  //             },
  //             {
  //               modelLabel: "manuequipment",
  //               name: "用能设备",
  //               nodeTypes: [1]
  //             },
  //             {
  //               modelLabel: "meteorologicalmonitor",
  //               name: "气象监测仪",
  //               nodeTypes: [1]
  //             },
  //             {
  //               name: "空调",
  //               modelLabel: "airconditioner",
  //               nodeTypes: [1]
  //             }
  //           ]
  //         }
  //       ]
  //     },
  //     {
  //       modelLabel: "building",
  //       name: "楼栋",
  //       nodeTypes: [1],
  //       children: [
  //         {
  //           modelLabel: "floor",
  //           name: "楼层",
  //           nodeTypes: [1],
  //           children: [
  //             {
  //               modelLabel: "room",
  //               name: "房间",
  //               nodeTypes: [1],
  //               roomType: null,
  //               children: [
  //                 {
  //                   name: "用能设备",
  //                   modelLabel: "manuequipment",
  //                   nodeTypes: [1]
  //                 }
  //               ]
  //             },
  //             {
  //               modelLabel: "manuequipment",
  //               name: "用能设备",
  //               nodeTypes: [1]
  //             },
  //             {
  //               modelLabel: "meteorologicalmonitor",
  //               name: "气象监测仪",
  //               nodeTypes: [1]
  //             },
  //             {
  //               name: "空调",
  //               modelLabel: "airconditioner",
  //               nodeTypes: [1]
  //             }
  //           ]
  //         },
  //         {
  //           modelLabel: "manuequipment",
  //           name: "用能设备",
  //           nodeTypes: [1]
  //         },
  //         {
  //           modelLabel: "meteorologicalmonitor",
  //           name: "气象监测仪",
  //           nodeTypes: [1]
  //         },
  //         {
  //           name: "空调",
  //           modelLabel: "airconditioner",
  //           nodeTypes: [1]
  //         }
  //       ]
  //     },
  //     {
  //       name: "配电房",
  //       modelLabel: "room",
  //       roomType: 1,
  //       nodeTypes: [2],
  //       children: [
  //         {
  //           name: "管道",
  //           modelLabel: "pipeline",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "变压器",
  //           modelLabel: "powertransformer",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "开关柜或一段线",
  //           modelLabel: "linesegmentwithswitch",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "母线",
  //           modelLabel: "busbarsection",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "母联",
  //           modelLabel: "busbarconnector",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "发电机",
  //           modelLabel: "generator",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "HVDC",
  //           modelLabel: "hvdc",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "UPS",
  //           modelLabel: "ups",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "蓄电池",
  //           modelLabel: "battery",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "PT柜",
  //           modelLabel: "ptcabinet",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "计量柜",
  //           modelLabel: "meteringcabinet",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "列头柜",
  //           modelLabel: "arraycabinet",
  //           nodeTypes: [2],
  //           children: [
  //             {
  //               name: "一段线",
  //               modelLabel: "linesegment",
  //               nodeTypes: [2]
  //             }
  //           ]
  //         },
  //         {
  //           name: "配电柜",
  //           modelLabel: "powerdiscabinet",
  //           nodeTypes: [2],
  //           children: [
  //             {
  //               name: "一段线",
  //               modelLabel: "linesegment",
  //               nodeTypes: [2]
  //             }
  //           ]
  //         },
  //         {
  //           name: "开关柜",
  //           modelLabel: "switchcabinet",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "AVC",
  //           modelLabel: "avc",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "ATC",
  //           modelLabel: "ats",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "DC柜",
  //           modelLabel: "dcpanel",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "IT机柜",
  //           modelLabel: "itcabinet",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "一段线",
  //           modelLabel: "linesegment",
  //           nodeTypes: [2]
  //         }
  //       ]
  //     },
  //     {
  //       name: "IT机房",
  //       modelLabel: "room",
  //       roomType: 2,
  //       nodeTypes: [2],
  //       children: [
  //         {
  //           name: "计算机",
  //           modelLabel: "computer",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "通信管理机",
  //           modelLabel: "gateway",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "表计",
  //           modelLabel: "meter",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "交换机",
  //           modelLabel: "interchanger",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "光电转换器",
  //           modelLabel: "photoeleconverter",
  //           nodeTypes: [2]
  //         }
  //       ]
  //     },
  //     {
  //       name: "空调机房",
  //       modelLabel: "room",
  //       roomType: 3,
  //       nodeTypes: [2],
  //       children: [
  //         {
  //           name: "冷水主机",
  //           modelLabel: "coldwatermainengine",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "风柜",
  //           modelLabel: "windset",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "冷却塔",
  //           modelLabel: "coolingtower",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "泵",
  //           modelLabel: "pump",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "板式换热器",
  //           modelLabel: "plateheatexchanger",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "PLC",
  //           modelLabel: "plc",
  //           nodeTypes: [2]
  //         }
  //       ]
  //     },
  //     {
  //       name: "空压机房",
  //       modelLabel: "room",
  //       roomType: 4,
  //       nodeTypes: [2],
  //       children: [
  //         {
  //           name: "空压机",
  //           modelLabel: "aircompressor",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "冷干机",
  //           modelLabel: "colddryingmachine",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "干燥机",
  //           modelLabel: "dryingmachine",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "冷却塔",
  //           modelLabel: "coolingtower",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "泵",
  //           modelLabel: "pump",
  //           nodeTypes: [2]
  //         }
  //       ]
  //     },
  //     {
  //       name: "锅炉房",
  //       modelLabel: "room",
  //       roomType: 5,
  //       nodeTypes: [2],
  //       children: [
  //         {
  //           name: "管道",
  //           modelLabel: "pipeline",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "锅炉",
  //           modelLabel: "boiler",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "泵",
  //           modelLabel: "pump",
  //           nodeTypes: [2]
  //         }
  //       ]
  //     },
  //     {
  //       name: "管道房",
  //       modelLabel: "room",
  //       roomType: 6,
  //       nodeTypes: [2],
  //       children: [
  //         {
  //           name: "管道",
  //           modelLabel: "pipeline",
  //           nodeTypes: [2]
  //         },
  //         {
  //           name: "泵",
  //           modelLabel: "pump",
  //           nodeTypes: [2]
  //         }
  //       ]
  //     },
  //     {
  //       name: "市政总管",
  //       modelLabel: "civicpipe",
  //       nodeTypes: [1]
  //     }
  //   ]
  // }
];

export async function init() {
  const res = await fetch({
    url: `/eem-service/v1/node/nodeTreeLabels`,
    method: "GET"
  });
  config = _.get(res, "data", []) || [];
}

export function hasProp(obj, key) {
  return Object.prototype.hasOwnProperty.call(obj, key);
}

/*
根据查询条件获取某个配置节点，如:
condition = { 
  modelLabel: "room",
  roomType: 4
}
*/
export const findNode = (condition = {}) => {
  if (_.isEmpty(condition) || _.isEmpty(config)) return;

  function isEqual(item) {
    const keys = Object.keys(condition);
    let result = true;
    keys.forEach(key => {
      if (!hasProp(item, key) || item[key] !== condition[key]) {
        result = false;
      }
    });
    return result;
  }

  function loop(items) {
    for (const item of items) {
      if (isEqual(item)) {
        return item;
      }
      if (item.children) {
        const ret = loop(item.children);
        if (ret) {
          return ret;
        }
      }
    }
  }
  return loop(config) || null;
};

/*
获取节点树入参
nodeType: 1:管理层级，2：管网

返回的是subLayerConditions
*/

export const getTreeParams = nodeType => {
  if (!_.isNumber(nodeType) || _.isEmpty(config)) return [];
  const nodeAll = _.get(config, "[0].children", []).filter(item => {
    if (nodeType === 2) {
      return (
        !_.isEmpty(item.nodeTypes) &&
        item.nodeTypes.length === 1 &&
        item.nodeTypes[0] === 2
      );
    }
    return !_.isEmpty(item.nodeTypes) && item.nodeTypes.includes(nodeType);
  });
  let subLayerConditions = [];

  function addSubLayerConditions(conditions, roomtype) {
    const oilConditions = subLayerConditions.find(item => {
      return item.modelLabel === conditions.modelLabel;
    });
    if (!oilConditions) {
      if (conditions.modelLabel === "room" && !_.isEmpty(roomtype)) {
        subLayerConditions.push({
          ...conditions,
          filter: {
            composemethod: true,
            expressions: [
              {
                limit: roomtype,
                operator: "IN",
                prop: "roomtype",
                tagid: 1
              }
            ]
          }
        });
      } else {
        subLayerConditions.push(conditions);
      }
    } else if (conditions.modelLabel === "room" && !_.isEmpty(roomtype)) {
      const limit = _.get(oilConditions, "filter.expressions[0].limit");
      let newLimit;
      if (!_.isEmpty(limit)) {
        newLimit = _.uniq([...roomtype, ...limit]);
      } else {
        newLimit = roomtype;
      }
      oilConditions.filter = {
        composemethod: true,
        expressions: [
          {
            limit: newLimit,
            operator: "IN",
            prop: "roomtype",
            tagid: 1
          }
        ]
      };
    }
  }

  function loop(items) {
    for (const item of items) {
      const conditions = {
        modelLabel: item.modelLabel
      };
      const roomtype =
        item.modelLabel === "room" ? [item.roomType || null] : null;
      addSubLayerConditions(conditions, roomtype);
      if (item.children) {
        loop(item.children);
      }
    }
  }

  loop(nodeAll);

  return subLayerConditions;
};
