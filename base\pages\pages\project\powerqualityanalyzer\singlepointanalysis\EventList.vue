<template>
  <div style="height: 355px">
    <el-table
      highlight-current-row
      :data="eventTable"
      border
      tooltip-effect="light"
      height="true"
      style="width: 100%; height: calc(100% - 34px)"
      @current-change="handleRowChange"
    >
      <el-table-column label="#" type="index" align="left" width="50" />
      <el-table-column
        align="left"
        :formatter="formatDate"
        prop="eventtime"
        label="触发时间"
        width="190"
      ></el-table-column>
      <el-table-column
        align="left"
        prop="pqvariationeventtype"
        :formatter="formatEnum"
        label="事件类型"
        width="120"
      ></el-table-column>
      <el-table-column
        align="left"
        prop="description"
        label="事件描述"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column align="left" label="关联波形" width="80">
        <template slot-scope="scope">
          <span
            class="clickformore"
            v-if="scope.row.waveformlogtime"
            @click="showWaveWindow(scope.row)"
          >
            查看
          </span>
          <span v-else>无</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="ptJ1 text-right">
      <el-pagination
        class="pagination"
        background
        small
        :pager-count="5"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-sizes="[20, 50, 100, 200]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="eventTotal"
      ></el-pagination>
    </div>
    <!-- 波形展示弹窗 -->
    <el-dialog
      class="wave-dialog"
      :visible.sync="isShowWaveWindow"
      title="波形"
      width="80%"
      @opened="updateWave"
      @closed="clearWave"
    >
      <Wave
        class="eem-cont-c1"
        :showTitle="true"
        :title="_.get(clickedEventLog, 'monitoredName', '--')"
        :showTable="true"
        :data="clickedEventLog"
      ></Wave>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import common from "eem-utils/common";
import Wave from "eem-components/Wave.vue";
import ELECTRICAL_DEVICE from "@/store/electricaldevice";
export default {
  name: "EventList",
  components: {
    Wave
  },
  props: {
    queryTime: Object,
    currentNode: Object,
    refreshTrigger_in: {
      type: [Number]
    }
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    modelLabels() {
      return ELECTRICAL_DEVICE.map(i => i.value);
    }
  },
  watch: {
    queryTime: {
      deep: true,
      handler: function (val, oldVal) {
        if (
          this.modelLabels.includes(
            this._.get(this.currentNode, "data.modelLabel")
          )
        )
          this.queryEventList();
      }
    },
    refreshTrigger_in: {
      deep: true,
      handler: function (val, oldVal) {
        this.queryEventList();
      }
    },
    currentNode: {
      deep: true,
      handler: function (val, oldVal) {
        if (
          this._.get(val, "data.tree_id", -1) ===
          this._.get(oldVal, "data.tree_id", -2)
        )
          return;
        if (this.modelLabels.includes(this._.get(val, "data.modelLabel")))
          this.queryEventList();
      }
    }
  },
  data() {
    return {
      eventTable: [],
      eventTypeEnum: [],
      isShowWaveWindow: false,
      cachedWaveData: {},
      clickedEventLog: {},
      eventTotal: 0,
      currentPage: 1,
      pageSize: 20,
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "关闭",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: () => {
            this.isShowWaveWindow = false;
          }
        }
      }
    };
  },
  methods: {
    formatDate(row, column, cellValue, index) {
      return common.formatDate(cellValue, "YYYY-MM-DD HH:mm:ss.SSS");
    },
    // 加载枚举数据
    loadEnumrations(...args) {
      this.eventTypeEnum = this.$store.state.enumerations.pqvariationeventtype;
    },
    formatEnum(row, column, cellValue) {
      const enumObj = this._.find(this.eventTypeEnum, ["id", cellValue]) || {};
      return enumObj.text || "--";
    },
    showWaveWindow(obj) {
      var wavedata = this._.cloneDeep(obj);
      wavedata.deviceId = wavedata.srcdeviceid;
      wavedata.waveTime = wavedata.waveformlogtime;
      this.cachedWaveData = wavedata;
      this.isShowWaveWindow = true;
    },

    updateWave() {
      this.clickedEventLog = { ...this.cachedWaveData };
    },

    clearWave() {
      this.cachedWaveData = {};
      this.clickedEventLog = {};
    },

    // 收集事件查询的参数
    collectQueryEventParam() {
      const vm = this;

      let node = null;
      if (vm.currentNode && vm.currentNode.data) {
        node = {
          id: vm.currentNode.data.id,
          modelLabel: vm.currentNode.data.modelLabel
        };
      }
      return {
        startTime: this.queryTime.startTime,
        endTime: this.queryTime.endTime,
        // 被检测设备类型
        // deviceTypes: ["linesegmentwithswitch"],
        // 事件等级
        // "eventClasses": [],
        // 高级查询中的事件类型
        eventTypes: [],
        // 搜索关键字
        keyWord: "",
        // 电能质量类型的监测设备 必传，目前只有 9  // 2019年11月20日17:07:06
        // meterTypes: [9],
        // 选中节点树中的测点 monitoredid
        node: node,
        page: {
          index: vm.pageSize * (vm.currentPage - 1),
          limit: vm.pageSize
        },
        // 高级查询中的区域
        toleranceBands: [],
        transientFaultDirections: [],
        projectId: vm.projectId
      };
    },
    queryEventList() {
      // console.log("故障诊断--事件");

      const vm = this;
      common.requestData(
        {
          url: "/eem-service/v1/pq/event",
          data: vm.collectQueryEventParam()
        },
        (data, res) => {
          vm.eventTable = data;
          vm.eventTotal = res.total;
        }
      );
    },
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.queryEventList();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.queryEventList();
    },
    handleRowChange(val) {
      this.$emit("handleEventRow", val);
    }
  },
  created() {
    this.loadEnumrations("pqvariationeventtype");
    // this.queryEventList();
  }
};
</script>
<style lang="scss" scoped>
.clickformore {
  cursor: pointer;
  // font-size: 13px;
  @include font_size("Ab");
  @include font_color(ZS);
}
.pagination {
  :deep(.el-input--mini .el-input__inner) {
    height: 22px;
    line-height: 22px;
  }
  .el-input--mini .el-input__icon {
    line-height: 22px;
  }
}
</style>
<style></style>
