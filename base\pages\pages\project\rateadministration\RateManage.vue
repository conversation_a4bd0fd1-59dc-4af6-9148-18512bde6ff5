<template>
  <div class="page eem-common">
    <div class="fullheight flex-row">
      <div style="width: 420px" class="fullheight flex-column eem-container">
        <div class="mbJ3" style="display: flex">
          <el-tooltip
            effect="light"
            :content="$T('费率管理')"
            placement="bottom"
          >
            <span class="fl common-title-H2 mrJ2 lh32 text-ellipsis">
              {{ $T("费率管理") }}
            </span>
          </el-tooltip>
          <div class="basic-box">
            <customElSelect
              class="fr"
              v-model="ElSelect_1.value"
              v-bind="ElSelect_1"
              v-on="ElSelect_1.event"
              :prefix_in="$T('能源类型')"
            >
              <ElOption
                v-for="item in ElOption_1.options_in"
                :key="item[ElOption_1.key]"
                :label="item[ElOption_1.label]"
                :value="item[ElOption_1.value]"
                :disabled="item[ElOption_1.disabled]"
              ></ElOption>
            </customElSelect>
          </div>
          <CetButton
            class="fr mlJ1"
            v-bind="CetButton_1"
            v-on="CetButton_1.event"
            v-permission="'feemanage_edit'"
          ></CetButton>
        </div>
        <CetTable
          class="flex-auto"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_energytype$text"></ElTableColumn>
          <ElTableColumn
            v-bind="ElTableColumn_feeratetype$text"
          ></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_allowDelete$text">
            <template slot-scope="{ row }">
              <span
                v-permission="'feemanage_edit'"
                :class="{
                  clickformore: true,
                  delete: true,
                  clickdisable: !row.allowDelete
                }"
                @click.stop="handleDeleteRate(row)"
              >
                {{ $T("删除") }}
              </span>
            </template>
          </ElTableColumn>
        </CetTable>
      </div>
      <div style="flex: 1" class="minWH fullheight mlJ3">
        <el-container class="fullheight flex-column">
          <div class="mbJ3">
            <span class="common-title-H1 lh32">{{ $T("方案详情") }}</span>
            <CetButton
              class="fr"
              v-bind="CetButton_2"
              v-on="CetButton_2.event"
              v-permission="'feemanage_edit'"
            ></CetButton>
          </div>
          <!-- <el-main style="padding: 0px; height: calc(100% - 58px); flex-direction: column"> -->
          <currentRate
            :currentRate="currentRate"
            :currentTabel1Item="currentTabel1Item"
          />
          <div style="flex: 1" class="minWH eem-container mtJ3">
            <div class="mbJ3 common-title-H2">{{ $T("调整历史") }}</div>
            <CetTable
              style="height: calc(100% - 42px)"
              :data.sync="CetTable_2.data"
              :dynamicInput.sync="CetTable_2.dynamicInput"
              v-bind="CetTable_2"
              v-on="CetTable_2.event"
            >
              <template>
                <ElTableColumn
                  v-for="(item, index) in ElTableColumnArr"
                  :key="index"
                  v-bind="item"
                >
                  <template slot-scope="scope">
                    <el-tag
                      v-if="item.type === 'tag'"
                      size="small"
                      style="color: #fff"
                      :type="statusTagTypeFormatter(scope.row.status$text)"
                    >
                      {{ scope.row.status$text || "--" }}
                    </el-tag>
                    <span v-else>
                      {{
                        item.formatter
                          ? item.formatter(scope.row, item.prop)
                          : scope.row[item.prop]
                      }}
                    </span>
                  </template>
                </ElTableColumn>
              </template>
              <ElTableColumn
                v-bind="ElTableColumn_edit"
                v-if="CetTable_2.showEdit"
              >
                <template slot-scope="scope">
                  <!-- 力调电费且当前状态为已过期时显示查看 -->
                  <span
                    class="clickformore mrJ1 f3"
                    v-if="
                      currentTabel1Item.feeratetype === 3 &&
                      scope.row.status$text == $T('已过期')
                    "
                    @click.stop="handleDetail(scope.$index, scope.row)"
                  >
                    {{ $T("查看") }}
                  </span>
                  <span
                    v-else
                    class="fl mrJ3"
                    :class="{
                      clickformore: true,
                      clickdisable: scope.row.status$text == $T('已过期')
                    }"
                    @click.stop="handleEdit(scope.$index, scope.row)"
                    v-permission="'feemanage_edit'"
                  >
                    {{ $T("编辑") }}
                  </span>
                  <span
                    class="fl mrJ3"
                    :class="{
                      clickformore: true,
                      delete: true,
                      clickdisable: scope.row.status$text !== $T('未生效')
                    }"
                    @click.stop="handleDelete(scope.$index, scope.row)"
                    v-permission="'feemanage_edit'"
                  >
                    {{ $T("删除") }}
                  </span>
                </template>
              </ElTableColumn>
            </CetTable>
          </div>
          <!-- </el-main> -->
        </el-container>
      </div>
    </div>
    <AddRateScheme
      :visibleTrigger_in="AddRateScheme.visibleTrigger_in"
      :closeTrigger_in="AddRateScheme.closeTrigger_in"
      :queryId_in="AddRateScheme.queryId_in"
      :recordId="AddRateScheme.recordId"
      :disableDate="AddRateScheme.disableDate"
      :detailVisible="AddRateScheme.detailVisible"
      :inputData_in="AddRateScheme.inputData_in"
      :dateArr="AddRateScheme.dateArr"
      :tableData="tableData"
      @finishTrigger_out="AddRateScheme_finishTrigger_out"
      @finishData_out="AddRateScheme_finishData_out"
      @saveData_out="AddRateScheme_saveData_out"
      @currentData_out="AddRateScheme_currentData_out"
    />
  </div>
</template>
<script>
import common from "eem-utils/common";
import currentRate from "./rateManage/currentRate.vue";
import AddRateScheme from "./rateManage/AddRateScheme.vue";
import Vue from "vue";
import { httping } from "@omega/http";
export default {
  name: "RateManage",
  components: {
    currentRate,
    AddRateScheme
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },

  data() {
    return {
      timeShareId: null, // 分时费率关联的分时方案id
      currentTabel1Item: null,
      currentTabel1Item2: null,
      currentRate: [], // 展示费率
      actionCurrentRate: null, //当前生效的费率
      ElSelect_1: {
        value: null,
        style: {
          width: "172px"
        },
        size: "small",
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [
          {
            id: 0,
            text: $T("全部")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      ElTableColumn_name: {
        //type: "",      // selection 勾选 index 序号
        prop: "name", // 支持path a[0].b
        label: $T("费率方案"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: common.formatTextCol()
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_energytype$text: {
        //type: "",      // selection 勾选 index 序号
        prop: "energyTypeName", // 支持path a[0].b
        label: $T("能源类型"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: common.formatTextCol(),
        //minWidth: "200",  //该宽度会自适应
        width: "78" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_feeratetype$text: {
        //type: "",      // selection 勾选 index 序号
        prop: "feerateTypeName", // 支持path a[0].b
        label: $T("费用类型"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: common.formatTextCol()
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_allowDelete$text: {
        //type: "",      // selection 勾选 index 序号
        prop: "allowDelete$text", // 支持path a[0].b
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      CetButton_1: {
        visible_in: true,
        disable_in: true,
        title: $T("新增费率方案"),
        type: "primary",
        plain: true,
        width: "100%",
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetTable_2: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_2_record_out,
          outputData_out: this.CetTable_2_outputData_out
        },
        showColumn: true,
        showDelete: true,
        showEdit: true
      },
      ElTableColumn_edit: {
        //type: "",      // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "120" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumnArr: [{}],
      AddRateScheme: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        recordId: 0,
        inputData_in: null,
        disableDate: false,
        detailVisible: false,
        dateArr: []
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: $T("费率调整"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      tableData: []
    };
  },
  watch: {},

  methods: {
    // 当前状态标签样式格式化
    statusTagTypeFormatter(cellValue) {
      return cellValue === $T("生效中")
        ? "status1"
        : cellValue === $T("已过期")
        ? "status2"
        : cellValue === $T("未生效")
        ? "status3"
        : "primary";
    },
    CetButton_2_statusTrigger_out(val) {
      this.tableData = this._.cloneDeep(this.CetTable_2.data);
      this.AddRateScheme.recordId = 0;
      this.AddRateScheme.disableDate = false;
      this.AddRateScheme.detailVisible = false;
      this.editRate();
    },
    //获取能源类型
    getEnergytype() {
      var vm = this;
      vm.ElOption_1.options_in = [];
      httping({
        url:
          "/eem-service/v1/project/projectEnergy?projectId=" + this.projectId,
        method: "GET"
      }).then(function (response) {
        if (response.code === 0 && response.data && response.data.length > 0) {
          vm.CetButton_1.disable_in = false;
          // 过滤掉能源类型为[13, 18, 22]
          const energyList = response.data.filter(
            item => ![13, 18, 22].includes(item.energytype)
          );
          var selectData = energyList.map(item => {
            return {
              id: item.energytype,
              text: item.name,
              symbol: common.formatSymbol(item.symbol) || "--"
            };
          });
          var res = {};
          res.id = 0;
          res.text = $T("全部");
          selectData.unshift(res);
          vm.ElOption_1.options_in = vm._.cloneDeep(selectData);
          vm.ElSelect_1.value = 0;
          vm.ElSelect_1_change_out(0);
        } else {
          vm.ElOption_1.options_in = null;
          vm.CetButton_1.disable_in = true;
        }
      });
    },
    // 查询计费方案
    getTabelData() {
      if (this.ElSelect_1.value === null) return;
      httping({
        url: `/eem-service/v1/schemeConfig/feeScheme/${this.projectId}?energyType=${this.ElSelect_1.value}`,
        method: "GET"
      }).then(response => {
        if (response.code === 0) {
          const resData = response.data || [];
          this.CetTable_1.data = resData;
        } else {
          this.CetTable_1.data = [];
          this.renderTable();
        }
      });
    },
    // 查询费率记录，不支持分时费率记录的查询
    getFeeSchemeDetail() {
      this.CetTable_2.data = [];
      this.currentRate = [];
      var feeRecordType = "";
      /*
      // 容量费率 volumefeerecord
      // 需量费率 demandfeerecord
      // 单一费率 singlefeerecord
      // 阶梯费率 stagefeerecord
      // 力调费率 powertariff
      // 附加费 surchargefeerecord
      */
      if (
        this.currentTabel1Item.energytype === 2 &&
        this.currentTabel1Item.feeratetype === 1 &&
        this.currentTabel1Item.feeratesubtype === 1
      ) {
        // 容量费率 volumefeerecord
        feeRecordType = "volumefeerecord";
      } else if (
        // 需量费率 demandfeerecord
        this.currentTabel1Item.energytype === 2 &&
        this.currentTabel1Item.feeratetype === 1 &&
        this.currentTabel1Item.feeratesubtype === 2
      ) {
        feeRecordType = "demandfeerecord";
      } else if (
        this.currentTabel1Item.feeratetype === 2 &&
        this.currentTabel1Item.feeratesubtype === 1
      ) {
        // 单一费率 singlefeerecord
        feeRecordType = "singlefeerecord";
      } else if (
        this.currentTabel1Item.feeratetype === 2 &&
        this.currentTabel1Item.feeratesubtype === 3
      ) {
        // 阶梯费率 stagefeerecord
        feeRecordType = "stagefeerecord";
      } else if (this.currentTabel1Item.feeratetype === 3) {
        // 力调费率 powertariff
        feeRecordType = "powertariff";
      } else if (
        this.currentTabel1Item.feeratetype === 4 &&
        this.currentTabel1Item.feeratesubtype === 1
      ) {
        // 附加费  单一费率 singlefeerecord
        feeRecordType = "singlefeerecord";
      } else if (
        this.currentTabel1Item.feeratetype === 4 &&
        this.currentTabel1Item.feeratesubtype === 3
      ) {
        // 附加费  单一费率 stagefeerecord
        feeRecordType = "stagefeerecord";
      }
      if (
        feeRecordType === "volumefeerecord" ||
        feeRecordType === "demandfeerecord"
      ) {
        httping({
          url:
            "/eem-service/v1/demand/maintain/getFeeSchemeItem?id=" +
            this.currentTabel1Item.id,
          method: "GET"
        }).then(response => {
          this.renderTable(response.data[feeRecordType + "_model"]);
        });
      } else {
        httping({
          url: `/eem-service/v1/schemeConfig/feeRecord/${feeRecordType}/${this.currentTabel1Item.id}`,
          method: "GET"
        }).then(response => {
          if (response.code === 0) {
            this.renderTable(response.data);
          }
        });
      }
    },
    // 查询分时费率记录
    getTimeFeeSchemeDetail() {
      this.CetTable_2.data = [];
      this.currentRate = [];
      this.timeShareId = null;
      httping({
        url: `/eem-service/v1/schemeConfig/feeRecord/timeShare/${this.currentTabel1Item.id}/${this.currentTabel1Item.energytype}`,
        method: "GET"
      }).then(response => {
        if (response.code === 0) {
          this.renderTable(response.data);
          this.timeShareId = response.data.timeShareId;
        }
      });
    },
    // 渲染表格
    renderTable(tabData) {
      this.actionCurrentRate = null;
      this.CetTable_2.data = [];
      this.currentRate = [];
      this.ElTableColumnArr = [{}];
      if (!tabData || !this.currentTabel1Item) {
        return;
      }
      if (
        (!tabData.length || tabData.length === 0) &&
        this.currentTabel1Item.feeratetype !== 2 &&
        this.currentTabel1Item.feeratesubtype !== 2
      ) {
        return;
      }
      // 解析字段
      var action = false;
      // 单独处理分时
      if (
        (this.currentTabel1Item.feeratetype === 2 &&
          this.currentTabel1Item.feeratesubtype === 2) ||
        (this.currentTabel1Item.feeratetype === 4 &&
          this.currentTabel1Item.feeratesubtype === 2)
      ) {
        this.CetTable_2.showDelete = false;
        this.CetTable_2.showEdit = false;
        this.CetTable_2.data = tabData.data || [];
        // 列表生效时间按倒序排序
        var wrap;
        for (var i = 0; i < this.CetTable_2.data.length; i++) {
          for (var k = i + 1; k < this.CetTable_2.data.length; k++) {
            if (
              this.CetTable_2.data[i].endTime <
              this.CetTable_2.data[k].startTime
            ) {
              wrap = this.CetTable_2.data[k];
              this.CetTable_2.data[k] = this.CetTable_2.data[i];
              this.CetTable_2.data[i] = wrap;
            }
          }
        }
        this.CetTable_2.data.forEach(item => {
          if (
            item.startTime <= this.$moment().endOf("day").valueOf() &&
            item.endTime >= this.$moment().startOf("day").valueOf()
          ) {
            if (!action) {
              item.status$text = $T("生效中");
              // 保存生效费率
              this.actionCurrentRate = item;
              action = true;
            }
          } else if (item.startTime > this.$moment().endOf("day").valueOf()) {
            item.status$text = $T("未生效");
          } else if (item.endTime < this.$moment().startOf("day").valueOf()) {
            item.status$text = $T("已过期");
          }
          item.effectivedate$text =
            this.$moment(item.startTime).format("YYYY-MM-DD") +
            " ~ " +
            this.$moment(item.endTime).format("YYYY-MM-DD");
        });
      } else {
        this.CetTable_2.showDelete = true;
        this.CetTable_2.showEdit = true;
        //  tab赋值
        this.CetTable_2.data = tabData;
        // 列表生效时间按倒序排序
        for (var j = 0; j < this.CetTable_2.data.length; j++) {
          for (var m = j + 1; m < this.CetTable_2.data.length; m++) {
            if (
              this.CetTable_2.data[j].effectivedate <
              this.CetTable_2.data[m].effectivedate
            ) {
              wrap = this.CetTable_2.data[m];
              this.CetTable_2.data[m] = this.CetTable_2.data[j];
              this.CetTable_2.data[j] = wrap;
            }
          }
        }
        this.CetTable_2.data.forEach(item => {
          if (item.effectivedate <= this.$moment().endOf("month").valueOf()) {
            if (!action) {
              item.status$text = $T("生效中");
              // 保存生效费率
              this.actionCurrentRate = item;
              action = true;
            } else {
              item.status$text = $T("已过期");
            }
          } else {
            item.status$text = $T("未生效");
          }
        });
      }
      // 处理单位
      var symbol = $T("元") + "/--";
      if (this.ElOption_1.options_in.length > 0) {
        symbol =
          $T("元") +
          "/" +
          this.ElOption_1.options_in.filter(
            item => item.id === this.currentTabel1Item.energytype
          )[0].symbol;
      }
      // if (this.currentTabel1Item.energytype == 3) {
      //   symbol = "元/吨";
      // } else if (this.currentTabel1Item.energytype == 15) {
      //   symbol = "元/立方米";
      // }
      // 判断调整历史的表头
      if (
        this.currentTabel1Item.energytype === 2 &&
        this.currentTabel1Item.feeratetype === 1 &&
        this.currentTabel1Item.feeratesubtype === 1
      ) {
        // 容量费率
        this.ElTableColumnArr = [
          {
            label: $T("生效时间"),
            prop: "effectivedate",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              return this.$moment(val.effectivedate).format("YYYY-MM");
            }
          },
          {
            label: $T("费率（元/kVA）"),
            prop: "feerate",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              if (this._.get(val, prop) || this._.get(val, prop) === 0) {
                return this._.get(val, prop);
              } else {
                return "--";
              }
            }
          },
          {
            type: "tag",
            label: $T("当前状态"),
            prop: "status$text",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left"
          }
        ];
        if (this.actionCurrentRate) {
          this.currentRate = [
            {
              Fee: this.actionCurrentRate.feerate,
              FeeText: $T("元") + "/kVA"
            }
          ];
        }
      } else if (
        this.currentTabel1Item.energytype === 2 &&
        this.currentTabel1Item.feeratetype === 1 &&
        this.currentTabel1Item.feeratesubtype === 2
      ) {
        // 需量计费
        this.ElTableColumnArr = [
          {
            label: $T("生效时间"),
            prop: "effectivedate",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              return this.$moment(val.effectivedate).format("YYYY-MM");
            }
          },
          {
            label: $T("费率（元/kW）"),
            prop: "feerate",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              if (this._.get(val, prop) || this._.get(val, prop) === 0) {
                return this._.get(val, prop);
              } else {
                return "--";
              }
            }
          },
          {
            label: $T("上限比例") + "(%)",
            prop: "highlimit",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              if (this._.get(val, prop) || this._.get(val, prop) === 0) {
                return this._.get(val, prop);
              } else {
                return "--";
              }
            }
          },
          {
            label: $T("惩罚系数"),
            prop: "punishrate",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              if (this._.get(val, prop) || this._.get(val, prop) === 0) {
                return this._.get(val, prop);
              } else {
                return "--";
              }
            }
          },
          {
            label: $T("计算负偏差"),
            prop: "calculatedeviation",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              return this._.get(val, prop) ? $T("是") : $T("否");
            }
          },
          {
            type: "tag",
            label: $T("当前状态"),
            prop: "status$text",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left"
          }
        ];
        if (this.actionCurrentRate) {
          this.currentRate = [
            {
              Fee: this.actionCurrentRate.feerate,
              FeeText: $T("元") + "/kW"
            }
          ];
        }
      } else if (
        (this.currentTabel1Item.feeratetype === 2 &&
          this.currentTabel1Item.feeratesubtype === 1) ||
        (this.currentTabel1Item.feeratetype === 4 &&
          this.currentTabel1Item.feeratesubtype === 1)
      ) {
        // 单一费率
        // 处理单位
        if (this.CetTable_2.data.length > 0) {
          this.CetTable_2.data.forEach(item => {
            item.feerate = item.feerate / 100000;
          });
        }
        this.ElTableColumnArr = [
          {
            label: $T("生效时间"),
            prop: "effectivedate",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              return this.$moment(val.effectivedate).format("YYYY-MM");
            }
          },
          {
            label: `${$T("费率")}（${symbol}）`,
            prop: "feerate",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              if (this._.get(val, prop) || this._.get(val, prop) === 0) {
                return this._.get(val, prop);
              } else {
                return "--";
              }
            }
          },
          {
            type: "tag",
            label: $T("当前状态"),
            prop: "status$text",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left"
          }
        ];
        if (this.actionCurrentRate) {
          this.currentRate = [
            {
              Fee: this.actionCurrentRate.feerate,
              FeeText: symbol
            }
          ];
        }
      } else if (
        (this.currentTabel1Item.feeratetype === 2 &&
          this.currentTabel1Item.feeratesubtype === 2) ||
        (this.currentTabel1Item.feeratetype === 4 &&
          this.currentTabel1Item.feeratesubtype === 2)
      ) {
        // 分时费率
        var maxNum = 0;
        this.CetTable_2.data.forEach(item => {
          if (item.data && item.data.length > 0) {
            if (item.data.length > maxNum) {
              maxNum = item.data.length;
            }
            item.data.forEach(i => {
              if (i.rate || i.rate === 0) {
                i.rate = i.rate / 100000;
              }
            });
          }
        });
        this.ElTableColumnArr = [
          {
            label: $T("时段方案"),
            prop: "dailyName",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            fixed: "left",
            formatter: (val, prop) => {
              if (this._.get(val, prop) || this._.get(val, prop) === 0) {
                return this._.get(val, prop);
              } else {
                return "--";
              }
            }
          },
          {
            label: $T("生效时间"),
            prop: "effectivedate$text",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            minWidth: 200,
            formatter: (val, prop) => {
              if (this._.get(val, prop) || this._.get(val, prop) === 0) {
                return this._.get(val, prop);
              } else {
                return "--";
              }
            }
          }
        ];
        for (let i = 0; i < maxNum; i++) {
          this.ElTableColumnArr.push({
            label: $T("时段"),
            prop: `data[${i}].identification`,
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              if (this._.get(val, prop) || this._.get(val, prop) === 0) {
                return this._.get(val, prop);
              } else {
                return "--";
              }
            }
          });
          this.ElTableColumnArr.push({
            label: $T("时间"),
            prop: `data[${i}].timePeriod`,
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            minWidth: 90,
            formatter: (val, prop) => {
              if (this._.get(val, prop) && this._.get(val, prop).length > 0) {
                return this._.get(val, prop).join(";");
              } else {
                return "--";
              }
            }
          });
          this.ElTableColumnArr.push({
            label: `${$T("费率")}（${symbol}）`,
            prop: `data[${i}].rate`,
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            width: 140,
            formatter: (val, prop) => {
              if (this._.get(val, prop) || this._.get(val, prop) === 0) {
                return this._.get(val, prop);
              } else {
                return "--";
              }
            }
          });
        }
        this.ElTableColumnArr.push({
          type: "tag",
          label: $T("当前状态"),
          prop: "status$text",
          showOverflowTooltip: true,
          headerAlign: "left",
          align: "left",
          fixed: "right",
          minWidth: 100
        });
        var currentRate = [];
        if (
          this.actionCurrentRate &&
          this.actionCurrentRate.data &&
          this.actionCurrentRate.data.length > 0
        ) {
          this.actionCurrentRate.data.forEach(item => {
            currentRate.push({
              Fee: item.rate || item.rate === 0 ? item.rate : "--",
              FeeText: symbol,
              bottomTitle: `${item.identification || "--"} ${$T("时段")}${$T(
                "费率"
              )}（${item.timePeriod || "--"}）`
            });
          });
        }
        this.currentRate = currentRate;
      } else if (
        (this.currentTabel1Item.feeratetype === 2 &&
          this.currentTabel1Item.feeratesubtype === 3) ||
        (this.currentTabel1Item.feeratetype === 4 &&
          this.currentTabel1Item.feeratesubtype === 3)
      ) {
        if (this.CetTable_2.data.length > 0) {
          this.CetTable_2.data.forEach(item => {
            if (item.stagefeeset_model && item.stagefeeset_model.length > 0) {
              item.stagefeeset_model.forEach((ite, index) => {
                // 处理单位
                ite.rate = ite.rate / 100000;
                var stagefeeset$text = "";
                if (item.stagefeeset_model[index + 1]) {
                  stagefeeset$text = `${ite.lowlimit} ~ ${
                    item.stagefeeset_model[index + 1].lowlimit
                  }`;
                } else {
                  stagefeeset$text = `>${ite.lowlimit}`;
                }
                Vue.set(ite, "stagefeeset$text", stagefeeset$text);
              });
            }
          });
        }
        // 阶梯费率
        this.ElTableColumnArr = [
          {
            label: $T("生效时间"),
            prop: "effectivedate",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              return this.$moment(val.effectivedate).format("YYYY-MM");
            }
          },
          {
            label: $T("第一阶梯用量"),
            prop: "stagefeeset_model[0].stagefeeset$text",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              if (this._.get(val, prop) || this._.get(val, prop) === 0) {
                return this._.get(val, prop);
              } else {
                return "--";
              }
            }
          },
          {
            label: $T("费率") + `（${symbol}）`,
            prop: "stagefeeset_model[0].rate",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              if (this._.get(val, prop) || this._.get(val, prop) === 0) {
                return this._.get(val, prop);
              } else {
                return "--";
              }
            }
          },
          {
            label: $T("第二阶梯用量"),
            prop: "stagefeeset_model[1].stagefeeset$text",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              if (this._.get(val, prop) || this._.get(val, prop) === 0) {
                return this._.get(val, prop);
              } else {
                return "--";
              }
            }
          },
          {
            label: `${$T("费率")}（${symbol}）`,
            prop: "stagefeeset_model[1].rate",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              if (this._.get(val, prop) || this._.get(val, prop) === 0) {
                return this._.get(val, prop);
              } else {
                return "--";
              }
            }
          },
          {
            label: $T("第三阶梯用量"),
            prop: "stagefeeset_model[2].stagefeeset$text",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              if (this._.get(val, prop) || this._.get(val, prop) === 0) {
                return this._.get(val, prop);
              } else {
                return "--";
              }
            }
          },
          {
            label: `${$T("费率")}（${symbol}）`,
            prop: "stagefeeset_model[2].rate",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              if (this._.get(val, prop) || this._.get(val, prop) === 0) {
                return this._.get(val, prop);
              } else {
                return "--";
              }
            }
          },
          {
            type: "tag",
            label: $T("当前状态"),
            prop: "status$text",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left"
          }
        ];
        if (
          this.actionCurrentRate &&
          this.actionCurrentRate.stagefeeset_model &&
          this.actionCurrentRate.stagefeeset_model.length > 0
        ) {
          this.currentRate = this.actionCurrentRate.stagefeeset_model.map(
            (item, index) => {
              return {
                Fee: item.rate,
                FeeText: symbol,
                bottomTitle: `${$T("第")}${
                  index === 0
                    ? $T("一")
                    : index === 1
                    ? $T("二")
                    : index === 2
                    ? $T("三")
                    : ""
                }${$T("阶梯")}（${item.stagefeeset$text}）`
              };
            }
          );
        } else {
          this.currentRate = [];
        }
      } else if (this.currentTabel1Item.feeratetype === 3) {
        // 力调电费
        // 处理单位
        if (this.CetTable_2.data.length > 0) {
          this.CetTable_2.data.forEach(item => {
            if (
              item.powertarifffactor_model &&
              item.powertarifffactor_model.length > 0
            ) {
              item.powertarifffactor_model.forEach((ite, index) => {
                // 处理单位
                ite.punishrate = Number((ite.punishrate * 100).toFixed2(2));
              });
            }
            // item.fee = item.name.replace(/[^(0-9) | .]/gi, "");
          });
        }
        this.ElTableColumnArr = [
          {
            label: $T("生效时间"),
            prop: "effectivedate",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              return this.$moment(val.effectivedate).format("YYYY-MM");
            }
          },
          {
            label: $T("力调费率考核标准"),
            prop: "name",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              if (this._.get(val, prop) || this._.get(val, prop) === 0) {
                return this._.get(val, prop);
              } else {
                return "--";
              }
            }
          },
          {
            type: "tag",
            label: $T("当前状态"),
            prop: "status$text",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left"
          }
        ];
        if (this.actionCurrentRate) {
          this.currentRate = [
            {
              Fee: this.actionCurrentRate.name,
              bottomTitle: $T("力调费率考核标准")
            }
          ];
        }
      } else if (this.currentTabel1Item.feeratetype === 4) {
        // 处理单位
        if (this.CetTable_2.data.length > 0) {
          this.CetTable_2.data.forEach(item => {
            item.feerate = item.feerate / 100000;
          });
        }
        // 附加费
        this.ElTableColumnArr = [
          {
            label: $T("生效时间"),
            prop: "effectivedate",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              return this.$moment(val.effectivedate).format("YYYY-MM");
            }
          },
          {
            label: `${$T("费率")}（${symbol}）`,
            prop: "feerate",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left",
            formatter: (val, prop) => {
              if (this._.get(val, prop) || this._.get(val, prop) === 0) {
                return this._.get(val, prop);
              } else {
                return "--";
              }
            }
          },
          {
            type: "tag",
            label: $T("当前状态"),
            prop: "status$text",
            showOverflowTooltip: true,
            headerAlign: "left",
            align: "left"
          }
        ];
        if (this.actionCurrentRate) {
          this.currentRate = [
            {
              Fee: this.actionCurrentRate.feerate,
              FeeText: symbol
            }
          ];
        }
      }
      var dateArr;
      if (tabData.length) {
        dateArr = tabData.map(item =>
          this.$moment(item.effectivedate).format("YYYY-MM")
        );
      }
      // 传入已有记录的时间
      this.AddRateScheme.dateArr = dateArr;

      this.CetTable_2.showColumn = false;
      this.$nextTick(() => {
        this.CetTable_2.showColumn = true;
      });
    },
    CetButton_1_statusTrigger_out(val) {
      this.AddRateScheme.recordId = 0;
      this.tableData = [];
      this.AddRateScheme.inputData_in = {
        energytype: this.ElSelect_1.value || 2
      };
      this.AddRateScheme.disableDate = false;
      this.AddRateScheme.visibleTrigger_in = this._.cloneDeep(val);
    },
    editRate(val) {
      var inputDataObj = {};
      var actionCurrentRate =
        this.actionCurrentRate || this.CetTable_2.data[0] || {};
      if (val) {
        actionCurrentRate = val;
      }
      /*
      // 容量费率 volumefeerecord
      // 需量费率 demandfeerecord
      // 单一费率 singlefeerecord
      // 阶梯费率 stagefeerecord
      // 力调费率 powertariff
      // 附加费 surchargefeerecord
      */
      if (
        this.currentTabel1Item.energytype === 2 &&
        this.currentTabel1Item.feeratetype === 1 &&
        this.currentTabel1Item.feeratesubtype === 1
      ) {
        // 容量费率 volumefeerecord
        inputDataObj = {
          id: this.currentTabel1Item.id,
          name: this.currentTabel1Item.name,
          feeratetype: this.currentTabel1Item.feeratetype, // 1: 基本电费 2： 电度电费 3 : 力调电费 4 :附加费
          effectivedate: actionCurrentRate.effectivedate, //生效时间
          energytype: this.currentTabel1Item.energytype, // 能源类型
          rateType: this.currentTabel1Item.feeratesubtype, // 1: 容量计费 2： 需量计费
          volumefeerate: actionCurrentRate.feerate // 容量计费值
        };
      } else if (
        // 需量费率 demandfeerecord
        this.currentTabel1Item.energytype === 2 &&
        this.currentTabel1Item.feeratetype === 1 &&
        this.currentTabel1Item.feeratesubtype === 2
      ) {
        inputDataObj = {
          id: this.currentTabel1Item.id,
          name: this.currentTabel1Item.name,
          feeratetype: this.currentTabel1Item.feeratetype, // 1: 基本电费 2： 电度电费 3 : 力调电费 4 :附加费
          effectivedate: actionCurrentRate.effectivedate, //生效时间
          energytype: this.currentTabel1Item.energytype, // 能源类型
          rateType: this.currentTabel1Item.feeratesubtype, // 1: 容量计费 2： 需量计费
          demandfeerate: actionCurrentRate.feerate, // 需量计费值
          highLimit: actionCurrentRate.highlimit,
          punishRate: actionCurrentRate.punishrate,
          calculatedeviation: actionCurrentRate.calculatedeviation
        };
      } else if (
        (this.currentTabel1Item.feeratetype === 2 &&
          this.currentTabel1Item.feeratesubtype === 1) ||
        (this.currentTabel1Item.feeratetype === 4 &&
          this.currentTabel1Item.feeratesubtype === 1)
      ) {
        // 单一费率 singlefeerecord
        inputDataObj = {
          id: this.currentTabel1Item.id,
          name: this.currentTabel1Item.name,
          feeratetype: this.currentTabel1Item.feeratetype, // 1: 基本电费 2： 电度电费 3 : 力调电费 4 :附加费
          effectivedate: actionCurrentRate.effectivedate, //生效时间
          electricitychargerate: this.currentTabel1Item.feeratesubtype,
          energytype: this.currentTabel1Item.energytype, // 能源类型
          feerate: actionCurrentRate.feerate // 单一费率值
        };
      } else if (
        (this.currentTabel1Item.feeratetype === 2 &&
          this.currentTabel1Item.feeratesubtype === 2) ||
        (this.currentTabel1Item.feeratetype === 4 &&
          this.currentTabel1Item.feeratesubtype === 2)
      ) {
        // 分时费率
        inputDataObj = {
          id: this.currentTabel1Item.id,
          name: this.currentTabel1Item.name,
          feeratetype: this.currentTabel1Item.feeratetype, // 1: 基本电费 2： 电度电费 3 : 力调电费 4 :附加费
          electricitychargerate: this.currentTabel1Item.feeratesubtype,
          energytype: this.currentTabel1Item.energytype, // 能源类型
          timeShareScheme_id: this.timeShareId //分时方案id
        };
      } else if (
        (this.currentTabel1Item.feeratetype === 2 &&
          this.currentTabel1Item.feeratesubtype === 3) ||
        (this.currentTabel1Item.feeratetype === 4 &&
          this.currentTabel1Item.feeratesubtype === 3)
      ) {
        // 阶梯费率 stagefeerecord
        inputDataObj = {
          id: this.currentTabel1Item.id,
          name: this.currentTabel1Item.name,
          feeratetype: this.currentTabel1Item.feeratetype, // 1: 基本电费 2： 电度电费 3 : 力调电费 4 :附加费
          effectivedate: actionCurrentRate.effectivedate, //生效时间
          electricitychargerate: this.currentTabel1Item.feeratesubtype,
          energytype: this.currentTabel1Item.energytype, // 能源类型
          stagefeerateTableData: []
        };
        var stagefeerateTableData = [];
        if (
          actionCurrentRate.stagefeeset_model &&
          actionCurrentRate.stagefeeset_model.length > 0
        ) {
          actionCurrentRate.stagefeeset_model.forEach((item, index) => {
            stagefeerateTableData.push({
              energyNum: item.lowlimit,
              name: item.name,
              rate: item.rate
            });
          });
        }
        inputDataObj.stagefeerateTableData = stagefeerateTableData;
      } else if (this.currentTabel1Item.feeratetype === 3) {
        // 力调费率 powertariff
        inputDataObj = {
          id: this.currentTabel1Item.id,
          name: this.currentTabel1Item.name,
          feeratetype: this.currentTabel1Item.feeratetype, // 1: 基本电费 2： 电度电费 3 : 力调电费 4 :附加费
          effectivedate: actionCurrentRate.effectivedate, //生效时间
          electricitychargerate: this.currentTabel1Item.feeratesubtype,
          energytype: this.currentTabel1Item.energytype, // 能源类型
          powertariff_model: actionCurrentRate
        };
      } else if (this.currentTabel1Item.feeratetype === 4) {
        // 附加费 surchargefeerecord
        inputDataObj = {
          id: this.currentTabel1Item.id,
          name: this.currentTabel1Item.name,
          feeratetype: this.currentTabel1Item.feeratetype, // 1: 基本电费 2： 电度电费 3 : 力调电费 4 :附加费
          effectivedate: actionCurrentRate.effectivedate, //生效时间
          energytype: this.currentTabel1Item.energytype, // 能源类型
          feerate: actionCurrentRate.feerate, // 单一费率值
          fjfName: actionCurrentRate.name
        };
      }
      this.AddRateScheme.inputData_in = this._.cloneDeep(inputDataObj);
      this.AddRateScheme.visibleTrigger_in = new Date().getTime();
    },
    handleDeleteRate(row) {
      if (!row.allowDelete) return;
      this.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            httping({
              url: `/eem-service/v1/schemeConfig/feeScheme/${row.id}`,
              method: "DELETE"
            }).then(response => {
              if (response.code === 0) {
                this.$message({
                  message: $T("删除成功"),
                  type: "success"
                });
                this.getTabelData();
              }
            });
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action !== "confirm") {
            this.$message({
              type: "info",
              message: $T("取消删除！")
            });
          }
        }
      });
    },
    ElSelect_1_change_out(val) {
      this.getTabelData();
    },
    CetTable_1_outputData_out(val) {},
    CetTable_1_record_out(val) {
      if (val.id === -1) {
        this.CetButton_2.disable_in = true;
        // 重置
        this.CetTable_2.data = [];
        this.currentRate = [];
        this.currentTabel1Item = null;
        return;
      } else {
        this.CetButton_2.disable_in = false;
      }
      this.currentTabel1Item = val;
      // 判断费率类型
      if (
        (val.feeratesubtype === 2 && val.feeratetype === 2) ||
        (val.feeratesubtype === 2 && val.feeratetype === 4)
      ) {
        // 分时费率
        this.getTimeFeeSchemeDetail();
      } else {
        // 非分时费率
        this.getFeeSchemeDetail();
      }
    },
    CetTable_2_outputData_out(val) {},
    CetTable_2_record_out(val) {
      if (val.id !== -1) {
        this.currentTabel1Item2 = val;
      } else {
        this.currentTabel1Item2 = null;
      }
    },
    handleDelete(index, row) {
      if (row.status$text !== $T("未生效")) return;
      var data = row;
      this.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            httping({
              url: `/eem-service/v1/schemeConfig/feeRecord/${data.id}/${data.modelLabel}`,
              method: "DELETE"
            }).then(response => {
              if (response.code === 0) {
                this.$message({
                  message: $T("删除成功"),
                  type: "success"
                });
                // 判断费率类型
                if (
                  (data.feeratesubtype === 2 && data.feeratetype === 2) ||
                  (data.feeratesubtype === 2 && data.feeratetype === 4)
                ) {
                  // 分时费率
                  this.getTimeFeeSchemeDetail();
                } else {
                  // 非分时费率
                  this.getFeeSchemeDetail();
                }
              }
            });
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action !== "confirm") {
            this.$message({
              type: "info",
              message: $T("取消删除！")
            });
          }
        }
      });
    },
    handleEdit(index, row) {
      if (row.status$text === $T("已过期")) return;
      this.tableData = this._.cloneDeep(this.CetTable_2.data);
      this.AddRateScheme.recordId = row.id;
      this.AddRateScheme.disableDate = true;
      this.AddRateScheme.detailVisible = false;
      this.editRate(row);
    },
    // 力调已过期查看详情
    handleDetail(index, row) {
      this.tableData = this._.cloneDeep(this.CetTable_2.data);
      this.AddRateScheme.recordId = row.id;
      this.AddRateScheme.disableDate = true;
      this.AddRateScheme.detailVisible = true;
      this.editRate(row);
    },
    AddRateScheme_currentData_out(val) {},
    AddRateScheme_finishData_out(val) {},
    AddRateScheme_finishTrigger_out(val) {
      this.getTabelData();
    },
    AddRateScheme_saveData_out(val) {}
  },
  created: function () {
    this.getEnergytype();
  },
  mounted: function () {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .basic-box {
    flex: 1;
    .basic-box-label {
      height: 32px;
      line-height: 32px;
    }
  }
  .status {
    padding: 2px 20px;
  }
}

.row-edit {
  width: 20px;
  height: 20px;
  display: inline-block;
  cursor: pointer;
  background: url("./assets/edit.png") no-repeat center center;
}

.row-delete {
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: inline-block;
  background: url("./assets/delete.png") no-repeat center center;
}
.el-tag.el-tag--status3 {
  background-color: #f85d59;
  border-color: #f85d59;
  color: #fff;
}
.el-tag.el-tag--status2 {
  background-color: #7881a0;
  border-color: #7881a0;
  color: #fff;
}
.el-tag.el-tag--status1 {
  background-color: #28af60;
  border-color: #28af60;
  color: #fff;
}
.clickformore {
  cursor: pointer;
  @include font_color(ZS);
  &.delete {
    @include font_color(Sta3);
  }
}
.clickdisable,
.clickdisable.delete {
  @include font_color(T4);
  cursor: not-allowed !important;
}
</style>
