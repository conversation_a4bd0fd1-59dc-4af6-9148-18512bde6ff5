<template>
  <div class="login-normal">
    <el-form :model="login" :rules="rules" ref="form">
      <FormLineItem symbolId="user-one-lin" :label="$T('账号')">
        <el-form-item prop="userName">
          <el-input :placeholder="$T('请输入账号')" v-model="login.userName" />
        </el-form-item>
      </FormLineItem>
      <FormLineItem symbolId="password-lin" :label="$T('密码')">
        <el-form-item prop="passWord">
          <el-input
            :placeholder="$T('请输入密码')"
            v-model="login.passWord"
            show-password
          />
        </el-form-item>
      </FormLineItem>
      <el-button
        class="login-btn"
        type="primary"
        size="medium"
        @click="evLoginBtnClick"
      >
        {{ $T("登录") }}
      </el-button>
    </el-form>
    <div v-if="showErrMsg" class="mtJ1 errMsg">
      {{ $T("连续输入密码错误5次，账号将会锁定，1h后自动解锁！") }}
    </div>
    <ResetPassword v-bind="ResetPassword" v-on="ResetPassword.event" />
  </div>
</template>

<script>
import FormLineItem from "./formLineItem.vue";
import omegaAuth from "@omega/auth";
import md5 from "crypto-js/md5";
import { encrypt, decryptAttributes } from "eem-utils/crypto.js";
import { HttpBase } from "@omega/http";
import ResetPassword from "./ResetPassword.vue";
export default {
  name: "LoginNormal",
  components: { FormLineItem, ResetPassword },
  data() {
    return {
      showErrMsg: false,
      ResetPassword: {
        openTrigger_in: new Date().getTime(),
        inputData_in: {},
        event: {
          refresh_out: this.ResetPassword_refresh_out
        }
      },
      login: {
        userName: "",
        passWord: ""
      },
      rules: {
        userName: [
          {
            required: true,
            message: $T("账号不能为空"),
            trigger: "change"
          }
        ],
        passWord: [
          {
            required: true,
            message: $T("密码不能为空"),
            trigger: "change"
          }
        ]
      }
    };
  },
  methods: {
    async evLoginBtnClick() {
      await this.$refs.form.validate();

      await omegaAuth.loginByApi(this.loginFn());
    },
    async loginFn() {
      const httping = new HttpBase(
        { silent: true },
        {
          headers: {
            post: {
              "Content-Type": "application/json;charset=UTF-8 "
            }
          },
          responseType: "json"
        }
      );
      const userName = this.login.userName,
        password = this.login.passWord,
        nowTime = new Date().getTime();
      const appName = "CET_Matterhorn";
      const passwordDigest = md5(
        `${appName}#${password}#${nowTime}`
      ).toString();
      const encrytedUserName = encrypt(userName);
      let res;
      try {
        res = await httping({
          url: `/eem-service/v1/auth/common/login`,
          method: "post",
          headers: { hideNotice: true },
          data: {
            userName: encrytedUserName,
            password: passwordDigest,
            timestamps: nowTime,
            client: "web"
          }
        });
      } catch (error) {
        res = error;
      }
      const code = this._.get(res, "code");

      this.showErrMsg = false;
      if (code === 0) {
        // 登录成功
        this.showErrMsg = false;
      } else if (code === -1305) {
        // 密码过期
        this.$message.warning($T("账户密码过期，请修改密码！"));
        this.ResetPassword.inputData_in.userName = this.login.userName;
        this.ResetPassword.openTrigger_in = +new Date();
        return;
      } else if (
        code === -1 &&
        ["用户名或者密码有误", "Incorrect username or password"].includes(
          res.msg
        )
      ) {
        // 密码错误
        this.showErrMsg = true;
        return;
      } else {
        this.showErrMsg = false;
        this.$message.error(this._.get(res, "msg"));
        return;
      }
      let user = this._.get(res, "data.user");
      if (user) {
        user = decryptAttributes(user, [
          "name",
          "nicName",
          "email",
          "mobilePhone"
        ]);
      }
      return {
        token: this._.get(res, "data.token"),
        user: user
      };
    },
    ResetPassword_refresh_out(userName, passWord) {
      this.login.userName = userName;
      this.login.passWord = passWord;
      this.evLoginBtnClick();
    }
  }
};
</script>

<style lang="scss" scoped>
.login-btn {
  @include margin_top(J4);
  @include font_color(T5);
  width: 100%;
}
.errMsg {
  @include font_color(Sta3);
  text-align: center;
}
</style>
