<template>
  <div class="page">
    <div class="head">
      <div class="flex-end">
        <CustomElDatePicker
          :prefix_in="$T('查询月份')"
          :clearable="false"
          style="width: 200px"
          v-model="queryTime"
          type="month"
          :placeholder="$T('选择时间')"
          @change="changeQueryTime"
          value-format="timestamp"
          :picker-options="pickerOptions"
        />
        <!-- update按钮组件 -->
        <CetButton
          class="mlJ1"
          v-bind="CetButton_update"
          v-on="CetButton_update.event"
        ></CetButton>
        <!-- export按钮组件 -->
        <CetButton
          class="mlJ1"
          v-bind="CetButton_export"
          v-on="CetButton_export.event"
        ></CetButton>
      </div>
    </div>
    <div class="content">
      <div class="report-box">
        <reportContent
          id="reportContent"
          ref="reportContent"
          :reportData="reportData"
          :queryTime="queryTime"
        />
      </div>
    </div>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import reportContent from "./components/reportContent.vue";
import moment from "moment";
export default {
  name: "energySavingReport",
  components: { reportContent },
  data() {
    return {
      queryTime: moment().subtract(1, "days").valueOf(),
      reportData: {},
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > moment().subtract(1, "days").valueOf();
        }
      },
      // update组件
      CetButton_update: {
        visible_in: true,
        disable_in: false,
        title: $T("更新数据"),
        event: {
          statusTrigger_out: this.CetButton_update_statusTrigger_out
        }
      },
      // export组件
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: $T("导出PDF"),
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      }
    };
  },
  watch: {},
  methods: {
    /**
     * 切换查询时间
     */
    changeQueryTime(val) {
      this.queryEnergySavingReport();
    },

    /**
     * 查询报告数据
     */
    queryEnergySavingReport() {
      const params = {
        logTime: this.$moment(this.queryTime).startOf("M").valueOf()
      };
      customApi.queryEnergySavingReport(params).then(res => {
        this.reportData = res.data || {};
      });
    },

    /**
     * 更新数据
     */
    CetButton_update_statusTrigger_out() {
      const params = {
        logTime: this.$moment(this.queryTime).startOf("M").valueOf()
      };
      customApi.updateEnergySavingReport(params).then(res => {
        if (res.code === 0) {
          this.$message.success($T("更新成功"));
          this.queryEnergySavingReport();
        }
      });
    },

    /**
     * 导出PDF
     */
    CetButton_export_statusTrigger_out() {
      setTimeout(() => {
        document.body.style.color = "#000";
        document.body.innerHTML =
          document.getElementById("reportContent").innerHTML;
        let fileName = $T("节能优化评估报告");
        document.title = fileName;
        setTimeout(() => {
          window.print();
          window.location.reload();
        }, 1000);
      }, 1000);
    }
  },
  mounted() {
    this.queryEnergySavingReport();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  color: #333;
}
.head {
  height: 32px;
  line-height: 32px;
  margin-bottom: 16px;
  display: flex;
}
.flex-end {
  justify-content: flex-end;
  margin-left: auto;
}
.content {
  height: calc(100% - 48px);
  background-color: #e6e8eb;
  padding: 16px;
  box-sizing: border-box;
}
.report-box {
  background-color: #fff;
  width: 800px;
  height: 100%;
  overflow-x: hidden;
  overflow-y: scroll;
  margin: 0 auto;
}
</style>
