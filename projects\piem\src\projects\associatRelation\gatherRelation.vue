<template>
  <div class="page">
    <el-aside
      class="mainBox nodeBox leftTree"
      :class="{ 'aside-collapsed': collapsed }"
      :width="asideWidth"
    >
      <div v-show="!collapsed" class="title">选择管网设备</div>
      <div v-show="!collapsed" style="padding: 0px; height: calc(100% - 58px)">
        <CetTree
          class="switch-tree"
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          :searchText_in.sync="CetTree_1.searchText_in"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
        ></CetTree>
      </div>
      <div class="collapse-btn" @click="onCollapseBtnClick">
        {{ collapseText }}
      </div>
    </el-aside>
    <el-aside class="mainBox deviceBox">
      <div class="title">关联采集设备</div>
      <CetTree
        style="height: calc(100% - 40px)"
        :selectNode.sync="CetTree_2.selectNode"
        :checkedNodes.sync="CetTree_2.checkedNodes"
        :searchText_in.sync="CetTree_2.searchText_in"
        v-bind="CetTree_2"
        v-on="CetTree_2.event"
      ></CetTree>
    </el-aside>
    <el-aside class="mainBox relatedBox">
      <div class="title">已关联采集设备列表</div>
      <div style="padding: 0px; height: calc(100% - 40px)">
        <CetTable
          ref="deviceTable"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <template v-for="item in Columns_1">
            <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
          </template>
          <el-table-column label="操作" header-align="center" width="80">
            <template slot-scope="scope">
              <el-button
                class="deleteBtn"
                icon="el-icon-delete"
                @click.stop="handleDelete(scope.row)"
              ></el-button>
            </template>
          </el-table-column>
        </CetTable>
      </div>
    </el-aside>
    <el-main class="mainBox measuringPointBox">
      <div class="title">采集设备测点</div>
      <CetButton
        class="fr"
        v-bind="CetButton_save"
        v-on="CetButton_save.event"
      ></CetButton>
      <el-table
        class="cetTable"
        ref="multipleTable"
        :data="tableData"
        tooltip-effect="light"
        style="width: 100%"
        height="calc(100% - 50px)"
        border
        stripe
        highlight-current-row
        @selection-change="handleSelectionChange"
      >
        <ElTableColumn v-bind="ElTableColumn_selection"></ElTableColumn>
        <ElTableColumn v-bind="ElTableColumn_paraName"></ElTableColumn>
        <ElTableColumn v-bind="ElTableColumn_dataId"></ElTableColumn>
        <ElTableColumn
          v-bind="ElTableColumn_logicalDeviceIndex"
        ></ElTableColumn>
      </el-table>
    </el-main>
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom";
import { httping } from "@omega/http";
export default {
  name: "gatherRelation",
  computed: {
    userInfo() {
      return this.$store.state.userInfo;
    },
    asideWidth() {
      return this.collapsed ? "15px!important" : "360px";
    },
    collapseText() {
      return this.collapsed ? ">" : "<";
    }
  },
  data() {
    return {
      collapsed: false,
      ajaxFlag: false,
      tableData: [],
      measuredDevices: [], // 关联的设备列表
      deviceItemActionIndex: -1,
      currentNode: null,
      currentTab: { id: -1 },
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children",
          isLeaf: "leaf",
          disabled: "disabled"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        lazy: false,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      // 2树组件
      CetTree_2: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        nodeKey: "tree_id",
        props: {
          label: "text",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: true,
        event: {
          checkedNodes_out: this.CetTree_2_checkedNodes_out
        }
      },

      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out
        }
      },
      CetButton_save: {
        visible_in: true,
        disable_in: true,
        title: "保存",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_save_statusTrigger_out
        }
      },
      // 1组件
      Columns_1: [
        {
          type: "index",
          label: "序号",
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          width: "50"
        },
        {
          prop: "text",
          label: "采集对象",
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true
        }
      ],
      ElTableColumn_selection: {
        type: "selection",
        width: "60px",
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true
      },
      // 设置组件唯一识别字段组件
      ElTableColumn_paraName: {
        prop: "paraName",
        minWidth: "120px",
        label: "测点",
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_dataId: {
        prop: "dataId",
        minWidth: "120px",
        label: "测点ID",
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_logicalDeviceIndex: {
        prop: "logicalDeviceIndex",
        minWidth: "120px",
        label: "回路号",
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      }
    };
  },
  watch: {
    currentNode: {
      handler(val) {
        if (
          val &&
          ![
            "oilcompany",
            "room",
            "waterinjectionstation",
            "oiltransferstation",
            "combinedstation"
          ].includes(val.modelLabel)
        ) {
          this.CetButton_save.disable_in = false;
        } else {
          this.CetButton_save.disable_in = true;
        }
      }
    },
    currentTab: {
      handler(val, oldVal) {
        if (
          this.CetTable_1.data.length === 0 ||
          (oldVal && val.id === oldVal.id)
        )
          return;
        const index = this.CetTable_1.data.findIndex(
          item => item.id === val.id
        );
        this.getMonitoredNode(val, index);
      }
    },
    "CetTable_1.data": {
      handler(val, oldVal) {
        // 增加一条
        if (val.length - oldVal.length === 1) {
          const newItemIndex = val.findIndex(
            item => !oldVal.find(it => it.tree_id === item.tree_id)
          );
          this.$refs.deviceTable.$refs.cetTable.setCurrentRow(
            this.CetTable_1.data[newItemIndex]
          );
        }
        // 减少一条
        if (oldVal.length - val.length === 1 && val.length) {
          this.$refs.deviceTable.$refs.cetTable.setCurrentRow(
            this.CetTable_1.data[0]
          );
        }
      }
    }
  },
  methods: {
    onCollapseBtnClick() {
      this.collapsed = !this.collapsed;
    },
    handleDelete(row) {
      const vm = this;
      vm.$confirm("确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let checkIndex = vm.CetTree_2.checkedNodes.findIndex(
            item => item.tree_id === row.tree_id
          );
          vm.CetTree_2.checkedNodes = vm.CetTree_2.checkedNodes.filter(
            (item, index) =>
              item.modelLabel == "*********" && index !== checkIndex
          );
          if (vm.CetTree_2.checkedNodes.length) {
            vm.$refs.deviceTable.$refs.cetTable.setCurrentRow(
              vm.CetTable_1.data[0]
            );
          }
        })
        .catch(() => {
          vm.$message.info("已取消");
        });
    },
    handleSelectionChange(val) {
      if (
        !this.ajaxFlag &&
        this.CetTable_1.data &&
        this.CetTable_1.data.length > 0
      ) {
        this.CetTable_1.data[this.deviceItemActionIndex].monitoredNodeArr = val;
      }
    },
    // 设置节点树是否禁选
    setTreeCheckStatus(treeData, disable) {
      if (!treeData || treeData.length === 0) {
        return;
      }
      treeData.forEach(node => {
        node.disabled = disable;
        this.setTreeCheckStatus(this._.get(node, "children", []), disable);
      });
    },
    // 获取测点
    getMonitoredNode(item, index) {
      const vm = this;
      this.deviceItemActionIndex = index;
      this.tableData = [];
      if (this._.isEmpty(item) || !item.id) {
        return;
      }
      var queryData = {
        deviceId: item.id,
        node: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel
        }
      };
      this.ajaxFlag = true;
      customApi.getMonitoredNode(queryData).then(response => {
        if (response.code === 0) {
          var data = vm._.get(response, "data", []);
          vm.tableData = data;
          // 设置选中
          let monitoredNodeArr = [];
          data.forEach(item => {
            if (item.selected) {
              monitoredNodeArr.push(item);
            }
          });
          if (vm.CetTable_1.data.length > 0) {
            vm.CetTable_1.data[index].monitoredNodeArr = monitoredNodeArr;
            vm.tableData.forEach(item => {
              monitoredNodeArr.forEach(item2 => {
                if (
                  item.dataId == item2.dataId &&
                  item.dataTypeId == item2.dataTypeId &&
                  item.logicalDeviceIndex == item2.logicalDeviceIndex
                ) {
                  vm.$nextTick(() => {
                    vm.$refs.multipleTable.toggleRowSelection(item);
                  });
                }
              });
            });
          }
        }
        vm.$nextTick(() => {
          vm.ajaxFlag = false;
        });
      });
    },
    // 保存关联测点
    CetButton_save_statusTrigger_out() {
      const data = [
        {
          measureInfos: [],
          node: {
            deviceIds: [],
            id: this.currentNode.id,
            modelLabel: this.currentNode.modelLabel
          }
        }
      ];
      if (this.CetTable_1.data.length > 0) {
        this.CetTable_1.data.forEach(item => {
          if (item.monitoredNodeArr && item.monitoredNodeArr.length > 0) {
            item.monitoredNodeArr.forEach(i => {
              i.deviceId = item.id;
            });
            data[0].measureInfos.push(...item.monitoredNodeArr);
          }
          data[0].node.deviceIds.push(item.id);
        });
      }

      const params2 = [
        {
          modelLabel: this.currentNode.modelLabel,
          nodes: [
            {
              deviceIds: data[0].node.deviceIds,
              id: this.currentNode.id
            }
          ]
        }
      ];
      httping({
        url: "/eem-service/v1/quantity/quantityMap/measureInfo",
        data,
        method: "PUT"
      }).then(() => {
        this.Edit_dataSource(params2);
      });
    },
    // 获取管网设备节点树数据
    async getDeviceTreeData() {
      const vm = this;
      const params = {
        rootID: 1,
        rootLabel: "project",
        subLayerConditions: [
          {
            modelLabel: "room",
            filter: {
              expressions: [
                {
                  limit: [1, 2, 5, 6, 7, 8, 9],
                  operator: "IN",
                  prop: "roomtype"
                }
              ]
            }
          },
          { modelLabel: "pipeline" },
          { modelLabel: "plateheatexchanger" },
          { modelLabel: "colddryingmachine" },
          { modelLabel: "dryingmachine" },
          { modelLabel: "boiler" },
          { modelLabel: "linesegment" },
          { modelLabel: "powertransformer" },
          { modelLabel: "capacitor" },
          { modelLabel: "linesegmentwithswitch" },
          { modelLabel: "busbarsection" },
          { modelLabel: "meteringcabinet" },
          { modelLabel: "busbarconnector" },
          { modelLabel: "ptcabinet" },
          { modelLabel: "powerdiscabinet" },
          { modelLabel: "switchcabinet" },
          { modelLabel: "arraycabinet" },
          { modelLabel: "ups" },
          { modelLabel: "battery" },
          { modelLabel: "hvdc" },
          { modelLabel: "interchanger" },
          { modelLabel: "dcpanel" },
          { modelLabel: "generator" },
          { modelLabel: "itcabinet" },
          { modelLabel: "ats" },
          { modelLabel: "avc" }
        ],
        treeReturnEnable: true
      };
      const treeRes = await customApi.queryNodeTreeConfig({
        configtype: 1,
        functionkey: "a31d2d0a-786c-2f77-3924-35d7d5fa1809"
      });
      const queryData = treeRes.data[0]?.value
        ? JSON.parse(treeRes.data[0]?.value)
        : params;
      const res = await customApi.getNodeTree(queryData);

      vm.CetTree_1.inputData_in = vm._.cloneDeep(
        vm._.get(res, "data", []) || []
      );
      if (vm.CetTree_1.inputData_in.length && !vm._.isEmpty(vm.currentNode)) {
        let flag = await vm.nodeIsExist(vm.currentNode.tree_id);
        if (flag) {
          vm.CetTree_1_currentNode_out(vm.currentNode);
        } else {
          vm.CetTree_2.checkedNodes = [];
        }
      }
    },
    // 获取采集设备节点树
    async getCollectTreeData() {
      const vm = this;
      const queryData = {
        loadDevice: true,
        async: false,
        tenantId: vm.userInfo.tenantId,
        nodeId: 0,
        nodeType: 269615104
      };
      const res = await httping({
        url: "/eem-service/v2/peccore/pecCoreMeterTree",
        method: "POST",
        data: queryData
      });
      var data = vm._.get(res, "data", []);
      vm.setPecCoreTreeLeaf(data);
      vm.setTreeCheckStatus(data, true);
      vm.CetTree_2.inputData_in = vm._.cloneDeep(data);
    },
    setPecCoreTreeLeaf(nodes) {
      if (nodes && nodes.length > 0) {
        nodes.forEach(item => {
          item.id = item.nodeId;
          item.modelLabel = item.nodeType;
          item.tree_id = item.modelLabel + "_" + item.id;
          this.setPecCoreTreeLeaf(this._.get(item, "children", []));
        });
      }
    },
    // 查询关系
    querySupplyRelation_out(val) {
      if (!val) {
        return;
      }
      const me = this;
      const param = [
        {
          modelLabel: val.modelLabel,
          id: val.id
        }
      ];
      common.requestData(
        {
          url: `/eem-service/v1/connect/measureBy`,
          data: param
        },
        (data, res) => {
          if (res.code === 0) {
            let checkedNodes = me._.get(res, "data", []).map(item => ({
              deviceName: item.deviceName,
              dataId: item.id,
              modelLabel: "*********",
              id: item.measuredby,
              tree_id: "*********_" + item.measuredby
            }));
            me.measuredDevices = me._.cloneDeep(checkedNodes);
            me.CetTable_1.data = me._.cloneDeep(checkedNodes);
            me.CetTree_2.checkedNodes = me._.cloneDeep(checkedNodes);
            let index = -1,
              item;
            if (me.CetTable_1.data && me.CetTable_1.data.length > 0) {
              index = 0;
              item = me.CetTable_1.data[0];
            }
            me.getMonitoredNode(item, index);
          }
        },
        () => {
          me.CetTable_1.data = [];
          me.CetTree_2.checkedNodes = [];
          me.getMonitoredNode(null, -1);
        }
      );
    },
    // 更新物理量数据源
    Edit_dataSource(data) {
      const vm = this;
      httping({
        url: "/eem-service/v1/quantity/quantityObject?dataSource=1",
        method: "POST",
        data
      }).then(response => {
        if (response.code === 0) {
          vm.$message.success("保存成功");
          if (vm._.isEmpty(vm.currentTab)) return;
          const index = vm.CetTable_1.data.findIndex(
            item => item.id === vm.currentTab.id
          );
          vm.getMonitoredNode(vm.currentTab, index);
        }
      });
    },
    // 1表格输出
    CetTable_1_record_out(val) {
      if (val.id === -1) {
        this.tableData = [];
        this.currentTab = null;
        return;
      }
      this.currentTab = this._.cloneDeep(val);
    },
    CetTree_1_currentNode_out(val) {
      if (this._.isEmpty(val) || val.id === -1) {
        return;
      }
      if (
        [
          "oilcompany",
          "room",
          "waterinjectionstation",
          "oiltransferstation",
          "combinedstation",
          "gasplatform",
          "dehydratingstation",
          "gasgatheringstation",
          "purificationplant"
        ].includes(val.modelLabel)
      ) {
        this.$message.warning("请选择设备节点");
        this.setTreeCheckStatus(this.CetTree_2.inputData_in, true);
        this.currentNode = null;
        this.CetTable_1.data = [];
        this.CetTree_2.checkedNodes = [];
        this.getMonitoredNode(false, -1);
      } else {
        this.setTreeCheckStatus(this.CetTree_2.inputData_in, false);
        this.currentNode = this._.cloneDeep(val);
        this.querySupplyRelation_out(val);
      }
    },
    // 2 输出
    CetTree_2_checkedNodes_out(val) {
      if (val && val.length > 0) {
        this.CetTable_1.data = val.filter(item => item.nodeType === *********);
      } else {
        this.CetTable_1.data = [];
      }
    },
    // 判断节点是否在节点树上
    async nodeIsExist(treeNodeId) {
      const [rootLabel, rootID] = treeNodeId.split("_");
      const data = {
        rootID,
        rootLabel,
        subLayerConditions: [],
        treeReturnEnable: true
      };
      const res = await customApi.getNodeTree(data);
      const resData = this._.get(res, "data", []) || [];
      return resData.length > 0;
    }
  },

  mounted() {
    this.getDeviceTreeData();
    this.getCollectTreeData();
  },
  activated() {
    this.getDeviceTreeData();
    this.getCollectTreeData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  display: flex;
}
.nodeBox {
  width: 360px !important;
  height: 100%;
}
.deviceBox {
  @include margin_left(J3);
  width: 360px !important;
}
.relatedBox {
  @include margin_left(J3);
  width: 360px !important;
}
.measuringPointBox {
  width: calc(100% - 1142px);
  @include margin_left(J3);
}
.mainBox {
  height: 100%;
  @include padding(J3);
  @include background_color(BG1);
  display: inline-block;
}
.title {
  display: inline-block;
  height: 40px;
  @include font_size(H1);
  font-weight: 600;
}
.cetTable :deep(.el-table__row--striped) {
  background: none;
}
.deleteBtn {
  width: 40px;
  height: 40px;
  border: none;
  background: none;
}
.deleteBtn :deep(.el-button:hover) {
  border: none;
  background: none;
}
.deleteBtn :deep(.el-icon-delete) {
  @include font_size(H1);
  @include font_color(T3);
}
.deleteBtn :deep(.el-icon-delete:hover) {
  @include font_color(Sta3);
}
.deleteBtn :deep(.is-disabled .el-icon-delete:hover) {
  @include font_color(T3);
}
.page :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  @include background_color(BG2, !important);
}
.aside-collapsed {
  padding: 0 !important;
}
.leftTree {
  position: relative;
  transition: width 0.3s;
}
.collapse-btn {
  cursor: pointer;
  position: absolute;
  top: 0;
  right: 0px;
  bottom: 0;
  margin: auto;
  width: 14px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  vertical-align: middle;
  @include background_color(ZS);
  @include font_color(T5);
  border-radius: 3px;
}
</style>
