<template>
  <div>
    <CetDialog v-bind="CetDialog_add" v-on="CetDialog_add.event" class="small">
      <el-container class="eem-cont-c1">
        <CetForm
          ref="createPlanForm"
          :data.sync="CetForm_1.inputData_in"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
        >
          <el-row :gutter="$J3">
            <el-col :span="12">
              <el-form-item :label="$T('巡检方案')" prop="inspectionschemeid">
                <ElSelect
                  v-model="CetForm_1.inputData_in.inspectionschemeid"
                  v-bind="ElSelect_inspectionscheme"
                  v-on="ElSelect_inspectionscheme.event"
                >
                  <ElOption
                    v-for="item in ElOption_inspectionscheme.options_in"
                    :key="item[ElOption_inspectionscheme.key]"
                    :label="item[ElOption_inspectionscheme.label]"
                    :value="item[ElOption_inspectionscheme.value]"
                    :disabled="item[ElOption_inspectionscheme.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('巡检路线')" prop="signgroupid">
                <ElSelect
                  v-model="CetForm_1.inputData_in.signgroupid"
                  v-bind="ElSelect_inspectionRoute"
                  v-on="ElSelect_inspectionRoute.event"
                >
                  <ElOption
                    v-for="item in ElOption_inspectionRoute.options_in"
                    :key="item[ElOption_inspectionRoute.key]"
                    :label="item[ElOption_inspectionRoute.label]"
                    :value="item[ElOption_inspectionRoute.value]"
                    :disabled="item[ElOption_inspectionRoute.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('执行时间')" prop="executetimeplan">
                <el-date-picker
                  popper-class="custom-date-picker-inspect"
                  v-model="CetForm_1.inputData_in.executetimeplan"
                  type="datetime"
                  :editable="true"
                  :pickerOptions="pickerOptions11"
                  :placeholder="$T('选择日期')"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('预计耗时')" prop="timeconsumeplan">
                <ElInputNumber
                  v-model="CetForm_1.inputData_in.timeconsumeplan"
                  v-bind="ElInputNumber_4"
                  v-on="ElInputNumber_4.event"
                ></ElInputNumber>
                <span class="form-item-unit">h</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('责任班组')" prop="teamid">
                <ElSelect
                  v-model="CetForm_1.inputData_in.teamid"
                  v-bind="ElSelect_team"
                  v-on="ElSelect_team.event"
                >
                  <ElOption
                    v-for="item in ElOption_team.options_in"
                    :key="item[ElOption_team.key]"
                    :label="item[ElOption_team.label]"
                    :value="item[ElOption_team.value]"
                    :disabled="item[ElOption_team.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('巡检目标')" prop="deviceListName">
                <!-- <ElInput disabled v-model="CetForm_1.inputData_in.deviceListName" v-bind="ElInput_1" v-on="ElInput_1.event">
                  <span slot="suffix" @click="OpenxjDevice" class="toConnect"></span>
                </ElInput> -->
                <div class="custom-btn" @click="OpenxjDevice">
                  <span>
                    {{ CetForm_1.inputData_in.deviceListName }}
                  </span>
                  <span class="toConnect">
                    <omega-icon symbolId="link-lin" />
                  </span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </CetForm>
      </el-container>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
    <DeviceList v-bind="DeviceList" v-on="DeviceList.event"></DeviceList>
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom.js";
import DeviceList from "./DeviceList";
const ONE_MINUTE_MILLISECONDS = 60 * 1000;
export default {
  name: "createPlan",
  components: { DeviceList },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    projectTenantId() {
      return this.$store.state.projectTenantId;
    }
  },
  data(vm) {
    return {
      CetDialog_add: {
        title: $T("新增巡检工单"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_add_open_out,
          close_out: this.CetDialog_add_close_out
        },
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "backendInterface", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "createInspectorWorkOrder",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        labelPosition: "top",
        rules: {
          planname: [
            {
              required: true,
              message: $T("请输入名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          inspectionschemeid: [
            {
              required: true,
              message: $T("请选择巡检方案"),
              trigger: ["blur", "change"]
            }
          ],
          signgroupid: [
            {
              required: true,
              message: $T("请选择巡检路线"),
              trigger: ["blur", "change"]
            }
          ],
          executetimeplan: [
            {
              required: true,
              message: $T("请选择执行时间"),
              trigger: ["blur", "change"]
            },
            {
              required: true,
              trigger: "blur",
              validator: (rule, value, callback) => {
                const now = new Date().getTime();
                const aheadduration =
                  this.CetForm_1.inputData_in.aheadduration *
                  ONE_MINUTE_MILLISECONDS;

                if (value < now + 15 * ONE_MINUTE_MILLISECONDS) {
                  callback(
                    new Error($T("首次执行时间不能小于当前时间+15分钟"))
                  );
                  return;
                }

                if (value - aheadduration < now) {
                  callback(
                    new Error($T("首次执行时间不能小于当前时间+工单生成时间"))
                  );
                  return;
                }

                callback();
              }
            }
          ],
          timeconsumeplan: [
            {
              required: true,
              message: $T("请输入预计耗时"),
              trigger: ["blur"]
            }
          ],
          aheadduration: [
            {
              required: true,
              message: $T("请选择工单生成时间"),
              trigger: ["blur", "change"]
            }
          ],
          teamid: [
            {
              required: true,
              message: $T("请选择责任班组"),
              trigger: ["blur", "change"]
            }
          ],
          deviceListName: [
            {
              required: true,
              message: $T("请选择巡检目标"),
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_1_currentData_out,
          saveData_out: this.CetForm_1_saveData_out,
          finishData_out: this.CetForm_1_finishData_out,
          finishTrigger_out: this.CetForm_1_finishTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        style: {},
        placeholder: $T("请输入内容"),
        event: {}
      },
      // 月，设置范围0-99
      ElInputNumber_1: {
        min: 0,
        max: 99,
        step: 2,
        precision: 0,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      // 天，设置范围0-29
      ElInputNumber_2: {
        min: 0,
        max: 29,
        step: 2,
        precision: 0,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      // 小时，设置范围0-23
      ElInputNumber_3: {
        min: 0,
        max: 23,
        step: 2,
        precision: 0,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInputNumber_4: {
        // ...common.check_numberInt,
        min: 0.01,
        max: 999999999.99,
        step: 2,
        precision: 2,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      pickerOptions: common.pickerOptions_laterThanYesterd,
      pickerOptions11: common.pickerOptions_laterThanYesterd11,
      //巡检方案
      ElSelect_inspectionscheme: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_inspectionscheme: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      //巡检路线，签到组
      ElSelect_inspectionRoute: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_inspectionRoute_change_out
        }
      },
      ElOption_inspectionRoute: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      //责任班组
      ElSelect_team: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_team: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // 工单生成时间选项组件
      ElSelect_AheadDuration: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_AheadDuration: {
        options_in: [
          {
            value: 5,
            label: $T("执行前5分钟")
          },
          {
            value: 24 * 60,
            label: $T("执行前1天")
          },
          {
            value: 2 * 24 * 60,
            label: $T("执行前2天")
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      //生效日设置
      ElCheckboxGroup_1: {
        value: [1, 2, 3, 4, 5, 6, 7],
        style: {
          display: "inline-block"
        },
        event: {
          change: this.ElCheckboxGroup_1_change_out
        }
      },
      ElCheckboxList_1: {
        options_in: [
          {
            id: 1,
            text: $T("周一")
          },
          {
            id: 2,
            text: $T("周二")
          },
          {
            id: 3,
            text: $T("周三")
          },
          {
            id: 4,
            text: $T("周四")
          },
          {
            id: 5,
            text: $T("周五")
          },
          {
            id: 6,
            text: $T("周六")
          },
          {
            id: 7,
            text: $T("周日")
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      // 巡检对象弹窗
      DeviceList: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        tableData: null,
        event: {
          saveData_out: this.deviceList_saveData_out
        }
      },
      showSet: false
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      this.CetForm_1.resetTrigger_in = this._.cloneDeep(val);
      this.showSet = false;

      Promise.all([
        new Promise((resolve, reject) => {
          this.queryInspectScheme_out(resolve);
        }),
        new Promise((resolve, reject) => {
          this.groupManage_out(resolve);
        }),
        new Promise((resolve, reject) => {
          this.getTeam_out(resolve);
        })
      ]).then(res => {
        vm.CetDialog_add.openTrigger_in = this._.cloneDeep(val);
        this.$nextTick(() => {
          this.CetForm_1.inputData_in = {};
          this.DeviceList.tableData = [];
          this.ElCheckboxGroup_1.value = [1, 2, 3, 4, 5, 6, 7];
        });
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    }
  },

  methods: {
    CetDialog_add_open_out(val) {},
    CetDialog_add_close_out(val) {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.$set(this.CetForm_1.inputData_in, "worksheetstatus", 1);
      this.CetForm_1.saveTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_currentData_out(val) {},
    CetForm_1_saveData_out(val) {
      this.$emit("confirm_out", this._.cloneDeep(val));
    },
    CetForm_1_finishData_out(val) {},
    CetForm_1_finishTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
      this.$emit("confirm_out", val);
    },
    ElCheckboxGroup_1_change_out(val) {},
    ElSelect_inspectionRoute_change_out(val) {
      this.$set(this.CetForm_1.inputData_in, "deviceListName", null);
      this.DeviceList.tableData = [];
    },
    OpenxjDevice(val) {
      if (!this.CetForm_1.inputData_in.signgroupid) {
        this.$message.warning($T("请先选择巡检路线"));
        return;
      }
      this.DeviceList.inputData_in = {
        signInGroup: this.CetForm_1.inputData_in.signgroupid
      };
      this.DeviceList.openTrigger_in = new Date().getTime();
    },
    init() {},
    // 巡检对象保存
    deviceList_saveData_out(val) {
      if (val && val.length) {
        this.$set(
          this.CetForm_1.inputData_in,
          "deviceListName",
          $T("已选择{0}个目标", val.length)
        );
        this.$refs.createPlanForm.$refs.cetForm.clearValidate("deviceListName");
      } else {
        this.$set(this.CetForm_1.inputData_in, "deviceListName", null);
      }

      this.CetForm_1.inputData_in.objectid = this._.get(val, "[0].id", null);
      this.CetForm_1.inputData_in.objectlabel = this._.get(
        val,
        "[0].modelLabel",
        null
      );
      this.DeviceList.tableData = this._.cloneDeep(val);
    },
    // 获取巡检方案列表信息
    queryInspectScheme_out(callback) {
      const _this = this;
      const params = {
        name: "",
        page: {
          index: 0,
          limit: 100
        },
        tenantId: this.projectTenantId
      };
      customApi.queryInspectSchemeXJ(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          _this.ElOption_inspectionscheme.options_in = _this._.cloneDeep(data);
          callback && callback();
        }
      });
    },
    // 获取签到组列表信息
    groupManage_out(callback) {
      const _this = this;
      const params = {};
      customApi.groupManageXJ(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          _this.ElOption_inspectionRoute.options_in = _this._.cloneDeep(data);
          callback && callback();
        }
      });
    },
    // 获取班组列表信息
    getTeam_out(callback) {
      const _this = this;
      const params = {};
      customApi.queryInspectorTeam(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          _this.ElOption_team.options_in = _this._.cloneDeep(data);
          callback && callback();
        }
      });
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.custom-btn {
  position: relative;
  padding-left: 15px;
  height: 32px;
  line-height: 32px;
  border-radius: 4px;
  border: 1px solid;
  @include border_color(B1);
  box-sizing: border-box;
  @include font_color(T2);
  cursor: pointer;
  @include background_color(BG4);
}
.toConnect {
  position: absolute;
  right: 2px;
  z-index: 999;
  display: inline-block;
  width: 30px;
  height: 30px;
  cursor: pointer;
  @include font_color(ZS);
  text-align: center;
}
</style>
