<template>
  <div class="layout-user">
    <div class="layout-user-btn" @click="visible = true">
      <omega-icon class="icon-size-I2 icon-p5" symbolId="account-lin" />
      <span class="layout-user-name">{{ userName }}</span>
    </div>
    <el-dialog
      width="450px"
      :center="true"
      :append-to-body="true"
      :visible.sync="visible"
      @open="onDialogOpen"
    >
      <el-form
        class="text-right eem-cont-c1"
        label-width="80px"
        label-position="left"
      >
        <el-form-item v-for="item in items" :key="item.label">
          <template #label>
            {{ item.label }}
          </template>
          {{ item.value }}
        </el-form-item>
      </el-form>

      <template #title>
        <omega-icon class="I5" icon-class="layout_user" />
      </template>
      <template #footer>
        <div class="clearfix">
          <el-button class="fl" type="danger" @click="evLogoutClick">
            {{ $T("退出") }}
          </el-button>
          <el-button class="fr" type="primary" @click="evResetPassword">
            {{ $T("修改密码") }}
          </el-button>
          <!-- <el-button class="fr" @click="visible = false">
            {{ $T("修改资料") }}
          </el-button> -->
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { showOmegaDialog } from "@omega/widget";
import ResetPassword from "./ResetPassword";
import omegaAuth from "@omega/auth";
import util from "eem-utils/util";

export default {
  name: "LayoutUser",
  data() {
    return {
      items: [],
      userName: omegaAuth.user.getUserName(),
      visible: false
    };
  },
  methods: {
    evLogoutClick() {
      this.logout();
    },
    evResetPassword() {
      const e = showOmegaDialog(ResetPassword);
      e.on("confirm", () => {
        this.logout();
      });
    },
    async logout() {
      await omegaAuth.logout();
      window.sessionStorage.removeItem("projectId");
      window.localStorage.removeItem("projectId");
      const singleSignOn = this.$store.state?.systemCfg?.singleSignOn;
      const retainLogin = this.$store.state?.systemCfg?.retainLogin;
      if (!retainLogin && singleSignOn) {
        window.location.href = `${window.location.origin}${window.location.pathname}/eem-service/thirdpart/sso/auth2.0/logout`;
      } else {
        await this.$router.push("/login");
        window.location.search = "";
      }
    },
    onDialogOpen() {
      const _user = omegaAuth.user._user;
      const roleName = _user.roles?.[0]?.name;
      const items = [
        {
          label: $T("用户名："),
          value: _user.name
        },
        {
          label: $T("角色："),
          value: roleName
        },
        {
          label: $T("电话："),
          value: _user.mobilePhone
        },
        {
          label: $T("邮箱："),
          value: _user.email
        }
      ];
      items.forEach(item => util.fillBlankObject(item));

      this.items = items;
    }
  }
};
</script>

<style lang="scss" scoped>
.layout-user {
  transition-duration: 0.3s;
  @include font_color(T2);
  @include font_size(Aa);
  &:hover {
    @include background_color(BG2);
  }
  &:active,
  &:focus {
    @include background_color(BG3);
  }
}
.layout-user-btn {
  display: flex;
  align-items: center;
  border-radius: 4px;
  cursor: pointer;
}
.layout-user-name {
  @include padding_right(J1);
}
.el-dialog__wrapper :deep(.el-dialog__body) {
  @include padding(J1, !important);
}
</style>
