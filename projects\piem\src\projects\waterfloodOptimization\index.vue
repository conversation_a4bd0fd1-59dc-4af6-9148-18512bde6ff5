<template>
  <div class="flex flex-col page">
    <el-tabs v-model="currenTab" class="tab mainBox p0">
      <el-tab-pane
        v-for="item in tabList"
        :key="item.name"
        :name="item.name"
        :label="item.label"
      ></el-tab-pane>
    </el-tabs>
    <div class="w-full h-[calc(100%-41px)] bg-BG1 box-border">
      <ModelCraete v-if="currenTab === 'modelCraete'"></ModelCraete>
      <OffLineModelTrain
        v-if="currenTab === 'offLineModelTrain'"
      ></OffLineModelTrain>
      <OnLinePolicyAnalysis
        v-if="currenTab === 'onLinePolicyAnalysis'"
      ></OnLinePolicyAnalysis>
    </div>
  </div>
</template>

<script>
import ModelCraete from "./modelCreate/index.vue";
import OffLineModelTrain from "./offLineModelTrain/index.vue";
import OnLinePolicyAnalysis from "./onLinePolicyAnalysis/index.vue";
export default {
  name: "waterfloodOptimization",
  props: {},
  components: { ModelCraete, OffLineModelTrain, OnLinePolicyAnalysis },
  data() {
    return {
      currenTab: "offLineModelTrain",
      tabList: [
        { name: "offLineModelTrain", label: "离线模型训练" },
        { name: "onLinePolicyAnalysis", label: "在线模型及策略分析" },
        { name: "modelCraete", label: "模型创建" }
      ]
    };
  },
  watch: {},
  methods: {},
  created() {}
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  :deep(.el-tree) {
    overflow: auto;
  }
  :deep(.mainBox) {
    box-sizing: border-box;
    @include padding(J3);
    @include background_color(BG1);
    @include border_radius(C);
  }
  :deep(.el-tabs__nav-scroll) {
    border-bottom: 1px solid;
    @include border_color(B2);
    @include padding(0 J4);
  }
  .tab {
    height: 40px;
    line-height: 40px;
    border-bottom-right-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    margin-bottom: 1px;
    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
  }
}
</style>
