<template>
  <div>
    <CetDialog v-bind="CetDialog_add" v-on="CetDialog_add.event" class="medium">
      <div>
        <CetForm
          ref="createPlanForm"
          :data.sync="CetForm_1.data"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
          class="eem-cont-c1"
          label-position="top"
        >
          <el-row :gutter="16">
            <el-col :span="8">
              <el-form-item :label="$T('名称')" prop="name">
                <ElInput
                  v-model.trim="CetForm_1.data.name"
                  v-bind="ElInput_name"
                  v-on="ElInput_name.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('周期策略')" prop="executestrategy">
                <ElSelect
                  v-model="CetForm_1.data.executestrategy"
                  v-bind="ElSelect_executestrategy"
                  v-on="ElSelect_executestrategy.event"
                >
                  <ElOption
                    v-for="item in ElOption_executestrategy.options_in"
                    :key="item[ElOption_executestrategy.key]"
                    :label="item[ElOption_executestrategy.label]"
                    :value="item[ElOption_executestrategy.value]"
                    :disabled="item[ElOption_executestrategy.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="CetForm_1.data.executestrategy == 2">
              <el-form-item
                :label="$T('运行时长间隔')"
                prop="interval"
                style="display: relative"
              >
                <ElInputNumber
                  v-model.trim="CetForm_1.data.interval"
                  v-bind="ElInputNumber_interval"
                  v-on="ElInputNumber_interval.event"
                ></ElInputNumber>
                <span class="hour_unit">h</span>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="CetForm_1.data.executestrategy == 1">
              <el-form-item :label="$T('周期')" prop="aggregationcycle">
                <ElSelect
                  v-model="CetForm_1.data.aggregationcycle"
                  v-bind="ElSelect_cycle"
                  v-on="ElSelect_cycle.event"
                >
                  <ElOption
                    v-for="item in ElOption_cycle.options_in"
                    :key="item[ElOption_cycle.key]"
                    :label="item[ElOption_cycle.label]"
                    :value="item[ElOption_cycle.value]"
                    :disabled="item[ElOption_cycle.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="
                CetForm_1.data.executestrategy == 1 &&
                CetForm_1.data.aggregationcycle == 0
              "
            >
              <el-form-item :label="$T('自定义时间')" prop="cycle">
                <el-container style="width: 280px">
                  <el-row :gutter="20" style="width: 280px">
                    <el-col :span="7">
                      <ElInputNumber
                        v-model.trim="cycleObj.month"
                        v-bind="ElInputNumber_1"
                        v-on="ElInputNumber_1.event"
                      ></ElInputNumber>
                    </el-col>
                    <el-col :span="1" style="padding: 0px">
                      <span>{{ en ? "M" : "月" }}</span>
                    </el-col>
                    <el-col :span="7">
                      <ElInputNumber
                        v-model.trim="cycleObj.day"
                        v-bind="ElInputNumber_2"
                        v-on="ElInputNumber_2.event"
                      ></ElInputNumber>
                    </el-col>
                    <el-col :span="1" style="padding: 0px">
                      <span>{{ en ? "d" : "天" }}</span>
                    </el-col>
                    <el-col :span="7">
                      <ElInputNumber
                        v-model.trim="cycleObj.hour"
                        v-bind="ElInputNumber_3"
                        v-on="ElInputNumber_3.event"
                      ></ElInputNumber>
                    </el-col>
                    <el-col :span="1" style="padding: 0px">
                      <span>{{ en ? "h" : "时" }}</span>
                    </el-col>
                  </el-row>
                </el-container>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('维保目标类型')" prop="nodelabel">
                <ElSelect
                  v-model="CetForm_1.data.nodelabel"
                  v-bind="ElSelect_3"
                  v-on="ElSelect_3.event"
                >
                  <ElOption
                    v-for="item in objectLabelIdList"
                    :key="item[ElOption_3.key]"
                    :label="item[ElOption_3.label]"
                    :value="item[ElOption_3.value]"
                    :disabled="item[ElOption_3.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                :label="$T('型号')"
                prop="model"
                v-if="
                  CetForm_1.data.nodelabel !== 'room' &&
                  CetForm_1.data.nodelabel !== 'manuequipment'
                "
              >
                <ElSelect
                  v-model="CetForm_1.data.model"
                  v-bind="ElSelect_model"
                  v-on="ElSelect_model.event"
                >
                  <ElOption
                    v-for="item in ElOption_model.options_in"
                    :key="item[ElOption_model.key]"
                    :label="item[ElOption_model.label]"
                    :value="item[ElOption_model.value]"
                    :disabled="item[ElOption_model.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('维保目标')" prop="deviceListName">
                <div class="custom-btn" @click="OpenwbDevice">
                  <span>
                    {{ CetForm_1.data.deviceListName }}
                  </span>
                  <span class="toConnect">
                    <omega-icon symbolId="link-lin" />
                  </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('维保项目')" prop="project">
                <div class="custom-btn" @click="handleProject">
                  <span>
                    {{ CetForm_1.data.project }}
                  </span>
                  <span class="toConnect">
                    <omega-icon symbolId="link-lin" />
                  </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('人员数量')" prop="population">
                <ElInputNumber
                  v-model.trim="CetForm_1.data.population"
                  v-bind="ElInputNumber_count"
                  v-on="ElInputNumber_count.event"
                ></ElInputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('工单生成时间')" prop="aheadduration">
                <ElSelect
                  v-model="CetForm_1.data.aheadduration"
                  v-bind="ElSelect_time"
                  v-on="ElSelect_time.event"
                >
                  <ElOption
                    v-for="item in ElOption_time.options_in"
                    :key="item[ElOption_time.key]"
                    :label="item[ElOption_time.label]"
                    :value="item[ElOption_time.value]"
                    :disabled="item[ElOption_time.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item :label="$T('预计耗时')" prop="timeconsumeplan">
                <ElInputNumber
                  v-model.trim="CetForm_1.data.timeconsumeplan"
                  v-bind="ElInputNumber_timeconsume"
                  v-on="ElInputNumber_timeconsume.event"
                ></ElInputNumber>
                <span class="form-item-unit">h</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('责任班组')" prop="teamid">
                <ElSelect
                  v-model="CetForm_1.data.teamid"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                >
                  <ElOption
                    v-for="item in ElOption_1.options_in"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('等级')" prop="worksheettasklevel">
                <ElSelect
                  v-model="CetForm_1.data.worksheettasklevel"
                  v-bind="ElSelect_2"
                  v-on="ElSelect_2.event"
                >
                  <ElOption
                    v-for="item in ElOption_2.options_in"
                    :key="item[ElOption_2.key]"
                    :label="item[ElOption_2.label]"
                    :value="item[ElOption_2.value]"
                    :disabled="item[ElOption_2.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('首次执行时间')" prop="executetime">
                <el-date-picker
                  v-model="CetForm_1.data.executetime"
                  type="datetime"
                  :editable="true"
                  :pickerOptions="pickerOptions11"
                  value-format="timestamp"
                  :placeholder="$T('选择日期')"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="CetForm_1.data.aggregationcycle != 18">
              <el-form-item
                :label="$T('结束时间')"
                prop="finishtime"
                class="form-finishtime"
              >
                <el-date-picker
                  v-model="CetForm_1.data.finishtime"
                  type="datetime"
                  :disabled="isForever"
                  :editable="true"
                  :pickerOptions="pickerOptions11"
                  value-format="timestamp"
                  :placeholder="$T('选择日期')"
                ></el-date-picker>
                <el-checkbox class="mlJ1" v-model="CetForm_1.data.isForever">
                  {{ $T("永远") }}
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$T('安全措施说明')" prop="safetymeasure">
                <ElInput
                  v-model.trim="CetForm_1.data.safetymeasure"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
          </el-row>
        </CetForm>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>

    <!-- 维保项目弹窗 -->
    <projectDialog v-bind="projectDialog" v-on="projectDialog.event" />
    <!-- 维保目标弹窗 -->
    <targetDialog v-bind="targetDialog" v-on="targetDialog.event" />
  </div>
</template>

<script>
import common from "eem-utils/common";
import projectDialog from "./editProjectDailog.vue";
import targetDialog from "./targetDialog";
import { mhCONST } from "@/config/const";
import customApi from "@/api/custom.js";

const ONE_MINUTE_MILLISECONDS = 60 * 1000;
const ONE_HOUR_MILLISECONDS = 60 * ONE_MINUTE_MILLISECONDS;

export default {
  name: "createPlan",
  components: { projectDialog, targetDialog },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    team_in: {
      type: Array
    },
    rankList: {
      type: Array
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.localStorage) {
          return false;
        } else {
          var storage = window.localStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    },
    calculatedOneCycle() {
      const vm = this;
      const aggregationcycle = vm.CetForm_1.data.aggregationcycle;
      const m = vm.cycleObj.month;
      const d = vm.cycleObj.day;
      const h = vm.cycleObj.hour;

      const oneCycle = {
        y: 0,
        m: 0,
        d: 0,
        h: 0
      };
      switch (aggregationcycle) {
        case 0:
          oneCycle.m = m;
          oneCycle.d = d;
          oneCycle.h = h;
          break;
        case 12:
          oneCycle.d = 1;
          break;
        case 13:
          oneCycle.d = 7;
          break;
        case 14:
          oneCycle.m = 1;
          break;
        case 16:
          oneCycle.y = 0.5;
          break;
        case 17:
          oneCycle.y = 1;
          break;
        case 18:
          break;
      }
      return oneCycle;
    },
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },
  data() {
    return {
      objectLabelIdList: [], // 维保目标类型列表
      CetDialog_add: {
        title: $T("新增维保计划"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        width: "550px",
        showClose: true,
        "modal-append-to-body": false
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入维保计划名称"),
              trigger: ["blur"]
            },
            common.check_name,
            common.pattern_name
          ],
          executestrategy: [
            {
              required: true,
              message: $T("请选择周期策略"),
              trigger: ["blur", "change"]
            }
          ],
          interval: [
            {
              required: true,
              message: $T("请输入运行时长间隔"),
              trigger: ["blur", "change"]
            }
          ],
          aggregationcycle: [
            {
              required: true,
              message: $T("请选择周期类型"),
              trigger: ["blur", "change"]
            }
          ],
          cycle: [
            {
              required: true,
              message: $T("自定义周期不能为零"),
              trigger: ["blur"]
            }
          ],
          nodelabel: [
            {
              required: true,
              message: $T("请选择维保目标类型"),
              trigger: ["blur", "change"]
            }
          ],
          model: [
            {
              required: true,
              message: $T("请选择型号"),
              trigger: ["blur", "change"]
            }
          ],
          project: [
            {
              required: true,
              message: $T("请选择维保项目"),
              trigger: ["blur", "change"]
            }
          ],
          population: [
            {
              required: true,
              message: $T("请输入人员数量"),
              trigger: ["blur"]
            }
          ],
          aheadduration: [
            {
              required: true,
              message: $T("请选择工单生成时间"),
              trigger: ["blur", "change"]
            }
          ],
          executetime: [
            {
              required: true,
              message: $T("请选择执行时间"),
              trigger: ["blur", "change"]
            },
            {
              required: true,
              trigger: "blur",
              validator: (rule, value, callback) => {
                const now = new Date().getTime();
                const aheadduration =
                  this.CetForm_1.data.aheadduration * ONE_MINUTE_MILLISECONDS;

                if (value < now + 15 * ONE_MINUTE_MILLISECONDS) {
                  callback(
                    new Error($T("首次执行时间不能小于当前时间+15分钟"))
                  );
                  return;
                }

                if (value - aheadduration < now) {
                  callback(
                    new Error($T("首次执行时间不能小于当前时间+工单生成时间"))
                  );
                  return;
                }

                callback();
              }
            }
          ],
          finishtime: [
            {
              required: true,
              message: $T("结束时间不足以执行一个周期的计划"),
              trigger: "blur",
              validator: (rule, value, callback) => {
                // 一次性创建计划不需要验证结束时间
                if (
                  this.CetForm_1.data.aggregationcycle === 18 ||
                  this.isForever
                ) {
                  callback();
                  return;
                }

                const executetime = this.CetForm_1.data.executetime;
                const calculatedOneCycle = this.calculatedOneCycle;
                let endtime;
                if (this.CetForm_1.data.executestrategy === 2) {
                  endtime = this.$moment(executetime)
                    .add(this.CetForm_1.data.interval, "h")
                    .valueOf();
                } else if (this.CetForm_1.data.executestrategy === 1) {
                  endtime = this.$moment(executetime)
                    .add(calculatedOneCycle.y, "y")
                    .add(calculatedOneCycle.m, "M")
                    .add(calculatedOneCycle.d, "d")
                    .add(calculatedOneCycle.h, "h")
                    .valueOf();
                }

                if (value > endtime) {
                  callback();
                } else {
                  callback(new Error());
                }
              }
            }
          ],
          timeconsumeplan: [
            {
              required: true,
              message: $T("请输入预计耗时"),
              trigger: ["blur"]
            }
          ],
          teamid: [
            {
              required: true,
              message: $T("请选择责任班组"),
              trigger: ["blur", "change"]
            }
          ],
          deviceListName: [
            {
              required: true,
              message: $T("请选择维保目标"),
              trigger: ["blur", "change"]
            }
          ],
          worksheettasklevel: [
            {
              required: true,
              message: $T("请选择等级"),
              trigger: ["blur", "change"]
            }
          ],
          safetymeasure: [
            {
              required: true,
              message: $T("请输入安全措施说明"),
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElInput_name: {
        value: "",
        style: {
          width: "100%"
        },
        placeholder: $T("请输入内容"),
        event: {}
      },
      ElInputNumber_interval: {
        ...common.check_numberFloat,
        min: 0.01,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElSelect_cycle: {
        value: 0,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_cycle: {
        options_in: [
          {
            value: 18,
            label: $T("只执行一次")
          },
          {
            value: 12,
            label: $T("1天")
          },
          {
            value: 13,
            label: $T("1周")
          },
          {
            value: 14,
            label: $T("1个月")
          },
          {
            value: 16,
            label: $T("半年")
          },
          {
            value: 17,
            label: $T("1年")
          },
          {
            value: 0,
            label: $T("自定义")
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      // executestrategy组件
      ElSelect_executestrategy: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      // executestrategy组件
      ElOption_executestrategy: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      cycleObj: {},
      // project组件
      ElInput_project: {
        value: "",
        readonly: true,
        style: {
          width: "100%"
        }
      },
      // 月，设置范围0-99
      ElInputNumber_1: {
        min: 0,
        max: 99,
        step: 2,
        precision: 0,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      // 天，设置范围0-29
      ElInputNumber_2: {
        min: 0,
        max: 29,
        step: 2,
        precision: 0,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      // 小时，设置范围0-23
      ElInputNumber_3: {
        min: 0,
        max: 23,
        step: 2,
        precision: 0,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInputNumber_count: {
        ...common.check_numberInt,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInputNumber_timeconsume: {
        ...common.check_numberFloat,
        min: 0.01,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      // 工单生成时间组件
      ElSelect_time: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          // change: this.ElSelect_time_change_out
        }
      },
      ElOption_time: {
        options_in: [
          {
            value: 5,
            label: $T("执行前5分钟")
          },
          {
            value: 30,
            label: $T("执行前30分钟")
          },
          {
            value: 60,
            label: $T("执行前1小时")
          },
          {
            value: 2 * 60,
            label: $T("执行前2小时")
          },
          {
            value: 24 * 60,
            label: $T("执行前1天")
          },
          {
            value: 2 * 24 * 60,
            label: $T("执行前2天")
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      pickerOptions: common.pickerOptions_laterThanYesterd,
      pickerOptions11: common.pickerOptions_laterThanYesterd11,
      // 责任班组
      ElSelect_1: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElInput_target: {
        value: "",
        readonly: true,
        style: {
          width: "100%"
        }
      },
      // 等级
      ElSelect_2: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_2: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_3: {
        value: "",
        style: {
          width: "100%"
        },
        filterable: true,
        event: {
          change: this.ElSelect_3_change_out
        }
      },
      ElOption_3: {
        options_in: [],
        key: "propertyLabel",
        value: "propertyLabel",
        label: "text",
        disabled: "disabled"
      },
      // 型号组件
      ElSelect_model: {
        value: "",
        style: {
          width: "100%"
        },
        filterable: true,
        event: {
          change: this.ElSelect_model_change_out
        }
      },
      ElOption_model: {
        options_in: [],
        key: "label",
        value: "label",
        label: "label",
        disabled: "disabled"
      },
      ElInput_1: {
        value: "",
        style: {},
        type: "textarea",
        resize: "none",
        placeholder: $T("请输入内容"),
        event: {}
      },
      projectDialog: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        tableData: [],
        event: {
          saveData_out: this.saveMaintenanceItems,
          closeDialog: this.CetButton_cancel_statusTrigger_out
        }
      },
      targetDialog: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        tableData: [], // 已经选择的维保目标
        event: {
          saveData_out: this.deviceList_saveData_out
        }
      },
      isForever: false
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.ElOption_1.options_in = this.team_in;
      this.ElOption_2.options_in = this.rankList;
      // 先屏蔽设备运行时长周期策略选择
      this.ElOption_executestrategy.options_in = (
        this.$store.state.enumerations.executestrategytype || []
      ).filter(item => item.id !== 2);
      // 维保目标类型下拉列表
      this.objectLabelIdList = [
        ...this.$store.state.enumerations.deviceclass,
        {
          propertyLabel: "manuequipment",
          text: $T("用能设备")
        },
        {
          propertyLabel: "room",
          text: $T("房间")
        }
      ];
      // 型号
      this.ElOption_model.options_in = [];
      this.CetForm_1.resetTrigger_in = new Date().getTime();
      this.CetDialog_add.openTrigger_in = val;
      this.CetForm_1.data = {};
      this.projectDialog.tableData = [];
      this.targetDialog.tableData = [];
      this.cycleObj = {};
      this.$set(this.CetForm_1.data, "isForever", false);
      this.$set(this.CetForm_1.data, "aggregationcycle", 0);
      this.$set(
        this.CetForm_1.data,
        "executestrategy",
        this.ElOption_executestrategy.options_in[0].id
      );
      this.$set(this.CetForm_1.data, "nodelabel", "");
      this.$set(this.CetForm_1.data, "model", "");
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    },
    cycleObj: {
      handler: function () {
        if (this.CetForm_1.data.aggregationcycle !== 0) {
          this.$set(this.CetForm_1.data, "cycle", null);
        } else {
          const cycle = this.calculatedCustomCycle();
          this.$set(this.CetForm_1.data, "cycle", cycle);
        }
      },
      deep: true
    },
    "CetForm_1.data.isForever": {
      handler: function (val, oldVal) {
        if (val) {
          this.isForever = true;
          this.CetForm_1.rules.finishtime[0].required = false;
          this.$refs.createPlanForm.$refs.cetForm.clearValidate("finishtime");
          this.CetForm_1.data.finishtime = null;
        } else {
          this.isForever = false;
          this.CetForm_1.rules.finishtime[0].required = true;
        }
      },
      deep: true
    }
  },

  methods: {
    // 类型下拉选择
    ElSelect_3_change_out(val) {
      // 类型改变，置空型号、维保目标、维保项目
      this.CetForm_1.data.model = "";
      this.$set(this.CetForm_1.data, "deviceListName", "");
      this.$set(this.CetForm_1.data, "project", "");
      this.CetForm_1.data.deviceplanrelationship_model = [];
      this.targetDialog.tableData = [];
      if (val === "room" || val === "manuequipment") return;
      customApi.querySparePartsModel(val).then(res => {
        if (res.code === 0) {
          const arr = [];
          res.data.forEach(item => {
            arr.push({ label: item });
          });
          this.ElOption_model.options_in = arr;
        }
      });
    },
    // 型号下拉选择
    ElSelect_model_change_out() {
      this.$set(this.CetForm_1.data, "deviceListName", "");
      this.$set(this.CetForm_1.data, "project", "");
      this.CetForm_1.data.deviceplanrelationship_model = [];
      this.targetDialog.tableData = [];
    },
    //过滤周期
    calculatedCustomCycle() {
      const vm = this;
      const m = vm.cycleObj.month || 0;
      const d = vm.cycleObj.day || 0;
      const h = vm.cycleObj.hour || 0;

      if (m <= 0 && d <= 0 && h <= 0) {
        return null;
      }
      return "P" + m + "M" + d + "D" + "T" + h + "H";
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      this.CetForm_1.localSaveTrigger_in = new Date().getTime();
    },
    CetForm_1_saveData_out() {
      if (this.CetForm_1.data.executestrategy === 2) {
        const cycle =
          "P" + 0 + "M" + 0 + "D" + "T" + this.CetForm_1.data.interval + "H";
        this.$set(this.CetForm_1.data, "cycle", cycle);
        this.CetForm_1.data.aggregationcycle = 0;
      } else if (this.CetForm_1.data.executestrategy === 1) {
        if (this.CetForm_1.data.aggregationcycle !== 0) {
          this.$set(this.CetForm_1.data, "cycle", null);
        } else {
          const cycle = this.calculatedCustomCycle();
          this.$set(this.CetForm_1.data, "cycle", cycle);
        }
      }
      // 选择永远按钮，结束时间传null
      if (this.CetForm_1.data.isForever) {
        this.$set(this.CetForm_1.data, "finishtime", null);
      }
      //选择只执行一次，结束时间传值和开始时间值一样
      if (this.CetForm_1.data.aggregationcycle === 18) {
        this.$set(
          this.CetForm_1.data,
          "finishtime",
          this.CetForm_1.data.executetime
        );
      }
      const data = this.CetForm_1.data;
      const params = {
        model: data.model,
        nodelabel: data.nodelabel,
        executestrategy: data.executestrategy,
        aggregationcycle: data.aggregationcycle,
        aheadduration: data.aheadduration,
        cycle: data.cycle,
        deviceplanrelationship_model: data.deviceplanrelationship_model,
        executetime: data.executetime,
        finishtime: data.finishtime,
        maintenanceextend: { maintenanceItems: data.maintenanceItems },
        name: data.name,
        population: data.population,
        safetymeasure: data.safetymeasure,
        teamid: data.teamid,
        timeconsumeplan: data.timeconsumeplan * ONE_HOUR_MILLISECONDS, // 预计耗时存毫秒,
        worksheettasklevel: data.worksheettasklevel
      };
      this.$emit("saveData_out", this._.cloneDeep(params));
    },
    OpenwbDevice() {
      const nodelabel = this.CetForm_1.data.nodelabel;
      if (!nodelabel) {
        return this.$message.warning($T("请选择维保目标类型!"));
      }
      // 房间、用能设备不需要型号
      if (
        nodelabel !== "room" &&
        nodelabel !== "manuequipment" &&
        !this.CetForm_1.data.model
      ) {
        return this.$message.warning($T("请选择型号!"));
      }
      this.targetDialog.inputData_in = {
        nodelabel, // 设备类型
        model: this.CetForm_1.data.model // 型号
      };
      this.targetDialog.openTrigger_in = new Date().getTime();
    },
    handleProject() {
      if (!this.CetForm_1.data.deviceListName) {
        return this.$message.warning($T("请先选择维保目标!"));
      }
      this.projectDialog.inputData_in = {
        nodelabel: this.CetForm_1.data.nodelabel, // 设备类型
        model: this.CetForm_1.data.model // 型号
      };
      this.projectDialog.openTrigger_in = new Date().getTime();
    },
    init() {},
    // 保存维保项目
    saveMaintenanceItems(val, num) {
      if (num) {
        this.$set(this.CetForm_1.data, "project", $T("已选择{0}项", num));
      } else {
        this.$set(this.CetForm_1.data, "project", "");
      }
      this.CetForm_1.data.maintenanceItems = this._.cloneDeep(val);
      this.projectDialog.tableData = this._.cloneDeep(val);
    },
    // 保存维保目标
    deviceList_saveData_out(val) {
      if (val && val.length) {
        this.$set(
          this.CetForm_1.data,
          "deviceListName",
          $T("已选择{0}项", val.length)
        );
      } else {
        this.$set(this.CetForm_1.data, "deviceListName", "");
      }
      const list = val || [];
      const arr = [];
      list.forEach(item => {
        arr.push({
          device_id: item.id,
          device_label: item.modelLabel,
          devicename: item.name
        });
      });
      if (
        !this._.isEqual(this.CetForm_1.data.deviceplanrelationship_model, arr)
      ) {
        this.$set(this.CetForm_1.data, "project", "");
        this.CetForm_1.data.maintenanceItems = [];
        this.projectDialog.tableData = [];
      }
      this.CetForm_1.data.deviceplanrelationship_model = this._.cloneDeep(arr);
      this.targetDialog.tableData = this._.cloneDeep(val);
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.eem-device-icon {
  position: absolute;
  right: 10px;
  z-index: 1;
  top: -3px;
  cursor: pointer;
}
.custom-btn {
  position: relative;
  padding-left: 15px;
  height: 32px;
  line-height: 32px;
  border-radius: 4px;
  border: 1px solid;
  @include border_color(B1);
  box-sizing: border-box;
  @include font_color(T2);
  cursor: pointer;
  @include background_color(BG4);
}
.toConnect {
  position: absolute;
  right: 2px;
  z-index: 999;
  display: inline-block;
  width: 30px;
  height: 30px;
  cursor: pointer;
  @include font_color(ZS);
  text-align: center;
}
.form-finishtime {
  :deep(.el-form-item__content) {
    display: flex;
  }
}
</style>
