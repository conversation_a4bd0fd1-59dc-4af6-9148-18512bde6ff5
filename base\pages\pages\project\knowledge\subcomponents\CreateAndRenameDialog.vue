<template>
  <el-container class="knowledgeDialog">
    <CetDialog
      class="CetDialog min"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
    >
      <el-main class="fullheight eem-cont-c1">
        <div class="lh20 mbJ">
          <span class="txt">{{ $T("名称") }}</span>
        </div>
        <ElInput
          v-model.trim="ElInput_name.value"
          v-bind="ElInput_name"
          v-on="ElInput_name.event"
          :maxlength="maxlength"
        ></ElInput>
      </el-main>
      <div slot="footer">
        <!-- cancel按钮组件 -->
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <!-- confirm按钮组件 -->
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </div>
    </CetDialog>
  </el-container>
</template>
<script>
import commonApi from "@/api/custom";
export default {
  name: "CreateAndRenameDialog",
  components: {},
  computed: {},
  props: {
    openTrigger_in: {
      type: Number
    },
    order: {
      type: String
    },
    folderId: {
      type: Number
    },
    txt: {
      type: Object
    }
  },
  data() {
    return {
      maxlength: 20,
      // 设置组件唯一识别字段弹窗组件
      // 1弹窗组件
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        width: "480px",
        showClose: true,
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        }
      },
      // name组件
      ElInput_name: {
        value: "",
        placeholder: $T("请输入名称"),
        event: {
          change: this.ElInput_name_change_out,
          input: this.ElInput_name_input_out
        }
      },
      // 确定按钮
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      // 取消按钮
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },

  watch: {
    openTrigger_in(val) {
      if (this.order === $T("新建目录")) {
        this.CetDialog_1.title = $T("新建");
      } else {
        this.CetDialog_1.title = $T("重命名");
      }
      this.CetDialog_1.openTrigger_in = val;
    },
    order(val) {
      if (val === $T("新建目录")) {
        this.maxlength = 20;
        this.ElInput_name.value = "";
      }
    },
    txt(val) {
      this.ElInput_name.value = this.txt.name;
      if (val.type == 1) {
        this.maxlength = 20;
      } else {
        this.maxlength = 100;
      }
    }
  },
  methods: {
    CetDialog_1_open_out(val) {},
    CetDialog_1_close_out(val) {},
    // dialog点击保存按钮
    CetButton_confirm_statusTrigger_out() {
      if (
        !this.ElInput_name ||
        !(this.ElInput_name && this.ElInput_name.value.trim())
      ) {
        this.$message({
          type: "warning",
          message: $T("名称不能为空")
        });
        return;
      }
      if (this.order === $T("新建目录")) {
        commonApi
          .createFolder([
            {
              name: this.ElInput_name.value
            }
          ])
          .then(res => {
            if (res.code == 0) {
              console.log(res);

              const modelNodes = [
                {
                  id: res.data[0].id,
                  modelLabel: res.data[0].modelLabel,
                  disabled: false,
                  authIds: [],
                  rule: ""
                }
              ];
              commonApi.addModelnodes({ modelNodes }).then(response => {
                if (response.code == 0) {
                  this.$message({
                    type: "success",
                    message: $T("目录创建成功")
                  });
                  this.$emit("refresh");
                  this.close();
                }
              });
            }
          });
      } else {
        if (this.ElInput_name.value == this.txt.name) {
          if (this.txt.type == 1) {
            this.$message({ type: "success", message: $T("目录重命名成功") });
          } else {
            this.$message({ type: "success", message: $T("文件重命名成功") });
          }
          this.close();
          return;
        }

        commonApi
          .updateFolder([
            {
              name: this.ElInput_name.value,
              id: this.folderId,
              modelLabel: "filemodel",
              fathernode_id: this.txt.fathernode_id
            }
          ])
          .then(res => {
            if (res.code == 0) {
              if (this.txt.type == 1) {
                this.$message({ type: "success", message: $T("目录重命名成功") });
                this.$emit("refresh", {
                  id: res.data[0].id,
                  label: res.data[0].name
                });
              } else {
                this.$message({ type: "success", message: $T("文件重命名成功") });
                this.$emit("refresh", 2);
              }
              this.close();
            }
          });
      }
    },
    // dialog点击取消按钮
    CetButton_cancel_statusTrigger_out() {
      this.close();
    },
    // 关闭弹窗
    close() {
      this.ElInput_name.value = "";
      this.CetDialog_1.closeTrigger_in = new Date().getTime();
    },
    // name输出,方法名要带_out后缀
    ElInput_name_change_out(val) {},
    ElInput_name_input_out(val) {}
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.txt::after {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}
</style>
