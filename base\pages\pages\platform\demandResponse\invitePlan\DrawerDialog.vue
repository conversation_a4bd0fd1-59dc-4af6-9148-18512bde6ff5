<template>
  <!-- 弹窗组件 -->
  <el-drawer title="电力用户详情" :visible.sync="drawer">
    <!-- <p class="secondtitle">基础信息</p>
    <div class="content" style="padding:28px 32px 24px 32px"> -->
    <div class="contentBox custom-detail">
      <div class="secondTitle">基础信息</div>
      <div class="dataCard">
        <div class="rowBox">
          <el-row :gutter="32">
            <el-col class="label" :span="8">用户名称</el-col>
            <el-col class="label" :span="8">合作开始时间</el-col>
            <el-col class="label" :span="8">第一联系人</el-col>
            <el-col :span="8" class="font">
              {{ formData && formData.enterprisename }}
            </el-col>
            <el-col :span="8" class="font">
              {{
                formData.starttime &&
                $moment(formData.starttime).format("YYYY-MM-DD HH:mm:ss.SSS")
              }}
            </el-col>
            <el-col :span="8" class="font">
              {{ formData && formData.contact_one }}
            </el-col>
          </el-row>
          <el-row :gutter="32" class="mtJ2">
            <el-col class="label" :span="8">统一社会信用代码</el-col>
            <el-col class="label" :span="8">合作截止时间</el-col>
            <el-col class="label" :span="8">第一联系人联系方式</el-col>
            <el-col :span="8" class="font">
              {{ formData && formData.creditcode }}
            </el-col>
            <el-col :span="8" class="font">
              {{
                formData.starttime &&
                $moment(formData.endtime).format("YYYY-MM-DD HH:mm:ss.SSS")
              }}
            </el-col>
            <el-col :span="8" class="font">
              {{ formData && formData.contactNumber_one }}
            </el-col>
          </el-row>
          <el-row :gutter="32" class="mtJ2">
            <el-col class="label" :span="8">用户地址</el-col>
            <el-col class="label" :span="8">报装容量（kVA）</el-col>
            <el-col class="label" :span="8">第二联系人</el-col>
            <el-col :span="8" class="font">
              {{ formData && formData.address.split("-")[0] }}
            </el-col>
            <el-col :span="8" class="font">
              {{ formData && formData.declaredCapacity }}
            </el-col>
            <el-col :span="8" class="font">
              {{ formData && formData.contact_two }}
            </el-col>
          </el-row>
          <el-row :gutter="32" class="mtJ2">
            <el-col class="label" :span="8">详细地址</el-col>
            <el-col class="label" :span="8">最大响应量（kW）</el-col>
            <el-col class="label" :span="8">第二联系人联系方式</el-col>
          </el-row>
          <el-row :gutter="32">
            <el-col :span="8" class="font">
              {{ formData && formData.address.split("-")[1] }}
            </el-col>
            <el-col :span="8" class="font">
              {{ formData && formData.maxResponse }}
            </el-col>
            <el-col :span="8" class="font">
              {{ formData && formData.contactNumber_two }}
            </el-col>
          </el-row>
        </div>
      </div>
      <!-- <p class="secondtitle">户号详情</p>
    <el-main class="tablebox"> -->
      <div class="secondTitle">户号详情</div>
      <div class="dataCard">
        <div class="rowBox">
          <CetTable
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
          >
            <el-table-column
              label="名称"
              prop="name"
              :formatter="val => (val.name ? val.name : '--')"
            ></el-table-column>
            <el-table-column
              label="户号"
              prop="accountno"
              :formatter="val => (val.accountno ? val.accountno : '--')"
            ></el-table-column>

            <el-table-column
              label="报装容量(kVA)"
              prop="declaredcapacity"
              :formatter="
                val => (val.declaredcapacity ? val.declaredcapacity : '--')
              "
            ></el-table-column>
            <el-table-column
              label="最大响应量(kW)"
              prop="maxresponse"
              :formatter="val => (val.maxresponse ? val.maxresponse : '--')"
            ></el-table-column>
          </CetTable>
        </div>
      </div>
    </div>
    <!-- </el-main> -->
  </el-drawer>
</template>
<script>
import customApi from "@/api/custom";
import moment from "moment";

export default {
  props: {
    openTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {},
  data() {
    return {
      formData: {},
      drawer: false,
      CetButton_edit: {
        visible_in: true,
        disable_in: false,
        title: "编辑",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_edit_statusTrigger_out
        }
      },
      num: 0,
      CetButton_download: {
        visible_in: true,
        title: "批量下载",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_download_statusTrigger_out
        }
      },

      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项

        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,

        exportFileName: "",
        defaultSort: { prop: "operationtime", order: "descending" },
        event: {
          //   totalCount_out: this.CetTable_1_totalCount_out
        }
      },
      ShowAccessory: {
        openTrigger_in: new Date().getTime(),
        inputData_in: null
      }
    };
  },
  watch: {
    openTrigger_in(val) {
      console.log(this.inputData_in);
      this.drawer = true;
      this.gettableData();
      //   this.$nextTick(() => {
      //     $(this.$refs.drawer.$el).find(".el-drawer__body").scrollTop(0);
      //   });
    }
  },
  methods: {
    CetButton_edit_statusTrigger_out(val) {
      this.drawer = false;
      this.$emit("edit_out");
    },
    CetButton_download_statusTrigger_out() {},
    toShowAccessory(scope) {
      this.ShowAccessory.openTrigger_in = new Date().getTime();
    },
    downloadAccessory(scope) {},
    gettableData() {
      this.formData = {};
      this.CetTable_1.data = [];
      customApi.invitationDisplaydetail(this.inputData_in.id).then(response => {
        this.formData = this._.get(response, "data", {});
        this.CetTable_1.data = this._.get(response, "data.accounts", []);

        console.log(this.CetTable_1.data);
      });
    }
  },
  created() {},
  mounted() {}
};
</script>
<style lang="scss" scoped>
@import "@/resources/szwCss.scss";
:deep(.el-drawer) {
  width: 1000px !important;
  .label {
    font-size: 14px;
  }
  .font {
    font-size: 14px;
    overflow: auto;
    // margin-right: 20px;
  }
}

.tablebox {
  @include border_radius(C2);
  height: 400px;
  width: calc(100% - 32px);
  @include margin_left(J2);
  @include margin_right(J2);
  overflow: auto;
  @include background_color(BG1);
}
</style>
