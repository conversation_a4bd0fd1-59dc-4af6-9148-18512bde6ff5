<template>
  <!-- 1弹窗组件 -->
  <div>
    <CetDialog
      class="CetDialog"
      ref="CetDialog"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
    >
      <div class="title">{{ $T("基本信息") }}</div>
      <div class="mtJ1 eem-cont-c1">
        <el-row :gutter="$J3">
          <el-col :span="8">
            <div class="label">
              {{ $T("方案名称") }}
              <span style="color: red">*</span>
            </div>
            <div class="value mtJ1">
              <ElInput
                v-model="queryBody.name"
                v-bind="ElInput_2"
                v-on="ElInput_2.event"
              ></ElInput>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">
              {{ $T("能源类型") }}
              <span style="color: red">*</span>
            </div>
            <div class="value mtJ1">
              <ElSelect
                v-model="queryBody.objectenergytype"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in ElOption_1.options_in"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </ElSelect>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="title mtJ1">{{ $T("分摊信息") }}</div>
      <div class="mtJ1 eem-cont-c1">
        <div class="tableTopRow">
          <el-row :gutter="$J3">
            <el-col :span="8">
              <div class="label">
                {{ $T("被分摊对象") }}
                <span style="color: red">*</span>
              </div>
              <div class="value mtJ1 flex-row">
                <ElInput
                  v-model="queryBody.objectName"
                  v-bind="ElInput_1"
                  :disabled="true"
                  v-on="ElInput_1.event"
                ></ElInput>
                <el-button
                  class="mlJ1"
                  type="primary"
                  @click="CetInput_1_Click()"
                  size="small"
                  plain
                >
                  {{ $T("关联") }}
                </el-button>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label">
                {{ $T("分摊对象") }}
                <span style="color: red">*</span>
              </div>
              <div class="value mtJ1 flex-row">
                <ElInput
                  v-model="ElInput_tree.value"
                  v-bind="ElInput_tree"
                  :disabled="true"
                  v-on="ElInput_tree.event"
                ></ElInput>
                <el-button
                  class="mlJ1"
                  type="primary"
                  size="small"
                  plain
                  @click="CetInput_2_Click"
                >
                  {{ $T("关联") }}
                </el-button>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label">
                {{ $T("分摊方式") }}
                <span style="color: red">*</span>
              </div>
              <div class="value mtJ1">
                <ElSelect
                  v-model="queryBody.energysharemethod"
                  v-bind="ElSelect_2"
                  v-on="ElSelect_2.event"
                >
                  <ElOption
                    v-for="item in ElOption_2.options_in"
                    :key="item[ElOption_2.key]"
                    :label="item[ElOption_2.label]"
                    :value="item[ElOption_2.value]"
                    :disabled="item[ElOption_2.disabled]"
                  ></ElOption>
                </ElSelect>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="$J3" class="mtJ3">
            <el-col :span="8" v-show="queryBody.energysharemethod == 2">
              <div class="label">
                {{ $T("分摊的能源类型") }}
                <span style="color: red">*</span>
              </div>
              <div class="value mtJ1">
                <ElSelect
                  v-model="queryBody.energytype"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                >
                  <ElOption
                    v-for="item in ElOption_1.options_in"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </ElSelect>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label">
                {{ $T("分摊计算间隔") }}
                <span style="color: red">*</span>
              </div>
              <div class="value mtJ1">
                <ElSelect
                  v-model="queryBody.aggregationcycle"
                  v-bind="ElSelect_5"
                  v-on="ElSelect_5.event"
                >
                  <ElOption
                    v-for="item in ElOption_5.options_in"
                    :key="item[ElOption_5.key]"
                    :label="item[ElOption_5.label]"
                    :value="item[ElOption_5.value]"
                    :disabled="item[ElOption_5.disabled]"
                  ></ElOption>
                </ElSelect>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label">
                {{ $T("起止时间") }}
                <span style="color: red">*</span>
              </div>
              <div class="value mtJ1">
                <el-date-picker
                  v-model="CetDatePicker_1.val"
                  v-bind="CetDatePicker_1.config"
                  :clearable="false"
                ></el-date-picker>
              </div>
            </el-col>
          </el-row>
        </div>
        <el-table :data="tableData" border height="400" class="mtJ3">
          <el-table-column label="#" type="index" width="41"></el-table-column>
          <template v-if="queryBody.energysharemethod == 1">
            <el-table-column
              v-for="(item, index) in tableDataColumns"
              :key="item.key"
              :show-overflow-tooltip="true"
              :label="item.title"
              :prop="item.key"
              header-align="left"
            >
              <template slot-scope="scope">
                <div style="text-align: left" v-if="index == 1">
                  {{ $T("固定分摊") }}
                </div>
                <div style="text-align: left" v-else-if="index != 2">
                  {{ scope.row[item.key] }}
                </div>
                <el-input
                  v-else-if="index == 2"
                  v-model="scope.row[item.key]"
                  @keyup.native="handleNum(scope.row, item.key, 4)"
                  @blur="handleNum(scope.row, item.key, 4)"
                  :placeholder="$T('输入系数')"
                ></el-input>
              </template>
            </el-table-column>
          </template>
          <template v-if="queryBody.energysharemethod == 2">
            <el-table-column
              :show-overflow-tooltip="true"
              :label="$T('分摊对象')"
              prop="name"
              header-align="left"
              align="left"
            ></el-table-column>
          </template>
        </el-table>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
    <NodeSelect
      :visibleTrigger_in="NodeSelect.visibleTrigger_in"
      :closeTrigger_in="NodeSelect.closeTrigger_in"
      :queryId_in="NodeSelect.queryId_in"
      :inputData_in="NodeSelect.inputData_in"
      @confirm_out="NodeSelect_confirm_out"
    />
    <NodeSelect2
      :visibleTrigger_in="NodeSelect2.visibleTrigger_in"
      :closeTrigger_in="NodeSelect2.closeTrigger_in"
      :checkedNodes_in="NodeSelect2.checkedNodes_in"
      @confirm_out="NodeSelect2_confirm_out"
    />
  </div>
</template>
<script>
import NodeSelect from "./NodeSelect.vue";
import NodeSelect2 from "./NodeSelect2.vue";
import common from "eem-utils/common";
import { httping } from "@omega/http";
export default {
  name: "AddShareScheme",
  components: {
    NodeSelect,
    NodeSelect2
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },

  computed: {
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    }
  },

  data() {
    return {
      tableData: [],
      tableDataColumns: [
        {
          title: $T("分摊对象"),
          key: "name",
          width: 100
        },
        {
          title: $T("分摊方式"),
          key: "",
          width: 100
        },
        {
          title: $T("分摊比例"),
          key: "rate",
          width: 100
        }
      ],
      queryBody: {
        id: 0,
        name: "",
        aggregationcycle: 12,
        createtime: this.$moment().valueOf(),
        starttime: this.$moment().startOf("day").valueOf(),
        endtime: this.$moment().add(1, "day").startOf("day").valueOf(),
        energysharemethod: 1,
        energysharetoobject_model: [],
        energytype: 2,
        objectenergytype: 2,
        objectid: null,
        objectlabel: null,
        objectName: null
      },
      CetDialog_1: {
        title: $T("新增分摊方案"),
        width: "960px",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElSelect_1: {
        value: 2,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_1: {
        options_in: [
          {
            id: 2,
            text: $T("电")
          },
          {
            id: 3,
            text: $T("水")
          },
          {
            id: 15,
            text: $T("天然气")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请选择被分摊对象"),
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInput_tree: {
        value: "",
        placeholder: $T("请选择分摊对象"),
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInput_2: {
        value: "",
        placeholder: $T("请输入方案名称"),
        style: {
          width: "100%"
        },
        event: {}
      },
      ElSelect_2: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_2: {
        options_in: [
          {
            id: 1,
            text: $T("固定比例")
          },
          {
            id: 2,
            text: $T("动态分摊")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_3: {
        value: "",
        style: {},
        event: {}
      },
      ElOption_3: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out
        }
      },
      CetDatePicker_1: {
        disable_in: false,
        val: [],
        config: {
          valueFormat: "timestamp",
          type: "daterange",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          style: {
            width: "100%"
          }
        }
      },
      ElSelect_5: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_5: {
        options_in: [
          {
            id: 18,
            text: "15min"
          },
          {
            id: 6,
            text: "30min"
          },
          {
            id: 7,
            text: "1h"
          },
          {
            id: 12,
            text: "1D"
          },
          {
            id: 14,
            text: "1M"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      NodeSelect: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      NodeSelect2: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        checkedNodes_in: null
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;

      vm.$nextTick(() => {
        $(this.$refs.CetDialog.$el).scrollTop(0);
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    inputData_in(val) {
      if (val.id) {
        this.CetDialog_1.title = $T("修改分摊方案");
      } else {
        this.CetDialog_1.title = $T("新增分摊方案");
      }
      let checkedNodes = [];
      if (val.id === 0 || val.id) {
        this.queryBody = this._.cloneDeep(val);
        this.CetDatePicker_1.val = [
          this.$moment(this.queryBody.starttime).valueOf(),
          this.$moment(this.queryBody.endtime).valueOf()
        ];
        if (!this.queryBody.energytype) {
          this.queryBody.energytype = 2;
        }
        // 反勾选节点
        var data = [];
        val.energysharetoobject_model.forEach(item => {
          data.push({
            id: item.objectid,
            modelLabel: item.objectlabel,
            // modelLabel: 'energysharetoobject',
            name: item.objectname,
            rate: item.rate
            // shareId: item.id
          });
          checkedNodes.push({
            id: item.objectid,
            modelLabel: item.objectlabel,
            name: item.objectname,
            tree_id: item.objectlabel + "_" + item.objectid
          });
        });
        this.tableData = data;
      } else {
        this.queryBody = {
          id: 0,
          name: "",
          aggregationcycle: 12,
          createtime: this.$moment().valueOf(),
          starttime: this.$moment().startOf("day").valueOf(),
          endtime: this.$moment().add(1, "day").startOf("day").valueOf(),
          energysharemethod: 1,
          energysharetoobject_model: [],
          energytype: 2,
          objectenergytype: 2,
          objectid: val.objectid,
          objectlabel: val.objectlabel,
          objectName: val.objectName
        };
        this.CetDatePicker_1.val = [
          this.$moment().startOf("day").valueOf(),
          this.$moment().add(1, "day").startOf("day").valueOf()
        ];
        this.tableData = [];
      }
      this.getEnergytype();
      this.CetGiantTree_1_checkedNodes_out(checkedNodes);
    },

    "CetDatePicker_1.val": {
      handler: function (val) {
        this.queryBody.starttime = this._.get(val, "[0]");
        this.queryBody.endtime = this._.get(val, "[1]");
      },
      deep: true
    }
  },

  methods: {
    //获取能源类型
    getEnergytype() {
      var vm = this;
      vm.ElOption_1.options_in = [];
      httping({
        url:
          "/eem-service/v1/project/projectEnergy?projectId=" + this.projectId,
        method: "GET"
      }).then(function (response) {
        if (response.code === 0 && response.data && response.data.length > 0) {
          let selectData = [];
          response.data.forEach(item => {
            if (![13, 18, 22].includes(item.energytype)) {
              selectData.push({
                id: item.energytype,
                text: item.name
              });
            }
          });
          // var res = {};
          // res.id = 0;
          // res.text = "全部";
          // selectData.unshift(res);
          vm.ElOption_1.options_in = vm._.cloneDeep(selectData);
        }
      });
    },
    // 聚合参数
    getQueryBody() {
      var queryBody = {
        id: 0,
        // modelLabel: 'energyconsumptionshare',
        aggregationcycle: this.queryBody.aggregationcycle,
        createtime: this.$moment().valueOf(),
        endtime: this.$moment(this.queryBody.endtime).startOf("day").valueOf(),
        starttime: this.$moment(this.queryBody.starttime)
          .startOf("day")
          .valueOf(),
        energysharemethod: this.queryBody.energysharemethod,
        energysharetoobject_model: [
          // {
          //   modellabel: "xxxx",
          //   id: 10,
          //   rate: 0.7
          // },
          // {
          //   modellabel: "xxxx",
          //   id: 11,
          //   rate: 0.3
          // }
        ],
        energytype: this.queryBody.energytype,
        name: this.queryBody.name,
        objectenergytype: this.queryBody.objectenergytype,
        objectid: this.queryBody.objectid,
        objectlabel: this.queryBody.objectlabel
      };
      if (!queryBody.objectid) {
        this.$message({
          message: $T("被分摊对象不能为空"),
          type: "warning"
        });
        return false;
      } else if (!this.ElInput_tree.value) {
        this.$message({
          message: $T("分摊对象不能为空"),
          type: "warning"
        });
        return false;
      } else if (!queryBody.name) {
        this.$message({
          message: $T("方案名称不能为空"),
          type: "warning"
        });
        return false;
      } else if (queryBody.name.length > 20) {
        this.$message({
          message: $T("方案名称长度在 1 到 20 个字符"),
          type: "warning"
        });
        return false;
      } else if (!common.check_pattern_name.pattern.test(queryBody.name)) {
        this.$message({
          message: $T("方案名称不能输入特殊字符"),
          type: "warning"
        });
        return false;
      } else if (!queryBody.objectenergytype) {
        this.$message({
          message: $T("能源类型不能为空"),
          type: "warning"
        });
        return false;
      } else if (queryBody.endtime < queryBody.starttime) {
        this.$message({
          message: $T("开始时间需要小于结束时间"),
          type: "warning"
        });
        return false;
      }
      if (this.inputData_in && this.inputData_in.id) {
        queryBody.id = this.inputData_in.id;
      }
      // 判断选择的节点是否重复
      if (queryBody.energysharemethod == 1) {
        var num = 0;
        for (var i = 0; i < this.tableData.length; i++) {
          if (
            this.tableData[i].id == this.queryBody.objectid &&
            this.tableData[i].modelLabel == this.queryBody.objectlabel
          ) {
            this.$message({
              message: $T("分摊对象与被分摊对象不能是同一个节点"),
              type: "warning"
            });
            return false;
          }
          queryBody.energysharetoobject_model.push({
            // id: this.tableData[i].shareId,
            // modelLabel: 'energysharetoobject',
            objectlabel: this.tableData[i].modelLabel,
            objectid: this.tableData[i].id,
            objectname: this.tableData[i].name,
            rate: Number(this.tableData[i].rate)
          });
          num += Number(this.tableData[i].rate) * 1000;
        }
        if (num / 1000 !== 1) {
          this.$message({
            message: $T("分摊比例总和必须等于1"),
            type: "warning"
          });
          return false;
        }
        queryBody.energytype = this.queryBody.objectenergytype;
      } else if (queryBody.energysharemethod == 2) {
        for (let i = 0; i < this.tableData.length; i++) {
          if (
            this.tableData[i].id == this.queryBody.objectid &&
            this.tableData[i].modelLabel == this.queryBody.objectlabel
          ) {
            this.$message({
              message: $T("分摊对象与被分摊对象不能是同一个节点"),
              type: "warning"
            });
            return false;
          }
          queryBody.energysharetoobject_model.push({
            // id: this.tableData[i].shareId,
            // modelLabel: 'energysharetoobject',
            objectlabel: this.tableData[i].modelLabel,
            objectid: this.tableData[i].id,
            objectname: this.tableData[i].name
          });
        }
        queryBody.energytype = this.queryBody.energytype;
      }
      return queryBody;
    },
    // 保存方案
    confirmScheme(data) {
      httping({
        url: `/eem-service/v1/schemeConfig/saveEnergyShareConfigScheme`,
        method: "PUT",
        data
      }).then(response => {
        if (response.code == 0 && response.data) {
          this.$message({
            message: $T("保存成功"),
            type: "success"
          });
          this.$emit("finishTrigger_out", new Date().getTime());
          this.CetDialog_1.closeTrigger_in = new Date().getTime();
        }
      });
    },
    CetInput_1_Click() {
      this.NodeSelect.inputData_in = {
        id: this.queryBody.objectid,
        modelLabel: this.queryBody.objectlabel
      };
      this.NodeSelect.visibleTrigger_in = new Date().getTime();
    },
    CetInput_2_Click() {
      this.NodeSelect2.checkedNodes_in = this._.cloneDeep(
        this.CetGiantTree_1.checkedNodes
      );
      this.NodeSelect2.visibleTrigger_in = new Date().getTime();
    },
    NodeSelect_confirm_out(val) {
      if (val) {
        this.queryBody.objectid = val.id;
        this.queryBody.objectlabel = val.modelLabel;
        this.queryBody.objectName = val.name;
      } else {
        this.queryBody.objectid = null;
        this.queryBody.objectlabel = null;
        this.queryBody.objectName = null;
      }
    },
    NodeSelect2_confirm_out(val) {
      this.CetGiantTree_1_checkedNodes_out(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      var data = this.getQueryBody();
      if (!data) {
        return;
      }
      if (!data.id) {
        //新增
        // 保存前先查一次上一条分摊方案，此条方案开始时间需要大于上一条结束时间
        httping({
          url: `/eem-service/v1/schemeConfig/energyShareConfigScheme?objectid=${data.objectid}&objectlabel=${data.objectlabel}&energytype=${data.objectenergytype}`,
          method: "GET"
        }).then(response => {
          if (
            response.code == 0 &&
            response.data &&
            response.data.length > 0 &&
            response.data[0].endtime
          ) {
            if (!response.data[0].endtime) {
              this.$message({
                message: $T("上一条失效时间为空，无法新增"),
                type: "warning"
              });
            } else {
              this.confirmScheme(data);
            }
          } else {
            this.confirmScheme(data);
          }
        });
      } else {
        // 修改
        this.confirmScheme(data);
      }
    },
    CetGiantTree_1_checkedNodes_out(val) {
      this.CetGiantTree_1.checkedNodes = val;
      this.ElInput_tree.value = val.map(i => i.name).join(",");
      var data = this._.cloneDeep(val);
      data.forEach(ite => {
        if (this.tableData.length > 0) {
          this.tableData.forEach(item => {
            if (item.modelLabel == ite.modelLabel && item.id == ite.id) {
              ite.rate = item.rate;
            } else if (!ite.rate) {
              ite.rate = 0;
            }
          });
        } else {
          ite.rate = 0;
        }
      });
      this.tableData = data;
    },
    // 输入数字控制
    handleNum(row, key, num) {
      var value;
      if (typeof row[key] === "object") {
        if (row[key]) {
          value = row[key].val;
        } else {
          return;
        }
      } else {
        value = row[key];
      }
      var reg = new RegExp("^(\\-)*(\\d+)\\.(\\d{0," + num + "}).*$");
      var reg2 = new RegExp("^(\\d{0,1})(\\d+)\\.(\\d{0," + num + "}).*$");
      var reg3 = new RegExp("^(\\d{0,1})(\\d+)$");
      var val = String(value);
      if (val) {
        val = val.replace(/[^\d.]/g, ""); //清除数字和'.'以外的字符
        val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", "."); //只保留顺位第一的'.'
        val = val.replace(reg, "$1$2.$3"); //只能输入两位位小数
      }
      if (val.length >= 2 && val.indexOf(".") != 1 && val[0] == 0) {
        //在非小数的时候清除前导0
        val = val.replace(/0/, "");
      }
      if (val.length >= 2 && val.indexOf(".") == 0) {
        // 在先输入小数点时补0
        val = Number(val);
      }
      if (typeof row[key] === "object") {
        if (String(val).indexOf(".") != -1 && Number(val) > 1) {
          val = val.replace(reg2, "$1.$3");
        } else if (String(val).indexOf(".") == -1 && String(val).length > 1) {
          val = val.replace(reg3, "$1");
        }
        row[key].val = val;
      } else {
        if (String(val).indexOf(".") != -1 && Number(val) > 1) {
          val = val.replace(reg2, "$1.$3");
        } else if (String(val).indexOf(".") == -1 && String(val).length > 1) {
          val = val.replace(reg3, "$1");
        }
        row[key] = val;
      }
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
  }
  .title {
    @include font_weight(MD);
    @include margin_left(J3);
  }
  .rowBox {
    @include padding(J4);
  }
}
.titleBox {
  text-align: center;
  line-height: 24px;
  border-radius: 5px;
  margin-left: 0 !important;
  margin-right: 0 !important;
  @include background_color(B1);
}
.tree :deep(.el-tree) {
  overflow: auto;
}
</style>
