<template>
  <div class="fullfilled">
    <CetChart v-bind="CetChart_1"></CetChart>
  </div>
</template>

<script>
import commonApi from "@/api/custom.js";
import common from "eem-utils/common.js";
export default {
  props: {
    currentNode: Object,
    logTimes: Array,
    energyTypeObj: Object,
    aggregationCycle: Number,
    maximum: Boolean,
    averageValue: Boolean,
    chartType: Number
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    token() {
      return this.$store.state.token;
    }
  },
  data() {
    return {
      CetChart_1: {
        inputData_in: null,
        options: {
          toolbox: {
            top: 40,
            right: 30,
            feature: {
              saveAsImage: {
                title: $T("保存为图片")
              }
            }
          },
          legend: {},
          tooltip: {
            trigger: "axis"
          },
          grid: {
            top: "80",
            left: "16",
            right: "16",
            bottom: "8",
            containLabel: true
          },
          xAxis: [
            {
              type: "category",
              data: [],
              splitLine: {
                show: false
              }
            }
          ],
          yAxis: [
            {
              type: "value",
              name: "",
              min: 0,
              max: function (value) {
                if (value.max < 0) {
                  return 0;
                }
                return null;
              },
              splitLine: {
                show: false
              },
              nameTextStyle: {
                align: "left"
              }
            }
          ],
          series: [
            {
              name: "",
              type: "bar",
              data: [],
              barWidth: "16"
            }
          ]
        }
      }
    };
  },
  watch: {
    currentNode: {
      handler: function () {
        this.getData();
      },
      deep: true,
      immediate: true
    },
    logTimes: {
      handler: function () {
        this.getData();
      },
      deep: true,
      immediate: true
    },
    maximum: {
      handler: function () {
        this.checksChange();
      },
      deep: true
    },
    averageValue: {
      handler: function () {
        this.checksChange();
      },
      deep: true
    }
  },
  methods: {
    initChart() {
      this.CetChart_1.options.xAxis[0].data = [];
      this.CetChart_1.options.series[0].name = "";
      this.CetChart_1.options.series[0].data = [];
      this.CetChart_1.options.series[0].markPoint = null;
      this.CetChart_1.options.series[0].markLine = null;
      this.CetChart_1.options.yAxis[0].name = "";
      this.CetChart_1.options = this._.cloneDeep(this.CetChart_1.options);
    },
    getParams() {
      let logTimes = [];
      if (this.aggregationCycle === 7) {
        // 小时能耗特殊处理
        let [start, end] = this.logTimes;
        while (
          this.$moment(start).startOf("H").valueOf() <
          this.$moment(end).endOf("H").valueOf() + 1
        ) {
          logTimes.push(this.$moment(start).startOf("H").valueOf());
          start = this.$moment(start).add(1, "H").startOf("H").valueOf();
        }
      } else {
        logTimes = this.logTimes;
      }
      return {
        aggregationCycle: this.aggregationCycle,
        energyType: this.energyTypeObj.energytype,
        logTimes: logTimes,
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel,
        projectId: this.projectId
      };
    },
    CetButton_export_statusTrigger_out() {
      const queryData = this.getParams();
      common.downExcel(
        "eem-service/v1/energy/contrast/export",
        queryData,
        this.token,
        this.projectId
      );
    },
    async getData() {
      if (!this.logTimes || !this.logTimes.length) {
        this.initChart();
        return;
      }
      const queryData = this.getParams();
      const res = await commonApi.energyContrast(queryData);
      if (res.code !== 0) {
        this.initChart();
        return;
      }
      this.setChart(res.data);
    },
    setChart(data) {
      const currentdata = this._.get(data, "currentdata", []) || [];
      let xAxisData = [],
        seriesData = [],
        dateFormat = "yyyy-MM-DD";
      switch (this.aggregationCycle) {
        case 17:
          dateFormat = "yyyy";
          break;
        case 14:
          dateFormat = "yyyy-MM";
          break;
        case 13:
          dateFormat = "yyyy年第WW周(M.D-M.D)";
          break;
        case 12:
          dateFormat = "yyyy-MM-DD";
          break;
        case 7:
          dateFormat = "MM-DD HH:mm";
          break;
        default:
          dateFormat = "yyyy-MM-DD";
          break;
      }
      currentdata.forEach(item => {
        if (item.value || item.value === 0) {
          seriesData.push(item.value.toFixed(2));
        } else {
          seriesData.push(null);
        }
        if (this.aggregationCycle === 13) {
          const start = this.$moment(item.time);
          const month = start.month();
          const week = this.$moment(start).isoWeek();
          let year;
          if (week === 1 && month === 11) {
            year = start.add(1, "year").format("YYYY");
          } else {
            year = start.format("YYYY");
          }
          xAxisData.push(
            `${year}年第${week}周(${start.format("M.D")}-${start
              .add(6, "d")
              .format("M.D")})`
          );
        } else {
          xAxisData.push(this.$moment(item.time).format(dateFormat));
        }
      });
      this.CetChart_1.options.xAxis[0].data = xAxisData;
      this.CetChart_1.options.series[0].data = seriesData;
      this.CetChart_1.options.series[0].name = this.currentNode.name;
      const symbol = data.symbol || "--";
      this.CetChart_1.options.tooltip.formatter = val => {
        const list = val || [];
        let formatterStr = "";
        for (let i = 0, len = list.length; i < len; i++) {
          if (i === 0) {
            formatterStr += `${val[i].name}`;
          }
          formatterStr += `<br/>${val[i].marker}${val[i].seriesName} : ${
            val[i].value == null ? "--" : val[i].value
          }(${symbol || "--"})`;
        }
        return formatterStr;
      };

      this.CetChart_1.options.yAxis[0].name = `用${this.energyTypeObj.name}量（${symbol}）`;
      this.checksChange();
      this.chartTypeChange();
    },
    checksChange() {
      const currentTheme = localStorage.getItem("omega_theme");
      let bgColor = "";
      let borderColor = "";
      if (["dark", "blue"].includes(currentTheme)) {
        bgColor = "rgba(38, 41, 56, 0.7)";
        borderColor = "#414b6e";
      } else {
        bgColor = " rgba(255, 255, 255, 0.7)";
        borderColor = "#e0e4e8";
      }
      if (this.maximum) {
        this.CetChart_1.options.series[0].markPoint = {
          data: [
            {
              type: "max",
              symbolSize: 30,
              name: $T("最大值"),
              label: {
                formatter(params) {
                  return (
                    params.name +
                    ": " +
                    common.formatNumberWithPrecision(params.value, 2)
                  );
                },
                position: "top",
                padding: 8,
                backgroundColor: bgColor,
                borderType: "solid",
                borderWidth: 1,
                borderColor: borderColor,
                borderRadius: 4
              }
            },
            {
              type: "min",
              symbolSize: 30,
              name: $T("最小值"),
              label: {
                formatter(params) {
                  return (
                    params.name +
                    ": " +
                    common.formatNumberWithPrecision(params.value, 2)
                  );
                },
                position: "top",
                padding: 8,
                backgroundColor: bgColor,
                borderType: "solid",
                borderWidth: 1,
                borderColor: borderColor,
                borderRadius: 4
              }
            }
          ]
        };
      } else {
        this.CetChart_1.options.series[0].markPoint = null;
      }
      if (this.averageValue) {
        this.CetChart_1.options.series[0].markLine = {
          data: [
            {
              type: "average",
              name: "平均值",
              label: {
                show: true,
                position: "insideEndTop",
                formatter(val) {
                  return `${Number(val.value).toFixed(2)}(平均值)`;
                }
              }
            }
          ]
        };
      } else {
        this.CetChart_1.options.series[0].markLine = null;
      }
      this.CetChart_1.options = this._.cloneDeep(this.CetChart_1.options);
    },
    // 图表类型切换
    chartTypeChange() {
      this.CetChart_1.options.series.forEach(item => {
        item.type = this.chartType === 1 ? "bar" : "line";
      });
      this.CetChart_1.options.grid.bottom = this.chartType === 1 ? 50 : 8;
      this.CetChart_1.options.dataZoom =
        this.chartType === 1
          ? [
              {
                type: "inside",
                startValue: 0,
                endValue: 29
                // zoomOnMouseWheel: false
              },
              {
                startValue: 0,
                endValue: 29
                // zoomLock: true,
                // brushSelect: false
              }
            ]
          : null;
      this.CetChart_1.options = this._.cloneDeep(this.CetChart_1.options);
    }
  }
};
</script>
<style lang="scss" scoped></style>
