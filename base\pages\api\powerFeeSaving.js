import fetch from "eem-utils/fetch";
let version = "v1";

// 电度电费概览-电度电费
export function dianduFeePreview(data) {
  return fetch({
    url: `/eem-service/${version}/costanalysis/costByNodeAndFeeRateType`,
    method: "POST",
    data
  });
}
// 电度电费概览-同环比数据
export function dianduFGPPreview(data) {
  return fetch({
    url: `/eem-service/${version}/diagnosis/electricityCharge/tsPeriodRatio`,
    method: "POST",
    data
  });
}

//峰谷平bar数据
export function dianduFGPChart(data) {
  return fetch({
    url: `/eem-service/${version}/energy/energydata/time/timeshare`,
    method: "POST",
    data
  });
}
//电度电费增改查
export function dianduDiagnoseStandarQuery(data) {
  return fetch({
    url: `/eem-service/${version}/energy/tsPeriod/ratio?type=1`,
    method: "GET",
    data
  });
}
export function dianduDiagnoseStandarSet(data) {
  return fetch({
    url: `/eem-service/${version}/energy/tsPeriod/ratio`,
    method: "PUT",
    data
  });
}

//电度电费诊断结果
export function dianduDiagnoseResult(data) {
  return fetch({
    url: `/eem-service/${version}/diagnosis/electricityCharge/diagnosis/result`,
    method: "POST",
    data
  });
}

//力调电费-成本查询
export function litiaoCostQuery(data) {
  return fetch({
    url: `/eem-service/${version}/costanalysis/costByNodeAndFeeRateType`,
    method: "POST",
    data
  });
}

// 查询用电峰谷平用电量
export function dianduFGPEnergyUse(data) {
  return fetch({
    url: `/eem-service/${version}/diagnosis/electricityCharge/tsConsumption`,
    method: "POST",
    data
  });
}

//力调电费- 查询功率因数以及同环比
export function litiaoFactorAndTHB(data) {
  return fetch({
    url: `/eem-service/${version}/diagnosis/powertraiff`,
    method: "POST",
    data
  });
}

//力调电费- 查询子周期功率因数以及同环比
export function litiaoUseChart(data) {
  return fetch({
    url: `/eem-service/${version}/diagnosis/powertraiff/child`,
    method: "POST",
    data
  });
}

//力调电费- 诊断结果
export function litiaoDiagnoseResult(data) {
  return fetch({
    url: `/eem-service/${version}/diagnosis/powertraiff/diagnosis/result`,
    method: "POST",
    data
  });
}

//力调电费- 标准功率因数
export function litiaoStandarFactor(data) {
  return fetch({
    url:
      `/eem-service/${version}/schemeConfig/bestPowerTariffFactor?time=` +
      Date.now(),
    method: "POST",
    data
  });
}

//基本电费-最大需量:sType--day:日，month:月
export function maxAvgNeed(data, sType) {
  return fetch({
    url: `/eem-service/${version}/diagnosis/basic/${sType}/detail`,
    method: "POST",
    data
  });
}

//基本电费-诊断结论
export function baseDiagnoseResult(data) {
  return fetch({
    url: `/eem-service/${version}/diagnosis/basic/diagnosis/result`,
    method: "POST",
    data
  });
}
