<template>
  <el-container class="page">
    <el-aside width="320px" class="fullheight bg1 pJ3 border-right">
      <!-- left组件 -->
      <CetGiantTree
        v-bind="CetGiantTree_left"
        v-on="CetGiantTree_left.event"
        class="treeStyle"
      ></CetGiantTree>
    </el-aside>
    <el-main class="fullheight bg1 pJ3">
      <div style="height: calc(50% - 8px)" class="mbJ3">
        <div class="head flex">
          <div class="title">{{ $T("运行参数分析") }}</div>
          <customElSelect
            class="flex-end multipleSelect"
            v-model="ElSelect_parameter.value"
            v-bind="ElSelect_parameter"
            v-on="ElSelect_parameter.event"
            :prefix_in="$T('分析参数')"
          >
            <ElOption
              v-for="item in ElOption_parameter.options_in"
              :key="item[ElOption_parameter.key]"
              :label="item[ElOption_parameter.label]"
              :value="item[ElOption_parameter.value]"
              :disabled="item[ElOption_parameter.disabled]"
            ></ElOption>
          </customElSelect>
          <CetDateSelect
            class="mlJ2 date-select"
            v-bind="CetDateSelect_time"
            v-on="CetDateSelect_time.event"
          ></CetDateSelect>
        </div>
        <runParamaterAnalysis
          style="height: calc(100% - 40px)"
          :chartData="chartData"
        />
      </div>
      <div style="height: calc(50% - 8px)">
        <el-row class="fullfilled" :gutter="16">
          <el-col :span="8" class="fullheight">
            <parameterCorrelationAnalysis :heatMapData="heatMapData" />
          </el-col>
          <el-col :span="4.5" class="fullheight" style="width: 18.75%">
            <efficiencyRatio :efficiencyRatioData="efficiencyRatioData" />
          </el-col>
          <el-col :span="4.5" class="fullheight" style="width: 18.75%">
            <efficiencyRank :efficiencyRankData="heatMapData" />
          </el-col>
          <el-col :span="7" class="fullheight">
            <suggestionOutput :suggestionData="suggestionData" />
          </el-col>
        </el-row>
      </div>
    </el-main>
  </el-container>
</template>

<script>
import customApi from "@/api/custom.js";
import runParamaterAnalysis from "./components/runParamaterAnalysis.vue";
import parameterCorrelationAnalysis from "./components/parameterCorrelationAnalysis.vue";
import efficiencyRatio from "./components/efficiencyRatio.vue";
import efficiencyRank from "./components/efficiencyRank.vue";
import suggestionOutput from "./components/suggestionOutput.vue";
export default {
  name: "singleMachine",
  components: {
    runParamaterAnalysis,
    parameterCorrelationAnalysis,
    efficiencyRatio,
    efficiencyRank,
    suggestionOutput
  },
  data() {
    return {
      currentNode: {},
      queryTime: {
        startTime: this.$moment(Date.now()).startOf("M").valueOf(),
        endTime: this.$moment(Date.now()).endOf("M").valueOf() + 1
      },
      chartData: [],
      heatMapData: [],
      efficiencyRatioData: {},
      suggestionData: "",
      // parameter组件
      ElSelect_parameter: {
        value: [13],
        multiple: true,
        "collapse-tags": true,
        style: {
          width: "250px"
        },
        event: {
          change: this.ElSelect_parameter_change_out
        }
      },
      // parameter组件
      ElOption_parameter: {
        options_in: this.$store.state.enumerations.compressoradjustparameter,
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // time组件
      CetDateSelect_time: {
        value: {
          dateType: "3",
          value: new Date().getTime()
        }, //设置日期值, dateType 1 日 2 周 3月 4季 5年 6自定义
        typeList: ["week", "month", "season"],
        align: "right",
        //自定义选项 typeList: ["day", "week", "month", "season",  "year", "daterange"]
        event: {
          date_out: this.CetDateSelect_time_date_out
        }
      },
      // left组件
      CetGiantTree_left: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: this.setNodeClasses
          },
          callback: {
            beforeClick: this.beforeClick
          }
        },
        event: {
          created_out: this.CetGiantTree_created_out,
          currentNode_out: this.CetGiantTree_left_currentNode_out //选中单行输出
        }
      }
    };
  },
  watch: {},
  methods: {
    /**
     * 获取节点树
     */
    getTreeData() {
      const params = {
        rootID: 0,
        rootLabel: "project",
        subLayerConditions: [
          {
            modelLabel: "gasgatheringstation"
          },
          {
            modelLabel: "dehydratingstation"
          },
          {
            modelLabel: "gascompressor",
            filter: {
              composemethod: true,
              expressions: [
                {
                  limit: 2,
                  operator: "EQ",
                  prop: "principletype",
                  tagid: 1
                },
                {
                  limit: 1,
                  operator: "EQ",
                  prop: "compressortype",
                  tagid: 1
                }
              ]
            }
          }
        ],
        treeReturnEnable: true
      };
      customApi.getNodeTree(params).then(res => {
        const data = res.data || [];
        this.handleDisablingTree(data);
        this.CetGiantTree_left.inputData_in = data;
        const flatData = this.flatTreeData(data);
        const node = flatData?.find(
          item => item.modelLabel === "gascompressor"
        );
        this.CetGiantTree_left.selectNode = node || {};
      });
    },
    /**
     * 拍平节点树数据
     */
    flatTreeData(treeData) {
      const cloneData = this._.cloneDeep(treeData);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },

    /**
     * 节点是否可选择
     */
    beforeClick(treeId, treeNode) {
      if (treeNode.modelLabel != "gascompressor") {
        return false;
      }
      return true;
    },
    /**
     * 设置节点禁用
     */
    handleDisablingTree(nodes) {
      nodes.forEach(item => {
        if (item.modelLabel !== "gascompressor") {
          item.disableNode = true;
        } else {
          item.disableNode = false;
        }
        if (item.children && item.children.length > 0) {
          this.handleDisablingTree(item.children);
        }
      });
    },
    setNodeClasses(treeId, treeNode) {
      return treeNode.disableNode
        ? { add: ["disableNode"] }
        : { remove: ["disableNode"] };
    },
    /**
     * 展开全部
     */
    CetGiantTree_created_out(val) {
      val.expandAll(true);
    },
    /**
     * 节点树选择
     */
    CetGiantTree_left_currentNode_out(val) {
      this.currentNode = _.cloneDeep(val);
      this.queryChartData();
      this.queryOtherData();
    },

    /**
     * 修改分析参数
     */
    ElSelect_parameter_change_out(val) {
      if (val.length > 6) {
        this.$message.warning($T("最多只可选择6个参数"));
        this.ElSelect_parameter.value.pop();
        return;
      }
      this.queryChartData();
    },

    /**
     * 时间切换
     */
    CetDateSelect_time_date_out(val) {
      const queryTime = {
        startTime: val[0],
        endTime: val[1] + 1
      };
      this.queryTime = queryTime;
      this.queryChartData();
      this.queryOtherData();
    },

    /**
     * 获取曲线数据
     */
    queryChartData() {
      const params = {
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel,
        params: this.ElSelect_parameter.value,
        startTime: this.queryTime.startTime,
        endTime: this.queryTime.endTime
      };
      customApi.queryParameterAnalysis(params).then(res => {
        const data = res.data || [];
        this.chartData = data;
      });
    },

    /**
     * 获取页面下方其他数据
     */
    queryOtherData() {
      const params = {
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel,
        startTime: this.queryTime.startTime,
        endTime: this.queryTime.endTime
      };
      customApi.queryParameterCorrelationAndStrategy(params).then(res => {
        const data = res.data || {};
        this.heatMapData = data.heatMap || [];
        this.efficiencyRatioData = {
          efficiency: data.efficient || 0,
          inefficiency: data.inefficiency || 0
        };
        this.suggestionData = data.suggestion || "";
      });
    }
  },
  mounted() {
    this.getTreeData();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.treeStyle {
  :deep(.disableNode) {
    @include font_color(T6 !important);
    cursor: not-allowed !important;
    opacity: 0.6;
  }
}
.border-right {
  border-right: 1px solid;
  @include border_color(B1);
}
.head {
  height: 32px;
  line-height: 32px;
  margin-bottom: 8px;
  .title {
    font-size: 16px;
    font-weight: bold;
  }
}
.flex-end {
  margin-left: auto;
  justify-content: flex-end;
}
.multipleSelect {
  :deep(.el-select__tags) {
    width: calc(100% - 96px) !important;
  }
}
.date-select {
  width: 350px;
  :deep(.el-date-editor) {
    width: 150px !important;
  }
}
</style>
