<template>
  <div>
    <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
      <div slot="title">
        <span class="common-title-H3 mrJ3">{{ CetDialog_1.title }}</span>
        <el-tooltip
          effect="dark"
          content="物理量会根据聚合方式、聚合周期、设备类型的选择结果综合判断是否重复关联过方案，如重复关联过方案则该物理量不可选中"
          placement="bottom-start"
        >
          <span class="el-icon-info"></span>
        </el-tooltip>
        <span class="fcT3">物理量关联规则</span>
      </div>
      <CetForm
        class="cetForm flex-column plJ4 prJ4 ptJ4 bg1 brC1"
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
      >
        <el-row :gutter="$J3">
          <el-col :span="24">
            <el-form-item label="方案名称" prop="name">
              <ElInput
                class="fullwidth"
                placeholder="请输入方案名称"
                maxlength="50"
                show-word-limit
                v-model.trim="CetForm_1.data.name"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="聚合方式" prop="quantityaggregationtype_model">
              <ElSelect
                v-model="CetForm_1.data.quantityaggregationtype_model"
                multiple
                class="fullwidth"
                @change="checkDataId"
              >
                <ElOption
                  v-for="item in ElOption_type.options_in"
                  :key="item[ElOption_type.key]"
                  :label="item[ElOption_type.label]"
                  :value="item[ElOption_type.value]"
                  :disabled="item[ElOption_type.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="聚合周期"
              prop="quantityaggregationcycle_model"
            >
              <ElSelect
                v-model="CetForm_1.data.quantityaggregationcycle_model"
                multiple
                class="fullwidth"
                @change="checkDataId"
              >
                <ElOption
                  v-for="item in ElOption_cycle.options_in"
                  :key="item[ElOption_cycle.key]"
                  :label="item[ElOption_cycle.label]"
                  :value="item[ElOption_cycle.value]"
                  :disabled="item[ElOption_cycle.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备类型" prop="objectlabel">
              <ElSelect
                v-model="CetForm_1.data.objectlabel"
                @change="deviceTypeChange"
                :disabled="!!programmeId"
                class="fullwidth"
              >
                <ElOption
                  v-for="item in ElOption_deviceType.options_in"
                  :key="item[ElOption_deviceType.key]"
                  :label="item[ElOption_deviceType.label]"
                  :value="item[ElOption_deviceType.value]"
                  :disabled="item[ElOption_deviceType.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="$J3" class="flex-auto">
          <el-col :span="24" class="fullheight">
            <el-form-item
              prop="quantityaggregationsetting_model"
              class="formItemFullheight"
            >
              <CetTable
                ref="cetTable"
                :data.sync="CetTable_1.data"
                :dynamicInput.sync="CetTable_1.dynamicInput"
                v-bind="CetTable_1"
                v-on="CetTable_1.event"
              >
                <template v-for="item in Columns_1">
                  <ElTableColumn
                    :key="item.label"
                    v-bind="item"
                  ></ElTableColumn>
                </template>
              </CetTable>
            </el-form-item>
          </el-col>
        </el-row>
      </CetForm>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom";
import common from "eem-utils/common.js";
export default {
  props: {
    openTrigger_in: Number,
    inputData_in: Object,
    deviceTypeList_in: Array,
    aggregationType_in: Array,
    aggregationCycle_in: Array
  },
  computed: {
    programmeId() {
      return this.inputData_in && this.inputData_in.id;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    oldDataIds() {
      if (!this.programmeId) return [];
      return (this.oldScheme.quantityaggregationsetting_model || []).map(
        i => i.dataid
      );
    }
  },
  data() {
    const checkPoints = (rule, value, callback) => {
      if (!value || !value.length) {
        return callback(new Error("请至少选择1项物理量"));
      }
      if (value.length > 10) {
        return callback(new Error("最多仅能选择10项物理量"));
      }
      callback();
    };
    return {
      oldScheme: null,
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        top: "10vh",
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "保存",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: "请输入方案名称",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          quantityaggregationtype_model: [
            {
              required: true,
              message: "请选择聚合方式",
              trigger: ["blur", "change"]
            }
          ],
          quantityaggregationcycle_model: [
            {
              required: true,
              message: "请选择聚合周期",
              trigger: ["blur", "change"]
            }
          ],
          objectlabel: [
            {
              required: true,
              message: "请选择设备类型",
              trigger: ["blur", "change"]
            }
          ],
          quantityaggregationsetting_model: [
            {
              required: true,
              validator: checkPoints,
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElInput_name: {
        value: "",
        placeholder: "请输入方案名称"
      },
      ElOption_type: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElOption_cycle: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElOption_deviceType: {
        options_in: [],
        key: "monitoredlabel",
        value: "monitoredlabel",
        label: "name",
        disabled: "disabled"
      },
      disableDataIds: [],
      disableSelection: false,
      CetTable_1: {
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 10,
          layout: "total,sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        "empty-text": "暂无数据展示，请先选择设备类型",
        highlightCurrentRow: false,
        "header-cell-class-name": ({ columnIndex }) => {
          if (this.disableSelection) {
            if (columnIndex === 0) {
              return "DisableSelection";
            }
          }
        },
        "row-class-name": ({ row, rowIndex }) => {
          return this.disableDataIds.includes(row.dataId) ? "disableRow" : "";
        },
        event: {
          "selection-change": this.handleSelectionChange
        }
      },
      Columns_1: [
        {
          type: "selection", // selection 勾选 index 序号
          headerAlign: "left",
          align: "left",
          selectable: row => {
            return !this.disableDataIds.includes(row.dataId);
          }
        },
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          width: "50" //绝对宽度
        },
        {
          prop: "templatename", // 支持path a[0].b
          label: "物理量", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "dataId", // 支持path a[0].b
          label: "DataID", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ]
    };
  },
  watch: {
    async openTrigger_in() {
      this.CetDialog_1.title = this.programmeId ? "编辑方案" : "新建方案";
      this.CetTable_1.data = [];
      this.ElOption_deviceType.options_in = this.deviceTypeList_in || [];
      this.ElOption_cycle.options_in = this.aggregationCycle_in || [];
      this.ElOption_type.options_in = this.aggregationType_in || [];
      this.disableSelection = false;
      if (this.programmeId) {
        this.editInit();
      } else {
        this.CetForm_1.data = {};
      }
      this.CetDialog_1.openTrigger_in = Date.now();
      this.CetForm_1.resetTrigger_in = Date.now();
    }
  },
  methods: {
    async editInit() {
      this.CetForm_1.data = {};
      this.CetForm_1.data = await this.queryScheme(this.programmeId);
      const quantityaggregationsetting_model =
        this._.cloneDeep(
          this.CetForm_1.data.quantityaggregationsetting_model
        ) || [];
      await this.deviceTypeChange(this.CetForm_1.data.objectlabel);
      // 设置选中测点
      const pointList = this.$refs.cetTable.tableData;
      quantityaggregationsetting_model.forEach(item => {
        const obj = pointList.find(i => i.dataId === item);
        if (obj) {
          this.$refs.cetTable.$refs.cetTable.toggleRowSelection(obj);
        }
      });
    },
    async queryScheme(id) {
      const res = await customApi.queryCalculateScheme({
        schemeId: id
      });
      const data = this._.get(res, "data");
      this.oldScheme = this._.cloneDeep(data);
      if (this._.isArray(data.quantityaggregationcycle_model)) {
        data.quantityaggregationcycle_model =
          data.quantityaggregationcycle_model.map(i => i.aggregationcycle);
      }
      if (this._.isArray(data.quantityaggregationtype_model)) {
        data.quantityaggregationtype_model =
          data.quantityaggregationtype_model.map(i => i.aggregationtype);
      }
      if (this._.isArray(data.quantityaggregationsetting_model)) {
        data.quantityaggregationsetting_model =
          data.quantityaggregationsetting_model.map(i => i.dataid);
      }
      return data;
    },
    // 设置哪些dataid是已经被关联过的就不允许选择
    async checkDataId() {
      const monitoredlabel = this.CetForm_1.data.objectlabel,
        quantityAggregationCycle =
          this.CetForm_1.data.quantityaggregationcycle_model || [],
        quantityAggregationType =
          this.CetForm_1.data.quantityaggregationtype_model || [];
      if (
        !monitoredlabel ||
        !quantityAggregationCycle.length ||
        !quantityAggregationType.length
      ) {
        // 没选完就重置
        this.setDisableDataIds([]);
        return;
      }
      let disableDataIds = [];
      const res = await customApi.queryCalculateCheckDataId({
        groupid: this.projectId,
        monitoredlabel,
        quantityAggregationCycle,
        quantityAggregationType
      });
      disableDataIds = this._.get(res, "data", []);
      this.setDisableDataIds(disableDataIds);
    },
    setDisableDataIds(ids) {
      // 编辑排除当前方案关联的dataid
      ids = ids.filter(i => !this.oldDataIds.includes(i));
      this.disableDataIds = ids;
      if (!this.CetForm_1.data.quantityaggregationsetting_model) return;
      // 已经勾选的先取消掉；
      const deleteIds = this._.intersection(
        this.CetForm_1.data.quantityaggregationsetting_model,
        ids
      );
      this.CetForm_1.data.quantityaggregationsetting_model = this._.uniq([
        ...this.CetForm_1.data.quantityaggregationsetting_model,
        ...deleteIds
      ]);
      const pointList = this.$refs.cetTable.tableData;
      deleteIds.forEach(item => {
        const obj = pointList.find(i => i.dataId === item);
        if (obj) {
          this.$refs.cetTable.$refs.cetTable.toggleRowSelection(obj, false);
        }
      });
      this.disableSelection = this.CetTable_1.data.length - ids.length > 10;
    },
    CetButton_confirm_statusTrigger_out() {
      this.CetForm_1.localSaveTrigger_in = Date.now();
    },
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    },
    async deviceTypeChange(val) {
      const res = await customApi.calculateMonitoredNode({
        monitoredlabel: val
      });
      const data = this._.get(res, "data", []) || [];
      this.$set(this.CetForm_1.data, "quantityaggregationsetting_model", []);
      if (this.$refs.cetTable) {
        this.$refs.cetTable.$refs.cetTable.clearSelection();
      }
      this.CetTable_1.data = data;
      this.disableSelection = data.length > 10;
      this.checkDataId();
    },
    handleSelectionChange(val) {
      this.CetForm_1.data.quantityaggregationsetting_model = val.map(
        i => i.dataId
      );
    },
    async CetForm_1_saveData_out() {
      const formData = this.CetForm_1.data;
      const seveData = {
        name: formData.name,
        id: formData.id,
        groupid: this.projectId,
        energytype: 2,
        objectlabel: formData.objectlabel,
        quantityaggregationsetting_model:
          formData.quantityaggregationsetting_model.map(i => {
            return {
              dataid: i,
              modelLabel: "quantityaggregationsetting"
            };
          }),
        quantityaggregationtype_model:
          formData.quantityaggregationtype_model.map(i => {
            return {
              aggregationtype: i,
              modelLabel: "quantityaggregationtype"
            };
          }),
        quantityaggregationcycle_model:
          formData.quantityaggregationcycle_model.map(i => {
            return {
              aggregationcycle: i,
              modelLabel: "quantityaggregationcycle"
            };
          })
      };
      const saveFn = seveData.id ? "calculateEdit" : "calculateCreate";
      // 编辑方案在原方案中找到id，剩下的打上删除标记
      if (seveData.id && this.oldScheme) {
        if (this._.isArray(this.oldScheme.quantityaggregationsetting_model)) {
          let quantityaggregationsetting_delete = [];
          this.oldScheme.quantityaggregationsetting_model.forEach(item => {
            const obj = seveData.quantityaggregationsetting_model.find(
              i => i.dataid === item.dataid
            );
            if (obj) {
              obj.id = item.id;
            } else {
              quantityaggregationsetting_delete.push({
                ...item,
                delete_flag: true
              });
            }
          });
          seveData.quantityaggregationsetting_model.push(
            ...quantityaggregationsetting_delete
          );
        }
        if (this._.isArray(this.oldScheme.quantityaggregationtype_model)) {
          let quantityaggregationtype_delete = [];
          this.oldScheme.quantityaggregationtype_model.forEach(item => {
            const obj = seveData.quantityaggregationtype_model.find(
              i => i.aggregationtype === item.aggregationtype
            );
            if (obj) {
              obj.id = item.id;
            } else {
              quantityaggregationtype_delete.push({
                ...item,
                delete_flag: true
              });
            }
          });
          seveData.quantityaggregationtype_model.push(
            ...quantityaggregationtype_delete
          );
        }
        if (this._.isArray(this.oldScheme.quantityaggregationcycle_model)) {
          let quantityaggregationcycle_delete = [];
          this.oldScheme.quantityaggregationcycle_model.forEach(item => {
            const obj = seveData.quantityaggregationcycle_model.find(
              i => i.aggregationcycle === item.aggregationcycle
            );
            if (obj) {
              obj.id = item.id;
            } else {
              quantityaggregationcycle_delete.push({
                ...item,
                delete_flag: true
              });
            }
          });
          seveData.quantityaggregationcycle_model.push(
            ...quantityaggregationcycle_delete
          );
        }
      }
      const res = await customApi[saveFn](seveData);
      if (res.code !== 0) return;
      this.$message({
        message: "保存成功",
        type: "success"
      });
      this.$emit("reloadTable");
      this.CetDialog_1.closeTrigger_in = Date.now();
    }
  }
};
</script>

<style lang="scss" scoped>
.cetForm {
  padding-bottom: 40px;
  height: 606px;
  .formItemFullheight {
    height: 100%;
    margin-bottom: 0;
    :deep(.el-form-item__content) {
      height: 100%;
    }
    :deep(.DisableSelection .el-checkbox) {
      display: none;
    }
    :deep(.disableRow .cell) {
      @include font_color(T6);
    }
  }
}
</style>
