import { conf } from "@omega/app";
// import image from "../omega/assets/favicon.png";

export function getProjectIco() {
  let projectLogoIco = _.get(conf.state, "otherSertting.project_logo_ico");
  var link = document.querySelector("link[rel*='icon']");
  // 通过直接替换图标的方法
  link.type = "image/x-icon";
  link.rel = "shortcut icon";
  link.href = projectLogoIco ? projectLogoIco : "./favicon.ico";
  document.getElementsByTagName("head")[0].appendChild(link);
}
