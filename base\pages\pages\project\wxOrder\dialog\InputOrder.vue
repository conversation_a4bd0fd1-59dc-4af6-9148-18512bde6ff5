<template>
  <div>
    <CetDialog v-bind="CetDialog_add" v-on="CetDialog_add.event">
      <el-container class="eem-cont-c1">
        <CetForm
          ref="createPlanForm"
          :data.sync="CetForm_1.data"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
        >
          <el-row :gutter="$J3">
            <el-col :span="8">
              <el-form-item :label="$T('工单号')" prop="code">
                <ElInput
                  v-model="inputData_in && inputData_in.code"
                  disabled
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('执行人')" prop="executor">
                <ElSelect
                  multiple
                  collapse-tags
                  v-model="CetForm_1.data.executor"
                  v-bind="ElSelect_executor"
                  v-on="ElSelect_executor.event"
                >
                  <ElOption
                    v-for="item in ElOption_executor.options_in"
                    :key="item[ElOption_executor.key]"
                    :label="item[ElOption_executor.label]"
                    :value="item[ElOption_executor.value]"
                    :disabled="item[ElOption_executor.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('开始时间')" prop="executetime">
                <el-date-picker
                  v-model="CetForm_1.data.executetime"
                  v-bind="CetDatePicker_1.config"
                  :placeholder="$T('选择日期')"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('完成时间')" prop="finishtime">
                <el-date-picker
                  v-model="CetForm_1.data.finishtime"
                  v-bind="CetDatePicker_1.config"
                  :placeholder="$T('选择日期')"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="inputData_in && inputData_in.fillformtype === 2"
            >
              <el-form-item
                :label="$T('维修确认时间')"
                prop="repairconfirmtime"
              >
                <el-date-picker
                  v-model="CetForm_1.data.repairconfirmtime"
                  v-bind="CetDatePicker_1.config"
                  :placeholder="$T('选择日期')"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="inputData_in && inputData_in.fillformtype === 2"
            >
              <el-form-item
                :label="$T('运值确认时间')"
                prop="inspectconfirmtime"
              >
                <el-date-picker
                  v-model="CetForm_1.data.inspectconfirmtime"
                  v-bind="CetDatePicker_1.config"
                  :placeholder="$T('选择日期')"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$T('维修记录')" prop="handledescription">
                <el-input
                  type="textarea"
                  rows="3"
                  resize="none"
                  :placeholder="$T('请输入内容')"
                  v-model="CetForm_1.data.handledescription"
                  onKeypress="javascript:if(event.keyCode == 32)event.returnValue = false;"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$T('消耗备件')">
                <div
                  class="list_item mtJ"
                  v-for="(item, index) in listData"
                  :key="index"
                >
                  <ElSelect
                    v-model="listData[index]"
                    v-bind="ElSelect_2"
                    v-on="ElSelect_2.event"
                    class="fl mrJ1"
                  >
                    <ElOption
                      v-for="item in ElOption_2.options_in"
                      :key="item[ElOption_2.key]"
                      :label="item[ElOption_2.label]"
                      :value="item"
                      :disabled="item[ElOption_2.disabled]"
                    ></ElOption>
                  </ElSelect>
                  <div class="fl countBox">
                    <ElInputNumber
                      v-model="item.number"
                      v-bind="ElInputNumber_1"
                      v-on="ElInputNumber_1.event"
                    ></ElInputNumber>
                    <el-tooltip
                      :content="item.unit"
                      effect="light"
                      placement="top"
                    >
                      <span class="count">
                        {{ filUnit(item.unit) || "" }}
                      </span>
                    </el-tooltip>
                  </div>
                  <i
                    class="el-icon-delete deleteIcon"
                    @click="handleDeleteSparepart(index)"
                    style="cursor: pointer"
                  ></i>
                </div>
                <!-- 添加备件按钮组件 -->
                <CetButton
                  v-bind="CetButton_add"
                  v-on="CetButton_add.event"
                  style="margin: 10px 0"
                ></CetButton>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('使用预案')" prop="">
                <ElRadioGroup
                  v-model="ElRadioGroup_1.value"
                  v-bind="ElRadioGroup_1"
                  v-on="ElRadioGroup_1.event"
                >
                  <ElRadio
                    v-for="item in ElRadioList_1.options_in"
                    :key="item[ElRadioList_1.key]"
                    :label="item[ElRadioList_1.label]"
                    :disabled="item[ElRadioList_1.disabled]"
                  >
                    {{ item[ElRadioList_1.text] }}
                  </ElRadio>
                </ElRadioGroup>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="ElRadioGroup_1.value === 1">
              <el-form-item :label="$T('使用预案')" prop="eventPlanId">
                <ElSelect
                  v-model="CetForm_1.data.eventPlanId"
                  v-bind="ElSelect_scene"
                  v-on="ElSelect_scene.event"
                >
                  <ElOption
                    v-for="item in ElOption_scene.options_in"
                    :key="item[ElOption_scene.key]"
                    :label="item[ElOption_scene.label]"
                    :value="item[ElOption_scene.value]"
                    :disabled="item[ElOption_scene.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="ElRadioGroup_1.value === 2">
              <el-form-item :label="$T('名称')" prop="eventPlanName">
                <ElInput
                  v-model.trim="CetForm_1.data.eventPlanName"
                  onKeypress="javascript:if(event.keyCode == 32)event.returnValue = false;"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="ElRadioGroup_1.value === 2">
              <el-form-item :label="$T('操作步骤')" prop="solution">
                <el-input
                  type="textarea"
                  rows="3"
                  resize="none"
                  :placeholder="$T('请输入内容')"
                  v-model="CetForm_1.data.solution"
                  onKeypress="javascript:if(event.keyCode == 32)event.returnValue = false;"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$T('上传附件')" prop="">
                <el-upload
                  ref="upload"
                  class="avatar-uploader"
                  action="/eem-service/v1/common/uploadFile"
                  :headers="{ Authorization: this.token }"
                  :file-list="fileList"
                  :multiple="false"
                  :before-upload="beforeImgUpload"
                  :on-change="handleChange"
                  :on-remove="handleRemove"
                  :on-success="handleUploadSuccess"
                  :on-error="errorUpload"
                >
                  <el-button size="small" type="primary">
                    {{ $T("点击上传") }}
                  </el-button>
                  <div slot="tip" class="el-upload__tip">
                    {{ $T("支持图片、视频、Excel") }}
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </CetForm>
      </el-container>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_submit"
          v-on="CetButton_submit.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom.js";

export default {
  name: "createPlan",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    userInfo() {
      var vm = this;
      return vm.$store.state.userInfo;
    }
  },
  data(vm) {
    const validateForm = function () {
      const executetime = vm.CetForm_1.data.executetime;
      const finishtime = vm.CetForm_1.data.finishtime;
      const repairconfirmtime = vm.CetForm_1.data.repairconfirmtime;
      const inspectconfirmtime = vm.CetForm_1.data.inspectconfirmtime;
      if (vm.inputData_in.fillformtype !== 2) {
        if (executetime < finishtime) {
          vm.$refs.createPlanForm.$refs.cetForm.clearValidate("executetime");
        }
        if (finishtime > executetime) {
          vm.$refs.createPlanForm.$refs.cetForm.clearValidate("finishtime");
        }
      }
      if (executetime < finishtime) {
        vm.$refs.createPlanForm.$refs.cetForm.clearValidate("executetime");
      }
      if (finishtime > executetime) {
        vm.$refs.createPlanForm.$refs.cetForm.clearValidate("finishtime");
      }
      if (repairconfirmtime > finishtime) {
        vm.$refs.createPlanForm.$refs.cetForm.clearValidate(
          "repairconfirmtime"
        );
      }
      if (inspectconfirmtime > repairconfirmtime) {
        vm.$refs.createPlanForm.$refs.cetForm.clearValidate(
          "inspectconfirmtime"
        );
      }
    };
    return {
      CetDialog_add: {
        title: $T("录入工单"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_add_open_out,
          close_out: this.CetDialog_add_close_out
        },
        showClose: true
      },
      CetButton_submit: {
        visible_in: true,
        disable_in: false,
        title: $T("提交"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_submit_statusTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "130px",
        labelPosition: "top",
        rules: {
          executor: [
            {
              required: true,
              message: $T("请选择执行人"),
              trigger: ["blur", "change"]
            }
          ],
          inspectionschemeid: [
            {
              required: true,
              message: $T("请选择巡检方案"),
              trigger: ["blur", "change"]
            }
          ],
          signgroupid: [
            {
              required: true,
              message: $T("请选择巡检路线"),
              trigger: ["blur", "change"]
            }
          ],
          handledescription: [
            {
              required: true,
              message: $T("请输入维修记录"),
              trigger: ["blur", "change"]
            }
          ],
          executetime: [
            {
              required: true,
              message: $T("请选择开始时间"),
              trigger: ["blur", "change"]
            },
            {
              required: true,
              trigger: "blur",
              validator: (rule, value, callback) => {
                const now = new Date().getTime();
                const finishtime = this.CetForm_1.data.finishtime;
                if (value > now) {
                  callback(new Error($T("开始时间不能大于当前时间")));
                  return;
                }
                if (!value || !finishtime) {
                  callback();
                }
                if (value >= finishtime) {
                  callback(new Error($T("开始时间不能大于完成时间")));
                  return;
                }
                validateForm();
                callback();
              }
            }
          ],
          finishtime: [
            {
              required: true,
              message: $T("请选择完成时间"),
              trigger: ["blur", "change"]
            },
            {
              required: true,
              trigger: "blur",
              validator: (rule, value, callback) => {
                const now = new Date().getTime();
                const executetime = this.CetForm_1.data.executetime;
                if (value > now) {
                  callback(new Error($T("完成时间不能大于当前时间")));
                  return;
                }
                if (!value || !executetime) {
                  callback();
                }
                if (value <= executetime) {
                  callback(new Error($T("完成时间不能小于开始时间")));
                  return;
                }
                validateForm();
                callback();
              }
            }
          ],
          repairconfirmtime: [
            {
              required: true,
              message: $T("请选择维修确认时间"),
              trigger: ["blur", "change"]
            },
            {
              required: true,
              trigger: "blur",
              validator: (rule, value, callback) => {
                const now = new Date().getTime();
                const finishtime = this.CetForm_1.data.finishtime;
                const inspectconfirmtime =
                  this.CetForm_1.data.inspectconfirmtime;
                if (!value || !inspectconfirmtime) {
                  callback();
                }
                if (value > now) {
                  callback(new Error($T("维修确认时间不能大于当前时间")));
                  return;
                }
                if (value <= finishtime) {
                  callback(new Error($T("维修确认时间不能小于完成时间")));
                  return;
                }
                validateForm();
                callback();
              }
            }
          ],
          inspectconfirmtime: [
            {
              required: true,
              message: $T("请选择运值确认时间"),
              trigger: ["blur", "change"]
            },
            {
              required: true,
              trigger: "blur",
              validator: (rule, value, callback) => {
                const now = new Date().getTime();
                const repairconfirmtime = this.CetForm_1.data.repairconfirmtime;
                if (!value || !repairconfirmtime) {
                  callback();
                }
                if (value > now) {
                  callback(new Error($T("运值确认时间不能大于当前时间")));
                  return;
                }
                if (value <= repairconfirmtime) {
                  callback(new Error($T("运值确认时间不能小于维修确认时间")));
                  return;
                }
                validateForm();
                callback();
              }
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_1_currentData_out,
          saveData_out: this.CetForm_1_saveData_out,
          finishData_out: this.CetForm_1_finishData_out,
          finishTrigger_out: this.CetForm_1_finishTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        style: {},
        placeholder: $T("请输入内容"),
        event: {}
      },
      // 天，设置范围0-29
      ElInputNumber_2: {
        min: 0,
        max: 29,
        step: 2,
        precision: 0,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      // 小时，设置范围0-23
      ElInputNumber_3: {
        min: 0,
        max: 23,
        step: 2,
        precision: 0,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInputNumber_4: {
        // ...common.check_numberInt,
        min: 1,
        max: 999999999,
        step: 2,
        precision: 0,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      pickerOptions: common.pickerOptions_laterThanYesterd,
      pickerOptions11: common.pickerOptions_laterThanYesterd11,
      //预案列表
      ElSelect_scene: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_scene: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // executor组件
      ElSelect_executor: {
        value: "",
        filterable: true,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_executor_change_out
        }
      },
      // executor组件
      ElOption_executor: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElRadioGroup_1: {
        value: 1,
        style: {},
        event: {
          change: this.ElRadioGroup_1_change_out
        }
      },
      ElRadioList_1: {
        options_in: [
          {
            id: 1,
            text: $T("选择现有预案")
          },
          {
            id: 2,
            text: $T("手动输入")
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      //时间控件
      CetDatePicker_1: {
        disable_in: false,
        val: this.$moment().add(1, "day").startOf("day").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "datetime",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          style: {
            display: "block"
          },
          pickerOptions: common.pickerOptions_earlierThanYesterd11
        }
      },
      listData: [], // 备件列表
      copySparepartsList: [], //备件列表备份
      // 备件组件
      ElSelect_2: {
        value: "",
        style: {
          width: "200px"
        },
        "value-key": "id",
        event: {
          change: this.ElSelect_2_change_out,
          "visible-change": this.ElSelect_2_visibleChange_out
        }
      },
      ElOption_2: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // 备件数量组件
      ElInputNumber_1: {
        value: "",
        style: {
          width: "100%"
        },
        controls: false,
        min: 0,
        event: {
          change: this.ElInputNumber_1_change_out
        }
      },
      // add组件
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("添加备件"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      fileList: [], // 上传文件列表
      imageUrl: null,
      buttonStatus: "" // 是保存还是提交
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.CetForm_1.resetTrigger_in = this._.cloneDeep(val);

      Promise.all([
        new Promise((resolve, reject) => {
          this.queryTeamUser_out(resolve);
        }),
        new Promise((resolve, reject) => {
          this.getSparePartsStorageByDevice_out(resolve);
        }),
        new Promise((resolve, reject) => {
          this.queryExpertEventPlan_out(resolve);
        })
      ]).then(res => {
        this.CetDialog_add.openTrigger_in = this._.cloneDeep(val);
        this.$nextTick(() => {
          this.ElRadioGroup_1.value = 1;
          let maintenanceContent = {};
          const inputData = this._.cloneDeep(this.inputData_in);
          if (inputData && inputData.maintenancecontent) {
            maintenanceContent = JSON.parse(inputData.maintenancecontent);
          }
          const users = maintenanceContent.users || [];
          const executor = users.map(item => {
            return item.userId;
          });
          const eventPlanId = this._.get(
            maintenanceContent,
            "eventPlan.eventPlanId",
            null
          );
          const formData = {
            code: inputData.code,
            executor: executor,
            executetime: inputData.executetime,
            finishtime: inputData.finishtime,
            inspectconfirmtime: inputData.inspectconfirmtime,
            repairconfirmtime: inputData.repairconfirmtime,
            fillformtype: inputData.fillformtype,
            handledescription: inputData.handledescription,
            eventPlanId: eventPlanId
          };
          const eventPlan = maintenanceContent.eventPlan || {};
          if (eventPlan.eventPlanName && !eventPlan.eventPlanId) {
            this.ElRadioGroup_1.value = 2;
            formData.eventPlanName = eventPlan.eventPlanName || null;
            formData.solution = eventPlan.solution || null;
          }

          this.CetForm_1.data = this._.cloneDeep(formData);
          const listData =
            this._.get(maintenanceContent, "sparePartsReplaceRecords", []) ||
            [];
          listData.forEach(item => {
            if (item.sparepartsstorageid) {
              item.id = item.sparepartsstorageid;
            }
          });
          this.listData = listData;
          //如果获取备件列表为空，则清除之前保存备件数据
          if (this.copySparepartsList && this.copySparepartsList.length === 0) {
            this.listData = [];
          }
          this.fileList =
            this._.get(inputData, "maintenanceContentObj.attachments", []) ||
            [];
          this.$nextTick(() => {
            this.$refs.createPlanForm.$refs.cetForm.clearValidate();
          });
        });
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    }
  },

  methods: {
    CetDialog_add_open_out(val) {},
    CetDialog_add_close_out(val) {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_submit_statusTrigger_out(val) {
      this.buttonStatus = $T("提交");
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.buttonStatus = $T("保存");
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_currentData_out(val) {},
    CetForm_1_saveData_out(val) {
      // this.$emit("confirm_out", this._.cloneDeep(val));
      // 附件信息
      let attachment = this.fileList.map(item => {
        return {
          name: item.name,
          url: this._.get(item, "response.data", "") || item.url
        };
      });
      attachment = attachment.filter(item => {
        return item.url;
      });
      //获取执行人
      const users = this.ElOption_executor.options_in.filter(item => {
        var executors = this.CetForm_1.data.executor || [];
        return Array.isArray(executors) && executors.includes(item.id);
      });

      const user = users.map(item => {
        return {
          userId: item.id,
          userName: item.name
        };
      });
      //获取预案方式
      let scene = {};
      if (this.ElRadioGroup_1.value === 1) {
        const scenesList = this.ElOption_scene.options_in || [];
        const scenes =
          scenesList.filter(item => {
            return item.id === this.CetForm_1.data.eventPlanId;
          }) || [];
        scene = scenes.map(item => {
          return {
            eventPlanId: item.id,
            eventPlanName: item.name,
            projectId: item.projectid,
            solution: item.solution
          };
        })[0];
      } else if (this.ElRadioGroup_1.value === 2) {
        scene = {
          eventPlanId: null,
          eventPlanName: this.CetForm_1.data.eventPlanName,
          projectId: this.projectId,
          solution: this.CetForm_1.data.solution
        };
      }
      //处理备件列表信息
      let isOk = true;
      const list = this.listData || [];
      list.forEach(item => {
        if (!item.id || !item.number) {
          isOk = false;
        }
      });

      if (!isOk) {
        this.$message.warning($T("添加备件没有选择备件或输入值"));
        return;
      }
      const params = {
        taskResult: this.inputData_in.fillformtype,
        // attachmentList: attachment,
        code: this.inputData_in.code,
        executetime: this.CetForm_1.data.executetime,
        finishtime: this.CetForm_1.data.finishtime,
        formData: {
          inspectconfirmtime: this.CetForm_1.data.inspectconfirmtime,
          repairconfirmtime: this.CetForm_1.data.repairconfirmtime,
          fillformtype: this.CetForm_1.data.fillformtype
        },
        handledescription: this.CetForm_1.data.handledescription,
        maintenanceContent: {
          eventPlan: scene,
          inspectParams: [],
          attachments: attachment,
          sparePartsReplaceRecords: [],
          users: user
        }
      };
      //处理备件信息
      const listData = this._.cloneDeep(this.listData || []);
      params.maintenanceContent.sparePartsReplaceRecords = listData.map(
        item => {
          return {
            logtime: new Date().getTime(),
            name: item.name,
            number: item.number,
            objectid: this._.get(
              this.inputData_in,
              "deviceplanrelationship_model[0].device_id",
              null
            ),
            objectlabel: this._.get(
              this.inputData_in,
              "deviceplanrelationship_model[0].device_label",
              null
            ),
            sparepartsstorageid: item.id,
            unit: item.unit,
            workorderid: this.inputData_in.id
          };
        }
      );

      // 保存工单信息
      if (this.buttonStatus === $T("保存")) {
        customApi.queryRepairWorkOrderUpdate(params).then(res => {
          if (res.code === 0) {
            this.$message.success($T("保存成功!"));
            this.CetDialog_add.closeTrigger_in = new Date().getTime();
            this.$emit("confirm_out", true);
          }
        });
      } else if (this.buttonStatus === $T("提交")) {
        // 提交工单信息
        customApi.queryRepairWorkOrderSubmit(params).then(res => {
          if (res.code === 0) {
            this.$message.success($T("提交成功!"));
            this.CetDialog_add.closeTrigger_in = new Date().getTime();
            this.$emit("confirm_out", true);
          }
        });
      }
    },
    CetForm_1_finishData_out(val) {},
    CetForm_1_finishTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
      this.$emit("confirm_out", val);
    },
    beforeImgUpload() {},
    handleChange(file, fileList) {
      // this.fileList = this._.cloneDeep(fileList);
    },
    handleRemove(file, fileList) {
      this.fileList = this._.cloneDeep(fileList);
    },
    handleUploadSuccess(response, file, fileList) {
      if (response.code === 0) {
        this.$message.success($T("上传成功"));
        this.fileList = this._.cloneDeep(fileList);
      } else if (response.code !== 0) {
        // 根据uid清除掉上传失败的文件显示
        const uid = file.uid;
        const idx = this.$refs.upload.uploadFiles.findIndex(
          item => item.uid === uid
        );
        this.$refs.upload.uploadFiles.splice(idx, 1);
        var tips = "";

        if (response.data) {
          for (var i = 0; i < response.data.length; i++) {
            tips = tips + response.data[i] + "<br/>";
          }
        } else {
          tips = response.msg;
        }
        this.$message({
          type: "error",
          message: tips
        });
      }
    },
    errorUpload() {},
    // 备件输出,方法名要带_out后缀
    ElSelect_2_change_out(val) {
      const id = this._.get(val, "id", null);
      const hasArr = this.listData.filter(item => {
        return item.id === id;
      });
      if (hasArr && hasArr.length > 1) {
        this.$message.warning($T("请选择不同备件"));
      }
    },
    //备件下拉框出现/隐藏时触发
    ElSelect_2_visibleChange_out(val) {
      if (val) {
        const sparepartsList = this.copySparepartsList.filter(item => {
          const idArr =
            this.listData.map(item => {
              return item.id;
            }) || [];
          return !idArr.includes(item.id);
        });
        this.ElOption_2.options_in = this._.cloneDeep(sparepartsList);
      } else {
        this.ElOption_2.options_in = this._.cloneDeep(this.copySparepartsList);
      }
    },
    ElInputNumber_1_change_out(val) {},
    // add输出
    CetButton_add_statusTrigger_out(val) {
      if (!this.ElOption_2.options_in.length) {
        return this.$message.warning($T("无备件!"));
      }
      const len = this.listData.length;
      if (len >= this.copySparepartsList.length) {
        this.$message.warning($T("消耗备件已超出数量!"));
        return;
      }
      this.listData.push({});
    },
    handleDeleteSparepart(index) {
      this.listData.splice(index, 1);
    },
    ElSelect_executor_change_out(val) {},
    ElRadioGroup_1_change_out(val) {},

    init() {},
    // 查班组下的人员
    queryTeamUser_out(callback) {
      if (!this.inputData_in.teamid) {
        callback && callback(false);
        return;
      }
      // 查班组下的人员
      customApi.queryTeamUser(this.inputData_in.teamid).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          this.ElOption_executor.options_in = this._.cloneDeep(data);
          callback && callback();
        }
      });
    },
    // 查维保目标下的备件库
    getSparePartsStorageByDevice_out(callback) {
      const _this = this;
      const device = this._.get(
        this.inputData_in,
        "deviceplanrelationship_model[0]",
        {}
      );
      const params = {
        id: device.device_id,
        modelLabel: device.device_label
      };
      // let params = {
      //   id: 25,
      //   modelLabel: "room"
      // };
      customApi.getSparePartsStorageByDevice(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          _this.copySparepartsList = this._.cloneDeep(data);
          _this.ElOption_2.options_in = this._.cloneDeep(data);
          callback && callback();
        }
      });
    },
    // 获取预案列表信息
    queryExpertEventPlan_out(callback) {
      const _this = this;
      const params = {
        scenariosId: this.inputData_in.faultscenariosid
      };
      customApi.queryExpertEventPlan(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          _this.ElOption_scene.options_in = _this._.cloneDeep(data);
          callback && callback();
        }
      });
    },
    //过滤备件单位字符宽度，设置最大只显示三个字符
    filUnit(val) {
      if ([null, undefined, NaN].includes(val)) {
        return "";
      } else {
        return String(val).slice(0, 3);
      }
    }
  },

  created: function () {}
};
</script>

<style lang="scss" scoped>
.countBox {
  position: relative;
  width: 140px;
  margin-right: 10px;
  :deep(.el-input__inner) {
    padding-right: 35px;
  }
  .count {
    line-height: 30px;
    position: absolute;
    top: 1px;
    right: 1px;
    @include padding(0 J1);
    @include background_color(BG);
    border-radius: 0 mh-get(C) mh-get(C) 0;
  }
}
.deleteIcon {
  @include font_color(Sta3);
}
</style>
