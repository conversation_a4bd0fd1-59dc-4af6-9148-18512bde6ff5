<template>
  <div class="page">
    <div style="height: 100%; width: 100%">
      <el-table
        ref="PQEventTable"
        :data="eventTableData"
        tooltip-effect="light"
        border
        stripe
        highlight-current-row
        height="true"
        style="height: 100%; width: 100%"
        row-key="tree_id"
        :default-expand-all="false"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        @current-change="handlerCurrentChange"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="40"
          reserve-selection
          :selectable="canConfirm"
          header-align="left"
          align="left"
        ></el-table-column>
        <el-table-column
          label="#"
          type="index"
          width="64"
          :index="table_index"
          header-align="left"
          align="left"
        ></el-table-column>

        <!-- 根据模型数据绑定表格列 -->
        <template v-for="item in renderColumns">
          <template v-if="item.formatterType">
            <el-table-column
              :key="item.key"
              :show-overflow-tooltip="true"
              :label="item.title"
              :min-width="item.width"
              :width="item.fixedwidth"
              :prop="item.key"
              :sortable="item.sortable"
              header-align="left"
              align="left"
            >
              <template v-if="item.formatterType" slot-scope="scope">
                <el-tag
                  effect="plain"
                  size="small"
                  disable-transitions
                  :color="item.formatterType(scope.row).bgColor"
                  :style="`color: ${item.formatterType(scope.row).color}`"
                >
                  {{
                    formatTable(scope.row, scope.column, scope.row[item.key])
                  }}
                </el-tag>
              </template>
            </el-table-column>
          </template>
          <template v-else-if="item.type">
            <el-table-column
              :key="item.key"
              :show-overflow-tooltip="true"
              :label="item.title"
              :min-width="item.width"
              :width="item.fixedwidth"
              :prop="item.key"
              :sortable="item.sortable"
              header-align="left"
              align="left"
            >
              <template v-if="item.type" slot-scope="scope">
                <span v-if="item.type == 'date'">
                  {{ formatDate(scope.row, scope.column, scope.row[item.key]) }}
                </span>
                <span v-else>
                  {{
                    formatTable(scope.row, scope.column, scope.row[item.key])
                  }}
                </span>
              </template>
            </el-table-column>
          </template>
          <template v-else>
            <el-table-column
              :key="item.key"
              :show-overflow-tooltip="true"
              :label="item.title"
              :min-width="item.width"
              :width="item.fixedwidth"
              :prop="item.key"
              :sortable="item.sortable"
              :formatter="formatTable"
              header-align="left"
              align="left"
            ></el-table-column>
          </template>
        </template>
      </el-table>
    </div>
  </div>
</template>
<script>
import common from "eem-utils/common";
import { httping } from "@omega/http";
import { getEventGradeColor } from "eem-utils/eventColor.js";
export default {
  name: "EnergyConsumptionEventTable",
  components: {},

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    enumerations() {
      var vm = this;
      return vm.$store.state.enumerations;
    },
    lastPageText() {
      return this.isLastPage ? $T("已加载完毕") : $T("点击加载更多");
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    }
  },
  props: {
    //导出操作触发
    exportTrigger_in: {
      type: Number
    },
    //查询节点输入
    queryNode_in: {
      type: Object
    },
    clickNode_in: {
      type: Object
    },
    convergence_in: {
      type: Boolean
    },
    confirmStatus_in: {
      type: Boolean
    },
    refreshTrigger_in: {
      type: Number
    },
    searchKeyWords_in: {
      type: [String]
    },
    updateEventData_in: {
      type: Array,
      default: () => []
    },
    page_in: {
      type: Object
    },
    levels_in: {
      type: Array
    },
    date_in: {
      type: Object
    }
  },

  data() {
    return {
      eventTableData: [],
      queryData: {
        checkbox: [1, 2, 3], //收敛事件
        children: [
          {
            modelLabel: "",
            nodeId: 0,
            name: ""
          }
        ], //查询收敛事件
        cycle: 20, //年17 月14 日12 小时7
        description: "",
        endtime: this.$moment().endOf("M").valueOf() + 1,
        id: 0,
        name: "",
        index: 0,
        limit: 20,
        modelLabel: "project",
        starttime: this.$moment().startOf("M").valueOf(),
        status: 1, //事件状态
        types: [704], //事件类型
        levels: [] //事件等级
      },
      pageSize: 50,
      loading: false,
      isLastPage: false,
      tableData: [],
      renderColumns: [
        {
          title: $T("发生时间"),
          key: "eventtime",
          // formatStr: "YYYY-MM-DD HH:mm:ss.SSS",
          formatStr: "YYYY-MM-DD HH:mm:ss",
          type: "date",
          width: 200,
          sortable: true
        },
        {
          title: $T("对象"),
          key: "name",
          width: 100
        },
        {
          title: $T("描述"),
          key: "description",
          width: 200
        },
        {
          title: $T("结束时间"),
          key: "endTime",
          // formatStr: "YYYY-MM-DD HH:mm:ss.SSS",
          formatStr: "YYYY-MM-DD HH:mm:ss",
          type: "date",
          width: 200
        },
        {
          title: $T("持续时长"),
          key: "durationTime",
          width: 100
        },
        {
          title: $T("事件等级"),
          key: "level$text",
          width: 100,
          formatterType: this.isLevel
        }
      ],
      isConvergence: false,
      eventtypeList: [],
      energytypeList: [],
      isLoading: false,
      isClickNode: false
    };
  },
  watch: {
    exportTrigger_in(val) {
      this.exportTable_out();
    },
    queryNode_in: {
      deep: true,
      handler: function (val) {
        this.paramsChange_out(val);
      }
    },
    clickNode_in: {
      deep: true,
      handler: function (val) {
        this.isLoading = false;
        this.isClickNode = true;
        this.clickNodeChange_out(val);
      }
    },
    page_in: {
      deep: true,
      handler: function (val) {
        if (this.isClickNode) {
          return;
        }
        this.clickPageChange_out(val);
      }
    },
    confirmStatus_in(val) {
      if (!val) {
        this.queryData.status = 3;
      } else {
        this.queryData.status = 1;
      }

      this.queryEvent_out();
    },
    convergence_in(val) {
      if (!val) {
        this.isConvergence = false;
      } else {
        this.isConvergence = true;
      }
      this.queryEvent_out();
    },
    refreshTrigger_in(val) {
      this.queryEvent_out();
    },
    searchKeyWords_in(val) {
      this.queryData.description = val;
    },
    updateEventData_in(list) {
      if (!(list && list.length)) {
        return;
      }

      const me = this;
      for (const eventItem of list) {
        const i = me._.findIndex(me.eventTableData, function (o) {
          return o.id === eventItem.id;
        });

        if (i !== -1) {
          me.eventTableData[i].remark = eventItem.remark;
          me.eventTableData[i].confirmeventstatus =
            eventItem.confirmeventstatus;
          me.eventTableData[i].operator = eventItem.operator;
          me.eventTableData[i].updatetime = eventItem.updatetime;
          me.$refs.PQEventTable.toggleRowSelection(me.eventTableData[i], false);
        }
      }
      me.eventTableData.splice(0, 0);
    },
    levels_in: {
      deep: true,
      handler: function (val) {
        if (val.length < 1) {
          this.$message({
            message: $T("至少选择一种告警等级"),
            type: "warning"
          });
          return;
        }
        this.queryData.levels = val;
        this.isClickNode = true;
        this.queryEvent_out();
      }
    },
    date_in: {
      deep: true,
      handler: function (val) {
        this.queryData.endtime = this.$moment(val.endtime).valueOf();
        this.queryData.starttime = this.$moment(val.starttime).valueOf();
        this.isClickNode = true;
        this.queryEvent_out();
      },
      immediate: true
    }
  },

  methods: {
    //占位函数，无实际用处
    nothing() {},
    table_index(index) {
      return (this.page_in.currentPage - 1) * this.page_in.pageSize + index + 1;
    },
    handleSelectionChange(val) {
      var vm = this;
      vm.$emit("selectionChange_out", val);
    },
    canConfirm(row, column) {
      const confirmeventstatus = row.confirmeventstatus;
      return !(confirmeventstatus && confirmeventstatus === 3);
    },
    getEventStatus(cellValue) {
      if (cellValue && cellValue === 3) {
        return "confirm";
      } else {
        return "unconfirm";
      }
    },
    hasWave_out(wavetime) {
      return !!(wavetime && wavetime > 0);
    },
    handleConfirm(row, value) {
      if (value === 3) {
        // 已确认事件不用再确认
        return;
      }
      this.$emit("confirmTrigger_out", [row]);
    },
    handleWave_out(index, row) {
      //详情弹窗触发信号
      var vm = this;
      vm.$refs.PQEventTable.setCurrentRow(row);
      vm.$emit("waveview_out", new Date().getTime());
      vm.$emit("record_out", row);
    },
    loadEnumrations_out(callback) {
      const _this = this;
      var queryOption = {
        url: `/eem-service/v1/common/enumerations/eventtype`,
        method: "GET"
      };

      httping(queryOption).then(function (response) {
        if (response.code === 0) {
          var data = _this._.get(response, ["data"], []);
          data = data.filter(item => {
            return item.id > 700;
          });
          _this.eventtypeList = data;
          // 事件类型默认全选
          _this.queryData.types = data.map(item => {
            return item.id;
          });
          _this.queryData.levels = [1, 2, 3, 4];
          callback && callback();
        }
      });

      var queryOption1 = {
        url: `/eem-service/v1/common/enumerations/energytype`,
        method: "GET"
      };

      httping(queryOption1).then(function (response) {
        if (response.code === 0) {
          // console.log(response.data);
          var data = _this._.get(response, ["data"], []);
          _this.energytypeList = data;
        }
      });
    },
    exportTable_out() {
      common.downExcel(
        "/eem-service/v1/alarmEvent/energydata",
        this.queryData,
        this.token,
        this.projectId
      );
    },
    queryEvent_out(more) {
      var me = this;
      var params;

      if (!more) {
        me.isLastPage = false;
      }

      if (me.isLoading) {
        return;
      }
      me.isLoading = true;
      params = me._.cloneDeep(me.queryData);
      var ids = [];
      if (params.children && params.children.length) {
        params.children.forEach(item => {
          if (item.modelLabel === "demandaccount") {
            ids.push(item.id);
          }
        });
      }
      params.id = ids;
      params.modelLabel = "demandaccount";
      if (ids.length === 0) {
        me.isLoading = false;
        me.$emit("labelCount_out", {
          firstlevel: 0,
          secondlevel: 0,
          thirdlevel: 0
        });
        me.eventTableData = [];
        me.$emit("totalCount_out", 0);
        return;
      }
      var queryOption = {
        url: `/eem-service/v1/alarmEvent/getDemandEvents`,
        method: "POST",
        data: params
      };

      httping(queryOption).then(
        function (response) {
          me.isLoading = false;
          if (response.code === 0) {
            var data = me._.get(response, ["data"], []);
            // 统计等级
            var firstlevel = 0;
            var secondlevel = 0;
            var thirdlevel = 0;
            data.forEach(item => {
              if (item.level === 1) {
                firstlevel++;
              } else if (item.level === 2) {
                secondlevel++;
              } else if (item.level === 3) {
                thirdlevel++;
              }
            });
            me.$emit("labelCount_out", {
              firstlevel,
              secondlevel,
              thirdlevel
            });
            // 分页处理
            // data = data.splice((me.page_in.currentPage - 1) * me.page_in.pageSize, me.page_in.pageSize);

            data.forEach(function (item, index) {
              item.confirmeventstatus$text =
                item.confirmeventstatus === 1 ? $T("待处理") : $T("已处理");
              item.eventtype$text = me.filEventType(item.eventtype);
              item.level$text = me.filLevel(item.level);
              item.energyType$text = me.filEnergyType(item.energytype);
              item.tree_id = item.modelLabel + "_" + item.id;
              var duration = item.duration;
              var hour =
                duration < 1000 ? 0 : parseInt(duration / 1000 / 60 / 60);
              var minu =
                duration < 1000 ? 0 : parseInt((duration / 1000 / 60) % 60);
              if (!duration || [NaN, null, undefined].includes(hour)) {
                hour = "--";
              }
              if (!duration || [NaN, null, undefined].includes(minu)) {
                minu = "--";
              }
              item.durationTime = `${hour}${$T("小时")}${minu}${$T("分")}`;
              if (!me.isConvergence) {
                item.children = [];
                item.convergence = 0;
              } else {
                item.children = item.children || [];
                item.convergence = item.children.length;
                item.children.forEach(item11 => {
                  item11.tree_id = item11.modelLabel + index + "_" + item11.id;
                });
              }
            });

            me.eventTableData = data;
            var total = response.total || 0;
            me.$emit("totalCount_out", total);
            // 加载新数据时清除掉原来已选择的事件。
            me.$refs.PQEventTable.clearSelection();
            me.setTimeout = setTimeout(() => {
              me.isClickNode = false;
              if (me.setTimeout) {
                clearTimeout(me.setTimeout);
              }
            }, 1000);
          }
        },
        function () {
          me.isLoading = false;
        }
      );
      this.$emit("queryEvent_out");
      me.$refs.PQEventTable.setCurrentRow();
    },
    filTabelData(res) {},
    filEventType(type = 0) {
      var eventtypeList = this.eventtypeList || [];
      var fil = {};
      eventtypeList.forEach(item => {
        if (item.id === type) {
          fil = item;
        }
      });
      return fil.text || "--";
    },
    filLevel(level = 5) {
      if (level === 1) {
        return $T("一级");
      } else if (level === 2) {
        return $T("二级");
      } else if (level === 3) {
        return $T("三级");
      } else if (level === 4) {
        return $T("其他");
      } else {
        return "--";
      }
    },
    filEnergyType(type = 0) {
      var energytypeList = this.energytypeList || [];
      var fil = {};
      energytypeList.forEach(item => {
        if (item.id === type) {
          fil = item;
        }
      });
      return fil.text || "--";
    },
    paramsChange_out(val) {
      this.queryData.starttime = val.starttime;
      this.queryData.endtime = val.endtime;
      this.queryData.types = val.types;
      this.queryData.levels = val.levels;
      this.queryData.description = val.description;
      this.queryData.id = val.id;
      this.queryData.name = val.name;
      this.queryData.modelLabel = val.modelLabel;
      this.queryData.children = val.children;

      this.queryEvent_out();
    },
    clickNodeChange_out(val) {
      if (!val) {
        return;
      }
      this.queryData.id = val.id;
      this.queryData.name = val.name;
      this.queryData.modelLabel = val.modelLabel;
      if (val.modelLabel !== "demandaccount") {
        var children = val.children || [];
        var arr = [];
        children.forEach(item => {
          if (item.modelLabel === "demandgroup") {
            if (item.children && item.children.length > 0) {
              item.children.forEach(ite => {
                var obj = {
                  id: ite.id,
                  name: ite.name,
                  modelLabel: ite.modelLabel
                };
                arr.push(obj);
              });
            }
          } else {
            var obj = {
              id: item.id,
              name: item.name,
              modelLabel: item.modelLabel
            };
            arr.push(obj);
          }
        });
        this.queryData.children = arr;
      } else {
        this.queryData.children = [
          {
            id: val.id,
            name: val.name,
            modelLabel: val.modelLabel
          }
        ];
      }

      this.queryEvent_out();
    },
    clickPageChange_out(val) {
      if (!val) {
        return;
      }
      /* this.queryData.index = 0;
      this.queryData.limit = 999999999; */
      this.queryData.index = (val.currentPage - 1) * val.pageSize;
      this.queryData.limit = val.pageSize;
      this.queryEvent_out();
    },
    formatEnumerations(row, column, cellValue, index) {
      const vm = this;
      let property = vm._.clone(column.property);

      if (property === "eventType") {
        property = "peceventtype";
      } else if (property === "eventClass") {
        property = "peceventlevel";
      } else if (property === "confirmeventstatus") {
        if (!cellValue) {
          return $T("未确认");
        }
      }
      const enm = vm.enumerations[property];

      if (cellValue) {
        const text = vm._.find(enm, ["id", cellValue]).text;
        if (text) return text;
      }
      return "--";
    },

    isLevel(row, column, cellValue, index) {
      return getEventGradeColor(row.level);
    },
    isState(row, column, cellValue, index) {
      if (row.confirmeventstatus === 3) {
        return "success";
      } else {
        return "danger";
      }
    },
    isAnalysis(row, column, cellValue, index) {
      return "row-analysis";
    },
    formatTable(row, column, cellValue, index) {
      if (!cellValue) {
        if (cellValue === 0 || cellValue === "") {
          return cellValue;
        } else {
          return "--";
        }
      }
      return cellValue;
    },
    //格式化日期列
    formatDate(row, column, cellValue, index, config) {
      //设置时间格式化字符串，如果配置有效则采用配置字符串，无效则采用默认格式化字符串
      if (cellValue) {
        // return this.$moment(cellValue).format("YYYY-MM-DD HH:mm:ss.SSS");
        return this.$moment(cellValue).format("YYYY-MM-DD HH:mm:ss");
      } else if (cellValue === "") {
        return "--";
      } else {
        return "--";
      }
    },
    //当前行变化
    handlerCurrentChange(currentRow) {
      this.$emit("handlerCurrentChange_out", currentRow);
    }
  },

  created: function () {
    if (this.clickNode_in) {
      this.queryData.id = this.clickNode_in.id;
      this.queryData.name = this.clickNode_in.name;
      this.queryData.modelLabel = this.clickNode_in.modelLabel;
      var children = this.clickNode_in.children || [];
      var arr = [];
      children.forEach(item => {
        var obj = {
          nodeId: item.id,
          name: item.name,
          modelLabel: item.modelLabel
        };
        arr.push(obj);
      });
      this.queryData.children = arr;
    }
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.wave-view-button {
  cursor: pointer;
  text-decoration: underline;
  color: rgb(0, 102, 204);
}
.el-icon-more {
  cursor: pointer;
}
.confirm {
  color: #ccc;
}
.unconfirm {
  color: red;
  cursor: pointer;
}
.more {
  color: #0066cc;
}
</style>

<style lang="scss">
.row-detail {
  display: inline-block;
  width: 25px;
  height: 24px;
  cursor: pointer;
  background: url("./assets/details.png") no-repeat center center;
  border-radius: 50%;
}
.row-analysis {
  display: inline-block;
  width: 25px;
  height: 24px;
  cursor: pointer;
  background: url("./assets/u10740.png") no-repeat center #0350da;
  border-radius: 50%;
}
.icon-bottom-hidden {
  display: inline-block;
  width: 100px;
  height: 24px;
  cursor: pointer;
  background: url("./assets/u7706.png") no-repeat center center;
  position: absolute;
  left: calc(50% - 50px);
}
.el-tag.el-tag--level1 {
  border-color: red;
  color: red;
}
.el-tag.el-tag--level2 {
  border-color: #ff9900;
  color: #ff9900;
}
.el-tag.el-tag--level3 {
  border-color: #fbe100;
  color: #fbe100;
}
.el-tag.el-tag--level4 {
  border-color: #9da2a5;
  color: #fff;
}
</style>
