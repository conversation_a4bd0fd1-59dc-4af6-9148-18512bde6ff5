<template>
  <div class="page eem-common">
    <div class="fullfilled flex-column">
      <div class="projectInfo pJ2 bg1 brC3 flex-row">
        <UploadImg
          class="loadImg mrJ2"
          :static_in="true"
          :imgUrl.sync="projectInfo.pic"
        />
        <div class="flex-auto flex-column">
          <div class="common-title-H1">{{ projectInfo.name | formatStr }}</div>
          <div class="flex-auto flex-row mtJ1 projectInfoList">
            <div class="flex-auto flex-row">
              <div class="projectInfoItem">
                <div class="label text-ellipsis" title="项目编号">项目编号</div>
                <div class="value text-ellipsis" :title="projectInfo.code">
                  {{ projectInfo.code | formatStr }}
                </div>
              </div>
              <div class="projectInfoItem">
                <div class="label text-ellipsis" title="所属公司">所属公司</div>
                <div
                  class="value text-ellipsis"
                  :title="projectInfo.enterprisename"
                >
                  {{ projectInfo.enterprisename | formatStr }}
                </div>
              </div>
              <div class="projectInfoItem">
                <div class="label text-ellipsis" title="所属区域">所属区域</div>
                <div class="value text-ellipsis" :title="projectInfo.hierarchy">
                  {{ projectInfo.hierarchy | formatStr }}
                </div>
              </div>
            </div>
            <div class="rightBtn">
              <!-- <span class="rbtn" @click="CetButton_batchNode_statusTrigger_out">
                导入节点树
              </span>
              <span class="fcB1">|</span> -->
              <span
                class="rbtn"
                @click="CetButton_projectCfg_statusTrigger_out"
              >
                项目设置
              </span>
              <span class="fcB1">|</span>
              <span class="rbtn" @click="edditProject">编辑</span>
              <span class="fcB1">|</span>
              <span class="rbtn" @click="detailProject">详情</span>
            </div>
          </div>
        </div>
      </div>
      <el-container class="mtJ2 flex-auto">
        <el-aside class="eem-aside bg1 brC3 pt0" width="315px">
          <el-container style="height: 100%" class="flex-column">
            <div class="common-title-H1 mtJ2 mbJ2">项目层级</div>
            <CetGiantTree
              ref="cetGiantTree"
              class="bg1 flex-auto mbJ2"
              v-bind="CetGiantTree_1"
              v-on="CetGiantTree_1.event"
            ></CetGiantTree>
            <div class="treeFoot">
              <CetButton
                class="fl mrJ1"
                v-bind="CetButton_7"
                v-on="CetButton_7.event"
              ></CetButton>
              <CetButton
                class="fl mrJ1"
                v-bind="CetButton_6"
                v-on="CetButton_6.event"
              ></CetButton>
              <CetButton
                class="fl mrJ1"
                v-bind="CetButton_18"
                v-on="CetButton_18.event"
              ></CetButton>
              <CetButton
                class="fl mrJ1"
                v-bind="CetButton_19"
                v-on="CetButton_19.event"
              ></CetButton>
              <CetButton
                class="fl mrJ1"
                v-bind="CetButton_20"
                v-on="CetButton_20.event"
              ></CetButton>
              <CetButton
                class="fl mrJ1"
                v-bind="CetButton_17"
                v-on="CetButton_17.event"
              ></CetButton>
              <CetButton
                class="fl mrJ1"
                v-bind="CetButton_16"
                v-on="CetButton_16.event"
              ></CetButton>
              <CetButton
                class="fl mrJ1"
                v-bind="CetButton_10"
                v-on="CetButton_10.event"
              ></CetButton>
              <CetButton
                class="fl mrJ1"
                v-bind="CetButton_15"
                v-on="CetButton_15.event"
              ></CetButton>
              <CetButton
                class="fl mrJ1"
                v-bind="CetButton_14"
                v-on="CetButton_14.event"
              ></CetButton>
              <CetButton
                class="fl mrJ1"
                v-bind="CetButton_9"
                v-on="CetButton_9.event"
              ></CetButton>
            </div>
          </el-container>
        </el-aside>
        <el-container
          class="mlJ2 fullheight flex-column bg1 brC3 plJ2 prJ2 pbJ2"
        >
          <div height="32px" class="p0 lh32 mtJ2 mbJ2">
            <span class="common-title-H1">
              {{ currentNode && currentNode.name }}
            </span>
          </div>
          <div class="minWH" style="flex: 1; position: relative">
            <div class="clearfix mbJ2">
              <div class="fl clearfix">
                <ElInput
                  class="fl"
                  v-model="CetTable_1.dynamicInput.name"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
                <div class="basic-box mlJ1 fl" v-show="showTab">
                  <span class="basic-box-label">节点类型</span>
                  <ElSelect
                    v-model="selectedMenu"
                    v-bind="ElSelect_1"
                    @change="handleTabClick_out"
                  >
                    <ElOption
                      v-for="item in menus"
                      :key="item"
                      :label="item"
                      :value="item"
                    ></ElOption>
                  </ElSelect>
                </div>
              </div>
              <!-- <CetButton
                class="fr mrJ1"
                v-bind="CetButton_projectCfg"
                v-on="CetButton_projectCfg.event"
              ></CetButton> -->
              <!-- <CetButton
                class="fr mrJ1"
                v-bind="CetButton_11"
                v-on="CetButton_11.event"
              ></CetButton> -->
              <CetButton
                class="fr mrJ1"
                v-bind="CetButton_12"
                v-on="CetButton_12.event"
              ></CetButton>
              <CetButton
                class="fr mrJ1"
                v-bind="CetButton_13"
                v-on="CetButton_13.event"
              ></CetButton>
              <!-- <CetButton
                class="fr mrJ1"
                v-bind="CetButton_batchNode"
                v-on="CetButton_batchNode.event"
              ></CetButton> -->
              <CetButton
                class="fr mrJ1"
                v-bind="CetButton_5"
                v-on="CetButton_5.event"
              ></CetButton>
              <CetButton
                class="fr mrJ1"
                v-bind="CetButton_21"
                v-on="CetButton_21.event"
              ></CetButton>
              <CetButton
                class="fr mrJ1"
                v-bind="CetButton_8"
                v-on="CetButton_8.event"
              ></CetButton>
              <el-upload
                style="display: none"
                :action="importUrl"
                :headers="{
                  Authorization: this.token,
                  projectId: this.projectId
                }"
                :data="{}"
                :before-upload="handleBeforeUpload"
                :on-success="uploadSuccess"
                :on-error="uploadError"
                :multiple="false"
              >
                <button ref="uploadBtn"></button>
              </el-upload>
            </div>
            <div style="height: calc(100% - 48px)">
              <CetTable
                style="height: 100%; text-align: center"
                :data.sync="CetTable_1.data"
                :dynamicInput.sync="CetTable_1.dynamicInput"
                v-bind="CetTable_1"
                v-on="CetTable_1.event"
                @selection-change="handleSelectionChange"
              >
                <ElTableColumn
                  type="selection"
                  width="39"
                  headerAlign="left"
                  align="left"
                ></ElTableColumn>
                <ElTableColumn
                  label="#"
                  width="55"
                  type="index"
                  headerAlign="left"
                  align="left"
                ></ElTableColumn>
                <ElTableColumn
                  v-for="(item, index) in ElTableColumnArr"
                  :key="index"
                  v-bind="item"
                ></ElTableColumn>
                <ElTableColumn
                  label="详情"
                  width="94"
                  headerAlign="center"
                  align="center"
                >
                  <template slot-scope="scope">
                    <span
                      class="row-handel"
                      @click.stop="CetButton_3_statusTrigger_out(scope.row)"
                    >
                      编辑
                    </span>
                    <span class="fcB1">|</span>
                    <span
                      class="row-handel"
                      @click.stop="
                        CetTable_1_detailTrigger_out(scope.$index, scope.row)
                      "
                    >
                      详情
                    </span>
                  </template>
                </ElTableColumn>
              </CetTable>
            </div>
          </div>
        </el-container>
      </el-container>
    </div>
    <addProjectConfig1
      :visibleTrigger_in="addProjectConfig1.visibleTrigger_in"
      :closeTrigger_in="addProjectConfig1.closeTrigger_in"
      :queryId_in="addProjectConfig1.queryId_in"
      :inputData_in="addProjectConfig1.inputData_in"
      :treeData_in="fatherNode"
      :type_in="type_in"
      :treeNameList_in="treeNameList"
      @finishTrigger_out="addProjectConfig1_finishTrigger_out"
      @finishData_out="addProjectConfig1_finishData_out"
      @saveData_out="addProjectConfig1_saveData_out"
      @currentData_out="addProjectConfig1_currentData_out"
    />
    <addProjectConfig2
      :netWork="netWork"
      :visibleTrigger_in="addProjectConfig2.visibleTrigger_in"
      :closeTrigger_in="addProjectConfig2.closeTrigger_in"
      :queryId_in="addProjectConfig2.queryId_in"
      :inputData_in="addProjectConfig2.inputData_in"
      :treeData_in="fatherNode"
      :currentTabItme_in="currentTabItme"
      :type_in="type_in"
      :treeNameList_in="treeNameList"
      @finishTrigger_out="addProjectConfig2_finishTrigger_out"
      @finishData_out="addProjectConfig2_finishData_out"
      @saveData_out="addProjectConfig2_saveData_out"
      @currentData_out="addProjectConfig2_currentData_out"
    />
    <addProjectConfigBuilding
      :netWork="netWork"
      :visibleTrigger_in="addProjectConfigBuilding.visibleTrigger_in"
      :closeTrigger_in="addProjectConfigBuilding.closeTrigger_in"
      :queryId_in="addProjectConfigBuilding.queryId_in"
      :inputData_in="addProjectConfigBuilding.inputData_in"
      :treeData_in="fatherNode"
      :currentTabItme_in="currentTabItme"
      :type_in="type_in"
      :treeNameList_in="treeNameList"
      @saveData_out="addProjectConfigSectionarea_saveData_out"
    />
    <addProjectConfig3
      :visibleTrigger_in="addProjectConfig3.visibleTrigger_in"
      :closeTrigger_in="addProjectConfig3.closeTrigger_in"
      :queryId_in="addProjectConfig3.queryId_in"
      :inputData_in="addProjectConfig3.inputData_in"
      :treeData_in="fatherNode"
      :currentTabItme_in="currentTabItme"
      :type_in="type_in"
      :treeNameList_in="treeNameList"
      @finishTrigger_out="addProjectConfig3_finishTrigger_out"
      @finishData_out="addProjectConfig3_finishData_out"
      @saveData_out="addProjectConfig3_saveData_out"
      @currentData_out="addProjectConfig3_currentData_out"
    />
    <addProjectConfig4
      :visibleTrigger_in="addProjectConfig4.visibleTrigger_in"
      :closeTrigger_in="addProjectConfig4.closeTrigger_in"
      :queryId_in="addProjectConfig4.queryId_in"
      :inputData_in="addProjectConfig4.inputData_in"
      :treeData_in="fatherNode"
      :currentTabItme_in="currentTabItme"
      :type_in="type_in"
      :treeNameList_in="treeNameList"
      @finishTrigger_out="addProjectConfig4_finishTrigger_out"
      @finishData_out="addProjectConfig4_finishData_out"
      @saveData_out="addProjectConfig4_saveData_out"
      @currentData_out="addProjectConfig4_currentData_out"
    />
    <addProjectConfig5
      :visibleTrigger_in="addProjectConfig5.visibleTrigger_in"
      :closeTrigger_in="addProjectConfig5.closeTrigger_in"
      :queryId_in="addProjectConfig5.queryId_in"
      :inputData_in="addProjectConfig5.inputData_in"
      :treeData_in="fatherNode"
      :currentTabItme_in="currentTabItme"
      :type_in="type_in"
      :treeNameList_in="treeNameList"
      @finishTrigger_out="addProjectConfig5_finishTrigger_out"
      @finishData_out="addProjectConfig5_finishData_out"
      @saveData_out="addProjectConfig5_saveData_out"
      @currentData_out="addProjectConfig5_currentData_out"
    />
    <addProjectConfig6
      :visibleTrigger_in="addProjectConfig6.visibleTrigger_in"
      :closeTrigger_in="addProjectConfig6.closeTrigger_in"
      :queryId_in="addProjectConfig6.queryId_in"
      :inputData_in="addProjectConfig6.inputData_in"
      :treeData_in="fatherNode"
      :currentTabItme_in="currentTabItme"
      :type_in="type_in"
      :treeNameList_in="treeNameList"
      @finishTrigger_out="addProjectConfig6_finishTrigger_out"
      @finishData_out="addProjectConfig6_finishData_out"
      @saveData_out="addProjectConfig6_saveData_out"
      @currentData_out="addProjectConfig6_currentData_out"
    />
    <addProjectConfig7
      :visibleTrigger_in="addProjectConfig7.visibleTrigger_in"
      :closeTrigger_in="addProjectConfig7.closeTrigger_in"
      :queryId_in="addProjectConfig7.queryId_in"
      :inputData_in="addProjectConfig7.inputData_in"
      :treeData_in="fatherNode"
      :currentTabItme_in="currentTabItme"
      :type_in="type_in"
      :treeNameList_in="treeNameList"
      @finishTrigger_out="addProjectConfig7_finishTrigger_out"
      @finishData_out="addProjectConfig7_finishData_out"
      @saveData_out="addProjectConfig7_saveData_out"
      @currentData_out="addProjectConfig7_currentData_out"
    />
    <addProjectConfig8
      :visibleTrigger_in="addProjectConfig8.visibleTrigger_in"
      :closeTrigger_in="addProjectConfig8.closeTrigger_in"
      :queryId_in="addProjectConfig8.queryId_in"
      :inputData_in="addProjectConfig8.inputData_in"
      :treeData_in="fatherNode"
      :currentTabItme_in="currentTabItme"
      :type_in="type_in"
      :treeNameList_in="treeNameList"
      @finishTrigger_out="addProjectConfig8_finishTrigger_out"
      @finishData_out="addProjectConfig8_finishData_out"
      @saveData_out="addProjectConfig8_saveData_out"
      @currentData_out="addProjectConfig8_currentData_out"
    />
    <addProjectConfig9
      :visibleTrigger_in="addProjectConfig9.visibleTrigger_in"
      :closeTrigger_in="addProjectConfig9.closeTrigger_in"
      :queryId_in="addProjectConfig9.queryId_in"
      :inputData_in="addProjectConfig9.inputData_in"
      :treeData_in="fatherNode"
      :currentTabItme_in="currentTabItme"
      :type_in="type_in"
      :treeNameList_in="treeNameList"
      @finishTrigger_out="addProjectConfig9_finishTrigger_out"
      @finishData_out="addProjectConfig9_finishData_out"
      @saveData_out="addProjectConfig9_saveData_out"
      @currentData_out="addProjectConfig9_currentData_out"
    />
    <addProjectConfig10
      :visibleTrigger_in="addProjectConfig10.visibleTrigger_in"
      :closeTrigger_in="addProjectConfig10.closeTrigger_in"
      :queryId_in="addProjectConfig10.queryId_in"
      :inputData_in="addProjectConfig10.inputData_in"
      :treeData_in="fatherNode"
      :currentTabItme_in="currentTabItme"
      :type_in="type_in"
      :treeNameList_in="treeNameList"
      @finishTrigger_out="addProjectConfig10_finishTrigger_out"
      @finishData_out="addProjectConfig10_finishData_out"
      @saveData_out="addProjectConfig10_saveData_out"
      @currentData_out="addProjectConfig10_currentData_out"
    />
    <addProjectConfig11
      :visibleTrigger_in="addProjectConfig11.visibleTrigger_in"
      :closeTrigger_in="addProjectConfig11.closeTrigger_in"
      :queryId_in="addProjectConfig11.queryId_in"
      :inputData_in="addProjectConfig11.inputData_in"
      :treeData_in="fatherNode"
      :currentTabItme_in="currentTabItme"
      :type_in="type_in"
      :treeNameList_in="treeNameList"
      @finishTrigger_out="addProjectConfig11_finishTrigger_out"
      @finishData_out="addProjectConfig11_finishData_out"
      @saveData_out="addProjectConfig11_saveData_out"
      @currentData_out="addProjectConfig11_currentData_out"
    />
    <addPipeline
      :visibleTrigger_in="addPipeline.visibleTrigger_in"
      :closeTrigger_in="addPipeline.closeTrigger_in"
      :queryId_in="addPipeline.queryId_in"
      :inputData_in="addPipeline.inputData_in"
      @finishTrigger_out="addPipeline_finishTrigger_out"
      @finishData_out="addPipeline_finishData_out"
      @saveData_out="addPipeline_saveData_out"
      @currentData_out="addPipeline_currentData_out"
      :type_in="type_in"
      :treeNameList_in="treeNameList"
    />
    <DeviceTypeSelectionDialog
      :openTrigger_in="DeviceTypeSelectionDialog.openTrigger_in"
      :fatherNode_in="DeviceTypeSelectionDialog.fatherNode_in"
      @deviceType_out="DeviceTypeSelectionDialog_DeviceType_out"
    ></DeviceTypeSelectionDialog>
    <AddOrEditDeviceDialog
      :deviceMsg_in="AddOrEditDeviceDialog.deviceMsg_in"
      :isEditMode_in="AddOrEditDeviceDialog.isEditMode_in"
      :roomData_in="fatherNode"
      :deviceData_in="AddOrEditDeviceDialog.deviceData_in"
      :openTrigger_in="AddOrEditDeviceDialog.openTrigger_in"
      @deviceChanged_out="handleDeviceChanged"
    ></AddOrEditDeviceDialog>
    <ProjectConfigDetail
      :visibleTrigger_in="ProjectConfigDetail.visibleTrigger_in"
      :closeTrigger_in="ProjectConfigDetail.closeTrigger_in"
      :queryId_in="ProjectConfigDetail.queryId_in"
      :inputData_in="ProjectConfigDetail.inputData_in"
      @finishTrigger_out="ProjectConfigDetail_finishTrigger_out"
      @finishData_out="ProjectConfigDetail_finishData_out"
      @saveData_out="ProjectConfigDetail_saveData_out"
      @currentData_out="ProjectConfigDetail_currentData_out"
      :dataInfo_in="currentTabItme"
    />
    <ProjectCfg
      :visibleTrigger_in="projectCfg.visibleTrigger_in"
      :closeTrigger_in="projectCfg.closeTrigger_in"
      :queryId_in="projectCfg.queryId_in"
      :inputData_in="projectCfg.inputData_in"
    ></ProjectCfg>
    <UploadDialog
      v-bind="uploadDialog"
      v-on="uploadDialog.event"
    ></UploadDialog>
    <batchChangeNode
      :visibleTrigger_in="batchChangeNode.visibleTrigger_in"
      :closeTrigger_in="batchChangeNode.closeTrigger_in"
      :fatherNode_in="fatherNode"
      :selectedData_in="selectedData"
      @updata_out="updataOut"
    />
  </div>
</template>
<script>
import common from "eem-utils/common";
import addProjectConfig1 from "../cloudProjectConfig/addProjectConfig/addProjectConfig1.vue";
import addProjectConfig2 from "./addProjectConfig/addProjectConfig2.vue";
import addProjectConfig3 from "./addProjectConfig/addProjectConfig3.vue";
import addProjectConfig4 from "./addProjectConfig/addProjectConfig4.vue";
import addProjectConfig5 from "./addProjectConfig/addProjectConfig5.vue";
import addProjectConfig6 from "./addProjectConfig/addProjectConfig6.vue";
import addProjectConfig7 from "./addProjectConfig/addProjectConfig7.vue";
import addProjectConfig8 from "./addProjectConfig/addProjectConfig8.vue";
import addProjectConfig9 from "./addProjectConfig/addProjectConfig9.vue";
import addProjectConfig10 from "./addProjectConfig/addProjectConfig10.vue";
import addProjectConfig11 from "./addProjectConfig/addProjectConfig11.vue";
import addProjectConfigBuilding from "./addProjectConfig/addProjectConfigBuilding.vue";
import addPipeline from "./addProjectConfig/addPipeline.vue";
import ProjectConfigDetail from "../cloudProjectConfig/ProjectConfigDetail.vue";
import ProjectCfg from "../cloudProjectConfig/ProjectCfg/ProjectCfg";
import batchChangeNode from "./subcomponents/batchChangeNode";
import TREE_PARAMS from "@/store/treeParams.js";
import { FullScreenLoading } from "@omega/http/loading.js";
const loading = new FullScreenLoading();
import UploadImg from "eem-components/uploadImg.vue";
import customApi from "@/api/custom.js";
import UploadDialog from "eem-components/uploadDialog";
import DeviceTypeSelectionDialog from "./nodeChild/DeviceTypeSelectionDialog";
import AddOrEditDeviceDialog from "./nodeChild/AddOrEditDeviceDialog";
import ELECTRICAL_DEVICE_RELATION from "@/store/nodeRelation";
import ELECTRICAL_DEVICE_NODE from "@/store/electricaldevicenode.js";
import { httping } from "@omega/http";
export default {
  components: {
    addProjectConfig1,
    addProjectConfig2,
    addProjectConfig3,
    addProjectConfig4,
    addProjectConfig5,
    addProjectConfig6,
    addProjectConfig7,
    addProjectConfig8,
    addProjectConfig9,
    addProjectConfig10,
    addProjectConfig11,
    DeviceTypeSelectionDialog,
    AddOrEditDeviceDialog,
    addPipeline,
    ProjectConfigDetail,
    ProjectCfg,
    addProjectConfigBuilding,
    UploadImg,
    UploadDialog,
    batchChangeNode
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    systemCfg() {
      return this.$store.state.systemCfg;
    },
    userInfo() {
      return this.$store.state.userInfo;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    stateProjectInfo() {
      return this.$store.state.projectInfo;
    }
  },

  data() {
    return {
      netWork: false,
      projectInfo: {},
      ajaxFlag: true,
      currentNode: null,
      fatherNode: null,
      fatherCurrentNode: null,
      // 拷贝的节点信息
      copyNode: null,
      showTab: false,
      menus: ["车间/楼栋", "用能设备"],
      selectedMenu: "车间/楼栋",
      type_in: null,
      modelLabel: null,
      currentTabItme: {},
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        unCheckTrigger_in: new Date().getTime(),
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out //选中单行输出
        }
      },
      ElInput_1: {
        value: "",
        style: {
          width: "200px"
        },
        placeholder: "请输入内容",
        event: {
          change: this.ElInput_1_change_out,
          input: this.ElInput_1_input_out
        }
      },
      CetButton_1: {
        visible_in: false,
        disable_in: false,
        title: "新建项目",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_11: {
        visible_in: true,
        disable_in: false,
        title: "展示项目",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_11_statusTrigger_out
        }
      },
      CetButton_projectCfg: {
        visible_in: true,
        disable_in: false,
        title: "项目配置",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_projectCfg_statusTrigger_out
        }
      },
      CetButton_12: {
        visible_in: true,
        disable_in: false,
        title: "复制",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_12_statusTrigger_out
        }
      },
      CetButton_13: {
        visible_in: true,
        disable_in: false,
        title: "粘贴",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_13_statusTrigger_out
        }
      },
      CetButton_batchNode: {
        visible_in: true,
        disable_in: false,
        title: "批量导入管理对象",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_batchNode_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "cloudProjectConfigQueryTable",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "nodes", operator: "EQ", prop: "nodes" },
            { name: "name", operator: "LIKE", prop: "name" },
            { name: "selected", operator: "EQ", prop: "selected" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {
          pageSize: 20
        },
        style: {
          textAlign: "right"
        },
        exportFileName: "",
        tableKey: "tree_id",
        // defaultSort: null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      ElTableColumnArr: [],
      CetButton_3: {
        visible_in: true,
        disable_in: false,
        title: "修改",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      CetButton_5: {
        visible_in: true,
        disable_in: true,
        title: "批量删除",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_5_statusTrigger_out
        }
      },
      CetButton_6: {
        visible_in: false,
        disable_in: false,
        title: "添加同级",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_6_statusTrigger_out
        }
      },
      CetButton_7: {
        visible_in: false,
        disable_in: false,
        title: "添加子层级",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_7_statusTrigger_out
        }
      },
      CetButton_10: {
        visible_in: false,
        disable_in: false,
        title: "添加配电设备",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_10_statusTrigger_out
        }
      },
      CetButton_14: {
        visible_in: false,
        disable_in: false,
        title: "添加管道",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_14_statusTrigger_out
        }
      },
      CetButton_15: {
        visible_in: false,
        disable_in: false,
        title: "添加泵类",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_15_statusTrigger_out
        }
      },
      CetButton_16: {
        visible_in: false,
        disable_in: false,
        title: "添加空调机房设备",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_16_statusTrigger_out
        }
      },
      CetButton_17: {
        visible_in: false,
        disable_in: false,
        title: "添加空压机房设备",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_17_statusTrigger_out
        }
      },
      CetButton_18: {
        visible_in: false,
        disable_in: false,
        title: "添加锅炉房设备",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_18_statusTrigger_out
        }
      },
      CetButton_19: {
        visible_in: false,
        disable_in: false,
        title: "添加一段线",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_19_statusTrigger_out
        }
      },
      CetButton_20: {
        visible_in: false,
        disable_in: false,
        title: "添加IT机房设备",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_20_statusTrigger_out
        }
      },
      CetButton_8: {
        visible_in: false,
        disable_in: false,
        title: "关联设备",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_8_statusTrigger_out
        }
      },
      CetButton_9: {
        visible_in: false,
        disable_in: false,
        title: "添加用能设备",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_9_statusTrigger_out
        }
      },
      CetButton_21: {
        visible_in: true,
        disable_in: true,
        title: "批量移动",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_21_statusTrigger_out
        }
      },
      addProjectConfig1: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addProjectConfig2: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addProjectConfig3: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addProjectConfig4: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addProjectConfig5: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addProjectConfig6: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addProjectConfig7: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addProjectConfig8: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addProjectConfig9: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addProjectConfig10: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addProjectConfig11: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      ProjectConfigDetail: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addPipeline: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addProjectConfigBuilding: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      selectedData: [],
      treeNameList: [],
      isRefreshTable: false,
      projectCfg: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      importUrl: "",
      ElSelect_1: {
        value: "",
        style: {
          width: "200px"
        }
      },
      uploadDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        extensionNameList_in: [".xlsx", ".xls"],
        hideDownload: true,
        dialogTitle: "导入",
        event: {
          download: this.uploadDialog_download,
          uploadFile: this.uploadDialog_uploadFile
        }
      },
      // 设备类型选择弹窗组件
      DeviceTypeSelectionDialog: {
        openTrigger_in: new Date().getTime(),
        fatherNode_in: null
      },
      // 添加或编辑弹窗
      AddOrEditDeviceDialog: {
        deviceMsg_in: {
          modelLabel: null,
          name: ""
        },
        roomData_in: null,
        deviceData_in: null,
        isEditMode_in: false,
        openTrigger_in: new Date().getTime()
      },
      //批量转移节点弹框
      batchChangeNode: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      }
    };
  },
  watch: {
    currentNode: {
      handler: function (val, old) {
        // 判断当前节点是否是复制节点的父节点
        if (
          this.copyNode &&
          val.modelLabel === this.copyNode.fatherNodeModelLabel
        ) {
          this.CetButton_13.disable_in = false;
        } else if (
          this.copyNode &&
          val.modelLabel === "project" &&
          this.copyNode.fatherNodeModelLabel === "district"
        ) {
          this.CetButton_13.disable_in = false;
        } else {
          this.CetButton_13.disable_in = true;
        }
      },
      deep: true
    },
    copyNode: {
      handler: function (val, old) {
        // 判断当前节点是否是复制节点的父节点
        if (val && this.currentNode.modelLabel === val.fatherNodeModelLabel) {
          this.CetButton_13.disable_in = false;
        } else if (
          val &&
          this.currentNode.modelLabel === "project" &&
          val.fatherNodeModelLabel === "district"
        ) {
          this.CetButton_13.disable_in = false;
        } else {
          this.CetButton_13.disable_in = true;
        }
      },
      deep: true
    }
  },

  methods: {
    // 打开项目配置
    CetButton_projectCfg_statusTrigger_out() {
      this.projectCfg.visibleTrigger_in = new Date().getTime();
    },
    //新建项目
    CetButton_1_statusTrigger_out(val) {
      var _this = this,
        treeData = _this.CetGiantTree_1.inputData_in || [],
        treeNameList = [];
      treeData.forEach(item => {
        if (item.modelLabel === "project") {
          treeNameList.push(item.name);
        }
      });
      this.type_in = 1;
      this.treeNameList = treeNameList;
      this.addProjectConfig1.inputData_in = {};
      this.addProjectConfig1.visibleTrigger_in = this._.cloneDeep(val);
    },
    //展示项目列表
    CetButton_11_statusTrigger_out(val) {
      var _this = this;
      var treeData = _this.CetGiantTree_1.inputData_in || [];
      var nodes = [];
      var treeNameList = [];
      treeData.forEach(item => {
        if (item.modelLabel === "project") {
          var hasChildren = false;
          if (item.children && item.children.length > 0) {
            hasChildren = true;
          }
          var obj = {
            modelLabel: item.modelLabel,
            id: item.id,
            hasChildren: hasChildren
          };
          nodes.push(obj);
          treeNameList.push(item.name);
        }
      });
      this.CetTable_1.dynamicInput.selected = {
        id: this.currentNode.id,
        modelLabel: this.currentNode.modelLabel,
        name: this.currentNode.name
      };
      this.CetTable_1.dynamicInput.nodes = nodes;
      this.ElTableColumnArr = [
        {
          label: "节点名称",
          prop: "name",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (val) {
            if (val.name && val.name !== 0) {
              return val.name;
            } else {
              return "--";
            }
          }
        },
        {
          label: "所属区域",
          prop: "hierarchy",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (val) {
            if (val.hierarchy && val.hierarchy !== 0) {
              return val.hierarchy;
            } else {
              return "--";
            }
          }
        },
        {
          label: "项目编号",
          prop: "code",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (val) {
            if (val.code && val.code !== 0) {
              return val.code;
            } else {
              return "--";
            }
          }
        },
        // {
        //   label: "合作截止时间",
        //   prop: "cooperativedeadline",
        //   formatter: function (row, column) {
        //     return common.formatDate(row.cooperativedeadline, "YYYY-MM-DD");
        //   },
        //   headerAlign: "left",
        //   align: "left",
        //   showOverflowTooltip: true
        // },
        {
          label: "所属公司",
          prop: "enterprisename",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (val) {
            if (val.enterprisename && val.enterprisename !== 0) {
              return val.enterprisename;
            } else {
              return "--";
            }
          }
        }
      ];
      this.treeNameList = treeNameList;

      this.CetButton_6.visible_in = false;
      this.CetButton_7.visible_in = false;
      this.CetButton_8.visible_in = false;
      this.CetButton_9.visible_in = false;
      this.CetButton_10.visible_in = false;

      if (nodes && nodes.length === 0) {
        this.CetButton_3.disable_in = true;
        // this.CetButton_7.disable_in = true;
      } else {
        this.CetButton_3.disable_in = false;
        // this.CetButton_7.disable_in = false;
      }
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    // 复制节点
    CetButton_12_statusTrigger_out(val) {
      if (this.currentNode.modelLabel === "project") {
        new Promise((resolve, reject) => {
          this.getDistrictNode(this.currentNode, resolve);
        }).then(node => {
          this.copyNode = {
            fatherNodeModelLabel: "district",
            fatherNode: node,
            node: this.currentNode
          };
        });

        // this.getDistrictNode(this.currentNode,(node) => {
        //   this.copyNode = {
        //     fatherNodeModelLabel: "district",
        //     fatherNode: node,
        //     node: this.currentNode
        //   };
        // });
      } else {
        if (!this.fatherCurrentNode) {
          return;
        }
        this.copyNode = {
          fatherNodeModelLabel: this.fatherCurrentNode.modelLabel,
          node: this.currentNode
        };
      }
    },
    // 粘贴
    CetButton_13_statusTrigger_out(val) {
      // console.log(this.copyNode);
      var vm = this;
      var confirmMsg = `是否将【${vm.copyNode.node.name}】复制到【${vm.currentNode.name}】`;
      if (
        vm.copyNode &&
        vm.copyNode.fatherNode &&
        vm.currentNode.modelLabel === "project"
      ) {
        confirmMsg = `是否复制【${vm.copyNode.node.name}】`;
      }
      vm.$confirm(confirmMsg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: "btn-custom-cancel",
        type: "info",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: function (action, instance, done) {
          var data = {
            cmodelLabel: vm.copyNode.node.modelLabel,
            cmoldelId: vm.copyNode.node.id,
            pmodelId: vm.currentNode.id,
            pmodelLabel: vm.currentNode.modelLabel,
            system: "cloudsp"
            // version: 2
          };
          if (
            vm.copyNode &&
            vm.copyNode.fatherNode &&
            vm.currentNode.modelLabel === "project"
          ) {
            data = {
              cmodelLabel: vm.copyNode.node.modelLabel,
              cmoldelId: vm.copyNode.node.id,
              pmodelId: vm.copyNode.fatherNode.id || 0,
              pmodelLabel: vm.copyNode.fatherNode.modelLabel || "",
              system: "cloud"
              // version: 2
            };
          }
          if (action === "confirm") {
            httping({
              url: "/eem-service/v1/node/copyNodes",
              method: "POST",
              data
            }).then(function (response) {
              if (response.code === 0) {
                vm.$message.success("操作成功");
                vm.getTreeData();
              }
            });
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action !== "confirm") {
            vm.$message({
              type: "info",
              message: "已取消！"
            });
          }
        }
      });
    },
    //点击批量导出节点关系
    CetButton_batchNode_statusTrigger_out() {
      this.uploadDialog.openTrigger_in = new Date().getTime();
      // this.importUrl = `/eem-service/v1/project/batchManageNode?projectId=${this.projectId}`;
      // this.$refs.uploadBtn.click();
    },
    uploadDialog_download(val) {},
    uploadDialog_uploadFile(val) {
      const formData = new FormData();
      formData.append("file", val.file);
      var queryData = {
        projectId: this.projectId
      };
      customApi.projectBatchManageNode(formData, queryData).then(response => {
        if (response.code === 0) {
          this.$message({
            type: "success",
            message: "导入成功！"
          });
          this.getTreeData();
        }
      });
    },
    // 导入前
    handleBeforeUpload: function (file) {
      loading.showLoading();
      if (
        file.name.indexOf(".xls") !== -1 ||
        file.name.indexOf(".xlsx") !== -1
      ) {
        var uploadDocSize = this.systemCfg.uploadDocSize || 0.5;
        const isLimit100M = file.size / 1024 / 1024 < uploadDocSize;
        if (!isLimit100M) {
          this.$message.error(
            `上传文件超过规定的最大上传大小${uploadDocSize}M`
          );
        }
        return isLimit100M;
      } else {
        loading.hideLoading();
        this.$message({
          type: "warning",
          message: "只能上传xls/xlsx格式文件"
        });
        return false;
      }
    },
    // 导入成功
    uploadSuccess: function (response) {
      loading.hideLoading();
      if (response.code === 0) {
        this.$message({
          message: "导入成功",
          type: "success"
        });
        this.getTreeData();
      } else if (response.code !== 0) {
        this.$message({
          type: "error",
          message: response.msg
        });
      }
    },
    uploadError: function (response) {
      loading.hideLoading();
      this.$message({
        message: "导入失败",
        type: "error"
      });
    },
    edditProject() {
      this.type_in = 3;
      this.addProjectConfig1.inputData_in = this._.cloneDeep(this.projectInfo);
      this.addProjectConfig1.visibleTrigger_in = new Date().getTime();
    },
    //点击弹出项目详情弹框
    detailProject() {
      this.CetTable_1_detailTrigger_out(0, this.projectInfo);
    },
    //编辑节点
    CetButton_3_statusTrigger_out(val) {
      this.currentTabItme = val;
      if (!this.currentTabItme.modelLabel) {
        return;
      }

      var arr = [
        "project",
        "sectionarea",
        "building",
        "floor",
        "room",
        "arraycabinet",
        "powerdiscabinet"
      ];
      if (arr.indexOf(this.currentNode.modelLabel) === -1) {
        // 属于叶子节点
        this.fatherNode = this._.cloneDeep(this.fatherCurrentNode);
      } else {
        this.fatherNode = this._.cloneDeep(this.currentNode);
      }
      this.type_in = 3;

      if (this.currentTabItme.modelLabel === "project") {
        this.addProjectConfig1.inputData_in = this._.cloneDeep(
          this.currentTabItme
        );
        this.addProjectConfig1.visibleTrigger_in = new Date().getTime();
      } else if (this.currentTabItme.modelLabel === "building") {
        this.addProjectConfigBuilding.inputData_in = this._.cloneDeep(
          this.currentTabItme
        );
        this.addProjectConfigBuilding.visibleTrigger_in = new Date().getTime();
      } else if (
        this.currentTabItme.modelLabel === "sectionarea" ||
        this.currentTabItme.modelLabel === "civicpipe"
      ) {
        this.addProjectConfig2.inputData_in = this._.cloneDeep(
          this.currentTabItme
        );
        this.addProjectConfig2.visibleTrigger_in = new Date().getTime();
      } else if (this.currentTabItme.modelLabel === "floor") {
        this.addProjectConfig3.inputData_in = this._.cloneDeep(
          this.currentTabItme
        );
        this.addProjectConfig3.visibleTrigger_in = new Date().getTime();
      } else if (
        this.currentTabItme.modelLabel === "room" &&
        this.currentTabItme.roomtype
      ) {
        this.addProjectConfig2.inputData_in = this._.cloneDeep(
          this.currentTabItme
        );
        this.addProjectConfig2.visibleTrigger_in = new Date().getTime();
      } else if (
        this.currentTabItme.modelLabel === "room" &&
        !this.currentTabItme.roomtype
      ) {
        this.addProjectConfig4.inputData_in = this._.cloneDeep(
          this.currentTabItme
        );
        this.addProjectConfig4.visibleTrigger_in = new Date().getTime();
      } else if (
        ["manuequipment", "meteorologicalmonitor", "airconditioner"].includes(
          this.currentTabItme.modelLabel
        )
      ) {
        this.addProjectConfig5.inputData_in = this._.cloneDeep(
          this.currentTabItme
        );
        this.addProjectConfig5.visibleTrigger_in = new Date().getTime();
      } else if (this.currentTabItme.modelLabel === "pipeline") {
        // 修改管道
        this.addPipeline.inputData_in = Object.assign(
          {
            fatherModelLabel: this.currentNode.modelLabel,
            fatherId: this.currentNode.id
          },
          this.currentTabItme
        );
        this.addPipeline.visibleTrigger_in = new Date().getTime();
      } else if (this.currentTabItme.modelLabel === "pump") {
        this.addProjectConfig7.inputData_in = this._.cloneDeep(
          this.currentTabItme
        );
        this.addProjectConfig7.visibleTrigger_in = new Date().getTime();
      } else if (
        [
          "coolingtower",
          "windset",
          "coldwatermainengine",
          "plateheatexchanger"
        ].includes(this.currentTabItme.modelLabel)
      ) {
        this.addProjectConfig8.inputData_in = this._.cloneDeep(
          this.currentTabItme
        );
        this.addProjectConfig8.visibleTrigger_in = new Date().getTime();
      } else if (
        ["aircompressor", "colddryingmachine", "dryingmachine"].includes(
          this.currentTabItme.modelLabel
        )
      ) {
        this.addProjectConfig9.inputData_in = this._.cloneDeep(
          this.currentTabItme
        );
        this.addProjectConfig9.visibleTrigger_in = new Date().getTime();
      } else if (["boiler"].includes(this.currentTabItme.modelLabel)) {
        this.addProjectConfig10.inputData_in = this._.cloneDeep(
          this.currentTabItme
        );
        this.addProjectConfig10.visibleTrigger_in = new Date().getTime();
      } else if (this.currentTabItme.modelLabel === "linesegment") {
        this.addProjectConfig11.inputData_in = this._.cloneDeep(
          this.currentTabItme
        );
        this.addProjectConfig11.visibleTrigger_in = new Date().getTime();
      } else if (
        [
          "computer",
          "gateway",
          "meter",
          "interchanger",
          "photoeleconverter"
        ].includes(this.currentTabItme.modelLabel)
      ) {
        this.AddOrEditDeviceDialog.isEditMode_in = true;
        this.AddOrEditDeviceDialog.deviceData_in = this._.cloneDeep(
          this.currentTabItme
        );
        const deviceList = ELECTRICAL_DEVICE_NODE.deviceNodeList || [];
        const createDevice = deviceList.find(
          item => item.value === this.currentTabItme.modelLabel
        );
        const params = {
          modelLabel: this.currentTabItme.modelLabel,
          name: createDevice && createDevice.label
        };
        this.AddOrEditDeviceDialog.deviceMsg_in = this._.cloneDeep(params);
        this.AddOrEditDeviceDialog.openTrigger_in = new Date().getTime();
      } else {
        this.addProjectConfig6.inputData_in = this._.cloneDeep(
          this.currentTabItme
        );
        this.addProjectConfig6.visibleTrigger_in = new Date().getTime();
      }
    },
    // 删除
    CetButton_5_statusTrigger_out(val) {
      this.$confirm("是否删除此节点?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          new Promise((resolve, reject) => {
            this.isExistChildren_out(resolve);
          }).then(data => {
            if (data.data === 0) {
              this.toDeleteNode_out();
            } else {
              this.$message({
                message: "所选节点底下包含有子节点，不允许被删除",
                type: "warning"
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    isExistChildren_out(callback) {
      var _this = this;
      var auth = _this.token; //身份验证
      let nodes = this.selectedData || [];
      if (nodes.length === 0) {
        return;
      }
      var data = nodes.map(item => {
        return {
          id: item.id,
          modelLabel: item.modelLabel
        };
      });
      httping({
        url: "/eem-service/v1/node/nodeChild",
        data,
        method: "POST",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          callback(res);
        }
      });
    },
    toDeleteNode_out() {
      var _this = this;
      var dataObj = {};
      _this.selectedData.forEach(item => {
        if (dataObj[item.modelLabel]) {
          dataObj[item.modelLabel].push(item.id);
        } else {
          dataObj[item.modelLabel] = [item.id];
        }
      });
      var promiseArr = [];
      Object.keys(dataObj).forEach(item => {
        promiseArr.push(
          new Promise((resolve, reject) => {
            var queryOption = {
              url: "/eem-service/v1/project/manageNode",
              method: "DELETE",
              data: {
                modelLabel: item,
                idRange: dataObj[item]
              }
            };
            httping(queryOption).then(function (response) {
              if (response.code === 0) {
                resolve();
              }
            });
          })
        );
      });
      Promise.all(promiseArr).then(() => {
        _this.$message.success("删除成功");
        _this.goDeleteImg_out();
        _this.goDeleteDoc_out();
        _this.deleteQuantityObject();
        _this.isRefreshTable = true;
        _this.getTreeData();
        _this.CetGiantTree_1.selectNode = _this._.cloneDeep(_this.currentNode);
      });
    },
    //添加同级节点
    CetButton_6_statusTrigger_out(val) {
      var fatherNode = this._.cloneDeep(this.fatherCurrentNode);
      var _this = this;
      var treeData = [];
      var treeNameList = [];
      if (this.currentNode.modelLabel === "project") {
        treeData = _this.CetGiantTree_1.inputData_in || [];
        treeNameList = [];
        treeData.forEach(item => {
          if (item.modelLabel === "project") {
            treeNameList.push(item.name);
          }
        });
        this.treeNameList = treeNameList;
      } else {
        treeData = fatherNode.children || [];
        treeNameList = [];
        treeData.forEach(item => {
          treeNameList.push(item.name);
        });
        this.treeNameList = treeNameList;
      }
      this.fatherNode = this._.cloneDeep(fatherNode);
      if (!this.currentNode.modelLabel) {
        return;
      }
      this.type_in = 1;
      if (this.currentNode.modelLabel === "project") {
        this.addProjectConfig1.inputData_in = {};
        this.addProjectConfig1.visibleTrigger_in = this._.cloneDeep(val);
      } else if (this.currentNode.modelLabel === "sectionarea") {
        this.netWork = false;
        this.addProjectConfig2.inputData_in = {};
        this.addProjectConfig2.visibleTrigger_in = this._.cloneDeep(val);
      } else if (this.currentNode.modelLabel === "building") {
        if (this.currentNode.getParentNode().modelLabel === "project") {
          this.netWork = false;
          this.addProjectConfig2.inputData_in = {};
          this.addProjectConfig2.visibleTrigger_in = this._.cloneDeep(val);
        } else {
          this.addProjectConfigBuilding.inputData_in = {};
          this.addProjectConfigBuilding.visibleTrigger_in =
            this._.cloneDeep(val);
        }
      } else if (this.currentNode.modelLabel === "floor") {
        this.addProjectConfig3.inputData_in = {};
        this.addProjectConfig3.visibleTrigger_in = this._.cloneDeep(val);
      } else if (this.currentNode.modelLabel === "room") {
        this.addProjectConfig4.inputData_in = {};
        this.addProjectConfig4.visibleTrigger_in = this._.cloneDeep(val);
      }
    },
    //添加子节点
    CetButton_7_statusTrigger_out(val) {
      if (!this.currentNode.modelLabel) {
        return;
      }
      this.fatherNode = this._.cloneDeep(this.currentNode);
      var treeData = this.fatherNode.children || [];
      var treeNameList = [];
      treeData.forEach(item => {
        treeNameList.push(item.name);
      });
      this.treeNameList = treeNameList;
      this.type_in = 1;
      if (this.currentNode.modelLabel === "project") {
        this.netWork = false;
        this.addProjectConfig2.inputData_in = {};
        this.addProjectConfig2.visibleTrigger_in = this._.cloneDeep(val);
      } else if (this.currentNode.modelLabel === "sectionarea") {
        this.addProjectConfigBuilding.inputData_in = {};
        this.addProjectConfigBuilding.visibleTrigger_in = this._.cloneDeep(val);
      } else if (this.currentNode.modelLabel === "building") {
        this.netWork = true;
        this.addProjectConfig2.inputData_in = {};
        this.addProjectConfig2.visibleTrigger_in = this._.cloneDeep(val);
      } else if (this.currentNode.modelLabel === "floor") {
        this.addProjectConfig4.inputData_in = {};
        this.addProjectConfig4.visibleTrigger_in = this._.cloneDeep(val);
      } else if (
        this.currentNode.modelLabel === "room" &&
        this.currentNode.roomtype !== 1
      ) {
        this.addProjectConfig5.inputData_in = {};
        this.addProjectConfig5.visibleTrigger_in = this._.cloneDeep(val);
      } else if (
        this.currentNode.modelLabel === "room" &&
        this.currentNode.roomtype === 1
      ) {
        this.addProjectConfig6.inputData_in = {};
        this.addProjectConfig6.visibleTrigger_in = this._.cloneDeep(val);
      }
    },
    // 关联设备
    CetButton_8_statusTrigger_out(val) {
      this.$router.push({
        name: "associatRelation",
        params: {
          projectId: this.projectId,
          deviceId: this.currentTabItme.id,
          deviceModelLabel: this.currentTabItme.modelLabel
        }
      });
    },
    //添加用能设备
    CetButton_9_statusTrigger_out(val) {
      if (!this.currentNode.modelLabel) {
        return;
      }
      this.fatherNode = this._.cloneDeep(this.currentNode);
      this.type_in = 1;
      // if(this.currentNode.modelLabel === "floor" && this.selectedMenu === "产线/房间"){
      //   this.type_in = 2;
      // }
      this.addProjectConfig5.inputData_in = {};
      this.addProjectConfig5.visibleTrigger_in = this._.cloneDeep(val);
    },
    //添加配电设备
    CetButton_10_statusTrigger_out(val) {
      if (!this.currentNode.modelLabel) {
        return;
      }
      this.fatherNode = this._.cloneDeep(this.currentNode);
      this.type_in = 1;
      this.addProjectConfig6.inputData_in = {};
      this.addProjectConfig6.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 添加管道
    CetButton_14_statusTrigger_out(val) {
      this.type_in = 1;
      this.addPipeline.visibleTrigger_in = this._.cloneDeep(val);
      this.addPipeline.inputData_in = {
        fatherModelLabel: this.currentNode.modelLabel,
        fatherId: this.currentNode.id
      };
    },
    // 添加管道房设备
    CetButton_15_statusTrigger_out(val) {
      if (!this.currentNode.modelLabel) {
        return;
      }
      this.fatherNode = this._.cloneDeep(this.currentNode);
      this.type_in = 1;
      this.addProjectConfig7.inputData_in = {};
      this.addProjectConfig7.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 添加空调机房设备
    CetButton_16_statusTrigger_out(val) {
      if (!this.currentNode.modelLabel) {
        return;
      }
      this.fatherNode = this._.cloneDeep(this.currentNode);
      this.type_in = 1;
      this.addProjectConfig8.inputData_in = {};
      this.addProjectConfig8.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 添加空压机房设备
    CetButton_17_statusTrigger_out(val) {
      if (!this.currentNode.modelLabel) {
        return;
      }
      this.fatherNode = this._.cloneDeep(this.currentNode);
      this.type_in = 1;
      this.addProjectConfig9.inputData_in = {};
      this.addProjectConfig9.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 添加锅炉房设备
    CetButton_18_statusTrigger_out(val) {
      if (!this.currentNode.modelLabel) {
        return;
      }
      this.fatherNode = this._.cloneDeep(this.currentNode);
      this.type_in = 1;
      this.addProjectConfig10.inputData_in = {};
      this.addProjectConfig10.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 添加一段线设备
    CetButton_19_statusTrigger_out(val) {
      if (!this.currentNode.modelLabel) {
        return;
      }
      this.fatherNode = this._.cloneDeep(this.currentNode);
      this.type_in = 1;
      this.addProjectConfig11.inputData_in = {};
      this.addProjectConfig11.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 添加IT机房设备
    CetButton_20_statusTrigger_out(val) {
      if (!this.currentNode.modelLabel) {
        return;
      }
      this.fatherNode = this._.cloneDeep(this.currentNode);
      this.type_in = 1;
      this.createNode_out();
    },
    ElInput_1_change_out(val) {},
    ElInput_1_input_out(val) {
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    CetTable_1_detailTrigger_out(index, val) {
      if (!val) {
        return;
      }
      this.currentTabItme = val;
      this.ProjectConfigDetail.inputData_in = this._.cloneDeep(
        this.currentTabItme
      );
      this.ProjectConfigDetail.visibleTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },

    CetTable_1_outputData_out(val) {},
    CetTable_1_record_out(val) {
      this.ajaxFlag = true;
      this.isRefreshTable = false;
      if (!val || !val.modelLabel) {
        return;
      }
      this.currentTabItme = val;
      // this.ProjectConfigDetail.inputData_in = this._.cloneDeep(val);
      if (val.modelLabel === "project") {
        this.addProjectConfig1.inputData_in = this._.cloneDeep(val);
      } else if (val.modelLabel === "sectionarea") {
        this.addProjectConfig2.inputData_in = this._.cloneDeep(val);
      } else if (val.modelLabel === "building") {
        this.addProjectConfigBuilding.inputData_in = this._.cloneDeep(val);
      } else if (val.modelLabel === "floor") {
        this.addProjectConfig3.inputData_in = this._.cloneDeep(val);
      } else if (val.modelLabel === "room" && val.roomtype !== 1) {
        this.addProjectConfig4.inputData_in = this._.cloneDeep(val);
      } else if (val.modelLabel === "room" && val.roomtype === 1) {
        this.addProjectConfig2.inputData_in = this._.cloneDeep(val);
      } else if (
        ["manuequipment", "meteorologicalmonitor", "airconditioner"].includes(
          val.modelLabel
        )
      ) {
        this.addProjectConfig5.inputData_in = this._.cloneDeep(val);
      } else if (val.modelLabel === "pump") {
        this.addProjectConfig7.inputData_in = this._.cloneDeep(val);
      } else {
        this.addProjectConfig6.inputData_in = this._.cloneDeep(val);
      }
    },
    //选中节点触发方法
    CetGiantTree_1_currentNode_out(val) {
      this.currentNode = val;
      if (val && val.modelLabel === "project") {
        this.fatherCurrentNode = null;
      } else {
        this.fatherCurrentNode = val.getParentNode() || null;
      }

      if (!this.ajaxFlag) {
        return;
      }
      if (this.isRefreshTable && this.selectedMenu === "用能设备") {
        // this.isRefreshTable = false;
        this.getTableData();
        return;
      }
      this.isRefreshTable = false;
      this.CetButton_6.visible_in = true;
      this.CetButton_7.visible_in = true;
      this.CetButton_8.visible_in = true;
      this.CetButton_9.visible_in = true;
      this.CetButton_10.visible_in = true;
      this.CetButton_12.visible_in = true;
      this.CetButton_13.visible_in = true;
      this.CetButton_3.visible_in = true;
      if (val.children && val.children.length > 0) {
        this.modelLabel = val.children[0].modelLabel;
      }
      // if(!val.children || val.children.length === 0){
      //   this.CetButton_7.disable_in = true;
      // } else {
      //   this.CetButton_7.disable_in = false;
      // }
      // 重置筛选条件
      this.CetTable_1.dynamicInput.name = "";
      //重置tab
      this.selectedMenu = null;
      if (val.modelLabel === "building") {
        this.showTab = false;
        this.menus = ["车间/楼栋", "用能设备"];
        this.selectedMenu = "车间/楼栋";
      } else if (val.modelLabel === "floor") {
        this.showTab = false;
        this.menus = ["产线/房间", "用能设备"];
        this.selectedMenu = "产线/房间";
      } else if (val.modelLabel === "room" && val.roomtype === 1) {
        this.showTab = true;
        this.menus = ["配电设备", "用能设备"];
        this.selectedMenu = "配电设备";
      } else {
        this.showTab = false;
      }

      if (val.modelLabel === "district") {
        this.ElTableColumnArr = [
          {
            label: "节点名称",
            prop: "name",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.name && val.name !== 0) {
                return val.name;
              } else {
                return "--";
              }
            }
          },
          {
            label: "所属区域",
            prop: "hierarchy",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.hierarchy && val.hierarchy !== 0) {
                return val.hierarchy;
              } else {
                return "--";
              }
            }
          },
          {
            label: "项目编号",
            prop: "code",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.code && val.code !== 0) {
                return val.code;
              } else {
                return "--";
              }
            }
          },
          // {
          //   label: "合作截止时间",
          //   prop: "cooperativedeadline",
          //   formatter: function (row, column) {
          //     return common.formatDate(row.cooperativedeadline, "YYYY-MM-DD");
          //   },
          //   headerAlign: "left",
          //   align: "left",
          //   showOverflowTooltip: true
          // },
          {
            label: "所属公司",
            prop: "enterprisename",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.enterprisename && val.enterprisename !== 0) {
                return val.enterprisename;
              } else {
                return "--";
              }
            }
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "project") {
        this.ElTableColumnArr = [
          {
            label: "节点名称",
            prop: "name",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.name && val.name !== 0) {
                return val.name;
              } else {
                return "--";
              }
            }
          },
          {
            label: "所属层级",
            prop: "project",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.project && val.project !== 0) {
                return val.project;
              } else {
                return "--";
              }
            }
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "sectionarea") {
        this.ElTableColumnArr = [
          {
            label: "节点名称",
            prop: "name",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.name && val.name !== 0) {
                return val.name;
              } else {
                return "--";
              }
            }
          },
          {
            label: "所属层级",
            prop: "sectionarea",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.sectionarea && val.sectionarea !== 0) {
                return val.sectionarea;
              } else {
                return "--";
              }
            }
          },
          {
            label: "编号",
            prop: "code",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.code && val.code !== 0) {
                return val.code;
              } else {
                return "--";
              }
            }
          },
          /* {
            label: "电压等级",
            prop: "voltagelevel$text",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.voltagelevel$text && val.voltagelevel$text !== 0) {
                return val.voltagelevel$text;
              } else {
                return "--";
              }
            }
          }, */
          {
            label: "面积（平方米）",
            prop: "area",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val, cell, cellVal) {
              if (val.floorarea && val.floorarea !== 0) {
                return val.floorarea;
              } else if (val.area && val.area !== 0) {
                return val.area;
              } else if (cellVal === 0) {
                return cellVal;
              } else {
                return "--";
              }
            }
          },
          {
            label: "人数（人）",
            prop: "population",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.population && val.population !== 0) {
                return val.population;
              } else {
                return "--";
              }
            }
          },
          {
            label: "合作截止时间",
            prop: "cooperativedeadline",
            formatter: function (row, column) {
              return common.formatDate(row.cooperativedeadline, "YYYY-MM-DD");
            },
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "building") {
        this.ElTableColumnArr = [
          {
            label: "节点名称",
            prop: "name",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.name && val.name !== 0) {
                return val.name;
              } else {
                return "--";
              }
            }
          },
          {
            label: "所属层级",
            prop: "building",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.building && val.building !== 0) {
                return val.building;
              } else {
                return "--";
              }
            }
          },
          {
            label: "地址",
            prop: "address",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.address && val.address !== 0) {
                return val.address;
              } else {
                return "--";
              }
            }
          },
          {
            label: "面积（平方米）",
            prop: "area",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.area && val.area !== 0) {
                return val.area;
              } else {
                return "--";
              }
            }
          },
          {
            label: "人数（人）",
            prop: "population",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.population && val.population !== 0) {
                return val.population;
              } else {
                return "--";
              }
            }
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "floor") {
        this.ElTableColumnArr = [
          {
            label: "节点名称",
            prop: "name",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.name && val.name !== 0) {
                return val.name;
              } else {
                return "--";
              }
            }
          },
          {
            label: "所属层级",
            prop: "floor",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.floor && val.floor !== 0) {
                return val.floor;
              } else {
                return "--";
              }
            }
          },
          {
            label: "地址",
            prop: "address",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.address && val.address !== 0) {
                return val.address;
              } else {
                return "--";
              }
            }
          },
          {
            label: "面积（平方米）",
            prop: "area",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.area && val.area !== 0) {
                return val.area;
              } else {
                return "--";
              }
            }
          },
          {
            label: "人数（人）",
            prop: "population",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.population && val.population !== 0) {
                return val.population;
              } else {
                return "--";
              }
            }
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "room") {
        if ([1, 2, 3, 4, 5].includes(val.roomtype)) {
          // 配电设备
          this.ElTableColumnArr = [
            {
              label: "节点名称",
              prop: "name",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.name && val.name !== 0) {
                  return val.name;
                } else {
                  return "--";
                }
              }
            },
            {
              label: "编号",
              prop: "code",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.code && val.code !== 0) {
                  return val.code;
                } else {
                  return "--";
                }
              }
            },
            {
              label: "型号",
              prop: "model",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.model && val.model !== 0) {
                  return val.model;
                } else {
                  return "--";
                }
              }
            },
            {
              label: "投运时间",
              prop: "commissiondate",
              formatter: function (row, column) {
                return common.formatDate(row.commissiondate, "YYYY-MM-DD");
              },
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true
            }
          ];
        } else if (val.roomtype === 6) {
          this.ElTableColumnArr = [
            {
              label: "节点名称",
              prop: "name",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.name && val.name !== 0) {
                  return val.name;
                } else {
                  return "--";
                }
              }
            },
            {
              label: "能源类型",
              prop: "energytype$text",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.energytype$text && val.energytype$text !== 0) {
                  return val.energytype$text;
                } else {
                  return "--";
                }
              }
            }
          ];
        } else {
          // 用能设备
          this.ElTableColumnArr = [
            {
              label: "节点名称",
              prop: "name",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.name && val.name !== 0) {
                  return val.name;
                } else {
                  return "--";
                }
              }
            },
            {
              label: "所属层级",
              prop: "room",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.room && val.room !== 0) {
                  return val.room;
                } else {
                  return "--";
                }
              }
            },
            {
              label: "安装位置",
              prop: "location",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.location && val.location !== 0) {
                  return val.location;
                } else {
                  return "--";
                }
              }
            },
            {
              label: "厂家",
              prop: "manufactor",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.manufactor && val.manufactor !== 0) {
                  return val.manufactor;
                } else {
                  return "--";
                }
              }
            },
            {
              label: "型号",
              prop: "model",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.model && val.model !== 0) {
                  return val.model;
                } else {
                  return "--";
                }
              }
            }
          ];
        }
        this.getTableData();
      } else if (["powerdiscabinet", "arraycabinet"].includes(val.modelLabel)) {
        this.ElTableColumnArr = [
          {
            label: "节点名称",
            prop: "name",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.name && val.name !== 0) {
                return val.name;
              } else {
                return "--";
              }
            }
          },
          {
            label: "编号",
            prop: "code",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.code && val.code !== 0) {
                return val.code;
              } else {
                return "--";
              }
            }
          },
          {
            label: "品牌",
            prop: "brand",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.brand && val.brand !== 0) {
                return val.brand;
              } else {
                return "--";
              }
            }
          },
          {
            label: "型号",
            prop: "model",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.model && val.model !== 0) {
                return val.model;
              } else {
                return "--";
              }
            }
          },
          {
            label: "投运时间",
            prop: "commissiondate",
            formatter: function (row, column) {
              return common.formatDate(row.commissiondate, "YYYY-MM-DD");
            },
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true
          }
          /* {
            label: "电压等级",
            prop: "voltagelevel$text",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.voltagelevel$text && val.voltagelevel$text !== 0) {
                return val.voltagelevel$text;
              } else {
                return "--";
              }
            }
          } */
        ];
        // this.CetTable_1.data = val.children || [];
        this.getTableData();
      } else {
        if (val.modelLabel === "manuequipment") {
          // 用能设备
          this.ElTableColumnArr = [
            {
              label: "节点名称",
              prop: "name",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.name && val.name !== 0) {
                  return val.name;
                } else {
                  return "--";
                }
              }
            },
            {
              label: "所属层级",
              prop: "room",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.room && val.room !== 0) {
                  return val.room;
                } else {
                  return "--";
                }
              }
            },
            {
              label: "安装位置",
              prop: "location",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.location && val.location !== 0) {
                  return val.location;
                } else {
                  return "--";
                }
              }
            },
            {
              label: "厂家",
              prop: "manufactor",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.manufactor && val.manufactor !== 0) {
                  return val.manufactor;
                } else {
                  return "--";
                }
              }
            },
            {
              label: "型号",
              prop: "model",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.model && val.model !== 0) {
                  return val.model;
                } else {
                  return "--";
                }
              }
            }
          ];
        } else if (
          ["meteorologicalmonitor", "airconditioner"].includes(val.modelLabel)
        ) {
          this.ElTableColumnArr = [
            {
              label: "节点名称",
              prop: "name",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.name && val.name !== 0) {
                  return val.name;
                } else {
                  return "--";
                }
              }
            }
          ];
        } else if (val.modelLabel === "civicpipe") {
          this.ElTableColumnArr = [
            {
              label: "节点名称",
              prop: "name",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.name && val.name !== 0) {
                  return val.name;
                } else {
                  return "--";
                }
              }
            },
            {
              label: "所属层级",
              prop: "project",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.project && val.project !== 0) {
                  return val.project;
                } else {
                  return "--";
                }
              }
            }
          ];
        } else if (val.modelLabel === "pipeline") {
          this.ElTableColumnArr = [
            {
              label: "节点名称",
              prop: "name",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.name && val.name !== 0) {
                  return val.name;
                } else {
                  return "--";
                }
              }
            },
            {
              label: "能源类型",
              prop: "energytype$text",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.energytype$text && val.energytype$text !== 0) {
                  return val.energytype$text;
                } else {
                  return "--";
                }
              }
            }
          ];
        } else {
          // 配电设备
          this.ElTableColumnArr = [
            {
              label: "节点名称",
              prop: "name",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.name && val.name !== 0) {
                  return val.name;
                } else {
                  return "--";
                }
              }
            },
            {
              label: "编号",
              prop: "code",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.code && val.code !== 0) {
                  return val.code;
                } else {
                  return "--";
                }
              }
            },
            {
              label: "型号",
              prop: "model",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.model && val.model !== 0) {
                  return val.model;
                } else {
                  return "--";
                }
              }
            },
            {
              label: "投运时间",
              prop: "commissiondate",
              formatter: function (row, column) {
                return common.formatDate(row.commissiondate, "YYYY-MM-DD");
              },
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true
            },
            {
              label: "厂家",
              prop: "manufactor",
              headerAlign: "left",
              align: "left",
              showOverflowTooltip: true,
              formatter: function (val) {
                if (val.manufactor && val.manufactor !== 0) {
                  return val.manufactor;
                } else {
                  return "--";
                }
              }
            }
          ];
        }
        var obj = {
          id: val.id,
          modelLabel: val.modelLabel
        };
        this.CetTable_1.dynamicInput.selected = {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          name: this.currentNode.name
        };
        this.CetTable_1.dynamicInput.nodes = [obj];
        this.CetButton_3.disable_in = false;
        this.CetTable_1.queryTrigger_in = new Date().getTime();
      }
      if (val.modelLabel === "room") {
        this.CetButton_6.visible_in = false;
        this.CetButton_7.visible_in = false;
        this.CetButton_8.visible_in = true;
        this.CetButton_9.visible_in = true;
      } else if (
        val.modelLabel === "project" ||
        val.modelLabel === "sectionarea" ||
        val.modelLabel === "building" ||
        val.modelLabel === "floor" ||
        val.modelLabel === "district"
      ) {
        this.CetButton_8.visible_in = false;
        this.CetButton_6.visible_in = true;
        this.CetButton_7.visible_in = true;
        this.CetButton_9.visible_in = false;
      } else {
        this.CetButton_6.visible_in = false;
        this.CetButton_7.visible_in = false;
      }

      if (
        val.modelLabel === "project" ||
        val.modelLabel === "building" ||
        val.modelLabel === "floor" ||
        val.modelLabel === "room"
      ) {
        // this.CetButton_9.visible_in = true;
      } else {
        this.CetButton_9.visible_in = false;
      }
      this.CetButton_14.visible_in = false;
      this.CetButton_15.visible_in = false;
      this.CetButton_16.visible_in = false;
      this.CetButton_17.visible_in = false;
      this.CetButton_18.visible_in = false;
      this.CetButton_19.visible_in = false;
      this.CetButton_20.visible_in = false;
      if (val.modelLabel === "room" && val.roomtype === 1) {
        this.CetButton_10.visible_in = true;
        this.CetButton_14.visible_in = false;
        this.CetButton_15.visible_in = false;
        this.CetButton_9.visible_in = true;
      } else if (val.modelLabel === "room" && val.roomtype === 2) {
        this.CetButton_10.visible_in = false;
        this.CetButton_14.visible_in = false;
        this.CetButton_15.visible_in = false;
        this.CetButton_9.visible_in = false;
        this.CetButton_20.visible_in = true;
      } else if (val.modelLabel === "room" && val.roomtype === 6) {
        this.CetButton_10.visible_in = false;
        this.CetButton_14.visible_in = true;
        this.CetButton_15.visible_in = true;
        this.CetButton_9.visible_in = false;
      } else if (val.modelLabel === "room" && val.roomtype === 3) {
        this.CetButton_16.visible_in = true;
        this.CetButton_10.visible_in = false;
        this.CetButton_15.visible_in = true;
        this.CetButton_9.visible_in = false;
      } else if (val.modelLabel === "room" && val.roomtype === 4) {
        this.CetButton_17.visible_in = true;
        this.CetButton_10.visible_in = false;
        this.CetButton_15.visible_in = true;
        this.CetButton_9.visible_in = false;
      } else if (val.modelLabel === "room" && val.roomtype === 5) {
        this.CetButton_18.visible_in = true;
        this.CetButton_10.visible_in = false;
        this.CetButton_15.visible_in = true;
        this.CetButton_9.visible_in = false;
      } else if (val.modelLabel === "powerdiscabinet") {
        this.CetButton_19.visible_in = true;
        this.CetButton_10.visible_in = false;
        this.CetButton_9.visible_in = false;
      } else if (val.modelLabel === "arraycabinet") {
        this.CetButton_19.visible_in = true;
        this.CetButton_10.visible_in = false;
        this.CetButton_9.visible_in = false;
      } else {
        this.CetButton_10.visible_in = false;
      }
      this.CetButton_1.visible_in = true;
      if (val.modelLabel === "project") {
        this.CetButton_6.visible_in = false;
        this.CetButton_12.disable_in = true;
      } else {
        this.CetButton_12.disable_in = false;
      }
    },
    addProjectConfig1_currentData_out(val) {},
    addProjectConfig1_finishData_out(val) {},
    addProjectConfig1_finishTrigger_out(val) {},
    addProjectConfig1_saveData_out(val) {
      this.$message.success("保存成功");
      if (this.type_in === 3) {
        this.getTreeData(true);
        // this.CetTable_1.queryTrigger_in = new Date().getTime();
      } else {
        // 新增需要关联项目和租户
        this.relateTenantAndProject(val);
        this.addAuthUser_out(val);
        // this.getTreeData();
        this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
      }
    },
    relateTenantAndProject(data) {
      httping({
        url: `/eem-service/v1/auth/cloud/relateTenantAndProject?projectId=${data.id}&tenantId=${this.userInfo.tenantId}`,
        method: "PUT"
      });
    },
    addProjectConfig2_currentData_out(val) {},
    addProjectConfig2_finishData_out(val) {},
    addProjectConfig2_finishTrigger_out(val) {},
    addProjectConfig2_saveData_out(val) {
      this.$message.success("保存成功");
      if (this.type_in !== 3) {
        this.addAuthUser_out(val);
      } else {
        this.getTreeData();
        this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
      }
    },
    addProjectConfigSectionarea_saveData_out(val) {
      this.$message.success("保存成功");
      if (this.type_in !== 3) {
        this.addAuthUser_out(val);
      } else {
        this.getTreeData();
        this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
      }
    },
    addProjectConfig3_currentData_out(val) {},
    addProjectConfig3_finishData_out(val) {},
    addProjectConfig3_finishTrigger_out(val) {},
    addProjectConfig3_saveData_out(val) {
      this.$message.success("保存成功");
      this.isRefreshTable = true;
      if (this.type_in !== 3) {
        this.addAuthUser_out(val);
      } else {
        this.getTreeData();
        this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
      }
    },
    addProjectConfig4_currentData_out(val) {},
    addProjectConfig4_finishData_out(val) {},
    addProjectConfig4_finishTrigger_out(val) {},
    addProjectConfig4_saveData_out(val) {
      this.$message.success("保存成功");
      this.isRefreshTable = true;
      if (this.type_in !== 3) {
        this.addAuthUser_out(val);
      } else {
        this.getTreeData();
        this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
      }
    },
    addProjectConfig5_currentData_out(val) {},
    addProjectConfig5_finishData_out(val) {},
    addProjectConfig5_finishTrigger_out(val) {},
    addProjectConfig5_saveData_out(val) {
      this.$message.success("保存成功");
      this.isRefreshTable = true;
      if (this.type_in !== 3) {
        this.addAuthUser_out(val);
      } else {
        this.getTreeData();
        // this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
      }
    },
    addProjectConfig6_currentData_out(val) {},
    addProjectConfig6_finishData_out(val) {},
    addProjectConfig6_finishTrigger_out(val) {},
    addProjectConfig6_saveData_out(val) {
      this.$message.success("保存成功");
      if (this.type_in !== 3) {
        this.addAuthUser_out(val);
      } else {
        this.getTreeData();
        this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
      }
    },
    addProjectConfig7_currentData_out(val) {},
    addProjectConfig7_finishData_out(val) {},
    addProjectConfig7_finishTrigger_out(val) {},
    addProjectConfig7_saveData_out(val) {
      this.$message.success("保存成功");
      if (this.type_in !== 3) {
        this.addAuthUser_out(val);
      } else {
        this.getTreeData();
        this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
      }
    },
    addProjectConfig8_currentData_out(val) {},
    addProjectConfig8_finishData_out(val) {},
    addProjectConfig8_finishTrigger_out(val) {},
    addProjectConfig8_saveData_out(val) {
      this.$message.success("保存成功");
      if (this.type_in !== 3) {
        this.addAuthUser_out(val);
      } else {
        this.getTreeData();
        this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
      }
    },
    addProjectConfig9_currentData_out(val) {},
    addProjectConfig9_finishData_out(val) {},
    addProjectConfig9_finishTrigger_out(val) {},
    addProjectConfig9_saveData_out(val) {
      this.$message.success("保存成功");
      if (this.type_in !== 3) {
        this.addAuthUser_out(val);
      } else {
        this.getTreeData();
        this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
      }
    },
    addProjectConfig10_currentData_out(val) {},
    addProjectConfig10_finishData_out(val) {},
    addProjectConfig10_finishTrigger_out(val) {},
    addProjectConfig10_saveData_out(val) {
      this.$message.success("保存成功");
      if (this.type_in !== 3) {
        this.addAuthUser_out(val);
      } else {
        this.getTreeData();
        this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
      }
    },
    addProjectConfig11_currentData_out(val) {},
    addProjectConfig11_finishData_out(val) {},
    addProjectConfig11_finishTrigger_out(val) {},
    addProjectConfig11_saveData_out(val) {
      this.$message.success("保存成功");
      if (this.type_in !== 3) {
        this.addAuthUser_out(val);
      } else {
        this.getTreeData();
        this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
      }
    },
    ProjectConfigDetail_currentData_out(val) {},
    ProjectConfigDetail_finishData_out(val) {},
    ProjectConfigDetail_finishTrigger_out(val) {},
    ProjectConfigDetail_saveData_out(val) {},
    addPipeline_currentData_out(val) {},
    addPipeline_finishData_out(val) {},
    addPipeline_finishTrigger_out(val) {},
    addPipeline_saveData_out(val) {
      this.$message.success("保存成功");
      if (this.type_in !== 3) {
        this.addAuthUser_out(val);
      } else {
        this.getTreeData();
        this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
      }
    },
    getTreeData(initProject) {
      var _this = this;
      var auth = _this.token; //身份验证
      let params;
      params = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.allManagement,
        treeReturnEnable: true
      };
      httping({
        url: "/eem-service/v1/node/nodeTree/simple",
        method: "POST",
        data: params
      }).then(res => {
        if (res.code === 0) {
          const resData = res.data || [];

          _this.CetGiantTree_1.inputData_in = resData;
          if (!_this.currentNode) {
            _this.CetGiantTree_1.selectNode = resData[0];
          } else {
            _this.CetGiantTree_1.selectNode = _this._.cloneDeep(
              _this.currentNode
            );
            setTimeout(() => {
              let node = _this.$refs.cetGiantTree.ztreeObj.getNodeByParam(
                "tree_id",
                _this.currentNode.tree_id
              );
              _this.CetGiantTree_1_currentNode_out(node);
            }, 0);
          }
          if (initProject) {
            _this.getProject();
          }
        } else {
          _this.$message.error(res.msg);
        }
      });
    },
    getTableData(params) {
      this.$nextTick(() => {
        var nodes = this.getChildrenNodes();
        // var params = {
        //   nodes: this.getChildrenNodes()
        // };
        this.CetTable_1.dynamicInput.selected = {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          name: this.currentNode.name
        };
        this.CetTable_1.dynamicInput.nodes = nodes;

        if (nodes && nodes.length === 0) {
          this.CetButton_3.disable_in = true;
          // this.CetButton_7.disable_in = true;
          this.$nextTick(() => {
            if (
              this.currentNode.modelLabel === "floor" &&
              this.selectedMenu === "产线/房间"
            ) {
              this.CetButton_9.disable_in = true;
            } else {
              this.CetButton_9.disable_in = false;
            }
          });
        } else {
          this.CetButton_3.disable_in = false;
          // this.CetButton_7.disable_in = false;
          this.CetButton_9.disable_in = false;
        }
        if (this.ajaxFlag) {
          this.ajaxFlag = false;
          this.CetTable_1.queryTrigger_in = new Date().getTime();
        }
      });
    },
    // 获取节点字列表
    getChildrenNodes() {
      var currentNode = this.currentNode;
      var childrenNodes = currentNode.children || [];
      var nodes = [];
      var treeNameList = [];
      var len = childrenNodes.length;
      for (var i = 0; i < len; i++) {
        if (this.selectedMenu === "用能设备") {
          if (
            [
              "manuequipment",
              "meteorologicalmonitor",
              "airconditioner"
            ].includes(childrenNodes[i].modelLabel)
          ) {
            var obj = {
              modelLabel: childrenNodes[i].modelLabel,
              id: childrenNodes[i].id
            };
            nodes.push(obj);
          }
        } else if (
          currentNode.modelLabel === "room" &&
          currentNode.roomtype !== 1
        ) {
          const obj = {
            modelLabel: childrenNodes[i].modelLabel,
            id: childrenNodes[i].id
          };
          nodes.push(obj);
        } else if (
          currentNode.modelLabel === "room" &&
          currentNode.roomtype === 1
        ) {
          if (
            this.selectedMenu === "配电设备" &&
            ![
              "manuequipment",
              "meteorologicalmonitor",
              "airconditioner"
            ].includes(childrenNodes[i].modelLabel)
          ) {
            const obj = {
              modelLabel: childrenNodes[i].modelLabel,
              id: childrenNodes[i].id
            };
            nodes.push(obj);
          }
        } else {
          if (
            ![
              "manuequipment",
              "meteorologicalmonitor",
              "airconditioner"
            ].includes(childrenNodes[i].modelLabel)
          ) {
            var hasChildren = false;
            if (
              childrenNodes[i].children &&
              childrenNodes[i].children.length > 0
            ) {
              hasChildren = true;
            }
            const obj = {
              modelLabel: childrenNodes[i].modelLabel,
              id: childrenNodes[i].id,
              hasChildren: hasChildren
            };
            nodes.push(obj);
          }
        }
        treeNameList.push(childrenNodes[i].name);
      }
      this.treeNameList = treeNameList;
      return nodes;
    },
    //表格点击多选框
    handleSelectionChange(val) {
      this.selectedData = val;
      if (val && val.length > 0) {
        this.CetButton_5.disable_in = false;
        this.CetButton_21.disable_in = false;
      } else {
        this.CetButton_5.disable_in = true;
        this.CetButton_21.disable_in = true;
      }
    },
    //删除绑定图片
    goDeleteImg_out() {
      var _this = this;
      var auth = _this.token; //身份验证
      var list = _this.selectedData || [];
      for (var i = 0, len = list.length; i < len; i++) {
        var path = list[i].pic || list[i].picture;
        if (!path) {
          continue;
        }
        var urlStr = `/eem-service/v1/common/deleteFile?path=${path}`;
        httping({
          url: urlStr,
          method: "DELETE",
          timeout: 10000
        });
      }
    },
    //删除绑定文件
    goDeleteDoc_out() {
      var _this = this;
      var auth = _this.token; //身份验证
      var list = _this.selectedData || [];
      for (var i = 0, len = list.length; i < len; i++) {
        var path = list[i].document || "";
        if (!path) {
          continue;
        }
        var urlStr = `/eem-service/v1/common/deleteFile?path=${path}`;
        httping({
          url: urlStr,
          method: "DELETE",
          timeout: 10000
        });
      }
    },
    // 删除物理量
    deleteQuantityObject() {
      var _this = this;
      var list = _this.selectedData || [];
      var params = [];
      for (var i = 0, len = list.length; i < len; i++) {
        if (
          list[i].modelLabel === "project" ||
          list[i].modelLabel === "building" ||
          list[i].modelLabel === "sectionarea" ||
          list[i].modelLabel === "floor" ||
          list[i].modelLabel === "room"
        ) {
          continue;
        }
        params.push({
          modelLabel: list[i].modelLabel,
          nodes: [
            {
              id: list[i].id
            }
          ]
        });
      }
      if (params.length === 0) {
        return;
      }
      httping({
        url: "/eem-service/v1/quantity/quantityObject",
        method: "DELETE",
        data: params
      }).then(response => {});
      this.deleteEnergysupplyto();
      this.deleteMeasuredby();
    },
    // 删除energysupplyto表相关设备数据
    deleteEnergysupplyto() {
      this.selectedData.forEach(item => {
        var data = {
          rootID: 0,
          rootLabel: "energysupplyto",
          rootCondition: {
            filter: {
              expressions: [
                {
                  operator: "EQ",
                  prop: "supplytolabel",
                  limit: item.modelLabel
                },
                {
                  operator: "EQ",
                  prop: "supplytoid",
                  limit: item.id
                }
              ]
            }
          }
        };
        httping({
          url: "/eem-service/v1/common/query/oneLayer",
          method: "POST",
          data
        }).then(response => {
          if (response.code === 0 && response.data.length > 0) {
            httping({
              url: `/eem-service/v1/connect/relationship`,
              method: "DELETE",
              data: {
                modelLabel: "energysupplyto",
                idRange: response.data.map(item => item.id)
              }
            }).then(function (response) {});
          }
        });
      });
    },
    // 删除measuredby表相关设备数据
    deleteMeasuredby() {
      this.selectedData.forEach(item => {
        var data = {
          rootID: 0,
          rootLabel: "measuredby",
          rootCondition: {
            filter: {
              expressions: [
                {
                  operator: "EQ",
                  prop: "monitoredlabel",
                  limit: item.modelLabel
                },
                {
                  operator: "EQ",
                  prop: "monitoredid",
                  limit: item.id
                }
              ]
            }
          }
        };
        httping({
          url: "/eem-service/v1/common/query/oneLayer",
          method: "POST",
          data
        }).then(response => {
          if (response.code === 0 && response.data.length > 0) {
            httping({
              url: `/eem-service/v1/project/measureBy`,
              method: "DELETE",
              data: {
                modelLabel: "measuredby",
                idRange: response.data.map(item => item.id)
              }
            }).then(function (response) {});
          }
        });
      });
    },
    //切换tab
    handleTabClick_out(tab, e) {
      this.selectedMenu = tab;
      if (tab === "车间/楼栋") {
        this.CetButton_6.visible_in = true;
        this.CetButton_7.visible_in = true;
        this.CetButton_8.visible_in = false;
        this.CetButton_9.visible_in = false;

        this.ElTableColumnArr = [
          {
            label: "节点名称",
            prop: "name",
            headerAlign: "center",
            align: "center",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.name && val.name !== 0) {
                return val.name;
              } else {
                return "--";
              }
            }
          },
          {
            label: "所属层级",
            prop: "building",
            headerAlign: "center",
            align: "center",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.building && val.building !== 0) {
                return val.building;
              } else {
                return "--";
              }
            }
          },
          {
            label: "地址",
            prop: "address",
            headerAlign: "center",
            align: "center",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.address && val.address !== 0) {
                return val.address;
              } else {
                return "--";
              }
            }
          },
          {
            label: "面积（平方米）",
            prop: "area",
            headerAlign: "center",
            align: "center",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.area && val.area !== 0) {
                return val.area;
              } else {
                return "--";
              }
            }
          },
          {
            label: "人数（人）",
            prop: "population",
            headerAlign: "center",
            align: "center",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.population && val.population !== 0) {
                return val.population;
              } else {
                return "--";
              }
            }
          }
        ];
        this.getTableData();
      } else if (tab === "产线/房间") {
        // this.CetButton_6.visible_in = true;
        // this.CetButton_7.visible_in = false;
        // this.CetButton_8.visible_in = false;
        // this.CetButton_9.visible_in = true;
        this.ElTableColumnArr = [
          {
            label: "节点名称",
            prop: "name",
            headerAlign: "center",
            align: "center",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.name && val.name !== 0) {
                return val.name;
              } else {
                return "--";
              }
            }
          },
          {
            label: "所属层级",
            prop: "floor",
            headerAlign: "center",
            align: "center",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.floor && val.floor !== 0) {
                return val.floor;
              } else {
                return "--";
              }
            }
          },
          {
            label: "地址",
            prop: "address",
            headerAlign: "center",
            align: "center",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.address && val.address !== 0) {
                return val.address;
              } else {
                return "--";
              }
            }
          },
          {
            label: "面积（平方米）",
            prop: "area",
            headerAlign: "center",
            align: "center",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.area && val.area !== 0) {
                return val.area;
              } else {
                return "--";
              }
            }
          },
          {
            label: "人数（人）",
            prop: "population",
            headerAlign: "center",
            align: "center",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.population && val.population !== 0) {
                return val.population;
              } else {
                return "--";
              }
            }
          }
        ];
        this.getTableData();
      } else if (tab === "配电设备") {
        // this.CetButton_6.visible_in = true;
        // this.CetButton_7.visible_in = false;
        // this.CetButton_8.visible_in = false;
        // this.CetButton_9.visible_in = true;
        // 配电设备
        this.ElTableColumnArr = [
          {
            label: "节点名称",
            prop: "name",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.name && val.name !== 0) {
                return val.name;
              } else {
                return "--";
              }
            }
          },
          {
            label: "编号",
            prop: "code",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.code && val.code !== 0) {
                return val.code;
              } else {
                return "--";
              }
            }
          },
          {
            label: "型号",
            prop: "model",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.model && val.model !== 0) {
                return val.model;
              } else {
                return "--";
              }
            }
          },
          {
            label: "投运时间",
            prop: "commissiondate",
            formatter: function (row, column) {
              return common.formatDate(row.commissiondate, "YYYY-MM-DD");
            },
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true
          }
        ];
        this.getTableData();
      } else if (tab === "用能设备") {
        // this.CetButton_6.visible_in = false;
        // this.CetButton_7.visible_in = false;
        // this.CetButton_8.visible_in = true;
        // this.CetButton_9.visible_in = true;
        this.ElTableColumnArr = [
          {
            label: "节点名称",
            prop: "name",
            headerAlign: "center",
            align: "center",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.name && val.name !== 0) {
                return val.name;
              } else {
                return "--";
              }
            }
          },
          {
            label: "编号",
            prop: "code",
            headerAlign: "center",
            align: "center",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.code && val.code !== 0) {
                return val.code;
              } else {
                return "--";
              }
            }
          },
          {
            label: "厂家",
            prop: "manufactor",
            headerAlign: "center",
            align: "center",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.manufactor && val.manufactor !== 0) {
                return val.manufactor;
              } else {
                return "--";
              }
            }
          },
          {
            label: "型号",
            prop: "model",
            headerAlign: "center",
            align: "center",
            showOverflowTooltip: true,
            formatter: function (val) {
              if (val.model && val.model !== 0) {
                return val.model;
              } else {
                return "--";
              }
            }
          },
          {
            label: "投运时间",
            prop: "commissiondate",
            formatter: function (row, column) {
              return common.formatDate(row.commissiondate, "YYYY-MM-DD");
            },
            headerAlign: "center",
            align: "center",
            showOverflowTooltip: true
          }
        ];
        this.getTableData();
      }
    },
    // 添加权限
    addAuthUser_out(data) {
      // 获取权限
      var _this = this;
      // console.log(this.userInfo);
      if (this.userInfo && this.userInfo.name === "ROOT") {
        _this.getTreeData();
        return;
      }
      var params = [
        {
          id: data.id,
          modelLabel: data.modelLabel
        }
      ];
      httping({
        url: `/eem-service/v1/node/modelNode`,
        method: "PUT",
        data: params
      }).then(res => {
        if (res.code === 0) {
          _this.getTreeData();
        }
      });
    },
    getDistrictNode(node, callback) {
      var _this = this;
      var auth = _this.token; //身份验证
      var params = {
        rootID: node.id,
        rootLabel: "project",
        subLayerConditions: [
          {
            modelLabel: "district"
          }
        ]
      };
      httping({
        url: "/eem-service/v1/common/query/nodes", //反查
        data: params,
        method: "POST",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          var districtNode = _this._.get(res, ["data", "0", "children"], []);
          if (districtNode && districtNode.length > 0) {
            callback(districtNode[0]);
            // callback && callback(districtNode[0]);
          } else {
            const backMsg = {
              id: 0,
              modelLabel: "string"
            };
            callback(backMsg);
          }
        }
      });
    },
    //获取项目信息
    getProject() {
      var _this = this;
      var treeData = _this.CetGiantTree_1.inputData_in || [];
      var nodes = [];
      var selected = {};
      treeData.forEach(item => {
        if (item.modelLabel === "project") {
          var hasChildren = false;
          if (item.children && item.children.length > 0) {
            hasChildren = true;
          }
          var obj = {
            modelLabel: item.modelLabel,
            id: item.id,
            hasChildren: hasChildren
          };
          nodes.push(obj);
          selected = obj;
        }
      });
      let queryData = {
        rootCondition: {
          page: {
            index: 0,
            limit: 20
          },
          filter: {
            expressions: [
              { prop: "nodes", operator: "EQ", limit: nodes },
              { prop: "selected", operator: "EQ", limit: selected }
            ]
          }
        }
      };
      customApi.cloudProjectConfigQueryTable(queryData).then(res => {
        _this.projectInfo = _this._.get(res, "data[0]");
      });
      _;
    },
    //进入创建节点判断
    createNode_out() {
      const list = this._.cloneDeep(ELECTRICAL_DEVICE_RELATION);
      let isHasChild = false;
      list.forEach(item => {
        if (this.fatherNode.modelLabel === item.modelLabel) {
          isHasChild = true;
        }
      });
      //判断是否有子节点
      if (isHasChild) {
        this.openDeviceTypeSelectionDialog();
      } else {
        const deviceList = ELECTRICAL_DEVICE_NODE.deviceNodeList || [];
        const createDevice = deviceList.find(
          item => item.value === this.currentNode.modelLabel
        );
        const params = {
          modelLabel: this.currentNode.modelLabel,
          name: createDevice && createDevice.text
        };
        this.DeviceTypeSelectionDialog_DeviceType_out(params);
      }
    },
    // 打开设备类型选择窗口 (入口参数)
    openDeviceTypeSelectionDialog() {
      let vm = this;
      vm.DeviceTypeSelectionDialog.fatherNode_in = this.fatherNode;
      vm.$nextTick(() => {
        vm.DeviceTypeSelectionDialog.openTrigger_in = new Date().getTime();
      });
    },
    // 设备类型设置好后需要刷新设备模型
    DeviceTypeSelectionDialog_DeviceType_out(val) {
      this.AddOrEditDeviceDialog.isEditMode_in = false;
      this.AddOrEditDeviceDialog.deviceData_in = null;
      this.AddOrEditDeviceDialog.deviceMsg_in = this._.cloneDeep(val);
      this.AddOrEditDeviceDialog.openTrigger_in = new Date().getTime();
    },
    handleDeviceChanged(val) {
      if (this.type_in === 3) {
        this.getTreeData();
        this.CetTable_1.queryTrigger_in = new Date().getTime();
      } else {
        this.addAuthUser_out(val);
        this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
      }
    },
    // 批量转移节点
    CetButton_21_statusTrigger_out(val) {
      let nodes = this.selectedData || [];
      if (nodes.length === 0) {
        return;
      }
      var arr = [
        "project",
        "sectionarea",
        "building",
        "floor",
        "room",
        "arraycabinet",
        "powerdiscabinet"
      ];
      if (arr.indexOf(this.currentNode.modelLabel) === -1) {
        // 属于叶子节点
        this.fatherNode = this._.cloneDeep(this.fatherCurrentNode);
      } else {
        this.fatherNode = this._.cloneDeep(this.currentNode);
      }
      this.batchChangeNode.visibleTrigger_in = new Date().getTime();
    },
    // 批量转移节点之后，刷新页面
    updataOut() {
      this.getTreeData();
      this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
    }
  },
  created: function () {},
  mounted: function () {},
  activated: function () {
    this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
    this.currentNode = null;
    this.copyNode = null;
    this.getTreeData(true);
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  :deep() {
    .form-item-unit {
      line-height: 30px;
      position: absolute;
      @include background_color(BG);
      @include padding(0 J1);
      right: 1px;
      text-align: center;
      top: 1px;
      border-radius: 0 mh-get(C1) mh-get(C1) 0;
    }
  }
}
.title {
  overflow: hidden;
}
.footBtn {
  position: relative;
  top: -32px;
}
.menu_group {
  text-align: center;
  padding: 5px 0;
}

.row-detail {
  display: inline-block;
  width: 25px;
  height: 24px;
  cursor: pointer;
  background: url("./assets/details.png") no-repeat center center;
  border-radius: 50%;
  vertical-align: middle;
}
.row-handel {
  cursor: pointer;
  &:hover {
    @include font_color(ZS);
  }
}
.fcB1 {
  @include font_color(B1);
}
.projectInfo {
  .loadImg {
    width: 100px;
    height: 100px;
  }
  .projectInfoList {
    overflow: auto;
    position: relative;
    .projectInfoItem {
      width: 160px;
      @include padding(J2);
      @include margin_right(J2);
      @include background_color(BG);
      @include border_radius(C2);
      .label {
        @include font_color(T3);
      }
      .value {
        @include font_weight(MD);
      }
    }
  }
  .rightBtn {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    .rbtn {
      cursor: pointer;
      &:hover {
        @include font_color(ZS);
      }
    }
  }
}
</style>
