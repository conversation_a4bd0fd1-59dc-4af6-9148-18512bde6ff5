<template>
  <div class="fullheight">
    <div id="container" ref="container"></div>
  </div>
</template>

<script>
import busbarconnector from "./assets/busbarconnector.png";
import generator from "./assets/generator.png";
import powertransformer from "./assets/powertransformer.png";
import busbarsection from "./assets/busbarsection.png";
import G6 from "@antv/g6";
export default {
  name: "TopologyChart",
  props: {
    inputData_in: {
      type: [Object, Array]
    }
  },
  components: {},
  data() {
    return {
      graph: null
    };
  },
  watch: {
    inputData_in: {
      handler: function (val) {
        console.log("watch", val.edges.length);
        const data = this.getChartData();
        this.$nextTick(() => {
          this.paintChart();
          // if (this._.isEmpty(this.graph)) this.initGraphConf();
          // this.graph.changeData(data);
          // this.graph.layout();
        });
      },
      deep: true
    }
  },
  methods: {
    // 初始化图形配置
    initGraphConf() {
      if (this.graph) return;
      var width = this.$refs.container.scrollWidth;
      var height = this.$refs.container.scrollHeight || 500;
      this.graph = new G6.Graph({
        container: "container",
        // width: this.$refs.container.offsetWidth,
        // height: this.$refs.container.offsetHeight,
        width: width,
        height: height,
        fitView: true,
        minZoom: 0.5,
        maxZoom: 3,
        modes: {
          default: [
            "drag-canvas",
            "drag-node",
            "zoom-canvas",
            {
              type: "tooltip"
              // formatText: this.formatLabel
            }
          ]
        },
        layout: {
          type: "dagre",
          rankdir: "LR",
          nodesep: 3
          // align: "DL",
          // controlPoints: true

          // nodesepFunc: () => 1,
          // ranksepFunc: () => 1
        },
        defaultNode: {
          // size: [30, 20],
          type: "customCircle",
          // type: "circle",
          labelCfg: {
            // refY: 20,
            style: {
              fill: "#fff",
              fontSize: 12
            },
            position: "bottom"
          },
          style: {
            lineWidth: 0,
            // stroke: "#5B8FF9",
            fill: "#C6E5FF",
            cursor: "pointer"
          }
        },
        defaultEdge: {
          size: 1,
          color: "#A3B1BF",
          type: "cubic-horizontal"
          // style: {
          //   // endArrow: {
          //   //   path: "M 0,0 L 8,4 L 8,-4 Z",
          //   //   fill: "#000"
          //   // }
          // }
        }
      });
    },
    formatLabel(params) {},
    // 获取图表数据
    getChartData() {
      let data = this._.cloneDeep(this.inputData_in);
      if (this._.isEmpty(data)) data = { nodes: [], edges: [] };
      return data;
    },
    // 绘制图形
    paintChart() {
      const data = this.getChartData();
      if (this._.isEmpty(this.graph)) this.initGraphConf();
      this.graph.data(data);
      this.graph.render();
    },
    setData() {
      const data = this.getChartData();
      this.$nextTick(() => {
        if (this._.isEmpty(this.graph)) this.initGraphConf();
        this.graph.changeData(data);
        this.graph.layout();
      });
    },
    // 超长文本显示
    fittingString(str, maxWidth, fontSize) {
      const ellipsis = "...";
      const ellipsisLength = G6.Util.getTextSize(ellipsis, fontSize)[0];
      let currentWidth = 0;
      let res = str;
      const pattern = new RegExp("[\u4E00-\u9FA5]+"); // distinguish the Chinese charactors and letters
      if (!str) return;
      str.split("").forEach((letter, i) => {
        if (currentWidth > maxWidth - ellipsisLength) return;
        if (pattern.test(letter)) {
          // Chinese charactors
          currentWidth += fontSize;
        } else {
          // get the width of single letter according to the fontSize
          currentWidth += G6.Util.getLetterWidth(letter, fontSize);
        }
        if (currentWidth > maxWidth - ellipsisLength) {
          res = `${str.substr(0, i)}${ellipsis}`;
        }
      });
      return res;
    }
  },
  mounted() {
    const _this = this;
    const currentTheme = localStorage.getItem("omega_theme");
    let color = "";
    if (["dark", "blue"].includes(currentTheme)) {
      color = "#f0f1f2";
    } else {
      color = " #333333";
    }
    G6.registerNode("customCircle", {
      drawShape(cfg, group) {
        let img;
        if (cfg.nodeLabel === "busbarconnector") {
          img = busbarconnector;
        } else if (cfg.nodeLabel === "generator") {
          img = generator;
        } else if (cfg.nodeLabel === "powertransformer") {
          img = powertransformer;
        } else if (cfg.nodeLabel === "busbarsection") {
          img = busbarsection;
        }

        if (img) {
          group.addShape("image", {
            attrs: {
              x: -12,
              y: -12,
              width: 24,
              height: 24,
              img: img
            },
            draggable: true
          });
        } else {
          group.addShape("circle", {
            attrs: {
              x: 0,
              y: 0,
              r: 10,
              fill: cfg.style.fill
            },
            draggable: true
          });
        }
        group.addShape("text", {
          attrs: {
            textAlign: "center",
            x: 0,
            y: 30,
            fill: color,
            // text: cfg.label
            text: _this.fittingString(cfg.label, 200, 14)
          }
        });

        return group;
      },
      getAnchorPoints: function getAnchorPoints() {
        return [
          [0.5, 0.3],
          [0.5, 0.3]
        ];
      }
    });
    // console.log("---mounted", this.inputData_in && this.inputData_in.edges.length);
    this.$nextTick(() => {
      //   this.initGraphConf();
      this.paintChart();
    });
  },
  activated() {},
  deactivated() {
    // console.log("deactivated");
    // this.graph && this.graph.clear();
    // console.log(this.graph);
  },
  beforeDestroy() {
    // console.log("beforeDestroy");
    // this.graph && this.graph.destroy();
  }
};
</script>

<style lang="scss" scoped>
#container {
  width: 100%;
  height: 100%;
}
</style>
<style lang="scss">
.g6-tooltip {
  white-space: nowrap;

  transition: left 0.4s cubic-bezier(0.23, 1, 0.32, 1) 0s,
    top 0.4s cubic-bezier(0.23, 1, 0.32, 1) 0s;
  background-color: rgba(50, 50, 50, 0.7);
  font-size: 14px;
  border-radius: 4px;
  color: rgb(255, 255, 255);
  padding: 10px;
  // min-width: 300px;
  // min-height: 200px;
  pointer-events: none;
}
</style>
