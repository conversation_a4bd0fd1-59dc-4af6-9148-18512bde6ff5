<template>
  <div class="page flex-column">
    <el-tabs v-model="activeTab" class="eem-tabs-custom">
      <el-tab-pane
        v-for="(item, index) in tabList"
        :label="item.name"
        :name="item.id"
        :key="index"
        @tab-click="tabClick"
      ></el-tab-pane>
    </el-tabs>
    <div class="flex-auto">
      <metricaldimension
        v-if="activeTab === '1'"
        :mountedTag="mountedTag"
      ></metricaldimension>
      <metricalnode
        v-if="activeTab === '2'"
        :mountedTag="mountedTag"
      ></metricalnode>
    </div>
  </div>
</template>

<script>
import metricalnode from "../metricalnode";
import metricaldimension from "../metricaldimension";
export default {
  name: "multidimensional",
  components: { metricalnode, metricaldimension },
  data() {
    return {
      activeTab: "1",
      tabList: [
        { name: $T("维度配置"), id: "1" },
        { name: $T("属性关联"), id: "2" }
      ],
      mountedTag: Date.now()
    };
  },
  methods: {
    tabClick() {
      this.mountedTag = Date.now();
    }
  },
  activated() {
    this.activeTab = "1";
  },
  deactivated() {
    this.activeTab = null;
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  :deep() {
    .el-tabs__nav-wrap::after {
      display: none;
    }
    .el-tabs__nav-scroll {
      @include padding_left(J2);
      @include background_color(BG1);
    }
  }
}
</style>
