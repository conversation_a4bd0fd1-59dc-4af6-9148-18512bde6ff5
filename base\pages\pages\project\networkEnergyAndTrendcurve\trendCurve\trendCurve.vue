<template>
  <el-container class="padding0 flex-auto">
    <el-header height="auto" class="pJ3 eem-container mbJ3">
      <div class="fr" style="display: flex; align-items: center">
        <!-- 向前查询按钮 -->
        <CetButton
          class="eem-date-range-button"
          v-bind="CetButton_prv"
          v-on="CetButton_prv.event"
        ></CetButton>
        <el-date-picker
          class="mlJ mrJ"
          v-model="CetDatePicker_time.val"
          v-bind="CetDatePicker_time.config"
          :disabled="CetDatePicker_time.disable_in"
          placeholder="选择日期"
        ></el-date-picker>
        <!-- 向后查询按钮 -->
        <CetButton
          class="eem-date-range-button"
          v-bind="CetButton_next"
          v-on="CetButton_next.event"
        ></CetButton>
        <!-- 请填写组件含义按钮组件 -->
        <CetButton
          class="mlJ1"
          v-bind="CetButton_empty"
          v-on="CetButton_empty.event"
        ></CetButton>
        <CetButton
          class="mlJ1"
          v-bind="CetButton_export"
          v-on="CetButton_export.event"
        ></CetButton>
      </div>
    </el-header>
    <el-container v-show="!showTrend" class="padding0 fullheight text-center">
      <p class="info">请选择设备。</p>
    </el-container>
    <el-container
      v-show="showTrend"
      style="
        height: 100%;
        overflow: hidden;
        margin: 0 0 0 -15px;
        padding: 0 0 0 15px;
      "
    >
      <el-aside
        style="width: 560px"
        class="sub-aside mrJ3 eem-aside"
        :class="collapseAside ? 'show-aside' : 'hide-aside'"
      >
        <el-collapse
          class="collapse"
          v-model="activeDevices"
          accordion
          @change="changeDevices"
        >
          <el-collapse-item
            class="aside-collapse"
            v-for="item in pecDeviceList[tree_id]"
            :name="item.measuredby"
            :key="item.modelLabel + '_' + item.id"
          >
            <template slot="title">
              <el-tooltip :content="item.deviceName" effect="light">
                <div class="text-ellipsis">{{ item.deviceName }}</div>
              </el-tooltip>
            </template>
            <template>
              <StationList
                :deviceID="item.measuredby"
                :checkedStations.sync="checkedStations[item.measuredby]"
                @changeStation="
                  val => changeStation(val, item.deviceName, item.measuredby)
                "
                :stations="stations[item.measuredby]"
              ></StationList>
            </template>
            <!-- {{activeDevices}}--{{typeof activeDevices}} -->
          </el-collapse-item>
        </el-collapse>
      </el-aside>
      <div
        class="page-main"
        :style="{
          marginLeft: collapseAside ? '0' : '-560px',
          width: collapseAside ? 'calc(100% - 560px)' : '100%'
        }"
      >
        <div class="collapse-aside" @click="collapseAside = !collapseAside">
          <i
            :class="
              collapseAside ? 'el-icon-arrow-left' : 'el-icon-arrow-right'
            "
          ></i>
        </div>
        <div
          class="fullheight brC1 bg1 ptJ3 pbJ3"
          style="box-sizing: border-box"
        >
          <CetTrend
            ref="trend"
            :queryMode="CetTable_realTime.queryMode"
            :dataConfig="CetTable_realTime.dataConfig"
            :queryTime_in="CetTable_realTime.queryTime_in"
            :params_in="CetTable_realTime.params_in"
            :queryTrigger_in="CetTable_realTime.queryTrigger_in"
            :clearTrigger_in="CetTable_realTime.clearTrigger_in"
            v-bind="CetTable_realTime.config"
          ></CetTrend>
        </div>
      </div>
    </el-container>
  </el-container>
</template>
<script>
import StationList from "./StationList";
import common from "eem-utils/common";
import { PEC_DATA_TYPE } from "@/store/constQuantity";
import { httping } from "@omega/http";
export default {
  name: "TrendCurve",
  components: {
    StationList
  },
  props: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    asideClass() {
      return "hide-aside";
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    systemCfg() {
      return this.$store.state.systemCfg;
    }
  },

  data() {
    return {
      refreshProjectTree: Date.now(),
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: true,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: true,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      //导出按钮
      CetButton_export: {
        visible_in: true,
        disable_in: true,
        title: "导出",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      // 清空按钮
      CetButton_empty: {
        visible_in: true,
        disable_in: true,
        title: "清空",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_empty_statusTrigger_out
        }
      },
      // 请填写组件含义组件
      CetDatePicker_time: {
        disable_in: true, //禁用
        val: new Date(),
        config: {
          clearable: false,
          valueFormat: "yyyy-MM-dd",
          type: "date", //date daterange
          format: "yyyy-MM-dd",
          rangeSeparator: "-",
          size: "small",
          style: {
            width: "140px"
          }
        }
      },
      CetTable_realTime: {
        queryMode: "diff",
        dataConfig: {
          queryUrl: "/device-data-service/api/v1/batch/datalog/span/group",
          // queryUrl: `/eem-service/v2/peccore/datalog?cacheInterval=0&cacheMonthNumber=3&dbInterval=0`,
          type: "POST"
        },
        queryTrigger_in: new Date().getTime(),
        clearTrigger_in: new Date().getTime(),
        queryTime_in: {
          timeType: 1,
          time: common.initDateRange().map(item => new Date(item))
        },
        title_in: "趋势曲线",
        params_in: [],
        config: {
          splitLine: false,
          showTableButton: true,
          showLegend: true,
          showExtremButton: true,
          showAverageButton: true,
          showPointButton: true,
          color: [
            "#5DE27C",
            "#99CCF4",
            "#F2E38C",
            "#EC3B3B",
            "#FFC8D5",
            "#FF8541"
          ]
        }
      },
      pecDeviceList: {
        project_0: []
      },
      activeDevices: "",
      checkedStations: {},
      // oldCheckedStations: {},
      stations: {},
      collapseAside: false,
      queryTime: common.initDateRange(),
      currentNode: null,
      showTrend: false,
      tree_id: "project_0"
    };
  },
  watch: {
    queryTime: {
      deep: true,
      handler: function (val, oldVal) {
        if (this._.isEmpty(this.CetTable_realTime.params_in)) return;
        this.CetTable_realTime.queryTime_in.time = val.map(
          item => new Date(item)
        );
      }
    },
    "CetDatePicker_time.val": function (val) {
      if (val) {
        const date = this.$moment(val);
        this.queryTime = [
          date.startOf("d").valueOf(),
          date.endOf("d").valueOf() + 1
        ];
      }
    },
    // checkedStations: {
    //   deep: true,
    //   handler: function(val, oldVal) {
    //     // console.log("checkedStations", val, oldVal);
    //     let len = this.getAllCheckLength(val);
    //     if (len > 6) {
    //       return;
    //     }
    //     this.oldCheckedStations = this._.cloneDeep(val);
    //   }
    // },
    // oldCheckedStations: {
    //   deep: true,
    //   handler: function(val, oldVal) {
    //     // console.log("oldCheckedStations", val, oldVal);
    //     if (this._.isEqual(val, oldVal)) return;
    //     let points = [];
    //     this._.keys(val).forEach(key => {
    //       val[key].forEach(item => {
    //         let device = this._.find(this.pecDeviceList, { measuredby: item.deviceId });
    //         points.push({
    //           dataId: item.dataId,
    //           dataName: item.paraName,
    //           dataTypeId: item.dataTypeId,
    //           dataTypeName: PEC_DATA_TYPE[item.dataTypeId],
    //           deviceId: item.deviceId,
    //           deviceName: device.name,
    //           logicalId: item.logicalDeviceIndex
    //         });
    //       });
    //     });
    //     this.CetTable_realTime.params_in = points;
    //   }
    // },
    currentNode: {
      deep: true,
      handler: function (val, oldVal) {}
    }
  },

  methods: {
    getAllCheckLength(val) {
      let len = 0;
      this._.keys(val).forEach(key => (len += val[key].length));
      return len;
    },
    setParamsIn(val) {
      const _this = this;
      const points = [];
      this._.keys(val).forEach(key => {
        val[key].forEach(item => {
          let device = {};
          _this._.keys(_this.pecDeviceList).forEach(key1 => {
            const findDevice = _this._.find(_this.pecDeviceList[key1], {
              measuredby: item.deviceId
            });
            if (findDevice) {
              device = findDevice;
            }
          });
          points.push({
            dataId: item.dataId,
            dataTypeId: item.dataTypeId,
            deviceId: item.deviceId,
            logicalId: item.logicalDeviceIndex,
            dataName: item.paraName,
            dataTypeName: PEC_DATA_TYPE[item.dataTypeId],
            deviceName: device.deviceName,
            unit: ""
          });
        });
      });
      this.CetTable_realTime.params_in = points;
    },
    CetTree_1_currentNode_out(val) {
      if (this._.isEmpty(val)) {
        this.collapseAside = false;
        this.currentNode = null;
        this.showTrend = false;
      } else {
        this.tree_id = val.tree_id;
        this.currentNode = val;
        this.getPecDevice(val);
        // this.collapseAside = true;
        this.showTrend = true;
      }
    },
    CetButton_prv_statusTrigger_out(val) {
      const date = this.$moment(this.CetDatePicker_time.val);
      this.CetDatePicker_time.val = date.subtract(1, "d")._d;
    },
    CetButton_next_statusTrigger_out(val) {
      const date = this.$moment(this.CetDatePicker_time.val);
      this.CetDatePicker_time.val = date.add(1, "d")._d;
    },
    //导出
    CetButton_export_statusTrigger_out(val) {
      const dataIdNameList = [];
      const meterConfigs = [];
      this._.each(this.CetTable_realTime.params_in, item => {
        dataIdNameList.push({
          dataId: item.dataId,
          dataName: item.dataName
        });
        meterConfigs.push({
          dataId: item.dataId,
          dataTypeId: item.dataTypeId,
          deviceId: item.deviceId,
          logicalId: item.logicalId,
          name: `${item.deviceName}-${item.dataName}-${item.dataTypeName}`
        });
      });
      const params = {
        // dataIdNameList,
        //需要组件支持
        // pictures: [this.$refs.trend.getDataURL()],
        // pictures: [this.$refs.trend.$refs.chart.getDataURL()],
        // trendSearchListVo: {
        endTime: this.$moment(this.queryTime[1]).format("YYYY-MM-DD HH:mm:ss"), // this.CetTable_realTime.queryTime_in.time[1],
        interval: 0,
        meterConfigs,
        startTime: this.$moment(this.queryTime[0]).format("YYYY-MM-DD HH:mm:ss") //this.CetTable_realTime.queryTime_in.time[0]
        // }
      };
      if (!this.$refs.trend.showTableSelect) {
        params.pictures = [this.$refs.trend.$refs.chart.getDataURL()];
      }
      let url = "";
      if (this._.get(this.$store.state, "systemCfg.trendcurveBuffer")) {
        url =
          "/eem-service/v2/peccore/datalog/export?cacheInterval=0&cacheMonthNumber=3&dbInterval=0";
      } else {
        url =
          "/eem-service/v2/peccore/datalog/export?cacheInterval=0&cacheMonthNumber=0&dbInterval=0";
      }
      common.downExcel(url, params, this.token);
    },
    CetButton_empty_statusTrigger_out(val) {
      this.checkedStations = {};
      this.CetTable_realTime.clearTrigger_in = new Date().getTime();
      this.CetDatePicker_time.disable_in = true;
      this.CetButton_prv.disable_in = true;
      this.CetButton_next.disable_in = true;
      this.CetButton_export.disable_in = true;
      this.CetButton_empty.disable_in = true;
    },
    changeStation(checkArr, devname, measuredby) {
      // console.log("changeStation", checkArr, devname, measuredby);
      const len = this.getAllCheckLength(this.checkedStations);
      if (len === 0) {
        this.CetDatePicker_time.disable_in = true;
        this.CetButton_prv.disable_in = true;
        this.CetButton_next.disable_in = true;
        this.CetButton_export.disable_in = true;
        this.CetButton_empty.disable_in = true;
      } else {
        this.CetDatePicker_time.disable_in = false;
        this.CetButton_prv.disable_in = false;
        this.CetButton_next.disable_in = false;
        this.CetButton_export.disable_in = false;
        this.CetButton_empty.disable_in = false;
      }
      if (len > 6) {
        this.$message("最多支持6个测点查询");
        const arr = this.checkedStations[measuredby];
        this.checkedStations[measuredby] = arr.slice(0, arr.length - 1);
      } else {
        this.setParamsIn(this.checkedStations);
      }
      // return;
      // this.CetTable_realTime.params_in = checkArr.map(item => {
      //   return {
      //     dataId: item.dataId,
      //     dataName: item.paraName,
      //     dataTypeId: item.dataTypeId,
      //     dataTypeName: PEC_DATA_TYPE[item.dataTypeId],
      //     deviceId: item.deviceId,
      //     deviceName: devname,
      //     logicalId: item.logicalDeviceIndex
      //   };
      // });
    },
    getPecDevice(node) {
      const params = [
        {
          id: node.id,
          modelLabel: node.modelLabel,
          name: node.name
        }
      ];
      httping({
        url: `/eem-service/v1/connect/deviceInfo`,
        method: "POST",
        data: params
      }).then(res => {
        const data = this._.get(res, "data", []);
        this.pecDeviceList[this.tree_id] = [];
        if (this._.isEmpty(data))
          return (this.pecDeviceList[this.tree_id] = []);
        this.initStations(data);
        this.activeDevices = data[0].measuredby;
        this.getPoints(data[0].measuredby);
        this.pecDeviceList[this.tree_id] = data;
        this.collapseAside = true;
      });
    },
    initStations(devs) {
      if (!this.stations) {
        this.stations = {};
      }
      if (!this.checkedStations) {
        this.checkedStations = {};
      }

      // this.oldCheckedStations = {};

      devs.forEach(item => {
        if (!this.stations[item.measuredby]) {
          this.$set(this.stations, item.measuredby, []);
        }
        if (!this.checkedStations[item.measuredby]) {
          this.$set(this.checkedStations, item.measuredby, []);
        }
        // this.$set(this.oldCheckedStations, item.measuredby, []);
      });
    },
    getPoints(id) {
      httping({
        url: `/device-data-service/api/comm/v1/device/datalog/points`,
        method: "POST",
        data: [id]
      }).then(res => {
        // console.log(res);
        this.stations[id] = res.data[id];
      });
    },
    changeDevices(val) {
      // console.log("changeDevices", val);
      if (!val) return;
      // if (this._.isEmpty(this.stations[val])) {
      this.getPoints(val);
      // }

      // this.checkedStations[val] = [];
      // this.CetTable_realTime.params_in = [];
      // val && this._.isEmpty(this.stations[val]) && this.getPoints(val);
    },
    init() {
      if (this._.get(this.$store.state, "systemCfg.trendcurveBuffer")) {
        this.CetTable_realTime.dataConfig.queryUrl =
          "/eem-service/v2/peccore/datalog?cacheInterval=0&cacheMonthNumber=3&dbInterval=0";
      } else {
        this.CetTable_realTime.dataConfig.queryUrl =
          "/eem-service/v2/peccore/datalog?cacheInterval=0&cacheMonthNumber=0&dbInterval=0";
      }
      this.checkedStations = {};
      this.stations = {};
      this.CetTable_realTime.clearTrigger_in = new Date().getTime();
      this.CetDatePicker_time.disable_in = true;
      this.CetButton_prv.disable_in = true;
      this.CetButton_next.disable_in = true;
      this.CetButton_export.disable_in = true;
      this.CetButton_empty.disable_in = true;
    }
  },
  mounted() {
    this.init();
  },
  activated() {
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.sub-aside {
  position: relative;
  transition: all 500ms;
  &.show-aside {
    transform: translateX(0);
  }
  &.hide-aside {
    transform: translateX(-100%);
  }
  :deep(.el-collapse-item__wrap) {
    border-bottom: none;
  }
  :deep(.el-collapse-item__content) {
    padding-bottom: 10px;
  }
}
.page-main {
  position: relative;
  transition: all 500ms;
  width: 100%;
}
.collapse-aside {
  position: absolute;
  left: -15px;
  top: 50%;
  width: 13px;
  height: 80px;
  line-height: 80px;
  transform: translateY(-50%);
  cursor: pointer;
  @include background_color(ZS);
  border-radius: 4px 0px 0px 4px;
  color: #fff;
}

.text-center {
  text-align: center;
  width: 100%;
  height: 100%;
  position: relative;
  .info {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    height: 50px;
    line-height: 50px;
    margin: auto;
    @include font_size(H1);
  }
}
.collapse {
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
}
</style>
<style lang="scss">
.aside-collapse {
  .el-collapse-item__header {
    height: 35px;
    line-height: 35px;
    padding: 0 10px;
  }
  .el-collapse-item__header.is-active {
    @include background_color(BG4);
  }
}
</style>
