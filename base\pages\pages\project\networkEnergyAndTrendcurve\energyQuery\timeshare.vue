<template>
  <CetChart
    @click="chartClick2"
    :inputData_in="CetChart_2.inputData_in"
    v-bind="CetChart_2.config"
  />
</template>

<script>
import common from "eem-utils/common";
import { httping } from "@omega/http";
export default {
  props: {
    queryBody_in: {
      type: Object
    },
    energy_in: {
      type: Object
    },
    CustomDatePicker_1: {
      type: Object
    },
    ElCheckboxGroup_2: {
      type: Object
    },
    queryTrigger_in: {
      type: Number
    }
  },
  data() {
    return {
      CetChart_2: {
        inputData_in: null,
        config: {
          options: {
            tooltip: {
              trigger: "axis",
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
              }
            },
            legend: {
              data: []
            },
            grid: {
              left: "3%",
              right: "4%",
              bottom: "3%",
              containLabel: true
            },
            dataset: {
              source: []
            },
            xAxis: { type: "category" },
            yAxis: {},
            series: []
          }
        }
      }
    };
  },
  watch: {
    queryTrigger_in() {
      this.queryData();
    }
  },
  methods: {
    queryData() {
      let _this = this;
      if (!this.queryBody_in) {
        _this.filChartData2([]);
        return;
      }
      let queryBody = this.queryBody_in;
      this.CetChart_2.config.options = {};
      var queryOption = {
        url: `/eem-service/v1/energy/energydata/time/timeshare`,
        method: "POST",
        data: queryBody
      };

      httping(queryOption).then(function (response) {
        if (response.code === 0) {
          //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
          var data = _this._.get(response, ["data", "0"], {});
          _this.filChartData2(data);
        }
      });
    },
    filChartData2(data) {
      let customIndex = 1;
      this.CetChart_2.config.options = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          },
          formatter: function (val) {
            var list = val || [];
            var formatterStr = "";
            var formatVal = _this.getFormatVal();
            if (_this.ElCheckboxGroup_2.value.length > 0) {
              formatVal = false;
            }
            for (var i = 0, len = list.length; i < len; i++) {
              if (i === 0) {
                formatterStr += `${val[i].name}`;
              }
              formatterStr += `<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                val[i].color
              };"></span>${
                formatVal
                  ? _this.$moment(val[i].data.time).format(formatVal)
                  : val[i].seriesName
              } : ${val[i].value || "--"}(${val[i].data.unit || "--"})`;
            }
            return formatterStr;
          }
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true
        },
        legend: {
          data: []
        },
        // dataset: {
        //   source: []
        // },
        xAxis: {
          type: "category",
          data: []
        },
        yAxis: {
          type: "value",
          nameTextStyle: {
            align: "left"
          }
        },
        series: []
      };
      var _this = this,
        energyDatas = data.energyDatas || [],
        touDatas = data.touDatas || [],
        legend = [],
        series = [];
      let leng = energyDatas.length;

      if (energyDatas.length === 0) {
        return;
      }

      let yAxisData1 = [];
      let xAxisData = [];

      customIndex = this.getMarginType(
        energyDatas[0].time,
        energyDatas[leng - 1].time
      );

      var source = [];

      let unit = data.symbol || "--";
      var ElOption_1Text;
      var showTimeSharing =
        this.ElCheckboxGroup_2.value.length > 0 ? true : false;
      if (showTimeSharing) {
        energyDatas.forEach(item => {
          var obj = {};
          obj.time = item.time;
          obj.product = this.getAxixs(item.time, customIndex);
          touDatas.forEach(item1 => {
            var tou = item1.tou || [];
            for (var i = 0, len = tou.length; i < len; i++) {
              if (item.time == tou[i].time) {
                obj[`yAxis${item1.segId}`] = common.formatNumberWithPrecision(
                  tou[i].value,
                  2
                );
              }
            }
          });
          source.push(obj);
        });

        source.forEach(item => {
          xAxisData.push(item.product);
        });
        for (var i = 0, len = touDatas.length; i < len; i++) {
          var yAxisData = [];
          source.forEach(item => {
            // yAxisData.push(item[`yAxis${touDatas[i].segId}`]);
            yAxisData.push({
              name: item["product"],
              time: item["time"],
              unit: unit,
              value: item[`yAxis${touDatas[i].segId}`]
            });
          });
          var obj = {
            // name: touDatas[i].segName,
            name: touDatas[i].segId,
            type: "bar",
            stack: "总量",
            // encode: { x: "product", y: `yAxis${touDatas[i].segId}` }
            data: yAxisData
          };
          // legend.push(touDatas[i].segName);
          legend.push(touDatas[i].segId);
          series.push(obj);
        }
        if (series && series.length > 0) {
          series[0].data.forEach(item => {
            if (Number(item.value) < 0) {
              item.value = 0;
            }
          });
        }

        if (showTimeSharing) {
          ElOption_1Text = _this.energy_in && _this.energy_in.text;
        }
        _this.CetChart_2.config.options.yAxis.name = ElOption_1Text
          ? `${ElOption_1Text}（${unit})`
          : "";

        this.CetChart_2.config.options.xAxis.data = xAxisData;
        this.CetChart_2.config.options.series = series;
        this.CetChart_2.config.options.legend.data = legend;
      } else {
        energyDatas.forEach(item => {
          var product = this.getAxixs(item.time, customIndex);
          var value = common.formatNumberWithPrecision(item.value, 2);
          var obj = {};
          obj.time = item.time;
          obj.product = product;
          obj.yAxis = value;
          source.push(obj);
          xAxisData.push(product);
          yAxisData1.push({
            name: product,
            time: item.time,
            unit,
            value: value
          });
        });

        var name = this.getLegend(this.CustomDatePicker_1.queryTime.startTime);
        legend.push(name);
        series.push({
          name: name,
          type: "bar",
          // encode: { x: "product", y: `yAxis` }
          data: yAxisData1
        });
        series[0].data.forEach(item => {
          if (Number(item.value) < 0) {
            item.value = 0;
          }
        });
        ElOption_1Text = _this.energy_in && _this.energy_in.text;
        _this.CetChart_2.config.options.yAxis.name = ElOption_1Text
          ? `${ElOption_1Text}（${unit})`
          : "";

        this.CetChart_2.config.options.xAxis.data = xAxisData;
        this.CetChart_2.config.options.series = series;
        this.CetChart_2.config.options.legend.data = legend;
      }
    },
    chartClick2(val) {
      this.$emit("click", val);
    },
    getFormatVal() {
      //17年，14月，12日，20自定义
      var cycle = this.CustomDatePicker_1.queryTime.cycle;
      switch (cycle) {
        case 17:
          return "YYYY";
        case 14:
          return "YYYY-MM";
        case 12:
          return "YYYY-MM-DD";
        default:
          return false;
      }
    },
    //获取图表图例
    getLegend(pDate, customIndex) {
      let sFormat = "";
      let cycle = this.CustomDatePicker_1.queryTime.cycle;
      if (cycle === 12) {
        sFormat = "YYYY-MM-DD";
      } else if (cycle === 14) {
        sFormat = "YYYY-MM";
      } else if (cycle === 17) {
        sFormat = "YYYY";
      } else if (cycle === 20) {
        if (customIndex === 1) {
          sFormat = "HH";
        } else if (customIndex === 2) {
          sFormat = "YYYY-MM";
        } else if (customIndex === 3) {
          sFormat = "YYYY";
        } else {
          sFormat = "YYYY";
        }
      }

      return this.$moment(pDate).format(sFormat);
    },
    getMarginType(iStartTime1, iEndTime) {
      let iMarginDay = this.$moment(iEndTime).subtract(2, "day");
      let iMarginMonth = this.$moment(iEndTime).subtract(3, "month");
      let iMarginYear = this.$moment(iEndTime).subtract(3, "year");
      if (iStartTime1 > iMarginDay) {
        return 1;
      } else if (iStartTime1 > iMarginMonth) {
        return 2;
      } else if (iStartTime1 > iMarginYear) {
        return 3;
      } else {
        return 4;
      }
    },
    //过滤获取图表x轴对应值
    getAxixs(pDate, customIndex) {
      let oDate = this.$moment(pDate);
      let type = this.CustomDatePicker_1.queryTime.cycle;
      if (type === 12) {
        if (oDate.format("HH:mm") == "00:00") {
          return oDate.format("DD");
        }
        return oDate.format("HH:mm");
      } else if (type === 14) {
        return oDate.format("DD");
      } else if (type === 17) {
        return oDate.format("MM");
      } else if (type === 20) {
        if (customIndex === 1) {
          if (oDate.format("HH:mm") == "00:00") {
            return oDate.format("DD");
          }
          return oDate.format("HH:mm");
        } else if (customIndex === 2) {
          return oDate.format("DD");
        } else if (customIndex === 3) {
          return oDate.format("MM");
        } else if (customIndex === 4) {
          return oDate.format("YYYY");
        }
      }
      return oDate.format("YYYY-MM-DD");
    }
  }
};
</script>

<style></style>
