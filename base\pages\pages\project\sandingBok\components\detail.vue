<template>
  <div>
    <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
      <el-main class="eem-cont-c1 fullheight">
        <CetForm
          :data.sync="CetForm_1.data"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
          class="form"
          ref="cetForm"
        >
          <el-form-item
            :label="`${item.alias}：`"
            :prop="item.name"
            v-for="(item, index) in templateArr"
            :key="index"
          >
            <el-tooltip
              placement="top"
              v-if="item.datatype == 'string'"
              :content="CetForm_1.data[item.name]"
            >
              <div class="text-ellipsis text">
                {{ CetForm_1.data[item.name] || "--" }}
              </div>
            </el-tooltip>
            <div class="text-ellipsis text" v-if="item.datatype == 'int4'">
              {{
                CetForm_1.data[item.name] || CetForm_1.data[item.name] === 0
                  ? CetForm_1.data[item.name].toFixed2(0)
                  : "--"
              }}
            </div>
            <div class="text-ellipsis text" v-if="item.datatype == 'int8'">
              {{
                CetForm_1.data[item.name] || CetForm_1.data[item.name] === 0
                  ? CetForm_1.data[item.name].toFixed2(0)
                  : "--"
              }}
            </div>
            <div class="text-ellipsis text" v-if="item.datatype == 'float'">
              {{
                CetForm_1.data[item.name] || CetForm_1.data[item.name] === 0
                  ? CetForm_1.data[item.name].toFixed2(2)
                  : "--"
              }}
            </div>
            <div class="text-ellipsis text" v-else-if="item.datatype == 'date'">
              {{
                CetForm_1.data[item.name]
                  ? $moment(CetForm_1.data[item.name]).format("YYYY-MM-DD")
                  : "--"
              }}
            </div>
            <div
              class="text-ellipsis text"
              v-else-if="item.datatype == 'boolean'"
            >
              {{
                CetForm_1.data[item.name] === true
                  ? $T("是")
                  : CetForm_1.data[item.name] === false
                  ? $T("否")
                  : "--"
              }}
            </div>
            <el-tooltip
              placement="top"
              v-else-if="
                item.datatype == 'enum' ||
                (item.enumerationvalue && item.enumerationvalue.length)
              "
              :content="
                item.enumerationvalue.find(
                  i => i.id === CetForm_1.data[item.name]
                )
                  ? item.enumerationvalue.find(
                      i => i.id === CetForm_1.data[item.name]
                    ).text
                  : '--'
              "
            >
              <div class="text-ellipsis text">
                {{
                  item.enumerationvalue.find(
                    i => i.id === CetForm_1.data[item.name]
                  )
                    ? item.enumerationvalue.find(
                        i => i.id === CetForm_1.data[item.name]
                      ).text
                    : "--"
                }}
              </div>
            </el-tooltip>
            <div
              :class="{
                'text-ellipsis': true,
                text: true,
                imgBox: true,
                imgBoxHover: CetForm_1.data[item.name] ? true : false
              }"
              v-else-if="item.datatype == 'img'"
            >
              <img :src="imgSrc" :alt="$T('暂无图片')" />
              <div class="masking">
                <div>
                  <div>{{ getImgName(CetForm_1.data, item.name) }}</div>
                  <div>
                    <i
                      class="el-icon-download"
                      @click="
                        download(
                          `${getImgName(CetForm_1.data, item.name)}${getSuffix(
                            CetForm_1.data[item.name]
                          )}`,
                          CetForm_1.data[item.name + 'blob']
                        )
                      "
                    ></i>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </CetForm>
      </el-main>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import FileSaver from "file-saver";
export default {
  name: "templateAdmin",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    template_in: {
      type: Array,
      default() {
        return [];
      }
    },
    data_in: {
      type: Object,
      default() {
        return {};
      }
    },
    actionTitle_in: {
      //1 仪表台账  2检定记录
      type: Number
    }
  },
  data() {
    return {
      templateArr: [],
      // 1表单组件
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "180px",
        rules: {},
        event: {},
        inline: true,
        labelPosition: "left"
      },
      // 1弹窗组件
      CetDialog_1: {
        title: $T("设备详情"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      imgSrc: ""
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.imgSrc = null;
      vm.CetDialog_1.openTrigger_in = val;
      vm.createCondition();
      if (vm.data_in) {
        vm.CetForm_1.data = vm._.cloneDeep(vm.data_in);
        vm.CetDialog_1.title =
          vm.actionTitle_in === 1 ? $T("设备详情") : $T("检定记录详情");
        vm.getImgUrl(vm.CetForm_1.data.certiication);
      } else {
        vm.CetForm_1.data = {};
      }
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },
  methods: {
    getImgUrl(uploadPath) {
      var me = this;
      if (!uploadPath) {
        return;
      }
      var url = "/eem-service/v1/common/downloadFile?path=" + uploadPath;
      const xhr = new XMLHttpRequest();
      xhr.open("GET", url, true);
      xhr.responseType = "blob";
      xhr.setRequestHeader("Authorization", this.token);
      xhr.onload = () => {
        if (
          xhr.status === 200 &&
          xhr.response.type === "application/x-download"
        ) {
          //将图片信息放到Img中
          me.imgSrc = window.URL.createObjectURL(xhr.response);
        }
      };

      xhr.send();
    },
    // 获取文件后缀
    getSuffix(url) {
      if (!url) {
        return;
      }
      return `.${url.split(".")[1]}`;
    },
    // 获取文件名称
    getImgName(data, prop) {
      if (this.actionTitle_in === 2) {
        if (!data.certiicationname) {
          return "--";
        }
        return data.certiicationname.split(".")[0];
      } else {
        if (!data[prop]) {
          return "--";
        }
        return data[prop];
      }
    },
    // 构造查询表单 过滤出启用字段
    createCondition() {
      this.templateArr = this.template_in.filter(
        item => !item.allowdeactivation || item.active
      );
    },
    download(name, blob) {
      FileSaver.saveAs(blob, name);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },
  activated: function () {}
};
</script>
<style lang="scss" scoped>
.form {
  .el-form-item {
    width: calc(50% - 10px);
  }
  :deep(.el-form-item__content) {
    width: calc(100% - 180px);
    padding-right: 20px;
    box-sizing: border-box;
  }
}
.text {
  text-align: right;
}
.imgBox {
  position: relative;
  img {
    width: 100%;
    height: 100%;
  }
  .masking {
    position: absolute;
    top: 0;
    display: none;
    opacity: 0.7;
    background: #000;
    width: 100%;
    height: 100%;
    text-align: center;
    & > div {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      height: 50px;
      width: 100%;
      margin: auto;
      & > div {
        .el-icon-download {
          font-size: 30px;
          cursor: pointer;
        }
      }
    }
  }
}
.imgBoxHover:hover .masking {
  display: block;
}
</style>
