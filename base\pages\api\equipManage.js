import store from "@/store";
import fetch from "eem-utils/fetch";
import Axios from "axios";
import { FullScreenLoading } from "@omega/http/loading.js";
const version = "v1";
const loading = new FullScreenLoading();
function processRequestByInterFace(data) {
  // 对 data 进行任意转换处理
  const conditions = _.get(data, "rootCondition.filter.expressions", []);
  const page = _.get(data, "rootCondition.page");
  const result = {};
  conditions.forEach(item => {
    result[item.prop] = item.limit;
  });
  result.page = page;
  return result;
}

// 设备管理
// 查询管网设备信息
export function queryPipeDevicelabels() {
  return fetch({
    url: `/eem-service/${version}/common/pipeDevice/labels`,
    method: "GET"
  });
}

// 查询设备列表信息
export function queryDeviceNodeData(data) {
  return fetch({
    url: `/eem-service/${version}/device/nodedata`,
    method: "POST",
    data: processRequestByInterFace(data)
  });
}

// 查询设备列表信息
export function queryDeviceNodeData1(data) {
  return fetch({
    url: `/eem-service/${version}/device/nodedata`,
    method: "POST",
    data
  });
}

// 查询设备模板信息
export function queryDeviceTemplateInfo(data) {
  return fetch({
    url: `/eem-service/${version}/device/TemplateInfo`,
    method: "POST",
    data
  });
}

// 查询设备模板信息
export function queryTemplateTree() {
  return fetch({
    url: `/eem-service/${version}/device/template/templateNodeTree`,
    method: "GET"
  });
}

// 查询设备技术参数
export function queryDeviceTechParam(data) {
  return fetch({
    url: `/eem-service/${version}/device/TechParam`,
    method: "POST",
    data
  });
}

// 写入更新设备技术参数
export function editDeviceTechParam(data) {
  return fetch({
    url: `/eem-service/${version}/device/TechParamValue`,
    method: "PUT",
    data
  });
}

// 保存文件技术参数上传文件
export function editManageNode(data) {
  return fetch({
    url: `/eem-service/${version}/project/manageNode`,
    method: "PUT",
    data
  });
}
// 编辑设备信息里面，修改技术参数和保护参数;--已和张鹏进行确认过
export function editUpdateNode(data) {
  return fetch({
    url: `/eem-service/${version}/project/updateNode`,
    method: "PUT",
    data
  });
}

// 查询测点的dataId和logicalId
export function queryDataServiceParam(id, data) {
  return fetch({
    url: `/eem-service/${version}/device/dataServiceParam?groupId=${id}`,
    method: "POST",
    data
  });
}

// 根据dataId和logicalId查询实时数据
export function queryDeviceEquipmentData(id, data) {
  return fetch({
    url: `/device-data-service/api/${version}/realtimedata/${id}`,
    method: "POST",
    data
  });
}

// 查设备的零件库
export function queryComponent(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/componentByEquipment`,
    method: "POST",
    data
  });
}

// 新增零件
export function addComponents(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/`,
    method: "POST",
    data
  });
}

// 编辑零件
export function editComponents(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/`,
    method: "PUT",
    data
  });
}

// 删除零件
export function deleteComponents(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/`,
    method: "DELETE",
    data
  });
}

// 查历史数据
export function queryHistoryData(id, data) {
  return fetch({
    url: `/eem-service/v2/peccore/mobile/datalog`,
    method: "POST",
    data
  });
}

function generateImgUrl(url, data, method) {
  loading.showLoading();
  const config = {
    responseType: "blob"
  };

  if (store.state.token) {
    config.headers = {
      Authorization: `Bearer ${store.state.token}`
    };
  }

  const p = new Promise((resolve, reject) => {
    let instance = null;
    if (method === "POST") {
      instance = Axios.post(url, data, config);
    } else {
      instance = Axios.get(url, config);
    }

    instance
      .then(res => {
        loading.hideLoading();
        // 判断响应头，如果响应头是json格式的说明有异常
        if (res.headers["content-type"].indexOf("application/json") > -1) {
          this.$message({
            message: "获取图片异常！",
            showClose: true,
            type: "error"
          });
          return;
        }

        // 将图片信息放到Img中
        const obejectURL = window.URL.createObjectURL(res.data);
        resolve(obejectURL);
      })
      .catch(() => {
        loading.hideLoading();
        this.$message({
          message: $T("请检查网络是否连接正常"),
          showClose: true,
          type: "error"
        });
      });
  });

  return p;
}

// 查询设备二维码
export function downloadQrCode(data) {
  /* return fetch({
    url: `/eem-service/${version}/device/downloadQrCode`,
    method: "POST",
    data,
    headers: { "User-ID": 1 },
  }); */
  const url = `/eem-service/${version}/device/downloadQrCode`;
  const method = "POST";
  return generateImgUrl(url, data, method);
}

// 设备详情查零件
export function queryComponentByEquipment(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/componentByEquipment`,
    method: "POST",
    data
  });
}

// 导出零件
export function exportComponents() {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/ouputComponents`,
    method: "POST"
  });
}

// 导入零件
export function importComponents() {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/importComponents`,
    method: "POST"
  });
}

// 查工单记录
export function queryWorkOrder(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/workOrder`,
    method: "POST",
    data: processRequestByInterFace(data)
  });
}

// 查工单数量
export function queryWorkOrderNumber(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/workOrder/count`,
    method: "POST",
    data
  });
}

// 查询设备事件记录
export function queryPeccoreeventLog(data) {
  return fetch({
    url: `/eem-service/${version}/event/peccore/eventLog`,
    method: "POST",
    data: processRequestByInterFace(data)
  });
}

// 查设备事件数量
export function queryPeccoreeventLogNumber(data) {
  return fetch({
    url: `/eem-service/${version}/event/peccore/count/eventLevel`,
    method: "POST",
    data
  });
}

// 查设备收敛事件数量
export function queryConvergenceeventLogNumber(data) {
  return fetch({
    url: `/eem-service/${version}/event/convergence/count/eventLevel`,
    method: "POST",
    data
  });
}

// 根据具体设备查询备件记录
export function querySparePartsReplaceByDevice(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/sparePartsReplaceByDevice`,
    method: "POST",
    data
  });
}

// 设备模板
// 设备模板节点树
export function getTemplateNodeTree() {
  return fetch({
    url: `/eem-service/${version}/device/template/queryTemplateNodeTree`,
    method: "POST"
  });
}

// 新增设备模板节点
export function addTemplateNode(data) {
  return fetch({
    url: `/eem-service/${version}/device/template/insertTemplateNodeTree`,
    method: "PUT",
    data
  });
}

// 编辑设备模板节点
export function editTemplateNode(data) {
  return fetch({
    url: `/eem-service/${version}/device/template/editTemplateNodeTree`,
    method: "PUT",
    data
  });
}

// 删除设备模板节点
export function deleteTemplateNode(data) {
  return fetch({
    url: `/eem-service/${version}/device/template/deleteDeviceTemplateNode`,
    method: "DELETE",
    data
  });
}

// 设备模板列表
export function queryTemplates(id) {
  return fetch({
    url: `/eem-service/${version}/device/template/queryTemplates?id=${id}`,
    method: "POST"
  });
}

// 运行参数分组节点
export function queryRunningParamNode(id) {
  return fetch({
    url: `/eem-service/${version}/device/template/queryRunningParamNode?id=${id}`,
    method: "POST"
  });
}

// 运行参数节点树
export function queryRunningParamNodeTree() {
  return fetch({
    url: `/eem-service/${version}/device/template/queryRunningParamNodeTree`,
    method: "POST"
  });
}

// 新增模板
export function addTemplate(id, data) {
  return fetch({
    url: `/eem-service/${version}/device/template/writeTemplate?parentId=${id}`,
    method: "POST",
    data
  });
}

// 编辑模板
export function editTemplate(id, data) {
  return fetch({
    url: `/eem-service/${version}/device/template/updateTemplate?parentId=${id}`,
    method: "POST",
    data
  });
}

// 删除模板
export function deleteTemplate(id, data) {
  return fetch({
    url: `/eem-service/${version}/device/template/deleteDeviceTemplate?parentId=${id}`,
    method: "DELETE",
    data
  });
}

// 备件零件
// 查询零部件节点树
export function querySparePartsTree() {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/tree`,
    method: "GET"
  });
}

// 查询系统和设备节点树
export function queryDeviceSystem() {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/deviceAndSystem`,
    method: "POST"
  });
}

// 新增系统
export function addDeviceSystem(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/deviceSystem`,
    method: "POST",
    data
  });
}

// 编辑系统
export function editDeviceSystem(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/deviceSystem`,
    method: "PUT",
    data
  });
}

// 新增设备
export function addSparePartsDevice(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/sparePartsDevice`,
    method: "POST",
    data
  });
}

// 编辑设备
export function editSparePartsDevice(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/sparePartsDevice`,
    method: "PUT",
    data
  });
}

// 查询设备型号
export function querySparePartsModel(model) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/model?modelkeyword=${model}`,
    method: "POST"
  });
}

// 删除系统
export function deleteDeviceSystem(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/deviceSystem`,
    method: "DELETE",
    data
  });
}

// 删除设备
export function deleteSparePartsDevice(data, id) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/sparePartsDevice?systemid=${id}`,
    method: "DELETE",
    data
  });
}

// 查询备件库列表
export function querySparePartsList(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/sparePartsList`,
    method: "POST",
    data
  });
}

// 新增备件
export function addSpareParts(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/spareParts`,
    method: "POST",
    data
  });
}

// 编辑备件
export function editSpareParts(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/spareParts`,
    method: "PUT",
    data
  });
}

// 删除备件
export function deleteSpareParts(data, id) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/spareParts?deviceid=${id}`,
    method: "DELETE",
    data
  });
}

// 按备件统计
export function querySparePartsByOther(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/sparePartsByOther`,
    method: "POST",
    data: processRequestByInterFace(data)
  });
}

// 按备件统计
export function querySparePartsByDevice(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/sparePartsByDevice`,
    method: "POST",
    data: processRequestByInterFace(data)
  });
}

// 将零件同步到备件库
export function importSpapreByComponent(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/importSpapreByComponent`,
    method: "POST",
    data
  });
}

// 根据设备查备件库
export function getSparePartsStorageByDevice(data) {
  return fetch({
    url: `/eem-service/${version}/device/spareParts/sparePartsStorageByDevice`,
    method: "POST",
    data
  });
}

// 根据设备查巡检方案
export function querySchemeByDevice(data) {
  return fetch({
    url: `/eem-service/${version}/inspect/param/querySchemeByDevice`,
    method: "POST",
    data
  });
}

// 根据巡检方案查询巡检参数详情
export function queryDetailByScheme(params) {
  return fetch({
    url: `/eem-service/${version}/inspect/param/queryDetailByScheme`,
    method: "GET",
    params
  });
}

// 根据设备查询巡检参数
export function queryParamByDevice(data) {
  return fetch({
    url: `/eem-service/${version}/inspect/param/queryByDevice`,
    method: "POST",
    data
  });
}
