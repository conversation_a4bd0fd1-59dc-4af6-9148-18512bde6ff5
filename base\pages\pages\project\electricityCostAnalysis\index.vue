<template>
  <div class="page eem-common">
    <el-container style="height: 100%; padding: 0px">
      <el-aside
        width="315px"
        class="eem-aside"
        style="height: 100%; position: relative; z-index: 100"
      >
        <CetTree
          :selectNode.sync="CetTree_leftTree.selectNode"
          :checkedNodes.sync="CetTree_leftTree.checkedNodes"
          :searchText_in.sync="CetTree_leftTree.searchText_in"
          v-bind="CetTree_leftTree"
          v-on="CetTree_leftTree.event"
          style="height: 100%"
        >
          <span
            class="el-tree-node__label"
            slot-scope="{ node }"
            :level="node.level"
          >
            <span
              :style="{
                color: filNodeColor(node)
              }"
            >
              {{ node.label }}
            </span>
          </span>
        </CetTree>
      </el-aside>
      <el-container class="fullheight mlJ3 flex-column" style="overflow: auto">
        <el-header
          height="auto"
          class="eem-container mbJ3 eem-min-width"
          style="
            padding: 0p;
            display: flex;
            justify-content: flex-end;
            align-items: center;
          "
        >
          <customElSelect
            class="fr mrJ1"
            v-model="ElSelect_scheme.value"
            v-bind="ElSelect_scheme"
            v-on="ElSelect_scheme.event"
            :prefix_in="$T('分时方案')"
          >
            <ElOption
              v-for="item in ElOption_scheme.options_in"
              :key="item[ElOption_scheme.key]"
              :label="item[ElOption_scheme.label]"
              :value="item[ElOption_scheme.value]"
              :disabled="item[ElOption_scheme.disabled]"
            ></ElOption>
          </customElSelect>
          <time-tool
            :typeID="14"
            :val.sync="startTime"
            @change="changeQueryTime"
            :timeType_in="timeType"
          ></time-tool>
        </el-header>
        <el-container class="flex-auto flex-column eem-min-width">
          <div
            style="height: 385px; min-height: 385px"
            class="chartBox eem-container flex-column"
          >
            <!-- 用电成本和平均电价趋势 -->
            <headerSpot class="mbJ3">
              {{ $T("用电成本和平均电价趋势") }}
            </headerSpot>
            <CetChart
              v-bind="CetChart_electrictyCost"
              class="flex-auto"
            ></CetChart>
            <span class="avgCost" v-if="ElSelect_scheme.value">
              <el-tooltip :content="toolTipContent">
                <i class="el-icon-question mrJ fcZS"></i>
              </el-tooltip>
              {{ $T("平均电价") }}
              <span>({{ avgUnit }})</span>
            </span>
          </div>
          <div style="flex: 4" class="proportion">
            <div class="flex-row fullheight" style="min-height: 230px">
              <div class="fullheight eem-container flex-column" style="flex: 1">
                <headerSpot class="mbJ3">
                  {{ $T("平均电价分析") }}
                </headerSpot>
                <div class="proportion-item avg-cost flex-auto flex-column">
                  <div class="cost-item mbJ1">
                    <span>{{ $T("时间") }}:</span>
                    &emsp;
                    <span>{{ selectTime }}</span>
                  </div>
                  <div class="cost-item mbJ1">
                    <span>{{ $T("平均电价") }}:</span>
                    &emsp;
                    <span>
                      {{
                        formatNumberWithPrecision(electrictyCostView.avgCost, 2)
                      }}
                    </span>
                    <span>{{ electrictyCostView.unit }}</span>
                  </div>
                  <div class="compare flex-auto">
                    <el-row
                      :gutter="20"
                      class="fullheight"
                      style="
                        display: flex;
                        justify-content: center;
                        position: relative;
                      "
                    >
                      <span class="fs16 preText tip">{{ tbName }}</span>
                      <el-col
                        :span="12"
                        class="fullheight"
                        style="position: relative; transform: translateY(23px)"
                      >
                        <CetChart
                          v-bind="CetChart_avgcost1"
                          style="position: absolute; top: 0; left: 0"
                        ></CetChart>
                        <div class="label">
                          <el-tooltip
                            :content="
                              _.isEmpty(tbLabel)
                                ? ''
                                : tbLabel.price + tbLabel.unit
                            "
                            effect="light"
                          >
                            <div class="price text-ellipsis">
                              <span class="fs20">{{ tbLabel.price }}</span>
                            </div>
                          </el-tooltip>
                          <div class="percent">
                            <el-tooltip
                              :content="tbLabel.percent"
                              effect="light"
                            >
                              <span class="text-ellipsis">
                                {{ tbLabel.percent }}
                              </span>
                            </el-tooltip>
                            <img :src="tbLabel.src" v-if="tbLabel.src" alt="" />
                          </div>
                        </div>
                      </el-col>
                      <span
                        v-if="queryTime.aggregationCycle === 14"
                        class="fs16 lastText tip"
                      >
                        {{ hbName }}
                      </span>
                      <el-col
                        :span="12"
                        class="fullheight"
                        style="position: relative; transform: translateY(23px)"
                        v-if="queryTime.aggregationCycle === 14"
                      >
                        <CetChart
                          v-bind="CetChart_avgcost2"
                          style="position: absolute; top: 0; left: 0"
                        ></CetChart>
                        <div class="label">
                          <el-tooltip
                            :content="
                              _.isEmpty(hbLabel)
                                ? ''
                                : hbLabel.price + hbLabel.unit
                            "
                            effect="light"
                          >
                            <div class="price text-ellipsis">
                              <span class="fs20">{{ hbLabel.price }}</span>
                            </div>
                          </el-tooltip>
                          <div class="percent">
                            <el-tooltip
                              :content="hbLabel.percent"
                              effect="light"
                            >
                              <span class="text-ellipsis">
                                {{ hbLabel.percent }}
                              </span>
                            </el-tooltip>
                            <img :src="hbLabel.src" v-if="hbLabel.src" alt="" />
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
              <div
                class="fullheight eem-container flex-column mlJ3"
                style="flex: 2"
              >
                <headerSpot class="mbJ3">
                  {{ $T("分时用电成本") }}
                </headerSpot>
                <div class="proportion-item flex-auto">
                  <div class="cost-item mbJ2">
                    <span>{{ $T("时间") }}:</span>
                    &emsp;
                    <span>{{ selectTime }}</span>
                  </div>
                  <div class="tsBox">
                    <div class="tsTable">
                      <CetTable
                        :data.sync="CetTable_cost.data"
                        :dynamicInput.sync="CetTable_cost.dynamicInput"
                        v-bind="CetTable_cost"
                        v-on="CetTable_cost.event"
                        style="height: 100%"
                      >
                        <template v-for="item in columnArr">
                          <ElTableColumn
                            :key="item.label"
                            v-bind="item"
                          ></ElTableColumn>
                        </template>
                      </CetTable>
                    </div>
                    <div class="tsChart mlJ3">
                      <CetChart v-bind="CetChart_timecost"></CetChart>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-container>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom";
import TimeTool from "eem-components/TimeTool.vue";
import TREE_PARAMS from "@/store/treeParams.js";
export default {
  name: "electricityCostAnalysis",
  components: {
    TimeTool
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    },
    tbName() {
      if (this.queryTime.aggregationCycle === 17) {
        return $T("上年成本");
      } else if (this.queryTime.aggregationCycle === 14) {
        return $T("上年同月成本");
      }
      return $T("同比");
    },
    hbName() {
      if (this.queryTime.aggregationCycle === 14) {
        return $T("上月成本");
      }
      return $T("同比");
    }
  },
  data() {
    return {
      // leftTree树组件
      CetTree_leftTree: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_leftTree_currentNode_out,
          parentList_out: this.CetTree_leftTree_parentList_out,
          checkedNodes_out: this.CetTree_leftTree_checkedNodes_out,
          halfCheckNodes_out: this.CetTree_leftTree_halfCheckNodes_out,
          allCheckNodes_out: this.CetTree_leftTree_allCheckNodes_out
        }
      },
      currentNode: null, // 当前点击树节点
      ElSelect_scheme: {
        value: "",
        style: {
          width: "250px"
        },
        size: "small",
        event: {
          change: this.ElSelect_scheme_change_out
        }
      },
      ElOption_scheme: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      startTime: new Date().getTime(),
      queryTime: {},
      timeType: [
        {
          type: "month",
          text: $T("月"),
          typeID: 14,
          number: 1,
          unit: "M"
        },
        {
          type: "year",
          text: $T("年"),
          typeID: 17,
          number: 1,
          unit: "y"
        }
      ],
      // electrictyCost组件
      CetChart_electrictyCost: {
        //组件输入项
        inputData_in: null,
        options: {}
      },
      // 分时用电成本
      CetChart_timecost: {
        //组件输入项
        inputData_in: null,
        options: {
          tooltip: {
            confine: true,
            trigger: "item",
            extraCssText: "white-space: normal; word-break: break-all",
            formatter(params) {
              return (
                params.name +
                ": " +
                Number(params.value).toFixed(2) +
                (params.data.unit || "") +
                "(" +
                Number(params.percent).toFixed(2) +
                "%)"
              );
            }
          },
          series: [
            {
              type: "pie",
              radius: "65%",
              label: {
                show: true,
                textStyle: {
                  fontSize: 12
                },
                formatter(params) {
                  return (
                    params.name + ": " + Number(params.percent).toFixed(2) + "%"
                  );
                }
              },
              data: []
            }
          ]
        }
      },
      electrictyCostView: {
        avgCost: null, // 平均电价值
        unit: "" // 平均电价单位
      }, // 平均电价
      tbLabel: {}, // 同比数据
      hbLabel: {}, // 环比数据
      // 同比
      CetChart_avgcost1: {
        //组件输入项
        inputData_in: null,
        options: {
          series: [
            {
              name: $T("同比"),
              type: "pie",
              radius: ["65%", "85%"],
              avoidLabelOverlap: false,
              emphasis: {
                scale: false
              },
              label: {
                position: "center",
                rich: {
                  a: {
                    color: "#00fff9",
                    fontSize: 24
                  },
                  b: {
                    color: "#00fff9"
                  },
                  c: {
                    fontSize: 16,
                    color: "#fff"
                  },
                  d: {
                    width: 14,
                    backgroundColor: {
                      image: "/static/assets/icons/arrow_up.png"
                    }
                  }
                }
              },
              labelLine: {
                show: false
              },
              data: []
            }
          ]
        }
      },
      // 环比
      CetChart_avgcost2: {
        //组件输入项
        inputData_in: null,
        options: {
          series: [
            {
              name: $T("同比"),
              type: "pie",
              radius: ["65%", "85%"],
              avoidLabelOverlap: false,
              emphasis: {
                scale: false
              },
              label: {
                position: "center",
                rich: {
                  a: {
                    color: "#00fff9",
                    fontSize: 24
                  },
                  b: {
                    color: "#00fff9"
                  },
                  c: {
                    fontSize: 16,
                    color: "#fff"
                  },
                  d: {
                    width: 14,
                    backgroundColor: {
                      image: "/static/assets/icons/arrow_up.png"
                    }
                  }
                }
              },
              labelLine: {
                show: false
              },
              data: []
            }
          ]
        }
      },
      selectTime: "",
      // cost表格组件
      CetTable_cost: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {
          record_out: this.CetTable_cost_record_out,
          outputData_out: this.CetTable_cost_outputData_out
        }
      },
      columnsUnit: "",
      Columns_cost: [
        {
          prop: "identification", // 支持path a[0].b
          label: $T("时段"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "50" //绝对宽度
        },
        {
          prop: "value", // 支持path a[0].b
          "render-header": h => {
            let text = $T("实际成本");
            if (this.queryTime.aggregationCycle === 17) {
              text = $T("当年成本");
            } else if (this.queryTime.aggregationCycle === 14) {
              text = $T("当月成本");
            }
            return h("span", text + this.columnsUnit);
          },
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatNumberCol(),
          minWidth: "80"
          // width: "160" //绝对宽度
        },
        {
          prop: "tbValue", // 支持path a[0].b
          "render-header": h => {
            let text = $T("同比");
            if (this.queryTime.aggregationCycle === 17) {
              text = $T("上年成本");
            } else if (this.queryTime.aggregationCycle === 14) {
              text = $T("上年同月成本");
            }
            return h("span", text + this.columnsUnit);
          },
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatNumberCol(),
          width: "130"
        },
        {
          prop: "hbValue", // 支持path a[0].b
          "render-header": h => {
            let text = $T("同比");
            if (this.queryTime.aggregationCycle === 14) {
              text = $T("上月成本");
            }
            return h("span", text + this.columnsUnit);
          },
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatNumberCol()
        }
      ],
      columnArr: [], // 表格列
      avgUnit: "",
      toolTipContent: "" // 平均电价计算提示框内容
    };
  },
  watch: {},
  methods: {
    //通过节点childSelectState属性，调整字体颜色
    filNodeColor(node) {
      const state = this._.get(node, "data.childSelectState", null);
      if (state !== 1) {
        return "#989898";
      }
      return;
    },

    formatNumberWithPrecision: common.formatNumberWithPrecision,
    ElSelect_scheme_change_out(val) {
      this.getAllData();
    },

    // leftTree 输出
    CetTree_leftTree_currentNode_out: _.debounce(function (val) {
      if (!val) return;
      if (val.childSelectState !== 1) {
        return this.$message.warning($T("该节点下无数据!"));
      }
      this.currentNode = this._.cloneDeep(val);
      const params = {
        objectId: val.id,
        objectLabel: val.modelLabel
      };
      customApi.queryTimeShareList(params).then(res => {
        if (res.code === 0) {
          this.ElOption_scheme.options_in = res.data;
          this.ElSelect_scheme.value = res.data[0] && res.data[0].id;
          this.getAllData();
        }
      });
    }),
    CetTree_leftTree_parentList_out(val) {},
    CetTree_leftTree_checkedNodes_out(val) {},
    CetTree_leftTree_halfCheckNodes_out(val) {},
    CetTree_leftTree_allCheckNodes_out(val) {},
    changeQueryTime({ val, timeOption }) {
      const date = this.$moment(val);
      this.queryTime = {
        aggregationCycle: timeOption.typeID,
        startTime: date.startOf(timeOption.unit).valueOf(),
        endTime: date.endOf(timeOption.unit).valueOf() + 1
      };
      const mothStr = this.language ? "YYYY-MM" : "YYYY年MM月";
      const yearStr = this.language ? "YYYY" : "YYYY年";
      this.selectTime =
        timeOption.typeID === 14
          ? date.format(mothStr)
          : timeOption.typeID === 17
          ? date.format(yearStr)
          : "--";
      this.getAllData();
      if (this.queryTime.aggregationCycle === 14) {
        this.toolTipContent = $T("日峰平谷电度电费之和/日峰平谷总电量");
      } else if (this.queryTime.aggregationCycle === 17) {
        this.toolTipContent = $T("月峰平谷电度电费之和/月峰平谷总电量");
      }
    },
    // cost表格输出
    CetTable_cost_record_out(val) {},
    CetTable_cost_outputData_out(val) {},
    getTreeData() {
      this.CetTree_leftTree.inputData_in = [];
      const data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.autoManagement,
        treeReturnEnable: true
      };
      customApi.queryElectricityTree(data).then(res => {
        if (res.code === 0) {
          this.CetTree_leftTree.inputData_in = res.data;
          // 选中第一个有数据（childSelectState = 1）的节点并展开节点
          const obj = this._.find(this.dataTransform(res.data), [
            "childSelectState",
            1
          ]);
          this.CetTree_leftTree.selectNode = obj;
        }
      });
    },
    dataTransform(array) {
      const cloneData = this._.cloneDeep(array);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },
    getAllData() {
      this.CetChart_electrictyCost.options = {};
      this.electrictyCostView = {};
      this.tbLabel = {};
      this.hbLabel = {};
      this.columnArr = [];
      this.CetTable_cost.data = [];
      this.CetChart_avgcost1.options.series[0].data = [];
      this.CetChart_avgcost2.options.series[0].data = [];
      this.CetChart_timecost.options.series[0].data = [];
      if (!this.currentNode || !this.ElSelect_scheme.value) return;
      const params = {
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel,
        energyType: 2,
        timeShareId: this.ElSelect_scheme.value,
        ...this.queryTime
      };
      this.getElectricityCostValueTrend(params);
      this.getAverageElectricityPrice(params);
      this.getTsObjectCost(params);
    },
    //过滤获取图表x轴对应值
    getAxixs(date, type) {
      if (type === 14) {
        return this.$moment(date).format("DD");
      } else if (type === 17) {
        return this.$moment(date).format("YYYY/MM");
      }
    },
    // 用电成本趋势tooltip格式化
    formatTooltip(params) {
      if (!params[0].data.time) return;
      const cycle = this.queryTime.aggregationCycle;
      const formatStr =
        cycle === 14 ? "YYYY-MM-DD" : cycle === 17 ? "YYYY-MM" : "";
      let str = this.$moment(params[0].data.time).format(formatStr) + "<br />";
      params.forEach(item => {
        str +=
          item.marker +
          item.seriesName +
          ": " +
          (item.data.value || item.data.value === 0
            ? Number(item.data.value).toFixed(2)
            : "--") +
          `(${item.data.unit})` +
          "<br />";
      });
      return str;
    },
    getElectricityCostValueTrend(params) {
      const _this = this;
      const cycle = this.queryTime.aggregationCycle;
      customApi.queryElectricityCostValueTrend(params).then(res => {
        if (res.code === 0 && res.data.length) {
          // 处理x轴
          const xAxisData = [];
          res.data[0].forEach(item => {
            xAxisData.push(
              this.getAxixs(item.time, this.queryTime.aggregationCycle)
            );
          });
          // 处理series数据
          var series = [];
          let unit = ""; // 峰平谷单位
          this.avgUnit = ""; // 平均电价单位
          res.data.forEach(item => {
            if (item[0].identification !== "平均电价") {
              series.push({
                name: item[0].identification,
                type: "bar",
                stack: $T("电"),
                barMaxWidth: 30,
                data: item
              });
              unit = item[0].unit;
            } else {
              this.avgUnit = item[0].unit;
              series.push({
                name: item[0].identification,
                type: "line",
                yAxisIndex: 1,
                data: item,
                markPoint: {
                  data: [
                    {
                      type: "max",
                      symbolSize: 30,
                      name: $T("平均电价最大值"),
                      label: {
                        formatter(params) {
                          return (
                            params.name +
                            ": " +
                            _this.formatNumberWithPrecision(params.value, 2)
                          );
                        },
                        position: "top",
                        padding: 8,
                        // backgroundColor: "rgba(38, 41, 56, 0.8)",
                        borderRadius: 4
                      }
                    },
                    {
                      type: "min",
                      symbolSize: 30,
                      name: $T("平均电价最小值"),
                      label: {
                        formatter(params) {
                          return (
                            params.name +
                            ": " +
                            _this.formatNumberWithPrecision(params.value, 2)
                          );
                        },
                        position: "top",
                        padding: 8,
                        // backgroundColor: "rgba(38, 41, 56, 0.8)",
                        borderRadius: 4
                      }
                    }
                  ]
                },
                smooth: true
              });
            }
          });
          this.CetChart_electrictyCost.options = {
            toolbox: {
              top: -10,
              right: 30,
              feature: {
                saveAsImage: {}
              }
            },
            tooltip: {
              trigger: "axis",
              formatter(params) {
                return _this.formatTooltip(params);
              }
            },
            legend: {
              top: 0
            },
            grid: {
              top: "70",
              left: "3%",
              right: "4%",
              bottom: "3%",
              containLabel: true
            },
            xAxis: {
              type: "category",
              name: cycle === 14 ? $T("天数") : $T("月份"),
              nameLocation: "end",
              data: xAxisData,
              axisPointer: {
                type: "shadow"
              }
            },
            yAxis: [
              {
                type: "value",
                name: unit ? `${$T("成本")}(${unit})` : "--"
              },
              {
                type: "value",
                name: this.avgUnit
                  ? `${$T("平均电价")}(${this.avgUnit})`
                  : "--",
                nameTextStyle: {
                  // verticalAlign: "middle"
                }
              }
            ],
            series
          };
        }
      });
    },
    // 平均电价分析
    getAverageElectricityPrice(params) {
      customApi.queryAverageElectricityPrice(params).then(res => {
        if (res.code === 0) {
          this.electrictyCostView = {
            avgCost: res.data.unitCostValue,
            unit: res.data.unit
          };
          // 同比
          const tbRate = Math.abs(res.data.tb);
          if (tbRate > 1) {
            this.CetChart_avgcost1.options.series[0].data = [1];
          } else {
            this.CetChart_avgcost1.options.series[0].data = [
              tbRate,
              1 - tbRate
            ];
          }
          this.tbLabel = {
            price:
              this.formatNumberWithPrecision(res.data.unitCostValueTb, 2) ||
              "--",
            percent:
              res.data.tb || res.data.tb === 0
                ? this.formatNumberWithPrecision(
                    Math.abs(res.data.tb) * 100,
                    2
                  ) + "%"
                : "--",
            src:
              res.data.tb > 0
                ? require("./assets/arrow_up.png")
                : res.data.tb < 0
                ? require("./assets/arrow_down.png")
                : "",
            unit: res.data.unit
          };

          // 环比
          const hbRate = Math.abs(res.data.hb);
          if (hbRate > 1) {
            this.CetChart_avgcost2.options.series[0].data = [1];
          } else {
            this.CetChart_avgcost2.options.series[0].data = [
              hbRate,
              1 - hbRate
            ];
          }
          this.hbLabel = {
            price:
              this.formatNumberWithPrecision(res.data.unitCostValueHb, 2) ||
              "--",
            percent:
              res.data.hb || res.data.hb === 0
                ? this.formatNumberWithPrecision(
                    Math.abs(res.data.hb) * 100,
                    2
                  ) + "%"
                : "--",
            src:
              res.data.hb > 0
                ? require("./assets/arrow_up.png")
                : res.data.hb < 0
                ? require("./assets/arrow_down.png")
                : "",
            unit: res.data.unit
          };
        }
      });
    },
    // 分时用电成本
    getTsObjectCost(params) {
      customApi.queryTsObjectCost(params).then(res => {
        if (res.code === 0) {
          // 表格头加单位
          let arr = [];
          if (this.queryTime.aggregationCycle === 14) {
            arr = this._.cloneDeep(this.Columns_cost);
          } else if (this.queryTime.aggregationCycle === 17) {
            arr = this._.dropRight(this._.cloneDeep(this.Columns_cost));
          }
          const unit =
            res.data && res.data.length ? "(" + res.data[0].unit + ")" : "";
          this.columnsUnit = unit;
          this.columnArr = arr;

          this.CetTable_cost.data = this._.cloneDeep(res.data);
          this.CetChart_timecost.options.series[0].data = [];
          res.data.forEach(item => {
            this.CetChart_timecost.options.series[0].data.push({
              value: item.value,
              name: item.identification,
              unit: item.unit
            });
          });
        }
      });
    }
  },
  activated() {
    this.getTreeData();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .chartBox {
    position: relative;
    .avgCost {
      position: absolute;
      top: 88px;
      right: 25px;
      font-size: 12px;
      height: 24px;
      @include background_color(BG1);
    }
  }
  .proportion {
    margin-top: 20px;
    .proportion-item {
      .cost-item {
        text-align: center;
        // padding: 2px 0;
        height: 28px;
        line-height: 28px;
        @include font_color(T8);
        @include background(BG6);
        span {
          font-size: 16px;
        }
      }
    }
    .avg-cost {
      padding-bottom: 0;
      .compare {
        .tip {
          position: absolute;
          top: 10px;
          left: 10px;
          z-index: 99;
          padding: 2px 20px 2px 2px;
        }
        .preText {
          @include font_color(T8);
          @include background(BG6);
        }
        .lastText {
          @include font_color(T8);
          @include background(BG6);
          left: calc(50% + 10px);
        }
        .label {
          width: 50%;
          height: 50px;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;
          .price {
            // color: #00fff9;
          }
          .percent {
            span {
              display: inline-block;
              max-width: calc(100% - 20px);
            }
            img {
              width: 14px;
            }
          }
        }
      }
    }
  }
  .tsBox {
    height: calc(100% - 50px);
    display: flex;
    .tsTable {
      width: 0;
      flex: 2;
      height: 100%;
      position: relative;
      .el-container {
        width: 100%;
        padding-top: 30px;
        /* max-height: 160px;
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%); */
      }
    }
    .tsChart {
      min-width: 360px;
      flex: 1.5;
      height: 100%;
      overflow: hidden;
    }
  }
}
</style>
