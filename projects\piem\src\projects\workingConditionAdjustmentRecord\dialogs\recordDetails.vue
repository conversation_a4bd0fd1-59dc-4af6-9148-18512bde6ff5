<template>
  <customDrawer v-bind="schemeDialog" ref="drawer">
    <div slot="headerRight">
      <el-button @click="handleEditScheme">{{ $T("编辑") }}</el-button>
    </div>
    <div class="fullheight eem-cont-c1">
      <el-row :gutter="16">
        <el-col
          :span="12"
          v-for="(item, index) in deviceAttribute"
          :key="index"
          class="mbJ3"
        >
          <div class="text-T3 mbJ1">{{ item.name }}</div>
          <div class="text-T1">
            {{ formatterValue(inputData_in, item) }}
          </div>
        </el-col>
      </el-row>
    </div>
  </customDrawer>
</template>

<script>
import common from "eem-utils/common.js";
import customApi from "@/api/custom.js";
import customDrawer from "@/components/customElDrawer";
export default {
  name: "recordDetails",
  components: { customDrawer },
  props: {
    openTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      schemeDialog: {
        title: $T("记录详情"),
        size: "480px",
        openTrigger_in: +new Date(),
        closeTrigger_in: +new Date()
      },
      deviceAttribute: [
        {
          name: $T("设备名称"),
          key: "name"
        },
        {
          name: $T("调节方式"),
          key: "adjustmentType",
          formatter: this.formatterAdjustmentMethod
        },
        {
          name: $T("工况生效时间"),
          key: "effectiveTime",
          formatter: this.formatterDate
        },
        {
          name: $T("调节前工况"),
          key: "preRegulationCondition",
          formatter: this.formatterAdjustedCondition
        },
        {
          name: $T("调节后工况"),
          key: "adjustedCondition",
          formatter: this.formatterAdjustedCondition
        },
        {
          name: $T("创建用户"),
          key: "operator"
        },
        {
          name: $T("创建时间"),
          key: "createTime",
          formatter: this.formatterDate
        }
      ]
    };
  },
  watch: {
    openTrigger_in() {
      this.schemeDialog.openTrigger_in = +new Date();
    }
  },
  methods: {
    /**
     * 编辑运行方案
     */
    handleEditScheme() {
      this.$emit("handleEditRecord", this.inputData_in);
    },

    /**
     * 数值转换
     */
    formatterValue(val, item) {
      const value = _.get(val, item.key, null);
      if (item.formatter) {
        return item.formatter(value);
      } else {
        return value || value === 0 ? value : "--";
      }
    },

    /**
     * 数字类型展示
     */
    formatterNumber(val) {
      return _.isNumber(val) ? val : "--";
    },

    /**
     * 调节方式转换
     */
    formatterAdjustmentMethod(val) {
      const list = this.$store.state.enumerations.gascompressoradjustablemode;
      const obj = list.find(item => item.id === val);
      return obj?.text || "--";
    },

    /**
     * 日期转换
     */
    formatterDate(val) {
      if (val) {
        return this.$moment(val).format("YYYY-MM-DD HH:mm");
      } else {
        return "--";
      }
    },

    /**
     * 调节前后工况转换
     */
    formatterAdjustedCondition(cellValue) {
      let result;
      if (this.inputData_in.adjustmentType === 2) {
        result = this.formatterCylinderOperationMode(cellValue);
      } else {
        let text =
          this.inputData_in.adjustmentType === 0
            ? $T("回流开度")
            : $T("余隙开度");
        result = cellValue || cellValue === 0 ? text + cellValue + "%" : "--";
      }
      return result;
    },

    /**
     * 气缸作用方式
     */
    formatterCylinderOperationMode(val) {
      const list = [
        {
          id: 0,
          text: $T("单作用")
        },
        {
          id: 1,
          text: $T("双作用")
        },
        {
          id: 2,
          text: $T("单双可调")
        },
        {
          id: 3,
          text: $T("单单作用")
        },
        {
          id: 4,
          text: $T("双双作用")
        }
      ];
      const obj = list.find(item => item.id === val);
      return obj?.text || "--";
    }
  }
};
</script>
<style lang="scss" scoped>
.flex1 {
  flex: 1;
}
</style>
