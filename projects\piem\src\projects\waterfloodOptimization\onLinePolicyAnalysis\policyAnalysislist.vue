<template>
  <div class="w-full h-full">
    <div class="w-full h-[32px] mb-[16px]">
      <el-input
        v-model="keyName"
        prefix-icon="el-icon-search"
        class="w-[209px] mr-[8px]"
        placeholder="请输入关键字"
        @blur="onBlur"
      ></el-input>
      <CustomElDatePicker
        prefix_in="选择时段"
        style="width: 345px"
        range-separator="至"
        v-bind="CetDatePicker_1.config"
        v-model="CetDatePicker_1.val"
        @change="CetDatePicker_1_change_out"
      ></CustomElDatePicker>
    </div>
    <div class="w-full h-[calc(100%-32px-16px)]">
      <div class="h-[calc(100%-32px-16px)] w-full">
        <el-table height="100%" :data="tableData" :highlight-current-row="true">
          <el-table-column label="序号" type="index" width="60" />
          <el-table-column
            label="发生时间"
            prop="eventtime"
            :formatter="onformatterTime"
            width="160"
          />
          <el-table-column label="策略评价" width="80">
            <template slot-scope="{ row }">
              <el-tag
                :type="
                  row.valid
                    ? 'success'
                    : row.valid === false
                    ? 'danger'
                    : row.valid === null
                    ? 'gray'
                    : ''
                "
              >
                {{
                  row.valid
                    ? "有效"
                    : row.valid === false
                    ? "无效"
                    : row.valid === null
                    ? "忽略"
                    : "--"
                }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            label="策略描述"
            prop="strategydescription"
            showOverflowTooltip
          />
          <el-table-column label="操作" width="140">
            <template slot-scope="{ row }">
              <el-button
                @click.stop="onDetail(row)"
                type="text"
                class="handleBtn"
              >
                详情
              </el-button>
              <el-button
                @click.stop="onEvaluate(row)"
                type="text"
                class="handleBtn"
              >
                策略评价
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        class="mt-[16px]"
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="->,total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="onSizeChange"
        @current-change="onCurrentChange"
      />
    </div>
    <StrategyEvaluation
      v-bind="strategyEvaluation"
      @updateTable="getTableData"
    ></StrategyEvaluation>
    <onLinePolicyAnalysisDetail
      v-bind="onLinePolicyAnalysisDetail"
      @updateTable="getTableData"
    ></onLinePolicyAnalysisDetail>
  </div>
</template>

<script>
import StrategyEvaluation from "./strategyEvaluation.vue";
import onLinePolicyAnalysisDetail from "./onLinePolicyAnalysisDetail.vue";
import customApi from "@/api/custom.js";
export default {
  name: "policyAnalysislist",
  props: { selectNode: Object },
  components: { StrategyEvaluation, onLinePolicyAnalysisDetail },
  data() {
    return {
      keyName: "",
      CetDatePicker_1: {
        val: [
          this.$moment().subtract(9, "d").startOf("d").valueOf(),
          this.$moment().endOf("d").valueOf()
        ],
        config: {
          valueFormat: "timestamp",
          type: "daterange",
          clearable: false,
          pickerOptions: {
            disabledDate: time => {
              return time.getTime() > Date.now();
            }
          }
        }
      },
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      onLinePolicyAnalysisDetail: {
        openTrigger_in: +new Date(),
        inputData_in: {}
      },
      strategyEvaluation: {
        openTrigger_in: +new Date(),
        inputData_in: {}
      }
    };
  },
  computed: {},
  watch: {
    selectNode: {
      deep: true,
      handler(val) {
        if (!_.isObject(val)) return;
        this.getTableData();
      }
    }
  },
  methods: {
    onBlur() {
      this.pagination.currentPage = 1;
      this.getTableData();
    },
    CetDatePicker_1_change_out() {
      this.getTableData();
    },
    async getTableData() {
      const params = {
        page: {
          index: (this.pagination.currentPage - 1) * this.pagination.pageSize,
          limit: this.pagination.pageSize
        },
        startTime: this.CetDatePicker_1.val[0],
        endTime: this.$moment(this.CetDatePicker_1.val[1]).endOf("d").valueOf(),
        objectId: this.selectNode?.id,
        objectLabel: this.selectNode?.modelLabel,
        strategyDescription: this.keyName || null
      };
      this.tableData = [];
      this.pagination.total = 0;
      if (params.objectLabel !== "waterinjectionstation") return;
      const res = await customApi.queryAnalysisOfStrategies(params);

      this.tableData = res?.data || [];
      this.pagination.total = res?.total || 0;
    },

    onformatterTime(row) {
      return row?.eventtime
        ? this.$moment(row?.eventtime).format("YYYY-MM-DD HH:mm:ss")
        : "--";
    },

    onDetail(row) {
      this.onLinePolicyAnalysisDetail.openTrigger_in = +new Date();
      this.onLinePolicyAnalysisDetail.inputData_in = _.cloneDeep(row);
    },
    onEvaluate(row) {
      this.strategyEvaluation.openTrigger_in = +new Date();
      this.strategyEvaluation.inputData_in = _.cloneDeep(row);
    },
    onSizeChange(val) {
      this.pagination.currentPage = 1;
      this.pagination.pageSize = val;
      this.getTableData();
    },
    onCurrentChange(val) {
      this.pagination.currentPage = val;
      this.getTableData();
    }
  },
  created() {},
  mounted() {
    this.getTableData();
  }
};
</script>

<style lang="scss" scoped>
.handleBtn {
  padding: 0;

  &:not(:first-of-type) {
    @include margin_left(J4);
  }

  :deep(span) {
    @include font_size(Aa);
  }
}

.el-tag.el-tag--gray {
  @include font_color(T3, !important);
  border: none;
  background-color: rgba(152, 152, 152, 0.1);
}
</style>
