<template>
  <div class="page flex-column eem-cont">
    <div class="top clearfix">
      <div class="placeholder"></div>
      <div class="cycle">{{ $T("统计周期") }}</div>
      <div class="budget">{{ $T("预算合计") }}</div>
    </div>
    <div class="flex-auto">
      <el-table
        class="table"
        :data="tableData"
        border
        stripe
        style="width: 100%; height: 100%"
      >
        <el-table-column prop="date" label="#" width="60"></el-table-column>
        <el-table-column
          prop="name"
          :label="$T('能源类型')"
          width="200"
        ></el-table-column>
        <el-table-column
          prop="address"
          :label="$T('时间')"
          width="300"
        ></el-table-column>
        <el-table-column prop="address" :label="$T('成本合计')" min-width="200">
          <template slot-scope="scope">
            <ElInputNumber
              v-model="scope.row.value"
              v-bind="ElInputNumber_num"
              @change="write(scope.row)"
            ></ElInputNumber>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="foot">
      <div class="count">{{ $T("合计") }}</div>
      <div class="num">{{ count }} ({{ $T("元") }})</div>
    </div>
    <div class="">
      <CetButton
        class="btn"
        v-bind="CetButton_1"
        v-on="CetButton_1.event"
      ></CetButton>
      <CetButton
        class="btn"
        v-bind="CetButton_2"
        v-on="CetButton_2.event"
      ></CetButton>
    </div>
  </div>
</template>
<script>
import { httping } from "@omega/http";
export default {
  name: "Entry",
  components: {},

  computed: {
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },

  data(vm) {
    return {
      count: 0,
      yearSelect: vm.$moment().year(),
      submitObj: {},
      tableData: [
        {
          date: "1",
          name: "电",
          address: "1月",
          value: ""
        },
        {
          date: "2",
          name: "电",
          address: "2月",
          value: ""
        },
        {
          date: "3",
          name: "电",
          address: "3月",
          value: ""
        },
        {
          date: "4",
          name: "电",
          address: "4月",
          value: ""
        },
        {
          date: "5",
          name: "电",
          address: "5月",
          value: ""
        },
        {
          date: "6",
          name: "电",
          address: "6月",
          value: ""
        },
        {
          date: "7",
          name: "电",
          address: "7月",
          value: ""
        },
        {
          date: "8",
          name: "电",
          address: "8月",
          value: ""
        },
        {
          date: "9",
          name: "电",
          address: "9月",
          value: ""
        },
        {
          date: "10",
          name: "电",
          address: "10月",
          value: ""
        },
        {
          date: "11",
          name: "电",
          address: "11月",
          value: ""
        },
        {
          date: "12",
          name: "电",
          address: "12月",
          value: ""
        }
      ],
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      ElInputNumber_num: {
        min: 0,
        max: 999999999.99,
        precision: 2,
        controls: false,
        placeholder: $T("请输入")
      }
    };
  },
  props: ["object", "year", "energyType", "budgetList"],
  watch: {
    year(val) {
      const year = new Date(val * 1).getFullYear();
      this.yearSelect = year;
    },
    energyType(val) {},
    budgetList(val) {
      let count = 0;
      const tableData = this.tableData;
      // let list = this._.orderBy(val, ["month"], ["asc"]);
      val.map((item, index) => {
        tableData[index].value = item.budgetValueEdit;
        tableData[index].logtime = item.logtime;
        let split = this.language ? "-" : "年";
        tableData[index].address = this.language
          ? item.time.split(split)[1] + " " + $T("月")
          : item.time.split(split)[1];
        count += item.budgetValueEdit * 1;
        tableData[index].name = this.object.energyType$text;
      });
      this.count = count.toFixed(2);
    }
  },

  methods: {
    write(row) {
      const list =
        this.tableData &&
        this.tableData.map(item => {
          return item.value;
        });
      const count = list.reduce((total, item) => {
        return total * 1 + item * 1;
      });

      this.count = count.toFixed(2);
    },
    submit(submitObj, val) {
      httping({
        url: "eem-service/v1/costcaculating/budget",
        method: "POST",
        data: submitObj
      }).then(res => {
        if (res.code == 0) {
          const tableData = this.tableData;
          tableData.map(item => {
            item.value = "";
          });
          this.count = 0;
          this.tableData = tableData;
          this.$emit("finishTrigger_out2", val);
        }
      });
    },
    CetButton_1_statusTrigger_out(val) {
      const year = this.yearSelect;
      const startTime = new Date(year + "-1-1").getTime();
      const endTime = new Date(year + 1 + "-1-1").getTime();
      const submitObj = this.submitObj;
      const list = this.tableData;
      console.log(list);
      const budgets = [];
      list.map((item, i) => {
        const obj = {};
        obj.time = item.logtime;
        obj.value = item.value;
        budgets.push(obj);
      });
      submitObj.projectId = sessionStorage.projectId * 1;
      submitObj.energyType = sessionStorage.energyType * 1;
      submitObj.budgets = budgets;
      submitObj.startTime = list[0].logtime;
      submitObj.endTime = list[list.length - 1].logtime;
      submitObj.cycle = 14;
      this.submit(submitObj, val);
    },
    CetButton_2_statusTrigger_out(val) {
      const tableData = this.tableData;
      tableData.map(item => {
        item.value = "";
      });
      this.tableData = tableData;
      this.count = 0;
      this.$emit("finishTrigger_out2", val);
    },
    CetTable_1_detailTrigger_out(val) {},
    CetTable_1_editTrigger_out(val) {},
    CetTable_1_outputData_out(val) {},
    CetTable_1_record_out(val) {}
  },

  created: function () {
    const startTime = this.$moment().startOf("year").valueOf();
    const endTime = this.$moment().add(1, "year").startOf("year").valueOf();
    const projectId = sessionStorage.projectId * 1;
    const energyType = sessionStorage.energyType;
    const obj = {
      cycle: 17,
      projectId,
      energyType: 2,
      startTime,
      endTime,
      budgets: []
    };
    this.submitObj = obj;
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.btn {
  float: right;
  @include margin(J3 0 0 J1);
}
.top {
  display: flex;
  overflow: hidden;
  line-height: 50px;
}
.top .placeholder {
  width: 260px;
}
.top .cycle {
  width: 300px;
  color: #fff;
  text-align: center;
  line-height: 38px;
  border-radius: 10px 10px 0 0;
  background: #ff9900;
  float: right;
}
.top .budget {
  min-width: 200px;
  flex: 1;
  color: #fff;
  text-align: center;
  line-height: 38px;
  border-radius: 10px 10px 0 0;
  background: #0152d9;
  float: right;
}
.foot {
  line-height: 50px;
  text-align: center;
  display: flex;
}
.foot .count {
  width: 560px;
  border: 1px solid;
  @include border_color(B1);
  border-top: none;
}
.foot .num {
  min-width: 200px;
  flex: 1;
  border: 1px solid;
  @include border_color(B1);
  border-top: none;
  border-left: none;
}
.el-table td,
.el-table th {
  text-align: center;
}
.table {
  :deep() {
    .el-table__body-wrapper {
      height: calc(100% - 40px);
      overflow-y: auto;
    }
  }
}
</style>
