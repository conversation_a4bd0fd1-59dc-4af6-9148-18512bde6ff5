<template>
  <div class="usingEnergyOverview">
    <span>
      {{ $T("通过对浙江油田西南采气厂集输增压系统与污水回注系统的") }}
    </span>
    <span class="value">{{ $moment(queryTime).format("YYYY年MM月") }}</span>
    <span>
      {{ $T("的能效数据进行分析，得出合理的运行优化策略，预计能实现月节电量") }}
    </span>
    <span class="value">
      {{ formatNumberWithPrecision(reportData.totalEnergySaving, 2)
      }}{{ reportData.overviewEnergyUnit }}
    </span>
    <span>
      {{ $T("，节省用电成本约") }}
    </span>
    <span class="value">
      {{ formatNumberWithPrecision(reportData.totalCostSaving, 2)
      }}{{ $T("万元") }}
    </span>
    <span>{{ $T("，月节能率") }}</span>
    <span class="value">
      {{ formatNumberWithPrecision(reportData.totalSavingRate, 2) }}%
    </span>
    <span>{{ $T("。其中，用电量为") }}</span>
    <span class="value">{{ $T("集输增压系统") }}</span>
    <span>{{ $T("用电量为") }}</span>
    <span class="value">
      {{ formatNumberWithPrecision(reportData.gatherSysEnergy, 2)
      }}{{ reportData.overviewEnergyUnit }}
    </span>
    <span>{{ $T("，通过对系统中") }}</span>
    <span class="value">
      {{ formatNumberWithPrecision(reportData.compressorCount, 0)
      }}{{ $T("台") }}
    </span>
    <span>{{ $T("电驱往复式天然气压缩机进行运行优化，预计节电量") }}</span>
    <span class="value">
      {{ formatNumberWithPrecision(reportData.gatherSysSavingEnergy, 2)
      }}{{ reportData.overviewEnergyUnit }}
    </span>
    <span>{{ $T("，节能率") }}</span>
    <span class="value">
      {{ formatNumberWithPrecision(reportData.gatherSysSavingRate, 2) }}%
    </span>
    <span>{{ $T("。") }}</span>
    <span class="value">{{ $T("污水回注系统") }}</span>
    <span>{{ $T("用电量") }}</span>
    <span class="value">
      {{ formatNumberWithPrecision(reportData.recycleSysEnergy, 2)
      }}{{ reportData.overviewEnergyUnit }}
    </span>
    <span>{{ $T("，通过对系统中") }}</span>
    <span class="value">
      {{ formatNumberWithPrecision(reportData.pumpCount, 0) }}{{ $T("台") }}
    </span>
    <span>{{ $T("回注泵进行运行优化，预计节省用电成本") }}</span>
    <span class="value">
      {{ formatNumberWithPrecision(reportData.recycleSysCostSaving, 2)
      }}{{ $T("万元") }}
    </span>
    <span>{{ $T("，节费率") }}</span>
    <span class="value">
      {{ formatNumberWithPrecision(reportData.costSavingRate, 2) }}%
    </span>
    <span>。</span>
  </div>
</template>

<script>
import common from "eem-utils/common";
export default {
  name: "usingEnergyOverview",
  components: {},
  props: {
    reportData: {
      type: Object
    },
    queryTime: {
      type: Number
    }
  },
  data() {
    return {};
  },
  watch: {},
  methods: {
    formatNumberWithPrecision(...args) {
      return common.formatNumberWithPrecision(...args);
    }
  }
};
</script>

<style lang="scss" scoped>
.usingEnergyOverview {
  background: #f8fafb;
  padding: 8px 16px;
  box-sizing: border-box;
  line-height: 20px;
  .value {
    font-weight: bold;
    color: #00b45e;
  }
  text-indent: 24px;
  * {
    word-break: break-all;
    font-size: 12px;
  }
}
</style>
