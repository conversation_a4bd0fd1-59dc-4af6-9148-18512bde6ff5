import fetch from "eem-utils/fetch";
import { getCustomParameter } from "./common/common.js";
import store from "@/store";
const version = "v1";

function processRequest(data) {
  return data;
}

function processResponse(response) {
  // 对 response 进行任意转换处理, response结构
  //   {
  //     // `data` 由服务器提供的响应
  //     data: {},

  //     // `status` 来自服务器响应的 HTTP 状态码
  //     status: 200,

  //     // `statusText` 来自服务器响应的 HTTP 状态信息
  //     statusText: 'OK',

  //     // `headers` 服务器响应的头
  //     headers: {},

  //     // `config` 是为请求提供的配置信息
  //     config: {}
  //   }
  return response;
}

/**
 *
 * @param {*维修工单管理} data
 */

//查询指定任务当前所处的节点
export function getRepairTaskConfig(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/repair/task/config/${data.code}`,
    method: "GET",
    // headers: { "User-ID": 1 },
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

//查询维修工单
export function queryRepairWorkOrder(data) {
  var newData = {
    teamId: 0,
    keyword: "",
    overTimeOnly: 0,
    endTime: 0,
    startTime: 0,
    workSheetStatus: 0,
    tasklevel: 0,
    page: data.rootCondition.page,
    tenantId: store.state.tenantId
  };

  var expressions = _.get(data, "rootCondition.filter.expressions");
  if (!_.isEmpty(expressions)) {
    newData.keyword = getCustomParameter(expressions, "code") || "";
    newData.overTimeOnly = getCustomParameter(expressions, "overTimeOnly");
    if (newData.overTimeOnly === 0) {
      delete newData.overTimeOnly;
    }
    newData.teamId = getCustomParameter(expressions, "teamId");
    if (!newData.teamId) {
      delete newData.teamId;
    }
    newData.endTime = getCustomParameter(expressions, "endTime");
    newData.startTime = getCustomParameter(expressions, "startTime");
    newData.workSheetStatus = getCustomParameter(
      expressions,
      "workSheetStatus"
    );
    newData.tasklevel = getCustomParameter(expressions, "tasklevel");
    if (newData.tasklevel === 0) {
      delete newData.tasklevel;
    }
  }
  return fetch({
    url: `/eem-service/${version}/workorder/repair`,
    method: "POST",
    // headers: { "User-ID": 1 },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(newData)
  });
}
//创建或者修改维修工单
export function createRepairWorkOrder(data) {
  delete data.deviceListName;
  delete data.deviceTypeName;
  delete data.pecEventTypeName;
  delete data.modelLabel;
  if (data.timeconsumeplan) {
    data.timeconsumeplan = data.timeconsumeplan * 1000 * 60 * 60;
  }
  return fetch({
    url: `/eem-service/${version}/workorder/repair`,
    method: "PUT",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

//查询指定维修工单
export function getRepairWorkOrder(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/repair/${data.code}`,
    method: "GET",
    // headers: { "User-ID": 1 },
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

//审核工单
export function queryRepairCheck(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/repair/check`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

//批量审核工单
export function queryRepairCheckBatch(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/repair/check/batch`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

//导出审核信息
export function queryRepairCheckImport(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/repair/check/import/${data.code}`,
    method: "GET",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

//暂存审核信息
export function queryRepairCheckStash(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/repair/check/stash`,
    method: "POST",
    // headers: { "User-ID": 1 },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

//查询暂存的审核信息
export function GetRepairCheckStashCode(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/repair/check/stash/${data.code}`,
    method: "GET",
    // headers: { "User-ID": 1 },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

//提交表单数据公共方法，写入内容由调用端自己决定，推动流程进入下一步
export function queryRepairCommitSubmit(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/repair/commit/submit`,
    method: "PUT",
    // headers: { "User-ID": 1 },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

//批量提交表单数据公共方法，写入内容由调用端自己决定，推动流程进入下一步
export function queryRepairCommonSubmitBatch(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/repair/common/submit/batch`,
    method: "PUT",
    // headers: { "User-ID": 1 },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

//统计维修工单数量
export function queryRepairWorkOrderCount(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/repair/count`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

//提交维修数据，推动流程进入下一步
export function queryRepairWorkOrderSubmit(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/repair/param/submit`,
    method: "PUT",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

//保存维修数据，不会推动流程进入下一步
export function queryRepairWorkOrderUpdate(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/repair/param/update`,
    method: "PUT",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

//查询指定任务当前所处的节点
export function getRepairConfigCode(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/repair/task/config/${data.code}`,
    method: "GET",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

//根据节点查询所关联的设备归类信息
export function queryDeviceClassificationNode(data) {
  return fetch({
    url: `/eem-service/${version}/expert/deviceClassification/node`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

//获取事件类型归类列表
export function queryEventclassification(data) {
  return fetch({
    url: `/eem-service/${version}/expert/query/name?projectId=${data.projectId}&modelLabel=eventclassification&name=`,
    method: "GET",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

//获取场景列表
export function queryFaultScenarios(data) {
  return fetch({
    url: `/eem-service/${version}/expert/faultScenarios`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

//获取场景列表
export function queryExpertEventPlan(data) {
  const scenariosId = data.scenariosId || 0;
  const limit = data.limit || 0;
  return fetch({
    url: `/eem-service/${version}/expert/eventPlan?scenariosId=${scenariosId}&limit=${limit}`,
    method: "GET",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

//获取事件详情信息
export function queryPeccoreEventLogById(data) {
  return fetch({
    url: `/eem-service/${version}/event/peccore/eventLog/${data.sourceid}`,
    method: "GET",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

// 根据事件索引字段查询设备事件记录
export function queryPeccoreEventLogByIndex(data) {
  return fetch({
    url: `/eem-service/${version}/event/peccore/eventLog/index`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
