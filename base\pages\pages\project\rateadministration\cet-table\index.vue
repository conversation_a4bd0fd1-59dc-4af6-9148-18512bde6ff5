<template>
  <el-container class="table-container">
    <el-main style="padding: 0px">
      <el-table
        ref="cetTable11"
        :data="tableData"
        tooltip-effect="light"
        border
        highlight-current-row
        :show-header="showHeader"
        height="true"
        style="height: 100%; width: 100%"
        @current-change="handlerCurrentChange"
        @sort-change="sortChange"
        v-bind="$attrs"
        v-on="$listeners"
      >
        <el-table-column
          v-if="showCheckBox"
          type="selection"
          width="45"
          :selectable="canConfirm"
        ></el-table-column>
        <el-table-column
          label="序号"
          v-if="showIndex"
          type="index"
          width="100"
        ></el-table-column>

        <!-- 根据模型数据绑定表格列 -->
        <template v-for="item in renderColumns">
          <template v-if="item.formatterType">
            <el-table-column
              :key="item.key"
              :show-overflow-tooltip="true"
              :label="item.title"
              :min-width="item.width"
              :width="item.fixedwidth"
              :prop="item.key"
              :sortable="item.sortable"
              header-align="center"
              align="center"
            >
              <template v-if="item.formatterType" slot-scope="scope">
                <span
                  v-if="item.displayMode == 'custom'"
                  :style="item.formatterType(scope)"
                >
                  {{
                    formatTable(scope.row, scope.column, scope.row[item.key])
                  }}
                  <i v-if="item.iconClass" :class="item.iconClass(scope)"></i>
                </span>
                <span
                  v-else-if="item.displayMode == 'fontColor'"
                  :style="{ color: item.formatterType(scope.row) }"
                >
                  {{
                    formatTable(scope.row, scope.column, scope.row[item.key])
                  }}
                </span>
                <el-tag
                  v-else
                  :type="item.formatterType(scope.row)"
                  disable-transitions
                >
                  {{
                    formatTable(scope.row, scope.column, scope.row[item.key])
                  }}
                </el-tag>
              </template>
            </el-table-column>
          </template>
          <template v-else>
            <el-table-column
              :key="item.key"
              :show-overflow-tooltip="true"
              :label="item.title"
              :min-width="item.width"
              :width="item.fixedwidth"
              :prop="item.key"
              :sortable="item.sortable"
              :formatter="formatTable"
              header-align="center"
              :align="item.align"
            ></el-table-column>
          </template>
        </template>

        <!-- <el-table-column prop="State" label="状态" width="90">
        <template slot-scope="scope">
          <i :class="scope.row.State|stateType"></i>
        </template>
        </el-table-column>-->

        <el-table-column v-if="showDetail" label="详情" width="65">
          <template slot-scope="scope">
            <i
              v-if="showTableButton(scope)"
              class="row-detail"
              @click="handleDetail(scope.$index, scope.row)"
            ></i>
          </template>
        </el-table-column>
        <el-table-column v-if="showEdit" label="编辑" width="65">
          <template slot-scope="scope">
            <i
              v-if="showTableButton(scope)"
              class="row-edit"
              @click="handleEdit(scope.$index, scope.row)"
            ></i>
          </template>
        </el-table-column>
        <el-table-column v-if="showDelete" label="删除" width="75">
          <template slot-scope="scope">
            <i
              v-if="showDeleteButton(scope) && showTableButton(scope)"
              class="row-delete"
              @click="handleDelete(scope.$index, scope.row)"
            ></i>
          </template>
        </el-table-column>
      </el-table>
    </el-main>
    <el-footer v-if="showPagination" height="32px" style="padding: 0px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentPageChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :total="totalCount"
        :layout="pageLayout"
        :pageSizes="pageSizes"
        v-bind="paginationCfg"
      ></el-pagination>
    </el-footer>
  </el-container>
</template>

<script>
import _ from "lodash";
import common from "eem-utils/common";
import { httping } from "@omega/http";

export default {
  components: {},
  name: "cetTable11",
  props: {
    //表格的查询模式 //查询按钮触发trigger，或者查询条件变化立即查询diff
    queryMode: {
      type: String
    },
    //表格数据获取模式 // 数据获取模式：backendInterface后端接口 ；其他组件 component; 静态数据 static
    dataMode: {
      type: String
    },
    //数据绑定配置
    dataConfig: {
      type: Object
    },
    //表格接受其他组件输入，并输出内部值变化到外部
    data: {
      type: Array
    },
    //查询节点输入
    queryNode_in: {
      type: Object
    },
    queryTime_in: {
      type: Object
    },
    customParams_in: {
      type: Object
    },
    //导出操作触发
    exportTrigger_in: {
      type: Number
    },
    //查询按钮状态输入，状态变化执行查询
    queryTrigger_in: {
      type: Number
    },
    //删除按钮状态输入，状态变化执行删除
    deleteTrigger_in: {
      type: Number
    },
    doLayoutTrigger_in: {
      type: Number
    },
    //取消操作后执行删除状态输入，状态变化执行删除
    deleteWithoutConfirmTrigger_in: {
      type: Number
    },
    //清空表格数据
    clearTrigger_in: [String, Number, Boolean, Array, Object, Date],
    //刷新表格数据
    refreshTrigger_in: {
      type: [Number]
    },
    //新增表格记录
    addData_in: {
      type: Object
    },
    //编辑表格记录
    editData_in: {
      type: Object
    },
    //动态输入
    dynamicInput: {
      type: Object
    },
    //展示列配置
    columns: {
      type: Array
    },
    showEdit: {
      type: Boolean
    },
    showDelete: {
      type: Boolean
    },
    showDeleteConfig: {
      type: Function
    },
    showDetail: {
      type: Boolean
    },
    showCheckBox: {
      type: Boolean
    },
    showIndex: {
      type: Boolean
    },
    showPagination: {
      type: Boolean
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    exportFileName: {
      type: [Function, String]
    },
    exportMultiHeader: {
      type: [Function]
    },
    //分页相关配置
    paginationCfg: {
      type: [Object],
      default: function () {
        return {};
      }
    }
  },
  data() {
    return {
      getTableNum: 0, //如果从接口获取一次数据，则getTableNum加1，可以判断是否从接口获取过数据，获取过数据则刷新时掉接口，否则不调接口
      activatedNum: 0, //组件activated钩子触发的次数
      //表格数据
      tableData: [],
      //当前选中节点
      currentRow: {},
      //删除的行
      deleteRows: [],
      totalCount: 0,
      pageSize: this.paginationCfg.pageSize || 100,
      pageLayout:
        this.paginationCfg.layout || "total,sizes, prev, pager, next, jumper",
      pageSizes: this.paginationCfg.pageSizes || [10, 20, 50, 100],
      currentPage: 1,
      columnType: {
        date: "date",
        number: "number",
        boolean: "boolean"
      },
      renderColumns: [],
      orders: this.dataConfig.orders ? this.dataConfig.orders : null
    };
  },
  watch: {
    queryNode_in: {
      deep: true,
      handler: function (val) {
        this.paramsChange();
      }
    },
    queryTime_in: {
      deep: true,
      handler: function (val) {
        this.paramsChange();
      }
    },
    customParams_in: {
      deep: true,
      handler: function (val) {
        this.paramsChange();
      }
    },
    //按钮触发查询
    queryTrigger_in() {
      var vm = this;
      vm.currentPage = 1; //分页置为第一页
      vm.getTableData();
    },
    //条件变化直接查询
    dynamicInput: {
      deep: true,
      handler: function (val) {
        this.paramsChange();
      }
    },
    tableData: {
      deep: true,
      handler: function (val) {
        this.$emit("outputList_out", val);
      }
    },
    //导出按钮触发
    exportTrigger_in() {
      this.exportTable();
    },
    //如果是从其他组件进行数据输入，则直接赋值
    data(val) {
      this.setInputData();
    },
    //删除触发
    deleteTrigger_in() {
      this.deleteRow();
    },
    doLayoutTrigger_in() {
      // this.$refs["cetTable11"].doLayout();
    },
    deleteWithoutConfirmTrigger_in() {
      this.deleteRowWithoutConfirm();
    },
    // 触发清空表格数据
    clearTrigger_in: {
      deep: true,
      handler: function (val, newValue) {
        this.tableData = [];
      }
    },
    //在本地通过数据新增一行记录，记录无id
    addData_in(val) {
      this.tableData.push(val);
      this.setOutputData();
    },
    //在本地编辑一行记录
    editData_in(val) {
      this.editRowInTable(val);
    },
    //刷新触发
    refreshTrigger_in() {
      this.refreshTable();
    },
    //根据columns生成实际的渲染对象
    columns: {
      immediate: true,
      handler() {
        const vm = this;

        const renderColumns = [];

        for (let i = 0; i < vm.columns.length; i++) {
          const column = vm._.cloneDeep(vm.columns[i]);
          if (vm.columns[i].type === vm.columnType.number) {
            column.align = "right";
          } else {
            column.align = "center";
          }
          renderColumns.push(column);
        }

        vm.renderColumns = renderColumns;
        // 表格动态渲染表头，数据少的时候还会出现滚动条,elementUI的表格计算高度不是在渲染之后导致的。关键就是在表格渲染完成之后重新布局表格使用doLayout方法
        this.$nextTick(() => {
          if (this.$refs.cetTable11) {
            this.$refs.cetTable11.doLayout();
          }
        });
      }
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    userInfo() {
      return this.$store.state.userInfo;
    }
  },
  methods: {
    canConfirm(row, column) {
      const isSelectAble = row.isSelectAble;
      return !isSelectAble;
    },
    showDeleteButton(scope) {
      if (scope._self.showDeleteConfig) {
        return scope._self.showDeleteConfig(scope.row);
      }

      return true;
    },
    showTableButton(scope) {
      const vm = this;
      if (vm.isShowSummary() && scope.$index === vm.tableData.length - 1) {
        return false;
      }
      return true;
    },
    formatTable(row, column, cellValue, index) {
      var vm = this;
      var config = this._.find(vm.columns, { key: column.property });
      var columnIndex = this._.findIndex(vm.columns, { key: column.property });
      var rowIndex = this._.findIndex(vm.tableData, row);

      //如果需要显示合计行，而且当前列不是合计列，则不需要进行格式化；只有合计行列才需要格式化
      if (
        vm.isShowSummary() &&
        rowIndex === this.tableData.length - 1 &&
        !vm.isSummaryColumn(columnIndex)
      ) {
        return cellValue;
      }
      if (config.type === this.columnType.date) {
        cellValue = vm.formatDate(row, column, cellValue, index, config);
      }
      if (config.type === this.columnType.number) {
        cellValue = vm.formatNumber(row, column, cellValue, index);
      }
      if (config.type === this.columnType.boolean) {
        cellValue = vm.formatBoolean(row, column, cellValue, index);
      }

      if (!cellValue) {
        if (cellValue === 0 || cellValue === "") {
          return cellValue;
        } else {
          return "--";
        }
      }

      return cellValue;
    },
    //格式化日期列
    formatDate(row, column, cellValue, index, config) {
      //设置时间格式化字符串，如果配置有效则采用配置字符串，无效则采用默认格式化字符串
      const formatStr = config.formatStr
        ? config.formatStr
        : "YYYY-MM-DD HH:mm:ss";
      if (cellValue) {
        return this.$moment(cellValue).format(formatStr);
      } else if (cellValue === "") {
        return cellValue;
      } else {
        return null;
      }
    },
    //格式化数字列
    formatNumber(row, column, cellValue, index) {
      //TODO 通过columns配置的format进行自定义格式化
      var vm = this;
      var config = this._.find(vm.columns, { key: column.property });
      if (cellValue === "") {
        return cellValue;
      }
      return common.formatNumber(config, cellValue, vm);
    },
    //格式化布尔值
    formatBoolean(row, column, cellValue, index) {
      var vm = this;
      var config = this._.find(vm.columns, { key: column.property });
      return common.formatBoolean(config, cellValue, vm);
    },
    checkQueryNode() {
      const vm = this;
      if (!vm.queryNode_in) {
        return false;
      }
      if (vm._.isEmpty(vm.queryNode_in)) {
        return false;
      }
      if (vm.queryNode_in.id < 0) {
        return false;
      }
      return true;
    },
    checkQueryTime() {
      const vm = this;
      if (!vm.queryTime_in) {
        return false;
      }
      if (!vm.queryTime_in.time) {
        return false;
      }
      return true;
    },
    //详情弹窗
    handleDetail(index, row) {
      //详情弹窗触发信号
      var vm = this;
      vm.$refs.cetTable11.setCurrentRow(row);
      vm.$emit("detailTrigger_out", new Date().getTime());
    },

    //编辑弹窗
    handleEdit(index, row) {
      //编辑弹窗触发信号
      //设置当前表格输出的行

      var vm = this;
      vm.$refs.cetTable11.setCurrentRow(row);
      vm.$emit("editTrigger_out", new Date().getTime());
    },

    //删除操作
    handleDelete(index, row) {
      var vm = this;
      vm.$refs.cetTable11.setCurrentRow(row);
      vm.deleteRow();
      //删除操作
    },

    //对查询参数变化从而查询表格数据的，判断参数是否有变化，没变化的不查询
    paramsChange() {
      const vm = this;

      if (vm.queryMode === "diff") {
        const queryBody = vm.getQueryBody();
        if (vm._.isEqual(vm.oldQueryBody, queryBody)) {
          return;
        }
        vm.currentPage = 1; //分页置为第一页
        vm.getTableData();
      }
    },

    // 从接口获取表格的数据
    getTableData() {
      const vm = this;
      let queryOption;
      let url;
      let queryBody = {};

      //如果不是接口获取数据模式，不调用接口
      if (vm.dataMode !== "backendInterface") {
        return;
      }

      //querynode为-1表示无效值，直接置为空
      if (vm.queryNode_in && vm.queryNode_in.id && vm.queryNode_in.id === -1) {
        vm.tableData = [];
        vm.totalCount = 0;

        //重新获取数据后清空当前行、删除数据数组
        vm.currentRow = null;
        vm.deleteRows = [];
        //置oldQueryBody为undefined，下次入参变化不阻止调用接口
        vm.oldQueryBody = undefined;
        return;
      }

      //组织body入参
      queryBody = vm.getQueryBody();
      //先保存接口入参，如果接口失败，则置vm.oldQueryBody为undefined，用于判断是否是重复的接口调用
      vm.oldQueryBody = vm._.cloneDeep(queryBody);

      url = vm.dataConfig.queryUrl;
      queryOption = {
        url: url,
        method: vm.dataConfig.type ? vm.dataConfig.type : "POST",
        data: queryBody
      };

      httping(queryOption).then(
        function (response) {
          if (response.code === 0) {
            vm.peocessTableData(response);
          } else {
            vm.oldQueryBody = undefined;
          }
        },
        function () {
          vm.oldQueryBody = undefined;
        }
      );
    },

    peocessTableData(response) {
      const vm = this;

      vm.getTableNum++; //如果从接口获取一次数据，则getTableNum加1，可以判断是否从接口获取过数据，获取过数据则刷新时掉接口，否则不调接口

      //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
      vm.checkShowSummary(response.data);
      vm.tableData = response.data;
      vm.totalCount = response.total;

      vm.selectRow(response.data);
      //重新获取数据后清空当前行、删除数据数组
      vm.deleteRows = [];
    },

    //选中指定行
    selectRow(data) {
      const vm = this;

      let addedRowID = -1;
      //说明有新增记录，需要选中新增的记录
      if (vm.tableData.length !== data.length) {
        addedRowID = vm.findAddedRowID(vm.tableData, data);
      }

      if (addedRowID !== -1) {
        //说明找到了新增记录的id
        const addedRow = vm._.find(vm.tableData, { id: addedRowID });
        vm.$refs.cetTable11.setCurrentRow(addedRow);
      } else {
        //没有找到新增记录就选中上一次选中的记录，或第一条记录
        if (
          !vm._.isUndefined(vm.currentRow) &&
          vm.currentRow !== null &&
          !vm._.isEmpty(vm.currentRow)
        ) {
          const lastSelectedRow = vm._.find(vm.tableData, {
            id: vm.currentRow.id
          });
          //上一个选中记录还存在就自动选中
          if (lastSelectedRow !== null && !vm._.isUndefined(lastSelectedRow)) {
            vm.$refs.cetTable11.setCurrentRow(lastSelectedRow);
          } else {
            vm.selectFirstRow();
          }
        } else {
          vm.selectFirstRow();
        }
      }
    },
    // 增加设置某行选中-lz
    setCurrentRow(data) {
      this.tableData.forEach(item => {
        if (item.id == data.id) {
          this.$refs.cetTable11.setCurrentRow(item);
        }
      });
    },

    //获取查询接口的入参
    getQueryBody() {
      const vm = this;
      const queryBody = {};
      queryBody.modelLabel = vm.dataConfig.modelLabel;
      vm.getConditions(queryBody);

      vm.getCustomParams(queryBody);

      queryBody.treeNode = vm.getQueryNode();
      queryBody.queryTime = vm.getQueryTime();
      queryBody.filter = vm.getFilterStr();
      queryBody.page = vm.showPagination
        ? {
            index: (vm.currentPage - 1) * vm.pageSize,
            limit: vm.pageSize
          }
        : null;
      queryBody.orders = vm.orders;

      queryBody.include_submodel = true;
      queryBody.props = [];
      return queryBody;
    },
    getConditions(queryBody) {
      const vm = this;
      const conditions = [];

      if (!vm.dataConfig.modelList || vm.dataConfig.modelList.length < 1) {
        return;
      }
      //构造conditions参数
      for (let i = 0; i < vm.dataConfig.modelList.length; i++) {
        var single = {
          filter: null, //vm.getConditionFilterStr(vm.dataConfig.modelList[i]),
          include_relations: [],
          modelLabel: vm.dataConfig.modelList[i],
          props: []
        };

        conditions.push(single);
      }

      queryBody.conditions = conditions;
    },
    //获取额外的查询入参
    getCustomParams(queryBody) {
      const vm = this;

      if (!vm.customParams_in) {
        return;
      }

      if (vm._.isObject(vm.customParams_in)) {
        vm._.assign(queryBody, vm.customParams_in);
      }
    },
    //判断是否需要展示合计行
    isShowSummary() {
      const vm = this;
      return vm.dataConfig.showSummary && vm.dataConfig.showSummary.enabled;
    },
    //判断是否为合计的列
    isSummaryColumn(columnIndex) {
      const vm = this;
      return (
        vm._.indexOf(vm.dataConfig.showSummary.summaryColumns, columnIndex) !==
        -1
      );
    },
    //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
    checkShowSummary(data) {
      const vm = this;
      if (vm.isShowSummary()) {
        const summaryRow = {};
        let total = 0;
        vm._(vm.columns).forEach(function (column, index) {
          if (index === 0) {
            summaryRow[column.key] = vm.dataConfig.showSummary.summaryTitle;
          } else if (vm.isSummaryColumn(index)) {
            total = 0;
            vm._(data).forEach(function (oneData) {
              total = oneData[column.key] ? total + oneData[column.key] : total;
            });
            summaryRow[column.key] = total;
          } else {
            summaryRow[column.key] = "";
          }
        });
        data.push(summaryRow);
      }
    },
    //寻找新增记录的id
    findAddedRowID(oldData, newData) {
      const vm = this;
      let id = -1;
      if (
        !vm._.isArray(oldData) ||
        !vm._.isArray(newData) ||
        oldData.length === newData.length
      ) {
        return id;
      }
      vm._(newData).forEach(function (item) {
        const findRow = vm._.find(oldData, { id: item.id }); //从旧的数据里面找新的记录，如果没有找到，说明是新增记录
        if (vm._.isUndefined(findRow)) {
          id = item.id;
          return false; //终止循环
        }
      });
      return id;
    },
    selectFirstRow() {
      const vm = this;
      //默认选中第一条记录
      if (vm.tableData && vm.tableData.length > 0) {
        vm.$refs.cetTable11.setCurrentRow(vm.tableData[0]);
      } else {
        vm.handlerCurrentChange(null);
      }
    },
    getQueryNode() {
      const vm = this;
      if (vm._.isEmpty(vm.queryNode_in) || !vm.queryNode_in) {
        return null;
      }
      if (!vm.queryNode_in.id) {
        return null;
      }

      const node = {
        id: vm.queryNode_in.id,
        modelLabel: vm.queryNode_in.modelLabel
      };

      return node;
    },
    getQueryTime() {
      const vm = this;
      let queryTime = {};
      if (vm._.isEmpty(vm.queryTime_in) || !vm.queryTime_in) {
        return null;
      }

      queryTime = {
        timeType: vm.queryTime_in.timeType ? vm.queryTime_in.timeType : 1,
        startTime: null,
        endTime: null
      };

      if (vm._.isArray(vm.queryTime_in.time)) {
        if (vm._.isDate(vm.queryTime_in.time[0])) {
          queryTime.startTime = vm.queryTime_in.time[0].getTime();
        }
        if (vm._.isDate(vm.queryTime_in.time[1])) {
          queryTime.endTime = vm.queryTime_in.time[1].getTime();
        }
      } else {
        if (vm._.isDate(vm.queryTime_in.time)) {
          queryTime.startTime = vm.queryTime_in.time.getTime();
        }
      }
      return queryTime;
    },
    getFilterStr() {
      var vm = this;
      var dataIndex = vm.dataConfig.dataIndex;
      var expressions = [];
      for (let i = 0; i < dataIndex.length; i++) {
        if (
          vm.dynamicInput[`${dataIndex[i]}_in`] !== "" &&
          !_.isNull(vm.dynamicInput[`${dataIndex[i]}_in`]) &&
          !vm._.isUndefined(vm.dynamicInput[`${dataIndex[i]}_in`])
        ) {
          let operator = "EQ";

          if (
            vm.dataConfig.filterOperator &&
            vm.dataConfig.filterOperator[dataIndex[i]]
          ) {
            //如果配置了dataConfig.filterOperator指定属性的过滤方法则用该方法，否则根据过滤的数据类型确定operator值
            operator = vm.dataConfig.filterOperator[dataIndex[i]];
          } else {
            if (_.isArray(vm.dynamicInput[`${dataIndex[i]}_in`])) {
              operator = "IN";
            } else if (_.isString(vm.dynamicInput[`${dataIndex[i]}_in`])) {
              operator = "LIKE";
            }
          }

          expressions.push({
            prop: dataIndex[i],
            operator: operator,
            limit: vm.dynamicInput[`${dataIndex[i]}_in`]
          });
        }
      }

      return {
        expressions: expressions
      };
    },

    getConditionFilterStr(subCondition) {
      var vm = this;
      var dataIndex = vm.dataConfig.dataIndex;
      var expressions = [];
      for (let i = 0; i < dataIndex.length; i++) {
        const indexStr = dataIndex[i];
        const index = indexStr.indexOf(`${subCondition}_model$`);
        if (index === -1) {
          continue;
        }

        if (
          vm.dynamicInput[`${dataIndex[i]}_in`] !== "" &&
          !_.isNull(vm.dynamicInput[`${dataIndex[i]}_in`]) &&
          !vm._.isUndefined(vm.dynamicInput[`${dataIndex[i]}_in`])
        ) {
          let operator = "EQ";

          if (_.isArray(vm.dynamicInput[`${dataIndex[i]}_in`])) {
            operator = "IN";
          } else if (_.isString(vm.dynamicInput[`${dataIndex[i]}_in`])) {
            operator = "LIKE";
          }

          expressions.push({
            prop: indexStr.substring(`${subCondition}_model$`.length),
            operator: operator,
            limit: vm.dynamicInput[`${dataIndex[i]}_in`]
          });
        }
      }

      return {
        expressions: expressions
      };
    },

    //其他组件直接输入表格数据的，通过inputData进行表格数据赋值
    setInputData() {
      var vm = this;
      if (vm.dataMode === "component") {
        if (!vm.data) {
          vm.tableData = _.cloneDeep([]);
        } else {
          //深拷贝赋值给表格
          vm.tableData = _.cloneDeep(vm.data);
        }

        //赋值时清空当前行，删除数据数组
        vm.selectRow(vm.tableData);
        vm.deleteRows = [];
      }
    },

    // 导出设备
    exportTable() {
      const vm = this;
      if (vm.dataMode === "backendInterface" && vm.showPagination) {
        vm.getAllData();
      } else {
        vm.doExport(vm._.cloneDeep(vm.tableData));
      }
    },
    getAllData() {
      const vm = this;
      let queryOption;
      let url;
      const queryBody = {};

      //如果不是接口获取数据模式，不调用接口
      if (vm.dataMode !== "backendInterface") {
        return;
      }

      if (vm.queryNode_in && vm.queryNode_in.id && vm.queryNode_in.id === -1) {
        return;
      }

      url = vm.dataConfig.queryUrl;

      queryBody.modelLabel = vm.dataConfig.modelLabel;
      vm.getConditions(queryBody);

      vm.getCustomParams(queryBody);

      queryBody.treeNode = vm.getQueryNode();
      queryBody.queryTime = vm.getQueryTime();
      queryBody.filter = vm.getFilterStr();
      queryBody.orders = vm.orders;

      queryBody.include_submodel = true;
      queryBody.props = [];

      queryOption = {
        url: url,
        method: vm.dataConfig.type ? vm.dataConfig.type : "POST",
        data: queryBody
      };

      httping(queryOption).then(function (response) {
        if (response.code === 0) {
          //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
          vm.checkShowSummary(response.data);
          vm.doExport(response.data);
        }
      });
    },
    doExport(list) {
      const vm = this;
      const tHeader = [];
      const filterVal = [];
      vm.getExportCol(tHeader, filterVal);
      const data = vm.formatJson(filterVal, list);
      let fileName = "";
      let multiHeader = [];

      //生成导出excel标题
      if (vm._.isFunction(vm.exportFileName)) {
        fileName = vm.exportFileName(vm);
      } else if (vm.exportFileName) {
        var reg = new RegExp("{{(.+?)}}", "igm"); //匹配 {{}} 的内容
        fileName = vm.exportFileName.replace(reg, function (node, key) {
          if (vm._.get(vm, key)) {
            return vm._.get(vm, key);
          } else {
            return "";
          }
        });
      } else {
        fileName = "表格导出";
      }

      //生成导出excel多标题
      if (vm.exportMultiHeader) {
        multiHeader = vm.exportMultiHeader(vm);
      }

      import("@/common/vendor/Export2Excel").then(excel => {
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: fileName,
          multiHeader: multiHeader,
          autoWidth: true,
          bookType: "xlsx"
        });
      });
    },
    formatJson(filterVal, jsonData) {
      const vm = this;
      return jsonData.map(v =>
        filterVal.map(j => {
          return vm.formatTable(v, { property: j }, v[j], 1);
        })
      );
    },
    getExportCol(tHeader, filterVal) {
      const vm = this;
      const cols = vm.columns;
      for (let i = 0; i < cols.length; i++) {
        tHeader.push(cols[i].title);
        filterVal.push(cols[i].key);
      }
    },

    //删除当前选中的行
    deleteRow() {
      const vm = this;
      //当前选中行无效则提示并返回
      if (!vm.currentRow) {
        this.$message({
          message: "请先选择需要删除的行",
          type: "warning"
        });
        return;
      }

      vm.$confirm("确定要删除所选项吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: "btn-custom-cancel",
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: function (action, instance, done) {
          if (action == "confirm") {
            if (vm.dataConfig.deleteUrl !== "") {
              vm.deleteRowFromServer();
            } else {
              vm.deleteRowFromTable();
            }
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action != "confirm") {
            vm.$message({
              type: "info",
              message: "取消删除！"
            });
          }
        }
      });
    },

    //删除当前选中的行，不需要确认，直接删除
    deleteRowWithoutConfirm() {
      const vm = this;
      //当前选中行无效则提示并返回
      if (!vm.currentRow) {
        this.$message({
          message: "请先选择需要删除的行",
          type: "warning"
        });
        return;
      }
      if (vm.dataConfig.deleteUrl !== "") {
        vm.deleteRowFromServer();
      } else {
        vm.deleteRowFromTable();
      }
    },
    //调用接口从服务器删除表格数据
    deleteRowFromServer() {
      const vm = this;
      let queryOption;

      queryOption = {
        url: `${vm.dataConfig.deleteUrl}`,
        method: "DELETE",
        data: {
          modelLabel: vm.dataConfig.modelLabel,
          idRange: [vm.currentRow.id]
        },
        title: "删除成功!"
      };

      httping(queryOption).then(function (response) {
        if (response.code === 0) {
          vm.$message.success(queryOption.title);
          vm.$emit("successfulOperation_out", new Date().getTime());

          //从服务器删除成功后再从本地删除该条记录
          vm.refreshTable();
        }
      });
    },
    //从本地表格中删除数据，不调用接口
    deleteRowFromTable() {
      const vm = this;
      //找到当前行，并从表格数据中删除，将删除的数据打上delete_flag标签，存入deleteRows数组中
      const index = vm.tableData.indexOf(vm.currentRow);

      vm.tableData.splice(index, 1);
      if (vm.totalCount > 0) {
        vm.totalCount = vm.totalCount - 1;
      }

      vm.$emit("successfulOperation_out", new Date().getTime());

      //如果当前行有id，则存入deleteRows数组，否则直接从表格删除即可
      if (vm.currentRow.id) {
        vm.currentRow.delete_flag = true;

        vm.deleteRows.push(vm.currentRow);
      }

      //设置输出的表格数据
      vm.setOutputData();
    },
    //在本地表格中编辑数据，不调用接口
    editRowInTable(editData) {
      const vm = this;
      const dataIndex = vm.dataConfig.dataIndex;
      //当前选中行无效则提示并返回
      if (!vm.currentRow) {
        this.$message({
          message: "请先选择需要编辑的行",
          type: "warning"
        });
        return;
      }

      //对当前行进行编辑操作，逐项赋值
      for (var i = 0; i < dataIndex.length; i++) {
        if (
          !_.isNull(editData[dataIndex[i]]) &&
          !_.isUndefined(editData[dataIndex[i]])
        ) {
          //深拷贝赋值
          vm.currentRow[dataIndex[i]] = _.cloneDeep(editData[dataIndex[i]]);
        }
      }
      //设置输出的表格数据
      vm.setOutputData();
    },
    setOutputData() {
      const vm = this;
      let outputData = [];
      //在增删改数据后进行排序，比如在分时费率界面新增时段后按照时段进行排序
      vm.sortByFrontEnd(vm.dataConfig.orders);

      outputData = outputData.concat(vm.tableData, vm.deleteRows);
      vm.$emit("update:data", vm.tableData);
      vm.$emit("outputData_out", outputData);
      vm.selectFirstRow();
    },
    //前端进行排序，对于没有分页的表格可以用前端排序，有分页的还是要用后端排序
    sortByFrontEnd(orders) {
      const vm = this;
      if (vm._.isArray(orders) && orders.length > 0) {
        orders = vm._.orderBy(orders, ["priority"], ["asc"]);
        const sortKeys = [];
        const orderTypes = [];
        vm._(orders).forEach(function (order) {
          sortKeys.push(order.propertyLabel);
          orderTypes.push(order.orderType);
        });
        const sortedData = vm._.orderBy(vm.tableData, sortKeys, orderTypes);
        vm.tableData = sortedData;
      }
    },
    //当前行变化
    handlerCurrentChange(currentRow) {
      const vm = this;
      const dataIndex = vm.dataConfig.dataIndex;
      let outRecord = {};
      if (vm._.isNull(currentRow)) {
        for (let i = 0; i < dataIndex.length; i++) {
          outRecord[dataIndex[i]] = "";
        }
        outRecord.id = -1;
      } else {
        outRecord = currentRow;
      }
      vm.currentRow = currentRow;

      this.$emit("record_out", outRecord);
    },
    //分页大小变化
    handleSizeChange(val) {
      this.currentPage = 1;
      this.getTableData();
    },
    //分页当前页变化
    handleCurrentPageChange(val) {
      this.getTableData();
    },
    refreshTable() {
      const vm = this;
      if (vm.getTableNum > 0) {
        this.getTableData();
      }
    },
    //表格列排序条件变化
    sortChange(val) {
      const vm = this;
      let prop = val.prop;
      if (val.prop.indexOf("$text") !== -1) {
        prop = prop.substring(0, prop.length - 5);
      }
      if (val.order === null) {
        vm.orders =
          vm.dataConfig.orders && vm.dataConfig.orders[0]
            ? vm.dataConfig.orders
            : null;
      }
      if (val.order === "ascending") {
        vm.orders = [
          {
            orderType: "asc",
            priority: 1,
            propertyLabel: prop
          }
        ];
      } else if (val.order === "descending") {
        vm.orders = [
          {
            orderType: "desc",
            priority: 1,
            propertyLabel: prop
          }
        ];
      }
      this.currentPage = 1; //分页置为第一页
      vm.getTableData();
    }
  },
  mounted: function () {
    const vm = this;
    //设置从data直接获取值的情况
    this.setInputData();
    //设置通过默认值获取表格数据的情况
    if (vm.queryMode === "diff") {
      // 获取到设备表格数据
      this.getTableData();
    }
  },
  activated() {
    const vm = this;
    vm.activatedNum++;
    //第一次激活不执行逻辑，后续激活更新数据
    if (vm.activatedNum > 1) {
      vm.refreshTable();

      // //设置从data直接获取值的情况
      // this.setInputData();
      // //设置通过默认值获取表格数据的情况
      // if (vm.queryMode === "diff") {
      //   // 获取到设备表格数据
      //   this.getTableData();
      // }
    }
  }
};
</script>
<style lang="scss" scoped>
.table-container {
  height: 100%;
}
.row-detail {
  display: inline-block;
  width: 25px;
  height: 24px;
  cursor: pointer;
  background: url("./assets/details.png") no-repeat center center;
  border-radius: 50%;
}

.row-edit {
  width: 20px;
  height: 20px;
  display: inline-block;
  cursor: pointer;
  background: url("./assets/edit.png") no-repeat center center;
}

.row-delete {
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: inline-block;
  background: url("./assets/delete.png") no-repeat center center;
}
</style>
