<template>
  <div class="page">
    <el-tabs class="header" v-model="activeName" @tab-click="tabClick">
      <el-tab-pane label="管网设备关联" name="pipeRelation"></el-tab-pane>
      <el-tab-pane label="采集设备关联" name="gatherRelation"></el-tab-pane>
    </el-tabs>
    <div class="main">
      <pipeRelation v-show="activeName == 'pipeRelation'" ref="pipeRelation" />
      <gatherRelation
        v-show="activeName == 'gatherRelation'"
        ref="gatherRelation"
      />
    </div>
  </div>
</template>

<script>
import pipeRelation from "./pipeRelation";
import gatherRelation from "./gatherRelation";
export default {
  name: "index",
  components: {
    pipeRelation,
    gatherRelation
  },
  data() {
    return {
      activeName: "pipeRelation"
    };
  },
  methods: {
    tabClick(tab) {
      if (tab.name === "pipeRelation") {
        this.$refs.pipeRelation.CetTree_1_currentNode_out(
          this.$refs.pipeRelation.currentNode
        );
      } else if (tab.name === "gatherRelation") {
        this.$refs.gatherRelation.CetTree_1_currentNode_out(
          this.$refs.gatherRelation.currentNode
        );
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  min-width: 1600px;
}
.header {
  height: 40px;
  @include padding(0 J3);
  @include background_color(BG1);
  :deep(.el-tabs__item) {
    @include font_size(H3);
  }
  ::after {
    background: none;
  }
}
.main {
  height: calc(100% - 56px);
  @include margin_top(J3);
  @include padding(0);
}
</style>
