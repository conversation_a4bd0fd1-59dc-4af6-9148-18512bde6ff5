<template>
  <div class="project-cfg">
    <!-- 1弹窗组件 -->
    <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="CetDialog">
      <el-tabs v-model="activeName" class="eltabs" @tab-click="handleClick">
        <el-tab-pane label="产品" name="产品">
          <div class="tableTitle clearfix">
            <div class="fl">
              已选
              <span class="fcZS">{{ productIds.length || 0 }}</span>
              条
            </div>
            <CetButton
              class="fr mlJ1"
              v-bind="CetButton_add1"
              v-on="CetButton_add1.event"
            ></CetButton>
            <CetButton
              class="fr delBtn"
              v-bind="CetButton_del1"
              v-on="CetButton_del1.event"
            ></CetButton>
          </div>
          <CetTable
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
            @selection-change="selectionProductChanged"
          >
            <ElTableColumn v-bind="ElTableColumn_selection"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_product_name"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_product_type"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_product_price"></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_product_type_id"
            ></ElTableColumn>
            <ElTableColumn
              label="操作"
              width="94"
              header-align="center"
              align="center"
            >
              <template slot-scope="scope">
                <span class="handle" @click.stop="edit1(scope.row)">编辑</span>
                <span class="fcB1">|</span>
                <span class="handle delete" @click.stop="del1(scope.row)">
                  删除
                </span>
              </template>
            </ElTableColumn>
          </CetTable>
        </el-tab-pane>
        <el-tab-pane label="能耗" name="能耗">
          <div class="tableTitle clearfix">
            <div class="fl">
              已选
              <span class="fcZS">{{ energyIds.length || 0 }}</span>
              条
            </div>
            <CetButton
              class="fr mlJ1"
              v-bind="CetButton_add2"
              v-on="CetButton_add2.event"
            ></CetButton>
            <CetButton
              class="fr delBtn"
              v-bind="CetButton_del2"
              v-on="CetButton_del2.event"
            ></CetButton>
          </div>
          <CetTable
            :data.sync="CetTable_2.data"
            :dynamicInput.sync="CetTable_2.dynamicInput"
            v-bind="CetTable_2"
            v-on="CetTable_2.event"
            @selection-change="selectionEnergyChanged"
          >
            <ElTableColumn v-bind="ElTableColumn_selection"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_energy_type"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_unit_coefficient"
            ></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_unit_price"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_unit_type"></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_energy_type_id"
            ></ElTableColumn>
            <ElTableColumn
              label="操作"
              width="94"
              header-align="center"
              align="center"
            >
              <template slot-scope="scope">
                <span class="handle" @click.stop="edit2(scope.row)">编辑</span>
                <span class="fcB1">|</span>
                <span class="handle delete" @click.stop="del2(scope.row)">
                  删除
                </span>
              </template>
            </ElTableColumn>
          </CetTable>
        </el-tab-pane>
        <el-tab-pane label="折标煤系数" name="折标煤系数">
          <div class="tableTitle clearfix">
            <CetButton
              class="fr mlJ1"
              v-bind="CetButton_add4"
              v-on="CetButton_add4.event"
            ></CetButton>
            <CetButton
              class="fr delBtn"
              v-bind="CetButton_del4"
              v-on="CetButton_del4.event"
            ></CetButton>
          </div>
          <CetTable
            :data.sync="CetTable_4.data"
            :dynamicInput.sync="CetTable_4.dynamicInput"
            v-bind="CetTable_4"
            v-on="CetTable_4.event"
            @selection-change="selectionCoefficientChanged"
          >
            <ElTableColumn v-bind="ElTableColumn_selection"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_sourceenergytype"
            ></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_targetenergytype"
            ></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_coefficient"></ElTableColumn>
            <ElTableColumn
              label="操作"
              width="94"
              header-align="center"
              align="center"
            >
              <template slot-scope="scope">
                <span class="handle" @click.stop="edit4(scope.row)">编辑</span>
                <span class="fcB1">|</span>
                <span class="handle delete" @click.stop="del4(scope.row)">
                  删除
                </span>
              </template>
            </ElTableColumn>
          </CetTable>
        </el-tab-pane>
        <el-tab-pane label="单位转换配置" name="单位转换配置">
          <el-container style="height: 100%; padding: 0px">
            <UnitTransition
              :visibleTrigger_in="unitTransition.visibleTrigger_in"
            ></UnitTransition>
          </el-container>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer">
        <!-- <CetButton v-bind="CetButton_confirm" v-on="CetButton_confirm.event"></CetButton> -->
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </span>
    </CetDialog>
    <AddProduct
      :visibleTrigger_in="addProduct.visibleTrigger_in"
      :closeTrigger_in="addProduct.closeTrigger_in"
      :queryId_in="addProduct.queryId_in"
      :inputData_in="addProduct.inputData_in"
      :tableData="CetTable_1.data"
      @addProductFinished="addProductFinished"
    ></AddProduct>
    <AddEnergy
      :visibleTrigger_in="addEnergy.visibleTrigger_in"
      :closeTrigger_in="addEnergy.closeTrigger_in"
      :queryId_in="addEnergy.queryId_in"
      :inputData_in="addEnergy.inputData_in"
      :tableData="CetTable_2.data"
      @addEnergyFinished="addEnergyFinished"
    ></AddEnergy>
    <AddConvertedstandardcoalcoef
      :visibleTrigger_in="addConvertedstandardcoalcoef.visibleTrigger_in"
      :closeTrigger_in="addConvertedstandardcoalcoef.closeTrigger_in"
      :queryId_in="addConvertedstandardcoalcoef.queryId_in"
      :inputData_in="addConvertedstandardcoalcoef.inputData_in"
      :tableData="CetTable_4.data"
      @addConvertedstandardcoalcoefFinished="
        addConvertedstandardcoalcoefFinished
      "
      :projectEnergy_in="projectEnergy"
    ></AddConvertedstandardcoalcoef>
  </div>
</template>
<script>
import custom from "@/api/custom";
import AddProduct from "./AddProduct";
import AddEnergy from "./AddEnergy";
import AddConvertedstandardcoalcoef from "./AddConvertedstandardcoalcoef";
import UnitTransition from "./unitTransition";
import { mhCONST } from "@/config/const";
export default {
  name: "ProjectCfg",
  components: {
    AddProduct,
    AddEnergy,
    AddConvertedstandardcoalcoef,
    UnitTransition
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    effiencyTable() {
      return this.$store.state.effiencyTable;
    }
  },

  data(vm) {
    return {
      activeName: "产品",
      projectEnergy: [],
      productInfo: {},
      energyInfo: {},
      coefficientInfo: {},
      productIds: [],
      energyIds: [],
      coefficientIds: [],
      addProduct: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addEnergy: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addConvertedstandardcoalcoef: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      unitTransition: {
        visibleTrigger_in: new Date().getTime()
      },
      // uploadPath: "",
      CetDialog_1: {
        title: "设置",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        },
        data: {
          name: null,
          factorynumber: null,
          managementnumber: null,
          barcode: null,
          alignorg: null,
          calibrationtime: null,
          nextaligndate: null,
          verifycer: null,
          result: null
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "确定",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "关闭",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "diff", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryProductTable",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "project",
          dataIndex: [],
          modelList: ["product"],
          filters: [{ name: "id_in", operator: "EQ", prop: "id" }], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          id_in: null
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        "highlight-current-row": false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      CetTable_2: {
        //组件模式设置项
        queryMode: "diff", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryEnergyTable",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "project",
          dataIndex: [],
          modelList: ["projectenergytype"],
          filters: [{ name: "id_in", operator: "EQ", prop: "id" }], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          id_in: null
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        "highlight-current-row": false,
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_2_record_out,
          outputData_out: this.CetTable_2_outputData_out
        }
      },
      CetTable_3: {
        //组件模式设置项
        queryMode: "", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            // { name: "id_in", operator: "EQ", prop: "id" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          // id_in: null
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        "highlight-current-row": false,
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_2_record_out,
          outputData_out: this.CetTable_2_outputData_out
        }
      },
      CetTable_4: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryConvertedstandardcoalcoefTable",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "project",
          dataIndex: [],
          modelList: ["convertedstandardcoalcoef"],
          filters: [{ name: "id_in", operator: "EQ", prop: "id" }], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          // id_in: null
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_4_record_out,
          outputData_out: this.CetTable_4_outputData_out
        }
      },
      ElTableColumn_selection: {
        type: "selection", // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: "", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "39" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: "#", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "41" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_product_name: {
        //type: "index",      // selection 勾选 index 序号
        prop: "name", // 支持path a[0].b
        label: "产品名称", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_product_type: {
        //type: "index",      // selection 勾选 index 序号
        prop: "producttype$text", // 支持path a[0].b
        label: "产品类型", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_product_type_id: {
        //type: "index",      // selection 勾选 index 序号
        prop: "producttype", // 支持path a[0].b
        label: "产品类型ID", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_product_price: {
        //type: "index",      // selection 勾选 index 序号
        prop: "uniprice", // 支持path a[0].b
        label: "产品单价（元）", //列名
        headerAlign: "right",
        align: "right",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_energy_type: {
        //type: "index",      // selection 勾选 index 序号
        prop: "energytype$text", // 支持path a[0].b
        label: "能源类型", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_energy_type_id: {
        //type: "index",      // selection 勾选 index 序号
        prop: "energytype", // 支持path a[0].b
        label: "能源类型ID", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_name: {
        //type: "index",      // selection 勾选 index 序号
        prop: "name", // 支持path a[0].b
        label: "名称", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_unit_coefficient: {
        //type: "index",      // selection 勾选 index 序号
        prop: "unitmultiplier$text", // 支持path a[0].b
        label: "单位系数", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue, index) {
          return cellValue || "--";
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_unit_price: {
        //type: "index",      // selection 勾选 index 序号
        prop: "unitprice", // 支持path a[0].b
        label: "单价（元）", //列名
        headerAlign: "right",
        align: "right",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_unit_type: {
        //type: "index",      // selection 勾选 index 序号
        prop: "unitsymbol$text", // 支持path a[0].b
        label: "单位类型", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue, index) {
          return cellValue || "--";
        }
      },
      ElTableColumn_efficiency_name: {
        //type: "index",      // selection 勾选 index 序号
        prop: "name", // 支持path a[0].b
        label: "能效指标名称", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_efficiency_unit_type: {
        //type: "index",      // selection 勾选 index 序号
        prop: "unittypeName", // 支持path a[0].b
        label: "能效单元类型", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_energytype: {
        //type: "index",      // selection 勾选 index 序号
        prop: "energytypeName", // 支持path a[0].b
        label: "能源类型", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_sourceenergytype: {
        //type: "index",      // selection 勾选 index 序号
        prop: "sourceenergytype", // 支持path a[0].b
        label: "原能源类型", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (row, column, cellValue, index) {
          var obj = vm.projectEnergy.find(
            item => item.energytype === cellValue
          );
          return (obj && obj.name) || "--";
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_targetenergytype: {
        //type: "index",      // selection 勾选 index 序号
        prop: "targetenergytype", // 支持path a[0].b
        label: "目标能源类型", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (row, column, cellValue, index) {
          var obj = vm.projectEnergy.find(
            item => item.energytype === cellValue
          );
          return (obj && obj.name) || "--";
        }
      },
      ElTableColumn_aggregationcycle: {
        //type: "index",      // selection 勾选 index 序号
        prop: "aggregationcycleName", // 支持path a[0].b
        label: "分析周期", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_producttype: {
        //type: "index",      // selection 勾选 index 序号
        prop: "producttypeName", // 支持path a[0].b
        label: "产品", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_coef: {
        //type: "index",      // selection 勾选 index 序号
        prop: "coef", // 支持path a[0].b
        label: "系数", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_coefficient: {
        //type: "index",      // selection 勾选 index 序号
        prop: "coef", // 支持path a[0].b
        label: "转换系数", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      // add1组件
      CetButton_add1: {
        visible_in: true,
        disable_in: false,
        title: "新增产品",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add1_statusTrigger_out
        }
      },
      // del1组件
      CetButton_del1: {
        visible_in: true,
        disable_in: true,
        title: "批量删除",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_del1_statusTrigger_out
        }
      },
      // edit1组件
      CetButton_edit1: {
        visible_in: true,
        disable_in: false,
        title: "修改",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_edit1_statusTrigger_out
        }
      },
      // add2组件
      CetButton_add2: {
        visible_in: true,
        disable_in: false,
        title: "新增能源",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add2_statusTrigger_out
        }
      },
      // del2组件
      CetButton_del2: {
        visible_in: true,
        disable_in: true,
        title: "批量删除",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_del2_statusTrigger_out
        }
      },
      // edit2组件
      CetButton_edit2: {
        visible_in: true,
        disable_in: false,
        title: "修改",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_edit2_statusTrigger_out
        }
      },
      CetButton_add4: {
        visible_in: true,
        disable_in: false,
        title: "新增折标煤系数",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add4_statusTrigger_out
        }
      },
      CetButton_del4: {
        visible_in: true,
        disable_in: true,
        title: "批量删除",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_del4_statusTrigger_out
        }
      },
      CetButton_edit4: {
        visible_in: true,
        disable_in: false,
        title: "修改",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_edit4_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.CetTable_1.dynamicInput.id_in = this.projectId;
      this.CetTable_2.dynamicInput.id_in = this.projectId;
      this.CetTable_4.dynamicInput.id_in = this.projectId;
      this.CetTable_1.queryTrigger_in = new Date().getTime();
      this.CetTable_2.queryTrigger_in = new Date().getTime();
      this.activeName = "产品";
      this.getProjectEnergytype(() => {
        this.CetTable_4.queryTrigger_in = new Date().getTime();
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {
      // var vm = this;
    },
    inputData_in(val) {
      this.CetDialog_1.data = val;
    },
    effiencyTable(val) {
      this.CetTable_3.data = val;
    }
  },

  methods: {
    getProjectEnergytype(callback) {
      custom.getProjectEnergy(this.projectId).then(res => {
        if (res.code === 0) {
          let data = this._.get(res, "data", []);
          this.projectEnergy = this._.cloneDeep(data);
          callback && callback();
        }
      });
    },
    addProductFinished() {
      this.CetTable_1.queryTrigger_in = new Date().getTime();
      // this.$emit("refresh")
    },
    addEnergyFinished() {
      this.CetTable_2.queryTrigger_in = new Date().getTime();
      this.getProjectEnergytype();
      // this.$emit("refresh")
    },
    addConvertedstandardcoalcoefFinished() {
      this.CetTable_4.queryTrigger_in = new Date().getTime();
      // this.$emit("refresh")
    },
    selectionProductChanged(val) {
      this.productIds = [];
      val.forEach(item => {
        this.productIds.push(item.id);
      });
      if (this.productIds.length) {
        this.CetButton_del1.disable_in = false;
      } else {
        this.CetButton_del1.disable_in = true;
      }
    },
    selectionEnergyChanged(val) {
      this.energyIds = [];
      val.forEach(item => {
        this.energyIds.push(item.id);
      });
      if (this.energyIds.length) {
        this.CetButton_del2.disable_in = false;
      } else {
        this.CetButton_del2.disable_in = true;
      }
    },
    selectionCoefficientChanged(val) {
      this.coefficientIds = [];
      val.forEach(item => {
        this.coefficientIds.push(item.id);
      });
      if (this.coefficientIds.length) {
        this.CetButton_del4.disable_in = false;
      } else {
        this.CetButton_del4.disable_in = true;
      }
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
      // this.$emit("refresh");
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_add1_statusTrigger_out(val) {
      this.addProduct.inputData_in = {};
      this.addProduct.visibleTrigger_in = new Date().getTime();
    },
    del1(row) {
      this.$confirm("确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let params = {
            projectId: this.projectId,
            productIds: [row.id]
          };
          custom["delProduct"](params).then(response => {
            if (response.code === 0) {
              this.$message({
                message: "删除成功",
                type: "success"
              });
              this.CetTable_1.queryTrigger_in = new Date().getTime();
              // this.$emit("refresh")
            }
          });
        })
        .catch(e => {
          console.log(e);

          this.$message({
            type: "info",
            message: "已取消"
          });
        });
    },
    CetButton_del1_statusTrigger_out() {
      this.$confirm("确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let params = {
            projectId: this.projectId,
            productIds: this.productIds
          };
          custom["delProduct"](params).then(response => {
            if (response.code === 0) {
              this.$message({
                message: "删除成功",
                type: "success"
              });
              this.CetTable_1.queryTrigger_in = new Date().getTime();
              // this.$emit("refresh")
            }
          });
        })
        .catch(e => {
          console.log(e);

          this.$message({
            type: "info",
            message: "已取消"
          });
        });
    },
    CetButton_edit1_statusTrigger_out(val) {
      this.addProduct.inputData_in = this._.cloneDeep(this.productInfo);
      this.addProduct.visibleTrigger_in = new Date().getTime();
    },
    edit1(val) {
      this.addProduct.inputData_in = this._.cloneDeep(val);
      this.addProduct.visibleTrigger_in = new Date().getTime();
    },
    CetButton_add2_statusTrigger_out(val) {
      this.addEnergy.inputData_in = {};
      this.addEnergy.visibleTrigger_in = new Date().getTime();
    },
    del2(row) {
      this.$confirm("确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let params = {
            projectId: this.projectId,
            energyIds: [row.id]
          };
          custom["delEnergy"](params).then(response => {
            if (response.code === 0) {
              this.$message({
                message: "删除成功",
                type: "success"
              });
              this.CetTable_2.queryTrigger_in = new Date().getTime();
              // this.$emit("refresh")
            }
          });
        })
        .catch(e => {
          this.$message({
            type: "info",
            message: "已取消"
          });
        });
    },
    CetButton_del2_statusTrigger_out(val) {
      this.$confirm("确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let params = {
            projectId: this.projectId,
            energyIds: this.energyIds
          };
          custom["delEnergy"](params).then(response => {
            if (response.code === 0) {
              this.$message({
                message: "删除成功",
                type: "success"
              });
              this.CetTable_2.queryTrigger_in = new Date().getTime();
              // this.$emit("refresh")
            }
          });
        })
        .catch(e => {
          this.$message({
            type: "info",
            message: "已取消"
          });
        });
    },
    CetButton_edit2_statusTrigger_out(val) {
      this.addEnergy.inputData_in = this._.cloneDeep(this.energyInfo);
      this.addEnergy.visibleTrigger_in = new Date().getTime();
    },
    edit2(val) {
      this.addEnergy.inputData_in = this._.cloneDeep(val);
      this.addEnergy.visibleTrigger_in = new Date().getTime();
    },
    CetButton_add4_statusTrigger_out(val) {
      this.addConvertedstandardcoalcoef.inputData_in = {};
      this.addConvertedstandardcoalcoef.visibleTrigger_in =
        new Date().getTime();
    },
    CetButton_del4_statusTrigger_out(val) {
      this.$confirm("确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let params = {
            projectId: this.projectId,
            energyIds: this.coefficientIds
          };
          custom["delConvertedstandardcoalcoef"](params).then(response => {
            if (response.code === 0) {
              this.$message({
                message: "删除成功",
                type: "success"
              });
              this.CetTable_4.queryTrigger_in = new Date().getTime();
              // this.$emit("refresh")
            }
          });
        })
        .catch(e => {
          this.$message({
            type: "info",
            message: "已取消"
          });
        });
    },
    del4(row) {
      this.$confirm("确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let params = {
            projectId: this.projectId,
            energyIds: [row.id]
          };
          custom["delConvertedstandardcoalcoef"](params).then(response => {
            if (response.code === 0) {
              this.$message({
                message: "删除成功",
                type: "success"
              });
              this.CetTable_4.queryTrigger_in = new Date().getTime();
              // this.$emit("refresh")
            }
          });
        })
        .catch(e => {
          this.$message({
            type: "info",
            message: "已取消"
          });
        });
    },
    CetButton_edit4_statusTrigger_out(val) {
      this.addConvertedstandardcoalcoef.inputData_in = this._.cloneDeep(
        this.coefficientInfo
      );
      this.addConvertedstandardcoalcoef.visibleTrigger_in =
        new Date().getTime();
    },
    edit4(val) {
      this.addConvertedstandardcoalcoef.inputData_in = this._.cloneDeep(val);
      this.addConvertedstandardcoalcoef.visibleTrigger_in =
        new Date().getTime();
    },
    CetDialog_1_open_out(val) {},
    CetDialog_1_close_out(val) {},
    ElInput_1_change_out(val) {},
    ElInput_1_input_out(val) {},
    ElInput_2_change_out(val) {},
    ElInput_2_input_out(val) {},
    ElInput_3_change_out(val) {},
    ElInput_3_input_out(val) {},
    // 1表格输出
    CetTable_1_record_out(val) {
      this.productInfo = this._.cloneDeep(val);
      if (val.id == -1) {
        this.CetButton_edit1.disable_in = true;
      } else {
        this.CetButton_edit1.disable_in = false;
      }
    },
    CetTable_1_outputData_out(val) {},
    // 2表格输出
    CetTable_2_record_out(val) {
      this.energyInfo = this._.cloneDeep(val);
      if (val.id == -1) {
        this.CetButton_edit2.disable_in = true;
      } else {
        this.CetButton_edit2.disable_in = false;
      }
    },
    CetTable_2_outputData_out(val) {},
    // 3表格输出
    CetTable_3_record_out(val) {},
    CetTable_3_outputData_out(val) {},
    CetTable_4_record_out(val) {
      this.coefficientInfo = this._.cloneDeep(val);
      if (val.id == -1) {
        this.CetButton_edit4.disable_in = true;
      } else {
        this.CetButton_edit4.disable_in = false;
      }
    },
    CetTable_4_outputData_out(val) {},
    handleClick(tab, event) {
      if (tab.name === "单位转换配置") {
        this.unitTransition.visibleTrigger_in = new Date().getTime();
      }
    }
  },

  activated: function () {},
  mounted() {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    padding: 0 !important;
    @include background_color(BG);
  }
}
.eltabs {
  height: 500px;
  border-radius: 0 !important;
  padding: 0 !important;
  background-color: transparent !important;
  display: flex;
  flex-direction: column;
  :deep(.el-tabs__header) {
    @include background_color(BG1);
    margin: 0;
    padding-left: mh-get(J3);
  }
  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }
  :deep(.el-tabs__content) {
    @include background_color(BG1);
    @include border_radius(C2);
    @include margin(J1);
    @include padding(J2);
    flex: 1;
    min-height: 0;
    display: flex;
    .el-tab-pane {
      flex: 1;
      display: flex;
      flex-direction: column;
      .table-container {
        flex: 1;
      }
    }
  }
}
.tableTitle {
  @include line_height(Hm);
  @include margin_bottom(J6);
  .fr {
    float: right;
  }
}
.handle {
  cursor: pointer;
  &:hover {
    @include font_color(ZS);
  }
  &.delete:hover {
    @include font_color(Sta3);
  }
}
.delBtn:hover {
  :deep(.el-button) {
    @include font_color(Sta3);
  }
}
</style>
