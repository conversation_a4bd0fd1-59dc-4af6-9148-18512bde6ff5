<template>
  <div class="page powerQualityAnalyzer">
    <div class="menu_group">
      <el-radio-group v-model="selectedMenu">
        <el-radio-button v-for="menu in menus" :label="menu" :key="menu">
          {{ menu }}
        </el-radio-button>
      </el-radio-group>
    </div>
    <el-container class="content" style="height: 100%">
      <el-aside width="315px">
        <div class="line-bottom mb5">
          <el-input
            placeholder="输入关键字以检索"
            class="device-search"
            v-model="filterText"
          ></el-input>
        </div>
        <el-tree
          ref="powerTree"
          style="width: 100%"
          :data="treeData"
          :lazy="true"
          node-key="tree_id"
          :props="treeProps"
          :load="loadTree"
          :filter-node-method="filterNode"
          :highlight-current="true"
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
        ></el-tree>
      </el-aside>
      <el-container style="height: 100%; padding: 0px">
        <SinglePointAnalysis
          :selectedMenu="selectedMenu"
          v-show="selectedMenu === '单点分析'"
          @jump="jumpTab"
          @changeQueryTime="changeQueryTime"
        />
        <voltage-variation
          :selectedMenu="selectedMenu"
          :wrapTime="queryTime"
          v-show="selectedMenu === '故障诊断'"
        ></voltage-variation>
        <tolerance
          :selectedMenu="selectedMenu"
          :wrapTime="queryTime"
          v-show="selectedMenu === '容忍度分析'"
        ></tolerance>
        <harmonic-analysis
          :selectedMenu="selectedMenu"
          v-show="selectedMenu === '谐波分析'"
        ></harmonic-analysis>
        <PQReport
          :selectedMenu="selectedMenu"
          v-show="selectedMenu === '评估报表'"
        ></PQReport>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import common from "eem-utils/common";
import SinglePointAnalysis from "./SinglePointAnalysis";
import VoltageVariation from "./VoltageVariation";
import HarmonicAnalysis from "./HarmonicAnalysis";
import Tolerance from "./Tolerance";
import PQReport from "./PQReport";
export default {
  // 电能质量分析
  name: "PowerQualityAnalyzer",
  components: {
    SinglePointAnalysis,
    VoltageVariation,
    Tolerance,
    PQReport,
    HarmonicAnalysis
  },

  computed: {
    defaultExpandedKeys() {
      return [];
    }
  },

  data() {
    return {
      treeData: [],
      treeProps: {
        children: "children",
        label: "name",
        isLeaf: "leaf"
      },
      filterText: "",
      currentNode: null,
      menus: ["单点分析", "故障诊断", "容忍度分析", "谐波分析", "评估报表"],
      selectedMenu: "单点分析",
      queryTime: null
    };
  },
  watch: {
    currentNode: {
      deep: true,
      handler: function (val, oldVal) {
        if (!(this._.get(val, "data.id") && this._.get(val, "data.modelLabel")))
          return;
        if (
          val.data.id === this._.get(oldVal, "data.id") &&
          val.data.modelLabel === this._.get(oldVal, "data.modelLabel")
        ) {
          return;
        }
        this.$store.commit("setPowerQualityNode", this.currentNode);
      }
    },
    selectedMenu(val) {
      this.$store.commit("setPowerQualityMenu", val);
    },
    filterText(val) {
      this.$refs.powerTree.filter(val);
    }
  },

  methods: {
    jumpTab(val) {
      this.selectedMenu = val;
    },
    changeQueryTime(queryTime) {
      console.log(queryTime);
      this.queryTime = queryTime;
    },
    CetRadio_1_resulttext_out(val) {},
    filterNode(value, data) {
      if (!value) return true;
      return data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1;
    },
    loadTree(node, resolve) {
      /* if (!node || !node["parent"]) {
        // 第一层显示固定的内容
        this.treeData = [
          {
            id: 0,
            name: "电能质量节点",
            tree_id: "root_0"
          }
        ];
      } else */
      if (node.level === 0) {
        // 第一层显示园区的内容
        common.requestData(
          {
            url: "/data-center/v1/project/queryData",
            data: {
              modelLabel: "dcbase"
            }
          },
          res => {
            if (!res || !Array.isArray(res)) {
              return;
            }
            resolve(res);
            this.$nextTick(() => {
              const currentNode = this._.get(res, "[0]");
              const node = this.$refs.powerTree.getNode(currentNode.tree_id);
              node.loaded = false;
              node.expand();
            });
          }
        );
      } else if (node.level === 1) {
        // 第二层加载所有开关柜下的所有的电能质量设备
        common.requestData(
          {
            url: `/data-center/v1/project/queryMonitoredDevice?parentId=${node.data.id}&parentLabel=${node.data.modelLabel}&meterTypes=9`,
            data: ["linesegmentwithswitch"]
          },
          res => {
            const data = res;
            if (!res) {
              res = [];
            }
            data.forEach(item => {
              item.tree_id = item.modelLabel + "_" + item.id;
              item.leaf = true;
            });
            if (this._.get(data, "[0]") && !this.currentNode) {
              const currentNode = this._.get(data, "[0]");
              this.$nextTick(() => {
                this.$refs.powerTree.setCurrentKey(currentNode.tree_id);
                this.currentNode = this.$refs.powerTree.getNode(
                  currentNode.tree_id
                );
              });
            }
            resolve(data);
          }
        );
      }

      /* else if (node.level === 2) {
        // 加载测点
        common.requestData(
          {
            url: `/data-center/v1/project/queryMeasuredByInfo?parentId=${node.data.id}&parentLabel=${node.data.modelLabel}`,
            data: [9]
          },
          res => {
            res.forEach(item => {
              item.tree_id = item.modelLabel + "_" + item.id;
              item.name = item.pecName;
              item.leaf = true;
            });
            resolve(res);
          }
        );
      } */
    },
    handleNodeClick(obj, node) {
      this.currentNode = node;
    }
  },

  created: function () {},
  mounted() {},
  activated() {
    this.$store.commit("setPowerQualityVisibi", true);
  },
  deactivated() {
    this.$store.commit("setPowerQualityVisibi", false);
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}

.menu_group {
  text-align: center;
  padding: 5px 0;
}

.content {
  height: calc(100% - 40px);
}
</style>
<style lang="scss">
.menu_group .el-radio-button__inner {
  padding: 7px 20px;
}
.line-bottom {
  border-bottom: 1px solid rgb(235, 235, 235);
}
.device-search {
  // margin: 8px 0px;
  margin: 0;
  @include margin_bottom(J1);
  @include margin_top(J1);
}
</style>
