<template>
  <!-- 1弹窗组件 -->
  <div>
    <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
      <CetForm
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
      >
        <div class="title">基本信息</div>
        <el-form-item label="设备编号" prop="managementnumber">
          <ElInput
            v-model="CetForm_1.data.managementnumber"
            v-bind="ElInput_1"
            v-on="ElInput_1.event"
          ></ElInput>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <ElInput
            v-model="CetForm_1.data.name"
            v-bind="ElInput_2"
            v-on="ElInput_2.event"
          ></ElInput>
        </el-form-item>
        <el-form-item label="测量对象" prop="measurementname">
          <ElInput
            v-model="CetForm_1.data.measurementname"
            v-bind="ElInput_3"
            v-on="ElInput_3.event"
          >
            <span
              slot="suffix"
              @click="toMeasureObj"
              class="toMeasureObj"
            ></span>
          </ElInput>
        </el-form-item>
        <div class="title">测量参数</div>
        <el-form-item label="准确度" prop="accuracy">
          <ElSelect
            v-model="CetForm_1.data.accuracy"
            v-bind="ElSelect_1"
            v-on="ElSelect_1.event"
          >
            <ElOption
              v-for="item in ElOption_1.options_in"
              :key="item[ElOption_1.key]"
              :label="item[ElOption_1.label]"
              :value="item[ElOption_1.value]"
              :disabled="item[ElOption_1.disabled]"
            ></ElOption>
          </ElSelect>
        </el-form-item>
        <el-form-item label="准确度要求" prop="accuracyrequire">
          <ElSelect
            v-model="CetForm_1.data.accuracyrequire"
            v-bind="ElSelect_2"
            v-on="ElSelect_2.event"
          >
            <ElOption
              v-for="item in ElOption_1.options_in"
              :key="item[ElOption_1.key]"
              :label="item[ElOption_1.label]"
              :value="item[ElOption_1.value]"
              :disabled="item[ElOption_1.disabled]"
            ></ElOption>
          </ElSelect>
        </el-form-item>
        <el-form-item label="校准周期（天）" prop="plannedcalibrationcycle">
          <ElInputNumber
            v-model="CetForm_1.data.plannedcalibrationcycle"
            v-bind="ElInputNumber_1"
            v-on="ElInputNumber_1.event"
          ></ElInputNumber>
        </el-form-item>
        <el-form-item label="计量单位" prop="measurementunit">
          <ElInput
            v-model="CetForm_1.data.measurementunit"
            v-bind="ElInput_4"
            v-on="ElInput_4.event"
          ></ElInput>
        </el-form-item>
        <el-form-item label="计量范围" prop="measurementrange">
          <ElInput
            v-model="CetForm_1.data.measurementrange"
            v-bind="ElInput_5"
            v-on="ElInput_5.event"
          ></ElInput>
        </el-form-item>
        <el-form-item label="计量介质" prop="metrologymedium">
          <ElInput
            v-model="CetForm_1.data.metrologymedium"
            v-bind="ElInput_6"
            v-on="ElInput_6.event"
          ></ElInput>
        </el-form-item>
        <div class="title">设备信息</div>
        <el-form-item label="设备状态" prop="usagestate">
          <ElSelect
            v-model="CetForm_1.data.usagestate"
            v-bind="ElSelect_3"
            v-on="ElSelect_3.event"
          >
            <ElOption
              v-for="item in ElOption_3.options_in"
              :key="item[ElOption_3.key]"
              :label="item[ElOption_3.label]"
              :value="item[ElOption_3.value]"
              :disabled="item[ElOption_3.disabled]"
            ></ElOption>
          </ElSelect>
        </el-form-item>
        <el-form-item label="出厂编号" prop="factorynumber">
          <ElInput
            v-model="CetForm_1.data.factorynumber"
            v-bind="ElInput_7"
            v-on="ElInput_7.event"
          ></ElInput>
        </el-form-item>
        <el-form-item label="型号规格" prop="modelspecification">
          <ElInput
            v-model="CetForm_1.data.modelspecification"
            v-bind="ElInput_8"
            v-on="ElInput_8.event"
          ></ElInput>
        </el-form-item>
        <el-form-item label="生产厂家" prop="manufacturer">
          <ElInput
            v-model="CetForm_1.data.manufacturer"
            v-bind="ElInput_9"
            v-on="ElInput_9.event"
          ></ElInput>
        </el-form-item>
        <el-form-item label="生产日期" prop="productiondate">
          <el-date-picker
            v-model="CetForm_1.data.productiondate"
            v-bind="CetDatePicker_1.config"
            placeholder="选择日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="启用日期" prop="operationdate">
          <el-date-picker
            v-model="CetForm_1.data.operationdate"
            v-bind="CetDatePicker_2.config"
            placeholder="选择日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="使用期限（年）" prop="servicelife">
          <ElInputNumber
            v-model="CetForm_1.data.servicelife"
            v-bind="ElInputNumber_2"
            v-on="ElInputNumber_2.event"
          ></ElInputNumber>
        </el-form-item>
        <el-form-item label="检定方式" prop="checkmethod">
          <ElSelect
            v-model="CetForm_1.data.checkmethod"
            v-bind="ElSelect_4"
            v-on="ElSelect_4.event"
          >
            <ElOption
              v-for="item in ElOption_4.options_in"
              :key="item[ElOption_4.key]"
              :label="item[ElOption_4.label]"
              :value="item[ElOption_4.value]"
              :disabled="item[ElOption_4.disabled]"
            ></ElOption>
          </ElSelect>
        </el-form-item>
        <el-form-item label="ABC分类" prop="abcclass">
          <ElSelect
            v-model="CetForm_1.data.abcclass"
            v-bind="ElSelect_5"
            v-on="ElSelect_5.event"
          >
            <ElOption
              v-for="item in ElOption_5.options_in"
              :key="item[ElOption_5.key]"
              :label="item[ElOption_5.label]"
              :value="item[ElOption_5.value]"
              :disabled="item[ElOption_5.disabled]"
            ></ElOption>
          </ElSelect>
        </el-form-item>
        <el-form-item label="强制检定" prop="forcedcheck">
          <ElSelect
            v-model="CetForm_1.data.forcedcheck"
            v-bind="ElSelect_6"
            v-on="ElSelect_6.event"
          >
            <ElOption
              v-for="item in ElOption_6.options_in"
              :key="item[ElOption_6.key]"
              :label="item[ElOption_6.label]"
              :value="item[ElOption_6.value]"
              :disabled="item[ElOption_6.disabled]"
            ></ElOption>
          </ElSelect>
        </el-form-item>
        <el-form-item label="是否属于石油专用" prop="oilspecial">
          <ElSelect
            v-model="CetForm_1.data.oilspecial"
            v-bind="ElSelect_7"
            v-on="ElSelect_7.event"
          >
            <ElOption
              v-for="item in ElOption_7.options_in"
              :key="item[ElOption_7.key]"
              :label="item[ElOption_7.label]"
              :value="item[ElOption_7.value]"
              :disabled="item[ElOption_7.disabled]"
            ></ElOption>
          </ElSelect>
        </el-form-item>
      </CetForm>
      <span slot="footer">
        <CetButton
          class="mrJ1"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </span>
    </CetDialog>
    <measureObj
      :visibleTrigger_in="measureObj.visibleTrigger_in"
      :closeTrigger_in="measureObj.closeTrigger_in"
      :queryId_in="measureObj.queryId_in"
      :inputData_in="measureObj.inputData_in"
      @CetButton_confirm_out="measureObj_CetButton_confirm_out"
    />
  </div>
</template>
<script>
import { httping } from "@omega/http";
import measureObj from "./measureObj.vue";
import common from "eem-utils/common";
export default {
  name: "addDevice",
  components: {
    measureObj
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "确认",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "150px",
        rules: {
          name: [
            {
              required: true,
              message: "请输入名称",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name,
            common.check_space
          ],
          managementnumber: [
            {
              required: true,
              message: "请输入设备编号",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.check_space
          ],
          measurementname: [
            {
              required: true,
              message: "请选择测量对象",
              trigger: ["blur", "change"]
            }
          ],
          measurementunit: [common.check_name],
          measurementrange: [common.check_name],
          metrologymedium: [common.check_name],
          factorynumber: [common.check_name],
          modelspecification: [common.check_name],
          manufacturer: [common.check_stringLessThan255]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInput_2: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInput_3: {
        value: "",
        placeholder: "请选择测量对象",
        style: {
          width: "100%"
        },
        disabled: true,
        event: {}
      },
      ElSelect_1: {
        value: "5.0",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_1: {
        options_in: [
          {
            id: "5.0",
            text: "5.0"
          },
          {
            id: "4.5",
            text: "4.5"
          },
          {
            id: "4.0",
            text: "4.0"
          },
          {
            id: "3.5",
            text: "3.5"
          },
          {
            id: "3.0",
            text: "3.0"
          },
          {
            id: "2.5",
            text: "2.5"
          },
          {
            id: "2.0",
            text: "2.0"
          },
          {
            id: "1.5",
            text: "1.5"
          },
          {
            id: "1.0",
            text: "1.0"
          },
          {
            id: "0.5S",
            text: "0.5S"
          },
          {
            id: "0.5",
            text: "0.5"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: "5.0",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInput_4: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInput_5: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInput_6: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElSelect_3: {
        value: 1,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_3: {
        options_in: [
          {
            id: 1,
            text: "在用"
          },
          {
            id: 2,
            text: "停用"
          },
          {
            id: 3,
            text: "备用"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElInput_7: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInput_8: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInput_9: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElSelect_4: {
        value: 1,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_4: {
        options_in: [
          {
            id: 1,
            text: "内部"
          },
          {
            id: 2,
            text: "送外"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_5: {
        value: 1,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_5: {
        options_in: [
          {
            id: 1,
            text: "是"
          },
          {
            id: 2,
            text: "否"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_6: {
        value: "A",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_6: {
        options_in: [
          {
            id: "A",
            text: "A"
          },
          {
            id: "B",
            text: "B"
          },
          {
            id: "C",
            text: "C"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_7: {
        value: 1,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_7: {
        options_in: [
          {
            id: 1,
            text: "内部"
          },
          {
            id: 2,
            text: "送外"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetDatePicker_1: {
        disable_in: false,
        val: this.$moment().startOf("day").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "date",
          format: "yyyy-MM-dd",
          rangeSeparator: "-",
          style: {
            display: "block"
          }
        }
      },
      CetDatePicker_2: {
        disable_in: false,
        val: this.$moment().startOf("day").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "date",
          format: "yyyy-MM-dd",
          rangeSeparator: "-",
          style: {
            display: "block"
          }
        }
      },
      ElInputNumber_1: {
        value: "",
        style: {
          width: "100%"
        },
        min: 0,
        max: 9999,
        step: 2,
        precision: 0,
        controlsPosition: "",
        placeholder: "请输入内容",
        controls: false,
        event: {}
      },
      ElInputNumber_2: {
        ...common.check_numberInt,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      measureObj: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    },
    inputData_in(val) {
      // 更改标题
      if (val.id) {
        this.CetDialog_1.title = "修改 计量器具";
      } else {
        this.CetDialog_1.title = "新建 计量器具";
      }
      if (val.id) {
        setTimeout(() => {
          this.CetForm_1.data = val;
        }, 0);
      } else {
        this.initFormDate();
      }
      this.$nextTick(() => {
        this.CetForm_1.resetTrigger_in = new Date().getTime();
      });
    }
  },

  methods: {
    initFormDate() {
      this.CetForm_1.data.monitorid = null;
      this.CetForm_1.data.monitorlabel = null;
      this.CetForm_1.data.measurementname = null;
      this.CetForm_1.data.id = null;
      this.CetForm_1.data.managementnumber = null;
      this.CetForm_1.data.name = null;
      this.CetForm_1.data.accuracy = "5.0";
      this.CetForm_1.data.accuracyrequire = "5.0";
      this.CetForm_1.data.plannedcalibrationcycle = null;
      this.CetForm_1.data.measurementunit = null;
      this.CetForm_1.data.measurementrange = null;
      this.CetForm_1.data.metrologymedium = null;
      this.CetForm_1.data.usagestate = 1;
      this.CetForm_1.data.factorynumber = null;
      this.CetForm_1.data.modelspecification = null;
      this.CetForm_1.data.manufacturer = null;
      this.CetForm_1.data.productiondate = null;
      this.CetForm_1.data.operationdate = null;
      this.CetForm_1.data.servicelife = null;
      this.CetForm_1.data.checkmethod = 1;
      this.CetForm_1.data.abcclass = 1;
      this.CetForm_1.data.forcedcheck = "A";
      this.CetForm_1.data.oilspecial = 1;
    },
    // 打开选择测量对象
    toMeasureObj() {
      if (this.CetForm_1.data.monitorid) {
        this.measureObj.inputData_in = {
          monitorid: this.CetForm_1.data.monitorid,
          monitorlabel: this.CetForm_1.data.monitorlabel,
          name: this.CetForm_1.data.measurementname
        };
      } else {
        this.measureObj.inputData_in = {};
      }
      this.measureObj.visibleTrigger_in = new Date().getTime();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_saveData_out(val) {
      var param = [];
      param.push(val);
      param[0].modelLabel = "measuringinstrument";
      param[0].id = this.CetForm_1.data.id;
      httping({
        url: "/oil-field-service/v1/measure/equip/save",
        data: param,
        method: "POST"
      }).then(response => {
        if (response.code == 0) {
          this.$message({
            type: "success",
            message: "保存成功"
          });
          this.CetDialog_1.closeTrigger_in = new Date().getTime();
          this.$emit("finishTrigger_out", new Date().getTime());
        }
      });
    },
    measureObj_CetButton_confirm_out(val) {
      if (val) {
        this.CetForm_1.data.measurementname = val.name;
        this.CetForm_1.data.monitorid = val.id;
        this.CetForm_1.data.monitorlabel = val.modelLabel;
      } else {
        this.CetForm_1.data.measurementname = null;
        this.CetForm_1.data.monitorid = null;
        this.CetForm_1.data.monitorlabel = null;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.toMeasureObj {
  display: inline-block;
  width: 30px;
  height: 30px;
  cursor: pointer;
  background: url("../assets/u2825.png") no-repeat center center;
}
.title {
  width: 100%;
  height: 32px;
  border-radius: 11px;
  text-align: left;
  border-bottom: 1px solid;
  @include margin_bottom(J1);
  @include border_color(B1);
  @include font_size(H3);
}
.el-form {
  display: flex;
  flex-wrap: wrap;
  .el-form-item {
    width: 50%;
    .textBox {
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      @include padding_left(J1);
    }
  }
}
</style>
