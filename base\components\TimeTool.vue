<template>
  <div class="clearfix">
    <!-- <slot name="title">
      <div class="fl lh32 mrJ1">
         {{ showOption ? "分析周期:" : "查询时段:" }} 
      </div>
    </slot> -->
    <customElSelect
      v-show="showOption"
      class="fl mrJ1"
      value-key="type"
      v-model="currentTimeOption"
      :disabled="selectDisable || disable_in"
      @change="changTimeType"
      size="small"
      :style="{ width: language ? '230px' : '160px' }"
      :prefix_in="$T('分析周期')"
    >
      <el-option
        v-for="(item, index) in timeOptions"
        :key="`${index}_${item.typeID}`"
        :label="item.text"
        :value="item"
      ></el-option>
    </customElSelect>

    <el-button
      class="fl mrJ"
      style="padding: 9px"
      plain
      size="small"
      icon="el-icon-arrow-left"
      :disabled="disable_in"
      @click="queryPrv"
    ></el-button>
    <CustomElDatePicker
      value-format="timestamp"
      :clearable="false"
      class="fl"
      style="width: 200px !important"
      v-model="value"
      :picker-options="pickerOptions"
      :type="currentTimeOption.type"
      :disabled="disable_in"
    />
    <el-button
      class="fl mlJ"
      plain
      size="small"
      style="padding: 9px"
      icon="el-icon-arrow-right"
      :disabled="nextDisabled || disable_in"
      @click="queryNext"
    ></el-button>
  </div>
</template>
<script>
import { TIME_TYPE } from "@/store/constQuantity";
export default {
  name: "TimeTool",
  props: {
    time: {
      type: [Number, Object],
      default: +new Date()
    },
    val: {
      type: [Number, Object, String]
    },
    typeID: {
      type: Number,
      default: 14
    },
    showOption: {
      type: Boolean,
      default: true
    },
    selectDisable: {
      type: Boolean,
      default: false
    },
    timeType_in: {
      type: Array,
      default: function () {
        return [];
      }
    },
    isNextDisabled: {
      type: Boolean,
      default: false
    },
    disable_in: {
      type: Boolean,
      default: false
    }
  },

  watch: {
    val: {
      deep: true,
      handler: function (val, oldVal) {
        this.value = val;
      }
    },
    value: {
      deep: true,
      handler: function (val, oldVal) {
        if (val === oldVal) return;
        this.$emit("update:val", val);
        this.$emit("change", {
          val,
          timeOption: this._.find(TIME_TYPE, {
            typeID: this.currentTimeOption.typeID
          })
        });
      }
    },
    typeID: {
      deep: true,
      handler: function (val, oldVal) {
        if (val === oldVal) return;
        this.$set(
          this,
          "currentTimeOption",
          this._.find(TIME_TYPE, { typeID: val })
        );
      }
    },
    showOption: {
      deep: true,
      handler: function (val, oldVal) {
        if (val === oldVal) return;
        this.$emit("change", {
          val: this.value,
          timeOption: this._.find(TIME_TYPE, {
            typeID: this.currentTimeOption.typeID
          })
        });
      }
    }
  },
  computed: {
    nextDisabled() {
      return this.isNextDisabled
        ? this.$moment(this.value)
            .startOf(this.currentTimeOption.unit)
            .valueOf() >=
            this.$moment().startOf(this.currentTimeOption.unit).valueOf()
        : false;
    },
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },
  data(vm) {
    return {
      pickerOptions: {
        disabledDate(time) {
          return vm.isNextDisabled ? time.getTime() > Date.now() : false;
        }
      },
      timeOptions: TIME_TYPE,
      value: +new Date(),
      currentTimeOption: this._.find(TIME_TYPE, { typeID: this.typeID })
    };
  },
  methods: {
    // 通过匹配key与value返回timeType对应对象
    findTimeType(key, value) {
      return this.timeOptions.find(item => {
        return item[key] === value;
      });
    },
    queryPrv() {
      var date = this.$moment(this.value);
      this.value = date.subtract(1, this.currentTimeOption.unit).valueOf();
    },
    queryNext() {
      var date = this.$moment(this.value);
      this.value = date.add(1, this.currentTimeOption.unit).valueOf();
    },
    changDate(val) {
      this.value = val;
      this.$emit("update:val", val);
      this.$emit("change", {
        val,
        timeOption: this._.find(TIME_TYPE, {
          typeID: this.currentTimeOption.typeID
        })
      });
    },
    changTimeType(val) {
      if (this.value > new Date().getTime()) {
        this.value = new Date().getTime();
      }
      this.$emit("change", { val: this.value, timeOption: val });
    }
  },
  mounted() {
    this.value = this.val;
    this.timeOptions =
      this.timeType_in.length > 0 ? this.timeType_in : TIME_TYPE;
  }
};
</script>
