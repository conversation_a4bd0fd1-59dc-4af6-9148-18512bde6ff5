<template>
  <div>
    <CetDialog v-bind="CetDialog_add" v-on="CetDialog_add.event">
      <el-container class="eem-cont-c1">
        <CetForm
          ref="editPlanForm"
          :data.sync="CetForm_1.inputData_in"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
        >
          <el-row :gutter="$J3">
            <el-col :span="8">
              <el-form-item :label="$T('名称')" prop="name">
                <ElInput
                  v-model.trim="CetForm_1.inputData_in.name"
                  onKeypress="javascript:if(event.keyCode == 32)event.returnValue = false;"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('周期')" prop="aggregationcycle">
                <ElSelect
                  v-model="CetForm_1.inputData_in.aggregationcycle"
                  v-bind="ElSelect_cycle"
                  v-on="ElSelect_cycle.event"
                >
                  <ElOption
                    v-for="item in ElOption_cycle.options_in"
                    :key="item[ElOption_cycle.key]"
                    :label="item[ElOption_cycle.label]"
                    :value="item[ElOption_cycle.value]"
                    :disabled="item[ElOption_cycle.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="CetForm_1.inputData_in.aggregationcycle == 0"
            >
              <el-form-item :label="$T('自定义时间')" prop="cycle">
                <el-container style="width: 100%">
                  <el-row :gutter="$J3">
                    <el-col :span="7">
                      <ElInputNumber
                        v-model="cycleObj.month"
                        v-bind="ElInputNumber_1"
                        v-on="ElInputNumber_1.event"
                      ></ElInputNumber>
                    </el-col>
                    <el-col :span="1" style="padding: 0px">
                      <span>{{ en ? "M" : "月" }}</span>
                    </el-col>
                    <el-col :span="7">
                      <ElInputNumber
                        v-model="cycleObj.day"
                        v-bind="ElInputNumber_2"
                        v-on="ElInputNumber_2.event"
                      ></ElInputNumber>
                    </el-col>
                    <el-col :span="1" style="padding: 0px">
                      <span>{{ en ? "d" : "天" }}</span>
                    </el-col>
                    <el-col :span="7">
                      <ElInputNumber
                        v-model="cycleObj.hour"
                        v-bind="ElInputNumber_3"
                        v-on="ElInputNumber_3.event"
                      ></ElInputNumber>
                    </el-col>
                    <el-col :span="1" style="padding: 0px">
                      <span>{{ en ? "h" : "时" }}</span>
                    </el-col>
                  </el-row>
                </el-container>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('巡检方案')" prop="inspectionschemeid">
                <ElSelect
                  v-model="CetForm_1.inputData_in.inspectionschemeid"
                  v-bind="ElSelect_inspectionscheme"
                  v-on="ElSelect_inspectionscheme.event"
                >
                  <ElOption
                    v-for="item in ElOption_inspectionscheme.options_in"
                    :key="item[ElOption_inspectionscheme.key]"
                    :label="item[ElOption_inspectionscheme.label]"
                    :value="item[ElOption_inspectionscheme.value]"
                    :disabled="item[ElOption_inspectionscheme.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('巡检路线')" prop="signgroupid">
                <ElSelect
                  v-model="CetForm_1.inputData_in.signgroupid"
                  v-bind="ElSelect_inspectionRoute"
                  v-on="ElSelect_inspectionRoute.event"
                >
                  <ElOption
                    v-for="item in ElOption_inspectionRoute.options_in"
                    :key="item[ElOption_inspectionRoute.key]"
                    :label="item[ElOption_inspectionRoute.label]"
                    :value="item[ElOption_inspectionRoute.value]"
                    :disabled="item[ElOption_inspectionRoute.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('工单生成时间')" prop="aheadduration">
                <ElSelect
                  v-model="CetForm_1.inputData_in.aheadduration"
                  v-bind="ElSelect_AheadDuration"
                  v-on="ElSelect_AheadDuration.event"
                >
                  <ElOption
                    v-for="item in ElOption_AheadDuration.options_in"
                    :key="item[ElOption_AheadDuration.key]"
                    :label="item[ElOption_AheadDuration.label]"
                    :value="item[ElOption_AheadDuration.value]"
                    :disabled="item[ElOption_AheadDuration.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('首次执行时间')" prop="executetime">
                <el-date-picker
                  disabled
                  popper-class="custom-date-picker-inspect"
                  v-model="CetForm_1.inputData_in.executetime"
                  type="datetime"
                  :pickerOptions="pickerOptions11"
                  :placeholder="$T('选择日期')"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="CetForm_1.inputData_in.aggregationcycle != 18"
            >
              <el-form-item
                :label="$T('结束时间')"
                prop="finishtime"
                class="form-finishtime"
              >
                <el-date-picker
                  popper-class="custom-date-picker-inspect"
                  v-model="CetForm_1.inputData_in.finishtime"
                  type="datetime"
                  :disabled="isForever"
                  :pickerOptions="pickerOptions11"
                  :placeholder="$T('选择日期')"
                ></el-date-picker>
                <el-checkbox
                  class="mlJ1"
                  v-model="CetForm_1.inputData_in.isForever"
                >
                  {{ $T("永远") }}
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('预计耗时')" prop="timeconsumeplan">
                <ElInputNumber
                  v-model="CetForm_1.inputData_in.timeconsumeplan"
                  v-bind="ElInputNumber_4"
                  v-on="ElInputNumber_4.event"
                ></ElInputNumber>
                <span class="form-item-unit">h</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('责任班组')" prop="teamid">
                <ElSelect
                  v-model="CetForm_1.inputData_in.teamid"
                  v-bind="ElSelect_team"
                  v-on="ElSelect_team.event"
                >
                  <ElOption
                    v-for="item in ElOption_team.options_in"
                    :key="item[ElOption_team.key]"
                    :label="item[ElOption_team.label]"
                    :value="item[ElOption_team.value]"
                    :disabled="item[ElOption_team.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('巡检目标')" prop="deviceListName">
                <div class="custom-btn" @click="OpenxjDevice">
                  <span>
                    {{ CetForm_1.inputData_in.deviceListName }}
                  </span>
                  <span class="toConnect">
                    <omega-icon symbolId="link-lin" />
                  </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col
              :span="24"
              v-if="CetForm_1.inputData_in.aggregationcycle != 18"
            >
              <div
                style="float: right; margin-bottom: 20px; cursor: pointer"
                @click="handlerSet"
              >
                <span>{{ $T("高级设置") }}</span>
                <i class="el-icon-arrow-right"></i>
              </div>
            </el-col>
            <el-col
              :span="24"
              v-if="CetForm_1.inputData_in.aggregationcycle != 18 && showSet"
            >
              <el-form-item :label="$T('生效日设置')" prop="voltagelevel">
                <ElCheckboxGroup
                  class="ml15"
                  v-model="ElCheckboxGroup_1.value"
                  v-bind="ElCheckboxGroup_1"
                  v-on="ElCheckboxGroup_1.event"
                >
                  <ElCheckbox
                    v-for="item in ElCheckboxList_1.options_in"
                    :key="item[ElCheckboxList_1.key]"
                    :label="item[ElCheckboxList_1.label]"
                    :disabled="item[ElCheckboxList_1.disabled]"
                  >
                    {{ item[ElCheckboxList_1.text] }}
                  </ElCheckbox>
                </ElCheckboxGroup>
              </el-form-item>
            </el-col>
          </el-row>
        </CetForm>
      </el-container>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
    <DeviceList v-bind="DeviceList" v-on="DeviceList.event"></DeviceList>
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom.js";
import DeviceList from "./DeviceList";
import { httping } from "@omega/http";
export default {
  name: "editPlan",
  components: { DeviceList },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectTenantId() {
      return this.$store.state.projectTenantId;
    },
    calculatedOneCycle() {
      const vm = this;
      const aggregationcycle = vm.CetForm_1.inputData_in.aggregationcycle;
      const m = vm.cycleObj.month;
      const d = vm.cycleObj.day;
      const h = vm.cycleObj.hour;

      const oneCycle = {
        y: 0,
        m: 0,
        d: 0,
        h: 0
      };
      switch (aggregationcycle) {
        case 0:
          oneCycle.m = m;
          oneCycle.d = d;
          oneCycle.h = h;
          break;
        case 12:
          oneCycle.d = 1;
          break;
        case 13:
          oneCycle.d = 7;
          break;
        case 14:
          oneCycle.m = 1;
          break;
        case 16:
          oneCycle.y = 0.5;
          break;
        case 17:
          oneCycle.y = 1;
          break;
        case 18:
          break;
      }
      return oneCycle;
    },
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },
  data() {
    return {
      CetDialog_add: {
        title: $T("编辑巡检计划"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_add_open_out,
          close_out: this.CetDialog_add_close_out
        },
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "backendInterface", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "editInspectPlan",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {
          aggregationcycle: 0,
          month: null,
          day: null,
          hour: null
        },
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          aggregationcycle: [
            {
              required: true,
              message: $T("请选择周期类型"),
              trigger: ["blur", "change"]
            }
          ],
          cycle: [
            {
              required: true,
              message: $T("自定义周期不能为零"),
              trigger: ["blur"]
            }
          ],
          inspectionschemeid: [
            {
              required: true,
              message: $T("请选择巡检方案"),
              trigger: ["blur", "change"]
            }
          ],
          signgroupid: [
            {
              required: true,
              message: $T("请选择巡检路线"),
              trigger: ["blur", "change"]
            }
          ],
          finishtime: [
            {
              required: true,
              message: $T("结束时间不足以执行一个周期的计划"),
              trigger: "blur",
              validator: (rule, value, callback) => {
                // 一次性创建计划不需要验证结束时间
                if (
                  this.CetForm_1.inputData_in.aggregationcycle === 18 ||
                  this.isForever
                ) {
                  callback();
                  return;
                }

                const executetime = this.CetForm_1.inputData_in.executetime;
                const calculatedOneCycle = this.calculatedOneCycle;
                const endtime = this.$moment(executetime)
                  .add(calculatedOneCycle.y, "y")
                  .add(calculatedOneCycle.m, "M")
                  .add(calculatedOneCycle.d, "d")
                  .add(calculatedOneCycle.h, "h")
                  .valueOf();

                if (value > endtime) {
                  callback();
                } else {
                  callback(new Error());
                }
              }
            }
          ],
          timeconsumeplan: [
            {
              required: true,
              message: $T("请输入预计耗时"),
              trigger: ["blur", "change"]
            }
          ],
          aheadduration: [
            {
              required: true,
              message: $T("请选择工单生成时间"),
              trigger: ["blur", "change"]
            }
          ],
          teamid: [
            {
              required: true,
              message: $T("请选择责任班组"),
              trigger: ["blur", "change"]
            }
          ],
          deviceListName: [
            {
              required: true,
              message: $T("请选择巡检目标"),
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_1_currentData_out,
          saveData_out: this.CetForm_1_saveData_out,
          finishData_out: this.CetForm_1_finishData_out,
          finishTrigger_out: this.CetForm_1_finishTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        style: {},
        placeholder: $T("请输入内容"),
        event: {}
      },
      // 月，设置范围0-99
      ElInputNumber_1: {
        min: 0,
        max: 99,
        step: 2,
        precision: 0,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      // 天，设置范围0-29
      ElInputNumber_2: {
        min: 0,
        max: 29,
        step: 2,
        precision: 0,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      // 小时，设置范围0-23
      ElInputNumber_3: {
        min: 0,
        max: 23,
        step: 2,
        precision: 0,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInputNumber_4: {
        min: 0.01,
        max: 999999999.99,
        step: 2,
        precision: 2,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      pickerOptions: common.pickerOptions_laterThanYesterd,
      pickerOptions11: common.pickerOptions_laterThanYesterd11,
      ElSelect_cycle: {
        value: 0,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_cycle_change_out
        }
      },
      ElOption_cycle: {
        options_in: [
          {
            value: 18,
            label: $T("只执行一次")
          },
          {
            value: 12,
            label: $T("1天")
          },
          {
            value: 13,
            label: $T("1周")
          },
          {
            value: 14,
            label: $T("1个月")
          },
          {
            value: 16,
            label: $T("半年")
          },
          {
            value: 17,
            label: $T("1年")
          },
          {
            value: 0,
            label: $T("自定义")
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      //巡检方案
      ElSelect_inspectionscheme: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_inspectionscheme: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      //巡检路线，签到组
      ElSelect_inspectionRoute: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_inspectionRoute_change_out
        }
      },
      ElOption_inspectionRoute: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      //责任班组
      ElSelect_team: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_team: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // 工单生成时间选项组件
      ElSelect_AheadDuration: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_AheadDuration: {
        options_in: [
          {
            value: 5,
            label: $T("执行前5分钟")
          },
          {
            value: 30,
            label: $T("执行前30分钟")
          },
          {
            value: 60,
            label: $T("执行前1小时")
          },
          {
            value: 2 * 60,
            label: $T("执行前2小时")
          },
          {
            value: 24 * 60,
            label: $T("执行前1天")
          },
          {
            value: 2 * 24 * 60,
            label: $T("执行前2天")
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      //生效日设置
      ElCheckboxGroup_1: {
        value: [1, 2, 3, 4, 5, 6, 7],
        style: {
          display: "inline-block"
        },
        event: {
          change: this.ElCheckboxGroup_1_change_out
        }
      },
      ElCheckboxList_1: {
        options_in: [
          {
            id: 1,
            text: $T("周一")
          },
          {
            id: 2,
            text: $T("周二")
          },
          {
            id: 3,
            text: $T("周三")
          },
          {
            id: 4,
            text: $T("周四")
          },
          {
            id: 5,
            text: $T("周五")
          },
          {
            id: 6,
            text: $T("周六")
          },
          {
            id: 7,
            text: $T("周日")
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      cycleObj: {},
      // 巡检对象弹窗
      DeviceList: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        tableData: null,
        event: {
          saveData_out: this.deviceList_saveData_out
        }
      },
      isForever: false,
      showSet: false
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var _this = this;
      this.CetForm_1.resetTrigger_in = this._.cloneDeep(val);
      this.showSet = false;

      Promise.all([
        new Promise(resolve => {
          this.queryInspectScheme_out(resolve);
        }),
        new Promise(resolve => {
          this.groupManage_out(resolve);
        }),
        new Promise(resolve => {
          this.getTeam_out(resolve);
        })
      ]).then(() => {
        _this.CetDialog_add.openTrigger_in = _this._.cloneDeep(val);
        this.$nextTick(() => {
          _this.CetForm_1.inputData_in = _this.inputData_in;

          _this.$set(
            _this.CetForm_1.inputData_in,
            "signgroupid",
            _this.inputData_in.signgroup_id
          );
          _this.$set(
            _this.CetForm_1.inputData_in,
            "devicelist",
            _this.inputData_in.deviceplanrelationship_model
          );
          _this.cycleObj = {
            month: "",
            day: "",
            hour: ""
          };
          if (
            _this.inputData_in.deviceplanrelationship_model &&
            _this.inputData_in.deviceplanrelationship_model.length > 0
          ) {
            const list = _this.inputData_in.deviceplanrelationship_model;
            const arr = [];
            list.forEach(item => {
              arr.push({
                id: item.device_id,
                name: item.devicename,
                modelLabel: item.device_label,
                tree_id: `${item.device_label}_${item.device_id}`
              });
            });
            _this.DeviceList.tableData = arr;
            _this.$set(
              _this.CetForm_1.inputData_in,
              "deviceListName",
              $T("已选择{0}个目标", arr.length)
            );
          } else {
            _this.DeviceList.tableData = [];
          }
          if (
            _this.CetForm_1.inputData_in.aggregationcycle !== 18 &&
            !_this.CetForm_1.inputData_in.finishtime
          ) {
            _this.$set(_this.CetForm_1.inputData_in, "isForever", true);
            _this.isForever = true;
            _this.CetForm_1.rules.finishtime[0].required = false;
            _this.$refs.editPlanForm.$refs.cetForm.clearValidate("finishtime");
          } else {
            _this.$set(_this.CetForm_1.inputData_in, "isForever", false);
            _this.isForever = false;
            _this.CetForm_1.rules.finishtime[0].required = true;
          }
          _this.ElCheckboxGroup_1.value = _this._.get(
            _this.inputData_in,
            "enableddays.weekDays",
            []
          );
          if (_this.inputData_in.cycle) {
            _this.filCycle_out(_this.inputData_in.cycle);
          }
          if (_this.inputData_in.timeconsumeplan) {
            const timeconsumeplan = Number(
              (_this.inputData_in.timeconsumeplan / (1000 * 60 * 60)).toFixed2(
                1
              )
            );
            _this.$set(
              _this.CetForm_1.inputData_in,
              "timeconsumeplan",
              timeconsumeplan
            );
          }
        });
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    },
    inputData_in(val) {
      this.CetForm_1.inputData_in = val;
    },
    cycleObj: {
      handler: function () {
        if (this.CetForm_1.inputData_in.aggregationcycle !== 0) {
          this.$set(this.CetForm_1.inputData_in, "cycle", null);
        } else {
          const cycle = this.calculatedCustomCycle();
          this.$set(this.CetForm_1.inputData_in, "cycle", cycle);
        }
      },
      deep: true
    },
    "CetForm_1.inputData_in.isForever": {
      handler: function (val) {
        if (val) {
          this.isForever = true;
          this.CetForm_1.rules.finishtime[0].required = false;
          this.$refs.editPlanForm.$refs.cetForm.clearValidate("finishtime");
        } else {
          this.isForever = false;
          this.CetForm_1.rules.finishtime[0].required = true;
        }
      },
      deep: true
    }
  },

  methods: {
    CetDialog_add_open_out() {},
    CetDialog_add_close_out() {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      if (this.CetForm_1.inputData_in.aggregationcycle !== 0) {
        this.$set(this.CetForm_1.inputData_in, "cycle", null);
      } else {
        const cycle = this.calculatedCustomCycle();
        this.$set(this.CetForm_1.inputData_in, "cycle", cycle);
      }
      //选择永远按钮，结束时间传null
      if (this.CetForm_1.inputData_in.isForever) {
        // delete this.CetForm_1.inputData_in.finishtime
        this.$set(this.CetForm_1.inputData_in, "finishtime", null);
      }
      //选择只执行一次，结束时间传值和开始时间值一样
      if (this.CetForm_1.inputData_in.aggregationcycle === 18) {
        this.$set(
          this.CetForm_1.inputData_in,
          "finishtime",
          this.CetForm_1.inputData_in.executetime
        );
      } else {
        this.CetForm_1.inputData_in.enableddays = {
          weekDays: this.ElCheckboxGroup_1.value
        };
      }

      this.CetForm_1.saveTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_currentData_out() {},
    CetForm_1_saveData_out(val) {
      this.$emit("confirm_out", this._.cloneDeep(val));
    },
    CetForm_1_finishData_out() {},
    CetForm_1_finishTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
      this.$emit("confirm_out", val);
    },
    ElCheckboxGroup_1_change_out() {},
    ElSelect_cycle_change_out() {},
    ElSelect_inspectionRoute_change_out() {
      this.$set(this.CetForm_1.inputData_in, "deviceListName", "");
      this.CetForm_1.inputData_in.devicelist = [];
      this.DeviceList.tableData = [];
    },
    OpenxjDevice() {
      if (!this.CetForm_1.inputData_in.signgroupid) {
        this.$message.warning($T("请先选择巡检路线"));
        return;
      }
      this.DeviceList.inputData_in = {
        signInGroup: this.CetForm_1.inputData_in.signgroupid
      };
      this.DeviceList.openTrigger_in = new Date().getTime();
    },
    init() {},
    Add_node(params) {
      var _this = this;
      var auth = _this.token; //身份验证
      // 经纬度如果是undefined 则转成null
      if (params[0].longitude === undefined) {
        params[0].longitude = null;
      }
      if (params[0].latitude === undefined) {
        params[0].latitude = null;
      }
      httping({
        url: "/eem-service/v1/project/manageNode",
        data: params,
        method: "PUT",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          _this.CetDialog_1.closeTrigger_in = _this._.cloneDeep(
            new Date().getTime()
          );
          _this.$emit("saveData_out", res.data[0]);
        }
      });
    },
    // 巡检对象保存
    deviceList_saveData_out(val) {
      if (val && val.length) {
        this.$set(
          this.CetForm_1.inputData_in,
          "deviceListName",
          $T("已选择{0}个目标", val.length)
        );
        this.$refs.editPlanForm.$refs.cetForm.clearValidate("deviceListName");
      } else {
        this.$set(this.CetForm_1.inputData_in, "deviceListName", "");
      }
      const list = val || [];
      const arr = [];
      list.forEach(item => {
        arr.push({
          device_id: item.id,
          device_label: item.modelLabel,
          devicename: item.name,
          enabled: true,
          // id: item.id,
          // modelLabel: item.modelLabel,
          name: item.name,
          ordercode: "",
          pmworksheet_id: 0,
          project_id: 0,
          projectname: "",
          room_id: 0,
          roomname: "",
          tree_id: item.tree_id
        });
      });
      this.CetForm_1.inputData_in.devicelist = this._.cloneDeep(arr);
      this.DeviceList.tableData = this._.cloneDeep(val);
    },
    // 获取巡检方案列表信息
    queryInspectScheme_out(callback) {
      const _this = this;
      const params = {
        name: "",
        page: {
          index: 0,
          limit: 100
        },
        tenantId: this.projectTenantId
      };
      customApi.queryInspectSchemeXJ(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          _this.ElOption_inspectionscheme.options_in = _this._.cloneDeep(data);
          callback && callback();
        }
      });
    },
    // 获取签到组列表信息
    groupManage_out(callback) {
      const _this = this;
      const params = {};
      customApi.groupManageXJ(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          _this.ElOption_inspectionRoute.options_in = _this._.cloneDeep(data);
          callback && callback();
        }
      });
    },
    // 获取班组列表信息
    getTeam_out(callback) {
      const _this = this;
      const params = {};
      customApi.queryInspectorTeam(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          _this.ElOption_team.options_in = _this._.cloneDeep(data);
          callback && callback();
        }
      });
    },
    calculatedCustomCycle() {
      const vm = this;
      const m = vm.cycleObj.month || 0;
      const d = vm.cycleObj.day || 0;
      const h = vm.cycleObj.hour || 0;

      if (m <= 0 && d <= 0 && h <= 0) {
        return null;
      }

      return "P" + m + "M" + d + "D" + "T" + h + "H";
    },
    //过滤表格周期显示
    filCycle_out(data) {
      if (data) {
        const cycle = data || "";
        let month = "";
        let day = "";
        let hour = "";

        const cycleAry = cycle.split("T") || [];
        cycleAry.forEach(cycleItem => {
          let subTimeStrAry = [];
          if (cycleItem.indexOf("P") > -1) {
            subTimeStrAry = cycleItem.match(/\d+[YMD]/g) || [];
            subTimeStrAry.forEach(subTimeStr => {
              const num = parseInt(subTimeStr.slice(0, -1));
              const unit = subTimeStr.slice(-1);
              switch (unit) {
                case "Y":
                  month += 12 * num;
                  break;
                case "M":
                  month += num;
                  break;
                case "D":
                  day = num;
                  break;
              }
            });
          } else {
            subTimeStrAry = cycleItem.match(/\d+H/g) || [];
            subTimeStrAry.forEach(subTimeStr => {
              hour = parseInt(subTimeStr.slice(0, -1));
            });
          }
        });

        this.cycleObj = {
          month: month,
          day: day,
          hour: hour
        };
      }
    },
    //点击进行高级设置
    handlerSet() {
      this.showSet = !this.showSet;
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.custom-btn {
  position: relative;
  padding-left: 15px;
  height: 32px;
  line-height: 32px;
  border-radius: 4px;
  border: 1px solid;
  @include border_color(B1);
  box-sizing: border-box;
  @include font_color(T2);
  cursor: pointer;
  @include background_color(BG4);
}
.toConnect {
  position: absolute;
  right: 2px;
  z-index: 999;
  display: inline-block;
  width: 30px;
  height: 30px;
  cursor: pointer;
  @include font_color(ZS);
  text-align: center;
}
.form-finishtime {
  :deep(.el-form-item__content) {
    display: flex;
  }
}
</style>
