<template>
  <customDrawer v-bind="schemeDialog" ref="drawer">
    <div slot="headerRight">
      <el-button @click="handleEditScheme">{{ $T("编辑") }}</el-button>
    </div>
    <div class="fullheight flex-column">
      <div class="eem-cont-c1">
        <el-row :gutter="16">
          <el-col
            :span="12"
            v-for="(item, index) in deviceAttribute"
            :key="index"
            :class="
              (deviceAttribute.length % 2 === 0 &&
                index < deviceAttribute.length - 2) ||
              (deviceAttribute.length % 2 !== 0 &&
                index < deviceAttribute.length - 1)
                ? 'mbJ3'
                : ''
            "
          >
            <div class="text-T3 mbJ1">{{ item.name }}</div>
            <div class="text-T1">
              {{ formatterValue(inputData_in, item) }}
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="eem-cont-c1 flex1">
        <!-- 螺杆式 -->
        <template v-if="inputData_in.principleType === 3">
          <div class="mbJ3">
            <div class="common-title-H3 mbJ3">{{ $T("气量调节") }}</div>
            <el-row :gutter="16">
              <el-col :span="12">
                <div class="text-T3 mbJ1">{{ $T("调节方式") }}</div>
                <div class="text-T1">
                  {{ formatterSlidingValveMode(detailData.slidingValveMode) }}
                </div>
              </el-col>
              <el-col :span="12" v-if="detailData.slidingValveMode === 0">
                <div class="text-T3 mbJ1">{{ $T("可调范围") }}</div>
                <div class="text-T1">
                  {{ formatterNumber(detailData.slidingMin) }}%-{{
                    formatterNumber(detailData.slidingMax)
                  }}%
                </div>
              </el-col>
            </el-row>
          </div>
        </template>

        <!-- 往复式 -->
        <template v-if="inputData_in.principleType === 2">
          <!-- 回流调节 -->
          <div class="mbJ4">
            <div class="common-title-H3 mbJ3">{{ $T("回流调节") }}</div>
            <el-row :gutter="16">
              <el-col :span="12">
                <div class="text-T3 mbJ1">{{ $T("可调范围") }}</div>
                <div class="text-T1">
                  {{ formatterNumber(detailData.refluxMinimum) }}%-{{
                    formatterNumber(detailData.refluxMaximum)
                  }}%
                </div>
              </el-col>
              <el-col :span="12">
                <div class="text-T3 mbJ1">{{ $T("基准偏离容忍度") }}</div>
                <div class="text-T1">
                  {{
                    formatterNumber(detailData.refluxBaseDeviationTolerance)
                  }}%
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 余隙调节 -->
          <div class="mbJ4">
            <div class="common-title-H3 mbJ3">{{ $T("余隙调节") }}</div>
            <el-row :gutter="16">
              <el-col :span="12" class="mbJ2">
                <div class="text-T3 mbJ1">{{ $T("调节方式") }}</div>
                <div class="text-T1">
                  {{
                    formatterClearanceRegulationMethod(
                      detailData.clearanceRegulationMethod
                    )
                  }}
                </div>
              </el-col>
              <el-col
                :span="12"
                class="mbJ2"
                v-if="
                  detailData.clearanceRegulationMethod === 1 ||
                  detailData.clearanceRegulationMethod === 2
                "
              >
                <div class="text-T3 mbJ1">{{ $T("可调范围") }}</div>
                <div class="text-T1">
                  {{ formatterNumber(detailData.clearanceRegulationMin) }}%-{{
                    formatterNumber(detailData.clearanceRegulationMax)
                  }}%
                </div>
              </el-col>
              <el-col
                :span="12"
                v-if="
                  detailData.clearanceRegulationMethod === 1 ||
                  detailData.clearanceRegulationMethod === 2
                "
              >
                <div class="text-T3 mbJ1">{{ $T("基准偏离容忍度") }}</div>
                <div class="text-T1">
                  {{
                    formatterNumber(
                      detailData.clearanceRegulationBaseDeviationTolerance
                    )
                  }}%
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 气缸调节 -->
          <div class="mbJ4">
            <div class="common-title-H3 mbJ3">{{ $T("气缸调节") }}</div>
            <el-row :gutter="16">
              <el-col :span="12" class="mbJ3">
                <div class="text-T3 mbJ1">{{ $T("气缸组数") }}</div>
                <div class="text-T1">
                  {{
                    formatterCylinderBanksNumber(detailData.cylinderBanksNumber)
                  }}
                </div>
              </el-col>
              <el-col :span="12" class="mbJ3">
                <div class="text-T3 mbJ1">{{ $T("作用方式") }}</div>
                <div class="text-T1">
                  {{
                    formatterCylinderOperationMode(
                      detailData.cylinderOperationMode
                    )
                  }}
                </div>
              </el-col>
              <el-col :span="12">
                <div class="text-T3 mbJ1">{{ $T("基准偏离容忍度") }}</div>
                <div class="text-T1">
                  {{
                    formatterNumber(detailData.cylinderBaseDeviationTolerance)
                  }}%
                </div>
              </el-col>
            </el-row>
          </div>
        </template>
      </div>
    </div>
  </customDrawer>
</template>

<script>
import common from "eem-utils/common.js";
import customApi from "@/api/custom.js";
import customDrawer from "@/components/customElDrawer";
export default {
  name: "optimizationStrategyDetails",
  components: { customDrawer },
  props: {
    openTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      schemeDialog: {
        title: $T("设备详情"),
        size: "480px",
        openTrigger_in: +new Date(),
        closeTrigger_in: +new Date()
      },
      detailData: {},
      deviceAttribute: [
        {
          name: $T("设备名称"),
          key: "name"
        },
        {
          name: $T("设备类型"),
          key: "compresssorType",
          formatter: this.formatterCompressorType
        },
        {
          name: $T("驱动类型"),
          key: "principleType",
          formatter: this.formatterPrincipleType
        },
        {
          name: $T("压缩级数"),
          key: "compressedSeries",
          formatter: this.formatterCompressedSeries
        },
        {
          name: $T("可调节方式"),
          key: "adjustableMode",
          formatter: this.formatterAdjustableMode
        },
        {
          name: $T("额定气量(万立方米/日)"),
          key: "ratedGascapacity",
          formatter: val => common.formatNumberWithPrecision(val, 2)
        }
      ]
    };
  },
  watch: {
    openTrigger_in() {
      this.detailData = {};
      this.queryDeviceOperationSchemeDetails();
      this.schemeDialog.openTrigger_in = +new Date();
    }
  },
  methods: {
    /**
     * 获取方案详情
     */
    queryDeviceOperationSchemeDetails() {
      if (!this.inputData_in.id) return;
      customApi
        .queryDeviceOperationSchemeDetails([this.inputData_in.id])
        .then(res => {
          this.detailData = res.data?.[0] || {};
        });
    },

    /**
     * 编辑运行方案
     */
    handleEditScheme() {
      this.$emit("handleEditScheme", this.inputData_in);
    },

    /**
     * 数值转换
     */
    formatterValue(val, item) {
      const value = _.get(val, item.key, null);
      if (item.formatter) {
        return item.formatter(value);
      } else {
        return value || value === 0 ? value : "--";
      }
    },

    /**
     * 数字类型展示
     */
    formatterNumber(val) {
      return _.isNumber(val) ? val : "--";
    },

    /**
     * 设备类型转换
     */
    formatterCompressorType(val) {
      const list = this.$store.state.enumerations.compressortype;
      const obj = list.find(item => item.id === val);
      return obj?.text || "--";
    },

    /**
     * 驱动类型转换
     */
    formatterPrincipleType(val) {
      const list = this.$store.state.enumerations.workingprincipletype;
      const obj = list.find(item => item.id === val);
      return obj?.text || "--";
    },

    /**
     * 压缩级数转换
     */
    formatterCompressedSeries(val) {
      const list = [
        { id: 0, text: $T("单级") },
        { id: 1, text: $T("双级") }
      ];
      const obj = list.find(item => item.id === val);
      return obj?.text || "--";
    },

    /**
     * 可调节类型转换
     */
    formatterAdjustableMode(val) {
      const list = this.$store.state.enumerations.gascompressoradjustablemode;
      const valList = val
        ?.map(item => {
          const obj = list.find(v => v.id === item);
          return obj?.text;
        })
        ?.filter(item => item);
      return _.isEmpty(valList) ? "--" : valList.join("，");
    },

    /**
     * 气量调节方式转换
     */
    formatterSlidingValveMode(val) {
      const list = [
        {
          id: 0,
          text: $T("手动")
        },
        {
          id: 1,
          text: $T("自动")
        }
      ];
      const obj = list.find(item => item.id === val);
      return obj?.text || "--";
    },

    /**
     * 余隙调节方式转换
     */
    formatterClearanceRegulationMethod(val) {
      const list = [
        {
          id: 0,
          text: $T("不可调节")
        },
        {
          id: 1,
          text: $T("手动调节")
        },
        {
          id: 2,
          text: $T("自动调节")
        }
      ];
      const obj = list.find(item => item.id === val);
      return obj?.text || "--";
    },

    /**
     * 气缸组数转换
     */
    formatterCylinderBanksNumber(val) {
      const list = [
        {
          id: 1,
          text: $T("1组")
        },
        {
          id: 2,
          text: $T("2组")
        }
      ];
      const obj = list.find(item => item.id === val);
      return obj?.text || "--";
    },

    /**
     * 气缸作用方式
     */
    formatterCylinderOperationMode(val) {
      const list = [
        {
          id: 0,
          text: $T("单作用")
        },
        {
          id: 1,
          text: $T("双作用")
        },
        {
          id: 2,
          text: $T("单双可调")
        },
        {
          id: 3,
          text: $T("单单作用")
        },
        {
          id: 4,
          text: $T("双双作用")
        }
      ];
      const obj = list.find(item => item.id === val);
      return obj?.text || "--";
    }
  }
};
</script>
<style lang="scss" scoped>
.flex1 {
  flex: 1;
}
</style>
