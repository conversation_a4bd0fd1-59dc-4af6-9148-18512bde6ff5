<template>
  <div class="page">
    <EditIndexes
      :visibleTrigger_in="EditIndexes.visibleTrigger_in"
      :closeTrigger_in="EditIndexes.closeTrigger_in"
      :queryId_in="EditIndexes.queryId_in"
      :inputData_in="EditIndexes.inputData_in"
      :activeTab_in="activeTab"
      @saveData_out="EditIndexes_saveData_out"
    />
    <div class="fullheight flex-column">
      <!-- <el-tabs v-model="activeTab" class="eem-tabs-custom">
        <el-tab-pane
          :label="$T('能效KPI配置')"
          name="ActiveEvent"
        ></el-tab-pane>
        <el-tab-pane
          :label="$T('转换效率配置')"
          name="HistoricalEvent"
        ></el-tab-pane>
      </el-tabs> -->
      <div class="flex-auto">
        <div class="fullheight flex-column">
          <div
            style="position: relative"
            class="eem-cont flex-column flex-auto"
          >
            <div class="clearfix mbJ3">
              <el-tooltip
                effect="light"
                :content="
                  activeTab == 'ActiveEvent' ? $T('能效KPI') : $T('转换效率')
                "
                placement="bottom"
              >
                <div class="common-title-H2 text-ellipsis nodeName fl">
                  {{
                    activeTab == "ActiveEvent" ? $T("能效KPI") : $T("转换效率")
                  }}
                </div>
              </el-tooltip>
              <CetButton
                class="fr mlJ1"
                v-bind="CetButton_add"
                v-on="CetButton_add.event"
              ></CetButton>
              <CetButton
                class="fr mlJ1"
                v-bind="CetButton_batchRelevance"
                v-on="CetButton_batchRelevance.event"
              ></CetButton>
              <CetButton
                class="fr mlJ1"
                v-bind="CetButton_deleteAll"
                v-on="CetButton_deleteAll.event"
              ></CetButton>
              <div class="fr">
                <ElInput
                  size="small"
                  class="fl mrJ1"
                  v-model="ElInput_1.value"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
                <customElSelect
                  class="fl mrJ1 customStyle"
                  :prefix_in="$T('能耗类型')"
                  v-model="ElSelect_1.value"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                >
                  <ElOption
                    v-for="item in ElOption_1.options_in"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </customElSelect>
                <customElSelect
                  class="fl customStyle"
                  :prefix_in="$T('周期类型')"
                  v-model="ElSelect_cycle.value"
                  v-bind="ElSelect_cycle"
                  v-on="ElSelect_cycle.event"
                  v-if="false"
                >
                  <ElOption
                    v-for="item in ElOption_cycle.options_in"
                    :key="item[ElOption_cycle.key]"
                    :label="item[ElOption_cycle.label]"
                    :value="item[ElOption_cycle.value]"
                    :disabled="item[ElOption_cycle.disabled]"
                  ></ElOption>
                </customElSelect>
                <customElSelect
                  class="fl customStyle"
                  :prefix_in="$T('指标类型')"
                  v-model="ElSelect_unitType.value"
                  v-bind="ElSelect_unitType"
                  v-on="ElSelect_unitType.event"
                >
                  <ElOption
                    v-for="item in ElOption_unitType.options_in"
                    :key="item[ElOption_unitType.key]"
                    :label="item[ElOption_unitType.label]"
                    :value="item[ElOption_unitType.value]"
                    :disabled="item[ElOption_unitType.disabled]"
                  ></ElOption>
                </customElSelect>
              </div>
            </div>
            <CetTable
              :data.sync="CetTable_1.data"
              :dynamicInput.sync="CetTable_1.dynamicInput"
              v-bind="CetTable_1"
              v-on="CetTable_1.event"
              class="CetTable flex-auto text-right"
            >
              <el-table-column type="expand" width="50px">
                <template slot-scope="props">
                  <CetTable
                    :data.sync="props.row.children"
                    :dynamicInput.sync="CetTable_2.dynamicInput"
                    v-bind="CetTable_2"
                    v-on="CetTable_2.event"
                    class="expandTable"
                  >
                    <ElTableColumn
                      v-for="(item, index) in ElTableColumnArr2"
                      v-bind="item"
                      :key="index"
                    ></ElTableColumn>
                    <ElTableColumn
                      v-bind="ElTableColumn_handele"
                      :width="enLanguage ? 250 : 150"
                      fixed="right"
                    >
                      <template slot-scope="scope">
                        <span
                          class="handel fl mrJ3"
                          @click="editBenchmarkset(scope.row, props.row)"
                        >
                          {{ $T("编辑对标") }}
                        </span>
                        <span
                          class="delete fl"
                          @click="deleteBenchmarkset(scope.row, props.row)"
                        >
                          {{ $T("删除") }}
                        </span>
                      </template>
                    </ElTableColumn>
                  </CetTable>
                </template>
              </el-table-column>
              <ElTableColumn v-bind="ElTableColumn_selection"></ElTableColumn>
              <ElTableColumn
                v-for="(item, index) in newElTableColumnArr"
                v-bind="item"
                :key="index"
              ></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_handele"
                width="60px"
                fixed="right"
              >
                <template slot-scope="scope">
                  <div @click.stop>
                    <el-dropdown class="dropdown" @command="handleCommand">
                      <span v-more></span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :command="{ type: 'edit', scope }">
                          {{ $T("编辑指标") }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          :command="{ type: 'relevance', scope }"
                        >
                          {{ $T("关联节点") }}
                        </el-dropdown-item>
                        <el-dropdown-item :command="{ type: 'add', scope }">
                          {{ $T("新建对标") }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          class="handle delete"
                          :command="{ type: 'delete', scope }"
                        >
                          {{ $T("删除") }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                </template>
              </ElTableColumn>
            </CetTable>
          </div>
        </div>
      </div>
    </div>
    <BenchmarkingManage
      :visibleTrigger_in="BenchmarkingManage.visibleTrigger_in"
      :closeTrigger_in="BenchmarkingManage.closeTrigger_in"
      :queryId_in="BenchmarkingManage.queryId_in"
      :inputData_in="BenchmarkingManage.inputData_in"
      :energyefficiencyset_in="energyefficiencyset"
      @saveData_out="BenchmarkingManage_saveData_out"
    />
    <EditEnergyefficiencyIndexes
      :visibleTrigger_in="EditEnergyefficiencyIndexes.visibleTrigger_in"
      :closeTrigger_in="EditEnergyefficiencyIndexes.closeTrigger_in"
      :queryId_in="EditEnergyefficiencyIndexes.queryId_in"
      :inputData_in="EditEnergyefficiencyIndexes.inputData_in"
      :energyefficiencyset_in="energyefficiencyset"
      :activeTab_in="activeTab"
      @saveData_out="EditEnergyefficiencyIndexes_saveData_out"
    />
    <Relevance
      :visibleTrigger_in="Relevance.visibleTrigger_in"
      :closeTrigger_in="Relevance.closeTrigger_in"
      :inputData_in="Relevance.inputData_in"
    />
  </div>
</template>
<script>
import commonApi from "@/api/custom.js";
import EditIndexes from "./dialog/EditIndexes.vue";
import BenchmarkingManage from "./dialog/BenchmarkingManage.vue";
import EditEnergyefficiencyIndexes from "./dialog/EditEnergyefficiencyIndexes.vue";
import Relevance from "./dialog/Relevance";
const isEN = window.localStorage.getItem("omega_language") === "en";
import { httping } from "@omega/http";
export default {
  name: "EnergyEfficiencyIndexes",
  components: {
    EditIndexes,
    BenchmarkingManage,
    EditEnergyefficiencyIndexes,
    Relevance
  },
  props: {},

  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    projectEnergyArr() {
      return this.ElOption_1.options_in || [];
    },
    enLanguage() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },

  data(vm) {
    return {
      activeTab: "ActiveEvent",
      benchmarkset: [],
      energyefficiencyset: {},
      energyefficiencyRow: {}, // 暂存当前选中的指标，用于修改对标时更新当前行内table
      ElTableColumnArr: [
        {
          prop: "name",
          label: $T("指标名称"),
          minWidth: "120px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            if (cellValue || cellValue === 0) {
              return cellValue;
            } else {
              return "--";
            }
          }
        },
        {
          prop: "energyTypeName",
          label: $T("能源类型"),
          minWidth: "120px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            if (cellValue || cellValue === 0) {
              return cellValue;
            } else {
              return "--";
            }
          }
        },
        {
          prop: "symbol",
          label: $T("单位"),
          minWidth: "120px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            if (cellValue || cellValue === 0) {
              return cellValue;
            } else {
              return "--";
            }
          }
        },
        {
          prop: "unittype",
          label: $T("指标属性"),
          minWidth: isEN ? "160px" : "120px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: (row, column, cellValue) => {
            var obj =
              this.$store.state.enumerations.energyefficiencyunittype.find(
                item => item.id === cellValue
              );
            return obj ? obj.text : "--";
          }
        },
        {
          prop: "aggregationcycle",
          label: $T("分析周期"),
          minWidth: isEN ? "170px" : "140px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            var obj = [
              {
                id: 7,
                text: $T("小时")
              },
              {
                id: 12,
                text: $T("日")
              },
              {
                id: 13,
                text: $T("周")
              },
              {
                id: 14,
                text: $T("月")
              },
              {
                id: 17,
                text: $T("年")
              }
            ];
            let str = "";
            cellValue.forEach(item => {
              str = str + obj.find(key => key.id === item)?.text + "、";
            });
            str = str.slice(0, -1);
            return str ? str : "--";
          }
        }
      ],
      newElTableColumnArr: [{}],
      ElTableColumnArr2: [
        {
          prop: "name",
          label: $T("对标名称"),
          minWidth: "200px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            if (cellValue || cellValue === 0) {
              return cellValue;
            } else {
              return "--";
            }
          }
        },
        {
          prop: "indicatortype",
          label: $T("指标类型"),
          minWidth: "150px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            var obj = vm.$store.state.enumerations.indicatortype.find(
              item => item.id === cellValue
            );
            return obj ? obj.text : "--";
          }
        },
        {
          prop: "limittype",
          label: $T("越限类型"),
          minWidth: "150px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            var obj = vm.$store.state.enumerations.indicatorlimittype.find(
              item => item.id === cellValue
            );
            return obj ? obj.text : "--";
          }
        },
        {
          prop: "limitvalue",
          label: $T("越限值"),
          minWidth: "150px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            if (cellValue || cellValue === 0) {
              return cellValue;
            } else {
              return "--";
            }
          }
        },
        {
          prop: "aggregationcycle",
          label: $T("能效周期"),
          minWidth: "150px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            var obj = [
              {
                id: 7,
                text: $T("小时")
              },
              {
                id: 12,
                text: $T("日")
              },
              {
                id: 13,
                text: $T("周")
              },
              {
                id: 14,
                text: $T("月")
              },
              {
                id: 17,
                text: $T("年")
              }
            ];
            let str = "";
            cellValue.forEach(item => {
              str = str + obj.find(key => key.id === item)?.text + "、";
            });
            str = str.slice(0, -1);
            return str ? str : "--";
          }
        }
      ],
      BenchmarkingManage: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        treeData_in: null
      },
      EditEnergyefficiencyIndexes: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        treeData_in: null
      },
      Relevance: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      EditIndexes: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        treeData_in: null,
        clickProjectNode_in: null
      },
      ElInput_1: {
        value: "",
        "suffix-icon": "el-icon-search",
        placeholder: $T("请输入内容"),
        style: {
          width: "200px"
        },
        event: {
          change: this.ElInput_1_change_out
        }
      },
      ElSelect_unitType: {
        value: "",
        style: {
          width: isEN ? "230px" : "200px"
        },
        event: {
          change: this.ElSelect_unitType_change_out
        }
      },
      ElOption_unitType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // ElSelect_cycle: {
      //   value: 12,
      //   style: {
      //     width: isEN ? "230px" : "200px"
      //   },
      //   event: {
      //     change: this.ElSelect_cycle_change_out
      //   }
      // },
      // ElOption_cycle: {
      //   options_in: [
      //     {
      //       id: 7,
      //       text: $T("小时")
      //     },
      //     {
      //       id: 12,
      //       text: $T("日")
      //     },
      //     {
      //       id: 13,
      //       text: $T("周")
      //     },
      //     {
      //       id: 14,
      //       text: $T("月")
      //     },
      //     {
      //       id: 17,
      //       text: $T("年")
      //     }
      //   ],
      //   key: "id",
      //   value: "id",
      //   label: "text",
      //   disabled: "disabled"
      // },
      ElSelect_1: {
        value: null,
        style: {
          width: isEN ? "300px" : "200px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("新增指标"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      ElTableColumn_selection: {
        type: "selection",
        width: "50px",
        headerAlign: "left",
        align: "left"
      },
      ElTableColumn_handele: {
        label: "操作",
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "getEfSet",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [
            { name: "projectId_in", operator: "EQ", prop: "projectId" },
            { name: "energyType_in", operator: "EQ", prop: "energyType" },
            { name: "keyword_in", operator: "LIKE", prop: "keyword" },
            { name: "unitType_in", operator: "EQ", prop: "unitType" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {
          projectId_in: null,
          energyType_in: null,
          keyword_in: "",
          unitType_in: null
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {},
        exportFileName: "",
        event: {
          "selection-change": this.CetTable_1_selectionChange,
          "expand-change": this.CetTable_1_expandChange,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      CetTable_2: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      CetButton_deleteAll: {
        visible_in: true,
        disable_in: true,
        title: $T("批量删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_deleteAll_statusTrigger_out
        }
      },
      CetButton_batchRelevance: {
        visible_in: true,
        disable_in: true,
        title: $T("批量关联节点"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_batchRelevance_statusTrigger_out
        }
      }
    };
  },
  watch: {
    // activeTab() {
    //   this.changeTab();
    //   this.getEfSet();
    // },
    "ElSelect_unitType.value": {
      handler(newVal, oldVal) {
        this.changeHeader(newVal);
      },
      deep: true
    }
  },

  methods: {
    async handleCommand({ type, scope }) {
      switch (type) {
        case "edit":
          this.editEnergyefficiencyset(scope.row);
          break;
        case "relevance":
          this.addRelevance(scope.row);
          break;
        case "add":
          this.addBenchmarkset(scope.row);
          break;
        case "delete":
          this.deleteEnergyefficiencyset(scope.row);
          break;
      }
    },
    CetTable_1_selectionChange(val) {
      this.CetTable_1.selectionAll = val;
      if (val && val.length > 0) {
        this.CetButton_deleteAll.disable_in = false;
        this.CetButton_batchRelevance.disable_in = false;
      } else {
        this.CetButton_deleteAll.disable_in = true;
        this.CetButton_batchRelevance.disable_in = true;
      }
    },
    CetTable_1_expandChange(row, expanded, flag) {
      var _this = this;
      let efSetIds = [];
      row.mergeDisplayAdds.forEach(item => {
        efSetIds.push(item.efSetId);
      });
      // 打开才更新
      if (expanded.indexOf(row) !== -1 || flag) {
        commonApi.getBenchMarkSet(efSetIds).then(response => {
          if (response.code === 0) {
            var data = _this._.get(response, "data", []);
            this.handleCycle(data);
            _this.$set(row, "children", data);
          }
        });
      }
    },
    CetButton_deleteAll_statusTrigger_out() {
      var _this = this;
      let efSetIds = [];
      this.CetTable_1.selectionAll.forEach(item => {
        item.mergeDisplayAdds.forEach(key => {
          efSetIds.push(key.efSetId);
        });
      });
      this.$confirm($T("是否删除此指标?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          commonApi.deleteEnergyefficiencyset(efSetIds).then(response => {
            if (response.code === 0) {
              _this.$message({
                type: "success",
                message: $T("删除成功!")
              });
              _this.getEfSet();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已取消删除")
          });
        });
    },
    CetButton_batchRelevance_statusTrigger_out() {
      this.Relevance.inputData_in = this._.cloneDeep(
        this.CetTable_1.selectionAll
      );
      this.Relevance.visibleTrigger_in = this._.cloneDeep(new Date().getTime());
    },
    // 单条增加关联
    addRelevance(row) {
      this.Relevance.inputData_in = [row];
      this.Relevance.visibleTrigger_in = this._.cloneDeep(new Date().getTime());
    },
    // 对标
    addBenchmarkset(row) {
      this.energyefficiencyRow = row;
      this.energyefficiencyset = this._.cloneDeep(row);
      this.BenchmarkingManage.inputData_in = {};
      this.BenchmarkingManage.visibleTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    editBenchmarkset(row, energyefficiencyset) {
      this.energyefficiencyRow = energyefficiencyset;
      this.energyefficiencyset = this._.cloneDeep(energyefficiencyset);
      this.BenchmarkingManage.inputData_in = this._.cloneDeep(row);
      this.BenchmarkingManage.visibleTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    deleteBenchmarkset(row, parent) {
      var _this = this;
      let benchMarkIds = [];
      row.mergeDisplayAdds.forEach(item => {
        benchMarkIds.push(item.benchMarkId);
      });
      this.$confirm($T("是否删除此对标?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          commonApi.deleteBenchmarkset(benchMarkIds).then(response => {
            if (response.code === 0) {
              _this.$message({
                type: "success",
                message: $T("删除成功!")
              });
              _this.CetTable_1_expandChange(parent, [], true);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已取消删除")
          });
        });
    },
    // 指标
    // 编辑能效指标
    editEnergyefficiencyset(val) {
      this.energyefficiencyset = this._.cloneDeep(val);
      this.EditEnergyefficiencyIndexes.visibleTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    // 删除指标
    deleteEnergyefficiencyset(val) {
      var _this = this;
      let efSetIds = [];
      val.mergeDisplayAdds.forEach(item => {
        efSetIds.push(item.efSetId);
      });
      this.$confirm($T("是否删除此指标?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          commonApi.deleteEnergyefficiencyset(efSetIds).then(response => {
            if (response.code === 0) {
              _this.$message({
                type: "success",
                message: $T("删除成功!")
              });
              _this.getEfSet();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已取消删除")
          });
        });
    },
    EditIndexes_saveData_out() {
      this.getEfSet();
    },
    BenchmarkingManage_saveData_out() {
      // 更新当前行
      this.CetTable_1_expandChange(this.energyefficiencyRow, [], true);
    },
    EditEnergyefficiencyIndexes_saveData_out() {
      this.getEfSet();
    },
    CetButton_add_statusTrigger_out(val) {
      this.EditIndexes.inputData_in = {};
      this.EditIndexes.visibleTrigger_in = this._.cloneDeep(val);
    },
    ElSelect_1_change_out() {
      this.getEfSet();
    },
    ElInput_1_change_out() {
      this.getEfSet();
    },

    //时间控件改变触发
    // ElSelect_cycle_change_out() {
    //   this.getEfSet();
    // },
    // 指标类型改变触发
    ElSelect_unitType_change_out() {
      this.getEfSet();
    },
    //获取能效指标列表
    getEfSet() {
      this.CetTable_1.dynamicInput.projectId_in = this.projectId;
      this.CetTable_1.dynamicInput.energyType_in = this.ElSelect_1.value;
      this.CetTable_1.dynamicInput.keyword_in = this.ElInput_1.value;
      this.CetTable_1.dynamicInput.unitType_in = this.ElSelect_unitType.value;
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    getProjectEnergy() {
      var _this = this;
      _this.ElOption_1.options_in = [];
      httping({
        url:
          "/eem-service/v1/project/projectEnergy?projectId=" + this.projectId,
        method: "GET"
      }).then(res => {
        if (res.code === 0 && res.data && res.data.length > 0) {
          var selectData = res.data.map(item => {
            return {
              id: item.energytype,
              text: item.name
            };
          });
          _this.ElOption_1.options_in = selectData;
          _this.ElSelect_1.value = selectData[0].id;
          _this.ElSelect_1_change_out(_this.ElSelect_1.value);
        }
      });
    },
    // changeTab() {
    //   var _this = this;
    //   _this.ElTableColumnArr = [
    //     {
    //       prop: "name",
    //       label: $T("指标名称"),
    //       minWidth: "120px",
    //       headerAlign: "left",
    //       align: "left",
    //       showOverflowTooltip: true,
    //       formatter: function (row, column, cellValue) {
    //         if (cellValue || cellValue === 0) {
    //           return cellValue;
    //         } else {
    //           return "--";
    //         }
    //       }
    //     },
    //     {
    //       prop: "energyTypeName",
    //       label:
    //         _this.activeTab === "ActiveEvent"
    //           ? $T("能源类型")
    //           : $T("消耗能源类型"),
    //       minWidth: "120px",
    //       headerAlign: "left",
    //       align: "left",
    //       showOverflowTooltip: true,
    //       formatter: function (row, column, cellValue) {
    //         if (cellValue || cellValue === 0) {
    //           return cellValue;
    //         } else {
    //           return "--";
    //         }
    //       }
    //     },
    //     {
    //       prop: "symbol",
    //       label: $T("单位"),
    //       minWidth: "120px",
    //       headerAlign: "left",
    //       align: "left",
    //       showOverflowTooltip: true,
    //       formatter: function (row, column, cellValue) {
    //         if (cellValue || cellValue === 0) {
    //           return cellValue;
    //         } else {
    //           return "--";
    //         }
    //       }
    //     }
    //   ];
    //   if (_this.activeTab === "ActiveEvent") {
    //     _this.ElTableColumnArr.push(
    //       {
    //         prop: "unittype",
    //         label: $T("指标属性"),
    //         minWidth: isEN ? "160px" : "120px",
    //         headerAlign: "left",
    //         align: "left",
    //         showOverflowTooltip: true,
    //         formatter: (row, column, cellValue) => {
    //           var obj =
    //             this.$store.state.enumerations.energyefficiencyunittype.find(
    //               item => item.id === cellValue
    //             );
    //           return obj ? obj.text : "--";
    //         }
    //       },
    //       {
    //         prop: "aggregationcycle",
    //         label: $T("分析周期"),
    //         minWidth: isEN ? "170px" : "120px",
    //         headerAlign: "left",
    //         align: "left",
    //         showOverflowTooltip: true,
    //         formatter: function (row, column, cellValue) {
    //           var obj = [
    //             {
    //               id: 7,
    //               text: $T("小时")
    //             },
    //             {
    //               id: 12,
    //               text: $T("日")
    //             },
    //             {
    //               id: 13,
    //               text: $T("周")
    //             },
    //             {
    //               id: 14,
    //               text: $T("月")
    //             },
    //             {
    //               id: 17,
    //               text: $T("年")
    //             }
    //           ].find(item => item.id === cellValue);
    //           return obj ? obj.text : "--";
    //         }
    //       },
    //       {
    //         prop: "productTypeName",
    //         label: $T("产品"),
    //         minWidth: "120px",
    //         headerAlign: "left",
    //         align: "left",
    //         showOverflowTooltip: true
    //         // formatter: function (row, column, cellValue, index) {
    //         //   var obj = this.$store.state.enumerations.producttype.find(item => item.id === cellValue);
    //         //   return obj ? obj.text : "--";
    //         // }
    //       },
    //       {
    //         prop: "coef",
    //         label: $T("系数"),
    //         minWidth: "120px",
    //         headerAlign: "left",
    //         align: "left",
    //         showOverflowTooltip: true,
    //         formatter: function (row, column, cellValue) {
    //           if (cellValue || cellValue === 0) {
    //             return cellValue;
    //           } else {
    //             return "--";
    //           }
    //         }
    //       }
    //     );
    //   } else {
    //     _this.ElTableColumnArr.push(
    //       {
    //         prop: "prodenergytype",
    //         label: $T("产出载能工质"),
    //         minWidth: "120px",
    //         headerAlign: "left",
    //         align: "left",
    //         showOverflowTooltip: true,
    //         formatter: function (row, column, cellValue) {
    //           var obj = _this.projectEnergyArr.find(
    //             item => item.id === cellValue
    //           );
    //           return obj ? obj.text : "--";
    //         }
    //       },
    //       {
    //         prop: "aggregationcycle",
    //         label: $T("分析周期"),
    //         minWidth: "120px",
    //         headerAlign: "left",
    //         align: "left",
    //         showOverflowTooltip: true,
    //         formatter: function (row, column, cellValue) {
    //           var obj = [
    //             {
    //               id: 7,
    //               text: $T("小时")
    //             },
    //             {
    //               id: 12,
    //               text: $T("日")
    //             },
    //             {
    //               id: 13,
    //               text: $T("周")
    //             },
    //             {
    //               id: 14,
    //               text: $T("月")
    //             },
    //             {
    //               id: 17,
    //               text: $T("年")
    //             }
    //           ].find(item => item.id === cellValue);
    //           return obj ? obj.text : "--";
    //         }
    //       }
    //     );
    //   }
    //   _this.ElTableColumnArr.push(
    //     ...[
    //       {
    //         prop: "dimensionName",
    //         label: $T("维度"),
    //         minWidth: "120px",
    //         headerAlign: "left",
    //         align: "left",
    //         showOverflowTooltip: true,
    //         formatter: function (row, column, cellValue) {
    //           if (cellValue || cellValue === 0) {
    //             return cellValue;
    //           } else {
    //             return "--";
    //           }
    //         }
    //       },
    //       {
    //         prop: "tagName",
    //         label: $T("用能对象"),
    //         minWidth: isEN ? "220px" : "120px",
    //         headerAlign: "left",
    //         align: "left",
    //         showOverflowTooltip: true,
    //         formatter: function (row, column, cellValue) {
    //           if (cellValue || cellValue === 0) {
    //             return cellValue;
    //           } else {
    //             return "--";
    //           }
    //         }
    //       }
    //     ]
    //   );
    // },
    // 获取指标类型
    grtEnergyefficiencyunittype() {
      var energyefficiencyunittypeArr =
        this.$store.state.enumerations.energyefficiencyunittype;
      var data = [];
      energyefficiencyunittypeArr.forEach(item => {
        if (item.id <= 4 || item.id === 7) {
          data.push(item);
        }
      });
      this.ElOption_unitType.options_in = data;
      if (data.length) {
        this.ElSelect_unitType.value = data[0].id;
      }
    },
    changeHeader(val) {
      var that = this;
      let array = this._.cloneDeep(this.ElTableColumnArr);
      if (val === 1) {
        array.push({
          prop: "productTypeName",
          label: $T("产品"),
          minWidth: "120px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true
        });
      } else if (val === 4) {
        array.push({
          prop: "coef",
          label: $T("系数"),
          minWidth: "120px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            if (cellValue || cellValue === 0) {
              return cellValue;
            } else {
              return "--";
            }
          }
        });
      } else if (val === 7) {
        array.push({
          prop: "prodenergytype",
          label: $T("产出载能工质"),
          minWidth: "120px",
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue) {
            var obj = that.projectEnergyArr.find(item => item.id === cellValue);
            return obj ? obj.text : "--";
          }
        });
      }
      array.push(
        ...[
          {
            prop: "dimensionName",
            label: $T("维度"),
            minWidth: "120px",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (row, column, cellValue) {
              if (cellValue || cellValue === 0) {
                return cellValue;
              } else {
                return "--";
              }
            }
          },
          {
            prop: "tagName",
            label: $T("用能对象"),
            minWidth: isEN ? "220px" : "120px",
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: function (row, column, cellValue) {
              if (cellValue || cellValue === 0) {
                return cellValue;
              } else {
                return "--";
              }
            }
          }
        ]
      );
      this.newElTableColumnArr = this._.cloneDeep(array);
    },
    handleCycle(val) {
      if (val.length) {
        val.forEach(item => {
          let res = [];
          if (item.mergeDisplayAdds && item.mergeDisplayAdds.length) {
            item.mergeDisplayAdds.forEach(key => {
              res.push(key.cycle);
            });
            if (item.mergeDisplayAdds.length === 1) {
              // item.name =
              //   this.ellipsisXmlName(item.name) +
              //   item.mergeDisplayAdds[0].cycleName;
              item.name = item.name + item.mergeDisplayAdds[0].cycleName;
            }
          }
          item.aggregationcycle = res;
        });
      }
    },
    CetTable_1_outputData_out(val) {
      this.handleCycle(val);
      this.CetTable_1.data = this._.cloneDeep(val);
    },
    // 去除字符串中的小括号内容
    ellipsisXmlName(value) {
      let result = "";
      if (!value) return "";
      if (value.indexOf("（") >= 0 && value.indexOf("）") >= 0) {
        result = value.replace(/（[^)]*）/g, "");
      } else if (value.indexOf("(") >= 0 && value.indexOf(")") >= 0) {
        result = value.replace(/\([^)]*\)/g, "");
      } else if (value.indexOf("（") >= 0 && value.indexOf(")") >= 0) {
        result = value.replace(/（[^)]*\)/g, "");
      } else if (value.indexOf("(") >= 0 && value.indexOf("）") >= 0) {
        result = value.replace(/\([^)]*）/g, "");
      } else {
        result = value;
      }
      return result;
    }
  },
  created() {
    this.getProjectEnergy();
    this.grtEnergyefficiencyunittype();
  },
  mounted() {
    this.getEfSet();
  },
  activated() {
    // this.changeTab();
    this.changeHeader(1);
    this.getEfSet();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;
  :deep() {
    .el-tabs__nav-wrap::after {
      display: none;
    }
    .el-tabs__nav-scroll {
      @include padding_left(J2);
      @include background_color(BG1);
    }
  }
  .nodeName {
    max-width: 420px;
    @include line_height(Hm);
  }
  .dropdown {
    line-height: 0;
  }
}
.line {
  @include font_color(B1);
}
.handel {
  cursor: pointer;
  @include font_color(ZS);
}
.delete {
  @include font_color(Sta3);
}

.expandTable {
  :deep(.el-table__body-wrapper) {
    height: auto !important;
  }
}
.customStyle {
  :deep(.customElSelect) {
    line-height: 32px !important;
  }
}
:deep(.el-table .el-table__expanded-cell) {
  padding-left: 60px;
  padding-right: 60px;
}
</style>
