<template>
  <!-- 1弹窗组件 -->
  <CetDialog
    class="CetDialog small"
    v-bind="CetDialog_1"
    v-on="CetDialog_1.event"
  >
    <el-container class="fullheight flex-column bg1 brC1 pJ3 plJ4 prJ4">
      <div class="clearfix mbJ1">
        <!-- <el-checkbox class="fl lhHm" v-model="checkStrictly">
          默认选中子节点
        </el-checkbox> -->
      </div>
      <!-- 父子关联 -->
      <CetGiantTree
        v-if="false"
        v-show="checkStrictly"
        class="switch-tree"
        v-bind="CetGiantTree_1"
        v-on="CetGiantTree_1.event"
      ></CetGiantTree>
      <!-- 父子不关联 -->
      <CetGiantTree
        v-show="!checkStrictly"
        class="switch-tree separate"
        v-bind="CetGiantTree_2"
        v-on="CetGiantTree_2.event"
      ></CetGiantTree>
      <div class="tagBox">
        <el-tag
          :key="index"
          v-for="(tag, index) in allTreeCheckedNodes"
          :closable="tag.closable"
          :disable-transitions="false"
          @close="handleClose(index)"
        >
          {{ tag.name }}
        </el-tag>
      </div>
    </el-container>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import commonApi from "@/api/custom.js";
import { httping } from "@omega/http";
import TREE_PARAMS from "@/store/treeParams.js";
export default {
  name: "add",
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    currentNode: {
      type: Object
    },
    // 已关联管理层级列表
    energySupplyToDataAll: {
      type: Array,
      default() {
        return [];
      }
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data() {
    return {
      allTreeCheckedNodes: [],
      checkStrictly: false,
      CetDialog_1: {
        title: $T("新增管理层级关联"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      checkedNodes1: [],
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: this.setNodeClasses
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out //勾选节点输出
        }
      },
      checkedNodes2: [],
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: this.setNodeClasses
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_2_checkedNodes_out //勾选节点输出
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      this.allTreeCheckedNodes = this.energySupplyToDataAll.map(item => {
        return {
          ...item,
          closable: false
        };
      });
      vm.checkStrictly = false;
      vm.CetDialog_1.openTrigger_in = val;
      this.getTreeData();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    inputData_in(val) {
      this.CetDialog_1.inputData_in = val;
    },
    checkStrictly(val) {
      if (val) {
        this.CetGiantTree_1.checkedNodes = this._.cloneDeep(this.checkedNodes2);
      } else {
        this.CetGiantTree_2.checkedNodes = this._.cloneDeep(this.checkedNodes1);
      }
    }
  },

  methods: {
    handleClose(index) {
      this.allTreeCheckedNodes.splice(index, 1);
      const checkNodes = this.allTreeCheckedNodes.filter(item => item.closable);
      if (this.checkStrictly) {
        this.CetGiantTree_1.checkedNodes = checkNodes;
        if (!checkNodes.length) {
          this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
        }
      } else {
        this.CetGiantTree_2.checkedNodes = checkNodes;
        if (!checkNodes.length) {
          this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
        }
      }
    },
    setNodeClasses(treeId, treeNode) {
      return treeNode.disabledClass
        ? { add: ["disabledClass"] }
        : { remove: ["disabledClass"] };
    },
    CetGiantTree_1_checkedNodes_out(val) {
      this.allTreeCheckedNodes = (val || [])
        .map(item => {
          return {
            ...item,
            closable: true
          };
        })
        .concat(this.energySupplyToDataAll);
      this.checkedNodes1 = this._.cloneDeep(val);
    },
    CetGiantTree_2_checkedNodes_out(val) {
      this.allTreeCheckedNodes = (val || [])
        .map(item => {
          return {
            ...item,
            closable: true
          };
        })
        .concat(this.energySupplyToDataAll);
      this.checkedNodes2 = this._.cloneDeep(val);
    },
    getTreeData() {
      let data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.pipeRelation,
        treeReturnEnable: true
      };

      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.checkedNodes1 = [];
      this.CetGiantTree_1.checkedNodes = [];
      this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
      this.checkedNodes2 = [];
      this.CetGiantTree_2.checkedNodes = [];
      httping({
        url: "/eem-service/v1/node/nodeTree/simple",
        method: "POST",
        data
      }).then(res => {
        if (res.code === 0) {
          var data = this._.get(res, "data", []) || [];
          this.setTreeLeaf_filterText1Change(data);
          this.CetGiantTree_1.inputData_in = data;
          this.CetGiantTree_2.inputData_in = data;
        }
      });
    },
    setTreeLeaf_filterText1Change(nodes) {
      if (!nodes) {
        return;
      }
      nodes.forEach(item => {
        if (!["project"].includes(item.modelLabel)) {
          item.leaf = true;
        }
        if (
          this.energySupplyToDataAll.filter(
            i => i.id === item.id && i.modelLabel === item.modelLabel
          ).length === 0
        ) {
          item.disabledClass = false;
        } else {
          item.disabledClass = true;
        }
        this.setTreeLeaf_filterText1Change(this._.get(item, "children", []));
      });
    },
    // 保存
    addSupplyRelation_out() {
      var me = this;
      var param = [];
      var url = `/eem-service/v1/connect/relationship`;
      let checkedNodes = me.checkedNodes1 || [];
      if (!this.checkStrictly) {
        checkedNodes = me.checkedNodes2 || [];
      }
      if (!me.currentNode) {
        return;
      }
      checkedNodes.forEach(function (value) {
        if (
          me.energySupplyToDataAll.filter(
            i => i.id === value.id && i.modelLabel === value.modelLabel
          ).length === 0
        ) {
          param.push({
            createtime: new Date().getTime(),
            objectlabel: me.currentNode.modelLabel,
            objectid: me.currentNode.id,
            supplytoid: value.id,
            supplytolabel: value.modelLabel,
            modelLabel: "energysupplyto",
            starttime: 0
            // starttime: new Date().getTime()
          });
        }
      });
      if (param.length === 0) {
        this.$message({
          message: $T("请选择管网设备"),
          type: "warning"
        });
        return;
      }
      httping({
        url,
        method: "PUT",
        data: param
      }).then(
        function (res) {
          if (res.code === 0) {
            me.$message({
              message: $T("保存成功"),
              type: "success"
            });
            me.$emit("updata_out");
            me.CetDialog_1.closeTrigger_in = new Date().getTime();
          }
        },
        function () {}
      );
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      this.addSupplyRelation_out();
    }
  }
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep() {
    .el-dialog__body {
      @include background_color(BG);
      @include padding(J1);
    }
  }
  .tagBox {
    min-height: 100px;
    border-top: 1px solid;
    border-bottom: 1px solid;
    @include border_color(B1);
    @include padding(J3 0);
    :deep(.el-tag) {
      @include margin_right(J1);
      @include margin_top(J1);
    }
  }
  .lhHm {
    @include line_height(Hm);
  }
}
.switch-tree {
  height: 500px;
  // :deep(& > .el-tree > .el-tree-node > .el-tree-node__content) {
  //   .el-tree-node__expand-icon + .el-checkbox {
  //     display: none;
  //   }
  // }
  // 模拟打钩
  :deep(.el-checkbox.is-disabled .el-checkbox__inner) {
    background: url("../assets/check.png") no-repeat center center;
  }
  :deep(.disabledClass) {
    position: relative;
    &::after {
      content: "";
      position: absolute;
      background: url("../assets/check.png") no-repeat center center;
      background-size: 100% 100%;
      height: 14px;
      left: -22px;
      top: 4px;
      width: 14px;
    }
    .node_name {
      position: relative;
      &::before {
        content: "";
        position: absolute;
        background: url("../assets/check.png") no-repeat center center;
        background-size: 100% 100%;
        height: 14px;
        left: -22px;
        top: 5px;
        width: 14px;
        cursor: no-drop;
      }
    }
  }
  &.separate {
    :deep(.ztree > div > li > span.button.chk) {
      // display: none !important;
    }
  }
}
</style>
