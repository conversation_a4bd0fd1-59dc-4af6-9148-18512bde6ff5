﻿<template>
  <div class="page">
    <div>
      <div
        :class="{ itemBox: true, isAction: projectId == item.id }"
        v-for="item in projectAll"
        :key="item.id"
        @click="itemBoxClick(item.id, item.tenantid, item)"
      >
        <img
          width="50px"
          height="30px"
          :src="!item.pic ? 'static/assets/projectNavigation.png' : '#'"
          @error="getImgUrl(item.pic)"
          alt=""
        />
        <div>{{ item.name }}</div>
        <div class="mask"></div>
      </div>
    </div>
    <div v-if="showCreateProject" class="createProject">
      <div class="CreateTitle">请在用户管理先创建企业</div>
    </div>

    <div v-if="!showCreateProject" class="searchProject">
      <ElInput
        v-model="ElInput_search.value"
        v-bind="ElInput_search"
        v-on="ElInput_search.event"
      ></ElInput>
    </div>
  </div>
</template>
<script>
import { find, isProd } from "eem-utils/util.js";
import userUtil from "@omega/auth/auth/user.js";
import { conf, router } from "@omega/app";
import { fileMenu } from "@/config/config";
import { PageAccessPlugin } from "@omega/auth/plugins/pageAccess.js";
import { httping } from "@omega/http";
import { HttpBase } from "@omega/http";
import { resetRouteViewKeepAlive } from "@omega/layout";
export default {
  name: "ProjectNavigation",
  components: {},

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    userInfo() {
      var vm = this;
      return vm.$store.state.userInfo;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    navmenu() {
      return this.$store.state.navmenu;
    }
  },

  data() {
    return {
      showCreateProject: true,
      projectAll: null,
      projectId: null,
      // search组件
      ElInput_search: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElInput_search_change_out,
          input: this.ElInput_search_input_out
        }
      }
    };
  },
  watch: {},

  methods: {
    getImgUrl: function (uploadPath) {
      var e = e || event;
      if (!uploadPath) {
        return;
      }
      var url = "/eem-service/v1/common/downloadFile?path=" + uploadPath;
      const xhr = new XMLHttpRequest();
      xhr.open("GET", url, true);
      xhr.responseType = "blob";
      xhr.setRequestHeader("Authorization", this.token);
      xhr.onload = () => {
        if (
          xhr.status === 200 &&
          xhr.response.type === "application/x-download"
        ) {
          //将图片信息放到Img中
          e.target.src = window.URL.createObjectURL(xhr.response);
        }
      };

      xhr.send();
    },
    // 获取拥有的所有项目
    getProjectAll() {
      this.showCreateProject = false;
      this.projectAll = [];
      this.copyProjectAll = [];
      var params = {
        rootID: 0,
        rootLabel: "project",
        treeReturnEnable: true
      };
      httping({
        url: `/eem-service/v1/auth/cloud/projects?tenantId=${this.userInfo.tenantId}&isQueryChild=true`,
        method: "POST",
        data: params
      }).then(response => {
        if (response.code == 0 && response.data && response.data.length > 0) {
          //项目名称按中文首字母排序
          response.data.sort((a, b) =>
            a.name.localeCompare(b.name, "zh-Hans-CN", {
              sensitivity: "accent"
            })
          );
          // 过滤掉除项目之外的层级
          this.projectAll = response.data;
          this.copyProjectAll = this._.cloneDeep(response.data);
        } else {
          this.showCreateProject = true;
        }
      });
    },
    itemBoxClick(id, tenantId, projectInfo) {
      this.projectId = id;
      var path = "";
      const PageNodes = userUtil.getRolePageNodes();
      const allNavmenu = this.navmenu;
      if (
        this.systemCfg.fwHomePage &&
        PageNodes.find(i => i.id === this.systemCfg.fwHomePage)
      ) {
        // 设置了项目首页，并且登录用户拥有此页面权限
        const item = find(allNavmenu, this.systemCfg.fwHomePage, {
          childKey: "subMenuList",
          valueKey: "permission"
        });
        path = item.location;
      } else {
        // 没有就去找第一个项目内页面
        PageNodes.forEach(item => {
          let navmenuItem = find(allNavmenu, item.id, {
            childKey: "subMenuList",
            valueKey: "permission"
          });
          if (!path && navmenuItem && navmenuItem.Module === "fw") {
            path = navmenuItem.location;
          }
        });
      }
      if (!isProd()) {
        let item = find(allNavmenu, this.systemCfg.fwHomePage, {
          childKey: "subMenuList",
          valueKey: "permission"
        });
        path = item.location;
      }
      if (!path) {
        this.$router.push({ path: "/noPermission" });
        return;
      }
      resetRouteViewKeepAlive();
      HttpBase.setHeadersOperate({ projectId: Number(id) });
      conf.setNavmenu(fileMenu("pjNavmenu"));
      this.$store.commit("setCurrentModule", "fw");
      this.$router.push({ path });
      this.$store.commit("setProjectId", Number(id));
      this.$store.commit("setProjectTenantId", Number(tenantId));
      this.$store.commit("setProjectInfo", projectInfo);
    },
    // 搜索
    // search输出,方法名要带_out后缀
    ElInput_search_change_out(val) {
      if (val) {
        this.projectAll = this._.cloneDeep(
          this.copyProjectAll.filter(item => item.name.indexOf(val) != -1)
        );
      } else {
        this.projectAll = this._.cloneDeep(this.copyProjectAll);
      }
    },
    ElInput_search_input_out(val) {},

    getLocalStorage() {
      if (!window.sessionStorage) {
        return false;
      } else {
        var storage = window.sessionStorage;
        this.projectId = storage.getItem("projectId");
      }
    }
  },
  activated: function () {
    this.ElInput_search.value = "";
    this.getProjectAll();
    this.getLocalStorage();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;
  padding: 80px 0 20px 0;
  overflow-y: auto;
  & > div {
    height: 100%;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    overflow: auto;
  }
}
.itemBox {
  text-align: center;
  box-sizing: border-box;
  padding: 10px;
  width: 400px;
  height: 260px;
  cursor: pointer;
  position: relative;
  & > div {
    @include font_size(H1);
  }
  & > div.mask {
    top: 0;
    left: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    @include background_color(BG4);
  }
  img {
    height: 180px;
    width: 310px;
  }
}
.isAction {
  & > div.mask {
    opacity: 0.2;
  }
}
.createProject {
  width: 200px;
  height: 50px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  text-align: center;
}
.page .searchProject {
  width: 200px;
  height: 50px;
  position: absolute;
  top: 20px;
  left: calc(50% - 100px);
  text-align: center;
}
.CreateTitle {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  height: 50px;
  font-size: 30px;
}
</style>
