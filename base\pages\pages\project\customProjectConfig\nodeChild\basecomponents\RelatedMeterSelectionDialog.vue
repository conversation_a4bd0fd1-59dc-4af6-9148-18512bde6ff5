<template>
  <div>
    <CetDialog
      width="600px"
      class="meter-selection-dialog"
      v-bind="CetDialog_MeterList"
    >
      <el-container>
        <el-main class="meter-tree no-padding">
          <CetTree
            v-if="show"
            :selectNode.sync="CetTree_meterList.selectNode"
            :checkedNodes.sync="CetTree_meterList.checkedNodes"
            v-bind="CetTree_meterList"
            v-on="CetTree_meterList.event"
            :default-expand-all="false"
          >
            <span class="span-ellipsis" slot-scope="{ node, data }">
              <span :title="node.label">
                <i
                  class="el-icon-success"
                  style="color: green"
                  v-if="data.isSelect"
                ></i>
                {{ node.label }}
              </span>
            </span>
          </CetTree>
        </el-main>
        <el-footer class="footer no-padding no-overflow line-h50" height="50px">
          {{ selectNodes.length == 0 ? "未选择表计" : "已选择：" }}
          <el-tag
            v-for="tag in selectNodes"
            :key="tag.name"
            closable
            type=""
            @close="deleteNode(tag.id)"
            :title="tag.txt"
          >
            {{ tag.name }}
          </el-tag>
        </el-footer>
      </el-container>
      <template slot="footer">
        <el-button size="small" type="primary" @click="confirm">确定</el-button>
        <!-- <el-button size="small" type="primary" :disabled="confirmBtnDisabled" @click="confirm">确定</el-button> -->
        <el-button size="small" @click="close">取消</el-button>
      </template>
    </CetDialog>
    <CetInterface
      :data.sync="CetInterface_meterList.data"
      :dynamicInput.sync="CetInterface_meterList.dynamicInput"
      v-bind="CetInterface_meterList"
      v-on="CetInterface_meterList.event"
    ></CetInterface>
  </div>
</template>
<script>
import customApi from "@/api/custom";
export default {
  name: "LowVoltageCabinetSelectionDialog",
  props: {
    defaultIds_in: {
      type: Array,
      default: null
    },
    limit_in: {
      type: Number,
      default: 1
    },
    deviceData_in: {
      type: Object,
      default: null
    },
    openTrigger_in: {
      type: Number,
      default: new Date().getTime()
    }
  },
  data() {
    return {
      show: true,
      props: {
        label: "nodeName",
        children: "children"
      },
      count: 1,
      selectNodes: [],
      // 设备弹窗组件
      CetDialog_MeterList: {
        title: "关联表计",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {}
      },

      // 设备树组件
      CetTree_meterList: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        lazy: true,
        load: this.treeLoad,
        nodeKey: "id",
        props: {
          label: "nodeName",
          children: "children",
          isLeaf: "leaf"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_meterList_currentNode_out,
          parentList_out: this.CetTree_meterList_parentList_out,
          checkedNodes_out: this.CetTree_meterList_checkedNodes_out,
          halfCheckNodes_out: this.CetTree_meterList_halfCheckNodes_out,
          allCheckNodes_out: this.CetTree_meterList_allCheckNodes_out
        }
      },

      // 设备树接口组件
      CetInterface_meterList: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "getMeterList",
          modelLabel: "anything",
          dataIndex: [],
          modelList: [],
          filters: [],
          treeReturnEnable: true,
          hasQueryNode: false,
          hasQueryId: true
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {},
        defaultSort: null,
        event: {
          result_out: this.CetInterface_meterList_result_out,
          finishTrigger_out: this.CetInterface_meterList_finishTrigger_out,
          failTrigger_out: this.CetInterface_meterList_failTrigger_out,
          totalNum_out: this.CetInterface_meterList_totalNum_out
        }
      },

      currentMeters: [],

      // 要输出的内容
      ouputData: {}
    };
  },
  computed: {
    selectedMeters() {
      let vm = this;
      let isSingle = vm.limit_in === 1;

      if (!vm.currentMeters || !vm.currentMeters.length) {
        return "";
      }

      if (isSingle) {
        return vm.currentMeters[0].name;
      } else {
        return vm.currentMeters.length;
      }
    },
    confirmBtnDisabled() {
      let vm = this;
      let meters = vm.currentMeters || [];
      let limit = vm.limit_in || 1;

      return meters.length <= 0 || meters.length > limit;
    },
    isSingleSelection() {
      return this.limit_in === 1;
    }
  },
  watch: {
    openTrigger_in() {
      let vm = this;

      let deviceData = vm.deviceData_in;
      if (deviceData) {
        vm.CetInterface_meterList.dataConfig.hasQueryId = true;
        vm.CetInterface_meterList.dataConfig.modelLabel = deviceData.modelLabel;
        vm.CetInterface_meterList.queryId_in = deviceData.id;
      } else {
        vm.CetInterface_meterList.dataConfig.hasQueryId = false;
        vm.CetInterface_meterList.dataConfig.modelLabel = "";
        vm.CetInterface_meterList.queryId_in = null;
      }

      vm.$nextTick(() => {
        vm.CetInterface_meterList.queryTrigger_in = new Date().getTime();
      });
    }
  },
  methods: {
    isDevice(node) {
      if (!node) {
        return false;
      }

      return node.nodeType === 269619472;
    },

    // 选中表计
    CetTree_meterList_currentNode_out(val) {
      let vm = this;
      let selectNodes = vm.selectNodes || [];
      // 判断是否为设备节点
      if (!vm.isDevice(val)) return;
      // 是否为单选
      if (vm.isSingleSelection) {
        selectNodes = [
          {
            id: val.nodeId,
            name: val.nodeName,
            txt: val.txt
          }
        ];
      } else {
        if (selectNodes.length == vm.limit_in) {
          vm.$message({ message: "最多关联" + vm.limit_in + "个表计!" });
          return;
        }
        let item = selectNodes.filter(item => {
          return item.id == val.nodeId;
        });
        if (item.length !== 0) {
          vm.$message({ message: "已选择该表计" });
          return;
        }
        selectNodes.push({
          id: val.nodeId,
          name: val.nodeName,
          txt: val.txt
        });
      }
      vm.selectNodes = selectNodes;
    },
    CetTree_meterList_parentList_out(val) {},
    CetTree_meterList_checkedNodes_out(val) {},
    CetTree_meterList_halfCheckNodes_out(val) {},
    CetTree_meterList_allCheckNodes_out(val) {},
    // 懒加载
    treeLoad(node, resolve) {
      if (node.level === 0) {
        let treeList = this.treeData;
        return resolve(treeList);
      }
      if (node.level === 2) {
        let list = node.data.children || [];
        resolve(list);
      }
      if (node.level > 2) {
        return resolve([]);
      }
      resolve(node.data.children);
    },
    // 删除tag
    deleteNode(val) {
      let selectNodes = this.selectNodes;
      this.selectNodes = selectNodes.filter(item => {
        return item.id !== val;
      });
    },
    // 表计树接口组件相关函数
    CetInterface_meterList_finishTrigger_out(val) {},
    CetInterface_meterList_failTrigger_out(val) {
      this.$message({
        message: "未能成功获取表计列表",
        type: "error"
      });
    },
    CetInterface_meterList_totalNum_out(val) {},
    // 获取节点树的数据
    CetInterface_meterList_result_out(val) {
      let vm = this;
      let treeList = val || [];
      let selectNodes = vm.defaultIds_in || [];
      treeList.forEach(item => {
        let name1 = item.nodeName;
        if (item.children) {
          item.children.forEach(item => {
            let name2 = item.nodeName;
            if (item.children) {
              item.children.forEach(item => {
                item.txt = name1 + "/" + name2 + "/" + item.nodeName;
                selectNodes.forEach(item1 => {
                  if (item1.id == item.nodeId) {
                    item1.txt = item.txt;
                  }
                });
              });
            }
          });
        }
      });
      this.treeData = treeList;

      this.selectNodes = selectNodes;
      this.show = true;

      if (vm.isSingleSelection) {
        vm.CetDialog_MeterList.title = "关联表计";
      } else {
        vm.CetDialog_MeterList.title = "关联表计（最多" + vm.limit_in + "个）";
      }

      vm.$nextTick(() => {
        vm.CetDialog_MeterList.openTrigger_in = new Date().getTime();
      });
    },

    // 确认
    confirm() {
      let vm = this;
      let selectNodes = vm._.cloneDeep(vm.selectNodes) || [];

      if (!selectNodes.length) {
        vm.$emit("metersData_out", null);
      } else {
        vm.$emit("metersData_out", selectNodes);
      }

      vm.close();
    },

    // 关闭窗口
    close() {
      this.show = false;
      this.CetDialog_MeterList.closeTrigger_in = new Date().getTime();
    }
  }
};
</script>
<style lang="scss" scoped>
.meter-selection-dialog :deep(.el-dialog__body) {
  padding: 0 20px 10px;
}
.meter-selection-dialog .footer {
  border: 0;
}
.meter-selection-dialog :deep(.footer .el-input__inner) {
  text-align: end;
  text-align: right;
}

.meter-tree {
  height: 470px;
}
.meter-tree :deep(.el-tree) {
  border: 1px solid #ebebeb;
  border-top: 0;
  overflow: auto;
}
</style>
