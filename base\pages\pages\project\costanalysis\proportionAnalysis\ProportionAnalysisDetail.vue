﻿<template>
  <!-- 1弹窗组件 -->
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="CetDialog">
    <div class="eem-cont-c1">
      <div class="proportionAnalysisDetail-title">{{ $T("基本信息") }}</div>
      <CetForm
        class="tabBox"
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
      >
        <el-form-item :label="`${$T('名称')}：`" prop="name">
          <div style="font-size: 16px; text-align: right">
            {{ CetForm_1.data.objectName }}
          </div>
        </el-form-item>
        <el-form-item :label="`${$T('所属节点')}：`" prop="name">
          <div style="font-size: 16px; text-align: right">
            {{ CetForm_1.data.objectPath }}
          </div>
        </el-form-item>
        <el-form-item :label="`${$T('能耗')}（${symbol_in}）：`" prop="name">
          <div style="font-size: 16px; text-align: right">
            {{ CetForm_1.data.energyValue }}
          </div>
        </el-form-item>
        <el-form-item :label="`${$T('成本合计（元）')}：`" prop="name">
          <div style="font-size: 16px; text-align: right">
            {{ CetForm_1.data.cost }}
          </div>
        </el-form-item>
        <el-form-item :label="`${$T('损耗占比(%)')}：`" prop="name">
          <div style="font-size: 16px; text-align: right">
            {{ CetForm_1.data.lossRate }}
          </div>
        </el-form-item>
        <!-- <el-form-item label="损耗成本（元）：" prop="name">
        <div style="font-size: 16px;color: #0033FF;text-align: right;">{{ CetForm_1.data.key2 }}</div>
      </el-form-item> -->
      </CetForm>
      <div class="proportionAnalysisDetail-title">{{ $T("成本构成") }}</div>
      <CetTable
        style="height: 300px"
        :data.sync="CetTable_1.data"
        :dynamicInput.sync="CetTable_1.dynamicInput"
        v-bind="CetTable_1"
        v-on="CetTable_1.event"
      >
        <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
        <ElTableColumn v-bind="ElTableColumn_nodeName"></ElTableColumn>
        <ElTableColumn v-bind="ElTableColumn_energyType$text"></ElTableColumn>
        <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
        <ElTableColumn
          v-bind="ElTableColumn_costcheckitem$text"
        ></ElTableColumn>
      </CetTable>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import { httping } from "@omega/http";
export default {
  name: "ProportionAnalysisDetail",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    symbol_in: {
      type: String
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data() {
    const language = window.localStorage.getItem("omega_language") === "en";
    return {
      CetDialog_1: {
        title: $T("节点对象"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        }
      },
      CetButton_confirm: {
        visible_in: false,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: language ? "240px" : "150px",
        rules: {},
        event: {
          currentData_out: this.CetForm_1_currentData_out,
          saveData_out: this.CetForm_1_saveData_out,
          finishData_out: this.CetForm_1_finishData_out,
          finishTrigger_out: this.CetForm_1_finishTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        // prop: "", // 支持path a[0].b
        label: "#", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "65" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      // nodeName组件
      ElTableColumn_nodeName: {
        //type: "",      // selection 勾选 index 序号
        prop: "nodeName", // 支持path a[0].b
        label: $T("节点名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      // 设置组件唯一识别字段组件
      ElTableColumn_energyType$text: {
        //type: "",      // selection 勾选 index 序号
        prop: "energyType$text", // 支持path a[0].b
        label: $T("能源类型"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      // name组件
      ElTableColumn_name: {
        //type: "",      // selection 勾选 index 序号
        prop: "name", // 支持path a[0].b
        label: $T("核算方案"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      // costcheckitem$text组件
      ElTableColumn_costcheckitem$text: {
        //type: "",      // selection 勾选 index 序号
        prop: "costcheckitem$text", // 支持path a[0].b
        label: $T("成本构成"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {
      var vm = this;
    },
    inputData_in(val) {
      this.CetForm_1.data = val;
      this.getTableData(val.objectId, val.objectLabel);
    }
  },

  methods: {
    getTableData(id, modelLabel) {
      httping({
        url: `/eem-service/v1/schemeConfig/costCheckNodeConfig/${modelLabel}/${id}`,
        method: "GET"
      }).then(response => {
        if (response.code === 0 && response.data) {
          var costcheckitemArr = [];
          if (
            response.data.costcheckitem_model &&
            response.data.costcheckitem_model.length > 0
          ) {
            response.data.costcheckitem_model.forEach(item => {
              costcheckitemArr.push(item.name);
            });
          }
          this.CetTable_1.data = [
            {
              nodeName: this.inputData_in.objectName,
              energyType$text: this.inputData_in.energyType$text,
              name: response.data.name,
              costcheckitem$text: costcheckitemArr.join("+")
            }
          ];
        } else {
          this.CetTable_1.data = [];
        }
      });
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {},
    CetDialog_1_open_out(val) {},
    CetDialog_1_close_out(val) {},
    CetForm_1_currentData_out(val) {},
    CetForm_1_saveData_out(val) {},
    CetForm_1_finishData_out(val) {},
    CetForm_1_finishTrigger_out(val) {},
    CetTable_1_outputData_out(val) {},
    CetTable_1_record_out(val) {}
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.tabBox {
  display: flex;
  flex-wrap: wrap;
  & > .el-form-item {
    width: 50%;
    display: inline-block;
  }
}
.proportionAnalysisDetail-title {
  border-width: 0px;
  height: 26px;
  line-height: 26px;
  background: inherit;
  @include background_color(B1);
  border: none;
  border-radius: 11px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  text-align: center;
  margin-bottom: 10px;
}
.CetDialog {
  :deep(.el-dialog__body) {
    @include padding(J1);
    @include background_color(BG);
  }
}
</style>
