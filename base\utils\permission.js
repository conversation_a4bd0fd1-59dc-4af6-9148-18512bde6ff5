import util from "@omega/layout/utils/util";
import omegaPermission from "@omega/auth/permission";
import store from "@/store/index.js";
import { conf } from "@omega/app";
import customApi from "@/api/custom";
import _ from "lodash";
let permission = [];
function checkSubMenuListPermission(item) {
  let hasValid = false;

  if (item.subMenuList) {
    for (const _item of item.subMenuList) {
      if (_item.type === "menuItem") {
        // menuItem 没有配置 permission 默认为有效的
        hasValid = !!_item.permission;
      } else {
        hasValid = checkSubMenuListPermission(_item);
      }
      if (hasValid) {
        return true;
      }
    }
  } else {
    hasValid = omegaPermission.checkPermission(item.permission);
    if (hasValid) {
      return true;
    }
  }
  return false;
}

/**********  页面权限 **********/
export function getPagePermissionTreeNodes(
  hasRootNode = false,
  module = ["fw"],
  all = false
) {
  const getTreeId = util.genUid("tree_id_");

  let navmenu = conf.getNavmenu();
  if (store.state.systemCfg.isPlatformModel) {
    // 平台环境暂不支持在线菜单配置，直接用菜单json
    navmenu = store.state.navmenu;
  }
  if (navmenu && navmenu.length === 0) {
    navmenu = store.state.navmenu;
  }
  // 合并所有的 navmenu
  const nodes = Object.keys(navmenu)
    .reduce((acc, cur) => acc.concat(navmenu[cur]), [])
    .filter(item => module.indexOf(item.Module) >= 0 || item.isCustom);
  // 添加 tree_id
  function loop(nodes, cnodes) {
    for (const node of nodes) {
      if (all || checkSubMenuListPermission(node)) {
        const cnode = {
          tree_id: getTreeId(),
          label: node.label,
          type: node.type
        };

        if (!node.type || node.type === "menuItem") {
          Object.assign(cnode, {
            location: node.location,
            permission: node.permission
          });
        }
        if (node.subMenuList) {
          Object.assign(cnode, {
            subMenuList: []
          });
          loop(node.subMenuList, cnode.subMenuList);
        }
        cnodes.push(cnode);
      }
    }
  }

  const cnodes = [];

  loop(nodes, cnodes);

  // 添加only-report页面权限
  cnodes.push({
    tree_id: "only_report",
    label: "onlyReport报表设计",
    location: "/only_report",
    type: "menuItem",
    permission: "only_report"
  });

  if (hasRootNode) {
    return [
      {
        tree_id: getTreeId(),
        label: "全选",
        subMenuList: cnodes
      }
    ];
  }

  return cnodes;
}

export function getPagePermissionFilterNodes(
  pageNodes,
  pagePermissionTreeNodes
) {
  pagePermissionTreeNodes =
    pagePermissionTreeNodes || getPagePermissionTreeNodes();
  const filterNodes = [];
  pageNodes.forEach(pageNode => {
    const treeNode = util.find(pagePermissionTreeNodes, pageNode.id, {
      valueKey: "permission",
      childKey: "subMenuList"
    });
    if (treeNode) {
      filterNodes.push({
        tree_id: treeNode.tree_id
      });
    }
  });
  return filterNodes;
}
export async function getPermission() {
  const res = await customApi.getPermissionTree();
  permission = res?.data || [];
}
/***********    操作权限  *****************/
export function getOperatePermissionTreeNodes(all = false, fliterArr) {
  const isEn = window.localStorage.getItem("omega_language") === "en";
  function dealData(data) {
    (data || []).forEach(item => {
      item.type = item.parentId === 0 ? "permissiongroup" : "permission";
      item.children?.length && dealData(item.children);
      !isEn && (item.name = item.label);
    });
  }
  const copyCermission = _.cloneDeep(permission);
  dealData(copyCermission);
  return copyCermission.filter(permission => {
    if (
      !all &&
      permission.parentId &&
      !omegaPermission.checkPermission(permission.onlyKey)
    ) {
      return false;
    }
    const [groupKey] = permission.onlyKey.split("_");
    if (fliterArr && fliterArr.length && fliterArr.includes(groupKey)) {
      return false;
    }
    return true;
  });
}
