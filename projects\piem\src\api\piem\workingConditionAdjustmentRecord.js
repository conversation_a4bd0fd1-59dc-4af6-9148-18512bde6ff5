import fetch from "eem-utils/fetch";
let version = "v1";

// 查询工况调整记录列表
export function queryWorkingConditionAdjustmentRecord(data) {
  return fetch({
    url: `/piem/${version}/gascompressor/adjustmentoperation/queryPageList`,
    method: `POST`,
    data
  });
}

// 查询工况调整记录详情
export function queryWorkingConditionDetailsById(data) {
  return fetch({
    url: `/piem/${version}/gascompressor/adjustmentoperation/queryDetailById`,
    method: `POST`,
    data
  });
}

// 查询运行方案详情-通过压缩机id
export function queryDeviceOperationSchemeDetailsByGasCompressorId(data) {
  return fetch({
    url: `/piem/${version}/gascompressor/operationscheme/queryByGascompressorId`,
    method: `POST`,
    data
  });
}

// 保存工况调整记录
export function saveWorkingConditionAdjustmentRecord(data) {
  return fetch({
    url: `/piem/${version}/gascompressor/adjustmentoperation/upsertAdjustment`,
    method: `POST`,
    data
  });
}

// 删除工况调整记录
export function deleteWorkingConditionAdjustmentRecord(data) {
  return fetch({
    url: `/piem/${version}/gascompressor/adjustmentoperation/deleteAdjustment`,
    method: `POST`,
    data
  });
}

// 获取已经配置运行方案的压缩机列表
export function queryGasCompressorBySheme(data) {
  return fetch({
    url: `/piem/${version}/gascompressor/operationscheme/queryexistschemeList`,
    method: `POST`,
    data
  });
}
