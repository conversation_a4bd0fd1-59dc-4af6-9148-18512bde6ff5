<template>
  <div>
    <el-dialog
      :title="$T('导入数据')"
      :visible.sync="dialogVisible"
      width="600px"
    >
      <div class="box-form">
        <div class="box-upload">
          <el-upload
            class="upload-demo"
            drag
            ref="upload"
            :action="uploadhttp"
            :on-change="uploadChange"
            :before-remove="fileRemove"
            :auto-upload="false"
            :multiple="false"
            :limit="1"
            :on-success="uploadSuccess"
            :headers="{
              Authorization: this.token,
              'User-ID': this.UserID,
              projectId: this.projectId
            }"
            accept=".xls,.xlsx"
          >
            <div class="uploadWrap">
              <i class="el-icon-upload icon"></i>
              <div class="explain">
                {{ $T("将文件拖到此处，或") }}
                <span class="ZS">{{ $T("点击上传") }}</span>
              </div>
              <div class="extension">{{ $T("支持拓展名.xlsx") }}</div>
              <div class="Downloadform" @click.stop="buttonimport">
                {{ $T("点击下载表格模板") }}
              </div>
            </div>
          </el-upload>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <CetButton
          class="custom—gray mrJ"
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <el-button
          :disabled="confirmDisabled"
          type="primary"
          @click="contracttypeConfirm"
        >
          {{ $T("确定") }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import common from "eem-utils/common.js";
export default {
  props: {
    openTrigger_in: {
      type: Number
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    UserID() {
      return this.$store.state.userInfo.id;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    systemCfg() {
      return this.$store.state.systemCfg;
    }
  },
  data() {
    return {
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      confirmDisabled: true,
      dialogVisible: false,
      uploadhttp: "/eem-service/video/config/excel/import"
    };
  },
  watch: {
    openTrigger_in() {
      this.dialogVisible = true;
      this.confirmDisabled = true;
      this.$nextTick(() => {
        this.$refs.upload.clearFiles();
      });
    }
  },
  methods: {
    CetButton_cancel_statusTrigger_out() {
      this.dialogVisible = false;
    },
    contracttypeConfirm() {
      this.$refs.upload.submit();
    },
    buttonimport() {
      const url = `/eem-service/video/config/excel/export?templateFlag=true`;
      common.downExcelGET(url, {}, this.token, this.projectId);
    },
    uploadChange(file, fileList) {
      if (file.name.indexOf(".xlsx") === -1) {
        this.$message({
          type: "warning",
          message: $T("只能上传xlsx格式文件")
        });
        this.confirmDisabled = true;
        this.$refs.upload.clearFiles();
        return false;
      }
      var uploadDocSize = this.systemCfg.uploadDocSize || 10;
      const isLimit100M = file.size / 1024 / 1024 < uploadDocSize;
      if (!isLimit100M) {
        this.confirmDisabled = true;
        this.$message.error($T("上传文件超过规定的最大上传大小"));
        this.$refs.upload.clearFiles();
        return false;
      }
      if (fileList && fileList.length > 0) {
        this.confirmDisabled = false;
      } else {
        this.confirmDisabled = true;
      }
    },
    fileRemove() {
      this.confirmDisabled = true;
    },
    //上传成功
    uploadSuccess(response) {
      if (response.code === 0) {
        this.$message({
          message: $T("上传成功"),
          type: "success"
        });
        this.dialogVisible = false;
        this.$emit("refresh");
      } else {
        this.$refs.upload.clearFiles();
        this.$message({
          type: "error",
          message: response.msg
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.box-form {
  border-radius: 0px !important;
  @include background_color(BG);
  @include padding(J1);
}
:deep(.el-dialog .el-dialog__body) {
  padding: 0px !important;
}
.box-upload {
  display: flex;
  justify-content: center;
  align-items: center;
  @include background_color(BG1);
  @include border_radius(C2);
}
.box-upload {
  width: 100%;
  @include padding(J2);
  display: block;
  box-sizing: border-box;
  :deep(.el-upload.el-upload--text),
  :deep(.el-upload-dragger) {
    @include background_color(BG1);
    // border-radius: 8px;
    border: none;
    width: 100%;
    height: 100%;
  }
  .uploadWrap {
    padding-bottom: 62px;
    .icon {
      margin-top: 73px;
    }
    .explain {
      @include font_color(T3);
      .ZS {
        @include font_color(ZS);
      }
    }
    .extension {
      @include font_color(T4);
    }
    .Downloadform {
      margin-top: 23px;
      padding: 9px 16px;
      display: inline-block;
      border: 1px solid;
      @include border_color(ZS);
      border-radius: 8px;
      cursor: pointer;
      @include font_color(ZS);
    }
  }
}
</style>
