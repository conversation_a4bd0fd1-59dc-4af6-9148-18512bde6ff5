<template>
  <!-- 1弹窗组件 -->
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="CetDialog">
    <el-row width="100%" style="overflow: auto; max-height: calc(84vh - 156px)">
      <el-col :span="12">
        <div class="eem-cont-c1">
          <CetGiantTree
            style="height: 506px"
            v-bind="CetGiantTree_1"
            v-on="CetGiantTree_1.event"
          ></CetGiantTree>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="eem-cont-c1 mlJ1">
          <!-- <div class="event-keyword">
          <el-input placeholder="输入事件描述关键字" prefix-icon="el-icon-search" v-model="CetDialog_1.data.description"></el-input>
        </div>
        <div class="clearfix time">
          <div class="time-label">时段:</div>
          <div class="time-range">
            <TimeRange @change="handleChange_out" :val="[CetDialog_1.data.starttime, CetDialog_1.data.endtime]"></TimeRange>
          </div>
        </div> -->
          <div class="event-box">
            <span class="event-box-head">
              {{ $T("事件类型") }}
              <div class="fr">
                <input
                  type="checkbox"
                  v-model="checkAll"
                  @change="check"
                  class="checkbox"
                  id="c1"
                />
                <label for="c1">{{ $T("全选") }}</label>
              </div>
            </span>
            <div class="event-box-content">
              <ElCheckboxGroup
                v-model="CetDialog_1.data.types"
                v-bind="ElCheckboxGroup_1"
                v-on="ElCheckboxGroup_1.event"
              >
                <ElCheckbox
                  v-for="item in ElCheckboxList_1.options_in"
                  :key="item[ElCheckboxList_1.key]"
                  :label="item[ElCheckboxList_1.label]"
                  :disabled="item[ElCheckboxList_1.disabled]"
                >
                  {{ item[ElCheckboxList_1.text] }}
                </ElCheckbox>
              </ElCheckboxGroup>
            </div>
          </div>
          <div class="event-box">
            <span class="event-box-head">
              {{ $T("事件等级") }}
              <div class="fr">
                <input
                  type="checkbox"
                  v-model="checkAllLevel"
                  @change="checkLevel"
                  id="c2"
                />
                <label for="c2">{{ $T("全选") }}</label>
              </div>
            </span>
            <div class="event-box-content">
              <ElCheckboxGroup
                v-model="CetDialog_1.data.levels"
                v-bind="ElCheckboxGroup_2"
                v-on="ElCheckboxGroup_2.event"
              >
                <ElCheckbox
                  v-for="item in ElCheckboxList_2.options_in"
                  :key="item[ElCheckboxList_2.key]"
                  :label="item[ElCheckboxList_2.label]"
                  :disabled="item[ElCheckboxList_2.disabled]"
                >
                  {{ item[ElCheckboxList_2.text] }}
                </ElCheckbox>
              </ElCheckboxGroup>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import customApi from "@/api/custom";
import TimeRange from "eem-components/TimeRange";
import moment from "moment";
import TREE_PARAMS from "@/store/treeParams.js";
export default {
  name: "AdvancedQuery",
  components: {
    TimeRange
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    keyWord_in: {
      type: String
    },
    clickNode_in: {
      type: Object
    },
    nodes: {
      type: Object
    },
    peceventlevel: {
      type: Array
    },
    selectNode: {
      type: Object
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.localStorage) {
          return false;
        } else {
          var storage = window.localStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },

  data(vm) {
    return {
      checkAll: true,
      checkAllLevel: true,
      CetDialog_1: {
        title: $T("事件-高级查询"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        },
        data: {
          starttime: moment().startOf("d").valueOf(),
          endtime: moment().endOf("d").valueOf() + 1,
          checkbox: [0], //收敛事件
          children: [
            {
              modelLabel: "",
              nodeId: 0,
              name: ""
            }
          ], //查询收敛事件
          cycle: 0, //年17 月14 日12 小时7
          description: "", //事件描述
          id: 0,
          name: "",
          index: 0,
          limit: 20,
          modelLabel: "",
          status: 0, //事件状态
          types: [], //事件类型
          levels: [] //事件等级
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetLabel_2: {
        dataConfig: {
          edition: "v1",
          queryUrl: "",
          type: "post",
          modelLabel: "",
          dropItemText: "",
          displayTextLeft: "",
          displayTextRight: "",
          width: "150px",
          textAlign: "left",
          fontSize: "14px"
        },
        dataMode: "static",
        val: $T("时段"),
        id_in: "",
        queryNode_in: {},
        queryTime_in: {
          timeType: 1,
          time: null
        },
        fontColor_in: "#606266",
        queryTrigger_in: new Date().getTime(),
        config: {}
      },
      ElCheckboxGroup_1: {
        value: [],
        style: {
          display: "inline-block",
          width: "100%"
        },
        event: {
          change: this.ElCheckboxGroup_1_change_out
        }
      },
      ElCheckboxList_1: {
        options_in: [],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      ElCheckboxGroup_2: {
        value: [],
        style: {
          display: "inline-block",
          width: "100%"
        },
        event: {
          change: this.ElCheckboxGroup_2_change_out
        }
      },
      ElCheckboxList_2: {
        options_in: [],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      eventKeyWord: "",
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out //选中单行输出
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.getTreeData();
    },
    "CetDialog_1.data.types"(val) {
      if (val.length == this.ElCheckboxList_1.options_in.length) {
        this.checkAll = true;
      } else {
        this.checkAll = false;
      }
    },
    "CetDialog_1.data.levels"(val) {
      if (val.length == this.ElCheckboxList_2.options_in.length) {
        this.checkAllLevel = true;
      } else {
        this.checkAllLevel = false;
      }
    },
    peceventlevel(val) {
      if (this.peceventlevel && this.peceventlevel.length > 0) {
        this.CetDialog_1.data.levels = this.peceventlevel;
      }
    }
  },

  methods: {
    getTreeData() {
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.distributionEquipment,
        treeReturnEnable: true
      };
      customApi.getNodeTree(data).then(res => {
        if (res.code === 0) {
          const resData = res.data || [];
          _this.CetGiantTree_1.inputData_in = resData;
          _this.CetGiantTree_1.selectNode = resData[0];
          if (res.data && res.data.length > 0) {
            if (!_this.nodes) {
              _this.CetGiantTree_1.selectNode = resData[0];
            } else {
              _this.CetGiantTree_1.selectNode = {
                tree_id: _this.nodes.modelLabel + "_" + _this.nodes.id
              };
            }
            if (_this.selectNode) {
              _this.CetTree_1.selectNode = {
                tree_id: _this.selectNode.modelLabel + "_" + _this.selectNode.id
              };
            }
          }
        } else {
          _this.$message.error(res.msg);
          _this.clearRoomsTreeData();
        }
      });
    },
    // 清除项目节点树数据
    clearRoomsTreeData() {
      this.CetGiantTree_1.inputData_in = [];
      this.CetGiantTree_1.selectNode = null;
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.$emit("saveData_out", this.CetDialog_1.data);
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    ElCheckboxGroup_1_change_out(val) {},
    ElCheckboxGroup_2_change_out(val) {},
    CetDialog_1_open_out(val) {},
    CetDialog_1_close_out(val) {},
    CetLabel_2_currenttext_out(val) {},
    CetGiantTree_1_currentNode_out(val) {
      if (!val) {
        return;
      }
      // if (val.childSelectState == 2) {
      //   this.$message.warning("你没有此节点的全部权限！");
      //   return;
      // }
      this.CetGiantTree_1.selectNode = this._.cloneDeep(val);
      // this.CetDialog_1.data.nodes = [{ id: val.id, modelLabel: val.modelLabel }];
      this.CetDialog_1.data.id = val.id;
      this.CetDialog_1.data.name = val.name;
      this.CetDialog_1.data.modelLabel = val.modelLabel;
      var children = val.children || [];
      function fn(children) {
        var arr = [];
        if (children && children.length > 0) {
          children.forEach(item => {
            var obj = {};
            if (item.children && item.children.length > 0) {
              obj = {
                nodeId: item.id,
                name: item.name,
                modelLabel: item.modelLabel,
                children: fn(item.children)
              };
            } else {
              obj = {
                nodeId: item.id,
                name: item.name,
                modelLabel: item.modelLabel,
                children: []
              };
            }
            arr.push(obj);
          });
        }
        return arr;
      }
      this.CetDialog_1.data.children = fn(children);
    },
    handleChange_out(val) {
      if (!val) {
        return;
      }
      if (val[0] === val[1]) {
        this.CetDialog_1.data.starttime = new Date(val[0]).getTime();
        this.CetDialog_1.data.endtime =
          new Date(val[0]).getTime() + 1000 * 3600 * 24;
        this.$message.warning($T("结束时间不能小于等于开始时间！"));
        return;
      }
      this.CetDialog_1.data.starttime = val[0] ? val[0] : new Date().getTime();
      this.CetDialog_1.data.endtime = val[1]
        ? val[1]
        : new Date().getTime() + 1000 * 3600 * 24;
    },
    loadEnumrations_out(val) {
      customApi.queryEnumerations("peceventlevel").then(res => {
        if (res.code !== 0 || res.data.length === 0) return;
        const list = [];
        res.data.forEach(item => {
          list.push(item.id);
        });
        this.ElCheckboxList_2.options_in = res.data;
        if (this.$route.params?.keepParams?.queryParams?.level?.length) {
          this.CetDialog_1.data.levels =
            this.$route.params.keepParams.queryParams.level;
        } else {
          this.CetDialog_1.data.levels = list;
        }
      });
      customApi.queryEnumerations("peceventtype").then(res => {
        if (res.code !== 0 || res.data.length === 0) return;
        const list = [];
        res.data.forEach(item => {
          list.push(item.id);
        });
        this.ElCheckboxList_1.options_in = res.data;
        if (this.$route.params?.keepParams?.queryParams?.eventType?.length) {
          this.CetDialog_1.data.types =
            this.$route.params.keepParams.queryParams.eventType;
        } else {
          this.CetDialog_1.data.types = list;
        }
      });
    },
    check(val) {
      if (this.checkAll) {
        const list = [];
        this.ElCheckboxList_1.options_in.forEach(item => {
          list.push(item.id);
        });
        this.CetDialog_1.data.types = list;
      } else {
        this.CetDialog_1.data.types = [];
      }
    },
    checkLevel(val) {
      if (this.checkAllLevel) {
        const list = [];
        this.ElCheckboxList_2.options_in.forEach(item => {
          list.push(item.id);
        });
        this.CetDialog_1.data.levels = list;
      } else {
        this.CetDialog_1.data.levels = [];
      }
    }
  },

  created: function () {
    const vm = this;
    // 初始化时间
    vm.CetDialog_1.data.starttime = moment().startOf("d").valueOf();
    vm.CetDialog_1.data.endtime = moment().endOf("d").valueOf() + 1;
    vm.loadEnumrations_out();
  },
  mounted: function () {
    // this.getTreeData();
  },
  activated() {
    if (this.$route.params?.keepParams) {
      //如果level为空数组，则不进行level和eventType的筛选
      if (!this.$route.params.keepParams.queryParams.level.length) {
        this.loadEnumrations_out();
        return;
      }
      this.CetDialog_1.data.levels =
        this.$route.params.keepParams.queryParams.level;
      this.CetDialog_1.data.types =
        this.$route.params.keepParams.queryParams.eventType;
    }
  }
};
</script>
<style lang="scss" scoped>
.event-box {
  border-width: 1px;
  border-style: solid;
  @include border_color(B1);
  border-radius: 4px;
  @include margin_bottom(J1);
}

.event-box-head {
  display: inline-block;
  width: 100%;
  height: 30px;
  line-height: 30px;
  // background-color: rgba(242, 242, 242, 1);
  box-sizing: border-box;
  border-width: 0 0 1px;
  border-style: solid;
  @include border_color(B1);
  border-radius: 4px 4px 0 0;
  padding: 0 20px;

  input[type="checkBox"] {
    display: none;
  }
  label {
    cursor: pointer;
  }
  input[type="checkBox"] + label:before {
    border: #ccc 1px solid;
    content: "\00a0";
    display: inline-block;
    font: 12px/1em sans-serif;
    height: 13px;
    margin: 0 4px 0 0;
    padding: 0;
    vertical-align: center;
    width: 13px;
  }
  input[type="checkBox"]:checked + label:before {
    @include background_color(ZS);
    color: #fff;
    content: "\2713";
    text-align: center;
  }
  input[type="checkBox"]:checked + label:after {
    font-weight: bold;
  }
}

.event-box-content {
  @include padding(J3);
  :deep(.el-checkbox) {
    min-width: 140px;
    line-height: 20px;
  }
}

.device-DatePicker {
  width: 280px;
  margin-left: 15px;
}

.device-left-tree {
  height: 520px !important;
  overflow: hidden;
}

.time {
  width: 100%;
  display: flex;
  height: 30px;
  margin-top: 10px;
}
.time-label {
  width: 50px;
  line-height: 30px;
}
.time-range {
  flex: 1;
}
.event-keyword {
  margin-top: 8px;
}
.CetDialog {
  :deep(.el-dialog__body) {
    @include padding(J1);
    @include background_color(BG);
  }
}
</style>
