﻿<template>
  <div class="page eem-common">
    <el-container class="fullheight">
      <el-aside width="315px" class="eem-aside">
        <CetTree
          :selectNode.sync="CetTree_left.selectNode"
          :checkedNodes.sync="CetTree_left.checkedNodes"
          v-bind="CetTree_left"
          v-on="CetTree_left.event"
        ></CetTree>
      </el-aside>
      <el-container class="page-main mlJ3 eem-container">
        <CetGraph v-bind="CetGraphData"></CetGraph>
      </el-container>
    </el-container>
    <DiagnosticAnalysisDialog v-bind="Dialog"></DiagnosticAnalysisDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";
import CetGraph from "cet-graph";
import { CetGraphConfig } from "cet-graph";
import DiagnosticAnalysisDialog from "./components/DiagnosticAnalysisDialog.vue";
import { httping } from "@omega/http";
export default {
  name: "RealtimeMonitor",
  components: {
    CetGraph,
    DiagnosticAnalysisDialog
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userInfo() {
      var vm = this;
      return vm.$store.state.userInfo;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    belongTenantId() {
      var vm = this;
      return vm.$store.state.projectInfo?.belongTenantId;
    }
  },

  data() {
    return {
      Dialog: {
        condition: null,
        show_trigger_in: -1
      },
      CetTree_left: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: false,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "text",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_left_currentNode_out
        },
        MainGraphPath: null
      },
      CetGraphData: {
        path_in: "",
        refresh_trigger_in: 0,
        userName_in: "ROOT",
        excuteJSInterception: async (details, done) => {
          const js = details.javascript;

          if (js !== "@expertsuggestion") {
            done();
            return;
          }

          const target = details.target;
          if (!target?.blinking) {
            return;
          }

          const condition = target.activatedBlinkingCondition;
          if (!condition) {
            return;
          }

          this.Dialog.condition = {
            deviceId: condition.deviceId,
            measureId: condition.measureId,
            projectId: this.$store.state.projectId
          };
          this.Dialog.show_trigger_in = Date.now();
        }
      },
      isFullscreen: false,
      showOperateBar: false,
      expandNode: []
    };
  },
  watch: {},

  methods: {
    CetTree_left_currentNode_out(val) {
      if (val && val.leaf) {
        if (val && val.leaf) {
          this.CetGraphData.path_in = val.nodeName;
          this.CetGraphData.refresh_trigger_in = new Date().getTime();
        }
      }
    },
    getTreeData_out() {
      var me = this;
      var queryData = {
        tenantId: me.belongTenantId || this.userInfo.tenantId,
        userId: me.userInfo.id
      };
      customApi.getGraphTree(queryData).then(res => {
        if (res.code === 0) {
          var treeData = me.formatGraphNodeTree(res.data || []);
          // console.log("nodeTree --> treeData ", treeData);
          me.CetTree_left.inputData_in = treeData;
          // me.$store.commit("setAllGraphNode", treeData);
          if (!me.CetTree_left.selectNode.tree_id && treeData.length > 0) {
            me.CetTree_left.selectNode = me.findChildren(treeData[0]);
          }
        }
      });
    },
    findChildren(obj) {
      if (obj.children && obj.children.length > 0) {
        return this.findChildren(obj.children[0]);
      } else {
        return obj;
      }
    },
    formatGraphNodeTree(rawNodeAry) {
      var me = this;
      rawNodeAry = rawNodeAry || [];

      me._(rawNodeAry).forEach(function (rawNode) {
        rawNode.id = rawNode.nodeId;

        if (rawNode.nodeType === 271712256) {
          rawNode.modelLabel = "graphnode"; // 自定义一个modelLabel
          rawNode.leaf = true;
        } else {
          rawNode.modelLabel = "graphfolder"; // 自定义一个modelLabel
          rawNode.leaf = false;
          rawNode.children = me.formatGraphNodeTree(rawNode.children);
        }

        rawNode.tree_id = `${rawNode.modelLabel}_${rawNode.id}`;
        // rawNode.tree_id = rawNode.tree_id;

        // 设置主画面
        if (me.CetTree_left.MainGraphPath) {
          if (me.CetTree_left.MainGraphPath === rawNode.nodeName) {
            me.CetTree_left.selectNode = rawNode;
          }
        }
      });

      return rawNodeAry;
    },
    readConfigFile_out(callback) {
      var me = this;
      httping({
        url: `/device-data-service/api/webview/v1/configfile`,
        method: "POST",
        headers: { hideNotice: true }
      }).then(
        res => {
          const graphPath = me._.get(
            res,
            "d.ResultList[0].MainGraphPath",
            null
          );
          me.CetTree_left.MainGraphPath = graphPath;
          callback && callback();
        },
        err => {
          callback && callback(err);
        }
      );
    }
  },
  created: function () {
    this.CetGraphData.userName_in = this.userInfo.name;
    localStorage.setItem("token", this.token);
    CetGraphConfig.enablePTZControl = this.systemCfg.enablePTZControl;
    CetGraphConfig.isNewAuth = this.systemCfg.isNewAuth;
    const enLanguage = window.localStorage.getItem("omega_language") === "en";
    CetGraphConfig.locale = enLanguage ? "en" : "zh-cn";
  },
  activated: function () {
    var me = this;
    me.readConfigFile_out(() => {
      me.getTreeData_out();
    });
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
}
.realtime-monitor-left {
  display: block;
}
.page-main {
  position: relative;
  transition: all 0.5s;
}
</style>
