<template>
  <div class="page eem-common">
    <el-container class="fullheight padding0">
      <el-aside width="315px" class="eem-aside">
        <CetTree
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
        ></CetTree>
      </el-aside>
      <el-container class="fullheight mlJ3 flex-column">
        <div class="clearfix eem-cont">
          <time-tool
            class="fr"
            :val.sync="startTime"
            :typeID="queryTime.cycle"
            @change="changeQueryTime"
          ></time-tool>
        </div>
        <div class="flex-auto mtJ3">
          <div class="fullheight no-overflow flex-column">
            <div class="flex-auto flex-row mbJ3">
              <div class="eem-cont minWH" style="height: 100%; flex: 1">
                <div class="fullheight flex-column">
                  <headerSpot class="mbJ3">电能质量综合指标</headerSpot>
                  <CetChart
                    class="flex-auto"
                    v-bind="CetChart2_powerQualityStatus"
                  ></CetChart>
                </div>
              </div>
              <div class="eem-cont minWH mlJ3" style="height: 100%; flex: 2">
                <div class="fullheight flex-column">
                  <headerSpot class="mbJ3">电能质量指标等级</headerSpot>
                  <CetChart
                    class="flex-auto"
                    v-bind="CetChart2_indicatorGrade"
                  ></CetChart>
                </div>
              </div>
            </div>
            <div class="flexSpecial flex-row mbJ3">
              <div class="eem-cont minWH" style="height: 100%; flex: 1">
                <div class="fullheight flex-column">
                  <headerSpot class="mbJ3">电能质量事件数量</headerSpot>
                  <CetChart v-bind="CetChart2_incidentsCount"></CetChart>
                </div>
              </div>
              <div class="eem-cont minWH mlJ3" style="height: 100%; flex: 2">
                <div class="fullheight flex-column default-scroll">
                  <headerSpot class="mbJ3">谐波电压指标</headerSpot>
                  <CetTable
                    class="flex-auto"
                    key="harmonicIndexTable"
                    :cell-style="cellStyle"
                    v-bind="CetTable2_1"
                    v-on="CetTable2_1.event"
                  >
                    <template v-for="item in ElTableColumn_1">
                      <ElTableColumn
                        :reserve-selection="true"
                        :key="item.key"
                        v-bind="item"
                        :showOverflowTooltip="true"
                        align="left"
                      ></ElTableColumn>
                    </template>
                  </CetTable>
                  <el-pagination
                    class="pagination mtJ"
                    @current-change="handleCurrentChange"
                    :current-page.sync="currentPage"
                    :page-size="5"
                    layout="total, prev, pager, next, jumper"
                    :total="tableTotal"
                  ></el-pagination>
                </div>
              </div>
            </div>
            <div class="flex-auto flex-row">
              <div class="eem-cont minWH" style="height: 100%; flex: 1">
                <div class="fullheight flex-column">
                  <headerSpot class="mbJ3">暂态事件统计</headerSpot>
                  <CetChart
                    class="flex-auto"
                    v-bind="CetChart2_transientCount"
                  ></CetChart>
                </div>
              </div>
              <div class="eem-cont minWH mlJ3" style="height: 100%; flex: 2">
                <div class="fullheight flex-column">
                  <headerSpot class="mbJ3">稳态超标次数统计</headerSpot>
                  <CetTable
                    class="flex-auto"
                    key="overrunsListTable"
                    :cell-style="cellStyle"
                    v-bind="CetTable2_2"
                    v-on="CetTable2_2.event"
                  >
                    <template v-for="item in ElTableColumn_2">
                      <ElTableColumn
                        :reserve-selection="true"
                        :key="item.key"
                        v-bind="item"
                        :showOverflowTooltip="true"
                      ></ElTableColumn>
                    </template>
                  </CetTable>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-container>
    </el-container>
    <!-- 获取电能质量综合指标接口 -->
    <CetInterface
      :data.sync="CetInterface2_allPowerQualityRank.data"
      :dynamicInput.sync="CetInterface2_allPowerQualityRank.dynamicInput"
      v-bind="CetInterface2_allPowerQualityRank"
      v-on="CetInterface2_allPowerQualityRank.event"
    ></CetInterface>
    <!-- 获取电能质量指标等级 -->
    <CetInterface
      :data.sync="CetInterface2_getPowerQualityRank.data"
      :dynamicInput.sync="CetInterface2_getPowerQualityRank.dynamicInput"
      v-bind="CetInterface2_getPowerQualityRank"
      v-on="CetInterface2_getPowerQualityRank.event"
    ></CetInterface>
    <!-- 统计电能质量事件条数 -->
    <CetInterface
      :data.sync="CetInterface2_getPQEventCount.data"
      :dynamicInput.sync="CetInterface2_getPQEventCount.dynamicInput"
      v-bind="CetInterface2_getPQEventCount"
      v-on="CetInterface2_getPQEventCount.event"
    ></CetInterface>
    <!-- 获取暂态事件统计 -->
    <CetInterface
      :data.sync="CetInterface2_getVariationCount.data"
      :dynamicInput.sync="CetInterface2_getVariationCount.dynamicInput"
      v-bind="CetInterface2_getVariationCount"
      v-on="CetInterface2_getVariationCount.event"
    ></CetInterface>
    <!-- 获取稳态指标超标次数列表 -->
    <CetInterface
      :data.sync="CetInterface2_getSteadyIndex.data"
      :dynamicInput.sync="CetInterface2_getSteadyIndex.dynamicInput"
      v-bind="CetInterface2_getSteadyIndex"
      v-on="CetInterface2_getSteadyIndex.event"
    ></CetInterface>
  </div>
</template>
<script>
import commonApi from "@/api/custom.js";
import common from "eem-utils/common";
import TimeTool from "eem-components/TimeTool.vue";
import ELECTRICAL_DEVICE from "@/store/electricaldevice.js";

export default {
  name: "IndicatorOverview",
  components: {
    TimeTool
  },
  props: {},
  computed: {
    dcbaseInfo() {
      return JSON.parse(window.localStorage.getItem("dcbaseInfo"));
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data() {
    //     // fontSizeauto(res){
    //    let docEl = document.documentElement,
    //        clientWidth = window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth;
    //    if (!clientWidth) return;
    //    let fontSizeauto = 100 * (clientWidth / 1920);
    // //    return res*fontSizeauto;

    // // }
    const dateRange = common.initDateRange("M");
    return {
      startTime: new Date().getTime(),
      queryTime: {
        startTime: common.initDateRange("M")[0],
        endTime: common.initDateRange("M")[0],
        cycle: 14
      },
      // 电能质量五项国标雷达图组件
      CetChart2_powerQualityStatus: {
        //组件输入项
        inputData_in: null,
        options: {
          tooltip: {
            formatter: function (data) {
              // console.log(data, "data");
              var arr = [];
              for (let i = 0; i < data.dimensionNames.length; i++) {
                if (data.value[i]) {
                  arr.push(
                    data.marker +
                      data.dimensionNames[i] +
                      ":" +
                      data.value[i] +
                      "%" +
                      "</br>"
                  );
                } else {
                  arr.push(
                    data.marker + data.dimensionNames[i] + ":  --" + "</br>"
                  );
                }
              }
              arr.unshift(data.seriesName + "</br>");
              const newArr = arr.join("");
              return newArr;
            }
          },
          radar: {
            center: ["50%", "50%"],
            radius: "60%",
            name: {
              textStyle: {
                // color: "#ffffff"
              }
            },
            indicator: [
              { name: "频率", max: 100 },
              { name: "谐波", max: 100 },
              { name: "闪变指标", max: 100 },
              { name: "电压不平衡度", max: 100 },
              { name: "电压偏差", max: 100 }
            ],
            splitArea: {
              // 坐标轴在 grid 区域中的分隔区域，默认不显示。
              show: true,
              areaStyle: {
                // 分隔区域的样式设置。
                color: ["rgba(255,255,255,0)", "rgba(255,255,255,0)"] // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
              }
            },
            axisLine: {
              //指向外圈文本的分隔线样式
              lineStyle: {
                type: "dashed",
                color: "#153269",
                width: 1.5
              }
            },
            splitLine: {
              lineStyle: {
                color: "#113865", // 分隔线颜色
                width: 1.5 // 分隔线线宽
              }
            }
          },

          series: [
            {
              name: "电能质量指标",
              type: "radar",
              itemStyle: {
                normal: {
                  lineStyle: {
                    color: "#145399"
                  },
                  shadowColor: "#1F95EE",
                  shadowBlur: 10
                }
              },
              areaStyle: {
                normal: {
                  color: "#1E94ED",
                  opacity: 0.2
                }
              }
            }
          ]
        }
      },
      // 电能质量指标等级
      CetChart2_indicatorGrade: {
        //组件输入项
        inputData_in: null,
        options: {
          color: [
            "#5A95FB",
            "#66CCCC",
            "#FF7574",
            "#F58E0D",
            "#08F2A2",
            "#DEA5F0",
            "#E5F0A5",
            "#868383"
          ],
          grid: {
            top: 50,
            right: 35,
            bottom: 35,
            left: 38
          },
          legend: {
            type: "scroll",
            data: []
          },
          tooltip: {
            trigger: "axis",
            // appendToBody: true,
            axisPointer: {
              type: "shadow"
            },
            formatter(data) {
              let str = data[0] && data[0].name;
              data.forEach(item => {
                str +=
                  "<br />" +
                  `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${item.color};"></span>` +
                  item.seriesName +
                  ": " +
                  ((item.value || item.value === 0) && item.value !== "--"
                    ? Number(item.value).toFixed(2)
                    : "--") +
                  "%";
              });
              return str;
            }
          },
          xAxis: {
            type: "category",
            data: ["谐波", "电压偏差", "频率", "闪变", "三相不平衡"],
            axisLabel: {
              interval: 0, //强制显示文字
              show: true
            }
          },
          yAxis: {
            type: "value",
            // axisLabel: {
            //   formatter: "{value}%"
            // }
            name: "%"
          },
          series: [
            {
              type: "bar",
              seriesLayoutBy: "row",
              barMaxWidth: 35,
              markLine: { data: [] }
            }
          ]
        }
      },
      // transientCount组件
      CetChart2_transientCount: {
        //组件输入项
        inputData_in: null,
        options: {
          color: ["#5BE27B", "#F5E48D", "#98CCF4", "#E93C3A"],
          legend: {
            type: "scroll",
            orient: "vertical",
            right: "10px",
            top: "middle",
            align: "left",
            textStyle: {
              fontFamily: "Microsoft YaHei",
              fontWeight: "normal",
              fontSize: 12
            }
          },
          title: {
            text: "--",
            textStyle: {
              // color: "#fff",
              fontSize: 24,
              fontWeight: "normal"
            },
            subtext: "告警数量",
            subtextStyle: {
              color: "#fff",
              fontSize: 14,
              fontWeight: "normal"
            },
            x: "19%",
            y: "38%",
            textAlign: "center"
          },
          series: [
            {
              seriesLayoutBy: "row",
              type: "pie",
              center: ["20%", "50%"],
              radius: ["60%", "75%"],
              label: {
                normal: {
                  formatter: "{b}\n{d}%",
                  show: false
                }
              },
              data: []
            }
          ],
          tooltip: {
            trigger: "item",
            formatter: function (data) {
              if (data.percent == 0) {
                return (
                  data.name.split("\xa0")[0] + ":  " + data.data.val + "条"
                );
              } else {
                return (
                  data.name.split("\xa0")[0] +
                  ":  " +
                  data.data.val +
                  "条,\n占" +
                  common.setNumFixed(data.percent, 1, "%")
                );
              }
            }
          }
        }
      },

      // 电能质量事件组件
      CetChart2_incidentsCount: {
        inputData_in: null,
        options: {
          color: ["#5FE27C", "#F4E38D", "#96CCF4"],
          textStyle: {
            fontFamily: "Microsoft YaHei",
            fontWeight: "normal"
          },
          legend: {
            type: "scroll",
            orient: "vertical",
            right: "10px",
            top: "middle",
            data: [],
            align: "left",
            textStyle: {
              fontFamily: "Microsoft YaHei",
              fontWeight: "normal",
              fontSize: 14
            }
          },
          title: {
            text: "--",
            textStyle: {
              // color: "#fff",
              fontSize: 24,
              fontWeight: "normal"
            },
            subtext: "告警数量",
            subtextStyle: {
              color: "#fff",
              fontSize: 14,
              fontWeight: "normal"
            },
            x: "19%",
            y: "38%",
            textAlign: "center"
          },
          // graphic: {
          //   type: "text",
          //   left: "center",
          //   top: "40%",
          //   style: {
          //     text: "在线率",
          //     textAlign: "center",
          //     fill: "#333",
          //     fontSize: 16
          //   }
          // },
          tooltip: {
            trigger: "item",
            formatter: function (data) {
              return (
                data.name.split("\xa0")[0] + ":  " + data.data.value + "条"
              );
            }
          },
          series: [
            {
              type: "pie",
              center: ["20%", "50%"],
              radius: ["50%", "65%"],
              data: [],
              label: {
                show: false
              }
            }
          ]
        }
      },
      // 1表格组件
      CetTable2_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        stripe: true,
        event: {
          record_out: this.CetTable2_1_record_out,
          outputData_out: this.CetTable2_1_outputData_out
        }
      },
      ElTableColumn_1: [
        {
          label: "分类/指标",
          prop: "name",
          width: 170,
          fixed: "left"
        },
        {
          label: "总畸变率（THDu）",
          width: 150,
          prop: "harmTime1"
        },
        ...this.createHarmTimeColumn(31)
      ],
      CetTable2_2: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        stripe: true,
        event: {
          record_out: this.CetTable2_2_record_out,
          outputData_out: this.CetTable2_2_outputData_out
        }
      },
      ElTableColumn_2: [
        {
          label: "分类/指标",
          prop: "name",
          width: 170,
          align: "left",
          fixed: "left"
        },
        {
          label: "谐波",
          prop: "harmonicexceededtimes",
          formatter: this.getColumnValue,
          align: "left"
          // width: 80
        },
        {
          label: "长时闪变",
          prop: "pltexceededtimes",
          formatter: this.getColumnValue,
          align: "left"
          // width: 80
        },
        {
          label: "电压偏差",
          prop: "voltdeviationexceededtimes",
          formatter: this.getColumnValue,
          align: "left"
          // width: 80
        },
        {
          label: "不平衡度",
          prop: "unbalanceexceededtimes",
          formatter: this.getColumnValue,
          align: "center"
          // width: 80
        },
        {
          label: "频率偏差",
          prop: "freqexceededtimes",
          formatter: this.getColumnValue,
          align: "center"
          // width: 80
        }
      ],
      // 电能质量枚举
      incidentsCount: {
        steady: "稳态",
        transient: "瞬态",
        voltagevariation: "暂态"
      },
      // 电能质量综合指标
      CetInterface2_allPowerQualityRank: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "queryAllpowerQualityRank",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "startTime_in", operator: "EQ", prop: "startTime" },
            { name: "endTime_in", operator: "EQ", prop: "endTime" },
            { name: "cycle_in", operator: "EQ", prop: "cycle" },
            { name: "id_in", operator: "EQ", prop: "id" },
            { name: "modelLabel_in", operator: "EQ", prop: "modelLabel" }
          ],
          orders: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {
          startTime_in: dateRange[0],
          endTime_in: dateRange[1],
          cycle_in: 14,
          id_in: 0,
          modelLabel: null
        },
        event: {
          result_out: this.CetInterface2_allPowerQualityRank_result_out,
          finishTrigger_out:
            this.CetInterface2_allPowerQualityRank_finishTrigger_out
        }
      },
      //电能指标等级
      CetInterface2_getPowerQualityRank: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "queryGetPowerQualityRank",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "startTime_in", operator: "EQ", prop: "startTime" },
            { name: "endTime_in", operator: "EQ", prop: "endTime" },
            { name: "cycle_in", operator: "EQ", prop: "cycle" },
            { name: "id_in", operator: "EQ", prop: "id" },
            { name: "modelLabel_in", operator: "EQ", prop: "modelLabel" }
          ],
          orders: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {
          startTime_in: dateRange[0],
          endTime_in: dateRange[1],
          cycle_in: 14,
          id_in: 0,
          modelLabel: null
        },
        event: {
          result_out: this.CetInterface2_getPowerQualityRank_result_out,
          finishTrigger_out:
            this.CetInterface2_getPowerQualityRank_finishTrigger_out
        }
      },
      //电能质量时间条数
      CetInterface2_getPQEventCount: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "queryGetPQEventCount",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "startTime_in", operator: "EQ", prop: "startTime" },
            { name: "endTime_in", operator: "EQ", prop: "endTime" },
            { name: "cycle_in", operator: "EQ", prop: "cycle" },
            { name: "id_in", operator: "EQ", prop: "id" },
            { name: "modelLabel_in", operator: "EQ", prop: "modelLabel" }
          ],
          orders: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {
          startTime_in: dateRange[0],
          endTime_in: dateRange[1],
          cycle_in: 14,
          id_in: 0,
          modelLabel: null
        },
        event: {
          result_out: this.CetInterface2_getPQEventCount_result_out,
          finishTrigger_out:
            this.CetInterface2_getPQEventCount_finishTrigger_out
        }
      },
      // 获取谐波电压指标
      tableTotal: 0,
      currentPage: 1,
      queryParams: {
        id: 0,
        modelLabel: null,
        cycle: 14,
        startTime: dateRange[0],
        endTime: dateRange[1]
      },
      // 获取暂态事件统计
      CetInterface2_getVariationCount: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "queryGetVariationCount",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "startTime_in", operator: "EQ", prop: "startTime" },
            { name: "endTime_in", operator: "EQ", prop: "endTime" },
            { name: "cycle_in", operator: "EQ", prop: "cycle" },
            { name: "id_in", operator: "EQ", prop: "id" },
            { name: "modelLabel_in", operator: "EQ", prop: "modelLabel" }
          ],
          orders: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {
          startTime_in: dateRange[0],
          endTime_in: dateRange[1],
          cycle_in: 14,
          id_in: 0,
          modelLabel: null
        },
        event: {
          result_out: this.CetInterface2_getVariationCount_result_out,
          finishTrigger_out:
            this.CetInterface2_getVariationCount_finishTrigger_out
        }
      },
      //获取稳态指标超标次数列表
      CetInterface2_getSteadyIndex: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "queryGetSteadyIndex",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "startTime_in", operator: "EQ", prop: "startTime" },
            { name: "endTime_in", operator: "EQ", prop: "endTime" },
            { name: "cycle_in", operator: "EQ", prop: "cycle" },
            { name: "id_in", operator: "EQ", prop: "id" },
            { name: "modelLabel_in", operator: "EQ", prop: "modelLabel" }
          ],
          orders: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {
          startTime_in: dateRange[0],
          endTime_in: dateRange[1],
          cycle_in: 14,
          id_in: 0,
          modelLabel: null
        },
        event: {
          result_out: this.CetInterface2_getSteadyIndex_result_out,
          finishTrigger_out: this.CetInterface2_getSteadyIndex_finishTrigger_out
        }
      },
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      }
    };
  },
  watch: {},

  methods: {
    getColumnValue(row, column, cellValue, index) {
      if (cellValue || cellValue === 0) {
        return cellValue;
      } else {
        return "--";
      }
    },
    // 1表格输出
    CetTable2_1_record_out(val) {},
    CetTable2_1_outputData_out(val) {},
    CetTable2_2_outputData_out(val) {},
    CetTable2_2_record_out(val) {},
    getParams() {
      if (!this.dcbaseInfo) return false;
      return {
        id: this.dcbaseInfo.id,
        modelLabel: this.dcbaseInfo.modelLabel,
        ...this.queryTime
      };
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (column.property != "name") {
        if (row["harmTime" + columnIndex] == "不合格") {
          return "color:#E82E3D";
        }
      }
    },
    // 获取指标
    queryBenchmarkingList() {
      common.requestData(
        {
          url: "/eem-service/v1/benchmarking/benchmarking",
          data: {
            modelLabel: "benchmarkmanagement",
            filter: {
              expressions: [
                {
                  limit: 6,
                  operator: "EQ",
                  prop: "dcindicatortype"
                }
              ]
            }
          }
        },
        data => {
          if (this._.isEmpty(data)) return;
          let options = this.CetChart2_indicatorGrade.options;

          const series0 = options.series[0];

          let min = 0;
          const marklineData = [];
          data.forEach(item => {
            if (item.dcindicatortype === 6) {
              if (item.highlimit > min) min = item.highlimit;
              marklineData.push({
                yAxis: item.lowlimit || 0,
                name: item.conclusion,
                label: {
                  formatter: "{c}" + item.conclusion,
                  position: "insideEndTop"
                }
              });
            }
          });
          if (marklineData.length > 0) {
            options.yAxis.max = min;
            series0.markLine = { data: marklineData };
            this.CetChart2_indicatorGrade.options = options;
          }
        }
      );
    },

    createHarmTimeColumn(time) {
      const arr = [];
      for (let index = 2; index < time + 1; index++) {
        arr.push({
          label: index + "次谐波含有率",
          prop: "harmTime" + index,
          minWidth: 130
        });
      }
      return arr;
    },

    getDataForMap(data, map) {
      const me = this;
      let total = 0;
      const series_data = [];
      const legend_data = [];
      me._.each(data, (item, key) => {
        total += item;
      });
      me._.each(data, (item, key) => {
        let name = map[key];

        if (!name) {
          return;
        }
        var ratio = 0;
        if (item && total) {
          ratio = item / total;
        }
        name += "\xa0\xa0\xa0\xa0" + (ratio * 100).toFixed2(1) + "% ";
        name += "\xa0\xa0\xa0\xa0" + item + "条";

        legend_data.push(name);
        series_data.push({
          name: name,
          value: item
        });
      });

      me.CetChart2_incidentsCount.options.title.text = total;
      me.CetChart2_incidentsCount.options.legend.data = legend_data;
      me.CetChart2_incidentsCount.options.series[0].data = series_data;
    },
    getPageData() {
      if (!this.currentNode || !this.currentNode.id) {
        return;
      }

      this.CetInterface2_allPowerQualityRank.queryTrigger_in =
        new Date().getTime();
      this.CetInterface2_getPowerQualityRank.queryTrigger_in =
        new Date().getTime();
      this.CetInterface2_getPQEventCount.queryTrigger_in = new Date().getTime();
      this.getHarmonicIndex();
      this.CetInterface2_getVariationCount.queryTrigger_in =
        new Date().getTime();
      this.CetInterface2_getSteadyIndex.queryTrigger_in = new Date().getTime();
    },
    // 格式化事件类型饼图的提示信息
    formatEventTypeTip(series) {
      return (
        `事件类型：${series.value[0]}<br />` + `事件条数：${series.value[1]}条`
      );
    },
    CetTree_1_currentNode_out(val) {
      if (this.currentNode && this.currentNode.tree_id === val.tree_id) {
        return;
      }
      this.currentNode = val;
      this.CetInterface2_allPowerQualityRank.dynamicInput.id_in = val.id;
      this.CetInterface2_allPowerQualityRank.dynamicInput.modelLabel_in =
        val.modelLabel;

      this.CetInterface2_getPowerQualityRank.dynamicInput.id_in = val.id;
      this.CetInterface2_getPowerQualityRank.dynamicInput.modelLabel_in =
        val.modelLabel;

      this.CetInterface2_getPQEventCount.dynamicInput.id_in = val.id;
      this.CetInterface2_getPQEventCount.dynamicInput.modelLabel_in =
        val.modelLabel;

      this.queryParams.id = val.id;
      this.queryParams.modelLabel = val.modelLabel;
      this.currentPage = 1;

      this.CetInterface2_getVariationCount.dynamicInput.id_in = val.id;
      this.CetInterface2_getVariationCount.dynamicInput.modelLabel_in =
        val.modelLabel;

      this.CetInterface2_getSteadyIndex.dynamicInput.id_in = val.id;
      this.CetInterface2_getSteadyIndex.dynamicInput.modelLabel_in =
        val.modelLabel;

      this.getPageData();
    },
    changeQueryTime({ val, timeOption }) {
      this.queryTime.cycle = timeOption.typeID;
      const date = this.$moment(val);
      const startTime = date.startOf(timeOption.unit).valueOf();
      const endTime = date.endOf(timeOption.unit).valueOf() + 1;
      const cycle = timeOption.typeID;
      //  currentDate[0] = date.subtract(shortcuts.number, shortcuts.unit).valueOf();
      // currentDate[1] = this.value[0];

      this.CetInterface2_allPowerQualityRank.dynamicInput.startTime_in =
        startTime;
      this.CetInterface2_allPowerQualityRank.dynamicInput.endTime_in = endTime;
      this.CetInterface2_allPowerQualityRank.dynamicInput.cycle_in = cycle;

      this.CetInterface2_getPowerQualityRank.dynamicInput.startTime_in =
        startTime;
      this.CetInterface2_getPowerQualityRank.dynamicInput.endTime_in = endTime;
      this.CetInterface2_getPowerQualityRank.dynamicInput.cycle_in = cycle;

      this.CetInterface2_getPQEventCount.dynamicInput.startTime_in = startTime;
      this.CetInterface2_getPQEventCount.dynamicInput.endTime_in = endTime;
      this.CetInterface2_getPQEventCount.dynamicInput.cycle_in = cycle;

      this.queryParams.startTime = startTime;
      this.queryParams.endTime = endTime;
      this.queryParams.cycle = cycle;
      this.currentPage = 1;

      this.CetInterface2_getVariationCount.dynamicInput.startTime_in =
        startTime;
      this.CetInterface2_getVariationCount.dynamicInput.endTime_in = endTime;
      this.CetInterface2_getVariationCount.dynamicInput.cycle_in = cycle;

      this.CetInterface2_getSteadyIndex.dynamicInput.startTime_in = startTime;
      this.CetInterface2_getSteadyIndex.dynamicInput.endTime_in = endTime;
      this.CetInterface2_getSteadyIndex.dynamicInput.cycle_in = cycle;

      this.getPageData();
    },
    // allPowerQualityRank展示文字输出
    CetInterface2_allPowerQualityRank_finishTrigger_out(val) {},
    CetInterface2_allPowerQualityRank_result_out(val) {
      // console.log(val, "allPowerQualityRank展示文字输出");
      this.setPowerQualityStatus(val);
    },
    // getPowerQualityRank展示文字输出
    CetInterface2_getPowerQualityRank_finishTrigger_out(val) {},
    CetInterface2_getPowerQualityRank_result_out(val) {
      // val.map(item => {
      //   if (item.name.indexOf(" ") == -1) {
      //   } else {
      //     item.name = item.name.trim().split(/\s+/)[1];
      //   }
      // });
      const arrs = this._.orderBy(val, ["name"], "asc");
      let series = [];
      const series0 = this._.cloneDeep(
        this.CetChart2_indicatorGrade.options.series[0]
      );
      let options = this._.cloneDeep(this.CetChart2_indicatorGrade.options);
      this.CetChart2_indicatorGrade.options = {};
      var nameArr = [];
      if (arrs.length) {
        for (var i = 0; i < arrs.length; i++) {
          nameArr.push(arrs[i].name);
          if (i === 0) {
            series.push({
              ...series0,
              type: "bar",
              seriesLayoutBy: "row",
              barMaxWidth: 35,
              name: arrs[i].name,
              data: [
                common.setNumFixed(arrs[i].harmonicqualificationrate),
                common.setNumFixed(arrs[i].voltdeviationqualificationrate),
                common.setNumFixed(arrs[i].freqqualificationrate),
                common.setNumFixed(arrs[i].pltqualificationrate),
                common.setNumFixed(arrs[i].unbalancequalificationrate)
              ]
            });
          } else {
            series.push({
              type: "bar",
              seriesLayoutBy: "row",
              barMaxWidth: 35,
              name: arrs[i].name,
              data: [
                common.setNumFixed(arrs[i].harmonicqualificationrate),
                common.setNumFixed(arrs[i].voltdeviationqualificationrate),
                common.setNumFixed(arrs[i].freqqualificationrate),
                common.setNumFixed(arrs[i].pltqualificationrate),
                common.setNumFixed(arrs[i].unbalancequalificationrate)
              ]
            });
          }
        }
      }
      options.legend.data = nameArr;
      options.series = series;
      this.CetChart2_indicatorGrade.options = options;
    },
    // getPQEventCount展示文字输出
    CetInterface2_getPQEventCount_finishTrigger_out(val) {},
    CetInterface2_getPQEventCount_result_out(val) {
      this.CetChart2_incidentsCount.inputData_in = this.getDataForMap(
        val,
        this.incidentsCount
      );
      // console.log(val, "getPQEventCount展示文字输出");
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getHarmonicIndex();
    },
    getHarmonicIndex() {
      if (!this.queryParams.modelLabel) return;
      const page = {
        index: (this.currentPage - 1) * 5,
        limit: 5
      };
      const params = { ...this.queryParams, page };
      commonApi.queryGetHarmonicIndex(params).then(res => {
        if (res.code === 0 && res.data?.length) {
          res.data.forEach(item => {
            item.data.forEach(it => {
              const key = "harmTime" + it.harmTime;
              item[key] = it.text || "--";
            });
          });
          this.tableTotal = res.total;
          this.CetTable2_1.data = res.data;
        } else {
          this.CetTable2_1.data = null;
          this.tableTotal = res.total;
        }
      });
    },
    // getVariationCount展示文字输出
    CetInterface2_getVariationCount_finishTrigger_out(val) {},
    CetInterface2_getVariationCount_result_out(val) {
      // function toPercent(point) {
      //   var str = Number(point * 100).toFixed(1);
      //   str += "%";
      //   return str;
      // }

      var ratio1 = 0;
      var ratio2 = 0;
      var ratio3 = 0;
      var ratio4 = 0;
      if (val.total) {
        if (val.interruption + val.longinterruption) {
          ratio1 = (val.interruption + val.longinterruption) / val.total;
        }
        if (val.longswell + val.swell) {
          ratio2 = (val.longswell + val.swell) / val.total;
        }
        if (val.longdip + val.dip) {
          ratio3 = (val.longdip + val.dip) / val.total;
        }
        if (val.undefined) {
          ratio4 = val.undefined / val.total;
        }
      }
      this.CetChart2_transientCount.options.series[0].data = [
        {
          value: ratio1,
          val: val.interruption + val.longinterruption,
          name: `电压中断\xa0\xa0\xa0\xa0${(ratio1 * 100).toFixed2(
            1
          )}%\xa0\xa0\xa0\xa0${val.interruption + val.longinterruption}条`
        },
        {
          value: ratio2,
          val: val.longswell + val.swell,
          name: `电压暂升\xa0\xa0\xa0\xa0${(ratio2 * 100).toFixed2(
            1
          )}%\xa0\xa0\xa0\xa0${val.longswell + val.swell}条`
        },
        {
          value: ratio3,
          val: val.longdip + val.dip,
          name: `电压暂降\xa0\xa0\xa0\xa0${(ratio3 * 100).toFixed2(
            1
          )}%\xa0\xa0\xa0\xa0${val.longdip + val.dip}条`
        },
        {
          value: ratio4,
          val: val.undefined,
          name: `其他\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0${ratio4.toFixed2(
            1
          )}%\xa0\xa0\xa0\xa0${val.undefined}条`
        }
      ];
      this.CetChart2_transientCount.options.title.text = val.total;
      // console.log(val, "getVariationCount展示文字输出");
    },
    // getSteadyIndex展示文字输出
    CetInterface2_getSteadyIndex_finishTrigger_out(val) {},
    CetInterface2_getSteadyIndex_result_out(val) {
      this.CetTable2_2.data = val;
      // console.log(val, "getSteadyIndex展示文字输出");
    },
    setPowerQualityStatus(pageData) {
      // 电能质量指标情况
      this.CetChart2_powerQualityStatus.inputData_in = [
        ["频率", "谐波", "闪变指标", "电压不平衡度", "电压偏差"],
        [
          common.roundNumber(
            common.findDataByKey(pageData, "freqqualificationrate", 0)
          ),
          common.roundNumber(
            common.findDataByKey(pageData, "harmonicqualificationrate", 0)
          ),
          common.roundNumber(
            common.findDataByKey(pageData, "pltqualificationrate", 0)
          ),
          common.roundNumber(
            common.findDataByKey(pageData, "unbalancequalificationrate", 0)
          ),
          common.roundNumber(
            common.findDataByKey(pageData, "voltdeviationqualificationrate", 0)
          )
        ]
      ];
    },
    setPowerEventNumber() {},
    getTreeData() {
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [
          {
            filter: {
              expressions: [{ limit: 1, operator: "EQ", prop: "roomtype" }]
            },
            modelLabel: "room"
          }
        ],
        treeReturnEnable: true
      };
      ELECTRICAL_DEVICE.forEach(item => {
        data.subLayerConditions.push({ modelLabel: item.value });
      });

      commonApi.getPqNodeTree(data, this.projectId, false).then(res => {
        if (res.code === 0) {
          _this.CetTree_1.inputData_in = res.data;
          _this.CetTree_1.selectNode = _this._.get(res, "data[0]", null);
          if (
            _this._.get(_this.currentNode, "tree_id", 1) ===
            _this._.get(res, "data[0].tree_id", 2)
          ) {
            _this.getPageData();
          }
        } else {
          _this.$message.error(res.msg);
        }
      });
    }
  },

  created: function () {
    // this.queryBenchmarkingList();
  },
  activated() {
    this.getTreeData();
    this.queryBenchmarkingList();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}

.border-box {
  box-sizing: border-box;
}

.flexSpecial {
  flex: 1.1;
  min-height: 0px;
  min-width: 0px;
}

.pagination {
  align-self: flex-end;
}
</style>
