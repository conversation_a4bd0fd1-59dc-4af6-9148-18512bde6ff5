<template>
  <CetDialog v-bind="CetDialog_project" v-on="CetDialog_project.event">
    <div>
      <CetTable
        :data.sync="CetTable_project.data"
        :dynamicInput.sync="CetTable_project.dynamicInput"
        v-bind="CetTable_project"
        v-on="CetTable_project.event"
        style="height: 300px"
        class="eem-cont-c1"
      >
        <template v-for="item in Columns_project">
          <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
        </template>
      </CetTable>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_close"
        v-on="CetButton_close.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>

<script>
export default {
  name: "projectDialog",
  components: {},
  props: {
    openTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Array
    }
  },
  data() {
    return {
      // project弹窗组件
      CetDialog_project: {
        title: $T("备件更换明细"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        showClose: true
      },
      // close组件
      CetButton_close: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_close_statusTrigger_out
        }
      },
      // project表格组件
      CetTable_project: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        "highlight-current-row": false,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      Columns_project: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "leftr",
          align: "left",
          showOverflowTooltip: true,
          //minWidth: "200",  //该宽度会自适应
          width: "60" //绝对宽度
        },
        {
          prop: "sparePartsName", // 支持path a[0].b
          label: $T("零件名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formatter
          // width: "160" //绝对宽度
        },
        {
          prop: "model", // 支持path a[0].b
          label: $T("型号"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        },
        {
          prop: "brand", // 支持path a[0].b
          label: $T("厂家"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        },
        {
          prop: "number", // 支持path a[0].b
          label: $T("数量"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        },
        {
          prop: "unit", // 支持path a[0].b
          label: $T("单位"), //列名
          headerAlign: "leftr",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        }
      ]
    };
  },
  watch: {
    openTrigger_in(val) {
      this.CetTable_project.data = this.inputData_in;
      this.CetDialog_project.openTrigger_in = val;
    }
  },
  methods: {
    // close输出
    CetButton_close_statusTrigger_out() {
      this.CetDialog_project.closeTrigger_in = new Date().getTime();
    },
    // project表格输出
    formatter(row, column, cellValue) {
      return cellValue || "--";
    }
  }
};
</script>

<style lang="scss" scoped></style>
