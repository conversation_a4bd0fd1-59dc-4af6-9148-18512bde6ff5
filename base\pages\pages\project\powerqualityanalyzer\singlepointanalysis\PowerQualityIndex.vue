<template>
  <CetChart
    :inputData_in="CetChart2_power.inputData_in"
    v-bind="CetChart2_power.config"
  ></CetChart>
</template>
<script>
import common from "eem-utils/common";
import ELECTRICAL_DEVICE from "@/store/electricaldevice";
const dcpqfivestdrate = [
  {
    label: "谐波",
    value: "harmonicqualificationrate"
  },
  {
    label: "电压偏差",
    value: "voltdeviationqualificationrate"
  },
  {
    label: "频率",
    value: "freqqualificationrate"
  },
  {
    label: "闪变指标",
    value: "pltqualificationrate"
  },
  {
    label: "电压不平衡度",
    value: "unbalancequalificationrate"
  }
];
export default {
  name: "PowerQualityIndex",
  props: {
    queryTime: Object,
    currentNode: Object,
    refreshTrigger_in: {
      type: [Number]
    }
  },
  computed: {
    powerQualityVisibi() {
      return this.$store.state.powerQualityVisibi;
    },
    modelLabels() {
      return ELECTRICAL_DEVICE.map(i => i.value);
    }
  },
  watch: {
    queryTime: {
      deep: true,
      handler: function (val, oldVal) {
        if (
          this.modelLabels.includes(
            this._.get(this.currentNode, "data.modelLabel")
          )
        )
          this.getPowerQualityRank();
      }
    },
    refreshTrigger_in: {
      deep: true,
      handler: function (val, oldVal) {
        this.getPowerQualityRank();
      }
    },
    powerQualityVisibi(val, oldVal) {
      if (val) this.getPowerQualityRank();
    },
    currentNode: {
      deep: true,
      handler: function (val, oldVal) {
        if (!this._.get(val, "data.tree_id")) return;
        if (
          this._.get(val, "data.tree_id", -1) ===
          this._.get(oldVal, "data.tree_id", -2)
        )
          return;
        if (this.modelLabels.includes(this._.get(val, "data.modelLabel")))
          this.getPowerQualityRank();
      }
    }
  },
  data() {
    return {
      // 电能质量指标组件
      CetChart2_power: {
        //组件输入项
        inputData_in: null,
        config: {
          options: {
            tooltip: {
              trigger: "item",
              formatter: function (params, ticket, callback) {
                var htmlStr = "";
                var seriesName = params.seriesName; //图例名称
                var dimensionNames = params.dimensionNames || []; //x轴名称列表
                var yvalue = params.value || []; //y轴值列表
                for (var i = 0; i < dimensionNames.length; i++) {
                  var xName = dimensionNames[i]; //x轴的名称
                  var value = yvalue[i] || "--"; //y轴值

                  if (i === 0) {
                    htmlStr += seriesName + "<br/>"; //x轴的名称
                  }
                  htmlStr += "<div>";
                  //圆点后面显示的文本
                  htmlStr += xName + "：" + value;

                  htmlStr += "</div>";
                }
                return htmlStr;
              }
            },
            radar: {
              center: ["50%", "50%"],
              name: {
                textStyle: {
                  // color: "#999999"
                }
              },
              indicator: [
                { name: "谐波", max: 100 },
                { name: "电压偏差", max: 100 },
                { name: "频率", max: 100 },
                { name: "闪变指标", max: 100 },
                { name: "电压不平衡度", max: 100 }
              ],
              splitArea: {
                // 坐标轴在 grid 区域中的分隔区域，默认不显示。
                show: true,
                areaStyle: {
                  // 分隔区域的样式设置。
                  color: ["rgba(255,255,255,0)", "rgba(255,255,255,0)"] // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
                }
              },
              axisLine: {
                //指向外圈文本的分隔线样式
                lineStyle: {
                  type: "dashed",
                  color: "#153269",
                  width: 1.5
                }
              },
              splitLine: {
                lineStyle: {
                  color: "#15e3e3", // 分隔线颜色
                  width: 1.5 // 分隔线线宽
                }
              }
            },
            series: [
              {
                type: "radar",
                name: "电能质量指标",
                areaStyle: { normal: {} }
              }
            ]
          }
        }
      }
    };
  },
  methods: {
    getPowerQualityRank() {
      if (!this._.get(this.currentNode, "data.id")) return;
      const url = "/eem-service/v1/pq/count/allPqRank";
      // if (!(this._.get( this.currentNode, "data.id") && this._.get(this.currentNode, "data.modelLabel"))) return;
      common.requestData(
        {
          url,
          data: {
            node: {
              id: this.currentNode.data.id,
              modelLabel: this.currentNode.data.modelLabel
            },
            ...this.queryTime
          }
        },
        data => {
          /* let temp = {
            name: "综合",
            pltqualificationrate: 0,
            unbalancequalificationrate: 0,
            voltdeviationqualificationrate: 0,
            freqqualificationrate: 0,
            harmonicqualificationrate: 0
          };
          dcpqfivestdrate.forEach(item => {
            temp[item.value] = this._.meanBy(data, o => o[item.value]);
          }); */

          var dataset = [[], []];
          // 需求是求平均数
          dcpqfivestdrate.forEach(item => {
            dataset[0].push(item.label);
            dataset[1].push(
              this._.round(
                this._.meanBy([data], o => o[item.value || undefined]),
                2
              )
            );
          });

          /*  dcpqfivestdrate.map(item=>item.label) */
          // 电能质量指标情况
          /* this.CetChart2_powerQualityStatus.inputData_in = [
            ["频率", "谐波", "闪变指标", "电压不平衡度", "电压偏差"],
            [
              DATA.stabilityData.rate,
              DATA.stabilityData.harmonic,
              DATA.stabilityData.flickerIndex,
              DATA.stabilityData.voltageUnbalance,
              DATA.stabilityData.voltageDeviation
            ]
          ]; */

          this.CetChart2_power.inputData_in = dataset;
        }
      );
    }
  },
  created() {
    // this.getPowerQualityRank();
  }
};
</script>
