<template>
  <div class="common-slide" @click="noThing" @mousedown="down">
    <!-- <button
      class="common-switch-aside"
      :class="{ open: !open }"
      @click="handleOpen"
    ></button> -->
    <div
      class="common-switch-aside"
      ref="commonSwitchAside"
      @click="handleOpen"
    >
      <i :class="open ? 'el-icon-arrow-left ' : 'el-icon-arrow-right'"></i>
    </div>
  </div>
</template>
<script>
export default {
  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data() {
    return {
      open: true, //控制左侧的侧边栏的伸缩
      leftWidth: null, //关闭aside前，aside的宽度
      leftdisplay: null
    };
  },
  methods: {
    noThing() {
      return false;
    },
    // 打开关闭左侧内容
    handleOpen(event) {
      var divTarget = this.$refs.commonSwitchAside;
      // 左侧jQuery对象
      var $commonAside = $(divTarget).parent().prev().children().eq(0);
      // 滑块jQuery对象
      var $commonSlide = $(divTarget).parent();
      //   var leftWidth = null;
      if (this.open) {
        this.leftWidth =
          $commonAside.width() +
          parseInt($commonAside.css("padding-left")) +
          parseInt($commonAside.css("padding-right"));
        this.leftdisplay = $commonAside.css("display");
      }
      this.open = !this.open;
      $commonSlide.css({
        left: this.open ? this.leftWidth + "px" : "0px"
      });
      $commonAside.css({
        width: this.open ? this.leftWidth + "px" : "0px",
        display: this.open ? this.leftdisplay : "none"
        // padding: this.open ? "0 15px" : "0px"
      });
      this.$emit("handSlide", new Date().getTime());
    },
    // 拖拽
    down(event) {
      var _this = this;
      var e = event || window.event;
      var divTarget = e.target || e.srcElement;
      // 如果点击的是展开收缩按钮则终止事件
      if (
        divTarget.className.indexOf("common-switch-aside") !== -1 ||
        divTarget.className.indexOf("el-icon") !== -1
      ) {
        return false;
      }
      // 收起状态下禁止拖拽
      if (!this.open) {
        return false;
      }
      var diffX = e.clientX - divTarget.offsetLeft;
      // 左侧jQuery对象
      var $commonAside = $(divTarget).prev().children().eq(0);

      document.onmousemove = function (event) {
        var e = event || window.event;
        var lientX = e.clientX;
        var left = lientX - diffX;
        var maxLeft = document.body.clientWidth - 100;
        left = left <= 100 ? 100 : left;
        left = left >= maxLeft ? maxLeft : left;
        $(divTarget).css({
          position: "absolute",
          opacity: "0.5",
          // eslint-disable-next-line no-dupe-keys
          left: left + "px"
        });
      };
      document.onmouseup = function (event) {
        var e = event || window.event;
        var lientX = e.clientX;
        var moveX = lientX - diffX;
        var maxLeft = document.body.clientWidth - 100;
        moveX = moveX <= 100 ? 100 : moveX;
        moveX = moveX >= maxLeft ? maxLeft : moveX;
        $(divTarget).css({
          opacity: "1"
        });
        $commonAside.css({
          width: moveX + "px"
        });
        document.onmousemove = null;
        document.onmouseup = null;
        _this.$emit("handSlide", new Date().getTime());
      };
      document.ondragstart = function (e) {
        return false;
      };
      document.ondragend = function (e) {
        return false;
      };
    }
  }
};
</script>
<style lang="scss" scoped>
.common-slide {
  width: 10px;
  height: 100%;
  position: absolute;
  cursor: col-resize;
  left: 315px;
  top: 0;
  z-index: 100;
  border-radius: 0 mh-get(C3) mh-get(C3) 0;
  .common-switch-aside {
    @include background_color(ZS);
    width: 10px;
    height: 71px;
    position: absolute;
    top: 50%;
    right: 0px;
    cursor: pointer;
    border-radius: 4px 0px 0px 4px;
    i {
      color: #fff;
      position: absolute;
      left: -2px;
      top: 30px;
    }
  }
}
</style>
