<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog v-bind="CetDialog_pagedialog" v-on="CetDialog_pagedialog.event">
      <template v-slot:footer>
        <span>
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
        </span>
      </template>
      <div class="bg1 brC2 rowBox">
        <omega-player
          controls
          class="video"
          ref="player"
          :controlslist="['noptzcommand']"
        ></omega-player>
        <div
          class="mtJ2"
          v-if="inputData_in && inputData_in.joinType === 'direct'"
        >
          <span class="required">*</span>
          <span>
            {{ $T("直连摄像头请修改音频为AAC，")
            }}{{ $T("视频编码为H264/H265，否则无法播放！") }}
          </span>
        </div>
      </div>
    </CetDialog>
  </div>
</template>
<script>
export default {
  name: "PreviewVideo",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("视频预览"),
        width: "960px",
        showClose: true,
        // destroyOnClose: true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      let sourceOrID = this.inputData_in.id;

      this.$nextTick(() => {
        const player = this.$refs.player;
        player.loadSourceOrID(sourceOrID);
      });
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    }
  },
  methods: {
    CetDialog_pagedialog_openTrigger_out(val) {
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      const player = this.$refs.player;
      player && player.clear();
      this.$emit("closeTrigger_out", val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    }
  }
};
</script>
<style lang="scss" scoped>
.required {
  @include font_color(Sta3);
}
.rowBox {
  @include padding(J3 J4);
  .video {
    margin: 0 auto;
    width: 100%;
    height: 600px;
  }
}
</style>
