<template>
  <div style="height: 100%">
    <el-header height="auto" class="bg-eee p5 clearfix">
      <ElRadioGroup
        class="fl"
        style="line-height: 32px"
        v-model="ElRadioGroup_spectrum.value"
        v-bind="ElRadioGroup_spectrum"
        v-on="ElRadioGroup_spectrum.event"
      >
        <ElRadio
          v-for="item in ElRadioList_spectrum.options_in"
          :key="item[ElRadioList_spectrum.key]"
          :label="item[ElRadioList_spectrum.label]"
          :disabled="item[ElRadioList_spectrum.disabled]"
        >
          {{ item[ElRadioList_spectrum.text] }}
        </ElRadio>
      </ElRadioGroup>

      <!-- 导出 -->
      <!-- export按钮组件 -->
      <CetButton
        class="fr"
        v-bind="CetButton2_export"
        v-on="CetButton2_export.event"
      ></CetButton>

      <div style="width: 400px" class="fr mr10">
        <!-- :onPick="onPick"
        :disabledDate="disabledDate"-->
        <time-range
          :val.sync="queryTime"
          :type-ids="[1, 2, 3, 4, 5]"
        ></time-range>
      </div>
      <CetButton
        class="fr mr10"
        v-bind="CetButton2_harmTimes"
        v-on="CetButton2_harmTimes.event"
      ></CetButton>
    </el-header>

    <div style="height: calc(100% - 40px)">
      <CetChart ref="chartLine" v-bind="CetChart2_FreqSpectrum"></CetChart>
    </div>
    <HarmTimesGroup
      :visible.sync="queryDialogVisble"
      :last-checked.sync="lastChecked"
      @confirm="queryDialogConfirm"
    />
  </div>
</template>
<script>
import common from "eem-utils/common";
import TimeRange from "eem-components/TimeRange";
import HarmTimesGroup from "./HarmTimesGroup";
export default {
  // 频谱分析
  name: "SpectralAnalysis",
  components: {
    HarmTimesGroup,
    TimeRange
  },
  props: {
    currentNode: Object,
    refreshTrigger_in: {
      type: [Number]
    }
  },
  watch: {
    currentNode: {
      deep: true,
      handler: function (val, oldVal) {
        this.queryHarmonicFreqSpectrum();
      }
    },
    refreshTrigger_in: {
      deep: true,
      handler: function (val, oldVal) {
        this.queryHarmonicFreqSpectrum();
      }
    },
    queryTime: {
      deep: true,
      handler: function (val, oldVal) {
        this.queryHarmonicFreqSpectrum();
      }
    }
  },
  data() {
    return {
      // spectrum组件
      ElRadioGroup_spectrum: {
        value: 1,
        style: {
          width: ""
        },
        event: {
          change: this.ElRadioGroup_spectrum_change_out
        }
      },
      // spectrum组件
      ElRadioList_spectrum: {
        options_in: [
          {
            id: 1,
            text: "电压含有率"
          },
          {
            id: 3,
            text: "电流含有率"
          },
          {
            id: 4,
            text: "电流有效值"
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },

      // 电压谐波畸变率
      CetChart2_FreqSpectrum: {
        //组件输入项
        inputData_in: null,
        options: {
          grid: {
            top: 35,
            right: 25,
            bottom: 35,
            left: 50
          },
          legend: {
            x: "right",
            y: "top"
          },
          tooltip: {
            trigger: "axis",
            // formatter(params) {
            //   console.log(params);
            //   let text = '谐波次数：' + this._.get(params,'[0].axisValue') + '<br />'
            //   this._.forEach(params,item=>{
            //     text + item.seriesName + '：'+
            //   })
            //   return
            // },
            axisPointer: {
              type: "shadow"
            }
          },

          xAxis: [{ type: "category" }],
          yAxis: {},

          series: [
            // These series are in the first grid.
            {
              type: "bar",
              seriesLayoutBy: "row",
              name: "A相",
              encode: {
                x: "harmTime",
                y: "1"
              },
              color: "orange"
            },
            {
              type: "bar",
              seriesLayoutBy: "row",
              name: "B相",
              encode: {
                x: "harmTime",
                y: "2"
              },
              color: "green"
            },
            {
              type: "bar",
              seriesLayoutBy: "row",
              name: "C相",
              encode: {
                x: "harmTime",
                y: "3"
              },
              color: "red"
            }
            // These series are in the second grid.
          ]
        }
      },

      // 谐波次数组件
      CetButton2_harmTimes: {
        visible_in: true,
        disable_in: false,
        title: "",
        type: "primary",
        plain: true,
        icon: "el-icon-setting",
        event: {
          statusTrigger_out: this.CetButton2_harmTimes_statusTrigger_out
        }
      },

      // 导出组件
      CetButton2_export: {
        visible_in: true,
        disable_in: false,
        title: "导出",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton2_export_statusTrigger_out
        }
      },

      queryTime: common.initDateRange(),
      minDate: null,
      queryDialogVisble: false,
      // 用于记录自定义谐波次数中确定勾选的选项
      lastChecked: common.fileNumArr(2, 25)
    };
  },
  methods: {
    // 自定义谐波次数
    CetButton2_harmTimes_statusTrigger_out(val) {
      this.queryDialogVisble = true;
    },
    //region 导出
    CetButton2_export_statusTrigger_out(val) {
      // harmType,harmTime,dataType,phaseType
      const data = this.getQueryParams([this.$refs.chartLine.getDataURL()]);
      common.downExcel(
        "/data-center/v1/harmonic/exportHarmonicFreqSpectrum",
        data
      );
    },
    //谐波电压
    ElRadioGroup_spectrum_change_out(val) {
      this.ElRadioGroup_spectrum.value = val;
      this.queryHarmonicFreqSpectrum();
    },

    /*  onPick({ maxDate, minDate }) {
      this.minDate = minDate;
    },
    disabledDate(time) {
      var date1 = this.$moment(this.minDate);
      var date2 = this.$moment(time);
      return date1.diff(date2, "days") > 35;
    }, */
    getQueryParams(pictures = []) {
      return {
        dataTypes: [2],
        // deviceId: this._.get(this, "currentNode.data.measuredby"),
        parentId: this.currentNode.data.id,
        parentLabel: this.currentNode.data.modelLabel,
        meterTypes: [9],
        endTime: this.queryTime[1],
        harmTimes: this.lastChecked,
        harmTypes: [this.ElRadioGroup_spectrum.value],
        interval: 1,
        phaseTypes: [],
        pictures,
        queryParams: [[0]],
        startTime: this.queryTime[0]
      };
    },
    queryHarmonicFreqSpectrum() {
      if (!this._.get(this, "currentNode.data.id")) return;
      const date1 = this.$moment(this.queryTime[0]);
      const date2 = this.$moment(this.queryTime[1]);
      if (date2.diff(date1, "days") > 36) {
        this.$message("查询间隔最多不能超过35天");
        return;
      }
      // /data-center/v1/harmonic/queryHarmonicFreqSpectrum
      const data = this.getQueryParams();
      common.requestData(
        {
          url: "/data-center/v1/harmonic/queryHarmonicFreqSpectrum",
          data
        },
        res => {
          const datas = this.formatChartData(res);
          // console.log(JSON.stringify(datas));
          this.CetChart2_FreqSpectrum.inputData_in = datas;
        }
      );
    },
    /*  groupBy(objectArray, property) {
      return objectArray.reduce(function(acc, obj) {
        var key = obj[property];
        acc.push({
          harmonicOrder: key,
          realData: []
        });
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(obj);
        return acc;
      }, {});
    }, */
    formatChartData(data) {
      const result = new Map();
      for (const item of data) {
        const harmTime = item.harmTime;
        if (!result.get(harmTime)) {
          result.set(harmTime, { harmTime: harmTime });
        }

        result.get(harmTime)[item.phaseType] = this._.round(item.dataValue, 2);
      }
      const temp = [];
      result.forEach(item => temp.push(item));
      return temp;
    },

    queryDialogConfirm() {
      this.queryHarmonicFreqSpectrum();
    }
  }
};
</script>
<style lang="scss" scoped>
.bg-eee {
  background: #eee;
}
</style>
