<template>
  <div class="page eem-common flex-column">
    <el-header height="auto" class="header eem-container mbJ3">
      <div class="text lhHm">
        <div class="goBack" @click="goBack">
          <i class="el-icon-arrow-left"></i>
          {{ $T("返回") }}
        </div>
        <span class="common-title-H2 mlJ1">{{ $T("工单详情") }}</span>
        <div style="float: right" v-show="!isShow_in">
          <CetButton
            class="fr mlJ1"
            v-bind="CetButton_1"
            v-on="CetButton_1.event"
          ></CetButton>
          <CetButton
            class="fr mlJ1"
            v-bind="CetButton_input"
            v-on="CetButton_input.event"
          ></CetButton>
        </div>
      </div>
    </el-header>
    <el-container class="flex-auto">
      <el-aside width="315px" class="eem-cont flex-column">
        <el-header height="auto" class="fs16 p0 mbJ3 common-title-H3">
          {{ $T("基础信息") }}
        </el-header>
        <el-main class="flex-auto p0">
          <div
            v-for="(item, index) in listData"
            :key="index"
            :class="{ clearfix: true, listItem: true }"
          >
            <div class="fl ellipsis" style="width: 120px" :title="item.name">
              {{ item.name }}:
            </div>

            <el-tooltip
              :content="
                filData(orderMsg[item.key], item.type, item.unit, item.keyPath)
              "
              effect="light"
              placement="top"
            >
              <div
                v-show="item.type != 'button'"
                class="fl text-overflow"
                style="width: calc(100% - 120px)"
              >
                {{
                  filData(
                    orderMsg[item.key],
                    item.type,
                    item.unit,
                    item.keyPath
                  )
                }}
              </div>
            </el-tooltip>
            <div v-show="item.type == 'button'" class="fl text-overflow">
              <span
                v-if="orderMsg && orderMsg.sourceid"
                @click="showEventMsg"
                class="clickformore"
              >
                {{
                  filData(
                    orderMsg[item.key],
                    item.type,
                    item.unit,
                    item.keyPath
                  )
                }}
              </span>
              <span v-else>
                {{
                  filData(
                    orderMsg[item.key],
                    item.type,
                    item.unit,
                    item.keyPath
                  )
                }}
              </span>
            </div>
          </div>
          <div class="plJ1 prJ1">
            <div class="contentTitle mbJ3 mtJ1">
              <span>{{ $T("故障详情") }}：</span>
            </div>
            <div class="description-icon">
              <div
                style="line-height: 1.5"
                v-for="(item, index) in faultdescription.split('\n')"
                :key="index"
              >
                {{ item }}
              </div>
            </div>
          </div>
          <div class="plJ1 prJ1">
            <div class="mtJ3 mbJ3">
              <span class="contentTitle lhHm">{{ $T("附件") }}：</span>
              <CetButton
                class="fr"
                v-bind="CetButton_3"
                v-on="CetButton_3.event"
              ></CetButton>
            </div>
            <div style="height: 125px">
              <CetTable
                :data.sync="CetTable_5.data"
                :dynamicInput.sync="CetTable_5.dynamicInput"
                v-bind="CetTable_5"
                v-on="CetTable_5.event"
              >
                <template v-for="(column, index) in Columns_File">
                  <el-table-column
                    v-if="column.custom && column.custom === 'tag'"
                    v-bind="column"
                    :key="index"
                    class-name="font0 hand"
                    label-class-name="font14"
                  >
                    <template slot-scope="scope">
                      <el-tag
                        size="small"
                        class="text-middle font14"
                        :effect="column.tagEffect"
                        :type="
                          column.tagTypeFormatter
                            ? column.tagTypeFormatter(scope.row, scope.column)
                            : 'primary'
                        "
                      >
                        {{
                          column.formatter
                            ? column.formatter(
                                scope.row,
                                scope.column,
                                scope.row[column.prop],
                                scope.$index
                              )
                            : scope.row[column.prop]
                        }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column
                    v-else-if="column.custom && column.custom === 'button'"
                    v-bind="column"
                    :key="index"
                    class-name="font0"
                    label-class-name="font14"
                  >
                    <template slot-scope="scope">
                      <span
                        class="clickformore"
                        @click.stop="
                          column.onButtonClick
                            ? column.onButtonClick(scope.row, scope.$index)
                            : void 0
                        "
                      >
                        {{
                          column.formatter
                            ? column.formatter(
                                scope.row,
                                scope.column,
                                scope.row[column.prop],
                                scope.$index
                              )
                            : column.text
                        }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    v-else
                    v-bind="column"
                    :key="index"
                    class-name="hand"
                  ></el-table-column>
                </template>
                <el-table-column
                  :label="$T('操作')"
                  width="120"
                  headerAlign="center"
                  align="center"
                >
                  <template slot-scope="scope">
                    <span
                      class="clickformore fl mrJ3"
                      :class="{
                        noclick: setClass_out(scope.row, scope.$index)
                      }"
                      @click="handleClick_see_out(scope.row, scope.$index)"
                    >
                      {{ $T("预览") }}
                    </span>
                    <span
                      class="clickformore fl"
                      :class="{
                        noclick: setClass_out(scope.row, scope.$index)
                      }"
                      @click="handleClick_download_out(scope.row, scope.$index)"
                    >
                      {{ $T("下载") }}
                    </span>
                  </template>
                </el-table-column>
              </CetTable>
            </div>
          </div>
        </el-main>
      </el-aside>
      <el-container class="mlJ3" style="height: 100%; padding: 0px">
        <el-header class="common-title-H3 p0 mbJ3" height="auto">
          {{ $T("维修结果") }}
        </el-header>
        <el-main class="flex-auto" style="padding: 0px; overflow-x: hidden">
          <el-row :gutter="$J3" class="eem-cont mbJ3">
            <el-col :span="6">
              <div class="mbJ3">
                <span>{{ $T("维修记录") }}：</span>
              </div>
              <div class="description-icon">
                <div
                  style="line-height: 1.5"
                  v-for="(item, index) in handledescription.split('\n')"
                  :key="index"
                >
                  {{ item }}
                </div>
              </div>
            </el-col>
            <el-col :span="9">
              <div class="mbJ3">
                <span>{{ $T("消耗备件") }}:</span>
              </div>
              <div style="height: 125px">
                <CetTable
                  :data.sync="CetTable_2.data"
                  :dynamicInput.sync="CetTable_2.dynamicInput"
                  v-bind="CetTable_2"
                  v-on="CetTable_2.event"
                >
                  <template v-for="(column, index) in Columns_Part">
                    <el-table-column
                      v-if="column.custom && column.custom === 'tag'"
                      v-bind="column"
                      :key="index"
                      class-name="font0 hand"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <el-tag
                          size="small"
                          class="text-middle font14"
                          :effect="column.tagEffect"
                          :type="
                            column.tagTypeFormatter
                              ? column.tagTypeFormatter(scope.row, scope.column)
                              : 'primary'
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : scope.row[column.prop]
                          }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else-if="column.custom && column.custom === 'button'"
                      v-bind="column"
                      :key="index"
                      class-name="font0"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <span
                          class="clickformore"
                          @click.stop="
                            column.onButtonClick
                              ? column.onButtonClick(scope.row, scope.$index)
                              : void 0
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : column.text
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else-if="column.custom && column.custom === 'text'"
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    >
                      <template slot-scope="scope">
                        <span
                          v-bind:style="
                            column.textTypeFormatter
                              ? column.textTypeFormatter(
                                  scope.row,
                                  scope.column
                                )
                              : ''
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : column.text
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    ></el-table-column>
                  </template>
                </CetTable>
              </div>
            </el-col>
            <el-col :span="9">
              <div class="mbJ3">
                <span>{{ $T("附件") }}：</span>
              </div>
              <div style="height: 125px">
                <CetTable
                  :data.sync="CetTable_4.data"
                  :dynamicInput.sync="CetTable_4.dynamicInput"
                  v-bind="CetTable_4"
                  v-on="CetTable_4.event"
                >
                  <template v-for="(column, index) in Columns_File">
                    <el-table-column
                      v-if="column.custom && column.custom === 'tag'"
                      v-bind="column"
                      :key="index"
                      class-name="font0 hand"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <el-tag
                          size="small"
                          class="text-middle font14"
                          :effect="column.tagEffect"
                          :type="
                            column.tagTypeFormatter
                              ? column.tagTypeFormatter(scope.row, scope.column)
                              : 'primary'
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : scope.row[column.prop]
                          }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else-if="column.custom && column.custom === 'button'"
                      v-bind="column"
                      :key="index"
                      class-name="font0"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <span
                          class="clickformore"
                          @click.stop="
                            column.onButtonClick
                              ? column.onButtonClick(scope.row, scope.$index)
                              : void 0
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : column.text
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    ></el-table-column>
                  </template>
                  <el-table-column
                    :label="$T('操作')"
                    width="120"
                    headerAlign="center"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <span
                        class="clickformore fl mrJ3"
                        :class="{
                          noclick: setClass_out(scope.row, scope.$index)
                        }"
                        @click="handleClick_see_out(scope.row, scope.$index)"
                      >
                        {{ $T("预览") }}
                      </span>
                      <span
                        class="clickformore fl"
                        :class="{
                          noclick: setClass_out(scope.row, scope.$index)
                        }"
                        @click="
                          handleClick_download_out(scope.row, scope.$index)
                        "
                      >
                        {{ $T("下载") }}
                      </span>
                    </template>
                  </el-table-column>
                </CetTable>
              </div>
              <div class="mtJ3">
                <CetButton
                  class="fr"
                  v-bind="CetButton_2"
                  v-on="CetButton_2.event"
                ></CetButton>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="$J3" class="eem-cont mbJ3">
            <el-col :span="24">
              <div class="mbJ3">
                <span class="fs16">{{ $T("专家库") }}</span>
              </div>
            </el-col>
            <el-col :span="24">
              <el-row :gutter="$J3">
                <el-col
                  :span="8"
                  v-for="(item, index) in expertListData"
                  :key="index"
                >
                  <div>
                    <div
                      class="fl ellipsis"
                      :style="{ width: en ? '170px' : '70px' }"
                      :title="item.name"
                    >
                      {{ item.name }}:
                    </div>
                    <el-tooltip
                      :content="
                        filData(
                          orderMsg[item.key],
                          item.type,
                          item.unit,
                          item.keyPath
                        )
                      "
                      effect="light"
                      placement="top"
                    >
                      <div
                        class="fl text-overflow"
                        :style="{
                          width: en ? 'calc(100% - 170px)' : 'calc(100% - 70px)'
                        }"
                      >
                        {{
                          filData(
                            orderMsg[item.key],
                            item.type,
                            item.unit,
                            item.keyPath
                          )
                        }}
                      </div>
                    </el-tooltip>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div>
                    <div class="fl ellipsis" style="width: 70px">
                      {{ $T("使用预案") }}:
                    </div>
                    <el-tooltip
                      :content="eventPlanName"
                      effect="light"
                      placement="top"
                    >
                      <div
                        class="fl text-overflow"
                        style="width: calc(100% - 70px)"
                      >
                        {{ eventPlanName || "--" }}
                      </div>
                    </el-tooltip>
                  </div>
                </el-col>
              </el-row>
              <div class="description-icon mtJ3">
                <span v-html="solution" style="white-space: pre-wrap"></span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="$J3" class="eem-cont">
            <el-col :span="24">
              <div class="mbJ3">
                <span class="fs16">{{ $T("流程状态") }}</span>
                <div style="float: right">
                  <span
                    :class="['btn_item', { active: isTab === 1 }]"
                    @click="handTab_out(1)"
                  >
                    {{ $T("流程表") }}
                  </span>
                  <span class="btnline"></span>
                  <span
                    :class="['btn_item', { active: isTab === 2 }]"
                    @click="handTab_out(2)"
                  >
                    {{ $T("流程图") }}
                  </span>
                </div>
              </div>
              <div style="height: 155px" v-show="isTab === 1">
                <CetTable
                  :data.sync="CetTable_3.data"
                  :dynamicInput.sync="CetTable_3.dynamicInput"
                  v-bind="CetTable_3"
                  v-on="CetTable_3.event"
                >
                  <template v-for="(column, index) in Columns_Process">
                    <el-table-column
                      v-if="column.custom && column.custom === 'tag'"
                      v-bind="column"
                      :key="index"
                      class-name="font0 hand"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <el-tag
                          size="small"
                          class="text-middle font14"
                          :effect="column.tagEffect"
                          :type="
                            column.tagTypeFormatter
                              ? column.tagTypeFormatter(scope.row, scope.column)
                              : 'primary'
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : scope.row[column.prop]
                          }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else-if="column.custom && column.custom === 'button'"
                      v-bind="column"
                      :key="index"
                      class-name="font0"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <span
                          class="clickformore"
                          @click.stop="
                            column.onButtonClick
                              ? column.onButtonClick(scope.row, scope.$index)
                              : void 0
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : column.text
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    ></el-table-column>
                  </template>
                </CetTable>
              </div>
              <div
                style="height: 300px; overflow: auto"
                class="bg1"
                v-show="isTab === 2"
              >
                <img :src="imgSrc" :alt="$T('暂无流程图')" />
              </div>
            </el-col>
          </el-row>
        </el-main>
      </el-container>
    </el-container>
    <toExamine
      :visibleTrigger_in="toExamine.visibleTrigger_in"
      :closeTrigger_in="toExamine.closeTrigger_in"
      :inputData_in="toExamine.inputData_in"
      :codes_in="toExamine.codes_in"
      @confirm_out="toExamine_confirm_out"
    />
    <FlowChart
      :visibleTrigger_in="flowChart.visibleTrigger_in"
      :closeTrigger_in="flowChart.closeTrigger_in"
      :inputData_in="flowChart.inputData_in"
      @confirm_out="flowChart_confirm_out"
    />
    <EventMsg v-bind="EventMsg" v-on="EventMsg.event"></EventMsg>
    <InputOrder
      :visibleTrigger_in="inputOrder.visibleTrigger_in"
      :closeTrigger_in="inputOrder.closeTrigger_in"
      :inputData_in="inputOrder.inputData_in"
      @confirm_out="inputOrder_confirm_out"
    />
  </div>
</template>

<script>
import common from "eem-utils/common";
import toExamine from "./toExamine";
import FlowChart from "./FlowChart";
import EventMsg from "./EventMsg";
import InputOrder from "./InputOrder";
import customApi from "@/api/custom.js";
var FileSaver = require("file-saver");
export default {
  name: "classDetail",
  components: {
    toExamine,
    FlowChart,
    EventMsg,
    InputOrder
  },
  props: {
    inputData_in: {
      type: Object
    },
    isShow_in: {
      type: Boolean
    }
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },
  data() {
    return {
      listData: [
        {
          name: $T("工单号"),
          key: "code",
          type: "string"
        },
        {
          name: $T("开始时间"),
          key: "executetime",
          type: "date"
        },
        {
          name: $T("完成时间"),
          key: "finishtime",
          type: "date"
        },
        {
          name: $T("维修确认时间"),
          key: "repairconfirmtime",
          type: "date"
        },
        {
          name: $T("巡检确认时间"),
          key: "inspectconfirmtime",
          type: "date"
        },
        {
          name: $T("工单来源"),
          key: "sourceTypeName",
          type: "button"
        },
        {
          name: $T("维修目标"),
          key: "deviceplanrelationship_model",
          type: "array"
        },
        {
          name: $T("维修方式"),
          key: "repairTypeName",
          type: "string"
        },
        {
          name: $T("填报方式"),
          key: "fillFormTypeName",
          type: "string"
        },
        {
          name: $T("等级"),
          key: "taskLevelName",
          type: "string"
        },
        {
          name: $T("完成状态"),
          key: "inspectResult",
          type: "string"
        },
        {
          name: $T("预计耗时"),
          key: "timeconsumeplan",
          type: "ms",
          unit: "h"
        },
        {
          name: $T("实际耗时"),
          key: "timeconsumereal",
          type: "ms",
          unit: "h"
        },
        {
          name: $T("责任班组"),
          key: "teamName",
          type: "string"
        },
        {
          name: $T("维修人"),
          key: "repairman",
          type: "string"
        }
      ],
      expertListData: [
        {
          name: $T("设备归类"),
          key: "deviceClassificationName",
          type: "string"
        },
        {
          name: $T("故障类型"),
          key: "eventClassificationName",
          type: "string"
        },
        {
          name: $T("故障"),
          key: "faultScenariosName",
          type: "string"
        }
      ],
      orderMsg: {},
      itemAction: 0,
      CetButton_1: {
        visible_in: false,
        disable_in: false,
        title: $T("审核"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: $T("全部下载"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetButton_3: {
        visible_in: true,
        disable_in: false,
        title: $T("全部下载"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      CetButton_input: {
        visible_in: false,
        disable_in: false,
        title: $T("录入"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_input_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryEventPlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          scenariosId_in: 0,
          limit_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 50
        },
        highlightCurrentRow: false,
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        },
        height: 50,
        style: {
          "text-align": "center"
        },
        showSelection: false
      },
      Columns_Simulation: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 50,
          label: "#",
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "paraName",
          minWidth: 100,
          width: "",
          label: $T("参数名"),
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "value",
          minWidth: 100,
          width: "",
          label: $T("数值"),
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          custom: "text",
          formatter: common.formatTextCol(),
          textTypeFormatter: this.valueStyleFormatter
        },
        {
          type: "",
          prop: "min",
          minWidth: 120,
          width: "",
          label: $T("下限"),
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "max",
          minWidth: 120,
          width: "",
          label: $T("上限"),
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ],
      CetTable_2: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryEventPlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          scenariosId_in: 0,
          limit_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 50
        },
        highlightCurrentRow: false,
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {},
        height: 50,
        style: {
          "text-align": "center"
        },
        showSelection: false
      },
      Columns_Part: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 50,
          label: "#",
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "name",
          minWidth: 100,
          width: "",
          label: $T("备件类型"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "number",
          minWidth: 100,
          width: "",
          label: $T("数量"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          custom: "text",
          showOverflowTooltip: true,
          formatter: this.partFormatter
        }
      ],
      CetTable_3: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryEventPlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          scenariosId_in: 0,
          limit_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 50
        },
        highlightCurrentRow: false,
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        },
        height: 50,
        style: {
          "text-align": "center"
        },
        showSelection: false
      },
      Columns_Process: [
        {
          type: "",
          prop: "nodename",
          minWidth: "",
          width: 100,
          label: $T("节点"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "operator",
          minWidth: 100,
          width: "",
          label: $T("操作人"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "remark",
          minWidth: "",
          width: "",
          label: $T("描述"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "logtime",
          minWidth: "",
          width: "",
          label: $T("日期时间"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatDateCol("YYYY-MM-DD HH:mm:ss")
        },
        {
          type: "",
          prop: "detail",
          minWidth: "",
          width: "",
          label: $T("内容"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ],
      CetTable_4: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryEventPlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          scenariosId_in: 0,
          limit_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 50
        },
        highlightCurrentRow: false,
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        },
        height: 50,
        style: {
          "text-align": "center"
        },
        showSelection: false
      },
      CetTable_5: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryEventPlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          scenariosId_in: 0,
          limit_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 50
        },
        highlightCurrentRow: false,
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        },
        height: 50,
        style: {
          "text-align": "center"
        },
        showSelection: false
      },
      Columns_File: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 50,
          label: "#",
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "name",
          minWidth: "",
          width: "",
          label: $T("名称"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        }
      ],
      toExamine: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        codes_in: []
      },
      flowChart: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      // 事件详情弹窗
      EventMsg: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        event: {}
      },
      inputOrder: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      handledescription: "",
      faultdescription: "",
      solution: "",
      eventPlanName: "",
      isTab: 1,
      imgSrc: null
    };
  },
  watch: {
    inputData_in: {
      handler: function (val, oldVal) {
        if ([1, 2].includes(val.worksheetstatus)) {
          return;
        }
        this.orderMsg = this._.cloneDeep(val);
        this.CetTable_2.data =
          this._.get(
            val,
            "maintenanceContentObj.sparePartsReplaceRecords",
            []
          ) || [];
        this.CetTable_3.data = this._.get(val, "processFlowUnits", []) || [];
        this.CetTable_4.data =
          this._.get(val, "maintenanceContentObj.attachments", []) || [];
        this.CetTable_5.data = this._.get(val, "attachmentList", []) || [];
        this.handledescription = val.handledescription || "--";
        this.faultdescription = val.faultdescription || "--";
        this.eventPlanName = this._.get(val, "eventPlan.name", "");
        this.solution = this._.get(val, "eventPlan.solution", "");
        if (!this.eventPlanName) {
          this.eventPlanName = this._.get(
            val,
            "maintenanceContentObj.eventPlan.eventPlanName",
            ""
          );
          this.solution = this._.get(
            val,
            "maintenanceContentObj.eventPlan.solution",
            ""
          );
        }
        if (val.executetime && val.finishtime) {
          this.orderMsg.timeconsumereal = val.finishtime - val.executetime;
        }
        if (val.maintenanceContentObj) {
          const users =
            this._.get(val, "maintenanceContentObj.users", []) || [];
          let executor = "";
          users.forEach((item, index) => {
            if (index) {
              executor += " , ";
            }
            executor += item.userName;
          });
          this.orderMsg.repairman = executor;
        }
        this.isTab = 1;
        this.imgSrc = null;
        this.getImgUrl(val.code);
        this.checkAuth(val.code);
      },
      deep: true,
      immediate: true
    }
  },

  methods: {
    // 是否具有工单权限信息
    checkAuth(code) {
      if (!code) return;
      customApi.checkWorkorderAuth(code).then(res => {
        if (res.code === 0) {
          if (this.inputData_in.worksheetstatus === 3) {
            this.CetButton_1.visible_in = res.data;
          } else {
            this.CetButton_1.visible_in = false;
          }
          if (
            this.inputData_in.worksheetstatus === 2 ||
            this.inputData_in.worksheetstatus === 4
          ) {
            this.CetButton_input.visible_in = res.data;
          } else {
            this.CetButton_input.visible_in = false;
          }
        }
      });
    },
    toExamine_confirm_out(val) {
      // this.CetTable_1.queryTrigger_in = new Date().getTime();
      this.$emit("goBack", true);
    },
    inputOrder_confirm_out(val) {
      this.$emit("goBack", true);
    },
    flowChart_confirm_out(val) {},
    CetButton_1_statusTrigger_out(val) {
      this.toExamine.inputData_in = this._.cloneDeep(this.inputData_in);
      this.toExamine.codes_in = this._.cloneDeep([this.inputData_in]);
      this.toExamine.visibleTrigger_in = new Date().getTime();
    },
    CetButton_2_statusTrigger_out(val) {
      const imgList = this.CetTable_4.data || [];
      if (imgList.length === 0) {
        this.$message.warning($T("无附件图片"));
        return;
      }
      imgList.forEach(item => {
        var downloadPath = item.url;
        var name = downloadPath.split("/").splice(-1)[0];
        this.downImg(downloadPath, name);
      });
    },
    CetButton_3_statusTrigger_out(val) {
      const imgList = this.CetTable_5.data || [];
      if (imgList.length === 0) {
        this.$message.warning($T("无附件图片"));
        return;
      }
      imgList.forEach(item => {
        var downloadPath = item.url;
        var name = downloadPath.split("/").splice(-1)[0];
        this.downImg(downloadPath, name);
      });
    },
    CetButton_input_statusTrigger_out(val) {
      this.inputOrder.inputData_in = this._.cloneDeep(this.inputData_in);
      this.inputOrder.visibleTrigger_in = new Date().getTime();
    },
    CetTable_1_record_out(val) {},
    CetTable_1_outputData_out(val) {},
    goBack() {
      // this.$router.go(-1);
      this.$emit("goBack", false);
    },
    //过滤数据格式
    filData(val, type, unit = "", keyPath = "") {
      if ([null, undefined, NaN].includes(val)) {
        return "--" + unit;
      } else if (type === "number") {
        return Number(val).toFixed(2) + unit;
      } else if (type === "date") {
        if (keyPath) {
          val = this._.get(val, keyPath, null);
        }
        return val ? this.$moment(val).format("YYYY-MM-DD HH:mm:ss") : "--";
      } else if (type === "ms") {
        let str = "--";
        const format = "hh h mm min";
        if (val || val === 0) {
          const hour = Math.floor(val / 3600000);
          const minute = Math.floor((val - hour * 3600000) / 60000);
          if (
            format.indexOf("hh") !== -1 &&
            format.indexOf("mm") !== -1 &&
            format.indexOf("ss") === -1
          ) {
            str = format.replace(
              /(.*)hh(.*)mm(.*)/,
              "$1" + hour + "$2" + minute + "$3"
            );
          }
        }
        return str;
      } else if (type === "array") {
        const list = val || [];
        let inspectObject = "";
        list.forEach((item, index) => {
          if (index) {
            inspectObject += " , ";
          }
          inspectObject += item.devicename;
        });
        return inspectObject || "--";
      } else if (type === "custom") {
        return this._.get(val, keyPath, "--") || "--";
      } else {
        return val + unit;
      }
    },
    handleClick_see_out(data) {
      if (data && !data.url) {
        this.$message.warning($T("获取不到附件路径"));
        return;
      }
      this.flowChart.inputData_in = this._.cloneDeep(data);
      this.flowChart.visibleTrigger_in = new Date().getTime();
    },
    handleClick_download_out(data) {
      if (data && !data.url) {
        this.$message.warning($T("获取不到附件路径"));
        return;
      }
      var downloadPath = data.url;
      var name = downloadPath.split("/").splice(-1)[0];

      this.downImg(downloadPath, name);
    },
    setClass_out(data) {
      let isOk = true;
      if (data && data.url) {
        isOk = false;
      }
      return isOk;
    },
    //下载图片
    downImg: function (downloadPath, name) {
      if (!downloadPath) {
        return;
      }
      var url =
        "/eem-service/v1/common/downloadFile?path=" +
        encodeURIComponent(downloadPath);
      const params = {};
      common.generateImg(url, params, "GET").then(res => {
        if (res.status === 200 && res.data.type === "application/x-download") {
          //下载附件图片
          FileSaver.saveAs(res.data, name);
        }
      });
    },
    //获取流程图图片
    getImgUrl: function (code) {
      var me = this;
      if (!code) {
        return;
      }
      // 主题色
      const currentTheme = localStorage.getItem("omega_theme");
      const isLightStyle = currentTheme === "light";
      var url = `/eem-service/v1/workorder/repair/workOrder/processDiagram/${code}?isLightStyle=${isLightStyle}`;

      const params = {};
      common.generateImg(url, params, "GET").then(res => {
        if (res.status === 200 && res.data.type === "image/png") {
          //将图片信息放到Img中
          me.imgSrc = window.URL.createObjectURL(res.data);
        }
      });
    },
    handTab_out(val) {
      this.isTab = val;
    },
    //过滤状态量状态
    partFormatter(row, val) {
      if ([null, undefined, NaN].includes(row.number)) {
        return "--";
      } else {
        return row.number + row.unit;
      }
    },
    //过滤状态量样式
    statusStyleFormatter(row) {
      if (row.status) {
        return {};
      } else if ([null, undefined, NaN].includes(row.status)) {
        return {};
      } else {
        return {
          color: "red"
        };
      }
    },
    //过滤模拟量数值样式
    valueStyleFormatter(row) {
      if (row.value && (row.value < row.min || row.value > row.max)) {
        return {
          color: "red"
        };
      } else {
        return {};
      }
    },
    //点击打开事件详情弹框
    showEventMsg() {
      if (!this.orderMsg.sourceid) {
        this.$message.warning($T("暂无关联事件信息"));
        return;
      }
      if (this.orderMsg.sourcetype === 3) {
        if (!this.$checkPermission("inspectorworkorder_browser")) {
          this.$message.warning($T("无巡检工单查看权限"));
        } else {
          this.$emit("goBack", 3);
        }
        return;
      }
      this.EventMsg.inputData_in = this._.cloneDeep(this.inputData_in);
      this.EventMsg.visibleTrigger_in = new Date().getTime();
    }
  },
  created: function () {},
  mounted: function () {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;
}
.header {
  .goBack {
    display: inline-block;
    @include font_color(ZS);
    cursor: pointer;
  }
}

.lhHm {
  @include line_height(Hm);
}
.listItem {
  box-sizing: border-box;
  @include padding(0 J1);
  @include margin_bottom(J1);
  line-height: 1.5;
}
.contentTitle {
  font-weight: bold;
}
.list-delete {
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: inline-block;
  //   background: url("../assets/22_u2309.png") no-repeat center 2px;
  background-size: 20px 20px;
}
.list-tips {
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: inline-block;
  //   background: url("../assets/u2319.png") no-repeat center 4px;
  background-size: 16px 16px;
}
.btnline {
  display: inline-block;
  position: relative;
  height: 14px;
  width: 1px;
  @include background_color(T1);
  top: 2px;
}
.btn_item {
  cursor: pointer;
  @include font_color(T1);
  &.active {
    @include font_color(ZS);
  }
}
.description-icon {
  height: 125px;
  overflow-y: auto;
  padding: 5px 10px;
  line-height: 1.5;
  box-sizing: border-box;
  width: 100%;
  @include background_color(BG1);
  background-image: none;
  border: 1px solid;
  @include border_color(B1);
  @include border_radius(C);
  word-break: break-all;
}
.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.clickformore {
  cursor: pointer;
  @include font_color(ZS);
}
.noclick {
  cursor: not-allowed;
  @include font_color(T6);
}
</style>
