<template>
  <div class="page">
    <el-container class="padding0 fullheight flex-column mtJ3">
      <div class="minWH" style="flex: 1; overflow: auto">
        <div class="eem-container">
          <div class="mbJ3 lh32">
            <el-tooltip :content="reportTitle" effect="light">
              <span class="common-title-H1 title text-ellipsis">
                {{ reportTitle }}
              </span>
            </el-tooltip>
            <div class="fr clearfix">
              <!-- 向前查询按钮 -->
              <CetButton
                class="fl custom—square"
                v-bind="CetButton_prv"
                v-on="CetButton_prv.event"
              ></CetButton>
              <CustomElDatePicker
                :prefix_in="$T('选择月份')"
                class="fl mlJ mrJ"
                v-bind="CetDatePicker_1.config"
                v-model="CetDatePicker_1.val"
              />
              <!-- <div class="basic-box fl ml-5 mr-5">
                <div class="basic-box-label">月份</div>
                <el-date-picker
                  v-model="CetDatePicker_1.val"
                  v-bind="CetDatePicker_1.config"
                ></el-date-picker>
              </div> -->
              <!-- 向后查询按钮 -->
              <CetButton
                class="fl custom—square"
                v-bind="CetButton_next"
                v-on="CetButton_next.event"
              ></CetButton>
            </div>
          </div>
          <div class="chartsBox">
            <CetChart
              :inputData_in="CetChart_1.inputData_in"
              v-bind="CetChart_1.config"
            />
          </div>
        </div>
        <div class="mtJ3 eem-container">
          <div class="clearfix mbJ3">
            <div class="fl" style="color: #e51b1d; font-size: 18px">
              {{ $T("超出申报值") }}{{ rate1 }}%{{ $T("的进线") }}
              <div class="tag" style="background: #e51b1d">
                {{ CetTable_1.data.length }}{{ $T("条") }}
              </div>
            </div>
            <el-tooltip
              effect="light"
              :content="
                `${$T('偏差费用')}` +
                `=（${$T('实际最大需量')}` +
                `-${$T('申报需量')}` +
                `*${$T('上限比例')}` +
                `）*${$T('需量电价')}` +
                `*${$T('惩罚系数')}` +
                `，${$T('不计算负偏差时')}` +
                `，${$T('偏差费用为0。')}`
              "
            >
              <i class="el-icon-question fl mtJ1 mlJ1 fsH3"></i>
            </el-tooltip>
            <CetButton
              class="fr"
              v-bind="CetButton_1"
              v-on="CetButton_1.event"
            ></CetButton>
            <CetButton
              class="fr mrJ1"
              v-bind="CetButton_2"
              v-on="CetButton_2.event"
            ></CetButton>
          </div>
          <div class="" style="height: 300px">
            <CetTable
              style="height: 100%"
              :data.sync="CetTable_1.data"
              :dynamicInput.sync="CetTable_1.dynamicInput"
              v-bind="CetTable_1"
              v-on="CetTable_1.event"
            >
              <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
              <ElTableColumn v-bind="ElTableColumn_accountName"></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_declareDemand"
              ></ElTableColumn>
              <ElTableColumn v-bind="ElTableColumn_maxDemand"></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_maxMonthDeclareDemand"
              ></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_overDurationText"
              ></ElTableColumn>
              <ElTableColumn v-bind="ElTableColumn_overTimes"></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_deviationCost"
              ></ElTableColumn>
            </CetTable>
          </div>
          <p class="tooltipInfo">
            {{
              $T(
                "对超需量持续时间较长的进线，建议调高申报需量值，减少偏差费用。"
              )
            }}
          </p>
          <p class="tooltipInfo" style="margin-bottom: 0">
            {{
              $T(
                "对超需量持续时间较短的进线，可查明超需量原因，降低实际最大需量。"
              )
            }}
          </p>
        </div>
        <div class="eem-container mtJ3">
          <div class="clearfix mbJ3">
            <div
              class="fl"
              style="color: rgba(254, 175, 20, 1); font-size: 18px"
            >
              {{ $T("低于申报值") }}{{ rate3 }}%{{ $T("的进线") }}
              <div class="tag" style="background: rgba(254, 175, 20, 1)">
                {{ CetTable_2.data.length }}{{ $T("条") }}
              </div>
            </div>
            <CetButton
              class="fr"
              v-bind="CetButton_3"
              v-on="CetButton_3.event"
            ></CetButton>
          </div>
          <div class="" style="height: 300px">
            <CetTable
              style="height: 100%"
              :data.sync="CetTable_2.data"
              :dynamicInput.sync="CetTable_2.dynamicInput"
              v-bind="CetTable_2"
              v-on="CetTable_2.event"
            >
              <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
              <ElTableColumn v-bind="ElTableColumn_accountName"></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_declareDemand"
              ></ElTableColumn>
              <ElTableColumn v-bind="ElTableColumn_maxDemand"></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_maxMonthDeclareDemand"
              ></ElTableColumn>
            </CetTable>
          </div>
          <p class="tooltipInfo" style="margin-bottom: 0">
            {{ $T("对申报需量值偏大的进线，可调低申报需量值，节省基本电费。") }}
          </p>
        </div>
      </div>
    </el-container>
    <BeyondInfo
      :visibleTrigger_in="BeyondInfo.visibleTrigger_in"
      :closeTrigger_in="BeyondInfo.closeTrigger_in"
      :queryId_in="BeyondInfo.queryId_in"
      :inputData_in="BeyondInfo.inputData_in"
      @finishTrigger_out="BeyondInfo_finishTrigger_out"
      @finishData_out="BeyondInfo_finishData_out"
      @saveData_out="BeyondInfo_saveData_out"
      @currentData_out="BeyondInfo_currentData_out"
      :date="CetDatePicker_1.val"
    />
  </div>
</template>
<script>
import common from "eem-utils/common";
import BeyondInfo from "./BeyondInfo.vue";
import { httping } from "@omega/http";
export default {
  name: "DemandReportMonth",
  components: {
    BeyondInfo
  },
  props: {
    currentNode: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    reportTitle() {
      return `${this.$moment(this.CetDatePicker_1.val).format(
        $T("YYYY年MM月")
      )}${$T("需量报告")}`;
    }
  },

  data() {
    const language = window.localStorage.getItem("omega_language") === "en";
    return {
      rate1: "--", //等级一的值
      rate3: "--", //等级三的值
      currentTabelItem: null,
      currentTabelItem2: null,
      CetDatePicker_1: {
        disable_in: false,
        val: this.$moment().add(0, "d").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "month",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          clearable: false,
          size: "small",
          style: {
            display: "inline-block",
            width: "200px"
          },
          pickerOptions: {
            disabledDate(time) {
              return time.getTime() > Date.now();
            }
          }
        }
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: true,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      CetChart_1: {
        inputData_in: {},
        config: {
          options: {
            tooltip: {
              trigger: "item",
              formatter: function (val) {
                var rete = val.percent || "--";
                if (
                  (val.data.rete || val.data.rete === 0) &&
                  val.data.rete != "NaN"
                ) {
                  rete = val.data.rete;
                }
                if (val.name) {
                  return `${val.name}<br /> ${$T("进线数")}：${Number(
                    val.value
                  ).toFixed2()} <br /> ${$T("占比")}：${rete}%`;
                }
              }
            },
            legend: {
              show: false
            },
            series: [
              {
                name: "",
                type: "pie",
                radius: [0, "40%"],
                label: {
                  fontSize: 14,
                  position: "inner"
                },
                labelLine: {
                  show: false
                },
                data: [
                  {
                    value: 679,
                    name: $T("需量"),
                    itemStyle: {
                      color: "#1695F8"
                    },
                    label: {
                      textBorderColor: "transparent"
                    }
                  },
                  {
                    value: 100,
                    name: $T("容量"),
                    itemStyle: {
                      color: "#aaaaaa"
                    },
                    label: {
                      textBorderColor: "transparent"
                    }
                  }
                ]
              },
              {
                name: "",
                type: "pie",
                radius: ["50%", "60%"],
                top: 30,
                bottom: 30,
                label: {
                  formatter: function (val) {
                    var rete = val.percent || "--";
                    if (
                      (val.data.rete || val.data.rete === 0) &&
                      val.data.rete != "NaN" &&
                      val.data.rete != undefined
                    ) {
                      rete = val.data.rete;
                    }
                    if (val.name) {
                      return `{b|${val.name}}\n {c|${$T(
                        "进线数"
                      )}：}{d|${Number(val.value).toFixed2()}${$T(
                        "条"
                      )}} \n {c|${$T("占比")}：}{d|${rete}%}`;
                    }
                  },
                  // formatter: "{b|{b}}\n {c|进线数：}{d|{c}条} \n {c|占比：}{d|{d}%}",
                  backgroundColor: "#1C2850",
                  borderColor: "#666666",
                  borderWidth: 0,
                  borderRadius: 4,
                  padding: [5, 5],
                  rich: {
                    b: {
                      fontSize: 14,
                      lineHeight: 24,
                      color: "#fff",
                      align: "center"
                    },
                    c: {
                      fontSize: 12,
                      lineHeight: 16,
                      color: "#fff",
                      align: "center"
                    },
                    d: {
                      color: "#fff",
                      align: "center"
                    }
                  }
                },
                data: [
                  {
                    value: 0,
                    name: $T("需量超出申报值") + "--%",
                    itemStyle: {
                      color: "#E61E1F"
                    },
                    rete: 0
                  },
                  {
                    value: 0,
                    name: $T("需量低于申报值") + "--%",
                    itemStyle: {
                      color: "#FEAF14"
                    },
                    rete: 0
                  },
                  {
                    value: 0,
                    name: $T("申报合理"),
                    itemStyle: {
                      color: "red"
                    },
                    rete: 0
                  },
                  {
                    value: 0,
                    name: "",
                    itemStyle: {
                      color: "transparent"
                    }
                  }
                ]
              }
            ]
          }
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        // defaultSort: null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      // 设置组件唯一识别字段组件
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: "#", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "50" //绝对宽度
      },
      ElTableColumn_accountName: {
        prop: "accountName", // 支持path a[0].b
        label: $T("进线名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "190" : "120" //该宽度会自适应
      },
      // declareDemand组件
      ElTableColumn_declareDemand: {
        prop: "declareDemand", // 支持path a[0].b
        label: $T("申报需量值") + "（kW）", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "290" : "150" //该宽度会自适应
      },
      // maxDemand组件
      ElTableColumn_maxDemand: {
        prop: "maxDemand", // 支持path a[0].b
        label: $T("月最大需量") + "（kW）", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "270" : "150" //该宽度会自适应
      },
      // maxMonthDeclareDemand组件
      ElTableColumn_maxMonthDeclareDemand: {
        prop: "maxMonthDeclareDemand", // 支持path a[0].b
        label: $T("月最大需量") + "/" + $T("申报值") + "（%）", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "380" : "150" //该宽度会自适应
      },
      // overDurationText组件
      ElTableColumn_overDurationText: {
        prop: "overDurationText", // 支持path a[0].b
        label: $T("超出持续总时长"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "180" : "150" //该宽度会自适应
      },
      // overTimes组件
      ElTableColumn_overTimes: {
        prop: "overTimes", // 支持path a[0].b
        label: $T("超出次数（次）"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "225" : "120" //该宽度会自适应
      },
      // deviationCost组件
      ElTableColumn_deviationCost: {
        prop: "deviationCost", // 支持path a[0].b
        label: $T("偏差费用（元）"), //列名
        headerAlign: "right",
        align: "right",
        showOverflowTooltip: true,
        minWidth: language ? "180" : "120" //该宽度会自适应
      },
      CetTable_2: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //  defaultSort:null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_2_record_out,
          outputData_out: this.CetTable_2_outputData_out
        }
      },
      CetButton_1: {
        visible_in: true,
        disable_in: true,
        title: $T("查看超出明细"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: true,
        title: $T("进线详情"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetButton_3: {
        visible_in: true,
        disable_in: true,
        title: $T("进线详情"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      BeyondInfo: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      }
    };
  },
  watch: {
    "CetDatePicker_1.val": function (val) {
      if (
        this.$moment(val).startOf("month").valueOf() >=
        this.$moment().startOf("month").valueOf()
      ) {
        this.CetButton_next.disable_in = true;
      } else {
        this.CetButton_next.disable_in = false;
      }
      this.getData();
    },
    currentNode: {
      handler: function (val) {
        if (val) {
          this.getData();
          this.getProjectDetail();
        }
      },
      deep: true
    }
  },

  methods: {
    // 获取需量预警方案
    getProjectDetail() {
      this.rate1 = "--";
      this.rate3 = "--";
      httping({
        url:
          "/eem-service/v1/alarm/getSchemes/" +
          this.projectId +
          "/project/3/-1/0",
        method: "GET"
      }).then(response => {
        if (response.code == 0 && response.data) {
          this.getSchemeLevel(
            response.data[response.data.length - 1].id,
            response.data[response.data.length - 1].modelLabel
          );
        }
      });
    },
    // 获取预警等级设置
    getSchemeLevel(id, modelLabel) {
      var me = this;
      httping({
        url: "/eem-service/v1/alarm/getSchemeLevel/" + id + "/" + modelLabel,
        method: "GET"
      }).then(response => {
        if (response.code == 0 && response.data && response.data.length > 0) {
          response.data.forEach(item => {
            let nameSeries = this._.cloneDeep(
              this.CetChart_1.config.options.series
            );
            if (item.alarmColor == 1) {
              nameSeries[1].data[0].name = `${$T("需量超出申报值")}${
                item.rate
              }%`;
              this.rate1 = item.rate;
            } else if (item.alarmColor == 3) {
              nameSeries[1].data[1].name = `${$T("低于申报值")}${item.rate}%`;
              this.rate3 = item.rate;
            }
            this.CetChart_1.config.options.series =
              this._.cloneDeep(nameSeries);
          });
        }
      });
    },
    // 获取月报信息
    getData() {
      if (!this.currentNode || this.ajaxFlag) {
        return;
      }
      this.ajaxFlag = true;
      var data = {
        startTime: this.$moment(this.CetDatePicker_1.val)
          .startOf("month")
          .valueOf(),
        endTime: this.$moment(this.CetDatePicker_1.val)
          .add(1, "month")
          .startOf("month")
          .valueOf(),
        node: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          name: this.currentNode.name
        },
        projectId: this.projectId
      };
      let mySeries = this._.cloneDeep(this.CetChart_1.config.options.series);
      mySeries[0].data[0].value = 0;
      mySeries[0].data[1].value = 0;
      // 需量合理性分布统计
      mySeries[1].data[0].value = 0;
      mySeries[1].data[1].value = 0;
      mySeries[1].data[2].value = 0;
      mySeries[1].data[3].value = 0;
      this.CetChart_1.config.options.series = this._.cloneDeep(mySeries);
      // 列表数据
      this.CetTable_1.data = [];
      this.CetTable_2.data = [{}];
      httping({
        url: `/eem-service/v1/demand/manage/monthReport`,
        method: "POST",
        data
      }).then(response => {
        this.ajaxFlag = false;
        if (response.code == 0) {
          let customSeries = this._.cloneDeep(
            this.CetChart_1.config.options.series
          );
          // 需量和容量数量分布
          if (response.data.basicFeeTypeCount) {
            customSeries[0].data[0].value =
              response.data.basicFeeTypeCount.demand &&
              response.data.basicFeeTypeCount.demand.toFixed(2);
            customSeries[0].data[0].name = $T("需量");
            customSeries[0].data[1].value =
              response.data.basicFeeTypeCount.volume &&
              response.data.basicFeeTypeCount.volume.toFixed(2);
            customSeries[0].data[1].name = $T("容量");
            if (
              customSeries[0].data[0].value &&
              !customSeries[0].data[1].value
            ) {
              customSeries[0].data[1].name = "";
            } else if (
              !customSeries[0].data[0].value &&
              customSeries[0].data[1].value
            ) {
              customSeries[0].data[0].name = "";
            }
          }
          // 需量合理性分布统计
          if (response.data.demandProperCountVo) {
            customSeries[1].data[0].value =
              response.data.demandProperCountVo.highLevel &&
              response.data.demandProperCountVo.highLevel.toFixed(0);
            customSeries[1].data[0].rete = (
              (Number(customSeries[1].data[0].value) /
                Number(customSeries[0].data[0].value)) *
              100
            ).toFixed2(2);
            customSeries[1].data[1].value =
              response.data.demandProperCountVo.lowLevel &&
              response.data.demandProperCountVo.lowLevel.toFixed(0);
            customSeries[1].data[1].rete = (
              (Number(customSeries[1].data[1].value) /
                Number(customSeries[0].data[0].value)) *
              100
            ).toFixed2(2);

            customSeries[1].data[2].value =
              response.data.demandProperCountVo.normal &&
              response.data.demandProperCountVo.normal.toFixed(2);
            customSeries[1].data[2].rete = (
              (Number(customSeries[1].data[2].value) /
                Number(customSeries[0].data[0].value)) *
              100
            ).toFixed2(2);
            // 空白量
            customSeries[1].data[3].value = (
              response.data.basicFeeTypeCount.demand +
              response.data.basicFeeTypeCount.volume -
              response.data.demandProperCountVo.highLevel -
              response.data.demandProperCountVo.lowLevel -
              response.data.demandProperCountVo.normal
            ).toFixed2(2);
          }
          this.CetChart_1.config.options.series =
            this._.cloneDeep(customSeries);
          // 列表数据
          this.CetTable_1.data = response.data.highLevelList || [];
          this.CetTable_2.data = response.data.lowLevelList || [];
          // 处理数据显示
          this.CetTable_1.data.forEach(item => {
            item.maxDemand = item.maxDemand && item.maxDemand.toFixed(2);
            item.declareDemand =
              item.declareDemand && item.declareDemand.toFixed(2);
            item.deviationCost =
              item.deviationCost && item.deviationCost.toFixed(2);
            item.maxMonthDeclareDemand =
              item.maxMonthDeclareDemand &&
              (item.maxMonthDeclareDemand * 100).toFixed(2);
            item.overTimes = item.overTimes && item.overTimes.toFixed(0);
            item.overDurationText =
              item.overDuration &&
              common.secondsFormat(
                item.overDuration,
                "hh" + $T("小时") + "mm" + $T("分钟")
              );
          });
          this.CetTable_2.data.forEach(item => {
            item.maxDemand = item.maxDemand && item.maxDemand.toFixed(2);
            item.declareDemand =
              item.declareDemand && item.declareDemand.toFixed(2);
            item.maxMonthDeclareDemand =
              item.maxMonthDeclareDemand &&
              (item.maxMonthDeclareDemand * 100).toFixed(2);
          });
        }
      });
    },
    // 查看超出明细
    CetButton_1_statusTrigger_out(val) {
      this.BeyondInfo.queryId_in = this.currentTabelItem.accountId;
      this.BeyondInfo.visibleTrigger_in = this._.cloneDeep(val);
    },
    CetButton_2_statusTrigger_out(val) {
      this.$router.push({
        name: "demandmonitor",
        params: this.currentTabelItem
      });
    },
    CetButton_3_statusTrigger_out(val) {
      this.$router.push({
        name: "demandmonitor",
        params: this.currentTabelItem2
      });
    },
    CetTable_1_outputData_out(val) {},
    CetTable_1_record_out(val) {
      this.currentTabelItem = val;
      if (val.id != -1) {
        this.CetButton_1.disable_in = false;
        this.CetButton_2.disable_in = false;
      } else {
        this.CetButton_1.disable_in = true;
        this.CetButton_2.disable_in = true;
      }
    },
    CetTable_2_outputData_out(val) {},
    CetTable_2_record_out(val) {
      this.currentTabelItem2 = val;
      if (val.id != -1) {
        this.CetButton_3.disable_in = false;
      } else {
        this.CetButton_3.disable_in = true;
      }
    },
    BeyondInfo_currentData_out(val) {},
    BeyondInfo_finishData_out(val) {},
    BeyondInfo_finishTrigger_out(val) {},
    BeyondInfo_saveData_out(val) {},
    CetButton_prv_statusTrigger_out(val) {
      const date = this.$moment(this.CetDatePicker_1.val);
      this.CetDatePicker_1.val = date.subtract(1, "month").valueOf();
    },
    CetButton_next_statusTrigger_out(val) {
      const date = this.$moment(this.CetDatePicker_1.val);
      if (
        date.startOf("month").valueOf() !=
        this.$moment().startOf("month").valueOf()
      ) {
        this.CetDatePicker_1.val = date.add(1, "month").valueOf();
      }
    }
  },
  created: function () {},
  mounted: function () {
    // this.getProjectDetail();
    this.getData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: calc(100% - 16px) !important;
  position: relative;
}
.chartsBox {
  height: 350px;
}
.tag {
  display: inline-block;
  text-align: center;
  line-height: 30px;
  border-width: 0px;
  width: 70px;
  height: 30px;
  background: inherit;
  border: none;
  border-radius: 5px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 14px;
  color: #ffffff;
}
.tooltipInfo {
  font-size: 13px;
  line-height: 16px;
}
.circle {
  display: inline-block;
  width: 16px;
  height: 16px;
  font-size: 14px;
  line-height: 16px;
  border-radius: 50%;
  text-align: center;
  @include background_color(ZS);
  color: #fff;
}
</style>
