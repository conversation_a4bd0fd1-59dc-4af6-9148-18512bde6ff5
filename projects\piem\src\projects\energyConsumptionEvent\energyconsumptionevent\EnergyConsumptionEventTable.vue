﻿<template>
  <div class="page flex-column">
    <div style="flex: 1; width: 100%" class="minWH">
      <el-table
        ref="PQEventTable"
        class="PQEvent-table"
        :data="eventTableData"
        tooltip-effect="light"
        border
        highlight-current-row
        height="true"
        style="height: 100%; width: 100%"
        row-key="tree_id"
        :default-expand-all="false"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        @current-change="handlerCurrentChange"
        @selection-change="handleSelectionChange"
        @expand-change="handleExpandChange"
        :row-class-name="tableRowClassName"
      >
        <el-table-column
          type="selection"
          width="40"
          align="left"
          reserve-selection
          :selectable="canConfirm"
        ></el-table-column>
        <el-table-column
          :label="$T('序号')"
          type="index"
          :width="language ? 100 : 65"
          :index="table_index"
          align="left"
        ></el-table-column>

        <!-- 根据模型数据绑定表格列 -->
        <template v-for="item in renderColumns">
          <template v-if="item.formatterType">
            <el-table-column
              :key="item.key"
              :show-overflow-tooltip="true"
              :label="item.title"
              :min-width="item.width"
              :width="item.fixedwidth"
              :prop="item.key"
              :sortable="item.sortable"
              header-align="left"
              align="left"
            >
              <template v-if="item.formatterType" slot-scope="scope">
                <span v-if="item.displayMode == 'icon'">
                  <i
                    :class="item.formatterType(item.key)"
                    @click="handleIcon(scope.$index, scope.row)"
                  ></i>
                </span>
                <span
                  v-else-if="item.displayMode == 'custom'"
                  :style="item.formatterType(scope)"
                >
                  {{
                    formatTable(scope.row, scope.column, scope.row[item.key])
                  }}
                  <i v-if="item.iconClass" :class="item.iconClass(scope)"></i>
                </span>
                <span
                  v-else-if="item.displayMode == 'fontColor'"
                  :style="{ color: item.formatterType(scope.row) }"
                >
                  {{
                    formatTable(scope.row, scope.column, scope.row[item.key])
                  }}
                </span>
                <el-tag
                  v-else-if="item.key == 'level$text'"
                  :color="item.formatterType(scope.row).bgColor"
                  :style="`color: ${item.formatterType(scope.row).color}`"
                >
                  {{
                    formatTable(scope.row, scope.column, scope.row[item.key])
                  }}
                </el-tag>
                <el-tag
                  v-else
                  :type="item.formatterType(scope.row)"
                  effect="plain"
                  size="small"
                  disable-transitions
                >
                  {{
                    formatTable(scope.row, scope.column, scope.row[item.key])
                  }}
                </el-tag>
              </template>
            </el-table-column>
          </template>
          <template v-else-if="item.type">
            <el-table-column
              :key="item.key"
              :show-overflow-tooltip="true"
              :label="item.title"
              :min-width="item.width"
              :width="item.fixedwidth"
              :prop="item.key"
              :sortable="item.sortable"
              header-align="left"
              align="left"
            >
              <template v-if="item.type" slot-scope="scope">
                <span v-if="item.type == 'date'">
                  {{ formatDate(scope.row, scope.column, scope.row[item.key]) }}
                </span>
                <span v-else>
                  {{
                    formatTable(scope.row, scope.column, scope.row[item.key])
                  }}
                </span>
              </template>
            </el-table-column>
          </template>
          <template v-else-if="!item.hidden">
            <el-table-column
              :key="item.key"
              :show-overflow-tooltip="true"
              :label="item.title"
              :min-width="item.width"
              :width="item.fixedwidth"
              :prop="item.key"
              :sortable="item.sortable"
              :formatter="formatTable"
              header-align="left"
              align="left"
            ></el-table-column>
          </template>
        </template>
        <el-table-column
          :label="$T('操作')"
          :width="language ? 120 : 80"
          headerAlign="left"
          align="left"
          fixed="right"
        >
          <template slot-scope="scope">
            <span
              class="eem-row-handle"
              @click.stop="handleDetail(scope.$index, scope.row)"
            >
              {{ $T("事件详情") }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <slot />
    <div
      style="flex: 1; width: 100%"
      class="minWH mtJ1 flex-column"
      v-if="showChart"
    >
      <div style="height: 24px; position: relative">
        <span class="icon-bottom-hidden" @click="hiddenChart()"></span>
      </div>
      <headerSpot class="mtJ3 mbJ3">
        <span class="analysis"></span>
        {{ analysisTitle }}
      </headerSpot>
      <div class="flex-auto">
        <CetChart
          :inputData_in="CetChart_1.inputData_in"
          v-bind="CetChart_1.config"
        />
      </div>
    </div>
  </div>
</template>
<script>
import common from "eem-utils/common";
import moment from "moment";
import * as echarts from "echarts";
import { httping } from "@omega/http";
import { getEventTypeColor, getEventGradeColor } from "eem-utils/eventColor.js";
export default {
  name: "EnergyConsumptionEventTable",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    analysisTitle() {
      if (this.$route.name == "energyConsumptionEvent") {
        return $T("报警分析");
      } else if (this.$route.name == "energyEfficiencyEvent") {
        return $T("告警分析");
      }
      return "";
    },
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },
  props: {
    //导出操作触发
    exportTrigger_in: {
      type: Number
    },
    //查询节点输入
    queryNode_in: {
      type: Object
    },
    clickNode_in: {
      type: Object
    },
    convergence_in: {
      type: Boolean
    },
    confirmStatus_in: {
      type: Boolean
    },
    refreshTrigger_in: {
      type: Number
    },
    searchKeyWords_in: {
      type: [String]
    },
    dateQuery: Array,
    updateEventData_in: {
      type: Array,
      default: () => []
    },
    page_in: {
      type: Object
    },
    energyConsumption: Array,
    energyEfficiecy: Array,
    energyAll: Array
  },

  data() {
    return {
      currentRow: null,
      eventTableData: [],
      queryData11: {
        confirmStatus: 0,
        endtime: moment().endOf("d").valueOf() + 1,
        eventClasses: null,
        eventTypes: null,
        keyWord: "",
        meterTypes: [9],
        nodes: null,
        offset: 250,
        page: {
          index: 0,
          limit: 50
        },
        starttime: moment().startOf("d").valueOf(),
        toleranceband: null,
        transientfaultdirection: null
      },
      queryData: {
        checkbox: [1, 2, 3], //收敛事件
        children: [
          {
            modelLabel: "",
            nodeId: 0,
            name: ""
          }
        ], //查询收敛事件
        cycle: 20, //年17 月14 日12 小时7
        description: "",
        endtime: null,
        id: 0,
        name: "",
        index: 0,
        limit: 100,
        modelLabel: "project",
        starttime: null,
        status: 1, //事件状态
        types: [], //事件类型
        levels: [] //事件等级
      },
      pageSize: 50,
      loading: false,
      isLastPage: false,
      tableData: [],
      renderColumns: [
        {
          title: $T("节点层级"),
          key: "levelName",
          width: 100
        },
        {
          title: $T("能源类型"),
          key: "energyTypeName",
          width: 120,
          hidden: false
        },
        {
          title: $T("指标类型"),
          key: "efSetName",
          width: 140,
          hidden: false
        },
        // {
        //   title: "是否收敛",
        //   key: "first4",
        //   width: 100
        // },
        // {
        //   title: "报警类型",
        //   key: "eventtype$text",
        //   width: 100
        // },
        {
          title: $T("报警类型"),
          key: "cycle$text",
          width: 100
        },
        {
          title: $T("描述"),
          key: "description",
          width: 200
        },
        {
          title: $T("事件等级"),
          key: "level$text",
          width: 100,
          formatterType: this.isLevel
        },
        {
          title: $T("分析"),
          key: "first9",
          width: 100,
          displayMode: "icon",
          formatterType: this.isAnalysis
        },
        // {
        //   title: "周期",
        //   key: "cycleName",
        //   width: 100
        // },
        {
          title: $T("发生时间"),
          key: "eventtime",
          formatStr: "YYYY-MM-DD HH:mm:ss.SSS",
          type: "date",
          width: 200
        },
        {
          title: $T("结束时间"),
          key: "endTime",
          formatStr: "YYYY-MM-DD HH:mm:ss.SSS",
          type: "date",
          width: 200
        },
        {
          title: $T("持续时长"),
          key: "durationTime",
          width: 100
        },
        {
          title: $T("状态"),
          key: "confirmeventstatus$text",
          width: 100,
          // displayMode: "fontColor",
          formatterType: this.isState
        }
      ],
      CetChart_1: {
        inputData_in: null,
        config: {
          options: {
            // color: ["#3398DB", "#AA4643", "#89A54E"],
            tooltip: {
              trigger: "axis",
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
              }
            },
            grid: {
              top: "12%",
              left: "3%",
              right: "4%",
              bottom: "3%",
              containLabel: true
            },
            legend: {
              data: [$T("预测值"), $T("实际值"), $T("阈值")]
            },
            dataset: {
              source: []
            },
            xAxis: { type: "category" },
            yAxis: {},
            series: [
              {
                name: $T("预测值"),
                type: "line",
                smooth: true,
                barWidth: "60%",
                encode: { x: "product", y: "yAxis1" }
              },
              {
                name: $T("实际值"),
                type: "line",
                smooth: true,
                encode: { x: "product", y: "yAxis2" }
              },
              {
                name: $T("阈值"),
                type: "line",
                smooth: true,
                encode: { x: "product", y: "yAxis3" }
              }
            ]
          }
        }
      },
      showChart: false,
      nodeName: "",
      isConvergence: false,
      eventtypeList: [],
      energytypeList: [],
      isLoading: false
    };
  },
  watch: {
    page_in: {
      deep: true,
      handler: function (val) {
        this.clickPageChange_out(val);
      }
    },
    exportTrigger_in(val) {
      this.exportTable_out();
    },
    dateQuery: {
      deep: true,
      handler: function (val) {
        this.queryData.endtime = val[1];
        this.queryData.starttime = val[0];
      }
    },
    queryNode_in: {
      deep: true,
      handler: function (val) {
        this.paramsChange_out(val);
      }
    },
    clickNode_in: {
      deep: true,
      handler: function (val) {
        if (this.page_in) {
          this.queryData.index =
            (this.page_in.currentPage - 1) * this.page_in.pageSize;
          this.queryData.limit = this.page_in.pageSize;
        }
        this.clickNodeChange_out(val);
      }
    },
    confirmStatus_in(val) {
      if (!val) {
        this.queryData.status = 3;
      } else {
        this.queryData.status = 1;
      }
    },
    convergence_in(val) {
      if (!val) {
        this.isConvergence = false;
      } else {
        this.isConvergence = true;
      }
    },
    refreshTrigger_in(val) {
      this.queryEvent_out();
    },
    searchKeyWords_in(val) {
      this.queryData.description = val;
    },
    updateEventData_in(list) {
      if (!(list && list.length)) {
        return;
      }

      let me = this;
      for (let eventItem of list) {
        let i = me._.findIndex(me.eventTableData, function (o) {
          return o.id === eventItem.id;
        });

        if (i !== -1) {
          me.eventTableData[i].remark = eventItem.remark;
          me.eventTableData[i].confirmeventstatus =
            eventItem.confirmeventstatus;
          me.eventTableData[i].operator = eventItem.operator;
          me.eventTableData[i].updatetime = eventItem.updatetime;
          me.$refs["PQEventTable"].toggleRowSelection(
            me.eventTableData[i],
            false
          );
        }
      }
      me.eventTableData.splice(0, 0);
    },
    $route(to, from) {
      if (
        to.name == "energyConsumptionEvent" ||
        to.name == "energyEfficiencyEvent"
      ) {
        if (
          from.name == "energyConsumptionEvent" ||
          from.name == "energyEfficiencyEvent"
        ) {
          this.judgeRoute();
          this.loadEnumrations_out();
        }
      }
    }
  },

  methods: {
    // 判断当前页面
    judgeRoute() {
      if (this.$route.name == "energyConsumptionEvent") {
        // 能耗超标需要展示能源类型
        this.renderColumns.find(
          item => item.key == "energyTypeName"
        ).hidden = false;
        this.renderColumns.find(item => item.key == "efSetName").hidden = true;
      } else if (this.$route.name == "energyEfficiencyEvent") {
        // 能效超标需要展示指标类型
        this.renderColumns.find(
          item => item.key == "energyTypeName"
        ).hidden = true;
        this.renderColumns.find(item => item.key == "efSetName").hidden = false;
      } else {
        this.renderColumns.find(
          item => item.key == "energyTypeName"
        ).hidden = true;
        this.renderColumns.find(item => item.key == "efSetName").hidden = true;
      }
    },
    table_index(index) {
      return (this.page_in.currentPage - 1) * this.page_in.pageSize + index + 1;
    },
    handleSelectionChange(val) {
      var vm = this;
      vm.$emit("selectionChange_out", val);
    },
    handleExpandChange(row, expanded) {
      this.$set(row, "expanded", expanded);
      row.children.forEach(item => {
        this.$set(item, "childrenFlag", expanded);
      });
    },
    canConfirm(row, column) {
      let confirmeventstatus = row.confirmeventstatus;
      return !(confirmeventstatus && confirmeventstatus == 3);
    },
    handleDetail(index, row) {
      //详情弹窗触发信号
      var vm = this;
      vm.$refs["PQEventTable"].setCurrentRow(row);
      vm.$emit("detailTrigger_out", new Date().getTime());
      vm.$emit("record_out", row);
    },
    loadEnumrations_out(callback) {
      let _this = this;
      var data = this.$store.state.enumerations.eventtype || [];
      data = data.filter(item => {
        return item.id > 700;
      });
      _this.eventtypeList = data;
      // 事件类型默认全选
      if (this.$route.name == "energyConsumptionEvent") {
        // 能耗超标
        _this.queryData.types = this.energyConsumption;
      } else if (_this.$route.name == "energyEfficiencyEvent") {
        // 能效超标
        _this.queryData.types = this.energyEfficiecy;
      } else {
        _this.queryData.types = this.energyAll;
      }
      if (_this.$route.params?.keepParams) {
        let level = _.cloneDeep(
          _this.$route.params.keepParams.queryParams.level
        );
        if (_this.$route.name === "energyConsumptionEvent") {
          //能耗超标报警中，预警 就是 其他，其他的id=4，但是预警的level是0。因此这里如果level中含0，就把0改成4。例：[0,1,2] => [4,1,2]
          const index = level.indexOf(0);
          if (index !== -1) {
            level.splice(index, 1, 4);
          }
        }
        _this.queryData.levels = level;
      } else {
        _this.queryData.levels = [1, 2, 3, 4];
      }

      _this.energytypeList = _this._.cloneDeep(
        this.$store.state.enumerations.energytype || []
      );
      _this.queryData.endtime = _this.dateQuery[1];
      _this.queryData.starttime = _this.dateQuery[0];
      callback && callback();
    },
    exportTable_out() {
      var queryData = this._.cloneDeep(this.queryData);
      if (this.isConvergence) {
        queryData.convergence = true;
      } else {
        queryData.convergence = false;
      }
      common.downExcel(
        "/eem-service/v1/alarmEvent/energydata",
        queryData,
        this.token
      );
    },
    queryEvent_out(more) {
      var me = this,
        params;

      if (!more) {
        me.isLastPage = false;
      }
      if (!me.queryData.name) {
        return;
      }

      // 参数为空时直接清空表格。
      // if (!more) {
      //   if (
      //     me._.isEmpty(me.queryData.eventTypes) ||
      //     me._.isEmpty(me.queryData.toleranceband) ||
      //     me._.isEmpty(me.queryData.transientfaultdirection)
      //   ) {
      //     me.eventTableData = [];
      //     me.isLastPage = true;
      //     return;
      //   }
      // }

      // if (more && me.isLastPage) {
      //   return;
      // }
      if (me.isLoading) {
        return;
      }
      me.isLoading = true;
      params = me._.cloneDeep(me.queryData);
      if (params.types.length === 0) {
        me.isLoading = false;
        return;
      }

      if (me.isConvergence) {
        params.convergence = true;
      } else {
        params.convergence = false;
      }
      var queryOption = {
        url: `/eem-service/v1/alarmEvent/queryEvents`,
        method: "POST",
        data: params
      };
      httping(queryOption).then(
        response => {
          me.isLoading = false;
          if (response.code === 0) {
            // 隐藏趋势曲线
            me.hiddenChart();
            var data = me._.get(response, ["data"], []);
            let fn = children => {
              if (children && children.length > 0) {
                children.forEach((item, index) => {
                  item.confirmeventstatus$text =
                    item.confirmeventstatus === 1 ? $T("待处理") : $T("已处理");
                  item.cycle$text = me.filCycle(item.cycle);
                  item.eventtype$text = me.filEventType(item.eventtype);
                  item.level$text = me.filLevel(item);
                  // item.energyType$text = me.filEnergyType(item.energytype);
                  item.tree_id = item.modelLabel + "_" + item.id;
                  var starttime = item.eventtime,
                    endtime = item.endTime,
                    duration = endtime - starttime,
                    hour = parseInt(duration / 1000 / 60 / 60),
                    minu = parseInt((duration / 1000 / 60) % 60);
                  if (isNaN(hour) || hour === null || hour === undefined) {
                    hour = "--";
                  }
                  if (isNaN(hour) || minu === null || minu === undefined) {
                    minu = "--";
                  }
                  item.durationTime = `${hour}${$T("小时")}${minu}${$T("分")}`;
                  if (!starttime || !endtime) {
                    item.durationTime = "--";
                  }
                  if (!me.isConvergence) {
                    item.children = [];
                    item.convergence = 0;
                  } else {
                    item.children = item.children || [];
                    item.convergence = item.children.length;
                    // item.children.forEach(item11 => {
                    //   item11.tree_id = item11.modelLabel + index + "_" + item11.id;
                    // });
                  }
                  if (item.children && item.children.length > 0) {
                    fn(item.children);
                  }
                });
              }
            };
            fn(data);
            me.eventTableData = data || [];
            var total = response.total || 0;
            me.$emit("totalCount_out", total);
            me.$emit("outputData_out", data);
            // 加载新数据时清除掉原来已选择的事件。
            me.$nextTick(() => {
              me.$refs["PQEventTable"].clearSelection();
            });
          }
        },
        function (err) {
          me.isLoading = false;
        }
      );
    },
    filEventType(type = 0) {
      var eventtypeList = this.eventtypeList || [],
        fil = {};
      eventtypeList.forEach(item => {
        if (item.id == type) {
          fil = item;
        }
      });
      return fil.text || "--";
    },
    filCycle(cycle) {
      if (cycle == 7) {
        return $T("小时告警");
      } else if (cycle == 12) {
        return $T("日告警");
      } else if (cycle == 13) {
        return $T("周告警");
      } else if (cycle == 14) {
        return $T("月告警");
      } else if (cycle == 17) {
        return $T("年告警");
      } else {
        return "--";
      }
    },
    filLevel(item) {
      // 预警直接展示“预警”
      if (item.eventtype == 701) {
        return $T("预警");
      } else {
        if (item.level == 1) {
          return $T("一级告警");
        } else if (item.level == 2) {
          return $T("二级告警");
        } else if (item.level == 3) {
          return $T("三级告警");
        } else if (item.level == 4) {
          return $T("其他");
        } else {
          return "--";
        }
      }
    },
    filEnergyType(type = 0) {
      var energytypeList = this.energytypeList || [],
        fil = {};
      energytypeList.forEach(item => {
        if (item.id == type) {
          fil = item;
        }
      });
      return fil.text || "--";
      // if (energyType == 1) {
      //   return "能耗事件";
      // } else if (energyType == 2) {
      //   return "能效事件";
      // } else if (energyType == 3) {
      //   return "需量事件";
      // } else if (energyType == 4) {
      //   return "损耗事件";
      // } else {
      //   return "--";
      // }
    },
    paramsChange_out(val) {
      this.queryData.starttime = val.starttime;
      this.queryData.endtime = val.endtime;
      this.queryData.types = val.types;
      this.queryData.levels = val.levels;
      this.queryData.description = val.description;
      this.queryData.id = val.id;
      this.queryData.name = val.name;
      this.queryData.modelLabel = val.modelLabel;
      this.queryData.children = val.children;
    },
    clickNodeChange_out(val) {
      if (!val) {
        return;
      }
      this.nodeName = val.name;
      this.queryData.id = val.id;
      this.queryData.name = val.name;
      this.queryData.modelLabel = val.modelLabel;
      var children = val.children || [];
      //   arr = [];
      // children.forEach(item => {
      //   var obj = {
      //     nodeId: item.id,
      //     name: item.name,
      //     modelLabel: item.modelLabel
      //   };
      //   arr.push(obj);
      // });

      function fn(children) {
        var arr = [];
        if (children && children.length > 0) {
          children.forEach(item => {
            var obj = {};
            if (item.children && item.children.length > 0) {
              obj = {
                nodeId: item.id || item.nodeId,
                name: item.name,
                modelLabel: item.modelLabel,
                children: fn(item.children)
              };
            } else {
              obj = {
                nodeId: item.id || item.nodeId,
                name: item.name,
                modelLabel: item.modelLabel,
                children: []
              };
            }
            arr.push(obj);
          });
        }
        return arr;
      }
      this.queryData.children = fn(children);
    },
    clickPageChange_out(val) {
      if (!val) {
        return;
      }
      this.queryData.index = (val.currentPage - 1) * val.pageSize;
      this.queryData.limit = val.pageSize;
    },

    isLevel(row) {
      // 预警直接展示“预警”
      if (row.eventtype == 701) {
        const { bgColor, color } = getEventTypeColor(4);
        return { bgColor, color };
      } else {
        const { bgColor, color } = getEventGradeColor(row.level);
        return { bgColor, color };
      }
    },
    isState(row, column, cellValue, index) {
      if (row.confirmeventstatus === 3) {
        return "success";
      } else {
        return "danger";
      }
    },
    isAnalysis(row, column, cellValue, index) {
      return "row-analysis";
    },
    formatTable(row, column, cellValue, index) {
      if (!cellValue) {
        if (cellValue === 0 || cellValue === "") {
          return cellValue;
        } else {
          return "--";
        }
      }
      return cellValue;
    },
    //格式化日期列
    formatDate(row, column, cellValue, index, config) {
      //设置时间格式化字符串，如果配置有效则采用配置字符串，无效则采用默认格式化字符串
      if (cellValue) {
        return this.$moment(cellValue).format("YYYY-MM-DD HH:mm:ss.SSS");
      } else if (cellValue === "") {
        return "--";
      } else {
        return "--";
      }
    },
    //当前行变化
    handlerCurrentChange(currentRow) {
      this.$emit("currentData_out", currentRow);
      this.currentRow = currentRow;
      if (this.showChart) {
        this.getChartData1(currentRow);
      }
    },
    handleIcon(index, row) {
      if (!this.showChart) {
        this.getChartData1(row);
      }
      this.showChart = true;
    },
    //分析显示图表
    hiddenChart() {
      this.showChart = false;
    },
    // 获取事件能效趋势曲线
    getChartData1(row) {
      var _this = this,
        auth = _this.token; //身份验证
      // var queryBody = this._.cloneDeep(this.queryData);
      var clickNode = this.clickNode_in;
      if (!row || !clickNode) {
        return;
      }
      var params = {
        endtime: this.$moment().endOf("M").valueOf() + 1,
        energytype: row.energytype,
        id: row.object_id,
        level: row.level,
        modelLabel: row.object_label,
        starttime: this.$moment().startOf("M").valueOf(),
        alarmtype: row.eventtype,
        eventId: row.id,
        projectId: this.projectId
      };
      // this.CetChart_1.config.options = {};
      params.endtime = row.updatetime || null;
      params.starttime = row.eventtime || null;
      // 根据事件的周期来传查询时间
      // var cycle = row.cycle || 14,
      //   eventtime = row.eventtime;
      // if (cycle == 12) {
      //   params.endtime = this.$moment(eventtime).endOf("date").valueOf() + 1;
      //   params.starttime = this.$moment(eventtime).startOf("date").valueOf();
      // } else if (cycle == 13) {
      //   params.endtime = this.$moment(eventtime).endOf("week").valueOf() + 1;
      //   params.starttime = this.$moment(eventtime).startOf("week").valueOf();
      // } else if (cycle == 14) {
      //   params.endtime = this.$moment(eventtime).endOf("M").valueOf() + 1;
      //   params.starttime = this.$moment(eventtime).startOf("M").valueOf();
      // }
      var queryOption = {
        url: `/eem-service/v1/alarmEvent/eventAnalysis`,
        method: "POST",
        data: params
      };

      httping(queryOption).then(function (response) {
        // this.loading1 = false;
        if (response.code === 0) {
          //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
          /*
          predictAction预测动作，predictReturn预测返回，actual实际值，threshold一二三级报警线，limit定额值
          */
          var data = _this._.get(response, ["data"], null);
          _this.filChartData1(data, row.eventtype, row.eventtime);
        }
      });
    },
    filChartData1(data, eventtype, eventtime) {
      if (!data) {
        return;
      }
      var symbol = this._.get(data, "symbol") || "MWh";
      this.CetChart_1.config.options = {
        tooltip: {
          trigger: "axis"
        },
        grid: {
          top: "12%",
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true
        },
        legend: {},
        dataset: {
          source: []
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          splitLine: { show: false }
        },
        yAxis: { name: `（${symbol}）` },
        series: []
      };
      var threshold = this._.get(data, "threshold", []) || [];
      var _this = this,
        actual = data.actual || [], //实际值
        predictAction = data.predictAction || [], //预测动作
        predictReturn = data.predictReturn || [], //预测返回
        limit = data.limit, //定额
        level1 = this._.get(
          threshold.find(item => item.level === 1),
          "value",
          null
        ),
        level2 = this._.get(
          threshold.find(item => item.level === 2),
          "value",
          null
        ),
        level3 = this._.get(
          threshold.find(item => item.level === 3),
          "value",
          null
        );

      var source = [];
      var cycle = this.currentRow.cycle || 14;
      for (var i = 0, len = actual.length; i < len; i++) {
        var value1 =
            actual[i] || actual[i] === 0
              ? common.formatNumberWithPrecision(actual[i].value, 2)
              : null,
          value2 = null,
          value3 = null,
          value4 = common.formatNumberWithPrecision(limit, 2),
          level1value = common.formatNumberWithPrecision(level1, 2),
          level2value = common.formatNumberWithPrecision(level2, 2),
          level3value = common.formatNumberWithPrecision(level3, 2),
          product = this.getAxixs(actual[i].time, cycle, eventtype, eventtime);
        var predictActionObj = predictAction.find(
          item =>
            this.getAxixs(item.time, cycle, eventtype, eventtime) == product
        );
        if (predictActionObj) {
          value2 = predictActionObj
            ? common.formatNumberWithPrecision(predictActionObj.value, 2)
            : null;
        }
        var predictReturnObj = predictReturn.find(
          item =>
            this.getAxixs(item.time, cycle, eventtype, eventtime) == product
        );
        if (predictReturnObj) {
          value3 = predictReturnObj
            ? common.formatNumberWithPrecision(predictReturnObj.value, 2)
            : null;
        }

        var sour = {
          time: actual[i].time,
          product: product,
          yAxis1: value1 || value1 === 0 ? value1 : "--",
          yAxis2: value2 || value2 === 0 ? value2 : "--",
          yAxis3: value3 || value3 === 0 ? value3 : "--",
          yAxis4: value4 || value4 === 0 ? value4 : "--",
          level1value: level1value || level1value === 0 ? level1value : "--",
          level2value: level2value || level2value === 0 ? level2value : "--",
          level3value: level3value || level3value === 0 ? level3value : "--"
        };

        source.push(sour);
      }
      // 0 实际 1预测 2预测 3定额
      var seriesArr = [
          {
            name: this.energyConsumption.includes(eventtype)
              ? $T("实际能耗")
              : $T("实际能效"),
            type: "line",
            showSymbol: true,
            symbolSize: 10,
            stack: $T("总量"),
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#0a6db9"
                },
                {
                  offset: 1,
                  color: "#091545"
                }
              ])
            },
            itemStyle: {
              color: "#3693D9"
            },
            barWidth: "60%",
            encode: { x: "product", y: "yAxis1" }
          },
          {
            name: $T("预测动作"),
            type: "line",
            showSymbol: false,
            encode: { x: "product", y: "yAxis2" },
            itemStyle: {
              color: "#1E96F3"
            },
            lineStyle: {
              type: "dashed"
            }
          },
          {
            name: $T("预测返回"),
            type: "line",
            showSymbol: false,
            encode: { x: "product", y: "yAxis3" },
            itemStyle: {
              color: "#8B658B"
            },
            lineStyle: {
              type: "dashed"
            }
          },
          {
            name: this.energyConsumption.includes(eventtype)
              ? $T("能耗定额")
              : $T("能效定额"),
            type: "line",
            showSymbol: false,
            encode: { x: "product", y: "yAxis4" },
            itemStyle: {
              color: "#5BE27B"
            },
            markLine: {
              symbol: "none",
              silent: true,
              label: {
                position: "insideEndTop"
              },
              lineStyle: {
                color: "#5BE27B",
                type: "solid",
                width: 2
              },
              data: [{ type: "average", name: $T("平均值") }]
            }
          },
          {
            name: $T("一级告警"),
            type: "line",
            showSymbol: false,
            encode: { x: "product", y: "level1value" },
            itemStyle: {
              color: "#ff0000"
            },
            markLine: {
              symbol: "none",
              silent: true,
              label: {
                position: "insideEndTop"
              },
              lineStyle: {
                color: "#ff0000",
                type: "solid",
                width: 2
              },
              data: [{ type: "average", name: $T("平均值") }]
            }
          },
          {
            name: $T("二级告警"),
            type: "line",
            showSymbol: false,
            encode: { x: "product", y: "level2value" },
            itemStyle: {
              color: "#ff9900"
            },
            markLine: {
              symbol: "none",
              silent: true,
              label: {
                position: "insideEndTop"
              },
              lineStyle: {
                color: "#ff9900",
                type: "solid",
                width: 2
              },
              data: [{ type: "average", name: $T("平均值") }]
            }
          },
          {
            name: $T("三级告警"),
            type: "line",
            showSymbol: false,
            encode: { x: "product", y: "level3value" },
            itemStyle: {
              color: "#0152d9"
            },
            markLine: {
              symbol: "none",
              silent: true,
              label: {
                position: "insideEndTop"
              },
              lineStyle: {
                color: "#0152d9",
                type: "solid",
                width: 2
              },
              data: [{ type: "average", name: $T("平均值") }]
            }
          }
        ],
        series = [];

      if (this.energyConsumption.includes(eventtype)) {
        // 能耗超标
        // 预警直接展示“预警”
        if (this.currentRow.eventtype == 701) {
          series.push(seriesArr[0], seriesArr[1], seriesArr[2], seriesArr[3]);
        } else {
          series.push(seriesArr[0], seriesArr[4], seriesArr[5], seriesArr[6]);
        }
      } else if (this.energyEfficiecy.includes(eventtype)) {
        // 能效超标
        seriesArr[0].areaStyle = null;
        series.push(
          seriesArr[0],
          seriesArr[4],
          seriesArr[5],
          seriesArr[6],
          seriesArr[3]
        );
      }
      var dataset = {
        source: source
      };
      this.$nextTick(function () {
        _this.CetChart_1.config.options.dataset = dataset;
        _this.CetChart_1.config.options.series = series;
      });
      // setTimeout(function(){
      //   _this.CetChart_1.config.options.dataset = dataset;
      //   _this.CetChart_1.config.options.series = series;
      //   _this.CetChart_1.config.options.legend.data = legend;
      // },1000);
    },
    getAxixs(date_in, type, eventtype, eventtime) {
      var date = new Date(date_in),
        y = date.getFullYear(),
        M = date.getMonth() + 1,
        d = date.getDate(),
        h = date.getHours(),
        m = date.getMinutes();
      if (M < 10) {
        M = "0" + M;
      }
      if (d < 10) {
        d = "0" + d;
      }
      if (h < 10) {
        h = "0" + h;
      }
      if (m < 10) {
        m = "0" + m;
      }
      if (this.energyEfficiecy.includes(eventtype)) {
        if (type === 7) {
          if (
            this.$moment(date_in).startOf("d").valueOf() !==
            this.$moment(eventtime).startOf("d").valueOf()
          ) {
            // 不是同一天改为“24:00”
            return "24:" + m;
          }
          return h + ":" + m;
        } else if (type === 12) {
          return M + "-" + d;
        } else if (type === 14) {
          return y + "-" + M;
        } else if (type === 17) {
          return y;
        } else {
          return y + "-" + M + "-" + d;
        }
      } else {
        if (type === 12) {
          if (
            this.$moment(date_in).startOf("d").valueOf() !==
            this.$moment(eventtime).startOf("d").valueOf()
          ) {
            // 不是同一天改为“24:00”
            return "24:" + m;
          }
          return h + ":" + m;
        } else if (type === 14 || type === 13) {
          return M + "-" + d;
        } else if (type === 17) {
          return y + "-" + M;
        } else {
          return y + "-" + M + "-" + d;
        }
      }
    },
    // tab行颜色
    tableRowClassName({ row, rowIndex }) {
      if (row.expanded) {
        return "warning-row";
      }
      if (row.childrenFlag) {
        return "warning-row2";
      }
    }
  },

  created: function () {
    let me = this;
    if (this.clickNode_in) {
      this.queryData.id = this.clickNode_in.id;
      this.queryData.name = this.clickNode_in.name;
      this.queryData.modelLabel = this.clickNode_in.modelLabel;
      var children = this.clickNode_in.children || [],
        arr = [];
      children.forEach(item => {
        var obj = {
          nodeId: item.id,
          name: item.name,
          modelLabel: item.modelLabel
        };
        arr.push(obj);
      });
      this.queryData.children = arr;
    }
  },
  activated: function () {
    this.judgeRoute();
    this.loadEnumrations_out();
  },
  mounted() {
    if (
      !["energyConsumptionEvent", "energyEfficiencyEvent"].includes(
        this.$route.name
      )
    ) {
      this.judgeRoute();
      this.loadEnumrations_out();
    }
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.wave-view-button {
  cursor: pointer;
  text-decoration: underline;
  color: rgb(0, 102, 204);
}
.el-icon-more {
  cursor: pointer;
}
.confirm {
  color: #ccc;
}
.unconfirm {
  color: red;
  cursor: pointer;
}
.more {
  color: #0066cc;
}
//有子节点 且未展开
// .table.el-table :deep(.el-icon-arrow-right:before) {
//   content: "\e791";
// }
//有子节点 且已展开
// .table.el-table :deep(.el-table__expand-icon--expanded) {
//   .el-icon-arrow-right:before {
//     content: "\e791";
//   }
// }

.PQEvent-table {
  // :deep(.el-icon-arrow-right) {
  //   color: #fff;
  // }
  :deep(.row-analysis) {
    display: inline-block;
    width: 25px;
    height: 24px;
    cursor: pointer;
    background: url("./assets/u10740.png") no-repeat center #0350da;
    border-radius: 50%;
    position: relative;
    top: 3px;
  }
}
.icon-bottom-hidden {
  display: inline-block;
  width: 100px;
  height: 24px;
  cursor: pointer;
  background: url("./assets/u7706.png") no-repeat center center;
  position: absolute;
  left: calc(50% - 50px);
}
.analysis {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url("./assets/u10740.png") no-repeat center #0350da;
  border-radius: 50%;
  position: relative;
  top: 4px;
}
</style>
