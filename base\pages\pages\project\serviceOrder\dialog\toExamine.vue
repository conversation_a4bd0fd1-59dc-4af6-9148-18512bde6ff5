<template>
  <div>
    <CetDialog
      v-bind="CetDialog_add"
      v-on="CetDialog_add.event"
      class="min el_dialog"
    >
      <div class="eem-cont-c1">
        <div style="height: 40px">
          <span>{{$T("处理描述")}}：</span>
        </div>
        <div style="height: 120px">
          <el-input
            type="textarea"
            rows="5"
            resize="none"
            :placeholder="$T('请输入内容')"
            v-model.trim="description"
            onKeypress="javascript:if(event.keyCode == 32)event.returnValue = false;"
          ></el-input>
        </div>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_submit"
          v-on="CetButton_submit.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_noSubmit"
          v-on="CetButton_noSubmit.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
export default {
  name: "toExamine",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    codes_in: {
      type: Array
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  data() {
    return {
      isOk: true,
      CetDialog_add: {
        title: $T("审核"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        width: "540px",
        showClose: true
      },
      CetButton_confirm: {
        visible_in: false,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_submit: {
        visible_in: true,
        disable_in: false,
        title: $T("通过"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_submit_statusTrigger_out
        }
      },
      CetButton_noSubmit: {
        visible_in: true,
        disable_in: false,
        title: $T("不通过"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_noSubmit_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      description: ""
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;

      if (!this.inputData_in || !this.inputData_in.code) {
        this.CetButton_confirm.visible_in = false;
        vm.CetDialog_add.openTrigger_in = val;
        this.description = "";
      } else {
        this.CetButton_confirm.visible_in = true;
        new Promise((res, err) => {
          this.getWorkOrderCheckStash_out(res);
        }).then(data => {
          if (data) {
            vm.CetDialog_add.openTrigger_in = new Date().getTime();
          } else {
            this.$message.warning($T("获取暂存审核工单信息失败"));
          }
        });
      }

      // this.reset();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    }
  },

  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      const parasm = {
        code: this.inputData_in.code,
        params: {
          remark: this.description
        }
      };
      this.toWorkOrderCheckStash_out(parasm);
    },
    CetButton_submit_statusTrigger_out() {
      const parasm = {
        codes: this.codes_in,
        params: {
          // attachments:[],
          // formData: {},
          // processVariables: {},
          // suggestion: "",
          remark: this.description,
          taskResult: true
        }
      };
      this.toWorkOrderCheck_out(parasm);
    },
    CetButton_noSubmit_statusTrigger_out() {
      const parasm = {
        codes: this.codes_in,
        params: {
          remark: this.description,
          taskResult: false
        }
      };
      this.toWorkOrderCheck_out(parasm);
    },
    init() {},
    //批量审核工单
    toWorkOrderCheck_out(params) {
      const _this = this;
      if (!params || !params.codes) {
        return;
      }

      customApi
        .toMaintenanceOrderCheckBatch(params)
        .then(res => {
          if (res.code === 0) {
            _this.CetDialog_add.closeTrigger_in = this._.cloneDeep(
              new Date().getTime()
            );
            _this.$emit("confirm_out", {});
            _this.$message.success($T("审核工单成功"));
          }
        })
        .catch(() => {});
    },
    //暂存审核信息
    toWorkOrderCheckStash_out(params) {
      const _this = this;
      if (!params || !params.code) {
        return;
      }

      customApi
        .toMaintenanceOrderCheckStash(params)
        .then(res => {
          if (res.code === 0) {
            _this.CetDialog_add.closeTrigger_in = this._.cloneDeep(
              new Date().getTime()
            );
            _this.$emit("confirm_out", {});
            _this.$message.success($T("暂存审核信息成功"));
          }
        })
        .catch(() => {});
    },
    //查询暂存审核信息
    getWorkOrderCheckStash_out(callback) {
      const _this = this;
      if (!this.inputData_in || !this.inputData_in.code) {
        callback && callback(false);
        return;
      }
      const params = {
        code: this.inputData_in.code
      };

      customApi
        .getMaintenceOrderCheckStash(params)
        .then(res => {
          if (res.code === 0) {
            const data = _this._.get(res, "data.remark", "");
            _this.description = data || "";
            callback && callback(true);
          } else {
            callback && callback(false);
          }
        })
        .catch(() => {});
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.stateBtn {
  cursor: pointer;
  float: left;
  width: 220px;
  height: 80px;
  border: 1px solid #1d71e0;
  border-radius: 4px;
  div {
    height: 40px;
    line-height: 40px;
    text-align: center;
  }
}
.isActive {
  background-color: #28a21e;
  border: none;
}
.isActive11 {
  background-color: rgb(207, 71, 71);
  border: none;
}
</style>
