<template>
  <customDrawer v-bind="modelCreateDetail">
    <template v-slot:headerRight>
      <el-button @click="onEdit">编辑</el-button>
    </template>
    <div class="w-full h-full eem-cont-c1">
      <div class="w-full">
        <div class="title">基本信息</div>
        <div class="gap-[24px] grid grid-cols-2">
          <div v-for="i in data" :key="i.label">
            <div class="text-T3 mb-[4px]">{{ i.label }}</div>
            <div class="ov" :title="i">
              {{ i.value }}
            </div>
          </div>
        </div>
      </div>
      <div class="w-full mt-[24px]">
        <div class="title">输入参数</div>
        <div class="gap-[24px] grid grid-cols-1">
          <div v-for="i in data1" :key="i.label">
            <div class="text-T3 mb-[4px]">{{ i.label }}</div>
            <div class="text-T2" :title="i.value">
              {{ i.value }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <AddOrEditModelCreate v-bind="addOrEditModelCreate" />
  </customDrawer>
</template>

<script>
import customDrawer from "@/components/customElDrawer";
import AddOrEditModelCreate from "./addOrEditModelCreate.vue";
import { getData, getDataParams } from "../component/tableCol.jsx";
export default {
  name: "modelCreateDetail",
  components: { customDrawer, AddOrEditModelCreate },
  computed: {},
  props: {
    openTrigger_in: Number,
    inputData_in: Object
  },
  data() {
    return {
      modelCreateDetail: {
        title: "方案详情",
        size: "480px",
        openTrigger_in: +new Date(),
        closeTrigger_in: +new Date()
      },
      addOrEditModelCreate: {
        openTrigger_in: +new Date(),
        inputData_in: {}
      },
      data: [
        { label: "方案名称", value: "", id: "name" },
        { label: "模型目标", value: "", id: "modelGoal" },
        { label: "目标参数", value: "", id: "targetParameter" },
        { label: "参数读取时间", value: "", id: "parameterPeriod" }
      ],
      data1: [
        { label: "注水站管阀参数", value: "", id: "inputParameter" },
        { label: "注水泵参数", value: "", id: "inputParameter1" },
        { label: "注水井参数", value: "", id: "inputParameter2" }
      ]
    };
  },
  watch: {
    openTrigger_in() {
      this.modelCreateDetail.openTrigger_in = +new Date();
    },
    inputData_in: {
      deep: true,
      handler(val) {
        this.data?.forEach(i => {
          i.value = _.isNumber(val[i.id])
            ? getData(i.id, val[i.id])
            : val[i.id] || "--";
        });

        this.data1?.forEach(i => {
          let data = getDataParams(i.id);
          i.value =
            val?.inputParameter
              ?.map(it => {
                const obj = data?.find(itx => itx.id === it);
                if (_.isObject(obj)) {
                  return obj?.text;
                }
              })
              ?.filter(i => i)
              ?.join(",") || "--";
        });
      }
    }
  },
  methods: {
    onEdit() {
      this.addOrEditModelCreate.openTrigger_in = +new Date();
      this.addOrEditModelCreate.inputData_in = _.cloneDeep(this.inputData_in);
    }
  }
};
</script>
<style lang="scss" scoped>
.title {
  @apply mb-[24px] font-semibold text-T2;
}
.ov {
  @apply overflow-hidden text-T2 text-ellipsis whitespace-nowrap;
}
</style>
