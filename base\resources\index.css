@import "@omega/theme/tailwind.var.css";

@layer components {
  .btnCy {
    @apply ml-J2 rounded-full;
    @apply bg-ZS !important;
  }
  .btn {
    /* @apply px-4 py-2 rounded-full bg-gray-400; */
  }
}

@layer base {
  :root {
    /* 间距 */
    --J1: 8px;
    --J2: 16px;
    --J3: 24px;
    --J4: 32px;
    --J5: 10px;
    --J6: 12px;
    --J7: 48px;

    /* 图标 */
    --I1: 24px;
    --I2: 32px;
    --I3: 40px;
    --I4: 64px;
    --I5: 96px;

    /* 字体 */
    --H: 28px;
    --H1: 22px;
    --H2: 18px;
    --H3: 16px;
    --Aa: 14px;
    --Ab: 12px;
    --H4: 22px;
    --H5: 20px;
    --Hh: 36px;
    --Hm: 34px;

    /* 强投影 */
    --S1: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    /* 浅色投影 */
    --S2: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    /* 圆角 */
    --C: 4px;
    --C1: 8px;
    --C2: 12px;
    --C3: 16px;

    /* 字重 */
    --MD: 600;
  }
}
