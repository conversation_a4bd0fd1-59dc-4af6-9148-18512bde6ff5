<template>
  <div>
    <CetDialog class="CetDialog" v-bind="CetDialog_1" v-on="CetDialog_1.event">
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
      <div class="eem-cont-c1">
        <div class="mbJ2">
          <CetButton
            v-bind="CetButton_add"
            v-on="CetButton_add.event"
          ></CetButton>
        </div>
        <div class="mbJ mlJ">
          <span>{{ $T("分组") }}</span>
          <span class="star">*</span>
        </div>
        <ElSelect
          class="custom-select-tag mbJ2"
          v-model="ElSelect_group.value"
          v-bind="ElSelect_group"
          v-on="ElSelect_group.event"
        >
          <ElOption
            v-for="item in ElOption_group.options_in"
            :key="item[ElOption_group.key]"
            :label="item[ElOption_group.label]"
            :value="item[ElOption_group.value]"
            :disabled="item[ElOption_group.disabled]"
          ></ElOption>
        </ElSelect>
        <CetTable
          ref="table"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
          style="height: 500px"
        >
          <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_name" :key="2">
            <template slot="header">
              <span>{{ $T("签到点名称") }}</span>
              <span class="star mlJ">*</span>
            </template>
            <template slot-scope="scope">
              <ElInput
                v-model.trim="CetTable_1.data[scope.$index].name"
                v-bind="ElInput_1"
                :maxlength="20"
                @change="change(scope)"
              ></ElInput>
            </template>
          </ElTableColumn>
          <ElTableColumn
            v-bind="ElTableColumn_objectName"
            :key="3"
          ></ElTableColumn>
          <ElTableColumn
            v-bind="ElTableColumn_parentname"
            :key="4"
          ></ElTableColumn>
          <ElTableColumn
            v-bind="ElTableColumn_nfc"
            :key="5"
            v-if="isSigninByNFC_in"
          >
            <template slot="header">
              <span>{{ $T("NFC") }}</span>
              <span class="star mlJ">*</span>
            </template>
            <template slot-scope="scope">
              <ElInput
                v-model.trim="CetTable_1.data[scope.$index].nfc"
                v-bind="ElInput_1"
                @change="change(scope)"
              ></ElInput>
            </template>
          </ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_interval" :key="6">
            <template slot-scope="scope">
              <ElInputNumber
                v-model="CetTable_1.data[scope.$index].interval"
                v-bind="ElInputNumber_1"
                @change="change(scope)"
              ></ElInputNumber>
              <!-- <span class="form-item-unit">min</span> -->
            </template>
          </ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_address" :key="7">
            <template slot-scope="scope">
              <ElInput
                v-model="CetTable_1.data[scope.$index].address"
                v-bind="ElInput_1"
                @change="change(scope)"
              ></ElInput>
            </template>
          </ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_ID" :key="8"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_operate">
            <template slot-scope="{ row }">
              <span class="deleteHandle" @click="handleDelete(row)">
                {{ $T("删除") }}
              </span>
            </template>
          </ElTableColumn>
        </CetTable>
      </div>
    </CetDialog>
    <inspectObjDialog
      v-bind="inspectObjDialog"
      v-on="inspectObjDialog.event"
    ></inspectObjDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom";
import inspectObjDialog from "./inspectObjDialog";
export default {
  components: {
    inspectObjDialog
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    // 选中的分组
    activeGroup: {
      type: Object
    },
    // 分组列表
    groupList: {
      type: Array
    },
    // 判断nfc是否展示
    isSigninByNFC_in: {
      type: Boolean,
      default: false
    }
  },
  data(vm) {
    return {
      // 设置组件唯一识别字段弹窗组件
      CetDialog_1: {
        title: $T("批量新建"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        width: "960px",
        showClose: true,
        // 遮盖层操作,将自身置于最外层展示
        "append-to-body": true
      },
      // confirm组件
      CetButton_confirm: {
        visible_in: true,
        disable_in: true,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      // cancel组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // 批量添加签到点
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("批量添加签到点"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      // group组件
      ElSelect_group: {
        value: [],
        multiple: true,
        "collapse-tags": true,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_group_change_out
        }
      },
      // group组件
      ElOption_group: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { midLimit: "name_in", operator: "LIKE", prop: "midLimit" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [{ name: "对象" }],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        // defaultSort: null, // { prop: "code"  order: "descending" },
        event: {}
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        label: "#", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "40" //绝对宽度
      },
      ElTableColumn_name: {
        prop: "name", // 支持path a[0].b
        label: $T("签到点名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: 120
      },
      ElTableColumn_objectName: {
        prop: "objectName", // 支持path a[0].b
        label: $T("巡检对象名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: 120
      },
      ElTableColumn_nfc: {
        prop: "nfc", // 支持path a[0].b
        label: "NFC", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: 120
      },
      ElTableColumn_ID: {
        prop: "tree_id", // 支持path a[0].b
        label: "ID", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: 120
      },
      ElTableColumn_parentname: {
        prop: "parentname", // 支持path a[0].b
        label: $T("父节点路径"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: 130
      },
      ElTableColumn_interval: {
        prop: "interval", // 支持path a[0].b
        label: $T("最短时间间隔") + "(min)", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: 150
      },
      ElTableColumn_address: {
        prop: "address", // 支持path a[0].b
        label: $T("地址"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: 200
      },
      ElTableColumn_operate: {
        prop: "", // 支持path a[0].b
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: 80,
        fixed: "right"
      },
      ElInputNumber_1: {
        value: "",
        placeholder: $T("请输入"),
        controls: false,
        min: 0,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入"),
        style: {
          width: "100%"
        },
        event: {}
      },
      // 巡检对象弹窗
      inspectObjDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        tableData: null,
        event: {
          saveData_out: this.insepectObjDialog_saveData_out
        }
      }
    };
  },
  watch: {
    openTrigger_in(val) {
      this.ElOption_group.options_in = this.groupList;
      this.ElSelect_group.value = [this.activeGroup.id];
      this.CetTable_1.data = [];
      this.inspectObjDialog.tableData = this.CetTable_1.data;
      this.CetDialog_1.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    "CetTable_1.data": {
      handler(newVal, oldVal) {
        this.CetButton_confirm.disable_in = newVal.length ? false : true;
      },
      deep: true
    }
  },
  methods: {
    CetButton_confirm_statusTrigger_out() {
      let params = [];
      let isEmpty = false;
      let isEmptyNFC = false;
      if (!this.ElSelect_group.value || !this.ElSelect_group.value.length) {
        return this.$message.error($T("请选择分组"));
      }
      this.CetTable_1.data.forEach((item, index) => {
        if (!item.name) {
          isEmpty = true;
        }
        if (!item.nfc && this.isSigninByNFC_in) {
          isEmptyNFC = true;
        }
        params.push({
          interval: item.interval,
          name: item.name,
          nfc: item.nfc,
          address: item.address,
          signInGroupIds: this.ElSelect_group.value,
          children: [
            {
              objectid: item.objectid,
              objectlabel: item.objectlabel
            }
          ]
        });
      });
      if (isEmpty) {
        return this.$message.error($T("请填入签到点名称"));
      } else if (isEmptyNFC) {
        return this.$message.error($T("请填入NFC"));
      } else {
        this.batchSaveSigninObj(params);
      }
    },
    // 批量保存
    batchSaveSigninObj(val) {
      customApi.batchSigninPoints(val).then(res => {
        if (res.code === 0) {
          this.$message.success($T("保存成功"));
          this.$emit("saveData_out");
          this.CetDialog_1.closeTrigger_in = Date.now();
        }
      });
    },
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    },
    CetButton_add_statusTrigger_out() {
      this.inspectObjDialog.openTrigger_in = new Date().getTime();
    },
    ElSelect_group_change_out() {},
    change() {},
    insepectObjDialog_saveData_out(val) {
      val.forEach(item => {
        item.objectName = item.name;
      });
      this.CetTable_1.data = this._.cloneDeep(val);
      this.inspectObjDialog.tableData = this._.cloneDeep(this.CetTable_1.data);
    },
    // 表格内数据删除
    handleDelete(row) {
      this.$confirm($T("确定要删除所选项吗？"), $T("删除确认"), {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消")
      }).then(() => {
        const index = row.rowIndex;
        this.CetTable_1.data.splice(index, 1);
        this.inspectObjDialog.tableData = this.CetTable_1.data;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.custom-select-tag :deep(.el-select__tags-text) {
  float: left;
  max-width: 90px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.deleteHandle {
  @include font_color(Sta3);
  cursor: pointer;
}
.star {
  color: red;
}
</style>
