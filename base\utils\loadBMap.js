import { store } from "@omega/app";

export const loadBMap = async () => {
  const isOnline = store?.state?.systemCfg?.onLine;
  let BMap_URL = [
    "static/library/baiduMap/map3.0_init.js",
    "static/library/baiduMap/map3.0.js"
  ];
  if (isOnline) {
    BMap_URL = [
      "http://api.map.baidu.com/api?v=3.0&ak=NUi5LUp0Ls477BWawHL8TyjKlY6EudEI&s=1&callback=onBMapCallback",
      "http://lbsyun.baidu.com/custom/stylelist.js",
      "static/js/baiduMap/TextIconOverlay.js",
      "static/js/baiduMap/MarkerClusterer.js",
      "static/js/baiduMap/InfoBox.js",
      "static/js/baiduMap/RichMarker.js",
      "static/js/baiduMap/RichMarker.js",
      "static/js/baiduMap/CurveLine.js"
    ];
  }
  if (typeof BMap !== "undefined") {
    // 如果已加载直接返回
    return true;
  }
  // // 百度地图异步加载回调处理
  return new Promise((res, reject) => {
    if (isOnline) {
      window.onBMapCallback = () => {
        console.log("百度地图脚本初始化成功...");
        setTimeout(() => {
          res();
        }, 300);
      };
    }
    // 插入script脚本
    let finishCount = 0;
    BMap_URL.forEach(sUrl => {
      let scriptNode = document.createElement("script");
      scriptNode.type = "text/javascript";
      scriptNode.src = sUrl;

      scriptNode.onload = scriptNode.onreadystatechange = function () {
        if (
          !this.readyState ||
          this.readyState === "loaded" ||
          this.readyState === "complete"
        )
          finishCount += 1;

        if (finishCount === BMap_URL.length) {
          setTimeout(() => {
            res();
          }, 300);
        }
      };
      document.body.appendChild(scriptNode);
    });
  });
};
