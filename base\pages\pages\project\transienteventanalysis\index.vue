<template>
  <div class="page eem-common">
    <div class="" style="overflow: auto">
      <transientEventAnalysisView
        v-if="activeName == 'transientEventAnalysisView'"
      />
    </div>
  </div>
</template>

<script>
import transientEventAnalysisView from "./transientEventAnalysisView";
export default {
  name: "transientEventAnalysis",
  components: {
    transientEventAnalysisView
  },
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  data() {
    return {
      activeName: "transientEventAnalysisView"
    };
  },
  watch: {},

  methods: {},

  created: function () {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  :deep(.el-tabs__item) {
    font-size: 16px;
  }
  :deep(.el-tabs) {
    width: 100%;
    height: 100%;
    padding: 10px;
    box-sizing: border-box;
  }
  :deep(.el-tabs__content > div) {
    width: 100%;
    height: 100%;
  }
}
</style>
