<template>
  <div class="flex justify-between w-full h-full">
    <div class="w-[320px] h-full p-[16px] box-border border-r border-B2">
      <customTree
        :dataMode="CetTree_1.dataMode"
        :inputData_in="CetTree_1.inputData_in"
        :selectNode.sync="CetTree_1.selectNode"
        :searchText_in="CetTree_1.searchText_in"
        :filterNodes_in="CetTree_1.filterNodes_in"
        :checkedArray.sync="CetTree_1.checkedArray"
        v-bind="CetTree_1.config"
      />
    </div>
    <div
      v-if="isWaterinjectionstation"
      class="flex flex-col justify-between w-[calc(100%-320px)] h-full px-[20px] py-[16px] box-border"
    >
      <div class="w-full text-T2 font-semibold text-[16px]">
        离线模型单耗预测值拟合曲线
      </div>
      <div class="flex-1 mt-[16px] flex flex-col">
        <LineChart
          class="w-full !h-[calc(100%-441px-16px)]"
          :selectNode="CetTree_1.selectNode"
          @paramsTime="onParamsTime"
        ></LineChart>
        <div
          class="border rounded border-B2 p-[16px] box-border w-full h-[441px] mt-[16px]"
        >
          <el-radio-group v-model="activeName" style="margin-bottom: 30px">
            <el-radio-button label="injectionStation">
              注水站参数
            </el-radio-button>
            <el-radio-button label="injectionWell">
              注水井参数Top20
            </el-radio-button>
          </el-radio-group>
          <div class="mt-[16px] w-full h-[calc(100%-32px-16px)]">
            <InjectionStation
              v-if="activeName == 'injectionStation'"
              :selectNode="CetTree_1.selectNode"
              :paramsTime="paramsTime"
              :activeName="activeName"
            ></InjectionStation>
            <InjectionWell
              :activeName="activeName"
              v-if="activeName == 'injectionWell'"
              :selectNode="CetTree_1.selectNode"
              :paramsTime="paramsTime"
            ></InjectionWell>
          </div>
        </div>
      </div>
    </div>
    <div
      class="w-[calc(100%-320px)] h-full flex flex-col items-center justify-center text-[24px] font-bold"
      v-else
    >
      <img v-if="isLight" src="../../../resources/assets/light.png" alt="" />
      <img v-else src="../../../resources/assets/dark.png" alt="" />
      <div class="text-[16px] text-T3 mt-[16px]">暂无数据</div>
    </div>
  </div>
</template>

<script>
import omegaTheme from "@omega/theme";
import customTree from "@/components/customTree/index.vue";
import customApi from "@/api/custom.js";
import LineChart from "./lineChart.vue";
import InjectionStation from "./injectionStation.vue";
import InjectionWell from "./injectionWell.vue";
import { flatTreeData } from "../component/tableCol.jsx";
export default {
  name: "offLineModelTrain",
  props: {},
  components: { customTree, LineChart, InjectionStation, InjectionWell },
  data() {
    return {
      CetTree_1: {
        dataMode: "static",
        inputData_in: [],
        searchText_in: "",
        filterNodes_in: "",
        selectNode: null,
        checkedArray: [],
        config: {
          highlightCurrent: true,
          ShowRootNode: false,
          expandMode: true,
          screen: true,
          initialFlag: false,
          showCheckbox: false,
          checkStrictly: false,
          filterModelLabel: "waterinjectionstation"
        }
      },
      activeName: "injectionStation",
      paramsTime: []
    };
  },
  computed: {
    isWaterinjectionstation() {
      return this.CetTree_1.selectNode?.modelLabel == "waterinjectionstation";
    },
    isLight() {
      return omegaTheme.theme === "light";
    }
  },
  watch: {},
  methods: {
    // 获取节点树
    async getTreeData() {
      const queryData = {
        rootID: 0,
        rootLabel: "oilcompany",
        subLayerConditions: [
          { modelLabel: "oilproductionplant" },
          { modelLabel: "operationarea" },
          { modelLabel: "oilproductioncrew" },
          { modelLabel: "waterinjectionstation" }
        ],
        treeReturnEnable: true
      };
      const res = await customApi.getNodeTree(queryData);
      this.CetTree_1.inputData_in = res?.data || [];

      if (!_.isEmpty(res?.data)) {
        const firstOilwell = _.find(flatTreeData(res?.data), [
          "modelLabel",
          "waterinjectionstation"
        ]);
        this.CetTree_1.selectNode = firstOilwell || {};
      }
    },

    onParamsTime(val) {
      this.paramsTime = _.cloneDeep(val);
    }
  },
  created() {},
  mounted() {
    this.getTreeData();
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-radio-group) {
  margin-bottom: 0 !important;
}
</style>
