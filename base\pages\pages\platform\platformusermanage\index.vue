<template>
  <div class="eem-common fullfilled">
    <div class="fullheight plJ3 prJ3 ptJ4 pbJ4 bg1 treeBox">
      <div class="common-title-H1">{{ $T("用户列表") }}</div>
      <TenantTree
        class="CetTree mtJ3"
        ref="tree"
        @currentNode_out="CetTree_1_currentNode_out"
        :refreshTrigger="refreshTenantTree"
        :refreshTreeDataTrigger="refreshTenantTreeData"
      />
      <div class="btnBox clearfix ptJ3">
        <CetButton
          class="fr"
          v-if="CetButton_service.visible_in && !onlyResetPassword"
          v-bind="CetButton_service"
          v-on="CetButton_service.event"
        ></CetButton>
        <CetButton
          class="mrJ1 fr"
          v-if="CetButton_enterprise.visible_in && !onlyResetPassword"
          v-bind="CetButton_enterprise"
          v-on="CetButton_enterprise.event"
        ></CetButton>
        <CetButton
          class="mrJ1 fr"
          v-if="CetButton_user.visible_in && !onlyResetPassword"
          v-bind="CetButton_user"
          v-on="CetButton_user.event"
        ></CetButton>
        <CetButton
          class="mrJ1 fr"
          v-if="CetButton_usergroup.visible_in && !onlyResetPassword"
          v-bind="CetButton_usergroup"
          v-on="CetButton_usergroup.event"
        ></CetButton>
      </div>
    </div>
    <div class="contentBox mlJ3">
      <div
        :class="{
          mainContent: true,
          bg1: true,
          borderRadiusC: currentNode && currentNode.modelLabel === 'usergroup'
        }"
      >
        <div class="title clearfix">
          <span class="name text-ellipsis">
            {{
              currentNode
                ? currentNode.modelLabel === "user"
                  ? $T("用户权限信息")
                  : currentNode.name
                : ""
            }}
          </span>

          <CetButton
            class="mlJ1 fr delete"
            v-if="
              CetButton_del.visible_in &&
              !isOneself &&
              !isROOT &&
              currentNode &&
              !onlyResetPassword
            "
            v-bind="CetButton_del"
            v-on="CetButton_del.event"
          ></CetButton>
          <CetButton
            class="mlJ1 fr"
            v-if="
              CetButton_edit.visible_in &&
              !isOneself &&
              !isROOT &&
              currentNode &&
              !onlyResetPassword
            "
            v-bind="CetButton_edit"
            v-on="CetButton_edit.event"
          ></CetButton>
          <CetButton
            v-if="showButton"
            class="mlJ1 fr"
            v-bind="CetButton_editPassword"
            v-on="CetButton_editPassword.event"
          ></CetButton>
        </div>
        <UserView
          :currentNode_in="currentNode"
          v-if="currentNode && currentNode.modelLabel === 'user'"
          @role_out="filterOperationAuths"
        />
        <ROOTView
          :currentNode_in="currentNode"
          v-else-if="
            currentNode &&
            currentNode.modelLabel === 'tenant' &&
            currentNode.id === 1
          "
        />
        <ServiceView
          :currentNode_in="currentNode"
          v-else-if="
            currentNode &&
            currentNode.modelLabel === 'tenant' &&
            currentNode.type === 0
          "
        />
        <EnterpriseView
          :currentNode_in="currentNode"
          v-else-if="
            currentNode &&
            currentNode.modelLabel === 'tenant' &&
            currentNode.type === 1
          "
        />
        <UserGroupView
          :currentNode_in="currentNode"
          v-else-if="currentNode && currentNode.modelLabel === 'usergroup'"
        />
      </div>
      <div
        class="tabs bg1"
        v-show="currentNode && currentNode.modelLabel !== 'usergroup'"
      >
        <el-tabs
          v-model="activeName"
          @tab-click="handleClick"
          class="eem-tabs-custom mlJ1"
        >
          <el-tab-pane
            :label="
              currentNode && currentNode.modelLabel === 'user'
                ? $T('操作权限')
                : $T('设备链路层节点')
            "
            name="pecCore"
          ></el-tab-pane>
          <el-tab-pane
            v-if="
              currentNode &&
              currentNode.modelLabel === 'user' &&
              currentNode.tenantId !== 1
            "
            :label="$T('项目节点权限')"
            name="project"
          ></el-tab-pane>
          <el-tab-pane :label="$T('实时监控权限')" name="Graph"></el-tab-pane>
          <el-tab-pane
            :label="$T('pecReport报表权限')"
            name="pecReport"
            v-if="!hidePecReport"
          ></el-tab-pane>
          <el-tab-pane
            :label="$T('mReport报表权限')"
            name="mReport"
            v-if="!hideMReport"
          ></el-tab-pane>
          <el-tab-pane
            :label="$T('onlyReport报表权限')"
            name="onlyReport"
            v-if="!hideOnlyReport"
          ></el-tab-pane>
          <el-tab-pane
            v-if="
              currentNode &&
              currentNode.modelLabel === 'user' &&
              currentNode.tenantId !== 1 &&
              !hideVideo
            "
            :label="$T('视频权限')"
            name="video"
          ></el-tab-pane>
        </el-tabs>
      </div>
      <div class="detailContent mtJ2 eem-cont" v-if="currentNode">
        <div v-show="currentNode.modelLabel !== 'usergroup'" class="fullfilled">
          <div v-show="activeName === 'pecCore'" class="fullfilled">
            <CetTree
              class="CetTree"
              v-show="currentNode.modelLabel === 'user'"
              v-bind="CetTree_Permissions"
            ></CetTree>
            <CetGiantTree
              class="CetTree"
              v-show="currentNode.modelLabel !== 'user'"
              v-bind="CetGiantTree_pecCore"
              v-on="CetGiantTree_pecCore.event"
            ></CetGiantTree>
          </div>
          <div v-show="activeName === 'project'" class="fullfilled">
            <CetTree
              class="CetTree"
              :selectNode.sync="CetTree_project.selectNode"
              :checkedNodes.sync="CetTree_project.checkedNodes"
              v-bind="CetTree_project"
              v-on="CetTree_project.event"
            ></CetTree>
          </div>
          <div v-show="activeName === 'Graph'" class="fullfilled">
            <CetGiantTree
              class="CetTree"
              v-bind="CetGiantTree_graph"
              v-on="CetGiantTree_graph.event"
            ></CetGiantTree>
          </div>
          <div v-show="activeName === 'pecReport'" class="fullfilled">
            <CetGiantTree
              class="CetTree"
              v-bind="CetGiantTree_pecReport"
              v-on="CetGiantTree_pecReport.event"
            ></CetGiantTree>
          </div>
          <div v-show="activeName === 'mReport'" class="fullfilled">
            <CetGiantTree
              class="CetTree"
              v-bind="CetGiantTree_mReport"
              v-on="CetGiantTree_mReport.event"
            ></CetGiantTree>
          </div>
          <div v-show="activeName === 'onlyReport'" class="fullfilled">
            <CetGiantTree
              class="CetTree"
              v-bind="CetGiantTree_onlyReport"
              v-on="CetGiantTree_onlyReport.event"
            ></CetGiantTree>
          </div>
          <div v-show="activeName === 'video'" class="fullfilled">
            <CetTree
              class="CetTree"
              :selectNode.sync="CetTree_video.selectNode"
              :checkedNodes.sync="CetTree_video.checkedNodes"
              :searchText_in.sync="CetTree_video.searchText_in"
              v-bind="CetTree_video"
              v-on="CetTree_video.event"
            ></CetTree>
          </div>
        </div>
        <div v-show="currentNode.modelLabel === 'usergroup'" class="fullfilled">
          <CetTable
            :data.sync="CetTable_user.data"
            :dynamicInput.sync="CetTable_user.dynamicInput"
            v-bind="CetTable_user"
            v-on="CetTable_user.event"
          >
            <template v-for="item in Columns_user">
              <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
            </template>
          </CetTable>
        </div>
      </div>
    </div>
    <EditService
      :visibleTrigger_in="EditService.visibleTrigger_in"
      :closeTrigger_in="EditService.closeTrigger_in"
      :inputData_in="EditService.inputData_in"
      :currentNode_in="EditService.currentNode_in"
      @finishTrigger_out="getUserTreeData"
    />
    <EditEnterprise
      :visibleTrigger_in="EditEnterprise.visibleTrigger_in"
      :closeTrigger_in="EditEnterprise.closeTrigger_in"
      :inputData_in="EditEnterprise.inputData_in"
      :currentNode_in="EditEnterprise.currentNode_in"
      @finishTrigger_out="getUserTreeData"
    />
    <EditUser
      :visibleTrigger_in="EditUser.visibleTrigger_in"
      :closeTrigger_in="EditUser.closeTrigger_in"
      :inputData_in="EditUser.inputData_in"
      :currentNode_in="EditUser.currentNode_in"
      :role_in="EditUser.role_in"
      @finishTrigger_out="getUserTreeData"
    />
    <EditUserGroup
      :visibleTrigger_in="EditUserGroup.visibleTrigger_in"
      :closeTrigger_in="EditUserGroup.closeTrigger_in"
      :inputData_in="EditUserGroup.inputData_in"
      :currentNode_in="EditUserGroup.currentNode_in"
      @finishTrigger_out="getUserTreeData"
    />
    <ResetPassword
      :visibleTrigger_in="ResetPassword.visibleTrigger_in"
      :closeTrigger_in="ResetPassword.closeTrigger_in"
      :queryId_in="ResetPassword.queryId_in"
      :inputData_in="ResetPassword.inputData_in"
    ></ResetPassword>
  </div>
</template>
<script>
import ROOTView from "./ROOT/view";
import ServiceView from "./service/view";
import EditService from "./service/edit";
import EnterpriseView from "./enterprise/view";
import EditEnterprise from "./enterprise/edit";
import UserGroupView from "./userGroup/view";
import EditUserGroup from "./userGroup/EditUserGroup";
import UserView from "./user/view";
import EditUser from "./user/edit";
import customApi from "@/api/custom.js";
import { getOperatePermissionTreeNodes } from "eem-utils/permission.js";
import common from "eem-utils/common";
import ResetPassword from "eem-components/resetPassword/index.vue";
import TenantTree from "./tenantTree";
export default {
  components: {
    UserView,
    ROOTView,
    ServiceView,
    EnterpriseView,
    UserGroupView,
    EditUserGroup,
    EditService,
    EditEnterprise,
    EditUser,
    ResetPassword,
    TenantTree
  },

  computed: {
    tenantId() {
      var vm = this;
      return vm.$store.state.tenantId;
    },
    userTenantId() {
      var vm = this;
      return vm.$store.state.userInfo.tenantId;
    },
    showButton() {
      return (
        this.currentNode &&
        this.currentNode.modelLabel === "user" &&
        this.$store.state.userInfo.id === 1
      );
    },
    isOneself() {
      return (
        this.currentNode &&
        this.currentNode.modelLabel === "user" &&
        this.$store.state.userInfo.id === this.currentNode.id &&
        this.$store.state.userInfo.id !== 1
      );
    },
    isROOT() {
      return this.currentNode && this.currentNode.id == 1;
    },
    onlyResetPassword() {
      return this.currentNode && this.currentNode.onlyResetPassword;
    },
    userInfo() {
      var vm = this;
      return vm.$store.state.userInfo;
    },
    hidePecReport() {
      return this._.get(
        this.$store.state,
        "systemCfg.platformusermanageTabs.hidePecReport"
      );
    },
    hideMReport() {
      return this._.get(
        this.$store.state,
        "systemCfg.platformusermanageTabs.hideMReport"
      );
    },
    hideOnlyReport() {
      return this._.get(
        this.$store.state,
        "systemCfg.platformusermanageTabs.hideOnlyReport"
      );
    },
    hideVideo() {
      return this._.get(
        this.$store.state,
        "systemCfg.platformusermanageTabs.hideVideo"
      );
    }
  },
  data() {
    return {
      refreshTenantTree: Date.now(),
      refreshTenantTreeData: Date.now(),
      currentNode: null,
      CetButton_enterprise: {
        visible_in: false,
        disable_in: false,
        title: $T("新增项目"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_enterprise_statusTrigger_out
        }
      },
      CetButton_service: {
        visible_in: false,
        disable_in: false,
        title: $T("新增服务商"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_service_statusTrigger_out
        }
      },
      CetButton_user: {
        visible_in: false,
        disable_in: false,
        title: $T("新增用户"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_user_statusTrigger_out
        }
      },
      CetButton_usergroup: {
        visible_in: false,
        disable_in: false,
        title: $T("新增用户组"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_usergroup_statusTrigger_out
        }
      },
      CetButton_edit: {
        visible_in: true,
        disable_in: false,
        title: $T("修改"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_edit_statusTrigger_out
        }
      },
      CetButton_editPassword: {
        visible_in: true,
        disable_in: false,
        title: $T("重置密码"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_editPassword_statusTrigger_out
        }
      },
      CetButton_del: {
        visible_in: true,
        disable_in: false,
        title: $T("删除"),
        plain: true,
        type: "danger",
        event: {
          statusTrigger_out: this.CetButton_del_statusTrigger_out
        }
      },
      activeName: "pecCore",
      // 操作权限树组件
      CetTree_Permissions: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        defaultExpandAll: true,
        event: {}
      },
      // pecCore组件
      CetGiantTree_pecCore: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {}
      },
      CetGiantTree_graph: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "text"
            }
          }
        },
        event: {}
      },
      CetGiantTree_pecReport: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "nodeName"
            }
          }
        },
        event: {}
      },
      CetGiantTree_mReport: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "nodeName"
            }
          }
        },
        event: {}
      },
      CetGiantTree_onlyReport: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "id"
            },
            key: {
              name: "name"
            }
          }
        },
        event: {}
      },
      CetTree_project: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: false,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {}
      },
      CetTree_video: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        event: {}
      },
      CetTable_user: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 10,
          layout: "sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        highlightCurrentRow: false,
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      },
      Columns_user: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "41" //绝对宽度
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("用户名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "mobilePhone", // 支持path a[0].b
          label: $T("移动电话"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "email", // 支持path a[0].b
          label: $T("电子邮箱"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ],
      EditUserGroup: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        currentNode_in: null
      },
      EditService: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        currentNode_in: null
      },
      EditEnterprise: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        currentNode_in: null
      },
      EditUser: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        role_in: null,
        currentNode_in: null
      },
      ResetPassword: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        finishTrigger_out: new Date().getTime(),
        saveData_out: {}
      }
    };
  },
  watch: {},
  methods: {
    init() {
      this.CetTree_Permissions.inputData_in =
        getOperatePermissionTreeNodes(true);
      this.activeName = "";
      this.$nextTick(() => {
        this.activeName = "pecCore";
      });
      this.currentNode = null;
      this.refreshTenantTree = Date.now();
    },
    getUserTreeData() {
      this.refreshTenantTreeData = Date.now();
    },
    CetTree_1_currentNode_out({ val, node }) {
      this.activeName = "";
      this.$nextTick(() => {
        this.activeName = "pecCore";
      });
      this.currentNode = this._.cloneDeep(val);
      let usergroupObj = this._.get(node, "parent.data");
      if (this._.get(usergroupObj, "modelLabel") === "usergroup") {
        this.currentNode.userGroupName = usergroupObj.name;
      }
      this.CetButton_enterprise.visible_in = false;
      this.CetButton_service.visible_in = false;
      this.CetButton_user.visible_in = false;
      this.CetButton_usergroup.visible_in = false;
      this.CetButton_del.visible_in = false;
      this.CetButton_edit.visible_in = false;
      this.handleClick({
        name: "pecCore"
      });
      // 用户组展示当前用户组下的用户
      if (val.modelLabel === "usergroup") {
        this.getGroupToUser();
      }
      if (val.modelLabel === "tenant" && val.id === 1) {
        // 平台
        this.CetButton_usergroup.visible_in =
          this.$checkPermission("usergroup_create");
        this.CetButton_service.visible_in = this.$checkPermission(
          "serviceprovider_create"
        );
      } else if (val.modelLabel === "tenant" && val.type === 0) {
        // 服务商
        this.CetButton_enterprise.visible_in =
          this.$checkPermission("qy_create");
        this.CetButton_usergroup.visible_in =
          this.$checkPermission("usergroup_create");
        // if (this.userInfo.tenantId !== val.id) {
        this.CetButton_del.visible_in = this.$checkPermission(
          "serviceprovider_delete"
        );
        this.CetButton_edit.visible_in = this.$checkPermission(
          "serviceprovider_update"
        );
        if (this.userTenantId === val.tenantId) {
          this.CetButton_del.visible_in = false;
          this.CetButton_edit.visible_in = false;
        }
        // }
      } else if (val.modelLabel === "usergroup") {
        // 用户组
        this.CetButton_user.visible_in = this.$checkPermission("user_create");
        this.CetButton_usergroup.visible_in =
          this.$checkPermission("usergroup_create");
        this.CetButton_del.visible_in =
          this.$checkPermission("usergroup_delete");
        this.CetButton_edit.visible_in =
          this.$checkPermission("usergroup_update");
      } else if (val.modelLabel === "user") {
        // 用户
        this.CetButton_del.visible_in = this.$checkPermission("user_delete");
        this.CetButton_edit.visible_in = this.$checkPermission("user_update");
      } else if (val.modelLabel === "tenant" && val.type === 1) {
        // 项目
        this.CetButton_del.visible_in = this.$checkPermission("qy_delete");
        this.CetButton_edit.visible_in = this.$checkPermission("qy_update");
      }
    },
    // 获取用户组下的用户
    async getGroupToUser() {
      const vm = this;
      if (!vm.currentNode) {
        return;
      }
      let data = vm.currentNode.children || [];
      vm.CetTable_user.data = data.filter(i => i.modelLabel === "user");
      vm.currentNode.userNum = vm.CetTable_user.data.length;
    },
    // 新增项目
    CetButton_enterprise_statusTrigger_out(val) {
      this.EditEnterprise.currentNode_in = this._.cloneDeep(this.currentNode);
      this.EditEnterprise.inputData_in = null;
      this.EditEnterprise.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 新增服务商
    CetButton_service_statusTrigger_out(val) {
      this.EditService.currentNode_in = this._.cloneDeep(this.currentNode);
      this.EditService.inputData_in = null;
      this.EditService.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 新增用户
    CetButton_user_statusTrigger_out(val) {
      this.EditUser.role_in = null;
      this.EditUser.currentNode_in = this._.cloneDeep(this.currentNode);
      this.EditUser.inputData_in = null;
      this.EditUser.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 新增用户组
    CetButton_usergroup_statusTrigger_out(val) {
      this.EditUserGroup.currentNode_in = this._.cloneDeep(this.currentNode);
      this.EditUserGroup.inputData_in = null;
      this.EditUserGroup.visibleTrigger_in = this._.cloneDeep(val);
    },
    handleClick(val) {
      if (!this.currentNode) {
        return;
      }
      switch (val.name) {
        case "pecCore":
          if (
            this.currentNode.modelLabel !== "user" &&
            this.currentNode.modelLabel !== "usergroup"
          ) {
            this.getPecCoreTree();
          }
          break;
        case "project":
          this.getProjectTree();
          break;
        case "Graph":
          this.getGraphTree();
          break;
        case "pecReport":
          this.getPecReportTree_out();
          break;
        case "mReport":
          this.getMReportTree_out();
          break;
        case "onlyReport":
          this.getOnlyReportTree_out();
          break;
        case "video":
          this.getVideoTree_out();
          break;

        default:
          break;
      }
    },
    CetButton_edit_statusTrigger_out(val) {
      switch (this._.get(this.currentNode, "modelLabel")) {
        case "tenant":
          if (this.currentNode.type === 0) {
            this.EditService.currentNode_in = null;
            this.EditService.inputData_in = this._.cloneDeep(this.currentNode);
            this.EditService.visibleTrigger_in = this._.cloneDeep(val);
          } else if (this.currentNode.type === 1) {
            this.EditEnterprise.currentNode_in = null;
            this.EditEnterprise.inputData_in = this._.cloneDeep(
              this.currentNode
            );
            this.EditEnterprise.visibleTrigger_in = this._.cloneDeep(val);
          }
          break;
        case "usergroup":
          this.EditUserGroup.currentNode_in = null;
          this.EditUserGroup.inputData_in = this._.cloneDeep(this.currentNode);
          this.EditUserGroup.visibleTrigger_in = this._.cloneDeep(val);
          break;
        case "user":
          this.EditUser.currentNode_in = null;
          this.EditUser.inputData_in = this._.cloneDeep(this.currentNode);
          this.EditUser.visibleTrigger_in = this._.cloneDeep(val);
          break;
      }
    },
    CetButton_del_statusTrigger_out() {
      var vm = this;
      var node = this.currentNode;
      var deleteFn = "",
        params = {
          id: node.id,
          name: node.name,
          tenantId: node.tenantId
        };

      if (node.children && node.children.length > 0) {
        vm.$message.warning($T("该节点下有子节点不能删除！"));
        return;
      }
      if (node.modelLabel == "user") {
        deleteFn = "deleteAuthCommonUser";
      } else if (node.modelLabel == "usergroup") {
        deleteFn = "deleteAuthCommonUserGroup";
      } else {
        deleteFn = "deleteAuthCommonTenant";
        //缺陷48499，根据春生反馈确认，项目和租户删除的tenantId,获取节点的parentId信息
        params.tenantId = node.parentId;
      }
      if (node) {
        vm.$confirm($T("确定要删除所选节点吗？"), $T("提示"), {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          cancelButtonClass: "btn-custom-cancel",
          type: "warning",
          closeOnClickModal: false,
          showClose: false,
          beforeClose: function (action, instance, done) {
            if (action == "confirm") {
              customApi[deleteFn](params).then(response => {
                if (response.code === 0) {
                  vm.$message.success($T("删除成功！"));
                  instance.confirmButtonLoading = false;
                  vm.currentNode = null;
                  vm.refreshTenantTree = Date.now();
                }
              });
            }

            done();
          },
          callback: function (action) {
            if (action != "confirm") {
              vm.$message({
                type: "info",
                message: $T("取消删除！")
              });
            }
          }
        });
      } else {
        vm.$message.info($T("请选择待操作数据!"));
      }
    },
    CetButton_editPassword_statusTrigger_out() {
      this.ResetPassword.inputData_in = this._.cloneDeep(this.currentNode);
      this.ResetPassword.visibleTrigger_in = new Date().getTime();
    },
    // 按角色过滤角色操作权限树
    filterOperationAuths(role) {
      const vm = this;
      vm.EditUser.role_in = this._.cloneDeep(role);
      const filtedAuths = [];
      if (!role) {
        vm.CetTree_Permissions.filterNodes_in = [];
        return;
      }
      (role.auths || []).forEach(val => {
        filtedAuths.push({
          id: val
        });
      });
      vm.CetTree_Permissions.filterNodes_in = filtedAuths;
    },
    // 获取设备链路层级
    getPecCoreTree() {
      const vm = this;
      if (!vm.currentNode) {
        return;
      }
      var queryData = {
        loadDevice: true,
        nodeId: 0,
        nodeType: 0,
        async: false,
        tenantId: vm.currentNode.id
      };
      customApi.getPecCoreMeterTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []) || [];
          vm.setTreeLeaf(data);
          this.CetGiantTree_pecCore.inputData_in = data;
        } else {
          this.CetGiantTree_pecCore.inputData_in = [];
        }
      });
    },
    setTreeLeaf(nodes) {
      if (nodes && nodes.length > 0) {
        nodes.forEach(item => {
          item.name = item.text;
          item.id = item.nodeId;
          item.modelLabel = item.nodeType;
          item.tree_id = item.modelLabel + "_" + item.id;
          this.setTreeLeaf(this._.get(item, "children", []));
        });
      }
    },
    // 获取用户项目节点权限
    getProjectTree() {
      var queryData = {
        tenantId: this.currentNode.tenantId,
        userId: this.currentNode.id
      };
      customApi.getEnterpriseTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data[0].children", []);
          this.CetTree_project.inputData_in = data;
        } else {
          this.CetTree_project.inputData_in = [];
        }
      });
    },
    // 获取图形节点权限
    getGraphTree() {
      var queryData = {
        tenantId: this.currentNode.id
      };
      if (this._.get(this.currentNode, "modelLabel") === "user") {
        queryData = {
          tenantId: this.currentNode.tenantId,
          userId: this.currentNode.id
        };
      }
      customApi.getGraphTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_graph.inputData_in = this.formatGraphNodeTree(data);
        } else {
          this.CetGiantTree_graph.inputData_in = [];
        }
      });
    },
    formatGraphNodeTree(rawNodeAry) {
      var me = this;
      rawNodeAry = rawNodeAry || [];

      me._(rawNodeAry).forEach(function (rawNode) {
        rawNode.id = rawNode.nodeId;
        if (!rawNode.children || !rawNode.children.length) {
          rawNode.children = null;
          rawNode.leaf = true;
        } else {
          rawNode.leaf = false;
          rawNode.children = me.formatGraphNodeTree(rawNode.children);
        }
        rawNode.tree_id = `${rawNode.nodeType}_${rawNode.id}`;
      });
      return rawNodeAry;
    },
    // 获取报表节点权限
    getPecReportTree_out() {
      var queryData = {
        tenantId: this.currentNode.id
      };
      if (this._.get(this.currentNode, "modelLabel") === "user") {
        queryData = {
          tenantId: this.currentNode.tenantId,
          userId: this.currentNode.id
        };
      }
      customApi.getPecReportTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_pecReport.inputData_in =
            this.formatReporthNodeTree(data);
        } else {
          this.CetGiantTree_pecReport.inputData_in = [];
        }
      });
    },
    getMReportTree_out() {
      var queryData = {
        tenantId: this.currentNode.id
      };
      if (this._.get(this.currentNode, "modelLabel") === "user") {
        queryData = {
          tenantId: this.currentNode.tenantId,
          userId: this.currentNode.id
        };
      }
      customApi.getMReportTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_mReport.inputData_in =
            this.formatReporthNodeTree(data);
        } else {
          this.CetGiantTree_mReport.inputData_in = [];
        }
      });
    },
    getOnlyReportTree_out() {
      var queryData = {
        tenantId: this.currentNode.id
      };
      if (this._.get(this.currentNode, "modelLabel") === "user") {
        queryData = {
          tenantId: this.currentNode.tenantId,
          userId: this.currentNode.id
        };
      }
      customApi.getOnlyReportTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_onlyReport.inputData_in = data;
        } else {
          this.CetGiantTree_onlyReport.inputData_in = [];
        }
      });
    },
    getVideoTree_out() {
      var queryData = {
        tenantId: this.currentNode.id
      };
      if (this._.get(this.currentNode, "modelLabel") === "user") {
        queryData = {
          tenantId: this.currentNode.tenantId,
          userId: this.currentNode.id
        };
      }
      customApi.videoQueryFolders(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetTree_video.inputData_in = data;
        } else {
          this.CetTree_video.inputData_in = [];
        }
      });
    },
    formatReporthNodeTree(rawNodeAry) {
      var me = this;
      rawNodeAry = rawNodeAry || [];

      me._(rawNodeAry).forEach(function (rawNode) {
        rawNode.id = rawNode.nodeId;
        if (!rawNode.children || !rawNode.children.length) {
          rawNode.children = null;
          rawNode.leaf = true;
        } else {
          rawNode.leaf = false;
          rawNode.children = me.formatReporthNodeTree(rawNode.children);
        }
        rawNode.tree_id = `${rawNode.nodeType}_${rawNode.id}`;
      });
      return rawNodeAry;
    }
  },
  activated() {
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.eem-common {
  display: flex;
  .treeBox {
    width: 316px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    @include border_radius(C);
    .CetTree {
      flex: 1;
      min-height: 0;
    }
    .btnBox {
      height: mh-get(Hm);
    }
  }
  .contentBox {
    flex: 1;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    min-width: 0;
    .mainContent {
      border-radius: mh-get(C) mh-get(C) 0 0;
      @include padding(J3 J4 J3 J4);
      .title {
        @include font_size(H1);
        @include line_height(Hm);
        font-weight: bold;
        display: flex;
        .name {
          @include font_size(H1);
          @include line_height(Hm);
          font-weight: bold;
          flex: 1;
        }
      }
      &.borderRadiusC {
        @include border_radius(C);
      }
    }
    .tabs {
      border-radius: 0 0 mh-get(C) mh-get(C);
    }
    .detailContent {
      flex: 1;
      min-height: 0;
      box-sizing: border-box;
      @include border_radius(C);
      .CetTree {
        width: 100%;
        :deep(.device-search) {
          width: 240px;
        }
      }
      .pecCoreTree {
        overflow: auto;
        height: 100%;
      }
    }
  }
}
.delete:hover {
  @include font_color(F2);
}
</style>
