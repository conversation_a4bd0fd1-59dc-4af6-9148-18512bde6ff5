<template>
  <div>
    <CetDialog
      v-bind="CetDialog_add"
      v-on="CetDialog_add.event"
      class="CetDialog"
    >
      <CetForm
        :data.sync="CetForm_add.data"
        v-bind="CetForm_add"
        v-on="CetForm_add.event"
        class="eem-cont-c1"
      >
        <el-row :gutter="$J3">
          <el-col :span="12">
            <el-form-item label="事件类型" prop="eventType">
              <div
                v-if="eventType_in && eventType_in.eventClassified"
                class="eventType"
              >
                {{ eventType_in.eventClassifiedName }}
              </div>
              <ElSelect
                v-else
                v-model="CetForm_add.data.eventType"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in ElOption_1.options_in"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="场景选择" prop="scenariosId">
              <ElSelect
                v-model="CetForm_add.data.scenariosId"
                v-bind="ElSelect_2"
                v-on="ElSelect_2.event"
              >
                <ElOption
                  v-for="item in ElOption_2.options_in"
                  :key="item[ElOption_2.key]"
                  :label="item[ElOption_2.label]"
                  :value="item[ElOption_2.value]"
                  :disabled="item[ElOption_2.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="处理预案"
              style="max-height: 380px; overflow: auto"
            >
              <div
                v-for="(item, index) in CetForm_add.data.reservePlan"
                :key="index"
              >
                <div class="pl10 reservePlanTitle">
                  处理预案{{ index + 1 }}
                  <span v-if="index" class="planDle" @click="planDle(index)">
                    删除
                  </span>
                </div>
                <el-form-item
                  label="预案名称"
                  :prop="`reservePlan[${index}].name`"
                  class="reservePlanTitle"
                  :rules="{
                    required: true,
                    message: '预案名称不能为空',
                    trigger: ['blur', 'change']
                  }"
                >
                  <ElInput
                    v-model.trim="CetForm_add.data.reservePlan[index].name"
                    v-bind="ElInput_1"
                    v-on="ElInput_1.event"
                  ></ElInput>
                </el-form-item>
                <el-form-item
                  label="操作步骤"
                  :prop="`reservePlan[${index}].solution`"
                  class="reservePlanTitle"
                  :rules="{
                    required: true,
                    message: '操作步骤不能为空',
                    trigger: ['blur', 'change']
                  }"
                >
                  <ElInput
                    v-model="CetForm_add.data.reservePlan[index].solution"
                    v-bind="ElInput_2"
                    v-on="ElInput_2.event"
                  ></ElInput>
                </el-form-item>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div @click="addPlan" class="addPlan">
              <div class="img"></div>
              <div class="text">新增预案</div>
            </div>
          </el-col>
        </el-row>
      </CetForm>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="mlJ"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import custom from "@/api/custom";
export default {
  name: "addExpertKnowledge",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Array
    },
    eventType_in: {
      type: Object
    },
    scenariosList_in: {
      type: Array
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.localStorage) {
          return false;
        } else {
          var storage = window.localStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },
  data() {
    return {
      // add弹窗组件
      CetDialog_add: {
        title: "新增专家知识",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_add_open_out,
          close_out: this.CetDialog_add_close_out
        },
        width: "500px",
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "保存",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_add: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {
          eventType: null,
          scenariosId: null,
          reservePlan: [
            {
              name: "",
              solution: ""
            },
            {
              name: "",
              solution: ""
            }
          ]
        },
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        labelPosition: "top",
        rules: {
          eventType: [
            {
              required: true,
              message: "请选择事件类型",
              trigger: ["blur", "change"]
            }
          ],
          scenariosId: [
            {
              required: true,
              message: "请选择场景",
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_add_currentData_out,
          saveData_out: this.CetForm_add_saveData_out,
          finishData_out: this.CetForm_add_finishData_out,
          finishTrigger_out: this.CetForm_add_finishTrigger_out
        }
      },
      ElSelect_1: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElSelect_2: {
        value: "",
        style: {
          width: "100%"
        },
        filterable: true,
        event: {}
      },
      ElOption_1: {
        options_in: [],
        key: "eventClassified",
        value: "eventClassified",
        label: "eventClassifiedName",
        disabled: "disabled"
      },
      ElOption_2: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElInput_1: {
        value: "",
        placeholder: "请输入预案名称",
        style: {
          width: "100%"
        },
        maxlength: 20,
        event: {}
      },
      ElInput_2: {
        value: "",
        placeholder: "请输入详细操作步骤（必填）",
        style: {
          width: "100%"
        },
        type: "textarea",
        rows: "4",
        event: {}
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.openTrigger_in = val;
      this.CetForm_add.data = {
        eventType: null,
        scenariosId: null,
        reservePlan: [
          {
            name: "",
            solution: ""
          }
        ]
      };
      this.CetForm_add.resetTrigger_in = new Date().getTime();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    },
    inputData_in(val) {
      // 事件和场景的传入
      if (val && val.length > 0) {
        this.ElOption_1.options_in = val.filter(
          item => item.faultScenariosList && item.faultScenariosList.length > 0
        );
        this.CetForm_add.data.eventType =
          this.ElOption_1.options_in[0].eventClassified;
        this.ElSelect_1_change_out(this.CetForm_add.data.eventType);
      } else {
        this.ElOption_1.options_in = [];
      }
    },
    eventType_in(val) {
      if (this._.isEmpty(val)) {
        this.CetDialog_add.title = "新增专家知识";
      } else {
        this.CetDialog_add.title = "新增场景";
        this.CetForm_add.data.eventType = val.eventClassified;
        this.ElOption_2.options_in = this.scenariosList_in;
        this.CetForm_add.data.scenariosId = this.ElOption_2.options_in[0].id;
      }
    }
  },

  methods: {
    ElSelect_1_change_out(val) {
      if (!val) {
        return;
      }
      this.ElOption_2.options_in = this.ElOption_1.options_in.filter(
        item => item.eventClassified == val
      )[0].faultScenariosList;
      this.CetForm_add.data.scenariosId = this.ElOption_2.options_in[0].id;
    },
    CetDialog_add_open_out(val) {},
    CetDialog_add_close_out(val) {},
    CetForm_add_currentData_out(val) {},
    CetForm_add_saveData_out(val) {
      var data = val.reservePlan.map(item => {
        return {
          name: item.name,
          solution: item.solution,
          adoptnumber: 0,
          projectid: this.projectId
        };
      });
      custom.addEventPlan(data, val.scenariosId).then(response => {
        if (response.code === 0) {
          this.$message({
            message: "保存成功！",
            type: "success"
          });
          this.$emit("confirm_out");
          this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
        }
      });
    },
    CetForm_add_finishData_out(val) {},
    CetForm_add_finishTrigger_out(val) {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_add.localSaveTrigger_in = this._.cloneDeep(val);
    },
    addPlan() {
      this.CetForm_add.data.reservePlan.push({
        name: "",
        solution: ""
      });
    },
    planDle(index) {
      this.CetForm_add.data.reservePlan.splice(index, 1);
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.reservePlanTitle {
  font-size: 12px;
  // color: #666;
  :deep(.el-form-item__label) {
    font-size: 12px;
    // color: #666;
  }
}
.eventType {
  // color: #333333;
  font-size: 18px;
  font-weight: bold;
}
.addPlan {
  width: 90px;
  height: 70px;
  border: 1px dashed #666;
  border-radius: 5px;
  text-align: center;
  position: relative;
  cursor: pointer;
  // margin-left: 160px;
  .img {
    height: 24px;
    width: 24px;
    background: url("../assets/u2636.png") no-repeat center center;
    background-size: 24px 24px;
    position: absolute;
    top: 10px;
    left: 30px;
  }
  .text {
    position: absolute;
    top: 40px;
    left: 15px;
  }
}
.planDle {
  float: right;
  color: red;
  font-size: 12px;
  cursor: pointer;
}
.CetDialog {
  :deep(.el-dialog__body) {
    @include padding(J1);
    @include background_color(BG);
  }
}
</style>
