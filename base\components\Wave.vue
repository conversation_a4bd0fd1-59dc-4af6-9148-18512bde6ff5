<template>
  <div style="height: 100%">
    <cetWaveform
      class="bg1"
      :comtradeData="comtradeData"
      :title="title"
      :langCode="langCode"
      :echartTheme="echartTheme"
    ></cetWaveform>
  </div>
</template>
<script>
import { httping } from "@omega/http";
import { themeMap } from "cet-chart";

export default {
  components: {},
  props: {
    data: {
      type: [Object],
      default: () => ({})
    },
    title: {
      type: String
    }
  },
  computed: {
    swfId() {
      return "WaveViewCtrl" + new Date().getTime();
    },
    eventTime() {
      var me = this;
      if (!me.data) {
        return "";
      }
      return me.data.eventtime || me.data.eventTime;
    },
    echartTheme() {
      const theme = localStorage.getItem("omega_theme");
      const waveformTheme = `waveform-${theme}`;
      return themeMap.has(waveformTheme) ? waveformTheme : theme;
    }
  },
  watch: {
    //外部输入数据
    data: {
      deep: true,
      handler() {
        this.getWaveData_out(data => {
          this.comtradeAnalysisByData(data);
        });
      }
    }
  },
  data() {
    return {
      comtradeData: null,
      langCode: "CN"
    };
  },
  methods: {
    formatTime(val) {
      if (!val) return val;
      return (val / 1000).toFixed(3);
    },
    getWaveData_out(callback) {
      const me = this;
      if (!me.data.deviceId || !me.data.waveTime) {
        me.comtradeData = [];
        return;
      }
      const url = `device-data-service/api/wave/v1/data/moment/${me.data.deviceId}?waveTime=${me.data.waveTime}`;
      httping({
        url: url,
        method: "GET",
        headers: { hideNotice: true }
      }).then(
        res => {
          if (callback) {
            callback(res.data);
          }
        },
        () => {}
      );
    },
    comtradeAnalysisByData(waveArray) {
      this.comtradeData = waveArray;
    }
  }
};
</script>
<style lang="scss" scope></style>
