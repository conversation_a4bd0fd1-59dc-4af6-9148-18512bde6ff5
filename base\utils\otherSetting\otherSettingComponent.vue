<template>
  <el-card class="image-setting">
    <div slot="header" class="image-setting-header">
      {{ "登录页面资源配置" }}
      <div>
        <el-popover trigger="click" popper-class="setting-popover">
          <loginPages ref="loginPages" :setting="form"></loginPages>
          <el-button slot="reference" type="primary">登录界面预览</el-button>
        </el-popover>
      </div>
    </div>
    <el-form
      class="image-setting-form"
      :model="form"
      label-position="top"
      ref="form"
      label-width="200px"
    >
      <el-form-item label="项目名称字体大小">
        <el-input-number
          v-model="form.login_logo_title_size"
          :controls="false"
          :min="12"
          :max="50"
        ></el-input-number>
      </el-form-item>
      <el-form-item label="背景底色">
        <t-color-picker v-model="form.login_background" :recent-colors="null" />
      </el-form-item>
      <el-form-item label="标题图标">
        <ImageUploader v-model="form.project_logo_ico" />
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script>
import { ImageUploader } from "@omega/admin";
import loginPages from "./previewLogin.vue";
// 引入组件库的少量全局样式变量
import "tdesign-vue/es/style/index.css";

export default {
  name: "customeSetting",
  props: ["setting"],
  components: {
    ImageUploader,
    loginPages
  },
  model: {
    prop: "setting",
    event: "change"
  },
  computed: {
    themeLight() {
      return localStorage.getItem("omega_theme") === "light";
    }
  },
  data() {
    return {
      form: {
        // i18n: false,
        // 登录界面登录图标下面的字体大小
        login_logo_title_size: undefined,
        // 登录界面的背景底色
        login_background: null,
        // 项目标题图标展示
        project_logo_ico: ""
      }
    };
  },
  watch: {
    form: {
      handler: function (val) {
        this.$emit("change", { ...val });
      },
      deep: true
    },
    setting: {
      handler: function (val, oldVal) {
        Object.assign(this.form, val);
      },
      deep: true
    }
  },
  created() {
    // 第三方组件颜色选择器的主题切换效果
    if (this.themeLight) {
      document.documentElement.removeAttribute("theme-mode");
    } else {
      document.documentElement.setAttribute("theme-mode", "dark");
    }
  }
};
</script>

<style lang="scss" scoped>
.image-setting {
  margin: 20px;
}
.image-setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
<style>
.setting-popover {
  transform: scale(0.8);
}
</style>
