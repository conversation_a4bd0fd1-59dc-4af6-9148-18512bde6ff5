<template>
  <div class="page eem-common">
    <el-container class="fullheight flex-column">
      <div style="flex: 1" class="minWH eem-container">
        <div class="mbJ3" style="height: 32px">
          <span class="common-title-H2">{{ $T("成本构成") }}</span>
          <CetButton
            class="fr"
            :disable_in="currentSchemeItem ? false : true"
            v-bind="CetButton_1"
            v-on="CetButton_1.event"
            v-permission="'costcheckitem_edit'"
          ></CetButton>
        </div>
        <CetTable
          style="height: calc(100% - 48px)"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <template v-for="item in Columns_scheme">
            <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
          </template>
          <ElTableColumn v-bind="ElTableColumn_edit">
            <template slot-scope="scope">
              <span
                class="handel fl mrJ3"
                @click.stop="handleEdit(scope.$index, scope.row)"
                v-permission="'costcheckitem_edit'"
              >
                {{ $T("编辑") }}
              </span>
              <span
                class="handel delete fl mrJ3"
                @click.stop="handleDelete(scope.$index, scope.row)"
                v-permission="'costcheckitem_edit'"
              >
                {{ $T("删除") }}
              </span>
            </template>
          </ElTableColumn>
        </CetTable>
      </div>
    </el-container>
    <AddConstComposition
      :visibleTrigger_in="AddConstComposition.visibleTrigger_in"
      :closeTrigger_in="AddConstComposition.closeTrigger_in"
      :queryId_in="AddConstComposition.queryId_in"
      :inputData_in="AddConstComposition.inputData_in"
      @finishTrigger_out="AddConstComposition_finishTrigger_out"
      @finishData_out="AddConstComposition_finishData_out"
      @saveData_out="AddConstComposition_saveData_out"
      @currentData_out="AddConstComposition_currentData_out"
      :basicfeerateFlag="basicfeerateFlag"
      :powertarifffeerateFlag="powertarifffeerateFlag"
    />
  </div>
</template>
<script>
import common from "eem-utils/common";
import AddConstComposition from "./dialog/AddConstComposition.vue";
import { httping } from "@omega/http";
export default {
  name: "CostComposition",
  components: {
    AddConstComposition
  },
  props: ["currentSchemeItem", "allTypeList"],

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },

  data() {
    return {
      // 项目能源类型
      energyTypeArr: [],
      basicfeerateFlag: false,
      powertarifffeerateFlag: false,
      currentTabItem: null,
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: $T("添加成本构成"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {
          // width: "200px"
        },
        event: {
          change: this.ElInput_1_change_out,
          input: this.ElInput_1_input_out
        }
      },
      AddConstComposition: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      Columns_scheme: [
        {
          type: "index", // selection 勾选 index 序号
          label: $T("序号"),
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "60"
        },
        {
          prop: "constName", // 支持path a[0].b
          label: $T("成本项名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol(),
          width: "150"
        },
        {
          prop: "energytype$text", // 支持path a[0].b
          label: $T("能源类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "feeratetype$text", // 支持path a[0].b
          label: $T("费用类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("费率方案"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "feesubratetype$text", // 支持path a[0].b
          label: $T("费率类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "costcheckitem$text", // 支持path a[0].b
          label: $T("成本计算项"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ],
      ElTableColumn_edit: {
        //type: "",      // selection 勾选 index 序号
        //  prop: "",      // 支持path a[0].b
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "130" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      }
    };
  },
  watch: {
    "CetTable_1.data": {
      handler: function (val) {
        this.basicfeerateFlag = false;
        this.powertarifffeerateFlag = false;
        if (val && val.length > 0) {
          val.forEach(item => {
            if (item.feeratetype === 1) {
              // 电度电费
              this.basicfeerateFlag = true;
            } else if (item.feeratetype === 3) {
              // 力调电费
              this.powertarifffeerateFlag = true;
            }
          });
        }
      },
      deep: true
    },
    currentSchemeItem: {
      deep: true,
      handler(val) {
        if (val) {
          this.getSchemeDetail(val.id);
        } else {
          this.CetTable_1.data = [];
        }
      }
    }
  },

  methods: {
    AddConstComposition_currentData_out(val) {},
    AddConstComposition_finishData_out(val) {
      const arr = this._.cloneDeep(this.CetTable_1.data);
      if (val.edit) {
        arr.forEach(item => {
          if (item.id === val.id) {
            item.costcheckitem = val.costcheckitem;
            item.constName = val.constName;
          }
        });
      } else {
        // 新增成本项
        arr.push(val);
      }
      // 验证成本项名称不能重复
      const target = arr.filter(item => item.constName === val.constName);
      if (target.length > 1) {
        return this.$message({
          message: $T("成本项名称重复"),
          type: "warning"
        });
      }
      var data = {
        costcheckitem_model: [],
        createtime: this.$moment().valueOf(),
        id: this.currentSchemeItem.id,
        name: this.currentSchemeItem.name,
        projectid: this.projectId
      };
      arr.forEach(item => {
        data.costcheckitem_model.push({
          energyType: item.energytype,
          feeRateType: item.feeratetype,
          feescheme_id: item.feescheme_id,
          id: item.id,
          name: item.constName,
          costcheckitem: item.costcheckitem
        });
      });
      httping({
        url: `/eem-service/v1/schemeConfig/costCheckPlan`,
        method: "PUT",
        data
      }).then(response => {
        if (response.code === 0) {
          this.$message({
            message: $T("保存成功"),
            type: "success"
          });
          this.AddConstComposition.closeTrigger_in = new Date().getTime();
          this.getSchemeDetail(this.currentSchemeItem.id);
        }
      });
    },
    AddConstComposition_finishTrigger_out(val) {},
    AddConstComposition_saveData_out(val) {},
    CetButton_1_statusTrigger_out(val) {
      this.AddConstComposition.inputData_in = {};
      this.AddConstComposition.visibleTrigger_in = this._.cloneDeep(val);
    },
    ElInput_1_change_out(val) {},
    ElInput_1_input_out(val) {},
    CetTable_1_outputData_out(val) {},
    CetTable_1_record_out(val) {
      if (val.id !== -1) {
        this.currentTabItem = val;
      } else {
        this.currentTabItem = null;
      }
    },
    handleEdit(index, row) {
      this.AddConstComposition.inputData_in = this._.cloneDeep(
        Object.assign(row, {
          edit: true
        })
      );
      this.AddConstComposition.visibleTrigger_in = new Date().getTime();
    },
    handleDelete(index, row) {
      var vm = this;
      vm.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        beforeClose: function (action, instance, done) {
          if (action === "confirm") {
            httping({
              url: `/eem-service/v1/schemeConfig/costCheckPlan/component`,
              method: "DELETE",
              data: [row.id]
            }).then(response => {
              if (response.code === 0) {
                this.$message({
                  message: $T("删除成功"),
                  type: "success"
                });
                vm.getSchemeDetail(vm.currentSchemeItem.id);
              }
            });
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action !== "confirm") {
            vm.$message({
              type: "info",
              message: $T("取消删除！")
            });
          }
        }
      });
    },
    getSchemeDetail(id) {
      httping({
        url: `/eem-service/v1/schemeConfig/costCheckPlan/plan/${id}`,
        method: "GET"
      }).then(response => {
        this.CetTable_1.data = [];
        if (response.code !== 0) return;
        const costcheckitem = response.data.costcheckitem_model;
        this.$emit("getCostcheckitem", costcheckitem);
        if (costcheckitem && costcheckitem.length > 0) {
          costcheckitem.forEach(item => {
            // 能源类型
            const target = this.allTypeList.find(
              type => type.energyType === item.energyType
            );
            item.energytype$text = target && target.energyTypeName;
            if (
              target &&
              target.feeRateTypesList &&
              target.feeRateTypesList.length
            ) {
              // 费用类型
              const feeRateTarget = target.feeRateTypesList.find(
                fee => fee.feeRateType === item.feeRateType
              );
              item.feeratetype$text =
                feeRateTarget && feeRateTarget.feeRateTypeName;
              // 费率类型
              if (
                feeRateTarget &&
                feeRateTarget.feeRateSubTypesList &&
                feeRateTarget.feeRateSubTypesList.length
              ) {
                const feeRateSubTarget = feeRateTarget.feeRateSubTypesList.find(
                  fee => fee.feeRateSubType === item.feeratesubtype
                );
                item.feesubratetype$text =
                  feeRateSubTarget && feeRateSubTarget.feeRateSubTypeName;
              }
            }
          });
          this.CetTable_1.data = response.data.costcheckitem_model;
        }
        this.CetTable_1.data.forEach(item => {
          item.energytype = item.energyType;
          item.constName = item.name;
          item.name = item.feeSchemeName;
          item.feeratetype = item.feeRateType;
          item.costcheckitem$text =
            JSON.parse(item.costcheckitem)
              .calculateitem.join("、")
              .replace(1, $T("能耗"))
              .replace(2, $T("损耗"))
              .replace(3, $T("分摊")) || "--";
        });
      });
    }
  },
  created: function () {},
  mounted: function () {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.handel {
  cursor: pointer;
  @include font_color(ZS);
  &.delete {
    @include font_color(Sta3);
  }
}
</style>
