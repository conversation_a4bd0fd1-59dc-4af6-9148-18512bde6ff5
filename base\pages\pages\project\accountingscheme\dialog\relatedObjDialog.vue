<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="CetDialog min"
    >
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            class="mlJ"
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <CetForm
        :data.sync="CetForm_pagedialog.data"
        v-bind="CetForm_pagedialog"
        v-on="CetForm_pagedialog.event"
        style="height: 400px"
        class="eem-cont-c1"
      >
        <el-checkbox v-model="checked" @change="checkedChange" class="mbJ1">
          {{ $T("默认选中子节点") }}
        </el-checkbox>
        <el-main style="height: calc(100% - 32px); padding: 0px">
          <CetGiantTree
            class="giantTree"
            ref="giantTree1"
            v-show="!checked"
            v-bind="CetGiantTree_1"
            v-on="CetGiantTree_1.event"
          ></CetGiantTree>
          <CetGiantTree
            class="giantTree"
            ref="giantTree2"
            v-show="checked"
            v-bind="CetGiantTree_2"
            v-on="CetGiantTree_2.event"
          ></CetGiantTree>
        </el-main>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
import Vue from "vue";
import TREE_PARAMS from "@/store/treeParams.js";
import { httping } from "@omega/http";

export default {
  name: "relatedObjDialog",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    currentTabItem: {
      type: Object
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("关联对象"),
        "show-close": true,
        "destroy-on-close": true,
        width: "500px",
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "static", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        rules: {},
        event: {
          currentData_out: this.CetForm_pagedialog_currentData_out,
          saveData_out: this.CetForm_pagedialog_saveData_out,
          finishData_out: this.CetForm_pagedialog_finishData_out,
          finishTrigger_out: this.CetForm_pagedialog_finishTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      checked: false,
      checkNodes: [],
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: this.setNodeClasses
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out //勾选节点输出
        }
      },
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: this.setNodeClasses
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_2_checkedNodes_out //勾选节点输出
        }
      },
      filterCheckData: []
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.checked = false;
      this.getTreeData();
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {
      this.CetForm_pagedialog.queryId_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      this.CetForm_pagedialog.inputData_in = this._.cloneDeep(val);
    }
  },
  methods: {
    setNodeClasses(treeId, treeNode) {
      return treeNode.disabledClass
        ? { add: ["disabledClass"] }
        : { remove: ["disabledClass"] };
    },
    checkedChange() {
      const vm = this;
      let checkNodes = vm._.cloneDeep(vm.checkNodes);
      setTimeout(() => {
        $(this.$refs.giantTree1.$el).find(".ztree").scrollTop(0);
        $(this.$refs.giantTree2.$el).find(".ztree").scrollTop(0);
        if (vm.checked) {
          vm.CetGiantTree_2.checkedNodes = checkNodes;
          if (!checkNodes.length) {
            vm.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
          }
        } else {
          vm.CetGiantTree_1.checkedNodes = checkNodes;
          if (!checkNodes.length) {
            vm.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
          }
        }
      }, 0);
    },
    CetForm_pagedialog_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_saveData_out(val) {
      this.$emit("saveData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishData_out(val) {
      this.$emit("finishData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishTrigger_out(val) {
      this.$emit("finishTrigger_out", val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetForm_pagedialog.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      // 过滤掉禁用的节点
      const filterData = this.checkNodes.filter(
        item =>
          !this.filterCheckData.some(
            ite =>
              item.id === ite.objectid && item.modelLabel === ite.objectlabel
          )
      );
      const data = filterData.map(item => {
        return {
          id: item.id,
          modelLabel: item.modelLabel,
          name: item.name
        };
      });
      httping({
        url: `/eem-service/v1/schemeConfig/costCheckNodeConfig?schemeId=${this.currentTabItem.id}`,
        method: "PUT",
        data
      }).then(response => {
        if (response.code === 0) {
          this.$message({
            message: $T("保存成功"),
            type: "success"
          });
          this.$emit("refreshData");
          this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
        }
      });
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    CetGiantTree_1_checkedNodes_out(val) {
      this.checkNodes = this._.cloneDeep(val);
    },
    CetGiantTree_2_checkedNodes_out(val) {
      this.checkNodes = this._.cloneDeep(val);
    },
    // 获取节点树
    getTreeData() {
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.relatedObj,
        treeReturnEnable: true
      };
      httping({
        url: "/eem-service/v1/node/nodeTree/simple",
        method: "POST",
        data
      }).then(res => {
        if (res.code === 0) {
          _this.getSchemeObj(res.data);
        }
      });
    },
    // 获取所有已关联的节点
    getSchemeObjAll(fn) {
      httping({
        url: `/eem-service/v1/schemeConfig/costCheckNodeConfig/project/${this.projectId}`,
        method: "GET"
      }).then(response => {
        if (response.code === 0 && response.data.length > 0) {
          fn(response.data);
        } else {
          fn([]);
        }
      });
    },
    // 获取方案的关联对象
    getSchemeObj(treeData) {
      this.checkNodes = [];
      this.getSchemeObjAll(data => {
        httping({
          url: `/eem-service/v1/schemeConfig/costCheckNodeConfig/${this.currentTabItem.id}`,
          method: "GET"
        }).then(response => {
          if (response.code === 0 && response.data.length > 0) {
            const nodes = response.data.map(item => {
              return {
                id: item.objectid,
                modelLabel: item.objectlabel,
                tree_id: item.objectlabel + "_" + item.objectid
              };
            });
            this.CetGiantTree_1.checkedNodes = this._.cloneDeep(nodes);
            this.checkNodes = nodes;
            setTimeout(() => {
              this.expandNode(nodes, "tree_id", this.$refs.giantTree1.ztreeObj);
              this.expandNode(nodes, "tree_id", this.$refs.giantTree2.ztreeObj);
            }, 0);

            // 设置节点禁用
            var newData = data.filter(
              item =>
                !response.data.some(
                  ite =>
                    item.objectid === ite.objectid &&
                    item.objectlabel === ite.objectlabel
                )
            );
            this.filterCheckData = this._.cloneDeep(newData);
            this.setTreeLeaf(treeData, newData);
          } else {
            this.filterCheckData = this._.cloneDeep(data);
            this.CetGiantTree_1.checkedNodes = [];
            this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
            this.setTreeLeaf(treeData, data);
          }
          this.CetGiantTree_1.inputData_in = treeData;
          this.CetGiantTree_2.inputData_in = treeData;
        });
      });
    },
    // 展开节点
    expandNode(nodes, key, ztreeObj) {
      nodes.forEach(item => {
        let node = ztreeObj.getNodeByParam(key, item[key]);
        let parentNodes = [],
          parentNode = node && node.getParentNode();
        while (parentNode) {
          parentNodes.push(parentNode);
          parentNode = parentNode.getParentNode();
        }
        parentNodes.forEach(i => {
          ztreeObj.expandNode(i, true);
        });
      });
      $(this.$refs.giantTree1.$el).find("#giantTree").scrollTop(0);
      $(this.$refs.giantTree2.$el).find("#giantTree").scrollTop(0);
    },
    setTreeLeaf(nodesAll, nodes) {
      if (!this._.isArray(nodesAll)) return;
      nodesAll.forEach(item => {
        var flag = false;
        nodes.forEach(ite => {
          if (item.id === ite.objectid && item.modelLabel === ite.objectlabel) {
            flag = true;
          }
        });
        if (flag) {
          Vue.set(item, "disabledClass", true);
        } else {
          Vue.set(item, "disabledClass", false);
        }
        this.setTreeLeaf(this._.get(item, "children", []) || [], nodes);
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.giantTree {
  :deep(.disabledClass) {
    position: relative;
    &::after {
      content: "";
      position: absolute;
      background: #969696 no-repeat center center;
      background-size: 100% 100%;
      height: 14px;
      left: -22px;
      top: 4px;
      width: 14px;
      z-index: 1;
      cursor: no-drop;
    }
    .node_name {
      position: relative;
      &::before {
        content: "";
        position: absolute;
        background: #969696 no-repeat center center;
        background-size: 100% 100%;
        height: 14px;
        left: -22px;
        top: 5px;
        width: 14px;
        cursor: no-drop;
        z-index: 1;
      }
    }
  }
}
.CetDialog {
  :deep(.el-dialog__body) {
    @include padding(J1);
    @include background_color(BG);
  }
}
</style>
