<template>
  <div>
    <CetDialog
      class="CetDialog"
      ref="CetDialog"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
      :title="title"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="formData"
        label-width="120px"
        class="eem-cont-c1"
      >
        <el-form-item label="方案名称" prop="name">
          <ElInput v-model="formData.name" placeholder="请输入"></ElInput>
        </el-form-item>
        <el-form-item label="事件等级" prop="alarmlevel">
          <ElSelect
            v-model="formData.alarmlevel"
            style="width: 100%"
            placeholder="请选择"
          >
            <ElOption
              v-for="item in alarmlevelOptions"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            ></ElOption>
          </ElSelect>
        </el-form-item>
        <el-form-item prop="waterfloodcrossing" class="flex-column">
          <template #label>
            注水越限百分比
            <el-tooltip
              content="累注量和配注量差值和配注量的越限百分比"
              placement="bottom"
            >
              <i class="z-[9999] rotate-180 el-icon-warning"></i>
            </el-tooltip>
          </template>

          <ElInputNumber
            placeholder="请输入"
            v-bind="inputNumber"
            v-model="formData.waterfloodcrossing"
          ></ElInputNumber>
          <span class="form-item-unit">%</span>
        </el-form-item>
        <el-form-item class="flex-column">
          <template #label>
            油压干压差越限值
            <el-tooltip content="干压和油压差值的阈值" placement="bottom">
              <i class="z-[9999] rotate-180 el-icon-warning"></i>
            </el-tooltip>
          </template>

          <ElInputNumber
            v-bind="inputNumber"
            placeholder="请输入"
            v-model="formData.differencevalue"
          ></ElInputNumber>
          <span class="form-item-unit">MPa</span>
        </el-form-item>
      </el-form>

      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom";
export default {
  name: "addOrEditCondition",
  props: {
    visibleTrigger_in: Number,
    inputData_in: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      CetDialog_1: {
        width: "480px",
        title: "",
        showClose: true,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        "append-to-body": true,
        event: {}
      },
      inputNumber: {
        style: {
          width: "100%"
        },
        ...common.check_numberFloat,
        controls: false
      },
      formData: {
        name: undefined,
        alarmlevel: undefined,
        waterfloodcrossing: undefined,
        differencevalue: undefined
      },
      rules: {
        name: [
          {
            required: true,
            message: "请输入方案名称",
            trigger: ["blur", "change"]
          },
          common.check_name,
          common.pattern_name,
          common.check_space
        ],

        alarmlevel: [
          {
            required: true,
            message: "请选择事件等级",
            trigger: ["blur", "change"]
          }
        ],

        waterfloodcrossing: [
          {
            required: true,
            message: "请输入注水越限百分比",
            trigger: ["blur"]
          }
        ]
      },

      alarmlevelOptions: [
        { id: 1, text: "一级" },
        { id: 2, text: "二级" },
        { id: 3, text: "三级" }
      ],

      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    async visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.init();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },
  computed: {
    title() {
      return `${this.inputData_in?.id ? "编辑" : "新增"}注水诊断方案`;
    },
    isEdit() {
      return this.inputData_in?.id;
    }
  },
  methods: {
    init() {
      this.$nextTick(() => {
        for (const key in this.formData) {
          if (this.inputData_in?.id) {
            this.formData[key] =
              this.inputData_in[key] || this.inputData_in[key] === 0
                ? this.inputData_in[key]
                : undefined;
          } else {
            this.formData[key] = undefined;
          }
        }
      });
    },

    CetButton_confirm_statusTrigger_out() {
      const params = {
        ...this.formData,
        status: true
      };
      if (this.isEdit) {
        params.id = this.inputData_in?.id;
      }
      this.$refs.formData.validate(async valid => {
        if (!valid) return;
        const res = await customApi.addUpdateScheme(params);
        if (res?.code === 0) {
          this.$message.success(this.isEdit ? "编辑成功" : "新增成功");
          this.$emit("updateTable");
          this.CetButton_cancel_statusTrigger_out();
        }
      });
    },
    CetButton_cancel_statusTrigger_out() {
      this.$refs.formData.resetFields();
      this.CetDialog_1.closeTrigger_in = +new Date();
    }
  }
};
</script>

<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
  }
  :deep(.el-form-item) {
    .el-form-item__label {
      line-height: 24px;
      text-align: left;
      width: 100% !important;
      float: none;
    }
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}
</style>
