import fetch from "eem-utils/fetch";
import store from "@/store";
import { encryptAttributes, decryptAttributes } from "eem-utils/crypto.js";
const version = "v1";
function processResponse(response) {
  return response;
}
function parseAttributes(data) {
  if (typeof data === "string") {
    data = JSON.parse(data);
  }
  const response = _.get(data, "data", []) || [];
  let newData = response.map(item => {
    // 需要进行解密
    return decryptAttributes(item, ["name", "nicName", "email", "mobilePhone"]);
  });
  return {
    ...data,
    data: newData
  };
}
function parseAttribute(data) {
  if (typeof data === "string") {
    data = JSON.parse(data);
  }
  let response = _.get(data, "data");
  response = decryptAttributes(response, [
    "name",
    "nicName",
    "email",
    "mobilePhone"
  ]);
  return {
    ...data,
    data: response
  };
}
function parseUserAndGroup(data) {
  if (typeof data === "string") {
    data = JSON.parse(data);
  }
  const response = _.get(data, "data", []) || [];
  let newData = response.map(item => {
    if (item.modelLabel === "user") {
      item = decryptAttributes(item, ["email", "mobilePhone", "name"]);
    }
    if (item.children && item.children.length) {
      let users = item.children.map(user => {
        // 需要进行解密
        return decryptAttributes(user, ["email", "mobilePhone", "name"]);
      });
      return {
        ...item,
        children: users
      };
    } else {
      return item;
    }
  });
  return {
    ...data,
    data: newData
  };
}

// 新增用户
export function addUser(data) {
  data.tenantId = store.state.tenantId;
  data.nicName = data.name;
  // eslint-disable-next-line no-prototype-builtins
  if (data.hasOwnProperty("_checkPassword")) {
    delete data._checkPassword;
  }
  return fetch({
    url: `/eem-service/${version}/auth/common/user`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}
// 修改用户
export function editUser(data) {
  data.tenantId = store.state.tenantId;
  data.nicName = data.name;
  // eslint-disable-next-line no-prototype-builtins
  if (data.hasOwnProperty("password")) {
    delete data.password;
  }
  // eslint-disable-next-line no-prototype-builtins
  if (data.hasOwnProperty("_checkPassword")) {
    delete data._checkPassword;
  }
  return fetch({
    url: `/eem-service/${version}/auth/common/user`,
    method: "PUT",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}
// 删除用户
export function deleteUser(data) {
  const userName = encodeURIComponent(data.name);
  return fetch({
    url: `/eem-service/${version}/auth/common/user?id=${data.id}&name=${userName}&tenantId=${store.state.tenantId}`,
    method: "DELETE",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

function processUsersResponse(response) {
  if (typeof response === "string") {
    response = JSON.parse(response);
  }
  if (response.code === 0) {
    const data = response.data;
    const userGroups = (data && data.userGroups) || [];

    const validUserGroups = [];

    userGroups.forEach(userGroup => {
      userGroup.modelLabel = "usergroup";
      userGroup.tree_id = "usergroup_" + userGroup.id;

      if ((userGroup.users || []).length) {
        validUserGroups.push(userGroup);

        // 需要按照id排序
        const users = userGroup.users || [];
        users.sort((a, b) => {
          return a.id - b.id;
        });

        users.forEach(user => {
          user.modelLabel = "user";
          user.tree_id = "user_" + user.id;
          user.relativeUserGroup = [userGroup.id];
          user.relativeUserGroupName = userGroup.name;
          if (userGroup.customConfig) {
            const logoData = JSON.parse(userGroup.customConfig);
            user.relativeUserGroupLogoName = logoData.fileName;
          } else {
            user.relativeUserGroupLogoName = null;
          }
        });
      }
    });

    response.data = validUserGroups;
  }

  return response;
}
function processUsersResponseAddROOT(response) {
  if (typeof response === "string") {
    response = JSON.parse(response);
  }
  if (response.code === 0) {
    const data = response.data;
    const userGroups = (data && data.userGroups) || [];

    const validUserGroups = [];

    userGroups.forEach(userGroup => {
      userGroup.modelLabel = "usergroup";
      userGroup.tree_id = "usergroup_" + userGroup.id;

      if ((userGroup.users || []).length) {
        validUserGroups.push(userGroup);

        // 需要按照id排序
        const users = userGroup.users || [];
        users.sort((a, b) => {
          return a.id - b.id;
        });

        users.forEach(user => {
          user.modelLabel = "user";
          user.tree_id = "user_" + user.id;
          user.relativeUserGroup = [userGroup.id];
          user.relativeUserGroupName = userGroup.name;
          if (userGroup.customConfig) {
            const logoData = JSON.parse(userGroup.customConfig);
            user.relativeUserGroupLogoName = logoData.fileName;
          } else {
            user.relativeUserGroupLogoName = null;
          }
        });
      }
    });

    const users = data.users;
    const ROOT = users.find(item => item.id === 1);
    if (ROOT) {
      ROOT.modelLabel = "user";
      ROOT.tree_id = "user_" + ROOT.id;
      validUserGroups.unshift(ROOT);
    }

    response.data = validUserGroups;
  }

  return response;
}

// 查询用户列表
export function getUsers(data) {
  return fetch({
    url: `/auth/${version}/tenant?tenantId=${store.state.tenantId}`,
    method: "GET",
    transformResponse: [processUsersResponse] //对接口返回的数据结构进行处理
  });
}

// 获取所有用户信息
export function getAllUsers(data) {
  return fetch({
    url: `/auth/${version}/users?tenantId=${store.state.tenantId}`,
    method: "GET",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

// 查询角色下所有的用户
export function getUsersByRole(roleId) {
  return fetch({
    url: `/eem-service/v2/user/queryByRole?roleId=${roleId}`,
    method: "GET",
    transformResponse: [parseAttributes] //对接口返回的数据结构进行处理
  });
}

// 通过名字查询用户信息
export function queryUserInfoByName(data) {
  const name = encodeURIComponent(data);
  return fetch({
    url: `/eem-service/v2/user/queryByName?name=${name}`,
    method: "GET",
    transformResponse: [parseAttribute] //对接口返回的数据结构进行处理
  });
}

export function queryUserInfoById(id) {
  return fetch({
    url: `/eem-service/v2/user/queryUserById?id=${id}`,
    method: "GET",
    transformResponse: [parseAttribute] //对接口返回的数据结构进行处理
  });
}

// 更新用户密码
export async function updateUserPassword(data) {
  // eslint-disable-next-line no-prototype-builtins
  if (data.hasOwnProperty("_checkPassword")) {
    delete data._checkPassword;
  }

  // 需要进行加密
  data = encryptAttributes(data, ["name", "newPassword", "oldPassword"]);

  return fetch({
    url: `/auth/${version}/user/password/updateSecurity`,
    method: "PUT",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}
// 通过name更新密码
export async function updateByNameSecurity(data) {
  // 需要进行加密
  data = encryptAttributes(data, ["name", "newPassword", "oldPassword"]);

  return fetch({
    url: `/auth/${version}/user/password/updateByNameSecurity`,
    method: "PUT",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}
// 判断是否存在超级用户
export function cloudRootExist() {
  let url = `/eem-service/${version}/auth/cloud/rootExist`;
  return fetch({
    url,
    method: "GET",
    transformResponse: [processResponse]
  });
}
// 平台用户管理V2 ***********
// 获取节点树
export function authsimplifyTenantTree(data) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/tree`,
    method: "POST",
    data: data
  });
}
// 用户管理节点树-全加载 走缓存
export function authManageTree(data) {
  return fetch({
    url: `/eem-service/v2/auth/manage/tree`,
    method: "POST",
    data: data
  });
}
// 获取租户详情
export function authGetTenantInfo(data) {
  return fetch({
    url: `/auth/v1/tenant`,
    method: "GET",
    params: data
  });
}
// 获取用户组详情
export function authGetUsergroupInfo(data) {
  return fetch({
    url: `/auth/v1/usergroup/queryById`,
    method: "GET",
    params: data
  });
}
// 服务商 *******
// 新增-基础信息
export function addSimplifyTenant(data) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/tenant`,
    method: "PUT",
    data: data
  });
}
// 编辑-基础信息
export function editSimplifyTenant(data) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/tenant`,
    method: "POST",
    data: data
  });
}
// 保存节点graphnode
export function editSimplifyTenantGraphnode(data) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/tenant/graphnode`,
    method: "POST",
    data: data
  });
}
// 保存节点modelnode
export function editSimplifyTenantModelnode(data) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/tenant/modelnode`,
    method: "POST",
    data: data
  });
}
// 保存节点pecstarnode
export function editSimplifyTenantPecstarnode(data) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/tenant/pecstarnode`,
    method: "POST",
    data: data
  });
}
// 项目 *******
// 新增-基础信息
export function addSimplifyEnterprise(data) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/enterprise`,
    method: "PUT",
    data: data
  });
}
// 编辑-基础信息
export function editSimplifyEnterprise(data) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/enterprise`,
    method: "POST",
    data: data
  });
}
// 保存节点graphnode
export function editSimplifyEnterpriseGraphnode(data) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/enterprise/graphnode`,
    method: "POST",
    data: data
  });
}
// 保存节点modelnode
export function editSimplifyEnterpriseModelnode(data) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/enterprise/modelnode`,
    method: "POST",
    data: data
  });
}
// 保存节点pecstarnode
export function editSimplifyEnterprisePecstarnode(data) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/enterprise/pecstarnode`,
    method: "POST",
    data: data
  });
}
// 用户 *******
export async function addSimplifyUser(data) {
  // 需要进行加密
  data = encryptAttributes(data, [
    "email",
    "mobilePhone",
    "name",
    "nicName",
    "password"
  ]);

  return fetch({
    url: `/eem-service/v2/auth/simplify/user`,
    method: "PUT",
    data: data
  });
}
// 编辑-基础信息
export function editSimplifyUser(data) {
  // 需要进行加密
  data = encryptAttributes(data, [
    "email",
    "mobilePhone",
    "name",
    "nicName",
    "password"
  ]);

  return fetch({
    url: `/eem-service/v2/auth/simplify/user`,
    method: "POST",
    data: data
  });
}
// 保存节点graphnode
export function editSimplifyUserGraphnode(data) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/user/graphnode`,
    method: "POST",
    data: data
  });
}
// 保存节点modelnode
export function editSimplifyUserModelnode(data) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/user/modelnode`,
    method: "POST",
    data: data
  });
}
// 保存节点pecstarnode
export function editSimplifyUserPecstarnode(data) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/user/pecstarnode`,
    method: "POST",
    data: data
  });
}
// 获取租户节点权限
// 租户
export function tenantGraphnodeQuery(data, params) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/tenant/graphnode/query`,
    method: "POST",
    data,
    params
  });
}
export function tenantModelnodeQuery(data, params) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/tenant/modelnode/query`,
    method: "POST",
    data,
    params
  });
}
export function tenantPecstarnodeQuery(data, params) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/tenant/pecstarnode/query`,
    method: "POST",
    data,
    params
  });
}
// 用户
export function userGraphnodeQuery(data, params) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/user/graphnode/query`,
    method: "POST",
    data,
    params
  });
}
export function userModelnodeQuery(data, params) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/user/modelnode/query`,
    method: "POST",
    data,
    params
  });
}
export function userPecstarnodeQuery(data, params) {
  return fetch({
    url: `/eem-service/v2/auth/simplify/user/pecstarnode/query`,
    method: "POST",
    data,
    params
  });
}
// 获取完整图形节点的树，不经过当前用户过滤
export function authNouserauthGraphTree(params) {
  return fetch({
    url: `/eem-service/v2/auth/nouserauth/graphTree`,
    method: "GET",
    params
  });
}
// 获取完整MReport节点的树，不经过当前用户过滤
export function authNouserauthMReportTree(params) {
  return fetch({
    url: `/eem-service/v2/auth/nouserauth/mReport/tree`,
    method: "GET",
    params
  });
}
// 获取完整PecReport节点的树，不经过当前用户过滤
export function authNouserauthPecReportTree(params) {
  return fetch({
    url: `/eem-service/v2/auth/nouserauth/pecReport/tree`,
    method: "GET",
    params
  });
}

// 查询项目内用户和用户组
export function queryProjectUserAndGroup(data) {
  return fetch({
    url: `/eem-service/v2/user/project/userAndGroup`,
    method: "POST",
    data: data,
    transformResponse: [parseUserAndGroup] //对接口返回的数据结构进行处理
  });
}
// 删除用户
export function deleteAuthCommonUser(params) {
  return fetch({
    url: `/eem-service/v1/auth/common/user`,
    method: "DELETE",
    params
  });
}
// 新增用户组
export function addAuthCommonUserGroup(data) {
  return fetch({
    url: `/eem-service/v1/auth/common/userGroup`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}
// 修改用户组
export function editAuthCommonUserGroup(data) {
  return fetch({
    url: `/eem-service/v1/auth/common/userGroup`,
    method: "PUT",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}
// 删除用户组
export function deleteAuthCommonUserGroup(params) {
  return fetch({
    url: `/eem-service/v1/auth/common/userGroup`,
    method: "DELETE",
    params
  });
}
// 删除租户
export function deleteAuthCommonTenant(params) {
  return fetch({
    url: `/eem-service/v1/auth/common/tenant`,
    method: "DELETE",
    params
  });
}

/**
 * ROOT 用户修改别人的密码
 */
export function updatePasswordByRoot(data) {
  // 需要进行加密
  data = encryptAttributes(data, [
    "rootName",
    "rootPassword",
    "userName",
    "newPassword"
  ]);

  return fetch({
    url: `/auth/v1/user/password/securityUpdateByRoot`,
    method: "PUT",
    data
  });
}
