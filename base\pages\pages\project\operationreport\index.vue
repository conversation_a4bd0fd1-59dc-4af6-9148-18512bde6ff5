<template>
  <div class="page eem-common">
    <el-container class="fullheight">
      <el-aside width="315px" class="eem-aside">
        <CetGiantTree
          class="treeBottomRadius"
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
        ></CetGiantTree>
      </el-aside>
      <div v-if="!isReport" class="text-center">
        <span class="info">{{ $T("请选择报表模板") }}</span>
      </div>
      <el-container
        v-else
        class="eem-container fullheight mlJ3 eem-min-width-mini flex-column"
      >
        <div class="mbJ3 flex-row">
          <el-tooltip :content="nodeLabel" effect="light" placement="top">
            <div class="flex-auto text-ellipsis" style="height: 32px">
              <span class="common-title-H2 lh32" style="display: inline">
                {{ nodeLabel }}
              </span>
            </div>
          </el-tooltip>
          <div class="mlJ3">
            <div class="fr text-right" v-if="isReport">
              <el-select
                v-model="currentTimeBucket"
                class="data-row"
                :disabled="currentNode.reportType !== 4"
                v-show="currentNode.reportType === 4"
                @change="changeTimeBucket"
                style="width: 90px"
                size="small"
              >
                <el-option
                  v-for="item in timeBucket"
                  :key="item.id + item.text"
                  :label="item.text"
                  :value="item.id"
                ></el-option>
              </el-select>
              <el-button
                class="fl mrJ1"
                size="small"
                icon="el-icon-arrow-left"
                @click="queryPrv"
              ></el-button>
              <el-date-picker
                class="data-row"
                size="small"
                :type="pickerType"
                v-model="queryTime.startDate"
                @change="changeDate"
                :clearable="false"
                :format="formatDate"
                :picker-options="startPickerOptions"
              ></el-date-picker>
              <el-time-picker
                v-show="pickerType === 'date'"
                class="data-row"
                :disabled="[0, 1, 2, 3].includes(currentNode.reportType)"
                size="small"
                v-model="queryTime.startTime"
                @change="changeDate"
                :clearable="false"
              ></el-time-picker>
              <span class="fl lh32 mrJ1">{{ $T("至") }}</span>
              <el-date-picker
                class="data-row"
                :disabled="[0, 1, 2, 3].includes(currentNode.reportType)"
                size="small"
                :type="pickerType"
                v-model="queryTime.endDate"
                @change="changeDate"
                :clearable="false"
                :format="formatDate"
                :picker-options="endPickerOptions"
              ></el-date-picker>
              <el-time-picker
                class="data-row"
                v-show="pickerType === 'date'"
                :disabled="[0, 1, 2, 3].includes(currentNode.reportType)"
                size="small"
                v-model="queryTime.endTime"
                @change="changeDate"
                :clearable="false"
              ></el-time-picker>
              <el-button
                class="fl"
                size="small"
                icon="el-icon-arrow-right"
                @click="queryNext"
              ></el-button>
              <el-button
                class="fl mlJ1"
                size="small"
                @click="exportReportFile"
                :disabled="!reportHtml"
              >
                {{ $T("导出") }}
              </el-button>
              <!-- <time-tool></time-tool> -->
            </div>
          </div>
        </div>
        <div class="flex-auto" style="overflow: auto">
          <div v-show="!reportHtml" class="text-center">
            <span class="info">{{ $T("暂无报表数据") }}</span>
          </div>
          <div v-show="reportHtml" v-html="reportHtml"></div>
          <!-- <iframe v-if="reportHtml" width="100%" height="100%" :src="'/device-data-service/api/report/v1/'+ reportHtml"  frameborder="0"></iframe> -->
        </div>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import common from "eem-utils/common";
import { httping } from "@omega/http";
export default {
  name: "OperationReport",
  components: {},

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    isReport() {
      return this._.get(this.currentNode, "nodeType") === 272760832;
    },
    userInfo() {
      var vm = this;
      return vm.$store.state.userInfo;
    },
    projectTenantId() {
      var vm = this;
      return vm.$store.state.projectTenantId;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data(vm) {
    return {
      nodeLabel: "",
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "nodeName"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out //选中单行输出
        }
      },
      currentNode: null,
      queryTime: {
        startDate: vm.$moment().startOf("d").format("YYYY-MM-DD"),
        endDate: vm.$moment().add(1, "d").startOf("d").format("YYYY-MM-DD"),
        startTime: vm.$moment().startOf("d").valueOf(),
        endTime: vm.$moment().add(1, "d").startOf("d").valueOf()
      },
      disablePicker: true,
      /* {
        0: [true, false, true, true, true],
        1: [true, false, true, true, true],
        2: [true, false, true, true, true],
        3: [true, false, true, true, true],
        4: [true, false, true, true, true]
      }, */
      pickerType: "date",
      formatDate: "yyyy-MM-dd",
      // DailyReportNodeType: 0,
      //   MonthlyReportNodeType: 1,
      //   WeeklyReportNodeType: 2,
      //   YearlyReportNodeType: 3,
      //   TimeSpanReportNodeType: 4,
      //   DailyReportTemplateNodeType: 5,
      //   MonthlyReportTemplateNodeType: 6,
      //   WeeklyReportTemplateNodeType: 7,
      //   YearlyReportTemplateNodeType: 8,
      //   TimeSpanReportTemplateNodeType: 9,
      //   DynamicReportTemplateNodeType: 10
      timeBucket: [
        {
          id: 1,
          text: "1天",
          nodeType: 0,
          intervalType: 1,
          number: 1,
          unit: "d"
        },
        {
          id: 2,
          text: "1周",
          nodeType: 2,
          intervalType: 2,
          number: 7,
          unit: "d"
        },
        {
          id: 3,
          text: "1月",
          nodeType: 1,
          intervalType: 3,
          number: 1,
          unit: "M"
        },
        {
          id: 4,
          text: "1季度",
          nodeType: 1,
          intervalType: 3,
          number: 3,
          unit: "M"
        },
        {
          id: 5,
          text: "1年",
          nodeType: 3,
          intervalType: 5,
          number: 1,
          unit: "y"
        }
      ],
      startPickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      endPickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() < vm.$moment(vm.queryTime.startDate).valueOf() ||
            time.getTime() > Date.now()
          );
        }
      },
      currentTimeBucket: 1,
      reportHtml: ""
    };
  },
  watch: {
    currentNode: {
      deep: true,
      handler: function (val, oldVal) {
        // console.log("currentNode", val, oldVal);

        if (this._.isEmpty(val)) return;
        let queryTime = this.queryTime;
        switch (val.reportType) {
          case 0:
            queryTime.startDate = this.$moment()
              .startOf("d")
              .format("YYYY-MM-DD");
            queryTime.endDate = this.$moment()
              .startOf("d")
              .add(1, "d")
              .format("YYYY-MM-DD");
            this.pickerType = "date";
            this.formatDate = "yyyy-MM-dd";
            this.currentTimeBucket = 1;
            break;
          case 1:
            queryTime.startDate = this.$moment()
              .startOf("M")
              .format("YYYY-MM-DD");
            queryTime.endDate = this.$moment()
              .startOf("M")
              .add(1, "M")
              .format("YYYY-MM-DD");
            this.pickerType = "month";
            this.formatDate = "yyyy-MM";
            this.currentTimeBucket = 3;
            break;
          case 2:
            queryTime.startDate = this.$moment()
              .startOf("w")
              .format("YYYY-MM-DD");
            queryTime.endDate = this.$moment()
              .startOf("w")
              .add(1, "w")
              .format("YYYY-MM-DD");
            this.pickerType = "date";
            this.formatDate = "yyyy-MM-dd";
            this.currentTimeBucket = 2;
            break;
          case 3:
            queryTime.startDate = this.$moment()
              .startOf("y")
              .format("YYYY-MM-DD");
            queryTime.endDate = this.$moment()
              .startOf("y")
              .add(1, "y")
              .format("YYYY-MM-DD");
            this.pickerType = "year";
            this.formatDate = "yyyy";
            this.currentTimeBucket = 5;
            break;
          default:
            queryTime.startDate = this.$moment()
              .startOf("d")
              .format("YYYY-MM-DD");
            queryTime.endDate = this.$moment()
              .startOf("d")
              .add(1, "d")
              .format("YYYY-MM-DD");
            this.pickerType = "date";
            this.formatDate = "yyyy-MM-dd";
            this.currentTimeBucket = 1;
            break;
        }
        queryTime.startTime = this.$moment().startOf("d").valueOf();
        queryTime.endTime = this.$moment().add(1, "d").startOf("d").valueOf();
        if (val.nodeType === 272760832) this.queryReportFile();
      }
    }
  },

  methods: {
    //点击节点树触发
    CetGiantTree_1_currentNode_out(val) {
      this.nodeLabel = val && val.nodeName;
      this.currentNode = val;
    },
    //迭戈节点树，添加tree_id字段
    setTreeKey: function (root) {
      //递归
      var res = root;
      function bfs(tree, idx) {
        if (!tree) return;
        // if (!Array.isArray(tree[idx])) {
        //   tree[idx] = [];
        // }
        tree.name = tree.nodeName;
        tree.id = tree.nodeType + tree.nodeId;
        tree.modelLabel = tree.nodeType + "";
        tree.tree_id = tree.nodeType + "_" + tree.nodeId;
        if (!Array.isArray(tree.children)) {
          return;
        }
        if (tree.children.length > 0) {
          for (var i = 0; i < tree.children.length; i++) {
            bfs(tree.children[i], idx + 1);
          }
        }
      }
      bfs(root, 0);
      return res;
    },
    //获取左侧报表节点树
    getReportTree() {
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      httping({
        url: `/eem-service/v2/peccore/pecReport/tree?tenantId=${this.projectTenantId}&userId=${this.userInfo.id}`,
        method: "GET"
      }).then(res => {
        if (res.code === 0) {
          if (this._.isEmpty(res.data)) return;
          let data = this.setTreeKey({
            children: res.data
          });
          this.CetGiantTree_1.inputData_in = data.children;
          this.CetGiantTree_1.selectNode = this._.get(data.children, "[0]");
        }
      });
    },
    //查询报表
    queryReportFile() {
      let node = this.currentNode;
      this._.get(node, "nodeType");
      if (this._.get(node, "nodeType") !== 272760832) return;
      let startTime =
        this.$moment(this.queryTime.startDate).format("YYYY-MM-DD") +
        this.$moment(this.queryTime.startTime).format(" HH:mm:ss");
      let endTime =
        this.$moment(this.queryTime.endDate).format("YYYY-MM-DD") +
        this.$moment(this.queryTime.endTime).format(" HH:mm:ss");
      if (this.$moment(endTime).diff(this.$moment(startTime)) < 0) {
        this.$message.warning("结束时间需要大于开始时间！");
        return;
      }
      let params = {
        startTime: startTime,
        exportType: 0,
        intervalType: 0,
        queryNodeID: 0,
        queryNodeType: 0,
        reportID: node.nodeId,
        reportName: node.nodeName,
        reportType: node.nodeType,
        endTime: endTime
      };
      httping({
        url: `/device-data-service/api/report/v1/html`,
        method: "POST",
        data: params,
        responseType: "text"
      })
        .then(res => {
          if (res.status !== 200) {
            this.reportHtml = "";
            return;
          }
          this.reportHtml = res.data;
        })
        .catch(res => {
          this.reportHtml = "";
        });
    },
    //导出报表
    exportReportFile() {
      let node = this.currentNode;
      let vm = this;
      let startTime =
        this.$moment(this.queryTime.startDate).format("YYYY-MM-DD") +
        this.$moment(this.queryTime.startTime).format(" HH:mm:ss");
      let endTime =
        this.$moment(this.queryTime.endDate).format("YYYY-MM-DD") +
        this.$moment(this.queryTime.endTime).format(" HH:mm:ss");
      if (this.$moment(endTime).diff(this.$moment(startTime)) < 0) {
        this.$message.warning("结束时间需要大于开始时间！");
        return;
      }
      let params = {
        startTime: startTime,
        exportType: 1,
        intervalType: 0,
        queryNodeID: 0,
        queryNodeType: 0,
        reportID: node.nodeId,
        reportName: node.nodeName,
        reportType: node.nodeType,
        endTime: endTime
      };
      common.downExcel(
        "/device-data-service/api/report/v1/excel",
        params,
        vm.token,
        this.projectId
      );
    },
    //时间控件修改触发
    changeDate(val) {
      // return;
      let node = this.currentNode;
      let queryTime = this._.cloneDeep(this.queryTime);
      switch (node.reportType) {
        case 0:
          queryTime.startDate = this.$moment(val)
            .startOf("d")
            .format("YYYY-MM-DD");
          queryTime.endDate = this.$moment(val)
            .startOf("d")
            .add(1, "d")
            .format("YYYY-MM-DD");
          this.pickerType = "date";
          this.formatDate = "yyyy-MM-dd";
          break;
        case 1:
          queryTime.startDate = this.$moment(val)
            .startOf("M")
            .format("YYYY-MM-DD");
          queryTime.endDate = this.$moment(val)
            .startOf("M")
            .add(1, "M")
            .format("YYYY-MM-DD");
          this.pickerType = "month";
          this.formatDate = "yyyy-MM";
          break;
        case 2:
          queryTime.startDate = this.$moment(val)
            .startOf("w")
            .format("YYYY-MM-DD");
          queryTime.endDate = this.$moment(val)
            .startOf("w")
            .add(1, "w")
            .format("YYYY-MM-DD");
          this.pickerType = "date";
          this.formatDate = "yyyy-MM-dd";
          break;
        case 3:
          queryTime.startDate = this.$moment(val)
            .startOf("y")
            .format("YYYY-MM-DD");
          queryTime.endDate = this.$moment(val)
            .startOf("y")
            .add(1, "y")
            .format("YYYY-MM-DD");
          this.pickerType = "year";
          this.formatDate = "yyyy";
          break;
        default:
          break;
      }
      this.queryTime = queryTime;
      this.queryReportFile();
    },
    //修改时间周期下拉框
    changeTimeBucket(val) {
      let queryTime = this._.cloneDeep(this.queryTime);
      let startDate = this.$moment(queryTime.startDate);
      let currentTimeBucket = this._.find(this.timeBucket, { id: val });

      let endDate = startDate.add(
        currentTimeBucket.number,
        currentTimeBucket.unit
      );
      this.queryTime.endDate = endDate;
      this.queryTime.endTime = queryTime.startTime;
      this.queryReportFile();
    },
    //点击上一时间段
    queryPrv() {
      if ([0, 1, 2, 3, 4].includes(this.currentNode.reportType)) {
        let queryTime = this.queryTime;
        let startDate = this.$moment(queryTime.startDate);
        let currentTimeBucket = this._.find(this.timeBucket, {
          id: this.currentTimeBucket
        });
        queryTime.startDate = startDate
          .subtract(currentTimeBucket.number, currentTimeBucket.unit)
          .format("YYYY-MM-DD");
        queryTime.endDate = this.$moment(queryTime.startDate)
          .add(currentTimeBucket.number, currentTimeBucket.unit)
          .format("YYYY-MM-DD");
        queryTime.endTime = queryTime.startTime;
      } else {
        let queryTime = this._.cloneDeep(this.queryTime);
        let startStr =
          this.$moment(queryTime.startDate).format("YYYY-MM-DD") +
          this.$moment(queryTime.startTime).format(" HH:mm:ss");
        let endStr =
          this.$moment(queryTime.endDate).format("YYYY-MM-DD") +
          this.$moment(queryTime.endTime).format(" HH:mm:ss");
        const timeDiff = this.$moment(endStr).diff(this.$moment(startStr));
        this.queryTime.startDate = this.$moment(startStr)
          .subtract(timeDiff)
          .format("YYYY-MM-DD");
        this.queryTime.endDate = this.$moment(startStr).format("YYYY-MM-DD");
        this.queryTime.startTime = this.$moment(startStr)
          .subtract(timeDiff)
          .valueOf();
        this.queryTime.endTime = this.$moment(startStr).valueOf();
      }

      this.queryReportFile();
    },
    //点击下一时间段
    queryNext() {
      if ([0, 1, 2, 3, 4].includes(this.currentNode.reportType)) {
        let queryTime = this.queryTime;
        let startDate = this.$moment(queryTime.startDate);
        let currentTimeBucket = this._.find(this.timeBucket, {
          id: this.currentTimeBucket
        });
        queryTime.startDate = startDate
          .add(currentTimeBucket.number, currentTimeBucket.unit)
          .format("YYYY-MM-DD");
        queryTime.endDate = this.$moment(queryTime.startDate)
          .add(currentTimeBucket.number, currentTimeBucket.unit)
          .format("YYYY-MM-DD");
        queryTime.endTime = queryTime.startTime;
      } else {
        let queryTime = this._.cloneDeep(this.queryTime);
        let startStr =
          this.$moment(queryTime.startDate).format("YYYY-MM-DD") +
          this.$moment(queryTime.startTime).format(" HH:mm:ss");
        let endStr =
          this.$moment(queryTime.endDate).format("YYYY-MM-DD") +
          this.$moment(queryTime.endTime).format(" HH:mm:ss");
        const timeDiff = this.$moment(endStr).diff(this.$moment(startStr));
        this.queryTime.startDate = this.$moment(endStr).format("YYYY-MM-DD");
        this.queryTime.endDate = this.$moment(endStr)
          .add(timeDiff)
          .format("YYYY-MM-DD");
        this.queryTime.startTime = this.$moment(endStr).valueOf();
        this.queryTime.endTime = this.$moment(endStr).add(timeDiff).valueOf();
      }
      this.queryReportFile();
    }
  },
  created: function () {
    // this.getReportTree();
  },
  activated() {
    this.getReportTree();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.data-row {
  float: left;
  @include margin_right(J1);
  width: 150px !important;
}
.H1Size {
  @include font_size(H1);
}
.text-center {
  text-align: center;
  width: 100%;
  height: 100%;
  position: relative;
  .info {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    height: 50px;
    line-height: 50px;
    margin: auto;
    @include font_size(H1);
  }
}
</style>
