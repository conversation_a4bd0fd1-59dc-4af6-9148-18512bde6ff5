<template>
  <el-container class="page">
    <el-aside width="320px" class="fullheight bg1 pJ3 border-right">
      <!-- left组件 -->
      <CetGiantTree
        v-bind="CetGiantTree_left"
        v-on="CetGiantTree_left.event"
        class="treeStyle"
      ></CetGiantTree>
    </el-aside>
    <el-main class="fullheight bg1 pJ3 flex-column">
      <div class="summary">
        <span class="title">{{ $T("优化策略总结：") }}</span>
        <span>
          {{ fitAndOptimizeData.strategySummarize || "--" }}
        </span>
      </div>
      <div class="head">
        <el-radio-group v-model="radio">
          <el-radio-button label="efficiency">
            {{ $T("近30日效率拟合曲线") }}
          </el-radio-button>
          <el-radio-button label="unitConsumption">
            {{ $T("近30日单耗拟合曲线") }}
          </el-radio-button>
        </el-radio-group>
        <CustomElDatePicker
          class="mlJ1"
          :prefix_in="$T('选择优化日')"
          :clearable="false"
          style="width: 200px"
          v-model="queryTime"
          type="date"
          :placeholder="$T('选择时间')"
          @change="changeQueryTime"
          :picker-options="queryTimePickerOptions"
          value-format="timestamp"
        />
        <CustomElDatePicker
          class="flex-end"
          :prefix_in="$T('拟合时段')"
          :clearable="false"
          style="width: 300px"
          v-model="fitTime"
          type="daterange"
          :placeholder="$T('选择时间')"
          :picker-options="fitTimePickerOptions"
          value-format="timestamp"
        />
        <el-button class="mlJ1" type="primary" @click="handleUpdateFitTime">
          {{ $T("更新拟合") }}
        </el-button>
      </div>
      <div class="flex1">
        <div style="height: calc(50% - 8px)" class="mbJ3 flex">
          <div style="width: calc(55% - 8px)" class="fullheight mrJ3">
            <fittingCurve
              :fitAndOptimizeData="fitAndOptimizeData"
              :type="radio"
              :queryTime="queryTime"
            ></fittingCurve>
          </div>
          <div style="width: calc(45% - 8px)" class="fullheight">
            <currentWorkingCondition :fitAndOptimizeData="fitAndOptimizeData" />
          </div>
        </div>
        <div style="height: calc(50% - 8px)" class="flex">
          <div style="width: calc(78% - 8px)" class="fullheight mrJ3">
            <efficiencyTrend :efficiencyTrendData="efficiencyTrendData" />
          </div>
          <div style="width: calc(22% - 8px)" class="fullheight">
            <benefitEvaluation :fitAndOptimizeData="fitAndOptimizeData" />
          </div>
        </div>
      </div>
    </el-main>
  </el-container>
</template>

<script>
import customApi from "@/api/custom.js";
import fittingCurve from "./components/fittingCurve.vue";
import currentWorkingCondition from "./components/currentWorkingCondition.vue";
import efficiencyTrend from "./components/efficiencyTrend.vue";
import benefitEvaluation from "./components/benefitEvaluation.vue";
import moment from "moment";
export default {
  name: "processingGasVolume",
  components: {
    fittingCurve,
    currentWorkingCondition,
    efficiencyTrend,
    benefitEvaluation
  },
  data() {
    return {
      radio: "efficiency",
      currentNode: {},
      queryTime: this.$moment().subtract(1, "d").startOf("d").valueOf(),
      fitTime: [
        this.$moment().subtract(6, "M").startOf("d").valueOf(),
        this.$moment().subtract(1, "d").endOf("d").valueOf()
      ],
      fitAndOptimizeData: {},
      efficiencyTrendData: [],
      queryTimePickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() > moment().subtract(1, "d").startOf("d").valueOf()
          );
        }
      },
      fitTimePickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() > moment().subtract(1, "d").startOf("d").valueOf()
          );
        },
        onPick: ({ maxDate, minDate }) => {
          if (minDate && !maxDate) {
            const endTime = moment().subtract(1, "d").startOf("d").valueOf();
            this.fitTimePickerOptions.disabledDate = time => {
              return (
                time.getTime() >
                  Math.min(moment(minDate).add(1, "year").valueOf(), endTime) ||
                time.getTime() <
                  moment(minDate).subtract(1, "year").valueOf() ||
                (time.getTime() < moment(minDate).add(1, "M").valueOf() &&
                  time.getTime() > moment(minDate).valueOf()) ||
                (time.getTime() > moment(minDate).subtract(1, "M").valueOf() &&
                  time.getTime() < moment(minDate).valueOf())
              );
            };
          } else {
            this.fitTimePickerOptions.disabledDate = time => {
              return (
                time.getTime() >
                moment().subtract(1, "d").startOf("d").valueOf()
              );
            };
          }
        }
      },
      // left组件
      CetGiantTree_left: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: this.setNodeClasses
          },
          callback: {
            beforeClick: this.beforeClick
          }
        },
        event: {
          created_out: this.CetGiantTree_created_out,
          currentNode_out: this.CetGiantTree_left_currentNode_out //选中单行输出
        }
      }
    };
  },
  watch: {},
  methods: {
    /**
     * 获取节点树
     */
    getTreeData() {
      const params = {
        rootID: 0,
        rootLabel: "project",
        subLayerConditions: [
          {
            modelLabel: "gasgatheringstation"
          },
          {
            modelLabel: "dehydratingstation"
          },
          {
            modelLabel: "gascompressor",
            filter: {
              composemethod: true,
              expressions: [
                {
                  limit: 2,
                  operator: "EQ",
                  prop: "principletype",
                  tagid: 1
                },
                {
                  limit: 1,
                  operator: "EQ",
                  prop: "compressortype",
                  tagid: 1
                }
              ]
            }
          }
        ],
        treeReturnEnable: true
      };
      customApi.getNodeTree(params).then(res => {
        const data = res.data || [];
        this.handleDisablingTree(data);
        this.CetGiantTree_left.inputData_in = data;
        const flatData = this.flatTreeData(data);
        const node = flatData?.find(
          item => item.modelLabel === "gascompressor"
        );
        this.CetGiantTree_left.selectNode = node || {};
      });
    },

    /**
     * 拍平节点树数据
     */
    flatTreeData(treeData) {
      const cloneData = this._.cloneDeep(treeData);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },

    /**
     * 节点是否可选择
     */
    beforeClick(treeId, treeNode) {
      if (treeNode.modelLabel != "gascompressor") {
        return false;
      }
      return true;
    },

    /**
     * 设置节点禁用
     */
    handleDisablingTree(nodes) {
      nodes.forEach(item => {
        if (item.modelLabel !== "gascompressor") {
          item.disableNode = true;
        } else {
          item.disableNode = false;
        }
        if (item.children && item.children.length > 0) {
          this.handleDisablingTree(item.children);
        }
      });
    },
    setNodeClasses(treeId, treeNode) {
      return treeNode.disableNode
        ? { add: ["disableNode"] }
        : { remove: ["disableNode"] };
    },

    /**
     * 展开全部
     */
    CetGiantTree_created_out(val) {
      val.expandAll(true);
    },

    /**
     * 节点树选择
     */
    CetGiantTree_left_currentNode_out(val) {
      this.currentNode = _.cloneDeep(val);
      this.queryFitAndOptimizeData();
      this.queryGasVolumeEfficiencyTrend();
    },

    /**
     * 切换优化日
     */
    changeQueryTime(val) {
      this.queryFitAndOptimizeData();
      this.queryGasVolumeEfficiencyTrend();
    },

    /**
     * 更新拟合
     */
    handleUpdateFitTime() {
      const params = {
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel,
        startTime: this.fitTime[0],
        endTime: this.$moment(this.fitTime[1]).endOf("d").valueOf() + 1
      };
      customApi.gasDataFitting(params).then(res => {
        if (res.code === 0) {
          this.$message.success($T("更新成功"));
          this.queryFitAndOptimizeData();
          this.queryGasVolumeEfficiencyTrend();
        }
      });
    },

    /**
     * 查询处理气量调优-获取拟合和优化数据
     */
    queryFitAndOptimizeData() {
      const params = {
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel,
        optimizeTime: this.$moment(this.queryTime).startOf("d").valueOf()
      };
      customApi.queryFitAndOptimizeData(params).then(res => {
        this.fitAndOptimizeData = res.data || {};
      });
    },

    /**
     * 查询处理气量调优-能效趋势
     */
    queryGasVolumeEfficiencyTrend() {
      const params = {
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel,
        optimizeTime: this.$moment(this.queryTime).startOf("d").valueOf()
      };
      customApi.queryGasVolumeEfficiencyTrend(params).then(res => {
        this.efficiencyTrendData = res.data || [];
      });
    }
  },
  mounted() {
    this.getTreeData();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.border-right {
  border-right: 1px solid;
  @include border_color(B1);
}
.summary {
  padding: 8px 12px;
  margin-bottom: 16px;
  line-height: 28px;
  position: relative;
  @include background_color(BG_oil1);
  .title {
    font-weight: bold;
    @include font_color(oil1);
  }
  &:before {
    content: "";
    display: inline-block;
    position: absolute;
    left: 0;
    top: 8px;
    bottom: 8px;
    width: 4px;
    border-radius: 0px 4px 4px 0px;
    @include background_color(oil1);
  }
}
.head {
  height: 32px;
  line-height: 32px;
  margin-bottom: 16px;
  display: flex;
  .title {
    font-size: 16px;
    font-weight: bold;
  }
}
.flex1 {
  flex: 1;
  min-height: 0px;
  min-width: 0px;
}
.flex-end {
  margin-left: auto;
  justify-content: flex-end;
}
.date-select {
  width: 350px;
  :deep(.el-date-editor) {
    width: 150px !important;
  }
}
.treeStyle {
  :deep(.disableNode) {
    @include font_color(T6 !important);
    cursor: not-allowed !important;
    opacity: 0.6;
  }
}
</style>
