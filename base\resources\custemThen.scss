//按钮禁用样式--start
.el-month-table td.disabled .cell {
  @include background_color(BG9);
  @include font_color(T6);
}
.el-button.is-disabled,
.el-button.is-disabled:focus,
.el-button.is-disabled:hover {
  @include font_color(T6);
  @include background_color(BG9);
  @include border_color(B1);
}
.el-button--primary.is-disabled,
.el-button--primary.is-disabled:active,
.el-button--primary.is-disabled:focus,
.el-button--primary.is-disabled:hover {
  @include font_color(T6);
  @include background_color(BG8);
  @include border_color(B1);
}
.el-button.is-disabled.is-plain,
.el-button.is-disabled.is-plain:focus,
.el-button.is-disabled.is-plain:hover {
  @include font_color(T6);
  @include background_color(BG9);
  @include border_color(B1);
}
.el-button--danger.is-disabled,
.el-button--danger.is-disabled:active,
.el-button--danger.is-disabled:focus,
.el-button--danger.is-disabled:hover {
  @include font_color(T6);
  @include background_color(BG10, !important);
  @include border_color(B1);
}
//按钮禁用样式--end
.el-input.is-disabled .el-input__inner {
  @include font_color(T3);
  @include border_color(B1);
  @include background_color(BG9);
}
input::-webkit-input-placeholder {
  @include font_color(T4);
}
// 火狐19+版本
input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  @include font_color(T4);
}
// IE10-11版本
input:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  @include font_color(T4);
}
textarea::-webkit-input-placeholder {
  @include font_color(T4);
}
// 火狐19+版本
textarea::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  @include font_color(T4);
}
// IE10-11版本
textarea:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  @include font_color(T4);
}
//分页样式--start
.el-pagination {
  font-weight: normal !important;
}
.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
  @include background_color(BG1);
  border: 1px solid;
  @include border_color(B1);
  @include font_color(T1);
  @include margin(0px J);
}
.el-pagination.is-background .btn-next:disabled,
.el-pagination.is-background .btn-prev:disabled {
  @include background_color(Sta5);
  @include font_color(T6);
}
.el-pagination.is-background .el-pager li:not(.disabled).active {
  @include background_color(ZS);
  border: none;
  @include font_color(T5);
}
.el-pagination.is-background .el-pagination__jump {
  @include margin_left(J2);
}
.el-pagination.is-background .el-pagination__sizes {
  @include margin_right(J1);
}
.el-pagination.is-background .el-pagination__total {
  @include margin_right(J1);
}
//分页样式--end
//表格头样式--start
.el-table th {
  @include background_color(BG);
}
//表格头样式--end

//多选框字体样式
.el-tag.el-tag--info {
  @include font_color(T2, !important);
  border: none;
  @include background_color(BG);
}

.el-tag.el-tag--info .el-tag__close {
  @include font_color(T2, !important);
}

.el-tag.el-tag--info .el-tag__close:hover {
  @include background_color(ZS, !important);
  @include font_color(T5, !important);
}

//表格选中列底色
.el-table__body tr.current-row > td {
  @include background_color(BG15);
}
// 表格头列禁止时颜色显示
.el-checkbox__input.is-disabled .el-checkbox__inner {
  @include background_color(BG14);
}
// 分组下拉框组头标题颜色修改
.el-select-group__title {
  @include font_color(T6, !important);
}

// 消息提示框中右上角叉叉按件
.el-message-box__headerbtn .el-message-box__close {
  @include font_color(T1);
}
.el-message-box__headerbtn:focus .el-message-box__close,
.el-message-box__headerbtn:hover .el-message-box__close {
  @include font_color(ZS);
}
