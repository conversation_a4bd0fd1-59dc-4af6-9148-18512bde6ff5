<template>
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="dialog">
    <div class="page">
      <div class="clearfix">
        <el-date-picker
          class="fr"
          v-model="CetDatePicker_1.val"
          v-bind="CetDatePicker_1.config"
          placeholder="选择日期"
        ></el-date-picker>
      </div>
      <div class="mtJ3" style="height: calc(100% - 48px)">
        <CetTable
          ref="table"
          style="height: calc(100% - 60px)"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <ElTableColumn
            v-bind="ElTableColumn_index"
            :index="table_index"
          ></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_logtimeText"></ElTableColumn>
          <ElTableColumn
            v-bind="ElTableColumn_managementnumber"
          ></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_measurementname"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_operator"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_result"></ElTableColumn>
        </CetTable>
        <el-pagination
          class="mtJ1"
          style="text-align: center"
          @size-change="handleSizeChange"
          @current-change="handleCurrentPageChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :total="totalCount"
          layout="total,sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
        ></el-pagination>
      </div>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import { httping } from "@omega/http";
export default {
  name: "DeviceCalibrationLog",
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      // 1弹窗组件
      CetDialog_1: {
        title: "校准日志",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {}
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "关闭",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // 设备进入存的设备信息
      deviceInfo: null,
      totalCount: 0,
      pageSize: 20,
      currentPage: 1,
      CetDatePicker_1: {
        disable_in: false,
        val: [
          this.$moment().subtract(1, "month").startOf("day").valueOf(),
          this.$moment().endOf("day").valueOf()
        ],
        config: {
          valueFormat: "timestamp",
          type: "daterange",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          clearable: false,
          style: {
            width: "280px"
          },
          "start-placeholder": "开始日期",
          "end-placeholder": "结束日期"
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      },
      ElTableColumn_index: {
        type: "index",
        label: "序号",
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        width: "50"
      },
      ElTableColumn_logtimeText: {
        prop: "logtimeText",
        label: "时间",
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        minWidth: "150",
        formatter(val) {
          if (val.logtimeText || val.logtimeText === 0) {
            return val.logtimeText;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_managementnumber: {
        prop: "managementnumber",
        label: "设备编号",
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        minWidth: "150",
        formatter(val) {
          if (val.managementnumber || val.managementnumber === 0) {
            return val.managementnumber;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_measurementname: {
        prop: "measurementname",
        label: "校准设备",
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        minWidth: "150",
        formatter(val) {
          if (val.measurementname || val.measurementname === 0) {
            return val.measurementname;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_operator: {
        prop: "operator",
        label: "校准工作人员",
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        minWidth: "150",
        formatter(val) {
          if (val.operator || val.operator === 0) {
            return val.operator;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_result: {
        prop: "result",
        label: "校准结果",
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        formatter(val) {
          if (val.result || val.result === 0) {
            return val.result;
          } else {
            return "--";
          }
        }
      }
    };
  },
  watch: {
    "CetDatePicker_1.val": {
      handler() {
        this.currentPage = 1;
        this.getTableData();
      },
      deep: true
    },
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.CetDatePicker_1.val = [];
      this.CetDialog_1.openTrigger_in = val;
      this.getTableData();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    table_index(index) {
      return (this.currentPage - 1) * this.pageSize + index + 1;
    },
    //分页大小变化
    handleSizeChange() {
      this.getTableData();
      this.currentPage = 1;
    },
    //分页当前页变化
    handleCurrentPageChange() {
      this.getTableData();
    },
    deviceToLog(device) {
      this.deviceInfo = device;
      this.getTableData();
    },
    getTableData() {
      const vm = this;
      vm.$nextTick(() => {
        if (vm.ajaxFlag || vm._.isEmpty(vm.inputData_in)) {
          return;
        }
        vm.ajaxFlag = true;
        vm.CetTable_1.data = [];
        const startTime = vm.CetDatePicker_1.val.length
          ? vm.$moment(vm.CetDatePicker_1.val[0]).startOf("day").valueOf()
          : null;
        const endTime = vm.CetDatePicker_1.val.length
          ? vm.$moment(vm.CetDatePicker_1.val[1]).endOf("day").valueOf()
          : null;
        var data = {
          startTime,
          ids: [vm.inputData_in.id],
          endTime,
          manageNumber: "",
          name: "",
          pageNum: vm.currentPage,
          pageSize: vm.pageSize
        };
        httping({
          url: `/oil-field-service/v1/measure/equip/log/query`,
          method: "POST",
          data
        }).then(
          response => {
            if (
              response.code == 0 &&
              response.data &&
              response.data.instruments &&
              response.data.instruments.length > 0
            ) {
              vm.CetTable_1.data = response.data.instruments;
              vm.CetTable_1.data.forEach(item => {
                item.logtimeText = vm
                  .$moment(item.calibrationtime)
                  .format("YYYY-MM-DD");
              });
            }
            vm.totalCount = response.total;
            vm.ajaxFlag = false;
            vm.$refs.table.$refs.cetTable.doLayout();
          },
          () => {
            vm.ajaxFlag = false;
            vm.$refs.table.$refs.cetTable.doLayout();
          }
        );
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  position: relative;
}
.dialog :deep(.el-dialog__body) {
  height: 550px;
}
</style>
