<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog v-bind="CetDialog_pagedialog" v-on="CetDialog_pagedialog.event">
      <div slot="title">
        <span class="dialogTitle">{{ CetDialog_pagedialog.title }}</span>
        <i class="el-icon-question pointer" @click="downHelp"></i>
      </div>
      <template v-slot:footer>
        <span>
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <div class="bg1 brC2 rowBox">
        <CetForm
          :data.sync="CetForm_pagedialog.data"
          v-bind="CetForm_pagedialog"
          v-on="CetForm_pagedialog.event"
        >
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item prop="name" :label="$T('摄像头名称')">
                <ElInput
                  clearable
                  :placeholder="$T('请输入')"
                  v-model.trim="CetForm_pagedialog.data.name"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('连接类型')" prop="joinType">
                <ElSelect
                  class="fullwidth"
                  v-model="CetForm_pagedialog.data.joinType"
                  v-on="ElSelect_linetype.event"
                >
                  <ElOption
                    v-for="item in ElOption_linetype.options_in"
                    :key="item[ElOption_linetype.key]"
                    :label="item[ElOption_linetype.label]"
                    :value="item[ElOption_linetype.value]"
                    :disabled="item[ElOption_linetype.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="originUrl" :label="$T('视频流地址')">
                <ElInput
                  clearable
                  :placeholder="
                    isClude()
                      ? $T('请输入')
                      : $T('rtsp://账号:密码@ip地址:端口') +
                        '/h264/ch1/main/av_stream'
                  "
                  v-model.trim="CetForm_pagedialog.data.originUrl"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                prop="appName"
                v-if="isClude()"
                :label="$T('云平台序列号')"
              >
                <ElInput
                  clearable
                  :placeholder="$T('请输入')"
                  v-model.trim="CetForm_pagedialog.data.appName"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                prop="streamName"
                v-if="isClude()"
                :label="$T('云平台通道号')"
              >
                <ElInput
                  clearable
                  :placeholder="$T('请输入')"
                  v-model.trim="CetForm_pagedialog.data.streamName"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('摄像头型号')" prop="unitType">
                <ElInput
                  clearable
                  :placeholder="$T('请输入')"
                  v-model.trim="CetForm_pagedialog.data.unitType"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('厂商')" prop="manufacturer">
                <ElInput
                  clearable
                  :placeholder="$T('请输入')"
                  v-model.trim="CetForm_pagedialog.data.manufacturer"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('设备序列号')" prop="deviceSerial">
                <ElInput
                  clearable
                  :placeholder="$T('请输入')"
                  v-model.trim="CetForm_pagedialog.data.deviceSerial"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$T('备注')" prop="remark">
                <ElInput
                  clearable
                  :placeholder="$T('请输入')"
                  v-model.trim="CetForm_pagedialog.data.remark"
                  type="textarea"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="!isClude()">
              <span class="required">*</span>
              <span class="tips">
                {{ $T("直连摄像头请修改音频为AAC，")
                }}{{ $T("视频编码为H264/H265，否则无法播放！") }}
              </span>
            </el-col>
          </el-row>
        </CetForm>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import common from "eem-utils/common";
const Linetypeenumeration = [
  { text: $T("直连"), propertyLabel: "direct" },
  { text: $T("萤石云"), propertyLabel: "YSCloud" }
];
export default {
  name: "CreateVideoDialog",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    editStatus: String
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("添加视频设备"),
        width: "600px",
        "show-close": true,
        event: {}
      },
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "component", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: ["joinType"], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "150px",
        labelPosition: "top",
        showClose: true,
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入摄像头名称"),
              trigger: ["blur"]
            },
            common.pattern_name,
            common.check_name
          ],
          joinType: [
            {
              required: true,
              message: $T("请选择连接类型"),
              trigger: ["blur", "change"]
            }
          ],
          originUrl: [
            {
              required: true,
              message: $T("请输入视频流地址"),
              trigger: ["blur"]
            }
          ],
          appName: [
            {
              required: true,
              message: $T("请输入云平台序列号"),
              trigger: ["blur"]
            }
          ],
          streamName: [
            {
              required: true,
              message: $T("请输入云平台通道号"),
              trigger: ["blur"]
            }
          ]
        },
        event: {
          finishData_out: this.CetForm_pagedialog_finishData_out
        }
      },
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // 连接类型下拉组件
      ElSelect_linetype: {
        value: "direct",
        event: {
          change: this.ElSelect_linetype_change_out
        }
      },
      // 连接类型选项组件
      ElOption_linetype: {
        options_in: Linetypeenumeration,
        key: "propertyLabel",
        value: "propertyLabel",
        label: "text",
        disabled: "disabled"
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.init();
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    }
  },
  methods: {
    isClude() {
      return this.CetForm_pagedialog.data.joinType === "YSCloud";
    },
    ElSelect_linetype_change_out() {},
    CetButton_preserve_statusTrigger_out(val) {
      this.CetForm_pagedialog.data.foldId = this.queryId_in;
      this.CetForm_pagedialog.saveTrigger_in = this._.cloneDeep(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    CetForm_pagedialog_finishData_out(val) {
      this.$emit("finishData_out");
      this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
    },
    // 初始化数据
    init() {
      this.CetForm_pagedialog.resetTrigger_in = new Date().getTime();
      this.CetForm_pagedialog.dataConfig.writeFunc =
        this.editStatus === "edit" ? "videoUpdateVideo" : "videoCreateVideo";
      this.CetDialog_pagedialog.title =
        this.editStatus === "edit" ? $T("编辑视频设备") : $T("添加视频设备");
      if (this.editStatus === "create") {
        this.CetForm_pagedialog.data = {
          joinType: Linetypeenumeration.length
            ? Linetypeenumeration[0].propertyLabel
            : ""
        };
      } else {
        this.CetForm_pagedialog.data = this._.cloneDeep(this.inputData_in);
      }
    },
    // 下载帮助文件
    downHelp() {
      const url = `/eem-service/video/config/file/download/help`;
      common.downExcelGET(url, {}, this.token, this.projectId);
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.el-dialog {
  .rowBox {
    @include padding(J3 J4);
  }
  .dialogTitle {
    @include font_size(H3);
    @include font_color(T2);
    font-weight: bold;
  }
  .el-icon-question {
    @include font_color(T2);
    @include font_size(H2);
  }
}
.required {
  @include font_color(Sta3);
}
.tips {
  @include font_color(T3);
  @include font_size(Ab);
}
</style>
