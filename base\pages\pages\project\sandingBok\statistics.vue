<template>
  <div>
    <div class="fsH1 mtJ2 mbJ2 text-center">
      {{ $T("点检到期") }}/{{ $T("超期统计") }}
    </div>
    <div class="chart">
      <CetChart v-bind="CetChart_1" @click="CetChartClick"></CetChart>
      <div class="legend">
        <div v-for="(item, index) in CetChart_1_legend" :key="index">
          {{ item.value }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
export default {
  name: "statistics",
  props: ["resetTrigger_in"],
  components: {},
  data() {
    return {
      CetChart_1_legend: [],
      CetChart_1: {
        //组件输入项
        inputData_in: null,
        options: {
          color: ["#EB3B3B", "#F3E38C", "#98CCF4", "#5DE27D"],
          legend: {
            type: "scroll",
            bottom: "10px",
            left: "10px",
            orient: "vertical",
            textStyle: {
              width: "120",
              overflow: "truncate"
            },
            tooltip: {
              show: true,
              confine: true,
              position: "bottom"
            }
          },
          tooltip: {
            show: true,
            position: "bottom"
          },
          series: [
            {
              name: "",
              type: "pie",
              radius: ["40%", "60%"],
              center: ["50%", "40%"],
              label: {
                show: true,
                position: "center",
                align: "center",
                formatter: function (val) {
                  return ``;
                },
                rich: {
                  a: {
                    fontSize: 14,
                    padding: [0, 0, 5, 0]
                  },
                  b: {
                    height: 1,
                    width: 80
                  },
                  c: {
                    fontSize: 14,
                    fontWeight: "bold",
                    padding: [0, 0, 5, 0]
                  }
                }
              },
              emphasis: {
                scale: false,
                label: {
                  show: true
                }
              },
              data: []
            }
          ]
        }
      }
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  watch: {
    resetTrigger_in: function (val) {
      this.getData();
    }
  },
  methods: {
    getData() {
      customApi.overdueCheck(this.projectId).then(response => {
        if (response.code === 0) {
          var _this = this;
          var data = this._.get(response, "data", {});
          var total = data.total || 0;
          var newData = {
            expired: data.expired || 0,
            expiresin7d: data.expiresin7d || 0,
            expiresin15d: data.expiresin15d || 0,
            expiresin30d: data.expiresin30d || 0
          };
          var sumNum =
            newData.expiresin7d + newData.expiresin15d + newData.expiresin30d;

          _this.CetChart_1_legend = [];
          for (var key in newData) {
            if (newData[key] && total) {
              _this.CetChart_1_legend.push({
                value:
                  ((newData[key] / total) * 100).toFixed2(0) +
                  "% " +
                  (newData[key] || 0) +
                  $T("个")
              });
            } else {
              _this.CetChart_1_legend.push({
                value: "0 % " + (newData[key] || 0) + $T("个")
              });
            }
          }
          _this.CetChart_1.options.series[0].label.formatter = function (val) {
            return `{c|${sumNum}${$T("个")}}\n{b|}\n{a|${$T("即将点检")}}`;
          };
          _this.CetChart_1.options.series[0].data = [
            {
              value: newData.expired,
              name: $T("点检超期仪表")
            },
            {
              value: newData.expiresin7d,
              name: $T("七天内需点检仪表")
            },
            {
              value: newData.expiresin15d,
              name: $T("15日内需点检仪表")
            },
            {
              value: newData.expiresin30d,
              name: $T("30日内需点检仪表")
            }
          ];
        } else {
          this.reset();
        }
      });
    },
    CetChartClick(val) {
      var nextoverhauldate;
      switch (val.name) {
        case $T("点检超期仪表"):
          nextoverhauldate = [0, this.$moment().startOf("date").valueOf() - 1];
          break;
        case $T("七天内需点检仪表"):
          nextoverhauldate = [
            this.$moment().startOf("date").valueOf(),
            this.$moment().add(7, "d").startOf("date").valueOf() - 1
          ];
          break;
        case $T("15日内需点检仪表"):
          nextoverhauldate = [
            this.$moment().add(7, "d").startOf("date").valueOf(),
            this.$moment().add(15, "d").startOf("date").valueOf() - 1
          ];
          break;
        case $T("30日内需点检仪表"):
          nextoverhauldate = [
            this.$moment().add(15, "d").startOf("date").valueOf(),
            this.$moment().add(30, "d").startOf("date").valueOf() - 1
          ];

          break;

        default:
          break;
      }
      this.$emit("nextoverhauldate_out", nextoverhauldate);
    },
    reset() {
      this.CetChart_1.options.series[0].label.formatter = function (val) {
        return ``;
      };
      this.CetChart_1.options.series[0].data = [];
      this.CetChart_1_legend = [];
    }
  }
};
</script>
<style lang="scss" scoped>
.chart {
  height: 350px;
  position: relative;
}
.legend {
  position: absolute;
  bottom: 10px;
  right: 30px;
  font-size: 14px;
  text-align: right;
  & > div {
    height: 24px;
    line-height: 24px;
  }
}
</style>
