<template>
  <div class="page PQReport eem-common">
    <el-container class="PQReportbox">
      <el-aside width="315px" class="eem-aside flex-column">
        <div class="mbJ3">
          <el-input
            suffix-icon="el-icon-search"
            placeholder="输入关键字以检索"
            v-model="filterText"
          ></el-input>
        </div>
        <div class="flex-auto" style="overflow: auto">
          <el-tree
            ref="powerTree"
            style="width: 100%; height: 100%"
            :data="treeData"
            node-key="tree_id"
            :props="treeProps"
            :filter-node-method="filterNode"
            :highlight-current="true"
            :default-expanded-keys="expandedKeys"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
          ></el-tree>
        </div>
      </el-aside>
      <div class="PQReport-main fullheight mlJ3">
        <div class="fullheight flex-column eem-min-width">
          <div class="page-header mbJ3 eem-cont">
            <el-tooltip :content="headerlabel" effect="light">
              <span
                class="common-title-H2 text-ellipsis lh32 fl"
                style="max-width: 230px; display: inline-block"
              >
                {{ headerlabel }}
              </span>
            </el-tooltip>
            <div class="fr">
              <CetButton
                class="fr"
                v-bind="CetButton2_1"
                v-on="CetButton2_1.event"
              ></CetButton>
              <time-range
                :val.sync="queryTime"
                class="fr mr10 timeRange"
              ></time-range>
            </div>
            <div class="fr mrJ1">
              <customElSelect
                prefix_in="报告类型"
                class="reportType"
                v-model="ElSelect_1.value"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in ElOption_1.options_in"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </customElSelect>
            </div>
          </div>
          <div
            v-show="modelLabels.includes(_.get(currentNode, 'data.modelLabel'))"
            class="reportbox minWH"
            style="flex: 1"
          >
            <PowerQualityAssessment
              key="PowerQualityAssessment_report"
              ref="pqReport"
              :queryParam="queryParam"
              v-if="ElSelect_1.value == 1"
            />
            <PowerQualityIncidentAssessment
              key="PowerQualityIncidentAssessment_report"
              ref="pqEventReport"
              :queryParam="queryParam"
              v-if="ElSelect_1.value == 2"
            />
          </div>
          <el-container
            v-show="
              !modelLabels.includes(_.get(currentNode, 'data.modelLabel'))
            "
            style="flex: 1"
            class="reportempty minWH"
          >
            <p class="text-center w100 fs20 info">请选择设备节点</p>
          </el-container>
        </div>
      </div>
    </el-container>
  </div>
</template>
<script>
import commonApi from "@/api/custom.js";
import ELECTRICAL_DEVICE from "@/store/electricaldevice.js";
import TimeRange from "eem-components/TimeRange";
import PowerQualityAssessment from "./reporttype/PowerQualityAssessment";
import PowerQualityIncidentAssessment from "./reporttype/PowerQualityIncidentAssessment";
import common from "eem-utils/common";
export default {
  name: "PQReport",
  components: {
    TimeRange,
    PowerQualityAssessment,
    PowerQualityIncidentAssessment
  },
  props: {
    selectedMenu: {
      type: String
    }
  },

  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    modelLabels() {
      return ELECTRICAL_DEVICE.map(i => i.value);
    }
  },

  data() {
    const time = common.initDateMonthRange();
    return {
      activatedNum: 0,
      headerlabel: "", // 选中的节点
      treeData: [],
      expandedKeys: [], // 初始展开
      treeProps: {
        children: "children",
        label: "name",
        isLeaf: "leaf"
      },
      currentNode: null,
      filterText: "",
      queryTime: time,
      queryParam: {
        queryTime: {
          startTime: time[0],
          endTime: time[1]
        }
      },
      // 1组件
      ElSelect_1: {
        value: 1,
        style: {
          width: "250px"
        },
        size: "small",
        event: {
          change: this.ElSelect_1_change_out
        }
      },

      // 1组件
      ElOption_1: {
        options_in: [
          {
            id: 1,
            text: "电能质量评估报告"
          },
          {
            id: 2,
            text: "电能质量事件评估报告"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },

      // 1组件
      CetButton2_1: {
        visible_in: true,
        disable_in: false,
        title: "导出",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton2_1_statusTrigger_out
        }
      }
    };
  },
  watch: {
    currentNode: {
      deep: true,
      handler: function (val, oldVal) {
        this.headerlabel = val && val.data && val.data.name;
        this.queryReport();
      }
    },
    queryTime: {
      deep: true,
      handler: function (val, oldVal) {
        this.queryReport();
      }
    },
    "ElSelect_1.value": {
      deep: true,
      handler: function (val, oldVal) {
        this.queryReport();
      }
    },
    filterText(val) {
      this.$refs.powerTree.filter(val);
    },
    selectedMenu: {
      deep: true,
      handler: function (val, oldVal) {
        if (val === "评估报表" && this._.get(this.currentNode, "data.leaf")) {
          this.queryReport();
        }
      }
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1;
    },

    getTreeData() {
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [
          {
            filter: {
              expressions: [{ limit: 1, operator: "EQ", prop: "roomtype" }]
            },
            modelLabel: "room"
          }
        ],
        treeReturnEnable: true
      };
      ELECTRICAL_DEVICE.forEach(item => {
        data.subLayerConditions.push({ modelLabel: item.value });
      });
      commonApi.getPqNodeTree(data, this.projectId, true).then(res => {
        if (res.code === 0) {
          _this.treeData = res.data;
          if (_this._.get(res.data, "[0]")) {
            let currentNode = _this
              .dataTransform(res.data)
              .find(item => this.modelLabels.includes(item.modelLabel));
            _this.$nextTick(() => {
              _this.$refs.powerTree.setCurrentKey(currentNode.tree_id);
              _this.currentNode = _this.$refs.powerTree.getNode(
                currentNode.tree_id
              );
              _this.expandedKeys = [currentNode.tree_id];
            });
          }
        }
      });
    },
    // 节点树数据平铺，用于选中第一个管网设备
    dataTransform(array) {
      const cloneData = this._.cloneDeep(array);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },
    handleNodeClick(obj, node) {
      this.currentNode = node;
      this.headerlabel = node && node.data.name;
    },
    // 1输出
    CetButton2_1_statusTrigger_out(val) {
      if (this.ElSelect_1.value === 1) {
        this.$refs.pqReport.exportReport();
      } else {
        this.$refs.pqEventReport.exportReport();
      }
    },

    // 1输出,方法名要带_out后缀
    ElSelect_1_change_out(val) {},

    // 收集查询参数
    collectQueryParam(exportType) {
      const vm = this;
      if (
        !vm.currentNode ||
        !vm.currentNode.data ||
        vm.currentNode.data.modelLabel === "dcbase"
      ) {
        return null;
      }

      return {
        queryTime: {
          startTime: vm.queryTime[0],
          endTime: vm.queryTime[1]
        },
        params: {
          startTime: vm.queryTime[0],
          endTime: vm.queryTime[1],
          id: vm.currentNode.data.id,
          modelLabel: vm.currentNode.data.modelLabel,
          aggregationCycle: 12 // 固定为日
        }
      };
    },
    queryReport() {
      const params = this.collectQueryParam();
      if (params == null) {
        return;
      }
      this.queryParam = params;
    }
  },
  activated() {
    this.getTreeData();
    if (this.activatedNum) this.refreshTrigger_in = new Date().getTime();
    this.activatedNum++;
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.PQReport {
  .PQReportbox {
    height: 100%;
    padding-top: 10px;
    width: 100%;
    .PQReport-main {
      overflow: auto;
      width: 100%;
      .page-header {
        .timeRange {
          width: calc(100% - 70px);
          max-width: 400px;
        }
        .reportType {
          display: inline-block;
        }
      }
    }
    .reportbox {
      height: calc(100% - 10px);
    }
    .reportempty {
      height: calc(100% - 10px);

      padding: 50px 16px 11px 12px;
    }
  }
}
.tree {
  height: 100%;
  @include background_color(BG1);
  @include padding_left(J2);
  @include padding_right(J2);
  @include border_radius(C3);
}
</style>
<style lang="scss">
// 自定义表格样式
.table-terse {
  width: 100%;
  text-align: center;
  border-collapse: collapse;
}

.table-terse tr th,
.table-terse tr td {
  border: 1px solid;
  @include border_color(B1);
}

.table-terse thead th {
  line-height: 35px;
  font-weight: 700;
}

.table-terse tbody td {
  line-height: 40px;
  // color: #fff;
  padding: 0 10px;
}
</style>
