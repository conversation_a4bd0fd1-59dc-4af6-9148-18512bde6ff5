<template>
  <div class="page eem-common">
    <el-container style="height: 100%; padding: 0px">
      <el-aside
        width="315px"
        class="eem-aside"
        style="height: 100%; position: relative; z-index: 100"
      >
        <CetTree
          :selectNode.sync="CetTree_leftTree.selectNode"
          :checkedNodes.sync="CetTree_leftTree.checkedNodes"
          :searchText_in.sync="CetTree_leftTree.searchText_in"
          v-bind="CetTree_leftTree"
          v-on="CetTree_leftTree.event"
        >
          <span
            class="el-tree-node__label"
            slot-scope="{ node }"
            :level="node.level"
          >
            <span
              :style="
                disabledNodeColor(node, 'data.childSelectState', 1, '#989898')
              "
            >
              {{ node.label }}
            </span>
          </span>
        </CetTree>
      </el-aside>
      <el-container style="padding: 0px 8px 1px 16px; height: 100%">
        <el-header height="48px" style="text-align: center">
          <el-radio-group v-model="selectedMenu" size="small">
            <el-radio-button :label="1">
              {{ $T("成本趋势分析") }}
            </el-radio-button>
            <el-radio-button :label="2">{{ $T("成本核算") }}</el-radio-button>
          </el-radio-group>
        </el-header>
        <div class="clearfix mbJ3 eem-cont">
          <CetButton
            v-if="selectedMenu === 2"
            class="fr mlJ1"
            v-bind="CetButton_export"
            v-on="CetButton_export.event"
          ></CetButton>
          <time-tool
            class="fr"
            :typeID="14"
            :val.sync="startTime"
            @change="changeQueryTime"
            :timeType_in="timeType"
          ></time-tool>
        </div>

        <!-- 成本趋势分析 -->
        <el-main style="padding: 0" class="flex-auto">
          <costTrend
            v-if="selectedMenu === 1"
            :selectTime="selectTime"
            :currentNode="currentNode"
            :queryTime="queryTime"
          ></costTrend>
          <costAccount
            v-if="selectedMenu === 2"
            :selectTime="selectTime"
            :currentNode="currentNode"
            :queryTime="queryTime"
          ></costAccount>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import common from "eem-utils/common";
import TimeTool from "eem-components/TimeTool.vue";
import customApi from "@/api/custom";
import costTrend from "./costTrend.vue";
import costAccount from "./costAccount.vue";
import TREE_PARAMS from "@/store/treeParams.js";
import piemCommon from "@/utils/common";
export default {
  name: "comprehensiveCostAnalysis",
  components: {
    TimeTool,
    costTrend,
    costAccount
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },
  data() {
    return {
      startTime: new Date().getTime(),
      queryTime: {},
      timeType: [
        {
          type: "month",
          text: $T("月"),
          typeID: 14,
          number: 1,
          unit: "M"
        },
        {
          type: "year",
          text: $T("年"),
          number: 1,
          typeID: 17,
          unit: "y"
        }
      ],
      selectTime: "",
      currentNode: null,
      // leftTree树组件
      CetTree_leftTree: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_leftTree_currentNode_out
        }
      },
      selectedMenu: 1,
      // export组件
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      }
    };
  },
  watch: {},
  methods: {
    disabledNodeColor: piemCommon.disabledNodeColor,
    changeQueryTime({ val, timeOption }) {
      const date = this.$moment(val);
      this.queryTime = {
        cycle: timeOption.typeID,
        startTime: date.startOf(timeOption.unit).valueOf(),
        endTime: date.endOf(timeOption.unit).valueOf() + 1
      };
      const mothStr = this.language ? "YYYY-MM" : "YYYY年MM月";
      const yearStr = this.language ? "YYYY" : "YYYY年";
      this.selectTime =
        timeOption.typeID === 14
          ? date.format(mothStr)
          : timeOption.typeID === 17
          ? date.format(yearStr)
          : "--";
    },
    // leftTree 输出
    CetTree_leftTree_currentNode_out(val) {
      if (val.childSelectState == 2) {
        return;
      }
      this.currentNode = this._.cloneDeep(val);
    },
    getTreeData() {
      this.CetTree_leftTree.inputData_in = [];
      const data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.autoManagement,
        treeReturnEnable: true
      };
      customApi.getNodeTree(data).then(res => {
        if (res.code === 0) {
          piemCommon.setTreeDisabledNode(res.data);
          this.CetTree_leftTree.inputData_in = res.data;
          const obj = this._.find(
            piemCommon.findFirstChildSelectState(res.data),
            ["childSelectState", 1]
          );
          this.CetTree_leftTree.selectNode = obj;
        }
      });
    },
    // export输出
    CetButton_export_statusTrigger_out(val) {
      if (this._.isEmpty(this.currentNode)) {
        return;
      }
      const params = {
        node: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          name: this.currentNode.name
        },
        energyType: 13,
        projectId: this.projectId,
        ...this.queryTime
      };
      common.downExcel(
        "/eem-service/v1/costcaculating/energycostcheck/export",
        params,
        this.token,
        this.projectId
      );
    }
  },
  created() {
    this.getTreeData();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
