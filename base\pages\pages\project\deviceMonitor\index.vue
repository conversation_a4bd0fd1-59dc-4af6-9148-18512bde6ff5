﻿<template>
  <div class="page">
    <el-container class="fullheight" v-show="showCetGraph">
      <CetGraph v-bind="CetGraphData"></CetGraph>
    </el-container>
    <div class="no-graph" v-show="!showCetGraph">
      <span>
        {{ $T("暂未配置实时画面，请参照部署包中文档：")
        }}{{ $T("首页概览配置说明文档进行设置！") }}
      </span>
    </div>
    <DiagnosticAnalysisDialog v-bind="Dialog"></DiagnosticAnalysisDialog>
  </div>
</template>

<script>
import CetGraph from "cet-graph";
import { CetGraphConfig } from "cet-graph";
import DiagnosticAnalysisDialog from "../realtimeMonitor/components/DiagnosticAnalysisDialog.vue";

export default {
  name: "RealtimeMonitor",
  components: {
    CetGraph,
    DiagnosticAnalysisDialog
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userInfo() {
      return this.$store.state.userInfo;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    isEn() {
      return (
        this.$store.state.systemCfg.internationalization &&
        window.localStorage.getItem("omega_language") === "en"
      );
    }
  },
  data() {
    return {
      Dialog: {
        condition: null,
        show_trigger_in: -1
      },
      CetGraphData: {
        path_in: "",
        refresh_trigger_in: 0,
        userName_in: "ROOT",
        excuteJSInterception: async (details, done) => {
          const js = details.javascript;

          if (js !== "@expertsuggestion") {
            done();
            return;
          }

          const target = details.target;
          if (!target?.blinking) {
            return;
          }

          const condition = target.activatedBlinkingCondition;
          if (!condition) {
            return;
          }

          this.Dialog.condition = {
            deviceId: condition.deviceId,
            measureId: condition.measureId,
            projectId: this.$store.state.projectId
          };
          this.Dialog.show_trigger_in = Date.now();
        }
      },
      showCetGraph: false,
      menu: this.$store.state.initialInfo.menuList,
      routeArr: [
        "/energyFlowAnalyse",
        "/generatrixMonitor",
        "/deviceMonitor",
        "/videoMonitor",
        "/home",
        "/energyConsumption",
        "/balanceConsumption",
        "/plantEnvironment",
        "/energyPredictionAndAnalysis",
        "/energyCostAnalysis",
        "/energySavingPotentialMining",
        "/energySavingBenefitEvaluation",
        "/transformerEnergyEfficiency",
        "/refrigerationEfficiency",
        "/airCompressionEnergyEfficiency",
        "/boilerEnergyEfficiency"
      ]
    };
  },
  watch: {
    // 直接修改url时，定制框架页面
    $route(to, from) {
      if (
        this.routeArr.indexOf(from.path) !== -1 &&
        this.routeArr.indexOf(to.path) !== -1
      ) {
        const obj = this.findLocation(this.menu, this.$route.path);
        if (obj && (obj.graphPath || obj.graphPathEn)) {
          this.showCetGraph = true;
          this.CetGraphData.path_in = this.isEn
            ? obj.graphPathEn
            : obj.graphPath;
          this.CetGraphData.refresh_trigger_in = new Date().getTime();
        } else {
          this.showCetGraph = false;
        }
      }
    }
  },

  methods: {
    findLocation(menu, path) {
      for (let index = 0; index < menu.length; index++) {
        if (menu[index].location === path) {
          return menu[index];
        } else if (menu[index].subMenuList && menu[index].subMenuList.length) {
          var obj = this.findLocation(menu[index].subMenuList, path);
          if (obj) {
            return obj;
          }
        }
      }
    }
  },
  created: function () {
    this.CetGraphData.userName_in = this.userInfo.name;
    localStorage.setItem("token", this.token);
    CetGraphConfig.enablePTZControl = this.systemCfg.enablePTZControl;
    CetGraphConfig.isNewAuth = this.systemCfg.isNewAuth;
    const enLanguage = window.localStorage.getItem("omega_language") === "en";
    CetGraphConfig.locale = enLanguage ? "en" : "zh-cn";
  },
  activated: function () {
    const obj = this.findLocation(this.menu, this.$route.path);
    if (obj && (obj.graphPath || obj.graphPathEn)) {
      this.showCetGraph = true;
      //开启缓存页面状态，切换页面，如果还是上次画面，不需要重新渲染
      let path = this.isEn ? obj.graphPathEn : obj.graphPath;
      if (this.CetGraphData.path_in !== path || !this.systemCfg.cachePage) {
        this.CetGraphData.path_in = path;
        this.CetGraphData.refresh_trigger_in = new Date().getTime();
      }
    } else {
      this.showCetGraph = false;
    }
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  // position: relative;
  .no-graph {
    height: 100%;
    text-align: center;
    padding: 200px 10% 0px;
    box-sizing: border-box;
    span {
      @include font_size(H1);
    }
  }
}
</style>
