<template>
  <div class="CustomElDatePicker">
    <span class="plJ1 prJ1 label">
      {{ prefix_in }}
    </span>
    <el-date-picker
      v-bind="$attrs"
      v-on="$listeners"
      class="datePicker"
      ref="datePicker"
    ></el-date-picker>
  </div>
</template>

<script>
export default {
  name: "CustomElDatePicker",
  props: {
    prefix_in: String
  },
  watch: {
    prefix_in() {
      this.init();
    }
  },
  methods: {
    init() {
      const _this = this;
      if (_this.prefix_in) {
        let span = document.createElement("span");
        span.innerText = _this.prefix_in;
        span.style.fontSize = "12px";
        document.getElementsByTagName("body")[0].append(span);
        let offsetWidth = span.offsetWidth;
        span.remove();
        setTimeout(() => {
          if (!_this.$refs.datePicker) {
            return;
          }
          let dom = $(_this.$refs.datePicker.$el);
          if (dom.attr("class").includes("el-range-editor")) {
            $(dom).css({
              "padding-left": offsetWidth + 22 + "px"
            });
          } else {
            $(dom)
              .find(".el-input__inner")
              .css({
                "padding-left": offsetWidth + 30 + "px"
              });
          }
        }, 100);
      }
    }
  },
  mounted() {
    this.init();
  },
  activated() {
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.CustomElDatePicker {
  display: inline-block;
  position: relative;
  .label {
    position: absolute;
    left: 4px;
    @include font_color(ZS);
    @include font_size("Aa");
    @include line_height(Hm);
    z-index: 1;
  }
  .datePicker {
    box-sizing: border-box;
    :deep(.el-input__inner) {
      @include font_size("Aa");
    }
    :deep(.el-input__prefix) {
      left: auto;
      right: 5px;
    }
    :deep(.el-input__icon.el-range__icon.el-icon-date) {
      position: absolute;
      right: 5px;
    }
  }
}
</style>
