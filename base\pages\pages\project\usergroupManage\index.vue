<template>
  <div class="eem-common fullfilled">
    <div class="fullheight treeBox eem-aside flex-column">
      <div class="common-title-H1">{{ $T("用户组列表") }}</div>
      <CetTree
        class="CetTree mtJ3 flex-auto eem-no-line"
        :selectNode.sync="CetTree_UserGroups.selectNode"
        :checkedNodes.sync="CetTree_UserGroups.checkedNodes"
        v-bind="CetTree_UserGroups"
        v-on="CetTree_UserGroups.event"
      ></CetTree>
      <CetInterface
        :data.sync="CetInterface_UserGroups.data"
        :dynamicInput.sync="CetInterface_UserGroups.dynamicInput"
        v-bind="CetInterface_UserGroups"
        v-on="CetInterface_UserGroups.event"
      ></CetInterface>
      <div class="clearfix">
        <CetButton
          class="mtJ3 fr"
          v-permission="'usergroup_create'"
          v-bind="CetButton_Add"
          v-on="CetButton_Add.event"
        ></CetButton>
      </div>
    </div>
    <div class="contentBox mlJ2">
      <div class="mainContent eem-cont">
        <div class="clearfix">
          <span class="common-title-H2">
            {{ currentNode ? currentNode.name : "" }}
          </span>
          <CetButton
            class="mlJ1 fr"
            v-permission="'usergroup_update'"
            v-bind="CetButton_edit"
            v-on="CetButton_edit.event"
          ></CetButton>
          <CetButton
            class="mlJ1 fr"
            v-permission="'usergroup_delete'"
            v-bind="CetButton_del"
            v-on="CetButton_del.event"
          ></CetButton>
        </div>
        <div class="wrap mtJ2">
          <div class="item bg mrJ2">
            <el-tooltip effect="light" :content="$T('包含的用户数(个)')">
              <div class="label text-ellipsis ptJ1 pbJ1 plJ2 prJ2">
                {{ $T("包含的用户数(个)") }}
              </div>
            </el-tooltip>
            <el-tooltip
              effect="light"
              :content="CetTable_user.data.length.toString()"
            >
              <div class="value text-ellipsis pbJ2 plJ2 prJ2">
                {{ CetTable_user.data.length }}
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
      <div class="detailContent mtJ2 eem-cont">
        <div class="fullfilled">
          <CetTable
            :data.sync="CetTable_user.data"
            :dynamicInput.sync="CetTable_user.dynamicInput"
            v-bind="CetTable_user"
            v-on="CetTable_user.event"
          >
            <template v-for="item in Columns_user">
              <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
            </template>
          </CetTable>
        </div>
      </div>
    </div>
    <AddOrEditUserGroupDialog
      :userGroupData_in="AddOrEditUserGroupDialog.userGroupData_in"
      :isEditMode="AddOrEditUserGroupDialog.isEditMode"
      :openTrigger_in="AddOrEditUserGroupDialog.openTrigger_in"
      @userGroupsChanged_out="refresh"
    ></AddOrEditUserGroupDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import AddOrEditUserGroupDialog from "./basecomponents/AddOrEditUserGroupDialog";
import common from "eem-utils/common";
export default {
  name: "UserGroupManage",
  components: { AddOrEditUserGroupDialog },
  data() {
    return {
      currentNode: null,
      // 用户组树组件
      CetTree_UserGroups: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "userGroups"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_UserGroups_currentNode_out
        }
      },
      // 用户组列表组件
      CetInterface_UserGroups: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "getUserGroups",
          modelLabel: "usergroup",
          dataIndex: [],
          modelList: [],
          filters: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {},
        page_in: null, // exp:{ index: 1, limit: 20 }
        defaultSort: null,
        event: {
          result_out: this.CetInterface_UserGroups_result_out
        }
      },

      // 增加用户组组件
      CetButton_Add: {
        visible_in: true,
        disable_in: false,
        title: $T("新增用户组"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_Add_statusTrigger_out
        }
      },
      CetButton_edit: {
        visible_in: true,
        disable_in: false,
        title: $T("修改"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_edit_statusTrigger_out
        }
      },
      CetButton_del: {
        visible_in: true,
        disable_in: true,
        title: $T("删除"),
        plain: true,
        type: "danger",
        event: {
          statusTrigger_out: this.CetButton_del_statusTrigger_out
        }
      },
      CetTable_user: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 10,
          layout: "sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        highlightCurrentRow: false,
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      },
      Columns_user: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "41" //绝对宽度
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("用户名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "mobilePhone", // 支持path a[0].b
          label: $T("移动电话"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "email", // 支持path a[0].b
          label: $T("电子邮箱"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ],
      UserGroupDetailContainer: {
        userGroupData_in: null,
        refreshTrigger_in: new Date().getTime()
      },

      // 新增或编辑用户组弹窗
      AddOrEditUserGroupDialog: {
        isEditMode: false,
        userGroupData_in: null,
        openTrigger_in: new Date().getTime()
      }
    };
  },
  computed: {
    projectTenantId() {
      var vm = this;
      return vm.$store.state.projectTenantId;
    }
  },
  methods: {
    // 用户组树组件输出
    CetTree_UserGroups_currentNode_out(val) {
      this.currentNode = val;
      this.initUserTable();
    },
    initUserTable() {
      let tableData = this._.get(this.currentNode, "children") || [];
      if (tableData.length) {
        this.CetButton_del.disable_in = true;
      } else {
        this.CetButton_del.disable_in = false;
      }

      this.CetTable_user.data = tableData;
    },
    CetButton_Add_statusTrigger_out() {
      this.AddOrEditUserGroupDialog.userGroupData_in = null;
      this.AddOrEditUserGroupDialog.isEditMode = false;
      this.AddOrEditUserGroupDialog.openTrigger_in = new Date().getTime();
    },
    CetButton_edit_statusTrigger_out() {
      this.editUserGroup(this.currentNode);
    },
    CetButton_del_statusTrigger_out() {
      this.deleteUserGroup(this.currentNode);
    },
    // 用户组接口输出
    CetInterface_UserGroups_result_out(val) {
      let vm = this;
      let treeData = val || [];

      if (!vm._.get(vm.CetTree_UserGroups, "selectNode.id")) {
        vm.CetTree_UserGroups.selectNode = vm._.get(treeData, "[0]", null);
      }
      vm.CetTree_UserGroups.inputData_in = this._.cloneDeep(treeData);
    },
    // 获取用户组列表信息
    getTreeData() {
      var _this = this;
      let data = {
        loadChildren: true,
        removeRootUser: true,
        tenantId: _this.projectTenantId
      };
      customApi.queryProjectUserAndGroup(data).then(res => {
        const treeData = _this._.get(res, "data", []) || [];
        if (!_this._.get(_this.CetTree_UserGroups, "selectNode.id")) {
          _this.CetTree_UserGroups.selectNode = _this._.get(
            treeData,
            "[0]",
            null
          );
        }
        _this.CetTree_UserGroups.inputData_in = this._.cloneDeep(treeData);
      });
    },
    editUserGroup(data) {
      this.AddOrEditUserGroupDialog.userGroupData_in = this._.cloneDeep(data);
      this.AddOrEditUserGroupDialog.isEditMode = true;
      this.AddOrEditUserGroupDialog.openTrigger_in = new Date().getTime();
    },

    deleteUserGroup(data) {
      let vm = this;
      vm.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          customApi["deleteUserGroup"]({
            id: data.id,
            name: data.name
          }).then(res => {
            if (res.code === 0) {
              vm.$message.success($T("删除成功！"));
              vm.CetTree_UserGroups.selectNode = null;
              vm.refresh();
            }
          });
        })
        .catch(() => {});
    },

    refresh() {
      // this.CetInterface_UserGroups.queryTrigger_in = new Date().getTime();
      this.getTreeData();
    }
  },
  activated() {
    this.CetTree_UserGroups.selectNode = null;
    this.refresh();
  }
};
</script>
<style lang="scss" scoped>
.eem-common {
  display: flex;
  .treeBox {
    width: 315px;
  }
  .contentBox {
    flex: 1;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    min-width: 0;
    .mainContent {
      .wrap {
        display: flex;
        overflow: auto;
        .item {
          flex: none;
          @include border_radius(C);
          width: 160px;
          min-width: 0;
          .label {
            @include font_color(T3);
          }
          .value {
            @include font_size(H3);
            font-weight: bold;
          }
        }
      }
    }
    .detailContent {
      flex: 1;
      min-height: 0;
      box-sizing: border-box;
    }
  }
}
</style>
