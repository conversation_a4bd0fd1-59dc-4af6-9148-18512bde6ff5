<template>
  <div class="page">
    <el-container style="height: 100%">
      <el-aside
        class="mainBox leftTree"
        :class="{ 'aside-collapsed': collapsed }"
        :width="asideWidth"
      >
        <CetTree
          v-show="!collapsed"
          ref="tree"
          class="tree"
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          :searchText_in.sync="CetTree_1.searchText_in"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
        ></CetTree>
        <div class="collapse-btn" @click="onCollapseBtnClick">
          {{ collapseText }}
        </div>
      </el-aside>
      <el-container class="mlJ3" style="height: 100%">
        <el-header class="mainBox clearfix" height="64px">
          <ElInput
            class="fl mrJ1"
            v-model="ElInput_1.value"
            v-bind="ElInput_1"
            v-on="ElInput_1.event"
          ></ElInput>
          <ElInput
            class="fl mrJ1"
            v-model="ElInput_2.value"
            v-bind="ElInput_2"
            v-on="ElInput_2.event"
          ></ElInput>
          <div class="basic-box fl">
            <div class="basic-box-label">状态</div>
            <ElSelect
              class="fl"
              v-model="ElSelect_1.value"
              v-bind="ElSelect_1"
              v-on="ElSelect_1.event"
            >
              <ElOption
                v-for="item in ElOption_1.options_in"
                :key="item[ElOption_1.key]"
                :label="item[ElOption_1.label]"
                :value="item[ElOption_1.value]"
                :disabled="item[ElOption_1.disabled]"
              ></ElOption>
            </ElSelect>
          </div>
          <div class="fr">
            <CetButton
              class="mlJ1"
              v-bind="CetButton_5"
              v-on="CetButton_5.event"
            ></CetButton>
            <CetButton
              class="mlJ1"
              v-bind="CetButton_3"
              v-on="CetButton_3.event"
            ></CetButton>
            <CetButton
              class="mlJ1"
              v-bind="CetButton_2"
              v-on="CetButton_2.event"
            ></CetButton>
          </div>
        </el-header>
        <el-main class="mtJ3 mainBox">
          <CetButton
            class="fl deleteBtn"
            v-bind="CetButton_delete"
            v-on="CetButton_delete.event"
          ></CetButton>
          <CetTable
            style="height: calc(100% - 80px)"
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
          >
            <ElTableColumn v-bind="ElTableColumn_seletion"></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_index"
              :index="table_index"
            ></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_managementnumber"
            ></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_measurementname"
            ></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_checkmethod"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_abcclass"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_accuracy"></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_accuracyrequire"
            ></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_plannedcalibrationcycle"
            ></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_latestcalibrationcycle"
            ></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_usagestate"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_detail">
              <template slot-scope="scope">
                <span title="校准">
                  <omega-icon
                    slot="reference"
                    class="table-icon mrJ1"
                    symbolId="calibration"
                    @click="handleCalibration(scope.row)"
                  ></omega-icon>
                </span>
                <span title="校准日志">
                  <omega-icon
                    slot="reference"
                    class="table-icon"
                    symbolId="calibration_log"
                    @click="handleCalibrationLog(scope.row)"
                  ></omega-icon>
                </span>
                <span title="查看详情">
                  <i class="row-i detail" @click="handleDetail(scope.row)"></i>
                </span>
                <span title="编辑">
                  <i class="row-i edit" @click="handleEdit(scope.row)"></i>
                </span>
                <span title="删除">
                  <omega-icon
                    class="table-icon mlJ1"
                    symbolId="delete-lin"
                    @click="handleDelete(scope.row)"
                  ></omega-icon>
                </span>
              </template>
            </ElTableColumn>
          </CetTable>
          <el-pagination
            class="mtJ1"
            style="text-align: center"
            @size-change="handleSizeChange"
            @current-change="handleCurrentPageChange"
            :current-page.sync="currentPage"
            :page-size.sync="pageSize"
            :total="totalCount"
            layout="total,sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100]"
          ></el-pagination>
        </el-main>
      </el-container>
    </el-container>
    <addDevice
      :visibleTrigger_in="addDevice.visibleTrigger_in"
      :closeTrigger_in="addDevice.closeTrigger_in"
      :queryId_in="addDevice.queryId_in"
      :inputData_in="addDevice.inputData_in"
      @finishTrigger_out="addDevice_finishTrigger_out"
    />
    <addDeviceCalibrationLog
      :visibleTrigger_in="addDeviceCalibrationLog.visibleTrigger_in"
      :closeTrigger_in="addDeviceCalibrationLog.closeTrigger_in"
      :queryId_in="addDeviceCalibrationLog.queryId_in"
      :inputData_in="addDeviceCalibrationLog.inputData_in"
      @finishTrigger_out="addDeviceCalibrationLog_finishTrigger_out"
    />
    <deviceDetail
      :visibleTrigger_in="deviceDetail.visibleTrigger_in"
      :closeTrigger_in="deviceDetail.closeTrigger_in"
      :queryId_in="deviceDetail.queryId_in"
      :inputData_in="deviceDetail.inputData_in"
    />
    <calibrationLog
      :visibleTrigger_in="calibrationLog.visibleTrigger_in"
      :closeTrigger_in="calibrationLog.closeTrigger_in"
      :queryId_in="calibrationLog.queryId_in"
      :inputData_in="calibrationLog.inputData_in"
    />
    <UploadDialog v-bind="uploadDialog" v-on="uploadDialog.event" />
  </div>
</template>
<script>
import { httping } from "@omega/http";
import customApi from "@/api/custom";
import common from "@/utils/common";
import UploadDialog from "eem-components/uploadDialog";
import addDevice from "./deviceLedger/addDevice.vue";
import addDeviceCalibrationLog from "./deviceLedger/addDeviceCalibrationLog.vue";
import calibrationLog from "./deviceLedger/calibrationLog.vue";
import deviceDetail from "./deviceLedger/deviceDetail.vue";
export default {
  name: "DeviceLedger",
  components: {
    UploadDialog,
    addDevice,
    addDeviceCalibrationLog,
    calibrationLog,
    deviceDetail
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    asideWidth() {
      return this.collapsed ? "15px" : "320px";
    },
    collapseText() {
      return this.collapsed ? ">" : "<";
    }
  },

  data(vm) {
    return {
      collapsed: false,
      tabtitle: "1",
      currentNode: null,
      tableSelectRowId: [],
      totalCount: 0,
      pageSize: 20,
      currentPage: 1,
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: "设备名称",
        style: {
          width: "200px"
        },
        event: { input: this.filter_change }
      },
      ElInput_2: {
        value: "",
        placeholder: "设备编号",
        style: {
          width: "200px"
        },
        event: { input: this.filter_change }
      },
      ElSelect_1: {
        value: 0,
        event: { change: this.filter_change }
      },
      ElOption_1: {
        options_in: [
          {
            id: 0,
            text: "所有"
          },
          {
            id: 1,
            text: "在用"
          },
          {
            id: 2,
            text: "停用"
          },
          {
            id: 3,
            text: "备用"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          "selection-change": this.CetTable_1_selection_change
        },
        style: {
          "text-align": "center"
        }
      },
      // seletion组件
      ElTableColumn_seletion: {
        type: "selection", //  勾选 index 序号
        prop: "", // 支持path a[0].b
        label: "", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        label: "序号", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        width: "50" //绝对宽度
      },
      // detail组件
      ElTableColumn_detail: {
        prop: "", // 支持path a[0].b
        label: "操作", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        width: "140" //绝对宽度
      },
      ElTableColumn_managementnumber: {
        prop: "managementnumber", // 支持path a[0].b
        label: "设备编号", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.managementnumber || val.managementnumber === 0) {
            return val.managementnumber;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_name: {
        prop: "name", // 支持path a[0].b
        label: "名称", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.name || val.name === 0) {
            return val.name;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_measurementname: {
        prop: "measurementname", // 支持path a[0].b
        label: "测量对象", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.measurementname || val.measurementname === 0) {
            return val.measurementname;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_checkmethod: {
        prop: "checkmethod", // 支持path a[0].b
        label: "检定方式", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        width: "80", //绝对宽度
        formatter: function (val) {
          if (val.checkmethod === 1) {
            return "内部";
          } else if (val.checkmethod === 2) {
            return "送外";
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_abcclass: {
        prop: "abcclass", // 支持path a[0].b
        label: "ABC分类", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        width: "80", //绝对宽度
        formatter: function (val) {
          if (val.abcclass) {
            return val.abcclass;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_accuracy: {
        prop: "accuracy", // 支持path a[0].b
        label: "准确度", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        width: "80", //绝对宽度
        formatter: function (val) {
          if (val.accuracy || val.accuracy === 0) {
            return val.accuracy;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_accuracyrequire: {
        prop: "accuracyrequire", // 支持path a[0].b
        label: "准确度要求", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        width: "100", //绝对宽度
        formatter: function (val) {
          if (val.accuracyrequire || val.accuracyrequire === 0) {
            return val.accuracyrequire;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_plannedcalibrationcycle: {
        prop: "plannedcalibrationcycle", // 支持path a[0].b
        label: "校准周期", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        width: "90", //绝对宽度
        formatter: function (val) {
          if (
            val.plannedcalibrationcycle ||
            val.plannedcalibrationcycle === 0
          ) {
            return val.plannedcalibrationcycle;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_latestcalibrationcycle: {
        prop: "latestcalibrationcycle", // 支持path a[0].b
        label: "最近校准日期", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.latestcalibrationcycle) {
            return vm.$moment(val.latestcalibrationcycle).format("YYYY-MM-DD");
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_usagestate: {
        prop: "usagestate", // 支持path a[0].b
        label: "使用状态", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        width: "80", //绝对宽度
        formatter: function (val) {
          if (val.usagestate == 1) {
            return "在用";
          } else if (val.usagestate == 2) {
            return "停用";
          } else if (val.usagestate == 3) {
            return "备用";
          } else {
            return "--";
          }
        }
      },
      CetButton_delete: {
        visible_in: true,
        disable_in: true,
        type: "danger",
        icon: "el-icon-delete",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_delete_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: "导出",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetButton_3: {
        visible_in: true,
        disable_in: false,
        title: "导入",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      CetButton_5: {
        visible_in: true,
        disable_in: false,
        title: "新建设备",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_5_statusTrigger_out
        }
      },
      uploadDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        extensionNameList_in: [".xlsx"],
        hideDownload: false,
        dialogTitle: $T("导入台账"),
        event: {
          download: this.uploadDialog_download,
          uploadFile: this.uploadDialog_uploadFile
        }
      },
      addDevice: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addDeviceCalibrationLog: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      calibrationLog: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      deviceDetail: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      }
    };
  },
  watch: {
    currentNode(val) {
      val && this.init();
    }
  },

  methods: {
    onCollapseBtnClick() {
      this.collapsed = !this.collapsed;
    },
    filter_change() {
      this.currentPage = 1;
      this.getTableData();
    },
    table_index(index) {
      return (this.currentPage - 1) * this.pageSize + index + 1;
    },
    init() {
      this.ElInput_1.value = "";
      this.ElInput_2.value = "";
      this.ElSelect_1.value = 0;
    },

    handleDetail(row) {
      this.deviceDetail.inputData_in = this._.cloneDeep(row);
      this.deviceDetail.visibleTrigger_in = new Date().getTime();
    },
    handleDelete(row) {
      const vm = this;
      vm.$confirm("确定要删除吗？", "提示", {
        distinguishCancelAndClose: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          httping({
            url: `/model/v1/measuringinstrument`,
            method: "DELETE",
            data: [row.id]
          }).then(response => {
            if (response.code == 0) {
              vm.$message.success("删除成功");
              vm.getTableData();
            }
          });
        })
        .catch(() => {
          vm.$message.info("已取消");
        });
    },
    handleEdit(row) {
      this.addDevice.inputData_in = this._.cloneDeep(row);
      this.addDevice.queryId_in = null;
      this.addDevice.visibleTrigger_in = +new Date();
    },
    handleCalibration(row) {
      this.addDeviceCalibrationLog.inputData_in = this._.cloneDeep(row);
      this.addDeviceCalibrationLog.visibleTrigger_in = +new Date();
    },
    handleCalibrationLog(row) {
      this.calibrationLog.inputData_in = this._.cloneDeep(row);
      this.calibrationLog.visibleTrigger_in = +new Date();
    },

    // 获取节点树
    async getOilTree() {
      const data = {
        rootID: 0,
        rootLabel: "project",
        subLayerConditions: [
          { modelLabel: "oilcompany" },
          { modelLabel: "oilproductionplant" },
          { modelLabel: "operationarea" },
          { modelLabel: "oilproductioncrew" },
          { modelLabel: "combinedstation" },
          { modelLabel: "platform" },
          { modelLabel: "oiltransferstation" },
          { modelLabel: "waterinjectionstation" },
          { modelLabel: "oilwell" },
          { modelLabel: "waterwell" },
          { modelLabel: "gasmine" },
          { modelLabel: "gasoperationarea" },
          { modelLabel: "gasplatform" },
          { modelLabel: "dehydratingstation" },
          { modelLabel: "gasgatheringstation" },
          { modelLabel: "purificationplant" },
          { modelLabel: "gaswell" },
          { modelLabel: "purificationcompany" },
          { modelLabel: "sectionarea" },
          { modelLabel: "building" },
          { modelLabel: "floor" },
          {
            modelLabel: "room",
            filter: {
              expressions: [
                {
                  limit: null,
                  operator: "EQ",
                  prop: "roomtype"
                }
              ]
            }
          },
          { modelLabel: "manuequipment" },
          { modelLabel: "meteorologicalmonitor" },
          { modelLabel: "airconditioner" },
          { modelLabel: "coldwatermainengine" },
          { modelLabel: "windset" },
          { modelLabel: "coolingtower" },
          { modelLabel: "plateheatexchanger" },
          { modelLabel: "aircompressor" },
          { modelLabel: "colddryingmachine" },
          { modelLabel: "dryingmachine" },
          { modelLabel: "boiler" },
          { modelLabel: "civicpipe" },
          { modelLabel: "mechanicalminingmachine" },
          { modelLabel: "pump" },
          { modelLabel: "gascompressor" },
          { modelLabel: "heatingfurnace" }
        ],
        treeReturnEnable: true
      };

      const res = await customApi.queryNodeTreeConfig({
        configtype: 1,
        functionkey: "2a551d72-1849-3f72-c33e-7289c8703609"
      });
      const queryData = res.data[0]?.value
        ? JSON.parse(res.data[0]?.value)
        : data;
      const treeRes = await customApi.getNodeTree(queryData);
      if (treeRes.code === 0) {
        this.CetTree_1.inputData_in = treeRes.data;
        this.CetTree_1.filterNodes_in = this.getFilterNodes(treeRes.data);
        if (
          !this.CetTree_1.selectNode.tree_id &&
          this.CetTree_1.inputData_in &&
          this.CetTree_1.inputData_in.length > 0
        ) {
          this.CetTree_1.selectNode = this.CetTree_1.inputData_in[0];
        }
      }
    },
    getFilterNodes(treeData, filterNodes = []) {
      treeData.forEach(item => {
        if (
          ![
            "pump",
            "heatingfurnace",
            "mechanicalminingmachine",
            "gascompressor"
          ].includes(item.modelLabel)
        ) {
          filterNodes.push(item);
        }
        if (!this._.isEmpty(item.children)) {
          this.getFilterNodes(item.children, filterNodes);
        }
      });
      return this._.isEmpty(filterNodes) ? null : filterNodes;
    },
    //分页大小变化
    handleSizeChange() {
      this.currentPage = 1;
      this.tableSelectRowId = [];
      this.getTableData();
    },
    //分页当前页变化
    handleCurrentPageChange() {
      this.tableSelectRowId = [];
      this.getTableData();
    },
    // 请求列表数据
    getTableData() {
      this.CetTable_1.data = [];
      if (!this.currentNode) {
        return;
      }
      const obj = {},
        data = [];
      this.getChildren(this.currentNode, obj);

      Object.keys(obj).forEach(item => {
        data.push({
          ids: obj[item],
          modelLabel: item
        });
      });
      const queryData = {
        name: this.ElInput_1.value,
        manageNumber: this.ElInput_2.value,
        usageState: this.ElSelect_1.value,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        nodeLists: data.length > 0 ? data : null,
        type: "oil"
      };
      httping({
        url: `/oil-field-service/v1/measure/equip/queryByNode`,
        method: "POST",
        data: queryData
      }).then(response => {
        if (response.code == 0 && response.data) {
          const resData = this._.get(response, "data.instruments", []);
          const NewResData = resData.sort(function (a, b) {
            return a.id - b.id;
          });
          this.CetTable_1.data = this._.cloneDeep(NewResData);
        }
        this.totalCount = this._.get(response, "total", 0) || 0;
      });
    },
    // 获取当前层级下的所有子节点
    getChildren(node, obj) {
      // if (
      //   node.modelLabel == "mechanicalminingmachine" ||
      //   node.modelLabel == "pump" ||
      //   node.modelLabel == "heatingfurnace" ||
      //   node.modelLabel == "gascompressor"
      // ) {
      // 找到设备

      if (
        node.modelLabel === this.currentNode.modelLabel &&
        node.id === this.currentNode.id
      ) {
        if (node.children && node.children.length > 0) {
          node.children.forEach(item => {
            this.getChildren(item, obj);
          });
        }
        return;
      }
      if (obj[node.modelLabel]) {
        if (obj[node.modelLabel].indexOf(node.id) == -1) {
          obj[node.modelLabel].push(node.id);
        }
      } else {
        obj[node.modelLabel] = [node.id];
      }
      // } else {
      if (node.children && node.children.length > 0) {
        node.children.forEach(item => {
          this.getChildren(item, obj);
        });
      }
      // }
    },
    addDevice_finishTrigger_out() {
      this.getTableData();
    },
    addDeviceCalibrationLog_finishTrigger_out() {
      this.getTableData();
    },

    CetTable_1_selection_change(val) {
      this.tableSelectRowId = [];
      this.CetButton_delete.disable_in = val.length === 0;
      if (val.length === 0) return;
      val.forEach(item => {
        this.tableSelectRowId.push(item.id);
      });
    },
    CetTree_1_currentNode_out(val) {
      if (!val) {
        return;
      }
      this.currentNode = val;
      this.currentPage = 1;
      this.getTableData();
    },
    // 删除
    CetButton_delete_statusTrigger_out() {
      const vm = this;
      vm.$confirm("确定要删除所选行吗？", "提示", {
        distinguishCancelAndClose: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          httping({
            url: `/model/v1/measuringinstrument`,
            method: "DELETE",
            data: [...vm.tableSelectRowId]
          }).then(response => {
            if (response.code == 0) {
              vm.$message.success("删除成功");
              vm.getTableData();
            }
          });
        })
        .catch(() => {
          vm.$message.info("已取消");
        });
    },
    // 导出
    CetButton_2_statusTrigger_out() {
      const vm = this;
      vm.$confirm("确定要导出吗？", "提示", {
        distinguishCancelAndClose: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          var obj = {},
            data = [];
          vm.getChildren(vm.currentNode, obj);
          Object.keys(obj).forEach(item => {
            data.push({
              ids: obj[item],
              modelLabel: item
            });
          });
          common.downExcelWithFileName(
            `/oil-field-service/v1/measure/equip/export`,
            {
              name: vm.ElInput_1.value,
              manageNumber: vm.ElInput_2.value,
              usageState: vm.ElSelect_1.value,
              pageNum: vm.currentPage,
              pageSize: vm.pageSize,
              nodeLists: data.length > 0 ? data : null
            },
            vm.token,
            `计量器具台账`
          );
        })
        .catch(action => {
          action === "cancel" && vm.$message.info("已取消");
        });
    },
    // 导入
    CetButton_3_statusTrigger_out() {
      this.uploadDialog.openTrigger_in = +new Date();
    },
    // 新建
    CetButton_5_statusTrigger_out(val) {
      this.addDevice.inputData_in = {};
      this.addDevice.queryId_in = null;
      this.addDevice.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 筛选节点树
    filterTree(children, modelLabel) {
      if (children && children.length > 0) {
        if (children[0].modelLabel == modelLabel) {
          // 是要去除的这层
          var arr = JSON.parse(JSON.stringify(children));
          children = [];
          arr.forEach(item => {
            children = Array.concat(children, item.children || []);
          });
          return children;
        } else {
          return children.map(item => {
            item.children = this.filterTree(item.children, modelLabel);
            return item;
          });
        }
      } else {
        return [];
      }
    },
    // 下载模板
    uploadDialog_download(val) {
      common.downExcelWithFileName(
        `/oil-field-service/v1/measure/equip/download`,
        {},
        this.token,
        `计量器具导入模板`
      );
    },
    uploadDialog_uploadFile(val) {
      const formData = new FormData();
      formData.append("file", val.file);
      httping({
        url: `/oil-field-service/v1/measure/equip/import`,
        method: "POST",
        data: formData
      }).then(response => {
        if (response.code === 0) {
          this.$message.success("导入成功");
        } else if (response.code !== 0) {
          let tips = "";
          if (response.data) {
            for (let i = 0; i < response.data.length; i++) {
              tips = tips + response.data[i] + "<br/>";
            }
          } else {
            tips = response.msg;
          }
          this.$message.error(tips);
        }
        this.uploadDialog.closeTrigger_in = new Date().getTime();
        this.getTableData();
      });
    }
  },
  mounted() {
    this.getOilTree();
    this.getTableData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  min-width: 1600px;
  width: 100%;
  height: 100%;
  position: relative;
}
.mainBox {
  @include background_color(BG1);
  @include padding(J3);
}
.tree :deep(.el-tree) {
  overflow: auto;
}
.row-i {
  display: inline-block;
  width: 25px;
  height: 24px;
  cursor: pointer;
  border-radius: 50%;
  vertical-align: middle;
}
.detail {
  background: url("./assets/details.png") no-repeat center center;
}
.edit {
  background: url("./assets/edit.png") no-repeat center center;
}
.table-icon {
  height: 30px;
  cursor: pointer;
  vertical-align: middle;
}
.deleteBtn {
  display: contents;
}
.deleteBtn :deep(.el-button) {
  width: 40px;
  height: 40px;
  border: none;
  background: none !important;
}
.deleteBtn :deep(.el-button:hover) {
  border: none;
  background: none !important;
}
.deleteBtn :deep(.el-icon-delete) {
  @include font_size(H1);
  @include font_color(T3);
}
.deleteBtn :deep(.el-icon-delete:hover) {
  @include font_color(Sta3);
}
.deleteBtn :deep(.is-disabled .el-icon-delete:hover) {
  @include font_color(T3);
}
.aside-collapsed {
  padding: 0 !important;
}
.leftTree {
  position: relative;
  transition: width 0.3s;
}
.collapse-btn {
  cursor: pointer;
  position: absolute;
  top: 0;
  right: 0px;
  bottom: 0;
  margin: auto;
  width: 14px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  vertical-align: middle;
  @include background_color(ZS);
  @include font_color(T5);
  border-radius: 3px;
}
</style>
