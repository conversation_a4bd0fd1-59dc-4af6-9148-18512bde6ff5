<template>
  <div class="page">
    <div class="padding0 fullfilled flex-column">
      <div
        class="RealTimeOverviewHeader"
        v-if="topTitle && topTitle.text"
        :style="{ background: topTitle.color }"
      >
        <span>{{ topTitle.num }}</span>
        {{ topTitle.text }}
      </div>
      <div class="flex-auto">
        <div class="fullfilled flex-column" style="overflow: auto">
          <div class="flex-auto cententBox mtJ3" style="min-height: 348px">
            <div class="fullheight flex-row">
              <div class="currentMonth brC bg1 eem-container">
                <p class="common-title-H2">{{ $T("本月") }}</p>
                <div style="flex: 2">
                  <p>{{ $T("最大需量") }}（kW）</p>
                  <p style="font-size: 28px; color: rgb(233, 47, 39)">
                    {{ currentMonthData.maxDemand || "--" }}
                  </p>
                  <div class="fcT3">
                    <div>{{ $T("更新于") }}：</div>
                    {{
                      currentMonthData.occurrenceTime
                        ? $moment(currentMonthData.occurrenceTime).format(
                            "YYYY-MM-DD HH:mm:ss"
                          )
                        : "--"
                    }}
                  </div>
                </div>
                <div style="flex: 1">
                  <p>{{ $T("最大需量") }}/{{ $T("申报值") }}</p>
                  <p style="font-size: 20px">
                    {{
                      currentMonthData.maxDemand &&
                      currentMonthData.maxDemand != "--" &&
                      currentMonthData.declareValue &&
                      currentMonthData.declareValue != "--"
                        ? (
                            (currentMonthData.maxDemand /
                              currentMonthData.declareValue) *
                            100
                          ).toFixed2(2) + "%"
                        : "--"
                    }}
                  </p>
                </div>
                <div style="flex: 1">
                  <p>{{ $T("本月申报需量") }}（kW）</p>
                  <p style="font-size: 20px">
                    {{ currentMonthData.declareValue }}
                  </p>
                </div>
              </div>
              <div style="flex: 2" class="minWH bg1 brC mlJ3">
                <CetChart
                  style="height: calc(100% - 50px)"
                  @click="CetChartClick_out"
                  :inputData_in="CetChart_1.inputData_in"
                  v-bind="CetChart_1.config"
                  ref="CetChart_1"
                />
                <div class="CetChart2_label">
                  <div v-for="(item, index) in CetChart2LabelList" :key="index">
                    <span
                      class="CetChart2_label_span"
                      :style="{ background: item.color }"
                    ></span>
                    {{ item.text }}
                  </div>
                </div>
              </div>
              <div
                style="flex: 1; position: relative"
                class="minWH gaugeBox mlJ3 bg1 eem-container brC"
              >
                <p class="common-title-H2">{{ $T("当前") }}</p>
                <div class="gaugeBox-div">
                  <p>
                    {{ $T("当前需量") }}:
                    {{ currentMonthData.currentDemandData }}kW
                  </p>
                </div>
                <CetChart
                  :inputData_in="CetChart_2.inputData_in"
                  v-bind="CetChart_2.config"
                  ref="CetChart_2"
                />
                <div class="pedestal">
                  <img src="../../assets/bottom.png" alt="" />
                  <span>
                    {{ CetChart_2.config.options.series[0].data[0].name }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div
            class="flex-auto eem-container mtJ3 flex-column"
            style="min-height: 300px"
          >
            <div class="clearfix pbJ3">
              <div class="fl common-title-H2">{{ $T("需量趋势") }}</div>
              <CetButton
                class="fr"
                v-bind="CetButton_1"
                v-on="CetButton_1.event"
              ></CetButton>
              <CetButton
                class="fr mr8"
                v-bind="CetButton_2"
                v-on="CetButton_2.event"
              ></CetButton>
              <div class="fr mrJ1 clearfix" style="display: flex">
                <!-- 向前查询按钮 -->
                <CetButton
                  class="fl custom—square"
                  v-bind="CetButton_prv"
                  v-on="CetButton_prv.event"
                ></CetButton>
                <!-- <el-date-picker
                  class="fl ml-5 mr-5"
                  v-model="CetDatePicker_1.val"
                  v-bind="CetDatePicker_1.config"
                  @change="CetDatePicker_1_dateVal_out"
                ></el-date-picker> -->
                <CustomElDatePicker
                  class="fl mlJ mrJ"
                  :prefix_in="$T('选择日期')"
                  v-bind="CetDatePicker_1.config"
                  v-model="CetDatePicker_1.val"
                  @change="CetDatePicker_1_dateVal_out"
                />
                <!-- 向后查询按钮 -->
                <CetButton
                  class="fl custom—square"
                  v-bind="CetButton_next"
                  v-on="CetButton_next.event"
                ></CetButton>
              </div>
            </div>
            <CetChart
              class="flex-auto"
              :inputData_in="CetChart_3.inputData_in"
              v-bind="CetChart_3.config"
            />
          </div>
        </div>
      </div>
      <ViewDetail v-bind="ViewDetail"></ViewDetail>
    </div>
  </div>
</template>
<script>
import common from "eem-utils/common";
import ViewDetail from "./ViewDetail.vue";
import * as echarts from "echarts";
import { httping } from "@omega/http";
export default {
  name: "RealTimeOverview",
  components: {
    ViewDetail
  },
  props: {
    currentNode: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    }
  },

  data() {
    return {
      rate1: null,
      rate2: null,
      rate3: null,
      CetChart2LabelList: [], //柱状图等级提示
      topTitle: {}, // 最新未确认事件
      // 本月需量数据
      currentMonthData: {
        value: null,
        occurrenceTime: null, //最大需量发生时间
        declareValue: null, //申报值
        currentDemandData: null, //实时值
        meterValue: null //表计值
      },
      CetChart_1: {
        inputData_in: {},
        config: {
          options: {
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "shadow"
              },
              formatter: function (value) {
                if (value.length > 0) {
                  var text = ``;
                  value.forEach(item => {
                    if (item.data) {
                      text += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                        item.color.colorStops
                          ? item.color.colorStops[1].color
                          : item.color
                      };"></span>${item.axisValueLabel}: ${item.data}<br />`;
                    }
                  });
                  return text;
                }
              }
            },
            grid: {
              left: "10%",
              top: 50,
              bottom: 0,
              containLabel: true
            },
            xAxis: {
              type: "value",
              show: false,
              max: null
            },
            yAxis: {
              type: "category",
              data: [$T("当前需量"), $T("本月最大需量")],
              axisLabel: {
                // color: "#fff"
              },
              axisLine: {
                lineStyle: {
                  // color: "#fff",
                  opacity: 0.6
                }
              }
            },
            series: [
              {
                name: $T("本月最大需量"),
                type: "bar",
                data: [null, 0],
                barGap: "-100%",
                barWidth: "50%",
                itemStyle: {
                  color: "#80E599"
                }
              },
              {
                name: $T("当前需量"),
                type: "bar",
                data: [0, null],
                barGap: "-100%",
                barWidth: "50%",
                itemStyle: {
                  color: "#80E599"
                }
              }
            ]
          }
        }
      },
      CetChart_2: {
        inputData_in: {},
        config: {
          options: {
            tooltip: {
              formatter: $T("实时") + "/" + $T("申报") + ": {c}%"
            },
            grid: {
              top: 100,
              containLabel: true
            },
            series: [
              {
                name: $T("实时") + "/" + $T("申报"),
                type: "gauge",
                max: 120,
                splitNumber: 12,
                radius: "70%",
                detail: {
                  formatter: function (val) {
                    if (String(val) === "NaN") {
                      return "--";
                    } else {
                      return val + "%";
                    }
                  },
                  fontSize: 24,
                  color: "#93BADF"
                },
                data: [
                  {
                    name: $T("实时") + "/" + $T("申报"),
                    value: 0
                  }
                ],
                title: {
                  offsetCenter: [0, "78%"],
                  color: "#A2D2F7",
                  fontSize: 16,
                  show: false
                },
                axisLine: {
                  lineStyle: {
                    width: 5,
                    color: [
                      [
                        1,
                        new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                          {
                            offset: 0,
                            color: "rgba(145,207,255,0)"
                          },
                          {
                            offset: 0.5,
                            color: "rgba(145,207,255,0.2)"
                          },
                          {
                            offset: 1,
                            color: "rgba(145,207,255,1)"
                          }
                        ])
                      ]
                    ]
                  }
                },
                splitLine: {
                  show: false
                },
                axisTick: {
                  show: false
                },
                axisLabel: {
                  formatter: function (val) {
                    if (val % 20 === 0) {
                      return val;
                    }
                  },
                  color: "#87CBFE",
                  distance: -36,
                  fontSize: 10
                },
                pointer: {
                  // 仪表盘指针。
                  show: true, // 是否显示指针,默认 true。
                  length: "50%", // 指针长度，可以是绝对数值，也可以是相对于半径的百分比,默认 80%。
                  width: 3 // 指针宽度,默认 8。
                }
              }
            ]
          }
        }
      },
      CetChart_3: {
        inputData_in: {},
        config: {
          options: {
            tooltip: {
              trigger: "axis",
              formatter: function (val) {
                var list = val || [];
                var formatterStr = `${list[0].name}`;
                for (var i = 0, len = list.length; i < len; i++) {
                  formatterStr += ` <br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                    val[i].color
                  };"></span>${val[i].seriesName}（kW） : ${
                    val[i].value || "--"
                  }`;
                }
                return formatterStr;
              },
              confine: true
            },
            grid: {
              left: 20,
              right: 50,
              bottom: 30,
              containLabel: true
            },
            legend: {
              top: 10,
              itemStyle: {
                opacity: 0
              }
            },
            title: {
              text: $T("需量趋势"),
              show: false
            },
            toolbox: {
              feature: {
                dataZoom: {
                  yAxisIndex: "none"
                },
                // restore: {},
                saveAsImage: {}
              },
              right: 30
            },
            xAxis: {
              type: "category",
              boundaryGap: false,
              data: [],
              name: $T("时间")
            },
            yAxis: {
              type: "value"
            },
            series: [
              {
                name: $T("今日需量"),
                type: "line",
                data: [],
                itemStyle: {
                  color: "#0152D9"
                },
                symbol: "none",
                markPoint: {
                  data: [
                    {
                      name: $T("今日最大需量"),
                      type: "max"
                    }
                  ],
                  itemStyle: {
                    // color: "#fff"
                  },
                  label: {
                    show: true,
                    formatter: function (val) {
                      return val.name;
                    }
                    // color: "#999",
                    // position: [10, 20]
                  }
                }
              },
              {
                name: $T("查询需量"),
                type: "line",
                data: [],
                itemStyle: {
                  color: "#7ECF51"
                },
                symbol: "none",
                markPoint: {
                  data: [
                    {
                      name: $T("查询最大需量"),
                      type: "max"
                    }
                  ],
                  itemStyle: {
                    // color: "#fff"
                  },
                  label: {
                    show: true,
                    formatter: function (val) {
                      return val.name;
                    }
                    // color: "#999",
                    // position: [10, 20]
                  }
                }
              },
              {
                name: $T("申报需量"),
                type: "line",
                data: [],
                symbol: "none",
                itemStyle: {
                  color: "#E92F27"
                },
                lineStyle: {
                  type: "dashed"
                }
              }
            ]
          }
        }
      },
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: $T("需量分析"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      ViewDetail: {
        inputData_in: null,
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        dateVal: this.$moment().subtract(1, "day").startOf("day").valueOf(),
        currentNode: null
      },
      CetDatePicker_1: {
        disable_in: false,
        val: this.$moment().subtract(1, "day").startOf("day").valueOf(),
        config: {
          valueFormat: "timestamp",
          format: $T("yyyy年MM月dd日"),
          rangeSeparator: "-",
          clearable: false,
          size: "small",
          style: {
            display: "inline-block",
            width: "220px"
          },
          pickerOptions: {
            disabledDate(time) {
              return (
                time.getTime() > new Date().setDate(new Date().getDate() - 1)
              );
            }
          }
        }
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: true,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      }
    };
  },
  watch: {
    "CetDatePicker_1.val": function (val) {
      this.getDemandTrend();
      if (
        this.$moment(val).startOf("day").valueOf() >=
        this.$moment().subtract(1, "day").startOf("day").valueOf()
      ) {
        this.CetButton_next.disable_in = true;
      } else {
        this.CetButton_next.disable_in = false;
      }
    },
    currentNode: {
      handler: function (val) {
        if (val) {
          this.getLatestUnconfirmEvent();
          this.getDemandData(false, () => {
            this.getDemandTrend();
          });
        }
      },
      deep: true
    }
  },

  methods: {
    // 获取需量信息
    getDemandData(ajaxFlag, fn) {
      if (!this.currentNode) {
        return;
      }
      this.currentMonthData = {
        maxDemand: "--", //最大需量
        occurrenceTime: "--", //最大需量发生时间
        declareValue: "--", //申报值
        currentDemandData: "--" //实时值
      };
      httping({
        url: `/eem-service/v1/demand/monitor/details/demandInfo`,
        method: "POST",
        data: {
          chargingWay: this.currentNode.chargingWay,
          id: this.currentNode.id,
          name: this.currentNode.name
        }
      }).then(response => {
        if (response.code === 0) {
          this.currentMonthData = {
            maxDemand:
              response.data && response.data.meterValue
                ? response.data.meterValue.toFixed2(2)
                : "--", //最大需量
            occurrenceTime: response.data && response.data.occurrenceTime, //最大需量发生时间
            declareValue:
              response.data && response.data.declareValue
                ? response.data.declareValue.toFixed2(2)
                : "--", //申报值
            currentDemandData:
              response.data && response.data.currentDemandData
                ? response.data.currentDemandData.toFixed2(2)
                : "--" //实时值
          };
          this.CetChart_1.config.options.series[0].data = [
            null,
            this.currentMonthData.maxDemand
          ];
          this.CetChart_1.config.options.series[1].data = [
            this.currentMonthData.currentDemandData,
            null
          ];
          console.log(this.currentMonthData, "3");
          if (
            this.currentMonthData.currentDemandData !== "--" &&
            this.currentMonthData.declareValue !== "--"
          ) {
            this.CetChart_2.config.options.series[0].data[0].value = (
              (this.currentMonthData.currentDemandData /
                this.currentMonthData.declareValue) *
              100
            ).toFixed2(2);
          } else {
            this.CetChart_2.config.options.series[0].data[0].value = null;
          }
          console.log(this.CetChart_2.config.options, "4");
          fn && fn();
          if (!ajaxFlag) {
            this.getProjectDetail();
          } else {
            this.judgeChartBarColor();
          }
        }
      });
    },
    getProjectDetail() {
      if (!this.currentNode) {
        return;
      }
      httping({
        url:
          "/eem-service/v1/alarm/getSchemes/" +
          this.currentNode.id +
          "/" +
          this.currentNode.modelLabel +
          "/3/-1/0",
        method: "GET"
      }).then(response => {
        if (response.code === 0 && response.data) {
          this.getSchemeLevel(
            response.data[response.data.length - 1].id,
            response.data[response.data.length - 1].modelLabel
          );
        } else {
          this.$nextTick(() => {
            if (this.CetChart_1.config.options.series[0].markLine) {
              this.CetChart_1.config.options.series[0].markLine.data = [];
            }
            this.$refs.CetChart_1.mergeOptions(this.CetChart_1.config.options);
          });
        }
      });
    },
    // 获取预警等级设置
    getSchemeLevel(id, modelLabel) {
      var CetChart2LabelList = [
        {
          text: $T("本月申报需量"),
          color: "#00CC99"
        }
      ];
      this.CetChart_1.config.options.series[0].markLine = {
        data: [
          {
            xAxis: this.currentMonthData.declareValue * 1,
            label: {
              formatter:
                $T("本月申报需量") +
                "\n" +
                this.currentMonthData.declareValue * 1,
              position: "end",
              show: false,
              textStyle: {
                // color: "#fff"
              }
            },
            lineStyle: {
              color: "#00CC99"
            }
          }
        ],
        symbolSize: 0
      };
      // var me = this;
      httping({
        url: "/eem-service/v1/alarm/getSchemeLevel/" + id + "/" + modelLabel,
        method: "GET"
      }).then(response => {
        if (response.code === 0 && response.data) {
          var arr = response.data.filter(item => item.active);
          var rate1;
          var rate2;
          var rate3;
          arr.forEach(item => {
            var obj;
            if (item.alarmColor === 1) {
              rate1 = item.rate;
              obj = {
                xAxis: (
                  (this.currentMonthData.declareValue * item.rate) /
                  100
                ).toFixed2(2),
                label: {
                  formatter:
                    $T("一级") +
                    "\n" +
                    (
                      (this.currentMonthData.declareValue * item.rate) /
                      100
                    ).toFixed2(2),
                  position: "end",
                  show: false,
                  textStyle: {
                    // color: "#fff"
                  }
                },
                lineStyle: {
                  color: "#E4151A"
                }
              };
              CetChart2LabelList.push({
                text: $T("一级"),
                color: "#E4151A"
              });
            } else if (item.alarmColor === 2) {
              rate2 = item.rate;
              obj = {
                xAxis: (
                  (this.currentMonthData.declareValue * item.rate) /
                  100
                ).toFixed2(2),
                label: {
                  formatter:
                    $T("二级") +
                    "\n" +
                    (
                      (this.currentMonthData.declareValue * item.rate) /
                      100
                    ).toFixed2(2),
                  position: "end",
                  show: false,
                  textStyle: {
                    // color: "#fff"
                  }
                },
                lineStyle: {
                  color: "#FF9900"
                }
              };
              CetChart2LabelList.push({
                text: $T("二级"),
                color: "#FF9900"
              });
            } else if (item.alarmColor === 3) {
              rate3 = item.rate;
              obj = {
                xAxis: (
                  (this.currentMonthData.declareValue * item.rate) /
                  100
                ).toFixed2(2),
                label: {
                  formatter:
                    $T("三级") +
                    "\n" +
                    (
                      (this.currentMonthData.declareValue * item.rate) /
                      100
                    ).toFixed2(2),
                  position: "end",
                  show: false,
                  textStyle: {
                    // color: "#fff"
                  }
                },
                lineStyle: {
                  color: "#fbe100"
                }
              };
              CetChart2LabelList.push({
                text: $T("三级"),
                color: "#fbe100"
              });
            }
            this.CetChart_1.config.options.series[0].markLine.data.push(obj);
          });
          this.CetChart2LabelList = CetChart2LabelList;
          this.rate1 = rate1;
          this.rate2 = rate2;
          this.rate3 = rate3;
          // 判断每个柱子的颜色
          this.judgeChartBarColor();
          // 取刻度线最大值
          var maxNum = Number(
            this.CetChart_1.config.options.series[0].markLine.data[0].xAxis
          );
          this.CetChart_1.config.options.series[0].markLine.data.forEach(
            item => {
              if (Number(item.xAxis) > Number(maxNum)) {
                maxNum = Number(item.xAxis);
              }
            }
          );
          if (
            Number(maxNum) <
            Number(this.CetChart_1.config.options.series[0].data[1])
          ) {
            maxNum = this.CetChart_1.config.options.series[0].data[1];
          }
          if (
            Number(maxNum) <
            Number(this.CetChart_1.config.options.series[1].data[0])
          ) {
            maxNum = Number(this.CetChart_1.config.options.series[1].data[0]);
          }
          this.CetChart_1.config.options.xAxis.max = maxNum * 1.1;
          this.$nextTick(() => {
            this.$refs.CetChart_1.mergeOptions(this.CetChart_1.config.options);
          });
          // 处理仪表盘 最大刻度是是一级告警的百分比+15
          var rate = rate1
            ? rate1 + 15
            : rate2
            ? rate + 15
            : rate3
            ? rate3 + 15
            : 100;
          /* this.CetChart_2.config.options.series[0].max = rate;
          this.CetChart_2.config.options.series[0].splitNumber = rate; */
          var colorArr = [];
          if (!rate3 && !rate2 && !rate1) {
            colorArr = [
              [
                1,
                new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  { offset: 0, color: "#00CC99" },
                  { offset: 1, color: "#ADFB44" }
                ])
              ]
            ];
          } else {
            if (rate3) {
              colorArr.push([
                rate3 / rate,
                new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  { offset: 0, color: "#00CC99" },
                  { offset: 1, color: "#ADFB44" }
                ])
              ]);
              if (rate2) {
                colorArr.push([
                  rate1 / rate,
                  new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                    { offset: 0, color: "#2057E8" },
                    { offset: 1, color: "#32BFFC" }
                  ])
                ]);
              }
              if (rate1) {
                colorArr.push([
                  rate1 / rate,
                  new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                    { offset: 0, color: "#FF9900" },
                    { offset: 1, color: "#FCE443" }
                  ])
                ]);
              }
              colorArr.push([
                1,
                new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  { offset: 0, color: "#E4151A" },
                  { offset: 1, color: "#FD945B" }
                ])
              ]);
            } else if (rate2) {
              colorArr.push([
                rate2 / rate,
                new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  { offset: 0, color: "#00CC99" },
                  { offset: 1, color: "#ADFB44" }
                ])
              ]);
              if (rate1) {
                colorArr.push([
                  rate1 / rate,
                  new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                    { offset: 0, color: "#FF9900" },
                    { offset: 1, color: "#FCE443" }
                  ])
                ]);
              }
              colorArr.push([
                1,
                new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  { offset: 0, color: "#E4151A" },
                  { offset: 1, color: "#FD945B" }
                ])
              ]);
            } else {
              colorArr.push([
                rate1 / rate,
                new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  { offset: 0, color: "#00CC99" },
                  { offset: 1, color: "#ADFB44" }
                ])
              ]);
              colorArr.push([
                1,
                new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  { offset: 0, color: "#E4151A" },
                  { offset: 1, color: "#FD945B" }
                ])
              ]);
            }
          }
          /* this.CetChart_2.config.options.series[0].axisLine = {
            lineStyle: {
              width: 5,
              color: [1, "#1FD19F"]
            }
          };
          this.CetChart_2.config.options.series[0].axisLabel.formatter = function (val) {
            if (val == 0 || (rate1 ? val == rate1 : false) || (rate2 ? val == rate2 : false) || (rate3 ? val == rate3 : false)) {
              return val + " %";
            }
            if (val % 20 === 0 || val === rate) {
              return val;
            }
          }; */
          this.$refs.CetChart_2.mergeOptions(this.CetChart_2.config.options);
        }
      });
    },
    // 判断柱状图柱子颜色
    judgeChartBarColor() {
      if (!this.rate1) {
        return;
      }
      // 最大需量
      if (
        this.CetChart_1.config.options.series[0].data[1] >=
        (this.currentMonthData.declareValue * this.rate1) / 100
      ) {
        this.CetChart_1.config.options.series[0].itemStyle.color =
          new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: "#E4151A" },
            { offset: 1, color: "#FD945B" }
          ]);
      } else if (
        this.CetChart_1.config.options.series[0].data[1] >=
        (this.currentMonthData.declareValue * this.rate2) / 100
      ) {
        this.CetChart_1.config.options.series[0].itemStyle.color =
          new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: "#FF9900" },
            { offset: 1, color: "#FCE443" }
          ]);
      } else if (
        this.CetChart_1.config.options.series[0].data[1] >=
        (this.currentMonthData.declareValue * this.rate3) / 100
      ) {
        this.CetChart_1.config.options.series[0].itemStyle.color =
          new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: "#2057E8" },
            { offset: 1, color: "#32BFFC" }
          ]);
      } else {
        this.CetChart_1.config.options.series[0].itemStyle.color =
          new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: "#00CC99" },
            { offset: 1, color: "#ADFB44" }
          ]);
      }
      // 当前需量
      if (
        this.CetChart_1.config.options.series[1].data[0] >=
        (this.currentMonthData.declareValue * this.rate1) / 100
      ) {
        this.CetChart_1.config.options.series[1].itemStyle.color =
          new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: "#E4151A" },
            { offset: 1, color: "#FD945B" }
          ]);
        // this.CetChart_2.config.options.series[0].detail.color = "#E4151A";
      } else if (
        this.CetChart_1.config.options.series[1].data[0] >=
        (this.currentMonthData.declareValue * this.rate2) / 100
      ) {
        this.CetChart_1.config.options.series[1].itemStyle.color =
          new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: "#FF9900" },
            { offset: 1, color: "#FCE443" }
          ]);
        // this.CetChart_2.config.options.series[0].detail.color = "#FF9900";
      } else if (
        this.CetChart_1.config.options.series[1].data[0] >=
        (this.currentMonthData.declareValue * this.rate3) / 100
      ) {
        this.CetChart_1.config.options.series[1].itemStyle.color =
          new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: "#2057E8" },
            { offset: 1, color: "#32BFFC" }
          ]);
        // this.CetChart_2.config.options.series[0].detail.color = "#2057E8";
      } else {
        this.CetChart_1.config.options.series[1].itemStyle.color =
          new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: "#00CC99" },
            { offset: 1, color: "#ADFB44" }
          ]);
        // this.CetChart_2.config.options.series[0].detail.color = "#00CC99";
      }
    },
    // 查询最新未确认事件
    getLatestUnconfirmEvent() {
      if (!this.currentNode) {
        return;
      }
      this.topTitle = null;
      httping({
        url: `/eem-service/v1/demand/monitor/details/queryLatestUnconfirmEvent/${this.currentNode.id}`,
        method: "GET"
      }).then(response => {
        if (response.code === 0 && response.data) {
          var color, num;
          if (response.data.level === 1) {
            color =
              "linear-gradient(90deg, #E4151A 0%, #E4151A 0%, #FD945B 100%, #FD945B 100%)";
            num = $T("一级");
          } else if (response.data.level === 2) {
            color =
              "linear-gradient(90deg, #FF9900 0%, #FF9900 0%, #FCE443 100%, #FCE443 100%)";
            num = $T("二级");
          } else if (response.data.level === 3) {
            color =
              "linear-gradient(90deg, #fbe100 0%, #fbe100 0%, #ebfb00 100%, #ebfb00 100%)";
            num = $T("三级");
          }
          this.topTitle = {
            // text: this.$moment(response.data.eventtime).format("YYYY-MM-DD HH:mm:ss") + " " + response.data.description,
            text: response.data.description,
            color: color,
            num: num
          };
        }
      });
    },
    // 查询需量趋势
    getDemandTrend() {
      if (!this.currentNode) {
        return;
      }
      this.CetChart_3.config.options.xAxis.data = [];
      this.CetChart_3.config.options.series[0].data = [];
      this.CetChart_3.config.options.series[1].data = [];
      this.CetChart_3.config.options.series[2].data = [];
      httping({
        url: "/eem-service/v1/demand/monitor/details/trend",
        method: "POST",
        data: {
          today: {
            endTime: this.$moment().add(1, "day").startOf("day").valueOf(),
            startTime: this.$moment().startOf("day").valueOf()
          },
          node: {
            modelLabel: this.currentNode.modelLabel,
            id: this.currentNode.id,
            name: this.currentNode.name
          },
          queryDay: {
            endTime: this.$moment(this.CetDatePicker_1.val)
              .add(1, "day")
              .startOf("day")
              .valueOf(),
            startTime: this.$moment(this.CetDatePicker_1.val)
              .startOf("day")
              .valueOf()
          },
          projectId: this.projectId,
          cycle: 12
        }
      }).then(response => {
        if (response.code === 0 && response.data && response.data.length > 0) {
          var arr1 = [];
          var arr2 = [];
          var arr3 = [];
          var xAxisArr = [];
          response.data.forEach(item => {
            xAxisArr.push(this.$moment(item.time).format("HH:mm"));
            arr3.push(this.currentMonthData.declareValue);
            arr1.push(item.dayDemand ? item.dayDemand.toFixed2(2) : "--");
            arr2.push(
              item.consultDemand ? item.consultDemand.toFixed2(2) : "--"
            );
          });
          this.CetChart_3.config.options.xAxis.data = xAxisArr;
          // 今日需量
          this.CetChart_3.config.options.series[0].data = arr1;
          // 查询需量
          this.CetChart_3.config.options.series[1].data = arr2;
          // 申报需量
          this.CetChart_3.config.options.series[2].data = arr3;
        }
      });
    },
    CetChartClick_out(val) {
      if (!this.currentMonthData.occurrenceTime) {
        this.$message.warning($T("暂无本月最大需量数据！"));
        return;
      }
      this.CetDatePicker_1.val = this.$moment(
        this.currentMonthData.occurrenceTime
      )
        .startOf("day")
        .valueOf();
    },
    // 导出
    CetButton_1_statusTrigger_out(val) {
      var data = {
        today: {
          endTime: this.$moment().add(1, "day").startOf("day").valueOf(),
          startTime: this.$moment().startOf("day").valueOf()
        },
        node: {
          modelLabel: this.currentNode.modelLabel,
          id: this.currentNode.id,
          name: this.currentNode.name
        },
        queryDay: {
          endTime: this.$moment(this.CetDatePicker_1.val)
            .add(1, "day")
            .startOf("day")
            .valueOf(),
          startTime: this.$moment(this.CetDatePicker_1.val)
            .startOf("day")
            .valueOf()
        },
        projectId: this.projectId
      };
      common.downExcel(
        "/eem-service/v1/demand/monitor/details/trendexport",
        data,
        this.token
      );
    },
    CetButton_prv_statusTrigger_out(val) {
      const date = this.$moment(this.CetDatePicker_1.val);
      this.CetDatePicker_1.val = date.subtract(1, "day").valueOf();
    },
    CetButton_next_statusTrigger_out(val) {
      const date = this.$moment(this.CetDatePicker_1.val);
      if (
        date.startOf("day").valueOf() !==
        this.$moment().subtract(1, "day").startOf("day").valueOf()
      ) {
        this.CetDatePicker_1.val = date.add(1, "day").valueOf();
      }
    },
    CetButton_2_statusTrigger_out() {
      this.ViewDetail.inputData_in = null;
      this.ViewDetail.visibleTrigger_in = new Date().getTime();
      this.ViewDetail.currentNode = this.currentNode;
    },
    CetDatePicker_1_dateVal_out(val) {
      this.getDemandTrend();
      this.ViewDetail.dateVal = val;
    }
  },
  created: function () {},
  mounted: function () {
    this.getLatestUnconfirmEvent();
    this.getDemandData(false, () => {
      this.getDemandTrend();
    });
    // 定时刷新
    this.setInterval = setInterval(() => {
      this.getDemandData(true);
    }, this.systemCfg.timingTime);
  },
  destroyed: function () {
    // 关闭定时器
    clearInterval(this.setInterval);
  },
  deactivated: function () {
    // 关闭定时器
    clearInterval(this.setInterval);
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: calc(100% - 8px) !important;
  height: 100%;
  position: relative;
  overflow: auto;
}
.RealTimeOverviewHeader {
  font-size: 16px;
  color: rgb(255, 255, 255);
  height: 50px;
  line-height: 50px;
  @include padding(0 J4);
  @include border_radius(C);
  & > span {
    line-height: 18px;
    display: inline-block;
    padding: 5px;
    border: 1px solid #fff;
  }
}
.cententBox {
  box-sizing: border-box;
}
.chertBox {
  width: 100%;
  height: 400px;
  // padding: 20px 20px 0 20px;
  box-sizing: border-box;
}
.currentMonth {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  p {
    padding: 0;
    margin: 0;
  }
  & > p {
    padding-bottom: 10px;
  }
  & > div {
    padding: 10px;
    margin-top: 10px;
    width: 220px;
  }
}
.gaugeBox {
  display: flex;
  flex-direction: column;
  p {
    padding: 0;
    margin: 0;
  }
  & > .gaugeBox-div {
    text-align: center;
    @include padding(J3);
  }
}
.CetChart2_label {
  width: 100%;
  text-align: center;
  & > div {
    display: inline-block;
    margin: 0 5px;
    font-size: 14px;
  }
  .CetChart2_label_span {
    display: inline-block;
    margin-right: 5px;
    border-radius: 10px;
    width: 10px;
    height: 10px;
    background-color: #80e599;
  }
}
.pedestal {
  position: absolute;
  bottom: 4%;
  left: 50%;
  transform: translate(-50%, -50%);
  img {
    transform: scale(0.8);
  }
  span {
    position: absolute;
    top: 42%;
    left: 50%;
    transform: translate(-50%, -50%);
    white-space: nowrap;
    color: #fff;
  }
}
</style>
