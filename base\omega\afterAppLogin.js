import { httping, HttpBase } from "@omega/http";
import { store, conf } from "@omega/app";
import { isDev } from "eem-utils/common";
import { fileMenu } from "@/config/config";
import { getJsonFile, find } from "eem-utils/util";
import _ from "lodash";
import utilUser from "@omega/auth/auth/user.js";
//引用element提示框
import { Message } from "element-ui";
import { setOriginNavmenu, OmegaAdminPlugin } from "@omega/admin";
import Vue from "vue";
import * as layoutStore from "@omega/layout";
import { init } from "eem-utils/analysisServiceConfig.js";
import { getPermission } from "eem-utils/permission.js";
const TOKENNAME = "omega_token";
// 获取token
function getToken() {
  const tokenToSessionStorage = _.get(
    store,
    "state.systemCfg.tokenToSessionStorage"
  );
  let token;
  if (!tokenToSessionStorage) {
    token = localStorage.getItem(TOKENNAME);
  } else {
    token = sessionStorage.getItem(TOKENNAME);
  }
  return token;
}

// 设置token
function setToken(token) {
  const tokenToSessionStorage = _.get(
    store,
    "state.systemCfg.tokenToSessionStorage"
  );
  if (!tokenToSessionStorage) {
    localStorage.setItem(TOKENNAME, token);
  } else {
    sessionStorage.setItem(TOKENNAME, token);
  }
}

// 设置token
function deleteToken() {
  const tokenToSessionStorage = _.get(
    store,
    "state.systemCfg.tokenToSessionStorage"
  );
  if (!tokenToSessionStorage) {
    localStorage.removeItem(TOKENNAME);
  } else {
    sessionStorage.removeItem(TOKENNAME);
  }
}

// 通过code进行登录
async function codeLogin(val) {
  const singleSignOnModel =
    _.get(store, "state.systemCfg.singleSignOnModel.model") || "code";
  let res, access_token;
  if (singleSignOnModel === "code") {
    // 置换对方token
    res = await httping({
      url: `/eem-service/thirdpart/sso/auth2.0/token`,
      method: "GET",
      params: {
        code: val
      }
    });
    access_token = _.get(res, "data.access_token");
  } else {
    access_token = val;
  }
  if (!access_token) {
    return;
  }
  return httping({
    url: `/eem-service/thirdpart/sso/auth2.0/login`,
    method: "post",
    data: {
      client: "web",
      timestamps: Date.now(),
      token: access_token
    }
  });
}

// 认证中心鉴权，未登录，接口返回重定向地址
export function getAuthorize() {
  window.location.href = `${window.location.origin}/eem-service/thirdpart/sso/auth2.0/authorize`;
}

/**
 * 获取所有的枚举类型值
 */
export function getEnumerations() {
  let urlStr = "/model/v1/enumerations";
  if (store && store.state.systemCfg.internationalization) {
    urlStr = "/eem-service/v1/common/enumerations";
  }
  return httping({
    url: urlStr,
    method: "GET"
  }).then(res => res.data);
}
/**
 * 设置国际化处理
 */
function globalization(Navmenu) {
  if (Navmenu.length) {
    Navmenu.forEach(item => {
      if (item.enLabel) {
        item.label = item.enLabel;
      }
      if (item.subMenuList && item.subMenuList.length) {
        globalization(item.subMenuList);
      }
      if (item.menuItemGroup && item.menuItemGroup.length) {
        globalization(item.menuItemGroup);
      }
    });
  }
}
/**
 * 对多项目环境，ROOT用户菜单栏进行过滤
 */
function filterROOTTree(tree = [], map = [], arr = []) {
  if (!tree.length) {
    return [];
  }
  for (let item of tree) {
    if (
      !map.includes(item.permission) &&
      !["subMenu", "menuItemGroup"].includes(item.type)
    ) {
      continue;
    }
    let node = { ...item };
    arr.push(node);
    if (item.subMenuList && item.subMenuList.length) {
      node.subMenuList = [];
      filterROOTTree(item.subMenuList, map, node.subMenuList);
    }
  }
  return arr;
}

// 判断当前处于平台菜单还是项目菜单
function getModule(user) {
  const sessionProjectId = window.sessionStorage.getItem("projectId");
  const projectId = Number(sessionProjectId);
  let module;
  const cfg = user.customConfig ? JSON.parse(user.customConfig) : {};
  if (projectId || cfg.usertype === 3 || cfg.usertype === 4) {
    module = "fw";
  } else {
    module = "pf";
  }
  if (!store.state.systemCfg.isPlatformModel) {
    module = "fw";
  }
  store.commit("setCurrentModule", module);
  return module;
}

async function loginCallback(user) {
  // 开发环境加上userId
  if (isDev()) {
    HttpBase.setHeadersOperate({
      "User-ID": user.id
    });
  }
  // 获取后端的操作权限列表
  // 获取所有枚举类型
  const [, enumerations] = await Promise.all([
    getPermission(),
    getEnumerations()
  ]);
  // 所有枚举类型
  let allEnumerations = {};
  enumerations.forEach(item => {
    allEnumerations[item.modelLabel] = item.enums;
  });
  store.commit("setEnumerations", allEnumerations);
  const cfg = user.customConfig ? JSON.parse(user.customConfig) : {};
  const userInfo = Object.assign(
    { userId: user.id, userName: user.name, usertype: cfg.usertype || 0 },
    user
  );
  this.store.commit("setUserInfo", userInfo);
  this.store.commit("setTenantId", userInfo.tenantId);
  // 框架修改 -兼容旧有项目 初始化用户信息
  store.dispatch("initUserInfo", {
    token: getToken(),
    // 深copyc
    user: userInfo
  });

  if (userInfo.usertype === 4 || userInfo.usertype === 3) {
    HttpBase.setHeadersOperate({ projectId: user.projectId });
    this.store.commit("setProjectTenantId", user.tenantId);
    this.store.commit("setCurrentModule", "fw");
  }
  // 判断路由模式
  let Module = getModule(user);
  let projectId = 0; //平台级默认是0
  let projectInfo = {};
  let tenantid = null;
  //判断是否是在平台项目环境下运行
  if (store.state.systemCfg.isPlatformModel) {
    if (Module === "fw" || userInfo.usertype === 3 || userInfo.usertype === 4) {
      var params = {
        rootID: 0,
        rootLabel: "project",
        treeReturnEnable: true
      };
      const urlStr = `/eem-service/v1/auth/cloud/projects?tenantId=${userInfo.tenantId}&isQueryChild=true`;
      await httping({
        url: urlStr,
        method: "POST",
        data: params
      }).then(response => {
        const sessionProjectId = window.sessionStorage.getItem("projectId");
        if (
          typeof Number(sessionProjectId) === "number" &&
          Number(sessionProjectId)
        ) {
          projectId = Number(sessionProjectId);
        }
        const data = response.data;
        projectInfo =
          data && data.length > 0
            ? data.find(item => item.id == projectId)
            : null;
        tenantid = projectInfo && projectInfo.tenantid;
        if (data && data.length && (!projectId || !projectInfo)) {
          projectId = data[0].id;
          tenantid = data[0].tenantid;
          projectInfo = data[0];
        }
      });
      // 如果是项目内用户的话，必须先有项目信息
      if ((userInfo.usertype === 3 || userInfo.usertype === 4) && !projectId) {
        deleteToken();
        localStorage.removeItem("token");
        Message({
          message: $T("登录失败，该用户尚未关联项目"),
          showClose: true,
          type: "error"
        });
        return;
      }
    }
  } else {
    const urlStr = "/eem-service/v1/project/project?system=cloud";
    await httping({
      url: urlStr,
      method: "GET"
    }).then(response => {
      const sessionProjectId = window.sessionStorage.getItem("projectId");
      if (
        typeof Number(sessionProjectId) === "number" &&
        Number(sessionProjectId)
      ) {
        projectId = Number(sessionProjectId);
      }
      const data = response.data;
      projectInfo =
        data && data.length > 0
          ? data.find(item => item.id == projectId)
          : null;
      tenantid = projectInfo && projectInfo.tenantid;
      if (data && data.length && (!projectId || !projectInfo)) {
        projectId = data[0].id;
        tenantid = data[0].tenantid;
        projectInfo = data[0];
      }
    });
    // 如果是项目内用户的话，必须先有项目信息
    if ((userInfo.usertype === 3 || userInfo.usertype === 4) && !projectId) {
      localStorage.removeItem("omega_token");
      localStorage.removeItem("token");
      Message({
        message: $T("登录失败，该用户尚未关联项目"),
        showClose: true,
        type: "error"
      });
      return;
    }
  }

  store.dispatch("initProject", { projectId, tenantid, projectInfo });
}

const beforeAppBoot = async function () {
  const navmenuJson = await getJsonFile(`/static/navmenu.json?t=${Date.now()}`);
  const allNavmenu = [...navmenuJson.main, ...navmenuJson.admin];
  // 对菜单进行国际化
  if (window.localStorage.getItem("omega_language") === "en") {
    globalization(allNavmenu);
  }
  const pfNavmenu = allNavmenu.filter(item => item.Module === "pf");
  const pjNavmenu = allNavmenu.filter(item => item.Module === "fw");
  fileMenu("pfNavmenu", pfNavmenu);
  fileMenu("pjNavmenu", pjNavmenu);
  setOriginNavmenu(allNavmenu);
  this.store.commit("setNavmenu", allNavmenu);
  this.store.commit("setMenuList", allNavmenu);
  await this.store.dispatch("setSystemCfgJson");
  const title = document.createElement("title");
  title.innerText = this.store.state.appTitle;
  document.head.appendChild(title);
  OmegaAdminPlugin.unregister = !_.get(
    store,
    "state.systemCfg.onlineConfig",
    false
  );
  // 单点登录
  const singleSignOn = _.get(this.store, "state.systemCfg.singleSignOn");
  const fieldName =
    _.get(this.store, "state.systemCfg.singleSignOnModel.fieldName") || "code";
  // 取code
  const param = {};
  const search = window.location.search;
  if (search.length > 1) {
    const couples = search.substr(1).split("&");
    couples.forEach(couple => {
      const [key, value] = couple.split("=");
      param[decodeURIComponent(key)] = value ? decodeURIComponent(value) : "";
    });
  }
  if (singleSignOn && !window.location.hash.includes("/login")) {
    const value = param[fieldName];
    let token = getToken();
    if (value) {
      if (!token) {
        // 有code 无token 获取第三方用户信息
        let codeLoginRes = await codeLogin(value);
        token = codeLoginRes.data.token;
        setToken(token);
      }
    }
  }
};
const afterAppLogin = async function ({ user, isRoot }) {
  let token = getToken();
  let _user = user._user;
  let usertype = _user.customConfig && JSON.parse(_user.customConfig).usertype;

  localStorage.setItem("token", token);
  $.ajaxSetup({
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  // 框架修改 -兼容旧有项目 初始化用户信息
  await store.dispatch("setAuthStringent");
  await init();
  let allNavmenu = conf.getNavmenu();
  if (allNavmenu && allNavmenu.length === 0) {
    allNavmenu = store.state.navmenu;
  }
  this.store.commit("setNavmenu", allNavmenu);
  this.store.commit("setMenuList", allNavmenu);
  const pfNavmenu = allNavmenu.filter(item => item.Module === "pf");
  const pjNavmenu = allNavmenu.filter(item =>
    [undefined, "fw"].includes(item.Module)
  );

  // 设置导航菜单列表
  let navmenu;
  let Module;
  Module = getModule(_user);
  navmenu = Module === "pf" ? pfNavmenu : pjNavmenu;
  if (store.state.systemCfg.isPlatformModel) {
    // 多项目环境
    if (Module === "fw") {
      navmenu = fileMenu("pjNavmenu");
    }
    //，如果是ROOT用户，同时没有超级管理权限
    if (utilUser.isRoot() && !store.state.authStringent) {
      //只显示平台用户管理+角色页面权限
      let mapAuth = _user.roles[0]?.pageNodes.map(item => {
        return item.id;
      });
      let mapTree = ["cloud_platformusermanage", ...mapAuth];
      navmenu = filterROOTTree(fileMenu("pfNavmenu"), mapTree);
    }
  }
  this.conf.setNavmenu(navmenu);
  await loginCallback.call(this, _user);
};

const completeAppBoot = async function () {
  const isShowLanguage = store?.state?.systemCfg?.internationalization;
  Vue.set(layoutStore.store.state, "isShowSettingLanguage", isShowLanguage);
};

export { beforeAppBoot, afterAppLogin, completeAppBoot };
