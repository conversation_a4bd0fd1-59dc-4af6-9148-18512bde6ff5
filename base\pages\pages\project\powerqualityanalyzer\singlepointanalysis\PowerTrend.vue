<template>
  <!-- <CetChart :inputData_in="CetChart_line.inputData_in" v-bind="CetChart_line.config"></CetChart> -->
  <div
    style="position: relative; padding-top: 35px; box-sizing: border-box"
    class="adsaa"
  >
    <div class="trend-header">
      <!-- dataId组件 -->
      <ElSelect
        class="trend-select"
        v-model="ElSelect_dataId.value"
        v-bind="ElSelect_dataId"
        v-on="ElSelect_dataId.event"
      >
        <ElOption
          v-for="item in ElOption_dataId.options_in"
          :key="item[ElOption_dataId.key]"
          :label="item[ElOption_dataId.label]"
          :value="item[ElOption_dataId.value]"
          :disabled="item[ElOption_dataId.disabled]"
        ></ElOption>
      </ElSelect>
      <ElSelect
        class="trend-select"
        v-model="ElSelect_dataType.value"
        v-bind="ElSelect_dataType"
        v-on="ElSelect_dataType.event"
      >
        <ElOption
          v-for="item in ElOption_dataType.options_in"
          :key="item[ElOption_dataType.key]"
          :label="item[ElOption_dataType.label]"
          :value="item[ElOption_dataType.value]"
          :disabled="item[ElOption_dataType.disabled]"
        ></ElOption>
      </ElSelect>
    </div>
    <!-- <CetChart :inputData_in="CetChart_trend.inputData_in" v-bind="CetChart_trend.config"></CetChart> -->
    <CetTrend
      class="CetTrend"
      :queryMode="CetTrend2_realTime.queryMode"
      :dataConfig="CetTrend2_realTime.dataConfig"
      :queryTime_in="CetTrend2_realTime.queryTime_in"
      :params_in="CetTrend2_realTime.params_in"
      :queryTrigger_in="CetTrend2_realTime.queryTrigger_in"
      :clearTrigger_in="CetTrend2_realTime.clearTrigger_in"
      :showExtremButton="false"
      :showAverageButton="false"
      :showPointButton="false"
      :showTableButton="false"
      :showDiffButton="false"
      :viewSize="'small'"
      v-bind="CetTrend2_realTime.config"
    ></CetTrend>
  </div>
</template>
<script>
import common from "eem-utils/common";

import ELECTRICAL_DEVICE from "@/store/electricaldevice.js";

export default {
  name: "PowerTrend",
  components: {},
  props: {
    queryTime: Object,
    currentNode: Object,
    refreshTrigger_in: {
      type: [Number]
    }
  },
  computed: {
    modelLabels() {
      return ELECTRICAL_DEVICE.map(i => i.value);
    },
    powerQualityVisibi() {
      return this.$store.state.powerQualityVisibi;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  watch: {
    queryTime: {
      deep: true,
      handler: function (val, oldVal) {
        this.CetTrend2_realTime.queryTime_in.time = [
          new Date(val.startTime),
          new Date(val.endTime)
        ];
        // this.getPowerQualityRank();
      }
    },
    refreshTrigger_in: {
      deep: true,
      handler: function (val, oldVal) {
        this.getPecDevice();
      }
    },
    currentNode: {
      deep: true,
      handler: function (val, oldVal) {
        if (
          !(
            this._.get(val, "data.tree_id") &&
            this._.get(val, "data.modelLabel")
          )
        )
          return;
        if (
          val.data.tree_id === this._.get(oldVal, "data.tree_id") &&
          val.data.modelLabel === this._.get(oldVal, "data.modelLabel")
        )
          return;
        this.getPecDevice();
      }
    },
    powerQualityVisibi(val, oldVal) {
      if (val) this.getPecDevice();
    }
  },
  data() {
    return {
      // dataId组件
      ElSelect_dataId: {
        value: 5,
        size: "mini",
        style: {
          width: "140px"
        },
        event: {
          change: this.ElSelect_dataId_change_out
        }
      },
      // dataId组件
      ElOption_dataId: {
        options_in: [
          // {
          //   id: 2,
          //   text: "Va",
          //   label: "A相电压"
          // },
          // {
          //   id: 3,
          //   text: "Vb",
          //   label: "B相电压"
          // },
          // {
          //   id: 4,
          //   text: "Vc",
          //   label: "C相电压"
          // },
          { id: 5, text: "Vab", label: "AB线电压" },
          { id: 6, text: "Vbc", label: "BC线电压" },
          { id: 7, text: "Vca", label: "CA线电压" },
          {
            id: 1000001,
            text: "Ia",
            label: "A相电流"
          },
          {
            id: 1000002,
            text: "Ib",
            label: "B相电流"
          },
          {
            id: 1000003,
            text: "Ic",
            label: "C相电流"
          },
          {
            id: 2000004,
            text: "kW Total",
            label: "总有功功率"
          },
          {
            id: 2000008,
            text: "kvar Total",
            label: "总无功功率"
          },
          {
            id: 2000012,
            text: "kVA Total",
            label: "总视在功率"
          },
          {
            id: 1,
            text: "Freq",
            label: "频率"
          },
          {
            id: 2000016,
            text: "PFavg",
            label: "平均功率因数"
          }
        ],
        key: "id",
        value: "id",
        label: "label",
        disabled: "disabled"
      },
      ElSelect_dataType: {
        value: 2,
        size: "mini",
        style: {
          width: "100px"
        },
        event: {
          change: this.ElSelect_dataType_change_out
        }
      },
      // dataId组件
      ElOption_dataType: {
        options_in: [
          {
            id: 2,
            text: "最大值"
          },
          {
            id: 3,
            text: "最小值"
          },
          {
            id: 4,
            text: "平均值"
          },
          {
            id: 5,
            text: "C95值"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      selectDataIdName: "AB线电压",
      selectDataTypeName: "最大值",
      CetTrend2_realTime: {
        queryMode: "diff",
        dataConfig: {
          queryUrl: "/device-data/api/v1/batch/datalog/span/group",
          type: "POST"
        },
        queryTrigger_in: new Date().getTime(),
        clearTrigger_in: new Date().getTime(),
        queryTime_in: {
          timeType: 1,
          time: common.initDateRange("M").map(item => new Date(item))
        },
        title_in: "趋势曲线",
        params_in: [],
        config: {
          splitLine: false,
          color: [
            "#c23531",
            "#61a0a8",
            "#d48265",
            "#91c7ae",
            "#749f83",
            "#ca8622",
            "#bda29a",
            "#6e7074",
            "#546570",
            "#c4ccd3"
          ]
        }
      },
      // 电能质量指标组件
      CetChart_trend: {
        //组件输入项
        inputData_in: null,
        config: {
          options: {
            title: {
              text: "设备告警次数趋势",
              x: "center",
              textStyle: {
                fontSize: 16,
                fontWeight: "normal",
                color: "#999"
              }
            },
            color: ["#61a5e8"],
            grid: {
              top: 35,
              right: 25,
              bottom: 35,
              left: 50
            },
            tooltip: {
              trigger: "axis",
              axisPointer: {
                lineStyle: {
                  width: 2,
                  color: "#61a5e8"
                }
              }
            },
            xAxis: {
              type: "time"
            },
            yAxis: {
              type: "value"
            },

            series: [
              {
                type: "line",
                encode: {
                  x: "time",
                  y: "value"
                },
                lineStyle: {
                  width: 3
                },
                symbol: "circle", //设定为实心点
                symbolSize: 12 //设定实心点的大小
              }
            ]
          }
        }
      },
      deviceidList: []
    };
  },
  methods: {
    // dataId输出,方法名要带_out后缀
    ElSelect_dataId_change_out(val) {
      var res = _.find(this.ElOption_dataId.options_in, { id: val });
      this.selectDataIdName = res.label;

      if (this._.isEmpty(this.deviceidList)) return;
      var _this = this;
      this.$nextTick(() => {
        _this.getTrend();
      });
    },
    // 数据类型
    ElSelect_dataType_change_out(val) {
      var res = _.find(this.ElOption_dataType.options_in, { id: val });
      this.selectDataTypeName = res.text;

      if (this._.isEmpty(this.deviceidList)) return;
      var _this = this;
      this.$nextTick(() => {
        _this.getTrend();
      });
    },
    getPecDevice() {
      const node = this.currentNode;
      if (!node) return;
      if (!this.modelLabels.includes(node.data.modelLabel)) {
        return; // this.$message("请选择被监测设备节点");
      }
      common.requestData(
        {
          url: "/eem-service/v1/pq/deviceId?projectId=" + this.projectId,
          data: {
            id: node.data.id,
            modelLabel: node.data.modelLabel
          }
        },
        (data, res) => {
          const list = this._.get(res, "data", []);
          const deviceidObj = this._.find(list, { metertype: 9 });
          this.deviceidList = [deviceidObj];
          if (deviceidObj && deviceidObj.protocalname === "iMeter6") {
            this.ElSelect_dataType.value = 1;
            this.selectDataTypeName = "实时值";
            this.ElOption_dataType.options_in = [
              {
                id: 1,
                text: "实时值"
              }
            ];
          } else {
            this.ElSelect_dataType.value = 2;
            this.selectDataTypeName = "最大值";
            this.ElOption_dataType.options_in = [
              {
                id: 2,
                text: "最大值"
              },
              {
                id: 3,
                text: "最小值"
              },
              {
                id: 4,
                text: "平均值"
              },
              {
                id: 5,
                text: "C95值"
              }
            ];
          }
          this.getTrend();
          // this.eventStatistics = res;
        }
      );
    },

    getTrend() {
      const node = this.currentNode;
      const selectDataIdName = this.selectDataIdName;
      const selectDataTypeName = this.selectDataTypeName;

      this.CetTrend2_realTime.params_in = this.deviceidList.map(item => {
        return {
          // dataId: this.CetSimpleSelect_dataId.selectNode,
          dataId: this.ElSelect_dataId.value,
          dataName: selectDataIdName,
          dataTypeId: this.ElSelect_dataType.value,
          dataTypeName: selectDataTypeName,
          deviceId: item.deviceid,
          deviceName: node.data.name,
          logicalId: 1,
          unit: ""
        };
      });
    }
  }
};
</script>
<style lang="scss" scope>
.trend-header {
  position: absolute;
  top: 0;
  right: 0;
  .trend-select {
    float: left;
    margin: 0 5px;
  }
}
.CetTrend {
  min-height: 220px;
  :deep(.el-header.trend-class) {
    background-color: transparent;
  }
}
</style>
