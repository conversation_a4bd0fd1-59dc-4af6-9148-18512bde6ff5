<template>
  <div class="fullfilled eem-common">
    <div class="fullheight eem-aside treeBox">
      <div class="common-title-H1">{{ $T("用户列表") }}</div>
      <CetTree
        ref="userTree"
        class="CetTree mtJ3 eem-no-line"
        :selectNode.sync="CetTree_Users.selectNode"
        :checkedNodes.sync="CetTree_Users.checkedNodes"
        v-bind="CetTree_Users"
        v-on="CetTree_Users.event"
      ></CetTree>
      <div class="clearfix">
        <CetButton
          class="mtJ3 fr"
          v-permission="'user_create'"
          v-bind="CetButton_Add"
          v-on="CetButton_Add.event"
        ></CetButton>
      </div>
    </div>
    <div
      class="contentBox mlJ3"
      v-show="currentNode && currentNode.modelLabel === 'user'"
    >
      <div class="mainContent bg1">
        <div class="title clearfix">
          {{ $T("用户权限信息") }}
          <CetButton
            class="mlJ1 fr"
            v-if="!isOneself && currentNode"
            v-permission="'user_update'"
            v-bind="CetButton_edit"
            v-on="CetButton_edit.event"
          ></CetButton>
          <CetButton
            class="mlJ1 fr delete"
            v-if="!isOneself && currentNode"
            v-permission="'user_delete'"
            v-bind="CetButton_del"
            v-on="CetButton_del.event"
          ></CetButton>
          <CetButton
            v-if="showButton"
            class="mlJ1 fr"
            v-bind="CetButton_editPassword"
            v-on="CetButton_editPassword.event"
          ></CetButton>
        </div>
        <UserView
          :currentNode_in="currentNode"
          v-if="currentNode && currentNode.modelLabel === 'user'"
          @role_out="view_role_out"
          @userInfo_out="view_userInfo_out"
        />
      </div>
      <div class="tabs bg1">
        <el-tabs
          v-model="activeName"
          @tab-click="handleClick"
          class="eem-tabs-custom mlJ1"
        >
          <el-tab-pane
            v-for="item in userManageTabs"
            :label="item.label"
            :name="item.name"
            :key="item.name"
          ></el-tab-pane>
        </el-tabs>
      </div>
      <div class="detailContent mtJ3 bg1 pJ3 plJ4 prJ4">
        <div v-show="activeName === 'permissions'" class="fullfilled">
          <CetTree
            class="CetTree eem-no-line"
            v-bind="CetTree_permissions"
            v-on="CetTree_permissions.event"
          ></CetTree>
        </div>
        <div v-show="activeName === 'project'" class="fullfilled">
          <CetGiantTree
            class="CetTree eem-no-line"
            v-bind="CetGiantTree_project"
            v-on="CetGiantTree_project.event"
          ></CetGiantTree>
        </div>
        <div v-show="activeName === 'Graph'" class="fullfilled">
          <CetGiantTree
            class="CetTree eem-no-line"
            v-bind="CetGiantTree_graph"
            v-on="CetGiantTree_graph.event"
          ></CetGiantTree>
        </div>
        <div v-show="activeName === 'pecReport'" class="fullfilled">
          <CetGiantTree
            class="CetTree eem-no-line"
            v-bind="CetGiantTree_pecReport"
            v-on="CetGiantTree_pecReport.event"
          ></CetGiantTree>
        </div>
        <div v-show="activeName === 'mReport'" class="fullfilled">
          <CetGiantTree
            class="CetTree eem-no-line"
            v-bind="CetGiantTree_mReport"
            v-on="CetGiantTree_mReport.event"
          ></CetGiantTree>
        </div>
        <div v-show="activeName === 'onlyReport'" class="fullfilled">
          <CetGiantTree
            class="CetTree eem-no-line"
            v-bind="CetGiantTree_onlyReport"
            v-on="CetGiantTree_onlyReport.event"
          ></CetGiantTree>
        </div>
        <div v-show="activeName === 'dashboard'" class="fullfilled">
          <CetTree
            class="CetTree eem-no-line"
            v-bind="CetTree_dashboard"
            v-on="CetTree_dashboard.event"
          ></CetTree>
        </div>
        <div v-show="activeName === 'video'" class="fullfilled">
          <CetGiantTree
            class="CetTree"
            v-bind="CetGiantTree_video"
            v-on="CetGiantTree_video.event"
          ></CetGiantTree>
        </div>
      </div>
    </div>
    <div
      class="contentBox mlJ3 eem-cont"
      v-show="!currentNode || currentNode.modelLabel !== 'user'"
    >
      {{ $T("请先选择用户节点") }}
    </div>
    <EditUser
      :visibleTrigger_in="EditUser.visibleTrigger_in"
      :closeTrigger_in="EditUser.closeTrigger_in"
      :inputData_in="EditUser.inputData_in"
      :userDashboardList="userDashboardList"
      @finishTrigger_out="refresh"
    />
    <ResetPassword
      :visibleTrigger_in="ResetPassword.visibleTrigger_in"
      :closeTrigger_in="ResetPassword.closeTrigger_in"
      :queryId_in="ResetPassword.queryId_in"
      :inputData_in="ResetPassword.inputData_in"
    ></ResetPassword>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";
import UserView from "./view/overview";
import EditUser from "./dialog/edit";
import ResetPassword from "eem-components/resetPassword/index.vue";
import { getOperatePermissionTreeNodes } from "eem-utils/permission.js";
import TREE_PARAMS from "@/store/treeParams.js";
import { httping } from "@omega/http";
export default {
  components: {
    UserView,
    EditUser,
    ResetPassword
  },
  computed: {
    showButton() {
      return (
        this.currentNode &&
        this.currentNode.modelLabel === "user" &&
        this.$store.state.userInfo.id === 1
      );
    },
    isOneself() {
      return (
        this.currentNode &&
        this.currentNode.modelLabel === "user" &&
        this.$store.state.userInfo.id === this.currentNode.id &&
        this.$store.state.userInfo.id !== 1
      );
    },
    userInfo() {
      var vm = this;
      return vm.$store.state.userInfo;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    projectTenantId() {
      var vm = this;
      return vm.$store.state.projectTenantId;
    },
    userManageTabs() {
      const en = window.localStorage.getItem("omega_language") === "en";
      const tabNameList = {
        permissions: $T("操作权限"),
        project: $T("项目节点权限"),
        Graph: $T("实时监控权限"),
        pecReport: $T("pecReport报表权限"),
        mReport: $T("mReport报表权限"),
        onlyReport: $T("onlyReport报表权限"),
        dashboard: $T("dashboard权限"),
        video: $T("视频权限")
      };
      if (!this.$store.state.systemCfg.userManageTabs) {
        const keys = Object.keys(tabNameList);
        return keys.map(item => {
          return {
            label: tabNameList[item],
            name: item
          };
        });
      } else {
        let userManageTabs = [];
        const systemCfgTabs = this.$store.state.systemCfg.userManageTabs;
        systemCfgTabs.forEach(item => {
          const label = en ? item.enLabel : item.label;
          userManageTabs.push({
            name: item.name,
            label: label ? label : tabNameList[item.name]
          });
        });
        return userManageTabs;
      }
    }
  },
  data(vm) {
    const addDiyDom = (treeId, treeNode) => {
        var aObj = $("#" + treeNode.tId + "_a");
        if (treeNode.level === 0 && vm.treeNodeTooltip) {
          vm.treeNodeTooltip = false;
          const dom = `
            <div class="inline-block relative tooltipBox">
              <i class="el-icon-question fcT2"></i>
              <div class="tooltip el-tooltip__popper is-light" x-placement="bottom">${$T(
                "置灰代表未拥有此节点全部权限"
              )}
                <div x-arrow="" class="popper__arrow" style="left: 49.5px;"></div>
              </div>
            </div>`;
          aObj.append(dom);
        }
      },
      setNodeClasses = (treeId, treeNode) => {
        return treeNode.childSelectState !== 1
          ? { add: ["halfSelectedNode"] }
          : { remove: ["halfSelectedNode"] };
      };
    return {
      treeNodeTooltip: true,
      dashboardList: [], // 全部dashboard节点
      userDashboardList: [], // 当前用户拥有权限的dashboard权限数组
      currentNode: null,
      // 用户树组件
      CetTree_Users: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        defaultExpandAll: true,
        event: {
          currentNode_out: this.CetTree_Users_currentNode_out
        }
      },

      // 增加用户组件
      CetButton_Add: {
        visible_in: true,
        disable_in: false,
        title: $T("新增用户"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_Add_statusTrigger_out
        }
      },
      CetButton_edit: {
        visible_in: true,
        disable_in: false,
        title: $T("修改"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_edit_statusTrigger_out
        }
      },
      CetButton_del: {
        visible_in: true,
        disable_in: false,
        title: $T("删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_del_statusTrigger_out
        }
      },
      CetButton_editPassword: {
        visible_in: true,
        disable_in: false,
        title: $T("重置密码"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_editPassword_statusTrigger_out
        }
      },
      activeName: "",
      // 操作权限树组件
      CetTree_permissions: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        defaultExpandAll: true,
        event: {}
      },
      CetGiantTree_graph: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "text"
            }
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {}
      },
      CetGiantTree_pecReport: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "nodeName"
            }
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {}
      },
      CetGiantTree_mReport: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "nodeName"
            }
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {}
      },
      CetGiantTree_onlyReport: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "id"
            },
            key: {
              name: "name"
            }
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {}
      },
      CetGiantTree_project: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "name"
            }
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {}
      },
      CetTree_dashboard: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: [], //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {}
      },
      CetGiantTree_video: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "name"
            }
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {}
      },
      // 新增或编辑用户组弹窗
      EditUser: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      ResetPassword: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        finishTrigger_out: new Date().getTime(),
        saveData_out: {}
      }
    };
  },
  methods: {
    // 用户树组件输出
    CetTree_Users_currentNode_out(val) {
      const vm = this;
      if (val.modelLabel === "usergroup") {
        return;
      }
      vm.currentNode = this._.cloneDeep(val);
      let node = vm.$refs.userTree.$refs.tree.getNode(val);
      if (node) {
        let usergroupObj = this._.get(node, "parent.data");
        if (this._.get(usergroupObj, "modelLabel") === "usergroup") {
          this.currentNode.userGroupName = usergroupObj.name;
        }
      }
      this.activeName = "";
      let activeName = this._.get(this.userManageTabs, "[0].name", "");
      this.$nextTick(() => {
        this.activeName = activeName;
      });
      this.handleClick({
        name: activeName
      });
      this.filterOperationAuths(this._.get(val, "roles[0]"));
    },
    CetButton_Add_statusTrigger_out() {
      this.EditUser.inputData_in = null;
      this.EditUser.visibleTrigger_in = new Date().getTime();
    },
    // 编辑用户
    CetButton_edit_statusTrigger_out() {
      const vm = this;
      if (!vm.currentNode) {
        return;
      }
      this.EditUser.inputData_in = this._.cloneDeep(this.currentNode);
      this.EditUser.visibleTrigger_in = new Date().getTime();
    },
    // 删除用户
    CetButton_del_statusTrigger_out() {
      const vm = this;
      const userData = vm.currentNode;
      if (!userData) {
        return;
      }

      vm.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          customApi
            .deleteUser({
              id: userData.id,
              name: userData.name
            })
            .then(() => {
              vm.CetTree_Users.selectNode = null;
              vm.currentNode = null;
              vm.refresh();
            });
        })
        .catch(() => {});
    },
    CetButton_editPassword_statusTrigger_out() {
      this.ResetPassword.inputData_in = this._.cloneDeep(this.currentNode);
      this.ResetPassword.visibleTrigger_in = new Date().getTime();
    },
    // 刷新数据
    refresh() {
      this.getTreeData();
    },
    async getTreeData() {
      const vm = this;
      let data = {
        loadChildren: true,
        removeRootUser: true,
        tenantId: vm.projectTenantId
      };
      let res = await customApi.queryProjectUserAndGroup(data);
      const treeData = vm._.get(res, "data", []) || [];

      vm.CetTree_Users.inputData_in = treeData;
      if (!vm._.get(vm.CetTree_Users.selectNode, "id")) {
        if (vm._.get(treeData, "[0].children[0]")) {
          vm.CetTree_Users.selectNode = vm._.get(treeData, "[0].children[0]");
        } else {
          vm.CetTree_Users.selectNode = vm._.get(treeData, "[0]");
        }
      }
    },
    getDashboardList() {
      var _this = this;
      httping({
        url: `/model/v1/query`,
        data: {
          rootID: 0,
          rootLabel: "dashboard"
        },
        method: "POST"
      }).then(res => {
        if (res.code === 0) {
          let dashboardList = [],
            data = _this._.get(res, "data", []) || [];
          dashboardList = data.filter(item => {
            return item.content.projectId === _this.projectId;
          });
          _this.CetTree_dashboard.inputData_in = dashboardList;
          _this.dashboardList = dashboardList;
          _this.userDashboardList = dashboardList;
        }
      });
    },
    handleClick(val) {
      if (!this.currentNode) {
        return;
      }
      this.treeNodeTooltip = true;
      switch (val.name) {
        case "project":
          this.getProjectTree();
          break;
        case "Graph":
          this.getGraphTree();
          break;
        case "pecReport":
          this.getPecReportTree_out();
          break;
        case "mReport":
          this.getMReportTree_out();
          break;
        case "onlyReport":
          this.getOnlyReportTree_out();
          break;
        case "video":
          this.getVideoTree_out();
          break;
        default:
          break;
      }
    },
    // 获取用户项目节点权限
    getProjectTree() {
      let queryData = {
          rootID: this.projectId,
          rootLabel: "project",
          subLayerConditions: TREE_PARAMS.userManageProjectTree,
          treeReturnEnable: true
        },
        paramsData = {
          userId: this.currentNode.id
        };
      customApi
        .getNodeTreeSimple(queryData, false, paramsData)
        .then(response => {
          if (response.code === 0) {
            let data = this._.get(response, "data", []);
            this.CetGiantTree_project.inputData_in = data;
          }
        });
    },
    // 获取图形节点权限
    getGraphTree() {
      var queryData = {
        tenantId: this.currentNode.tenantId,
        userId: this.currentNode.id
      };
      customApi.getGraphTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_graph.inputData_in = this.formatGraphNodeTree(data);
        } else {
          this.CetGiantTree_graph.inputData_in = [];
        }
      });
    },
    formatGraphNodeTree(rawNodeAry) {
      var me = this;
      rawNodeAry = rawNodeAry || [];

      me._(rawNodeAry).forEach(function (rawNode) {
        rawNode.id = rawNode.nodeId;
        if (!rawNode.children || !rawNode.children.length) {
          rawNode.children = null;
          rawNode.leaf = true;
        } else {
          rawNode.leaf = false;
          rawNode.children = me.formatGraphNodeTree(rawNode.children);
        }
        rawNode.tree_id = `${rawNode.nodeType}_${rawNode.id}`;
      });
      return rawNodeAry;
    },
    // 获取报表节点权限
    getPecReportTree_out() {
      var queryData = {
        tenantId: this.currentNode.tenantId,
        userId: this.currentNode.id
      };
      customApi.getPecReportTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_pecReport.inputData_in =
            this.formatReporthNodeTree(data);
        } else {
          this.CetGiantTree_pecReport.inputData_in = [];
        }
      });
    },
    // 获取onlyReport报表节点权限
    getOnlyReportTree_out() {
      var queryData = {
        tenantId: this.currentNode.tenantId,
        userId: this.currentNode.id
      };
      customApi.getOnlyReportTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_onlyReport.inputData_in =
            this.formatReporthNodeTree(data);
        } else {
          this.CetGiantTree_onlyReport.inputData_in = [];
        }
      });
    },
    getMReportTree_out() {
      var queryData = {
        tenantId: this.currentNode.tenantId,
        userId: this.currentNode.id
      };
      customApi.getMReportTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_mReport.inputData_in =
            this.formatReporthNodeTree(data);
        } else {
          this.CetGiantTree_mReport.inputData_in = [];
        }
      });
    },
    formatReporthNodeTree(rawNodeAry) {
      var me = this;
      rawNodeAry = rawNodeAry || [];

      me._(rawNodeAry).forEach(function (rawNode) {
        rawNode.id = rawNode.nodeId;
        if (!rawNode.children || !rawNode.children.length) {
          rawNode.children = null;
          rawNode.leaf = true;
        } else {
          rawNode.leaf = false;
          rawNode.children = me.formatReporthNodeTree(rawNode.children);
        }
        rawNode.tree_id = `${rawNode.nodeType}_${rawNode.id}`;
      });
      return rawNodeAry;
    },
    getVideoTree_out() {
      const queryData = {
        tenantId: this.currentNode.tenantId,
        userId: this.currentNode.id
      };
      customApi.videoQueryFolders(queryData).then(response => {
        if (response.code === 0) {
          this.CetGiantTree_video.inputData_in = this.formatVideoNodeTree(
            response.data
          );
        } else {
          this.CetGiantTree_video.inputData_in = [];
        }
      });
    },
    formatVideoNodeTree(rawNodeAry) {
      var me = this;
      rawNodeAry = rawNodeAry || [];

      me._(rawNodeAry).forEach(function (rawNode) {
        if (!rawNode.children || !rawNode.children.length) {
          rawNode.children = null;
          rawNode.leaf = true;
        } else {
          rawNode.leaf = false;
          rawNode.children = me.formatVideoNodeTree(rawNode.children);
        }
      });
      return rawNodeAry;
    },
    // 解析出dashboard权限
    analysisDashboard(userInfo) {
      const vm = this;
      if (!userInfo) {
        vm.CetTree_dashboard.filterNodes_in = [];
        return;
      }
      var customConfig = vm._.get(userInfo, "customConfig")
        ? JSON.parse(vm._.get(userInfo, "customConfig"))
        : {};
      vm.CetTree_dashboard.filterNodes_in = customConfig.dashboard
        ? customConfig.dashboard.map(i => {
            return { id: i };
          })
        : [];
    },
    // 按角色过滤角色操作权限树
    filterOperationAuths(role) {
      const vm = this;
      if (!role) {
        vm.CetTree_permissions.filterNodes_in = [];
        return;
      }
      const filtedAuths = [];
      (role.auths || []).forEach(val => {
        filtedAuths.push({
          id: val
        });
      });
      vm.CetTree_permissions.filterNodes_in = filtedAuths;
    },
    view_role_out(role) {
      this.filterOperationAuths(role);
    },
    view_userInfo_out(userInfo) {
      this.analysisDashboard(userInfo);
    }
  },
  activated() {
    this.CetTree_Users.selectNode = null;
    this.refresh();
    this.getDashboardList();
    this.CetTree_permissions.inputData_in = getOperatePermissionTreeNodes(true);
  }
};
</script>
<style lang="scss" scoped>
.eem-common {
  display: flex;
  :deep(.halfSelectedNode) {
    @include font_color(T6);
  }
  .treeBox {
    width: 316px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    .CetTree {
      flex: 1;
      min-height: 0;
    }
  }
  .contentBox {
    flex: 1;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    min-width: 0;
    .mainContent {
      border-radius: mh-get(C) mh-get(C) 0 0;
      @include padding(J3 J4 J3 J4);
      .title {
        @include font_size(H1);
        @include line_height(Hm);
        font-weight: bold;
      }
      &.borderRadiusC {
        @include border_radius(C);
      }
    }
    .tabs {
      border-radius: 0 0 mh-get(C) mh-get(C);
    }
    .detailContent {
      flex: 1;
      min-height: 0;
      box-sizing: border-box;
      @include border_radius(C);
      .CetTree {
        :deep(.device-search) {
          width: 240px;
        }
      }
      .pecCoreTree {
        overflow: auto;
        height: 100%;
      }
    }
  }
  :deep(.tooltipBox) {
    .tooltip {
      left: -44px;
      display: none;
    }
    &:hover .tooltip {
      display: block;
    }
  }
}
</style>
