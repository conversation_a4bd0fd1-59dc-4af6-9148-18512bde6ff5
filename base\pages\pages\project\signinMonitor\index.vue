﻿<template>
  <div class="page eem-common">
    <el-container style="height: 100%">
      <el-aside width="315px" class="eem-aside flex-column">
        <div>
          <span class="common-title-H2">签到点分组</span>
        </div>
        <div class="flex-auto eem-group-list mtJ3">
          <el-tooltip
            v-for="(item, key) in groupList"
            :key="key"
            :content="item.name"
            effect="light"
            placement="top"
          >
            <div
              :class="['group-item', { active: key === teamIndex }]"
              @click="handleClick_team_out(item, key)"
            >
              {{ item.name }}
            </div>
          </el-tooltip>
        </div>
        <div class="mtJ3" v-show="isHasBtn">
          <!-- add按钮组件 -->
          <CetButton
            class="fr"
            v-bind="CetButton_add"
            v-on="CetButton_add.event"
          ></CetButton>
        </div>
        <div class="mtJ3 common-title-H2">巡检概况</div>
        <div class="mtJ3">
          <div class="lh32">
            <span class="fl text-left">当前班组：</span>
            <el-tooltip :content="activeGroup.name" effect="light">
              <span class="fl text-right">{{ activeGroup.name }}</span>
            </el-tooltip>
          </div>
          <div class="lh32">
            <span class="fl text-left">签到点进度：</span>
            <span class="fl text-right">
              {{ filNum(signinMsg.signedPointNumber) }}/{{
                filNum(signinMsg.signPointNumber)
              }}
            </span>
          </div>
          <div class="lh32">
            <span class="fl text-left">巡检设备进度：</span>
            <span class="fl text-right">
              {{ filNum(signinMsg.checkedObjectNumber) }}/{{
                filNum(signinMsg.objectNumber)
              }}
            </span>
          </div>
        </div>
      </el-aside>
      <el-container class="fullheight mlJ3 eem-container">
        <div style="height: 100%; width: 100%" v-show="showGraph">
          <CetGraph v-bind="CetGraphData"></CetGraph>
        </div>
        <div
          style="height: 100%; width: 100%; text-align: center"
          v-show="!showGraph"
        >
          <span style="position: relative; top: 45%; font-size: 24px">
            暂无平面图信息，请绑定图元！
          </span>
        </div>
      </el-container>
    </el-container>
    <graphTree v-bind="graphTree" v-on="graphTree.event"></graphTree>
    <jumpDialog v-bind="jumpDialog" v-on="jumpDialog.event"></jumpDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";
import CetGraph from "cet-graph";
import { CetGraphConfig } from "cet-graph";
import graphTree from "./dialog/graphTreeDialog";
import jumpDialog from "./dialog/jumpDialog";

export default {
  name: "RealtimeMonitor",
  components: {
    CetGraph,
    graphTree,
    jumpDialog
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userInfo() {
      var vm = this;
      return vm.$store.state.userInfo;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    }
  },

  data(vm) {
    return {
      groupList: [], // 分组列表
      CetGraphData: {
        path_in: "",
        refresh_trigger_in: 0,
        userName_in: "ROOT",
        excuteJSInterception: function name(details, done) {
          console.log(details);
          const javascriptStr = vm._.get(details, "javascript", "");
          // let javascriptStr = "@signin?id=15";
          if (javascriptStr.indexOf("@signin") !== -1) {
            const signinId = javascriptStr.split("?id=")[1] || null;
            vm.jumpDialog.inputData_in = {
              id: Number(signinId)
            };
            vm.jumpDialog.openTrigger_in = new Date().getTime();
          } else {
            done();
          }
        }
      },
      // ad组件
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: "绑定平面图",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      // 巡检对象弹窗
      graphTree: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        tableData: null,
        event: {
          saveData_out: this.graphTree_saveData_out
        }
      },
      jumpDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        event: {
          saveData_out: this.jumpDialog_saveData_out
        }
      },
      teamIndex: 0, // 当前点击的分组
      showGraph: false,
      activeGroup: {},
      signinMsg: {},
      isHasBtn: false
    };
  },
  watch: {},
  methods: {
    //打开绑定签到点图形
    CetButton_add_statusTrigger_out(val) {
      this.graphTree.inputData_in = this._.cloneDeep(this.activeGroup);
      this.graphTree.openTrigger_in = new Date().getTime();
    },
    // 巡检对象保存
    graphTree_saveData_out(val) {
      this.getGroupList_out();
    },
    // 确定跳转页面
    jumpDialog_saveData_out(val) {
      if (!val || !val.jumpName) {
        return;
      }
      this.$router.push({
        name: val.jumpName,
        params: {
          id: val.id
        }
      });
    },
    findChildren(obj) {
      if (obj.children && obj.children.length > 0) {
        return this.findChildren(obj.children[0]);
      } else {
        return obj;
      }
    },
    // 获取分组列表
    getGroupList_out() {
      customApi.groupManage("GET").then(res => {
        if (res.code === 0) {
          this.groupList = res.data;
          const teamData = this._.get(res, "data[0]", {});
          if (this.activeGroup) {
            let activeGroup = teamData;
            let teamIndex = 0;
            this.groupList.forEach((item, index) => {
              if (this.activeGroup.id === item.id) {
                activeGroup = item;
                teamIndex = index;
              }
            });
            this.handleClick_team_out(activeGroup, teamIndex);
          } else {
            this.handleClick_team_out(teamData, 0);
          }
        }
      });
    },
    //点击签到点分组
    handleClick_team_out(team, index) {
      this.teamIndex = index;
      this.activeGroup = this._.cloneDeep(team);
      this.querySignInPointStatusCount_out();
      if (team.relatedgraph) {
        this.showGraph = true;
        const relatedgraph = JSON.parse(team.relatedgraph);
        this.CetGraphData.path_in = relatedgraph.nodeName;
        // this.CetGraphData.path_in = ".//测试图形//HQP测试数据源.drw";
        this.CetGraphData.refresh_trigger_in = new Date().getTime();
      } else {
        this.showGraph = false;
        this.CetGraphData.path_in = null;
        this.CetGraphData.refresh_trigger_in = new Date().getTime();
      }
    },
    querySignInPointStatusCount_out() {
      if (!this.activeGroup && !this.activeGroup.id) {
        return;
      }
      var queryData = {
        signGroupId: this.activeGroup.id
      };
      customApi.querySignInPointStatusCount(queryData).then(res => {
        if (res.code === 0) {
          this.signinMsg = res.data || {};
        }
      });
    },
    queryAuthOperationAuthCheck_out() {
      var queryData = {
        andOr: 1,
        authNames: ["signgroup_releategraph"]
      };
      customApi.queryAuthOperationAuthCheck(queryData).then(res => {
        if (res.code === 0) {
          const isOk = res.data || false;
          this.isHasBtn = isOk;
        }
      });
    },
    filNum(val) {
      if ([undefined, null, NaN].includes(val)) {
        return "--";
      } else {
        return val;
      }
    }
  },
  created: function () {
    this.CetGraphData.userName_in = this.userInfo.name;
    localStorage.setItem("token", this.token);
    CetGraphConfig.enablePTZControl = this.systemCfg.enablePTZControl;
    CetGraphConfig.isNewAuth = this.systemCfg.isNewAuth;
    const enLanguage = window.localStorage.getItem("omega_language") === "en";
    CetGraphConfig.locale = enLanguage ? "en" : "zh-cn";
  },
  activated: function () {
    this.queryAuthOperationAuthCheck_out();
    this.getGroupList_out();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  // position: relative;
}
.lh40 {
  height: 40px;
  line-height: 40px;
  padding: 0px 10px;
}
.text-center {
  text-align: center;
}
.text-left {
  display: inline-block;
  width: 100px;
}
.text-right {
  display: inline-block;
  width: calc(100% - 100px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.hotarea {
  position: absolute;
  width: 10px;
  right: 0px;
  top: 0px;
  bottom: 0px;
  background-color: #aaaaaa;
}

.operate-bar-container {
  position: absolute;
  width: 80px;
  right: 0px;
  top: 0px;
  height: 100%;
}

.operate-bar {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
  background-color: #aaaaaa;
}

.full-screen {
  position: absolute;
  font-size: 50px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
</style>
