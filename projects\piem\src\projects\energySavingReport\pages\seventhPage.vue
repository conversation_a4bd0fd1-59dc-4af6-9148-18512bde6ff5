<template>
  <div class="page">
    <reportPage :pageNum="pagesNumber" :queryTime="queryTime">
      <div class="mbJ4">
        <div class="second-title mbJ2">
          {{ $T("3.3 污水回注系统预计节费量趋势") }}
        </div>
        <energySavingTrend
          :chartData="reportData.stationsCostVOList || []"
          type="cost"
        />
      </div>

      <div class="mbJ4">
        <div class="second-title mbJ2">
          {{ $T("3.4 回注泵节费分析") }}
        </div>
        <energySavingAnalysis
          :chartData="reportData.pumpCostVOList || []"
          type="cost"
        />
      </div>

      <div class="mbJ4">
        <stationTable
          :columnList="pumpColumnList"
          :tableData="reportData.pumpCostVOList?.slice(0, 8) || []"
        />
      </div>
    </reportPage>
  </div>
</template>

<script>
import reportPage from "../components/reportPage.vue";
import energySavingTrend from "../components/energySavingTrend.vue";
import energySavingAnalysis from "../components/energySavingAnalysis.vue";
import stationTable from "../components/stationTable.vue";
import common from "eem-utils/common";
export default {
  name: "secondPage",
  components: {
    reportPage,
    energySavingTrend, // 集输增压系统预计节能量趋势
    energySavingAnalysis, // 天然气压缩机节能分析
    stationTable // 天然气压缩机节能分析
  },
  props: {
    reportData: {
      type: Object,
      default: () => {}
    },
    queryTime: {
      type: Number
    },
    pagesNumber: {
      type: String,
      default: () => {
        return "07";
      }
    }
  },
  data() {
    return {
      pumpColumnList: [
        {
          prop: "order",
          label: $T("排名"),
          align: "left",
          showOverflowTooltip: true,
          width: 50
        },
        {
          prop: "name",
          label: $T("分析对象"),
          align: "left",
          showOverflowTooltip: true
        },
        {
          prop: "recycleValue",
          label: $T("月实际回注量(万立方米)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 110
        },
        {
          prop: "cost",
          label: $T("月实际用电成本(万元)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 100
        },
        {
          prop: "optimizationCost",
          label: $T("优化后预计用电成本(万元)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 120
        },
        {
          prop: "savingCost",
          label: $T("预计节费量(万元)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 100
        },
        {
          prop: "savingCostRate",
          label: $T("预计节费率(%)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 80
        }
      ]
    };
  },
  watch: {},
  methods: {
    formatNumberWithPrecision(...args) {
      return common.formatNumberWithPrecision(...args);
    }
  }
};
</script>

<style lang="scss" scoped>
@media print {
  @page {
    margin: 0;
  }

  body {
    margin: 1.6cm;
    -webkit-print-color-adjust: exact !important;
    -moz-print-color-adjust: exact !important;
    -ms-print-color-adjust: exact !important;
  }
  button {
    display: none;
  }
}
.page {
  width: 100%;
  height: 100%;
  position: relative;
  background: #ffffff;
}
.title {
  color: #242424;
  font-weight: bold;
  line-height: 20px;
}
.second-title {
  color: #242424;
  line-height: 18px;
}
</style>
