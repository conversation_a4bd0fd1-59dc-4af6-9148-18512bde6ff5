<template>
  <div class="date-range">
    <customElSelect
      v-model="CetSelect_timetype.selectNode"
      placeholder="请选择"
      style="width: 200px"
      @change="CetSelect_timetype_dropItemId_out"
      class="fl mrJ1"
      :prefix_in="$T('分析周期')"
    >
      <el-option
        v-for="item in CetSelect_timetype.inputData_in"
        :key="item.id"
        :label="item.text"
        :value="item.id"
        :disabled="item.disabled"
      ></el-option>
    </customElSelect>
    <div
      class="fl mrJ1"
      style="display: inline-block"
      v-show="CetSelect_timetype.selectNode != 11"
    >
      <!-- 向前查询按钮 -->
      <div class="fl">
        <el-button
          type="display: inline-block;"
          v-show="CetButton_prv.visible_in"
          :disabled="CetButton_prv.disable_in"
          v-bind="CetButton_prv.config"
          @click="CetButton_prv_statusTrigger_out"
        ></el-button>
      </div>
      <el-date-picker
        class="fl mlJ mrJ"
        style="width: 150px"
        v-bind="CetDatePicker_time.config"
        v-model="CetDatePicker_time.val"
        placeholder="选择日期时间"
        :picker-options="pickerOptions"
        @change="CetDatePicker_time_queryTime_out"
      ></el-date-picker>
      <!-- 向后查询按钮 -->
      <div class="fl">
        <el-button
          type="display: inline-block;"
          v-show="CetButton_next.visible_in"
          :disabled="backToTimeBtnDis1"
          v-bind="CetButton_next.config"
          @click="CetButton_next_statusTrigger_out"
        ></el-button>
      </div>
    </div>
    <div
      class="fl mrJ1"
      style="display: inline-block"
      v-show="CetSelect_timetype.selectNode == 11"
    >
      <TimeRange
        style="width: 420px"
        :val.sync="TimeRange_1.queryTime"
        @change="TimeRange_1_change"
      ></TimeRange>
    </div>
  </div>
</template>
<script>
import common from "eem-utils/common";
import moment from "moment";
import TimeRange from "eem-components/TimeRange";
export default {
  name: "CustomDatePicker",
  components: {
    TimeRange
  },
  props: {
    //数据绑定配置
    dataConfig: {
      type: Object
    },
    val: {
      type: Object
    }
  },
  computed: {
    // 实际对标考核下一时段按钮禁点状态
    backToTimeBtnDis1() {
      var actTime = null; //控件时间
      var maxTime = null; //当前时间
      var cycle = this.CetSelect_timetype.selectNode;
      var time = this.CetDatePicker_time.val;

      if (cycle === 1) {
        actTime = moment(time).startOf("day").valueOf();
        maxTime = moment()
          .add(1, "d")
          .startOf("day")
          .subtract(0, "d")
          .valueOf();
      } else if (cycle === 3) {
        actTime = moment(time).startOf("month").valueOf();
        maxTime = moment().add(1, "M").startOf("month").valueOf();
      } else if (cycle === 5) {
        actTime = moment(time).startOf("year").valueOf();
        maxTime = moment().add(1, "y").startOf("year").valueOf();
      }

      if (this.dataConfig.dateLimitNext) {
        return this.dataConfig.dateLimitNext(actTime, maxTime);
      }
      return actTime >= maxTime;
    }
  },
  data(vm) {
    return {
      CetSelect_timetype: {
        dataConfig: {
          edition: "v1",
          queryUrl: "",
          type: "post",
          dropItemId: "id",
          dropItemText: "text",
          dropItemType: "type",
          modelLabel: "accountstatus"
        },
        dataMode: "static",
        inputData_in: [
          {
            id: 1,
            text: "日",
            type: "date"
          },
          {
            id: 3,
            text: "月",
            type: "month"
          },
          {
            id: 5,
            text: "年",
            type: "year"
          },
          {
            id: 11,
            text: "自定义",
            type: "date"
          }
        ],
        disable_in: false,
        visible_in: true,
        clearTrigger_in: new Date().getTime(),
        selectNode: 1,
        config: {
          initialFlag: false
        }
      },
      TimeRange_1: {
        queryTime: []
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        config: {
          title: "",
          size: "small",
          icon: "el-icon-arrow-left"
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        config: {
          title: "",
          size: "small",
          icon: "el-icon-arrow-right"
        }
      },
      CetDatePicker_time: {
        disable_in: false,
        val: this.$moment().add(0, "d").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "date",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          clearable: false,
          size: "small",
          style: {
            display: "inline-block"
          }
        }
      },
      pickerOptions: {
        disabledDate(time) {
          const cycle = vm.CetSelect_timetype.selectNode;
          if (cycle === 1) {
            return time.getTime() > moment().add(1, "d").valueOf();
          } else if (cycle === 3) {
            return time.getTime() > moment().add(1, "M").valueOf();
          } else if (cycle === 5) {
            return time.getTime() > moment().add(1, "y").valueOf();
          }
        }
      },
      timenum: {
        1: 12,
        3: 14,
        5: 17,
        11: 20
      },
      timenum1: {
        12: 1,
        14: 3,
        17: 5,
        20: 11
      },
      queryTime: {
        startTime: null,
        endTime: null,
        cycle: 12 //17年，14月，12日，20自定义
      }
    };
  },
  watch: {
    val: {
      deep: true,
      handler: function (val, oldVal) {
        if (!val) {
          return;
        }
        this.value = val;
      }
    },
    value: {
      deep: true,
      handler: function (val, oldVal) {
        this.changDate(val);
      }
    },
    dataConfig: {
      deep: true,

      handler: function (val, oldVal) {
        if (!val || !val.time || !val.cycle) {
          return;
        }

        this.CetSelect_timetype.selectNode = val.cycle;
        if (val.cycle === 1) {
          this.CetDatePicker_time.config.type = "date";
          this.CetDatePicker_time.val = common.dateTypeChange(
            val.time,
            val.cycle
          );
        } else if (val.cycle === 3) {
          this.CetDatePicker_time.config.type = "month";
          this.CetDatePicker_time.val = common.dateTypeChange(
            val.time,
            val.cycle
          );
        } else if (val.cycle === 5) {
          this.CetDatePicker_time.config.type = "year";
          this.CetDatePicker_time.val = common.dateTypeChange(
            val.time,
            val.cycle
          );
        } else if (val.cycle === 11) {
          this.TimeRange_1.queryTime = val.time;
          this.CetSelect_timetype.inputData_in = val.type;
          this.emitTime(11);
          return;
        }
        this.CetSelect_timetype.inputData_in = val.type;
        this.CetSelect_timetype_dropItemId_out(
          this.CetSelect_timetype.selectNode
        );
      }
    },
    queryTime: {
      deep: true,
      handler: function (val, oldVal) {
        this.$emit("change", val);
      }
    },
    "CetDatePicker_time.val": {
      deep: true,
      handler: function (val, oldVal) {
        const date = this.$moment(val);
        // this.queryTime = [date.startOf("d").valueOf(), date.endOf("d").valueOf() + 1];
        var type = this.CetSelect_timetype.selectNode;

        var value;
        if (type === 1) {
          value = {
            startTime: date.startOf("d").valueOf(),
            endTime: date.endOf("d").valueOf() + 1,
            cycle: this.timenum[this.CetSelect_timetype.selectNode]
          };
        } else if (type === 3) {
          value = {
            startTime: date.startOf("M").valueOf(),
            endTime: date.endOf("M").valueOf() + 1,
            cycle: this.timenum[this.CetSelect_timetype.selectNode]
          };
        } else if (type === 5) {
          value = {
            startTime: date.startOf("Y").valueOf(),
            endTime: date.endOf("Y").valueOf() + 1,
            cycle: this.timenum[this.CetSelect_timetype.selectNode]
          };
        }
        // this.$emit("change", value);
        this.queryTime = value;
      }
    }
  },
  methods: {
    CetSelect_timetype_dropItemId_out(val) {
      var time = this.CetDatePicker_time.val;
      if (new Date(time) > new Date()) {
        time = new Date().getTime();
      }
      if (val === 1) {
        this.CetDatePicker_time.config.type = "date";
        this.CetDatePicker_time.val = common.dateTypeChange(time, val);
      } else if (val === 3) {
        this.CetDatePicker_time.config.type = "month";
        this.CetDatePicker_time.val = common.dateTypeChange(time, val);
      } else if (val === 5) {
        this.CetDatePicker_time.config.type = "year";
        this.CetDatePicker_time.val = common.dateTypeChange(time, val);
      } else if (val === 11) {
        this.TimeRange_1.queryTime = common.initDateRange();
      }
      this.emitTime(val);
    },
    CetButton_prv_statusTrigger_out(val) {
      // let date = this.$moment(this.CetDatePicker_time.val);
      // this.CetDatePicker_time.val = date.subtract(1, "d")._d;
      var time = this.CetDatePicker_time.val;
      var type = this.CetSelect_timetype.selectNode;
      this.CetDatePicker_time.val = common.goToTime(time, type);
    },
    CetButton_next_statusTrigger_out(val) {
      // let date = this.$moment(this.CetDatePicker_time.val);
      // this.CetDatePicker_time.val = date.add(1, "d")._d;
      var time = this.CetDatePicker_time.val;
      var type = this.CetSelect_timetype.selectNode;
      this.CetDatePicker_time.val = common.backToTime(time, type);
    },
    CetDatePicker_time_queryTime_out(val) {},
    TimeRange_1_change(val) {
      if (this.CetSelect_timetype.selectNode != 11) {
        return;
      }

      if (val[0] === val[1]) {
        var starttime = new Date(val[0]).getTime();
        var endtime = new Date(val[0]).getTime() + 1000 * 3600 * 24;
        this.TimeRange_1.queryTime = [starttime, endtime];
        this.$message.warning("结束时间不能小于等于开始时间！");
        return;
      }
      var value = {
        startTime: val[0],
        endTime: val[1],
        cycle: this.timenum[this.CetSelect_timetype.selectNode]
      };
      // this.$emit("change", value);
      this.queryTime = value;
    },
    emitTime(type) {
      var time = this.CetDatePicker_time.val;
      const date = this.$moment(time);
      var value;
      if (type === 1) {
        value = {
          startTime: date.startOf("d").valueOf(),
          endTime: date.endOf("d").valueOf() + 1,
          cycle: this.timenum[type]
        };
      } else if (type === 3) {
        value = {
          startTime: date.startOf("M").valueOf(),
          endTime: date.endOf("M").valueOf() + 1,
          cycle: this.timenum[type]
        };
      } else if (type === 5) {
        value = {
          startTime: date.startOf("Y").valueOf(),
          endTime: date.endOf("Y").valueOf() + 1,
          cycle: this.timenum[type]
        };
      } else if (type === 11) {
        var val = this.TimeRange_1.queryTime;
        value = {
          startTime: val[0],
          endTime: val[1],
          cycle: this.timenum[type]
        };
      }
      // this.$emit("change", value);
      this.queryTime = value;
    }
  },
  created: function () {
    this.TimeRange_1.queryTime = common.initDateRange();
  },
  mounted() {
    this.CetSelect_timetype.inputData_in = this.dataConfig.type;
    this.CetSelect_timetype.selectNode = this.dataConfig.cycle;
    this.CetSelect_timetype_dropItemId_out(this.CetSelect_timetype.selectNode);
  }
};
</script>
<style lang="scss">
.date-range {
  display: flex;
  align-items: center;
  .date-range-label {
    display: block;
    // width: 86px;
    padding: 0 10px;
    text-align: center;
    line-height: 32px;
    height: 32px;
    box-sizing: border-box;
    border: 1px solid;
    border-right: 0px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    @include border_color(B1);
    @include background_color(BG1);
    @include font_color(T1);
  }
  .date-picker {
    flex: 1;
    border-top-left-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
  }
  .el-button {
    padding: 9px;
  }
  .basic-box .basic-box-label {
    height: 32px;
    line-height: 32px;
  }
}
</style>
