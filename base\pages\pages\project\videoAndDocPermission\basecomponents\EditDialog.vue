<template>
  <div>
    <CetDialog
      class="CetDialog"
      :title="$T('编辑文档权限')"
      v-bind="CetDialog_User"
      v-on="CetDialog_User.event"
    >
      <div class="eem-cont-c1 content">
        <el-row :gutter="20" class="fullfilled">
          <!-- <el-col :span="12" class="right-container">
          <el-container class="fullfilled">
            <el-header class="tree-header " height="32px">
              视频范围
              <div class="right">
                <input type="checkbox" v-model="checkAllVideo" @change="videoCheckAll" />
                全选
              </div>
            </el-header>
            <el-main class="padding0 ">
              <CetTree
                class="fullfilled"
                :selectNode.sync="CetTree_video.selectNode"
                :checkedNodes.sync="CetTree_video.checkedNodes"
                v-bind="CetTree_video"
                v-on="CetTree_video.event"
              ></CetTree>
            </el-main>
          </el-container>
        </el-col> -->
          <el-col :span="24" class="fullheight">
            <div class="fullfilled flex-column">
              <div class="clearfix tree-header mbJ3">
                {{ $T("文档范围") }}
                <div class="fr">
                  <el-checkbox v-model="checkAllDoc" @change="docCheckAll">
                    {{ $T("全选") }}
                  </el-checkbox>
                </div>
              </div>
              <CetTree
                class="flex-auto cetTree"
                :selectNode.sync="CetTree_knowledge.selectNode"
                :checkedNodes.sync="CetTree_knowledge.checkedNodes"
                v-bind="CetTree_knowledge"
                v-on="CetTree_knowledge.event"
                :searchText_in.sync="CetTree_knowledge.searchText_in"
              ></CetTree>
            </div>
          </el-col>
        </el-row>
      </div>
      <template slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </template>
    </CetDialog>
  </div>
</template>
<script>
import commonApi from "@/api/custom";
export default {
  name: "addOrEditUserDialog",
  components: {},
  props: {
    userData_in: {
      type: Object,
      default: null
    },
    currentUserId: {
      type: Number
    },
    openTrigger_in: {
      type: Number,
      default: new Date().getTime()
    },
    documentNodes_in: {
      type: Array
    }
  },
  data() {
    return {
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.save
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.close
        }
      },
      value1: "",
      value2: "",
      checkAllVideo: false, // 视频全选按钮
      checkAllDoc: false, // 知识库文档全选按钮状态
      selectVideoList: [], // 当前勾选的视频列表
      selectKnowledgeList: [], // 当前选中的知识库文档列表
      // 用户弹窗组件
      CetDialog_User: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      // 视频节点树组件
      CetTree_video: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: true,
        checkStrictly: false,
        event: {
          allCheckNodes_out: this.CetTree_video_allCheckNodes_out
        }
      },

      // 文档节点树组件
      CetTree_knowledge: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: true,
        checkStrictly: false,
        event: {
          allCheckNodes_out: this.CetTree_knowledge_allCheckNodes_out
        }
      }
    };
  },
  computed: {
    projectTenantId() {
      var vm = this;
      return vm.$store.state.projectTenantId;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.localStorage) {
          return false;
        } else {
          var storage = window.localStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },
  watch: {
    openTrigger_in() {
      let params = {
        userId: this.currentUserId,
        tenantId: this.projectTenantId,
        projectId: this.projectId
      };
      commonApi.getVideoPermission(params).then(res => {
        if (res.code == 0) {
          this.CetTree_knowledge.inputData_in = res.data;
          let checkedNodes = [];
          if (this.documentNodes_in && this.documentNodes_in.length) {
            checkedNodes = this.documentNodes_in.map(i => {
              return {
                id: i.id
              };
            });
          }
          this.$nextTick(() => {
            this.CetTree_knowledge.checkedNodes = checkedNodes;
          });

          //   let defaultVideoList = [];
          //   let defaultKnowledgeList = [];
          //   let videoNodes = res.data.videoNodes || [];
          //   let knowledgeNodes = res.data.knowledgeNodes || [];
          //   videoNodes.forEach(item => {
          //     if (item.selected) {
          //       defaultVideoList.push({
          //         id: item.id
          //       });
          //     }
          //   });
          //   knowledgeNodes.forEach(item => {
          //     if (item.selected) {
          //       defaultKnowledgeList.push({
          //         id: item.id
          //       });
          //     }
          //   });
          //   this.CetTree_video.inputData_in = videoNodes;
          //   this.CetTree_video.checkedNodes = defaultVideoList;
          //   this.CetTree_knowledge.inputData_in = knowledgeNodes;
          //   this.CetTree_knowledge.checkedNodes = defaultKnowledgeList;
        }
      });
      this.CetDialog_User.openTrigger_in = new Date().getTime();
    }
  },
  methods: {
    // 视频权限树输出
    CetTree_video_allCheckNodes_out(val) {
      const list = val || [];
      if (
        val.length === this.CetTree_video.inputData_in.length &&
        val.length > 0
      ) {
        this.checkAllVideo = true;
      } else {
        if (!this.CetTree_video.searchText_in) {
          this.checkAllVideo = false;
        }
      }
      this.selectVideoList = list;
    },
    // 知识库文档权限树输出
    CetTree_knowledge_allCheckNodes_out(val) {
      const list = val || [];
      if (
        val.length === this.CetTree_knowledge.inputData_in.length &&
        val.length > 0
      ) {
        this.checkAllDoc = true;
      } else {
        if (!this.CetTree_knowledge.searchText_in) {
          this.checkAllDoc = false;
        }
      }
      this.selectKnowledgeList = list;
    },
    // 点击确定按钮
    save() {
      const selectNodes = this.selectVideoList.concat(this.selectKnowledgeList);
      const modelNodes = [];
      selectNodes.forEach(item => {
        modelNodes.push({
          authIds: [],
          childSelectState: 1,
          disabled: false,
          id: item.id,
          modelLabel: item.modelLabel,
          rule: ""
        });
      });
      let params = {
        userId: this.userData_in.id,
        projectId: this.projectId
      };
      commonApi
        .saveVideoPermissionAuthModelnodes({
          modelNodes,
          params
        })
        .then(response => {
          if (response.code == 0) {
            this.$message({ message: $T("操作成功"), type: "success" });
            this.$emit("save", this.userData_in.id);
            this.close();
          }
        });
    },
    // 关闭弹窗
    close() {
      this.CetTree_knowledge.searchText_in = "11";
      this.CetTree_video.searchText_in = "11";
      this.$nextTick(() => {
        this.CetTree_knowledge.searchText_in = "";
        this.CetTree_video.searchText_in = "";
      });
      this.CetDialog_User.closeTrigger_in = new Date().getTime();
    },
    // 点击视频全选按钮
    videoCheckAll() {
      //debugger;
      if (this.checkAllVideo) {
        this.fliter1();
      } else {
        this.CetTree_video.checkedNodes = [];
      }
    },
    // 点击文档全选按钮
    docCheckAll() {
      if (this.checkAllDoc) {
        this.fliter2();
      } else {
        this.CetTree_knowledge.checkedNodes = [];
      }
    },
    fliter1() {
      const list = [];
      this.CetTree_video.inputData_in.forEach(item => {
        if (item.name.indexOf(this.CetTree_video.searchText_in) > -1) {
          list.push({
            id: item.id
          });
        }
      });
      this.CetTree_video.checkedNodes = list;
    },
    fliter2() {
      const list = [];
      this.CetTree_knowledge.inputData_in.forEach(item => {
        if (item.name.indexOf(this.CetTree_knowledge.searchText_in) > -1) {
          list.push({
            id: item.id
          });
        }
      });
      this.CetTree_knowledge.checkedNodes = list;
    }
  },
  created() {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
    box-sizing: border-box;
  }
  .content {
    height: 500px;
  }
  .tree-header {
    @include line_height(Hm);
  }
}
</style>
