import fetch from "eem-utils/fetch";
import _ from "lodash";
const version = "v1";

function processRequestByInterFace(data) {
  // 对 data 进行任意转换处理
  const conditions = _.get(data, "rootCondition.filter.expressions", []);
  const page = _.get(data, "rootCondition.page");
  const result = {};
  conditions.forEach(item => {
    result[item.prop] = item.limit;
  });
  result.page = page;
  return result;
}

function processResponse(response) {
  return response;
}

// 查询维保项目组
export function queryMaintenanceGroup() {
  return fetch({
    url: `/eem-service/${version}/maintenance`,
    method: "GET"
  });
}

// 新增维保项目组
export function addMaintenanceGroup(data) {
  return fetch({
    url: `/eem-service/${version}/maintenance/group`,
    method: "POST",
    data
  });
}

// 编辑维保项目组
export function editMaintenanceGroup(data) {
  return fetch({
    url: `/eem-service/${version}/maintenance/group`,
    method: "PATCH",
    data
  });
}

// 删除维保项目组
export function deleteMaintenanceGroup(data) {
  return fetch({
    url: `/eem-service/${version}/maintenance/group`,
    method: "DELETE",
    data
  });
}

// 查询维保项目
export function queryMaintenanceItem(id) {
  return fetch({
    url: `/eem-service/${version}/maintenance/item?maintenanceGroupId=${id}`,
    method: "GET"
  });
}

// 查询维保计划中的维保项目
export function queryMaintenanceByNodeInfo(data) {
  return fetch({
    url: `/eem-service/${version}/maintenance/itemByNodeInfo`,
    method: "POST",
    data
  });
}

// 新增维保项目
export function addMaintenanceItem(data) {
  return fetch({
    url: `/eem-service/${version}/maintenance/item`,
    method: "POST",
    data
  });
}

// 编辑维保项目
export function editMaintenanceItem(data) {
  return fetch({
    url: `/eem-service/${version}/maintenance/item`,
    method: "PATCH",
    data
  });
}

// 删除维保项目
export function deleteMaintenanceItem(data) {
  return fetch({
    url: `/eem-service/${version}/maintenance/item`,
    method: "DELETE",
    data
  });
}

// 维保项目排序
export function sortMaintenanceItem(data) {
  return fetch({
    url: `/eem-service/${version}/maintenance/item/sort`,
    method: "PATCH",
    data
  });
}

// 查询维保项目的维保方式
export function queryMaintenanceType() {
  return fetch({
    url: `/eem-service/${version}/maintenance/type`,
    method: "GET"
  });
}

// 新增维保方式
export function addMaintenanceType(data) {
  return fetch({
    url: `/eem-service/${version}/maintenance/type`,
    method: "POST",
    data
  });
}

// 编辑维保方式
export function editMaintenanceType(data) {
  return fetch({
    url: `/eem-service/${version}/maintenance/type`,
    method: "PATCH",
    data
  });
}

// 删除维保方式
export function deleteMaintenanceType(data) {
  return fetch({
    url: `/eem-service/${version}/maintenance/type`,
    method: "DELETE",
    data
  });
}

// 查询维保计划
export function queryMaintenancePlan(data) {
  return fetch({
    url: `/eem-service/${version}/maintenance/plan/query`,
    method: "POST",
    data: processRequestByInterFace(data)
  });
}

// 新增维保计划
export function addMaintenancePlan(data) {
  return fetch({
    url: `/eem-service/${version}/maintenance/plan`,
    method: "POST",
    data
  });
}

// 编辑维保计划
export function editMaintenancePlan(data) {
  return fetch({
    url: `/eem-service/${version}/maintenance/plan`,
    method: "PATCH",
    data
  });
}

//批量禁用维保计划
export function setMaintenancePlanDisable(data) {
  return fetch({
    url: `/eem-service/${version}/maintenance/plan/disable`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data
  });
}

//批量启用维保计划
export function setMaintenancePlanEnable(data) {
  return fetch({
    url: `/eem-service/${version}/maintenance/plan/enable`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data
  });
}

// 查询维保计划工单信息
export function querytMaintenancePlanOrder(id) {
  return fetch({
    url: `/eem-service/${version}/maintenance/plan/order?workSheetId=${id}`,
    method: "GET"
  });
}

// 查询维保计划中的维保项目
export function queryMaintenancePlanItem(id) {
  return fetch({
    url: `/eem-service/${version}/maintenance/plan/item?planSheetId=${id}`,
    method: "GET"
  });
}

// 查询维保工单
export function queryMaintenanceOrder(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/maintenance/query`,
    method: "POST",
    data: processRequestByInterFace(data)
  });
}

// 查询是否有维保工单相关权限
export function checkWorkorderAuth(code) {
  return fetch({
    url: `/eem-service/${version}/workorder/checkAuth?code=${code}`,
    method: "POST"
  });
}

// 查询工单统计信息
export function queryMaintenanceCount(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/maintenance/count`,
    method: "POST",
    data
  });
}

//创建或者修改维保工单
export function createMaintenanceWorkOrder(data) {
  delete data.deviceListName;
  delete data.modelLabel;
  delete data.project;
  delete data.timeconsume;
  delete data.nodelabel;
  delete data.model;
  return fetch({
    url: `/eem-service/${version}/workorder/maintenance/create/batch`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data
  });
}

// 查询暂存审核信息
export function getMaintenceOrderCheckStash(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/maintenance/workOrder/check/stash/${data.code}`,
    method: "GET",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

//暂存审核信息
export function toMaintenanceOrderCheckStash(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/maintenance/workOrder/check/stash`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data
  });
}

//批量审核工单
export function toMaintenanceOrderCheckBatch(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/maintenance/workOrder/check/batch`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data
  });
}

// 查询班组成员
export function queryTeamUser(groupId) {
  return fetch({
    url: `/eem-service/${version}/inspector/inspectorUser?groupId=${groupId}`,
    method: "GET"
  });
}

// 保存维保工单信息
export function saveMaintenanceOrder(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/maintenance/save`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data
  });
}

// 提交维保工单信息
export function submitMaintenanceOrder(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/maintenance/submit`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data
  });
}

// 查工单详情
export function queryDetailByCode(id) {
  return fetch({
    url: `/eem-service/${version}/workorder/maintenance/detailByCode?code=${id}`,
    method: "GET",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}
