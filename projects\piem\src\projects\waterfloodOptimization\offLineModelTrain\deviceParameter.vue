<template>
  <CetDialog class="CetDialog" v-bind="CetDialog_1">
    <div class="eem-cont-c1 h-[672px]">
      <el-radio-group class="mlJ1" v-model="activeName">
        <el-radio-button label="chart">图表</el-radio-button>
        <el-radio-button label="data">数据</el-radio-button>
      </el-radio-group>
      <div class="h-[calc(100%-32px-16px)] w-full mt-4">
        <CetChart
          v-show="activeName === 'chart'"
          v-bind="CetChart_pic"
        ></CetChart>
        <div class="w-full h-full" v-if="activeName === 'data'">
          <div class="w-full h-[32px] mb-[16px]">
            <customElSelect
              v-model="selectParams"
              size="small"
              :style="{ width: '272px' }"
              :prefix_in="$T('选择参数')"
              @change="onChange"
            >
              <el-option
                v-for="item in selectParamsList"
                :key="item.id"
                :label="item.text"
                :value="item.id"
              ></el-option>
            </customElSelect>
          </div>
          <div
            class="w-full h-[calc(100%-32px-16px)] grid gap-[16px]"
            :class="[isDouble ? 'grid-cols-2' : 'grid-cols-1']"
          >
            <el-table
              v-for="(it, ix) in tableDatas"
              :key="ix"
              height="100%"
              :data="it"
              :highlight-current-row="true"
            >
              <el-table-column
                v-for="(item, index) in tableCol"
                :key="index"
                v-bind="item"
              />
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_close"
        v-on="CetButton_close.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>

<script>
export default {
  name: "zoomDataDialog",
  props: {
    openTrigger_in: Number,
    inputData_in: Object
  },
  data() {
    return {
      activeName: "chart",
      CetDialog_1: {
        width: "1080px",

        title: "注水站参数相关性",
        "show-close": true,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime()
      },
      CetButton_close: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_close_statusTrigger_out
        }
      },
      CetChart_pic: {
        inputData_in: null,
        options: {}
      },
      selectParams: null,
      selectParamsList: [],
      tableCol: []
    };
  },
  computed: {
    isDouble() {
      return this.tableDatas?.length === 2;
    }
  },
  watch: {
    async openTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;

      this.$nextTick(() => {
        this.tableCol = this.inputData_in.tableCol;
        this.selectParamsList = this.inputData_in.selectParamsList;
        this.selectParams = this.selectParamsList[0]?.id;
        this.getTableData(this.inputData_in?.resData);
        this.CetChart_pic.options = {
          ...this.inputData_in.chartData,
          visualMap: {
            ...this.inputData_in.chartData.visualMap,
            itemHeight: 475,
            right: "0%"
          }
        };
      });
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    onChange() {
      this.getTableData(this.inputData_in?.resData);
    },
    getTableData(data) {
      let newData = data[this.selectParams];
      const arr = newData?.length > 12 ? [[], []] : [[]];
      this.tableDatas = _.isArray(data)
        ? arr?.map((it, ix) =>
            ix === 0 ? newData?.slice(0, 12) : newData?.slice(12)
          )
        : [];
    },
    CetButton_close_statusTrigger_out() {
      this.$emit("updateLabel");
      this.CetDialog_1.closeTrigger_in = +new Date();
    }
  }
};
</script>

<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog) {
    margin-top: 7vh !important;
  }
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
  }
  :deep(.titleCol) {
    @include background_color(BG2);
  }
}
</style>
