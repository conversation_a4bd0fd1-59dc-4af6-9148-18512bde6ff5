<template>
  <div class="fullheight region-dialog">
    <!-- <p class="fsH1 highlight lh40 m0" style="padding: 10px 0">ITIC曲线</p> -->
    <el-row :gutter="10" style="padding-bottom: 10px; height: 25px">
      <el-col :span="18">{{ eventLog.pecName }}</el-col>
      <el-col :span="6">
        <div class="fr" style="padding-right: 10px">
          时间：{{ formatDate(null, null, eventLog.eventtime) }}
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20" style="height: 650px; overflow: auto">
      <template>
        <div style="height: 60%">
          <div class="text-right" style="position: absolute; right: 20px">
            <span>
              <i class="square" style="background: #008001" />
              A区
            </span>
            <!-- <span><i class="square" style="background: #008001" /> A区</span> -->
            <span class="ml20">
              <i
                class="el-icon-caret-right"
                style="
                  color: #eb8308;
                  font-size: 30px;
                  line-height: 0;
                  position: relative;
                  top: 5px;
                "
              />
              B区
            </span>
            <span class="ml20">
              <i
                class="el-icon-caret-right"
                style="
                  color: #d32a46;
                  font-size: 30px;
                  line-height: 0;
                  position: relative;
                  top: 5px;
                "
              />
              C区
            </span>
          </div>
          <CetChart
            style="height: calc(100% - 20px)"
            :inputData_in="CetChart2_ITIC.inputData_in"
            v-bind="CetChart2_ITIC.config"
          ></CetChart>
        </div>
      </template>
      <template>
        <el-table
          stripe
          :data="tableData"
          border
          tooltip-effect="light"
          style="width: 100%"
          cell-class-name="nowhite-space"
          :max-height="500"
        >
          <el-table-column type="expand">
            <template slot-scope="props">
              <div class="fullheight pJ2 bg">
                <table class="table-terse" cellspacing="0" cellpadding="0">
                  <tbody>
                    <tr>
                      <td width="150" class="bg-f0">额定电压(V)：</td>
                      <td width="150">{{ props.row.nominalvoltage }}</td>
                      <td width="150" class="bg-f0">总幅值(%)：</td>
                      <td width="150">{{ props.row.magnitude }}</td>
                      <td width="150" class="bg-f0">持续时间(ms)：</td>
                      <td width="150">
                        {{ (props.row.duration / 1000) | formatNum }}
                      </td>
                      <!-- <td width="150">{{props.row.duration | formatNum }}</td> -->
                    </tr>
                    <tr>
                      <td class="bg-f0">A相电压幅值：</td>
                      <td>{{ props.row.v1magnitude | formatNum(2) }}</td>
                      <td class="bg-f0">B相电压幅值：</td>
                      <td>{{ props.row.v2magnitude | formatNum(2) }}</td>
                      <td class="bg-f0">C相电压幅值：</td>
                      <td>{{ props.row.v3magnitude | formatNum(2) }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            label="时间"
            prop="eventtime"
            :formatter="formatDate"
            width="200"
          ></el-table-column>
          <el-table-column
            align="left"
            label="事件类型"
            prop="pqvariationeventtype$text"
            width="110"
          ></el-table-column>
          <el-table-column
            align="left"
            label="故障方向"
            width="110"
            prop="transientfaultdirection$text"
          ></el-table-column>
          <el-table-column
            align="left"
            width="110"
            label="持续时间(ms)"
            prop="duration"
          >
            <template slot-scope="scope">
              {{ (scope.row.duration / 1000) | formatNum }}
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            width="110"
            label="电压幅值(%)"
            prop="magnitude"
          ></el-table-column>
          <el-table-column
            align="left"
            label="区域"
            prop="toleranceband$text"
            width="110"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            align="left"
            width="110"
            label="节点"
            prop="monitoredName"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            align="left"
            label="描述"
            prop="description"
            show-overflow-tooltip
          ></el-table-column>
        </el-table>
      </template>
    </el-row>
    <!-- <el-row :gutter="20">
      <el-col :span="24">

      </el-col>
    </el-row> -->
    <!-- <div style="position: absolute; height: 3%; right: 20px; bottom: 3%">
      <el-button size="small" class="is-plain" type="primary" @click="close">
        关 闭
      </el-button>
    </div> -->
  </div>
</template>
<script>
import common from "eem-utils/common";
import { COMMON } from "@/store/constQuantity";
export default {
  name: "ToleranceWidget",
  props: {
    eventLog: {
      type: [Object],
      default: () => ({})
    },
    ENUM: {
      type: [Object],
      default: () => {}
    }
  },
  computed: {
    tableData() {
      this.setChartData([this.eventLog]);
      return this.formatTableData([this.eventLog]);
    }
  },
  data(vm) {
    return {
      // ITIC组件
      CetChart2_ITIC: {
        //组件输入项
        inputData_in: null,
        config: {
          options: {
            // color: ["yellow", "green", "red"],
            title: {
              show: false,
              text: "",
              x: "center",
              textStyle: {
                fontSize: 16,
                fontWeight: "normal",
                color: "#999"
              }
            },
            graphic: [
              {
                type: "text",
                z: 100,
                left: "12%",
                top: "middle",
                style: {
                  fill: "#DFE5F1",
                  text: "A区-容忍区\n(设备可接受电压范围)",
                  font: "bolder 14px Microsoft YaHei"
                }
              },
              {
                type: "text",
                z: 100,
                left: "60%",
                top: "40%",
                style: {
                  fill: "#DFE5F1",
                  text: "B区-设备损坏区\n(一旦到达此区域，设备损坏)",
                  font: "bolder 14px Microsoft YaHei"
                }
              },
              {
                type: "text",
                z: 100,
                left: "68%",
                bottom: "8.5%",
                style: {
                  fill: "#DFE5F1",
                  text: "C区-设备无损坏区\n(设备功能不能正常发挥，但不至于对设备自身造成损坏)",
                  font: "bolder 14px Microsoft YaHei"
                }
              }
            ],
            grid: {
              show: "true",
              backgroundColor: "#677399",
              top: 35,
              right: 30,
              bottom: 35,
              left: 43
            },
            legend: {
              x: "center",
              y: "top"
            },
            tooltip: {
              // trigger: "axis",
              formatter(params) {
                // console.log(params);

                const info = params.data[2];
                let txt = "";
                txt += "节点：" + info.monitoredName + "<br />";
                txt +=
                  "时间：" +
                  vm.$moment(info.eventtime).format("YYYY-MM-DD HH:mm:ss.SSS") +
                  "<br />";

                txt +=
                  "事件类型：" +
                  vm.formatEnum(info.pqvariationeventtype) +
                  "<br />";
                txt += "电压幅值（%）：" + params.data[1] + "<br />";
                txt += "持续时间（s）：" + params.data[0] + "<br />";
                return txt;
              }
            },

            xAxis: {
              type: "log",
              min: 0.000001,
              interval: 10,
              max: 100,
              splitLine: {
                show: false
              },
              axisLabel: {
                formatter: (val, i) => {
                  // console.log(val, i);
                  if (i == 0 || i == 1 || i == 2) {
                    return val * 1000000 + "μs";
                  } else if (i == 5 || i == 3 || i == 4) {
                    return val * 1000 + "ms";
                  } else {
                    return val + "s";
                  }
                }
              }
            },
            yAxis: {
              max: 500,
              name: "电压幅值(%)",
              interval: 50
            },
            series: [
              {
                name: "A区",
                data: [],
                type: "scatter",
                symbol: "rect",
                markLine: {
                  symbol: "none",
                  silent: true,
                  data: [
                    {
                      yAxis: 100
                    }
                  ],
                  lineStyle: {
                    color: "red",
                    type: "solid",
                    width: 2.5
                  }
                },
                symbolSize: 12,
                zlevel: 10,
                itemStyle: {
                  color: "#008001"
                }
              },
              {
                name: "B区",
                data: [],
                type: "scatter",
                symbol: "triangle",
                symbolSize: 12,
                zlevel: 10,
                itemStyle: {
                  color: "#EB8308"
                }
              },
              {
                name: "C区",
                data: [],
                type: "scatter",
                symbol: "triangle",
                symbolRotate: 180,
                symbolSize: 12,
                zlevel: 10,
                itemStyle: {
                  color: "#D32A46"
                }
              },
              {
                data: [
                  [Math.pow(10, -0.398) / 1000, 500],
                  [Math.pow(10, 0) / 1000, 200],
                  [Math.pow(10, 0.477) / 1000, 140],
                  [Math.pow(10, 0.477) / 1000, 120],
                  [Math.pow(10, 1.3) / 1000, 120],
                  [Math.pow(10, 2.699) / 1000, 120],
                  [Math.pow(10, 2.699) / 1000, 110],
                  [Math.pow(10, 4) / 1000, 110],
                  [Math.pow(10, 5) / 1000, 110]
                ],
                type: "line",
                symbol: "none",
                areaStyle: {
                  color: "#737EB4",
                  origin: "end"
                },
                lineStyle: {
                  color: "#2096ED"
                }
              },
              {
                data: [
                  [Math.pow(10, 1) / 1000, 0],
                  [Math.pow(10, 1) / 1000, 70],
                  [Math.pow(10, 2.699) / 1000, 70],
                  [Math.pow(10, 2.699) / 1000, 80],
                  [Math.pow(10, 4) / 1000, 80],
                  [Math.pow(10, 4) / 1000, 90],
                  [Math.pow(10, 5) / 1000, 90]
                ],
                type: "line",
                symbol: "none",
                areaStyle: {
                  color: "#9D788A"
                },
                lineStyle: {
                  color: "#2096ED"
                }
              }
            ]
          }
        }
      }
    };
  },
  methods: {
    close() {
      this.$emit("closeToleranceWindow", null);
    },
    formatDate(row, column, cellValue, index) {
      return common.formatDate(cellValue, "YYYY-MM-DD HH:mm:ss.SSS");
    },
    // 将数据分类 放到ITIC图表中
    setChartData(data) {
      const A = [];
      const B = [];
      const C = [];
      data.forEach(item => {
        if (item.toleranceband === 2)
          A.push([item.duration / 1000 / 1000, item.magnitude, item]);
        else if (item.toleranceband === 3)
          B.push([item.duration / 1000 / 1000, item.magnitude, item]);
        else if (item.toleranceband === 4)
          C.push([item.duration / 1000 / 1000, item.magnitude, item]);
      });
      const options = this._.cloneDeep(this.CetChart2_ITIC.config.options);
      options.series[0].data = A;
      options.series[1].data = B;
      options.series[2].data = C;
      this.CetChart2_ITIC.config.options = options;
    },
    // 格式化部分枚举类型
    formatTableData(data) {
      return data.map(item => {
        const format = {
          pqvariationeventtype$text: this._.find(
            this.ENUM[COMMON.PQ_VARIATION_EVENTTYPE],
            ["id", item.pqvariationeventtype]
          ).text,
          transientfaultdirection$text: this._.find(
            this.ENUM[COMMON.TRANSIENT_FALUT_DIRECTION],
            ["id", item.transientfaultdirection]
          ).text,
          toleranceband$text: this._.find(this.ENUM[COMMON.TOLERANCE_BAND], [
            "id",
            item.toleranceband
          ]).text,
          confirmeventstatus$text: this._.find(
            this.ENUM[COMMON.CONFIRM_EVENT_STATUS],
            ["id", item.confirmeventstatus]
          ).text
        };
        return Object.assign(item, format);
      });
    },
    formatEnum(value) {
      const text = this._.find(this.ENUM[COMMON.PQ_VARIATION_EVENTTYPE], [
        "id",
        value
      ]).text;
      if (text) return text;

      return "--";
    },

    loadEnumrations(...args) {
      this.ENUM = {};
      args.forEach(item => {
        this.ENUM[item] = this.$store.state.enumerations[item];
      });
      this.eventTypeGroup.enum = this.ENUM.pqvariationeventtype;
      this.tolerancebandGroup.enum = this.ENUM.toleranceband;
    }
  }
};
</script>
<style lang="scss" scope>
.square {
  height: 18px;
  width: 18px;
  display: inline-block;
  border-radius: 5px;
  line-height: 20px;
  font-size: 0;
  position: relative;
  top: 4px;
  margin-right: 10px;
}
.region-dialog {
  height: 800px;
}
</style>
<style lang="scss">
.region-dialog {
  .table-terse tr td {
    text-align: left;
    padding-left: 8px;
    padding-right: 8px;
    border: 1px solid !important;
    @include border_color(B1);
  }
}
</style>
