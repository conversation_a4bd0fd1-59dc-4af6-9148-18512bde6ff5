<template>
  <div class="pagebox flex-auto">
    <el-container class="contentBox">
      <el-aside width="316px" class="aside bg1 brC3">
        <span class="treetitle">管理层级</span>
        <CetTree
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          :searchText_in.sync="CetTree_1.searchText_in"
          v-bind="CetTree_1"
          default-expand-all
          v-on="CetTree_1.event"
          @node-click="treeclick"
        ></CetTree>
      </el-aside>
      <el-main
        style="height: 100%; padding: 0px"
        class="bgcolor mlJ2 flex-cloumn"
      >
        <div style="height: 98px" class="countbox">
          <div class="borderbottom" v-if="checkdata.modelLabel === 'poweruser'">
            <p>无效响应存在次数占比</p>
            <h3>
              {{
                countlist.percentageInvalidTimes ||
                countlist.percentageInvalidTimes === 0
                  ? (countlist.percentageInvalidTimes * 100).toFixed(2) + "%"
                  : "--"
              }}
            </h3>
          </div>
          <div
            class="borderbottom"
            v-if="checkdata.modelLabel === 'demandgroup'"
          >
            <p>总有效响应率</p>
            <h3>
              {{ formatNum() }}
            </h3>
          </div>
          <div class="borderbottom">
            <p>响应总次数（次）</p>
            <h3>
              {{
                countlist.totalTimes || countlist.totalTimes === 0
                  ? countlist.totalTimes
                  : "--"
              }}
            </h3>
          </div>
          <div class="borderbottom" v-if="checkdata.modelLabel === 'poweruser'">
            <p>本年无效响应存在次数占比</p>
            <h3>
              {{
                countlist.yearPercentageInvalidTimes ||
                countlist.yearPercentageInvalidTimes === 0
                  ? (countlist.yearPercentageInvalidTimes * 100).toFixed(2) +
                    "%"
                  : "--"
              }}
            </h3>
          </div>
          <div
            class="borderbottom"
            v-if="checkdata.modelLabel === 'demandgroup'"
          >
            <p>有效响应总次数（次）</p>
            <h3>
              {{
                countlist.percentageTimes || countlist.percentageTimes === 0
                  ? countlist.percentageTimes
                  : "--"
              }}
            </h3>
          </div>
          <div
            class="borderbottom"
            v-if="checkdata.modelLabel === 'demandgroup'"
          >
            <p>本年有效响应率</p>
            <h3>
              {{ yearFormatNum() }}
            </h3>
          </div>
          <div class="borderbottom">
            <p>本年响应次数（次）</p>
            <h3>
              {{
                countlist.yearTotalTimes || countlist.yearTotalTimes === 0
                  ? countlist.yearTotalTimes
                  : "--"
              }}
            </h3>
          </div>
        </div>
        <div class="hearderbox clearfix">
          <ElInput
            class="searchInput fl marginRightJ1"
            v-model="ElInput_search.value"
            v-bind="ElInput_search"
            v-on="ElInput_search.event"
            placeholder="请输入关键字搜索"
            @change="getDetailData"
          >
            <i slot="suffix" class="el-icon-search"></i>
          </ElInput>

          <customElSelect
            class="customElSelect fl marginRightJ1"
            v-model="ElSelect_2.value"
            v-bind="ElSelect_2"
            v-on="ElSelect_2.event"
            prefix_in="响应结果"
            placeholder="请筛选"
            clearable
            @change="getDetailData"
            v-if="checkdata.modelLabel === 'demandgroup'"
          >
            <ElOption
              v-for="item in ElOption_2.options_in"
              :key="item[ElOption_2.key]"
              :label="item[ElOption_2.label]"
              :value="item[ElOption_2.value]"
              :disabled="item[ElOption_2.disabled]"
            ></ElOption>
          </customElSelect>

          <div class="basic-box fl">
            <span class="basic-box-label">开始时段</span>
            <div class="box-col searchBox">
              <el-date-picker
                class="datePicker fl szwDatePickertime"
                style="width: 400px"
                v-model="CetDatePicker_1.val"
                size="small"
                value-format="timestamp"
                type="datetimerange"
                align="left"
                unlink-panels
                :clearable="false"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                @change="getDetailData"
              ></el-date-picker>
            </div>
          </div>
        </div>
        <div class="tableTop clearfix mtJ1">
          <div class="fl marginLeftJ2">
            共
            <span class="sum">{{ totalCount }}</span>
            条
          </div>
        </div>
        <div class="tableBody">
          <CetTable
            ref="CetTable"
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
          >
            <template v-for="item in Columns_1">
              <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
            </template>
            <ElTableColumn
              label="响应结果"
              header-align="center"
              align="center"
              v-if="checkdata.modelLabel === 'demandgroup'"
            >
              <template slot-scope="scope">
                <span style="color: #08c673" v-if="scope.row.validstate === 1">
                  有效
                </span>
                <span
                  style="color: #f65d68"
                  v-else-if="scope.row.validstate === 2"
                >
                  无效
                </span>
                <span v-else>--</span>
              </template>
            </ElTableColumn>
            <ElTableColumn
              label="操作"
              header-align="center"
              align="center"
              width="80"
            >
              <template slot-scope="scope">
                <span
                  @click="Todetail(scope.row)"
                  class="handel highLight colorZS"
                >
                  负荷监测
                </span>
              </template>
            </ElTableColumn>
          </CetTable>
        </div>
        <div class="tableFooter">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-sizes="pageSizes"
            :page-size.sync="pageSize"
            layout=" sizes, prev, pager, next, jumper"
            :total="totalCount"
          ></el-pagination>
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import moment from "moment";
export default {
  //       filters: {
  //   formatNum(value,val) {
  //        console.log(val)

  //     if (!this.countlist.totalTimes) return '--'
  //       if(this.countlist.percentageTimes || this.countlist.percentageTimes === 0){
  // return (this.countlist.percentageTimes / this.countlist.totalTimes) * 100 +
  //                       "%"

  //       }

  //   },
  //     yearFormatNum(value,val) {
  //       console.log(val)
  //     if (!this.countlist.yearTotalTimes) return '--'
  //       if(this.countlist.yearTotalInvalidTimes || this.countlist.yearTotalInvalidTimes=== 0){
  // return (this.countlist.yearTotalInvalidTimes/ this.countlist.yearTotalTimes) * 100 +
  //                       "%"

  //       }

  //   }
  // },
  computed: {},
  data() {
    return {
      checkdata: { modelLabel: "poweruser" },
      totalCount: 0,
      currentPage: 1,
      pageSizes: [10, 20, 50, 100],
      pageSize: 10,
      pageTotal: 0,
      isborderbottom: false,
      countlist: {},
      activeName: "负荷聚合商",
      sum: 0,
      showDetail: false,
      nodelevel: 1,
      // 1树组件
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        nodeKey: "tree_id",
        props: {
          label: function (data, node) {
            let title = data.name ? data.name : "";
            let num = data.accountno ? data.accountno : " ";
            let meterno = data.meterno ? data.meterno : " ";
            if (data.modelLabel === "demandgroup") {
              return title + num;
            } else if (data.modelLabel === "demandaccount") {
              return title + meterno;
            } else {
              return data.name;
            }
          },

          children: "children"
        },
        highlightCurrent: true,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      ElInput_search: {
        value: "",

        style: {},
        event: {}
      },

      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        "highlight-current-row": false,
        paginationCfg: {
          pageSize: 10,
          textAlign: "center",
          layout: "sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          "selection-change": this.handleSelectionChange
        }
      },
      Columns_1: [],
      Columns_inletWire: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          width: "50" //绝对宽度
        },
        {
          prop: "name", // 支持path a[0].b
          label: "计划名称", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,

          formatter: val => (val.name || val.name == 0 ? val.name : "--")
        },
        {
          prop: "starttime", // 支持path a[0].b
          label: "响应时间", //列名
          headerAlign: "left",
          align: "left",
          width: "280",
          showOverflowTooltip: true,

          formatter: function (val) {
            return val.starttime
              ? moment(val.starttime).format("YYYY/MM/DD HH:mm:ss") +
                  "至" +
                  moment(val.endtime).format("YYYY/MM/DD HH:mm:ss")
              : "--";
          }
        },
        {
          prop: "planvalue", // 支持path a[0].b
          label: "申报响应量（MW）", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "220",
          formatter: val =>
            val.planvalue || val.planvalue == 0
              ? val.planvalue.toFixed(4)
              : "--"
        },
        {
          prop: "calcvalue", // 支持path a[0].b
          label: "实际响应量（MW）", //列名
          headerAlign: "left",
          align: "left",
          width: "250",
          showOverflowTooltip: true,
          formatter: val =>
            val.calcvalue || val.calcvalue == 0
              ? val.calcvalue.toFixed(4)
              : "--"
        }
      ],
      Columns_resource: [],

      ElSelect_2: {
        value: "",
        style: {
          width: "200px"
        },
        event: {}
      },
      ElOption_2: {
        options_in: this.$store.state.enumerations.responsevalidstate || [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetDatePicker_1: {
        disable_in: false,
        val: [
          this.$moment() + 5 * 24 * 60 * 60 * 1000 - 24 * 60 * 60 * 1000 * 365,
          this.$moment() + 5 * 24 * 60 * 60 * 1000
        ],
        config: {
          valueFormat: "timestamp",
          type: "monthrange",
          rangeSeparator: "至",
          size: "small",
          pickerOptions: {}
        }
      },
      editInletWire: {
        openTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      editResource: {
        openTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      relevanceDetail: {
        openTrigger_in: new Date().getTime(),
        inputData_in: null
      }
    };
  },
  watch: {
    showDetail: {
      handler(val) {
        {
          if (val) {
            this.Columns_1 = this._.cloneDeep(this.Columns_resource);
          } else {
            this.Columns_1 = this._.cloneDeep(this.Columns_inletWire);
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    formatNum() {
      if (!this.countlist.totalTimes) return "--";
      if (
        this.countlist.percentageTimes ||
        this.countlist.percentageTimes === 0
      ) {
        return (
          (
            (this.countlist.percentageTimes / this.countlist.totalTimes) *
            100
          ).toFixed(2) + "%"
        );
      }
    },
    yearFormatNum() {
      if (!this.countlist.yearTotalTimes) return "--";
      if (
        this.countlist.yearTotalInvalidTimes ||
        this.countlist.yearTotalInvalidTimes === 0
      ) {
        return (
          (
            (this.countlist.yearTotalInvalidTimes /
              this.countlist.yearTotalTimes) *
            100
          ).toFixed(2) + "%"
        );
      }
    },
    init() {
      this.sum = 0;
      this.CetTable_1.data = [];
      this.ElInput_search.value = "";
      this.ElSelect_1.value = "";
      // 清除列表勾选
      // this.$refs.CetTable.$refs.cetTable.clearSelection();
    },
    Todetail(row) {
      row.isselfoperate = true;
      row.isquery = true;
      if (this.nodelevel === 1) {
        row.planid = row.id;
      }
      this.$router.push({ path: "/realTimeMonitoring", query: row });
    },
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.getDetailData();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getDetailData();
    },
    treeclick(data, node) {
      console.log(data);
      this.checkdata = data;
      this.nodelevel = node.level;
      this.getDetailData();
      // if (this.checkdata.modelLabel !== "responseresource")
      //   this.getDetailData();
    },
    getTreeData() {
      let data = {
        objectlabel: "",
        powersellingcompanyId: this.$store.state.sellCompanyId
      };
      console.log(data);
      customApi.baselineCalculationqueryTreeNodePark(data).then(response => {
        console.log(response);
        if (response.code === 0) {
          let options = this._.get(response, "data", []);
          this.CetTree_1.inputData_in = options;
        }
      });
    },

    getDetailData() {
      this.CetTable_1.data = [];
      this.countlist = {};
      console.log(this.CetDatePicker_1.val);
      let data = {
        isselfoperate: this.isSelfOperate,
        keyword: this.ElInput_search.value,
        modelLabel: this.checkdata.modelLabel,
        page: {
          index: this.currentPage - 1,
          limit: this.pageSize
        },
        powersellingcompanyid: this.$store.state.sellCompanyId,
        id:
          this.checkdata.modelLabel === "poweruser"
            ? this.checkdata.poweruserId
            : this.checkdata.id,
        starttime: this.CetDatePicker_1.val ? this.CetDatePicker_1.val[0] : "",
        endtime: this.CetDatePicker_1.val ? this.CetDatePicker_1.val[1] : "",
        status: this.ElSelect_2.value
      };
      let src = "";
      switch (this.checkdata.modelLabel) {
        case "poweruser":
          src = "statisticsDataspowerPlantIndex";
          break;
        case "demandgroup":
          src = "statisticsDataspowerPlantGroup";
          break;
        case "demandaccount":
          src = "statisticsDataspowerPlantAccount";
          break;
        case "responseresource":
          src = "statisticsDataspowerPlantAccount";
          break;
      }
      customApi[src](data).then(response => {
        if (response.code == 0) {
          this.CetTable_1.data = response.data.details;
          this.totalCount = response.total;
          this.countlist = response.data;
          console.log(this.countlist);
        }
      });
    },
    CetTree_1_currentNode_out(val) {
      this.showDetail = false;
    },
    handleSelectionChange(val) {
      this.sum = val.length;
    }
  },
  created() {},
  mounted() {
    this.getTreeData();
    console.log(this.$moment().format("YYYY/MM/DD HH:mm:ss"));
  },
  activated() {}
};
</script>
<style lang="scss" scoped>
.pagebox {
  margin: 0 !important;
  width: 100% !important;
  box-sizing: border-box;
  @include padding_top(J2);
  .countbox {
    display: flex;
    width: calc(100% - 16px);
    @include margin(J1);
    @include margin_top(J2);
    margin-bottom: 0;
    height: 98px;
    div {
      width: 28%;
      height: 98px;
      @include padding_left(J2);
      @include background_color(BG);
      margin: 0 8px;
      @include border_radius(C3);
      position: relative;
      h3 {
        @include font_size("H");
        font-weight: 600;
        line-height: 28px;
        // text-align: left;
        margin: 0;
      }
      p {
        @include font_size("Aa");
        @include font_color(T3);
        margin-top: 24px;
        font-weight: 400;
        line-height: 14px;
      }
    }
    // .borderbottom:hover:after {
    //   content: "";
    //   width: 110px;
    //   height: 3px;
    //   background: #3e77fc;
    //   position: absolute;
    //   bottom: 0;
    //   left: 0;
    //   right: 0;
    //   margin: 0 auto;
    // }
  }
  .searchInput {
    width: 298px;
    :deep(.el-input__inner) {
      padding-right: 60px;
    }
  }
  .customElSelect {
    // margin-left: 8px;
  }
  .contentBox {
    // @include padding(J2);
    // height: calc(100% - 140px);
    height: 100%;
    box-sizing: border-box;
    .aside {
      height: 100%;
      @include padding_top(J3);
      .treetitle {
        @include font_size("H2");
        @include margin_left(J2);
        font-weight: 600;
        line-height: 18px;
      }
      .tree {
        height: calc(100% - 40px) !important;
        @include margin_top(J2);
        @include padding_left(J2);
        @include padding_right(J2);
      }
    }
    .hearderbox {
      @include padding_top(J2);
      @include padding_left(J2);
      @include background_color(BG1);
      border-top-left-radius: mh-get(C3);
      border-top-right-radius: mh-get(C3);
    }
    .tableTop {
      @include background_color(BG1);
      .sum {
        @include font_color(ZS);
      }
    }
    .bgcolor {
      @include background_color(BG1);
      @include border_radius(C3);
    }
    .tableBody {
      @include background_color(BG1);
      padding-top: 12px;
      @include padding_left(J2);
      @include padding_right(J2);
      position: relative;
      height: 64%;
      :deep(.el-footer) {
        position: absolute;
        right: 36px;
        bottom: -48px;
      }
      .handel {
        cursor: pointer;
      }
    }
    .tableFooter {
      text-align: right;
      @include padding_right(J2);
      @include padding_top(J2);
      @include background_color(BG1);
      border-bottom-left-radius: mh-get(C3);
      border-bottom-right-radius: mh-get(C3);
    }
  }
  .el-icon-search {
    @include font_size("Aa");
    margin: 10px 7px 0 0;
  }
  .el-dropdown-link {
    cursor: pointer;
  }
}
</style>
