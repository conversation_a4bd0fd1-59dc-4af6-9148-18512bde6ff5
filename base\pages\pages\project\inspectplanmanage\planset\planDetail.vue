<template>
  <div class="fullheight flex-column">
    <div class="mtJ3" style="display: flex; align-items: center">
      <span class="mrJ1 nowrap">{{ $T("名称") }}:</span>
      <el-tooltip :content="detail.name" effect="light" placement="top-start">
        <span class="text-ellipsis common-title-H2">{{ detail.name }}</span>
      </el-tooltip>
    </div>
    <div class="mtJ1 mbJ1">
      <span class="common-title-H3">{{ $T("模拟量") }}</span>
    </div>
    <div class="flex-auto eem-container">
      <el-table
        height="100%"
        border
        stripe
        :data="tableData"
        highlight-current-row
        tooltip-effect="light"
      >
        <template v-for="item in simulate_columns">
          <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
        </template>
      </el-table>
    </div>
    <div class="mtJ1 mbJ1">
      <span class="common-title-H3">{{ $T("状态量") }}</span>
    </div>
    <div class="flex-auto eem-container">
      <el-table
        height="100%"
        border
        stripe
        :data="tableData1"
        highlight-current-row
        tooltip-effect="light"
      >
        <el-table-column
          type="index"
          align="left"
          label="#"
          width="60"
        ></el-table-column>
        <el-table-column
          align="left"
          prop="inspectionparameterid"
          :label="$T('参数名')"
          :formatter="formatterParam"
        ></el-table-column>
      </el-table>
    </div>
    <div class="mtJ1 mbJ1" v-if="showTextQuantity">
      <span class="common-title-H3">{{ $T("文本量") }}:</span>
    </div>
    <div class="flex-auto eem-container" v-if="showTextQuantity">
      <el-table
        height="100%"
        border
        stripe
        :data="tableData2"
        highlight-current-row
        tooltip-effect="light"
      >
        <el-table-column
          type="index"
          align="left"
          label="#"
          width="60"
        ></el-table-column>
        <el-table-column
          align="left"
          show-overflow-tooltip
          prop="inspectionparameterid"
          :label="$T('参数名')"
          :formatter="formatterParam"
        ></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import common from "eem-utils/common";

export default {
  name: "planDetail",
  components: {},
  props: {
    paramList: {
      type: Array
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "component", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "60px",
        labelPosition: "left",
        rules: {},
        event: {
          currentData_out: this.CetForm_pagedialog_currentData_out,
          saveData_out: this.CetForm_pagedialog_saveData_out,
          finishData_out: this.CetForm_pagedialog_finishData_out,
          finishTrigger_out: this.CetForm_pagedialog_finishTrigger_out
        }
      },
      tableData: [], // 模拟量表格数据
      tableData1: [], // 状态量表格数据
      tableData2: [], // 文本量表格数据
      simulate_columns: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "60"
        },
        {
          prop: "inspectionparameterid", // 支持path a[0].b
          label: $T("参数名"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatterParam
        },
        {
          prop: "min", // 支持path a[0].b
          label: $T("下限"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: common.formatNumberColumn
        },
        {
          prop: "max", // 支持path a[0].b
          label: $T("上限"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: common.formatNumberColumn
        }
      ],
      detail: {}
    };
  },
  computed: {
    showTextQuantity() {
      return this.$store.state.systemCfg.showTextQuantity;
    }
  },
  watch: {
    inputData_in(val) {
      this.detail = this._.cloneDeep(val);
      this.tableData = this.detail.analogQuantity || [];
      this.tableData1 = this.detail.statusQuantity || [];
      this.tableData2 = this.detail.textQuantity || [];
    }
  },
  methods: {
    formatter(row, column, cellValue) {
      return cellValue || "--";
    },
    formatterParam(row, column, cellValue) {
      const obj = this.paramList.find(item => item.id === cellValue);
      return (obj && obj.name) || "--";
    },
    CetForm_pagedialog_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_saveData_out(val) {
      this.$emit("saveData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishData_out(val) {
      this.$emit("finishData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishTrigger_out(val) {
      this.$emit("finishTrigger_out", val);
    }
  }
};
</script>

<style lang="scss" scoped></style>
