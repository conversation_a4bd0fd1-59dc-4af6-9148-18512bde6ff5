<template>
  <div>
    <CetDialog v-bind="CetDialog_add" v-on="CetDialog_add.event">
      <div class="eem-cont-c1">
        <div style="height: 500px; overflow: auto">
          <video
            v-if="
              videoList.includes(
                inputData_in && inputData_in.url.split('.').pop()
              )
            "
            :src="imgSrc"
            class="fullwidth"
            controls
          ></video>
          <img v-else :src="imgSrc" :alt="$T('暂无附件')" />
        </div>
      </div>
      <template v-slot:footer>
        <span>
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
        </span>
      </template>
    </CetDialog>
  </div>
</template>

<script>
import common from "eem-utils/common";
export default {
  name: "FlowChart",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  data(vm) {
    return {
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      isOk: true,
      CetDialog_add: {
        title: $T("附件"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_add_open_out,
          close_out: this.CetDialog_add_close_out
        },
        width: "980px",
        showClose: true
      },
      imgSrc: null,
      videoList: ["mp4", "3gp", "mkv", "avi", "mepg", "mpg"] // 视频支持的格式
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      // eslint-disable-next-line promise/param-names
      new Promise((res, err) => {
        this.getImgUrl(res);
      }).then(data => {
        if (data) {
          vm.CetDialog_add.openTrigger_in = val;
        } else {
          vm.$message.warning($T("获取附件失败"));
        }
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    }
  },

  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetDialog_add_open_out(val) {},
    CetDialog_add_close_out(val) {},
    getImgUrl: function (callback) {
      var me = this;
      const downloadPath = this.inputData_in.url;
      if (!downloadPath) {
        // eslint-disable-next-line standard/no-callback-literal
        callback && callback(false);
        return;
      }
      var url =
        "/eem-service/v1/common/downloadFile?path=" +
        encodeURIComponent(downloadPath);
      const params = {};
      common.generateImg(url, params, "GET").then(res => {
        if (res.status === 200 && res.data.type === "application/x-download") {
          // eslint-disable-next-line standard/no-callback-literal
          callback && callback(true);
          //将图片信息放到Img中
          me.imgSrc = window.URL.createObjectURL(res.data);
        } else {
          // eslint-disable-next-line standard/no-callback-literal
          callback && callback(false);
        }
      });
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped></style>
