<template>
  <div class="page eem-common">
    <div class="fullheight flex-row">
      <div style="width: 550px" class="fullheight flex-column">
        <div style="flex: 1" class="minWH eem-container">
          <div class="mbJ3" style="height: 32px">
            <el-tooltip
              effect="light"
              :content="$T('核算方案')"
              placement="bottom"
            >
              <div
                class="common-title-H2 fl mrJ1 text-ellipsis"
                style="width: 72px"
              >
                {{ $T("核算方案") }}
              </div>
            </el-tooltip>
            <!-- 关联对象按钮组件 -->
            <CetButton
              :disable_in="currentTabItem ? false : true"
              class="mlJ1 fr"
              v-bind="CetButton_relatedObj"
              v-on="CetButton_relatedObj.event"
              v-permission="'costcheckitem_associate'"
            ></CetButton>
            <CetButton
              class="mlJ1 fr"
              v-bind="CetButton_5"
              v-on="CetButton_5.event"
              v-permission="'costcheckitem_edit'"
            ></CetButton>
            <ElInput
              class="mlJ1 fr"
              v-model="ElInput_1.value"
              v-bind="ElInput_1"
              v-on="ElInput_1.event"
            ></ElInput>
          </div>
          <CetTable
            style="height: calc(100% - 48px)"
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
            ref="schemeTable"
          >
            <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_operate">
              <template slot-scope="scope">
                <span
                  class="handel fl mrJ3"
                  @click.stop="handleEdit(scope.$index, scope.row)"
                  v-permission="'costcheckitem_edit'"
                >
                  {{ $T("编辑") }}
                </span>
                <span
                  :class="{
                    fl: true,
                    mrJ1: true,
                    handel: true,
                    delete: true,
                    clickdisable: scope.row.bindNode
                  }"
                  @click.stop="handleDelete(scope.row)"
                  v-permission="'costcheckitem_edit'"
                >
                  {{ $T("删除") }}
                </span>
              </template>
            </ElTableColumn>
          </CetTable>
        </div>
      </div>
      <div class="flex-auto fullheight mlJ3">
        <CostComposition
          :currentSchemeItem="currentTabItem"
          :allTypeList="allTypeList"
          @getCostcheckitem="getCostcheckitem"
        ></CostComposition>
      </div>
    </div>
    <addOrEditScheme
      v-bind="addOrEditScheme"
      v-on="addOrEditScheme.event"
    ></addOrEditScheme>
    <relatedObjDialog
      v-bind="relatedObjDialog"
      v-on="relatedObjDialog.event"
      :currentTabItem="currentTabItem"
    ></relatedObjDialog>
  </div>
</template>
<script>
import commonApi from "@/api/custom";
import addOrEditScheme from "./dialog/addOrEditScheme.vue";
import CostComposition from "./CostComposition.vue";
import relatedObjDialog from "./dialog/relatedObjDialog.vue";
import { httping } from "@omega/http";

export default {
  name: "Accountingscheme",
  components: {
    addOrEditScheme,
    CostComposition,
    relatedObjDialog
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },

  data() {
    return {
      tabData: [],
      currentTabItem: null,
      allTypeList: [], // 所有能源类型、费用类型、费率类型的列表
      ElInput_1: {
        value: "",
        placeholder: $T("输入关键字以检索"),
        "suffix-icon": "el-icon-search",
        style: {
          width: "150px"
        },
        size: "small",
        event: {
          change: this.ElInput_1_change_out,
          input: this.ElInput_1_input_out
        }
      },
      CetButton_relatedObj: {
        visible_in: true,
        // disable_in: false,
        title: $T("关联对象"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_relatedObj_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        // prop: "", // 支持path a[0].b
        label: "#", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "50" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      // name组件
      ElTableColumn_name: {
        //type: "",      // selection 勾选 index 序号
        prop: "name", // 支持path a[0].b
        label: $T("核算方案名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_operate: {
        //type: "",      // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "120" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      CetButton_5: {
        visible_in: true,
        disable_in: false,
        title: $T("新增方案"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_5_statusTrigger_out
        }
      },
      addOrEditScheme: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: {},
        isEdit: false, // 编辑还是新增
        event: {
          saveData_out: this.saveSchemeName
        }
      },
      relatedObjDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: {},
        event: {
          refreshData: this.getTabData
        }
      },
      costcheckitem: [] // 某个核算方案的成本构成
    };
  },
  watch: {},

  methods: {
    CetButton_relatedObj_statusTrigger_out(val) {
      this.relatedObjDialog.openTrigger_in = new Date().getTime();
    },
    handleEdit(index, row) {
      this.addOrEditScheme.isEdit = true;
      this.addOrEditScheme.inputData_in = row;
      this.addOrEditScheme.openTrigger_in = new Date().getTime();
    },
    handleDelete(row) {
      if (row.bindNode) return;
      this.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            httping({
              url: `/eem-service/v1/schemeConfig/costCheckPlan/${this.currentTabItem.id}`,
              method: "DELETE"
            }).then(response => {
              if (response.code === 0) {
                this.$message({
                  message: $T("删除成功"),
                  type: "success"
                });
                this.getTabData();
              }
            });
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action !== "confirm") {
            this.$message({
              type: "info",
              message: $T("取消删除！")
            });
          }
        }
      });
    },
    // 查询所有能源类型、费用类型、费率类型
    async getEnergytype() {
      await commonApi.queryEnergySchemeConfig([]).then(res => {
        if (res.code === 0) {
          this.allTypeList = this._.cloneDeep(res.data);
        }
      });
    },
    // 获取列表数据
    getTabData() {
      this.tabData = [];
      this.CetTable_1.data = [];
      httping({
        url: `/eem-service/v1/schemeConfig/costCheckPlan/project/${this.projectId}`,
        method: "GET"
      }).then(response => {
        if (response.code === 0 && response.data && response.data.length > 0) {
          this.tabData = response.data;
          this.tabData.forEach(item => {
            if (
              item.costcheckitem_model &&
              item.costcheckitem_model.length > 0
            ) {
              item.constNames = item.costcheckitem_model
                .map(i => {
                  return i.name;
                })
                .join("+");
            }
          });
          this.filterTabData();
        }
      });
    },
    filterTabData() {
      if (this.tabData && this.tabData.length > 0 && this.ElInput_1.value) {
        this.CetTable_1.data = this.tabData.filter(item => {
          return item.name.indexOf(this.ElInput_1.value) !== -1;
        });
      } else {
        this.CetTable_1.data = this._.cloneDeep(this.tabData);
      }
      if (this.currentTabItem) {
        this.$refs.schemeTable.$refs.cetTable.setCurrentRow(
          this.currentTabItem
        );
      }
    },
    // 新建核算方案
    CetButton_5_statusTrigger_out(val) {
      this.addOrEditScheme.isEdit = false;
      this.addOrEditScheme.openTrigger_in = new Date().getTime();
    },
    getCostcheckitem(val) {
      this.costcheckitem = this._.cloneDeep(val);
    },
    // 保存核算方案名称
    saveSchemeName(params) {
      const costcheckitemModel = this.costcheckitem || [];
      const data = {
        name: params.name,
        createtime: this.$moment().valueOf(),
        costcheckitem_model: params.id ? costcheckitemModel : [],
        projectid: this.projectId,
        id: params.id || 0
      };
      httping({
        url: `/eem-service/v1/schemeConfig/costCheckPlan`,
        method: "PUT",
        data
      }).then(response => {
        if (response.code === 0) {
          this.$message({
            message: $T("保存成功"),
            type: "success"
          });
          this.addOrEditScheme.closeTrigger_in = new Date().getTime();
          this.getTabData();
        }
      });
    },
    ElInput_1_change_out(val) {},
    ElInput_1_input_out(val) {
      this.filterTabData();
    },
    CetTable_1_outputData_out(val) {},
    CetTable_1_record_out(val) {
      if (val.id !== -1) {
        this.currentTabItem = val;
      } else {
        this.currentTabItem = null;
      }
    }
  },
  created: function () {},
  activated: async function () {
    await this.getEnergytype();
    this.getTabData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.cetLeabel {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.tree :deep(.el-tree) {
  overflow: auto;
}
.clickformore {
  cursor: pointer;
}
.handel {
  cursor: pointer;
  @include font_color(ZS);
  &.delete {
    @include font_color(Sta3);
  }
  &.clickdisable {
    @include font_color(T4);
    cursor: not-allowed !important;
  }
}
</style>
