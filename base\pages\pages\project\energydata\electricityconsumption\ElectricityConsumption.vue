<template>
  <div class="page">
    <el-container style="height: 100%">
      <el-header height="auto" class="lh32" style="padding: 0px">
        <el-tooltip effect="light" :content="nodeName" placement="top-start">
          <div class="common-title-H2 text-ellipsis" style="width: 250px">
            {{ nodeName || "--" }}
          </div>
        </el-tooltip>
      </el-header>
      <el-main class="elMain padding0 fullheight flex-column mtJ3">
        <el-row :gutter="$J3" class="flex-row">
          <el-col :span="16">
            <div class="eem-container fullheight clearfix">
              <div class="clearfix">
                <span class="common-title-H2">各分项用能统计</span>
                <!-- <ElSelect
                  v-model="ElSelect_type.value"
                  v-bind="ElSelect_type"
                  v-on="ElSelect_type.event"
                  prefix_in="能源类型"
                >
                  <ElOption
                    v-for="item in ElOption_type.options_in"
                    :key="item[ElOption_type.key]"
                    :label="item[ElOption_type.label]"
                    :value="item[ElOption_type.value]"
                    :disabled="item[ElOption_type.disabled]"
                  ></ElOption>
                </ElSelect> -->
              </div>
              <div
                :class="{
                  'cont-cont': true,
                  ml0: index % 2 === 0,
                  mr0: index % 2 !== 0
                }"
                v-for="(item, index) in items"
                :key="index"
              >
                <div class="cont-cont-title label-cl">
                  <span class="label-icon" :class="geLabelIcon(index)"></span>
                  <span
                    class="text-ellipsis"
                    :title="item.name + '（' + symbol + '）'"
                  >
                    {{ item.name || "--" }}（{{ symbol || "kWh" }}）
                  </span>
                </div>
                <div class="clearfix">
                  <div class="cont-cont-label mbJ1">
                    <span class="label-cl">当日</span>
                    <span class="text-overflow" :title="item.curDayData">
                      {{ item.curDayData || "--" }}
                    </span>
                  </div>
                  <div class="cont-cont-label mbJ1">
                    <span class="label-cl">当月</span>
                    <span class="text-overflow" :title="item.curMonData">
                      {{ item.curMonData || "--" }}
                    </span>
                  </div>
                  <div class="cont-cont-label">
                    <span class="label-cl">当年</span>
                    <span class="text-overflow" :title="item.curYearData">
                      {{ item.curYearData || "--" }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="eem-container fullheight flex-column">
              <div class="clearfix">
                <span class="common-title-H2">当月分项用能占比</span>
              </div>
              <div class="flex-auto">
                <CetChart
                  :inputData_in="CetChart_1.inputData_in"
                  v-bind="CetChart_1.config"
                />
              </div>
            </div>
          </el-col>
        </el-row>
        <div
          class="flex-auto mtJ3 eem-container flex-column"
          style="min-height: 180px"
        >
          <div class="clearfix mbJ3">
            <span class="common-title-H2 fl mtJ">各分项用能趋势</span>
            <div class="fr">
              <!-- 向前查询按钮 -->
              <CetButton
                class="fl custom—square"
                v-bind="CetButton_prv"
                v-on="CetButton_prv.event"
              ></CetButton>
              <CustomElDatePicker
                class="fl mlJ mrJ"
                prefix_in="选择年份"
                v-bind="CetDatePicker_time.config"
                v-model="CetDatePicker_time.val"
                @change="CetDatePicker_time_queryTime_out"
              />
              <!-- 向后查询按钮 -->
              <CetButton
                class="fl custom—square"
                :disable_in="backToTimeBtnDis1"
                v-bind="CetButton_next"
                v-on="CetButton_next.event"
              ></CetButton>
            </div>
          </div>
          <div class="flex-auto">
            <CetChart
              :inputData_in="CetChart_2.inputData_in"
              v-bind="CetChart_2.config"
            />
          </div>
        </div>
      </el-main>
      <!-- </el-container> -->
    </el-container>
    <!-- <Slide></Slide> -->
  </div>
</template>
<script>
import common from "eem-utils/common";
import moment from "moment";
import { httping } from "@omega/http";
export default {
  name: "ElectricityConsumption",
  components: {},
  props: {
    clickNode: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    // 实际对标考核下一时段按钮禁点状态
    backToTimeBtnDis1() {
      var actTime = null, //控件时间
        maxTime = null, //当前时间
        cycle = 5,
        time = this.CetDatePicker_time.val;

      if (cycle === 1) {
        actTime = moment(time).startOf("day").valueOf();
        maxTime = moment().startOf("day").subtract(1, "d").valueOf();
      } else if (cycle === 3) {
        actTime = moment(time).startOf("month").valueOf();
        maxTime = moment().startOf("month").valueOf();
      } else if (cycle === 5) {
        actTime = moment(time).startOf("year").valueOf();
        maxTime = moment().startOf("year").valueOf();
      }
      return actTime >= maxTime;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    },
    unitInfo() {
      return this.$store.state.unitInfo;
    }
  },

  data(vm) {
    return {
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      // 请填写组件含义组件
      CetDatePicker_time: {
        disable_in: false, //禁用
        val: new Date(),
        config: {
          clearable: false,
          valueFormat: "yyyy",
          type: "year", //date daterange
          format: "yyyy",
          rangeSeparator: "-",
          size: "small",
          style: {
            width: "200px"
          }
        }
      },
      CetChart_1: {
        inputData_in: null,
        config: {
          options: {
            legend: {
              type: "scroll",
              bottom: 0
            },
            tooltip: {
              trigger: "item",
              formatter: "{b}:<br/> {c} ({d}%)"
            },
            series: [
              {
                type: "pie",
                radius: "60%",
                center: ["50%", "50%"],
                data: [],
                label: {
                  formatter: "{b}{d}%"
                }
              }
            ]
          }
        }
      },
      CetChart_2: {
        inputData_in: null,
        config: {
          options: {
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "cross",
                label: {
                  backgroundColor: "#6a7985"
                }
              },
              formatter(val) {
                let list = val || [];
                if (list.length === 0) {
                  return "--";
                }
                let result = list[0].axisValue;
                list.forEach(item => {
                  result +=
                    "<br/>" +
                    item.marker +
                    item.seriesName +
                    " " +
                    item.value +
                    (vm.unitInfo[vm.energyType]
                      ? vm.unitInfo[vm.energyType].unitEn
                      : "");
                });
                return result;
              }
            },
            legend: {
              data: []
            },
            toolbox: {
              feature: {
                saveAsImage: {}
              }
            },
            grid: {
              top: 32,
              left: 32,
              right: 32,
              bottom: 0,
              containLabel: true
            },
            xAxis: [
              {
                type: "category",
                boundaryGap: false,
                data: []
              }
            ],
            yAxis: [
              {
                type: "value",
                name: "用能量(kWh)",
                nameTextStyle: {
                  padding: [0, 0, 0, 10]
                }
              }
            ],
            series: []
          }
        }
      },
      items: [
        {
          tag: "--",
          name: "--",
          curDayData: "--",
          curMonData: "--",
          curYearData: "--",
          rate: "--"
        },
        {
          tag: "--",
          name: "--",
          curDayData: "--",
          curMonData: "--",
          curYearData: "--",
          rate: "--"
        },
        {
          tag: "--",
          name: "--",
          curDayData: "--",
          curMonData: "--",
          curYearData: "--",
          rate: "--"
        },
        {
          tag: "--",
          name: "--",
          curDayData: "--",
          curMonData: "--",
          curYearData: "--",
          rate: "--"
        }
      ],
      Propertys: [],
      nodeName: "",
      ElSelect_type: {
        value: "",
        style: {
          width: "200px",
          float: "right"
        },
        event: {
          change: this.ElSelect_type_change_out
        }
      },
      ElOption_type: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      symbol: ""
    };
  },
  watch: {
    clickNode: {
      deep: true,
      handler: function (val, oldVal) {
        if (!(this._.get(val, "id") && this._.get(val, "modelLabel"))) return;
        if (
          val.id === this._.get(oldVal, "id") &&
          val.modelLabel === this._.get(oldVal, "modelLabel")
        ) {
          return;
        }
        this.nodeName = val.name;
        this.getEachItemed();
        this.getChartData();
      }
    },
    "CetDatePicker_time.val": {
      handler: function (val) {
        this.CetDatePicker_time_queryTime_out(val);
      },
      deep: true
    }
  },
  methods: {
    grtProjectEnergy(response) {
      var _this = this;
      _this.ElOption_type.options_in = [];

      httping({
        url:
          "/eem-service/v1/project/projectEnergy?projectId=" + this.projectId,
        method: "GET"
      }).then(res => {
        if (res.code === 0 && res.data) {
          var selectData = res.data || [];
          _this.ElOption_type.options_in = _this._.cloneDeep(selectData);
          let obj = selectData.find(i => i.energytype === 2);
          if (obj) {
            _this.ElSelect_type.value = obj.id;
            _this.symbol = obj.symbol;
          } else {
            // _this.ElSelect_type.value = _this._.get(selectData, "[0].id", null);
            // _this.symbol = _this._.get(selectData, "[0].symbol", null);
          }
          response && response();
        }
      });
    },
    ElSelect_type_change_out(val) {
      this.ElOption_type.options_in.forEach(item => {
        if (item.id == val) {
          this.symbol = item.symbol;
        }
      });
      this.getEachItemed();
      this.getChartData();
    },
    CetButton_prv_statusTrigger_out(val) {
      let date = this.$moment(this.CetDatePicker_time.val);
      this.CetDatePicker_time.val = date.subtract(1, "Y")._d;
    },
    CetButton_next_statusTrigger_out(val) {
      let date = this.$moment(this.CetDatePicker_time.val);
      this.CetDatePicker_time.val = date.add(1, "Y")._d;
    },
    CetDatePicker_time_queryTime_out(val) {
      let date = this.$moment(val);
      this.getChartData();
      // this.queryTime = [date.startOf("d").valueOf(), date.endOf("d").valueOf() + 1];
    },
    // 获取标签数据列表
    getPropertys(response) {
      var _this = this,
        auth = _this.token; //身份验证
      var params = {
        dimId: 1
      };
      httping({
        url: "/eem-service/v1/dim/setting/propertysByDimId",
        params,
        method: "GET",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          _this.Propertys = res.data;
          response();
        }
      });
    },
    // 获取各分项用能统计
    getEachItemed() {
      var _this = this,
        auth = _this.token; //身份验证
      if (!this.clickNode) {
        // this.$message.warning("请先选择节点！");
        return;
      }
      let energyType = null;
      _this.ElOption_type.options_in.forEach(item => {
        if (item.id == _this.ElSelect_type.value) {
          energyType = item.energytype;
        }
      });
      if (!energyType) {
        return;
      }
      this.energyType = energyType;
      var params = {
        id: this.clickNode.id,
        modelLabel: this.clickNode.modelLabel,
        tags: _this.getParams(),
        energyType: energyType,
        normal: true,
        projectId: this.projectId
      };
      httping({
        url: "/eem-service/v1/energy/energydata/eachItemized",
        data: params,
        method: "POST",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          _this.filEachItemedData(res.data);
        }
      });
    },
    filEachItemedData(val) {
      var data = val || [],
        seriesData = [];
      data.forEach(item => {
        var rate = (Number(item.rate) * 100).toFixed(0);
        var obj = {
          value: `${item.curMonData}`,
          name: `${item.name}`
        };
        seriesData.push(obj);
      });
      this.items = data;
      this.CetChart_1.config.options.series[0].data = seriesData;
    },
    //获取各分项用能趋势
    getChartData() {
      var _this = this,
        auth = _this.token; //身份验证
      if (!this.clickNode) {
        // this.$message.warning("请先选择节点！");
        return;
      }
      var tags = _this.getParams();
      if (!tags || tags.length == 0) {
        return;
      }
      let energyType = null;
      _this.ElOption_type.options_in.forEach(item => {
        if (item.id == _this.ElSelect_type.value) {
          energyType = item.energytype;
        }
      });
      if (!energyType) {
        return;
      }
      var queryBody = {
        id: this.clickNode.id,
        modelLabel: this.clickNode.modelLabel,
        time: moment(this.CetDatePicker_time.val).startOf("Y").valueOf(),
        tags: tags,
        energyType: energyType,
        projectId: this.projectId
      };
      var queryOption = {
        url: `/eem-service/v1/energy/energydata/accumItemized`,
        method: "POST",
        data: queryBody
      };

      httping(queryOption).then(function (response) {
        if (response.code === 0) {
          //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
          console.log(response.data);
          var data = _this._.get(response, ["data"], []);
          _this.filChartData(data);
        }
      });
    },
    filChartData(val) {
      var _this = this,
        data = val || [],
        legend = [],
        xAxis = [],
        series = [];
      for (var i = 0, len = data.length; i < len; i++) {
        var datas = data[i].datas,
          xAxisData = [],
          yAxisData = [];

        datas.forEach(item => {
          var value = common.formatNumberWithPrecision(item.value, 2);
          yAxisData.push(value);
          var time = this.getAxixs(item.time, 17);
          xAxisData.push(time);
        });
        var obj = {
          name: data[i].name,
          data: yAxisData,
          type: "line",
          itemStyle: {
            normal: {
              lineStyle: {
                color: "transparent"
              }
            }
          },
          smooth: true,
          areaStyle: {},
          stack: "总量"
        };
        if (i === 0) {
          xAxis = xAxisData;
        }
        legend.push(data[i].name);
        series.push(obj);
      }

      this.CetChart_2.config.options.xAxis[0].data = xAxis;
      this.CetChart_2.config.options.series = series;
      this.CetChart_2.config.options.legend.data = legend;
    },
    getParams() {
      var _this = this,
        Propertys = this.Propertys || [],
        tags = [];
      Propertys.forEach(item => {
        var obj = {
          name: item.name,
          tag: item.id
        };
        tags.push(obj);
      });
      return tags;
    },
    getAxixs(val, type) {
      var date = new Date(val),
        y = date.getFullYear(),
        M = date.getMonth() + 1,
        d = date.getDate(),
        h = date.getHours(),
        m = date.getMinutes();
      if (M < 10) {
        M = "0" + M;
      }
      if (d < 10) {
        d = "0" + d;
      }
      if (h < 10) {
        h = "0" + h;
      }
      if (m < 10) {
        m = "0" + m;
      }
      if (type === 12) {
        return h + ":" + m;
      } else if (type === 14) {
        return M + "-" + d;
      } else if (type === 17) {
        return y + "-" + M;
      } else {
        return y + "-" + M + "-" + d;
      }
    },
    geLabelIcon(val) {
      var index = val || 0;
      if (index % 4 == 0) {
        return "label-icon1";
      } else if (index % 4 == 1) {
        return "label-icon2";
      } else if (index % 4 == 2) {
        return "label-icon3";
      } else if (index % 4 == 3) {
        return "label-icon4";
      } else {
        return "label-icon1";
      }
    }
  },
  created: function () {
    Promise.all([
      new Promise((res, err) => {
        this.getPropertys(res);
      }),
      new Promise((res, err) => {
        this.grtProjectEnergy(res);
      })
    ]).then(() => {
      this.getEachItemed();
      this.getChartData();
    });
  },
  mounted: function () {
    this.nodeName = this.clickNode ? this.clickNode.name : null;
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
}
.elMain {
  overflow-x: hidden;
  overflow-y: auto;
}

.cont-cont {
  @include margin_right(J1);
  @include margin_left(J1);
  @include margin_top(J2);
  @include padding(J2);
  width: calc(50% - 8px);
  float: left;
  box-sizing: border-box;
  @include border_radius(C1);
  @include background_color(BG2);
}

.cont-cont-title {
  height: mh-get(I2);
  line-height: mh-get(I2);
  @include margin_bottom(J2);
  display: flex;
  .label-icon {
    float: left;
    display: block;
    width: mh-get(I2);
    height: mh-get(I2);
    border-radius: 50%;
    @include margin_right(J1);
  }
  .label-icon1 {
    background: url(../assets/u6993.png) no-repeat center;
    background-size: contain;
  }
  .label-icon2 {
    background: url(../assets/u6995.png) no-repeat center;
    background-size: contain;
  }
  .label-icon3 {
    background: url(../assets/u7062.png) no-repeat center;
    background-size: contain;
  }
  .label-icon4 {
    background: url(../assets/u6997.png) no-repeat center;
    background-size: auto;
  }
}
.text-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.cont-cont-label {
  display: flex;
  .label-cl {
    width: 50px;
    @include font_color(T3);
  }
  .text-overflow {
    flex: 1;
    text-align: right;
  }
  span {
    display: block;
    @include line_height(H5);
  }
}
.bd-bg {
  width: calc(34% - 2px);
  border-left: 1px solid;
  border-right: 1px solid;
  @include border_color(B1);
}
.ml5 {
  margin-left: 5px;
}
.ml10 {
  margin-left: 10px;
}
.mr5 {
  margin-right: 5px;
}
.mr10 {
  margin-right: 10px;
}
.basic-box .basic-box-label {
  height: 34px;
  line-height: 34px;
}
</style>
