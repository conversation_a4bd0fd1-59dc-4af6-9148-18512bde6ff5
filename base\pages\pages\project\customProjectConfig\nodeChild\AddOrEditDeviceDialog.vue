<template>
  <div>
    <CetDialog
      class="device-config-dialog CetDialog"
      width="1024px"
      :title="dialogTitle"
      v-bind="CetDialog_Device"
      v-on="CetDialog_Device.event"
    >
      <CetForm
        class="fullfilled overflow-auto"
        :label-width="calculatedLabelWidth"
        :data.sync="CetForm_Device.data"
        v-bind="CetForm_Device"
        v-on="CetForm_Device.event"
      >
        <div class="bg1 brC2 rowBox">
          <template v-for="property in DevicePropertyList">
            <template v-if="!property.disabled">
              <div
                class="device-property-container"
                :key="property.id"
                v-if="property.propertyLabel != 'description'"
              >
                <el-form-item
                  class="custom-form-item gray-label"
                  :label="property.name"
                  :prop="property.propertyLabel"
                >
                  <!-- deviceclassification 设备归类需要下拉选择，但模型定义为int8 -->
                  <ElSelect
                    v-if="property.propertyLabel == 'deviceclassification'"
                    v-model="
                      CetForm_Device.inputData_in[property.propertyLabel]
                    "
                    class="fullfilled"
                  >
                    <ElOption
                      v-for="item in deviceclassificationOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></ElOption>
                  </ElSelect>
                  <ElSelect
                    v-else-if="property.detailedDataType == 'boolean'"
                    v-model="
                      CetForm_Device.inputData_in[property.propertyLabel]
                    "
                    :popper-append-to-body="false"
                    class="fullfilled"
                  >
                    <ElOption
                      v-for="item in ElOption_bool.options_in"
                      :key="item[ElOption_bool.key]"
                      :label="item[ElOption_bool.label]"
                      :value="item[ElOption_bool.value]"
                      :disabled="item[ElOption_bool.disabled]"
                    ></ElOption>
                  </ElSelect>
                  <ElInput
                    v-else-if="property.detailedDataType === 'string'"
                    clearable
                    :maxlength="property.maxLength"
                    :placeholder="$T('请输入{0}', property.alias)"
                    v-model.trim="
                      CetForm_Device.inputData_in[property.propertyLabel]
                    "
                  ></ElInput>
                  <el-date-picker
                    v-else-if="property.detailedDataType === 'date'"
                    valueFormat="timestamp"
                    type="date"
                    :placeholder="$T('请选择{0}', property.alias)"
                    v-model="
                      CetForm_Device.inputData_in[property.propertyLabel]
                    "
                    :clearable="!property.required"
                  ></el-date-picker>
                  <ElInputNumber
                    v-else-if="property.detailedDataType === 'number'"
                    v-model="
                      CetForm_Device.inputData_in[property.propertyLabel]
                    "
                    class="fullwidth text-top"
                    controls-position="right"
                    :placeholder="$T('请输入{0}', property.alias)"
                    :min="property.rangeMin"
                    :max="property.rangeMax"
                    :precision="property.precision || 0"
                  ></ElInputNumber>
                  <ElInputNumber
                    v-else-if="property.detailedDataType === 'long'"
                    v-model="
                      CetForm_Device.inputData_in[property.propertyLabel]
                    "
                    class="fullwidth text-top"
                    controls-position="right"
                    :placeholder="$T('请输入{0}', property.alias)"
                    :min="property.rangeMin"
                    :max="property.rangeMax"
                    :precision="property.precision || 0"
                  ></ElInputNumber>
                  <ElSelect
                    v-else-if="property.detailedDataType === 'enum'"
                    v-model="
                      CetForm_Device.inputData_in[property.propertyLabel]
                    "
                    class="fullfilled"
                  >
                    <ElOption
                      v-for="item in property.options"
                      :key="item.id"
                      :label="item.text"
                      :value="item.id"
                    ></ElOption>
                  </ElSelect>
                  <template v-if="property.detailedDataType === 'busbarsegi'">
                    <ElInput
                      readonly
                      :placeholder="$T('请输入{0}', property.alias)"
                      v-model.trim="busbarSegI"
                    >
                      <el-button
                        slot="append"
                        icon="el-icon-folder-opened darkblue-font"
                        @click.stop="showBusbarSegSelectionDialog(1)"
                      ></el-button>
                    </ElInput>
                  </template>
                  <template v-if="property.detailedDataType === 'busbarsegii'">
                    <ElInput
                      readonly
                      :placeholder="$T('请输入{0}', property.alias)"
                      v-model.trim="busbarSegII"
                    >
                      <el-button
                        slot="append"
                        icon="el-icon-folder-opened darkblue-font"
                        @click.stop="showBusbarSegSelectionDialog(2)"
                      ></el-button>
                    </ElInput>
                  </template>
                  <template v-else-if="property.detailedDataType === 'pic'">
                    <CustomUploader
                      :placeholder="$T('请选择设备图片')"
                      accept=".png,.jpg,.jpeg,.bmp,.gif"
                      :infoText_in="
                        $T('支持 JPG,JPEG,BMP,GIF,PNG 文件，大小限制为200K')
                      "
                      :maxSize="200"
                      :defaultFileName_in="
                        CustomUploader_DevicePic.defaultFileName_in
                      "
                      :resetTrigger_in="
                        CustomUploader_DevicePic.resetTrigger_in
                      "
                      @fileChanged_out="
                        CustomUploader_DevicePic_fileChanged_out
                      "
                    ></CustomUploader>
                  </template>

                  <template
                    v-else-if="property.detailedDataType === 'document'"
                    style="position: relative"
                  >
                    <CustomUploader
                      style="height: 105px"
                      :placeholder="$T('请选择设备文档')"
                      accept=".doc,.docx,.pdf"
                      :infoText_in="$T('支持 doc,docx,pdf 文件，大小限制为10M')"
                      :maxSize="10240"
                      :defaultFileName_in="
                        CustomUploader_DeviceDoc.defaultFileName_in
                      "
                      :resetTrigger_in="
                        CustomUploader_DeviceDoc.resetTrigger_in
                      "
                      @fileChanged_out="
                        CustomUploader_DeviceDoc_fileChanged_out
                      "
                    ></CustomUploader>
                    <div class="files">
                      <div
                        class="file"
                        v-for="(item, i) in CustomUploader_DeviceDoc.files"
                        :key="i"
                        @close="deleteNode(i)"
                      >
                        {{ item.name }}
                        <div class="del fr" @click="deleteNode(i)">X</div>
                      </div>
                    </div>
                  </template>
                  <ElInput
                    v-else-if="property.detailedDataType === '_location'"
                    readonly
                    maxlength="30"
                    :placeholder="$T('请选择经纬度')"
                    v-model="
                      CetForm_Device.inputData_in[property.propertyLabel]
                    "
                  >
                    <el-button
                      slot="append"
                      icon="el-icon-position darkblue-font"
                      @click.stop="showLocatePositionDialog"
                    ></el-button>
                  </ElInput>
                </el-form-item>
              </div>
              <div
                class="device-property-container"
                :key="property.id"
                v-if="property.propertyLabel == 'description'"
              >
                <el-form-item
                  label-width="0"
                  class="custom-form-item-vertical"
                  :label="$T('描述')"
                  prop="description"
                >
                  <!-- <header class="line-h30 input-header">描述</header> -->
                  <ElInput
                    clearable
                    class="input-body"
                    type="textarea"
                    resize="none"
                    maxlength="200"
                    rows="5"
                    show-word-limit
                    :placeholder="$T('请输入描述')"
                    v-model.trim="CetForm_Device.inputData_in.description"
                  ></ElInput>
                </el-form-item>
              </div>
            </template>
          </template>
        </div>
      </CetForm>
      <template slot="footer">
        <span class="device-Button">
          <el-button size="small" type="primary" plain @click="close">
            {{ $T("取消") }}
          </el-button>
        </span>
        <span class="device-Button">
          <el-button size="small " type="primary" @click="addOrEditDevice">
            {{ $T("确定") }}
          </el-button>
        </span>
      </template>
    </CetDialog>
    <DeviceModelInterface
      :refreshTrigger_in="DeviceModelInterface.refreshTrigger_in"
      :deviceLabel_in="DeviceModelInterface.deviceLabel_in"
      @deviceModel_out="DeviceModelInterface_DeviceModel_out"
    ></DeviceModelInterface>
    <CetInterface
      :data.sync="CetInterface_DeviceDetail.data"
      :dynamicInput.sync="CetInterface_DeviceDetail.dynamicInput"
      v-bind="CetInterface_DeviceDetail"
      v-on="CetInterface_DeviceDetail.event"
    ></CetInterface>
    <LocatePositionDialog
      :location_in="LocatePositionDialog.location_in"
      :openTrigger_in="LocatePositionDialog.openTrigger_in"
      @location_out="handleLocationOut"
    ></LocatePositionDialog>
    <BusbarSegSelectionDialog
      :defaultId_in="BusbarSegSelectionDialog.defaultId_in"
      :busbarMarkType_in="BusbarSegSelectionDialog.busbarMarkType_in"
      :roomId_in="BusbarSegSelectionDialog.roomId_in"
      :openTrigger_in="BusbarSegSelectionDialog.openTrigger_in"
      @busbarSegData_out="handleBusbarSegData_out"
    ></BusbarSegSelectionDialog>
    <CetInterface
      :data.sync="CetInterface_query.data"
      :dynamicInput.sync="CetInterface_query.dynamicInput"
      v-bind="CetInterface_query"
      v-on="CetInterface_query.event"
    ></CetInterface>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import CustomUploader from "./basecomponents/customUploader";
import LocatePositionDialog from "./basecomponents/LocatePositionDialog";
import DeviceModelInterface from "./basecomponents/DeviceModelInterface";
import BusbarSegSelectionDialog from "./basecomponents/BusbarSegSelectionDialog";

const notEumnType = ["int8", "int4", "float", "string", "jsonb", "boolean"];

export default {
  name: "addOrEditDeviceDialog",
  components: {
    CustomUploader,
    LocatePositionDialog,
    DeviceModelInterface,
    BusbarSegSelectionDialog
  },
  props: {
    isEditMode_in: {
      type: Boolean,
      default: false
    },
    roomData_in: {
      type: Object,
      default: null
    },
    deviceData_in: {
      type: Object,
      default: null
    },
    lowVoltageCabinetData_in: {
      type: Object,
      default: null
    },
    openTrigger_in: {
      type: Number,
      default: new Date().getTime()
    },
    deviceMsg_in: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      // 设备归类
      deviceclassificationOptions: [],
      // 配电室弹窗组件
      CetDialog_Device: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          open_out: this.CetDialog_Device_open_out,
          close_out: this.CetDialog_Device_close_out,
          opened: this.CetDialog_Device_OpenSuccessfully
        }
      },

      // 配电室表单组件
      CetForm_Device: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "createEndEditDevice",
          modelLabel: "room",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        labelPosition: "top",
        rules: {},
        event: {
          currentData_out: this.CetForm_Device_currentData_out,
          saveData_out: this.CetForm_Device_saveData_out,
          finishData_out: this.CetForm_Device_finishData_out,
          finishTrigger_out: this.CetForm_Device_finishTrigger_out
        }
      },

      // 设备类型组件
      ElSelect_DeviceType: {
        event: {
          change: this.ElSelect_DeviceType_change_out
        }
      },

      // 设备图片组件
      ElInput_DeviceImage: {
        value: "",
        currentFile: null
      },

      // 地图弹窗组件
      LocatePositionDialog: {
        openTrigger_in: new Date().getTime(),
        location_in: null
      },

      // 查询设备详情的接口
      CetInterface_DeviceDetail: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "queryModel",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: true
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {},
        defaultSort: null,
        event: {
          result_out: this.CetInterface_DeviceDetail_result_out,
          finishTrigger_out: this.CetInterface_DeviceDetail_finishTrigger_out,
          failTrigger_out: this.CetInterface_DeviceDetail_failTrigger_out,
          totalNum_out: this.CetInterface_DeviceDetail_totalNum_out
        }
      },

      // 设备模型接口组件
      DeviceModelInterface: {
        refreshTrigger_in: new Date().getTime(),
        deviceLabel_in: null
      },

      // 母线弹窗组件
      BusbarSegSelectionDialog: {
        defaultId_in: null,
        busbarMarkType_in: 1,
        roomId_in: null,
        openTrigger_in: new Date().getTime()
      },

      // 设备图片组件
      CustomUploader_DevicePic: {
        defaultFileName_in: "",
        currentFile: null,
        resetTrigger_in: new Date().getTime()
      },

      // 设备文档组件
      CustomUploader_DeviceDoc: {
        defaultFileName_in: "",
        currentFile: null,
        files: [],
        resetTrigger_in: new Date().getTime()
      },

      DevicePropertyList: [],
      CetInterface_query: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "getEventClassification",
          modelLabel: "deviceclassification",
          dataIndex: [],
          modelList: [],
          filters: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {},
        page_in: null, // exp:{ index: 1, limit: 20 }
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          result_out: this.CetInterface_query_result_out
        }
      },
      ElOption_bool: {
        options_in: [
          {
            id: true,
            text: $T("是")
          },
          {
            id: false,
            text: $T("否")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      }
    };
  },
  computed: {
    calculatedLabelWidth() {
      let vm = this;
      let list = vm.DevicePropertyList;
      let labelWidth = 80;

      vm._.each(list, item => {
        let width = item.name.length * 16;
        labelWidth = Math.max(labelWidth, width);
      });

      return labelWidth + 10 + "px";
    },
    dialogTitle() {
      let name = this._.get(this.deviceMsg_in, "name", null) || $T("设备");
      let title = this.isEditMode_in
        ? $T("编辑{0}", name)
        : $T("新增{0}", name);
      return title;
    },
    lineSegmentwithSwitchIdDisabled() {
      return this.CetForm_Device.inputData_in.linefunctiontype !== 5;
    },
    showMaintenanceParameters() {
      let deviceLabel = this.DeviceModelInterface.deviceLabel_in;
      return (
        this.CetForm_Device.inputData_in.linefunctiontype === 3 ||
        this.CetForm_Device.inputData_in.linefunctiontype === 4 ||
        deviceLabel === "ptcabinet" ||
        deviceLabel === "busbarconnector" ||
        deviceLabel === "capacitor" ||
        deviceLabel === "meteringcabinet"
      );
    },
    lineSegmentwithSwitchId() {
      let vm = this;
      let avaliable = vm.CetForm_Device.inputData_in.linefunctiontype === 5;
      let id = vm.CetForm_Device.inputData_in.linesegmentwithswitch_id;

      if (!avaliable || vm._.isNil(id)) {
        return "";
      } else {
        return $T("所属低电压柜已选择");
      }
    },
    inLowVoltageCabinet() {
      return !!this.lowVoltageCabinetData_in;
    },
    busbarSegI() {
      let vm = this;
      let id = vm.CetForm_Device.inputData_in.busbarsegi;
      return vm._.isNil(id) ? "" : $T("I段母线对象已选择");
    },
    busbarSegII() {
      let vm = this;
      let id = vm.CetForm_Device.inputData_in.busbarsegii;
      return vm._.isNil(id) ? "" : $T("II段母线对象已选择");
    },
    relatedMeters() {
      let vm = this;
      let measureids = vm.CetForm_Device.inputData_in.measureids || [];
      return measureids.length ? $T("已选择{0}个表计", measureids.length) : "";
    }
  },
  watch: {
    openTrigger_in() {
      let modelLabel = this._.get(this.deviceMsg_in, "modelLabel");
      if (!modelLabel) {
        return;
      }
      this.DeviceModelInterface_Refesh(modelLabel);
      this.CetInterface_query.queryTrigger_in = new Date().getTime();
    },
    lineSegmentwithSwitchIdDisabled: {
      immediate: true,
      handler: function (val) {
        let vm = this;
        let inputData = vm.CetForm_Device.inputData_in;
        if (val && inputData.hasOwnProperty("linesegmentwithswitch_id")) {
          inputData.linesegmentwithswitch_id = null;
        }
      }
    }
  },
  methods: {
    CetInterface_query_result_out(val) {
      this.deviceclassificationOptions = val;
    },
    CetDialog_Device_open_out(val) {},
    CetDialog_Device_close_out(val) {},

    // 设备表单相关函数
    CetForm_Device_currentData_out(val) {},
    CetForm_Device_saveData_out(val) {},
    CetForm_Device_finishData_out(val) {
      this.$emit("deviceChanged_out", val);
      this.close();
    },
    CetForm_Device_finishTrigger_out(val) {},

    ElSelect_DeviceType_change_out(val) {},

    // 设备详情接口组件相关函数
    CetInterface_DeviceDetail_finishTrigger_out(val) {},
    CetInterface_DeviceDetail_failTrigger_out(val) {},
    CetInterface_DeviceDetail_totalNum_out(val) {},
    CetInterface_DeviceDetail_result_out(val) {
      let vm = this;
      if (!val) {
        return;
      }

      vm.CetInterface_DeviceDetail_RefreshSuccessfully(val[0]);
    },

    // 显示地图定位弹窗
    showLocatePositionDialog() {
      let vm = this;
      vm.$nextTick(() => {
        vm.LocatePositionDialog.openTrigger_in = new Date().getTime();
      });
    },

    // 获取设备模型
    DeviceModelInterface_Refesh(label) {
      let vm = this;
      vm.DeviceModelInterface.deviceLabel_in = label;
      vm.$nextTick(() => {
        vm.DeviceModelInterface.refreshTrigger_in = new Date().getTime();
      });
    },

    // 获取设备模型后需要刷新设备详情
    DeviceModelInterface_DeviceModel_out(val) {
      let vm = this;
      //表计模型-能源类型默认选中网络信息号energytype=26;已和张壮确认2022-7-18
      if (val.label === "meter") {
        let propertyList = val.propertyList || [];
        propertyList.forEach(item => {
          if (item.dataType === "energytype") {
            const enumList = item.options || [];
            let find_26 = enumList.find(item => item.id === 26);
            if (find_26) {
              item.defaultValue = 26;
            }
          }
        });
      }
      // 屏蔽经纬度和安装位置
      if (
        vm._.findIndex(val.propertyList, ["propertyLabel", "longitude"]) !== -1
      ) {
        val.propertyList.splice(
          vm._.findIndex(val.propertyList, ["propertyLabel", "longitude"]),
          1
        );
      }
      if (
        vm._.findIndex(val.propertyList, ["propertyLabel", "latitude"]) !== -1
      ) {
        val.propertyList.splice(
          vm._.findIndex(val.propertyList, ["propertyLabel", "latitude"]),
          1
        );
      }
      if (
        vm._.findIndex(val.propertyList, ["propertyLabel", "location"]) !== -1
      ) {
        val.propertyList.splice(
          vm._.findIndex(val.propertyList, ["propertyLabel", "location"]),
          1
        );
      }
      let propertyList = vm.formatPropertyList(val.propertyList);

      propertyList.forEach(item => {
        if (item.propertyLabel === "name") {
          item.nullable = false;
          item.required = true;
        }
      });

      vm.DevicePropertyList = propertyList;
      vm.resetFormConfig(vm.DevicePropertyList);
      vm.CetInterface_DeviceDetail_Refresh();
    },

    // 格式化模型属性列表
    formatPropertyList(propertyList) {
      let vm = this;
      let formattedPropertyList = vm._.cloneDeep(propertyList) || [];

      // 查看需不需要配置经纬度
      let lngProp = vm._.find(formattedPropertyList, {
        propertyLabel: "longitude"
      });
      let latProp = vm._.find(formattedPropertyList, {
        propertyLabel: "latitude"
      });
      let locationProp = vm._.find(formattedPropertyList, {
        propertyLabel: "location"
      });
      if (lngProp && latProp) {
        lngProp.disabled = true;
        latProp.disabled = true;

        let _locationIndex;
        if (locationProp) {
          _locationIndex = formattedPropertyList.indexOf(locationProp) + 1;
        } else {
          _locationIndex = Math.min(
            formattedPropertyList.indexOf(lngProp),
            formattedPropertyList.indexOf(latProp)
          );
        }

        formattedPropertyList.splice(_locationIndex, 0, {
          type: "_location",
          detailedDataType: "_location",
          name: $T("经纬度"),
          options: null,
          precision: null,

          alias: $T("经纬度"),
          defaultValue: null,
          required: lngProp.required || latProp.required,
          propertyLabel: "_location",
          maxLength: null,
          rangeMax: null,
          rangeMin: null
        });
      }

      // 按照需求，删除设备启用状态、上次检修日期、上次预检日期
      let targetIndex = vm._.findIndex(formattedPropertyList, {
        propertyLabel: "status"
      });
      if (targetIndex > -1) {
        formattedPropertyList.splice(targetIndex, 1);
      }

      targetIndex = vm._.findIndex(formattedPropertyList, {
        propertyLabel: "lastpretestdate"
      });
      if (targetIndex > -1) {
        formattedPropertyList.splice(targetIndex, 1);
      }

      targetIndex = vm._.findIndex(formattedPropertyList, {
        propertyLabel: "lastoverhauldate"
      });
      if (targetIndex > -1) {
        formattedPropertyList.splice(targetIndex, 1);
      }

      // 不需要显示所属低压馈电柜
      targetIndex = vm._.findIndex(formattedPropertyList, {
        propertyLabel: "linesegmentwithswitch_id"
      });
      if (targetIndex > -1) {
        if (vm.inLowVoltageCabinet) {
          formattedPropertyList[targetIndex].defaultValue =
            vm.lowVoltageCabinetData_in.id;
        } else {
          formattedPropertyList[targetIndex].defaultValue = null;
        }
        formattedPropertyList[targetIndex].required = true;
      }
      //调整描述输入框到最后
      let descriptionIndex = vm._.findIndex(formattedPropertyList, {
        propertyLabel: "description"
      });
      if (descriptionIndex !== -1) {
        let descriptionObj = formattedPropertyList.splice(
          descriptionIndex,
          1
        )[0];
        formattedPropertyList.push(descriptionObj);
      }
      return formattedPropertyList;
    },

    // 重置表单设置
    resetFormConfig(propertyList) {
      let vm = this;

      // 需要重置数据
      vm.CetForm_Device.data = {};

      // 重置dataIndex和rules
      let dataIndex = ["id", "roomid", "roomname", "modelLabel"];
      let rules = {};
      let inputData = {
        id: null,
        roomid: null,
        roomname: "",
        modelLabel: ""
      };
      propertyList.forEach(propItem => {
        let label = propItem.propertyLabel;
        dataIndex.push(label);
        if (propItem.propertyLabel == "deviceclassification") {
          // 设备归类转成null
          inputData[label] = null;
          this.$nextTick(() => {
            vm.CetForm_Device.inputData_in.deviceclassification = null;
          });
        } else {
          inputData[label] = propItem.defaultValue;
        }

        if (!propItem.required) {
          return true;
        }

        if (label === "measureids") {
          rules[label] = [
            {
              required: true,
              type: "array",
              message: $T("{0}不能为空", propItem.alias),
              trigger: "blur"
            }
          ];
        } else if (label === "linesegmentwithswitch_id") {
          rules[label] = [
            {
              required: true,
              message: $T("{0}不能为空", propItem.alias),
              trigger: "blur",
              validator: (rule, value, callback) => {
                if (vm.lineSegmentwithSwitchIdDisabled) {
                  callback();
                  return;
                }

                if (vm._.isNil(value)) {
                  callback(new Error());
                  return;
                }

                callback();
              }
            }
          ];
        } else {
          rules[label] = [
            {
              required: true,
              message: $T("{0}不能为空", propItem.alias),
              trigger: "blur"
            }
          ];
        }
      });

      vm.CetForm_Device.dataConfig.dataIndex = dataIndex;
      vm.CetForm_Device.inputData_in = inputData;
      vm.CetForm_Device.rules = rules;
    },

    // 刷新设备详情
    CetInterface_DeviceDetail_Refresh() {
      let vm = this;

      if (!vm.deviceData_in) {
        vm.CetInterface_DeviceDetail_RefreshSuccessfully(null);
        return;
      }

      vm.CetInterface_DeviceDetail.dataConfig.modelLabel =
        vm.deviceData_in.modelLabel;
      vm.CetInterface_DeviceDetail.queryId_in = vm.deviceData_in.id;

      vm.$nextTick(() => {
        vm.CetInterface_DeviceDetail.queryTrigger_in = new Date().getTime();
      });
    },

    // 重置数据
    resetData(deviceDetailData) {
      let vm = this;
      let inputData = vm.CetForm_Device.inputData_in;

      //设置modelLabel
      inputData.modelLabel = vm.DeviceModelInterface.deviceLabel_in;

      // 设置通常属性
      let propertyList = vm.DevicePropertyList;
      propertyList.forEach(propItem => {
        let label = propItem.propertyLabel;

        if (deviceDetailData) {
          inputData[label] = deviceDetailData[label];
          return true;
        }

        inputData[label] = propItem.defaultValue;
      });

      // 设置地理属性，新增情况下默认显示配电室的位置。
      if (deviceDetailData) {
        vm.updateLoacation();
      } else {
        vm.handleLocationOut(
          [vm.roomData_in.longitude, vm.roomData_in.latitude],
          vm.roomData_in.address
        );
      }

      // 设置id, measureids
      if (vm.isEditMode_in && deviceDetailData) {
        inputData.id = deviceDetailData.id;
        inputData.measureids = deviceDetailData.measureids;
      } else {
        inputData.id = null;
        inputData.measureids = [];
      }

      // 设置配电室信息
      inputData.roomid = vm.roomData_in.id;
      inputData.roomname = vm.roomData_in.name;

      // 刷新文件和文档组件的显示
      vm.CustomUploader_DevicePic.defaultFileName_in =
        inputData.pic || inputData.picture || "";
      vm.CustomUploader_DevicePic.currentFile = null;
      vm.CustomUploader_DevicePic.resetTrigger_in = new Date().getTime();
      vm.CustomUploader_DeviceDoc.defaultFileName_in = inputData.document || "";
      vm.CustomUploader_DeviceDoc.currentFile = null;
      vm.CustomUploader_DeviceDoc.files = [];
      vm.CustomUploader_DeviceDoc.resetTrigger_in = new Date().getTime();
    },

    // 成功获取设备详情后需要打开窗口
    CetInterface_DeviceDetail_RefreshSuccessfully(deviceDetailData) {
      let vm = this;
      vm.resetData(deviceDetailData);

      // 编辑和复制新建不需要获取默认图片和文档
      if (vm.isEditMode_in || deviceDetailData) {
        vm.CetDialog_Device_Open();
      } else {
        vm.CetDialog_Device_Open();
      }
    },
    // 打开窗口
    CetDialog_Device_Open() {
      let vm = this;
      vm.$nextTick(() => {
        vm.CetDialog_Device.openTrigger_in = new Date().getTime();
      });
    },

    // 打开窗口后需要刷新巡检内容
    CetDialog_Device_OpenSuccessfully() {},

    // 打开弹窗
    openDialog() {
      let vm = this;
      vm.$nextTick(() => {
        vm.CetDialog_Device.openTrigger_in = new Date().getTime();
      });
    },

    // 新建或编辑表单
    addOrEditDevice() {
      let vm = this;
      vm.uploadPicAndDoc().then(() => {
        vm.CetForm_Device.data.children = [
          {
            id: this.roomData_in.id,
            modelLabel: this.roomData_in.modelLabel
          }
        ];
        if (vm.CetForm_Device.data.hasOwnProperty("document")) {
          if (!vm.CetForm_Device.data.document) {
            vm.CetForm_Device.data.document = "";
          }
        }
        vm.CetForm_Device.dataConfig.modelLabel =
          vm.CetForm_Device.inputData_in.modelLabel;
        vm.$nextTick(() => {
          vm.CetForm_Device.saveTrigger_in = new Date().getTime();
        });
      });
    },

    // 关闭弹窗
    close() {
      this.CetDialog_Device.closeTrigger_in = new Date().getTime();
    },

    // 更新自定义地址信息，_location为自定义的属性
    updateLoacation() {
      let vm = this;
      let inputData = vm.CetForm_Device.inputData_in;

      if (!inputData.hasOwnProperty("_location")) {
        return;
      }

      let lng = parseFloat(inputData.longitude);
      let lat = parseFloat(inputData.latitude);
      let location, _location;

      if (isNaN(lng) || isNaN(lat)) {
        location = null;
        _location = null;
      } else {
        location = [lng, lat];
        _location = lng.toFixed2(6) + "," + lat.toFixed2(6);
      }

      inputData._location = _location;
      vm.LocatePositionDialog.location_in = location;
    },

    // 更新地址信息
    handleLocationOut(loc, address) {
      let vm = this;
      let inputData = vm.CetForm_Device.inputData_in;

      if (address && inputData.hasOwnProperty("location")) {
        inputData.location = address;
      }

      if (!inputData.hasOwnProperty("_location")) {
        return;
      }

      let lng = parseFloat(loc[0]);
      let lat = parseFloat(loc[1]);

      if (isNaN(lng) || isNaN(lat)) {
        inputData.longitude = null;
        inputData.latitude = null;
      } else {
        inputData.longitude = lng;
        inputData.latitude = lat;
      }

      vm.updateLoacation();
    },

    // 处理低压馈电柜输出
    handleLowVoltageCabinetData_out(data) {
      let vm = this;
      let inputData = vm.CetForm_Device.inputData_in;
      let id = data ? data.id : null;
      if (inputData.hasOwnProperty("linesegmentwithswitch_id")) {
        inputData["linesegmentwithswitch_id"] = id;
      }
    },

    // 弹出母线选择弹窗
    showBusbarSegSelectionDialog(busbarMarkType) {
      let vm = this;
      let id;

      if (busbarMarkType === 1) {
        id = vm.CetForm_Device.inputData_in.busbarsegi;
      } else {
        id = vm.CetForm_Device.inputData_in.busbarsegii;
      }

      vm.BusbarSegSelectionDialog.defaultId_in = id;
      vm.BusbarSegSelectionDialog.busbarMarkType_in = busbarMarkType;
      vm.BusbarSegSelectionDialog.roomId_in =
        vm.CetForm_Device.inputData_in.roomid;
      vm.BusbarSegSelectionDialog.openTrigger_in = new Date().getTime();
    },

    // 处理母线对象输出
    handleBusbarSegData_out(data) {
      let vm = this;
      let inputData = vm.CetForm_Device.inputData_in;
      let id = data ? data.id : null;

      if (data.type === 1 && inputData.hasOwnProperty("busbarsegi")) {
        inputData["busbarsegi"] = id;
      }

      if (data.type === 2 && inputData.hasOwnProperty("busbarsegii")) {
        inputData["busbarsegii"] = id;
      }
    },

    // 处理表计对象输出
    handleMetersData_out(data) {
      let vm = this;
      vm.CetForm_Device.inputData_in.measureids = vm._.cloneDeep(data);
    },

    // 图片文件发生变化
    CustomUploader_DevicePic_fileChanged_out(file) {
      let vm = this;
      let inputData = vm.CetForm_Device.inputData_in;

      let picLabel = "";
      if (inputData.hasOwnProperty("picture")) {
        picLabel = "picture";
      }
      if (inputData.hasOwnProperty("pic")) {
        picLabel = "pic";
      }

      if (picLabel) {
        vm.CustomUploader_DevicePic.currentFile = file;
        vm.CetForm_Device.inputData_in[picLabel] = null;
      }
    },

    // 文档文件发生变化
    CustomUploader_DeviceDoc_fileChanged_out1(file) {
      let vm = this;
      let inputData = vm.CetForm_Device.inputData_in;

      let docLabel = "";
      if (inputData.hasOwnProperty("document")) {
        docLabel = "document";
      }

      if (docLabel) {
        vm.CustomUploader_DeviceDoc.currentFile = file;
        vm.CetForm_Device.inputData_in[docLabel] = null;
      }
    },

    // 文档文件发生变化
    CustomUploader_DeviceDoc_fileChanged_out(file) {
      let vm = this;
      let inputData = vm.CetForm_Device.inputData_in;

      let docLabel = "";
      if (inputData.hasOwnProperty("document")) {
        docLabel = "document";
      }

      if (docLabel) {
        if (vm.CustomUploader_DeviceDoc.files.length < 5) {
          vm.CustomUploader_DeviceDoc.files.push(file);
          vm.CetForm_Device.inputData_in[docLabel] = null;
        } else {
          this.$message($T("最多支持上传五个文档"));
        }
      }
    },
    deleteNode(id) {
      this.CustomUploader_DeviceDoc.files =
        this.CustomUploader_DeviceDoc.files.filter((item, i) => {
          return i !== id;
        });
    },

    // 上传图片和文档
    uploadPicAndDoc() {
      let vm = this;
      let ps = [];

      let inputData = vm.CetForm_Device.inputData_in;

      // 图片上传
      let picLabel = "";
      if (inputData.hasOwnProperty("picture")) {
        picLabel = "picture";
      }
      if (inputData.hasOwnProperty("pic")) {
        picLabel = "pic";
      }

      if (picLabel && vm.CustomUploader_DevicePic.currentFile) {
        let p = new Promise(resolve => {
          customApi["upload"](vm.CustomUploader_DevicePic.currentFile)
            .then(res => {
              if (res.code == 0) {
                inputData[picLabel] = res.data;
                vm.CustomUploader_DevicePic.defaultFileName_in = res.data;
                vm.CustomUploader_DevicePic.currentFile = null;
                vm.CustomUploader_DevicePic.resetTrigger_in =
                  new Date().getTime();
                resolve();
              } else {
                reject();
              }
            })
            .catch(() => {
              reject();
            });
        });
        ps.push(p);
      }

      // 文档上传
      let docLabel = "";
      if (inputData.hasOwnProperty("document")) {
        docLabel = "document";
      }
      let files = vm.CustomUploader_DeviceDoc.files;
      let doc = [];
      // if (docLabel && vm.CustomUploader_DeviceDoc.currentFile) {
      if (docLabel && files.length > 0) {
        files.forEach((item, i) => {
          let p = new Promise(resolve => {
            customApi["upload"](item)
              .then(res => {
                if (res.code == 0) {
                  doc.push(res.data);
                  inputData[docLabel] = doc.join(",");
                  // vm.CetForm_Device.data.document = doc.join(",");
                  vm.CustomUploader_DeviceDoc.resetTrigger_in =
                    new Date().getTime();
                  resolve();
                } else {
                  reject();
                }
              })
              .catch(() => {
                reject();
              });
          });
          ps.push(p);
        });
      }
      return ps.length ? Promise.all(ps) : Promise.resolve();
    }
  },
  mounted() {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
    box-sizing: border-box;
  }
  .rowBox {
    @include padding(J4 J4 J2 J4);
    padding-bottom: mh-get(J4) - 18px;
  }
}
// .device-config-dialog :deep(.el-dialog__body) {
//   height: 420px;
// }

// .custom-form-item :deep(.el-input__prefix),
// .custom-form-item :deep(.el-icon-date) {
//   line-height: 30px;
// }
// .custom-form-item :deep(.el-form-item__label) {
//   height: 100%;
//   padding-left: 10px;
// }
// .custom-form-item :deep(.el-input__inner) {
//   border-left: 0;
//   border-top-left-radius: 0;
//   border-bottom-left-radius: 0;
//   vertical-align: top;
// }
// .gray-label :deep(.el-form-item__label) {
//   // width: 132px !important;
//   padding: 0 4px;
//   line-height: 30px;
//   @include background_color(BG1);
//   border: 1px solid;
//   @include border_color(B1);
//   border-radius: 4px 0 0 4px;
//   text-align: center;
// }

.device-config-dialog :deep(.el-tabs__content) {
  overflow-y: auto;
}
.device-property-container {
  display: inline-block;
  box-sizing: border-box;
  padding: 0 5px;
  width: calc(50% - 4px);
  vertical-align: top;
}
.device-property-container :deep(.el-button.is-disabled) {
  background-color: transparent;
}
.files {
  height: 50px;
  padding: 12px 10px;
  position: absolute;
  border: 1px solid;
  @include border_color(B1);
  border-top: none;
  border-radius: 0 0 4px 4px;
  top: 30px;
  right: 0;
  left: 0;
  // left: -154px;
  overflow: auto;
  .file {
    line-height: 32px;
    .del {
      cursor: pointer;
    }
  }
}
.overflow-auto {
  overflow: auto;
}
// .custom-form-item-vertical :deep(.el-form-item__label) {
//   display: none;
// }
// .custom-form-item-vertical :deep(.input-header) {
//   display: block;
//   padding: 0 10px;
//   @include background_color(BG1);
//   border: 1px solid;
//   @include border_color(B1);
//   border-radius: 4px 4px 0 0;
// }
// .custom-form-item-vertical :deep(.el-textarea__inner) {
//   border-top: 0;
//   border-radius: 0 0 4px 4px;
// }
</style>
