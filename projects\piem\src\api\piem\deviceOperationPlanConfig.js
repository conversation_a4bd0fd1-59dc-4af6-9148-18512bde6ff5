import fetch from "eem-utils/fetch";
let version = "v1";

// 查询压缩机运行方案
export function queryDeviceOperationScheme(data) {
  return fetch({
    url: `/piem/${version}/gascompressor/operationscheme/queryPageList`,
    method: `POST`,
    data
  });
}

// 查询压缩机运行方案详情
export function queryDeviceOperationSchemeDetails(data) {
  return fetch({
    url: `/piem/${version}/gascompressor/operationscheme/queryDetail`,
    method: `POST`,
    data
  });
}

// 更新压缩机运行方案
export function updateDeviceOperationScheme(data) {
  return fetch({
    url: `/piem/${version}/gascompressor/operationscheme/upsertScheme`,
    method: `POST`,
    data
  });
}

// 查询压缩机运行工况详情
export function queryWorkingConditionDetails(data) {
  return fetch({
    url: `/piem/${version}/gascompressor/adjustmentoperation/queryDetail`,
    method: `POST`,
    data
  });
}
