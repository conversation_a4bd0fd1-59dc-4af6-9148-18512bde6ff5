<template>
  <!-- 1弹窗组件 -->
  <div>
    <CetDialog
      class="CetDialog eem-common"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
    >
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </span>
      <span slot="title" class="el-dialog__title" v-if="!en">
        项目节点（共
        <span class="fcZS">{{ projectNum }}</span>
        个）
      </span>
      <span slot="title" class="el-dialog__title" v-if="en">
        {{ $T("项目节点（共{0}个）", projectNum) }}
      </span>
      <div class="tableBox eem-cont-c1">
        <CetTable
          :data.sync="CetTable_project.data"
          :dynamicInput.sync="CetTable_project.dynamicInput"
          v-bind="CetTable_project"
          v-on="CetTable_project.event"
        >
          <template v-for="item in Columns_project">
            <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
          </template>
        </CetTable>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import { httping } from "@omega/http";
export default {
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },

  computed: {
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },

  data() {
    return {
      CetDialog_1: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      projectNum: 0,
      CetTable_project: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 10,
          layout: "sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        highlightCurrentRow: false,
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      },
      Columns_project: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "41" //绝对宽度
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("项目名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true
        }
      ]
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      this.init();
      vm.CetDialog_1.openTrigger_in = val;
    }
  },

  methods: {
    init() {
      // 获取所属项目
      var vm = this;
      if (!vm.inputData_in) {
        return;
      }
      var tenantId = vm.inputData_in.id;
      var params = {
        rootID: 0,
        rootLabel: "project",
        treeReturnEnable: true
      };
      httping({
        url: `/eem-service/v1/auth/cloud/projects?tenantId=${tenantId}&isQueryChild=true`,
        method: "POST",
        data: params
      }).then(function (res) {
        if (res.code === 0) {
          vm.CetTable_project.data = vm._.get(res, "data", []) || [];
          vm.projectNum = vm.CetTable_project.data.length;
        }
      });
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
      this.$emit("finishTrigger_out", val);
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep() {
    .el-dialog__body {
      @include background_color(BG, !important);
      @include padding(J1);
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }
  }
  .tableBox {
    :deep(.el-table__body-wrapper) {
      height: 400px !important;
    }
  }
}
</style>
