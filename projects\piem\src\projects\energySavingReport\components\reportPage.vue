<template>
  <div class="page">
    <div class="head mbJ4">
      <div class="left"></div>
      <div class="topTitle">
        <span>
          {{ $T("浙江油田西南采气厂月节能评估报告") }}
        </span>
        <span class="fr time">
          {{ $moment(queryTime).format("YYYY年MM月") }}{{ $T("月报") }}
        </span>
      </div>
      <div class="pageNum mlJ2">{{ pageNum }}</div>
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "reportPage",
  components: {},
  props: {
    pageNum: {
      type: String
    },
    queryTime: {
      type: Number
    }
  },
  data() {
    return {};
  },
  watch: {},
  methods: {}
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  padding: 36px;
  box-sizing: border-box;
}
.head {
  height: 28px;
  display: flex;
  .left {
    height: 100%;
    width: 8px;
    border-left: 4px solid rgba(84, 192, 129, 1);
    border-right: 4px solid rgba(127, 208, 160, 1);
  }
  .topTitle {
    line-height: 28px;
    text-indent: 10px;
    color: #13171f;
    font-size: 14px;
    border-bottom: 1px solid rgba(127, 208, 160, 1);
    flex: 1;
    margin-left: 4px;
    .time {
      font-size: 10px;
    }
  }
}
.content {
  height: calc(100% - 28px - 24px);
}
.pageNum {
  line-height: 28px;
  font-size: 16px;
  color: #989898;
}
</style>
