<template>
  <div class="fullheight">
    <div id="container" ref="container"></div>
  </div>
</template>

<script>
import G6 from "@antv/g6";
export default {
  name: "TopologyChart",
  props: {
    inputData_in: {
      type: [Object, Array]
    }
  },
  components: {},
  data() {
    return {
      graph: null
    };
  },
  watch: {
    inputData_in: {
      handler: function (val) {
        this.$nextTick(() => {
          this.paintChart();
        });
      },
      deep: true
    }
  },
  methods: {
    // 初始化图形配置
    initGraphConf() {
      if (this.graph) return;
      var width = this.$refs.container.scrollWidth;
      var height = this.$refs.container.scrollHeight || 500;
      this.graph = new G6.Graph({
        container: "container",
        width,
        height,
        layout: {
          type: "dagre",
          ranksep: 70,
          rankdir: "LR"
        },
        defaultNode: {
          size: [150, 0],
          type: "rect-jsx"
        },
        defaultEdge: {
          type: "line-arrow",
          style: {
            endArrow: {
              path: G6.Arrow.triangle(),
              fill: "#F6BD16"
            },
            lineWidth: 2,
            stroke: "#F6BD16"
          }
        },
        modes: {
          default: [
            "drag-canvas",
            "drag-node",
            "zoom-canvas",
            {
              type: "tooltip",
              formatText: this.formatLabel
            }
          ]
        },
        fitView: true
      });
    },
    formatLabel(params) {
      return params.label;
    },
    // 获取图表数据
    getChartData() {
      let data = this._.cloneDeep(this.inputData_in);
      if (this._.isEmpty(data)) data = { nodes: [], edges: [] };
      return data;
    },
    // 绘制图形
    paintChart() {
      const data = this.getChartData();
      if (this._.isEmpty(this.graph)) this.initGraphConf();
      this.graph.data(data);
      this.graph.render();
    },
    setData() {
      const data = this.getChartData();
      this.$nextTick(() => {
        if (this._.isEmpty(this.graph)) this.initGraphConf();
        this.graph.changeData(data);
        this.graph.layout();
      });
    },
    // 超长文本显示
    fittingString(str, maxWidth, fontSize) {
      const ellipsis = "...";
      const ellipsisLength = G6.Util.getTextSize(ellipsis, fontSize)[0];
      let currentWidth = 0;
      let res = str;
      const pattern = new RegExp("[\u4E00-\u9FA5]+"); // distinguish the Chinese charactors and letters
      if (!str) return;
      str.split("").forEach((letter, i) => {
        if (currentWidth > maxWidth - ellipsisLength) return;
        if (pattern.test(letter)) {
          // Chinese charactors
          currentWidth += fontSize;
        } else {
          // get the width of single letter according to the fontSize
          currentWidth += G6.Util.getLetterWidth(letter, fontSize);
        }
        if (currentWidth > maxWidth - ellipsisLength) {
          res = `${str.substr(0, i)}${ellipsis}`;
        }
      });
      return res;
    }
  },
  mounted() {
    const _this = this;
    const currentTheme = localStorage.getItem("omega_theme");
    let textColor = "";
    let bgColor = "";
    if (["dark", "blue"].includes(currentTheme)) {
      textColor = "#f0f1f2";
      bgColor = "#0e1b47";
    } else {
      textColor = " #242424";
      bgColor = " #ffffff";
    }
    G6.registerEdge(
      "line-arrow",
      {
        getPath(points) {
          const startPoint = points[0];
          const endPoint = points[1];
          return [
            ["M", startPoint.x, startPoint.y],
            ["L", startPoint.x + 40, startPoint.y],
            ["L", startPoint.x + 40, endPoint.y],
            ["L", endPoint.x, endPoint.y]
          ];
        }
      },
      "line"
    );
    G6.registerNode("rect-jsx", {
      jsx: cfg => `
        <group>
          <circle style={{
            stroke: '#2D89AE',
            lineWidth: 2,
            height: 100,
            lineDash: ${cfg.virtual ? "[4,2]" : "[4,0]"},
            r: 35,
            marginTop: 70,
            fill:  ${
              cfg.virtual
                ? "#989898"
                : cfg.communicationStatus === 1 || cfg.communicationStatus === 0
                ? "#06C25D"
                : "#EB3C3E"
            }
          }} name="circle">
              <text style={{ marginTop: -18, fill: '#fff',textAlign: 'center', fontWeight: 'bold',fontSize: '12',}}>{{codename}}</text>
              <rect style={{
                width: 68,
                height: 1,
                marginLeft: -34,
                marginTop: -16,
                stroke: '#ffffff',
                fill: '#ffffff',
              }}>
              </rect>
              <text style={{ marginTop: -15,fill: '#fff',textAlign: 'center',fontWeight: 'bold',}}>{{accuracylevel}}</text>
          </circle>
          <rect style={{
              width: 10,
              height: 1,
              marginLeft: 37,
              marginTop: 0,
              stroke: '#F3E38C',
              fill: '#F3E38C',
            }}>
          </rect>

          <rect style={{
            width: 200,
            height: 20,
            marginTop: -10,
            marginLeft: 48,
            stroke: '#4B7AB1',
            fill: ${bgColor},
          }}>
            <text style={{ marginLeft: 70 ,fill: ${textColor}, }}>{{${_this.fittingString(
        cfg.label,
        200,
        14
      )}}}</text>
          </rect>
        </group>`,
      getAnchorPoints: function getAnchorPoints() {
        return [
          [0, 0.5],
          [1, 0.5]
        ];
      }
    });
    this.$nextTick(() => {
      this.paintChart();
    });
  },
  activated() {},
  deactivated() {},
  beforeDestroy() {}
};
</script>

<style lang="scss" scoped>
#container {
  width: 100%;
  height: 100%;
}
</style>
<style lang="scss">
.g6-tooltip {
  white-space: nowrap;
  transition: left 0.4s cubic-bezier(0.23, 1, 0.32, 1) 0s,
    top 0.4s cubic-bezier(0.23, 1, 0.32, 1) 0s;
  @include background_color(BG11);
  font-size: 14px;
  border-radius: 4px;
  @include font_color(T1);
  padding: 10px;
  // min-width: 300px;
  // min-height: 200px;
  border: 1px solid;
  @include border_color(B2);
  pointer-events: none;
}
</style>
