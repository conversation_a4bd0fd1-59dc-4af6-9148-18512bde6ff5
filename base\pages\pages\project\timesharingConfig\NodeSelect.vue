<template>
  <!-- 1弹窗组件 -->
  <CetDialog
    v-bind="CetDialog_1"
    v-on="CetDialog_1.event"
    class="CetDialog small"
  >
    <div class="eem-cont-c1">
      <el-checkbox class="mbJ3" v-model="checked" @change="checkedChange">
        {{ $T("默认选中子节点") }}
      </el-checkbox>
      <CetGiantTree
        class="giantTree"
        ref="giantTree1"
        v-show="!checked"
        v-bind="CetGiantTree_1"
        v-on="CetGiantTree_1.event"
      ></CetGiantTree>
      <CetGiantTree
        class="giantTree"
        ref="giantTree2"
        v-show="checked"
        v-bind="CetGiantTree_2"
        v-on="CetGiantTree_2.event"
      ></CetGiantTree>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import TREE_PARAMS from "@/store/treeParams.js";
import { httping } from "@omega/http";

export default {
  name: "NodeSelect",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },

  computed: {
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    }
  },

  data() {
    return {
      currentNode: null,
      CetDialog_1: {
        title: $T("关联节点"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      checked: false,
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out //勾选节点输出
        }
      },
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_2_checkedNodes_out //勾选节点输出
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.checked = false;
      this.getTreeData();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    checkedChange() {
      const vm = this;
      let checkNodes = vm._.cloneDeep(vm.checkNodes);
      setTimeout(() => {
        $(this.$refs.giantTree1.$el).find("#giantTree").scrollTop(0);
        $(this.$refs.giantTree2.$el).find("#giantTree").scrollTop(0);
        if (vm.checked) {
          vm.CetGiantTree_2.checkedNodes = checkNodes;
          if (!checkNodes.length) {
            vm.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
          }
        } else {
          vm.CetGiantTree_1.checkedNodes = checkNodes;
          if (!checkNodes.length) {
            vm.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
          }
        }
      }, 0);
    },
    // 获取节点树
    getTreeData() {
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [...TREE_PARAMS.timesharingConfig],
        treeReturnEnable: true
      };
      httping({
        url: "/eem-service/v1/node/nodeTree/simple",
        method: "POST",
        data
      }).then(res => {
        if (res.code === 0 && res.data.length > 0) {
          // 过滤掉项目下的开关柜或一段线
          var data = [];
          res.data.forEach((item, index) => {
            var obj = _this._.cloneDeep(item);
            obj.children = [];
            data.push(obj);
            if (item.children && item.children.length > 0) {
              item.children.forEach(ite => {
                if (ite.modelLabel != "linesegmentwithswitch") {
                  data[index].children.push(ite);
                }
              });
            }
          });
          _this.getNodeByTSScheme(data);
        }
      });
    },
    // 获取关联节点
    getNodeByTSScheme(treeData) {
      this.checkNodes = [];
      httping({
        url: `/eem-service/v1/schemeConfig/nodeByTSScheme?schemeId=${this.inputData_in.id}`,
        method: "GET"
      }).then(res => {
        if (res.code === 0 && res.data.length > 0) {
          var checkedNodes = [];
          res.data.forEach(item => {
            checkedNodes.push({
              id: item.objectid,
              modelLabel: item.objectlabel,
              tree_id: item.objectlabel + "_" + item.objectid
            });
          });
          this.CetGiantTree_1.checkedNodes = this._.cloneDeep(checkedNodes);
          this.checkNodes = checkedNodes;
          setTimeout(() => {
            this.expandNode(
              checkedNodes,
              "tree_id",
              this.$refs.giantTree1.ztreeObj
            );
            this.expandNode(
              checkedNodes,
              "tree_id",
              this.$refs.giantTree2.ztreeObj
            );
          }, 0);
        } else {
          this.CetGiantTree_1.checkedNodes = [];
          this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
        }
        this.CetGiantTree_1.inputData_in = treeData;
        this.CetGiantTree_2.inputData_in = treeData;
      });
    },
    // 展开节点
    expandNode(nodes, key, ztreeObj) {
      nodes.forEach(item => {
        let node = ztreeObj.getNodeByParam(key, item[key]);
        let parentNodes = [],
          parentNode = node && node.getParentNode();
        while (parentNode) {
          parentNodes.push(parentNode);
          parentNode = parentNode.getParentNode();
        }
        parentNodes.forEach(i => {
          ztreeObj.expandNode(i, true);
        });
      });
      $(this.$refs.giantTree1.$el).find("#giantTree").scrollTop(0);
      $(this.$refs.giantTree2.$el).find("#giantTree").scrollTop(0);
    },
    CetGiantTree_1_checkedNodes_out(val) {
      this.checkNodes = this._.cloneDeep(val);
    },
    CetGiantTree_2_checkedNodes_out(val) {
      this.checkNodes = this._.cloneDeep(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      var data = [];
      this.checkNodes.forEach(item => {
        data.push({
          id: item.id,
          modelLabel: item.modelLabel,
          name: item.name
        });
      });
      httping({
        url: `/eem-service/v1/schemeConfig/timeShareRelationship?schemeId=${this.inputData_in.id}`,
        method: "PUT",
        data
      }).then(res => {
        if (res.code === 0) {
          this.$message({
            message: $T("保存成功"),
            type: "success"
          });
          this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
        }
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  .giantTree {
    height: 400px;
  }
}
</style>
