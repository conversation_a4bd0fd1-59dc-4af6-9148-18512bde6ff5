<template>
  <div class="page">
    <div class="common-title-H3 mbJ3 mlJ3">{{ $T("能效趋势") }}</div>
    <CetChart
      style="height: calc(100% - 42px)"
      v-bind="CetChart_trend"
    ></CetChart>
  </div>
</template>

<script>
import common from "eem-utils/common";
export default {
  name: "efficiencyTrend",
  components: {},
  props: {
    efficiencyTrendData: {
      type: Array
    }
  },
  data() {
    return {
      // trend组件
      CetChart_trend: {
        //组件输入项
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            },
            confine: true,
            formatter: val => {
              if (_.isEmpty(val)) return;
              const head = `<div>${val[0]?.axisValue}</div>`;
              const conent = val.map(item => {
                const unit =
                  this.CetChart_trend.options.series[item.seriesIndex].unit;
                const key =
                  this.CetChart_trend.options.series[item.seriesIndex].encode.y;
                return `<div style='margin-top: 4px'>
                  <span>${item.marker}</span>
                  <span>${item.seriesName}</span>
                  <span style="float: right; margin-left: 4px">${unit}</span>
                  <span style="font-size: 14px; font-weight: 600; float: right; margin-left: 24px">${item.data[key]}</span>
                  </div>`;
              });
              return head + conent.join("");
            }
          },
          grid: {
            top: 60,
            bottom: 20,
            left: 60,
            right: 120
          },
          legend: {
            show: true
          },
          xAxis: {
            type: "category",
            axisTick: { show: false }
          },
          yAxis: [
            {
              name: "单位(万立方米)",
              type: "value"
            },
            {
              name: "单位(kWh/万立方米)",
              type: "value"
            },
            {
              name: "单位(%)",
              type: "value",
              offset: 80
            }
          ],
          series: [
            {
              name: "输气量",
              type: "bar",
              barMaxWidth: 10,
              yAxisIndex: 0,
              smooth: true,
              showSymbol: false,
              unit: "万立方米",
              encode: {
                x: "logTime",
                y: "gasTransmissionVolume"
              }
            },
            {
              name: "单耗",
              type: "bar",
              barMaxWidth: 10,
              yAxisIndex: 1,
              smooth: true,
              showSymbol: false,
              unit: "kWh/万立方米",
              encode: {
                x: "logTime",
                y: "unitConsumption"
              }
            },
            {
              name: "机组效率",
              type: "line",
              yAxisIndex: 2,
              smooth: true,
              showSymbol: false,
              unit: "%",
              encode: {
                x: "logTime",
                y: "efficiency"
              }
            }
          ]
        }
      }
    };
  },
  watch: {
    efficiencyTrendData(val) {
      this.renderChart();
    },
    type(val) {
      this.renderChart();
    }
  },
  methods: {
    renderChart() {
      const list = this.efficiencyTrendData || [];
      this.CetChart_trend.inputData_in = list.map(item => {
        return {
          logTime: this.$moment(item.logTime).format("DD"),
          efficiency: common.formatNumberWithPrecision(item.efficiency, 2),
          gasTransmissionVolume: common.formatNumberWithPrecision(
            item.gasTransmissionVolume,
            2
          ),
          unitConsumption: common.formatNumberWithPrecision(
            item.unitConsumption,
            2
          )
        };
      });
    }
  },
  activated() {
    this.renderChart();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.title {
}
</style>
