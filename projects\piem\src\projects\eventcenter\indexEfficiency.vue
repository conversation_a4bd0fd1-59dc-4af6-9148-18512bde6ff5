<template>
  <Eventcenter :isJump1="isJump" :keepParams1="keepParams" />
</template>
<script>
import Eventcenter from "./index";
export default {
  components: {
    Eventcenter
  },
  data() {
    return {
      isJump: false,
      keepParams: {}
    };
  },
  deactivated() {
    this.isJump = false;
  },
  beforeRouteEnter(to, from, next) {
    if (from.name === "eventNumberRank") {
      next(vm => {
        vm.isJump = true;
        vm.keepParams = vm.$route.params.keepParams;
      });
    } else {
      next();
    }
  }
};
</script>
