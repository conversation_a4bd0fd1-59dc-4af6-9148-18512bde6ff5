<template>
  <div style="order: -2" @click="goBack" v-if="showGoBack" class="back">
    返回平台
  </div>
</template>

<script>
import { conf, router } from "@omega/app";
import { fileMenu } from "@/config/config";
import userUtil from "@omega/auth/auth/user.js";
import { find } from "eem-utils/util.js";
import { PageAccessPlugin } from "@omega/auth/plugins/pageAccess.js";
export default {
  data() {
    return {
      isProjectLevel: true
    };
  },
  computed: {
    showGoBack() {
      var vm = this;
      if (!vm.$store.state.systemCfg.isPlatformModel) {
        return false;
      }
      const usertype = vm.$store.state.userInfo.usertype;
      const currentModule = vm.$store.state.currentModule;
      if ([3, 4].includes(usertype)) return false;
      if (currentModule === "pf") return false;
      return true;
    }
  },
  methods: {
    goBack() {
      conf.setNavmenu(fileMenu("pfNavmenu"));
      let menu = conf.getNavmenu();
      this.$store.commit("setCurrentModule", "pf");
      const PageNodes = userUtil.getRolePageNodes();
      const pfHomePage = this.$store.state.systemCfg.pfHomePage;
      let path = "";
      //跳转到SystemCfg配置文件里面配置平台页面
      if (pfHomePage && PageNodes.find(i => i.id === pfHomePage)) {
        const item = find(menu, pfHomePage, {
          childKey: "subMenuList",
          valueKey: "permission"
        });
        path = item?.location;
      }
      //跳转到默认平台第一个页面
      if (!path) {
        const item = find(menu, "menuItem", {
          childKey: "subMenuList",
          valueKey: "type"
        });
        path = item?.location;
      }
      const pagePlugin = new PageAccessPlugin(
        {
          conf: conf,
          router: router
        },
        {}
      );
      pagePlugin.afterAppLogin();
      //如果都找不到跳转到项目导航页面
      this.$router.push({ path: path || "/projectnavigation" });
    }
  }
};
</script>

<style lang="scss">
.back {
  &:hover {
    // @include font_color(ZS);
    background-color: var(--BG2);
  }
  line-height: 32px;
  cursor: pointer;
}
</style>
