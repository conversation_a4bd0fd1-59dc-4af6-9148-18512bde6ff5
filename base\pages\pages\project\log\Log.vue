<template>
  <div class="page eem-common">
    <el-container style="height: 100%">
      <el-aside class="eem-aside" width="315px">
        <CetTree
          :selectNode.sync="CetTree_Left.selectNode"
          :checkedNodes.sync="CetTree_Left.checkedNodes"
          v-bind="CetTree_Left"
          v-on="CetTree_Left.event"
        ></CetTree>
      </el-aside>
      <el-container class="mlJ3 flex-column">
        <el-main class="flex-auto eem-container flex-column">
          <div class="mbJ3">
            <div class="fr flex-row">
              <div class="prJ1" style="line-height: inherit">
                <ElInput
                  suffix-icon="el-icon-search"
                  v-model="ElInput_1.value"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </div>
              <div class="mrJ1" style="line-height: inherit">
                <customElSelect
                  v-model="ElSelect_type.value"
                  v-bind="ElSelect_type"
                  v-on="ElSelect_type.event"
                  :prefix_in="$T('操作类型')"
                >
                  <ElOption
                    v-for="item in ElOption_type.options_in"
                    :key="item[ElOption_type.key]"
                    :label="item[ElOption_type.label]"
                    :value="item[ElOption_type.value]"
                    :disabled="item[ElOption_type.disabled]"
                  ></ElOption>
                </customElSelect>
              </div>

              <div class="clearfix prJ1" style="line-height: inherit">
                <!-- <span class="fl lh32">时间：</span> -->
                <!-- 向前查询按钮 -->
                <CetButton
                  class="fl"
                  v-bind="CetButton_prv"
                  v-on="CetButton_prv.event"
                ></CetButton>
                <CustomElDatePicker
                  class="fl plJ"
                  style="width: 200px !important"
                  v-model="CetDatePicker_1.val"
                  v-bind="CetDatePicker_1.config"
                  @change="CetDatePicker_1_dateVal_out"
                  type="date"
                  :placeholder="$T('选择日期')"
                  :prefix_in="$T('时间')"
                ></CustomElDatePicker>
                <!-- 向后查询按钮 -->
                <CetButton
                  class="fl mlJ"
                  v-bind="CetButton_next"
                  v-on="CetButton_next.event"
                ></CetButton>
              </div>
              <CetButton
                v-bind="CetButton_2"
                v-on="CetButton_2.event"
              ></CetButton>
            </div>
          </div>
          <div class="flex-column flex-auto">
            <div class="p0 flex-auto">
              <CetTable
                @sort-change="sortChange"
                :data.sync="CetTable_1.data"
                :dynamicInput.sync="CetTable_1.dynamicInput"
                v-bind="CetTable_1"
                v-on="CetTable_1.event"
              >
                <el-table-column
                  label="#"
                  type="index"
                  width="100"
                ></el-table-column>

                <!-- 根据模型数据绑定表格列 -->
                <template v-for="item in columnsArr">
                  <template>
                    <el-table-column
                      :key="item.key"
                      :show-overflow-tooltip="true"
                      :label="item.title"
                      :min-width="item.width"
                      :width="item.fixedwidth"
                      :prop="item.key"
                      :sortable="item.sortable"
                      :formatter="formatTable"
                      header-align="left"
                      align="left"
                    ></el-table-column>
                  </template>
                </template>
              </CetTable>
            </div>
            <div class="ptJ1 text-right">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentPageChange"
                :current-page.sync="currentPage"
                :page-size.sync="pageSize"
                :total="totalCount"
                :layout="pageLayout"
                :pageSizes="pageSizes"
              ></el-pagination>
            </div>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import common from "eem-utils/common";
import CustomElDatePicker from "eem-components/CustomElDatePicker.vue";
import customApi from "@/api/custom.js";
import { httping } from "@omega/http";
export default {
  name: "Log",
  components: {
    CustomElDatePicker
  },

  computed: {
    userInfo() {
      var vm = this;
      return vm.$store.state.userInfo;
    },
    projectTenantId() {
      var vm = this;
      return vm.$store.state.projectTenantId;
    }
  },

  data() {
    return {
      CetDatePicker_1: {
        disable_in: false,
        dateVal_out: "",
        startVal_out: "",
        endVal_out: "",
        val: this.$moment().startOf("day")._d,
        config: {
          type: "date",
          format: "yyyy-MM-dd",
          rangeSeparator: "-",
          pickerOptions: common.pickerOptions_earlierThanTomorrow,
          size: "small"
        }
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: true,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {
          width: "200px"
        },
        event: {
          change: this.ElInput_1_change_out,
          input: this.ElInput_1_input_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: true,
        title: $T("导出"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      columnsArr: [
        {
          title: $T("时间"),
          key: "operationtime",
          type: "date",
          sortable: "custom",
          width: 100
        },
        {
          title: $T("操作类型"),
          key: "operationtype$text",
          width: 100
        },
        {
          title: $T("操作人"),
          key: "operator",
          width: 100
        },
        {
          title: $T("描述"),
          key: "description",
          width: 100
        }
      ],
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        defaultSort: { prop: "operationtime", order: "descending" },
        event: {}
      },
      CetTree_Left: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_Left_currentNode_out,
          parentList_out: this.CetTree_Left_parentList_out,
          checkedNodes_out: this.CetTree_Left_checkedNodes_out,
          halfCheckNodes_out: this.CetTree_Left_halfCheckNodes_out,
          allCheckNodes_out: this.CetTree_Left_allCheckNodes_out
        }
      },
      currentNode: null,
      orders: [
        {
          orderType: "desc",
          priority: 1,
          propertyLabel: "operationtime"
        }
      ],
      order: {
        orderType: "desc",
        priority: 1,
        propertyLabel: "operationtime"
      },
      totalCount: 0,
      pageSize: 100,
      pageLayout: "total,sizes, prev, pager, next, jumper",
      pageSizes: [10, 20, 50, 100],
      currentPage: 1,
      ElSelect_type: {
        value: 0,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_type_change_out
        }
      },
      ElOption_type: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      }
    };
  },
  watch: {
    "CetDatePicker_1.val": {
      handler: function (val) {
        if (val) {
          if (
            this.$moment(val).startOf("day").valueOf() >=
            this.$moment().startOf("day").valueOf()
          ) {
            this.CetButton_next.disable_in = true;
          } else {
            this.CetButton_next.disable_in = false;
          }
        }
      },
      deep: true
    }
  },

  methods: {
    getOperationtype(fn) {
      var modelLabel;
      // 判断是操作日志还是登录日志
      if (this.$route.name === "log") {
        modelLabel = "eemoperationtype";
      } else if (this.$route.name === "operationlog") {
        modelLabel = "operationtype";
      }
      httping({
        url: `/eem-service/v1/common/enumerations/${modelLabel}`,
        method: "GET"
      }).then(response => {
        if (response.code === 0) {
          let data = this._.get(response, "data", []);
          data.unshift({
            id: 0,
            text: $T("全部")
          });
          this.ElOption_type.options_in = data;
          fn && fn();
        }
      });
    },
    ElSelect_type_change_out() {
      this.getLog();
    },
    getTreeData() {
      this.getOperationtype(() => {
        var _this = this;
        let data = {
          loadChildren: true,
          removeRootUser: _this.userInfo.id !== 1,
          tenantId: _this.projectTenantId
        };
        customApi.queryProjectUserAndGroup(data).then(res => {
          const treeData = _this._.get(res, "data", []) || [];
          _this.CetTree_Left.inputData_in = treeData;
          _this.CetTree_Left.selectNode = treeData[0];
        });
      });
    },
    CetButton_2_statusTrigger_out(val) {
      this.CetTable_1.exportFileName = this.exportFileName_out();
      this.CetTable_1.exportTrigger_in = this._.cloneDeep(val);
    },
    CetDatePicker_1_dateVal_out() {
      this.getLog();
    },
    CetButton_prv_statusTrigger_out(val) {
      let date;
      if (this.CetDatePicker_1.val) {
        date = this.$moment(this.CetDatePicker_1.val);
        this.CetDatePicker_1.val = date.subtract(1, "d")._d;
      } else {
        this.CetDatePicker_1.val = this.$moment()._d;
      }
      this.getLog();
    },
    CetButton_next_statusTrigger_out(val) {
      let date;
      if (this.CetDatePicker_1.val) {
        date = this.$moment(this.CetDatePicker_1.val);
        this.CetDatePicker_1.val = date.add(1, "d")._d;
      } else {
        this.CetDatePicker_1.val = this.$moment()._d;
      }
      this.getLog();
    },
    ElInput_1_change_out(val) {
      this.CetTable_1.dynamicInput.description_in = this._.cloneDeep(val);
      this.getLog();
    },
    ElInput_1_input_out(val) {},

    CetTable_1_detailTrigger_out(val) {},
    CetTable_1_editTrigger_out(val) {},
    CetTable_1_outputData_out(val) {},
    CetTable_1_record_out(val) {},
    CetTree_Left_currentNode_out(val) {
      if (val.modelLabel === "usergroup") {
        this.CetTable_1.queryNode_in = null;
        this.currentNode = null;
        this.CetTable_1.data = [];
        return;
      }
      this.CetTable_1.queryNode_in = this._.cloneDeep(val);
      this.currentNode = val;
      if (val) {
        this.getLog();
      }
    },
    CetTree_Left_parentList_out(val) {},
    CetTree_Left_checkedNodes_out(val) {},
    CetTree_Left_halfCheckNodes_out(val) {},
    CetTree_Left_allCheckNodes_out(val) {},
    exportFileName_out() {
      const vm = this;
      var time = "";
      if (vm.CetDatePicker_1.val) {
        time = this.$moment(this._.cloneDeep(vm.CetDatePicker_1.val)).format(
          $T("yyyy年MM月DD日")
        );
      }
      let dateStr = this.CetTable_1.queryNode_in.name + " " + time;
      let name = "";
      // 判断是操作日志还是登录日志
      if (this.$route.name === "Log") {
        name = $T("操作日志");
      } else if (this.$route.name === "operationlog") {
        name = $T("登录日志");
      }
      return `${dateStr} ${name}`;
    },
    //表格列排序条件变化
    sortChange(val) {
      const vm = this;
      let prop = val.prop;
      if (val.prop.indexOf("$text") !== -1) {
        prop = prop.substring(0, prop.length - 5);
      }
      if (val.order === null) {
        vm.orders = null;
      }
      if (val.order === "ascending") {
        vm.orders = [
          {
            orderType: "asc",
            priority: 1,
            propertyLabel: prop
          }
        ];
      } else if (val.order === "descending") {
        vm.orders = [
          {
            orderType: "desc",
            priority: 1,
            propertyLabel: prop
          }
        ];
      }
      this.currentPage = 1; //分页置为第一页
      vm.getLog();
    },
    //分页大小变化
    handleSizeChange(val) {
      this.currentPage = 1;
      this.getLog();
    },
    //分页当前页变化
    handleCurrentPageChange(val) {
      this.getLog();
    },
    // 请求操作日志
    getLog() {
      if (!this.currentNode) {
        if (!this.currentNode) {
          this.$message({
            message: $T("请先选择用户节点"),
            type: "warning"
          });
        }
        return;
      }
      var params = {
        rootCondition: {
          filter: {
            expressions: [
              {
                limit: this.currentNode.id,
                operator: "EQ",
                prop: "user_id"
              }
            ]
          },
          page: {
            index: 0,
            limit: 100
          },
          orders: [
            ...(this.orders || []),
            // 取消id排序
            {
              orderType: null,
              priority: 2,
              propertyLabel: "id"
            }
          ]
        },
        rootID: 0,
        rootLabel: "eemoperationlog"
      };
      // 判断是操作日志还是登录日志
      if (this.$route.name === "Log") {
        params.rootLabel = "eemoperationlog";
      } else if (this.$route.name === "operationlog") {
        params.rootLabel = "operationlog";
      }
      // 加上操作类型入参
      if (this.ElSelect_type.value) {
        params.rootCondition.filter.expressions.push({
          limit: this.ElSelect_type.value,
          operator: "EQ",
          prop: "operationtype"
        });
      }

      params.rootCondition.page = {
        index: (this.currentPage - 1) * this.pageSize,
        limit: this.pageSize
      };
      if (this.CetDatePicker_1.val) {
        params.rootCondition.filter.expressions.push({
          limit: this.$moment(this.CetDatePicker_1.val)
            .startOf("day")
            .valueOf(),
          operator: "GE",
          prop: "operationtime"
        });
        params.rootCondition.filter.expressions.push({
          limit: this.$moment(this.CetDatePicker_1.val)
            .add(1, "day")
            .startOf("day")
            .valueOf(),
          operator: "LT",
          prop: "operationtime"
        });
      }
      if (this.ElInput_1.value) {
        params.rootCondition.filter.expressions.push({
          limit: this.ElInput_1.value,
          operator: "LIKE",
          prop: "description"
        });
      }
      this.CetButton_2.disable_in = true;
      httping({
        url: "/eem-service/v1/common/operationLog",
        method: "POST",
        data: params
      }).then(res => {
        if (res.code === 0 && res.data && res.data.length > 0) {
          this.totalCount = res.total;
          this.CetTable_1.data = res.data;
          this.CetButton_2.disable_in = false;
        } else {
          this.CetTable_1.data = [];
        }
      });
    },

    formatTable(row, column, cellValue, index) {
      var vm = this,
        config = this._.find(vm.columnsArr, {
          key: column.property || column.prop
        }),
        columnIndex = this._.findIndex(vm.columnsArr, {
          key: column.property || column.prop
        }),
        rowIndex = this._.findIndex(vm.CetTable_1.data, row);
      if (config.type === "date") {
        cellValue = vm.formatDate(row, column, cellValue, index, config);
      }
      if (config.type === "number") {
        cellValue = vm.formatNumber(row, column, cellValue, index);
      }
      if (config.type === "boolean") {
        cellValue = vm.formatBoolean(row, column, cellValue, index);
      }

      if (!cellValue) {
        if (cellValue === 0 || cellValue === "") {
          return cellValue;
        } else {
          return "--";
        }
      }

      return cellValue;
    },
    //格式化日期列
    formatDate(row, column, cellValue, index, config) {
      //设置时间格式化字符串，如果配置有效则采用配置字符串，无效则采用默认格式化字符串
      let formatStr = config.formatStr
        ? config.formatStr
        : "YYYY-MM-DD HH:mm:ss";
      if (cellValue) {
        return this.$moment(cellValue).format(formatStr);
      } else if (cellValue === "") {
        return cellValue;
      } else {
        return null;
      }
    },
    //格式化数字列
    formatNumber(row, column, cellValue, index) {
      //TODO 通过columns配置的format进行自定义格式化
      var vm = this,
        config = this._.find(vm.columns, { key: column.property });
      if (cellValue === "") {
        return cellValue;
      }
      return common.formatNumber(config, cellValue, vm);
    },
    //格式化布尔值
    formatBoolean(row, column, cellValue, index) {
      var vm = this,
        config = this._.find(vm.columns, { key: column.property });
      return common.formatBoolean(config, cellValue, vm);
    }
  },
  created: function () {
    const vm = this;
  },
  activated: function () {
    this.CetDatePicker_1.val = this.$moment().startOf("day")._d;
    this.ElInput_1.value = "";
    this.totalCount = 0;
    this.ElSelect_type.value = 0;
    this.getTreeData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
