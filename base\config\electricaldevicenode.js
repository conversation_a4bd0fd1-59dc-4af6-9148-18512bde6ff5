const ELECTRICAL_DEVICE_NODE = {
  deviceNodeList: [
    {
      value: "powertransformer",
      label: $T("变压器")
    },
    {
      value: "capacitor",
      label: $T("电容柜")
    },
    {
      value: "linesegmentwithswitch",
      label: $T("开关柜或一段线")
    },
    {
      value: "busbarsection",
      label: $T("母线")
    },
    {
      value: "meteringcabinet",
      label: $T("计量柜")
    },
    {
      value: "busbarconnector",
      label: $T("母联")
    },
    {
      value: "ptcabinet",
      label: $T("PT柜")
    },
    {
      value: "powerdiscabinet",
      label: $T("配电柜")
    },
    {
      value: "switchcabinet",
      label: $T("开关柜")
    },
    {
      value: "arraycabinet",
      label: $T("列头柜")
    },
    {
      value: "ups",
      label: $T("ups")
    },
    {
      value: "battery",
      label: $T("蓄电池")
    },
    {
      value: "hvdc",
      label: $T("高压直流设备")
    },
    {
      value: "interchanger",
      label: $T("交换机")
    },
    {
      value: "dcpanel",
      label: $T("直流屏")
    },
    {
      value: "generator",
      label: $T("发电机")
    },
    {
      value: "coldwatermainengine",
      label: $T("冷水主机")
    },
    {
      value: "windset",
      label: $T("风柜")
    },
    {
      value: "coolingtower",
      label: $T("冷却塔")
    },
    {
      value: "plateheatexchanger",
      label: $T("板式换热器")
    },
    {
      value: "pump",
      label: $T("泵")
    },
    {
      value: "aircompressor",
      label: $T("空压机")
    },
    {
      value: "colddryingmachine",
      label: $T("冷干机")
    },
    {
      value: "dryingmachine",
      label: $T("干燥机")
    },
    {
      value: "boiler",
      label: $T("锅炉")
    },
    {
      value: "airconditioner",
      label: $T("空调")
    },
    {
      value: "meteorologicalmonitor",
      label: $T("气象监测仪")
    },
    {
      label: $T("计算机"),
      value: "computer"
    },
    {
      label: $T("通信管理机"),
      value: "gateway"
    },
    {
      label: $T("表计"),
      value: "meter"
    },
    {
      label: $T("光电转换器"),
      value: "photoeleconverter"
    },
    {
      label: $T("一段线"),
      value: "linesegment"
    }
  ],
  nodes: [
    "powertransformer",
    "capacitor",
    "linesegmentwithswitch",
    "busbarsection",
    "meteringcabinet",
    "busbarconnector",
    "ptcabinet",
    "powerdiscabinet",
    "arraycabinet",
    "ups",
    "battery",
    "hvdc",
    "interchanger",
    "dcpanel",
    "generator",
    "pump"
  ],
  roomtype1: [
    "powertransformer",
    "capacitor",
    "linesegmentwithswitch",
    "busbarsection",
    "meteringcabinet",
    "busbarconnector",
    "ptcabinet",
    "powerdiscabinet",
    "arraycabinet",
    "ups",
    "battery",
    "hvdc",
    "interchanger",
    "dcpanel",
    "generator"
  ],
  roomtype2: ["pump", "linesegment"],
  deviceList: [
    "coolingtower",
    "windset",
    "coldwatermainengine",
    "plateheatexchanger",
    "aircompressor",
    "colddryingmachine",
    "dryingmachine",
    "boiler",
    "computer",
    "gateway",
    "meter",
    "interchanger",
    "photoeleconverter",
    "itcabinet",
    "ats",
    "avc",
    "plc"
  ],
  common: [
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次检修日期"),
      propertyLabel: "lastoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("保养周期"),
      propertyLabel: "maintenanceperiod",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号规格"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("下次检修日期"),
      propertyLabel: "nextoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("资产归属"),
      propertyLabel: "ownship",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备归类"),
      propertyLabel: "deviceclassification",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    }
  ],
  powertransformer: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("圈变压类型"),
      propertyLabel: "coiltype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次检修日期"),
      propertyLabel: "lastoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("下次检修日期"),
      propertyLabel: "nextoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("空载电流百分比"),
      propertyLabel: "noloadcurrent",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("空载损耗"),
      propertyLabel: "noloadloss",
      type: "string",
      unit: "kW"
    },
    {
      id: 1,
      text: $T("相别类型"),
      propertyLabel: "phasetype",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("PT变比"),
      propertyLabel: "ptratio",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定容量"),
      propertyLabel: "ratedcapacity",
      type: "float",
      unit: "kVA"
    },
    {
      id: 1,
      text: $T("额定电流"),
      propertyLabel: "ratedcurrent",
      type: "string",
      unit: "A"
    },
    {
      id: 1,
      text: $T("额定电压"),
      propertyLabel: "ratedvoltage",
      type: "string",
      unit: "kV"
    },
    {
      id: 1,
      text: $T("短路阻抗"),
      propertyLabel: "shortcircuitimpedance",
      type: "string",
      unit: "Ω"
    },
    {
      id: 1,
      text: $T("短路损耗"),
      propertyLabel: "shortcircuitloss",
      type: "string",
      unit: "kW"
    },
    {
      id: 1,
      text: "设备启用状态",
      propertyLabel: "status",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("质保期限"),
      propertyLabel: "warrantydate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备归类"),
      propertyLabel: "deviceclassification",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("变压器等级"),
      propertyLabel: "transformerlevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("无功经济当量"),
      propertyLabel: "reactiveeconomicequivalent",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("中载区间下限"),
      propertyLabel: "sectionlowlimit",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("中载区间上限"),
      propertyLabel: "sectionupperlimit",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("短路电压百分比"),
      propertyLabel: "shortcircuitvoltage",
      type: "string",
      unit: ""
    }
  ],
  capacitor: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备归类"),
      propertyLabel: "deviceclassification",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    }
  ],
  linesegmentwithswitch: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("CT极性"),
      propertyLabel: "ctpolarity",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备归类"),
      propertyLabel: "deviceclassification",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("线的功能"),
      propertyLabel: "linefunctiontype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("所属低压馈电柜ID"),
      propertyLabel: "linesegmentwithswitch_id",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("供电负载类型"),
      propertyLabel: "loadclass",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定电流"),
      propertyLabel: "ratedcurrent",
      type: "float",
      unit: "A"
    },
    {
      id: 1,
      text: $T("线路电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    }
  ],
  busbarsection: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("母线标记类型"),
      propertyLabel: "busbarmarktype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("材质"),
      propertyLabel: "materialtype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定电流"),
      propertyLabel: "ratedcurrent",
      type: "float",
      unit: "A"
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备归类"),
      propertyLabel: "deviceclassification",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    }
  ],
  meteringcabinet: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备归类"),
      propertyLabel: "deviceclassification",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    }
  ],
  busbarconnector: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备归类"),
      propertyLabel: "deviceclassification",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("母联所关联的I段母线对象"),
      propertyLabel: "busbarsegi",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("母联所关联的II段母线对象"),
      propertyLabel: "busbarsegii",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("CT极性"),
      propertyLabel: "ctpolarity",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定容量"),
      propertyLabel: "ratedcapacity",
      type: "float",
      unit: "kVA"
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    }
  ],
  ptcabinet: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备归类"),
      propertyLabel: "deviceclassification",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    }
  ],
  powerdiscabinet: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备类别"),
      propertyLabel: "devicetype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次检修日期"),
      propertyLabel: "lastoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次预检日期"),
      propertyLabel: "lastpretestdate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("保养周期"),
      propertyLabel: "maintenanceperiod",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备运行状态"),
      propertyLabel: "operationstatus",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("配电柜类型"),
      propertyLabel: "powerdiscabinettype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产标准"),
      propertyLabel: "prodstandard",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("防护等级"),
      propertyLabel: "protlevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("质保期限"),
      propertyLabel: "warrantydate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("资产编码"),
      propertyLabel: "asset",
      type: "string",
      unit: ""
    }
  ],
  arraycabinet: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("资产编码"),
      propertyLabel: "asset",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备类别"),
      propertyLabel: "devicetype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次检修日期"),
      propertyLabel: "lastoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次预检日期"),
      propertyLabel: "lastpretestdate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("保养周期"),
      propertyLabel: "maintenanceperiod",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备运行状态"),
      propertyLabel: "operationstatus",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定容量"),
      propertyLabel: "ratedcapacity",
      type: "float",
      unit: "kVA"
    },
    {
      id: 1,
      text: $T("额定电流"),
      propertyLabel: "ratedcurrent",
      type: "string",
      unit: "A"
    },
    {
      id: 1,
      text: $T("额定电压"),
      propertyLabel: "ratedvoltage",
      type: "string",
      unit: "kV"
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("质保期限"),
      propertyLabel: "warrantydate",
      type: "date",
      unit: ""
    }
  ],
  ups: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备归类"),
      propertyLabel: "deviceclassification",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("转换时间"),
      propertyLabel: "conversiontime",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("输入电压"),
      propertyLabel: "inputvoltage",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("逆变器效率"),
      propertyLabel: "inverterefficiency",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次检修日期"),
      propertyLabel: "lastoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("整机效率"),
      propertyLabel: "machineefficiency",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("下次检修日期"),
      propertyLabel: "nextoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("输出电压"),
      propertyLabel: "outputvoltage",
      type: "string",
      unit: "V"
    },
    {
      id: 1,
      text: $T("额定容量"),
      propertyLabel: "ratedcapacity",
      type: "float",
      unit: "kVA"
    },
    {
      id: 1,
      text: $T("逆变器结构"),
      propertyLabel: "upsstructure",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("质保期限"),
      propertyLabel: "warrantydate",
      type: "date",
      unit: ""
    }
  ],
  battery: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备归类"),
      propertyLabel: "deviceclassification",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次检修日期"),
      propertyLabel: "lastoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("下次检修日期"),
      propertyLabel: "nextoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("质保期限"),
      propertyLabel: "warrantydate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    }
  ],
  hvdc: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("资产编码"),
      propertyLabel: "asset",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备归类"),
      propertyLabel: "deviceclassification",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次检修日期"),
      propertyLabel: "lastoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次预检日期"),
      propertyLabel: "lastpretestdate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("保养周期"),
      propertyLabel: "maintenanceperiod",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备运行状态"),
      propertyLabel: "operationstatus",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定容量"),
      propertyLabel: "ratedcapacity",
      type: "float",
      unit: "kVA"
    },
    {
      id: 1,
      text: $T("额定电流"),
      propertyLabel: "ratedcurrent",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定电压"),
      propertyLabel: "ratedvoltage",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("质保期限"),
      propertyLabel: "warrantydate",
      type: "date",
      unit: ""
    }
  ],
  interchanger: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("资产编码"),
      propertyLabel: "asset",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("背板带宽"),
      propertyLabel: "bandwidth",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备归类"),
      propertyLabel: "deviceclassification",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    },
    {
      id: 1,
      text: "产品类型",
      propertyLabel: "interchangertype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次检修日期"),
      propertyLabel: "lastoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次预检日期"),
      propertyLabel: "lastpretestdate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("保养周期"),
      propertyLabel: "maintenanceperiod",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("网口数量"),
      propertyLabel: "netport",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备运行状态"),
      propertyLabel: "operationstatus",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("光口数量"),
      propertyLabel: "opticalport",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定容量"),
      propertyLabel: "ratedcapacity",
      type: "float",
      unit: "kVA"
    },
    {
      id: 1,
      text: $T("额定电流"),
      propertyLabel: "ratedcurrent",
      type: "string",
      unit: "A"
    },
    {
      id: 1,
      text: $T("额定电压"),
      propertyLabel: "ratedvoltage",
      type: "string",
      unit: "kV"
    },
    {
      id: 1,
      text: $T("传输速率"),
      propertyLabel: "transmissionrate",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("质保期限"),
      propertyLabel: "warrantydate",
      type: "date",
      unit: ""
    }
  ],
  dcpanel: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("资产编码"),
      propertyLabel: "asset",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定电压"),
      propertyLabel: "ratedvoltage",
      type: "string",
      unit: "kV"
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("质保期限"),
      propertyLabel: "warrantydate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备归类"),
      propertyLabel: "deviceclassification",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次检修日期"),
      propertyLabel: "lastoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次预检日期"),
      propertyLabel: "lastpretestdate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("保养周期"),
      propertyLabel: "maintenanceperiod",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备运行状态"),
      propertyLabel: "operationstatus",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定容量"),
      propertyLabel: "ratedcapacity",
      type: "float",
      unit: "kVA"
    },
    {
      id: 1,
      text: $T("额定电流"),
      propertyLabel: "ratedcurrent",
      type: "string",
      unit: "A"
    }
  ],
  generator: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备归类"),
      propertyLabel: "deviceclassification",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次检修日期"),
      propertyLabel: "lastoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("下次检修日期"),
      propertyLabel: "nextoverhauldate",
      type: "date",
      unit: ""
    }
  ],
  pump: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("空调系统类型"),
      propertyLabel: "airconditionsystemtype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("资产编码"),
      propertyLabel: "asset",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("更换日期"),
      propertyLabel: "changedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("冷却方式"),
      propertyLabel: "coolingmode",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("效率节能评价值"),
      propertyLabel: "effevaluation",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("效率限定值"),
      propertyLabel: "efflimit",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("电机厂家"),
      propertyLabel: "electricalmachinerymanufacturer",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次检修日期"),
      propertyLabel: "lastoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("检修周期(天)"),
      propertyLabel: "maintenanceperiod",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号规格"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("下次检修日期"),
      propertyLabel: "nextoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备归类"),
      propertyLabel: "deviceclassification",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("资产归属"),
      propertyLabel: "ownship",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("频率类型"),
      propertyLabel: "frequencytype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("功能类型"),
      propertyLabel: "functiontype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("是否报废"),
      propertyLabel: "isscrap",
      type: "boolean",
      unit: ""
    },
    {
      id: 1,
      text: $T("润滑方式"),
      propertyLabel: "lubricationmode",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("电机型号"),
      propertyLabel: "motortype",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("工作介质类型"),
      propertyLabel: "operationmediumtype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("泵类型"),
      propertyLabel: "physicaltype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("一次泵或二次泵"),
      propertyLabel: "pumpcycletype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定冲次"),
      propertyLabel: "ratedblankingtimes",
      type: "int8",
      unit: "次/min"
    },
    {
      id: 1,
      text: $T("额定流量"),
      propertyLabel: "rateddischarge",
      type: "float",
      unit: "m³/h"
    },
    {
      id: 1,
      text: $T("额定扬程"),
      propertyLabel: "ratedlift",
      type: "float",
      unit: "m"
    },
    {
      id: 1,
      text: $T("电机额定电流"),
      propertyLabel: "ratedmotorcurrent",
      type: "float",
      unit: "A"
    },
    {
      id: 1,
      text: $T("电机额定效率"),
      propertyLabel: "ratedmotorefficiency",
      type: "float",
      unit: "%"
    },
    {
      id: 1,
      text: $T("电机额定功率"),
      propertyLabel: "ratedmotorpower",
      type: "float",
      unit: "kW"
    },
    {
      id: 1,
      text: $T("电机额定功率因数"),
      propertyLabel: "ratedmotorpowerfactor",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定转速"),
      propertyLabel: "ratedspeed",
      type: "float",
      unit: "r/min"
    },
    {
      id: 1,
      text: $T("额定冲程"),
      propertyLabel: "ratedstroke",
      type: "float",
      unit: "mm"
    },
    {
      id: 1,
      text: $T("额定工作压力"),
      propertyLabel: "ratedworkingpressure",
      type: "float",
      unit: "Mpa"
    },
    {
      id: 1,
      text: $T("报废日期"),
      propertyLabel: "scrapdate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("使用状态"),
      propertyLabel: "usagestate",
      type: "enum",
      unit: ""
    }
  ],
  linesegment: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("资产编码"),
      propertyLabel: "asset",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("CT极性"),
      propertyLabel: "ctpolarity",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("配电用途"),
      propertyLabel: "distributionpurpose",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次检修日期"),
      propertyLabel: "lastoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次预检日期"),
      propertyLabel: "lastpretestdate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("线的功能"),
      propertyLabel: "linefunctiontype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("供电负载类型"),
      propertyLabel: "loadclass",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("保养周期"),
      propertyLabel: "maintenanceperiod",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备运行状态"),
      propertyLabel: "operationstatus",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("电源性质"),
      propertyLabel: "powerproperty",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定电流"),
      propertyLabel: "ratedcurrent",
      type: "float",
      unit: "A"
    },
    {
      id: 1,
      text: $T("电阻"),
      propertyLabel: "resistance",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("线路电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("质保期限"),
      propertyLabel: "warrantydate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    }
  ],
  switchcabinet: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("资产编码"),
      propertyLabel: "asset",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("CT极性"),
      propertyLabel: "ctpolarity",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("配电用途"),
      propertyLabel: "distributionpurpose",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次检修日期"),
      propertyLabel: "lastoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次预检日期"),
      propertyLabel: "lastpretestdate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("供电负载类型"),
      propertyLabel: "loadclass",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("保养周期"),
      propertyLabel: "maintenanceperiod",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备运行状态"),
      propertyLabel: "operationstatus",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("电源性质"),
      propertyLabel: "powerproperty",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定容量"),
      propertyLabel: "ratedcapacity",
      type: "float",
      unit: "kVA"
    },
    {
      id: 1,
      text: $T("额定电流"),
      propertyLabel: "ratedcurrent",
      type: "float",
      unit: "A"
    },
    {
      id: 1,
      text: $T("额定电压"),
      propertyLabel: "ratedvoltage",
      type: "float",
      unit: "kV"
    },
    {
      id: 1,
      text: $T("电阻"),
      propertyLabel: "resistance",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: "开关柜的功能",
      propertyLabel: "switchfunctiontype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("线路电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("质保期限"),
      propertyLabel: "warrantydate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    }
  ],
  sectionarea: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    }
  ],
  building: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: "地址",
      propertyLabel: "address",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("功能类型"),
      propertyLabel: "buildingfunctiontype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: "投运时间",
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: "合作截止时间",
      propertyLabel: "cooperativedeadline",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: "建筑面积",
      propertyLabel: "floorarea",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: "占地面积",
      propertyLabel: "footprintarea",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: "人数",
      propertyLabel: "population",
      type: "int8",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    }
  ],
  floor: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: "编码",
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: "地址",
      propertyLabel: "address",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: "面积",
      propertyLabel: "area",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: "人数",
      propertyLabel: "population",
      type: "int8",
      unit: ""
    }
  ],
  room: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: "面积",
      propertyLabel: "area",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: "房间类型",
      propertyLabel: "roomtype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: "地址",
      propertyLabel: "address",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: "编码",
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: "投运时间",
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: "合作截止时间",
      propertyLabel: "cooperativedeadline",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: "人数",
      propertyLabel: "population",
      type: "int8",
      unit: ""
    },
    {
      id: 1,
      text: "配电室类型",
      propertyLabel: "powerroomtype",
      type: "enum",
      unit: ""
    }
  ],
  manuequipment: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    }
  ],
  meteorologicalmonitor: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    }
  ],
  airconditioner: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    }
  ],
  coolingtower: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("空调系统类型"),
      propertyLabel: "airconditionsystemtype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("机组尺寸"),
      propertyLabel: "devicesize",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("机组总重量"),
      propertyLabel: "deviceweight",
      type: "float",
      unit: "吨"
    },
    {
      id: 1,
      text: $T("是否变频"),
      propertyLabel: "frequencytype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("最大工作频率"),
      propertyLabel: "maxworkingfrequency",
      type: "float",
      unit: "Hz"
    },
    {
      id: 1,
      text: $T("最小工作频率"),
      propertyLabel: "minworkingfrequency",
      type: "float",
      unit: "Hz"
    }
  ],
  windset: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("空调系统类型"),
      propertyLabel: "airconditionsystemtype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("机组尺寸"),
      propertyLabel: "devicesize",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("机组重量"),
      propertyLabel: "deviceweight",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    }
  ],
  coldwatermainengine: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("机组尺寸"),
      propertyLabel: "devicesize",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("机组重量"),
      propertyLabel: "deviceweight",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("空调系统类型"),
      propertyLabel: "airconditionsystemtype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("制冷方式"),
      propertyLabel: "coolingmethod",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("机组属性"),
      propertyLabel: "engineattr",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("冷却循环水量"),
      propertyLabel: "coolingcyclewater",
      type: "float",
      unit: "m3/h"
    },
    {
      id: 1,
      text: $T("最大恒定出水温度"),
      propertyLabel: "maxconstantoutwatertemp",
      type: "float",
      unit: "℃"
    },
    {
      id: 1,
      text: $T("冷却循环水最大温度"),
      propertyLabel: "maxcoolingcycletemp",
      type: "float",
      unit: "℃"
    },
    {
      id: 1,
      text: $T("冷冻循环水最大温度"),
      propertyLabel: "maxrefrigeratingcycletemp",
      type: "float",
      unit: "℃"
    },
    {
      id: 1,
      text: $T("最小恒定出水温度"),
      propertyLabel: "minconstantoutwatertemp",
      type: "float",
      unit: "℃"
    },
    {
      id: 1,
      text: $T("冷却循环水最小温度"),
      propertyLabel: "mincoolingcycletemp",
      type: "float",
      unit: "℃"
    },
    {
      id: 1,
      text: $T("冷冻循环水最小温度"),
      propertyLabel: "minrefrigeratingcycletemp",
      type: "float",
      unit: "℃"
    },
    {
      id: 1,
      text: $T("额定功率"),
      propertyLabel: "ratedmotorpower",
      type: "float",
      unit: "KW"
    },
    {
      id: 1,
      text: $T("额定制冷量"),
      propertyLabel: "ratedrefrigeration",
      type: "float",
      unit: "KW"
    },
    {
      id: 1,
      text: $T("冷冻循环水量"),
      propertyLabel: "refrigeratingcyclewater",
      type: "float",
      unit: "m3/h"
    },
    {
      id: 1,
      text: $T("制冷介质"),
      propertyLabel: "refrigerationmediumtype",
      type: "enum",
      unit: ""
    }
  ],
  plateheatexchanger: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定热换量"),
      propertyLabel: "ratedheatexchange",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("阀门类型"),
      propertyLabel: "valvetype",
      type: "enum",
      unit: ""
    }
  ],
  aircompressor: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("空压机组属性"),
      propertyLabel: "aircompressorattr",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("机组尺寸"),
      propertyLabel: "devicesize",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("机组重量"),
      propertyLabel: "deviceweight",
      type: "float",
      unit: "吨"
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    }
  ],
  colddryingmachine: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("冷干机属性"),
      propertyLabel: "colddrymachineattr",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("机组尺寸"),
      propertyLabel: "devicesize",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("机组总重量"),
      propertyLabel: "deviceweight",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    }
  ],
  dryingmachine: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("干燥机属性"),
      propertyLabel: "dryingmachineattr",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("机组尺寸"),
      propertyLabel: "devicesize",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("机组总重量"),
      propertyLabel: "deviceweight",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("干燥剂"),
      propertyLabel: "dryingagent",
      type: "string",
      unit: ""
    }
  ],
  boiler: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("锅炉类型"),
      propertyLabel: "boilertype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("机组尺寸"),
      propertyLabel: "devicesize",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("机组重量"),
      propertyLabel: "deviceweight",
      type: "float",
      unit: "吨"
    },
    {
      id: 1,
      text: $T("燃料类型"),
      propertyLabel: "fueltype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    }
  ],
  pipeline: [
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    }
  ],
  computer: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("计算机名称"),
      propertyLabel: "computername",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("cpu负载"),
      propertyLabel: "cpuload",
      type: "number",
      unit: ""
    },
    {
      id: 1,
      text: $T("描述"),
      propertyLabel: "description",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("磁盘负载"),
      propertyLabel: "diskload",
      type: "number",
      unit: ""
    },
    {
      id: 1,
      text: $T("磁盘剩余空间"),
      propertyLabel: "diskremainingcapacity",
      type: "number",
      unit: ""
    },
    {
      id: 1,
      text: $T("IP"),
      propertyLabel: "ip",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("IP2"),
      propertyLabel: "ip2",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("是否后台服务器"),
      propertyLabel: "isbackgroudserver",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("是否是配置服务"),
      propertyLabel: "isconfigserver",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("是否前台服务器"),
      propertyLabel: "isfrontserver",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("是否时间服务器"),
      propertyLabel: "istimeserver",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("内存负载"),
      propertyLabel: "memoryload",
      type: "number",
      unit: ""
    },
    {
      id: 1,
      text: $T("主备模式"),
      propertyLabel: "standbymode",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("备用服务器的id"),
      propertyLabel: "standbyserverid",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("时区"),
      propertyLabel: "timezone",
      type: "enum",
      unit: ""
    }
  ],
  gateway: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("通信状态"),
      propertyLabel: "communicationstatus",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("描述"),
      propertyLabel: "description",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("通信管理机型号"),
      propertyLabel: "gatewaymodel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("地址1"),
      propertyLabel: "networkaddress1",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("地址2"),
      propertyLabel: "networkaddress2",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("地址3"),
      propertyLabel: "networkaddress3",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("地址4"),
      propertyLabel: "networkaddress4",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("备用管理机的id"),
      propertyLabel: "standbygatewayid",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("主备模式"),
      propertyLabel: "standbymode",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("通信成功率"),
      propertyLabel: "successrate",
      type: "number",
      unit: ""
    }
  ],
  meter: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("波特率"),
      propertyLabel: "boudrate",
      type: "number",
      unit: ""
    },
    {
      id: 1,
      text: $T("回路ID"),
      propertyLabel: "circuitid",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("回路名称"),
      propertyLabel: "circuitname",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("串口号"),
      propertyLabel: "com",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("通信id"),
      propertyLabel: "commid",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("通信方式"),
      propertyLabel: "communicationmode",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("通信状态"),
      propertyLabel: "communicationstatus",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("接线方式"),
      propertyLabel: "connection",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("数据位"),
      propertyLabel: "databits",
      type: "number",
      unit: ""
    },
    {
      id: 1,
      text: $T("描述"),
      propertyLabel: "description",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("表计ID"),
      propertyLabel: "deviceid",
      type: "number",
      unit: ""
    },
    {
      id: 1,
      text: $T("能源类型"),
      propertyLabel: "energytype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("是否已查看"),
      propertyLabel: "ischecked",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("表计地址"),
      propertyLabel: "meteraddress",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("表计型号"),
      propertyLabel: "metermodel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("表计类型"),
      propertyLabel: "metertype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("网络地址"),
      propertyLabel: "networkaddress",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("奇偶校验"),
      propertyLabel: "paritycheck",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("规约类型名称"),
      propertyLabel: "protocalname",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("通信规约"),
      propertyLabel: "protocaltype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("规约版本"),
      propertyLabel: "protocalversion",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("变比"),
      propertyLabel: "ratio",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("翻转量"),
      propertyLabel: "rollover",
      type: "number",
      unit: ""
    },
    {
      id: 1,
      text: $T("停止位"),
      propertyLabel: "stopbit",
      type: "number",
      unit: ""
    },
    {
      id: 1,
      text: $T("通信成功率"),
      propertyLabel: "successrate",
      type: "number",
      unit: ""
    }
  ],
  photoeleconverter: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("资产编码"),
      propertyLabel: "asset",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备归类"),
      propertyLabel: "deviceclassification",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    },
    {
      id: 1,
      text: $T("电口数量"),
      propertyLabel: "elecport",
      type: "number",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次检修日期"),
      propertyLabel: "lastoverhauldate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("上次预检日期"),
      propertyLabel: "lastpretestdate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("保养周期"),
      propertyLabel: "maintenanceperiod",
      type: "number",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备运行状态"),
      propertyLabel: "operationstatus",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("光口数量"),
      propertyLabel: "opticalport",
      type: "number",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定容量"),
      propertyLabel: "ratedcapacity",
      type: "float",
      unit: "kVA"
    },
    {
      id: 1,
      text: $T("额定电流"),
      propertyLabel: "ratedcurrent",
      type: "number",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定电压"),
      propertyLabel: "ratedvoltage",
      type: "number",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "vendor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("质保期限"),
      propertyLabel: "warrantydate",
      type: "date",
      unit: ""
    }
  ],
  itcabinet: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定容量"),
      propertyLabel: "ratedcapacity",
      type: "float",
      unit: "kVA"
    }
  ],
  ats: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("编号"),
      propertyLabel: "code",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("资产编码"),
      propertyLabel: "asset",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("品牌"),
      propertyLabel: "brand",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("投运日期"),
      propertyLabel: "commissiondate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("文档"),
      propertyLabel: "document",
      type: "document",
      unit: ""
    },
    {
      id: 1,
      text: $T("纬度"),
      propertyLabel: "latitude",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("安装位置"),
      propertyLabel: "location",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("经度"),
      propertyLabel: "longitude",
      type: "float",
      unit: ""
    },
    {
      id: 1,
      text: $T("保养周期"),
      propertyLabel: "maintenanceperiod",
      type: "number",
      unit: ""
    },
    {
      id: 1,
      text: $T("出厂日期"),
      propertyLabel: "manufacturedate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定容量"),
      propertyLabel: "ratedcapacity",
      type: "float",
      unit: "kVA"
    },
    {
      id: 1,
      text: $T("额定电流"),
      propertyLabel: "ratedcurrent",
      type: "number",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定电压"),
      propertyLabel: "ratedvoltage",
      type: "number",
      unit: ""
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("质保期限"),
      propertyLabel: "warrantydate",
      type: "date",
      unit: ""
    },
    {
      id: 1,
      text: $T("生产厂家"),
      propertyLabel: "manufactor",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    }
  ],
  avc: [
    {
      id: 1,
      text: $T("设备名称"),
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("型号"),
      propertyLabel: "model",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: $T("设备类别"),
      propertyLabel: "devicetype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("材质"),
      propertyLabel: "materialtype",
      type: "enum",
      unit: ""
    },
    {
      id: 1,
      text: $T("图片"),
      propertyLabel: "pic",
      type: "pic",
      unit: ""
    },
    {
      id: 1,
      text: $T("额定容量"),
      propertyLabel: "ratedcapacity",
      type: "float",
      unit: "kVA"
    },
    {
      id: 1,
      text: $T("电压等级"),
      propertyLabel: "voltagelevel",
      type: "enum",
      unit: ""
    }
  ],
  plc: [
    {
      id: 1,
      text: "名称",
      propertyLabel: "name",
      type: "string",
      unit: ""
    },
    {
      id: 1,
      text: "品牌",
      propertyLabel: "brand",
      type: "string",
      unit: ""
    }
  ]
};
export default ELECTRICAL_DEVICE_NODE;
