﻿<template>
  <div class="page">
    <el-row :gutter="16" style="height: 482px">
      <el-col :span="24" style="height: 482px">
        <div class="eem-container fullheight">
          <ElectricityTrend
            :queryNode_in="clickNode"
            :inputData_in="ElectricityTrend_1.inputData_in"
            :dataConfig="ElectricityTrend_1.dataConfig"
            :customParams_in="ElectricityTrend_1.customParams_in"
            :visibleTrigger_in="ElectricityTrend_1.visibleTrigger_in"
            :refreshTrigger_in="ElectricityTrend_1.refreshTrigger_in"
            @date_out="ElectricityTrend_1_date_out"
          ></ElectricityTrend>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="16" style="height: 300px" class="gapJ2">
      <el-col :span="12" style="height: 300px">
        <div class="eem-container fullheight">
          <IndexRanking
            :queryNode_in="clickNode"
            :inputData_in="IndexRanking_1.inputData_in"
            :dataConfig="IndexRanking_1.dataConfig"
            :customParams_in="IndexRanking_1.customParams_in"
            :visibleTrigger_in="IndexRanking_1.visibleTrigger_in"
            :refreshTrigger_in="IndexRanking_1.refreshTrigger_in"
            @clickChart="IndexRanking_1_clickChart"
            @data_out="IndexRanking_1_data_out"
          ></IndexRanking>
        </div>
      </el-col>
      <el-col :span="12" style="height: 300px">
        <div class="eem-container fullheight">
          <IndexRanking
            :queryNode_in="clickNode"
            :inputData_in="IndexRanking_2.inputData_in"
            :dataConfig="IndexRanking_2.dataConfig"
            :customParams_in="IndexRanking_2.customParams_in"
            :visibleTrigger_in="IndexRanking_2.visibleTrigger_in"
            :refreshTrigger_in="IndexRanking_2.refreshTrigger_in"
            @clickChart="IndexRanking_2_clickChart"
          ></IndexRanking>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="16" v-if="nodeIsLeaf === 'room'">
      <el-col
        :span="6"
        class="gapJ2"
        v-for="(item, index) in deviceList"
        :key="index"
      >
        <div class="eem-container fullheight">
          <div style="text-align: center">
            <span class="card-head-title">{{ item.name || "--" }}</span>
          </div>
          <div style="height: 200px">
            <ContImg class="ContImg" :imgPath_in="item.picture"></ContImg>
          </div>
          <div style="height: calc(100% - 250px)">
            <div>
              <span>{{ $T("设备型号") }}：</span>
              <span class="fr">{{ item.model || "--" }}</span>
            </div>
            <div>
              <span>{{ $T("电压等级") }}（kV）：</span>
              <span class="fr">{{ item.voltagelevel$text || "--" }}</span>
            </div>
            <div>
              <span>{{ $T("用能量") }}（kWh）：</span>
              <span class="fr">{{ item.consumption || "--" }}</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import ElectricityTrend from "./ElectricityTrend";
import IndexRanking from "./IndexRanking";
import ContImg from "./ContImg";
export default {
  name: "EnergyData2",
  components: {
    ElectricityTrend,
    IndexRanking,
    ContImg
  },
  props: {
    clickNode: {
      type: Object
    },
    nodeIsLeaf: {
      type: String
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data() {
    return {
      ElectricityTrend_1: {
        visibleTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        currentNode_in: null,
        inputData_in: null,
        customParams_in: null,
        dataConfig: {
          title: $T("月度累计用电"),
          queryUrl: "/eem-service/v1/energy/consumption",
          modelLabel: "",
          type: "POST",
          showType: true,
          showTime: true,
          showExport: true
        }
      },
      IndexRanking_1: {
        visibleTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        currentNode_in: null,
        inputData_in: null,
        customParams_in: null,
        dataConfig: {
          title: $T("月用电量排名"),
          // subtitle: "项目厂房各配电室用电量排名",
          queryUrl: "/eem-service/v1/energy/consumption",
          modelLabel: "",
          type: "POST",
          showType: false
        }
      },
      IndexRanking_2: {
        visibleTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        currentNode_in: null,
        inputData_in: null,
        customParams_in: null,
        dataConfig: {
          title: $T("月用电指标排名"),
          // subtitle: "项目厂房各配电室用电量排名",
          queryUrl: "/eem-service/v1/energy/energyEfficiency",
          modelLabel: "",
          type: "POST",
          showType: true
        }
      },
      deviceList: []
    };
  },
  watch: {
    clickNode: {
      deep: true,
      immediate: true,
      handler: function (val, oldVal) {
        if (!(this._.get(val, "id") && this._.get(val, "modelLabel"))) return;
        if (
          val.id === this._.get(oldVal, "id") &&
          val.modelLabel === this._.get(oldVal, "modelLabel")
        ) {
          return;
        }
        if (
          val.modelLabel !== "manuequipment" &&
          val.modelLabel !== "project"
        ) {
          this.setElectricityTrend();
          this.setIndexRanking();
          this.setElectricRanking();
        }
        if (val.modelLabel === "room") {
          this.deviceList = [];
        }
      }
    }
  },

  methods: {
    ElectricityTrend_1_date_out(val) {
      this.ElectricityTrend_1.dateOut = this.$moment(val).valueOf();
      this.setIndexRanking();
      this.setElectricRanking();
    },
    IndexRanking_1_clickChart(val) {
      // console.log(val)
      this.$emit("treeDrilling", val.name);
    },
    IndexRanking_1_data_out(val) {
      if (this.clickNode.modelLabel != "room") {
        return;
      }
      var data = this._.get(val, "2", []);
      var children = this._.get(this.clickNode, "children", []);
      children.forEach(item => {
        var consumption =
          data.filter(i => i.objectid == item.id)[0] &&
          data.filter(i => i.objectid == item.id)[0].total;
        if (consumption || consumption == 0) {
          item.consumption = consumption.toFixed2(2);
        }
      });
      this.deviceList = children;
    },
    IndexRanking_2_clickChart(val) {
      // console.log(val)
      this.$emit("treeDrilling", val.name);
    },
    //累计用电
    setElectricityTrend() {
      if (!this.clickNode) {
        return;
      }
      var params = {
        nodes: [
          {
            modelLabel: this.clickNode.modelLabel,
            nodes: [
              {
                id: this.clickNode.id,
                name: this.clickNode.name
              }
            ]
          }
        ],
        startTime: this.$moment().startOf("M").valueOf(),
        endTime: this.$moment().endOf("M").valueOf() + 1,
        aggregationCycle: 14,
        energyTypes: [2] //2:电，3：水
      };
      this.ElectricityTrend_1.customParams_in = params;
      this.ElectricityTrend_1.visibleTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    //用电排名
    setIndexRanking() {
      if (!this.clickNode) {
        return;
      }
      var startTime = this.$moment(this.ElectricityTrend_1.dateOut)
        .startOf("M")
        .valueOf();
      var endTime =
        this.$moment(this.ElectricityTrend_1.dateOut).endOf("M").valueOf() + 1;
      var nodes = this.getChildrenNodes();
      // if(nodes.length === 0){
      //   return
      // }
      var params = {
        nodes: nodes,
        startTime: startTime,
        endTime: endTime,
        aggregationCycle: 14,
        energyTypes: [2] //2:电，3：水
      };
      this.IndexRanking_1.customParams_in = params;
      this.IndexRanking_1.visibleTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    //用电指标排名
    setElectricRanking() {
      var startTime = this.$moment(this.ElectricityTrend_1.dateOut)
        .startOf("M")
        .valueOf();
      var endTime =
        this.$moment(this.ElectricityTrend_1.dateOut).endOf("M").valueOf() + 1;
      var nodes = this.getChildrenNodes();
      // if(nodes.length === 0){
      //   return
      // }
      var params = {
        nodes: nodes,
        startTime: startTime,
        endTime: endTime,
        aggregationCycle: 14, //年17 月14 日12 小时7
        energyTypes: [2], //2:电，2：水
        unittype: [1],
        productType: 1
      };

      this.IndexRanking_2.customParams_in = params;
      this.IndexRanking_2.visibleTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    // 获取节点字列表
    getChildrenNodes() {
      var clickNode = this.clickNode,
        childrenNodes = clickNode.children || [],
        nodes = [],
        len = childrenNodes.length,
        obj = {};
      for (var i = 0; i < len; i++) {
        // if (childrenNodes[i].modelLabel !== "manuequipment") {
        if (obj[childrenNodes[i].modelLabel]) {
          obj[childrenNodes[i].modelLabel].push({
            id: childrenNodes[i].id,
            name: childrenNodes[i].name
          });
        } else {
          obj[childrenNodes[i].modelLabel] = [
            {
              id: childrenNodes[i].id,
              name: childrenNodes[i].name
            }
          ];
        }
        // var obj = {
        //   modelLabel: childrenNodes[i].modelLabel,
        //   nodes: [
        //     {
        //       id: childrenNodes[i].id,
        //       name: childrenNodes[i].name
        //     }
        //   ]
        // };
        // nodes.push(obj);
        // }
      }
      for (var index in obj) {
        var newObj = {
          modelLabel: index,
          nodes: obj[index]
        };
        nodes.push(newObj);
      }
      return nodes;
    },
    // 获取节点字列表
    getChildrenNodes1() {
      var clickNode = this.clickNode,
        childrenNodes = clickNode.children || [],
        nodes = [],
        len = childrenNodes.length;
      for (var i = 0; i < len; i++) {
        if (childrenNodes[i].modelLabel === "manuequipment") {
          var obj = {
            modelLabel: childrenNodes[i].modelLabel,
            nodes: [
              {
                id: childrenNodes[i].id,
                name: childrenNodes[i].name
              }
            ]
          };
          nodes.push(obj);
        }
      }
      return nodes;
    },
    getImgUrl: function (val) {
      var me = this;
      var uploadPath = val;
      if (!uploadPath) {
        return "";
      }
      var url = "/eem-service/v1/common/downloadFile?path=" + uploadPath;
      if (!url) {
        return;
      }
      const xhr = new XMLHttpRequest();
      xhr.open("GET", url, true);
      xhr.responseType = "blob";
      xhr.setRequestHeader("Authorization", this.token);
      xhr.onload = () => {
        if (xhr.status === 200) {
          //将图片信息放到Img中
          var imgSrc = window.URL.createObjectURL(xhr.response);
          // debugger
          // return imgSrc;
        }
      };

      xhr.send();
    }
  },

  created: function () {},
  mounted: function () {
    if (!this.clickNode) {
      return;
    }
    if (
      this.clickNode.modelLabel !== "manuequipment" &&
      this.clickNode.modelLabel !== "project"
    ) {
      this.setElectricityTrend();
      this.setIndexRanking();
      this.setElectricRanking();
    }
    if (this.clickNode.modelLabel === "room") {
      this.deviceList = [];
    }
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  // position: relative;
  overflow-x: hidden;
  overflow-y: auto;
}
.cont11-bg {
  position: relative;
  background: url(./assets/u3131.png) no-repeat center;
}

.card-head-title {
  @include font_size(H2);
  @include line_height(H2);
}
.ContImg {
  @include padding(J1);
  box-sizing: border-box;
}
.card-cont {
  height: 80px;
  background: #f9f9f9;
  span {
    display: block;
    padding-left: 24px;
  }
}
.card-cont-num-danger {
  color: #e92f27;
  @include font_size(H2);
}

.card-cont-num-success {
  color: #0152d9;
  @include font_size(H2);
}
.card-head-label {
  line-height: 50px;
  @include font_size(H2);
  color: #0152d9;
  float: left;
  span {
    display: block;
    float: left;
    width: 32px;
    height: 32px;
    border-radius: 32px;
    margin: 9px 15px;
    background: url(./assets/u7103.png) no-repeat center;
  }
}
.card-cont-img {
  display: inline-block;
  width: 200px;
  height: 100%;
  margin: auto;
  background-image: url(./assets/u7752.png);
  background-repeat: round;
}
.gapJ2 {
  @include margin_top(J2);
}
</style>
