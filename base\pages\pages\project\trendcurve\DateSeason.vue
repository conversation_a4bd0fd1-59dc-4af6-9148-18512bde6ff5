<template>
  <div class="date-range">
    <!-- 向前查询按钮 -->
    <CetButton
      class="custom—square mrJ"
      v-bind="CetButton_prv"
      v-on="CetButton_prv.event"
    ></CetButton>
    <div class="basic-box mrJ">
      <CustomElDatePicker
        class="fr"
        :prefix_in="$T('选择季度')"
        v-bind="CetDatePicker_1.config"
        v-model="CetDatePicker_1.val"
      />
    </div>
    <ElSelect
      class="mrJ"
      v-model="ElSelect_1.value"
      v-bind="ElSelect_1"
      v-on="ElSelect_1.event"
    >
      <ElOption
        v-for="item in ElOption_1.options_in"
        :key="item[ElOption_1.key]"
        :label="item[ElOption_1.label]"
        :value="item[ElOption_1.value]"
        :disabled="item[ElOption_1.disabled]"
      ></ElOption>
    </ElSelect>
    <!-- 向后查询按钮 -->
    <CetButton
      class="custom—square"
      v-bind="CetButton_next"
      v-on="CetButton_next.event"
    ></CetButton>
  </div>
</template>
<script>
import moment from "moment";

export default {
  name: "TimeRange",
  props: {
    val: Array,
    disabledDate: Function
  },
  watch: {
    val: {
      deep: true,
      handler: function (val, oldVal) {
        this.CetDatePicker_1.val = this.$moment(val[0])
          .startOf("year")
          .valueOf();
      }
    },
    "CetDatePicker_1.val": function (val) {
      this.deteChange(val);
      this.nextDisable();
      this.changDate();
    },
    "ElSelect_1.value": function (val) {
      this.nextDisable();
    }
  },
  computed: {},
  data() {
    return {
      value: [],
      CetDatePicker_1: {
        disable_in: false,
        val: this.$moment().add(0, "d").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "year",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          clearable: false,
          size: "small",
          style: {
            display: "inline-block",
            width: "200px"
          },
          pickerOptions: {
            disabledDate(time) {
              return time.getTime() > Date.now();
            }
          }
        }
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: true,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      ElSelect_1: {
        value: 1,
        style: {
          width: "150px"
        },
        event: {
          change: this.ElSelect_1_change_out
        },
        size: "small"
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      }
    };
  },
  methods: {
    // 获取季报信息
    changDate() {
      let startTime = null;
      let endTime = null;
      // 取季度时间
      if (this.ElSelect_1.value == 1) {
        startTime = this.$moment(this.CetDatePicker_1.val)
          .month(0)
          .startOf("month")
          .valueOf();
        endTime = this.$moment(this.CetDatePicker_1.val)
          .month(3)
          .startOf("month")
          .valueOf();
      } else if (this.ElSelect_1.value == 2) {
        startTime = this.$moment(this.CetDatePicker_1.val)
          .month(3)
          .startOf("month")
          .valueOf();
        endTime = this.$moment(this.CetDatePicker_1.val)
          .month(6)
          .startOf("month")
          .valueOf();
      } else if (this.ElSelect_1.value == 3) {
        startTime = this.$moment(this.CetDatePicker_1.val)
          .month(6)
          .startOf("month")
          .valueOf();
        endTime = this.$moment(this.CetDatePicker_1.val)
          .month(9)
          .startOf("month")
          .valueOf();
      } else if (this.ElSelect_1.value == 4) {
        startTime = this.$moment(this.CetDatePicker_1.val)
          .month(9)
          .startOf("month")
          .valueOf();
        endTime = this.$moment(this.CetDatePicker_1.val)
          .startOf("year")
          .add(1, "year")
          .startOf("month")
          .valueOf();
      }
      this.$emit("change", [startTime, endTime]);
    },
    // 判断next按钮是否生效
    nextDisable() {
      var currentSeason;
      if (this.$moment().month() < 3) {
        currentSeason = 1;
      } else if (this.$moment().month() < 6) {
        currentSeason = 2;
      } else if (this.$moment().month() < 9) {
        currentSeason = 3;
      } else {
        currentSeason = 4;
      }
      const date = this.$moment(this.CetDatePicker_1.val);
      if (date.year() < this.$moment().year()) {
        this.CetButton_next.disable_in = false;
      } else if (date.year() == this.$moment().year()) {
        if (currentSeason == 1) {
          this.CetButton_next.disable_in = true;
        } else if (this.ElSelect_1.value < currentSeason - 1) {
          this.CetButton_next.disable_in = false;
        } else {
          this.CetButton_next.disable_in = true;
        }
      } else {
        this.CetButton_next.disable_in = true;
      }
    },
    // 判断季度是否可选
    deteChange(val) {
      if (
        this.$moment(val).startOf("year").valueOf() >=
        this.$moment().startOf("year").valueOf()
      ) {
        if (this.$moment().month() < 3) {
          this.ElOption_1.options_in = [
            {
              id: 1,
              text: $T("一季度")
            }
          ];
          this.ElSelect_1.value = 1;
        } else if (this.$moment().month() < 6) {
          this.ElOption_1.options_in = [
            {
              id: 1,
              text: $T("一季度")
            },
            {
              id: 2,
              text: $T("二季度")
            }
          ];
          this.ElSelect_1.value = 1;
        } else if (this.$moment().month() < 9) {
          this.ElOption_1.options_in = [
            {
              id: 1,
              text: $T("一季度")
            },
            {
              id: 2,
              text: $T("二季度")
            },
            {
              id: 3,
              text: $T("三季度")
            }
          ];
          this.ElSelect_1.value = 1;
        } else {
          this.ElOption_1.options_in = [
            {
              id: 1,
              text: $T("一季度")
            },
            {
              id: 2,
              text: $T("二季度")
            },
            {
              id: 3,
              text: $T("三季度")
            },
            {
              id: 4,
              text: $T("四季度")
            }
          ];
          this.ElSelect_1.value = 1;
        }
      } else {
        this.ElOption_1.options_in = [
          {
            id: 1,
            text: $T("一季度")
          },
          {
            id: 2,
            text: $T("二季度")
          },
          {
            id: 3,
            text: $T("三季度")
          },
          {
            id: 4,
            text: $T("四季度")
          }
        ];
      }
    },
    CetButton_prv_statusTrigger_out(val) {
      if (!this.ElSelect_1.value || this.ElSelect_1.value == 1) {
        const date = this.$moment(this.CetDatePicker_1.val);
        this.CetDatePicker_1.val = date.subtract(1, "year").valueOf();
        this.ElSelect_1.value = 4;
      } else {
        this.ElSelect_1_change_out(this.ElSelect_1.value - 1);
      }
    },
    CetButton_next_statusTrigger_out(val) {
      if (this.ElSelect_1.value == 4) {
        const date = this.$moment(this.CetDatePicker_1.val);
        this.CetDatePicker_1.val = date.add(1, "year").valueOf();
        this.ElSelect_1.value = 1;
      } else {
        this.ElSelect_1_change_out(this.ElSelect_1.value + 1);
      }
    },
    ElSelect_1_change_out(val) {
      if (val) {
        this.ElSelect_1.value = val;
        this.changDate();
      }
    }
  },
  created: function () {},
  mounted() {
    this.deteChange(this.$moment().add(0, "d").valueOf());
    this.nextDisable();
    this.changDate();
  }
};
</script>
<style lang="scss" scoped>
.date-range {
  display: flex;
  align-items: center;
  .date-picker {
    flex: 1;
    width: auto;
  }
  .el-button {
    padding: 9px;
  }
}
</style>
