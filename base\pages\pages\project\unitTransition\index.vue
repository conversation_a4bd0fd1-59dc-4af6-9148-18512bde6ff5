<template>
  <div class="page eem-common">
    <div class="fullheight flex-column">
      <div class="flex-auto eem-cont flex-column">
        <div class="mbJ3">
          <CetButton
            class="fr mlJ1"
            v-bind="CetButton_add"
            v-on="CetButton_add.event"
          ></CetButton>
          <div class="fr">
            <customElSelect
              v-model="ElSelect_energyType.value"
              v-bind="ElSelect_energyType"
              v-on="ElSelect_energyType.event"
              :prefix_in="typeName"
            >
              <ElOption
                v-for="item in ElOption_energyType.options_in"
                :key="item[ElOption_energyType.key]"
                :label="item[ElOption_energyType.label]"
                :value="item[ElOption_energyType.value]"
                :disabled="item[ElOption_energyType.disabled]"
              ></ElOption>
            </customElSelect>
          </div>
          <div class="fr mrJ1">
            <customElSelect
              v-model="ElSelect_unitClass.value"
              v-bind="ElSelect_unitClass"
              v-on="ElSelect_unitClass.event"
              :prefix_in="$T('类型归类')"
            >
              <ElOption
                v-for="item in ElOption_unitClass.options_in"
                :key="item[ElOption_unitClass.key]"
                :label="item[ElOption_unitClass.label]"
                :value="item[ElOption_unitClass.value]"
                :disabled="item[ElOption_unitClass.disabled]"
              ></ElOption>
            </customElSelect>
          </div>
        </div>
        <CetTable
          class="eem-table-custom flex-auto"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
          <ElTableColumn
            v-bind="ElTableColumn_energyType"
            :label="typeName"
          ></ElTableColumn>
          <ElTableColumn
            v-bind="ElTableColumn_basicUnitSymbolName"
          ></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_uniten"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_unitcn"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_coef"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_handele">
            <template slot-scope="scope">
              <span class="handel fl mrJ3" @click="handelEdit(scope.row)">
                {{ $T("编辑") }}
              </span>
              <span class="handel delete fl" @click="handelDelete(scope.row)">
                {{ $T("删除") }}
              </span>
            </template>
          </ElTableColumn>
        </CetTable>
      </div>
    </div>
    <add
      :projectEnergytype="ElOption_energyType.options_in"
      :visibleTrigger_in="add.visibleTrigger_in"
      :editData_in="add.editData_in"
      :tableData_in="CetTable_1.data"
      :unitClass_in="ElSelect_unitClass.value"
      :closeTrigger_in="add.closeTrigger_in"
      @updata_out="getData"
    />
  </div>
</template>
<script>
import commonApi from "@/api/custom.js";
import add from "./add.vue";
export default {
  name: "unitTransition",
  components: {
    add
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    }
  },

  data(vm) {
    const en = window.localStorage.getItem("omega_language") === "en";
    return {
      add: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        editData_in: null
      },
      ElSelect_energyType: {
        value: "",
        style: {
          width: en ? "310px" : "200px"
        },
        event: {
          change: this.ElSelect_energyType_change_out
        }
      },
      ElOption_energyType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_unitClass: {
        value: 1,
        style: {
          width: en ? "310px" : "200px"
        },
        event: {
          change: this.ElSelect_unitClass_change_out
        }
      },
      ElOption_unitClass: {
        options_in: [
          {
            id: 1,
            name: $T("能耗")
          },
          {
            id: 2,
            name: $T("产品")
          },
          {
            id: 4,
            name: $T("其他")
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("新增"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "getUnitTransition",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [
            { name: "energyType_in", operator: "EQ", prop: "energyType" },
            {
              name: "projectUnitClassify_in",
              operator: "EQ",
              prop: "projectUnitClassify"
            },
            { name: "projectId", operator: "EQ", prop: "projectId" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {
          projectUnitClassify_in: null,
          energyType_in: null,
          projectId: null
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      ElTableColumn_index: {
        type: "index",
        width: "50px",
        label: "#",
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      ElTableColumn_handele: {
        width: en ? 120 : 100, //绝对宽度
        label: $T("操作"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      ElTableColumn_energyType: {
        prop: "typeName",
        minWidth: en ? "160px" : "120px",
        label: $T("能耗类型"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_basicUnitSymbolName: {
        prop: "basicUnitSymbolName",
        minWidth: "120px",
        label: $T("基础单位"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_uniten: {
        prop: "uniten",
        minWidth: "120px",
        label: $T("目标单位(英文)"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_unitcn: {
        prop: "unitcn",
        minWidth: "120px",
        label: $T("目标单位(中文)"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_coef: {
        prop: "coef",
        minWidth: "120px",
        label: $T("系数"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        }
      },
      productList: [],
      energyList: [],
      otherUnitTypeList: [],
      typeName: $T("能耗类型")
    };
  },
  watch: {},

  methods: {
    init() {
      this.CetTable_1.data = [];
      this.CetTable_1.dynamicInput.projectId = this.projectId;
      Promise.all([
        new Promise((resolve, reject) => {
          this.queryProductList_out(resolve);
        }),
        new Promise((resolve, reject) => {
          this.queryProjectEnergyList_out(resolve);
        }),
        new Promise((resolve, reject) => {
          this.queryOtherUnitType_out(resolve);
        })
      ]).then(() => {
        this.ElSelect_unitClass_change_out(this.ElSelect_unitClass.value);
      });
    },
    //获取产品计量参数列表
    queryProductList_out(callback) {
      const params = {
        projectId: this.projectId
      };
      commonApi.queryProductList(params).then(response => {
        if (response.code === 0) {
          this.productList = response.data || [];
        } else {
          this.productList = [];
        }
        callback && callback();
      });
    },
    // 获取能耗计量参数列表
    queryProjectEnergyList_out(callback) {
      const params = {
        projectId: this.projectId
      };
      commonApi.queryProjectEnergyList(params).then(response => {
        if (response.code === 0) {
          this.energyList = response.data || [];
        } else {
          this.energyList = [];
        }
        callback && callback();
      });
    },
    // 获取其他类型列表
    queryOtherUnitType_out(callback) {
      const list = this.$store.state.enumerations.otherunittype || [];
      list.forEach(item => {
        item.name = item.text;
      });
      this.otherUnitTypeList = list;
      callback && callback();
    },
    getData() {
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    handelEdit(row) {
      this.add.editData_in = this._.cloneDeep(row);
      this.add.visibleTrigger_in = new Date().getTime();
    },
    handelDelete(row) {
      this.$confirm($T("确定要删除吗?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          commonApi.deleteUnitTransition([row.id]).then(response => {
            if (response.code === 0) {
              this.$message({
                type: "success",
                message: $T("删除成功!")
              });
              this.getData();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已取消删除")
          });
        });
    },
    ElSelect_energyType_change_out(val) {
      const list = this.ElOption_energyType.options_in || [];
      const clickVal = list.find(item => item.id === val);
      if (clickVal && this.ElSelect_unitClass.value === 1) {
        this.CetTable_1.dynamicInput.energyType_in = clickVal.energytype;
      } else if (clickVal && this.ElSelect_unitClass.value === 2) {
        this.CetTable_1.dynamicInput.energyType_in = clickVal.producttype;
      } else if (clickVal && this.ElSelect_unitClass.value === 4) {
        this.CetTable_1.dynamicInput.energyType_in = clickVal.id;
      }
      this.getData();
    },
    ElSelect_unitClass_change_out(val) {
      this.ElSelect_energyType.value = null;
      this.ElOption_energyType.options_in = [];
      if (val === 1) {
        this.typeName = $T("能耗类型");
        this.ElOption_energyType.options_in = this._.cloneDeep(this.energyList);
        this.CetTable_1.dynamicInput.energyType_in = this._.get(
          this.energyList,
          "[0].energytype",
          null
        );
      } else if (val === 2) {
        this.typeName = $T("产品类型");
        this.ElOption_energyType.options_in = this._.cloneDeep(
          this.productList
        );
        this.CetTable_1.dynamicInput.energyType_in = this._.get(
          this.productList,
          "[0].producttype",
          null
        );
      } else if (val === 4) {
        this.typeName = $T("其他类型");
        this.ElOption_energyType.options_in = this._.cloneDeep(
          this.otherUnitTypeList
        );
        this.CetTable_1.dynamicInput.energyType_in = this._.get(
          this.otherUnitTypeList,
          "[0].id",
          null
        );
      }
      if (this.ElOption_energyType.options_in.length > 0) {
        this.ElSelect_energyType.value =
          this.ElOption_energyType.options_in[0].id;
      }
      this.CetTable_1.dynamicInput.projectUnitClassify_in =
        this._.cloneDeep(val);
      this.getData();
    },
    CetButton_add_statusTrigger_out(val) {
      this.add.editData_in = null;
      this.add.visibleTrigger_in = new Date().getTime();
    }
  },
  created: function () {},
  activated: function () {
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;
}
.handel {
  cursor: pointer;
  @include font_color(ZS);
  &.delete {
    @include font_color(Sta3);
  }
}
</style>
