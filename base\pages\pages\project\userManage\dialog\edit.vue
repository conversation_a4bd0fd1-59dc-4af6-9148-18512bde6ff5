<template>
  <!-- 1弹窗组件 -->
  <div>
    <CetDialog
      class="CetDialog eem-common max"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
    >
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </span>
      <CetForm
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
      >
        <div class="title">{{ $T("用户信息") }}</div>
        <div class="cardBox eem-cont mtJ1 clearfix">
          <el-row :gutter="$J3">
            <el-col :span="6">
              <el-form-item :label="$T('账户名称')" prop="name">
                <ElInput
                  v-model.trim="CetForm_1.data.name"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$T('姓名')" prop="nicName">
                <ElInput
                  v-model.trim="CetForm_1.data.nicName"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$T('移动电话')" prop="mobilePhone">
                <ElInput
                  v-model.trim="CetForm_1.data.mobilePhone"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$T('电子信箱')" prop="email">
                <ElInput
                  v-model.trim="CetForm_1.data.email"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                :label="$T('账户密码')"
                prop="password"
                v-if="!userId"
              >
                <ElInput
                  v-model.trim="CetForm_1.data.password"
                  v-bind="ElInput_1"
                  showPassword
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                :label="$T('确认密码')"
                prop="confirmPassword"
                v-if="!userId"
              >
                <ElInput
                  v-model.trim="CetForm_1.data.confirmPassword"
                  v-bind="ElInput_1"
                  showPassword
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$T('角色')" prop="role">
                <ElSelect
                  v-model="CetForm_1.data.role"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                >
                  <ElOption
                    v-for="item in ElOption_1.options_in"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$T('用户组')" prop="relativeUserGroup">
                <ElSelect
                  v-model="CetForm_1.data.relativeUserGroup"
                  v-bind="ElSelect_userGroup"
                  v-on="ElSelect_userGroup.event"
                >
                  <ElOption
                    v-for="item in ElOption_userGroup.options_in"
                    :key="item[ElOption_userGroup.key]"
                    :label="item[ElOption_userGroup.label]"
                    :value="item[ElOption_userGroup.value]"
                    :disabled="item[ElOption_userGroup.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                v-if="$checkPermission('user_disable') && inputData_in"
                :label="$T('状态')"
                prop="state"
              >
                <ElSelect
                  v-model="CetForm_1.data.state"
                  v-bind="ElSelect_state"
                  v-on="ElSelect_state.event"
                >
                  <ElOption
                    v-for="item in ElOption_state.options_in"
                    :key="item[ElOption_state.key]"
                    :label="item[ElOption_state.label]"
                    :value="item[ElOption_state.value]"
                    :disabled="item[ElOption_state.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <CetButton
                class="fr"
                v-bind="CetButton_infoSave"
                v-on="CetButton_infoSave.event"
              ></CetButton>
            </el-col>
          </el-row>
        </div>
      </CetForm>
      <div class="bottomBox flex-row" v-show="!authBtnDisable">
        <div class="leftContent mrJ3">
          <div class="title ptJ2 pbJ1">{{ $T("通知类型") }}</div>
          <div class="noticeContent bg1">
            <div class="label mbJ1">{{ $T("通知方式") }}</div>
            <ElCheckboxGroup
              v-model="ElCheckboxGroup_1.value"
              v-bind="ElCheckboxGroup_1"
              v-on="ElCheckboxGroup_1.event"
              @change="checkboxChange"
            >
              <ElCheckbox
                class="fullwidth"
                v-for="item in ElCheckboxList_1.options_in"
                :key="item[ElCheckboxList_1.key]"
                :label="item[ElCheckboxList_1.label]"
                :disabled="item[ElCheckboxList_1.disabled]"
              >
                {{ item[ElCheckboxList_1.text] }}
              </ElCheckbox>
            </ElCheckboxGroup>
          </div>
        </div>
        <div class="rightContent flex-auto">
          <el-tabs
            v-model="activeName"
            @tab-click="handleClick"
            class="eem-tabs-custom ptJ2 pbJ1"
          >
            <el-tab-pane
              v-for="item in userManageTabs"
              :label="item.label"
              :name="item.name"
              :key="item.name"
            ></el-tab-pane>
          </el-tabs>
          <div class="treeBox bg1">
            <div v-show="activeName === 'project'" class="fullfilled">
              <CetButton
                class="fr mlJ2 saveBtn"
                :disable_in="authBtnDisable"
                v-bind="CetButton_project"
                v-on="CetButton_project.event"
              ></CetButton>
              <CetGiantTree
                ref="projectTree"
                class="CetTree eem-no-line"
                v-bind="CetGiantTree_project"
                v-on="CetGiantTree_project.event"
              ></CetGiantTree>
            </div>
            <div v-show="activeName === 'Graph'" class="fullfilled">
              <CetButton
                class="fr mlJ2 saveBtn"
                :disable_in="authBtnDisable"
                v-bind="CetButton_graph"
                v-on="CetButton_graph.event"
              ></CetButton>
              <CetGiantTree
                ref="graphTree"
                class="CetTree eem-no-line"
                v-bind="CetGiantTree_graph"
                v-on="CetGiantTree_graph.event"
              ></CetGiantTree>
            </div>
            <div v-show="activeName === 'pecReport'" class="fullfilled">
              <CetButton
                class="fr mlJ2 saveBtn"
                :disable_in="authBtnDisable"
                v-bind="CetButton_pecReport"
                v-on="CetButton_pecReport.event"
              ></CetButton>
              <CetGiantTree
                ref="pecReportTree"
                class="CetTree eem-no-line"
                v-bind="CetGiantTree_pecReport"
                v-on="CetGiantTree_pecReport.event"
              ></CetGiantTree>
            </div>
            <div v-show="activeName === 'mReport'" class="fullfilled">
              <CetButton
                class="fr mlJ2 saveBtn"
                :disable_in="authBtnDisable"
                v-bind="CetButton_mReport"
                v-on="CetButton_mReport.event"
              ></CetButton>
              <CetGiantTree
                ref="mReportTree"
                class="CetTree eem-no-line"
                v-bind="CetGiantTree_mReport"
                v-on="CetGiantTree_mReport.event"
              ></CetGiantTree>
            </div>
            <div v-show="activeName === 'onlyReport'" class="fullfilled">
              <CetButton
                class="fr mlJ2 saveBtn"
                :disable_in="authBtnDisable"
                v-bind="CetButton_onlyReport"
                v-on="CetButton_onlyReport.event"
              ></CetButton>
              <CetGiantTree
                ref="onlyReportTree"
                class="CetTree eem-no-line"
                v-bind="CetGiantTree_onlyReport"
                v-on="CetGiantTree_onlyReport.event"
              ></CetGiantTree>
            </div>
            <div v-show="activeName === 'dashboard'" class="fullfilled">
              <CetButton
                class="fr mlJ2 saveBtn"
                :disable_in="authBtnDisable"
                v-bind="CetButton_dashboard"
                v-on="CetButton_dashboard.event"
              ></CetButton>
              <CetGiantTree
                ref="dashboard"
                class="CetTree eem-no-line"
                v-bind="CetGiantTree_dashboard"
                v-on="CetGiantTree_dashboard.event"
              ></CetGiantTree>
            </div>
            <div v-show="activeName === 'video'" class="fullfilled">
              <CetButton
                class="fr mlJ2 saveBtn"
                :disable_in="authBtnDisable"
                v-bind="CetButton_video"
                v-on="CetButton_video.event"
              ></CetButton>
              <CetGiantTree
                class="CetTree eem-no-line"
                ref="videoTree"
                v-bind="CetGiantTree_video"
                v-on="CetGiantTree_video.event"
              ></CetGiantTree>
            </div>
          </div>
        </div>
      </div>
      <div class="bottomBox bg1 mtJ2 brC1 pJ2" v-show="authBtnDisable">
        <span class="pJ2">{{ $T("请先保存基本信息") }}</span>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import common from "eem-utils/common";
import customApi from "@/api/custom.js";
// const modelLabels = ["project", "sectionarea", "building", "floor", "room"];
import TREE_PARAMS from "@/store/treeParams.js";
export default {
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    userDashboardList: {
      type: Array,
      default() {
        return [];
      }
    }
  },

  computed: {
    authBtnDisable() {
      return this.userId ? false : true;
    },
    projectTenantId() {
      return this.$store.state.projectTenantId;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    isRoot() {
      return this.$store.state.userInfo.id === 1;
    },
    isPlatformModel() {
      return this.$store.state.systemCfg.isPlatformModel;
    },
    userManageTabs() {
      const en = window.localStorage.getItem("omega_language") === "en";
      const tabNameList = {
        project: $T("项目节点权限"),
        Graph: $T("实时监控权限"),
        pecReport: $T("pecReport报表权限"),
        mReport: $T("mReport报表权限"),
        onlyReport: $T("onlyReport报表权限"),
        dashboard: $T("dashboard权限"),
        video: $T("视频权限")
      };
      if (!this.$store.state.systemCfg.userManageTabs) {
        const keys = Object.keys(tabNameList);
        return keys.map(item => {
          return {
            label: tabNameList[item],
            name: item
          };
        });
      } else {
        let userManageTabs = [];
        const systemCfgTabs = this.$store.state.systemCfg.userManageTabs;
        systemCfgTabs.forEach(item => {
          if (tabNameList[item.name]) {
            const label = en ? item.enLabel : item.label;
            userManageTabs.push({
              name: item.name,
              label: label ? label : tabNameList[item.name]
            });
          }
        });
        return userManageTabs;
      }
    }
  },

  data(vm) {
    const addDiyDom = (treeId, treeNode) => {
        var aObj = $("#" + treeNode.tId + "_a");
        if (treeNode.level === 0 && vm.treeNodeTooltip) {
          vm.treeNodeTooltip = false;
          const dom = `
            <div class="inline-block relative tooltipBox">
              <i class="el-icon-question fcT2"></i>
              <div class="tooltip el-tooltip__popper is-light" x-placement="bottom">${$T(
                "置灰代表未拥有此节点全部权限"
              )}
                <div x-arrow="" class="popper__arrow" style="left: 49.5px;"></div>
              </div>
            </div>`;
          aObj.append(dom);
        }
      },
      setNodeClasses = (treeId, treeNode) => {
        return treeNode.childSelectState !== 1
          ? { add: ["halfSelectedNode"] }
          : { remove: ["halfSelectedNode"] };
      };
    return {
      treeNodeTooltip: true,
      userId: 0,
      lastName: "project",
      activeName: "project",
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          close: this.CetDialog_1_close_out
        }
      },
      CetButton_infoSave: {
        visible_in: true,
        disable_in: false,
        title: $T("保存基本信息"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_infoSave_statusTrigger_out
        }
      },
      CetButton_project: {
        visible_in: true,
        // disable_in: false,
        title: $T("保存项目节点权限"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_project_statusTrigger_out
        }
      },
      CetButton_graph: {
        visible_in: true,
        // disable_in: false,
        title: $T("保存实时监控权限"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_graph_statusTrigger_out
        }
      },
      CetButton_pecReport: {
        visible_in: true,
        // disable_in: false,
        title: $T("保存报表权限"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_pecReport_statusTrigger_out
        }
      },
      CetButton_mReport: {
        visible_in: true,
        // disable_in: false,
        title: $T("保存报表权限"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_mReport_statusTrigger_out
        }
      },
      CetButton_onlyReport: {
        visible_in: true,
        title: $T("保存报表权限"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_onlyReport_statusTrigger_out
        }
      },
      CetButton_dashboard: {
        visible_in: true,
        // disable_in: false,
        title: $T("保存dashboard权限"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_dashboard_statusTrigger_out
        }
      },
      CetButton_video: {
        visible_in: true,
        disable_in: false,
        title: $T("保存视频权限"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_video_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: ["name", "tenantId", "parentId"], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入账户名称")
            },
            common.pattern_name,
            common.check_stringLessThan50
          ],
          nicName: [
            {
              required: true,
              message: $T("请输入账户编号"),
              trigger: ["blur", "change"]
            },
            common.pattern_name,
            common.check_stringLessThan50
          ],
          password: [
            {
              required: true,
              message: $T("请输入账户密码"),
              trigger: ["blur", "change"]
            },
            {
              checkType: "password",
              validatorProp: "cetconfirmpassword",
              trigger: ["blur"]
            },
            common.check_strongPassword
          ],
          confirmPassword: [
            {
              required: true,
              message: $T("请输入确认密码"),
              trigger: ["blur", "change"]
            },
            {
              checkType: "checkPassword",
              relationProp: "password",
              trigger: "blur"
            }
          ],
          mobilePhone: [
            {
              required: true,
              message: $T("请输入手机号码"),
              trigger: ["blur", "change"]
            },
            common.check_phone
          ],
          role: [
            {
              required: true,
              message: $T("请选择角色"),
              trigger: ["blur", "change"]
            }
          ],
          relativeUserGroup: [
            {
              required: true,
              message: $T("请选择用户组"),
              trigger: ["blur", "change"]
            }
          ],
          state: [
            {
              required: true,
              message: $T("请选择状态"),
              trigger: ["blur", "change"]
            }
          ],
          email: [
            {
              type: "email",
              message: $T("请输入正确的邮箱格式"),
              trigger: "change"
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入"),
        style: {
          width: "100%"
        },
        event: {}
      },
      ElSelect_1: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_userGroup: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_userGroup: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_state: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_state: {
        options_in: [
          {
            id: 0,
            text: $T("正常")
          },
          {
            id: 1,
            text: $T("停用")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      //通知类型选择
      ElCheckboxGroup_1: {
        value: [],
        style: {},
        event: {}
      },
      ElCheckboxList_1: {
        options_in: vm.$store.state.systemCfg.alarmnotificationmethodList || [],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      CetGiantTree_project: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "name"
            }
          },
          callback: {
            onCheck: this.CetGiantTree_project_checkedNodes_out
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {
          // checkedNodes_out: this.CetGiantTree_project_checkedNodes_out //勾选节点输出
        }
      },
      CetGiantTree_graph: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "text"
            }
          },
          callback: {
            onCheck: this.CetGiantTree_graph_checkedNodes_out
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {
          // checkedNodes_out: this.CetGiantTree_graph_checkedNodes_out //勾选节点输出
        }
      },
      CetGiantTree_pecReport: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "nodeName"
            }
          },
          callback: {
            onCheck: this.CetGiantTree_pecReport_checkedNodes_out
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {
          // checkedNodes_out: this.CetGiantTree_pecReport_checkedNodes_out //勾选节点输出
        }
      },
      CetGiantTree_mReport: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "nodeName"
            }
          },
          callback: {
            onCheck: this.CetGiantTree_mReport_checkedNodes_out
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {
          // checkedNodes_out: this.CetGiantTree_mReport_checkedNodes_out //勾选节点输出
        }
      },
      CetGiantTree_dashboard: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "id"
            },
            key: {
              name: "name"
            }
          },
          callback: {
            onCheck: this.CetGiantTree_dashboard_checkedNodes_out
          }
        },
        event: {
          // checkedNodes_out: this.CetGiantTree_dashboard_checkedNodes_out //勾选节点输出
        }
      },
      CetGiantTree_video: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "name"
            }
          },
          callback: {
            onCheck: this.CetGiantTree_video_checkedNodes_out
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {
          // checkedNodes_out: this.CetGiantTree_video_checkedNodes_out //勾选节点输出
        }
      },
      CetGiantTree_onlyReport: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "id"
            },
            key: {
              name: "name"
            }
          },
          callback: {
            onCheck: this.CetGiantTree_onlyReport_checkedNodes_out
          },
          view: {
            addDiyDom: addDiyDom,
            nodeClasses: setNodeClasses
          }
        },
        event: {
          // checkedNodes_out: this.CetGiantTree_onlyReport_checkedNodes_out //勾选节点输出
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      this.init();
      vm.CetDialog_1.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    CetDialog_1_close_out() {
      this.$emit("finishTrigger_out");
    },
    init() {
      this.initTree();
      this.getRoleBytenantId();
      this.getUserGroup();
      this.userId = 0;
      if (this.inputData_in) {
        this.userId = this.inputData_in.id;
        this.CetDialog_1.title = $T("编辑用户");
        this.getDetail();
      } else {
        this.initDashboardTree();
        this.CetDialog_1.title = $T("新增用户");
        this.CetForm_1.data = {};
        this.ElCheckboxGroup_1.value = [];
        this.CetForm_1.resetTrigger_in = new Date().getTime();
      }
      this.lastName = this._.get(this.userManageTabs, "[0].name", "");
      this.activeName = this.lastName;
      this.initTooltip();
      this.handleClick({
        name: this.activeName
      });
    },
    initDashboardTree() {
      this.CetGiantTree_dashboard.saveFlag = true;
      this.CetGiantTree_dashboard.checkedFlag = false;
      this.CetGiantTree_dashboard.inputData_in = this.userDashboardList || [];
    },
    initTree() {
      this.CetGiantTree_project.checkedNodes = [];
      this.CetGiantTree_project.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_graph.checkedNodes = [];
      this.CetGiantTree_graph.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_pecReport.checkedNodes = [];
      this.CetGiantTree_pecReport.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_mReport.checkedNodes = [];
      this.CetGiantTree_mReport.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_video.checkedNodes = [];
      this.CetGiantTree_video.unCheckTrigger_in = new Date().getTime();
    },
    // 获取角色
    getRoleBytenantId() {
      var vm = this;
      var tenantId = vm.projectTenantId;

      customApi.getTenantRole(tenantId).then(response => {
        if (response.code === 0) {
          let data = vm._.get(response, "data", []);
          if (vm.isPlatformModel) {
            data = data.filter(item => {
              if (item.customConfig && JSON.parse(item.customConfig)) {
                return JSON.parse(item.customConfig).roletype === 4;
              }
            });
          }
          vm.ElOption_1.options_in = data;
        }
      });
    },
    // 获取用户组
    getUserGroup() {
      var vm = this;
      customApi.getUserGroups().then(response => {
        if (response.code === 0) {
          var data = vm._.get(response, "data", []);
          vm.ElOption_userGroup.options_in = data;
        }
      });
    },
    async getDetail() {
      const vm = this;
      if (!vm.userId) {
        vm.initTree();
        return;
      }
      let res = await customApi.queryUserInfoById(vm.userId);
      if (res.code === 0) {
        let data = vm._.get(res, "data");
        // 基础信息
        let fromData = {
          id: data.id,
          name: data.name,
          email: data.email,
          mobilePhone: data.mobilePhone,
          nicName: data.nicName,
          relativeUserGroup: vm._.get(data, "relativeUserGroup[0]"),
          role: vm._.get(data, "roles[0].id"),
          state: data.state,
          tenantId: data.tenantId,
          customConfig: data.customConfig ? JSON.parse(data.customConfig) : {}
        };
        vm.CetForm_1.data = fromData;
        vm.CetForm_1.resetTrigger_in = new Date().getTime();
        // 通知
        let notifytypeArr = [];
        if (data.customConfig) {
          notifytypeArr = JSON.parse(data.customConfig).notifytype || [];
        }
        vm.ElCheckboxGroup_1.value = notifytypeArr;
        vm.setDashboardCheckedNodes(data);
      }
    },
    // 展开节点
    expandNode(nodes, key, ztreeObj) {
      setTimeout(() => {
        nodes.forEach(item => {
          let node = ztreeObj.getNodeByParam(key, item[key]);
          let parentNodes = [],
            parentNode = node && node.getParentNode();
          while (parentNode) {
            parentNodes.push(parentNode);
            parentNode = parentNode.getParentNode();
          }
          parentNodes.forEach(i => {
            ztreeObj.expandNode(i, true);
          });
        });
      }, 0);
    },
    setDashboardCheckedNodes(userInfo) {
      this.CetGiantTree_dashboard.saveFlag = false;
      var customConfig = this._.get(userInfo, "customConfig")
        ? JSON.parse(this._.get(userInfo, "customConfig"))
        : {};
      let dashboard = customConfig.dashboard ? customConfig.dashboard : [];
      this.CetForm_1.data.dashboard = this._.cloneDeep(dashboard);
      this.CetGiantTree_dashboard.checkedNodes = dashboard.map(i => {
        return { id: i };
      });
      if (!this.CetGiantTree_dashboard.checkedNodes.length) {
        this.CetGiantTree_dashboard.unCheckTrigger_in = new Date().getTime();
      }
      this.CetGiantTree_dashboard.inputData_in =
        this._.cloneDeep(this.userDashboardList) || [];

      this.CetGiantTree_dashboard.checkedFlag =
        this.CetGiantTree_dashboard.inputData_in.length &&
        this.CetGiantTree_dashboard.checkedNodes.length;
      this.$nextTick(() => {
        this.CetGiantTree_dashboard.saveFlag = true;
      });
    },
    async handleClick(val) {
      let obj = {
        project: this.CetGiantTree_project.confirmHint,
        Graph: this.CetGiantTree_graph.confirmHint,
        pecReport: this.CetGiantTree_pecReport.confirmHint,
        mReport: this.CetGiantTree_mReport.confirmHint,
        dashboard: this.CetGiantTree_dashboard.confirmHint,
        video: this.CetGiantTree_video.confirmHint,
        onlyReport: this.CetGiantTree_onlyReport.confirmHint
      };
      if (obj[this.lastName]) {
        this.$confirm($T("节点权限已修改，是否放弃?"), $T("提示"), {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        })
          .then(async () => {
            await this.getTree(val);
          })
          .catch(() => {
            this.activeName = this.lastName;
          });
      } else {
        await this.getTree(val);
      }
    },
    initTooltip() {
      this.CetGiantTree_project.confirmHint = false;
      this.CetGiantTree_graph.confirmHint = false;
      this.CetGiantTree_pecReport.confirmHint = false;
      this.CetGiantTree_mReport.confirmHint = false;
      this.CetGiantTree_dashboard.confirmHint = false;
      this.CetGiantTree_video.confirmHint = false;
      this.CetGiantTree_onlyReport.confirmHint = false;
    },
    async getTree(val) {
      this.treeNodeTooltip = true;
      this.lastName = val.name;
      this.initTooltip();
      switch (val.name) {
        case "project":
          await this.getProjectTree();
          break;
        case "Graph":
          await this.getGraphTree();
          break;
        case "pecReport":
          await this.getPecReportTree_out();
          break;
        case "mReport":
          await this.getMReportTree_out();
          break;
        case "onlyReport":
          await this.getOnlyReportTree_out();
          break;
        case "video":
          await this.getVideoTree_out();
          break;
        default:
          break;
      }
    },
    // 获取用户项目节点权限
    async getProjectTree() {
      let vm = this,
        nodes = [];
      vm.CetGiantTree_project.saveFlag = false;
      if (vm.userId) {
        const modelLabels = TREE_PARAMS.userManageModelLabels || [];
        let res = await customApi.userModelnodeQuery(modelLabels, {
          userId: vm.userId
        });
        let data = vm._.get(res, "data", []) || [];
        data.forEach(i => {
          if (i.childSelectState !== 2) {
            nodes.push({
              id: i.id,
              nodeId: i.id,
              nodeType: i.modelLabel,
              tree_id: `${i.modelLabel}_${i.id}`
            });
          }
        });
      }
      vm.CetGiantTree_project.checkedNodes = nodes;
      if (!nodes.length) {
        vm.CetGiantTree_project.unCheckTrigger_in = new Date().getTime();
      }

      let queryData = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.userManageProjectTree,
        treeReturnEnable: true
      };
      await customApi.getNodeTreeSimple(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_project.inputData_in = this.formatNodeTree(data);
          setTimeout(() => {
            this.expandNode(
              nodes,
              this.CetGiantTree_project.setting.data.simpleData.idKey,
              this.$refs.projectTree.ztreeObj
            );
          }, 0);
        }
        this.$nextTick(() => {
          vm.CetGiantTree_project.saveFlag = true;
        });
      });
    },
    formatNodeTree(rawNodeAry) {
      var me = this;
      rawNodeAry = rawNodeAry || [];
      me._(rawNodeAry).forEach(function (rawNode) {
        if (!rawNode.children || !rawNode.children.length) {
          rawNode.children = null;
          rawNode.leaf = true;
        } else {
          rawNode.children = me.formatNodeTree(rawNode.children);
        }
      });
      return rawNodeAry;
    },
    // 获取图形节点权限
    async getGraphTree() {
      let vm = this,
        nodes = [];
      vm.CetGiantTree_graph.saveFlag = false;
      if (vm.userId) {
        let res = await customApi.userGraphnodeQuery(
          this.$store.state.graphTypes,
          { userId: vm.userId }
        );
        let data = vm._.get(res, "data", []) || [];
        data.forEach(i => {
          if (i.childSelectState !== 2) {
            nodes.push({
              nodeId: i.nodeID,
              nodeName: i.nodeName,
              text: i.text,
              nodeType: i.nodeType,
              tree_id: `${i.nodeType}_${i.nodeID}`
            });
          }
        });
      }
      vm.CetGiantTree_graph.checkedNodes = nodes;
      if (!nodes.length) {
        vm.CetGiantTree_graph.unCheckTrigger_in = new Date().getTime();
      }

      var queryData = {
        tenantId: this.projectTenantId
      };
      await customApi.getGraphTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_graph.inputData_in = this.formatGraphNodeTree(data);
          setTimeout(() => {
            this.expandNode(
              nodes,
              this.CetGiantTree_graph.setting.data.simpleData.idKey,
              this.$refs.graphTree.ztreeObj
            );
          }, 0);
        } else {
          this.CetGiantTree_graph.inputData_in = [];
        }
        this.$nextTick(() => {
          vm.CetGiantTree_graph.saveFlag = true;
        });
      });
    },
    formatGraphNodeTree(rawNodeAry) {
      var me = this;
      rawNodeAry = rawNodeAry || [];

      me._(rawNodeAry).forEach(function (rawNode) {
        rawNode.id = rawNode.nodeId;
        if (!rawNode.children || !rawNode.children.length) {
          rawNode.children = null;
          rawNode.leaf = true;
        } else {
          rawNode.leaf = false;
          rawNode.children = me.formatGraphNodeTree(rawNode.children);
        }
        rawNode.tree_id = `${rawNode.nodeType}_${rawNode.id}`;
      });
      return rawNodeAry;
    },
    // 获取报表节点权限
    async getPecReportTree_out() {
      let vm = this,
        nodes = [];
      vm.CetGiantTree_pecReport.saveFlag = false;
      if (vm.userId) {
        let res = await customApi.userPecstarnodeQuery(
          this.$store.state.pReportTypes,
          { userId: vm.userId }
        );
        let data = vm._.get(res, "data", []) || [];
        data.forEach(i => {
          if (i.childSelectState !== 2) {
            nodes.push({
              id: `${i.nodeType}_${i.nodeID}`,
              nodeId: i.nodeID,
              nodeType: i.nodeType,
              tree_id: `${i.nodeType}_${i.nodeID}`
            });
          }
        });
      }
      if (!nodes.length) {
        vm.CetGiantTree_pecReport.unCheckTrigger_in = new Date().getTime();
      }
      vm.CetGiantTree_pecReport.checkedNodes = nodes;

      var queryData = {
        tenantId: vm.projectTenantId
      };
      await customApi.getPecReportTree(queryData).then(response => {
        if (response.code === 0) {
          var data = vm._.get(response, "data", []);
          vm.CetGiantTree_pecReport.inputData_in =
            vm.formatReporthNodeTree(data);
          setTimeout(() => {
            vm.expandNode(
              nodes,
              vm.CetGiantTree_pecReport.setting.data.simpleData.idKey,
              vm.$refs.pecReportTree.ztreeObj
            );
          }, 0);
        } else {
          vm.CetGiantTree_pecReport.inputData_in = [];
        }
        vm.$nextTick(() => {
          vm.CetGiantTree_pecReport.saveFlag = true;
        });
      });
    },
    // 获取报表节点权限
    async getMReportTree_out() {
      let vm = this,
        nodes = [];
      vm.CetGiantTree_mReport.saveFlag = false;
      if (vm.userId) {
        let res = await customApi.userModelnodeQuery(
          this.$store.state.mReportTypes,
          { userId: vm.userId }
        );
        let data = vm._.get(res, "data", []) || [];
        data.forEach(i => {
          if (i.childSelectState !== 2) {
            nodes.push({
              nodeId: i.id,
              nodeType: i.modelLabel,
              tree_id: `${i.modelLabel}_${i.id}`
            });
          }
        });
      }
      vm.CetGiantTree_mReport.checkedNodes = nodes;
      if (!nodes.length) {
        vm.CetGiantTree_mReport.unCheckTrigger_in = new Date().getTime();
      }

      var queryData = {
        tenantId: this.projectTenantId
      };
      await customApi.getMReportTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_mReport.inputData_in =
            this.formatReporthNodeTree(data);
          setTimeout(() => {
            this.expandNode(
              nodes,
              this.CetGiantTree_mReport.setting.data.simpleData.idKey,
              this.$refs.mReportTree.ztreeObj
            );
          }, 0);
        } else {
          this.CetGiantTree_mReport.inputData_in = [];
        }
        this.$nextTick(() => {
          vm.CetGiantTree_mReport.saveFlag = true;
        });
      });
    },
    // 获取onlyReport报表节点权限
    async getOnlyReportTree_out() {
      let vm = this,
        nodes = [];
      vm.CetGiantTree_onlyReport.saveFlag = false;
      if (vm.userId) {
        const modelLabels = this.$store.state.onlyReportTypes;
        let res = await customApi.userModelnodeQuery(modelLabels, {
          userId: vm.userId
        });
        let data = vm._.get(res, "data", []) || [];
        data.forEach(i => {
          if (i.childSelectState !== 2) {
            nodes.push({
              id: i.id,
              nodeId: i.id,
              nodeType: i.modelLabel,
              tree_id: `${i.modelLabel}_${i.id}`
            });
          }
        });
      }
      vm.CetGiantTree_onlyReport.checkedNodes = nodes;
      if (!nodes.length) {
        vm.CetGiantTree_onlyReport.unCheckTrigger_in = new Date().getTime();
      }

      var queryData = {
        tenantId: this.projectTenantId
      };
      await customApi.getOnlyReportTree(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_onlyReport.inputData_in = this.formatNodeTree(data);
          setTimeout(() => {
            this.expandNode(
              nodes,
              this.CetGiantTree_onlyReport.setting.data.simpleData.idKey,
              this.$refs.onlyReportTree.ztreeObj
            );
          }, 0);
        } else {
          this.CetGiantTree_onlyReport.inputData_in = [];
        }
        this.$nextTick(() => {
          vm.CetGiantTree_onlyReport.saveFlag = true;
        });
      });
    },
    formatReporthNodeTree(rawNodeAry) {
      var me = this;
      rawNodeAry = rawNodeAry || [];

      me._(rawNodeAry).forEach(function (rawNode) {
        rawNode.id = rawNode.nodeId;
        if (!rawNode.children || !rawNode.children.length) {
          rawNode.children = null;
          rawNode.leaf = true;
        } else {
          rawNode.leaf = false;
          rawNode.children = me.formatReporthNodeTree(rawNode.children);
        }
        rawNode.tree_id = `${rawNode.nodeType}_${rawNode.id}`;
      });
      return rawNodeAry;
    },
    // 获取视频节点权限
    async getVideoTree_out() {
      let vm = this,
        nodes = [];
      vm.CetGiantTree_video.saveFlag = false;
      if (vm.userId) {
        let res = await customApi.videoQueryFolders({ userId: vm.userId });
        let data = vm._.get(res, "data", []) || [];
        data = this.flattenTree(data);
        data.forEach(i => {
          if (i.childSelectState !== 2) {
            nodes.push({
              id: i.id,
              nodeId: i.id,
              nodeType: i.modelLabel,
              tree_id: i.tree_id
            });
          }
        });
      }
      vm.CetGiantTree_video.checkedNodes = nodes;
      if (!nodes.length) {
        vm.CetGiantTree_video.unCheckTrigger_in = new Date().getTime();
      }

      var queryData = {
        tenantId: vm.projectTenantId
      };
      await customApi.videoQueryFolders(queryData).then(response => {
        if (response.code === 0) {
          var data = vm._.get(response, "data", []);
          vm.CetGiantTree_video.inputData_in = vm.formatVideoNodeTree(data);
          setTimeout(() => {
            vm.expandNode(
              nodes,
              vm.CetGiantTree_video.setting.data.simpleData.idKey,
              vm.$refs.videoTree.ztreeObj
            );
          }, 0);
        } else {
          vm.CetGiantTree_video.inputData_in = [];
        }
        vm.$nextTick(() => {
          vm.CetGiantTree_video.saveFlag = true;
        });
      });
    },
    flattenTree(tree) {
      let flatData = [];
      function flatten(data) {
        if (data?.length) {
          data.forEach(node => {
            flatData.push(node);
            if (node?.children?.length) {
              flatten(node.children);
            }
          });
        }
      }
      flatten(tree);
      return flatData;
    },
    formatVideoNodeTree(rawNodeAry) {
      var me = this;
      rawNodeAry = rawNodeAry || [];

      me._(rawNodeAry).forEach(function (rawNode) {
        // if (rawNode.childSelectState !== 1) {
        //   rawNode.nocheck = true;
        // }
        if (!rawNode.children || !rawNode.children.length) {
          rawNode.children = null;
          rawNode.leaf = true;
        } else {
          rawNode.leaf = false;
          rawNode.children = me.formatVideoNodeTree(rawNode.children);
        }
      });
      return rawNodeAry;
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_infoSave_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    checkboxChange() {
      this.CetForm_1.localSaveTrigger_in = new Date().getTime();
    },
    CetForm_1_saveData_out() {
      this.userSave();
    },
    userSave() {
      const vm = this;
      let saveData = {
          email: vm.CetForm_1.data.email || "",
          id: vm.userId,
          mobilePhone: vm.CetForm_1.data.mobilePhone,
          name: vm.CetForm_1.data.name,
          nicName: vm.CetForm_1.data.nicName,
          relativeUserGroup: [vm.CetForm_1.data.relativeUserGroup],
          roles: [
            {
              id: vm.CetForm_1.data.role
            }
          ],
          state: vm.CetForm_1.data.state,
          tenantId: vm.CetForm_1.data.tenantId,
          customConfig: ""
        },
        fn = "editSimplifyUser",
        cfg = {
          dashboard: vm.CetForm_1.data.dashboard
        };
      cfg.usertype = 4;
      cfg.notifytype = vm.ElCheckboxGroup_1.value;
      cfg = Object.assign(vm.CetForm_1.data.customConfig || {}, cfg);
      saveData.customConfig = JSON.stringify(cfg);
      if (!vm.userId) {
        saveData.state = 0;
        fn = "addSimplifyUser";
        saveData.tenantId = vm.projectTenantId;
        saveData.password = vm.CetForm_1.data.password;
      }
      customApi[fn](saveData).then(response => {
        if (response.code === 0) {
          vm.CetForm_1.data.state = saveData.state;
          vm.CetForm_1.data.tenantId = saveData.tenantId;
          vm.$message.success($T("保存成功！"));
          vm.userId = response.data;
        }
      });
    },
    CetGiantTree_project_checkedNodes_out() {
      if (!this.CetGiantTree_project.saveFlag) {
        return;
      }
      this.CetGiantTree_project.confirmHint = true;
    },
    CetGiantTree_graph_checkedNodes_out() {
      if (!this.CetGiantTree_graph.saveFlag) {
        return;
      }
      this.CetGiantTree_graph.confirmHint = true;
    },
    CetGiantTree_pecReport_checkedNodes_out() {
      if (!this.CetGiantTree_pecReport.saveFlag) {
        return;
      }
      this.CetGiantTree_pecReport.confirmHint = true;
    },
    CetGiantTree_mReport_checkedNodes_out() {
      if (!this.CetGiantTree_mReport.saveFlag) {
        return;
      }
      this.CetGiantTree_mReport.confirmHint = true;
    },
    CetGiantTree_dashboard_checkedNodes_out() {
      if (!this.CetGiantTree_dashboard.saveFlag) {
        return;
      }
      this.CetGiantTree_dashboard.confirmHint = true;
    },
    CetGiantTree_video_checkedNodes_out() {
      if (!this.CetGiantTree_video.saveFlag) {
        return;
      }
      this.CetGiantTree_video.confirmHint = true;
    },
    CetGiantTree_onlyReport_checkedNodes_out() {
      if (!this.CetGiantTree_onlyReport.saveFlag) {
        return;
      }
      this.CetGiantTree_onlyReport.confirmHint = true;
    },
    // 获取节点
    getCheckNodes(ztreeObj) {
      const nodes = ztreeObj.getNodesByFilter(node => {
        const checkStatus = node.getCheckStatus();
        if (node.childSelectState === 1 && !checkStatus.half) {
          node.saveChildSelectState = 1;
        } else {
          node.saveChildSelectState = 2;
        }
        if (!node.checked) return false;

        const parentNode = node.getParentNode();
        if (!parentNode) return true;

        // eslint-disable-next-line no-unsafe-optional-chaining
        const { checked, half } = parentNode?.getCheckStatus();

        if (checked && !half && parentNode.childSelectState === 1) return false;
        return true;
      });
      return nodes;
    },
    CetButton_project_statusTrigger_out() {
      const vm = this;
      const modelLabels = TREE_PARAMS.userManageModelLabels || [];
      let saveData = {
        id: vm.userId,
        modelLabels: modelLabels,
        modelNodes: []
      };

      const nodes = vm.getCheckNodes(vm.$refs.projectTree.ztreeObj);
      nodes.forEach(item => {
        saveData.modelNodes.push({
          authIds: [],
          disabled: false,
          childSelectState: item.saveChildSelectState,
          id: item.id,
          modelLabel: item.modelLabel,
          rule: ""
        });
      });
      customApi.editSimplifyUserModelnode(saveData).then(response => {
        if (response.code === 0) {
          vm.CetGiantTree_project.confirmHint = false;
          vm.CetGiantTree_graph.confirmHint = false;
          vm.CetGiantTree_pecReport.confirmHint = false;
          vm.CetGiantTree_mReport.confirmHint = false;
          vm.CetGiantTree_dashboard.confirmHint = false;
          vm.CetGiantTree_video.confirmHint = false;
          vm.CetGiantTree_onlyReport.confirmHint = false;
          vm.$message.success($T("保存成功！"));
        }
      });
    },
    CetButton_graph_statusTrigger_out() {
      const vm = this;
      let saveData = {
        id: vm.userId,
        nodeTypes: this.$store.state.graphTypes,
        graphNodes: []
      };
      const nodes = vm.getCheckNodes(vm.$refs.graphTree.ztreeObj);
      nodes.forEach(item => {
        saveData.graphNodes.push({
          disabled: false,
          childSelectState: item.saveChildSelectState,
          nodeID: item.nodeId,
          nodeName: item.nodeName,
          text: item.text,
          nodeType: item.nodeType,
          authIds: []
        });
      });
      customApi.editSimplifyUserGraphnode(saveData).then(response => {
        if (response.code === 0) {
          vm.CetGiantTree_project.confirmHint = false;
          vm.CetGiantTree_graph.confirmHint = false;
          vm.CetGiantTree_pecReport.confirmHint = false;
          vm.CetGiantTree_mReport.confirmHint = false;
          vm.CetGiantTree_dashboard.confirmHint = false;
          vm.CetGiantTree_video.confirmHint = false;
          vm.CetGiantTree_onlyReport.confirmHint = false;
          vm.$message.success($T("保存成功！"));
        }
      });
    },
    CetButton_pecReport_statusTrigger_out() {
      const vm = this;
      let saveData = {
        id: vm.userId,
        nodeTypes: this.$store.state.pReportTypes,
        pecStarNodes: []
      };
      const nodes = vm.getCheckNodes(vm.$refs.pecReportTree.ztreeObj);
      nodes.forEach(item => {
        saveData.pecStarNodes.push({
          authIds: [],
          disabled: false,
          childSelectState: item.saveChildSelectState,
          nodeID: item.nodeId,
          nodeType: item.nodeType
        });
      });
      customApi.editSimplifyUserPecstarnode(saveData).then(response => {
        if (response.code === 0) {
          vm.CetGiantTree_project.confirmHint = false;
          vm.CetGiantTree_graph.confirmHint = false;
          vm.CetGiantTree_pecReport.confirmHint = false;
          vm.CetGiantTree_mReport.confirmHint = false;
          vm.CetGiantTree_dashboard.confirmHint = false;
          vm.CetGiantTree_video.confirmHint = false;
          vm.CetGiantTree_onlyReport.confirmHint = false;
          vm.$message.success($T("保存成功！"));
        }
      });
    },
    CetButton_mReport_statusTrigger_out() {
      const vm = this;
      let saveData = {
        id: vm.userId,
        modelLabels: this.$store.state.mReportTypes,
        modelNodes: []
      };
      const nodes = vm.getCheckNodes(vm.$refs.mReportTree.ztreeObj);
      nodes.forEach(item => {
        saveData.modelNodes.push({
          authIds: [],
          disabled: false,
          childSelectState: item.saveChildSelectState,
          id: item.nodeId,
          modelLabel: item.nodeType,
          rule: ""
        });
      });
      customApi.editSimplifyUserModelnode(saveData).then(response => {
        if (response.code === 0) {
          vm.CetGiantTree_project.confirmHint = false;
          vm.CetGiantTree_graph.confirmHint = false;
          vm.CetGiantTree_pecReport.confirmHint = false;
          vm.CetGiantTree_mReport.confirmHint = false;
          vm.CetGiantTree_dashboard.confirmHint = false;
          vm.CetGiantTree_video.confirmHint = false;
          vm.CetGiantTree_onlyReport.confirmHint = false;
          vm.$message.success($T("保存成功！"));
        }
      });
    },
    CetButton_dashboard_statusTrigger_out() {
      const nodes = this.getCheckNodes(this.$refs.dashboard.ztreeObj);
      let ids = nodes.map(item => item.id);
      this.CetForm_1.data.dashboard = ids;
      this.initTooltip();
      this.userSave();
    },
    CetButton_video_statusTrigger_out() {
      const vm = this;
      const modelLabels = ["camerafold"] || [];
      let saveData = {
        id: vm.userId,
        modelLabels,
        modelNodes: []
      };
      const nodes = vm.getCheckNodes(vm.$refs.videoTree.ztreeObj);
      nodes.forEach(item => {
        saveData.modelNodes.push({
          authIds: [],
          disabled: false,
          childSelectState: item.saveChildSelectState,
          id: item.id,
          modelLabel: item.modelLabel,
          rule: ""
        });
      });
      customApi.editSimplifyUserModelnode(saveData).then(response => {
        if (response.code === 0) {
          vm.CetGiantTree_project.confirmHint = false;
          vm.CetGiantTree_graph.confirmHint = false;
          vm.CetGiantTree_pecReport.confirmHint = false;
          vm.CetGiantTree_mReport.confirmHint = false;
          vm.CetGiantTree_dashboard.confirmHint = false;
          vm.CetGiantTree_video.confirmHint = false;
          vm.CetGiantTree_onlyReport.confirmHint = false;
          vm.$message.success($T("保存成功！"));
        }
      });
    },
    CetButton_onlyReport_statusTrigger_out() {
      const vm = this;
      const modelLabels = this.$store.state.onlyReportTypes; // nodeType值，1-文件夹，2-报表节点
      let saveData = {
        id: vm.userId,
        modelLabels,
        modelNodes: []
      };
      const nodes = vm.getCheckNodes(vm.$refs.onlyReportTree.ztreeObj);
      nodes.forEach(item => {
        saveData.modelNodes.push({
          authIds: [],
          disabled: false,
          childSelectState: item.saveChildSelectState,
          id: item.id,
          modelLabel: item.nodeType,
          rule: ""
        });
      });
      customApi.editSimplifyUserModelnode(saveData).then(response => {
        if (response.code === 0) {
          vm.CetGiantTree_project.confirmHint = false;
          vm.CetGiantTree_graph.confirmHint = false;
          vm.CetGiantTree_pecReport.confirmHint = false;
          vm.CetGiantTree_mReport.confirmHint = false;
          vm.CetGiantTree_dashboard.confirmHint = false;
          vm.CetGiantTree_video.confirmHint = false;
          vm.CetGiantTree_onlyReport.confirmHint = false;
          vm.$message.success($T("保存成功！"));
        }
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep() {
    .is-fullscreen {
      display: flex;
      flex-direction: column;
    }
    .el-dialog__body {
      @include background_color(BG, !important);
      @include padding(J2);
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }
  }
  .nbsp :deep(.el-form-item__label) {
    height: 42px;
  }

  .title {
    font-weight: bold;
    @include margin_left(J3);
  }
  .cardBox {
    @include border_radius(C1);
  }
  .bottomBox {
    height: 500px;
    .leftContent {
      width: 300px;
      display: flex;
      flex-direction: column;
      .title {
        line-height: 32px;
      }
      .noticeContent {
        @include padding(J3 J4);
        @include border_radius(C1);
        flex: 1;
        box-sizing: border-box;
        .label {
          @include font_color(T3);
        }
      }
    }
    .rightContent {
      display: flex;
      flex-direction: column;
      flex: 1;
      .eem-tabs-custom {
        @include background_color(BG);
        :deep(.el-tabs__nav-wrap::after) {
          @include background_color(BG);
        }
      }
      .treeBox {
        @include border_radius(C1);
        @include padding(J4);
        box-sizing: border-box;
        flex: 1;
        min-height: 0;
        .fullfilled {
          position: relative;
          .saveBtn {
            position: absolute;
            right: 0;
            top: 0;
          }
        }
        .CetTree {
          width: 100%;
          :deep(.device-search .el-input) {
            width: 240px;
          }
        }
      }
    }
  }
  :deep(.tooltipBox) {
    .tooltip {
      left: -44px;
      display: none;
    }
    &:hover .tooltip {
      display: block;
    }
  }
}
</style>
