﻿<script>
import EventConfirm from "./EventConfirm.vue";
export default {
  extends: EventConfirm,
  name: "EnergyConsumptionEventConfirm",
  methods: {
    // getConfirmEventData_out(events) {
    //   var me = this;
    //   var list = [];
    //   if (!events || !events.length || events.length === 0) {
    //     return;
    //   }
    //   this._(events).forEach(function(item) {
    //     item.modelLabel = item.modelLabel ? item.modelLabel : "systemevent";
    //     item.confirmeventstatus = 3;
    //     item.operator = me.userInfo.nodeName;
    //     item.operator_id = me.userInfo.nodeID;
    //     item.remark = me.CetInput_1.val;
    //     item.updatetime = new Date().getTime();
    //   });
    //   return events;
    // }
  }
};
</script>
