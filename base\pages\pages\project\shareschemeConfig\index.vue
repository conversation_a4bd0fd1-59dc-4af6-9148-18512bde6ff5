<template>
  <div class="page eem-common">
    <el-container style="height: 100%">
      <el-aside width="315px" class="flex-column eem-aside">
        <div class="flex-auto">
          <CetGiantTree
            class="tree"
            v-bind="CetGiantTree_1"
            v-on="CetGiantTree_1.event"
          ></CetGiantTree>
        </div>
      </el-aside>
      <el-container class="mlJ2 fullheight flex-auto flex-column eem-cont">
        <div class="mbJ3 clearfix">
          <el-tooltip
            effect="light"
            :content="currentNode && currentNode.name"
            placement="bottom"
          >
            <div class="common-title-H2 text-ellipsis nodeName fl">
              {{ currentNode && currentNode.name }}
            </div>
          </el-tooltip>
          <CetButton
            class="fr mlJ1"
            v-bind="CetButton_3"
            v-on="CetButton_3.event"
          ></CetButton>
          <customElSelect
            class="fr"
            :prefix_in="$T('能源类型')"
            v-model="ElSelect_1.value"
            v-bind="ElSelect_1"
            v-on="ElSelect_1.event"
          >
            <ElOption
              v-for="item in ElOption_1.options_in"
              :key="item[ElOption_1.key]"
              :label="item[ElOption_1.label]"
              :value="item[ElOption_1.value]"
              :disabled="item[ElOption_1.disabled]"
            ></ElOption>
          </customElSelect>
        </div>
        <div class="flex-auto">
          <CetTable
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
          >
            <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_energysharemethodText"
            ></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_timeText"></ElTableColumn>
            <ElTableColumn
              :label="$T('操作')"
              width="150"
              header-align="left"
              align="left"
            >
              <template slot-scope="scope">
                <div @click.stop>
                  <span
                    class="handle fl mrJ3"
                    @click.stop="handleCommand({ type: 'detail', scope })"
                  >
                    {{ $T("详情") }}
                  </span>
                  <span
                    class="handle fl mrJ4"
                    @click.stop="handleCommand({ type: 'edit', scope })"
                  >
                    {{ $T("编辑") }}
                  </span>
                  <el-dropdown class="fl" @command="handleCommand">
                    <span class="el-icon-more"></span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :command="{ type: 'copy', scope }">
                        {{ $T("复制方案") }}
                      </el-dropdown-item>
                      <el-dropdown-item
                        class="handle delete"
                        :command="{ type: 'delete', scope }"
                      >
                        {{ $T("删除") }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </template>
            </ElTableColumn>
          </CetTable>
        </div>
      </el-container>
    </el-container>
    <AddShareScheme
      :visibleTrigger_in="AddShareScheme.visibleTrigger_in"
      :closeTrigger_in="AddShareScheme.closeTrigger_in"
      :queryId_in="AddShareScheme.queryId_in"
      :inputData_in="AddShareScheme.inputData_in"
      @finishTrigger_out="AddShareScheme_finishTrigger_out"
      @finishData_out="AddShareScheme_finishData_out"
      @saveData_out="AddShareScheme_saveData_out"
      @currentData_out="AddShareScheme_currentData_out"
    />
    <Detail v-bind="detail" v-on="detail.event" />
  </div>
</template>
<script>
import AddShareScheme from "./AddShareScheme.vue";
import TREE_PARAMS from "@/store/treeParams.js";
import Detail from "./detail";
import { httping } from "@omega/http";
export default {
  name: "Shareschemeconfig",
  components: {
    AddShareScheme,
    Detail
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    }
  },

  data() {
    return {
      currentNode: null,
      currentTebelItem: null,
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out //选中单行输出
        }
      },
      ElSelect_1: {
        value: 0,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {}
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        label: "#", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "41" //绝对宽度
      },
      ElTableColumn_name: {
        prop: "name", // 支持path a[0].b
        label: $T("方案名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.name) {
            return val.name;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_energysharemethodText: {
        //type: "",      // selection 勾选 index 序号
        prop: "energysharemethodText", // 支持path a[0].b
        label: $T("分摊方式"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.energysharemethodText) {
            return val.energysharemethodText;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_timeText: {
        prop: "timeText", // 支持path a[0].b
        label: $T("生效时间"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.timeText) {
            return val.timeText;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      CetButton_3: {
        visible_in: true,
        disable_in: false,
        title: $T("新增分摊方案"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      CetTable_2: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {}
      },
      ElTableColumn_objectname: {
        prop: "objectname", // 支持path a[0].b
        label: "分摊对象", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.objectname) {
            return val.objectname;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_rate: {
        prop: "rate", // 支持path a[0].b
        label: "分摊系数", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.rate || val.rate === 0) {
            return val.rate;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_energytypeText: {
        prop: "energytypeText", // 支持path a[0].b
        label: "分摊的能源类型", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.energytypeText || val.energytypeText === 0) {
            return val.energytypeText;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      AddShareScheme: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      detail: {
        openTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        inputData_in: null,
        table_in: [],
        currentNode_in: null,
        event: {
          updateSchemeList_out: this.getEnergyShareConfigScheme,
          edit_out: this.detail_edit_out,
          copy_out: this.detail_copy_out
        }
      }
    };
  },
  watch: {},

  methods: {
    //获取能源类型
    getEnergytype() {
      var vm = this;
      vm.ElOption_1.options_in = [];
      httping({
        url:
          "/eem-service/v1/project/projectEnergy?projectId=" + this.projectId,
        method: "GET"
      }).then(function (response) {
        if (response.code === 0 && response.data && response.data.length > 0) {
          let selectData = [];
          response.data.forEach(item => {
            if (![13, 18, 22].includes(item.energytype)) {
              selectData.push({
                id: item.energytype,
                text: item.name
              });
            }
          });
          var res = {};
          res.id = 0;
          res.text = $T("全部");
          selectData.unshift(res);
          vm.ElOption_1.options_in = vm._.cloneDeep(selectData);
        } else {
          vm.ElSelect_1.value = null;
        }
      });
    },
    // 获取节点树
    getTreeData() {
      var _this = this;
      _this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      _this.CetGiantTree_1.inputData_in = [];
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.powerEquipment,
        treeReturnEnable: true
      };
      httping({
        url: "/eem-service/v1/node/nodeTree",
        method: "POST",
        data
      }).then(res => {
        if (res.code === 0 && res.data.length > 0) {
          // 过滤掉项目下的开关柜或一段线
          var data = [];
          res.data.forEach((item, index) => {
            var obj = _this._.cloneDeep(item);
            obj.children = [];
            data.push(obj);
            if (item.children && item.children.length > 0) {
              item.children.forEach(ite => {
                if (ite.modelLabel != "linesegmentwithswitch") {
                  data[index].children.push(ite);
                }
              });
            }
          });
          _this.CetGiantTree_1.inputData_in = data;
          _this.CetGiantTree_1.selectNode = data[0];
        }
      });
    },
    // 获取分摊方案
    async getEnergyShareConfigScheme() {
      if (!this.currentNode || this.ElSelect_1.value === null) {
        this.CetTable_1.data = [];
        return;
      }
      let response = await httping({
        url: `/eem-service/v1/schemeConfig/energyShareConfigScheme?objectid=${this.currentNode.id}&objectlabel=${this.currentNode.modelLabel}&energytype=${this.ElSelect_1.value}`,
        method: "GET"
      });
      if (response.code == 0 && response.data) {
        response.data.forEach(item => {
          item.energysharemethodText =
            item.energysharemethod == 1
              ? $T("固定分摊")
              : item.energysharemethod == 2
              ? $T("动态分摊")
              : "";
          if (item.endtime) {
            item.timeText =
              this.$moment(item.starttime).format("YYYY/MM/DD") +
              "~" +
              this.$moment(item.endtime).format("YYYY/MM/DD");
          } else {
            item.timeText = this.$moment(item.starttime).format("YYYY/MM/DD");
          }
        });
        this.CetTable_1.data = response.data;
      } else {
        this.CetTable_1.data = [];
      }
    },
    // 根据时间获取分摊方案
    async getEnergyShareConfigSchemeByParam(val) {
      if (!val) {
        return;
      }
      this.CetTable_2.data = [];
      let response = await httping({
        url: `/eem-service/v1/schemeConfig/shareConfig/${val.id}`,
        method: "GET"
      });
      if (
        response.code == 0 &&
        response.data &&
        response.data.length > 0 &&
        response.data[0].energysharetoobject_model &&
        response.data[0].energysharetoobject_model.length > 0
      ) {
        var tebData = response.data[0].energysharetoobject_model.map(item => {
          return {
            id: item.id,
            modelLabel: item.modelLabel,
            objectname: item.objectname,
            objectid: item.objectid,
            objectlabel: item.objectlabel,
            rate: item.rate,
            energytypeText:
              this.ElOption_1.options_in.filter(
                ite => ite.id == response.data[0].energytype
              ).length > 0
                ? this.ElOption_1.options_in.filter(
                    ite => ite.id == response.data[0].energytype
                  )[0].text
                : ""
          };
        });
        this.CetTable_2.data = tebData;
      }
    },
    AddShareScheme_currentData_out() {},
    AddShareScheme_finishData_out() {},
    async AddShareScheme_finishTrigger_out() {
      await this.getEnergyShareConfigScheme();
      this.currentTebelItem = this.CetTable_1.data.find(
        i => i.id === this.currentTebelItem.id
      );
      await this.getEnergyShareConfigSchemeByParam(this.currentTebelItem);
      this.detail.currentNode_in = this._.cloneDeep(this.currentNode);
      this.detail.inputData_in = this._.cloneDeep(this.currentTebelItem);
      this.detail.table_in = this._.cloneDeep(this.CetTable_2.data);
      this.detail.resetTrigger_in = new Date().getTime();
    },
    AddShareScheme_saveData_out() {},
    CetButton_1_statusTrigger_out() {
      this.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        cancelButtonClass: "btn-custom-cancel",
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: (action, instance, done) => {
          if (action == "confirm") {
            httping({
              url:
                "/eem-service/v1/schemeConfig/delEnergyShareConfigSchemeById?id=" +
                this.currentTebelItem.id,
              method: "DELETE"
            }).then(response => {
              if (response.code == 0) {
                this.$message({
                  message: $T("删除成功"),
                  type: "success"
                });
                this.getEnergyShareConfigScheme();
              }
            });
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action != "confirm") {
            this.$message({
              type: "info",
              message: $T("取消删除！")
            });
          }
        }
      });
    },
    CetButton_2_statusTrigger_out(val) {
      var data = this._.cloneDeep(this.currentTebelItem);
      data.id = 0;
      data.objectName = this.currentNode.name;
      data.objectid = this.currentNode.id;
      data.objectlabel = this.currentNode.modelLabel;
      data.name = data.name + "-副本";

      data.energysharetoobject_model = this.CetTable_2.data;
      this.AddShareScheme.inputData_in = this._.cloneDeep(data);
      this.AddShareScheme.visibleTrigger_in = this._.cloneDeep(val);
    },
    CetButton_3_statusTrigger_out(val) {
      // 有方案先判断最近一条方案是否有失效时间
      // if (this.CetTable_1.data.length > 0) {
      //   if (!this.CetTable_1.data[0].endtime) {
      //     this.$message({
      //       message: "上一条失效时间为空，无法新增",
      //       type: "warning"
      //     });
      //     return;
      //   }
      // }
      this.AddShareScheme.inputData_in = {
        objectName: this.currentNode.name,
        objectid: this.currentNode.id,
        objectlabel: this.currentNode.modelLabel
      };
      this.AddShareScheme.visibleTrigger_in = this._.cloneDeep(val);
    },
    CetButton_4_statusTrigger_out(val) {
      var data = this._.cloneDeep(this.currentTebelItem);
      data.objectName = this.currentNode.name;

      data.energysharetoobject_model = this.CetTable_2.data;
      this.AddShareScheme.inputData_in = data;
      this.AddShareScheme.visibleTrigger_in = this._.cloneDeep(val);
    },

    async handleCommand({ type, scope }) {
      this.currentTebelItem = scope.row;
      if (type !== "delete") {
        await this.getEnergyShareConfigSchemeByParam(this.currentTebelItem);
      }
      switch (type) {
        case "detail":
          this.detail.currentNode_in = this._.cloneDeep(this.currentNode);
          this.detail.inputData_in = this._.cloneDeep(this.currentTebelItem);
          this.detail.table_in = this._.cloneDeep(this.CetTable_2.data);
          this.detail.openTrigger_in = new Date().getTime();
          break;
        case "edit":
          this.CetButton_4_statusTrigger_out(new Date().getTime());
          break;
        case "copy":
          this.CetButton_2_statusTrigger_out(new Date().getTime());
          break;
        case "delete":
          this.CetButton_1_statusTrigger_out(new Date().getTime());
          break;
      }
    },
    detail_edit_out() {
      this.CetButton_4_statusTrigger_out(new Date().getTime());
    },
    detail_copy_out() {
      this.CetButton_2_statusTrigger_out(new Date().getTime());
    },
    ElSelect_1_change_out() {
      this.getEnergyShareConfigScheme();
    },
    CetGiantTree_1_currentNode_out(val) {
      if (!val) {
        return;
      }
      this.currentNode = val;
      this.getEnergyShareConfigScheme();
    }
  },
  activated() {
    this.getEnergytype();
    this.getTreeData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.tab2Title {
  @include padding(J1);
  @include border_radius(C);
  @include background_color(BG1);
}
.nodeName {
  max-width: 420px;
  @include line_height(Hm);
}
.handle {
  cursor: pointer;
  @include font_color(ZS);
}
.handle.delete {
  @include font_color(Sta3);
}
</style>
