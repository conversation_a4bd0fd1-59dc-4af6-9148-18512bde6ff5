<?xml version="1.0" encoding="UTF-8"?>
<svg width="62px" height="62px" viewBox="0 0 62 62" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 40</title>
    <defs>
        <linearGradient x1="3.57176277%" y1="1.51962629%" x2="77.5242384%" y2="100%" id="linearGradient-1">
            <stop stop-color="#B3D4FF" offset="0%"></stop>
            <stop stop-color="#B0D2FF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="节能策略-电费优化-电度电费" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="用能策略-电费优化-电度电费" transform="translate(-304.000000, -255.000000)">
            <rect id="矩形" fill="#F6F6F7" x="-1" y="1" width="1883" height="950"></rect>
            <g id="编组-11" transform="translate(280.000000, 197.000000)" fill="#FFFFFF">
                <path d="M16,0 L350,0 C358.836556,-1.623249e-15 366,7.163444 366,16 L366,317 C366,325.836556 358.836556,333 350,333 L16,333 C7.163444,333 1.082166e-15,325.836556 0,317 L0,16 C-1.082166e-15,7.163444 7.163444,1.623249e-15 16,0 Z" id="矩形备份-6"></path>
            </g>
            <g id="编组-40" transform="translate(304.000000, 255.000000)">
                <rect id="矩形备份-18" fill-opacity="0.4" fill="#3E77FC" opacity="0.157178606" x="0" y="0" width="62" height="62" rx="8"></rect>
                <g id="编组-7" transform="translate(16.000000, 17.000000)">
                    <circle id="椭圆形备份-5" fill="#3E77FC" cx="10" cy="10" r="10"></circle>
                    <path d="M7.22520766,5.01494404 C6.65587975,4.91894495 6.11455158,5.29094141 6.01588585,5.84693611 C5.91722012,6.4015975 6.29854983,6.92959247 6.86787774,7.02559155 C8.11586585,7.23758953 9.09985648,8.18158054 9.33452091,9.39490232 L9.46918629,10.0908957 C8.92119151,10.8882214 8.50786211,11.7815463 8.2611978,12.7428704 C7.67187008,15.028182 7.67187008,17.4188259 8.2611978,19.7041375 C8.31053066,19.9001356 8.36786345,20.0921338 8.43186284,20.2814653 C8.47719574,20.417464 8.5265286,20.5534627 8.57986143,20.6867948 L8.83052571,21.3254553 L8.86385872,21.3254553 C9.35718736,22.3214459 10.0438475,23.2081041 10.8758396,23.9387638 C10.6998412,24.1547617 10.5545093,24.3960928 10.445177,24.6534236 C10.2131792,25.1920852 10.1465132,25.7854129 10.2491789,26.3614074 C10.3531779,26.9360686 10.623842,27.4707301 11.0291714,27.9013927 C11.4358342,28.3320553 11.9584959,28.6413857 12.5384904,28.7907176 C13.1184849,28.9400495 13.730479,28.925383 14.3011403,28.746718 C14.8718015,28.568053 15.3784633,28.2347229 15.7611264,27.7840605 C16.1371228,27.3400647 16.3784538,26.8027365 16.4557864,26.2320753 C18.0117716,26.4627397 19.5930899,26.4734063 21.1517417,26.2627416 C21.2050745,26.746737 21.3757395,27.2133993 21.6517369,27.6227287 C21.9824004,28.1120574 22.450396,28.4987203 22.9997241,28.7373847 C23.5503855,28.9747158 24.1583797,29.0533817 24.7530407,28.9640492 C25.3477017,28.8747168 25.9023631,28.6213858 26.3530255,28.2320562 C26.8050212,27.8440599 27.1316847,27.3387314 27.2956832,26.7734034 C27.4596816,26.2094088 27.453015,25.6120812 27.2796833,25.0507532 C27.170351,24.7000899 26.9983527,24.3734263 26.7716881,24.0840957 C27.6810128,23.3267696 28.4276724,22.3894452 28.9556673,21.3254553 L28.987667,21.3254553 L29.2383313,20.6867948 C29.2876642,20.5627959 29.3329971,20.4387971 29.3756633,20.3121317 C29.4436627,20.1134669 29.5049954,19.9108022 29.5583283,19.7041375 C30.1463227,17.4188259 30.1463227,15.028182 29.5583283,12.7428704 C28.7530026,9.62156683 26.2063602,7.21358976 22.9810576,6.52559632 L22.4050631,6.40293082 C20.101085,5.9109355 17.7171077,5.9109355 15.4131297,6.40293082 L14.8371352,6.52559632 C13.4691482,6.81759353 12.2211601,7.41892114 11.1785033,8.25491318 C10.5625092,6.57892914 9.06519014,5.32560775 7.22520766,5.01494404 Z" id="路径" fill-opacity="0.4" fill="#C2E2FF"></path>
                    <path d="M7.22520766,5.01494404 C6.65587975,4.91894495 6.11455158,5.29094141 6.01588585,5.84693611 C5.91722012,6.4015975 6.29854983,6.92959247 6.86787774,7.02559155 C8.11586585,7.23758953 9.09985648,8.18158054 9.33452091,9.39490232 L9.46918629,10.0908957 C8.92119151,10.8882214 8.50786211,11.7815463 8.2611978,12.7428704 C7.67187008,15.028182 7.67187008,17.4188259 8.2611978,19.7041375 C8.31053066,19.9001356 8.36786345,20.0921338 8.43186284,20.2814653 C8.47719574,20.417464 8.5265286,20.5534627 8.57986143,20.6867948 L8.83052571,21.3254553 L8.86385872,21.3254553 C9.35718736,22.3214459 10.0438475,23.2081041 10.8758396,23.9387638 C10.6998412,24.1547617 10.5545093,24.3960928 10.445177,24.6534236 C10.2131792,25.1920852 10.1465132,25.7854129 10.2491789,26.3614074 C10.3531779,26.9360686 10.623842,27.4707301 11.0291714,27.9013927 C11.4358342,28.3320553 11.9584959,28.6413857 12.5384904,28.7907176 C13.1184849,28.9400495 13.730479,28.925383 14.3011403,28.746718 C14.8718015,28.568053 15.3784633,28.2347229 15.7611264,27.7840605 C16.1371228,27.3400647 16.3784538,26.8027365 16.4557864,26.2320753 C18.0117716,26.4627397 19.5930899,26.4734063 21.1517417,26.2627416 C21.2050745,26.746737 21.3757395,27.2133993 21.6517369,27.6227287 C21.9824004,28.1120574 22.450396,28.4987203 22.9997241,28.7373847 C23.5503855,28.9747158 24.1583797,29.0533817 24.7530407,28.9640492 C25.3477017,28.8747168 25.9023631,28.6213858 26.3530255,28.2320562 C26.8050212,27.8440599 27.1316847,27.3387314 27.2956832,26.7734034 C27.4596816,26.2094088 27.453015,25.6120812 27.2796833,25.0507532 C27.170351,24.7000899 26.9983527,24.3734263 26.7716881,24.0840957 C27.6810128,23.3267696 28.4276724,22.3894452 28.9556673,21.3254553 L28.987667,21.3254553 L29.2383313,20.6867948 C29.2876642,20.5627959 29.3329971,20.4387971 29.3756633,20.3121317 C29.4436627,20.1134669 29.5049954,19.9108022 29.5583283,19.7041375 C30.1463227,17.4188259 30.1463227,15.028182 29.5583283,12.7428704 C28.7530026,9.62156683 26.2063602,7.21358976 22.9810576,6.52559632 L22.4050631,6.40293082 C20.101085,5.9109355 17.7171077,5.9109355 15.4131297,6.40293082 L14.8371352,6.52559632 C13.4691482,6.81759353 12.2211601,7.41892114 11.1785033,8.25491318 C10.5625092,6.57892914 9.06519014,5.32560775 7.22520766,5.01494404 Z" id="路径备份-5" stroke="url(#linearGradient-1)" stroke-width="0.5"></path>
                    <polygon id="路径备份-4" fill="#FFFFFF" fill-rule="nonzero" points="20.2 10 14.44 17.2 18.76 17.2 17.8 22 23.56 14.8 19.24 14.8 20.2 10"></polygon>
                </g>
            </g>
        </g>
    </g>
</svg>