<template>
  <div class="page">
    <el-container style="height: 100%">
      <el-header height="58px" style="padding: 0px 16px 0 12px; line-height: 58px">
        <div class="basic-box fl" style="width: 400px">
          <span class="basic-box-label" style="width: 110px; margin-top: 9px">
            方案名称
            <span style="color: red">*</span>
          </span>
          <ElInput v-model="ElInput_1.value" v-bind="ElInput_1" v-on="ElInput_1.event"></ElInput>
        </div>
        <div class="fl costCompositionTitle">
          <i class="el-icon-warning" style="font-size: 24px; color: #3372da; position: relative; top: 5px"></i>
          定制提示：成本构成电度电费和附加费可以在一个方案中无限制叠加，
          <span style="color: red">基本电费</span>
          和
          <span style="color: red">力调电费</span>
          只能各添加一类作为成本构成项！
        </div>
        <CetButton class="fr" v-bind="CetButton_1" v-on="CetButton_1.event"></CetButton>
      </el-header>
      <el-main style="height: 100%; padding: 0px 16px 11px 12px">
        <CetTable :data.sync="CetTable_1.data" :dynamicInput.sync="CetTable_1.dynamicInput" v-bind="CetTable_1" v-on="CetTable_1.event">
          <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_energytype$text"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_feeratetype$text"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_costcheckitem$text"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_edit">
            <template slot-scope="scope">
              <i class="row-edit" @click="handleEdit(scope.$index, scope.row)"></i>
            </template>
          </ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_delete">
            <template slot-scope="scope">
              <i class="row-delete" @click="handleDelete(scope.$index, scope.row)"></i>
            </template>
          </ElTableColumn>
        </CetTable>
      </el-main>
      <el-footer height="50px" style="padding: 0px; line-height: 50px">
        <CetButton class="fr ml5" v-bind="CetButton_2" v-on="CetButton_2.event"></CetButton>
        <CetButton class="fr ml5" v-bind="CetButton_3" v-on="CetButton_3.event"></CetButton>
      </el-footer>
    </el-container>
    <AddConstComposition
      :visibleTrigger_in="AddConstComposition.visibleTrigger_in"
      :closeTrigger_in="AddConstComposition.closeTrigger_in"
      :queryId_in="AddConstComposition.queryId_in"
      :inputData_in="AddConstComposition.inputData_in"
      @finishTrigger_out="AddConstComposition_finishTrigger_out"
      @finishData_out="AddConstComposition_finishData_out"
      @saveData_out="AddConstComposition_saveData_out"
      @currentData_out="AddConstComposition_currentData_out"
      :basicfeerateFlag="basicfeerateFlag"
      :powertarifffeerateFlag="powertarifffeerateFlag"
    />
  </div>
</template>
<script>
import AddConstComposition from "./costComposition/AddConstComposition.vue";
import { httping } from "@omega/http";
export default {
  name: "CostComposition",
  components: {
    AddConstComposition
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },

  data() {
    return {
      // 项目能源类型
      energyTypeArr: [],
      basicfeerateFlag: false,
      powertarifffeerateFlag: false,
      currentTabItem: null,
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: "添加成本构成",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: "请输入内容",
        style: {
          // width: "200px"
        },
        event: {
          change: this.ElInput_1_change_out,
          input: this.ElInput_1_input_out
        }
      },
      AddConstComposition: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetButton_3: {
        visible_in: true,
        disable_in: false,
        title: "保存",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        //  prop: "",      // 支持path a[0].b
        label: "序号", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "50" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_delete: {
        //type: "",      // selection 勾选 index 序号
        //  prop: "",      // 支持path a[0].b
        label: "删除", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "50" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_edit: {
        //type: "",      // selection 勾选 index 序号
        //  prop: "",      // 支持path a[0].b
        label: "编辑", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "50" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_energytype$text: {
        //type: "",      // selection 勾选 index 序号
        prop: "energytype$text", // 支持path a[0].b
        label: "能源类型", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: val => {
          if (this._.get(val, "energytype$text")) {
            return this._.get(val, "energytype$text");
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_feeratetype$text: {
        //type: "",      // selection 勾选 index 序号
        prop: "feeratetype$text", // 支持path a[0].b
        label: "账单成本构成项", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: val => {
          if (this._.get(val, "feeratetype$text")) {
            return this._.get(val, "feeratetype$text");
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_name: {
        //type: "",      // selection 勾选 index 序号
        prop: "name", // 支持path a[0].b
        label: "费率方案", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: val => {
          if (this._.get(val, "name")) {
            return this._.get(val, "name");
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_costcheckitem$text: {
        //type: "",      // selection 勾选 index 序号
        prop: "costcheckitem$text", // 支持path a[0].b
        label: "成本计算项", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: val => {
          if (this._.get(val, "costcheckitem$text")) {
            return this._.get(val, "costcheckitem$text");
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      }
    };
  },
  watch: {
    "CetTable_1.data": {
      handler: function (val) {
        this.basicfeerateFlag = false;
        this.powertarifffeerateFlag = false;
        if (val && val.length > 0) {
          val.forEach(item => {
            // switch (item.energytype) {
            //   case 2:
            //     item.energytype$text = "电";
            //     break;
            //   case 3:
            //     item.energytype$text = "水";
            //     break;
            //   case 15:
            //     item.energytype$text = "天然气";
            //     break;
            //   default:
            //     break;
            // }
            var energyType = this.energyTypeArr.filter(i => i.energytype == item.energytype);
            item.energytype$text = this._.get(energyType, "[0].name")
            switch (item.feeratetype) {
              case 1:
                item.feeratetype$text = "基本电费";
                break;
              case 2:
                if (item.energytype == 2) {
                  item.feeratetype$text = "电度电费";
                } else {
                  item.feeratetype$text = item.energytype$text + "费";
                }
                break;
              case 3:
                item.feeratetype$text = "力调电费";
                break;
              case 4:
                item.feeratetype$text = "附加费";
                break;
              default:
                break;
            }
            if (item.feeratetype == 1) {
              // 电度电费
              this.basicfeerateFlag = true;
              // this.powertarifffeerateFlag = true;
            } else if (item.feeratetype == 3) {
              // 力调电费
              this.powertarifffeerateFlag = true;
              // this.basicfeerateFlag = true;
            }
          });
        }
      },
      deep: true
    }
  },

  methods: {
    //获取能源类型
    getEnergytype(fn) {
      var vm = this;
      vm.energyTypeArr = [];
      httping({
        url:
          "/eem-service/v1/project/projectEnergy?projectId=" + this.projectId,
        method: "GET"
      }).then(function (response) {
        if (response.code === 0) {
          vm.energyTypeArr = vm._.get(response, "data", []);
          fn && fn();
        }
      });
    },
    AddConstComposition_currentData_out(val) {},
    AddConstComposition_finishData_out(val) {
      if (val.edit) {
        // 编辑输出
        var currentTabItemIndex;
        var flag = true;
        this.CetTable_1.data.forEach((item, index) => {
          if (this.currentTabItem.constName == item.constName) {
            currentTabItemIndex = index;
            val.id = item.id;
          } else if (item.constName == val.constName && flag) {
            this.$message({
              message: "成本项名称重复",
              type: "warning"
            });
            flag = false;
          }
        });
        if (flag) {
          this.CetTable_1.data.splice(currentTabItemIndex, 1, val);
          this.currentTabItem = this.CetTable_1.data[currentTabItemIndex];
        }
      } else {
        // 新增输出
        if (this.CetTable_1.data.filter(item => item.constName == val.constName).length > 0) {
          this.$message({
            message: "成本项名称重复",
            type: "warning"
          });
        } else {
          this.CetTable_1.data.push(val);
        }
      }
    },
    AddConstComposition_finishTrigger_out(val) {},
    AddConstComposition_saveData_out(val) {},
    CetButton_1_statusTrigger_out(val) {
      this.AddConstComposition.inputData_in = {};
      this.AddConstComposition.visibleTrigger_in = this._.cloneDeep(val);
    },
    CetButton_2_statusTrigger_out(val) {
      this.ElInput_1.value = "";
      this.CetTable_1.data = [];
      if (this.$route.params.costComposition) {
        this.$router.push({
          name: "accountingscheme"
        });
      }
    },
    CetButton_3_statusTrigger_out(val) {
      if (!this.ElInput_1.value) {
        this.$message({
          message: "请填写方案名称",
          type: "warning"
        });
        return;
      } else if (this.ElInput_1.value.length > 20) {
        this.$message({
          message: "方案名称长度在 1 到 20 个字符",
          type: "warning"
        });
        return false;
      } else if (!/^((?![`~!@$%^&*()+=\[\]{}\\|;:\'"<,>.?\/]).)*$/.test(this.ElInput_1.value)) {
        this.$message({
          message: "方案名称不能输入特殊字符",
          type: "warning"
        });
        return false;
      }
      var data = {
        costcheckitem_model: [
          // {
          //   energyType: 0,
          //   energyTypeName: "string",
          //   feeRateType: 0,
          //   feeRateTypeName: "string",
          //   feescheme_id: 0,
          //   id: 0,
          //   modelLabel: "string",
          //   name: "string"
          // }
        ],
        createtime: this.$moment().valueOf(),
        // effectivetime: 0,
        id: 0,
        // modelLabel: "string",
        name: this.ElInput_1.value,
        projectid: this.projectId
      };
      var costcheckitem_model = [];
      if (this.CetTable_1.data.length > 0) {
        this.CetTable_1.data.forEach(item => {
          costcheckitem_model.push({
            energyType: item.energytype,
            // energyTypeName: "string",
            feeRateType: item.feeratetype,
            // feeRateTypeName: "string",
            feescheme_id: item.feescheme_id,
            id: item.id,
            // modelLabel: "string",
            name: item.constName,
            costcheckitem: item.costcheckitem
          });
        });
      }
      data.costcheckitem_model = costcheckitem_model;
      if (this.$route.params.costComposition) {
        data.id = this.$route.params.id;
      }
      httping({
        url: `/eem-service/v1/schemeConfig/costCheckPlan`,
        method: "PUT",
        data
      }).then(response => {
        if (response.code === 0) {
          this.$message({
            message: "保存成功",
            type: "success"
          });
          // if (this.$route.params.costComposition) {
          this.$router.push({
            name: "accountingscheme"
          });
          // }
        }
      });
    },
    ElInput_1_change_out(val) {},
    ElInput_1_input_out(val) {},
    CetTable_1_outputData_out(val) {},
    CetTable_1_record_out(val) {
      if (val.id != -1) {
        this.currentTabItem = val;
      } else {
        this.currentTabItem = null;
      }
    },
    handleEdit(index, row) {
      this.AddConstComposition.inputData_in = this._.cloneDeep(
        Object.assign(row, {
          edit: true
        })
      );
      this.AddConstComposition.visibleTrigger_in = new Date().getTime();
    },
    handleDelete(index, row) {
      var vm = this;
      vm.$confirm("确定要删除所选项吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: "btn-custom-cancel",
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: function (action, instance, done) {
          if (action == "confirm") {
            //找到当前行，并从表格数据中删除
            vm.CetTable_1.data.splice(index, 1);
            vm.$message({
              type: "success",
              message: "删除成功！"
            });
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action != "confirm") {
            vm.$message({
              type: "info",
              message: "取消删除！"
            });
          }
        }
      });
    },
    getSchemeDetail(id) {
      httping({
        url: `/eem-service/v1/schemeConfig/costCheckPlan/plan/${id}`,
        method: "GET"
      }).then(response => {
        if (response.code === 0) {
          if (this.$route.params.id) {
            // 修改
            this.ElInput_1.value = response.data.name;
          } else {
            // 复制新建
            this.ElInput_1.value = "";
          }
          if (response.data.costcheckitem_model && response.data.costcheckitem_model.length > 0) {
            this.CetTable_1.data = response.data.costcheckitem_model;
          } else {
            this.CetTable_1.data = [];
          }
          this.CetTable_1.data.forEach(item => {
            item.energytype = item.energyType;
            item.constName = item.name;
            item.name = item.feeSchemeName;
            item.feeratetype = item.feeRateType;
            item.costcheckitem$text = JSON.parse(item.costcheckitem).calculateitem.join("、").replace(1, "能耗").replace(2, "损耗").replace(3, "分摊") || "--";
          });
        }
      });
    }
  },
  created: function () {},
  mounted: function () {
    if (this.$route.params.costComposition) {
      // 核算方案进入
      console.log("核算方案进入");
      this.getEnergytype(() => {
        if (this.$route.params.schemeId) {
          // 编辑或者复制
          this.getSchemeDetail(this.$route.params.schemeId);
        }
      });
    } else {
    }
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.costCompositionTitle {
  line-height: 30px;
  margin: 14px 0 14px 50px;
  padding: 0 20px 0 10px;
  border-radius: 5px;
  border: 1px solid #7ca5eb;
  background: #dde8ff;
  color: rgb(1, 82, 217);
}
.row-edit {
  width: 20px;
  height: 20px;
  display: inline-block;
  cursor: pointer;
  background: url("./assets/edit.png") no-repeat center center;
}

.row-delete {
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: inline-block;
  background: url("./assets/delete.png") no-repeat center center;
}
</style>
