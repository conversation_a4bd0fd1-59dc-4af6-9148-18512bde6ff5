<template>
  <div class="treeBox">
    <CetGiantTree
      class="CetGiantTree"
      v-bind="CetGiantTree_1"
      v-on="CetGiantTree_1.event"
    ></CetGiantTree>
  </div>
</template>

<script>
//项目级逻辑组件
import customApi from "@/api/custom";
import TREE_PARAMS from "@/store/treeParams.js";
export default {
  props: {
    refreshTrigger: {
      type: Number
    }
  },

  computed: {
    projectId() {
      return this.$store.state.projectId;
    }
  },

  watch: {
    refreshTrigger() {
      this.CetGiantTree_1.inputData_in = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.getTreeData();
    }
  },
  data() {
    return {
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetTree_1_currentNode_out //选中单行输出
        }
      }
    };
  },
  methods: {
    getTreeData() {
      let queryData = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.energyEntry,
        treeReturnEnable: true
      };
      customApi.getNodeTreeSimple(queryData).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.CetGiantTree_1.inputData_in = data;
          this.CetGiantTree_1.selectNode = this._.get(response, "data[0]");
        }
      });
    },
    CetTree_1_currentNode_out(val) {
      this.$emit("currentNode_out", val);
    }
  }
};
</script>

<style lang="scss" scoped>
.treeBox {
  height: 100%;
}
</style>
