<template>
  <div class="fullheight">
    <div id="container" ref="container"></div>
  </div>
</template>

<script>
import G6 from "@antv/g6";
export default {
  name: "VirtualChart",
  props: {
    inputData_in: {
      type: [Object, Array]
    },
    currentNode: {
      type: Object
    }
  },
  components: {},
  data() {
    return {
      graph: null
    };
  },
  watch: {
    inputData_in: {
      handler: function (val) {
        this.$nextTick(() => {
          this.paintChart();
        });
      },
      deep: true
    },
    currentNode: {
      handler: function (val) {
        this.initCustomChart();
      },
      deep: true
    }
  },
  methods: {
    // 初始化拓扑图形
    initGraphConf() {
      if (this.graph) return;
      let width = this.$refs.container.scrollWidth;
      let height = this.$refs.container.scrollHeight || 500;
      this.graph = new G6.Graph({
        // 写入元素所在id
        container: "container",
        width: width,
        height: height,
        // 自适应全屏展示
        fitView: true,
        // 图形放大和缩小
        minZoom: 0.5,
        maxZoom: 3,
        modes: {
          default: [
            "drag-canvas",
            "drag-node",
            "zoom-canvas",
            {
              type: "tooltip"
            }
          ]
        },
        layout: {
          type: "dagre",
          rankdir: "LR",
          nodesep: 3
        },
        defaultNode: {
          type: "customCircle",
          labelCfg: {
            style: {
              fill: "#fff",
              fontSize: 12
            },
            position: "bottom"
          },
          style: {
            lineWidth: 0,
            fill: "#C6E5FF",
            cursor: "pointer"
          }
        },
        defaultEdge: {
          size: 1,
          color: "#A3B1BF",
          type: "cubic-horizontal"
        }
      });
    },
    // 获取图表数据
    getChartData() {
      let data = this._.cloneDeep(this.inputData_in);
      if (this._.isEmpty(data)) {
        data = {
          nodes: [],
          edges: []
        };
      }
      return data;
    },
    // 绘制图形
    paintChart() {
      const data = this.getChartData();
      if (this._.isEmpty(this.graph)) {
        this.initGraphConf();
      }
      this.graph.data(data);
      this.graph.render();
    },
    // 自定义重构图形
    initCustomChart() {
      const that = this;
      const currentTheme = localStorage.getItem("omega_theme");
      let color = currentTheme === "light" ? "#333333" : "#f0f1f2";
      let name = that.currentNode.modelLabel + "$" + that.currentNode.id;
      console.log(name, "888");
      G6.registerNode("customCircle", {
        drawShape(cfg, group) {
          if (cfg.id === name) {
            group.addShape("circle", {
              attrs: {
                x: 0,
                y: 0,
                r: 10,
                fill: "red"
              },
              draggable: true
            });
          } else {
            group.addShape("circle", {
              attrs: {
                x: 0,
                y: 0,
                r: 10,
                fill: cfg.style.fill
              },
              draggable: true
            });
          }
          group.addShape("text", {
            attrs: {
              textAlign: "center",
              x: 0,
              y: 30,
              fill: color,
              text: cfg.label
            }
          });
          return group;
        },
        getAnchorPoints: function getAnchorPoints() {
          return [
            [0.5, 0.3],
            [0.5, 0.3]
          ];
        }
      });
      this.$nextTick(() => {
        this.paintChart();
      });
    }
  },
  mounted() {
    this.initCustomChart();
  }
};
</script>

<style lang="scss" scoped>
#container {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
<style lang="scss">
.g6-tooltip {
  white-space: nowrap;

  transition: left 0.4s cubic-bezier(0.23, 1, 0.32, 1) 0s,
    top 0.4s cubic-bezier(0.23, 1, 0.32, 1) 0s;
  background-color: rgba(50, 50, 50, 0.7);
  font-size: 14px;
  border-radius: 4px;
  color: rgb(255, 255, 255);
  padding: 10px;
  // min-width: 300px;
  // min-height: 200px;
  pointer-events: none;
}
</style>
