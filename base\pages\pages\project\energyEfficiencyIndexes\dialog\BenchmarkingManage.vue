<template>
  <!-- 1弹窗组件 -->
  <div>
    <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
      <span slot="footer">
        <!-- 设置组件唯一识别字段按钮组件 -->
        <CetButton
          class="fl"
          v-bind="CetButton_delete"
          v-on="CetButton_delete.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </span>
      <div>
        <CetForm
          :data.sync="CetForm_1.data"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
        >
          <el-form-item label="对标管理名称" prop="name" style="width: 320px">
            <ElInput
              class="eem_name_input"
              v-model="CetForm_1.data.name"
              v-bind="ElInput_1"
              v-on="ElInput_1.event"
            ></ElInput>
            <ElSelect
              class="eem_name_select"
              v-model="CetForm_1.data.benchmarkSelect"
              v-bind="ElSelect_benchmarkSelect"
              v-on="ElSelect_benchmarkSelect.event"
            >
              <ElOption
                v-for="item in ElOption_benchmarkSelect.options_in"
                :key="item[ElOption_benchmarkSelect.key]"
                :label="item[ElOption_benchmarkSelect.label]"
                :value="item[ElOption_benchmarkSelect.value]"
                :disabled="item[ElOption_benchmarkSelect.disabled]"
              ></ElOption>
            </ElSelect>
          </el-form-item>
          <el-form-item label="指标类型" prop="indicatortype">
            <ElSelect
              v-model="CetForm_1.data.indicatortype"
              v-bind="ElSelect_indicatortype"
              v-on="ElSelect_indicatortype.event"
            >
              <ElOption
                v-for="item in ElOption_indicatortype.options_in"
                :key="item[ElOption_indicatortype.key]"
                :label="item[ElOption_indicatortype.label]"
                :value="item[ElOption_indicatortype.value]"
                :disabled="item[ElOption_indicatortype.disabled]"
              ></ElOption>
            </ElSelect>
          </el-form-item>
          <el-form-item label="越限类型" prop="limittype">
            <ElSelect
              v-model="CetForm_1.data.limittype"
              v-bind="ElSelect_limittype"
              v-on="ElSelect_limittype.event"
            >
              <ElOption
                v-for="item in ElOption_limittype.options_in"
                :key="item[ElOption_limittype.key]"
                :label="item[ElOption_limittype.label]"
                :value="item[ElOption_limittype.value]"
                :disabled="item[ElOption_limittype.disabled]"
              ></ElOption>
            </ElSelect>
          </el-form-item>
          <el-form-item label="越限值" prop="limitvalue">
            <ElInputNumber
              v-model="CetForm_1.data.limitvalue"
              v-bind="ElInputNumber_1"
              v-on="ElInputNumber_1.event"
            ></ElInputNumber>
          </el-form-item>
          <el-form-item label="能效周期" prop="aggregationcycle">
            <ElSelect
              v-model="CetForm_1.data.aggregationcycle"
              v-bind="ElSelect_aggregationcycle"
              v-on="ElSelect_aggregationcycle.event"
            >
              <ElOption
                v-for="item in ElOption_aggregationcycle.options_in"
                :key="item[ElOption_aggregationcycle.key]"
                :label="item[ElOption_aggregationcycle.label]"
                :value="item[ElOption_aggregationcycle.value]"
                :disabled="item[ElOption_aggregationcycle.disabled]"
              ></ElOption>
            </ElSelect>
          </el-form-item>
        </CetForm>
      </div>
    </CetDialog>
    <CetInterface
      :data.sync="CetInterface_indicatortype.data"
      :dynamicInput.sync="CetInterface_indicatortype.dynamicInput"
      v-bind="CetInterface_indicatortype"
      v-on="CetInterface_indicatortype.event"
    ></CetInterface>
    <CetInterface
      :data.sync="CetInterface_limittype.data"
      :dynamicInput.sync="CetInterface_limittype.dynamicInput"
      v-bind="CetInterface_limittype"
      v-on="CetInterface_limittype.event"
    ></CetInterface>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import common from "eem-utils/common";
import { httping } from "@omega/http";
export default {
  name: "BenchmarkingManage",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    treeData_in: {
      type: Object
    },
    benchmarkset_in: {
      type: Array
    },
    energyefficiencyset_in: {
      type: Object
    },
    isEditBenchmark_in: {
      type: Boolean
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: "对标管理",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        width: "420px",
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        }
      },
      CetButton_delete: {
        visible_in: true,
        disable_in: false,
        title: "删除",
        type: "danger",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_delete_statusTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "确认",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        rules: {
          name: [
            {
              required: true,
              message: "请输入对标管理名称",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          indicatortype: [
            {
              required: true,
              message: "请选择指标类型",
              trigger: ["blur", "change"]
            }
          ],
          limittype: [
            {
              required: true,
              message: "请选择越限类型",
              trigger: ["blur", "change"]
            }
          ],
          limitvalue: [
            {
              required: true,
              message: "请输入越限值",
              trigger: ["blur", "change"]
            }
          ],
          aggregationcycle: [
            {
              required: true,
              message: "请选择能效周期",
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_1_currentData_out,
          saveData_out: this.CetForm_1_saveData_out,
          finishData_out: this.CetForm_1_finishData_out,
          finishTrigger_out: this.CetForm_1_finishTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        style: {},
        rows: 2,
        placeholder: "请输入内容",
        event: {
          change: this.ElInput_1_change_out,
          input: this.ElInput_1_input_out
        }
      },
      ElInputNumber_1: {
        ...common.check_numberFloat,
        value: "",
        style: {
          width: "200px"
        },
        controls: false,
        event: {
          change: this.ElInputNumber_1_change_out
        }
      },
      ElSelect_benchmarkSelect: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_benchmarkSelect_change_out
        }
      },
      ElOption_benchmarkSelect: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_indicatortype: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_indicatortype_change_out
        }
      },
      ElOption_indicatortype: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetInterface_indicatortype: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "queryEnum",
          modelLabel: "IndicatorType",
          dataIndex: [],
          modelList: [],
          filters: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {},
        page_in: null, // exp:{ index: 1, limit: 20 }
        defaultSort: null, // { prop: "code"  order: "descending" },
        event: {
          result_out: this.CetInterface_indicatortype_result_out,
          finishTrigger_out: this.CetInterface_indicatortype_finishTrigger_out,
          failTrigger_out: this.CetInterface_indicatortype_failTrigger_out,
          totalNum_out: this.CetInterface_indicatortype_totalNum_out
        }
      },
      ElSelect_limittype: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_limittype_change_out
        }
      },
      ElOption_limittype: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetInterface_limittype: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "queryEnum",
          modelLabel: "indicatorlimittype",
          dataIndex: [],
          modelList: [],
          filters: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {},
        page_in: null, // exp:{ index: 1, limit: 20 }
        defaultSort: null, // { prop: "code"  order: "descending" },
        event: {
          result_out: this.CetInterface_limittype_result_out,
          finishTrigger_out: this.CetInterface_limittype_finishTrigger_out,
          failTrigger_out: this.CetInterface_limittype_failTrigger_out,
          totalNum_out: this.CetInterface_limittype_totalNum_out
        }
      },
      ElSelect_aggregationcycle: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_aggregationcycle_change_out
        }
      },
      ElOption_aggregationcycle: {
        options_in: [
          {
            id: 12,
            text: "日"
          },
          {
            id: 14,
            text: "月"
          },
          {
            id: 17,
            text: "年"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      vm.$nextTick(() => {
        vm.CetInterface_indicatortype.queryTrigger_in = new Date().getTime();
        vm.CetInterface_limittype.queryTrigger_in = new Date().getTime();
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {
      var vm = this;
      vm.CetDialog_1.queryId_in = val;
    },
    inputData_in(val) {
      this.CetDialog_1.inputData_in = val;
      this.CetForm_1.data = {
        benchmarkSelect: 0,
        name: ""
      };
    },
    treeData_in(val) {
      this.CetDialog_1.treeData_in = val;
    },
    benchmarkset_in(val) {
      this.CetForm_1.data.benchmarkSelect = 0;
      this.ElOption_benchmarkSelect.options_in = this._.cloneDeep(val);
      if (!val || val.length === 0) {
        this.ElOption_benchmarkSelect.options_in = [{ name: "新建", id: 0 }];
      } else {
        this.ElOption_benchmarkSelect.options_in.unshift({
          name: "新建",
          id: 0
        });
      }
      this.CetForm_1.resetTrigger_in = new Date().getTime();
    },
    energyefficiencyset_in(val) {}
  },

  methods: {
    CetInterface_indicatortype_finishTrigger_out(val) {},
    CetInterface_indicatortype_failTrigger_out(val) {},
    CetInterface_indicatortype_totalNum_out(val) {},
    CetInterface_indicatortype_result_out(val) {
      this.ElOption_indicatortype.options_in = this._.cloneDeep(val);
    },

    CetInterface_limittype_finishTrigger_out(val) {},
    CetInterface_limittype_failTrigger_out(val) {},
    CetInterface_limittype_totalNum_out(val) {},
    CetInterface_limittype_result_out(val) {
      this.ElOption_limittype.options_in = this._.cloneDeep(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    // 删除指标
    CetButton_delete_statusTrigger_out(val) {
      var _this = this;
      var auth = _this.token; //身份验证
      var idRange = [];
      var params = {};
      var id = this.CetForm_1.data.id;
      if (!id) {
        this.$message.warning("请选择删除对标！");
        return;
      }
      idRange.push(id);
      params.modelLabel = "benchmarkset";
      params.idRange = idRange;
      this.$confirm("是否删除此对标?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          customApi.deleteModelInstence(params).then(res => {
            if (res.code === 0) {
              _this.$message({
                type: "success",
                message: "删除成功!"
              });
              _this.$emit("saveData_out", new Date().getTime());
              _this.CetDialog_1.closeTrigger_in = new Date().getTime();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    CetDialog_1_open_out(val) {},
    CetDialog_1_close_out(val) {},
    CetForm_1_currentData_out(val) {},
    CetForm_1_saveData_out(val) {
      var params = [];
      var model_data = this._.cloneDeep(this.CetForm_1.data);
      model_data.modelLabel = "benchmarkset";
      if (!model_data.energyefficiencyset_id) {
        model_data.energyefficiencyset_id = this.energyefficiencyset_in.id;
      }

      params[0] = model_data;
      this.Add_benchmarkset(params);
    },
    CetForm_1_finishData_out(val) {},
    CetForm_1_finishTrigger_out(val) {},
    ElInput_1_change_out(val) {},
    ElInput_1_input_out(val) {},

    ElInputNumber_1_change_out(val) {},
    // 设置组件唯一识别字段输出,方法名要带_out后缀
    ElSelect_benchmarkSelect_change_out(id) {
      if (
        !this.ElOption_benchmarkSelect.options_in ||
        this.ElOption_benchmarkSelect.options_in.length == 0
      ) {
        return;
      }
      var val = this.ElOption_benchmarkSelect.options_in.filter(
        item => item.id == id
      )[0];
      if (!val || !val.id) {
        this.CetForm_1.data = {
          name: ""
        };
        return;
      }

      var obj = {
        name: val.name,
        id: val.id,
        indicatortype: val.indicatortype,
        limittype: val.limittype,
        limitvalue: val.limitvalue,
        aggregationcycle: val.aggregationcycle,
        energyefficiencyset_id: val.energyefficiencyset_id
      };
      // this.CetForm_1.data = this._.cloneDeep(obj);
      this.CetForm_1.data.name = val.name;
      this.CetForm_1.data.id = val.id;
      this.CetForm_1.data.indicatortype = val.indicatortype;
      this.CetForm_1.data.limittype = val.limittype;
      this.CetForm_1.data.limitvalue = val.limitvalue;
      this.CetForm_1.data.aggregationcycle = val.aggregationcycle;
      this.CetForm_1.data.energyefficiencyset_id = val.energyefficiencyset_id;
    },

    // 设置组件唯一识别字段输出,方法名要带_out后缀
    ElSelect_indicatortype_change_out(val) {},
    ElSelect_limittype_change_out(val) {},
    ElSelect_aggregationcycle_change_out(val) {},

    Add_benchmarkset(params) {
      var _this = this;
      var auth = _this.token; //身份验证
      httping({
        url: "/model/v1/write/hierachy",
        data: params,
        method: "POST",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          _this.$message({
            message: "添加成功",
            type: "success"
          });
          _this.$emit("saveData_out", new Date().getTime());
          _this.CetDialog_1.closeTrigger_in = new Date().getTime();
        }
      });
    }
  },
  created: function () {
    this.CetForm_1.data.benchmarkSelect = 0;
    this.ElOption_benchmarkSelect.options_in = this._.cloneDeep(
      this.benchmarkset_in
    );
    if (!this.benchmarkset_in || this.benchmarkset_in.length === 0) {
      this.ElOption_benchmarkSelect.options_in = [{ name: "新建", id: 0 }];
    } else {
      this.ElOption_benchmarkSelect.options_in.unshift({ name: "新建", id: 0 });
    }
  }
};
</script>
<style lang="scss" scoped></style>
<style>
.eem_name_input {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 2;
  width: calc(100% - 30px);
}
.eem_name_input .el-input__inner {
  border-right: none !important;
  border-radius: 4px 0px 0px 4px !important;
  padding-right: 0px !important;
}
.eem_name_input .el-input__inner:focus {
  border-color: #dcdfe6;
}
.eem_name_select {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 1;
  width: 100%;
}
.eem_name_select .el-input__inner:focus {
  border-color: #dcdfe6;
}
.eem_name_select .el-select .el-input.is-focus .el-input__inner {
  border-color: #dcdfe6;
}
</style>
