<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="small"
    >
      <template slot="footer">
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <CetForm
        class="eem-cont-c1"
        :data.sync="CetForm_pagedialog.data"
        v-bind="CetForm_pagedialog"
        v-on="CetForm_pagedialog.event"
      >
        <CetTree
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
          style="height: 500px"
          ref="cetTree"
          class="nodeTree"
        ></CetTree>
        <div class="selected mtJ3 ptJ3">
          <span>
            {{ $T("已选择") }}:
            <el-tag
              class="mrJ mbJ"
              v-for="(item, index) in checkedNodes"
              :key="index"
              @close="handleClose(item)"
              closable
            >
              {{ item && item.name }}
            </el-tag>
          </span>
        </div>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";
import TREE_PARAMS from "@/store/treeParams.js";

export default {
  name: "inspectObjDialog",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    tableData: {
      type: Array
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("巡检对象"),
        "append-to-body": true,
        showClose: true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "static", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        rules: {},
        event: {
          currentData_out: this.CetForm_pagedialog_currentData_out,
          saveData_out: this.CetForm_pagedialog_saveData_out,
          finishData_out: this.CetForm_pagedialog_finishData_out,
          finishTrigger_out: this.CetForm_pagedialog_finishTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: true,
        checkStrictly: true,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out,
          checkedNodes_out: this.CetTree_1_checkedNodes_out,
          parentList_out: this.CetTree_1_parentList_out
        }
      },
      checkedNodes: [] // 选中节点
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      new Promise(res => {
        this.getTreeData(res);
      }).then(res => {
        if (res) {
          this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
        } else {
          this.$message.warning($T("获取目标设备节点树失败"));
        }
      });
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {
      this.CetForm_pagedialog.queryId_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      this.CetForm_pagedialog.inputData_in = this._.cloneDeep(val);
    }
  },
  methods: {
    CetForm_pagedialog_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_saveData_out() {
      this.$emit("saveData_out", this.checkedNodes);
      this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
    },
    CetForm_pagedialog_finishData_out(val) {
      this.$emit("finishData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishTrigger_out(val) {
      this.$emit("finishTrigger_out", val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetForm_pagedialog.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      this.CetForm_pagedialog.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    no() {},
    CetTree_1_currentNode_out() {},
    CetTree_1_checkedNodes_out(val) {
      this.checkedNodes = val;
    },
    //输出选中节点父节点列表
    outputParentList(treenode) {
      const arr = [];
      const vm = this;

      while (treenode != null && treenode.parent != null) {
        //循环res.parent获取所有父节点

        const obj = {};
        //判断key是否是数组，如果是数组则新增对象不添加该属性值
        for (var key in treenode.data) {
          if (!vm._.isArray(treenode.data[key])) {
            obj[key] = treenode.data[key];
          }
        }
        arr.unshift(obj);
        treenode = treenode.parent;
      }
      return arr;
    },
    CetTree_1_parentList_out() {},
    //获取巡检对象节点树信息
    getTreeData(callback) {
      var _this = this;
      if (!this.inputData_in.signInGroup) {
        callback && callback(false);
        return;
      }
      let params = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.inspectObjTree,
        treeReturnEnable: true
      };
      customApi
        .querySigninEquipmentXJ(params, this.inputData_in.signInGroup)
        .then(res => {
          if (res.code === 0) {
            // 只有room和选中manuequipment可选
            // _this.loop(res.data, 1);//后端接口加disabled字段,做是否可选判断了
            _this.CetTree_1.inputData_in = res.data;
            _this.CetTree_1.selectNode = res.data[0];
            _this.checkedNodes = _this._.cloneDeep(this.tableData);
            _this.CetTree_1.checkedNodes = _this._.cloneDeep(this.tableData);
            callback && callback(true);
          } else {
            callback && callback(false);
          }
        });
    },
    loop(data, index) {
      data.forEach(item => {
        this.$set(item, "tree_id", item.tree_id + "_" + index);
        if (item.children) {
          this.loop(item.children, index + 1);
        }
      });
    },
    // 点击删除tag标签
    handleClose(tag) {
      this.checkedNodes.splice(this.checkedNodes.indexOf(tag), 1);
      this.CetTree_1.checkedNodes = this._.cloneDeep(this.checkedNodes);
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.selected {
  height: 50px;
  border-top: 1px solid;
  @include border_color(B1);
  overflow: auto;
}
.nodeTree {
  :deep(.el-checkbox.is-disabled) {
    display: none !important;
  }
}
</style>
