<template>
  <div class="page flex-column">
    <div class="search mbJ3 eem-cont">
      <customElSelect
        style="width: 200px"
        :prefix_in="$T('能源类型')"
        v-model="ElSelect_1.value"
        v-bind="ElSelect_1"
        v-on="ElSelect_1.event"
      >
        <ElOption
          v-for="item in ElOption_1.options_in"
          :key="item[ElOption_1.key]"
          :label="item[ElOption_1.label]"
          :value="item[ElOption_1.value]"
          :disabled="item[ElOption_1.disabled]"
        ></ElOption>
      </customElSelect>
    </div>
    <div class="flex-auto flex-column eem-cont">
      <div class="tableTitle clearfix">
        <div class="common-title-H2 fl name">{{ $T("方案列表") }}</div>
        <CetButton
          class="fr"
          v-bind="CetButton_3"
          v-on="CetButton_3.event"
        ></CetButton>
      </div>
      <div class="flex-auto mtJ3">
        <CetTable
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
          @expand-change="tableExpandChange"
        >
          <ElTableColumn type="expand">
            <template slot-scope="props">
              <CetTable
                class="expandTable"
                :data.sync="props.row.children"
                :dynamicInput.sync="CetTable_1.dynamicInput"
                v-bind="CetTable_1"
              >
                <el-table-column
                  prop="name"
                  :label="$T('日时段方案')"
                  showOverflowTooltip
                ></el-table-column>
                <el-table-column
                  prop="identificationText"
                  :label="$T('时段')"
                  showOverflowTooltip
                ></el-table-column>
              </CetTable>
            </template>
          </ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_energytype$text"></ElTableColumn>
          <ElTableColumn
            :label="$T('操作')"
            width="150"
            header-align="left"
            align="left"
          >
            <template slot-scope="scope">
              <div @click.stop>
                <span
                  class="handle fl mrJ3"
                  @click.stop="handleCommand({ type: 'detail', scope })"
                >
                  {{ $T("详情") }}
                </span>
                <span
                  class="handle fl mrJ4"
                  @click.stop="handleCommand({ type: 'edit', scope })"
                >
                  {{ $T("编辑") }}
                </span>
                <el-dropdown class="fl" @command="handleCommand">
                  <span class="el-icon-more"></span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item :command="{ type: 'relevance', scope }">
                      {{ $T("关联节点") }}
                    </el-dropdown-item>
                    <el-dropdown-item :command="{ type: 'copy', scope }">
                      {{ $T("复制方案") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      class="delete"
                      :command="{ type: 'delete', scope }"
                    >
                      {{ $T("删除") }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </template>
          </ElTableColumn>
        </CetTable>
      </div>
    </div>
    <AddTimeSharingScheme
      :visibleTrigger_in="AddTimeSharingScheme.visibleTrigger_in"
      :closeTrigger_in="AddTimeSharingScheme.closeTrigger_in"
      :queryId_in="AddTimeSharingScheme.queryId_in"
      :inputData_in="AddTimeSharingScheme.inputData_in"
      :editId_in="AddTimeSharingScheme.editId_in"
      @finishData_out="AddTimeSharingScheme_finishData_out"
      @updateParams="updateAddTimeSharingSchemeParams"
      :timeSchemeColors="timeSchemeColors"
    />
    <AddDayScheme
      :visibleTrigger_in="AddDayScheme.visibleTrigger_in"
      :closeTrigger_in="AddDayScheme.closeTrigger_in"
      :queryId_in="AddDayScheme.queryId_in"
      :inputData_in="AddDayScheme.inputData_in"
      @finishTrigger_out="AddDayScheme_finishTrigger_out"
      @saveData_out="AddDayScheme_saveData_out"
      :timeShareSchemeDetail="timeShareSchemeDetail"
    />
    <NodeSelect
      :visibleTrigger_in="NodeSelect.visibleTrigger_in"
      :closeTrigger_in="NodeSelect.closeTrigger_in"
      :inputData_in="NodeSelect.inputData_in"
    />
    <Detail
      v-bind="detail"
      v-on="detail.event"
      :timeSchemeColors="timeSchemeColors"
    />
  </div>
</template>
<script>
import AddDayScheme from "./AddDayScheme.vue";
import AddTimeSharingScheme from "./AddTimeSharingScheme.vue";
import TREE_PARAMS from "@/store/treeParams.js";
import NodeSelect from "./NodeSelect.vue";
import Detail from "./detail";
import { httping } from "@omega/http";
export default {
  name: "Timesharingconfig",
  components: {
    AddTimeSharingScheme,
    AddDayScheme,
    NodeSelect,
    Detail
  },

  computed: {
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    }
  },

  data() {
    let timeSchemeColors = [];
    const currentTheme = localStorage.getItem("omega_theme");
    // 要与index.vue中的css中的日历单元格对应起来，当前只给了12中配色
    if (currentTheme === "dark") {
      timeSchemeColors = [
        "#0C82F8",
        "#4AFDA7",
        "#FFC531",
        "#41E0E4",
        "#193DFF",
        "#FF8F27",
        "#A4D738",
        "#6D990C",
        "#69B4FF",
        "#FFDD85",
        "#DF7EB7",
        "#048C8E"
      ];
    } else {
      timeSchemeColors = [
        "#70E09E",
        "#4CA6FF",
        "#FFD12F",
        "#7DD9FF",
        "#3166EF",
        "#FF9D09",
        "#B9E177",
        "#6FBE0B",
        "#94C8FB",
        "#FFE69B",
        "#F184C4",
        "#B1F6FF"
      ];
    }
    return {
      timeSchemeColors,
      checkNodes: [],
      // 因编辑日时段方案需要传整个方案信息进入，所有创个对象保存方案信息
      timeShareSchemeDetail: null,
      currentTebelItem: null,
      checked: false,
      showAssociationObj: false,
      daySchemeDetail: null,
      // 日时段方案列表
      daySchemeArr: [],
      actionDayScheme: -1,
      ElSelect_1: {
        value: 0,
        style: {
          // width:"200px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {}
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        label: "#", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "50" //绝对宽度
      },
      ElTableColumn_name: {
        prop: "name", // 支持path a[0].b
        label: $T("方案名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "100", //该宽度会自适应
        formatter: function (val) {
          if (val.name) {
            return val.name;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_energytype$text: {
        prop: "energytype$text", // 支持path a[0].b
        label: $T("能源类型"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "100", //该宽度会自适应
        formatter: function (val) {
          if (val.energytype$text) {
            return val.energytype$text;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      CetButton_3: {
        visible_in: true,
        disable_in: false,
        title: $T("新增分时方案"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      CetTable_2: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {}
      },

      ElTableColumn_identification: {
        //type: "",      // selection 勾选 index 序号
        prop: "identification", // 支持path a[0].b
        label: $T("日时段方案"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "100", //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (val) {
          if (val.identification) {
            return val.identification;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_daysharesetText: {
        //type: "",      // selection 勾选 index 序号
        prop: "daysharesetText ", // 支持path a[0].b
        label: $T("时段"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "100", //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (val) {
          if (val.daysharesetText) {
            return val.daysharesetText;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      CetButton_4: {
        visible_in: true,
        disable_in: true,
        title: $T("编辑"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_4_statusTrigger_out
        }
      },
      CetButton_5: {
        visible_in: true,
        disable_in: true,
        title: $T("删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_5_statusTrigger_out
        }
      },
      CetButton_7: {
        visible_in: true,
        disable_in: true,
        title: $T("编辑"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_7_statusTrigger_out
        }
      },
      getTreeDataFlag: true,
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out //勾选节点输出
        }
      },
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_2_checkedNodes_out //勾选节点输出
        }
      },
      AddTimeSharingScheme: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        editId_in: 0
      },
      AddDayScheme: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      CetButton_1disable_in: false,
      NodeSelect: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      detail: {
        openTrigger_in: new Date().getTime(),
        daySchemeDetail_in: null,
        inputData_in: null,
        currentNode_in: null,
        event: {}
      }
    };
  },
  watch: {},

  methods: {
    tableExpandChange(row, expanded) {
      if (expanded.indexOf(row) !== -1) {
        httping({
          url: `/eem-service/v1/schemeConfig/timeShareScheme/${row.id}`,
          method: "GET"
        }).then(res => {
          let timeshareperiod_model =
            this._.get(res, "data[0].timeshareperiod_model", []) || [];
          let tableData = {};
          timeshareperiod_model.forEach(item => {
            if (!tableData[item.name]) {
              tableData[item.name] = [];
            }
            var daysharesetText = [];
            if (item.dayshareset_model && item.dayshareset_model.length > 0) {
              item.dayshareset_model.forEach(ite => {
                if (Number(ite.beginhour) < 10) {
                  ite.beginhour = "0" + Number(ite.beginhour);
                }
                if (Number(ite.beginminute) < 10) {
                  ite.beginminute = "0" + Number(ite.beginminute);
                }
                if (Number(ite.endhour) < 10) {
                  ite.endhour = "0" + Number(ite.endhour);
                }
                if (Number(ite.endminute) < 10) {
                  ite.endminute = "0" + Number(ite.endminute);
                }
                daysharesetText.push(
                  `${ite.beginhour}:${ite.beginminute}~${ite.endhour}:${ite.endminute}`
                );
              });
            }
            daysharesetText = daysharesetText.join(";");
            tableData[item.name].push(
              `(${item.identification}:${daysharesetText || "--"})`
            );
          });
          let childrenData = [];
          Object.keys(tableData).forEach(item => {
            childrenData.push({
              name: item,
              identificationText: tableData[item].join("、")
            });
          });
          this.$set(row, "children", childrenData);
        });
      }
    },

    async handleCommand({ type, scope }) {
      this.currentTebelItem = scope.row;
      if (type !== "delete" && type !== "detail" && type !== "detail") {
        await this.getTimeShareSchemeDetail(scope.row.id);
      }
      switch (type) {
        case "detail":
          this.detail.daySchemeDetail_in = this._.cloneDeep(
            this.daySchemeDetail
          );
          this.detail.inputData_in = this._.cloneDeep(this.currentTebelItem);
          this.detail.openTrigger_in = new Date().getTime();
          break;
        case "edit":
          this.AddTimeSharingScheme.inputData_in = null;
          this.AddTimeSharingScheme.editId_in = this.currentTebelItem.id;
          this.AddTimeSharingScheme.visibleTrigger_in = new Date().getTime();
          break;
        case "copy":
          this.AddTimeSharingScheme.editId_in = 0;
          this.AddTimeSharingScheme.inputData_in = this._.cloneDeep(
            this.daySchemeDetail
          );
          this.AddTimeSharingScheme.inputData_in.timeshareperiodArr.forEach(
            item => {
              if (item.data && item.data.length > 0) {
                item.data.forEach(ite => {
                  ite.dayset_model = [];
                  ite.daysetObj = {};
                });
              }
            }
          );
          this.AddTimeSharingScheme.inputData_in.id = 0;
          this.AddTimeSharingScheme.inputData_in.name =
            this.AddTimeSharingScheme.inputData_in.name + $T("-副本");
          this.AddTimeSharingScheme.visibleTrigger_in = new Date().getTime();
          break;

        case "relevance":
          this.NodeSelect.inputData_in = this._.cloneDeep(
            this.currentTebelItem
          );
          this.NodeSelect.visibleTrigger_in = new Date().getTime();
          break;
        case "delete":
          this.CetButton_1_statusTrigger_out();
          break;
      }
    },
    CetGiantTree_1_checkedNodes_out(val) {
      this.checkNodes = this._.cloneDeep(val);
    },
    CetGiantTree_2_checkedNodes_out(val) {
      this.checkNodes = this._.cloneDeep(val);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    checkChange() {
      this.checkNodes = this.$refs.tree.getCheckedNodes();
    },
    // 获取节点树
    getTreeData() {
      if (!this.getTreeDataFlag) {
        return;
      }
      var _this = this;
      _this.CetGiantTree_1.inputData_in = [];
      _this.CetGiantTree_2.inputData_in = [];
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [...TREE_PARAMS.timesharingConfig],
        treeReturnEnable: true
      };
      httping({
        url: "/eem-service/v1/node/nodeTree",
        method: "POST",
        data
      }).then(res => {
        if (res.code === 0 && res.data.length > 0) {
          this.getTreeDataFlag = false;
          // 过滤掉项目下的开关柜或一段线
          var data = [];
          res.data.forEach((item, index) => {
            var obj = _this._.cloneDeep(item);
            obj.children = [];
            data.push(obj);
            if (item.children && item.children.length > 0) {
              item.children.forEach(ite => {
                if (ite.modelLabel != "linesegmentwithswitch") {
                  data[index].children.push(ite);
                }
              });
            }
          });
          _this.CetGiantTree_1.inputData_in = data;
          _this.CetGiantTree_2.inputData_in = data;
        }
      });
    },
    //获取能源类型
    getEnergytype() {
      var vm = this;

      vm.ElOption_1.options_in = [];
      httping({
        url:
          "/eem-service/v1/project/projectEnergy?projectId=" + this.projectId,
        method: "GET"
      }).then(function (response) {
        if (response.code === 0 && response.data && response.data.length > 0) {
          let selectData = [];
          response.data.forEach(item => {
            if (![13, 18, 22].includes(item.energytype)) {
              selectData.push({
                id: item.energytype,
                text: item.name
              });
            }
          });
          var res = {};
          res.id = 0;
          res.text = $T("全部");
          selectData.unshift(res);
          vm.ElOption_1.options_in = selectData;
          vm.ElSelect_1_change_out(selectData[0].id);
        } else {
          vm.ElSelect_1.value = null;
        }
      });
    },
    // 根据能源类型和项目获取分时方案
    getTimeShareScheme() {
      // this.CetTable_1.data = [];
      this.daySchemeArr = [];
      this.actionDayScheme = -1;
      this.CetTable_2.data = [];
      this.CetGiantTree_1.checkedNodes = [];
      this.CetGiantTree_2.checkedNodes = [];
      this.checkNodes = [];
      if (this.ElOption_1.options_in.length == 0) {
        return;
      }
      // 清空日历
      // this.$refs.yearPicker.disable(true);
      // this.$refs.yearPicker.drap_select(
      //   this.$moment().startOf("year").format("MM-DD"),
      //   this.$moment().endOf("year").format("MM-DD"),
      //   "",
      //   true
      // );
      httping({
        url: `/eem-service/v1/schemeConfig/timeShareScheme?energyType=${this.ElSelect_1.value}&projectId=${this.projectId}`,
        method: "GET"
      }).then(res => {
        if (res.code === 0 && res.data && res.data.length > 0) {
          let tableData = res.data;
          tableData.forEach(item => {
            let energytypeObj = this.ElOption_1.options_in.find(
              i => i.id == item.energytype
            );
            item.energytype$text = energytypeObj ? energytypeObj.text : "--";
            item.children = [];
          });
          this.CetTable_1.data = tableData;
        } else {
          this.CetTable_1.data = [];
        }
      });
    },
    // 获取分时方案
    async getTimeShareSchemeDetail(id) {
      this.$refs.yearPicker && this.$refs.yearPicker.disable(true);
      this.daySchemeArr = [];
      this.actionDayScheme = -1;
      this.daySchemeDetail = null;
      if (this.showAssociationObj) {
        return;
      }
      await httping({
        url: `/eem-service/v1/schemeConfig/timeShareScheme/${id}`,
        method: "GET"
      }).then(res => {
        if (res.code === 0 && res.data && res.data.length > 0) {
          // 保存分时详情
          this.timeShareSchemeDetail = res.data[0];
          // 处理年历
          if (
            res.data[0].timeshareperiod_model &&
            res.data[0].timeshareperiod_model.length > 0
          ) {
            res.data[0].timeshareperiod_model.forEach(item => {
              if (item.dayset_model && item.dayset_model.length > 0) {
                item.dayset_model.forEach(ite => {
                  if (!item.daysetObj) {
                    item.daysetObj = {};
                  }
                  if (!item.daysetObj[this.$moment(ite.day).year()]) {
                    item.daysetObj[this.$moment(ite.day).year()] = [ite];
                  } else {
                    item.daysetObj[this.$moment(ite.day).year()].push(ite);
                  }
                });
              }
            });
          }
          // 过滤出日时段方案
          var dayObj = {};
          if (
            res.data[0].timeshareperiod_model &&
            res.data[0].timeshareperiod_model.length > 0
          ) {
            res.data[0].timeshareperiod_model.forEach(item => {
              if (!dayObj[item.name]) {
                dayObj[item.name] = [item];
              } else {
                dayObj[item.name].push(item);
              }
            });
          }
          res.data[0].timeshareperiodArr = [];
          Object.keys(dayObj).forEach(item => {
            res.data[0].timeshareperiodArr.push({
              name: item,
              data: dayObj[item]
            });
          });
          this.daySchemeDetail = res.data[0];
          this.daySchemeArr = res.data[0].timeshareperiodArr;
          if (this.daySchemeArr.length > 0) {
            this.actionDayScheme = 0;
          }
          // this.renderYear(this.$moment().year());
          if (this.daySchemeArr && this.daySchemeArr.length > 0) {
            for (var i = this.daySchemeArr.length - 1; i >= 0; i--) {
              this.daySchemeArrCilck(this.daySchemeArr[i], i);
            }
          } else {
            // 清空
            this.CetTable_2.data = [];
            this.$refs.yearPicker &&
              this.$refs.yearPicker.drap_select(
                this.$moment().startOf("year").format("MM-DD"),
                this.$moment().endOf("year").format("MM-DD"),
                "",
                true
              );
          }
        }
      });
    },
    // 渲染年历
    renderYear(year) {
      this.$refs.yearPicker.setYear(this.$moment(year + ".1.1").valueOf());
      // 清空
      this.$refs.yearPicker.drap_select(
        this.$moment().startOf("year").format("MM-DD"),
        this.$moment().endOf("year").format("MM-DD"),
        "",
        true
      );
      if (this.actionDayScheme != -1) {
        if (this.daySchemeArr && this.daySchemeArr.length > 0) {
          this.daySchemeArr.forEach((item, index) => {
            if (item.data && item.data.length > 0) {
              if (item.data[0].daysetObj && item.data[0].daysetObj[year]) {
                item.data[0].daysetObj[year].forEach(ite => {
                  this.$refs.yearPicker.drap_select(
                    this.$moment(ite.day).format("MM-DD"),
                    this.$moment(ite.day).format("MM-DD"),
                    "color" + (index + 1)
                  );
                });
              }
            }
          });
        }
      }
    },
    // 日时段方案点击
    daySchemeArrCilck(item, index) {
      var data = item.data;
      if (data && data.length > 0) {
        data.forEach(item => {
          var daysharesetText = [];
          if (item.dayshareset_model && item.dayshareset_model.length > 0) {
            item.dayshareset_model.forEach(ite => {
              if (Number(ite.beginhour) < 10) {
                ite.beginhour = "0" + Number(ite.beginhour);
              }
              if (Number(ite.beginminute) < 10) {
                ite.beginminute = "0" + Number(ite.beginminute);
              }
              if (Number(ite.endhour) < 10) {
                ite.endhour = "0" + Number(ite.endhour);
              }
              if (Number(ite.endminute) < 10) {
                ite.endminute = "0" + Number(ite.endminute);
              }
              daysharesetText.push(
                `${ite.beginhour}:${ite.beginminute}~${ite.endhour}:${ite.endminute}`
              );
            });
          }
          item.daysharesetText = daysharesetText.join(";");
        });
      }
      this.actionDayScheme = index;
      this.CetTable_2.data = data;
    },
    // 日历输出时间
    yearPicker_year_out(val) {
      this.renderYear(this.$moment(val).year());
    },
    // 获取关联节点
    getNodeByTSScheme() {
      this.CetGiantTree_1.checkedNodes = [];
      this.CetGiantTree_2.checkedNodes = [];
      this.checkNodes = [];
      this.CetButton_1disable_in = false;
      if (!this.showAssociationObj) {
        return;
      }
      httping({
        url: `/eem-service/v1/schemeConfig/nodeByTSScheme?schemeId=${this.currentTebelItem.id}`,
        method: "GET"
      }).then(res => {
        if (res.code === 0 && res.data.length > 0) {
          this.CetButton_1disable_in = true;
          var checkedNodes = [];
          res.data.forEach(item => {
            checkedNodes.push({
              id: item.objectid,
              modelLabel: item.objectlabel,
              tree_id: item.objectlabel + "_" + item.objectid
            });
          });
          this.CetGiantTree_1.checkedNodes = this._.cloneDeep(checkedNodes);
          this.CetGiantTree_2.checkedNodes = this._.cloneDeep(checkedNodes);
          this.checkNodes = checkedNodes;
        }
        // this.$refs.tree.setCheckedKeys(
        //   this.checkNodes.map(item => item.tree_id)
        // );
      });
      // console.log("看看这个饿", this.checkNodes);
    },
    AddTimeSharingScheme_finishData_out() {
      this.getTimeShareScheme();
    },
    updateAddTimeSharingSchemeParams(val) {
      Object.assign(this.AddTimeSharingScheme, val);
    },
    AddDayScheme_finishTrigger_out() {
      this.getTimeShareSchemeDetail(this.currentTebelItem.id);
      this.getNodeByTSScheme();
    },
    AddDayScheme_saveData_out() {},
    CetButton_1_statusTrigger_out() {
      this.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        cancelButtonClass: "btn-custom-cancel",
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: (action, instance, done) => {
          if (action == "confirm") {
            if (this.CetButton_1disable_in) {
              this.$message({
                message: $T("当前方案已关联费率或已关联节点,不允许被删除"),
                type: "warning"
              });
              instance.confirmButtonLoading = false;
              done();
              return;
            }
            httping({
              url:
                "/eem-service/v1/schemeConfig/timeShareScheme?schemeId=" +
                this.currentTebelItem.id,
              method: "DELETE"
            }).then(response => {
              if (response.code == 0) {
                this.$message({
                  message: $T("删除成功"),
                  type: "success"
                });
                this.getTimeShareScheme();
              }
            });
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action != "confirm") {
            this.$message({
              type: "info",
              message: $T("取消删除！")
            });
          }
        }
      });
    },
    CetButton_10_statusTrigger_out() {
      var data = [];
      this.checkNodes.forEach(item => {
        data.push({
          id: item.id,
          modelLabel: item.modelLabel,
          name: item.name
        });
      });
      httping({
        url: `/eem-service/v1/schemeConfig/timeShareRelationship?schemeId=${this.currentTebelItem.id}`,
        method: "PUT",
        data
      }).then(res => {
        if (res.code === 0) {
          this.$message({
            message: $T("保存成功"),
            type: "success"
          });
        }
      });
    },
    CetButton_11_statusTrigger_out() {
      this.getNodeByTSScheme();
    },
    // 新增方案
    CetButton_3_statusTrigger_out(val) {
      this.AddTimeSharingScheme.editId_in = 0;
      this.AddTimeSharingScheme.inputData_in = null;
      this.AddTimeSharingScheme.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 编辑日时段方案
    CetButton_4_statusTrigger_out(val) {
      var data = this._.cloneDeep(this.daySchemeArr[this.actionDayScheme]);
      if (data && data.data && data.data.length > 0) {
        data.data.forEach(item => {
          item.name = item.identification;
          item.timeInterval = item.daysharesetText;
        });
      }
      data.timeInterval = data.data;
      data.schemeData = this.daySchemeDetail;
      this.AddDayScheme.inputData_in = data;
      this.AddDayScheme.queryId_in = 1;
      this.AddDayScheme.visibleTrigger_in = this._.cloneDeep(val);
    },
    CetButton_5_statusTrigger_out() {
      this.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        cancelButtonClass: "btn-custom-cancel",
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: (action, instance, done) => {
          if (action == "confirm") {
            httping({
              url: `/eem-service/v1/schemeConfig/timeSharePeriod?schemeId=${
                this.currentTebelItem.id
              }&dailyName=${this.daySchemeArr[this.actionDayScheme].name}`,
              method: "DELETE"
            }).then(response => {
              if (response.code == 0) {
                this.$message({
                  message: $T("删除成功"),
                  type: "success"
                });
                this.getTimeShareScheme();
              }
            });
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action != "confirm") {
            this.$message({
              type: "info",
              message: $T("取消删除！")
            });
          }
        }
      });
    },
    CetButton_6_statusTrigger_out() {
      this.showAssociationObj = true;
      this.getTreeData();
      if (!this.currentTebelItem) {
        return;
      }
      this.$nextTick(() => {
        this.getNodeByTSScheme();
      });
    },
    // 编辑方案
    CetButton_7_statusTrigger_out(val) {
      this.AddTimeSharingScheme.inputData_in = this._.cloneDeep(
        this.daySchemeDetail
      );
      this.AddTimeSharingScheme.visibleTrigger_in = this._.cloneDeep(val);
    },
    CetButton_9_statusTrigger_out() {
      this.showAssociationObj = false;
      this.getTimeShareSchemeDetail(this.currentTebelItem.id);
    },
    ElSelect_1_change_out() {
      this.getTimeShareScheme();
    },
    CetTable_1_outputData_out() {},
    CetTable_1_record_out(val) {
      if (val.id != -1) {
        this.currentTebelItem = val;
        this.getTimeShareSchemeDetail(val.id);
        this.getNodeByTSScheme();
      }
    }
  },
  activated() {
    this.getTreeDataFlag = true;
    this.showAssociationObj = false;
    this.getEnergytype();
    this.getTimeShareScheme();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .tableTitle {
    .name {
      @include line_height(Hm);
    }
  }
  .expandTable {
    height: 300px;
  }
}
.delete {
  @include font_color(Sta3);
}
.handle {
  cursor: pointer;
  @include font_color(ZS);
}
</style>
