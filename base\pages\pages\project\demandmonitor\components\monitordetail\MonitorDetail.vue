<template>
  <div class="page">
    <div
      v-if="currentNode && currentNode.modelLabel !== 'demandaccount'"
      style="width: 100%; height: 100%; position: relative"
    >
      <el-header
        height="32px"
        style="padding: 0px; line-height: 32px"
        class="mbJ3"
      >
        <el-row>
          <el-tooltip :content="currentNode && currentNode.name" effect="light" placement="bottom-start">
            <el-col class="text-ellipsis common-title-H1" :span="5">
              {{ currentNode && currentNode.name }}
            </el-col>
          </el-tooltip>
          <el-col :span="16" style="text-align: center">
            <el-tag class="firstlevel">{{ $T("一级") }}</el-tag>
            <span>
              {{ $T("进线数") }}：
              <span>{{ firstlevelNum }}</span>
            </span>
            <el-tag class="secondlevel mlJ3">{{ $T("二级") }}</el-tag>
            <span>
              {{ $T("进线数") }}：
              <span>{{ secondlevelNum }}</span>
            </span>
            <el-tag class="thirdlevel mlJ3">{{ $T("三级") }}</el-tag>
            <span>
              {{ $T("进线数") }}：
              <span>{{ thirdlevelNum }}</span>
            </span>
          </el-col>
          <el-col :span="3"></el-col>
        </el-row>
      </el-header>
      <el-main v-if="this.showEchart && demoData.length > 0" class="main-box">
        <div
          :class="{
            'echart-box': true,
            'echart-box1': item.text === $T('一级'),
            'echart-box2': item.text === $T('二级'),
            'echart-box3': item.text === $T('三级')
          }"
          v-for="(item, i) in demoData"
          :key="i"
          @click="toInletwiredetail(item)"
          style="position: relative"
        >
          <div style="text-align: right; height: 30px; line-height: 30px">
            <el-tag
              v-if="item.text"
              :class="{
                'title-tag': true,
                firstlevel: item.text === $T('一级'),
                secondlevel: item.text === $T('二级'),
                thirdlevel: item.text === $T('三级')
              }"
            >
              {{ item.text }}
            </el-tag>
          </div>
          <CetChart
            v-if="item.declareDemand"
            style="cursor: pointer; height: calc(100% - 100px)"
            :inputData_in="[item.data]"
            v-bind="CetChart_Arr[i].config"
          />
          <div v-if="!item.declareDemand" style="height: calc(100% - 100px)">
            <div
              class="fcT3"
              style="
                font-weight: bold;
                line-height: 110px;
                font-size: 20px;
              "
            >
              {{ $T("无申报数据") }}
            </div>
            <el-button type="primary" @click="toDemandmonitor(item)">
              {{ $T("立即录入") }}
            </el-button>
          </div>
          <div v-if="item.declareDemand" class="pedestal">
            <img src="../../assets/bottom.png" alt="" />
            <span>
              {{ CetChart_Arr[i].config.options.series[0].data[0].name }}
            </span>
          </div>
          <div class="fs14" v-if="parentNodes && parentNodes.length">
            <div class="basic-box-label text-overflow-2">
              <el-popover
                placement="top-start"
                trigger="hover"
                :content="parentNodesStr + '>' + item.name"
              >
                <span slot="reference" class="head-label-detail">
                  {{ parentNodesStr + ">" + item.name }}
                </span>
              </el-popover>
            </div>
          </div>
          <div class="box-content mt10 text-ellipsis">
            <el-tooltip :content="`${$T('当前需量')}：${item.value}kW`" effect="light">
              <div class="pro_span">
                {{ $T("当前需量") }}:
                  <span class="fs16">{{ item.value }}</span>
                  kW
              </div>
            </el-tooltip>
          </div>
        </div>
      </el-main>
      <el-main v-if="!this.showEchart && demoData.length > 0" class="main-box">
        <div
          :class="{
            'info-box': true,
            'info-box1': item.text === $T('一级'),
            'info-box2': item.text === $T('二级'),
            'info-box3': item.text === $T('三级')
          }"
          v-for="(item, i) in demoData"
          :key="i"
          @click="toInletwiredetail(item)"
        >
          <div class="basic-box-label text-ellipsis" style="height: 34px">
            <el-popover
              placement="top-start"
              trigger="hover"
              :content="parentNodesStr + '>' + item.name"
            >
              <span slot="reference" class="head-label-detail box-title fs14">
                {{ parentNodesStr + ">" + item.name }}
              </span>
            </el-popover>
          </div>
          <div style="height: 25px; line-height: 25px" v-if="item.value">
            <span>{{ $T("当前需量") }}: &nbsp;</span>
            <span class="dataStyle1">{{ (item.value || "--") + "kW" }}</span>
            <el-tag
              v-if="item.text"
              :class="{
                'title-tag': true,
                fr: true,
                firstlevel: item.text === $T('一级'),
                secondlevel: item.text === $T('二级'),
                thirdlevel: item.text === $T('三级')
              }"
            >
              {{ item.text }}
            </el-tag>
          </div>
          <div v-if="item.value">
            <span>{{ $T("需量") }}/{{ $T("容量") }}: &nbsp;</span>
            <span class="dataStyle2">{{ item.loadRate || "--" }}</span>
          </div>
          <div v-if="!item.value" style="text-align: center">
            <el-button type="primary" @click="toDemandmonitor(item)">
              {{ $T("立即录入") }}
            </el-button>
          </div>
        </div>
      </el-main>
      <el-main v-if="demoData.length === 0" class="main-box">
        <div class="no-graph" >
          <span>{{`${currentNode.modelLabel == 'project' ? '当前项目' : '需量账户组'}无需量账户或者需量账户未选择计费方式！`}}</span>
        </div>
      </el-main>
      <el-footer height="50px" style="padding: 0px; line-height: 50px">
        <div style="float: right">
          <i
            style="font-size: 30px; cursor: pointer"
            class="el-icon-s-unfold"
            @click="showEchart = !showEchart"
          ></i>
        </div>
      </el-footer>
    </div>
    <InletwireDetail
      v-if="currentNode && currentNode.modelLabel === 'demandaccount'"
      ref="inletwireDetail"
      :currentNode="currentNode"
      :demandMenu="demandMenu"
      :copyTreeData="copyTreeData"
    ></InletwireDetail>
  </div>
</template>

<script>
import InletwireDetail from "./InletwireDetail";
import * as echarts from "echarts";
import { httping } from "@omega/http";

export default {
  name: "MonitorDetail",
  components: {
    InletwireDetail
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    parentNodesStr() {
      return this.parentNodes
        .map(obj => {
          return obj.name;
        })
        .join(">");
    }
  },
  props: {
    currentNode: {
      type: Object
    },
    parentNodes: {},
    demandMenu: {},
    copyTreeData: {}
  },
  data() {
    return {
      setInterval: null, // 定时器
      firstlevelNum: 0,
      secondlevelNum: 0,
      thirdlevelNum: 0,
      schemeLevel: [], //预警等级信息
      showEchart: true,
      demoData: [],
      CetChart_Arr: [],
      testChart: {
        inputData_in: {},
        config: {
          options: {
            tooltip: {
              appendToBody: true,
              formatter: "{b} : {c}%"
            },
            grid: {
              containLabel: true
            },
            series: [
              {
                name: "--",
                type: "gauge",
                max: 120,
                radius: "65%",
                splitNumber: 120,
                detail: {
                  formatter: "{value}%",
                  fontSize: 24,
                  color: "#93BADF",
                  offsetCenter: [0, "28%"]
                },
                data: [
                  {
                    name: "--",
                    value: 0
                  }
                ],
                title: {
                  show: false,
                  offsetCenter: [0, "78%"],
                  color: "#93BADF",
                  fontSize: 16
                },
                axisLine: {
                  lineStyle: {
                    width: 5,
                    // color: [[[1, "#93BADF"]]]
                    color: [
                      [
                        1,
                        new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                          {
                            offset: 0,
                            color: "rgba(145,207,255,0)"
                          },
                          {
                            offset: 0.5,
                            color: "rgba(145,207,255,0.2)"
                          },
                          {
                            offset: 1,
                            color: "rgba(145,207,255,1)"
                          }
                        ])
                      ]
                    ]
                  }
                },
                splitLine: {
                  show: false
                },
                axisTick: {
                  show: false
                },
                axisLabel: {
                  formatter: "{value}",
                  color: "#87CBFE",
                  distance: -36,
                  fontSize: 10
                },
                pointer: {
                  // 仪表盘指针。
                  show: true, // 是否显示指针,默认 true。
                  length: "50%", // 指针长度，可以是绝对数值，也可以是相对于半径的百分比,默认 80%。
                  width: 3 // 指针宽度,默认 8。
                }
              }
            ]
          }
        }
      }
    };
  },
  watch: {
    currentNode: {
      handler(val) {
        // 关闭定时器
        clearInterval(this.setInterval);
        if (val.modelLabel !== "demandaccount") {
          this.getRealtime();
          // 定时刷新
          this.setInterval = setInterval(() => {
            this.getRealtime();
          }, this.systemCfg.timingTime);
        }
      }
    }
  },
  methods: {
    // 获取预警方案
    getProjectDetail() {
      this.schemeLevel = [];
      httping({
        url:
          "/eem-service/v1/alarm/getSchemes/" +
          this.projectId +
          "/project/3/-1/0",
        method: "GET"
      }).then(response => {
        if (response.code === 0 && response.data) {
          this.getSchemeLevel(
            response.data[response.data.length - 1].id,
            response.data[response.data.length - 1].modelLabel
          );
        }
      });
    },
    // 获取预警等级设置
    getSchemeLevel(id, modelLabel) {
      httping({
        url: "/eem-service/v1/alarm/getSchemeLevel/" + id + "/" + modelLabel,
        method: "GET"
      }).then(response => {
        if (response.code === 0 && response.data) {
          this.schemeLevel = response.data;
          var arr = response.data.filter(item => item.active);
          var rate1, rate2, rate3;
          arr.forEach(item => {
            if (item.alarmColor === 1) {
              rate1 = item.rate;
            } else if (item.alarmColor === 2) {
              rate2 = item.rate;
            } else if (item.alarmColor === 3) {
              rate3 = item.rate;
            }
          });
          this.rate1 = rate1;
          this.rate2 = rate2;
          this.rate3 = rate3;
          // 处理仪表盘 最大刻度是是一级告警的百分比+15
          var rate = rate1
            ? rate1 + 15
            : rate2
            ? rate + 15
            : rate3
            ? rate3 + 15
            : 100;
          /* this.testChart.config.options.series[0].max = rate;
          this.testChart.config.options.series[0].splitNumber = rate; */
          var colorArr = [];
          if (!rate3 && !rate2 && !rate1) {
            colorArr = [[1, "#1FD19F"]];
          } else {
            if (rate3) {
              colorArr.push([rate3 / rate, "#1FD19F"]);
              if (rate2) {
                colorArr.push([rate1 / rate, "#CAF13C"]);
              }
              if (rate1) {
                colorArr.push([rate1 / rate, "#F88A1E"]);
              }
              colorArr.push([1, "#EB3A2D"]);
            } else if (rate2) {
              colorArr.push([rate2 / rate, "#1FD19F"]);
              if (rate1) {
                colorArr.push([rate1 / rate, "#F88A1E"]);
              }
              colorArr.push([1, "#EB3A2D"]);
            } else {
              colorArr.push([rate1 / rate, "#1FD19F"]);
              colorArr.push([1, "#EB3A2D"]);
            }
          }
          this.testChart.config.options.series[0].axisLine = {
            lineStyle: {
              width: 5,
              // color: [1, "#1FD19F"]
              color: [
                [
                  1,
                  new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                    {
                      offset: 0,
                      color: "rgba(145,207,255,0)"
                    },
                    {
                      offset: 0.5,
                      color: "rgba(145,207,255,0.2)"
                    },
                    {
                      offset: 1,
                      color: "rgba(145,207,255,1)"
                    }
                  ])
                ]
              ]
            }
          };
          this.testChart.config.options.series[0].axisLabel.formatter =
            function (val) {
              /* if (val === 0 || val === rate1 || val === rate2 || val === rate3 || val === rate) {
              return val + " %";
            } */
              if (val % 20 === 0) {
                return val + " %";
              }
            };
        }
        this.getRealtime();
      });
    },
    // 获取预警方案
    getProjectDetail2(id, modelLabel, fn) {
      httping({
        url: `/eem-service/v1/alarm/getSchemes/${id}/${modelLabel}/3/-1/0`,
        method: "GET"
      }).then(response => {
        if (response.code === 0 && response.data) {
          this.getSchemeLevel2(
            response.data[response.data.length - 1].id,
            response.data[response.data.length - 1].modelLabel,
            fn
          );
        } else {
          fn && fn();
        }
      });
    },
    // 获取预警等级设置
    getSchemeLevel2(id, modelLabel, fn) {
      httping({
        url: "/eem-service/v1/alarm/getSchemeLevel/" + id + "/" + modelLabel,
        method: "GET"
      }).then(response => {
        if (response.code === 0 && response.data) {
          var arr = response.data.filter(item => item.active);
          var rate1, rate2, rate3;
          arr.forEach(item => {
            if (item.alarmColor === 1) {
              rate1 = item.rate;
            } else if (item.alarmColor === 2) {
              rate2 = item.rate;
            } else if (item.alarmColor === 3) {
              rate3 = item.rate;
            }
          });
          // 处理仪表盘 最大刻度是是一级告警的百分比+15
          var rate = rate1
            ? rate1 + 15
            : rate2
            ? rate + 15
            : rate3
            ? rate3 + 15
            : 100;
          var obj = this._.cloneDeep(this.testChart);
          /* obj.config.options.series[0].max = rate;
          obj.config.options.series[0].splitNumber = rate; */
          var colorArr = [];
          if (!rate3 && !rate2 && !rate1) {
            colorArr = [[1, "#1FD19F"]];
          } else {
            if (rate3) {
              colorArr.push([rate3 / rate, "#1FD19F"]);
              if (rate2) {
                colorArr.push([rate1 / rate, "#CAF13C"]);
              }
              if (rate1) {
                colorArr.push([rate1 / rate, "#F88A1E"]);
              }
              colorArr.push([1, "#EB3A2D"]);
            } else if (rate2) {
              colorArr.push([rate2 / rate, "#1FD19F"]);
              if (rate1) {
                colorArr.push([rate1 / rate, "#F88A1E"]);
              }
              colorArr.push([1, "#EB3A2D"]);
            } else {
              colorArr.push([rate1 / rate, "#1FD19F"]);
              colorArr.push([1, "#EB3A2D"]);
            }
          }
          obj.config.options.series[0].axisLine = {
            lineStyle: {
              width: 5,
              // color: colorArr
              color: [
                [
                  1,
                  new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                    {
                      offset: 0,
                      color: "rgba(145,207,255,0)"
                    },
                    {
                      offset: 0.5,
                      color: "rgba(145,207,255,0.2)"
                    },
                    {
                      offset: 1,
                      color: "rgba(145,207,255,1)"
                    }
                  ])
                ]
              ]
            }
          };
          obj.config.options.series[0].axisLabel.formatter = function (val) {
            if (val % 20 === 0) {
              return val;
            }
          };
          fn && fn(obj);
        } else {
          fn && fn();
        }
      });
    },
    // 获取实时需量
    getRealtime() {
      if (!this.currentNode) {
        return;
      }
      this.CetChart_Arr = [];
      this.demoData = [];
      this.firstlevelNum = 0;
      this.secondlevelNum = 0;
      this.thirdlevelNum = 0;
      var data = {
        alarmLevels: this.schemeLevel,
        lines: [],
        projectId: this.projectId
      };
      var obj = {};
      this.getChildrenDevice(this.currentNode, obj);
      data.lines = obj.demandaccount;
      if (!data.lines) {
        return;
      }
      this.getDemandaccountSchemesAll(data.lines, schemeAll => {
        //2022-11-1 根据张壮反馈修改47512缺陷，接口新增alarmLevelWithAccounts参数；
        schemeAll = schemeAll || [];
        let alarmLevelWithAccounts = [];
        schemeAll.forEach(item => {
          alarmLevelWithAccounts.push({
            alarmLevels: item.alarmlevelconfig_model,
            demandAccountId: item.relateNodeId
          })
        })
        data.alarmLevelWithAccounts = alarmLevelWithAccounts;
        httping({
          url: "/eem-service/v1/demand/monitor/demandMonitor/realtime",
          method: "POST",
          data
        }).then(response => {
          if (response.code !== 0 && response.data) {
            return;
          }
          const accounts = this._.get(response, "data.accounts", []) || [];
          let CetChartArr = [];
          let demoDataArr = [];
          let firstlevelNum = 0;
          let secondlevelNum = 0;
          let thirdlevelNum = 0;

          accounts.forEach(item => {
            var obj;
            // 判断此账户是否有方案
            if (
              schemeAll &&
              Array.isArray(schemeAll) &&
              schemeAll.filter(
                schemeAllitem => schemeAllitem.relateNodeId === item.id
              ).length > 0
            ) {
              obj = this.chartsTemplate(
                schemeAll.filter(
                  schemeAllitem => schemeAllitem.relateNodeId === item.id
                )[0]
              );
            } else {
              obj = this.chartsTemplate();
            }
            // this.getProjectDetail2(item.id, "demandaccount", obj => {
            // if (!obj) {
            // var obj = this._.cloneDeep(this.testChart);
            // }
            if (item.realTimeDemand && item.declareDemand) {
              obj.config.options.series[0].data[0].value = (
                (item.realTimeDemand / item.declareDemand) *
                100
              ).toFixed(2);
            } else {
              obj.config.options.series[0].data[0].value = 0;
            }
            // 处理小数位
            if (item.realTimeDemand || item.realTimeDemand === 0) {
              item.realTimeDemand = Number(item.realTimeDemand.toFixed2(2));
            }
            if (item.declareDemand !== 0 && !item.declareDemand) {
              // 没有申报
              demoDataArr.push({
                name: item.name,
                text: "",
                value: item.realTimeDemand || "--",
                loadRate: item.loadRate
                  ? item.loadRate.toFixed(2) + "%"
                  : "--",
                declareDemand: item.declareDemand,
                id: item.id,
                modelLabel: "demandaccount"
              });
            } else if (item.chargingWay === 1) {
              // var feeRate = item.feeRate ? Number(item.feeRate.toFixed2(2)) : 0;
              // 容量计费
              obj.config.options.series[0].max = 100;
              obj.config.options.series[0].splitNumber = 100;
              obj.config.options.series[0].axisLine = {
                lineStyle: {
                  width: 5,
                  // color: [[1, "#93BADF"]]
                  color: [
                    [
                      1,
                      new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                        {
                          offset: 0,
                          color: "rgba(145,207,255,0)"
                        },
                        {
                          offset: 0.5,
                          color: "rgba(145,207,255,0.2)"
                        },
                        {
                          offset: 1,
                          color: "rgba(145,207,255,1)"
                        }
                      ])
                    ]
                  ]
                }
              };
              demoDataArr.push({
                name: item.name,
                text: "",
                value: item.realTimeDemand || "--",
                loadRate: item.loadRate
                  ? (item.loadRate * 100).toFixed2(2) + "%"
                  : "--",
                declareDemand: item.declareDemand.toFixed2(2),
                id: item.id,
                modelLabel: "demandaccount"
              });
              obj.config.options.series[0].data[0].name = `${$T('需量')}/${$T('容量')}`;
              obj.config.options.series[0].data[0].value = item.loadRate
                ? (item.loadRate * 100).toFixed2(2)
                : null;
              obj.config.options.series[0].detail = {
                formatter: function (val) {
                  if (String(val) === "NaN") {
                    return "--";
                  } else {
                    return val + "%";
                  }
                },
                fontSize: 24,
                color: "#93BADF",
                offsetCenter: [0, "28%"]
              };
              obj.config.options.series[0].axisLabel.formatter = function (
                val
              ) {
                if (val % 20 === 0) {
                  return val;
                }
              };
            } else if (item.chargingWay === 2) {
              // 需量计费
              // 判断等级
              if (item.alarmLevel === 1) {
                demoDataArr.push({
                  name: item.name,
                  text: $T("一级"),
                  value: item.realTimeDemand || "--",
                  loadRate: item.loadRate
                    ? (item.loadRate * 100).toFixed2(2) + "%"
                    : "--",
                  declareDemand: item.declareDemand,
                  id: item.id,
                  modelLabel: "demandaccount"
                });
                firstlevelNum++;
              } else if (item.alarmLevel === 2) {
                demoDataArr.push({
                  name: item.name,
                  text: $T("二级"),
                  value: item.realTimeDemand || "--",
                  loadRate: item.loadRate
                    ? (item.loadRate * 100).toFixed2(2) + "%"
                    : "--",
                  declareDemand: item.declareDemand,
                  id: item.id,
                  modelLabel: "demandaccount"
                });
                secondlevelNum++;
              } else if (item.alarmLevel === 3) {
                demoDataArr.push({
                  name: item.name,
                  text: $T("三级"),
                  value: item.realTimeDemand || "--",
                  loadRate: item.loadRate
                    ? (item.loadRate * 100).toFixed2(2) + "%"
                    : "--",
                  declareDemand: item.declareDemand,
                  id: item.id,
                  modelLabel: "demandaccount"
                });
                thirdlevelNum++;
              } else {
                demoDataArr.push({
                  name: item.name,
                  text: "",
                  value: item.realTimeDemand || "--",
                  loadRate: item.loadRate
                    ? (item.loadRate * 100).toFixed2(2) + "%"
                    : "--",
                  declareDemand: item.declareDemand,
                  id: item.id,
                  modelLabel: "demandaccount"
                });
              }
              obj.config.options.series[0].data[0].name = `${$T('实时')}/${$T('申报')}`;
            }
            CetChartArr.push(obj);
            // });
          });
        
          this.CetChart_Arr = CetChartArr;
          this.demoData = demoDataArr;
          this.firstlevelNum = firstlevelNum;
          this.secondlevelNum = secondlevelNum;
          this.thirdlevelNum = thirdlevelNum;
          // this.firstlevelNum = response.data.levels["1"] || 0;
          // this.secondlevelNum = response.data.levels["2"] || 0;
          // this.thirdlevelNum = response.data.levels["3"] || 0;

        });
      });
    },
    // 获取每个账户的预警方案
    getDemandaccountSchemesAll(demandaccountArr, fn) {
      var data = {
        alarmType: 3,
        energyType: 0,
        nodes: []
      };
      demandaccountArr.forEach(item => {
        data.nodes.push({
          id: item.id,
          name: item.name,
          modelLabel: "demandaccount"
        });
      });
      httping({
        url: "/eem-service/v1/alarm/alarm/scheme",
        method: "POST",
        data
      }).then(response => {
        if (response.code === 0) {
          fn && fn(response.data);
        }
      });
    },
    // 生成仪表盘模板
    chartsTemplate(data) {
      if (
        data &&
        data.alarmlevelconfig_model.filter(item => item.isactive).length > 0
      ) {
        var rate1, rate2, rate3;
        var arr = data.alarmlevelconfig_model.filter(item => item.isactive);
        arr.forEach(item => {
          if (item.alarmcolorset_id === 1) {
            rate1 = item.rate;
          } else if (item.alarmcolorset_id === 2) {
            rate2 = item.rate;
          } else if (item.alarmcolorset_id === 3) {
            rate3 = item.rate;
          }
        });
        // 处理仪表盘 最大刻度是是一级告警的百分比+15
        var rate = rate1
          ? rate1 + 15
          : rate2
          ? rate + 15
          : rate3
          ? rate3 + 15
          : 100;
        var obj = this._.cloneDeep(this.testChart);
        /* obj.config.options.series[0].max = rate;
        obj.config.options.series[0].splitNumber = rate; */
        var colorArr = [];
        if (!rate3 && !rate2 && !rate1) {
          colorArr = [[1, "#1FD19F"]];
        } else {
          if (rate3) {
            colorArr.push([rate3 / rate, "#1FD19F"]);
            if (rate2) {
              colorArr.push([rate1 / rate, "#CAF13C"]);
            }
            if (rate1) {
              colorArr.push([rate1 / rate, "#F88A1E"]);
            }
            colorArr.push([1, "#EB3A2D"]);
          } else if (rate2) {
            colorArr.push([rate2 / rate, "#1FD19F"]);
            if (rate1) {
              colorArr.push([rate1 / rate, "#F88A1E"]);
            }
            colorArr.push([1, "#EB3A2D"]);
          } else {
            colorArr.push([rate1 / rate, "#1FD19F"]);
            colorArr.push([1, "#EB3A2D"]);
          }
        }
        obj.config.options.series[0].axisLine = {
          lineStyle: {
            width: 5,
            // color: [[1, "#93BADF"]]
            color: [
              [
                1,
                new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  {
                    offset: 0,
                    color: "rgba(145,207,255,0)"
                  },
                  {
                    offset: 0.5,
                    color: "rgba(145,207,255,0.2)"
                  },
                  {
                    offset: 1,
                    color: "rgba(145,207,255,1)"
                  }
                ])
              ]
            ]
          }
        };
        obj.config.options.series[0].axisLabel.formatter = function (val) {
          if (val % 20 === 0) {
            return val;
          }
        };
        return obj;
      } else {
        obj = this._.cloneDeep(this.testChart);
        obj.config.options.series[0].max = 100;
        obj.config.options.series[0].splitNumber = 100;
        obj.config.options.series[0].axisLine = {
          lineStyle: {
            width: 5,
            // color: [[1, "#93BADF"]]
            color: [
              [
                1,
                new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  {
                    offset: 0,
                    color: "rgba(145,207,255,0)"
                  },
                  {
                    offset: 0.5,
                    color: "rgba(145,207,255,0.2)"
                  },
                  {
                    offset: 1,
                    color: "rgba(145,207,255,1)"
                  }
                ])
              ]
            ]
          }
        };
        obj.config.options.series[0].axisLabel.formatter = function (val) {
          if (val % 20 === 0) {
            return val;
          }
        };
        return obj;
      }
    },
    // 获取节点下的所有进线id
    getChildrenDevice(node, obj) {
      if (node.modelLabel === "demandaccount") {
        // 找到设备
        if (obj[node.modelLabel]) {
          if (
            obj[node.modelLabel].indexOf({
              id: node.id,
              name: node.name,
              chargingWay: node.chargingWay
            }) === -1
          ) {
            obj[node.modelLabel].push({
              id: node.id,
              name: node.name,
              chargingWay: node.chargingWay
            });
          }
        } else {
          obj[node.modelLabel] = [
            {
              id: node.id,
              name: node.name,
              chargingWay: node.chargingWay
            }
          ];
        }
      } else {
        if (node.children && node.children.length > 0) {
          node.children.forEach(item => {
            this.getChildrenDevice(item, obj);
          });
        }
      }
    },
    // 页面跳转
    toInletwiredetail(data) {
      // 申报值存在才跳转
      if (data.declareDemand) {
        this.$emit("changeNode", data);
      }
    },
    toDemandmonitor(data) {
      if (!data.declareDemand) {
        this.$router.push({
          name: "declareproposal",
          params: data
        });
      }
    }
  },
  activated: function () {
    this.showEchart = true;
    this.getProjectDetail();
    // 定时刷新
    this.setInterval = setInterval(() => {
      this.getRealtime();
    }, this.systemCfg.timingTime);
  },
  destroyed: function () {
    // 关闭定时器
    clearInterval(this.setInterval);
  },
  deactivated: function () {
    // 关闭定时器
    clearInterval(this.setInterval);
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .el-tag {
    line-height: 25px;
  }
}
.head-title {
  float: left;
  height: 48px;
  line-height: 48px;
}
.main-box {
  padding: 0px;
  height: calc(100% - 110px);
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
}
.echart-box {
  width: 238px;
  height: 330px;
  float: left;
  @include margin(J1);
  @include background_color(BG1);
  @include border_radius(C);
  text-align: center;
  @include padding(J3 J4);
  box-sizing: border-box;
  cursor: pointer;
}

.echart-box1 {
  .box-content {
    span {
      color: rgb(233, 47, 39);
    }
  }
}
.echart-box2 {
  .box-content {
    span {
      color: rgb(249, 137, 29);
    }
  }
}
.echart-box3 {
  .box-content {
    span {
      color: rgb(251, 225, 0);
    }
  }
}
.title-tag {
  margin: 5px 8px 0 0;
}
.info-box {
  width: 300px;
  height: 110px;
  padding: 10px;
  box-sizing: border-box;
  margin: 5px;
  float: left;
  border-radius: 5px;
  @include background_color(BG1);
  cursor: pointer;
  .box-title {
    width: calc(100% - 50px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.info-box1 {
  text-align: left;
  .info-content {
    & > div:nth-child(2) {
      color: rgb(233, 47, 39);
    }
  }
  .dataStyle1 {
    color: rgb(233, 47, 39);
  }
  .dataStyle2 {
    color: rgb(233, 47, 39);
  }
}
.info-box2 {
  text-align: left;
  .info-content {
    & > div:nth-child(2) {
      color: rgb(249, 137, 29);
    }
  }
  .dataStyle1 {
    color: rgb(249, 137, 29);
  }
  .dataStyle2 {
    color: rgb(249, 137, 29);
  }
}
.info-box3 {
  text-align: left;
  .info-content {
    & > div:nth-child(2) {
      color: rgb(251, 225, 0);
    }
  }
  .dataStyle1 {
    color: rgb(251, 225, 0);
  }
  .dataStyle2 {
    color: rgb(251, 225, 0);
  }
}
.firstlevel {
  height: 25px;
  border-color: rgba(255, 0, 0, 1);
  color: rgba(255, 0, 0, 1);
  background: rgba(0, 0, 0, 0);
}
.secondlevel {
  height: 25px;
  border-color: rgba(255, 153, 0, 1);
  color: rgba(255, 153, 0, 1);
  background: rgba(0, 0, 0, 0);
}
.thirdlevel {
  height: 25px;
  border-color: rgba(251, 225, 0, 1);
  color: rgba(251, 225, 0, 1);
  background: rgba(0, 0, 0, 0);
}
.leftlabel {
  flex: 1;
  text-align: center;
  display: inline-block;
  margin: 12px 0 3px 0;
}
.rightlabel {
  flex: 1;
  text-align: center;
  display: inline-block;
  margin: 12px 0 3px 0;
}
.dataStyle1 {
  flex: 1;
  color: rgb(29, 204, 164);
  display: inline-block;
  // margin: 12px 0 3px 0;
}
.dataStyle2 {
  flex: 1;
  color: rgb(29, 204, 164);
  // margin: 12px 0 3px 0;
}
.dataStyle {
  color: #e92f27;
  display: inline-block;
  margin: 0px 35px 0 10px;
}
.pedestal {
  position: absolute;
  top: 68%;
  left: 50%;
  transform: translate(-50%, -50%);
  img {
    transform: scale(0.8);
  }
  span {
    color: #fff;
    position: absolute;
    top: 42%;
    left: 50%;
    transform: translate(-50%, -50%);
    white-space: nowrap;
  }
}
.pro_span {
  display: inline;
  text-align: center; 
  font-size: 14px;
}
.no-graph {
  height: 100%;
  width: 100%;
  text-align: center;
  padding: 200px 10% 0px;
  box-sizing: border-box;
  span {
    @include font_size(H1);
  }
}
.text-overflow-2 {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
</style>
