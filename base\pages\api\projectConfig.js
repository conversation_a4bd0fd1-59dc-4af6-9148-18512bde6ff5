import fetch from "eem-utils/fetch";
const version = "v1";

function processResponse(response) {
  // 对 response 进行任意转换处理, response结构
  //   {
  //     // `data` 由服务器提供的响应
  //     data: {},

  //     // `status` 来自服务器响应的 HTTP 状态码
  //     status: 200,

  //     // `statusText` 来自服务器响应的 HTTP 状态信息
  //     statusText: 'OK',

  //     // `headers` 服务器响应的头
  //     headers: {},

  //     // `config` 是为请求提供的配置信息
  //     config: {}
  //   }
  return response;
}

// 获取节点树
export function getProjectConfig(data) {
  return fetch({
    url: `/eem-service/v1/node/nodeTree?system=cloud&version=2`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}
// 获取节点树，并且获取配电室下的出线
export function getProjectConfigAndLinesegmentwithswitch(data) {
  data.subLayerConditions[
    data.subLayerConditions.findIndex(
      item => item.modelLabel == "linesegmentwithswitch"
    )
  ].filter = {
    expressions: [
      {
        prop: "voltagelevel",
        operator: "IN",
        limit: [12, 13, 14]
      },
      { prop: "operationstatus", operator: "EQ", limit: 1 },
      { prop: "linefunctiontype", operator: "EQ", limit: 3 }
    ]
  };
  return fetch({
    url: `/eem-service/v1/node/nodeTree?system=cloud&version=2`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}
// 新增/修改厂区
export function createSectionarea(data) {
  if (data.children && data.children.length > 0) {
    data.children = [
      {
        id: data.children[0].id,
        modelLabel: data.children[0].modelLabel
      }
    ];
  }

  return fetch({
    url: `/eem-service/${version}/project/manageNode`,
    method: "PUT",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: [data]
  });
}
// 删除厂区
export function deleteSectionarea(data) {
  return fetch({
    url: `/eem-service/${version}/project/manageNode`,
    method: "DELETE",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}
// 删除楼栋
export function deleteBuilding(data) {
  return fetch({
    url: `/eem-service/${version}/project/manageNode`,
    method: "DELETE",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}

// 删除用能设备
export function deleteManuequipment(data) {
  return fetch({
    url: `/eem-service/${version}/project/manageNode`,
    method: "DELETE",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}
// 删除文件
export function deleteFile(path) {
  return fetch({
    url: `/eem-service/${version}/common/deleteFile?path=${path}`,
    method: "DELETE",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}
// 新增/修改配电室
export function createProjectConfigRoom(data) {
  data.children = [
    {
      id: data._projectId,
      modelLabel: data._modelLabel
    }
  ];
  return fetch({
    url: `/eem-service/${version}/project/manageNode`,
    method: "PUT",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: [data]
  });
}
// 删除配电室
export function deleteRoomManageNode(data) {
  return fetch({
    url: `/eem-service/${version}/project/manageNode`,
    method: "DELETE",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}
// 通信机房
// 删除设备
export function deleteDeviceManageNode(data) {
  return fetch({
    url: `/eem-service/${version}/project/manageNode`,
    method: "DELETE",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}
// 新建或者修改设备
export function createEndEditDevice(data) {
  return fetch({
    url: `/eem-service/${version}/project/manageNode`,
    method: "PUT",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: [data]
  });
}
// 新建或者修改节点
export function createEndEditNode(data) {
  return fetch({
    url: `/eem-service/${version}/project/manageNode`,
    method: "PUT",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}
// 创建物理量
export function quantityObjectSetting(data) {
  return fetch({
    url: `/eem-service/${version}/quantity/quantityObjectSetting`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: data
  });
}
