<template>
  <!-- 1弹窗组件 -->
  <CetDialog
    class="CetDialog small"
    v-bind="CetDialog_1"
    v-on="CetDialog_1.event"
  >
    <el-container class="fullheight flex-column bg1 brC pJ3 plJ4 prJ4">
      <div class="mbJ1">
        <el-input
          :placeholder="$T('输入关键字以检索')"
          class="device-search"
          v-model="filterText"
          @change="filterText1Change"
        ></el-input>
      </div>
      <CetTree
        v-if="flag"
        ref="CetTree2"
        class="switch-tree"
        :selectNode.sync="CetTree_3.selectNode"
        :checkedNodes.sync="CetTree_3.checkedNodes"
        :searchText_in.sync="CetTree_3.searchText_in"
        v-bind="CetTree_3"
        v-on="CetTree_3.event"
      ></CetTree>
      <div class="tagBox" v-if="showTagList">
        <el-tag
          :key="index"
          v-for="(tag, index) in allTreeCheckedNodes"
          :closable="tag.closable"
          :disable-transitions="false"
          @close="handleClose(index)"
        >
          {{ tag.name }}
        </el-tag>
      </div>
    </el-container>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import { httping } from "@omega/http";
export default {
  name: "shareRate",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    currentNode: {
      type: Object
    },
    // 已关联采集设备列表
    deviceList: {
      type: Array,
      default() {
        return [];
      }
    },
    // 是否展示已关联列表
    showTagList: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userInfo() {
      var vm = this;
      return vm.$store.state.userInfo;
    }
  },

  data() {
    return {
      allTreeCheckedNodes: [],
      flag: false,
      // 懒加载开关
      loadNodeFlag: false,
      CetDialog_1: {
        title: $T("添加采集设备"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        },
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      filterText: "",
      CetTree_3: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: false,
        ShowRootNode: false,
        defaultExpandedKeys: [],
        nodeKey: "tree_id",
        props: {
          label: "text",
          children: "children",
          isLeaf: "leaf",
          disabled: "disabled"
        },
        highlightCurrent: true,
        showCheckbox: true,
        checkStrictly: false,
        lazy: true,
        load: this.loadNode3,
        event: {
          checkedNodes_out: this.CetTree_3_checkedNodes_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      vm.CetTree_3.checkedNodes = [];
      this.CetTree_3.defaultExpandedKeys = [];
      this.allTreeCheckedNodes = this.deviceList.map(item => {
        return {
          ...item,
          closable: false
        };
      });
      vm.filterText = "";
      vm.flag = false;
      vm.$nextTick(() => {
        this.CetTree_3.lazy = true;
        vm.flag = true;
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    inputData_in(val) {
      this.CetDialog_1.inputData_in = val;
    }
  },

  methods: {
    handleClose(index) {
      this.allTreeCheckedNodes.splice(index, 1);
      const checkNodes = this.allTreeCheckedNodes.filter(item => item.closable);
      this.CetTree_3.checkedNodes = checkNodes;
    },
    filterText1Change(val) {
      this.CetTree_3.lazy = !val;
      if (val) {
        httping({
          url: `/eem-service/v2/peccore/pecCoreMeterTree`,
          method: "POST",
          data: {
            loadDevice: true,
            async: false,
            name: this.filterText,
            tenantId: this.$store.state.projectTenantId,
            nodeId: 0,
            nodeType: 269615104
          }
        }).then(res => {
          var data = this._.get(res, "data", []);
          this.setPecCoreTreeLeaf(data);
          this.CetTree_3.inputData_in = data;
          this.CetTree_3.defaultExpandedKeys = this.loop(data);
        });
      } else {
        this.loadNode3({ level: 0 }, false, true);
        this.CetTree_3.defaultExpandedKeys = [];
      }
    },
    // 懒加载搜索，用于将搜索的节点展开显示
    loop(data, arr = []) {
      data.forEach(item => {
        arr.push(item.tree_id);
        if (item.children && item.children.length) {
          this.loop(item.children, arr);
        }
      });
      return arr;
    },
    // 采集设备懒加载
    loadNode3(node, resolve, init) {
      if (this.filterText) {
        resolve(node.data.children || []);
        return;
      }
      if (node.isLeaf) {
        resolve([]);
        return;
      }
      var nodeId, nodeType;
      if (node.level === 0 || init) {
        nodeId = 0;
        nodeType = 269615104;
      } else {
        nodeId = node.data.nodeId;
        nodeType = node.data.nodeType;
      }
      httping({
        url: `/eem-service/v2/peccore/pecCoreMeterTree`,
        method: "POST",
        data: {
          loadDevice: true,
          async: true,
          tenantId: this.$store.state.projectTenantId,
          nodeId: nodeId,
          nodeType: nodeType
        }
      }).then(res => {
        var data = this._.get(res, "data", []);
        this.setPecCoreTreeLeaf(data);
        if (init) {
          this.CetTree_3.inputData_in = data;
        } else {
          resolve(data);
        }
      });
    },
    setPecCoreTreeLeaf(nodes) {
      if (nodes && nodes.length > 0) {
        nodes.forEach(item => {
          if (item.nodeType == "269619472") {
            item.leaf = true;
            if (
              this.deviceList.filter(
                i => i.id == item.nodeId && i.modelLabel == item.nodeType
              ).length == 0
            ) {
              item.disabled = false;
            } else {
              item.disabled = true;
            }
          } else {
            item.disabled = true;
          }
          item.id = item.nodeId;
          item.modelLabel = item.nodeType;
          item.tree_id = item.modelLabel + "_" + item.id;
          this.setPecCoreTreeLeaf(this._.get(item, "children", []));
        });
      }
    },
    CetTree_3_checkedNodes_out(val) {
      this.allTreeCheckedNodes = (val || [])
        .map(item => {
          if (item.nodeType == "269619472")
            return {
              ...item,
              name: item.text,
              closable: true
            };
        })
        .filter(i => i)
        .concat(this.deviceList);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      var _this = this;
      var deviceArr = [];
      _this.CetTree_3.checkedNodes.map(item => {
        if (item.nodeType == "269619472") {
          deviceArr.push({
            deviceName: item.text,
            modelLabel: "269619472",
            id: item.nodeId,
            tree_id: "269619472_" + item.nodeId
          });
        }
      });
      if (!deviceArr.length) {
        _this.$message({
          message: $T("请选择采集设备"),
          type: "warning"
        });
        return;
      }
      this.Edit_quantity(deviceArr);
    },
    CetDialog_1_open_out(val) {},
    CetDialog_1_close_out(val) {},
    // 保存关联测点
    Edit_quantity(deviceArr) {
      let deviceList = this._.cloneDeep(this.deviceList) || [];
      deviceList.push(...deviceArr);
      var params = [
        {
          measureInfos: [],
          node: {
            deviceIds: [],
            id: this.currentNode.id,
            modelLabel: this.currentNode.modelLabel
          }
        }
      ];
      if (deviceList.length > 0) {
        deviceList.forEach(item => {
          if (item.monitoredNodeArr && item.monitoredNodeArr.length > 0) {
            item.monitoredNodeArr.forEach(i => {
              i.deviceId = item.id;
            });
            params[0].measureInfos.push(...item.monitoredNodeArr);
          }
          params[0].node.deviceIds.push(item.id);
        });
      }

      var params2 = [
        {
          modelLabel: this.currentNode.modelLabel,
          nodes: [
            {
              deviceIds: params[0].node.deviceIds,
              id: this.currentNode.id
            }
          ]
        }
      ];
      // 数据源
      var dataSource = 6;
      if (params[0].node.deviceIds.length === 0) {
        dataSource = 6;
      } else {
        dataSource = 1;
      }
      httping({
        url: "/eem-service/v1/quantity/quantityMap/measureInfo",
        data: params,
        method: "PUT",
        timeout: 10000
      }).then(res => {
        // 向外输入选中的采集设备
        this.$emit("deviceArr_out", deviceArr);
        this.Edit_dataSource(dataSource, params2);
      });
    },
    // 更新物理量数据源
    Edit_dataSource(dataSource, params) {
      httping({
        url: "/eem-service/v1/quantity/quantityObject?dataSource=" + dataSource,
        method: "POST",
        data: params
      }).then(response => {
        if (response.code === 0) {
          this.$emit("updata_out");
          this.$message({
            message: $T("保存成功"),
            type: "success"
          });
          this.CetDialog_1.closeTrigger_in = new Date().getTime();
        }
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep() {
    .el-dialog__body {
      @include background_color(BG);
      @include padding(J1);
    }
  }
  .tagBox {
    min-height: 100px;
    border-top: 1px solid;
    border-bottom: 1px solid;
    @include border_color(B1);
    @include padding(J3 0);
    :deep(.el-tag) {
      @include margin_right(J1);
      @include margin_top(J1);
    }
  }
}
.switch-tree {
  height: 500px;
  :deep(.el-tree) {
    // background: #1c2850;
  }
  :deep & > .el-tree > .el-tree-node > .el-tree-node__content {
    .el-tree-node__expand-icon + .el-checkbox {
      display: none;
    }
  }
  :deep
    &
    > .el-tree
    > .el-tree-node
    > .el-tree-node__children
    > .el-tree-node
    > .el-tree-node__content {
    .el-tree-node__expand-icon + .el-checkbox {
      display: none;
    }
  }
  // 模拟打钩
  :deep .el-checkbox.is-disabled .el-checkbox__inner {
    background: url("../assets/check.png") no-repeat center center;
  }
}
</style>
