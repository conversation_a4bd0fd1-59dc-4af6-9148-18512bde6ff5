<template>
  <div class="page" :class="bgClass">
    <div class="head">
      <div class="title">{{ $T("有效率占比") }}</div>
    </div>
    <div style="height: calc(100% - 40px)">
      <div style="height: 60%">
        <CetChart v-bind="CetChart_ratio" class="fullheight"></CetChart>
      </div>
      <div style="height: 40%">
        <div class="text-center mtJ3">
          <span>{{ $T("有效率") }}</span>
          <span class="fs26" :class="efficiencyClass" style="font-weight: 600">
            {{ CetChart_ratio.options.series.data[0].value }}%
          </span>
        </div>
        <div class="text-center mtJ3">
          <span>{{ $T("无效率") }}</span>
          <span class="fs26 color-Sta3" style="font-weight: 600">
            {{ CetChart_ratio.options.series.data[1].value }}%
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import common from "eem-utils/common";
import omegaTheme from "@omega/theme";
export default {
  name: "efficiencyRatio",
  components: {},
  props: {
    efficiencyRatioData: {
      type: Object
    }
  },
  data() {
    return {
      bgClass: "bg-green",
      efficiencyClass: "color-Sta1",
      CetChart_ratio: {
        //组件输入项
        inputData_in: null,
        options: {
          series: {
            type: "pie",
            radius: ["80%", "90%"],
            name: $T("有效率占比"),
            avoidLabelOverlap: false,
            label: {
              show: true,
              position: "center",
              formatter: params => {
                return this.CetChart_ratio.options.series.data[0].value + "%";
              },
              fontSize: 26,
              fontWeight: 600,
              color: "#29b061"
            },
            labelLine: {
              show: false
            },
            emphasis: {
              disabled: true
            },
            data: [
              {
                value: 0,
                itemStyle: {
                  color: "#29b061"
                }
              },
              {
                value: 0,
                itemStyle: {
                  color: "rgba(13, 255, 134,0.1)"
                }
              }
            ]
          }
        }
      }
    };
  },
  watch: {
    efficiencyRatioData(val) {
      const efficiencyPrecent = val.efficiency
        ? common.formatNumberWithPrecision(val.efficiency * 100, 2)
        : 0;
      const inefficiencyPrecent = val.inefficiency
        ? common.formatNumberWithPrecision(val.inefficiency * 100, 2)
        : 0;
      this.CetChart_ratio.options.series.data[0].value = efficiencyPrecent;
      this.CetChart_ratio.options.series.data[1].value = inefficiencyPrecent;

      // 样式
      if (efficiencyPrecent > 50) {
        this.bgClass = "bg-green";
        this.efficiencyClass = "color-Sta1";

        const color = omegaTheme.theme === "light" ? "#29b061" : "#0DFF86";
        const opacityColor =
          omegaTheme.theme === "light"
            ? "rgba(41, 176, 97,0.1)"
            : "rgba(13, 255, 134,0.1)";
        this.CetChart_ratio.options.series.label.color = color;
        this.CetChart_ratio.options.series.data[0].itemStyle.color = color;
        this.CetChart_ratio.options.series.data[1].itemStyle.color =
          opacityColor;
      } else {
        this.bgClass = "bg-yellow";
        this.efficiencyClass = "color-Sta2";

        this.CetChart_ratio.options.series.label.color = "#ff782b";
        this.CetChart_ratio.options.series.data[0].itemStyle.color = "#ff782b";
        this.CetChart_ratio.options.series.data[1].itemStyle.color =
          "rgba(255, 120, 43,0.1)";
      }
    }
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  border-radius: 8px;
}
.bg-green {
  background: url("../../../assets/bg-green.png") no-repeat;
  background-size: cover;
}
.bg-yellow {
  background: url("../../../assets/bg-yellow.png") no-repeat;
  background-size: cover;
}
.head {
  height: 32px;
  line-height: 32px;
  margin-bottom: 8px;
  .title {
    font-size: 16px;
    font-weight: bold;
  }
}
.flex-end {
  margin-left: auto;
  justify-content: flex-end;
}
.flex1 {
  flex: 1;
}
.color-Sta1 {
  @include font_color(Sta1);
}
.color-Sta2 {
  color: #ff782b;
}
.color-Sta3 {
  @include font_color(Sta3);
}
</style>
