<template>
  <!-- 1弹窗组件 -->
  <CetDialog
    v-bind="CetDialog_1"
    v-on="CetDialog_1.event"
    class="el_dialog small"
  >
    <CetForm
      :data.sync="CetForm_1.data"
      v-bind="CetForm_1"
      v-on="CetForm_1.event"
      class="eem-cont"
      label-position="top"
    >
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="方案名称" prop="name">
            <ElInput
              v-model="CetForm_1.data.name"
              v-bind="ElInput_1"
              v-on="ElInput_1.event"
            ></ElInput>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="费率类型" prop>
            <ElInput v-model="ElInput_3.value" v-bind="ElInput_3"></ElInput>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="费率值（元/kVA）" prop="feerate">
            <ElInputNumber
              v-model="CetForm_1.data.feerate"
              v-bind="ElInputNumber_1"
              v-on="ElInputNumber_1.event"
            ></ElInputNumber>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生效时间" prop="effectivedate">
            <el-date-picker
              style="width: 100%"
              v-model="CetForm_1.data.effectivedate"
              v-bind="CetDatePicker_1.config"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
    </CetForm>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import common from "eem-utils/common";
import { httping } from "@omega/http";
export default {
  name: "AddScheme",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    data_list_in: {
      type: Array
    },
    schemeName: {
      // 验证重名
      type: String
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: "调整计费方案",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        width: "480px",
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "确定",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "关闭",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [
            "name",
            "feerate",
            "effectivedate",
            "schemeId",
            "modelId",
            "modelLabel"
          ], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "150px",
        rules: {
          name: [
            {
              required: true,
              message: "请输入方案名称",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          feerate: [
            {
              required: true,
              message: "请输入费率值",
              trigger: ["blur", "change"]
            }
          ],
          effectivedate: [
            {
              required: true,
              message: "请选择生效时间",
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        disabled: false,
        event: {}
      },
      ElInput_3: {
        value: " 容量计费",
        style: {
          width: "100%"
        },
        disabled: true
      },
      ElInputNumber_1: {
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        min: 0,
        max: 999999999999.99999,
        step: 2,
        precision: 5,
        controlsPosition: "",
        disabled: false,
        event: {}
      },
      CetDatePicker_1: {
        disable_in: false,
        val: this.$moment().add(1, "month").startOf("month").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "month",
          rangeSeparator: "-",
          style: {
            display: "inline-block"
          },
          pickerOptions: {
            disabledDate(time) {
              return time.getTime() < new Date().getTime();
            }
          }
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.CetForm_1.resetTrigger_in = new Date().getTime();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },

    inputData_in(val) {
      this.CetForm_1.data = this._.cloneDeep(val);
    }
  },

  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_saveData_out() {
      if (this.schemeName == this.CetForm_1.data.name) {
        this.$message({
          message: "方案名重名",
          type: "warning"
        });
      } else {
        this.addScheme1();
      }
    },
    addScheme1() {
      var data = {
        chargeway: 1,
        effectivedate:
          this.CetForm_1.data.effectivedate &&
          this.$moment(this.CetForm_1.data.effectivedate)
            .startOf("month")
            .valueOf(),
        fee: this.CetForm_1.data.feerate,
        name: this.CetForm_1.data.name,
        recordId: 0,
        schemeId: this.CetForm_1.data.schemeId,
        modelId: this.CetForm_1.data.modelId,
        modelLabel: this.CetForm_1.data.modelLabel
      };
      // 历史记录已有此月，加传recordId
      var arr = this.data_list_in.filter(item => {
        if (
          this.$moment(item.effectivedate).startOf("month").valueOf() ==
          this.$moment(this.CetForm_1.data.effectivedate)
            .startOf("month")
            .valueOf()
        ) {
          data.recordId = item.id;
        }
      });
      httping({
        url: "/eem-service/v1/demand/maintain/saveVolumeFeeScheme",
        method: "POST",
        data
      }).then(response => {
        if (response.code == 0) {
          this.$message({
            message: "保存成功",
            type: "success"
          });
          this.CetDialog_1.closeTrigger_in = this._.cloneDeep(
            new Date().getTime()
          );
          this.$emit("finishTrigger_out", new Date().getTime());
        }
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.tooltipInfo {
  color: #999999;
  font-size: 13px;
  position: relative;
  top: -10px;
}
.el_dialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
    @include border-radius(C1);
  }
}
</style>
