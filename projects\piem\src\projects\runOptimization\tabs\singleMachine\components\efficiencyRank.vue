<template>
  <div class="page">
    <div class="head">
      <div class="title">{{ $T("效率相关性排名") }}</div>
    </div>
    <div style="height: calc(100% - 40px); overflow: auto">
      <div v-for="(item, index) in rankList" :key="index" class="rank-row mbJ1">
        <img
          :src="
            require(`../../../assets/rank-${index > 2 ? 3 : index + 1}.png`)
          "
          alt=""
          v-if="item.value >= 0.5"
          class="mrJ2"
        />
        <span>{{ item.name }}</span>
        <span class="value flex-end">
          {{ item.value }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import common from "eem-utils/common";
export default {
  name: "efficiencyRank",
  components: {},
  props: {
    efficiencyRankData: {
      type: Array
    }
  },
  data() {
    return {
      rankList: []
    };
  },
  watch: {
    efficiencyRankData(val) {
      const list = val?.find(item => item?.find(v => v.x === "效率")) || [];
      const newList = list.filter(item => item.y !== "效率");
      const positiveList = newList.map(item => {
        return {
          name: item.y,
          value: common.formatNumberWithPrecision(Math.abs(item.value), 2)
        };
      });
      const sortList = positiveList.sort((a, b) => b.value - a.value);
      this.rankList = sortList;
    }
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  background: url("../../../assets/bg-blue.png") no-repeat;
  background-size: cover;
}
.head {
  height: 32px;
  line-height: 32px;
  margin-bottom: 8px;
  .title {
    font-size: 16px;
    font-weight: bold;
  }
}
.flex-end {
  margin-left: auto;
  justify-content: flex-end;
}
.flex1 {
  flex: 1;
}
.rank-row {
  height: 32px;
  line-height: 32px;
  display: flex;
  align-items: center;
  .value {
    font-size: 20px;
    font-weight: 600;
    @include font_color(oil2);
  }
}
</style>
