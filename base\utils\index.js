import { isDev, tableColumsFormatter, formRules, util } from "./common.js";
import commonDefaultExport from "./common.js";
import api from "./api.js";
import enums from "./enums.js";
import fetch from "./fetch.js";
import * as fetchExport from "./fetch.js";
import { DataModel } from "./model.js";
import task from "./task.js";
import utilDefaultExport from "./util.js";
import * as utilExport from "./util.js";
import moment from "moment";

const engineeringInte = function (Vue) {
  Number.prototype.toFixed2 = function (precision) {
    precision = precision || 0;
    var pow = Math.pow(10, precision);
    return (Math.round(this * pow) / pow).toFixed(precision);
  };

  Vue.filter("formatNum", (num, precision = 0, unit = "") => {
    if (num === 0) return 0;
    if (!num || _.isNaN(_.toNumber(num))) return "--";
    if (!precision && precision !== 0) return num;
    let pow = Math.pow(10, precision);
    return (
      (Math.round(num * pow) / pow).toFixed(precision) + (unit ? unit : "")
    );
  });

  // 千位加逗号
  Vue.filter("formatNum2", num => {
    if (num === 0) return 0;
    if (!num || _.isNaN(_.toNumber(num))) return "--";
    var floorStr = "";
    if ((num || 0).toString().split(".").length > 1) {
      floorStr = (num || 0).toString().split(".")[1];
    }
    var result = "",
      counter = 0;
    num = (num || 0).toString().split(".")[0];
    for (var i = num.length - 1; i >= 0; i--) {
      counter++;
      result = num.charAt(i) + result;
      if (!(counter % 3) && i != 0) {
        result = "," + result;
      }
    }
    return result + "." + floorStr;
  });

  Vue.filter("formatDate", (val, format) => {
    if (!val) {
      return "--";
    }

    if (!format) format = "YYYY-MM-DD HH:mm:ss";
    return moment(val).format(format);
  });

  Vue.filter("formatStr", string => {
    if (string === "") return "--";
    if (!string) return "--";
    return string;
  });

  // 更多的三个点的效果
  Vue.directive("more", {
    bind: el => {
      el.className += "more-custom";
      el.innerHTML = `
    <i></i>
    <i></i>
    <i></i>`;
    }
  });
};
export {
  isDev,
  tableColumsFormatter,
  formRules,
  util,
  commonDefaultExport,
  api,
  enums,
  fetch,
  fetchExport,
  DataModel,
  task,
  utilDefaultExport,
  utilExport,
  engineeringInte
};
