<template>
  <div class="page eem-common">
    <el-container class="fullheight flex-column" style="padding-top: 32px">
      <div class="eem-container mtJ3 mbJ3">
        <CetButton
          class="fr custom—square"
          v-bind="CetButton_8"
          v-on="CetButton_8.event"
        ></CetButton>
        <div class="basic-box fr mlJ mrJ">
          <!-- <div class="basic-box-label" style="width: 50px">
            {{ status === 14 ? "月份" : "年份" }}
          </div> -->
          <div class="block" v-show="status !== 14">
            <!-- <el-date-picker
              class="w120"
              v-model="value3"
              type="year"
              placeholder="选择年份"
              @change="yearChange"
            ></el-date-picker> -->
            <CustomElDatePicker
              class="fr"
              style="width: 200px"
              :prefix_in="$T('选择年份')"
              v-model="value3"
              type="year"
              :clearable="false"
              :placeholder="$T('选择年份')"
              @change="yearChange"
            />
          </div>
          <div class="block" v-show="status === 14">
            <CustomElDatePicker
              class="fr"
              :prefix_in="$T('选择日期')"
              v-model="value1"
              style="width: 256px"
              type="monthrange"
              :clearable="false"
              :range-separator="$T('至')"
              @change="monthChange"
            />
            <!-- <el-date-picker
              class="w300"
              v-model="value1"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              @change="monthChange"
            ></el-date-picker> -->
          </div>
        </div>
        <CetButton
          class="fr custom—square"
          v-bind="CetButton_7"
          v-on="CetButton_7.event"
        ></CetButton>
        <div class="basic-box mlJ3 fr">
          <customElSelect
            class="fr mrJ1"
            v-model="ElSelect_1.value"
            v-bind="ElSelect_1"
            v-on="ElSelect_1.event"
            :prefix_in="$T('能源类型')"
          >
            <ElOption
              v-for="item in ElOption_1.options_in"
              :key="item[ElOption_1.key]"
              :label="item[ElOption_1.label]"
              :value="item[ElOption_1.value]"
              :disabled="item[ElOption_1.disabled]"
            ></ElOption>
          </customElSelect>
          <customElSelect
            class="fr mrJ1"
            v-model="ElSelect_2.value"
            v-bind="ElSelect_2"
            v-on="ElSelect_2.event"
            :prefix_in="$T('核算周期')"
          >
            <ElOption
              v-for="item in ElOption_2.options_in"
              :key="item[ElOption_2.key]"
              :label="item[ElOption_2.label]"
              :value="item[ElOption_2.value]"
              :disabled="item[ElOption_2.disabled]"
            ></ElOption>
          </customElSelect>
        </div>
      </div>
      <div class="minWH" style="flex: 1; overflow-y: auto">
        <div class="fullheight flex-column">
          <div class="minWH eem-container" style="flex: 1; min-height: 200px">
            <div class="text-middle text-ellipsis mbJ3">
              <el-tooltip :content="title" effect="light" placement="top">
                <span class="common-title-H2">{{ title }}</span>
              </el-tooltip>
            </div>
            <CetTable
              style="height: 76%"
              :data.sync="CetTable_1.data"
              :dynamicInput.sync="CetTable_1.dynamicInput"
              v-bind="CetTable_1"
              v-on="CetTable_1.event"
            >
              <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
              <ElTableColumn
                v-for="(item, index) in ElTableColumnArr"
                :key="index"
                v-bind="item"
              ></ElTableColumn>
            </CetTable>
          </div>
          <div class="count mtJ3 mbJ3">
            <span class="fsH3 text-middle">{{ $T("总成本合计") }}</span>
            <div class="right">
              <span class="fsH fcZS">{{ count }}</span>
              {{ this.unitTransition ? `(${$T("万元")})` : `(${$T("元")})` }}
            </div>
          </div>
          <div class="tip1 eem-container">
            <div class="fullheight flex-column">
              <div class="mbJ3 common-title-H2">{{ $T("核算统计") }}</div>
              <div class="minWH" style="flex: 1; min-height: 300px">
                <CetChart v-bind="CetChart_1"></CetChart>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-container>
  </div>
</template>
<script>
import { httping } from "@omega/http";
export default {
  name: "EneryCost",
  components: {},
  computed: {
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },

  data(vm) {
    const language = window.localStorage.getItem("omega_language") === "en";
    return {
      unitTransition: false, // 是否需要进行单位转换
      selectObj: {},
      count: 0,
      value3: vm.$moment().startOf("year").toDate(),
      value4: "",
      value5: "",
      status: 17,
      value1: [
        this.$moment().toDate(),
        this.$moment().add("month", 1).toDate()
      ],
      // 请填写组件含义组件
      ElSelect_1: {
        value: 13,
        style: { width: language ? "300px" : "200px" },
        event: {
          change: this.energyChange
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: 17,
        style: { width: language ? "300px" : "200px" },
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [
          { id: 17, text: $T("按年") },
          { id: 14, text: $T("自定义") }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetButton_7: {
        visible_in: true,
        disable_in: false,
        title: "",
        plain: true,
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_7_statusTrigger_out
        }
      },
      CetButton_8: {
        visible_in: true,
        disable_in: false,
        title: "",
        plain: true,
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_8_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      // index组件
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        //  prop: "",      // 支持path a[0].b
        label: $T("序号"), //列名
        headerAlign: "left",
        align: "lftt",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: language ? "100px" : "50px" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumnArr: [{}],
      CetChart_1: {
        //组件输入项
        inputData_in: null,
        options: {}
      }
    };
  },
  props: ["obj", "title", "energyList", "energySelect"],
  watch: {
    obj(val) {
      if (!val || !val.id) {
        this.CetTable_1.data = [];
        this.ElTableColumnArr = [{}];
        this.count = 0;
        this.getEcharts([], [], []);
        return;
      }
      const startTime = this.$moment().startOf("year").valueOf();
      const endTime = this.$moment().add(1, "year").startOf("year").valueOf();
      this.value3 = this.$moment().startOf("year").toDate();
      this.value1 = [
        this.$moment().toDate(),
        this.$moment().add("month", 1).toDate()
      ];
      const data = {
        cycle: 17, // 周期
        energyType: 13, // 能源类型
        endTime, //
        node: {
          id: val.id,
          modelLabel: val.modelLabel,
          name: val.name
        },
        startTime,
        projectId: val.projectId
      };

      this.selectObj = data;
      this.getList(data);
    },
    energyList(val) {
      this.ElOption_1.options_in = val;
    },
    energySelect(val) {
      this.ElSelect_1.value = val;
    }
  },

  methods: {
    queryTime_out() {},
    ElSelect_2_change_out(val) {
      this.status = val;
      if (this.status === 17) {
        this.yearChange(new Date(this.value3));
      } else if (this.status === 14) {
        this.monthChange([new Date(this.value1[0]), new Date(this.value1[1])]);
      }
    },
    // 千位加逗号
    formatNum(num) {
      if (num === 0) return 0;
      if (!num || _.isNaN(_.toNumber(num))) return "--";
      var floorStr = "";
      if ((num || 0).toString().split(".").length > 1) {
        floorStr = (num || 0).toString().split(".")[1];
      }
      var result = "";
      var counter = 0;
      num = (num || 0).toString().split(".")[0];
      for (var i = num.length - 1; i >= 0; i--) {
        counter++;
        result = num.charAt(i) + result;
        if (!(counter % 3) && i != 0) {
          result = "," + result;
        }
      }
      return result + "." + floorStr;
    },
    // 获取能源成本核算的列表
    getList(obj) {
      var _this = this;
      _this.unitTransition = false;
      httping({
        url: "eem-service/v1/costcaculating/energycostcheck",
        method: "post",
        data: obj
      }).then(res => {
        if (res.code == 0 && res.data) {
          const head = [
            {
              label: $T("核算周期"),
              prop: "cycle",
              width: 100
            }
          ];
          const list = [];
          const data = res.data.data;
          let count = 0;
          const source = [["product"]];
          const series = [];
          const nameList = [];
          const colorList = [
            { type: "bar" },
            { type: "bar" },
            { type: "bar" },
            { type: "bar" },
            { type: "bar" },
            { type: "bar" },
            { type: "bar" },
            { type: "bar" },
            { type: "bar" },
            { type: "bar" }
          ];
          // 先判断是否需要进行单位转换
          for (const item in data) {
            if (item != 0) {
              data[item].forEach(item1 => {
                if (item1.value && item1.value * 1 > 10000) {
                  this.unitTransition = true;
                }
              });
            }
          }

          res.data.header.map((item, index) => {
            const obj = {
              label: this.unitTransition
                ? item.name + `(${$T("万元")})`
                : item.name + `(${$T("元")})`,
              prop: "node" + index,
              minWidth: 100,
              showOverflowTooltip: true,
              headerAlign: "center",
              align: "center",
              formatter: function (val) {
                if (val["node" + index] || val["node" + index] === 0) {
                  return _this.formatNum(val["node" + index]);
                } else {
                  return "--";
                }
              }
            };
            if (index != res.data.header.length - 1) {
              source[0].push(item.name);
              series[index] = colorList[index];
            }
            head.push(obj);
            nameList.push({
              name: item.name
            });
          });
          for (const item in data) {
            if (item != 0) {
              // let time = new Date(item * 1).getMonth() + 1 + "月";
              let formatStr = this.language ? "YYYY-MM" : "YYYY年MM月";
              const time = this.$moment(item * 1).format(formatStr);
              const item2 = [time];
              const obj = {
                cycle: time
              };
              data[item].map((item1, i) => {
                if (this.unitTransition && (item1.value || item.value === 0)) {
                  item1.value = item1.value / 10000;
                }
                const name = "node" + i;
                if (item1.value != null) obj[name] = item1.value.toFixed2(2);
                if (i < data[item].length - 1) {
                  item2.push(
                    item1.value != null ? item1.value.toFixed2(2) : ""
                  );
                }
                if (i == data[item].length - 1) {
                  if (item1.value != null) {
                    count += item1.value * 1;
                  }
                }
              });
              list.push(obj);
              source.push(item2);
            }
          }
          this.ElTableColumnArr = head;
          this.CetTable_1.data = list;
          this.count = _this.formatNum(count.toFixed2(2));
          this.getEcharts(source, nameList, series);
        } else {
          this.CetTable_1.data = [];
          this.ElTableColumnArr = [{}];
          this.count = 0;
          this.getEcharts([], [], []);
        }
      });
    },
    getEcharts(source, nameList, series) {
      this.CetChart_1.options = {};
      this.CetChart_1.options = {
        legend: { bottom: true, icon: "circle", data: nameList },
        tooltip: {},
        dataset: {
          source
        },
        xAxis: {
          type: "category"
        },
        yAxis: {
          name: this.unitTransition ? $T("成本（万元）") : $T("成本（元）")
        },
        series
      };
    },
    energyChange(val) {
      const selectObj = this.selectObj;
      selectObj.energyType = val;
      this.getList(selectObj);
    },
    yearChange(val) {
      let time = val.getTime();
      time = new Date(time * 1).getFullYear();
      const time1 = time + "-1-1";
      const time2 = time * 1 + 1 + "-1-1";
      const startTime = new Date(time1).getTime();
      const endTime = new Date(time2).getTime();
      const selectObj = this.selectObj;
      selectObj.startTime = startTime;
      selectObj.endTime = endTime;
      selectObj.cycle = 17;
      this.getList(selectObj);
    },
    monthChange(val) {
      const startTime = this.$moment(val[0]).startOf("month").valueOf();
      const endTime = this.$moment(val[1]).endOf("month").valueOf() + 1;
      const selectObj = this.selectObj;
      selectObj.startTime = startTime;
      selectObj.endTime = endTime;
      selectObj.cycle = 17;
      this.getList(selectObj);
    },
    // 向前按钮
    CetButton_7_statusTrigger_out() {
      if (this.status === 17) {
        const selectObj = this.selectObj;
        let time = this.value3;
        if (time > 1000000000) {
          time = new Date(time * 1).getFullYear();
        }
        time--;
        this.value3 = time.toString();
        const time1 = time + "-1-1";
        const time2 = time * 1 + 1 + "-1-1";
        const startTime = new Date(time1).getTime();
        const endTime = new Date(time2).getTime();
        selectObj.startTime = startTime;
        selectObj.endTime = endTime;
        this.selectObj.cycle = 17;
        this.selectObj = selectObj;
        this.getList(selectObj);
      } else if (this.status == 14) {
        var startTime = this.$moment(this.value1[0])
          .add("month", -1)
          .startOf("month")
          .valueOf();
        var endTime = this.$moment(this.value1[1])
          .add("month", -1)
          .endOf("month")
          .valueOf();
        this.value1 = [new Date(startTime), new Date(endTime)];
        this.selectObj.startTime = startTime;
        this.selectObj.endTime = endTime + 1;
        this.getList(this.selectObj);
      }
    },
    // 向后按钮
    CetButton_8_statusTrigger_out() {
      if (this.status === 17) {
        const selectObj = this.selectObj;
        let time = this.value3;
        if (time > 1000000000) {
          time = new Date(time * 1).getFullYear();
        }
        time++;
        this.value3 = time.toString();
        const time1 = time + "-1-1";
        const time2 = time * 1 + 1 + "-1-1";
        const startTime = new Date(time1).getTime();
        const endTime = new Date(time2).getTime();
        selectObj.startTime = startTime;
        selectObj.endTime = endTime;
        this.selectObj = selectObj;
        this.getList(selectObj);
      } else if (this.status == 14) {
        var startTime = this.$moment(this.value1[0])
          .add("month", 1)
          .startOf("month")
          .valueOf();
        var endTime = this.$moment(this.value1[1])
          .add("month", 1)
          .endOf("month")
          .valueOf();
        this.value1 = [new Date(startTime), new Date(endTime)];
        this.selectObj.startTime = startTime;
        this.selectObj.endTime = endTime + 1;
        this.getList(this.selectObj);
      }
    },
    CetTable_1_record_out(val) {},
    CetTable_1_outputData_out(val) {}
  },
  created: function () {},
  mounted() {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.top {
  width: 750px;
  display: flex;
  align-items: center;
}
.tip1 {
  height: 400px;
  position: relative;
}
.count {
  padding-left: 300px;
  line-height: 28px;
  border-top: none;
}
.count .right {
  float: right;
  margin-right: 100px;
}
#main {
  :deep(& > div) {
    overflow: hidden;
  }
}
</style>
