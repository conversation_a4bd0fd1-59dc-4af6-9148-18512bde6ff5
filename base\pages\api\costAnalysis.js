import fetch from "eem-utils/fetch";
const version = "v1";

// 单位成本分析趋势
export function queryUnitObjectCostTrend(data) {
  return fetch({
    url: `/eem-service/${version}/unitObjectCost/trend`,
    method: "POST",
    data
  });
}

// 单位成本概览
export function queryUnitObjectCostView(data) {
  return fetch({
    url: `/eem-service/${version}/unitObjectCost/view`,
    method: "POST",
    data
  });
}

// 单位成本占比
export function queryUnitObjectCostPercent(data) {
  return fetch({
    url: `/eem-service/${version}/unitObjectCost/percent`,
    method: "POST",
    data
  });
}

// 用电分析过滤后的节点树
export function queryElectricityTree(data) {
  return fetch({
    url: `/eem-service/${version}/electricityCostValue/nodeTree`,
    method: "POST",
    data
  });
}

// 分时方案列表
export function queryTimeShareList(data) {
  return fetch({
    url: `/eem-service/${version}/electricityCostValue/timeShareList`,
    method: "POST",
    data
  });
}

// 用电成本和平均电价趋势
export function queryElectricityCostValueTrend(data) {
  return fetch({
    url: `/eem-service/${version}/electricityCostValue/trend`,
    method: "POST",
    data
  });
}

// 平均电价分析
export function queryAverageElectricityPrice(data) {
  return fetch({
    url: `/eem-service/${version}/electricityCostValue/averageElectricityPrice`,
    method: "POST",
    data
  });
}

// 分时用电成本
export function queryTsObjectCost(data) {
  return fetch({
    url: `/eem-service/${version}/electricityCostValue/tsObjectCost`,
    method: "POST",
    data
  });
}

// 综合成本趋势
export function queryComprehensiveTrend(data) {
  return fetch({
    url: `/eem-service/${version}/costanalysis/costTotalValue`,
    method: "POST",
    data
  });
}

// 综合成本概览
export function queryComprehensiveCost(data) {
  return fetch({
    url: `/eem-service/${version}/costanalysis/costByNode`,
    method: "POST",
    data
  });
}

// 分类成本占比
export function queryEnergyCostValue(data) {
  return fetch({
    url: `/eem-service/${version}/costanalysis/costValue`,
    method: "POST",
    data
  });
}

// 核算统计
export function queryCostCalc(data) {
  return fetch({
    url: `/eem-service/${version}/costaccount/accountValue`,
    method: "POST",
    data
  });
}

// 能源成本核算
export function queryEnergycostcheck(data) {
  return fetch({
    url: `/eem-service/${version}/costcaculating/energycostcheckbasic`,
    method: "POST",
    data
  });
}

// 查询所有能源类型、费用类型、费率类型
export function queryEnergySchemeConfig(data) {
  return fetch({
    url: `/eem-service/${version}/schemeConfig/energys`,
    method: "POST",
    data
  });
}

// 综合成本预测
export function queryComprehensiveForecast(data) {
  return fetch({
    url: `/eem-service/${version}/costanalysis/costforecast`,
    method: "POST",
    data
  });
}

// 节点关联的核算方案
export function queryCostcheckplan(data) {
  return fetch({
    url: `/eem-service/${version}/costanalysis/queryCostcheckplan`,
    method: "POST",
    data
  });
}
