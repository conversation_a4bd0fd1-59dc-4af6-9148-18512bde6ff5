<template>
  <!-- 1弹窗组件 -->
  <div>
    <CetDialog
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
      class="CetDialog small"
    >
      <span slot="footer">
        <span class="fl mlJ1">
          {{ $T("最多选中{0}个节点", this.maxRelevanceNodeNumber) }}
        </span>
        <!-- 设置组件唯一识别字段按钮组件 -->
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
      <div class="eem-cont-c1">
        <el-checkbox v-model="checked" @change="checkedChange" class="mbJ1">
          {{ $T("默认选中子节点") }}
        </el-checkbox>
        <CetGiantTree
          ref="giantTree1"
          style="height: 300px"
          v-show="!checked"
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
        ></CetGiantTree>
        <CetGiantTree
          ref="giantTree2"
          style="height: 300px"
          v-show="checked"
          v-bind="CetGiantTree_2"
          v-on="CetGiantTree_2.event"
        ></CetGiantTree>
        <div class="tagBox">
          <el-tag
            :key="index"
            v-for="(tag, index) in treeCheckedNodes"
            :closable="!checked"
            :disable-transitions="false"
            @close="handleClose(index)"
          >
            {{ tag.name }}
          </el-tag>
        </div>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import commonApi from "@/api/custom.js";
import TREE_PARAMS from "@/store/treeParams.js";
export default {
  name: "Relevance",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Array
    }
  },

  computed: {
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data() {
    return {
      // 最大关联节点树
      maxRelevanceNodeNumber: 200,
      // 选择框是否点击
      checked: false,
      CetDialog_1: {
        title: $T("关联节点"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      treeCheckedNodes: [],
      // 1组件
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out //勾选节点输出
        }
      },
      // 2组件
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "ps", N: "ps" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_2_checkedNodes_out //勾选节点输出
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    async visibleTrigger_in(val) {
      var vm = this;
      this.treeCheckedNodes = [];
      this.checked = false;
      vm.CetDialog_1.openTrigger_in = val;
      await vm.getTreeData();
      if (vm.inputData_in && vm.inputData_in.length === 1) {
        // 单条管理需要先查已关联节点
        vm.getAlreadyRelevance();
      } else {
        this.CetGiantTree_1.checkedNodes = [];
        this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
        this.CetGiantTree_2.checkedNodes = [];
        this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
        this.treeCheckedNodes = [];
      }
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      this.Add_relevance();
    },
    CetGiantTree_1_checkedNodes_out(val) {
      if (!this.checked) {
        this.treeCheckedNodes = this._.cloneDeep(val);
      }
    },
    CetGiantTree_2_checkedNodes_out(val) {
      if (this.checked) {
        this.treeCheckedNodes = this._.cloneDeep(val);
      }
    },
    handleClose(index) {
      this.treeCheckedNodes.splice(index, 1);
      this.CetGiantTree_1.checkedNodes = this.treeCheckedNodes;
      // this.CetGiantTree_2.checkedNodes = this.treeCheckedNodes;
      if (!this.treeCheckedNodes.length) {
        this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
        this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
      }
    },
    getAlreadyRelevance() {
      var _this = this;
      var id = _this._.get(_this.inputData_in, "[0].id");
      if (!id) {
        return;
      }
      commonApi
        .getEfSetIdByRelevance({
          efSetId: id
        })
        .then(response => {
          if (response.code === 0) {
            var data = _this._.get(response, "data", []);
            let checkedNodes = data.map(item => {
              return {
                id: item.objectid,
                modelLabel: item.objectlabel,
                tree_id: `${item.objectlabel}_${item.objectid}`
              };
            });
            if (checkedNodes.length) {
              _this.CetGiantTree_1.checkedNodes = checkedNodes;
              // _this.CetGiantTree_2.checkedNodes = checkedNodes;
              _this.expandNode(
                checkedNodes,
                "tree_id",
                _this.$refs.giantTree1.ztreeObj
              );
              _this.expandNode(
                checkedNodes,
                "tree_id",
                _this.$refs.giantTree2.ztreeObj
              );
            } else {
              _this.CetGiantTree_1.checkedNodes = [];
              _this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
              _this.CetGiantTree_2.checkedNodes = [];
              _this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
              _this.treeCheckedNodes = [];
            }
            _this.CetGiantTree_1.inputData_in = this._.cloneDeep(
              _this.CetGiantTree_1.inputData_in
            );
            // _this.CetGiantTree_2.inputData_in = this._.cloneDeep(
            //   _this.CetGiantTree_2.inputData_in
            // );
          }
        });
    },
    // 展开节点
    expandNode(nodes, key, ztreeObj) {
      setTimeout(() => {
        nodes.forEach(item => {
          let node = ztreeObj.getNodeByParam(key, item[key]);
          let parentNodes = [],
            parentNode = node && node.getParentNode();
          while (parentNode) {
            parentNodes.push(parentNode);
            parentNode = parentNode.getParentNode();
          }
          parentNodes.forEach(i => {
            ztreeObj.expandNode(i, true);
          });
        });
        $(this.$refs.giantTree1.$el).find(".ztree").scrollTop(0);
        $(this.$refs.giantTree2.$el).find(".ztree").scrollTop(0);
      }, 0);
    },
    Add_relevance() {
      var _this = this;
      var params = [];
      var customApiFn;
      let efSetIds = [];
      // if (_this.inputData_in && _this.inputData_in.length === 1) {
      //   params = {
      //     efSetId: _this.inputData_in[0].id,
      //     nodes: []
      //   };
      //   customApiFn = commonApi.addEfSetByNodeRelevanceAll;
      //   _this.treeCheckedNodes.forEach(item => {
      //     params.nodes.push({
      //       id: item.id,
      //       modelLabel: item.modelLabel
      //     });
      //   });
      // } else {
      //   params = [];
      //   customApiFn = commonApi.addEfSetByNodeRelevanceIncrement;
      //   _this.treeCheckedNodes.forEach(item => {
      //     _this.inputData_in.forEach(item2 => {
      //       params.push({
      //         energyefficiencyset_id: item2.id,
      //         objectid: item.id,
      //         objectlabel: item.modelLabel
      //       });
      //     });
      //   });
      // }
      _this.inputData_in.forEach(item => {
        item.mergeDisplayAdds.forEach(key => {
          efSetIds.push(key.efSetId);
        });
      });
      params = {
        efSetIds: efSetIds,
        nodes: []
      };
      if (_this.treeCheckedNodes.length > this.maxRelevanceNodeNumber) {
        return this.$message.error(
          $T("最多选中{0}个节点", this.maxRelevanceNodeNumber)
        );
      }
      customApiFn = commonApi.addEfSetByNodeRelevanceAll;
      _this.treeCheckedNodes.forEach(item => {
        params.nodes.push({
          id: item.id,
          modelLabel: item.modelLabel
        });
      });
      customApiFn(params).then(response => {
        if (response.code === 0) {
          _this.$message({
            type: "success",
            message: $T("保存成功！")
          });
          _this.CetDialog_1.closeTrigger_in = new Date().getTime();
        }
      });
    },
    async getTreeData() {
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.powerEquipment,
        treeReturnEnable: true
      };
      await commonApi.getNodeTree(data).then(res => {
        if (res.code === 0) {
          var treeData = this._.get(res, "data", []);
          _this.CetGiantTree_1.inputData_in = treeData;
          _this.CetGiantTree_2.inputData_in = treeData;
        }
      });
    },
    // 选择框的改变
    checkedChange() {
      const vm = this;
      let checkNodes = vm._.cloneDeep(vm.treeCheckedNodes);
      setTimeout(() => {
        $(this.$refs.giantTree1.$el).find(".ztree").scrollTop(0);
        $(this.$refs.giantTree2.$el).find(".ztree").scrollTop(0);
        if (vm.checked) {
          vm.CetGiantTree_2.checkedNodes = checkNodes;
          vm.expandNode(checkNodes, "tree_id", vm.$refs.giantTree2.ztreeObj);
          if (!checkNodes.length) {
            vm.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
          }
        } else {
          vm.CetGiantTree_1.checkedNodes = checkNodes;
          vm.expandNode(checkNodes, "tree_id", vm.$refs.giantTree1.ztreeObj);
          if (!checkNodes.length) {
            vm.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
          }
        }
      }, 0);
    }
  }
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
    box-sizing: border-box;
  }
  .tagBox {
    height: 100px;
    overflow: auto;
    border-top: 1px solid;
    border-bottom: 1px solid;
    @include border_color(B1);
    @include padding(J3 0);
    :deep(.el-tag) {
      @include margin_right(J1);
      @include margin_top(J1);
    }
  }
}
</style>
