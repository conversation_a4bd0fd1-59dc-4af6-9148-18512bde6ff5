<template>
  <div class="page">
    <el-container class="content">
      <el-main style="height: 100%; padding: 0px" class="bgcolor marginLeftJ2">
        <el-header height="58px" style="line-height: 58px" class="headerBox">
          <el-row>
            <el-col :span="20" class="box-col">
              <div class="el-form-item__label" style="line-height: inherit">
                <!-- 关键字： -->
                <ElInput
                  v-model="ElInput_1.value"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                  class="searchInput"
                ></ElInput>
              </div>

              <div class="el-form-item__label" style="line-height: inherit">
                <el-cascader
                  class="cascader"
                  clearable
                  v-model="address"
                  :props="treeProps"
                  :options="areaOptions"
                  @change="handleChange"
                ></el-cascader>
              </div>
            </el-col>
          </el-row>
        </el-header>
        <el-header class="numbox">
          <div
            v-for="(item, index) in numlist"
            :key="index"
            class="numone radiusBox"
          >
            <div>
              <P>{{ item.num }}</P>
              <div style="font-size: 14px">{{ item.name }}</div>
            </div>
            <img :src="item.icon" alt="" />
          </div>
        </el-header>

        <el-main class="tabBox radiusBox">
          <div class="mb5">
            共
            <span class="totalCount">{{ totalCount }}</span>
            条
          </div>
          <CetTable
            style="height: calc(100% - 25px)"
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
            v-if="!isdetail"
          >
            <el-table-column
              label="#"
              width="42"
              type="index"
            ></el-table-column>

            <!-- 根据模型数据绑定表格列 -->
            <template v-for="item in columnsArr">
              <template>
                <el-table-column
                  :key="item.key"
                  :show-overflow-tooltip="true"
                  :label="item.title"
                  :min-width="item.width"
                  :width="item.fixedwidth"
                  :prop="item.key"
                  :sortable="item.sortable"
                  :formatter="formatTable"
                  header-align="center"
                  align="center"
                ></el-table-column>
              </template>
            </template>
            <el-table-column label="操作" width="100">
              <template slot-scope="scope">
                <span style="font-size: 20px" @click="Todetail">...</span>
              </template>
            </el-table-column>
          </CetTable>
          <CetTable
            style="height: calc(100% - 25px)"
            @sort-change="sortChange"
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
            v-if="isdetail"
          >
            <el-table-column
              label="#"
              width="42"
              type="index"
            ></el-table-column>

            <!-- 根据模型数据绑定表格列 -->
            <template v-for="item in columnsArrdetail">
              <template>
                <el-table-column
                  :key="item.key"
                  :show-overflow-tooltip="true"
                  :label="item.title"
                  :min-width="item.width"
                  :width="item.fixedwidth"
                  :prop="item.key"
                  :sortable="item.sortable"
                  :formatter="formatTable"
                  header-align="center"
                  align="center"
                ></el-table-column>
              </template>
            </template>
          </CetTable>
        </el-main>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import img1 from "./assets/tuandui.png";
import img2 from "./assets/renwu.png";
import img3 from "./assets/jiaoxue.png";
import customApi from "@/api/custom";

export default {
  props: {},
  computed: {},
  data() {
    return {
      drawer: false,
      address: [],
      treeProps: {
        expandTrigger: "hover",
        children: "children",
        label: "name",
        value: "name"
      },
      areaOptions: [],
      isdetail: false,
      totalCount: 0,
      numlist: [
        { name: "用户数量（个）", num: 980, icon: img1 },
        { name: "报装容量（KVA）", num: 980, icon: img2 },
        { name: "最大响应量（KW）", num: 980, icon: img3 }
      ],

      ElInput_search: {
        value: "",

        style: {},
        event: {}
      },

      ElInput_1: {
        value: "",
        placeholder: "请输入",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElInput_1_change_out,
          input: this.ElInput_1_input_out
        }
      },
      ElSelect_type: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_type_change_out
        }
      },
      ElOption_type: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [
          {
            operationtime: "aa",
            operationtype: "aa",
            operator: "aa",
            description: "aa",
            operator1: "aa",
            operator2: "aa",
            operator3: "aa",

            operator4: "aa"
          },
          {
            operationtime: "aa",
            operationtype: "aa",
            operator: "aa",
            description: "aa",
            operator1: "aa",
            operator2: "aa",
            operator3: "aa",

            operator4: "aa"
          }
        ],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {
          pageSize: 20,
          textAlign: "center",
          layout: "sizes, prev, pager, next, jumper"
        },

        exportFileName: "",
        defaultSort: { prop: "operationtime", order: "descending" },
        event: {
          totalCount_out: this.CetTable_1_totalCount_out
        }
      },
      columnsArr: [
        {
          title: "园区名称",
          key: "operationtime",
          // type: "date",
          // sortable: "custom",
          width: 100
        },
        {
          title: "响应区域",
          key: "address",
          width: 100,
          formatter: function (val) {
            if (val.address) {
              return val.address.split("-")[0];
            } else {
              return "--";
            }
          }
        },
        {
          title: "报装容量",
          key: "operator",
          width: 100
        },
        {
          title: "最大响应量",
          key: "description",
          width: 100
        },
        {
          title: "电压等级",
          key: "operator1",
          width: 100
        },
        {
          title: "用户地址",
          key: "operator2",
          width: 100
        },
        {
          title: "联系人",
          key: "operator3",
          width: 100
        },
        {
          title: "联系方式",
          key: "operator4",
          width: 100
        }
      ],
      columnsArrdetail: [
        {
          title: "资源名称",
          key: "operationtime",
          width: 100
        },
        {
          title: "报装容量",
          key: "operator",
          width: 100
        },
        {
          title: "最大响应量",
          key: "description",
          width: 100
        },
        {
          title: "电压等级",
          key: "operator1",
          width: 100
        }
      ]
    };
  },
  watch: {},
  methods: {
    CetTable_1_totalCount_out(val) {
      console.log(val);
      this.totalCount = val;
    },
    init() {
      this.sum = 0;
      // this.CetTable_1.data = [];
      this.ElInput_search = "";
      this.ElSelect_1.value = "";
      // 清除列表勾选
      this.$refs.CetTable.$refs.cetTable.clearSelection();
    },
    tabsClick() {
      this.showDetail = !this.showDetail;
      this.init();
    },
    Todetail() {
      // this.isdetail = true;
      this.drawer = true;
    },
    handleChange() {
      // this.poweruserQuery();
    },
    getDistrictTree() {
      customApi.getDistrictTree().then(response => {
        this.areaOptions = this._.get(response, "data", []);
        console.log(this.areaOptions);
      });
    },
    gettableData() {
      let data = {
        isSelfOperate: this.isSelfOperate,
        sellingId: this.$store.state.sellCompanyId
      };
      customApi.invitationDisplayquery(data).then(response => {
        this.CetTable_1.data = this._.get(response, "data", []);
        console.log(this.CetTable_1.data);
      });
    },
    getTreeData() {
      this.CetTree_1.inputData_in = [
        {
          id: 1,
          name: "园区虚拟电厂1",
          tree_id: "1",

          children: [
            {
              id: 1,
              name: "园区1",
              tree_id: "1",
              children: [
                {
                  id: 1,
                  name: "进线1",
                  tree_id: "1"
                },
                {
                  id: 2,
                  name: "进线2",
                  tree_id: "2"
                }
              ]
            },
            {
              id: 2,
              name: "园区2",
              tree_id: "2"
            }
          ]
        },
        {
          id: 2,
          name: "园区虚拟电厂2",
          tree_id: "2"
        }
      ];
    }
  },
  created() {
    this.getDistrictTree();
  },
  mounted() {
    this.getTreeData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;

  margin: 0 !important;
}
.searchInput {
  :deep(.el-input__inner) {
    padding-right: 50px;
  }
}
.content {
  width: 100%;
  height: 100%;
  // margin: 0 20px;
  .aside {
    background: #eeeef1;
    height: calc(100% - 16px);
    @include margin_top("J2");

    border-top-left-radius: mh-get(C3);
    border-bottom-left-radius: mh-get(C3);
  }
  :deep(.el-tree) {
    background-color: #eeeef1;
  }
  .numbox {
    height: 110px !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
    .numone {
      height: 98px;
      width: 33%;
      display: flex;
      justify-content: space-around;
      align-items: center;

      color: #3e424e;
      @include background_color("BG1");
      p {
        height: 40px;
        @include font_size("H");
        margin: 0;
      }
      img {
        height: 24px;
        width: 24px;
      }
    }
  }
}
.el-header .device-Input,
.el-header .device-Select {
  display: inline-block;
}

.device-DatePicker,
.device-Select {
  display: inline-block;
}

.device-Button {
  display: inline-block;
  margin-left: 40px;
}

.box-elfooter {
  // height:32px;
  // line-height: 60px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  @include background_color("BG1");
}

.headerBox {
  padding: 0;
  @include padding_left(J1);
  @include padding_right(J1);
}
.tabBox {
  @include padding(J2);
  @include background_color(BG1);
  height: calc(100% - 168px);
}
.radiusBox {
  @include border_radius(C3);
}
.contentBox {
  @include border_radius(C3);
  height: calc(100% - 58px);
}
.totalCount {
  @include font_color(ZS);
}
:deep(.el-footer) {
  text-align: right;
}
</style>
