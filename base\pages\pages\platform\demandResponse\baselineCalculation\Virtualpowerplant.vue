<template>
  <div class="pagebox basebox">
    <el-container class="contentBox">
      <div style="width: 316px" class="searchbox flex-column">
        <el-row>
          <el-col :span="24">
            <customElSelect
              class="
                customElSelect
                marginBottomJ1
                marginRightJ2
                fl
                marginLeftJ2
              "
              v-model="ElSelect_3.value"
              v-bind="ElSelect_3"
              v-on="ElSelect_3.event"
              prefix_in="节点类型"
              placeholder="请筛选"
              @change="handelclick"
            >
              <ElOption
                v-for="item in ElOption_3.options_in"
                :key="item[ElOption_3.key]"
                :label="item[ElOption_3.label]"
                :value="item[ElOption_3.value]"
                :disabled="item[ElOption_3.disabled]"
              ></ElOption>
            </customElSelect>
            <!-- </el-col>
          <el-col :span="3"> -->
            <!-- <img src="./assets/edit.jpg" @click="toChecked" class="handel" /> -->
            <!-- <el-popover
              placement="bottom-start"
              width="200"
              trigger="hover"
              popper-class="my-popover fl"
            >
              <div class="contentbox">
                <p>同时勾选父节点和子节点时默认获取子节点计算。</p>
              </div>
              <span
                class="el-icon-info"
                style="font-size: 18px; color: #d7dbe5"
                slot="reference"
              ></span>
            </el-popover> -->
          </el-col>
          <el-col :span="24">
            <customElSelect
              class="customElSelect marginLeftJ2 mrJ2"
              v-model="ElSelect_2.value"
              v-bind="ElSelect_2"
              v-on="ElSelect_2.event"
              prefix_in="资源类型"
              placeholder="请筛选"
              clearable
              :disabled="ElSelect_3.value !== 'responseresource'"
              @change="handelclick"
            >
              <ElOption
                v-for="item in ElOption_2.options_in"
                :key="item[ElOption_2.key]"
                :label="item[ElOption_2.label]"
                :value="item[ElOption_2.value]"
                :disabled="item[ElOption_2.disabled]"
              ></ElOption>
            </customElSelect>
          </el-col>
        </el-row>
        <el-aside width="316px" class="aside flex-auto mbJ2">
          <CetTree
            :selectNode.sync="CetTree_1.selectNode"
            :checkedNodes.sync="CetTree_1.checkedNodes"
            :searchText_in.sync="CetTree_1.searchText_in"
            v-bind="CetTree_1"
            v-on="CetTree_1.event"
            show-checkbox
            default-expand-all
            @node-click="treeclick"
            ref="tree"
          ></CetTree>
        </el-aside>
      </div>
      <el-main
        style="height: 100%; padding: 0px"
        class="marginLeftJ2 flex-column"
      >
        <el-row>
          <el-col :span="24" class="box-col">
            <div class="basic-box fl">
              <span class="basic-box-label">目标日期</span>
              <el-date-picker
                v-model="sameDay"
                value-format="yyyy-MM-dd 00:00:00"
                style="width: 200px"
                type="date"
                size="small"
                class="datePicker fl marginRightJ1"
                :picker-options="CetDatePicker_1.config.pickerOptions"
                placeholder="请筛选"
                :disabled="defaultdisabled"
                @change="defaultchange"
              ></el-date-picker>
            </div>
            <div class="basic-box fl">
              <span class="basic-box-label">基线参考日</span>
              <el-date-picker
                v-model="searchdate"
                value-format="yyyy-MM-dd 00:00:00"
                style="width: 200px"
                :picker-options="CetDatePicker_1.config.pickerOptions"
                type="dates"
                size="small"
                class="datePicker datePickertwo fl marginRightJ1"
                :disabled="basedisabled"
                placeholder="请筛选"
                @change="datechange"
              ></el-date-picker>
            </div>
            <!-- <div
              class="el-form-item__label clearfix"
              style="line-height: inherit; padding-right: 8px"
            >
              <div class="basic-box">
                <el-time-picker
                  class="fl datePickerrange"
                  style="width: 340px; border-radius: 8px; height: 34px"
                  v-model="CetDatePicker_1.val"
                  value-format="HH:00:00"
                  format="HH:00:00"
                  is-range
                  size="small"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  @change="timechange"
                  popper-class="tpcrange"
                ></el-time-picker> -->
            <div class="basic-box fl">
              <span class="basic-box-label">起始时间</span>
              <el-time-select
                class="timeSelect marginRightJ1 fl"
                placeholder="起始时间"
                v-model="CetDatePicker_1.val[0]"
                :picker-options="{
                  start: '00:00',
                  step: '00:60',
                  end: '24:00'
                }"
              ></el-time-select>
            </div>
            <div class="basic-box fl">
              <span class="basic-box-label">结束时间</span>
              <el-time-select
                class="timeSelect fl"
                placeholder="结束时间"
                v-model="CetDatePicker_1.val[1]"
                :picker-options="{
                  start: '00:00',
                  step: '00:60',
                  end: '24:00',
                  minTime: CetDatePicker_1.val[0]
                }"
              ></el-time-select>
            </div>
            <!-- </div>
            </div> -->
            <el-button
              type="primary"
              class="marginLeftJ1 fl"
              @click="getchartData"
              :disabled="
                (!searchdate && !sameDay) ||
                !CetDatePicker_1.val[0] ||
                !CetDatePicker_1.val[1]
              "
            >
              计算
            </el-button>
          </el-col>
        </el-row>

        <div style="height: 180px" class="countbox">
          <div class="leftcount">
            <h5>
              {{ isBaseDate ? "基线参考日（自定义）" : "基线参考日（默认）" }}

              <el-tooltip placement="bottom-start">
                <div slot="content">
                  <p>
                    选择目标日期时，采用系统默认基线参考日选取和剔除规则进行计算；
                  </p>
                  <p>选择基线参考日时，基线负荷根据自定义所选参考日计算。</p>
                </div>
                <span class="el-icon-info" style="font-size: 18px"></span>
              </el-tooltip>
            </h5>
            <div class="datebox" v-if="!isBaseDate">
              <p v-for="(item, index) in defaultdate" :key="index">
                {{ item }}
              </p>
            </div>

            <div class="datebox" v-if="isBaseDate">
              <p v-for="(item, index) in datelist" :key="index">
                {{ item }}
              </p>
            </div>
          </div>
          <div class="rightcount">
            <h5>
              {{
                Number(avaeragedata)
                  ? Math.floor(avaeragedata * 100) / 100
                  : "--"
              }}
            </h5>

            <p>基线平均负荷(kW)</p>
          </div>
        </div>

        <div class="tableBody flex-auto">
          <div class="empty" v-if="chartdata.length === 0">
            <img src="/static/assets/empty.svg" class="imgbox" alt="" />
          </div>
          <CetChart
            v-if="chartdata.length !== 0"
            :inputData_in="CetChart_1.inputData_in"
            v-bind="CetChart_1.config"
          />
        </div>
      </el-main>
    </el-container>
    <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
      <div style="height: 360px">
        <el-checkbox v-model="checked" style="margin-top: 16px">
          选中子层级节点
        </el-checkbox>
      </div>

      <span slot="footer">
        <el-button @click="CetDialog_1.closeTrigger_in = new Date().getTime()">
          取消
        </el-button>
        <el-button @click="checkedBtn" type="primary">确定</el-button>
      </span>
    </CetDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import moment from "moment";
export default {
  props: {},
  computed: {},
  data() {
    return {
      avaeragedata: "",
      defaultdate: [],
      checked: true,
      checkdata: {},
      isBaseDate: false,
      datelist: [],
      datedata: [],
      chartdata: [],
      sameDay: "",
      searchdate: "",
      num: 2,
      isborderbottom: false,
      activeName: "负荷聚合商",
      sum: 0,
      showDetail: false,
      CetDialog_1: {
        title: "设置",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        width: "352px",
        showClose: true
      },
      // 1树组件
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        checkStrictly: false,
        nodeKey: "tree_id",
        props: {
          label: function (data, node) {
            let title = data.name ? data.name : "";
            let num = data.accountno ? data.accountno : " ";
            let meterno = data.meterno ? data.meterno : " ";
            if (data.modelLabel === "demandgroup") {
              return title + num;
            } else if (data.modelLabel === "demandaccount") {
              return title + meterno;
            } else {
              return data.name;
            }
          },
          children: "children"
        },
        highlightCurrent: true,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      ElInput_search: {
        value: "",

        style: {},
        event: {}
      },

      ElSelect_1: {
        value: "",
        style: {
          width: "200px"
        },
        event: {}
      },
      ElOption_1: {
        options_in: [
          {
            id: 1,
            name: "类型1"
          },
          {
            id: 2,
            name: "类型2"
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: "",
        style: {
          width: "200px"
        },
        event: {}
      },
      ElOption_2: {
        options_in: this.$store.state.enumerations.responseresourcetype || [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_3: {
        value: "responseresource",
        style: {
          width: "200px"
        },
        event: {}
      },
      ElOption_3: {
        options_in: [
          { id: "demandgroup", text: "户号" },
          { id: "demandaccount", text: "进线" },
          { id: "responseresource", text: "资源" }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetDatePicker_1: {
        disable_in: false,
        val: ["00:00", "24:00"],
        config: {
          valueFormat: "timestamp",
          rangeSeparator: "至",
          clearable: false,
          size: "small",
          pickerOptions: {}
        }
      },
      CetChart_1: {
        inputData_in: {},
        config: {
          options: {
            legend: {
              data: [],
              itemGap: 20,
              selected: {},
              right: 30,
              top: 0,
              // icon: "circle",
              textStyle: {
                color: "#9CA2B1"
              }
            },
            grid: {
              top: 80,
              left: 70,
              right: 70
            },
            tooltip: {
              extraCssText:
                'background: #0F2557; border-radius: 12px;color: #fff; opacity: 0.8; box-shadow: "0px 0px 4px 0px rgba(62, 66, 78, 0.22)";border:none;text-align:left;',
              trigger: "axis",
              axisPointer: {
                type: "shadow"
              },
              textStyle: {
                color: "#FFF",
                fontsize: "10px"
              },
              formatter: function (params) {
                let str = params[0].name + "<br>";
                let arr = [];
                params.forEach((item, index) => {
                  arr.push(item.seriesName);
                });
                arr
                  .sort()
                  .reverse()
                  .forEach((item, index) => {
                    params.forEach(code => {
                      if (code.seriesName === item) {
                        arr[index] = code;
                        str +=
                          code.marker +
                          code.seriesName +
                          "(kW):" +
                          code.value.toFixed(2) +
                          "<br>";
                      }
                    });
                  });
                return str;
              }
            },
            xAxis: {
              type: "category",
              // boundaryGap: false,
              name: "时间",
              axisLabel: {
                //---坐标轴 标签
                show: true, //---是否显示
                inside: false, //---是否朝内
                rotate: 0, //---旋转角度
                margin: 5, //---刻度标签与轴线之间的距离
                color: "#9CA2B1" //---默认取轴线的颜色
              },
              data: []
            },
            yAxis: {
              type: "value",
              name: "功率（kW）",
              scale: true,
              axisLabel: {
                //---坐标轴 标签
                show: true, //---是否显示
                inside: false, //---是否朝内
                rotate: 0, //---旋转角度
                margin: 5, //---刻度标签与轴线之间的距离
                color: "#9CA2B1" //---默认取轴线的颜色
              }
            },
            series: []
          }
        }
      },
      basedisabled: false,
      defaultdisabled: false
    };
  },
  watch: {
    // checked: function (val) {
    //   this.CetTree_1.checkStrictly = !val;
    // }
    "CetTree_1.searchText_in": {
      handler(val) {
        if (val) {
          this.CetTree_1.checkedNodes = [];
        }
        console.log(val);
      },
      immediate: true
    }
  },
  methods: {
    init() {
      this.sum = 0;
      this.ElInput_search.value = "";
      this.ElSelect_1.value = "";
    },
    handelclick() {
      this.CetTree_1.checkedNodes = [];
      this.getTreeData();
    },
    checkedBtn() {
      this.CetTree_1.checkStrictly = !this.checked;
      this.CetDialog_1.closeTrigger_in = new Date().getTime();
    },
    toChecked() {
      this.checked = false;
      this.CetDialog_1.openTrigger_in = new Date().getTime();
    },
    treeclick(data, node) {
      console.log(data);
      this.checkdata = data;
    },
    getTreeData() {
      let address = this.address ? this.address.join("/") : [];
      let data = {
        objectlabel: this.ElSelect_3.value,
        type: this.ElSelect_2.value,
        powersellingcompanyId: this.$store.state.sellCompanyId
      };
      customApi.baselineCalculationqueryTreeNodePark(data).then(response => {
        if (response.code === 0) {
          let options = this._.get(response, "data", []);
          this.CetTree_1.inputData_in = options;
        }
      });
    },

    getchartData() {
      this.avaeragedata = "";
      this.defaultdate = [];
      this.clearchart();
      let checknode = [];
      // this.checked=true
      if (this.checked) {
        checknode = this.$refs.tree.$refs.tree.getCheckedNodes(true);
      } else {
        checknode = this.$refs.tree.$refs.tree.getCheckedNodes(false);
      }
      if (checknode.length <= 0) {
        this.$message({
          type: "warning",
          message: "请先勾选节点再计算"
        });
        return;
      }
      console.log(checknode);
      let responseResource = [];
      checknode.forEach(item => {
        // if (item.modelLabel === "responseresource") {
        //   responseResource.push({
        //     modelLabel: item.modelLabel,
        //     objectlabel: item.objectlabel,
        //     objectid: item.objectid
        //   });
        // } else {
        responseResource.push({
          modelLabel: item.modelLabel,
          objectlabel: item.modelLabel,
          objectid: item.id
        });
        // }
      });
      let ids = checknode.map(item => {
        return item.id;
      });
      let itemdate1 = "";
      let itemdate2 = "";
      let data = {
        cycle: 4,
        powersellingcompanyid: this.$store.state.sellCompanyId,
        responseResource: responseResource
      };
      if (this.isBaseDate) {
        itemdate1 =
          moment(this.datedata[0]).format("yy-MM-DD") +
          " " +
          this.CetDatePicker_1.val[0];
        itemdate2 =
          moment(this.datedata[0]).format("yy-MM-DD") +
          " " +
          this.CetDatePicker_1.val[1];

        data.endtime = moment(itemdate2).valueOf();
        data.starttime = moment(itemdate1).valueOf();

        data.sameDay = new Date(
          moment(this.datedata[0]).format("yy-MM-DD 00:00:00")
        ).getTime();
        data.referencetime = this.datedata;
      } else {
        this.datelist = [];
        // this.datedata = [];
        data.referencetime = [];

        itemdate1 =
          moment(this.sameDay).format("yy-MM-DD") +
          " " +
          this.CetDatePicker_1.val[0];
        itemdate2 =
          moment(this.sameDay).format("yy-MM-DD") +
          " " +
          this.CetDatePicker_1.val[1];
        data.endtime = moment(itemdate2).valueOf();
        data.starttime = moment(itemdate1).valueOf();
        data.sameDay = new Date(
          moment(this.sameDay).format("yy-MM-DD 00:00:00")
        ).getTime();
      }
      customApi
        .baselineCalculationqueryVirtualPowerPlant(data)
        .then(response => {
          console.log(response);
          if (response.code === 0) {
            this.chartdata = this._.get(response, "data", []);
            this.avaeragedata = this.chartdata.基线平均负荷[0].value;
            this.CetChart_1.config.options.xAxis.data =
              this.chartdata.基线负荷.map(item => {
                return item.timeStr;
              });

            let arr = [];
            let num = 0;
            let colorList = [
              "#5160FF",
              "#FFA435",
              "#F65D68",
              "#08C673",
              "#9F74F0",
              "#7859FD",
              "#F6BD16",
              "#91C100",
              "#10C1E3",
              "#377DFC",
              "#CD2FE7",
              "#F17737"
            ];
            let obj = {};
            for (var i in this.chartdata) {
              console.log(i);
              console.log(colorList[num]);
              this.CetChart_1.config.options.series.push({
                data: this.chartdata[i].map(item => {
                  return item.value;
                }),
                color: colorList[num],
                type: "line",
                smooth: true,
                showSymbol: false,
                name: i,
                itemStyle: {
                  // color: "#FFA435",
                  borderWidth: 3
                  // borderColor: "#FFA435",
                  // opacity: 0.5
                }
              });
              num++;
              this.CetChart_1.config.options.legend.data.push(i);
              if (i === "基线负荷" || i === "基线平均负荷") {
                obj[i] = true;
              } else {
                obj[i] = false;
                arr.push(moment(i).format("yy年MM月DD日"));
              }
            }
            this.CetChart_1.config.options.legend.selected = obj;
            this.CetChart_1.config.options.series.forEach(item => {
              if (item.name === "基线平均负荷") {
                item.data = [];
                this.CetChart_1.config.options.xAxis.data.forEach(time => {
                  item.data.push(this.avaeragedata);
                });
              }
            });

            this.isBaseDate
              ? (this.defaultdate = [])
              : (this.defaultdate = arr.sort());
            // this.CetChart_1.config.options.legend.data.forEach(item => {
            //   if (item.name === "基线负荷" || item.name === "基线平均负荷") {
            //     this.CetChart_1.config.options.legend.selected[
            //       item.name
            //     ] = true;
            //   } else {
            //     this.CetChart_1.config.options.legend.selected[
            //       item.name
            //     ] = false;
            //   }
            // });

            this.CetChart_1.inputData_in = this.chartdata;
            this.CetChart_1.config.options.legend.data.sort().reverse();
          }
        });
    },
    clearchart() {
      this.CetChart_1 = {
        inputData_in: {},
        config: {
          options: {
            legend: {
              data: [],
              right: 30,
              itemGap: 20,
              top: 0,
              selected: {},
              // icon: "circle",
              textStyle: {
                color: "#9CA2B1"
              }
            },
            grid: {
              top: 80,
              left: 70,
              right: 70
            },
            tooltip: {
              extraCssText:
                'background: #0F2557; border-radius: 12px;color: #fff; opacity: 0.8; box-shadow: "0px 0px 4px 0px rgba(62, 66, 78, 0.22)";border:none;text-align:left;',
              trigger: "axis",
              axisPointer: {
                type: "shadow"
              },
              textStyle: {
                color: "#FFF",
                fontsize: "10px"
              },

              formatter: function (params) {
                let str = params[0].name + "<br>";
                let arr = [];
                params.forEach((item, index) => {
                  arr.push(item.seriesName);
                });
                arr
                  .sort()
                  .reverse()
                  .forEach((item, index) => {
                    params.forEach(code => {
                      if (code.seriesName === item) {
                        arr[index] = code;
                        str +=
                          code.marker +
                          code.seriesName +
                          "(kW):" +
                          code.value.toFixed(2) +
                          "<br>";
                      }
                    });
                  });
                return str;
              }
            },
            xAxis: {
              type: "category",
              // boundaryGap: false,
              name: "时间",
              axisLabel: {
                //---坐标轴 标签
                show: true, //---是否显示
                inside: false, //---是否朝内
                rotate: 0, //---旋转角度
                margin: 5, //---刻度标签与轴线之间的距离
                color: "#9CA2B1" //---默认取轴线的颜色
              },
              data: []
            },
            yAxis: {
              type: "value",
              name: "功率（kW）",
              scale: true,
              axisLabel: {
                //---坐标轴 标签
                show: true, //---是否显示
                inside: false, //---是否朝内
                rotate: 0, //---旋转角度
                margin: 5, //---刻度标签与轴线之间的距离
                color: "#9CA2B1" //---默认取轴线的颜色
              }
            },
            series: []
          }
        }
      };
    },
    defaultchange() {
      if (this.sameDay) {
        this.defaultdisabled = false;
        this.basedisabled = true;
        this.isBaseDate = false;
      } else {
        this.basedisabled = false;
      }
    },
    timechange() {
      if (
        this.CetDatePicker_1.val &&
        this.CetDatePicker_1.val[0] === this.CetDatePicker_1.val[1]
      ) {
        this.$message({
          type: "warning",
          message: "开始时间和结束时间不能相等"
        });
        this.CetDatePicker_1.val = "";
      }
    },
    datechange() {
      if (this.searchdate) {
        this.basedisabled = false;
        this.defaultdisabled = true;
        this.isBaseDate = true;
      } else {
        this.defaultdisabled = false;
      }
      console.log(this.searchdate);
      if (this.searchdate.length > 12) {
        this.$message({
          type: "warning",
          message: "最多选择十二个参考日"
        });
        this.searchdate.splice(0, this.searchdate.length - 12);
      }

      this.datelist = this.searchdate.sort().map(item => {
        return moment(item).format("yy年MM月DD日");
      });
      console.log(this.searchdate.sort());

      this.datedata = this.searchdate.map(item => {
        return moment(item).valueOf();
      });
    },
    CetTree_1_currentNode_out(val) {},
    handleSelectionChange(val) {
      this.sum = val.length;
    }
  },
  created() {
    this.init();
    this.getTreeData();
  },
  mounted() {},
  activated() {}
};
</script>
<style lang="scss" scoped>
.pagebox {
  margin: 0 !important;
  height: 100%;
  width: 100% !important;
  .searchInput {
    width: 298px;
  }
  .contentBox {
    @include padding_top(J2);
    height: 100% !important;
    box-sizing: border-box;
    .searchbox {
      @include background_color(BG1, !important);
      @include border_radius(C3);
      @include padding_top(J2);
    }
    .aside {
      @include background_color(BG1);
      border-top-left-radius: mh-get(C3);
      border-bottom-left-radius: mh-get(C3);
      // @include padding_top(J3);
      .tree {
        // height: calc(100% - 40px) !important;
        // @include margin_top(J2);
        @include padding_left(J2);
        @include padding_right(J2);
      }
      :deep(.el-tree) {
        @include background_color(BG1);
      }
    }

    .countbox {
      display: flex;
      width: 100%;
      justify-content: space-between;
      @include margin_top(J2);
      @include margin_bottom(J2);
      .leftcount {
        width: 68.8%;
        height: 180px;
        @include padding(J2);
        @include background_color(BG1);
        @include border_radius(C3);
        box-sizing: border-box;
        h5 {
          @include font_size("H2");
          font-weight: 400;
          line-height: 14px;
          margin: 0;
          @include margin_bottom(J2);
          // @include margin_top(J2);
        }
        // h5:hover {
        //   color: #6592fd;
        // }
        .datebox {
          display: flex;
          max-width: 860px;
          height: 120px;
          // justify-content: space-around;
          flex-wrap: wrap;
          overflow: auto;
          p {
            width: 20%;
            margin: 10px 21px;
            @include font_size("Aa");
            @include background_color(BG2);
            text-align: center;

            line-height: 30px;
            height: 30px;
            border-radius: 4px;
          }
        }
      }
      .rightcount {
        width: 30%;
        height: 180px;

        @include background_color(BG1);
        @include border_radius(C3);
        text-align: center;
        h5 {
          height: 40px;
          @include font_size("H");
          margin: 40px 0 10px 0;
          font-weight: 500;
          line-height: 40px;
        }
        p {
          height: 20px;
          @include font_size("Aa");

          font-weight: 400;
          line-height: 20px;
        }
      }
    }

    .bgcolor {
      @include background_color(BG1);
      @include border_radius(C3);
    }
    .tableBody {
      text-align: center;
      @include background_color(BG1);
      @include padding_top(J2);
      position: relative;
      height: 66%;
      @include border_radius(C3);
      .empty::after {
        content: "\6682\65e0\6570\636e";
        display: inline-block;
        position: absolute;
        @include font_size("H3");
        line-height: 16px;
        top: calc(50% + 68px);
        margin: auto;
      }
      .imgbox {
        margin: auto;
        position: absolute;
        top: 30%;
        left: 45%;
      }
    }
  }
  .handel {
    cursor: pointer;
  }
  .el-icon-search {
    @include font_size("Aa");
    margin: 10px 7px 0 0;
  }
  .el-dropdown-link {
    cursor: pointer;
  }
}
.datePickerrange {
  width: 300px;
  text-align: left;
  border-radius: 8px !important;
  :deep(.el-range__icon) {
    width: 75px;
  }
  :deep(.el-range-input) {
    text-align: center;
  }
  :deep(.el-range__icon:before) {
    content: "\76ee\6807\65f6\6bb5";
    @include font_color(ZS);
  }
  :deep(.el-range__icon:after) {
    content: "\e71f";
    position: absolute;
    right: 24px;
  }
  :deep(.el-range__close-icon) {
    float: right;
    padding-right: 18px;
  }
  :deep(.el-input__icon.el-icon-circle-close:before) {
    content: "\e79d";
  }
}
.timeSelect {
  width: 100px;
}
.el-icon-info {
  @include font_color(B1);
}
</style>
