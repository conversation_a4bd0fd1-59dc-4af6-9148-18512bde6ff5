<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog v-bind="CetDialog_pagedialog" v-on="CetDialog_pagedialog.event">
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </span>
      <div class="eem-cont-c1 flex-column">
        <div class="clearfix mbJ3">
          <!-- 查询时间: -->
          <CetDateSelect
            class="fr"
            v-bind="CetDateSelect_time"
            v-on="CetDateSelect_time.event"
          ></CetDateSelect>
        </div>
        <CetTable
          class="text-right"
          :data.sync="CetTable_message.data"
          :dynamicInput.sync="CetTable_message.dynamicInput"
          v-bind="CetTable_message"
          v-on="CetTable_message.event"
        >
          <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_time"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_type"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_description"></ElTableColumn>
        </CetTable>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import common from "eem-utils/common";
export default {
  name: "MessageCenter",
  components: {},
  computed: {},
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      // index组件
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        label: "#", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "100" //绝对宽度
      },
      // time组件
      ElTableColumn_time: {
        prop: "recordTime", // 支持path a[0].b
        minWidth: 100, //该宽度会自适应
        label: $T("时间"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: common.formatDateCol("YYYY-MM-DD HH:mm:ss.SSS") //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      // type组件
      ElTableColumn_type: {
        prop: "logTypeName", // 支持path a[0].b
        minWidth: 100, //该宽度会自适应
        label: $T("类型"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      // description组件
      ElTableColumn_description: {
        prop: "content", // 支持path a[0].b
        minWidth: 100, //该宽度会自适应
        label: $T("描述"),
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      // message表格组件
      CetTable_message: {
        //组件模式设置项
        queryMode: "diff", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryMessage",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [{ name: "time_in", operator: "BETWEEN", prop: "time" }], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {
          time_in: [
            this.$moment().startOf("day").valueOf(),
            this.$moment().endOf("day").valueOf()
          ]
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {},
        exportFileName: "",
        style: {
          height: "500px"
        },
        event: {
          record_out: this.CetTable_message_record_out,
          outputData_out: this.CetTable_message_outputData_out
        }
      },
      CetDateSelect_time: {
        value: { dateType: "1", value: new Date().getTime() },
        typeList: ["day", "week", "month"],
        event: {
          date_out: this.CetDateSelect_time_date_out
        }
      },
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("信息中心"),
        showClose: true,
        "append-to-body": true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    }
  },
  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    CetDateSelect_time_date_out(val) {
      this.CetTable_message.dynamicInput.time_in = this._.cloneDeep(val);
    },
    // message表格输出
    CetTable_message_record_out(val) {},
    CetTable_message_outputData_out(val) {},
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetTable_message.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    no() {}
  },
  created: function () {}
};
</script>
<style lang="scss" scoped></style>
