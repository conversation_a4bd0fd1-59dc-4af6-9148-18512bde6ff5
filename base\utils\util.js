/**
 * 定时器函数
 *
 * @param {*} cb 定时回调
 * @param {*} time 每次执行过后多久再次执行
 */
import axios from "axios";

let gid = 0;
/**
 * 获取全局的唯一ID
 *
 * @param prefix 前缀
 */
export function guid(prefix) {
  if (gid > 100000) {
    gid = 0;
  }
  return (prefix || "") + gid++;
}
export function hasProp(obj, key) {
  return Object.prototype.hasOwnProperty.call(obj, key);
}
export function timer(cb, interval = 4e3, { immediate = true } = {}) {
  let isRun = true;
  let timerId = null;

  const exec = timeout => {
    return new Promise((resolve, reject) => {
      timerId = setTimeout(() => {
        if (isRun) {
          resolve(cb(cancel));
        }
      }, timeout || interval);
    });
  };

  const run = async () => {
    try {
      if (immediate) {
        await exec(0);
      }
    } finally {
      // eslint-disable-next-line no-unmodified-loop-condition
      while (isRun) {
        try {
          await exec();
        } finally {
          await exec();
        }
      }
    }
  };

  const cancel = () => {
    isRun = false;
    if (timerId) {
      window.clearTimeout(timerId);
    }
  };

  run();
  return cancel;
}

/**
 * 空白值
 */
export function isBlankValue(value) {
  if (value === "" || value === undefined || value === null) {
    return true;
  }
  return false;
}

/**
 * 空白值的默认填充
 */
export function fillBlankValue(value, signal) {
  if (isBlankValue(value)) {
    return signal || "--";
  }
  return value;
}

export function fillBlankObject(obj, keys = []) {
  _.each(obj, (value, key) => {
    if (!keys.length || ~keys.indexOf(key)) {
      obj[key] = fillBlankValue(value);
    }
  });
}
/**
 * 是否为开发环境
 */
export function isDev() {
  return process.env.NODE_ENV === "development";
}

/**
 * 是否为生产环境
 */
export function isProd() {
  return process.env.NODE_ENV === "production";
}

/**
 * 是否为Debug环境
 */
export function isDebug() {
  return isDev() || window.location.search.includes("?debug");
}
export function error(desc, ...argv) {
  if (isDebug()) {
    console.log(
      `%c mh_error: ${desc}`,
      "background:#F56C6C;color:#fff;border-radius:2px",
      ...argv
    );
  }
}
/**
 * 获取hash后的路径
 */
export function getHashPath() {
  const hash = window.decodeURIComponent(window.location.hash);
  return hash.replace(/^#/, "");
}

export default {
  timer,
  fillBlankValue,
  fillBlankObject,
  randomStr,
  error,
  isProd,
  getHashPath
};

/**
 * 获取一个 json 文件
 * @param {String} uri 资源url
 */
export function getJsonFile(uri) {
  return axios.get(uri).then(v => {
    const xml = v.request;
    let ret;
    try {
      ret = JSON.parse(xml.responseText);
    } catch (e) {
      throw new Error(`uri: ${uri}, JSON 文件解析失败，请检查该Json文件格式`);
    }
    return ret;
  });
}

/**
 * 获取一段唯一的随机字符串
 */
export function randomStr() {
  return guid("r") + Math.random().toString(16).slice(2);
}

/**
 * @param {Array} items
 * @param {Any} value 查找的value
 * @prop {Function} diffFn 对比判定函数
 * @prop {String} childKey
 * @prop {String} key
 */
export function find(
  items,
  value,
  { diffFn, childKey = "child", valueKey = "id" }
) {
  function isEqual(item) {
    return diffFn
      ? diffFn(item[valueKey], value, item)
      : hasProp(item, valueKey) && item[valueKey] === value;
  }

  function loop(items) {
    for (const item of items) {
      if (isEqual(item)) {
        return item;
      }
      if (item[childKey]) {
        const ret = loop(item[childKey]);
        if (ret) {
          return ret;
        }
      }
    }
  }
  return loop(items) || null;
}
