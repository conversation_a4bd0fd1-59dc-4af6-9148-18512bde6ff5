<template>
  <div class="fullfilled eem-common">
    <el-container class="fullfilled">
      <el-aside class="eem-aside">
        <CetTree
          class="fullheight"
          :selectNode.sync="CetTree_Users.selectNode"
          :checkedNodes.sync="CetTree_Users.checkedNodes"
          v-bind="CetTree_Users"
          v-on="CetTree_Users.event"
        ></CetTree>
      </el-aside>
      <el-main class="mlJ3 p0">
        <Content
          :userData_in="UserDetailContainer.userData_in"
          :resetTrigger_in="UserDetailContainer.resetTrigger_in"
          @editUser_out="editUser"
        ></Content>
      </el-main>
      <EditDialog
        :currentUserId="userId"
        :documentNodes_in="AddOrEditUserDialog.documentNodes_in"
        :userData_in="AddOrEditUserDialog.userData_in"
        :openTrigger_in="AddOrEditUserDialog.openTrigger_in"
        @save="save"
      ></EditDialog>
    </el-container>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import Content from "./basecomponents/Content";
import EditDialog from "./basecomponents/EditDialog";

export default {
  name: "UserGroupManage",
  components: { Content, EditDialog },
  data() {
    return {
      // 用户树组件
      CetTree_Users: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        defaultExpandAll: true,
        event: {
          currentNode_out: this.CetTree_Users_currentNode_out
        }
      },

      // 新增或编辑用户组弹窗
      AddOrEditUserDialog: {
        isEditMode: false,
        userData_in: null,
        documentNodes_in: null,
        openTrigger_in: new Date().getTime()
      },

      // 用户详情组件
      UserDetailContainer: {
        userData_in: null,
        resetTrigger_in: new Date().getTime()
      }
    };
  },
  computed: {
    userId() {
      var vm = this;
      return vm.$store.state.userInfo.id;
    },
    projectTenantId() {
      var vm = this;
      return vm.$store.state.projectTenantId;
    }
  },
  methods: {
    // 用户树组件输出
    CetTree_Users_currentNode_out(val) {
      const vm = this;
      if (val.modelLabel === "usergroup") {
        return;
      }

      vm.UserDetailContainer.userData_in = val;
      vm.UserDetailContainer.resetTrigger_in = new Date().getTime();
    },

    // 获取用户组列表信息
    getTreeData() {
      var _this = this;
      let data = {
        loadChildren: true,
        removeRootUser: true,
        tenantId: _this.projectTenantId
      };
      customApi.queryProjectUserAndGroup(data).then(res => {
        let treeData = _this._.get(res, "data", []) || [];
        treeData.forEach(item => {
          let child = item.children || [];
          child.forEach(i => {
            i.relativeUserGroupName = item.name;
          });
        });
        _this.CetTree_Users.selectNode = null;
        _this.CetTree_Users.inputData_in = treeData;
        _this.CetTree_Users.selectNode = this._.get(
          treeData,
          "[0].children[0]",
          null
        );
        if (!treeData.length) {
          _this.UserDetailContainer.userData_in = null;
          _this.UserDetailContainer.resetTrigger_in = new Date().getTime();
        }
      });
    },

    // 编辑用户
    editUser(userData, videoData, documentNodes) {
      const vm = this;

      if (!userData) {
        return;
      }

      vm.AddOrEditUserDialog.documentNodes_in = vm._.cloneDeep(documentNodes);
      vm.AddOrEditUserDialog.userData_in = vm._.cloneDeep(userData);
      vm.AddOrEditUserDialog.isEditMode = true;
      vm.AddOrEditUserDialog.openTrigger_in = new Date().getTime();
    },

    // 删除用户
    deleteUser(userData) {
      const vm = this;

      if (!userData) {
        return;
      }

      vm.$confirm("确定要删除所选项吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          customApi
            .deleteUser({
              id: userData.id,
              name: userData.name
            })
            .then(() => {
              vm.refresh();
            });
        })
        .catch(() => {});
    },

    // 刷新数据
    refresh() {
      this.getTreeData();
    },
    save(val) {
      this.CetTree_Users.selectNode = {
        tree_id: "user_" + val
      };
    },
    userGroupAddBtnClick() {
      this.userGroupAddAndEditShow = true;
    }
  },
  activated() {
    this.CetTree_Users.selectNode = null;
    this.refresh();
  }
};
</script>
<style lang="scss" scoped>
.UserGroupManage_rightbox {
  :deep(.el-main) {
    padding: 0px;
  }
}
</style>
