<template>
  <div class="page eem-cont-c1">
    <div class="clearfix mbJ3">
      <div class="title fsH2 fl">{{ $T("需量分析") }}</div>
      <div class="pickers fr">
        <el-date-picker
          v-model="CetDatePicker_1.val"
          v-bind="CetDatePicker_1"
          type="date"
          :placeholder="$T('选择日期')"
          :clearable="false"
          :picker-options="CetDatePicker_1.pickerOptions"
          @change="CetDatePicker_1_dateVal_out"
        ></el-date-picker>
        <!-- format属性 用于设置时间选择器的选择 -->
        <el-time-picker
          class="mlJ1"
          is-range
          v-model="CetTimePicker.val"
          v-bind="CetTimePicker"
          :range-separator="$T('至')"
          start-placeholde="开始时间"
          end-placeholde="结束时间"
          placeholder="选择时间范围"
          format="HH:mm"
          :clearable="false"
          @change="CetTimePicker_1_TimeVal_out"
        ></el-time-picker>
      </div>
    </div>

    <div style="height: 600px; width: 100%">
      <CetChart
        :inputData_in="CetChart_1.inputData_in"
        v-bind="CetChart_1.config"
      ></CetChart>
    </div>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
export default {
  props: {
    // 树节点点击数据
    currentNode: {
      type: Object
    },
    // 日期被点击数据
    dateVal: {
      type: Number
    },
    // 日期被点击数据
    init: {
      type: Number
    }
  },
  watch: {
    // 对节点的改变进行监听
    currentNode: {
      handler: function (val) {
        if (val) {
          this.getDemandTrend();
        } else {
          this.CetChart_1.config.options.xAxis.data = [];
          this.CetChart_1.config.options.series[0].data = [];
        }
      },
      deep: true
    },
    // 对日期的改变进行监听
    init() {
      this.CetDatePicker_1.val = this.dateVal;
    }
  },
  computed: {
    // 获取项目ID
    projectId() {
      return this.$store.state.projectId;
    }
  },
  data() {
    return {
      startTime: "",
      endTime: "",
      bool: false,
      // 图表属性绑定
      CetChart_1: {
        inputData_in: {},
        config: {
          options: {
            tooltip: {
              trigger: "axis",
              formatter: function (val) {
                var list = val || [];
                var formatterStr = `${list[0].name}`;
                for (var i = 0, len = list.length; i < len; i++) {
                  formatterStr += ` <br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                    val[i].color
                  };"></span>${val[i].seriesName}（kW） : ${
                    val[i].value || "--"
                  }`;
                }
                return formatterStr;
              },
              confine: true
            },
            grid: {
              left: 50,
              right: 50,
              bottom: 40
            },
            legend: {
              top: 10,
              itemStyle: {
                opacity: 0
              }
            },
            title: {
              text: $T("需量趋势"),
              show: false
            },
            toolbox: {
              feature: {
                dataZoom: {
                  yAxisIndex: "none"
                },
                // restore: {},
                saveAsImage: {}
              },
              right: 30
            },
            xAxis: {
              type: "category",
              boundaryGap: false,
              data: [],
              name: $T("时间")
            },
            yAxis: {
              type: "value"
            },
            series: [
              {
                name: $T("查询需量"),
                type: "line",
                data: [],
                itemStyle: {
                  color: "#7ECF51"
                },
                symbol: "none",
                markPoint: {
                  data: [
                    {
                      name: $T("查询最大需量"),
                      type: "max"
                    }
                  ],
                  itemStyle: {
                    // color: "#fff"
                  },
                  label: {
                    show: true,
                    formatter: function (val) {
                      return val.name;
                    }
                    // color: "#999",
                    // position: [10, 20]
                  }
                }
              }
            ]
          }
        }
      },
      // 日期选择器属性绑定
      CetDatePicker_1: {
        disable_in: false,
        val: this.dateVal,
        style: {
          display: "inline-block",
          width: "220px"
        },
        // 设置日期选择器中的日期只能选择今天之前的日期
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        }
      },
      // 时间选择器属性绑定
      CetTimePicker: {
        // 给时间选择器初始化值
        val: [
          this.$moment(this.dateVal).add(6, "h").format(),
          this.$moment(this.dateVal).add(12, "h").format()
        ],
        style: {
          width: "200px"
        }
      }
    };
  },

  methods: {
    // 图表的数据请求
    async getDemandTrend() {
      if (!this.currentNode) {
        return;
      }
      if (this.bool) {
        return;
      }
      this.bool = true;
      this.CetChart_1.config.options.xAxis.data = [];
      this.CetChart_1.config.options.series[0].data = [];
      var data = {
        node: {
          modelLabel: this.currentNode.modelLabel,
          id: this.currentNode.id,
          name: this.currentNode.name,
          tree_id: this.currentNode.tree_id
        },
        queryDay: {
          endTime: new Date(
            this.$moment(this.CetDatePicker_1.val).format("YYYY-MM-DD") +
              " " +
              this.$moment(this.CetTimePicker.val[1]).format("HH:mm")
          ).valueOf(),
          startTime: new Date(
            this.$moment(this.CetDatePicker_1.val).format("YYYY-MM-DD") +
              " " +
              this.$moment(this.CetTimePicker.val[0]).format("HH:mm")
          ).valueOf()
        },
        projectId: this.projectId,
        cycle: 7
      };
      const response = await customApi.getMonitoredLineTrend(data);
      this.bool = false;
      if (response.code === 0 && response.data && response.data.length > 0) {
        var arr1 = [];
        var xAxisArr = [];
        response.data.forEach(item => {
          xAxisArr.push(this.$moment(item.time).format("HH:mm"));
          arr1.push(
            item.consultDemand || item.consultDemand === 0
              ? item.consultDemand.toFixed2(2)
              : "--"
          );
        });
        this.CetChart_1.config.options.xAxis.data = xAxisArr;
        // 查询需量
        this.CetChart_1.config.options.series[0].data = arr1;
      }
    },
    // 日期的时间选择
    CetDatePicker_1_dateVal_out() {
      this.getDemandTrend();
    },
    // 时间的选择操作
    CetTimePicker_1_TimeVal_out() {
      this.getDemandTrend();
    }
  },
  created() {},
  mounted() {
    this.getDemandTrend();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  .title {
    text-align: left;
    font-weight: bold;
  }
  .pickers {
    text-align: right;
  }
  :deep(.el-range-separator) {
    width: 30px;
  }
}
</style>
