<template>
  <div class="page" style="padding: 0px">
    <template v-if="!showWriteOrder">
      <div class="fullheight flex-column">
        <el-header height="auto" class="header eem-cont mbJ3 clearfix">
          <div class="goBack fl mrJ1 lhHm" @click="goBack">
            <i class="el-icon-arrow-left"></i>
            {{ $T("返回") }}
          </div>
          <span class="fl lhHm common-title-H2">{{ $T("工单详情") }}</span>
          <div class="text fr">
            <!-- 审核 -->
            <CetButton
              class="fr"
              v-bind="CetButton_1"
              v-on="CetButton_1.event"
            ></CetButton>
            <!-- 录入工单 -->
            <CetButton
              class="fr"
              v-bind="CetButton_4"
              v-on="CetButton_4.event"
            ></CetButton>
            <!-- 打印 -->
            <CetButton
              class="fr mrJ1"
              v-bind="CetButton_3"
              v-on="CetButton_3.event"
            ></CetButton>
          </div>
          <!-- <div class="line"></div> -->
        </el-header>
        <el-container class="eem-common flex-auto">
          <el-aside width="315px" class="eem-aside flex-column">
            <div class="common-title-H2 mbJ3">{{ $T("基础信息") }}</div>
            <el-main class="flex-auto" style="padding: 0px">
              <div
                v-for="(item, index) in listData"
                :key="index"
                :class="{ clearfix: true, listItem: true }"
              >
                <div class="fl ellipsis" style="width: 120px">
                  {{ item.name }}:
                </div>
                <el-tooltip
                  :content="filData(orderMsg[item.key], item.type)"
                  effect="light"
                >
                  <div
                    class="fl text-overflow"
                    style="width: calc(100% - 120px)"
                  >
                    {{ filData(orderMsg[item.key], item.type) || "--" }}
                  </div>
                </el-tooltip>
              </div>
              <div class="clearfix listItem">
                <div class="fl ellipsis mbJ1" style="width: 120px">
                  {{ $T("安全措施说明") }}:
                </div>
                <!-- <div class="fullwidth">
                <ElInput v-model="orderMsg.description" v-bind="ElInput_describe" class="mt5"></ElInput>
              </div> -->
                <div class="description-icon">
                  {{ orderMsg.safetymeasure || "--" }}
                </div>
              </div>
            </el-main>
          </el-aside>
          <el-container class="mlJ3 eem-cont">
            <el-main style="height: 100%; padding: 0px">
              <el-tabs
                tab-position="top"
                class="el-container_tabs"
                v-model="activeName"
              >
                <el-tab-pane
                  :label="$T('维保内容')"
                  name="maintenance"
                  class="flex-column fullheight"
                >
                  <div class="flex-auto flex-column mbJ3">
                    <span>{{ $T("维保项目") }}:</span>
                    <div class="mtJ3 flex-auto">
                      <CetTable
                        :data.sync="CetTable_1.data"
                        :dynamicInput.sync="CetTable_1.dynamicInput"
                        v-bind="CetTable_1"
                        v-on="CetTable_1.event"
                      >
                        <template v-for="(column, index) in Columns_Simulation">
                          <el-table-column
                            v-if="column.custom && column.custom === 'tag'"
                            v-bind="column"
                            :key="index"
                            class-name="font0 hand"
                            label-class-name="font14"
                          >
                            <template slot-scope="scope">
                              <el-tag
                                size="small"
                                class="text-middle font14"
                                :effect="column.tagEffect"
                                :type="
                                  column.tagTypeFormatter
                                    ? column.tagTypeFormatter(
                                        scope.row,
                                        scope.column
                                      )
                                    : 'primary'
                                "
                              >
                                {{
                                  column.formatter
                                    ? column.formatter(
                                        scope.row,
                                        scope.column,
                                        scope.row[column.prop],
                                        scope.$index
                                      )
                                    : scope.row[column.prop]
                                }}
                              </el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column
                            v-else-if="
                              column.custom && column.custom === 'button'
                            "
                            v-bind="column"
                            :key="index"
                            class-name="font0"
                            label-class-name="font14"
                          >
                            <template slot-scope="scope">
                              <span
                                class="clickformore"
                                @click.stop="
                                  column.onButtonClick
                                    ? column.onButtonClick(
                                        scope.row,
                                        scope.$index
                                      )
                                    : void 0
                                "
                              >
                                {{
                                  column.formatter
                                    ? column.formatter(
                                        scope.row,
                                        scope.column,
                                        scope.row[column.prop],
                                        scope.$index
                                      )
                                    : column.text
                                }}
                              </span>
                            </template>
                          </el-table-column>
                          <el-table-column
                            v-else-if="
                              column.custom && column.custom === 'text'
                            "
                            v-bind="column"
                            :key="index"
                            class-name="hand"
                          >
                            <template slot-scope="scope">
                              <span
                                v-bind:style="
                                  column.textTypeFormatter
                                    ? column.textTypeFormatter(
                                        scope.row,
                                        scope.column
                                      )
                                    : ''
                                "
                              >
                                {{
                                  column.formatter
                                    ? column.formatter(
                                        scope.row,
                                        scope.column,
                                        scope.row[column.prop],
                                        scope.$index
                                      )
                                    : column.text
                                }}
                              </span>
                            </template>
                          </el-table-column>
                          <el-table-column
                            v-else
                            v-bind="column"
                            :key="index"
                            class-name="hand"
                          ></el-table-column>
                        </template>
                      </CetTable>
                    </div>
                  </div>
                  <el-row :gutter="$J3">
                    <el-col :span="8">
                      <div class="mbJ3">
                        <span>{{ $T("处理描述") }}：</span>
                      </div>
                      <div class="description-icon" style="height: 150px">
                        {{ handledescription || "--" }}
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="mbJ3">
                        <span>{{ $T("额外消耗备件") }}：</span>
                      </div>
                      <div style="height: 150px">
                        <CetTable
                          :data.sync="CetTable_5.data"
                          :dynamicInput.sync="CetTable_5.dynamicInput"
                          v-bind="CetTable_5"
                          v-on="CetTable_5.event"
                        >
                          <template v-for="item in Columns_spareParts">
                            <ElTableColumn
                              :key="item.label"
                              v-bind="item"
                            ></ElTableColumn>
                          </template>
                        </CetTable>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="mbJ3">
                        <span>{{ $T("附件") }}：</span>
                      </div>
                      <div style="height: 150px">
                        <CetTable
                          :data.sync="CetTable_4.data"
                          :dynamicInput.sync="CetTable_4.dynamicInput"
                          v-bind="CetTable_4"
                          v-on="CetTable_4.event"
                        >
                          <template v-for="(column, index) in Columns_File">
                            <el-table-column
                              v-if="column.custom && column.custom === 'tag'"
                              v-bind="column"
                              :key="index"
                              class-name="font0 hand"
                              label-class-name="font14"
                            >
                              <template slot-scope="scope">
                                <el-tag
                                  size="small"
                                  class="text-middle font14"
                                  :effect="column.tagEffect"
                                  :type="
                                    column.tagTypeFormatter
                                      ? column.tagTypeFormatter(
                                          scope.row,
                                          scope.column
                                        )
                                      : 'primary'
                                  "
                                >
                                  {{
                                    column.formatter
                                      ? column.formatter(
                                          scope.row,
                                          scope.column,
                                          scope.row[column.prop],
                                          scope.$index
                                        )
                                      : scope.row[column.prop]
                                  }}
                                </el-tag>
                              </template>
                            </el-table-column>
                            <el-table-column
                              v-else-if="
                                column.custom && column.custom === 'button'
                              "
                              v-bind="column"
                              :key="index"
                              class-name="font0"
                              label-class-name="font14"
                            >
                              <template slot-scope="scope">
                                <span
                                  class="clickformore"
                                  @click.stop="
                                    column.onButtonClick
                                      ? column.onButtonClick(
                                          scope.row,
                                          scope.$index
                                        )
                                      : void 0
                                  "
                                >
                                  {{
                                    column.formatter
                                      ? column.formatter(
                                          scope.row,
                                          scope.column,
                                          scope.row[column.prop],
                                          scope.$index
                                        )
                                      : column.text
                                  }}
                                </span>
                              </template>
                            </el-table-column>
                            <el-table-column
                              v-else
                              v-bind="column"
                              :key="index"
                              class-name="hand"
                            ></el-table-column>
                          </template>
                          <el-table-column
                            :label="$T('操作')"
                            width="140"
                            align="left"
                          >
                            <template slot-scope="scope">
                              <u
                                :class="[
                                  {
                                    preview: isPreview(scope.row),
                                    disabled: !isPreview(scope.row)
                                  }
                                ]"
                                @click="
                                  handleClick_see_out(scope.row, scope.$index)
                                "
                              >
                                {{ $T("预览") }}
                              </u>
                              <span class="btnline"></span>
                              <span
                                class="clickformore"
                                @click="
                                  handleClick_download_out(
                                    scope.row,
                                    scope.$index
                                  )
                                "
                              >
                                {{ $T("下载") }}
                              </span>
                            </template>
                          </el-table-column>
                        </CetTable>
                      </div>
                      <div>
                        <CetButton
                          class="fr mtJ3"
                          v-bind="CetButton_2"
                          v-on="CetButton_2.event"
                        ></CetButton>
                      </div>
                    </el-col>
                  </el-row>
                </el-tab-pane>
                <el-tab-pane
                  :label="$T('流程状态')"
                  name="flowchart"
                  class="flex-column fullheight"
                >
                  <div class="flex-auto flex-column mbJ3">
                    <span>{{ $T("流程表") }}:</span>
                    <div class="mtJ3 flex-auto">
                      <CetTable
                        :data.sync="CetTable_3.data"
                        :dynamicInput.sync="CetTable_3.dynamicInput"
                        v-bind="CetTable_3"
                        v-on="CetTable_3.event"
                      >
                        <template v-for="(column, index) in Columns_Process">
                          <el-table-column
                            v-if="column.custom && column.custom === 'tag'"
                            v-bind="column"
                            :key="index"
                            class-name="font0 hand"
                            label-class-name="font14"
                          >
                            <template slot-scope="scope">
                              <el-tag
                                size="small"
                                class="text-middle font14"
                                :effect="column.tagEffect"
                                :type="
                                  column.tagTypeFormatter
                                    ? column.tagTypeFormatter(
                                        scope.row,
                                        scope.column
                                      )
                                    : 'primary'
                                "
                              >
                                {{
                                  column.formatter
                                    ? column.formatter(
                                        scope.row,
                                        scope.column,
                                        scope.row[column.prop],
                                        scope.$index
                                      )
                                    : scope.row[column.prop]
                                }}
                              </el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column
                            v-else-if="
                              column.custom && column.custom === 'button'
                            "
                            v-bind="column"
                            :key="index"
                            class-name="font0"
                            label-class-name="font14"
                          >
                            <template slot-scope="scope">
                              <span
                                class="clickformore"
                                @click.stop="
                                  column.onButtonClick
                                    ? column.onButtonClick(
                                        scope.row,
                                        scope.$index
                                      )
                                    : void 0
                                "
                              >
                                {{
                                  column.formatter
                                    ? column.formatter(
                                        scope.row,
                                        scope.column,
                                        scope.row[column.prop],
                                        scope.$index
                                      )
                                    : column.text
                                }}
                              </span>
                            </template>
                          </el-table-column>
                          <el-table-column
                            v-else
                            v-bind="column"
                            :key="index"
                            class-name="hand"
                          ></el-table-column>
                        </template>
                      </CetTable>
                    </div>
                  </div>
                  <span>{{ $T("流程图") }}:</span>
                  <div class="mtJ3" style="height: 300px; overflow: auto">
                    <img :src="imgSrc" :alt="$T('暂无流程图')" />
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-main>
          </el-container>
        </el-container>
      </div>
      <!-- 维保工单打印 -->
      <div id="printContent" style="display: none">
        <printOrder :inputData_in="printDetail"></printOrder>
      </div>
    </template>
    <WriteOrder
      v-if="showWriteOrder"
      v-bind="WriteOrder"
      v-on="WriteOrder.event"
    ></WriteOrder>
    <toExamine
      :visibleTrigger_in="toExamine.visibleTrigger_in"
      :closeTrigger_in="toExamine.closeTrigger_in"
      :inputData_in="toExamine.inputData_in"
      :codes_in="toExamine.codes_in"
      @confirm_out="toExamine_confirm_out"
    />
    <FlowChart
      :visibleTrigger_in="flowChart.visibleTrigger_in"
      :closeTrigger_in="flowChart.closeTrigger_in"
      :inputData_in="flowChart.inputData_in"
    />
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom.js";
import toExamine from "./toExamine";
import FlowChart from "./FlowChart";
import WriteOrder from "./WriteOrder";
import printOrder from "./printOrder.vue";
var FileSaver = require("file-saver");

export default {
  name: "classDetail",
  components: {
    toExamine,
    FlowChart,
    WriteOrder,
    printOrder
  },
  props: {
    inputData_in: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  data() {
    return {
      activeName: "maintenance",
      listData: [
        {
          name: $T("工单号"),
          key: "code",
          type: "string"
        },
        {
          name: $T("当前状态"),
          key: "currentStatus",
          type: "string"
        },
        {
          name: $T("维保目标"),
          key: "inspectObject",
          type: "string"
        },
        {
          name: $T("执行人"),
          key: "staffName",
          type: "string"
        },
        {
          name: $T("工单来源"),
          key: "createType",
          type: "string"
        },
        {
          name: $T("创建人"),
          key: "creatorname",
          type: "string"
        },
        {
          name: $T("预计开始时间"),
          key: "executetimeplan",
          type: "date"
        },
        {
          name: $T("预计结束时间"),
          key: "finishtimeplan",
          type: "date"
        },
        {
          name: $T("实际开始时间"),
          key: "executetime",
          type: "date"
        },
        {
          name: $T("实际结束时间"),
          key: "finishtime",
          type: "date"
        },
        {
          name: $T("责任班组"),
          key: "teamName",
          type: "string"
        },
        {
          name: $T("等级"),
          key: "taskLevelName",
          type: "string"
        }
      ],
      // describe组件
      ElInput_describe: {
        value: "",
        type: "textarea",
        resize: "none",
        rows: 5,
        style: {
          width: "100%"
        }
      },
      orderMsg: {},
      itemAction: 0,
      CetButton_1: {
        visible_in: false,
        disable_in: false,
        title: $T("审核"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: $T("全部下载"),
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetButton_3: {
        visible_in: true,
        disable_in: false,
        title: $T("打印"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      CetButton_4: {
        visible_in: true,
        disable_in: false,
        title: $T("录入"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_4_statusTrigger_out
        }
      },

      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryEventPlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          scenariosId_in: 0,
          limit_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 50
        },
        highlightCurrentRow: false,
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          // record_out: this.CetTable_1_record_out
        },
        height: 50,
        style: {
          "text-align": "center"
        },
        showSelection: false
      },
      Columns_Simulation: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 60,
          label: "#",
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "groupName",
          minWidth: 100,
          width: "",
          label: $T("维保分组"),
          sortable: false,
          headerAlign: "leftr",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "maintenanceTypeName",
          minWidth: 100,
          width: "",
          label: $T("维保方式"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "content",
          minWidth: 100,
          width: "",
          label: $T("维保内容"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          custom: "text",
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "sparePartName",
          minWidth: 100,
          width: "",
          label: $T("零部件名称"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "number",
          minWidth: 110,
          width: "",
          label: $T("预计消耗数量"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "consumeAmount",
          minWidth: 110,
          width: "",
          label: $T("实际消耗数量"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "unit", // 支持path a[0].b
          label: $T("单位"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "80", //绝对宽度
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "executorName",
          minWidth: 120,
          width: "",
          label: $T("执行人"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formatterExecutor
        },
        {
          type: "",
          prop: "maintenanceResult",
          minWidth: 120,
          width: "",
          label: $T("维保结果"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ],
      Columns_State: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 60,
          label: "#",
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "paraName",
          minWidth: 100,
          width: "",
          label: $T("参数名"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "status",
          minWidth: 100,
          width: "",
          label: $T("状态"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          custom: "text",
          showOverflowTooltip: true,
          formatter: this.statusFormatter,
          textTypeFormatter: this.statusStyleFormatter
        }
      ],
      CetTable_3: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryEventPlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          scenariosId_in: 0,
          limit_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 50
        },
        highlightCurrentRow: false,
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          // record_out: this.CetTable_1_record_out
        },
        height: 50,
        style: {
          "text-align": "center"
        },
        showSelection: false
      },
      Columns_Process: [
        {
          type: "",
          prop: "nodename",
          minWidth: "",
          width: 100,
          label: $T("节点"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "operator",
          minWidth: 100,
          width: "",
          label: $T("操作人"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "detail",
          minWidth: "",
          width: "",
          label: $T("描述"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "logtime",
          minWidth: "",
          width: "",
          label: $T("日期时间"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatDateCol("YYYY-MM-DD HH:mm:ss")
        },
        {
          type: "",
          prop: "remark",
          minWidth: "",
          width: "",
          label: $T("内容"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ],
      CetTable_4: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryEventPlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          scenariosId_in: 0,
          limit_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 50
        },
        highlightCurrentRow: false,
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {},
        height: 50,
        style: {
          "text-align": "center"
        },
        showSelection: false
      },
      Columns_File: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 60,
          label: "#",
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "name",
          minWidth: 100,
          width: "",
          label: $T("名称"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        }
      ],
      CetTable_5: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        highlightCurrentRow: false,
        paginationCfg: {},
        exportFileName: "",
        event: {
          /* record_out: this.CetTable_5_record_out,
          outputData_out: this.CetTable_5_outputData_out */
        }
      },
      Columns_spareParts: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          //minWidth: "200",  //该宽度会自适应
          width: "60" //绝对宽度
          //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("备件名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
          // width: "160" //绝对宽度
        },
        {
          prop: "number", // 支持path a[0].b
          label: $T("数量"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatterUnit
        }
      ],
      toExamine: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        codes_in: []
      },
      flowChart: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      handledescription: "",
      isTab: 1,
      imgSrc: null,
      showWriteOrder: false,
      WriteOrder: {
        inputData_in: null,
        event: {
          goBackDetail: this.goBackDetail,
          WriteOrderConfirm: this.WriteOrderConfirm_out
        }
      },
      printDetail: {} // 要打印的工单详情
    };
  },
  watch: {
    inputData_in: {
      handler: function (val, oldVal) {
        this.init();
      },
      deep: true,
      immediate: true
    }
  },

  methods: {
    // 显示详情信息
    init() {
      if (this._.isEmpty(this.inputData_in)) return;
      this.activeName = "maintenance";
      this.showWriteOrder = false;
      this.orderMsg = this._.cloneDeep(this.inputData_in);
      this.WriteOrder.inputData_in = this._.cloneDeep(this.inputData_in);
      this.printDetail = this._.cloneDeep(this.inputData_in);
      // this.querySchemeDetailXJ_out();
      // 维保项目
      const maintenanceItems = this._.get(
        this.inputData_in,
        "maintenanceItemExtendVos",
        []
      );
      this.CetTable_1.data = maintenanceItems;
      this.CetTable_3.data =
        this._.get(this.inputData_in, "processFlowUnits", []) || [];
      this.CetTable_4.data =
        this._.get(this.inputData_in, "attachmentList", []) || [];
      this.CetTable_5.data =
        this._.get(this.inputData_in, "sparePartNumbers", []) || [];
      this.handledescription = this.inputData_in.handledescription;
      if (
        this.inputData_in.executetimeplan &&
        this.inputData_in.timeconsumeplan
      ) {
        this.orderMsg.finishtimeplan =
          this.inputData_in.executetimeplan + this.inputData_in.timeconsumeplan;
      }
      this.orderMsg.currentStatus = this._.get(
        this.inputData_in,
        "userTaskConfig.name",
        null
      );
      this.isTab = 1;
      this.imgSrc = null;
      this.getImgUrl(this.inputData_in.code);
      this.checkAuth(this.inputData_in.code);
    },
    // 是否具有录入工单信息权限
    checkAuth(code) {
      if (!code) return;
      customApi.checkWorkorderAuth(code).then(res => {
        if (res.code === 0) {
          if (this.inputData_in.worksheetstatus === 3) {
            this.CetButton_1.visible_in = res.data;
          } else {
            this.CetButton_1.visible_in = false;
          }
          if (
            this.inputData_in.worksheetstatus === 1 ||
            this.inputData_in.worksheetstatus === 4
          ) {
            this.CetButton_3.visible_in = true;
            this.CetButton_4.visible_in = res.data;
          } else {
            this.CetButton_3.visible_in = false;
            this.CetButton_4.visible_in = false;
          }
        }
      });
    },
    // 数量 + 单位
    formatterUnit(row) {
      if (row.number || row.number === 0) {
        return row.number + (" " + row.unit || "个");
      } else {
        return "--";
      }
    },
    // 可以预览的文件
    isPreview(row) {
      const uploadPath = row.url;
      const suffixArray = (uploadPath && uploadPath.split(".")) || [];
      const suffix = suffixArray[suffixArray.length - 1];
      // 支持预览的格式
      const fileType = [
        "jpg",
        "png",
        "gif",
        "bmp",
        "jpeg",
        "mp4",
        "3gp",
        "mkv",
        "avi",
        "mepg",
        "mpg",
        "pdf"
      ];
      return fileType.includes(suffix.toLowerCase());
    },
    toExamine_confirm_out() {
      // this.CetTable_1.queryTrigger_in = new Date().getTime();
      this.$emit("goBack", true);
    },
    CetButton_1_statusTrigger_out() {
      this.toExamine.inputData_in = this._.cloneDeep(this.inputData_in);
      this.toExamine.codes_in = this._.cloneDeep([this.inputData_in.code]);
      this.toExamine.visibleTrigger_in = new Date().getTime();
    },
    CetButton_2_statusTrigger_out() {
      const imgList = this.CetTable_4.data || [];
      if (imgList.length === 0) {
        this.$message.warning($T("暂无附件"));
        return;
      }
      imgList.forEach(item => {
        var downloadPath = item.url;
        var name = downloadPath.split("/").splice(-1)[0];
        this.downImg(downloadPath, name);
      });
    },
    // 已退回/待处理  打印工单
    CetButton_3_statusTrigger_out() {
      const newStr = document.getElementById("printContent").innerHTML;
      var wind = window.open(
        "",
        "newwindow",
        "top=0, left=0, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no"
      );
      wind.document.body.innerHTML = newStr;
      wind.print();
    },
    // 已退回/待处理  录入工单
    CetButton_4_statusTrigger_out() {
      // this.WriteOrder.inputData_in = this.inputData_in;
      this.showWriteOrder = true;
    },
    goBack() {
      // this.$router.go(-1);
      this.$emit("goBack", false);
    },
    // 列表点击
    listItemClick(item, index) {
      if (!this._.isEmpty(item)) {
        this.itemAction = index;
        this.CetTable_1.dynamicInput.scenariosId_in = item.id;
        this.CetTable_1.queryTrigger_in = new Date().getTime();
        // 如果是搜索进入需要去掉其他预案
        if (this.inputData_in.eventPlanId_in) {
          this.CetTable_1.dataConfig.filters = [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" },
            { name: "id_in", operator: "EQ", prop: "id" }
          ];
          this.CetTable_1.dynamicInput.id_in = this.inputData_in.eventPlanId_in;
        } else {
          this.CetTable_1.dataConfig.filters = [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ];
        }
        // 往新增、编辑弹框传入场景id
        this.CetButton_1.disable_in = false;
        this.CetButton_2.disable_in = false;
      } else {
        this.CetButton_1.disable_in = true;
        this.CetButton_2.disable_in = true;
      }
    },
    filData(val, type) {
      if ([null, undefined, NaN].includes(val)) {
        return "--";
      } else if (type === "number") {
        return Number(val).toFixed(2);
      } else if (type === "date") {
        return this.$moment(val).format("YYYY-MM-DD HH:mm:ss");
      } else {
        return val;
      }
    },
    handleClick_see_out(data) {
      const uploadPath = data.url;
      if (!uploadPath || !this.isPreview(data)) {
        return;
      }
      const suffix = uploadPath.split(".") || [];
      if (suffix[suffix.length - 1].toLowerCase() === "pdf") {
        var url = "/eem-service/v1/common/downloadFile?path=" + data.url;
        common.pdfPreview(url, {}, this.token);
      } else {
        this.flowChart.inputData_in = this._.cloneDeep(data);
        this.flowChart.visibleTrigger_in = new Date().getTime();
      }
    },
    handleClick_download_out(data) {
      var downloadPath = data.url;
      var name = downloadPath.split("/").splice(-1)[0];

      this.downImg(downloadPath, name);
    },
    downImg: function (downloadPath, name) {
      if (!downloadPath) {
        return;
      }
      var url =
        "/eem-service/v1/common/downloadFile?path=" +
        encodeURIComponent(downloadPath);
      const params = {};
      common.generateImg(url, params, "GET").then(res => {
        if (res.status === 200 && res.data.type === "application/x-download") {
          //下载附件图片
          FileSaver.saveAs(res.data, name);
        }
      });
    },
    //获取流程图图片
    getImgUrl: function (code) {
      var me = this;
      if (!code) {
        return;
      }
      // 主题色
      const currentTheme = localStorage.getItem("omega_theme");
      const isLightStyle = currentTheme === "light";
      var url = `/eem-service/v1/workorder/maintenance/workOrder/processDiagram/${code}?isLightStyle=${isLightStyle}`;

      const params = {};
      common.generateImg(url, params, "GET").then(res => {
        if (res.status === 200 && res.data.type === "image/png") {
          //将图片信息放到Img中
          me.imgSrc = window.URL.createObjectURL(res.data);
        }
      });
    },
    handTab_out(val) {
      this.isTab = val;
    },
    //获取巡检方案详情
    querySchemeDetailXJ_out(callback) {
      const _this = this;
      const params = {
        inspectionschemeid: _this.inputData_in.inspectionschemeid
      };
      if (!_this.inputData_in.inspectionschemeid) {
        return;
      }
      customApi.querySchemeDetailXJ(params).then(res => {
        if (res.code === 0) {
          const analogQuantity = this._.get(res, "data.analogQuantity", []);
          _this.CetTable_1.data = analogQuantity;
        }
      });
    },
    //过滤状态量状态
    statusFormatter(row) {
      if (row.status) {
        return $T("正常");
      } else if ([null, undefined, NaN].includes(row.status)) {
        return "--";
      } else {
        return $T("异常");
      }
    },
    //过滤状态量样式
    statusStyleFormatter(row) {
      if (row.status) {
        return {};
      } else if ([null, undefined, NaN].includes(row.status)) {
        return {};
      } else {
        return {
          color: "red"
        };
      }
    },
    formatterExecutor(row, column, cellValue) {
      return cellValue && cellValue.length ? cellValue.join("、") : "--";
    },
    goBackDetail() {
      this.activeName = "maintenance";
      this.showWriteOrder = false;
    },
    WriteOrderConfirm_out(flag) {
      this.$emit("goBack", flag);
    }
  },
  created: function () {},
  mounted: function () {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  padding: 0 10px 0 20px;
  box-sizing: border-box;
}
.header {
  .lhHm {
    @include line_height(Hm);
  }
  .goBack {
    display: inline-block;
    @include font_color(ZS);
    cursor: pointer;
  }
}

.listItem {
  box-sizing: border-box;
  @include padding(0 J1);
  @include margin_bottom(J1);
  line-height: 1.5;
}
.btnline {
  display: inline-block;
  position: relative;
  height: 14px;
  width: 1px;
  @include background_color(T1);
  top: 2px;
}
.btn_item {
  cursor: pointer;
  @include font_color(T1);
  &.active {
    @include font_color(ZS);
  }
}
.description-icon {
  height: 120px;
  overflow-y: auto;
  line-height: 1.5;
  box-sizing: border-box;
  width: 100%;
  @include background_color(BG1);
  background-image: none;
  border: 1px solid;
  @include padding(J1);
  @include border_color(B2);
  @include border_radius(C);
}
.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.clickformore {
  cursor: pointer;
  text-decoration: underline;
}
.preview {
  cursor: pointer;
}
.disabled {
  color: #c0c4cc;
}
.el-container_tabs {
  height: calc(100% - 2px);
  display: flex;
  flex-direction: column;
  min-height: 550px;
  overflow: auto;
  &:deep(.el-tabs__content) {
    flex: 1;
  }
}
.el-tab_row {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
