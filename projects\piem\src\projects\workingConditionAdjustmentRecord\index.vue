<template>
  <div class="page">
    <el-container class="fullheight">
      <el-aside width="320px" class="bg1 pJ3 brC">
        <!-- left组件 -->
        <CetGiantTree
          v-bind="CetGiantTree_left"
          v-on="CetGiantTree_left.event"
        ></CetGiantTree>
      </el-aside>
      <el-main style="height: 100%" class="bg1 pJ4 brC mlJ3">
        <div class="head">
          <span class="common-title-H3 lh32">{{ currentNode.name }}</span>
          <ElInput
            class="flex-end"
            v-model="ElInput_search.value"
            v-bind="ElInput_search"
            v-on="ElInput_search.event"
          ></ElInput>
          <customElSelect
            class="mlJ1"
            v-model="ElSelect_adjustmentMethod.value"
            v-bind="ElSelect_adjustmentMethod"
            v-on="ElSelect_adjustmentMethod.event"
            :prefix_in="$T('调节方式')"
          >
            <ElOption
              v-for="item in ElOption_adjustmentMethod.options_in"
              :key="item[ElOption_adjustmentMethod.key]"
              :label="item[ElOption_adjustmentMethod.label]"
              :value="item[ElOption_adjustmentMethod.value]"
              :disabled="item[ElOption_adjustmentMethod.disabled]"
            ></ElOption>
          </customElSelect>
          <CustomElDatePicker
            class="mlJ1"
            :prefix_in="$T('筛选时段')"
            :clearable="false"
            style="width: 300px"
            v-model="queryTime"
            type="daterange"
            :placeholder="$T('选择时间')"
            @change="changeQueryTime"
            value-format="timestamp"
            :picker-options="pickerOptions"
          />
          <!-- addRecord按钮组件 -->
          <CetButton
            class="mlJ1"
            v-bind="CetButton_addRecord"
            v-on="CetButton_addRecord.event"
          ></CetButton>
        </div>
        <div class="content">
          <CetTable
            class="cet-table"
            :data.sync="CetTable_scheme.data"
            :dynamicInput.sync="CetTable_scheme.dynamicInput"
            v-bind="CetTable_scheme"
            v-on="CetTable_scheme.event"
          >
            <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
            <template v-for="(item, index) in ColumnList">
              <ElTableColumn
                :key="index"
                v-bind="item"
                v-if="item.prop === 'adjustmentType'"
              >
                <template slot-scope="scope">
                  <el-tag
                    :type="
                      formatterAdjustmentMethodClass(scope.row.adjustmentType)
                    "
                  >
                    {{ formatterAdjustmentMethod(scope.row.adjustmentType) }}
                  </el-tag>
                </template>
              </ElTableColumn>
              <ElTableColumn :key="index" v-bind="item" v-else></ElTableColumn>
            </template>
            <ElTableColumn v-bind="ElTableColumn_operation">
              <template slot-scope="scope">
                <el-link
                  class="mrJ3"
                  :underLine="false"
                  type="primary"
                  @click="handleEditRecord(scope.row)"
                  :disabled="
                    scope.row.createTime + 24 * 60 * 60 * 1000 < Date.now()
                  "
                >
                  {{ $T("编辑") }}
                </el-link>
                <el-link
                  class="mrJ3"
                  :underLine="false"
                  type="primary"
                  @click="handleRecordDetails(scope.row)"
                >
                  {{ $T("详情") }}
                </el-link>
                <el-link
                  :underLine="false"
                  type="danger"
                  @click="handleDeleteRecord(scope.row)"
                  :disabled="
                    scope.row.createTime + 24 * 60 * 60 * 1000 < Date.now()
                  "
                >
                  {{ $T("删除") }}
                </el-link>
              </template>
            </ElTableColumn>
          </CetTable>
          <div class="foot">
            <el-pagination
              class="flex-end"
              :current-page="currentPage"
              :page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-main>
    </el-container>
    <addOrEditRecord v-bind="addOrEditRecord" v-on="addOrEditRecord.event" />
    <recordDetails
      ref="detailsDialog"
      v-bind="recordDetails"
      v-on="recordDetails.event"
    />
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import addOrEditRecord from "./dialogs/addOrEditRecord.vue";
import recordDetails from "./dialogs/recordDetails.vue";
import common from "eem-utils/common.js";
import moment from "moment";
const SHORT_CUTS = [
  {
    text: "今天",
    typeID: 1,
    unit: "d",
    number: 1,
    onClick(picker) {
      console.log("picker", picker);

      const end = moment().endOf("d").valueOf() + 1;
      const start = moment().startOf("d").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: "昨天",
    typeID: 2,
    unit: "d",
    number: 1,
    onClick(picker) {
      const end = moment().startOf("d").valueOf();
      const start = moment().startOf("d").add(-1, "d").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: "最近3天",
    typeID: 3,
    unit: "d",
    number: 3,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-2, "d").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: "最近一周",
    typeID: 4,
    unit: "w",
    number: 1,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-6, "d").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: "最近一个月",
    typeID: 5,
    unit: "M",
    number: 1,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-1, "M").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: "最近三个月",
    typeID: 6,
    unit: "M",
    number: 3,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-3, "M").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: "最近半年",
    typeID: 7,
    unit: "M",
    number: 6,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-6, "M").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: "最近1年",
    typeID: 8,
    unit: "y",
    number: 1,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-1, "y").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  }
];
export default {
  name: "workingConditionAdjustmentRecord",
  components: { addOrEditRecord, recordDetails },
  data() {
    return {
      currentNode: {},
      gascompressorList: [],
      queryTime: [
        this.$moment().startOf("M").valueOf(),
        this.$moment().endOf("M").valueOf()
      ],
      pickerOptions: {
        shortcuts: SHORT_CUTS
      },
      // left组件
      CetGiantTree_left: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_left_currentNode_out //选中单行输出
        }
      },
      // search组件
      ElInput_search: {
        value: "",
        placeholder: $T("请输入内容"),
        "prefix-icon": "el-icon-search",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElInput_search_change_out
        }
      },
      // deviceType组件
      ElSelect_adjustmentMethod: {
        value: null,
        style: {
          width: "250px"
        },
        event: {
          change: this.ElSelect_adjustmentMethod_change_out
        }
      },
      // deviceType组件
      ElOption_adjustmentMethod: {
        options_in: [
          {
            id: null,
            text: $T("全部")
          }
        ].concat(
          this.$store.state.enumerations.gascompressoradjustablemode?.filter(
            item => [0, 1, 2].includes(item.id)
          )
        ),
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // addRecord组件
      CetButton_addRecord: {
        visible_in: true,
        disable_in: false,
        title: $T("新增记录"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_addRecord_statusTrigger_out
        }
      },
      currentPage: 1,
      pageSize: 20,
      total: 0,
      // scheme表格组件
      CetTable_scheme: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        border: false,
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      },
      // index组件
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: $T("序号"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "60" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ColumnList: [
        {
          prop: "name", // 支持path a[0].b
          label: $T("设备名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true
        },
        {
          prop: "adjustmentType", // 支持path a[0].b
          label: $T("调节方式"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "140" //绝对宽度
        },
        {
          prop: "effectiveTime", // 支持path a[0].b
          label: $T("工况生效时间"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "140", //绝对宽度
          formatter: common.formatDateCol("YYYY-MM-DD HH:mm")
        },
        {
          prop: "adjustedCondition", // 支持path a[0].b
          label: $T("调节后工况"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "140", //绝对宽度
          formatter: this.formatterAdjustedCondition
        },
        {
          prop: "preRegulationCondition", // 支持path a[0].b
          label: $T("调节前工况"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formatterAdjustedCondition
        },
        {
          prop: "operator", // 支持path a[0].b
          label: $T("创建人"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true
        },
        {
          prop: "createTime", // 支持path a[0].b
          label: $T("创建时间"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "140", //绝对宽度
          formatter: common.formatDateCol("YYYY-MM-DD HH:mm")
        }
      ],
      // operation组件
      ElTableColumn_operation: {
        // type: "", // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "180" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      addOrEditRecord: {
        openTrigger_in: Date.now(),
        isEdit: false,
        inputData_in: {},
        event: {
          saveData_out: this.CetDialog_pagedialog_saveData_out
        }
      },
      recordDetails: {
        openTrigger_in: Date.now(),
        inputData_in: {},
        event: {
          handleEditRecord: this.handleEditRecord
        }
      }
    };
  },
  watch: {},
  methods: {
    /**
     * 获取节点树
     */
    getTreeData() {
      const params = {
        rootID: 0,
        rootLabel: "project",
        subLayerConditions: [
          {
            modelLabel: "gasgatheringstation"
          },
          {
            modelLabel: "dehydratingstation"
          },
          {
            modelLabel: "gascompressor",
            filter: {
              composemethod: true,
              expressions: [
                {
                  limit: 2,
                  operator: "EQ",
                  prop: "principletype",
                  tagid: 1
                },
                {
                  limit: 1,
                  operator: "EQ",
                  prop: "compressortype",
                  tagid: 1
                }
              ]
            }
          }
        ],
        treeReturnEnable: true
      };
      customApi.getNodeTree(params).then(res => {
        const data = res.data || [];
        this.CetGiantTree_left.inputData_in = data;
        const flatData = this.flatTreeData(data);
        const node = flatData?.find(item => item.modelLabel === "project");
        this.CetGiantTree_left.selectNode = node || {};
      });
    },

    /**
     * 拍平节点树数据
     */
    flatTreeData(treeData) {
      const cloneData = this._.cloneDeep(treeData);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },

    /**
     * 点击节点
     */
    CetGiantTree_left_currentNode_out(val) {
      this.currentNode = _.cloneDeep(val);
      let gascompressorList = [];
      this.getChildren(val, gascompressorList);
      this.gascompressorList = gascompressorList;
      this.currentPage = 1;
      this.queryTableData();
    },

    /**
     * 获取压缩机id
     */
    getChildren(val, list) {
      if (val.modelLabel === "gascompressor") {
        list.push(val);
      } else {
        val.children?.forEach(item => {
          if (item.modelLabel === "gascompressor") {
            list.push(item);
          } else {
            this.getChildren(item, list);
          }
        });
      }
    },

    /**
     * 查询表格数据
     */
    async queryTableData() {
      const params = {
        name: this.ElInput_search.value,
        adjustmentType: this.ElSelect_adjustmentMethod.value,
        gascompressorIds: this.gascompressorList?.map(item => item.id),
        startTime: this.queryTime[0],
        endTime: this.$moment(this.queryTime[1]).endOf("d").valueOf() + 1,
        page: {
          index: (this.currentPage - 1) * this.pageSize,
          limit: this.pageSize
        }
      };
      await customApi
        .queryWorkingConditionAdjustmentRecord(params)
        .then(res => {
          this.CetTable_scheme.data = res.data || [];
          this.total = res.total || 0;
        });
    },

    /**
     * 分页大小变化
     */
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.queryTableData();
    },

    /**
     * 分页当前页变化
     */
    handleCurrentChange(val) {
      this.currentPage = val;
      this.queryTableData();
    },

    /**
     * 关键字搜索
     */
    ElInput_search_change_out(val) {
      this.currentPage = 1;
      this.queryTableData();
    },

    /**
     * 切换调节方式
     */
    ElSelect_adjustmentMethod_change_out(val) {
      this.currentPage = 1;
      this.queryTableData();
    },

    /**
     * 切换筛选时段
     */
    changeQueryTime(val) {
      this.currentPage = 1;
      this.queryTableData();
    },

    /**
     * 新增工况调整记录
     */
    CetButton_addRecord_statusTrigger_out() {
      this.addOrEditRecord.isEdit = false;
      this.addOrEditRecord.openTrigger_in = Date.now();
    },

    /**
     * 编辑工况调整记录
     */
    handleEditRecord(val) {
      this.addOrEditRecord.isEdit = true;
      this.addOrEditRecord.inputData_in = val;
      this.addOrEditRecord.openTrigger_in = Date.now();
    },

    /**
     * 编辑成功-刷新表格
     */
    async CetDialog_pagedialog_saveData_out() {
      await this.queryTableData();
      if (this.$refs.detailsDialog?.$refs.drawer?.drawerShow) {
        const id = this.recordDetails.inputData_in?.id;
        const newInputData_in = this.CetTable_scheme.data?.find(
          item => item.id === id
        );
        this.handleRecordDetails(newInputData_in);
      }
    },

    /**
     * 工况调整记录详情
     */
    handleRecordDetails(val) {
      this.recordDetails.inputData_in = val;
      this.recordDetails.openTrigger_in = Date.now();
    },

    /**
     * 删除工况调整记录
     */
    handleDeleteRecord(val) {
      this.$confirm($T("确定要删除该工况调整记录吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          customApi
            .deleteWorkingConditionAdjustmentRecord([val.id])
            .then(res => {
              if (res.code === 0) {
                this.$message.success($T("删除成功"));
                this.queryTableData();
              }
            });
        })
        .catch(e => {});
    },

    /**
     * 调节方式转换
     */
    formatterAdjustmentMethod(val) {
      const list = this.$store.state.enumerations.gascompressoradjustablemode;
      const obj = list.find(item => item.id === val);
      return obj?.text || "--";
    },

    /**
     * 调节方式样式
     */
    formatterAdjustmentMethodClass(val) {
      if (val === 0) {
        return "oil2";
      } else if (val === 1) {
        return "oil4";
      } else {
        return "oil3";
      }
    },

    /**
     * 调节前后工况转换
     */
    formatterAdjustedCondition(row, column, cellValue) {
      let result;
      if (row.adjustmentType === 2) {
        result = this.formatterCylinderOperationMode(cellValue);
      } else {
        let text = row.adjustmentType === 0 ? $T("回流开度") : $T("余隙开度");
        result = cellValue || cellValue === 0 ? text + cellValue + "%" : "--";
      }
      return result;
    },

    /**
     * 气缸作用方式
     */
    formatterCylinderOperationMode(val) {
      const list = [
        {
          id: 0,
          text: $T("单作用")
        },
        {
          id: 1,
          text: $T("双作用")
        },
        {
          id: 2,
          text: $T("单双可调")
        },
        {
          id: 3,
          text: $T("单单作用")
        },
        {
          id: 4,
          text: $T("双双作用")
        }
      ];
      const obj = list.find(item => item.id === val);
      return obj?.text || "--";
    }
  },
  mounted() {
    this.getTreeData();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.head {
  display: flex;
  height: 32px;
  line-height: 32px;
  margin-bottom: 16px;
}
.content {
  height: calc(100% - 96px);
}
.foot {
  display: flex;
  height: 32px;
  line-height: 32px;
  margin-top: 16px;
}
.flex-end {
  margin-left: auto;
  justify-content: flex-end;
}
.cet-table :deep(.el-tag--oil2) {
  @include font_color(oil2);
  @include background_color(BG_oil2);
  @include border_color(BG_oil2);
}
.cet-table :deep(.el-tag--oil3) {
  @include font_color(oil3);
  @include background_color(BG_oil3);
  @include border_color(BG_oil3);
}
.cet-table :deep(.el-tag--oil4) {
  @include font_color(oil4);
  @include background_color(BG_oil4);
  @include border_color(BG_oil4);
}
</style>
