<template>
  <div class="page" :class="isMultiple ? 'multiple' : 'single'">
    <div class="head">
      <div class="title">{{ $T("设备调参建议输出") }}</div>
    </div>
    <div
      style="line-height: 32px; height: calc(100% - 40px)"
      v-html="suggestionData"
    ></div>
  </div>
</template>

<script>
export default {
  name: "suggestionOutput",
  components: {},
  props: {
    suggestionData: {
      type: String
    },
    isMultiple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  watch: {},
  methods: {}
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  border-radius: 8px;
  :deep(.value) {
    @include font_color(oil6);
    font-weight: 600;
    margin: 0 4px;
  }
}
.multiple {
  background: url("../../../assets/bg-purple-row.png") no-repeat;
  background-size: cover;
}
.single {
  background: url("../../../assets/bg-purple.png") no-repeat;
  background-size: cover;
}
.head {
  height: 32px;
  line-height: 32px;
  margin-bottom: 8px;
  .title {
    font-size: 16px;
    font-weight: bold;
  }
}
.flex-end {
  margin-left: auto;
  justify-content: flex-end;
}
.flex1 {
  flex: 1;
}
</style>
