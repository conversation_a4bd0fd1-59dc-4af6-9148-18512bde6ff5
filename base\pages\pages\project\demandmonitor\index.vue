<template>
  <div class="page eem-common">
    <el-container style="height: 100%">
      <el-aside class="eem-aside brC3 flex-column" style="width: 315px">
        <div class="mbJ1 multipleSelect">
          <customElSelect
            v-model="ElSelect_1.value"
            v-bind="ElSelect_1"
            v-on="ElSelect_1.event"
            :prefix_in="$T('计费方式')"
          >
            <ElOption
              v-for="item in ElOption_1.options_in"
              :key="item[ElOption_1.key]"
              :label="item[ElOption_1.label]"
              :value="item[ElOption_1.value]"
              :disabled="item[ElOption_1.disabled]"
            ></ElOption>
          </customElSelect>
        </div>
        <div style="flex: 1" class="minWH">
          <CetTree
            :selectNode.sync="CetTree_1.selectNode"
            :checkedNodes.sync="CetTree_1.checkedNodes"
            v-bind="CetTree_1"
            v-on="CetTree_1.event"
          ></CetTree>
        </div>
      </el-aside>
      <el-container
        class="fullheight padding0 mlJ3"
        style="flex-direction: column"
      >
        <MonitorDetail
          ref="monitorDetail"
          :currentNode="currentNode"
          :parentNodes="parentNodes"
          :copyTreeData="copyTreeData"
          @changeNode="changeNode"
          :demandMenu="demandMenu"
        ></MonitorDetail>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import MonitorDetail from "./components/monitordetail/MonitorDetail";
import { httping } from "@omega/http";

export default {
  name: "demandMonitor",
  components: {
    MonitorDetail
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    }
  },

  data() {
    return {
      currentNode: null,
      parentNodes: null, // 选中节点的父节点
      demandMenu: "", // 需量监测选中的tab
      copyTreeData: null,
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out,
          parentList_out: this.CetTree_1_parentList_out,
          checkedNodes_out: this.CetTree_1_checkedNodes_out,
          halfCheckNodes_out: this.CetTree_1_halfCheckNodes_out,
          allCheckNodes_out: this.CetTree_1_allCheckNodes_out
        }
      },
      ElSelect_1: {
        value: [1, 2],
        style: {},
        multiple: true,
        collapseTags: true,
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [
          {
            id: 1,
            text: $T("容量计费")
          },
          {
            id: 2,
            text: $T("需量计费")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      }
    };
  },
  watch: {
    "ElSelect_1.value": function (val, old) {
      if (val.length == 0) {
        // 控制最少选择一个计费方式
        this.$message({
          message: $T("至少选择一种计费方式"),
          type: "warning"
        });
        this.ElSelect_1.value = old;
      } else {
        this.sliceTree(this.copyTreeData);
      }
    }
  },

  methods: {
    ElSelect_1_change_out(val) {},
    CetTree_1_checkedArray_out(val) {},

    CetTree_1_currentNode_out(val) {
      this.currentNode = val;
    },
    CetTree_1_parentList_out(val) {
      this.parentNodes = val;
    },
    CetTree_1_checkedNodes_out(val) {},
    CetTree_1_halfCheckNodes_out(val) {},
    CetTree_1_allCheckNodes_out(val) {},
    // 获取节点树
    getTree() {
      this.CetTree_1.inputData_in = [];
      this.copyTreeData = [];
      httping({
        url: "/eem-service/v1/demand/node/getDemandNodes",
        method: "POST",
        data: [this.projectId]
      }).then(response => {
        if (response.code == 0 && response.data) {
          this.copyTreeData = this._.cloneDeep(response.data);
          this.sliceTree(this.copyTreeData);
          if (!this._.isEmpty(this.$route.params)) {
            this.changeNode(this.$route.params);
          }
        }
      });
    },
    // 获取第一个节点
    findNode(arr) {
      if (arr && arr.length > 0) {
        if (arr[0].children && arr[0].children.length > 0) {
          return this.findNode(arr[0].children);
        } else {
          return arr[0];
        }
      }
    },
    // 过滤节点
    sliceTree(treeData) {
      if (treeData && treeData.length > 0 && this.ElSelect_1.value) {
        if (this.ElSelect_1.value.length == 0) {
          this.CetTree_1.inputData_in = [];
        } else if (this.ElSelect_1.value.length == 1) {
          const nodes = [];
          // 计费方式
          treeData.forEach((item, index) => {
            var obj = this._.cloneDeep(item);
            obj.children = [];
            nodes.push(obj);
            if (item.children && item.children.length > 0) {
              item.children.forEach((ite, inde) => {
                var obj = this._.cloneDeep(ite);
                obj.children = [];
                nodes[index].children.push(obj);
                if (ite.children && ite.children.length > 0) {
                  ite.children.forEach(i => {
                    if (i.chargingWay == this.ElSelect_1.value[0]) {
                      nodes[index].children[inde].children.push(i);
                    }
                  });
                }
              });
            }
          });
          this.CetTree_1.inputData_in = this._.cloneDeep(nodes);
        } else {
          this.CetTree_1.inputData_in = this._.cloneDeep(treeData);
        }
        if (
          this.CetTree_1.inputData_in &&
          this.CetTree_1.inputData_in[0].children
        ) {
          this.CetTree_1.selectNode = {
            id: this.CetTree_1.inputData_in[0].children[0].id,
            modelLabel: this.CetTree_1.inputData_in[0].children[0].modelLabel,
            tree_id:
              this.CetTree_1.inputData_in[0].children[0].modelLabel +
              "_" +
              this.CetTree_1.inputData_in[0].children[0].id
          };
        }
      }
    },
    // 获取节点下的所有进线id
    getChildrenDevice(node, obj) {
      if (node.modelLabel == "demandaccount") {
        // 找到设备
        if (obj[node.modelLabel]) {
          if (
            obj[node.modelLabel].indexOf({
              id: node.id,
              name: node.name,
              chargingWay: node.chargingWay
            }) == -1
          ) {
            obj[node.modelLabel].push({
              id: node.id,
              name: node.name,
              chargingWay: node.chargingWay
            });
          }
        } else {
          obj[node.modelLabel] = [
            {
              id: node.id,
              name: node.name,
              chargingWay: node.chargingWay
            }
          ];
        }
      } else {
        if (node.children && node.children.length > 0) {
          node.children.forEach(item => {
            this.getChildrenDevice(item, obj);
          });
        }
      }
    },
    // 改变选中节点
    changeNode(val) {
      if (val.accountId) {
        // 需量报告的跳转
        this.CetTree_1.selectNode = {
          id: val.accountId,
          modelLabel: "demandaccount",
          tree_id: "demandaccount_" + val.accountId
        };
        this.demandMenu = $T("历史趋势");
      } else if (val.toAlForecast) {
        // 申报建议的跳转
        this.CetTree_1.selectNode = {
          id: val.id,
          modelLabel: "demandaccount",
          tree_id: "demandaccount_" + val.id
        };
        this.demandMenu = $T("Al预测");
      } else if (val.id) {
        // 需量检测的跳转
        this.CetTree_1.selectNode = {
          id: val.id,
          modelLabel: "demandaccount",
          tree_id: "demandaccount_" + val.id
        };
        this.demandMenu = $T("实时概况");
      } else {
        this.CetTree_1.selectNode = this.findNode(this.copyTreeData);
        this.demandMenu = $T("实时概况");
      }
    }
  },
  created: function () {},
  mounted: function () {},
  activated: function () {
    this.ElSelect_1.value = [1, 2];
    this.getTree();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
// el-select多选以tag展示时，超过显示长度以...省略号显示
.multipleSelect {
  :deep(.el-select__tags-text) {
    display: inline-block;
    max-width: 90px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  :deep(.el-tag__close) {
    top: -5px;
  }
}
</style>
