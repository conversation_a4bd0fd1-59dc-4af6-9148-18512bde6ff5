<template>
  <div class="page fullfilled flex-row eem-common">
    <div class="eem-aside treeBox">
      <CetGiantTree
        class="fullheight"
        v-bind="CetGiantTree_1"
        v-on="CetGiantTree_1.event"
      ></CetGiantTree>
    </div>
    <div class="mlJ3 fullheight flex-column flex-auto">
      <div class="mbJ3 text-ellipsis">
        <el-tooltip
          effect="light"
          :content="currentNode && currentNode.name"
          placement="bottom"
        >
          <span class="common-title-H1">
            {{ currentNode && currentNode.name }}
          </span>
        </el-tooltip>
      </div>
      <div class="bg1 pJ3 plJ4 prJ4 flex-auto flex-column">
        <div class="mbJ3 clearfix">
          <span class="common-title-H2 fl contentTitle">
            {{ $T("已关联的管网设备") }}
          </span>
          <div class="fr">
            <el-dropdown
              class="fr"
              split-button
              type="primary"
              @command="dropdownClick"
            >
              {{ $T("更多") }}
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="export">
                  {{ $T("导出供能关系") }}
                </el-dropdown-item>
                <el-dropdown-item command="import">
                  {{ $T("导入供能关系") }}
                </el-dropdown-item>
                <el-dropdown-item command="shareRate">
                  {{ $T("分摊系数") }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <CetButton
              class="fr mrJ1"
              v-bind="CetButton_add"
              v-on="CetButton_add.event"
            ></CetButton>
            <CetButton
              class="fr mrJ1"
              v-bind="CetButton_del"
              v-on="CetButton_del.event"
              :disable_in="checkedDataIds.length ? false : true"
            ></CetButton>
            <el-upload
              style="display: none"
              :action="importUrl"
              :headers="{
                Authorization: this.token,
                projectId: this.projectId
              }"
              :data="{}"
              :before-upload="handleBeforeUpload"
              :on-success="uploadSuccess"
              :on-error="uploadError"
              :multiple="false"
            >
              <button ref="uploadBtn"></button>
            </el-upload>
          </div>
        </div>

        <div class="flex-auto" style="overflow-y: auto">
          <el-checkbox
            :indeterminate="isIndeterminate"
            v-model="checkAll"
            @change="handleCheckAllChange"
            class="mbJ1 mlJ3"
          >
            全选
          </el-checkbox>
          <el-checkbox-group
            v-model="checkedDataIds"
            @change="handleCheckedDataIdsChange"
          >
            <div
              v-for="item in energySupplyToDataAll"
              :key="item.dataId"
              class="deviceItem mbJ1 pJ3 brC"
            >
              <el-checkbox
                :label="item.dataId"
                :key="item.dataId"
                class="checkboxItem"
              ></el-checkbox>
              <div class="deviceItemName text-ellipsis">
                {{ item.name || "--" }}
                <i
                  class="el-icon-delete deleteBtn"
                  @click="delectSupplyRelation_out(item.dataId)"
                ></i>
              </div>
              <div class="deviceItemList">
                <span
                  v-for="(
                    measuredBysItem, measuredBysIndex
                  ) in item.measuredBys"
                  :key="measuredBysIndex"
                >
                  {{ measuredBysItem.deviceName }}
                </span>
              </div>
            </div>
          </el-checkbox-group>
        </div>
      </div>
    </div>
    <shareRate
      :visibleTrigger_in="shareRate.visibleTrigger_in"
      :closeTrigger_in="shareRate.closeTrigger_in"
      :queryId_in="shareRate.queryId_in"
      :inputData_in="shareRate.inputData_in"
      :roomDeviceNodes="energySupplyToDataAll"
      :roomDeviceList="roomDeviceList"
    />
    <add
      :visibleTrigger_in="add.visibleTrigger_in"
      :closeTrigger_in="add.closeTrigger_in"
      :inputData_in="add.inputData_in"
      :roomDeviceList="roomDeviceList"
      :currentNode="currentNode"
      :deviceList="energySupplyToDataAll"
      :outlastNode="outlastNode"
      @updata_out="updataList"
    />
  </div>
</template>

<script>
import common from "eem-utils/common";
import shareRate from "./shareRate.vue";
import add from "./add.vue";
import ELECTRICAL_DEVICE from "@/store/electricaldevice.js";
import { FullScreenLoading } from "@omega/http/loading.js";
const loading = new FullScreenLoading();
import TREE_PARAMS from "@/store/treeParams.js";
import { httping } from "@omega/http";
export default {
  name: "pipeRelation",
  components: {
    shareRate,
    add
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    systemCfg() {
      return this.$store.state.systemCfg;
    }
  },
  data() {
    const roomDeviceList = [
      {
        filter: null,
        modelLabel: "linesegment",
        props: []
      }
    ];
    ELECTRICAL_DEVICE.forEach(item => {
      var obj = {
        filter: null,
        modelLabel: item.value,
        props: []
      };
      if (item.value === "linesegmentwithswitch") {
        obj.depth = 1;
      }
      roomDeviceList.push(obj);
    });
    return {
      // 配电室设备模型
      roomDeviceList: roomDeviceList,
      // 已关联配电设备的列表
      energySupplyToDataAll: [],
      currentNode: null,
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out //选中单行输出
        }
      },

      CetButton_shareRate: {
        visible_in: true,
        disable_in: true,
        title: $T("分摊系数"),
        plain: true,
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_shareRate_statusTrigger_out
        }
      },
      CetButton_add: {
        visible_in: true,
        disable_in: true,
        title: $T("添加管网设备"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      CetButton_import: {
        visible_in: true,
        disable_in: false,
        title: $T("导入供能关系"),
        plain: true,
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_import_statusTrigger_out
        }
      },
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: $T("导出供能关系"),
        plain: true,
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      CetButton_del: {
        visible_in: true,
        // disable_in: false,
        title: $T("批量删除"),
        plain: true,
        type: "danger",
        event: {
          statusTrigger_out: this.CetButton_del_statusTrigger_out
        }
      },
      shareRate: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      add: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      outlastNode: {},
      importUrl: "",
      checkAll: false,
      checkedDataIds: [],
      isIndeterminate: false
    };
  },
  watch: {
    currentNode: {
      handler(val, old) {
        // if (val && val.modelLabel != "project") {
        if (val) {
          this.CetButton_add.disable_in = false;
        } else {
          this.CetButton_add.disable_in = true;
        }
      }
    },
    energySupplyToDataAll: {
      handler(val, old) {
        if (val && val.length > 0) {
          this.CetButton_shareRate.disable_in = false;
        } else {
          this.CetButton_shareRate.disable_in = true;
        }
      }
    }
  },
  methods: {
    getTreeData() {
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.pipeRelation,
        treeReturnEnable: true
      };
      httping({
        url: "/eem-service/v1/node/nodeTree",
        method: "POST",
        data
      }).then(res => {
        if (res.code === 0) {
          _this.CetGiantTree_1.inputData_in = res.data;
          _this.CetGiantTree_1.selectNode = res.data[0];
        }
      });
    },
    // 查询关系
    querySupplyRelation_out(val) {
      if (!val) {
        return;
      }
      var me = this;
      var param;
      var url;
      param = [
        {
          modelLabel: val.modelLabel,
          id: val.id
        }
      ];
      url = `/eem-service/v1/connect/energySupplyTo/measuredBy?time=${new Date().getTime()}`;
      common.requestData(
        {
          url: url,
          data: param
        },
        (data, res) => {
          if (res.code === 0) {
            var relationData = me._.get(res, "data", []);
            // 过滤出对应的关联关系
            const checkedNodes = [];
            relationData.forEach(item => {
              checkedNodes.push({
                dataId: item.id,
                modelLabel: item.objectlabel,
                name: item.objectName,
                id: item.objectid,
                tree_id: item.objectlabel + "_" + item.objectid,
                measuredBys: item.measuredBys || []
              });
            });
            me.energySupplyToDataAll = checkedNodes;
          }
        },
        () => {
          me.energySupplyToDataAll = [];
        }
      );
    },
    // 删除关系
    delectSupplyRelation_out(val) {
      var me = this;
      me.$confirm($T("确定要删除吗？"), $T("提示"), {
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          var param = {
            idRange: me._.isArray(val) ? val : [val],
            modelLabel: "energysupplyto"
          };

          httping({
            url: `/eem-service/v1/connect/relationship`,
            data: param,
            method: "DELETE"
          }).then(
            function (res) {
              me.CetGiantTree_1_currentNode_out(me.currentNode);
              me.$message({
                message: $T("保存成功"),
                type: "success"
              });
            },
            function () {}
          );
        })
        .catch(action => {
          if (action === "cancel") {
            me.$message({
              type: "info",
              message: $T("已取消")
            });
          }
        });
    },
    // 更新已关联的列表
    updataList(param) {
      this.CetGiantTree_1_currentNode_out(this.currentNode);
      this.outlastNode = this._.cloneDeep(param);
    },
    CetGiantTree_1_currentNode_out(val) {
      if (!val) {
        return;
      }
      this.currentNode = this._.cloneDeep(val);
      // if (val.modelLabel === "project") {
      //   this.$message({
      //     message: "不能关联设备",
      //     type: "warning"
      //   });
      //   this.currentNode = null;
      //   this.energySupplyToDataAll = [];
      //   return;
      // }
      this.checkAll = false;
      this.checkedDataIds = [];
      this.isIndeterminate = false;
      this.querySupplyRelation_out(val);
    },
    dropdownClick(val) {
      switch (val) {
        case "export":
          this.CetButton_export_statusTrigger_out();
          break;
        case "import":
          this.CetButton_import_statusTrigger_out();
          break;
        case "shareRate":
          this.CetButton_shareRate_statusTrigger_out();
          break;

        default:
          break;
      }
    },
    CetButton_del_statusTrigger_out() {
      if (!this.checkedDataIds.length) {
        this.$message.warning($T("请选择节点"));
        return;
      }
      this.delectSupplyRelation_out(this.checkedDataIds);
    },
    CetButton_shareRate_statusTrigger_out(val) {
      this.shareRate.inputData_in = this._.cloneDeep(this.currentNode);
      this.shareRate.visibleTrigger_in = Date.now();
    },
    CetButton_add_statusTrigger_out(val) {
      this.add.inputData_in = this._.cloneDeep(this.currentNode);
      this.add.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 批量导入供能关系数据
    CetButton_import_statusTrigger_out(val) {
      this.importUrl = `/eem-service/v1/connect/energySupplyTo/batchImport`;
      this.$refs.uploadBtn.click();
    },
    // 导入前
    handleBeforeUpload: function (file) {
      loading.showLoading();
      if (
        file.name.indexOf(".xls") !== -1 ||
        file.name.indexOf(".xlsx") !== -1
      ) {
        var uploadDocSize = this.systemCfg.uploadDocSize || 0.5;
        const isLimit100M = file.size / 1024 / 1024 < uploadDocSize;
        if (!isLimit100M) {
          this.$message.error(
            $T("上传文件超过规定的最大上传大小{0}M", uploadDocSize)
          );
        }
        return isLimit100M;
      } else {
        loading.hideLoading();
        this.$message({
          type: "warning",
          message: $T("只能上传xls/xlsx格式文件")
        });
        return false;
      }
    },
    // 导入成功
    uploadSuccess: function (response) {
      loading.hideLoading();
      if (response.code === 0) {
        this.$message({
          message: $T("导入成功"),
          type: "success"
        });
        this.CetGiantTree_1_currentNode_out(this.currentNode);
      } else if (response.code !== 0) {
        this.$message({
          type: "error",
          message: response.msg
        });
      }
    },
    uploadError: function (response) {
      loading.hideLoading();
      this.$message({
        message: $T("导入失败"),
        type: "error"
      });
    },
    //导出功能关系数据
    CetButton_export_statusTrigger_out(val) {
      const url = `/eem-service/v1/connect/energySupplyTo/export/${this.projectId}`;

      common.downExcel(url, {}, this.token, this.projectId);
    },
    handleCheckAllChange(val) {
      if (val) {
        this.checkedDataIds = this.energySupplyToDataAll.map(i => i.dataId);
      } else {
        this.checkedDataIds = [];
      }
      this.isIndeterminate = false;
    },
    handleCheckedDataIdsChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.energySupplyToDataAll.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.energySupplyToDataAll.length;
    }
  },
  mounted() {
    this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
    this.getTreeData();
  },
  activated() {
    this.outlastNode = {};
  }
};
</script>
<style lang="scss" scoped>
.page {
  .treeBox {
    width: 315px;
  }
  .contentTitle {
    @include line_height(Hm);
  }
  .deviceItem {
    position: relative;
    @include background_color(BG12);
    .deviceItemName {
      width: 100%;
      padding-right: 20px;
      padding-left: 20px;
      box-sizing: border-box;
      @include margin_bottom(J);
      @include font_size(H3);
      .deleteBtn {
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        @include font_color(Sta3);
      }
    }
    .deviceItemList {
      width: 100%;
      @include font_size(Ab);
      overflow-y: auto;
      max-height: 50px;
      padding-right: 20px;
      padding-left: 20px;
      box-sizing: border-box;
      & > span {
        margin-right: 5px;
      }
    }
    .checkboxItem {
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      :deep(.el-checkbox__label) {
        display: none;
      }
    }
  }
}
</style>
