import fetch from "eem-utils/fetch";
import { http, HttpBase } from "@omega/http";
let version = "v2";

function processRequest(data) {
  // 对 data 进行任意转换处理
  return data;
}

function processResponse(response) {
  // 对 response 进行任意转换处理, response结构
  //   {
  //     // `data` 由服务器提供的响应
  //     data: {},

  //     // `status` 来自服务器响应的 HTTP 状态码
  //     status: 200,

  //     // `statusText` 来自服务器响应的 HTTP 状态信息
  //     statusText: 'OK',

  //     // `headers` 服务器响应的头
  //     headers: {},

  //     // `config` 是为请求提供的配置信息
  //     config: {}
  //   }
  return response;
}

export function getReportTree(data) {
  return fetch({
    url: `/eem-service/v2/peccore/report/tree`,
    method: "GET",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    params: data
  });
}

export function getPecReportTree(data) {
  return fetch({
    url: `/eem-service/v2/peccore/pecReport/tree`,
    method: "GET",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    params: data
  });
}

export function getMReportTree(data) {
  return fetch({
    url: `/eem-service/v2/peccore/mReport/tree`,
    method: "GET",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    params: data
  });
}
export function getEnterpriseTree(data) {
  return fetch({
    url: `/eem-service/v2/auth/tenant/tenantNode`,
    method: "GET",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    params: data
  });
}
export function getPecCoreMeterTree(data) {
  return fetch({
    url: `/eem-service/v2/peccore/pecCoreMeterTree`,
    method: "GET",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    params: data
  });
}

export function getOnlyReportTree(params, hideNotice) {
  return fetch({
    url: `/eem-service/v2/peccore/only-report/tree`,
    method: "GET",
    headers: { hideNotice },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    params
  });
}

const option = {
  headers: {
    post: {
      "Content-Type": "application/json;charset=UTF-8 "
    }
  },
  responseType: "json"
};
const httping = new HttpBase(
  {
    rejectErrorCode: true
  },
  option
);

export function getOnlyReportTemplate(id, params, hideNotice) {
  return httping({
    url: `/only-report/api/v1/template/query/${id}`,
    method: "GET",
    headers: { hideNotice },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    params
  });
}

// 获取所有报表节点
export function getOnlyReportTreeAll(params, hideNotice) {
  return fetch({
    url: `/eem-service/v1/report/peccore/tool/tree`,
    method: "GET",
    headers: { hideNotice },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    params
  });
}
