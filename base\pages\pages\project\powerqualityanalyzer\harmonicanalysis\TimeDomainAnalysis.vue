<template>
  <el-container
    class="timeDomainAnalysisbox eem-common fullheight eem-min-width-mini"
  >
    <el-aside
      class="sub-aside flex-column"
      :class="collapseAside ? 'show-aside' : 'hide-aside'"
    >
      <div class="mbJ1">
        <div class="mbJ">相别：</div>
        <ElSelect
          v-model="ElSelect_Phase.value"
          v-bind="ElSelect_Phase"
          v-on="ElSelect_Phase.event"
        >
          <ElOption
            v-for="item in ElOption_Phase.options_in"
            :key="item[ElOption_Phase.key]"
            :label="item[ElOption_Phase.label]"
            :value="item[ElOption_Phase.value]"
            :disabled="item[ElOption_Phase.disabled]"
          ></ElOption>
        </ElSelect>
      </div>
      <div class="mbJ3">
        <div class="mbJ">数据类型：</div>
        <ElSelect
          v-model="ElSelect_dataType.value"
          v-bind="ElSelect_dataType"
          v-on="ElSelect_dataType.event"
        >
          <ElOption
            v-for="item in ElOption_dataType.options_in"
            :key="item[ElOption_dataType.key]"
            :label="item[ElOption_dataType.label]"
            :value="item[ElOption_dataType.value]"
            :disabled="item[ElOption_dataType.disabled]"
          ></ElOption>
        </ElSelect>
      </div>
      <el-tree
        class="td-point-tree flex-auto"
        style="overflow: auto"
        :data="treeData"
        show-checkbox
        node-key="treeId"
        @check-change="pointCheckChange"
        ref="pointTree"
        highlight-current
        :check-strictly="true"
        :props="treeProps"
      ></el-tree>
    </el-aside>
    <div class="page-main eem-cont" :style="pageMainStyle">
      <div class="collapse-aside" @click="handleAside">
        <i
          :class="collapseAside ? 'el-icon-arrow-left' : 'el-icon-arrow-right'"
        ></i>
      </div>
      <el-header height="auto" class="clearfix padding0">
        <CetButton
          class="fr"
          v-bind="CetButton2_export"
          v-on="CetButton2_export.event"
        ></CetButton>

        <div class="fr mrJ1" style="width: 380px">
          <time-range
            :val.sync="queryTime"
            :typeIds="[1, 2, 3, 4, 5]"
            :onPick="onPick"
            :disabledDate="disabledDate"
          ></time-range>
        </div>

        <CetButton
          class="fr mrJ1"
          v-bind="CetButton2_restore"
          v-on="CetButton2_restore.event"
        ></CetButton>
        <CetButton
          class="fr mrJ1"
          v-bind="CetButton2_clear"
          v-on="CetButton2_clear.event"
        ></CetButton>
      </el-header>
      <div class="chartlinebox">
        <div class="chartlinebox-main">
          <CetChart ref="chartLine" v-bind="CetChart2_line"></CetChart>
        </div>
      </div>
    </div>
  </el-container>
</template>
<script>
import TimeRange from "eem-components/TimeRange";
import common from "eem-utils/common";
import ELECTRICAL_DEVICE from "@/store/electricaldevice.js";
const pointIcon =
  "M512 960c-235.639467 0-426.666667-191.0272-426.666667-426.666667S276.360533 106.666667 512 106.666667s426.666667 191.0272 426.666667 426.666666-191.0272 426.666667-426.666667 426.666667z m0-320c58.909867 0 106.666667-47.7568 106.666667-106.666667s-47.7568-106.666667-106.666667-106.666666-106.666667 47.7568-106.666667 106.666666 47.7568 106.666667 106.666667 106.666667z";

// 勾选的测点类型
const PointAlias = new Map([
  [1, "V"],
  [2, "I"]
]);
import { httping } from "@omega/http";

export default {
  name: "TimeDomainAnalysis",
  components: {
    TimeRange
  },
  props: {
    currentNode: Object,
    refreshTrigger_in: {
      type: [Number]
    },
    token: {
      type: String
    }
  },
  computed: {
    pageMainStyle() {
      return {
        marginLeft: this.collapseAside ? "0" : "-300px",
        width: this.collapseAside ? "calc(100% - 300px)" : "100%"
      };
    },
    modelLabels() {
      return ELECTRICAL_DEVICE.map(i => i.value);
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  watch: {
    currentNode: {
      deep: true,
      handler: function (val, oldVal) {
        if (
          !(this._.get(val, "data.id") && this._.get(val, "data.modelLabel"))
        ) {
          return;
        }
        if (
          val.data.id === this._.get(oldVal, "data.id") &&
          val.data.modelLabel === this._.get(oldVal, "data.modelLabel")
        ) {
          return;
        }

        this.collapseAside = val.data.modelLabel;
        var _this = this;
        if (_this._.get(val, "isLeaf")) {
          _this.$nextTick(function () {
            _this.$refs.pointTree.setCheckedNodes([]);
            _this.source = {};
            _this.getPecDevice();
          });
        }
      },
      immediate: true
    },
    refreshTrigger_in: {
      deep: true,
      handler: function (val, oldVal) {
        var _this = this;
        _this.$nextTick(function () {
          _this.$refs.pointTree.setCheckedNodes([]);
          _this.source = {};
        });
      }
    },
    queryTime: {
      deep: true,
      handler: function (val, oldVal) {
        this.pointStore.forEach(point => {
          this.getData(point);
        });
      }
    },
    source: {
      deep: true,
      handler(val) {
        this.formatChartData(val);
      }
    },
    queryParams: {
      deep: true,
      handler: function (val, oldVal) {
        this.CetButton2_export.disable_in = !this._.keys(val).length;
      }
    }
  },
  data(vm) {
    return {
      //相别
      // Phase组件
      ElSelect_Phase: {
        value: 1,
        size: "small",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_Phase_change_out
        }
      },
      // Phase组件
      ElOption_Phase: {
        options_in: [
          {
            id: 1,
            text: "A相",
            alias: "a"
          },
          {
            id: 2,
            text: "B相",
            alias: "b"
          },
          {
            id: 3,
            text: "C相",
            alias: "c"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },

      // 数据类型
      // dataType组件
      ElSelect_dataType: {
        value: 2,
        size: "small",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_dataType_change_out
        }
      },
      // dataType组件
      ElOption_dataType: {
        options_in: [
          {
            id: 2,
            text: "最大值"
          },
          {
            id: 3,
            text: "最小值"
          },
          {
            id: 4,
            text: "平均值"
          },
          {
            id: 5,
            text: "C95值"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },

      // 清空组件
      // clear组件
      CetButton2_clear: {
        visible_in: true,
        disable_in: true,
        title: "清空",
        type: "primary",
        plain: true,
        style: {
          display: "block"
        },
        event: {
          statusTrigger_out: this.CetButton2_clear_statusTrigger_out
        }
      },
      // 还原组件
      // restore组件
      CetButton2_restore: {
        visible_in: true,
        disable_in: true,
        title: "还原",
        type: "primary",
        plain: true,
        style: {
          display: "block"
        },
        event: {
          statusTrigger_out: this.CetButton2_restore_statusTrigger_out
        }
      },
      // 曲线组件
      CetChart2_line: {
        //组件输入项
        inputData_in: null,
        options: {
          legend: {
            top: 30,
            show: true
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            }
          },

          grid: {
            top: 60,
            left: 50,
            right: 50,
            bottom: 80
          },
          toolbox: {
            feature: {
              myShowSymbol: {
                show: true,
                title: "打点",
                icon: pointIcon,
                onclick: function () {
                  const series = vm.CetChart2_line.options.series;
                  series.forEach(item => {
                    item.showSymbol = !item.showSymbol;
                  });
                }
              }
            }
          },
          dataZoom: [
            {
              type: "slider",
              left: 150,
              right: 150
            }
          ],
          xAxis: { type: "time" },
          yAxis: { type: "value", scale: true },
          series: []
        }
      },

      queryTime: common.initDateRange(),
      treeData: [],
      treeProps: {
        label: "text",
        children: "children",
        isLeaf: "leaf"
      },
      // 存储节点树勾选的测点
      pointStore: [],
      // 存储已勾选列表的测点
      pointStoreCheck: [],
      pointRestore: [],
      // 导出组件
      // export组件
      CetButton2_export: {
        visible_in: true,
        disable_in: true,
        title: "导出",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton2_export_statusTrigger_out
        }
      },
      collapseAside: true,
      source: {},
      sourceUnit: {},
      queryParams: {},
      minDate: null
    };
  },
  methods: {
    // 相角
    ElSelect_Phase_change_out(val) {
      this.$refs.pointTree.setCheckedNodes([]);
      this.source = {};
    },

    // 数据类型
    ElSelect_dataType_change_out(val) {
      this.$refs.pointTree.setCheckedNodes([]);
      this.source = {};
    },

    // 清空按钮组件
    CetButton2_clear_statusTrigger_out(val) {
      this.pointRestore = this.pointStore;
      this.pointStore = [];
      this.$refs.pointTree.setCheckedNodes([]);
      this.$nextTick(() => {
        this.CetButton2_restore.disable_in = this._.isEmpty(this.pointRestore);
      });
    },

    // 还原按钮组件
    // restore输出
    CetButton2_restore_statusTrigger_out(val) {
      this.$refs.pointTree.setCheckedNodes(this.pointRestore);
    },

    //region 导出
    CetButton2_export_statusTrigger_out(val) {
      const params = {
        dataTypes: [0],
        endTime: this.queryTime[1],
        harmTimes: [0],
        harmTypes: [0],
        interval: 0,
        node: {
          childSelectState: 0,
          children: [null],
          deviceIds: [0],
          endPoint: true,
          id: this.currentNode.data.id,
          modelLabel: this.currentNode.data.modelLabel,
          name: "时域分析",
          startPoint: true
        },
        nodeNames: ["string"],
        phaseTypes: [0],
        pictures: [this.$refs.chartLine.getDataURL()],
        projectId: this.projectId,
        queryParams: this._.values(this.queryParams),
        startTime: this.queryTime[0]
      };

      common.downExcel(
        "/eem-service/v1/pq/harmonic/historyData/export",
        params,
        this.token
      );
    },
    // 显示或者隐藏测点栏
    handleAside() {
      if (!this._.get(this.currentNode, "data.modelLabel")) {
        return;
      }
      this.collapseAside = !this.collapseAside;
    },
    generateShowText() {},
    // 获取测点
    getPoints() {
      httping({
        url: "/eem-service/v1/pq/harmonic/paramNodes",
        method: "GET"
      }).then(response => {
        if (response.code === 0) {
          const res = response.data;
          this.treeData = res;
        }
      });
    },

    // 给勾选的测点增加 对应的相别，数据类型名称
    setPointName(point) {
      const temp = this._.cloneDeep(point);
      temp.name =
        this.currentNode?.data?.name +
        "-" +
        PointAlias.get(temp.itemType) +
        this._.find(this.ElOption_Phase.options_in, {
          id: this.ElSelect_Phase.value
        }).alias +
        " " +
        temp.text +
        " " +
        this._.find(this.ElOption_dataType.options_in, {
          id: this.ElSelect_dataType.value
        }).text;
      return temp;
    },
    // 勾选测点事件
    pointCheckChange(data, isCheck) {
      this.CetButton2_restore.disable_in = true;
      const checked = this.$refs.pointTree.getCheckedNodes();
      if (checked.length <= 0) {
        this.CetButton2_clear.disable_in = true;
      } else {
        this.CetButton2_clear.disable_in = false;
      }
      if (checked.length > 4) {
        this.$message("只允许选择4个测点");
        this.$refs.pointTree.setChecked(data.treeId, false);
        return;
      }
      const point = this.setPointName(data);
      const parentLabel = this.$refs.pointTree.getNode(data).parent.label;
      const unitObj = {
        谐波含有率: "%",
        谐波相角: "°",
        谐波有效值: "A"
      };
      point.unit = unitObj[parentLabel];
      point.show = true;
      if (isCheck) {
        this.getData(point);
        this.pointStore.push(point);
        this.pointStoreCheck.push(point);
        this.$set(
          this.queryParams,
          [point.treeId],
          [
            point.harmType,
            point.harmTime,
            this.ElSelect_dataType.value,
            this.ElSelect_Phase.value
          ]
        );
      } else {
        this._.remove(this.pointStore, { treeId: point.treeId });
        this.$delete(this.queryParams, point.treeId);
        this.pointStoreCheck = this.pointStoreCheck.filter(item => {
          return item.treeId != point.treeId;
        });
        this.$delete(this.source, point.name);
      }
    },

    // 根据测点查询数据  根据性能要求，目前一次最好只查一个
    getData(point) {
      const params = {
        dataTypes: [this.ElSelect_dataType.value],
        endTime: this.queryTime[1],
        harmTimes: [point.harmTime],
        harmTypes: [point.harmType],
        interval: 1,
        node: {
          childSelectState: 0,
          children: [null],
          deviceIds: [0],
          endPoint: true,
          id: this.currentNode.data.id,
          modelLabel: this.currentNode.data.modelLabel,
          name: "",
          startPoint: true
        },
        nodeNames: [],
        phaseTypes: [0],
        pictures: [],
        projectId: this.projectId,
        queryParams: [[0]],
        startTime: this.queryTime[0]
      };

      common.requestData(
        {
          url: "/eem-service/v1/pq/harmonic/historyData",
          data: params
        },
        (data, res) => {
          this.$set(
            this.source,
            point.name,
            this._.get(res, "data[0].dataList", [])
          );
          this.$set(this.sourceUnit, point.name, point.unit);
        }
      );
    },

    formatChartData(source) {
      const names = Object.keys(source);
      const temp = [];
      names.forEach(name => {
        const data = [];
        const unit = this.sourceUnit[name];
        source[name].forEach(item => {
          if (item.value === -**********) return;
          data.push([item.time, this._.round(item.value, 2)]);
        });
        temp.push({
          type: "line",
          name: name + "(" + unit + ")",
          showSymbol: false,
          data
        });
      });

      this.CetChart2_line.options.series = [];
      const option = this._.cloneDeep(this.CetChart2_line.options);
      option.series = temp;
      this.CetChart2_line.options = option;
    },
    onPick({ maxDate, minDate }) {
      this.minDate = minDate;
    },
    disabledDate(time) {
      var date1 = this.$moment(this.minDate);
      var date2 = this.$moment(time);
      return date2.diff(date1, "days") > 35 || date2.diff(date1, "days") < -35;
    },
    getPecDevice() {
      const node = this.currentNode;
      common.requestData(
        {
          url: "/eem-service/v1/pq/deviceId?projectId=" + this.projectId,
          data: {
            id: node.data.id,
            modelLabel: node.data.modelLabel
          }
        },
        (data, res) => {
          const list = this._.get(res, "data", []);
          const deviceidObj = this._.find(list, { metertype: 9 });
          if (deviceidObj && deviceidObj.protocalname === "iMeter6") {
            this.ElSelect_dataType.value = 1;
            this.ElOption_dataType.options_in = [
              {
                id: 1,
                text: "实时值"
              }
            ];
          } else {
            this.ElSelect_dataType.value = 2;
            this.ElOption_dataType.options_in = [
              {
                id: 2,
                text: "最大值"
              },
              {
                id: 3,
                text: "最小值"
              },
              {
                id: 4,
                text: "平均值"
              },
              {
                id: 5,
                text: "C95值"
              }
            ];
          }
        }
      );
    }
  },
  created() {
    this.getPoints();
  },
  mounted() {}
};
</script>
<style lang="scss" scoped>
.sub-aside {
  position: relative;
  transition: all 0.5s;
}
.sub-aside.show-aside {
  transform: translateX(0);
}
.sub-aside.hide-aside {
  transform: translateX(-100%);
}
.page-main {
  position: relative;
  transition: all 0.5s;
  width: 100%;
}
.collapse-aside {
  position: absolute;
  left: -16px;
  top: 50%;
  width: 13px;
  height: 80px;
  line-height: 80px;
  transform: translateY(-50%);
  cursor: pointer;
  @include background_color(ZS);
}
.timeDomainAnalysisbox {
  .sub-aside {
    width: 300px;
    @include padding(J4 J3);
    @include background_color(BG1);
    @include border_radius(C);
    margin: 0px;
    @include margin_right(J3);
  }
  .chartlinebox {
    height: calc(100% - 45px);
    .chartlinebox-main {
      height: 100%;
    }
  }
}
.td-point-tree :deep() {
  .el-tree-node {
    .is-leaf + .el-checkbox .el-checkbox__inner {
      display: inline-block;
    }
    .el-checkbox .el-checkbox__inner {
      display: none;
    }
  }
}
</style>
