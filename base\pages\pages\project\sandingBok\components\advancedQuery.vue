<template>
  <div>
    <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="small">
      <el-main class="eem-cont-c1 fullheight">
        <CetForm
          :data.sync="CetForm_1.data"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
          class="form"
        >
          <el-form-item
            :label="`${item.alias}`"
            :prop="item.name"
            v-for="(item, index) in templateArr"
            :key="index"
          >
            <!-- 测量对象使用弹框 -->
            <div
              class="text img"
              :title="CetForm_1.data[item.name]"
              v-if="item.name == 'monitorname'"
            >
              <span class="text-ellipsis fl">
                {{ CetForm_1.data[item.name] || "--" }}
              </span>
              <CetButton
                class="fr"
                v-bind="CetButton_add"
                v-on="CetButton_add.event"
              ></CetButton>
            </div>
            <ElInput
              v-else-if="item.datatype == 'string'"
              v-model.trim="CetForm_1.data[item.name]"
              v-bind="ElInput_string"
              v-on="ElInput_string.event"
            ></ElInput>
            <ElInputNumber
              v-else-if="item.datatype == 'int4'"
              v-model="CetForm_1.data[item.name]"
              v-bind="ElInputNumber_num1"
              v-on="ElInputNumber_num1.event"
            ></ElInputNumber>
            <ElInputNumber
              v-else-if="item.datatype == 'int8'"
              v-model="CetForm_1.data[item.name]"
              v-bind="ElInputNumber_num1"
              v-on="ElInputNumber_num1.event"
            ></ElInputNumber>
            <ElInputNumber
              v-else-if="item.datatype == 'float'"
              v-model="CetForm_1.data[item.name]"
              v-bind="ElInputNumber_num2"
              v-on="ElInputNumber_num2.event"
            ></ElInputNumber>
            <el-date-picker
              style="width: 250px !important"
              v-else-if="item.datatype == 'date'"
              v-model="CetForm_1.data[item.name]"
              :placeholder="$T('选择日期')"
              value-format="timestamp"
              type="daterange"
              :range-separator="$T('至')"
              :start-placeholder="$T('开始日期')"
              :end-placeholder="$T('结束日期')"
            ></el-date-picker>
            <ElSelect
              v-else-if="item.datatype == 'boolean'"
              v-model="CetForm_1.data[item.name]"
              v-bind="ElSelect_boolean"
              v-on="ElSelect_boolean.event"
            >
              <ElOption
                v-for="item in ElOption_boolean.options_in"
                :key="item[ElOption_boolean.key]"
                :label="item[ElOption_boolean.label]"
                :value="item[ElOption_boolean.value]"
                :disabled="item[ElOption_boolean.disabled]"
              ></ElOption>
            </ElSelect>
            <ElSelect
              v-else-if="
                item.datatype == 'enum' ||
                (item.enumerationvalue && item.enumerationvalue.length)
              "
              v-model="CetForm_1.data[item.name]"
              v-bind="ElSelect_select"
              v-on="ElSelect_select.event"
            >
              <ElOption
                v-for="(item, index) in item.enumerationvalue"
                :key="index"
                :label="item.text"
                :value="item.id"
              ></ElOption>
            </ElSelect>
          </el-form-item>
        </CetForm>
      </el-main>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_reset"
          v-on="CetButton_reset.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
      <measureObj
        :visibleTrigger_in="measureObj.visibleTrigger_in"
        :closeTrigger_in="measureObj.closeTrigger_in"
        :inputData_in="measureObj.inputData_in"
        @CetButton_confirm_out="measureObj_CetButton_confirm_out"
      />
    </CetDialog>
  </div>
</template>

<script>
import common from "eem-utils/common.js";
import measureObj from "./measureObj.vue";

export default {
  name: "templateAdmin",
  components: {
    measureObj
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    template_in: {
      type: Array,
      default() {
        return [];
      }
    },
    queryCondition_in: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data(vm) {
    return {
      templateArr: [],
      // 1表单组件
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelPosition: "top",
        labelWidth: "180px",
        rules: {},
        event: {},
        inline: true
      },
      // string组件
      ElInput_string: {
        value: "",
        placeholder: $T("请输入"),
        style: {
          width: "250px"
        },
        event: {}
      },
      ElSelect_select: {
        value: "",
        style: {
          width: "250px"
        },
        event: {},
        clearable: true
      },
      ElSelect_boolean: {
        value: "",
        style: {
          width: "250px"
        },
        event: {},
        clearable: true
      },
      ElOption_boolean: {
        options_in: [
          {
            value: true,
            label: $T("是")
          },
          {
            value: false,
            label: $T("否")
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      ElInputNumber_num1: {
        ...common.check_numberInt,
        value: "",
        controls: false,
        placeholder: $T("请输入"),
        style: {
          width: "250px"
        },
        event: {}
      },
      ElInputNumber_num2: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        placeholder: $T("请输入"),
        style: {
          width: "250px"
        },
        event: {}
      },
      // 1弹窗组件
      CetDialog_1: {
        title: $T("高级查询"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_reset: {
        visible_in: true,
        disable_in: false,
        title: $T("重置"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_reset_statusTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("查询"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("选择"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      measureObj: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      }
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      vm.createCondition();
      vm.CetForm_1.data = vm._.cloneDeep(vm.queryCondition_in);
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },
  methods: {
    // 构造查询表单 过滤出既启用并且显示项
    createCondition() {
      this.templateArr = this.template_in.filter(item => {
        if (!item.allowdeactivation) {
          return item.display;
        } else {
          return item.display && item.active;
        }
      });
    },
    reset() {
      this.templateArr.forEach(item => {
        this.$set(this.CetForm_1.data, item.name, undefined);
      });
    },
    CetButton_reset_statusTrigger_out(val) {
      this.reset();
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.$emit("queryCondition_out", this.CetForm_1.data);
      this.CetDialog_1.closeTrigger_in = val;
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = val;
    },
    CetButton_add_statusTrigger_out(val) {
      if (this.CetForm_1.data.monitorid) {
        this.measureObj.inputData_in = {
          monitorid: this.CetForm_1.data.monitorid,
          monitorlabel: this.CetForm_1.data.monitorlabel,
          name: this.CetForm_1.data.monitorname
        };
      } else {
        this.measureObj.inputData_in = {};
      }
      this.measureObj.visibleTrigger_in = new Date().getTime();
    },
    measureObj_CetButton_confirm_out(val) {
      if (val) {
        this.$set(this.CetForm_1.data, "monitorname", val.name);
        this.$set(this.CetForm_1.data, "monitorid", val.id);
        this.$set(this.CetForm_1.data, "monitorlabel", val.modelLabel);
      } else {
        this.$set(this.CetForm_1.data, "monitorname", null);
        this.$set(this.CetForm_1.data, "monitorid", null);
        this.$set(this.CetForm_1.data, "monitorlabel", null);
      }
    }
  },
  activated: function () {}
};
</script>
<style lang="scss" scoped>
.form {
  .el-form-item {
    width: calc(50% - 10px);
    .img {
      width: 250px;
      & > span {
        width: calc(100% - 50px);
        display: block;
      }
    }
  }
}
:deep(.el-range-separator) {
  width: 30px;
}
</style>
