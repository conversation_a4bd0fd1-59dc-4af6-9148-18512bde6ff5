<template>
  <div class="page">
    <el-container class="fullheight flex-column">
      <div style="height: 400px" class="eem-container flex-column">
        <div class="common-title-H2 mbJ1">{{ $T("预算成本与实际偏差") }}</div>
        <div class="flex-auto" >
          <CetChart v-bind="CetChart_1"></CetChart>
        </div>
      </div>
      <div class="flex-auto eem-container mtJ3 flex-column" style="min-height: 300px">
        <div class="mbJ1">
          <span class="common-title-H2 lh32">{{ $T("预算与实际数据") }}</span>
          <CetButton
            class="fr"
            v-bind="CetButton_toEnery"
            v-on="CetButton_toEnery.event"
          ></CetButton>
        </div>
        <CetTable
          class="flex-auto"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_logtime1"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_costValue"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_budgetValue"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_deviationRate"></ElTableColumn>
        </CetTable>
      </div>
    </el-container>
  </div>
</template>
<script>

export default {
  name: "Implement",
  components: {},

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data(vm) {
    return {
      CetButton_toEnery: {
        visible_in: true,
        disable_in: false,
        title: $T("预算录入"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.toEntry
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        // prop: "", // 支持path a[0].b
        label: "#", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "50" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_logtime1: {
        //type: "",      // selection 勾选 index 序号
        prop: "logtime1", // 支持path a[0].b
        label: $T("统计周期"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.logtime1 || val.logtime1 === 0) {
            return val.logtime1;
          } else {
            return "--";
          }
        }
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
      },
      ElTableColumn_costValue: {
        //type: "",      // selection 勾选 index 序号
        prop: "costValueView", // 支持path a[0].b
        label: $T("实际成本(元)"), //列名
        headerAlign: "right",
        align: "right",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.costValue || val.costValue === 0) {
            return val.costValue;
          } else {
            return "--";
          }
        }
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
      },
      ElTableColumn_budgetValue: {
        //type: "",      // selection 勾选 index 序号
        prop: "budgetValueView", // 支持path a[0].b
        label: $T("预算成本(元)"), //列名
        headerAlign: "right",
        align: "right",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.budgetValue || val.budgetValue === 0) {
            return val.budgetValue;
          } else {
            return "--";
          }
        }
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
      },
      ElTableColumn_deviationRate: {
        prop: "deviationRate", // 支持path a[0].b
        label: $T("偏差率(%)"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.deviationRate || val.deviationRate === 0) {
            return val.deviationRate;
          } else {
            return "--";
          }
        }
      },
      CetChart_1: {
        //组件输入项
        inputData_in: null,
        options: {}
      }
    };
  },
  props: ["object", "source", "list", "unitTransition"],
  watch: {
    source: {
      handler(newVal, oldVal) {
        this.createChert(newVal);
      },
      deep: true
    },
    list: {
      handler(newVal, oldVal) {
        this.CetTable_1.data = newVal;
        if (this.unitTransition) {
          this.ElTableColumn_costValue.label = $T("实际成本(万元)");
          this.ElTableColumn_budgetValue.label = $T("预算成本(万元)");
        } else {
          this.ElTableColumn_costValue.label = $T("实际成本(元)");
          this.ElTableColumn_budgetValue.label = $T("预算成本(元)");
        }
      },
      deep: true
    }
  },

  methods: {
    createChert(source) {
      this.CetChart_1.options = {};
      this.CetChart_1.options = {
        toolbox: {
          top: 0,
          right: 50,
          feature: {
            saveAsImage: {
              title: $T("保存为图片")
            }
          }
        },
        grid: {
          left: "30",
          right: "15",
          bottom: "30",
          top: "35",
          containLabel: true
        },
        legend: { bottom: true, icon: "circle" },
        tooltip: {},
        dataset: {
          source
        },
        xAxis: { type: "category" },
        yAxis: {
          name: this.unitTransition ? $T("成本（万元）") : $T("成本（元）")
        },
        series: [
          { type: "bar", barWidth: 20 },
          { type: "bar",  barWidth: 20 }
        ]
      };
    },
    toEntry(val) {
      this.$emit("finishTrigger_out1", val);
    },
    CetTable_1_outputData_out(val) {},
    CetTable_1_record_out(val) {}
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 96%;
  position: relative;
}
.tip1 {
  height: 450px;
  overflow: hidden;
}
.tip2 {
  height: calc(100% - 454px);
  min-height: 200px;
}
.foot {
  line-height: 60px;
  text-align: center;
  display: flex;
}
.foot .count {
  width: 47.6%;
  border: 1px solid #e4e4e4;
  border-top: none;
}
.foot .num1,
.num2 {
  width: 26.7%;
  border: 1px solid #e4e4e4;
  border-top: none;
  border-left: none;
}
</style>
