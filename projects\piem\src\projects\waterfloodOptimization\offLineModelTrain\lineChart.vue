<template>
  <div class="w-full h-full">
    <div class="h-[32px] w-full flex items-center justify-between">
      <div class="flex items-center w-3/4">
        <div
          class="text-T2 font-medium text-[16px] overflow-hidden text-ellipsis whitespace-nowrap"
          :class="[schemeName ? 'mr-[16px]' : '']"
          v-if="schemeName"
        >
          {{ schemeName }}
        </div>
        <CustomElDatePicker
          prefix_in="样本数据时段"
          style="width: 345px"
          v-bind="CetDatePicker_1.config"
          v-model="CetDatePicker_1.val"
          @change="CetDatePicker_1_change_out"
        ></CustomElDatePicker>
      </div>
      <div class="flex items-center justify-end w-1/4">
        <div class="text-T2 font-medium text-[16px] mr-[16px] flex">
          单耗模型精度：
          <span class="text-ZS">{{ precision || "--" }}</span>
        </div>
        <el-button type="primary" @click="onTraining">开始训练</el-button>
      </div>
    </div>
    <div class="w-full h-[calc(100%-32px-16px)] mt-[16px]">
      <CetChart v-bind="CetChart_line" v-if="isData"></CetChart>
      <div class="flex flex-col items-center justify-center empty" v-else>
        <img v-if="isLight" src="../../../resources/assets/light.png" alt="" />
        <img v-else src="../../../resources/assets/dark.png" alt="" />
        <div class="text-[16px] text-T3 mt-[16px]">暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script>
import CustomElDatePicker from "eem-components/CustomElDatePicker";
import omegaTheme from "@omega/theme";
import customApi from "@/api/custom.js";
import common from "eem-utils/common.js";
export default {
  props: {
    selectNode: Object
  },
  components: { CustomElDatePicker },
  data() {
    return {
      paramsTime: [
        this.$moment().subtract(9, "d").startOf("d").valueOf(),
        this.$moment().endOf("d").valueOf() + 1
      ],
      CetDatePicker_1: {
        val: [],
        config: {
          valueFormat: "timestamp",
          type: "daterange",
          clearable: false,
          pickerOptions: {
            disabledDate: time => {
              return time.getTime() > Date.now();
            }
          }
        }
      },
      CetChart_line: {
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "axis"
          },
          encode: {
            x: 0,
            y: 1
          },
          legend: { data: [$T("实际值"), $T("预测值")] },
          grid: {
            top: "14%",
            left: "2%",
            right: "2%",
            bottom: "12%",
            containLabel: true
          },
          xAxis: {
            type: "time",
            axisTick: { show: false },
            data: []
          },
          yAxis: {
            type: "value",
            name: "",
            nameTextStyle: {
              padding: [0, 0, 0, 30]
            },
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: {
              lineStyle: {
                type: "dashed"
              }
            }
          },
          dataZoom: [
            {
              type: "inside",
              yAxisIndex: 0
            },
            {
              type: "slider",
              minValueSpan: 3600 * 1000 * 8,
              handleSize: 15,
              height: 15,
              bottom: 10
            }
          ],
          series: [
            {
              name: $T("实际值"),
              type: "line",
              smooth: true,
              showSymbol: false,
              data: []
            },
            {
              name: $T("预测值"),
              type: "line",
              smooth: true,
              showSymbol: false,
              data: []
            }
          ]
        }
      },
      precision: null,
      schemeName: null
    };
  },
  computed: {
    isLight() {
      return omegaTheme.theme === "light";
    },
    isData() {
      return (
        this.CetChart_line.options.series[0].data?.length > 0 &&
        this.CetChart_line.options.series[1].data?.length > 0
      );
    }
  },
  watch: {
    selectNode: {
      deep: true,
      handler(val) {
        if (!_.isObject(val)) return;
        this.getChartData();
        // this.onTraining();
      }
    },
    paramsTime: {
      immediate: true,
      deep: true,
      handler(val) {
        this.$emit("paramsTime", val);
      }
    }
  },
  methods: {
    CetDatePicker_1_change_out(val) {
      this.paramsTime = _.cloneDeep(val);
      this.getChartData();
    },
    async getChartData() {
      const params = {
        endTime: this.paramsTime[1],
        objectId: this.selectNode?.id,
        objectLabel: this.selectNode?.modelLabel,
        startTime: this.paramsTime[0]
      };
      this.CetChart_line.options.series[0].data = [];
      this.CetChart_line.options.series[1].data = [];
      this.CetChart_line.options.yAxis.name = [];
      this.schemeName = null;

      if (params.objectLabel !== "waterinjectionstation") return;
      const res = await customApi.fittingcurve(params);
      this.CetChart_line.options.series[0].data =
        res?.data?.dataVOList?.map(i => [
          i.logTime,
          common.formatNumberWithPrecision(i.productValue, 2)
        ]) || [];
      this.CetChart_line.options.series[1].data =
        res?.data?.dataVOList?.map(i => [
          i.logTime,
          common.formatNumberWithPrecision(i.forecastProductValue, 2)
        ]) || [];
      this.CetChart_line.options.yAxis.name = res?.data?.unit
        ? `单位(${res?.data?.unit})`
        : "";
      this.schemeName = res?.data?.schemeName;
      this.precision = common.formatNumberWithPrecision(res?.data?.score, 3);
    },
    async onTraining() {
      const params = {
        endTime: this.paramsTime[1],
        objectId: this.selectNode?.id,
        objectLabel: this.selectNode?.modelLabel,
        startTime: this.paramsTime[0]
      };
      this.precision = null;
      if (params.objectLabel !== "waterinjectionstation") return;
      const res = await customApi.offlineTraining(params);
      if (res?.data) {
        this.precision = common.formatNumberWithPrecision(res?.data, 3);
        this.$message.success("训练成功！");
        this.getChartData();
      }
    }
  },
  created() {},
  mounted() {
    this.CetDatePicker_1.val = _.cloneDeep(this.paramsTime);
    this.getChartData();
    // this.onTraining();
  }
};
</script>

<style lang="scss" scoped>
.empty {
  img {
    width: 220px;
    height: 135px;
  }
}
</style>
