<template>
  <div class="page harmonicAnalysis eem-common">
    <el-container class="harmonicAnalysisbox">
      <el-aside width="315px" class="eem-aside flex-column">
        <div class="mbJ3">
          <el-input
            suffix-icon="el-icon-search"
            placeholder="输入关键字以检索"
            v-model="filterText"
          ></el-input>
        </div>
        <div class="flex-auto" style="overflow: auto">
          <el-tree
            ref="powerTree"
            style="width: 100%; height: 100%"
            :data="treeData"
            node-key="tree_id"
            :props="treeProps"
            :filter-node-method="filterNode"
            :highlight-current="true"
            :default-expanded-keys="expandedKeys"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
          ></el-tree>
        </div>
      </el-aside>
      <el-container
        v-show="modelLabels.includes(_.get(currentNode, 'data.modelLabel'))"
        class="mlJ3 flex-column"
      >
        <el-tabs v-model="tabName" class="eem-tabs-custom">
          <el-tab-pane label="实时谐波" name="实时谐波"></el-tab-pane>
          <el-tab-pane label="时域分析" name="时域分析"></el-tab-pane>
        </el-tabs>
        <div class="flex-auto mtJ3" style="overflow: auto">
          <RealTimeHarmonic
            v-if="tabName === '实时谐波'"
            :refreshTrigger_in="refreshTrigger_in"
            :selectedMenu="selectedMenu"
            :currentNode="currentNode"
            :token="token"
          />
          <TimeDomainAnalysis
            v-if="tabName === '时域分析'"
            :refreshTrigger_in="refreshTrigger_in"
            :currentNode="currentNode"
            :token="token"
          />
        </div>
      </el-container>
      <el-container
        v-show="!modelLabels.includes(_.get(currentNode, 'data.modelLabel'))"
        class="empty-box"
      >
        <p class="text-center w100 fs20 info">请选择设备节点</p>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import RealTimeHarmonic from "./harmonicanalysis/RealTimeHarmonic";
import TimeDomainAnalysis from "./harmonicanalysis/TimeDomainAnalysis";
import commonApi from "@/api/custom.js";
import ELECTRICAL_DEVICE from "@/store/electricaldevice.js";
export default {
  name: "HarmonicAnalysis",
  components: {
    RealTimeHarmonic,
    TimeDomainAnalysis
  },
  props: {
    selectedMenu: {
      type: String
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    modelLabels() {
      return ELECTRICAL_DEVICE.map(i => i.value);
    }
  },

  data() {
    return {
      activatedNum: 0,
      treeData: [],
      expandedKeys: [], // 初始展开
      treeProps: {
        children: "children",
        label: "name",
        isLeaf: "leaf"
      },
      currentNode: null,
      filterText: "",
      tabName: "实时谐波",
      refreshTrigger_in: new Date().getTime()
    };
  },
  watch: {
    filterText(val) {
      this.$refs.powerTree.filter(val);
    }
  },

  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1;
    },
    getTreeData() {
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [
          {
            filter: {
              expressions: [{ limit: 1, operator: "EQ", prop: "roomtype" }]
            },
            modelLabel: "room"
          }
        ],
        treeReturnEnable: true
      };
      ELECTRICAL_DEVICE.forEach(item => {
        data.subLayerConditions.push({ modelLabel: item.value });
      });
      commonApi.getPqNodeTree(data, this.projectId, true).then(res => {
        if (res.code === 0) {
          _this.treeData = res.data;
          if (_this._.get(res.data, "[0]")) {
            let currentNode = _this
              .dataTransform(res.data)
              .find(item => this.modelLabels.includes(item.modelLabel));
            _this.$nextTick(() => {
              _this.$refs.powerTree.setCurrentKey(currentNode.tree_id);
              _this.currentNode = _this.$refs.powerTree.getNode(
                currentNode.tree_id
              );
              _this.expandedKeys = [currentNode.tree_id];
            });
          }
        }
      });
    },
    // 节点树数据平铺，用于选中第一个管网设备
    dataTransform(array) {
      const cloneData = this._.cloneDeep(array);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },
    handleNodeClick(obj, node) {
      this.currentNode = node;
    }
  },
  created: function () {},
  activated() {
    this.currentNode = {};
    this.getTreeData();
    if (this.activatedNum) this.refreshTrigger_in = new Date().getTime();
    this.activatedNum++;
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.harmonicAnalysis {
  .harmonicAnalysisbox {
    height: 100%;
  }
  .empty-box {
    height: 100%;
    padding: 50px 16px 11px 12px;
  }
  :deep(.el-tabs__header) {
    margin: 0px;
  }
  :deep(.el-tabs__content) {
    height: calc(100% - 42px) !important;
  }
}
</style>
