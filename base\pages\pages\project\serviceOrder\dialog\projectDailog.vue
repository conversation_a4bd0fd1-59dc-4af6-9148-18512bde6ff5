<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="el_dialog medium"
    >
      <template v-slot:footer>
        <span class="fl mr10" style="line-height: 32px">
          {{ $T("已选择{0}个项目", number) }}
        </span>
        <!-- 跳转至维保项目页面按钮组件 -->
        <CetButton
          class="fl"
          v-bind="CetButton_push"
          v-on="CetButton_push.event"
        ></CetButton>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <div class="eem-cont-c1">
        <el-row :gutter="$J3" style="height: 500px" class="eem-common">
          <el-col class="fullheight" :span="6">
            <div class="group_list fullfilled">
              <div
                v-for="(item, key) in groupList"
                :key="key"
                :class="[
                  'group_item',
                  'text-ellipsis',
                  { active: (activeGroup && activeGroup.id) === item.id }
                ]"
                @click="activeGroup = item"
              >
                <el-tooltip :content="item.name" effect="light">
                  <span>{{ item.name }}</span>
                </el-tooltip>
              </div>
            </div>
          </el-col>
          <el-col class="fullheight" :span="18">
            <el-table
              ref="multipleTable"
              tooltip-effect="light"
              border
              stripe
              height="100%"
              style="width: 100%"
              :data.sync="CetTable_content.data"
              :dynamicInput.sync="CetTable_content.dynamicInput"
              v-bind="CetTable_content"
              v-on="CetTable_content.event"
            >
              <template v-for="item in columns_content">
                <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
              </template>
            </el-table>
          </el-col>
        </el-row>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";

export default {
  name: "editProjectDailog",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    tableData: {
      type: Array
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("选择维保项目"),
        width: "1000px",
        showClose: true,
        "modal-append-to-body": false,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      groupList: [], // 分组
      activeGroup: null, // 选中组
      // content表格组件
      CetTable_content: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        "highlight-current-row": false,
        "row-key": "id",
        paginationCfg: {},
        exportFileName: "",
        event: {
          "selection-change": this.handleSelectionChange
        }
      },
      columns_content: [
        {
          type: "selection", // selection 勾选 index 序号
          label: "", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // "reserve-selection": true,
          width: "60"
        },
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "60"
        },
        {
          prop: "maintenanceTypeName", // 支持path a[0].b
          label: $T("维保方式"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        },
        {
          prop: "content", // 支持path a[0].b
          label: $T("维保内容"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        },
        {
          prop: "sparePartName", // 支持path a[0].b
          label: $T("零部件类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        },
        {
          prop: "number", // 支持path a[0].b
          label: $T("零部件数量"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        },
        {
          prop: "unit", // 支持path a[0].b
          label: $T("单位"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "80", //绝对宽度
          formatter: this.formatter
        }
      ],
      selectedList: [], // 选中的项目数
      number: 0,
      // push组件
      CetButton_push: {
        visible_in: true,
        disable_in: false,
        title: $T("维保项目"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_push_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.selectedList = this._.cloneDeep(this.tableData);
      this.number = 0;
      this.selectedList.forEach(item => {
        if (item.itemIds.length) {
          this.number += item.itemIds.length;
        }
      });
      this.getGroupList();
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    activeGroup: {
      deep: true,
      handler(val) {
        if (val) {
          this.getMaintenanceItem();
        }
      }
    }
  },
  methods: {
    // push输出
    CetButton_push_statusTrigger_out() {
      this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
      this.$emit("closeDialog", new Date().getTime());
      this.$router.push({
        path: "/serviceProject"
      });
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out() {
      // 过滤掉itemIds为空的数据
      const filterSelectedList = this.selectedList.filter(
        item => item.itemIds.length
      );
      this.$emit("saveData_out", filterSelectedList, this.number);
      this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    no() {},
    formatter(row, column, cellValue) {
      return cellValue || "--";
    },
    handleSelectionChange(val) {
      // 所有维保项id
      const ids = [];
      val.forEach(item => ids.push(item.id));
      // 找出每个分组所选的维保项id
      const itemsIds = [];
      this.CetTable_content.data.forEach(item => {
        if (ids.includes(item.id)) {
          itemsIds.push(item.id);
        }
      });
      const index = this._.findIndex(this.selectedList, [
        "groupId",
        this.activeGroup.id
      ]);
      if (index !== -1) {
        this.selectedList[index] = {
          groupId: this.activeGroup.id,
          itemIds: itemsIds
        };
      } else {
        this.selectedList.push({
          groupId: this.activeGroup.id,
          itemIds: itemsIds
        });
      }
      const itemIds = [];
      this.selectedList.forEach(item => {
        if (item.itemIds && item.itemIds.length) {
          itemIds.push(...item.itemIds);
        }
      });
      this.number = itemIds.length;
    },
    // 获取分组列表
    getGroupList() {
      customApi.queryMaintenanceGroup().then(res => {
        if (res.code === 0) {
          this.groupList = this._.cloneDeep(res.data);
          this.activeGroup = this.groupList[0];
        }
      });
    },
    // 获取分组下的维保项目
    getMaintenanceItem() {
      const param = {
        maintenanceGroupId: this.activeGroup.id,
        model: this.inputData_in.model,
        nodeLabel: this.inputData_in.nodelabel
      };
      // console.log(1111, param);
      customApi.queryMaintenanceByNodeInfo(param).then(res => {
        if (res.code === 0) {
          this.CetTable_content.data = this._.cloneDeep(res.data || []);

          const target = this.selectedList.find(
            item => item.groupId === this.activeGroup.id
          );
          if (target) {
            this.$nextTick(() => {
              this.CetTable_content.data.forEach(item => {
                target.itemIds.forEach(row => {
                  if (item.id === row) {
                    this.$refs.multipleTable.toggleRowSelection(item, true);
                  }
                });
              });
            });
          }
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
}
.group_list {
  flex: 1;
  overflow: auto;
  .group_item {
    @include padding_left(J1);
    @include padding_right(J1);
    line-height: 32px;
    border: 1px solid;
    @include border_color(B1);
    border-radius: 4px;
    @include margin_bottom(J1);
    cursor: pointer;
  }
  .active {
    @include background_color(BG4);
  }
}
.el_dialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
    @include border-radius(C1);
  }
}
</style>
