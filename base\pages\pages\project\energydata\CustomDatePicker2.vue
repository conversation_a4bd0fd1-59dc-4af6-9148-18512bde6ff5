<template>
  <div class="date-range">
    <customElSelect
      v-model="CetSelect_timetype.selectNode"
      placeholder="请选择"
      style="width: 160px"
      @change="CetSelect_timetype_dropItemId_out"
      class="fl mrJ1"
      prefix_in="分析周期"
    >
      <el-option
        v-for="item in CetSelect_timetype.inputData_in"
        :key="item.id"
        :label="item.text"
        :value="item.id"
        :disabled="item.disabled"
      ></el-option>
    </customElSelect>
    <div
      class="fl"
      style="display: inline-block"
      v-show="CetSelect_timetype.selectNode != 11"
    >
      <!-- 向前查询按钮 -->
      <div class="fl">
        <el-button
          type="display: inline-block;"
          v-show="CetButton_prv.visible_in"
          :disabled="CetButton_prv.disable_in"
          v-bind="CetButton_prv.config"
          @click="CetButton_prv_statusTrigger_out"
        ></el-button>
      </div>
      <div class="fl mlJ mrJ">
        <!-- <span
          class="basic-box-label"
          style="margin-top: 14px; height: 32px; line-height: 32px"
        >
          开始时间
        </span> -->
        <CustomElDatePicker
          class="fl startDate"
          style="width: 180px"
          prefix_in="开始时间"
          v-bind="CetDatePicker_startTinme.config"
          v-model="CetDatePicker_startTinme.val"
          placeholder="选择日期时间"
          @change="CetDatePicker_startTinme_queryTime_out"
        ></CustomElDatePicker>
      </div>
      <div class="fl mlJ mrJ">
        <!-- <span
          class="basic-box-label"
          style="margin-top: 14px; height: 32px; line-height: 32px"
        >
          结束时间
        </span> -->
        <CustomElDatePicker
          class="fl endDate"
          style="width: 180px"
          prefix_in="结束时间"
          v-bind="CetDatePicker_endTinme.config"
          v-model="CetDatePicker_endTinme.val"
          placeholder="选择日期时间"
          @change="CetDatePicker_endTinme_queryTime_out"
        ></CustomElDatePicker>
      </div>
      <!-- 向后查询按钮 -->
      <div class="fl">
        <el-button
          type="display: inline-block;"
          v-show="CetButton_next.visible_in"
          :disabled="backToTimeBtnDis1"
          v-bind="CetButton_next.config"
          @click="CetButton_next_statusTrigger_out"
        ></el-button>
      </div>
    </div>
    <div
      class="fl"
      style="display: inline-block"
      v-show="CetSelect_timetype.selectNode == 11"
    >
      <TimeRange
        style="width: 420px"
        :val.sync="TimeRange_1.queryTime"
        @change="TimeRange_1_change"
      ></TimeRange>
    </div>
  </div>
</template>
<script>
import common from "eem-utils/common";
import moment from "moment";
import TimeRange from "eem-components/TimeRange";
export default {
  name: "CustomDatePicker",
  components: {
    TimeRange
  },
  props: {
    //数据绑定配置
    dataConfig: {
      type: Object
    },
    val: {
      type: Object
    }
  },
  computed: {
    // 实际对标考核下一时段按钮禁点状态
    backToTimeBtnDis1() {
      var actTime = null, //控件时间
        maxTime = null, //当前时间
        cycle = this.CetSelect_timetype.selectNode,
        time = this.CetDatePicker_endTinme.val;

      if (cycle === 1) {
        actTime = moment(time).startOf("day").valueOf();
        maxTime = moment().startOf("day").subtract(0, "d").valueOf();
      } else if (cycle === 3) {
        actTime = moment(time).startOf("month").valueOf();
        maxTime = moment().startOf("month").valueOf();
      } else if (cycle === 5) {
        actTime = moment(time).startOf("year").valueOf();
        maxTime = moment().startOf("year").valueOf();
      }
      return actTime >= maxTime;
    }
  },
  data(vm) {
    return {
      CetSelect_timetype: {
        dataConfig: {
          edition: "v1",
          queryUrl: "",
          type: "post",
          dropItemId: "id",
          dropItemText: "text",
          dropItemType: "type",
          modelLabel: "accountstatus"
        },
        dataMode: "static",
        inputData_in: [
          {
            id: 1,
            text: "日",
            type: "date"
          },
          {
            id: 3,
            text: "月",
            type: "month"
          },
          {
            id: 5,
            text: "年",
            type: "year"
          },
          {
            id: 11,
            text: "自定义",
            type: "date"
          }
        ],
        disable_in: false,
        visible_in: true,
        clearTrigger_in: new Date().getTime(),
        selectNode: 1,
        config: {
          initialFlag: false
        }
      },
      TimeRange_1: {
        queryTime: []
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        config: {
          title: "",
          size: "small",
          icon: "el-icon-arrow-left"
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        config: {
          title: "",
          size: "small",
          icon: "el-icon-arrow-right"
        }
      },
      CetDatePicker_startTinme: {
        disable_in: false,
        val: this.$moment().add(-30, "d").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "date",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          clearable: false,
          pickerOptions: {
            disabledDate(time) {
              return (
                time.getTime() > vm.$moment().valueOf() ||
                time.getTime() >
                  vm.$moment(vm.CetDatePicker_endTinme.val).valueOf()
              );
            }
          },
          size: "small",
          style: {
            display: "inline-block"
          }
        }
      },
      CetDatePicker_endTinme: {
        disable_in: false,
        val: this.$moment().add(0, "d").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "date",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          clearable: false,
          pickerOptions: {
            disabledDate(time) {
              return (
                time.getTime() > vm.$moment().valueOf() ||
                time.getTime() <
                  vm.$moment(vm.CetDatePicker_startTinme.val).valueOf()
              );
            }
          },
          size: "small",
          style: {
            display: "inline-block"
          }
        }
      },
      timenum: {
        1: 12,
        3: 14,
        5: 17,
        11: 20
      },
      timenum1: {
        12: 1,
        14: 3,
        17: 5,
        20: 11
      },
      queryTime: {
        startTime: null,
        endTime: null,
        cycle: 12 //17年，14月，12日，20自定义
      }
    };
  },
  watch: {
    val: {
      deep: true,
      handler: function (val, oldVal) {
        if (!val) {
          return;
        }
        this.value = val;
      }
    },
    value: {
      deep: true,
      handler: function (val, oldVal) {
        this.changDate(val);
      }
    },
    dataConfig: {
      deep: true,

      handler: function (val, oldVal) {
        if (!val || !val.time || !val.cycle) {
          return;
        }
        this.CetSelect_timetype.selectNode = val.cycle;
        if (val.cycle === 1) {
          this.CetDatePicker_startTinme.config.type = "date";
          this.CetDatePicker_startTinme.val = common.dateTypeChange(
            val.time,
            val.cycle
          );
        } else if (val.cycle === 3) {
          this.CetDatePicker_startTinme.config.type = "month";
          this.CetDatePicker_startTinme.val = common.dateTypeChange(
            val.time,
            val.cycle
          );
        } else if (val.cycle === 5) {
          this.CetDatePicker_startTinme.config.type = "year";
          this.CetDatePicker_startTinme.val = common.dateTypeChange(
            val.time,
            val.cycle
          );
        }
      }
    },
    queryTime: {
      deep: true,
      handler: function (val, oldVal) {
        this.$emit("change", val);
      }
    }
  },
  methods: {
    CetSelect_timetype_dropItemId_out(val) {
      var endtime = this.CetDatePicker_endTinme.val;
      if (val === 1) {
        this.CetDatePicker_endTinme.config.type = "date";
        this.CetDatePicker_endTinme.val = common.dateTypeChange(endtime, val);
      } else if (val === 3) {
        this.CetDatePicker_endTinme.config.type = "month";
        this.CetDatePicker_endTinme.val = common.dateTypeChange(endtime, val);
      } else if (val === 5) {
        this.CetDatePicker_endTinme.config.type = "year";
        this.CetDatePicker_endTinme.val = common.dateTypeChange(endtime, val);
      } else if (val === 11) {
        this.TimeRange_1.queryTime = common.initDateRange();
      }
      // 开始时间往前计算
      var starttime = this.CetDatePicker_startTinme.val;
      if (val === 1) {
        this.CetDatePicker_startTinme.config.type = "date";
        this.CetDatePicker_startTinme.val = this.$moment(
          this.CetDatePicker_endTinme.val
        ).subtract(30, "day");
      } else if (val === 3) {
        this.CetDatePicker_startTinme.config.type = "month";
        this.CetDatePicker_startTinme.val = this.$moment(
          this.CetDatePicker_endTinme.val
        ).subtract(12, "month");
      } else if (val === 5) {
        this.CetDatePicker_startTinme.config.type = "year";
        this.CetDatePicker_startTinme.val = this.$moment(
          this.CetDatePicker_endTinme.val
        ).subtract(3, "year");
      } else if (val === 11) {
        this.TimeRange_1.queryTime = common.initDateRange();
      }

      this.emitTime(val);
    },
    CetButton_prv_statusTrigger_out(val) {
      // let date = this.$moment(this.CetDatePicker_startTinme.val);
      // this.CetDatePicker_startTinme.val = date.subtract(1, "d")._d;
      var time = this.CetDatePicker_startTinme.val;
      var type = this.CetSelect_timetype.selectNode;
      this.CetDatePicker_startTinme.val = common.goToTime(time, type);

      this.CetDatePicker_endTinme.val = common.goToTime(
        this.CetDatePicker_endTinme.val,
        type
      );
      this.changeDate();
    },
    CetButton_next_statusTrigger_out(val) {
      // let date = this.$moment(this.CetDatePicker_startTinme.val);
      // this.CetDatePicker_startTinme.val = date.add(1, "d")._d;
      var time = this.CetDatePicker_startTinme.val;
      var type = this.CetSelect_timetype.selectNode;
      this.CetDatePicker_startTinme.val = common.backToTime(time, type);

      this.CetDatePicker_endTinme.val = common.backToTime(
        this.CetDatePicker_endTinme.val,
        type
      );
      this.changeDate();
    },
    changeDate() {
      let startdate = this.$moment(this.CetDatePicker_startTinme.val);
      let enddate = this.$moment(this.CetDatePicker_endTinme.val);
      // this.queryTime = [date.startOf("d").valueOf(), date.endOf("d").valueOf() + 1];
      var type = this.CetSelect_timetype.selectNode;

      var value;
      if (type === 1) {
        value = {
          startTime: startdate.startOf("d").valueOf(),
          endTime: enddate.endOf("d").valueOf() + 1,
          cycle: this.timenum[this.CetSelect_timetype.selectNode]
        };
      } else if (type === 3) {
        value = {
          startTime: startdate.startOf("M").valueOf(),
          endTime: enddate.endOf("M").valueOf() + 1,
          cycle: this.timenum[this.CetSelect_timetype.selectNode]
        };
      } else if (type === 5) {
        value = {
          startTime: startdate.startOf("Y").valueOf(),
          endTime: enddate.endOf("Y").valueOf() + 1,
          cycle: this.timenum[this.CetSelect_timetype.selectNode]
        };
      }
      this.queryTime = value;
    },
    CetDatePicker_startTinme_queryTime_out(val) {
      let startdate = this.$moment(val);
      let enddate = this.$moment(this.CetDatePicker_endTinme.val);
      // this.queryTime = [date.startOf("d").valueOf(), date.endOf("d").valueOf() + 1];
      var type = this.CetSelect_timetype.selectNode;

      var value;
      if (type === 1) {
        value = {
          startTime: startdate.startOf("d").valueOf(),
          endTime: enddate.endOf("d").valueOf() + 1,
          cycle: this.timenum[this.CetSelect_timetype.selectNode]
        };
      } else if (type === 3) {
        value = {
          startTime: startdate.startOf("M").valueOf(),
          endTime: enddate.endOf("M").valueOf() + 1,
          cycle: this.timenum[this.CetSelect_timetype.selectNode]
        };
      } else if (type === 5) {
        value = {
          startTime: startdate.startOf("Y").valueOf(),
          endTime: enddate.endOf("Y").valueOf() + 1,
          cycle: this.timenum[this.CetSelect_timetype.selectNode]
        };
      }
      // this.$emit("change", value);
      this.queryTime = value;
    },
    CetDatePicker_endTinme_queryTime_out(val) {
      let enddate = this.$moment(val);
      let startdate = this.$moment(this.CetDatePicker_startTinme.val);
      // this.queryTime = [date.startOf("d").valueOf(), date.endOf("d").valueOf() + 1];
      var type = this.CetSelect_timetype.selectNode;

      var value;
      if (type === 1) {
        value = {
          startTime: startdate.startOf("d").valueOf(),
          endTime: enddate.endOf("d").valueOf() + 1,
          cycle: this.timenum[this.CetSelect_timetype.selectNode]
        };
      } else if (type === 3) {
        value = {
          startTime: startdate.startOf("M").valueOf(),
          endTime: enddate.endOf("M").valueOf() + 1,
          cycle: this.timenum[this.CetSelect_timetype.selectNode]
        };
      } else if (type === 5) {
        value = {
          startTime: startdate.startOf("Y").valueOf(),
          endTime: enddate.endOf("Y").valueOf() + 1,
          cycle: this.timenum[this.CetSelect_timetype.selectNode]
        };
      }
      // this.$emit("change", value);
      this.queryTime = value;
    },
    TimeRange_1_change(val) {
      if (this.CetSelect_timetype.selectNode != 11) {
        return;
      }

      if (val[0] === val[1]) {
        var starttime = new Date(val[0]).getTime();
        var endtime = new Date(val[0]).getTime() + 1000 * 3600 * 24;
        this.TimeRange_1.queryTime = [starttime, endtime];
        this.$message.warning("结束时间不能小于等于开始时间！");
        return;
      }
      var value = {
        startTime: val[0],
        endTime: val[1],
        cycle: this.timenum[this.CetSelect_timetype.selectNode]
      };
      // this.$emit("change", value);
      this.queryTime = value;
    },
    emitTime(type) {
      var starttime = this.CetDatePicker_startTinme.val;
      let startdate = this.$moment(starttime);

      var endtime = this.CetDatePicker_endTinme.val;
      let enddate = this.$moment(endtime);
      var value;
      if (type === 1) {
        value = {
          startTime: startdate.startOf("d").valueOf(),
          endTime: enddate.endOf("d").valueOf() + 1,
          cycle: this.timenum[type]
        };
      } else if (type === 3) {
        value = {
          startTime: startdate.startOf("M").valueOf(),
          endTime: enddate.endOf("M").valueOf() + 1,
          cycle: this.timenum[type]
        };
      } else if (type === 5) {
        value = {
          startTime: startdate.startOf("Y").valueOf(),
          endTime: enddate.endOf("Y").valueOf() + 1,
          cycle: this.timenum[type]
        };
      } else if (type === 11) {
        var val = this.TimeRange_1.queryTime;
        value = {
          startTime: val[0],
          endTime: val[1],
          cycle: this.timenum[type]
        };
      }
      this.$emit("change", value);
      this.queryTime = value;
    }
  },
  created: function () {
    this.TimeRange_1.queryTime = common.initDateRange();
  },
  mounted() {
    this.CetSelect_timetype.inputData_in = this.dataConfig.type;
    this.CetSelect_timetype.selectNode = this.dataConfig.cycle;
    this.CetSelect_timetype_dropItemId_out(this.CetSelect_timetype.selectNode);
  }
};
</script>
<style lang="scss" scoped>
.date-range {
  display: flex;
  align-items: center;
  .date-range-label {
    display: block;
    // width: 86px;
    padding: 0 10px;
    text-align: center;
    line-height: 32px;
    height: 32px;
    box-sizing: border-box;
    border: 1px solid;
    border-right: 0px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    @include border_color(B1);
    @include background_color(BG1);
    @include font_color(T1);
  }
  // .date-picker {
  //   flex: 1;
  //   border-top-left-radius: 0px !important;
  //   border-bottom-left-radius: 0px !important;
  // }
  .el-button {
    padding: 9px;
  }
}
</style>
