import common from "eem-utils/common";
export function getData(id, val) {
  const data = {
    modelGoal: [{ id: 1, text: "最小单耗" }],
    targetParameter: [{ id: 1, text: "注水单耗" }],
    parameterPeriod: [{ id: 2, text: "2小时" }]
  };
  return data[id]?.find(i => i.id === val)?.text || "--";
}

// 拍平节点树数据
export function flatTreeData(treeData) {
  const cloneData = _.cloneDeep(treeData);
  const arr = [];
  const expanded = datas => {
    if (datas && datas.length > 0 && datas[0]) {
      datas.forEach(e => {
        arr.push(e);
        expanded(e.children);
      });
      return arr;
    }
  };
  return expanded(cloneData);
}

export function onInputNumber(num) {
  var reg = new RegExp("^(\\-)*(\\d+)\\.(\\d{0," + 0 + "}).*$");
  if (num <= 0) {
    return null;
  } else if (num > 30) {
    return 30;
  }

  var val = String(num);
  val = val.split(".")[0];
  if (val) {
    val = val.replace(/[^\d.]/g, ""); //清除数字和'.'以外的字符
    val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", "."); //只保留顺位第一的'.'
    return val.replace(reg, "$1$2.$3"); //只能输入两位位小数
  }
  if (val.length >= 2 && val.indexOf(".") !== 1 && val[0] === "0") {
    //在非小数的时候清除前导0
    return val.replace(/0/, "");
  }
  if (val.length >= 2 && val.indexOf(".") === 0) {
    // 在先输入小数点时补0
    val = null;
  }

  return !this._.isNil(val) ? Number(val) : null;
}

export function getDataParams(id) {
  const data = {
    inputParameter: [
      { id: 1, text: "出站汇管压力" },
      { id: 2, text: "出站汇管累计流量" }
    ],
    inputParameter1: [
      { id: 3, text: "注水泵正向有功电能" },
      { id: 4, text: "注水泵额定功率" }
    ],
    inputParameter2: [
      { id: 5, text: "日额定配注量" },
      { id: 6, text: "累计流量" },
      { id: 7, text: "瞬时流量" },
      { id: 8, text: "干管压力" },
      { id: 9, text: "油管压力" },
      { id: 10, text: "阀开度" }
    ]
  };
  return data[id];
}

export function getTableCol() {
  const tableCol = [
    {
      type: "index",
      label: "序号",
      width: "100",
      showOverflowTooltip: true
    },
    {
      prop: "name",
      label: "方案名称",
      showOverflowTooltip: true,
      formatter: (row, column, cellValue) => (cellValue ? cellValue : "--")
    },
    {
      prop: "modelGoal",
      label: "模型目标",
      width: "160",
      showOverflowTooltip: true,
      formatter: (row, column, cellValue) => {
        if (cellValue === 1) {
          return getData("modelGoal", cellValue);
        } else {
          return "--";
        }
      }
    }
  ];

  return tableCol;
}

export function getTableColStation() {
  const tableCol = [
    {
      prop: "y",
      label: "参数名称",
      showOverflowTooltip: true,
      formatter: (row, column, cellValue) => (cellValue ? cellValue : "--")
    },
    {
      prop: "value",
      label: "相关系数",
      showOverflowTooltip: true,
      formatter: common.formatNumberCol(2)
    }
  ];

  return tableCol;
}

export function getTableColWater() {
  const tableCol = [
    {
      prop: "order",
      label: "序号",
      width: "100",
      formatter: (row, column, cellValue) => (cellValue ? cellValue : "--")
    },
    {
      prop: "wellName",
      label: "井号",
      showOverflowTooltip: true,
      formatter: (row, column, cellValue) => (cellValue ? cellValue : "--")
    },
    {
      prop: "shortMeasure",
      label: "欠注量（m³）",
      showOverflowTooltip: true,
      formatter: common.formatNumberCol(1)
    }
  ];

  return tableCol;
}
