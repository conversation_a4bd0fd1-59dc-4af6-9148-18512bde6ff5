<template>
  <div class="page eem-common">
    <el-container class="fullheight padding0">
      <el-aside width="315px" class="eem-aside flex-column">
        <customElSelect
          v-model="ElSelect_1.value"
          v-bind="ElSelect_1"
          v-on="ElSelect_1.event"
          class="mbJ1"
          :prefix_in="$T('能源类型')"
        >
          <ElOption
            v-for="item in ElOption_1.options_in"
            :key="item[ElOption_1.key]"
            :label="item[ElOption_1.label]"
            :value="item[ElOption_1.value]"
            :disabled="item[ElOption_1.disabled]"
          ></ElOption>
        </customElSelect>
        <customElSelect
          v-model="ElSelect_cycle.value"
          v-bind="ElSelect_cycle"
          v-on="ElSelect_cycle.event"
          class="mbJ1"
          :prefix_in="$T('分析周期')"
        >
          <ElOption
            v-for="item in ElOption_cycle.options_in"
            :key="item[ElOption_cycle.key]"
            :label="item[ElOption_cycle.label]"
            :value="item[ElOption_cycle.value]"
            :disabled="item[ElOption_cycle.disabled]"
          ></ElOption>
        </customElSelect>
        <customElSelect
          v-model="ElSelect_indicatorType.value"
          v-bind="ElSelect_indicatorType"
          v-on="ElSelect_indicatorType.event"
          class="mbJ1"
          :prefix_in="$T('指标类型')"
        >
          <ElOption
            v-for="item in ElOption_indicatorType.options_in"
            :key="item[ElOption_indicatorType.key]"
            :label="item[ElOption_indicatorType.label]"
            :value="item"
            :disabled="item[ElOption_indicatorType.disabled]"
          ></ElOption>
        </customElSelect>
        <CetTree
          class="flex-auto"
          ref="cetTree"
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          :searchText_in.sync="CetTree_1.searchText_in"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
          v-if="isShow"
        >
          <span
            class="custom-tree-node el-tree-node__label"
            slot-scope="{ node }"
            :level="node.level"
          >
            <span :class="{ noClick: node.data.childSelectState !== 1 }">
              {{ node.label }}
            </span>
          </span>
        </CetTree>
      </el-aside>
      <el-container class="fullheight mlJ3" style="overflow: auto">
        <div class="fullfilled flex-column eem-min-width">
          <div class="eem-container mbJ3 flex-row">
            <div class="flex-auto text-ellipsis">
              <el-tooltip
                effect="light"
                :content="nodeName"
                placement="top-start"
              >
                <span class="common-title-H2 lh32">
                  {{ nodeName || "--" }}
                </span>
              </el-tooltip>
            </div>
            <div class="fr">
              <customElSelect
                v-model="ElSelect_analysisType.value"
                v-bind="ElSelect_analysisType"
                v-on="ElSelect_analysisType.event"
                class="mbJ1 fl"
                :prefix_in="$T('分析类型')"
              >
                <ElOption
                  v-for="item in ElOption_analysisType.options_in"
                  :key="item[ElOption_analysisType.key]"
                  :label="item[ElOption_analysisType.label]"
                  :value="item[ElOption_analysisType.value]"
                  :disabled="item[ElOption_analysisType.disabled]"
                ></ElOption>
              </customElSelect>
              <time-tool
                class="fl"
                :showOption="false"
                :typeID="ElSelect_cycle.value"
                :val.sync="startTime"
                @change="changeQueryTime"
                :timeType_in="timeType"
              ></time-tool>
              <!-- export按钮组件 -->
              <CetButton
                class="mlJ1"
                v-bind="CetButton_export"
                v-on="CetButton_export.event"
                :disable_in="
                  ElSelect_analysisType.value === 3 &&
                  !CetTree_1.checkedNodes.length
                "
              ></CetButton>
            </div>
          </div>
          <div
            class="flex-auto eem-container flex-column"
            style="min-height: 300px"
          >
            <!-- 单位产量能耗 -->
            <headerSpot class="mbJ3">
              {{
                ElSelect_indicatorType.value &&
                ElSelect_indicatorType.value.name
              }}
            </headerSpot>
            <div class="text-right" v-if="ElSelect_analysisType.value === 3">
              <el-radio-group v-model="chartType" @change="chartTypeChange">
                <el-radio-button :label="1">
                  <omega-icon class="cetIcon" symbolId="bar" />
                </el-radio-button>
                <el-radio-button :label="2">
                  <omega-icon class="cetIcon" symbolId="line" />
                </el-radio-button>
              </el-radio-group>
            </div>
            <div class="flex-auto">
              <CetChart v-bind="CetChart_energyConsume"></CetChart>
            </div>
          </div>
          <div
            style="height: 320px"
            class="proportion flex-row mtJ3"
            v-show="ElSelect_analysisType.value !== 3"
          >
            <div style="flex: 9" class="fullheight eem-container flex-column">
              <headerSpot class="mbJ3">
                {{ $T("能效最值") }}
              </headerSpot>
              <div class="proportion-item flex-auto efficiency">
                <p class="unit" style="margin: 0">
                  {{ $T("单位") }}:
                  <span>
                    {{
                      ElSelect_indicatorType.value &&
                      ElSelect_indicatorType.value.symbol
                    }}
                  </span>
                </p>
                <el-row class="mb20 row-item">
                  <el-col :span="8">
                    {{ currentText }} KPI {{ $T("最大值") }}
                  </el-col>
                  <el-col :span="16">
                    <span>
                      {{ formatterDate(historyData.maxTime, currentStr) }}
                    </span>
                    <el-tooltip
                      :content="
                        formatNumberWithPrecision(historyData.maxValue, 2)
                      "
                      effect="light"
                    >
                      <span class="fs28 text-ellipsis maxVal">
                        {{ formatNumberWithPrecision(historyData.maxValue, 2) }}
                      </span>
                    </el-tooltip>
                  </el-col>
                </el-row>
                <el-row class="mb20 row-item">
                  <el-col :span="8">
                    {{ currentText }} KPI {{ $T("最小值") }}
                  </el-col>
                  <el-col :span="16">
                    <span>
                      {{ formatterDate(historyData.minTime, currentStr) }}
                    </span>
                    <el-tooltip
                      :content="
                        formatNumberWithPrecision(historyData.minValue, 2)
                      "
                      effect="light"
                    >
                      <span class="fs28 text-ellipsis minVal">
                        {{ formatNumberWithPrecision(historyData.minValue, 2) }}
                      </span>
                    </el-tooltip>
                  </el-col>
                </el-row>
                <el-row class="row-item">
                  <el-col :span="8">
                    {{ currentText }} KPI {{ $T("平均值") }}
                  </el-col>
                  <el-col :span="16">
                    <span>{{ formatAvgVal }}</span>
                    <el-tooltip
                      :content="
                        formatNumberWithPrecision(historyData.avgValue, 2)
                      "
                      effect="light"
                    >
                      <span class="fs28 text-ellipsis avgVal">
                        {{ formatNumberWithPrecision(historyData.avgValue, 2) }}
                      </span>
                    </el-tooltip>
                  </el-col>
                </el-row>
              </div>
            </div>
            <div
              style="flex: 6"
              class="fullheight eem-container flex-column mlJ3 mrJ3"
            >
              <headerSpot class="mbJ3">
                {{ $T("历史最优值") }}
              </headerSpot>
              <div class="proportion-item flex-auto history flex-column">
                <div class="unit">
                  {{ $T("单位") }}:
                  <span>
                    {{
                      ElSelect_indicatorType.value &&
                      ElSelect_indicatorType.value.symbol
                    }}
                  </span>
                </div>
                <div class="content flex-auto">
                  <el-tooltip
                    :content="
                      formatNumberWithPrecision(bestData.extremevalue, 2)
                    "
                    effect="light"
                    placement="top"
                  >
                    <span class="bestVal text-ellipsis">
                      {{ formatNumberWithPrecision(bestData.extremevalue, 2) }}
                    </span>
                  </el-tooltip>
                  <span class="time">
                    {{ formatterDate(bestData.extremetime, currentStr) }}
                  </span>
                </div>
              </div>
            </div>
            <div style="flex: 9" class="fullheight eem-container flex-column">
              <headerSpot class="mbJ3">
                {{ $T("能效对标") }}
              </headerSpot>
              <div class="proportion-item flex-auto">
                <div class="unit">
                  <el-tooltip
                    effect="light"
                    :content="benchmarkingTime"
                    placement="top-start"
                  >
                    <span class="fl pro_span text-ellipsis text-left">
                      {{ $T("时间") }}:
                      <span>{{ benchmarkingTime }}</span>
                    </span>
                  </el-tooltip>
                  <span class="fr">
                    {{ $T("单位") }}:
                    <span>
                      {{
                        ElSelect_indicatorType.value &&
                        ElSelect_indicatorType.value.symbol
                      }}
                    </span>
                  </span>
                </div>
                <CetChart
                  v-bind="CetChart_benchmarking"
                  style="height: calc(100% - 48px)"
                ></CetChart>
              </div>
            </div>
          </div>
        </div>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import common from "eem-utils/common";
import TimeTool from "./TimeTool.vue";
import { getCyclePreLevel } from "eem-utils/measure.js";
import TREE_PARAMS from "@/store/treeParams.js";
export default {
  name: "EnergyEfficiencyIndexes",
  components: {
    TimeTool
  },
  props: {},

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    numberOfNodesCompared() {
      return this.$store.state.systemCfg.numberOfNodesCompared || 4;
    }
  },

  data(vm) {
    const isEN = window.localStorage.getItem("omega_language") === "en";
    return {
      chartType: 1,
      isShow: true,
      clickNode: {},
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children",
          disabled: this.setDisabled
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: true,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out,
          parentList_out: this.CetTree_1_parentList_out,
          check: this.CetTree_1_checkedNodes_out,
          halfCheckNodes_out: this.CetTree_1_halfCheckNodes_out,
          allCheckNodes_out: this.CetTree_1_allCheckNodes_out
        }
      },
      startTime: new Date().getTime(),
      // queryTime: common.initDateRange(),
      queryTime: {
        startTime: null,
        endTime: null,
        cycle: 12 //17年，14月，12日，20自定义，7小时
      },
      timeType: [
        {
          type: "date",
          text: "日",
          typeID: 7,
          number: 1,
          unit: "d"
        },
        {
          type: "week",
          text: "周",
          typeID: 13,
          number: 1,
          unit: "w"
        },
        {
          type: "month",
          text: "月",
          typeID: 12,
          number: 1,
          unit: "M"
        },
        {
          type: "year",
          text: "年",
          number: 1,
          typeID: 14,
          unit: "y"
        },
        {
          type: "year",
          text: "十年",
          number: 1,
          typeID: 17,
          unit: "y"
        }
      ],
      // export组件
      CetButton_export: {
        visible_in: true,
        // disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      // 单位产量能耗
      CetChart_energyConsume: {
        //组件输入项
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "axis",
            formatter(params) {
              return vm.formatTooltip(params);
            }
          },
          title: {
            text: "",
            left: "center"
          },
          legend: {
            top: "10px",
            tooltip: {
              show: true,
              formatter(params) {
                return vm.formatLegend(params.name);
              }
            }
          },
          grid: {
            left: "15px",
            right: "45px",
            top: "45px",
            bottom: "10px",
            containLabel: true
          },
          xAxis: [
            {
              type: "category",
              name: "",
              nameLocation: "end",
              data: [],
              axisPointer: {
                type: "shadow"
              }
            }
          ],
          yAxis: [
            {
              type: "value",
              name: "",
              nameLocation: "end"
            }
          ],
          series: []
        }
      },
      // 能效对标
      CetChart_benchmarking: {
        //组件输入项
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            },
            formatter(params) {
              return (
                params[0].name +
                (vm.formatNumberWithPrecision(params[0].value, 2) || "--")
              );
            }
          },
          grid: {
            left: "3%",
            right: "10%",
            top: "5%",
            bottom: "-20px",
            containLabel: true
          },
          xAxis: {
            show: false,
            type: "value",
            max: function (value) {
              return value.max * 1.25;
            },
            boundaryGap: [0, 0.01]
          },
          yAxis: {
            type: "category",
            axisLine: { show: false },
            axisTick: {
              show: false
            },
            axisLabel: {
              fontSize: 16,
              formatter(params) {
                params = params.toString();
                var maxlength = 5;
                if (params.length > maxlength) {
                  return params.substring(0, maxlength - 1) + "...";
                } else {
                  return params;
                }
              }
            },
            data: []
          },
          series: [
            {
              type: "bar",
              barWidth: 20,
              label: {
                show: true,
                position: "right",
                fontSize: 16
                // formatter: function (params) {
                //   if (params.value == 0) {
                //     //为0时不显示
                //     return "";
                //   } else {
                //     return params.value;
                //   }
                // }
                // width: 50,
                // overflow: "truncate",
                // ellipsis: "..."
              },
              data: [],
              showBackground: true
            }
          ]
        }
      },
      BenchmarkingManage: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        treeData_in: null
      },
      EditEnergyefficiencyIndexes: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        treeData_in: null
      },
      ElSelect_1: {
        value: null,
        style: {
          width: "calc(100% -86px)"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      //分析类型下拉
      ElSelect_analysisType: {
        value: 1,
        // "value-key": "id",
        style: {
          width: isEN ? "280px" : "240px"
        },
        event: {
          change: this.ElSelect_analysisType_change_out
        }
      },
      ElOption_analysisType: {
        options_in: [
          {
            id: 1,
            name: $T("同比环比")
          },
          {
            id: 3,
            name: $T("节点对比")
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // 指标类型下拉
      ElSelect_indicatorType: {
        value: "",
        "value-key": "id",
        style: {
          width: "calc(100% -86px)"
        },
        event: {
          change: this.ElSelect_indicatorType_change_out
        }
      },
      ElOption_indicatorType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // 分析周期下拉
      ElSelect_cycle: {
        value: 12,
        style: {
          width: "calc(100% -86px)"
        },
        event: {
          change: this.ElSelect_cycle_change_out
        }
      },
      ElOption_cycle: {
        options_in: [
          {
            id: 7,
            text: $T("小时")
          },
          {
            id: 12,
            text: $T("日")
          },
          {
            id: 13,
            text: $T("周")
          },
          {
            id: 14,
            text: $T("月")
          },
          {
            id: 17,
            text: $T("年")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      currentText: "", // 根据选择的时间显示时/日/月/年
      currentStr: "", // 根据选择的时间格式化要显示的时间字符串 yyyy-MM
      formatAvgVal: "", // 能效最值平均值时间
      historyData: {}, // 能效最值数据
      bestData: {}, // 历史最优值
      CetButton_edit: {
        visible_in: true,
        disable_in: false,
        config: {
          title: "编辑",
          type: "primary",
          plain: true
        }
      },
      energyIndexList: [],
      benchmarkset: [],
      benchmarkset1: [],
      benchmarkset2: [],
      benchmarkset3: [],
      energyefficiencyset: {},
      isEditBenchmark: true,
      nodeName: "",
      efSetList: [],
      benchmarksetList: [],
      benchmarkingTime: "", // 能效对标时间显示
      isInit: true
    };
  },
  watch: {
    clickNode: {
      deep: true,
      handler: _.debounce(function (val, oldVal) {
        this.nodeName = val && val.name;
        this.isInit = false;
        let type = this.ElSelect_analysisType.value;
        type === 1 ? this.getAllData() : this.updateChart();
      }, 300)
    },
    queryTime: {
      deep: true,
      handler: _.debounce(function (val, oldVal) {
        if (this.clickNode) {
          let type = this.ElSelect_analysisType.value;
          type === 1 ? this.getAllData() : this.updateChart();
        }
      }, 300)
    }
  },

  methods: {
    //过滤获取图表x轴对应值
    getAxixs(date, type) {
      if (type === 7) {
        return this.$moment(date).format("HH");
      } else if (type === 12) {
        return this.$moment(date).format("DD");
      } else if (type === 13) {
        return this.$moment(date).format($T("YYYY年第ww周"));
      } else if (type === 14) {
        return this.$moment(date).format("YYYY/MM");
      } else if (type === 17) {
        return this.$moment(date).format("YYYY");
      }
    },
    formatterDate(cellValue, formatStr = "YYYY-MM-DD HH:mm:ss") {
      if (cellValue) {
        return this.$moment(cellValue).format(formatStr);
      } else if (cellValue === 0 || cellValue === "") {
        return cellValue;
      } else {
        return "--";
      }
    },
    formatNumberWithPrecision: common.formatNumberWithPrecision,
    CetTree_1_currentNode_out(val) {
      if (this._.isEmpty(val)) {
        return;
      }
      if (val.childSelectState !== 1) {
        return this.$message.warning("该节点下无数据!");
      }
      this.clickNode = this._.cloneDeep(val);
      if (this.ElSelect_analysisType.value === 3) {
        this.$refs.cetTree.$refs.tree.setCheckedKeys([this.clickNode.tree_id]);
      }
    },
    CetTree_1_parentList_out(val) {},
    CetTree_1_checkedNodes_out(val, checkData) {
      const nodeCheck = checkData.checkedNodes || [];
      if (nodeCheck.length > this.numberOfNodesCompared) {
        this.$message.warning(
          $T("最多对比{0}个节点", this.numberOfNodesCompared)
        );
        this.CetTree_1.checkedNodes = nodeCheck.filter(
          item => item.tree_id !== val.tree_id
        );
        return;
      }
      if (this.ElSelect_analysisType.value == 3) {
        this.updateChart();
      }
    },
    CetTree_1_halfCheckNodes_out(val) {},
    CetTree_1_allCheckNodes_out(val) {},
    changeQueryTime: _.debounce(function ({ val, timeOption }) {
      const date = this.$moment(val);
      // 十年数据单独处理
      if (timeOption.typeID === 17) {
        this.queryTime = {
          startTime: this.$moment(val)
            .subtract(9, timeOption.unit)
            .startOf(timeOption.unit)
            .valueOf(),
          endTime: date.endOf(timeOption.unit).valueOf() + 1,
          cycle: timeOption.typeID,
          unit: timeOption.unit
        };
      } else if (timeOption.typeID === 13) {
        //十周数据单独处理
        this.queryTime = {
          startTime: this.$moment(val)
            .subtract(9, timeOption.unit)
            .startOf("isoWeek")
            .valueOf(),
          endTime: date.endOf("isoWeek").valueOf() + 1,
          cycle: timeOption.typeID,
          unit: timeOption.unit
        };
      } else {
        this.queryTime = {
          startTime: date.startOf(timeOption.unit).valueOf(),
          endTime: date.endOf(timeOption.unit).valueOf() + 1,
          cycle: timeOption.typeID,
          unit: timeOption.unit
        };
      }
      const typeID = timeOption.typeID;
      switch (typeID) {
        case 7:
          this.currentText = $T("时");
          this.currentStr = $T("YYYY年MM月DD日HH时");
          break;
        case 12:
          this.currentText = $T("日");
          this.currentStr = $T("YYYY年MM月DD日");
          break;
        case 13:
          this.currentText = $T("周");
          this.currentStr = $T("YYYY年第ww周");
          break;
        case 14:
          this.currentText = $T("月");
          this.currentStr = $T("YYYY年MM月");
          break;
        case 17:
          this.currentText = $T("年");
          this.currentStr = $T("YYYY年");
          break;
      }
      // KPI平均值
      const startTime = this.queryTime.startTime;
      const endTime = this.queryTime.endTime;
      const cycle = this.queryTime.cycle;
      const str =
        cycle === 7
          ? $T("YYYY年MM月DD日")
          : cycle === 12
          ? $T("YYYY年MM月")
          : $T("YYYY年");
      if (cycle === 17) {
        this.formatAvgVal =
          this.$moment(startTime).format(str) +
          " -" +
          this.$moment(endTime).subtract(1, "y").format(str);
      } else {
        this.formatAvgVal = str ? this.$moment(startTime).format(str) : "";
      }
    }, 300),
    // export输出
    CetButton_export_statusTrigger_out(val) {
      if (this._.isEmpty(this.clickNode)) {
        return;
      }
      if (this.ElSelect_analysisType.value === 3) {
        let params = {
          nodes: this.getSelectNode(),
          startTime: this.queryTime.startTime,
          endTime: this.queryTime.endTime,
          aggregationCycle: getCyclePreLevel(this.queryTime.cycle),
          energyType: this.ElSelect_1.value,
          unitType: this.ElSelect_indicatorType.value?.unittype,
          efSetId: this.ElSelect_indicatorType.value?.id,
          unitCycle: this.queryTime.cycle
        };
        common.downExcel(
          "/eem-service/v1/group/platform/query/ef/data/export",
          params,
          this.token,
          this.projectId
        );
      } else if (this.ElSelect_analysisType.value === 1) {
        var data = {
          nodes: [
            {
              modelLabel: this.clickNode.modelLabel,
              nodes: [
                {
                  id: this.clickNode.id,
                  name: this.clickNode.name
                }
              ]
            }
          ],
          startTime: this.queryTime.startTime,
          endTime: this.queryTime.endTime,
          aggregationCycle: this.queryTime.cycle,
          energyTypes: [this.ElSelect_1.value],
          unittype: [this.ElSelect_indicatorType.value.unittype],
          energyefficiencysetId: [this.ElSelect_indicatorType.value.id],
          type: true
        };
        common.downExcel(
          "/eem-service/v1/energy/energyEfficiency/export",
          data,
          this.token,
          this.projectId
        );
      }
    },
    ElSelect_1_change_out(val) {
      this.getEnergyefficiencyset();
    },
    ElSelect_indicatorType_change_out(val) {
      this.getTreeData();
    },
    ElSelect_analysisType_change_out(val) {
      this.CetTree_1.showCheckbox = val === 3;
      if (val === 3) {
        this.$refs.cetTree.$refs.tree.setCheckedKeys([this.clickNode?.tree_id]);
        this.updateChart();
      } else if (val === 1) {
        this.getAllData();
      }
    },
    setDisabled(data, node) {
      return !(data.childSelectState === 1);
    },
    ElSelect_cycle_change_out() {
      this.isInit = true;
      this.getEnergyefficiencyset();
    },
    // 单位产量能耗图例本周期、上周期、计划目标增加悬浮提示
    formatLegend(name, startTime, endTime) {
      const cycle = this.queryTime.cycle;
      const unit = this.queryTime.unit;
      const unit1 =
        cycle === 7
          ? "H"
          : cycle === 12
          ? "d"
          : cycle === 13
          ? "w"
          : cycle === 14
          ? "M"
          : "";
      let str = name + ":";
      if (
        name === $T("本周期") ||
        name === $T("计划目标") ||
        name === $T("产量")
      ) {
        if (cycle === 13) {
          str +=
            this.formatterDate(startTime, this.currentStr) +
            "-" +
            this.formatterDate(this.$moment(endTime), this.currentStr);
        } else {
          str +=
            this.formatterDate(startTime, this.currentStr) +
            "-" +
            this.formatterDate(
              this.$moment(endTime).add(1, unit1),
              this.currentStr
            );
        }
      }
      if (name === $T("上周期")) {
        if (cycle === 13) {
          str +=
            this.formatterDate(
              this.$moment(startTime).subtract(1, unit),
              this.currentStr
            ) +
            "-" +
            this.formatterDate(
              this.$moment(endTime).subtract(1, unit1),
              this.currentStr
            );
        } else {
          str +=
            this.formatterDate(
              this.$moment(startTime).subtract(1, unit),
              this.currentStr
            ) +
            "-" +
            this.formatterDate(startTime, this.currentStr);
        }
      }
      return str;
    },
    // 获取单位产量、能效最值、历史最优值、能效对标数据
    getAllData() {
      // 清空数据
      this.CetChart_energyConsume.options = {};
      this.historyData = {};
      this.bestData = {};
      this.CetChart_benchmarking.options.series[0].data = [];
      this.CetChart_benchmarking.options.yAxis.data = [
        `${$T("计划目标")}:`,
        `${$T("历史最优")}:`,
        `${$T("能效实绩")}:`
      ];
      this.benchmarkingTime = "";
      if (this._.isEmpty(this.clickNode) || this.isInit) {
        return;
      }
      var queryBody = {
        nodes: [
          {
            modelLabel: this.clickNode.modelLabel,
            nodes: [
              {
                id: this.clickNode.id,
                name: this.clickNode.name
              }
            ]
          }
        ],
        startTime: this.queryTime.startTime,
        endTime: this.queryTime.endTime,
        aggregationCycle: this.queryTime.cycle,
        energyTypes: [this.ElSelect_1.value],
        unittype: [this.ElSelect_indicatorType.value.unittype],
        energyefficiencysetId: [this.ElSelect_indicatorType.value.id],
        type: true
      };
      this.getChartData(queryBody);
      this.getMaxMinAvgEnergyEfficiency(queryBody);
      this.getHistoryOptimumValue(queryBody);
      this.getEnergyEfficiencyBenchmark(queryBody);
    },
    // 获取单位产量能耗图表数据
    getChartData(queryBody) {
      const _this = this;
      const energyefficiencysetId = this.ElSelect_indicatorType.value.id;
      let params = {
        ...queryBody,
        type: true // 新加type，兼容不使用非自然日的情况
      };
      customApi.queryEnergyEfficiencyData(params).then(res => {
        if (
          res.code === 0 &&
          res.data[energyefficiencysetId] &&
          res.data[energyefficiencysetId][0]
        ) {
          // 十年不显示上周期
          let series = [];
          if (this.queryTime.cycle === 17) {
            series = [
              {
                type: "bar",
                barWidth: 30,
                name: $T("本周期"),
                data: res.data[energyefficiencysetId][0].data
                // itemStyle: {
                //   color: "#1d78ed"
                // }
              },
              {
                type: "line",
                name: $T("计划目标"),
                data: res.data[energyefficiencysetId][0].planObjectData,
                // itemStyle: {
                //   color: "#facdaa"
                // },
                symbolSize: 2
              }
            ];
          } else {
            series = [
              {
                type: "bar",
                name: $T("本周期"),
                barWidth: _this.queryTime.cycle === 14 ? "30px" : "",
                data: res.data[energyefficiencysetId][0].data
                // itemStyle: {
                //   color: "#1d78ed"
                // }
              },
              {
                type: "bar",
                name: $T("上周期"),
                barWidth: _this.queryTime.cycle === 14 ? "30px" : "",
                data: res.data[energyefficiencysetId][0].preData
                // itemStyle: {
                //   color: "#05acd9"
                // }
              },
              {
                type: "line",
                name: $T("计划目标"),
                data: res.data[energyefficiencysetId][0].planObjectData,
                // itemStyle: {
                //   color: "#facdaa"
                // },
                symbolSize: 2
              }
            ];
          }
          // 自然周期和非自然周期返回的开始时间和结束时间
          const startTime = res.data[energyefficiencysetId][0].data[0].time;
          const endTime =
            res.data[energyefficiencysetId][0].data[
              res.data[energyefficiencysetId][0].data.length - 1
            ].time;
          // 能效对标时间显示
          const unit1 =
            this.queryTime.cycle === 7
              ? "H"
              : this.queryTime.cycle === 12
              ? "d"
              : this.queryTime.cycle === 14
              ? "M"
              : "";
          this.benchmarkingTime =
            this.formatterDate(startTime, this.currentStr) +
              "-" +
              this.formatterDate(
                this.$moment(endTime).add(1, unit1),
                this.currentStr
              ) || "--";

          this.CetChart_energyConsume.options = {
            toolbox: {
              top: 0,
              right: 10,
              feature: {
                saveAsImage: {
                  title: $T("保存为图片")
                }
              }
            },
            tooltip: {
              trigger: "axis",
              formatter(params) {
                if (!params[0].data.time) return;
                const cycle = _this.queryTime.cycle;
                const unit = _this.queryTime.unit;
                const formatStr =
                  cycle === 7
                    ? $T("YYYY-MM-DD HH时")
                    : cycle === 12
                    ? "YYYY-MM-DD"
                    : cycle === 13
                    ? $T("YYYY年第ww周")
                    : cycle === 14
                    ? "YYYY-MM"
                    : "YYYY";
                let str = "";
                params.forEach(item => {
                  let str1;
                  if (item.seriesName === $T("本周期")) {
                    str1 = _this.formatterDate(params[0].data.time, formatStr);
                  } else if (item.seriesName === $T("上周期")) {
                    str1 = _this.formatterDate(
                      _this.$moment(params[0].data.time).subtract(1, unit),
                      formatStr
                    );
                  } else {
                    str1 = item.seriesName;
                  }
                  str +=
                    str1 +
                    ": " +
                    (item.data.value || item.data.value === 0
                      ? Number(item.data.value).toFixed(2)
                      : "--") +
                    "(" +
                    (item.data.symbol ||
                      _this.ElSelect_indicatorType.value.symbol) +
                    ")" +
                    "<br />";
                });
                return str;
              }
            },
            title: {
              text: "",
              textStyle: {
                fontSize: 14
              },
              left: "center"
            },
            legend: {
              top: "10px",
              tooltip: {
                show: true,
                formatter(params) {
                  return _this.formatLegend(params.name, startTime, endTime);
                }
              }
            },
            grid: {
              left: "15px",
              right: "45px",
              top: "45px",
              bottom: "10px",
              containLabel: true
            },
            xAxis: [
              {
                type: "category",
                name:
                  this.queryTime.cycle === 7
                    ? $T("小时")
                    : this.queryTime.cycle === 12
                    ? $T("天数")
                    : this.queryTime.cycle === 13
                    ? $T("周份")
                    : this.queryTime.cycle === 14
                    ? $T("月份")
                    : $T("年份"),
                nameLocation: "end",
                data: [],
                axisPointer: {
                  type: "shadow"
                }
              }
            ],
            yAxis: [
              {
                type: "value",
                name:
                  this.ElSelect_indicatorType.value &&
                  this.ElSelect_indicatorType.value.symbol,
                nameLocation: "end",
                nameTextStyle: {
                  align: "left"
                }
              }
            ],
            series
          };
          // 处理x轴显示
          this.CetChart_energyConsume.options.series[0].data.forEach(item => {
            this.CetChart_energyConsume.options.xAxis[0].data.push(
              this.getAxixs(item.time, this.queryTime.cycle)
            );
          });
          this.getProductionData(startTime, endTime, params.aggregationCycle);
        }
      });
    },
    // 能效最值
    getMaxMinAvgEnergyEfficiency(queryBody) {
      customApi.queryMaxMinAvgEnergyEfficiency(queryBody).then(res => {
        if (res.code === 0) {
          this.historyData = this._.cloneDeep(res.data);
        }
      });
    },
    // 历史最优值
    getHistoryOptimumValue(queryBody) {
      customApi.queryHistoryOptimumValue(queryBody).then(res => {
        if (res.code === 0) {
          this.bestData = this._.cloneDeep(res.data);
        }
      });
    },
    // 能效对标
    getEnergyEfficiencyBenchmark(queryBody) {
      customApi.queryEnergyEfficiencyBenchmark(queryBody).then(res => {
        if (res.code === 0) {
          const data = this._.cloneDeep(res.data);
          for (const key in data) {
            data[key] = this.formatNumberWithPrecision(data[key], 2);
          }
          const options = this.CetChart_benchmarking.options;
          options.yAxis.data = [
            `${$T("计划目标")}:`,
            `${$T("历史最优")}:`,
            `${$T("能效实绩")}:`
          ];
          options.series[0].data = [
            data.planObjectValue,
            data.historyOptimumValue,
            data.energyEfficiencyValue
          ];
          if (res.data.sets && res.data.sets.length) {
            res.data.sets.forEach(item => {
              options.yAxis.data.unshift(item.name + ":");
              options.series[0].data.unshift(
                this.formatNumberWithPrecision(item.limitvalue, 2)
              );
            });
          }
        }
      });
    },
    // 获取项目能源类型
    getProjectEnergy() {
      this.ElOption_1.options_in = [];
      customApi.getProjectEnergy(this.projectId).then(res => {
        if (res.code === 0 && res.data && res.data.length > 0) {
          const selectData = res.data.map(item => {
            return {
              id: item.energytype,
              text: item.name
            };
          });
          this.ElOption_1.options_in = selectData;
          this.ElSelect_1.value = selectData[0].id;
          this.ElSelect_1_change_out(this.ElSelect_1.value);
        }
      });
    },
    // 获取指标类型
    getEnergyefficiencyset() {
      this.CetTree_1.selectNode = null;
      this.clickNode = null;
      this.CetTree_1.inputData_in = [];
      this.ElSelect_indicatorType.value = {};
      if (!this.ElSelect_cycle.value || !this.ElSelect_1.value) return;
      const param = {
        aggregationCycle: this.ElSelect_cycle.value,
        energyTypes: [this.ElSelect_1.value]
      };
      customApi.queryEnergyefficiencyset(param).then(res => {
        if (res.code === 0) {
          this.ElOption_indicatorType.options_in = this._.cloneDeep(res.data);
          this.ElSelect_indicatorType.value =
            this.ElOption_indicatorType.options_in[0];
          if (!this.ElSelect_indicatorType.value) {
            this.CetTree_1.inputData_in = [];
            return;
          }
          this.getTreeData();
        }
      });
    },
    getSelectNode() {
      let list = this.$refs.cetTree.$refs.tree.getCheckedNodes() || [];

      let resArr = []; //存放结果数组
      list.forEach(item => {
        resArr.push({
          nodeId: item.id,
          modelLabel: item.modelLabel,
          name: item.name
        });
      });

      return resArr;
    },
    updateChart() {
      let vm = this;
      this.CetChart_energyConsume.options = {};
      let params = {
        nodes: this.getSelectNode(),
        startTime: this.queryTime.startTime,
        endTime: this.queryTime.endTime,
        aggregationCycle: getCyclePreLevel(this.queryTime.cycle),
        energyType: this.ElSelect_1.value,
        unitType: this.ElSelect_indicatorType.value?.unittype,
        efSetId: this.ElSelect_indicatorType.value?.id,
        unitCycle: this.queryTime.cycle
      };
      customApi.queryNodeCompare(params).then(res => {
        if (res.code === 0) {
          this.CetChart_energyConsume.options = {
            toolbox: {
              top: 40,
              right: 30,
              feature: {
                saveAsImage: {
                  title: $T("保存为图片")
                }
              }
            },
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "shadow"
              },
              formatter: function (val) {
                const list = val || [];
                let formatterStr = "";
                for (let i = 0, len = list.length; i < len; i++) {
                  if (i === 0) {
                    formatterStr += `${val[i].name}`;
                  }
                  formatterStr += `<br/>${val[i].marker}${
                    val[i].seriesName
                  } : ${val[i].value || "--"}(${
                    vm.ElSelect_indicatorType.value.symbol || "--"
                  })`;
                }
                return formatterStr;
              }
            },
            grid: {
              top: "80",
              left: "16",
              right: "16",
              bottom: "8",
              containLabel: true
            },
            legend: {
              data: []
            },
            xAxis: {
              type: "category",
              data: []
            },
            yAxis: {
              type: "value",
              name:
                this.ElSelect_indicatorType.value &&
                this.ElSelect_indicatorType.value.symbol,
              nameLocation: "end",
              nameTextStyle: {
                align: "left"
              }
            },
            series: []
          };
          let id = 0;
          let modelLabel = "";
          var legend = [];
          var series = [];
          if (this.clickNode) {
            id = this.clickNode.id;
            modelLabel = this.clickNode.modelLabel;
          } else {
            this.$message.warning($T("请选择节点"));
            return;
          }
          let list = this.$refs.cetTree.$refs.tree.getCheckedNodes() || [];
          let isHasNode = false;
          list.forEach(item => {
            if (
              item.id === this.clickNode.id &&
              item.modelLabel === this.clickNode.modelLabel
            ) {
              isHasNode = true;
            }
          });
          if (!isHasNode && list.length > 0) {
            id = list[0].id;
            modelLabel = list[0].modelLabel;
          }
          let cycle = this.ElSelect_cycle.value;
          let source = [];
          res.data.forEach(item => {
            var serie = {};
            if (
              item.node.nodeId === id &&
              item.node.modelLabel === modelLabel
            ) {
              var itemData1 = item.timeValues || [];
              itemData1.forEach(item1 => {
                var obj = {};
                obj.time = item1.logTime;
                obj.product = this.getAxixs(item1.logTime, cycle);
                obj[`yAxis${item.node.modelLabel}${item.node.nodeId}`] =
                  common.formatNumberWithPrecision(item1.value, 2);
                source.push(obj);
              });
              serie = {
                name: item.node.name,
                type: "bar",
                smooth: true,
                encode: {
                  x: "product",
                  y: `yAxis${item.node.modelLabel}${item.node.nodeId}`
                }
              };
            } else {
              serie = {
                name: item.node.name,
                type: "line",
                smooth: true,
                encode: {
                  x: "product",
                  y: `yAxis${item.node.modelLabel}${item.node.nodeId}`
                }
              };
            }
            legend.push(item.node.name);
            series.push(serie);
          });
          res.data.forEach(item => {
            if (item.node.nodeId != id || item.node.modelLabel !== modelLabel) {
              let itemData1 = item.timeValues || [];
              itemData1.forEach(item1 => {
                source.forEach(item2 => {
                  if (item2.time === item1.logTime) {
                    item2[`yAxis${item.node.modelLabel}${item.node.nodeId}`] =
                      common.formatNumberWithPrecision(item1.value, 2);
                  }
                });
              });
            }
          });
          var cloneLegend = [],
            xAxisData = [],
            cloneSeries = [];
          source.forEach(item => {
            xAxisData.push(item.product);
          });
          res.data.forEach(item => {
            let serie = {};
            let yAxisData = [];
            source.forEach(item11 => {
              yAxisData.push({
                name: item11.product,
                time: item11.time,
                value: item11[`yAxis${item.node.modelLabel}${item.node.nodeId}`]
              });
            });
            if (
              item.node.nodeId === id &&
              item.node.modelLabel === modelLabel
            ) {
              serie = {
                name: item.node.name,
                type: "bar",
                smooth: true,
                data: yAxisData
              };
            } else {
              serie = {
                name: item.node.name,
                type: "line",
                smooth: true,
                data: yAxisData
              };
            }
            cloneLegend.push(item.node.name);
            cloneSeries.push(serie);
          });
          this.CetChart_energyConsume.options.xAxis.data = xAxisData;
          this.CetChart_energyConsume.options.series = cloneSeries;
          this.CetChart_energyConsume.options.legend.data = cloneLegend;
          this.chartTypeChange();
        }
      });
    },
    // 图表类型切换
    chartTypeChange() {
      this.CetChart_energyConsume.options.series.forEach(item => {
        item.type = this.chartType === 1 ? "bar" : "line";
      });
      this.CetChart_energyConsume.options.dataZoom =
        this.chartType === 1
          ? [
              {
                type: "inside",
                startValue: 0,
                endValue: 14,
                zoomOnMouseWheel: false
              },
              {
                startValue: 0,
                endValue: 14,
                zoomLock: true,
                brushSelect: false
              }
            ]
          : null;
      this.CetChart_energyConsume.options = this._.cloneDeep(
        this.CetChart_energyConsume.options
      );
    },
    // 根据指标类型获取树节点
    getTreeData() {
      this.isShow = false;
      var me = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.autoManagement,
        treeReturnEnable: true
      };
      if (!this.ElSelect_indicatorType.value.id) return;
      customApi
        .queryEnergyTree(data, this.ElSelect_indicatorType.value.id)
        .then(res => {
          if (res.code === 0) {
            this.isShow = true;
            // 过滤掉项目下的开关柜或一段线
            var data = [];
            res.data.forEach((item, index) => {
              var obj = me._.cloneDeep(item);
              obj.children = [];
              data.push(obj);
              if (item.children && item.children.length > 0) {
                item.children.forEach((ite, inde) => {
                  if (ite.modelLabel !== "linesegmentwithswitch") {
                    data[index].children.push(ite);
                  }
                });
              }
            });
            me.CetTree_1.inputData_in = data;
            // 选中第一个有数据（childSelectState = 1）的节点并展开节点
            const obj = this._.find(this.dataTransform(data), [
              "childSelectState",
              1
            ]);
            this.CetTree_1.selectNode = obj;
            if (this.ElSelect_analysisType.value === 3) {
              this.CetTree_1.checkedNodes = [obj];
            }
            this.clickNode = obj;
            this.CetChart_benchmarking.options.series[0].data = [];
            this.CetChart_benchmarking.options.yAxis.data = [
              `${$T("计划目标")}:`,
              `${$T("历史最优")}:`,
              `${$T("能效实绩")}:`
            ];
          }
        });
    },
    dataTransform(array) {
      const cloneData = this._.cloneDeep(array);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },
    // 获取产量数据
    async getProductionData(startTime, endTime, cycle) {
      let indicatorType = this.ElSelect_indicatorType.value;
      if (
        !indicatorType ||
        indicatorType.unittype !== 1 ||
        this.ElSelect_analysisType.value !== 1
      ) {
        return null;
      }
      switch (cycle) {
        case 7:
          endTime = this.$moment(endTime).endOf("h").valueOf() + 1;
          break;
        case 12:
          endTime = this.$moment(endTime).endOf("d").valueOf() + 1;
          break;
        case 13:
          endTime = this.$moment(endTime).endOf("w").valueOf() + 1;
          break;
        case 14:
          endTime = this.$moment(endTime).endOf("M").valueOf() + 1;
          break;
        case 17:
          endTime = this.$moment(endTime).endOf("y").valueOf() + 1;
          break;
      }
      const res = await customApi.querySystemDataInput({
        aggregationCycle: cycle,
        startTime: startTime,
        endTime: endTime,
        measureTypes: [indicatorType.producttype],
        dataEntryType: 1,
        id: this.clickNode.id,
        modelLabel: this.clickNode.modelLabel
      });
      const details = this._.get(res, "data[0].details", []) || [];
      const unit = this._.get(res, "data[0].unit");
      this.CetChart_energyConsume.options.yAxis.push({
        type: "value",
        name: unit,
        nameLocation: "end",
        nameTextStyle: {
          align: "right"
        }
      });
      this.CetChart_energyConsume.options.series.push({
        name: $T("产量"),
        type: "line",
        smooth: true,
        yAxisIndex: 1,
        data: details.map(i => {
          return {
            time: i.logTime,
            value: i.value,
            symbol: unit
          };
        })
      });
    }
  },

  created() {
    // this.getProjectEnergy();
  },
  mounted() {},
  activated() {
    this.isInit = true;
    this.getProjectEnergy();
    if (this.systemCfg.analysisPeriod) {
      let newArr = [];
      this.ElOption_cycle.options_in.forEach(item => {
        if (this.systemCfg.analysisPeriod.includes(item.id)) {
          newArr.push(item);
        }
      });
      this.ElOption_cycle.options_in = newArr;
    }
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.main {
  padding: 0 50px 0 0;
  overflow: hidden;
  text-align: center;
}
.proportion {
  .proportion-item {
    box-sizing: border-box;
    position: relative;
    .unit {
      text-align: right;
      height: 48px;
      line-height: 48px;
      .pro_span {
        display: inline-block;
        width: 200px;
      }
    }
  }
  .efficiency {
    display: flex;
    flex-direction: column;
    .row-item {
      display: flex;
      flex: 1;
      div {
        display: flex;
        justify-content: center;
        align-items: center;
        &:first-of-type {
          flex: 1;
          font-size: 16px;
          font-weight: 900;
        }
        &:nth-of-type(2) {
          flex: 2;
          padding: 0 24px;
          display: flex;
          span {
            &:nth-of-type(1) {
              flex: 2;
            }
            &:nth-of-type(2) {
              font-weight: 900;
              flex: 3;
              text-align: right;
            }
          }
        }
        .maxVal {
          @include font_color(T7);
        }
        .minVal {
          @include font_color(T8);
        }
        .avgVal {
          @include font_color(T9);
        }
      }
      &:nth-of-type(1) {
        div {
          @include font_color(T7);
          @include background(BG5);
          // background: linear-gradient(to right, rgba(186, 129, 255, 0.4), rgba(9, 21, 69, 0.4));
        }
      }
      &:nth-of-type(2) {
        div {
          @include font_color(T8);
          @include background(BG6);
        }
      }
      &:nth-of-type(3) {
        div {
          @include font_color(T9);
          @include background(BG7);
        }
      }
    }
  }
  .history {
    @include background_color(BG1);
    .content {
      // background: url("./assets/historyBest.png") no-repeat center;
      @include background_image(CUSTOM_BG_IMG1);
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      position: relative;
      span {
        position: absolute;
      }
    }
    .bestVal {
      top: 45%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 40px;
      color: #07dbd6;
      font-weight: 900;
      display: inline-block;
      width: 170px;
      text-align: center;
    }
    .time {
      width: 170px;
      left: 50%;
      bottom: 34%;
      transform: translateX(-50%);
      text-align: center;
    }
  }
}
.custom-tree-node :deep() {
  .noClick {
    @include font_color(T6);
  }
  .normal {
    @include font_color(T1);
  }
}
</style>
