<template>
  <div class="page tolerance eem-common">
    <el-container class="tolerance-box">
      <el-aside width="315px" class="eem-aside flex-column">
        <div class="mbJ3">
          <el-input
            suffix-icon="el-icon-search"
            placeholder="输入关键字以检索"
            v-model="filterText"
          ></el-input>
        </div>
        <div class="flex-auto" style="overflow: auto">
          <el-tree
            ref="powerTree"
            style="width: 100%; height: 100%"
            :data="treeData"
            node-key="tree_id"
            :props="treeProps"
            :filter-node-method="filterNode"
            :highlight-current="true"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
          ></el-tree>
        </div>
      </el-aside>
      <el-container class="tolerance-content mlJ3 flex-column">
        <div class="page-header mbJ3 eem-cont">
          <div class="flex-row">
            <div class="flex-auto text-ellipsis">
              <el-tooltip
                :content="_.get(currentNode, 'data.name', '--')"
                effect="light"
              >
                <span class="common-title-H2 lh32" style="display: inline">
                  {{ `${_.get(currentNode, "data.name", "--")}` }}
                </span>
              </el-tooltip>
            </div>
            <div class="datebtn clearfix mrJ1">
              <div class="datebtn-box" style="width: 360px">
                <time-range :val.sync="queryTime"></time-range>
              </div>
              <div class="keyword-box mrJ1">
                <ElInput
                  suffix-icon="el-icon-search"
                  v-model="ElInput_keyword.value"
                  v-bind="ElInput_keyword"
                  v-on="ElInput_keyword.event"
                  style="width: 100%"
                ></ElInput>
              </div>
            </div>
            <!-- 1按钮组件 -->
            <CetButton
              class="levelsreach mrJ1"
              v-bind="CetButton2_1"
              v-on="CetButton2_1.event"
            ></CetButton>
            <!-- 2按钮组件 -->
            <CetButton
              class="exportbtn"
              v-bind="CetButton2_2"
              v-on="CetButton2_2.event"
            ></CetButton>
          </div>
        </div>
        <div class="tolerance-main flex-auto">
          <div class="fullheight">
            <template>
              <div class="eem-cont mbJ3">
                <div>
                  <headerSpot>事件统计</headerSpot>
                </div>
                <div class="mtJ3">
                  <table
                    class="table-terse"
                    cellspacing="0"
                    cellpadding="0"
                    align="center"
                  >
                    <thead>
                      <tr>
                        <th>总点数</th>
                        <th>区域内</th>
                        <th>区域外</th>
                        <th>A区事件</th>
                        <th>B区事件</th>
                        <th>C区事件</th>
                        <th>瞬变事件</th>
                        <th>暂降事件</th>
                        <th>暂升事件</th>
                        <th>中断事件</th>
                        <th>不确定类型事件</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>{{ eventStatistics.total }}</td>
                        <td>{{ eventStatistics.inRegion }}</td>
                        <td>{{ eventStatistics.outRegion }}</td>
                        <td>{{ eventStatistics.regionA }}</td>
                        <td>{{ eventStatistics.regionB }}</td>
                        <td>{{ eventStatistics.regionC }}</td>
                        <td>{{ eventStatistics.transients }}</td>
                        <td>{{ eventStatistics.dip }}</td>
                        <td>{{ eventStatistics.swell }}</td>
                        <td>{{ eventStatistics.interruption }}</td>
                        <td>{{ eventStatistics.undefinedEvent }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </template>
            <div class="eem-cont">
              <div class="mbJ3">
                <headerSpot>容忍度分析</headerSpot>
              </div>
              <template>
                <p class="common-title-H2 highlight" style="margin: 0px">
                  ITIC曲线
                  <i class="el-icon-question" @click="handleShowHelp"></i>
                </p>
                <div
                  class="mtJ3 mbJ3"
                  style="height: 550px; position: relative"
                >
                  <div
                    class="text-right pr25"
                    style="position: absolute; right: 10px; display: none"
                  >
                    <span>
                      <i class="square" style="background: #008001" />
                      A区
                    </span>
                    <span class="ml20">
                      <i class="el-icon-caret-right caretb"></i>
                      B区
                    </span>
                    <span class="ml20">
                      <i class="el-icon-caret-right caretc"></i>
                      C区
                    </span>
                  </div>
                  <CetChart
                    style="height: calc(100% - 20px)"
                    ref="itic"
                    :inputData_in="CetChart2_ITIC.inputData_in"
                    v-bind="CetChart2_ITIC.config"
                  ></CetChart>
                </div>
              </template>
              <template>
                <el-table
                  stripe
                  :data="tableData"
                  border
                  tooltip-effect="light"
                  style="width: 100%; min-height: 200px"
                  cell-class-name="nowhite-space"
                  :max-height="500"
                >
                  <el-table-column type="expand">
                    <template slot-scope="props">
                      <div class="fullheight pJ2 bg">
                        <table
                          class="table-terse"
                          cellspacing="0"
                          cellpadding="0"
                          align="center"
                        >
                          <tbody>
                            <tr>
                              <td width="150" class="bg-f0">额定电压(V)：</td>
                              <td width="150">
                                {{ props.row.nominalvoltage }}
                              </td>
                              <td width="150" class="bg-f0">总幅值(%)：</td>
                              <td width="150">{{ props.row.magnitude }}</td>
                              <td width="150" class="bg-f0">持续时间(ms)：</td>
                              <td width="150">
                                {{ (props.row.duration / 1000) | formatNum }}
                              </td>
                            </tr>
                            <tr>
                              <td class="bg-f0">A相电压幅值：</td>
                              <td>
                                {{
                                  (props.row.v1magnitude * 100) | formatNum(2)
                                }}
                              </td>
                              <td class="bg-f0">B相电压幅值：</td>
                              <td>
                                {{
                                  (props.row.v2magnitude * 100) | formatNum(2)
                                }}
                              </td>
                              <td class="bg-f0">C相电压幅值：</td>
                              <td>
                                {{
                                  (props.row.v3magnitude * 100) | formatNum(2)
                                }}
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    align="left"
                    label="序号"
                    type="index"
                    width="50"
                  ></el-table-column>
                  <el-table-column
                    align="left"
                    label="时间"
                    prop="eventtime"
                    :formatter="formatDate"
                    width="200"
                  ></el-table-column>
                  <el-table-column
                    align="left"
                    label="事件类型"
                    prop="pqvariationeventtype$text"
                    width="80"
                  ></el-table-column>
                  <el-table-column
                    align="left"
                    width="80"
                    label="故障方向"
                    prop="transientfaultdirection$text"
                  ></el-table-column>
                  <el-table-column
                    align="left"
                    width="110"
                    label="持续时间(ms)"
                    prop="duration"
                  >
                    <template slot-scope="scope">
                      {{ (scope.row.duration / 1000) | formatNum }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    align="left"
                    width="105"
                    label="电压幅值(%)"
                    prop="magnitude"
                    :formatter="formatTable"
                  ></el-table-column>
                  <el-table-column
                    align="left"
                    label="区域"
                    prop="toleranceband$text"
                    width="100"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    align="left"
                    width="105"
                    label="设备名称"
                    prop="monitoredName"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    align="left"
                    label="描述"
                    prop="description"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column align="left" label="关联波形" width="80">
                    <template slot-scope="scope">
                      <a
                        class="clickformore fcZS"
                        v-if="scope.row.waveformlogtime"
                        @click="showWaveWindow(scope.row)"
                      >
                        查看
                      </a>
                      <span v-else>无</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="状态" width="100">
                    <template slot-scope="scope">
                      <span
                        :class="getEventStatus(scope.row.confirmeventstatus)"
                        @click="showConfirmWindow(scope.row)"
                      >
                        {{
                          formatEnum(
                            scope.row,
                            { property: "confirmeventstatus" },
                            scope.row.confirmeventstatus
                          )
                        }}
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  background
                  class="ptJ1 text-right"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page.sync="currentPage"
                  :page-sizes="pageSizes"
                  :page-size.sync="pageSize"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="pageTotal"
                ></el-pagination>
              </template>
            </div>
          </div>
        </div>
      </el-container>
    </el-container>

    <el-dialog
      title="高级查询"
      :visible.sync="queryDialogVisble"
      :show-close="false"
      width="40%"
    >
      <el-card class="mb10" shadow="never">
        <div slot="header" class="clearfix">
          <span>事件类型</span>
          <el-checkbox
            class="fr"
            :indeterminate="eventTypeGroup.isIndeterminate"
            v-model="eventTypeGroup.checkAll"
            @change="val => handleCheckAll(val, 'eventTypeGroup')"
          >
            全选
          </el-checkbox>
        </div>
        <el-checkbox-group
          v-model="eventTypeGroup.checked"
          @change="val => handleCheckedChange(val, 'eventTypeGroup')"
        >
          <el-checkbox
            style="width: 50%"
            class="m0 mb5"
            v-for="item in eventTypeGroup.enum"
            :label="item.id"
            :key="item.id"
          >
            {{ findDataByKey(item, "text") }}
          </el-checkbox>
        </el-checkbox-group>
      </el-card>
      <el-card shadow="never">
        <div slot="header" class="clearfix">
          <span>区域</span>
          <el-checkbox
            class="fr"
            :indeterminate="tolerancebandGroup.isIndeterminate"
            v-model="tolerancebandGroup.checkAll"
            @change="val => handleCheckAll(val, 'tolerancebandGroup')"
          >
            全选
          </el-checkbox>
        </div>
        <el-checkbox-group
          v-model="tolerancebandGroup.checked"
          @change="val => handleCheckedChange(val, 'tolerancebandGroup')"
        >
          <el-checkbox
            style="width: 50%"
            class="m0 mb5"
            v-for="item in tolerancebandGroup.enum"
            :label="item.id"
            :key="item.id"
          >
            {{ findDataByKey(item, "text") }}
          </el-checkbox>
        </el-checkbox-group>
      </el-card>
      <span slot="footer">
        <span class="device-Button">
          <el-button
            size="small"
            type="primary"
            plain
            @click="queryDialogCancel"
          >
            取消
          </el-button>
        </span>
        <span class="device-Button">
          <el-button size="small" type="primary" @click="queryDialogConfirm">
            确定
          </el-button>
        </span>
      </span>
    </el-dialog>
    <el-dialog title="帮助" :show-close="true" :visible.sync="showHelp">
      <img src="./imgs/itic-help.png" width="100%" alt class="eem-cont-c1" />
      <span slot="footer">
        <el-button size="small" type="primary" plain @click="showHelp = false">
          关闭
        </el-button>
      </span>
    </el-dialog>
    <!-- 事件确认弹窗 -->
    <el-dialog
      title="事件确认"
      :close-on-click-modal="false"
      :visible.sync="isShowConfirmWindow"
    >
      <event-confirm
        class="eem-cont-c1"
        ref="eventConfirm"
        @afterEventConfirmed="afterEventConfirmed"
        :confirmData="confirmData"
      ></event-confirm>
      <span slot="footer">
        <span class="device-Button">
          <el-button
            size="small"
            plain
            type="primary"
            @click="afterEventConfirmed"
          >
            关闭
          </el-button>
        </span>

        <span class="device-Button">
          <el-button
            size="small"
            type="primary"
            @click="
              () => {
                this.$refs.eventConfirm.saveConfirmData();
              }
            "
          >
            确定
          </el-button>
        </span>
      </span>
    </el-dialog>
    <!-- 波形展示弹窗 -->
    <el-dialog
      title="波形"
      :visible.sync="isShowWaveWindow"
      width="80%"
      @opened="updateWave"
      @closed="clearWave"
    >
      <Wave
        class="eem-cont-c1"
        :showTitle="true"
        :title="_.get(clickedEventLog, 'monitoredName', '--')"
        :showTable="true"
        :data="clickedEventLog"
      ></Wave>
      <span slot="footer">
        <el-button
          size="small"
          plain
          type="primary"
          @click="isShowWaveWindow = false"
        >
          关闭
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import commonApi from "@/api/custom.js";
import TimeRange from "eem-components/TimeRange";
import common from "eem-utils/common";
import { COMMON } from "@/store/constQuantity";
import Wave from "eem-components/Wave.vue";
import EventConfirm from "./widget/EventConfirm";
import ELECTRICAL_DEVICE from "@/store/electricaldevice.js";
export default {
  // 容忍度分析
  name: "Tolerance",
  components: {
    TimeRange,
    Wave,
    EventConfirm
  },

  props: {
    selectedMenu: {
      type: String
    },
    wrapTime: {
      type: Object
    }
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    modelLabels() {
      return ELECTRICAL_DEVICE.map(i => i.value);
    },
    token() {
      return this.$store.state.token;
    },
    enumerations() {
      var vm = this;
      return vm.$store.state.enumerations;
    }
  },

  watch: {
    currentNode: {
      deep: true,
      handler: function (val, oldVal) {
        if (
          this._.get(val, "data.id", -1) === this._.get(oldVal, "data.id", -2)
        )
          return;
        console.log("watch-currentNode");
        this.CetButton_query_statusTrigger_out();
      }
    },
    wrapTime: {
      deep: true,
      immediate: true,
      handler: function (val, oldVal) {
        if (!val) return;
        this.queryTime = [val.startTime, val.endTime];
      }
    },
    queryTime: {
      deep: true,
      handler: function (val, oldVal) {
        this.CetButton_query_statusTrigger_out();
      }
    },
    filterText(val) {
      this.$refs.powerTree.filter(val);
    },
    selectedMenu: {
      deep: true,
      handler: function (val, oldVal) {
        this.CetButton_query_statusTrigger_out();
      }
    }
  },

  data(vm) {
    // 当前主题颜色
    let themeActiveId = localStorage.getItem("omega_theme");
    return {
      activatedNum: 0,
      refreshTrigger_in: new Date().getTime(),
      treeData: [],
      treeProps: {
        children: "children",
        label: "name",
        isLeaf: "leaf"
      },
      currentNode: null,
      filterText: "",
      // 关键字输入框
      ElInput_keyword: {
        value: "",
        placeholder: "请输入内容",
        type: "text",
        size: "small",
        rows: 2,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElInput_keyword_change_out,
          input: this.ElInput_keyword_input_out
        }
      },
      CetButton2_1: {
        visible_in: true,
        disable_in: false,
        title: "高级查询",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton2_1_statusTrigger_out
        }
      },
      CetButton2_2: {
        visible_in: true,
        disable_in: false,
        title: "导出",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton2_2_statusTrigger_out
        }
      },
      firstVisited: true,
      // 查询按钮组件
      CetButton_query: {
        visible_in: true,
        disable_in: false,
        config: {
          title: "查询",
          type: "primary",
          plain: true
        }
      },
      queryTime: this.wrapTime
        ? [this.wrapTime.startTime, this.wrapTime.endTime]
        : common.initDateRange(),
      // ITIC组件
      CetChart2_ITIC: {
        //组件输入项
        inputData_in: null,
        config: {
          options: {
            // color: ["yellow", "green", "red"],
            title: {
              show: false,
              text: "",
              x: "center",
              textStyle: {
                fontSize: 16,
                fontWeight: "normal",
                color: "#999"
              }
            },
            graphic: [
              {
                type: "text",
                z: 100,
                left: "15%",
                top: "middle",
                style: {
                  fill: themeActiveId === "light" ? "#242424" : "#F0F1F2",
                  text: "A区-容忍区\n(设备可接受电压范围)",
                  font: "bolder 14px Microsoft YaHei"
                }
              },
              {
                type: "text",
                z: 100,
                left: "60%",
                top: "40%",
                style: {
                  fill: themeActiveId === "light" ? "#242424" : "#F0F1F2",
                  text: "B区-设备损坏区\n(一旦到达此区域，设备损坏)",
                  font: "bolder 14px Microsoft YaHei"
                }
              },
              {
                type: "text",
                z: 100,
                left: "65%",
                bottom: "8%",
                style: {
                  fill: themeActiveId === "light" ? "#242424" : "#F0F1F2",
                  text: "C区-设备无损坏区\n(设备功能不能正常发挥，\n但不至于对设备自身造成损坏)",
                  font: "bolder 14px Microsoft YaHei"
                }
              }
            ],
            grid: {
              show: "true",
              backgroundColor:
                themeActiveId === "light"
                  ? "rgba(31,32,34,0.05)"
                  : "rgba(250,251,251,0.05)",
              top: 35,
              right: 25,
              bottom: 35,
              left: 50
            },
            legend: {
              x: "center",
              y: "top"
            },
            toolbox: {
              itemSize: 25,
              top: 35,
              right: 25,

              feature: {
                dataZoom: {
                  yAxisIndex: "none"
                },
                restore: {}
              }
            },
            tooltip: {
              formatter(params) {
                const info = params.data[2];
                const monitoredName = info.monitoredName
                  ? info.monitoredName
                  : "--";
                let txt = "";
                txt += "设备名称：" + monitoredName + "<br />";
                txt +=
                  "时间：" +
                  vm.$moment(info.eventtime).format("YYYY-MM-DD HH:mm:ss.SSS") +
                  "<br />";

                txt +=
                  "事件类型：" +
                  vm.formatEnum(
                    null,
                    { property: "pqvariationeventtype" },
                    info.pqvariationeventtype
                  ) +
                  "<br />";
                txt += "电压幅值（%）：" + params.data[1] + "<br />";
                txt += "持续时间（s）：" + params.data[0] + "<br />";
                return txt;
              }
            },

            xAxis: {
              type: "log",
              min: 0.000001,
              interval: 10,
              max: 100,
              splitLine: {
                show: false
              },
              axisLabel: {
                formatter: (val, i) => {
                  if (i == 0 || i == 1 || i == 2) {
                    return val * 1000000 + "μs";
                  } else if (i == 5 || i == 3 || i == 4) {
                    return val * 1000 + "ms";
                  } else {
                    return val + "s";
                  }
                }
              }
            },
            yAxis: {
              max: 500,
              name: "电压幅值(%)",
              interval: 50,
              splitLine: {
                show: false
              }
            },
            series: [
              {
                name: "A区",
                data: [],
                type: "scatter",
                symbol: "rect",
                symbolSize: 12,
                markLine: {
                  symbol: "none",
                  silent: true,
                  data: [
                    {
                      yAxis: 100
                    }
                  ],
                  lineStyle: {
                    color: "red",
                    type: "solid",
                    width: 2.5
                  }
                },
                zlevel: 10,
                itemStyle: {
                  color: "#008001"
                }
              },
              {
                name: "B区",
                data: [],
                type: "scatter",
                symbol: "triangle",
                symbolSize: 12,
                zlevel: 10,
                itemStyle: {
                  color: "#EB8308"
                }
              },
              {
                name: "C区",
                data: [],
                type: "scatter",
                symbol: "triangle",
                symbolRotate: 180,
                symbolSize: 12,
                zlevel: 10,
                // 图标颜色修改
                itemStyle: {
                  color: "#D32A46"
                }
              },
              {
                data: [
                  [Math.pow(10, -0.398) / 1000, 500],
                  [Math.pow(10, 0) / 1000, 200],
                  [Math.pow(10, 0.477) / 1000, 140],
                  [Math.pow(10, 0.477) / 1000, 120],
                  [Math.pow(10, 1.3) / 1000, 120],
                  [Math.pow(10, 2.699) / 1000, 120],
                  [Math.pow(10, 2.699) / 1000, 110],
                  [Math.pow(10, 4) / 1000, 110],
                  [Math.pow(10, 5) / 1000, 110]
                ],
                type: "line",
                symbol: "none",
                // 区域颜色修改
                areaStyle: {
                  color:
                    themeActiveId === "light"
                      ? "rgba(41,176,97,0.2)"
                      : "rgba(13,134,255,0.2)",
                  origin: "end"
                },
                lineStyle: {
                  color: themeActiveId === "light" ? "#29B061" : "#0D86FF"
                }
              },
              {
                data: [
                  [Math.pow(10, 1) / 1000, 0],
                  [Math.pow(10, 1) / 1000, 70],
                  [Math.pow(10, 2.699) / 1000, 70],
                  [Math.pow(10, 2.699) / 1000, 80],
                  [Math.pow(10, 4) / 1000, 80],
                  [Math.pow(10, 4) / 1000, 90],
                  [Math.pow(10, 5) / 1000, 90]
                ],
                type: "line",
                symbol: "none",
                areaStyle: {
                  color:
                    themeActiveId === "light"
                      ? "rgba(255,194,76,0.2)"
                      : "rgba(252,185,44,0.2)"
                },
                lineStyle: {
                  color: themeActiveId === "light" ? "#29B061" : "#0D86FF"
                }
              }
            ]
          }
        }
      },
      tableData: [],
      queryDialogVisble: false,
      eventTypeGroup: {
        checkAll: false,
        checked: [],
        enum: [],
        isIndeterminate: false
      },
      tolerancebandGroup: {
        checkAll: false,
        checked: [],
        enum: [],
        isIndeterminate: false
      },
      // 用于记录高级查询中确定勾选的选项
      lastChecked: {
        eventTypeGroup: [],
        tolerancebandGroup: []
      },
      ENUM: {},
      // 事件统计
      eventStatistics: {
        dip: 0,
        inRegion: 0,
        interruption: 0,
        outRegion: 0,
        regionA: 0,
        regionB: 0,
        regionC: 0,
        swell: 0,
        total: 0,
        transients: 0,
        undefinedEvent: 0
      },
      currentPage: 1,
      pageSizes: [10, 20, 30, 40],
      pageSize: 10,
      pageTotal: 0,
      showHelp: false,
      confirmData: {},
      isShowConfirmWindow: false, // 显示事件确认弹框
      isShowWaveWindow: false,
      cachedWaveData: {},
      clickedEventLog: {}
    };
  },

  methods: {
    findDataByKey(...arg) {
      return common.findDataByKey(...arg);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1;
    },
    getTreeData() {
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [
          {
            filter: {
              expressions: [{ limit: 1, operator: "EQ", prop: "roomtype" }]
            },
            modelLabel: "room"
          }
        ],
        treeReturnEnable: true
      };
      ELECTRICAL_DEVICE.forEach(item => {
        data.subLayerConditions.push({ modelLabel: item.value });
      });
      commonApi.getPqNodeTree(data, this.projectId, true).then(res => {
        if (res.code === 0) {
          _this.treeData = res.data;
          console.log(this.treeData);
          console.log(_this.treeData);

          if (_this._.get(res.data, "[0]")) {
            const currentNode = _this._.get(res.data, "[0]");

            _this.$nextTick(() => {
              // console.log(currentNode.tree_id);
              _this.$refs.powerTree.setCurrentKey(currentNode.tree_id);
              _this.currentNode = _this.$refs.powerTree.getNode(
                currentNode.tree_id
              );
              // console.log(currentNode.tree_id);
              _this.$refs.powerTree.getNode(currentNode.tree_id).expand();
            });
          }
        }
      });
    },
    handleNodeClick(obj, node) {
      this.currentNode = node;
    },
    // keyword输出,方法名要带_out后缀
    ElInput_keyword_change_out(val) {
      if (!this.currentNode) return;
      this.CetButton_query_statusTrigger_out();
    },
    ElInput_keyword_input_out(val) {},
    handleShowHelp() {
      this.showHelp = true;
    },
    // 高级查询
    CetButton2_1_statusTrigger_out(val) {
      this.queryDialogVisble = true;
    },
    CetButton2_2_statusTrigger_out(val) {
      const vm = this;
      const pic = [this.$refs.itic.getDataURL()];
      common.downExcel(
        "/eem-service/v1/pq/event/tolerance/export",
        vm.getQueryParams(pic),
        vm.token
      );
    },
    // 查询
    CetButton_query_statusTrigger_out(val) {
      this.getToleranceEvent();
      this.getVariationEvent();
      this.getVariationEvent(false);
    },
    // 事件统计列表
    getToleranceEvent() {
      const params = this.getQueryParams();
      console.log(params);
      const initparam = {
        confirmStatus: [],
        endTime: params.endTime,
        eventTypes: params.eventTypes,
        keyWord: params.keyWord,
        node: {
          childSelectState: 0,
          children: [null],
          deviceIds: [],
          endPoint: true,
          id: params.node.id,
          modelLabel: params.node.modelLabel,
          name: "",
          startPoint: true
        },
        page: {
          index: params.page.index,
          limit: params.page.limit
        },
        pictures: [],
        projectId: this.projectId,
        startTime: params.startTime,
        toleranceBands: params.toleranceband,
        transientFaultDirections: [],
        waveStatus: 0
      };
      console.log(initparam);

      common.requestData(
        {
          url: "/eem-service/v1/pq/count/eventType/toleranceBand",
          data: initparam
        },
        res => {
          this.eventStatistics = res;
        }
      );
    },
    // 暂态事件容忍度分析列表
    getVariationEvent(uspage = true) {
      common.requestData(
        {
          url: "/eem-service/v1/pq/event",
          data: this.getQueryParams([], uspage)
        },
        (data, res) => {
          if (uspage) {
            this.pageTotal = res.total;
            this.tableData = this.formatTableData(data);
          } else {
            this.setChartData(data);
          }
        }
      );
    },
    // 将数据分类 放到ITIC图表中
    setChartData(data) {
      const A = [];
      const B = [];
      const C = [];
      data.forEach(item => {
        if (item.toleranceband === 2)
          A.push([item.duration / 1000 / 1000, item.magnitude, item]);
        else if (item.toleranceband === 3)
          B.push([item.duration / 1000 / 1000, item.magnitude, item]);
        else if (item.toleranceband === 4)
          C.push([item.duration / 1000 / 1000, item.magnitude, item]);
      });
      const options = this._.cloneDeep(this.CetChart2_ITIC.config.options);
      options.series[0].data = A;
      options.series[1].data = B;
      options.series[2].data = C;
      this.CetChart2_ITIC.config.options = options;
    },
    // 格式化部分枚举类型
    formatTableData(data) {
      const get = common.findDataByKey;
      console.log(data);
      return data.map(item => {
        item.confirmeventstatus = get(item, "confirmeventstatus", 1);
        const format = {
          pqvariationeventtype$text: get(
            this._.find(this.ENUM[COMMON.PQ_VARIATION_EVENTTYPE], [
              "id",
              item.pqvariationeventtype
            ]),
            "text"
          ),
          transientfaultdirection$text: get(
            this._.find(this.ENUM[COMMON.TRANSIENT_FALUT_DIRECTION], [
              "id",
              item.transientfaultdirection
            ]),
            "text"
          ),
          toleranceband$text: get(
            this._.find(this.ENUM[COMMON.TOLERANCE_BAND], [
              "id",
              item.toleranceband
            ]),
            "text"
          ),
          confirmeventstatus$text: get(
            this._.find(this.ENUM[COMMON.CONFIRM_EVENT_STATUS], [
              "id",
              item.confirmeventstatus
            ]),
            "text",
            "未处理"
          )
        };
        return Object.assign(item, format);
      });
    },
    getQueryParams(pic = [], uspage = true) {
      let node = null;
      if (this.currentNode && this.currentNode.data) {
        node = {
          id: this.currentNode.data.id,
          modelLabel: this.currentNode.data.modelLabel
        };
      }

      const params = {
        startTime: this.queryTime[0],
        endTime: this.queryTime[1],
        // 被检测设备类型
        // deviceTypes: ["linesegmentwithswitch"],
        // 事件等级
        // "eventClasses": [],
        // 高级查询中的事件类型
        eventTypes:
          this.lastChecked.eventTypeGroup.length == 0
            ? [-1]
            : [...this.lastChecked.eventTypeGroup],
        // 搜索关键字
        keyWord: this.ElInput_keyword.value,
        // 电能质量类型的监测设备 必传，目前只有 9  // 2019年11月20日17:07:06s
        meterTypes: [9],
        pictures: pic,
        // 选中节点树中的测点 monitoredid
        node,
        // 高级查询中的区域
        toleranceBands:
          this.lastChecked.tolerancebandGroup.length == 0
            ? [-1]
            : [...this.lastChecked.tolerancebandGroup],
        transientFaultDirections: [],
        projectId: this.projectId
      };
      if (uspage) {
        params.page = {
          index: (this.currentPage - 1) * this.pageSize,
          limit: this.pageSize
        };
      } else {
        params.page = {
          index: 0,
          limit: 99999
        };
      }
      return params;
    },
    // 初始化每一个复选组的勾选状态
    handleGroupCheckStatus() {
      this.handleCheckedChange(this.eventTypeGroup.checked, "eventTypeGroup");
      this.handleCheckedChange(
        this.tolerancebandGroup.checked,
        "tolerancebandGroup"
      );
    },
    handleCheckAll(val, type) {
      const arr = val ? this[type].enum.map(item => item.id) : [];
      this[type].checked = arr;
      this[type].isIndeterminate = false;
    },
    handleCheckedChange(val, type) {
      const checkedCount = val.length;
      this[type].checkAll = checkedCount === this[type].enum.length;
      this[type].isIndeterminate =
        checkedCount > 0 && checkedCount < this[type].enum.length;
    },
    queryDialogCancel() {
      this.queryDialogVisble = false;
      this.eventTypeGroup.checked = [...this.lastChecked.eventTypeGroup];
      this.tolerancebandGroup.checked = [
        ...this.lastChecked.tolerancebandGroup
      ];
      this.handleCheckedChange(this.eventTypeGroup.checked, "eventTypeGroup");
      this.queryDialogVisble = false;
      this.handleCheckedChange(
        this.tolerancebandGroup.checked,
        "tolerancebandGroup"
      );
    },
    queryDialogConfirm() {
      this.lastChecked.eventTypeGroup = [...this.eventTypeGroup.checked];
      this.lastChecked.tolerancebandGroup = [
        ...this.tolerancebandGroup.checked
      ];
      this.queryDialogVisble = false;
      this.CetButton_query_statusTrigger_out();
    },
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.getVariationEvent();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getVariationEvent();
    },
    // 加载枚举数据
    loadEnumrations(...args) {
      this.ENUM = {};
      args.forEach(item => {
        this.ENUM[item] = this.$store.state.enumerations[item];
        // this.ENUM[item] = this.enumerations[item];
        console.log(this.ENUM[item]);
      });

      this.eventTypeGroup.enum = this.ENUM.pqvariationeventtype;
      this.tolerancebandGroup.enum = this.ENUM.toleranceband;
      if (this.firstVisited) {
        this.eventTypeGroup.checked = this.ENUM.pqvariationeventtype.map(
          it => it.id
        );
        this.tolerancebandGroup.checked = this.ENUM.toleranceband.map(
          it => it.id
        );
        this.lastChecked.eventTypeGroup = [...this.eventTypeGroup.checked];
        this.lastChecked.tolerancebandGroup = [
          ...this.tolerancebandGroup.checked
        ];
        this.handleGroupCheckStatus();
        this.firstVisited = false;
      }
    },
    formatDate(row, column, cellValue, index) {
      return common.formatDate(cellValue, "YYYY-MM-DD HH:mm:ss.SSS");
    },
    formatEnum(row, column, cellValue) {
      const get = common.findDataByKey;
      const vm = this;
      let data = [];
      switch (column.property) {
        case "pqvariationeventtype":
          data = vm.ENUM.pqvariationeventtype;
          break;
        case "transientfaultdirection":
          data = vm.ENUM.transientfaultdirection;
          break;
        case "toleranceband":
          data = vm.ENUM.toleranceband;
          break;
        case "confirmeventstatus":
          data = vm.ENUM.confirmeventstatus;
          break;
      }

      return get(vm._.find(data, ["id", cellValue]), "text");
    },
    showConfirmWindow(obj) {
      if (!obj || obj.confirmeventstatus != 1) {
        return;
      }
      this.confirmData = {
        updatetime: new Date(),
        data: obj
      };
      this.isShowConfirmWindow = true;
    },
    afterEventConfirmed(data) {
      const vm = this;
      vm.isShowConfirmWindow = false;
      if (!data || !Array.isArray(data)) {
        return;
      }
      // TODO: 此处的更新数据逻辑需要调整，暂时不清楚有什么更高效的方法
      for (const item of data) {
        for (const log of vm.tableData) {
          if (log.id === item.id) {
            log.confirmeventstatus = item.confirmeventstatus;
            log.remark = item.remark;
            log.updatetime = item.updatetime;
            log.operator = item.operator;
            break;
          }
        }
      }
    },
    formatTable(row, column, cellValue, index) {
      var vm = this;
      if (!cellValue) {
        if (cellValue === 0 || cellValue === "") {
          return cellValue;
        } else {
          return "--";
        }
      }
      return cellValue;
    },
    showWaveWindow(obj) {
      var wavedata = this._.cloneDeep(obj);
      wavedata.deviceId = wavedata.srcdeviceid;
      wavedata.waveTime = wavedata.waveformlogtime;
      this.cachedWaveData = wavedata;
      this.isShowWaveWindow = true;
    },

    updateWave() {
      this.clickedEventLog = { ...this.cachedWaveData };
    },

    clearWave() {
      this.cachedWaveData = {};
      this.clickedEventLog = {};
    },

    getEventStatus(cellValue) {
      if (cellValue === 3) {
        return "confirm";
      } else {
        return "unconfirm";
      }
    }
  },

  created: function () {
    this.loadEnumrations(
      COMMON.PQ_VARIATION_EVENTTYPE,
      COMMON.TRANSIENT_FALUT_DIRECTION,
      COMMON.TOLERANCE_BAND,
      COMMON.CONFIRM_EVENT_STATUS
    );
  },

  activated() {
    this.getTreeData();
    if (this.activatedNum) this.queryTime = common.initDateRange();
    this.activatedNum++;
  }
};
</script>
<style lang="scss" scoped>
.tolerance {
  width: 100%;
  height: 100%;
  position: relative;
  .confirm {
    cursor: not-allowed;
  }
  .unconfirm {
    @include font_color(Sta3);
    cursor: pointer;
  }
  .highlight {
    // color: #fff;
    @include font_color(T1);
  }
  .tolerance-box {
    height: 100%;
    .tolerance-content {
      height: 100%;
      .page-header {
        .exportbtn {
          line-height: normal;
        }
        .levelsreach {
          line-height: normal;
        }
        .datebtn {
          line-height: normal;
          .datebtn-box {
            min-width: 320px;
            float: right;
          }
          .keyword-box {
            width: 200px;
            float: right;
            line-height: normal;
          }
        }
      }
      .tolerance-main {
        overflow: auto;
        width: 100%;
        .table-terse {
          @include background_color(BG1);
          width: 100%;
          table-layout: fixed;
          :deep(tr th),
          tr td {
            padding-left: 8px;
            padding-right: 8px;
            box-sizing: border-box;
            text-align: left;
            border: 1px solid;
            @include border_color(B1);
            line-height: 32px;
          }
          :deep(tr th) {
            border-bottom: none;
          }
        }
        :deep(.el-icon-question) {
          cursor: pointer;
        }
        .caretb {
          color: #eb8308;
          font-size: 30px;
          line-height: 0;
          position: relative;
          top: 5px;
        }
        .caretc {
          color: #d32a46;
          font-size: 30px;
          line-height: 0;
          position: relative;
          top: 5px;
        }
      }
    }
  }
  :deep(.el-table__expand-icon > .el-icon) {
    color: #2d91e9;
  }

  .clickformore {
    cursor: pointer;
    @include font_size("Ab");
  }
  .square {
    height: 18px;
    width: 18px;
    display: inline-block;
    border-radius: 5px;
    line-height: 20px;
    font-size: 0;
    position: relative;
    top: 4px;
    margin-right: 10px;
  }
  .nowhite-space {
    white-space: nowrap;
    .cell {
      white-space: nowrap;
    }
  }
}
</style>
