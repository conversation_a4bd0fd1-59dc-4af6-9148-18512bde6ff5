<template>
  <div class="page eem-common">
    <el-container class="fullheight">
      <el-aside width="315px" class="eem-aside flex-column">
        <CetTree
          class="flex-auto"
          :selectNode.sync="CetTree_folder.selectNode"
          :checkedNodes.sync="CetTree_folder.checkedNodes"
          v-bind="CetTree_folder"
          v-on="CetTree_folder.event"
        ></CetTree>
        <div class="mtJ3" v-if="$checkPermission('videocamera_create')">
          <div class="fr more">
            <el-button class="moreText" type="primary" plain>
              {{ $T("更多") }}
            </el-button>
            <el-dropdown @command="handleCommand">
              <el-button
                class="moreIcon"
                type="primary"
                plain
                icon="el-icon-more"
              ></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="addRoot">
                  {{ $T("增加根节点") }}
                </el-dropdown-item>
                <el-dropdown-item command="rename" :disabled="!currentNode.id">
                  {{ $T("重命名") }}
                </el-dropdown-item>
                <el-dropdown-item
                  command="delete"
                  :disabled="
                    Boolean(
                      !currentNode.id ||
                        (currentNode.children && currentNode.children.length) ||
                        total
                    )
                  "
                >
                  {{ $T("删除节点") }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <CetButton
            class="fr mrJ1"
            v-bind="CetButton_addNode"
            v-on="CetButton_addNode.event"
          ></CetButton>
        </div>
      </el-aside>
      <el-container class="eem-container fullheight mlJ3 flex-column">
        <VideoTable
          :currentNode="currentNode"
          @getTotal="getTotal"
        ></VideoTable>
      </el-container>
    </el-container>

    <!-- 新建修改文件夹节点 -->
    <addNodeDialog
      v-bind="addNodeDialog"
      v-on="addNodeDialog.event"
    ></addNodeDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom";
import addNodeDialog from "./components/addNodeDialog.vue";
import VideoTable from "./components/VideoTable.vue";

export default {
  name: "videoConfig",
  components: {
    addNodeDialog,
    VideoTable
  },
  computed: {
    UserID() {
      return this.$store.state.userInfo.id;
    }
  },
  data() {
    return {
      CetTree_folder: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_folder_currentNode_out,
          parentList_out: this.CetTree_folder_parentList_out
        }
      },
      currentNode: {},
      command: "",
      CetButton_addNode: {
        visible_in: true,
        disable_in: true,
        title: $T("增加节点"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_addNode_statusTrigger_out
        }
      },
      addNodeDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: {},
        dialogTitle: "",
        event: {
          saveData_out: this.saveFolder
        }
      },
      total: 0
    };
  },
  watch: {},
  methods: {
    CetTree_folder_currentNode_out(val) {
      this.currentNode = val;
    },
    CetTree_folder_parentList_out(val) {
      // 树节点层级限制5级
      const limitLevel = 5;
      this.CetButton_addNode.disable_in = val.length >= limitLevel;
    },
    CetButton_addNode_statusTrigger_out(val) {
      this.addNodeDialog.dialogTitle = $T("增加节点");
      this.command = "addNode";
      this.addNodeDialog.inputData_in = {};
      this.addNodeDialog.openTrigger_in = new Date().getTime();
    },
    async saveFolder(val) {
      let data;
      let method;
      // 重命名
      if (this.command === "rename") {
        method = "videoRenameFolder";
        data = {
          id: this.currentNode.id,
          name: val.name
        };
      } else {
        method = "videoCreateFolder";
        data = [
          {
            parentid:
              this.command === "addRoot" ? undefined : this.currentNode.id,
            name: val.name
          }
        ];
      }
      const res = await customApi[method](data);
      if (res.code !== 0) {
        return;
      }
      this.addNodeDialog.closeTrigger_in = new Date().getTime();
      this.$message.success($T("保存成功!"));
      this.getTreeData();
    },
    handleCommand(command) {
      this.command = command;
      if (command === "delete") {
        this.$confirm($T("确定要删除所选项吗？"), $T("删除确认"), {
          type: "warning",
          distinguishCancelAndClose: true,
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消")
        }).then(() => {
          customApi.videoDeleteFolder(this.currentNode.id).then(res => {
            if (res.code === 0) {
              this.$message.success($T("删除成功!"));
              this.currentNode = {};
              this.getTreeData();
            }
          });
        });
        return;
      }
      this.addNodeDialog.dialogTitle =
        command === "addRoot" ? $T("增加根节点") : $T("重命名");
      this.addNodeDialog.inputData_in =
        command === "rename" ? this._.cloneDeep(this.currentNode) : {};
      this.addNodeDialog.openTrigger_in = new Date().getTime();
    },
    // 获取视频文件夹
    async getTreeData() {
      const data = {
        userId: this.UserID,
        tenantId: this.$store.state.projectTenantId
      };
      const res = await customApi.videoQueryFolders(data);
      if (res.code !== 0) {
        return;
      }
      this.CetTree_folder.inputData_in = res.data;
      this.CetTree_folder.selectNode = this._.isEmpty(this.currentNode)
        ? res.data[0]
        : this._.cloneDeep(this.currentNode);
    },
    getTotal(val) {
      this.total = val;
    }
  },
  activated() {
    this.currentNode = {};
    this.getTreeData();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .more {
    .moreText {
      // margin-right: -4px;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    .moreIcon {
      height: 32px;
      border-left: 0;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}
</style>
