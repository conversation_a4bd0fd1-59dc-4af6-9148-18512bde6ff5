<template>
  <div class="page eem-common">
    <div
      class="fullheight"
      v-show="!showDetail && !showDetailConfirm && !showInspectDetail"
    >
      <div class="fullheight flex-row">
        <div class="eem-aside fullheight flex-column" style="width: 240px">
          <div class="mbJ3">
            <span class="common-title-H2">{{ $T("工单状态统计") }}</span>
          </div>
          <div style="height: 200px; position: relative">
            <CetChart v-bind="CetChart_order"></CetChart>
            <div
              style="
                text-align: center;
                position: absolute;
                width: 100%;
                top: 80px;
              "
            >
              <div>
                <span class="chartLine">
                  <span class="text-overflow" style="font-size: 20px">
                    {{ totleNum }}
                  </span>
                  {{ $T("单") }}
                </span>
              </div>
              <div>
                <span>{{ $T("工单总数") }}</span>
              </div>
            </div>
          </div>
          <div class="flex-auto">
            <el-row
              :gutter="10"
              style="margin: 0px"
              class="ptJ1 pbJ1"
              v-for="(item, index) in orderList"
              :key="index"
            >
              <el-col :span="10" class="text-overflow">
                <i
                  class="echar-icon1 mrJ"
                  :style="{ background: item.itemStyle.color }"
                ></i>
                <el-tooltip :content="item.name" effect="light" placement="top">
                  <span>{{ item.name }}</span>
                </el-tooltip>
              </el-col>
              <el-col :span="7">
                <span>{{ filNumTotle(item.value, item.totle) }}%</span>
              </el-col>
              <el-col :span="7" class="text-overflow">
                <el-tooltip
                  :content="filNum(item.value) + $T('个')"
                  effect="light"
                  placement="top"
                >
                  <span>{{ filNum(item.value) }}{{ $T("个") }}</span>
                </el-tooltip>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="flex-auto fullheight mlJ3 flex-column">
          <div class="">
            <el-tabs
              v-model="selectedMenu"
              @tab-click="handleTabClick_out(selectedMenu)"
              class="eem-tabs-custom"
            >
              <el-tab-pane
                v-for="menu in menus"
                :label="menu"
                :name="menu"
                :key="menu"
              ></el-tab-pane>
            </el-tabs>
          </div>
          <div class="flex-auto mtJ3 eem-cont" style="overflow: auto">
            <div class="fullheight flex-column eem-min-width">
              <div class="clearfix">
                <CetButton
                  v-permission="'repairworkorder_create'"
                  class="fr mlJ1 mbJ3"
                  v-bind="CetButton_addOrder"
                  v-on="CetButton_addOrder.event"
                ></CetButton>
                <!-- refreshOrder按钮组件 -->
                <CetButton
                  class="fr mlJ1 mbJ3"
                  v-bind="CetButton_refresh"
                  v-on="CetButton_refresh.event"
                ></CetButton>
                <CetButton
                  class="fr mlJ1 mbJ3"
                  v-if="CetButton_examine.visible_in"
                  v-bind="CetButton_examine"
                  v-on="CetButton_examine.event"
                ></CetButton>
                <CetButton
                  class="fr mlJ1 mbJ3"
                  v-if="CetButton_print.visible_in"
                  v-bind="CetButton_print"
                  v-on="CetButton_print.event"
                ></CetButton>
                <div class="time fr mlJ1 mbJ3" :class="en && 'enTime'">
                  <div class="time-range">
                    <TimeRange
                      @change="handleTime_out"
                      :val.sync="queryTime"
                    ></TimeRange>
                  </div>
                </div>
                <div class="mlJ1 fr mbJ3">
                  <div class="lh32 fl">
                    <el-tooltip effect="light" :content="orderContent">
                      <i class="el-icon-question fsH3"></i>
                    </el-tooltip>
                  </div>
                  <customElSelect
                    class="fl mlJ"
                    size="small"
                    default-first-option
                    v-model="ElSelect_team.value"
                    v-bind="ElSelect_team"
                    v-on="ElSelect_team.event"
                    :prefix_in="$T('责任/运值班组')"
                  >
                    <ElOption
                      v-for="item in ElOption_team.options_in"
                      :key="item[ElOption_team.key]"
                      :label="item[ElOption_team.label]"
                      :value="item[ElOption_team.value]"
                    ></ElOption>
                  </customElSelect>
                </div>
                <div class="mlJ1 fr mbJ3">
                  <customElSelect
                    default-first-option
                    v-model="ElSelect_state.value"
                    v-bind="ElSelect_state"
                    v-on="ElSelect_state.event"
                    :prefix_in="$T('工单状态')"
                  >
                    <ElOption
                      v-for="item in ElOption_state.options_in"
                      :key="item[ElOption_state.key]"
                      :label="item[ElOption_state.label]"
                      :value="item[ElOption_state.value]"
                    ></ElOption>
                  </customElSelect>
                </div>
                <div class="mlJ1 fr mbJ3">
                  <customElSelect
                    default-first-option
                    v-model="ElSelect_tasklevel.value"
                    v-bind="ElSelect_tasklevel"
                    v-on="ElSelect_tasklevel.event"
                    :prefix_in="$T('工单等级')"
                  >
                    <ElOption
                      v-for="item in ElOption_tasklevel.options_in"
                      :key="item[ElOption_tasklevel.key]"
                      :label="item[ElOption_tasklevel.label]"
                      :value="item[ElOption_tasklevel.value]"
                    ></ElOption>
                  </customElSelect>
                </div>
                <ElInput
                  class="search-input fr mbJ3"
                  suffix-icon="el-icon-search"
                  :placeholder="$T('输入工单号或故障描述以检索')"
                  v-model="filterText"
                  v-bind="ElInput_keyword"
                  v-on="ElInput_keyword.event"
                ></ElInput>
              </div>
              <div class="flex-auto">
                <CetTable
                  ref="CetTable"
                  class="eem-table-custom"
                  :data.sync="CetTable_1.data"
                  :dynamicInput.sync="CetTable_1.dynamicInput"
                  v-bind="CetTable_1"
                  v-on="CetTable_1.event"
                  row-key="id"
                  @selection-change="handleSelectionChange"
                >
                  <el-table-column
                    v-if="
                      selectedMenu === $T('待维修') ||
                      selectedMenu === $T('待审核')
                    "
                    type="selection"
                    width="40"
                    align="left"
                    :selectable="canConfirm"
                  ></el-table-column>
                  <template v-for="(column, index) in Columns_Order">
                    <el-table-column
                      v-if="column.custom && column.custom === 'tag'"
                      v-bind="column"
                      :key="index"
                      class-name="font0 hand"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <el-tag
                          size="small"
                          class="text-middle font14 nowrap"
                          :effect="column.tagEffect"
                          :type="
                            column.tagTypeFormatter
                              ? column.tagTypeFormatter(scope.row, scope.column)
                              : 'primary'
                          "
                          :color="
                            column.colorTypeFormatter
                              ? column.colorTypeFormatter(
                                  scope.row,
                                  scope.column
                                )
                              : '#fff'
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : scope.row[column.prop]
                          }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else-if="column.custom && column.custom === 'button'"
                      v-bind="column"
                      :key="index"
                      class-name="font0"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <span
                          class="clickformore"
                          @click.stop="
                            column.onButtonClick
                              ? column.onButtonClick(scope.row, scope.$index)
                              : void 0
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : column.text
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    ></el-table-column>
                  </template>
                </CetTable>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <orderDetail
      v-show="showDetail"
      @goBack="goBack"
      :inputData_in="orderMsg_in"
    ></orderDetail>
    <inspectOrderDetail
      v-show="showInspectDetail"
      @goBack="goBack"
      :isShow_in="true"
      :inputData_in="inspectOrderMsg_in"
    ></inspectOrderDetail>
    <DetailConfirm
      v-show="showDetailConfirm"
      @goBack="goBack"
      :inputData_in="orderMsg_in"
    ></DetailConfirm>
    <CreateOrder
      @confirm_out="createOrder_confirm_out"
      :visibleTrigger_in="createOrder.visibleTrigger_in"
      :closeTrigger_in="createOrder.closeTrigger_in"
      :inputData_in="createOrder.inputData_in"
    />
    <toExamine
      :visibleTrigger_in="toExamine.visibleTrigger_in"
      :closeTrigger_in="toExamine.closeTrigger_in"
      :inputData_in="toExamine.inputData_in"
      :codes_in="toExamine.codes_in"
      @confirm_out="toExamine_confirm_out"
    />
    <!-- 维修工单打印 -->
    <div id="printContent11" style="display: none">
      <printOrder
        :inputData_in="printOrder.inputData_in"
        :printOrderList_in="printOrder.printOrderList_in"
      ></printOrder>
    </div>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import common from "eem-utils/common.js";
import CreateOrder from "./dialog/CreateOrder";
import orderDetail from "./dialog/Detail";
import inspectOrderDetail from "../xjOrder/dialog/Detail";
import DetailConfirm from "./dialog/DetailConfirm";
import toExamine from "./dialog/toExamine";
import printOrder from "./dialog/printOrder.vue";
import TimeRange from "eem-components/TimeRange";
import { measureTextWidth } from "eem-utils/measure.js";
const WORKSHEET_STATUS_TAG_COLORS = [
  "#fff",
  "#ff5a5a",
  "#0026AE",
  "#1b89b7",
  "#ffa32b",
  "#FF8A45",
  "#16a66c",
  "#58D9F9",
  "#9e9e9e"
];
const WORKSHEET_STATUS_TAG_NAMES = [
  "--",
  $T("待确认"),
  $T("待维修"),
  $T("待审核"),
  $T("被退回"),
  $T("已废弃"),
  $T("已完成"),
  $T("异常单"),
  $T("已超时")
];

export default {
  name: "repairorder",
  components: {
    CreateOrder,
    orderDetail,
    DetailConfirm,
    toExamine,
    TimeRange,
    printOrder,
    inspectOrderDetail
  },
  data(vm) {
    return {
      orderContent:
        $T("根据用户所属班组进行过滤，如果当前班组用户是运值班组用户，") +
        $T("会根据运值确认班组进行过滤，否则会根据责任班组进行过滤"),
      totleNum: "",
      orderList: [],
      menus: [
        $T("待确认"),
        $T("待维修"),
        $T("待审核"),
        $T("被退回"),
        $T("已完成"),
        $T("全部工单")
      ],
      selectedMenu: "",
      overTimeOnly: false,
      // addOrder组件
      CetButton_addOrder: {
        visible_in: true,
        disable_in: false,
        title: $T("新建"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_addOrder_statusTrigger_out
        }
      },
      // refresh组件
      CetButton_refresh: {
        visible_in: true,
        disable_in: false,
        title: $T("刷新"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_refresh_statusTrigger_out
        }
      },
      // examine组件
      CetButton_examine: {
        visible_in: false,
        disable_in: true,
        title: $T("批量审核"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_examine_statusTrigger_out
        }
      },
      // examine组件
      CetButton_print: {
        visible_in: true,
        disable_in: false,
        title: $T("批量打印"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_print_statusTrigger_out
        }
      },
      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryRepairWorkOrder",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [
            { name: "code_in", operator: "EQ", prop: "code" },
            { name: "teamId_in", operator: "EQ", prop: "teamId" },
            { name: "startTime_in", operator: "EQ", prop: "startTime" },
            { name: "endTime_in", operator: "EQ", prop: "endTime" },
            {
              name: "workSheetStatus_in",
              operator: "EQ",
              prop: "workSheetStatus"
            },
            { name: "overTimeOnly_in", operator: "EQ", prop: "overTimeOnly" },
            { name: "tasklevel_in", operator: "EQ", prop: "tasklevel" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: $T("合计")
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {},
        exportFileName: "",
        event: {},
        highlightCurrentRow: false
      },
      Columns_Order: [],
      // order组件
      CetChart_order: {
        //组件输入项
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "item"
          },
          legend: {
            show: false
          },
          series: [
            {
              name: "",
              type: "pie",
              radius: ["70%", "85%"],
              label: {
                show: false
              },
              data: []
            }
          ]
        }
      },
      // 关键字组件
      ElInput_keyword: {
        value: "",
        event: {
          change: this.ElInput_keyword_change_out
        }
      },
      filterText: "",
      // 工单状态下拉框
      ElSelect_state: {
        value: 0,
        style: {
          width: "140px"
        },
        event: {
          change: this.stateChange_out
        }
      },
      ElOption_state: {
        options_in: [
          {
            id: 0,
            name: $T("全部")
          },
          {
            id: false,
            name: $T("正常")
          },
          {
            id: true,
            name: $T("超时")
          }
        ],
        key: "id",
        value: "id",
        label: "name"
      },
      // 工单等级下拉框
      ElSelect_tasklevel: {
        value: 0,
        style: {
          width: "140px"
        },
        event: {
          change: this.levelChange_out
        }
      },
      ElOption_tasklevel: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text"
      },
      // 责任班组下拉框
      ElSelect_team: {
        value: 0,
        style: {
          width: "180px"
        },
        event: {
          change: this.teamChange_out
        }
      },
      ElOption_team: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name"
      },

      queryTime: [
        this.$moment().startOf("month").valueOf(),
        this.$moment().endOf("month").valueOf() + 1
      ],
      createOrder: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      toExamine: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        codes_in: []
      },
      printOrder: {
        inputData_in: {},
        printOrderList_in: [
          {
            orderMsg: {},
            eventPlanTable_in: []
          }
        ]
      },
      showDetail: false,
      showDetailConfirm: false,
      showInspectDetail: false,
      orderMsg_in: {},
      inspectOrderMsg_in: {},
      orderMsg: {},
      selecteOrders: [],
      isInit: true,
      isPrintOrderOk: false
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },
  watch: {
    overTimeOnly: function (val) {
      this.CetTable_1.dynamicInput.overTimeOnly_in = !!val;
      this.resfresTable();
    }
  },
  methods: {
    ElInput_keyword_change_out(val) {
      this.CetTable_1.dynamicInput.code_in = val ? this._.cloneDeep(val) : "";
      this.resfresTable();
    },
    // 工单状态下拉框
    stateChange_out(val) {
      this.CetTable_1.dynamicInput.overTimeOnly_in = this._.cloneDeep(val);
      this.resfresTable();
    },
    // 工单等级
    levelChange_out(val) {
      this.CetTable_1.dynamicInput.tasklevel_in = this._.cloneDeep(val);
      this.resfresTable();
    },
    // 责任班组下拉框
    teamChange_out(val) {
      this.CetTable_1.dynamicInput.teamId_in = val ? this._.cloneDeep(val) : "";
      this.resfresTable(2);
    },
    handleTime_out(val) {
      this.CetTable_1.dynamicInput.startTime_in = val[0]
        ? this._.cloneDeep(val[0])
        : "";
      this.CetTable_1.dynamicInput.endTime_in = val[1]
        ? this._.cloneDeep(val[1])
        : "";
      this.resfresTable(2);
    },
    canConfirm(row, column) {
      if (this.selectedMenu === $T("待维修")) {
        return true;
      } else {
        const isOk = this._.get(row, "userTaskConfig.authorized", false);
        return !!isOk;
      }
    },
    handleSelectionChange(val) {
      this.selecteOrders = this._.cloneDeep(val);
      if (val && val.length > 0) {
        this.CetButton_examine.disable_in = false;
        this.CetButton_print.disable_in = false;
      } else {
        this.CetButton_examine.disable_in = true;
        this.CetButton_print.disable_in = true;
      }
    },
    handleTabClick_out(val) {
      // eslint-disable-next-line camelcase
      const Columns_Order = [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 60,
          label: "#",
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "code",
          minWidth: 100,
          width: "200",
          label: $T("工单号"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "createtime",
          minWidth: 120,
          width: "160",
          label: $T("创建时间"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatDateCol("YYYY-MM-DD HH:mm:ss")
        },
        {
          type: "",
          prop: "inspectObject",
          minWidth: 100,
          width: "",
          label: $T("维修对象"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.filDevicelist_out
        },
        {
          type: "",
          prop: "timeconsumeplan",
          minWidth: "80",
          width: "",
          label: $T("预计耗时"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formTimeconsumeplan_out
        },
        {
          type: "",
          prop: "taskLevelName",
          minWidth: 100,
          width: "",
          label: $T("等级"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "repairTypeName",
          minWidth: "80",
          width: "",
          label: $T("维修方式"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "teamName",
          minWidth: "80",
          width: "",
          label: $T("责任班组"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "inspectTeamName",
          minWidth: "",
          width: "120",
          label: $T("运值确认班组"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "faultdescription",
          minWidth: "80",
          width: "",
          label: $T("故障描述"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ];
      const inputData = this.CetTable_1.dynamicInput;
      if (val === $T("待确认")) {
        Columns_Order.splice(6, 1);
        this.CetTable_1.dynamicInput.workSheetStatus_in = 1;
        this.Columns_Order = [
          // eslint-disable-next-line camelcase
          ...Columns_Order,
          {
            type: "",
            prop: "",
            width: "120",
            fixed: "right",
            label: $T("操作"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            custom: "button",
            text: $T("查看并确认"),
            buttonFormatter: null,
            onButtonClick: this.showOrderDetail,
            formatter: this.formatModeBtn_out
          }
        ];
      } else if (val === $T("待维修")) {
        inputData.workSheetStatus_in = 2;
        this.Columns_Order = [
          // eslint-disable-next-line camelcase
          ...Columns_Order,
          {
            type: "",
            prop: "",
            width: "120",
            fixed: "right",
            label: $T("操作"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            custom: "button",
            text: $T("查看并维修"),
            buttonFormatter: null,
            onButtonClick: this.showOrderDetail,
            formatter: this.formatInputBtn_out
          }
        ];
      } else if (val === $T("待审核")) {
        inputData.workSheetStatus_in = 3;
        this.Columns_Order = [
          // eslint-disable-next-line camelcase
          ...Columns_Order,
          {
            type: "",
            prop: "",
            width: "120",
            fixed: "right",
            label: $T("操作"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            custom: "button",
            text: $T("查看并审核"),
            buttonFormatter: null,
            onButtonClick: this.showOrderDetail,
            formatter: this.formatExamineBtn_out
          }
        ];
      } else if (val === $T("被退回")) {
        inputData.workSheetStatus_in = 4;
        this.Columns_Order = [
          // eslint-disable-next-line camelcase
          ...Columns_Order,
          {
            type: "",
            prop: "",
            width: "60",
            fixed: "right",
            label: $T("操作"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: null,
            custom: "button",
            text: $T("详情"),
            buttonFormatter: null,
            onButtonClick: this.showOrderDetail
          }
        ];
      } else if (val === $T("已完成")) {
        Columns_Order.splice(5, 0, {
          type: "",
          prop: "timeconsumereal",
          minWidth: "100",
          width: 80,
          label: $T("实际耗时"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.timeconsumereal_out
        });
        inputData.workSheetStatus_in = 6;
        this.Columns_Order = [
          // eslint-disable-next-line camelcase
          ...Columns_Order,
          {
            type: "",
            prop: "inspectResult",
            minWidth: "80",
            width: "",
            label: $T("完成状态"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: common.formatTextCol()
          },
          {
            type: "",
            prop: "",
            width: "60",
            fixed: "right",
            label: $T("操作"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: null,
            custom: "button",
            text: $T("详情"),
            buttonFormatter: null,
            onButtonClick: this.showOrderDetail
          }
        ];
      } else if (val === $T("全部工单")) {
        Columns_Order.splice(5, 0, {
          type: "",
          prop: "timeconsumereal",
          minWidth: "100",
          width: "",
          label: $T("实际耗时"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.timeconsumereal_out
        });
        inputData.workSheetStatus_in = 0;
        this.Columns_Order = [
          // eslint-disable-next-line camelcase
          ...Columns_Order,
          {
            type: "",
            prop: "workSheetStatusName",
            // minWidth: 90,
            width: measureTextWidth($T("待巡检"), "14px") + 46,
            label: $T("工单状态"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            custom: "tag",
            tagEffect: "light",
            colorTypeFormatter: this.statusColorTypeFormatter,
            // eslint-disable-next-line no-dupe-keys
            formatter: this.statusFormatter
          },
          {
            type: "",
            prop: "inspectResult",
            minWidth: "",
            width: "80",
            label: $T("完成状态"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: common.formatTextCol()
          },
          {
            type: "",
            prop: "",
            width: "60",
            fixed: "right",
            label: $T("操作"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: null,
            custom: "button",
            text: $T("详情"),
            buttonFormatter: null,
            onButtonClick: this.showOrderDetail
          }
        ];
      }
      if (val === $T("待审核")) {
        this.CetButton_examine.visible_in = true;
        this.CetButton_examine.disable_in = true;
      } else {
        this.CetButton_examine.visible_in = false;
      }
      if (val === $T("待维修")) {
        this.CetButton_print.visible_in = true;
        this.CetButton_print.disable_in = true;
      } else {
        this.CetButton_print.visible_in = false;
      }
      this.resfresTable();
      this.$refs.CetTable.$refs.cetTable.doLayout();
    },
    changeMenu(val) {
      this.selectedMenu = val;
    },
    //初始化入口
    init() {
      this.isInit = true;
      this.selectedMenu = $T("待确认");
      this.filterText = "";
      // this.overTimeOnly = false;
      this.queryTime = [
        this.$moment().startOf("month").valueOf(),
        this.$moment().endOf("month").valueOf() + 1
      ];
      this.CetTable_1.dynamicInput = {
        teamId_in: 0,
        code_in: "",
        endTime_in: this.$moment().endOf("month").valueOf() + 1,
        startTime_in: this.$moment().startOf("month").valueOf(),
        workSheetStatus_in: 1,
        overTimeOnly_in: 0,
        tasklevel_in: 0
      };
      this.handleTabClick_out(this.selectedMenu);
      this.getTeam_out();
      const tasklevelList =
        this.$store.state.enumerations.worksheettasklevel || [];
      let resData = [
        {
          id: 0,
          text: $T("全部")
        }
      ];
      resData = resData.concat(tasklevelList);
      this.ElSelect_tasklevel.value = 0;
      this.ElOption_tasklevel.options_in = this._.cloneDeep(resData);
    },
    //重新刷新页面
    reset() {
      if (this.selectedMenu) {
        this.queryRepairWorkOrderCount_out();
        this.handleTabClick_out(this.selectedMenu);
      }
    },
    //查询表格数据
    resfresTable(type = 1) {
      if (!this.isInit) {
        type !== 1 && this.queryRepairWorkOrderCount_out();
        this.CetTable_1.queryTrigger_in = new Date().getTime();
      }
    },
    // 获取班组列表信息
    getTeam_out() {
      const _this = this;
      const params = {};
      customApi.queryInspectorTeamWithOutUser(params).then(res => {
        if (res.code === 0) {
          this.isInit = false;
          const data = this._.get(res, "data", []) || [];
          let resData = [
            {
              id: 0,
              name: $T("全部")
            }
          ];
          resData = resData.concat(data);
          const teamId = 0;
          _this.ElOption_team.options_in = _this._.cloneDeep(resData);
          _this.ElSelect_team.value = teamId;
          _this.teamChange_out(teamId);
        }
      });
    },
    //查询工单数量
    queryRepairWorkOrderCount_out() {
      const params = {
        endTime: this.$moment(this.queryTime[1]).valueOf(),
        startTime: this.$moment(this.queryTime[0]).valueOf(),
        workSheetStatuses: [1, 2, 3, 4, 5, 6, 7, 8],
        taskType: 4,
        teamId: this.ElSelect_team.value || null
      };
      customApi.queryRepairWorkOrderCount(params).then(res => {
        if (res.code === 0) {
          const list = res.data || [];
          let num1 = null;
          let num2 = null;
          let num3 = null;
          let num4 = null;
          let num5 = null;
          let totle = null;
          list.forEach(item => {
            if ([1].includes(item.workOrderStatus)) {
              num1 = item.count;
            } else if ([2].includes(item.workOrderStatus)) {
              num2 = item.count;
            } else if ([3].includes(item.workOrderStatus)) {
              num3 = item.count;
            } else if ([6].includes(item.workOrderStatus)) {
              num4 = item.count;
            } else if ([4].includes(item.workOrderStatus)) {
              num5 = item.count;
            }
            if ([1, 2, 3, 4, 6].includes(item.workOrderStatus) && item.count) {
              totle += item.count;
            }
          });
          this.totleNum = totle;

          const dataList = [
            {
              value: num1,
              totle: totle,
              name: $T("待确认"),
              itemStyle: { color: "#ff5a5a" }
            },
            {
              value: num2,
              totle: totle,
              name: $T("待维修"),
              itemStyle: { color: "#0026AE" }
            },
            {
              value: num3,
              totle: totle,
              name: $T("待审核"),
              itemStyle: { color: "#1b89b7" }
            },
            {
              value: num5,
              totle: totle,
              name: $T("被退回"),
              itemStyle: { color: "#ffa32b" }
            },
            {
              value: num4,
              totle: totle,
              name: $T("已完成"),
              itemStyle: { color: "#16a66c" }
            }
          ];
          this.orderList = this._.cloneDeep(dataList);
          this.CetChart_order.options.series[0].data =
            this._.cloneDeep(dataList);
        }
      });
    },
    // addOrder输出
    CetButton_addOrder_statusTrigger_out(val) {
      this.createOrder.inputData_in = {};
      this.createOrder.visibleTrigger_in = new Date().getTime();
    },
    // 点击新建计划按钮
    CetButton_refresh_statusTrigger_out(val) {
      this.reset();
    },
    CetButton_examine_statusTrigger_out(val) {
      const list = this.selecteOrders || [];
      const codeArr = [];
      list.forEach(item => {
        codeArr.push(item.code);
      });

      this.toExamine.inputData_in = this._.cloneDeep({});
      this.toExamine.codes_in = this._.cloneDeep(list);
      this.toExamine.visibleTrigger_in = new Date().getTime();
    },
    CetButton_print_statusTrigger_out(val) {
      this.isPrintOrderOk = false;
      this.CetButton_print.disable_in = true;
      const list = this.selecteOrders || [];
      this.printOrder.printOrderList_in = [];
      const orderNum = list.length;
      let num = 0;
      if (orderNum > 10) {
        this.$message.warning($T("请选择10条工单数以下进行批量打印"));
        return;
      }
      list.forEach(item => {
        this.printOrder.inputData_in = this._.cloneDeep(item);
        let sparePartsName = "";
        const spareParts =
          this._.get(
            item,
            "maintenanceContentObj.sparePartsReplaceRecords",
            []
          ) || [];
        spareParts.forEach((item, index) => {
          if (index) {
            sparePartsName += " , ";
          }
          sparePartsName += item.name + $T("备件") + item.number + item.unit;
        });
        if (sparePartsName) {
          sparePartsName = "(" + sparePartsName + ")";
        }
        item.sparePartsName = sparePartsName;
        this.queryExpertEventPlan_out(item, () => {
          num++;
          if (num === orderNum) {
            this.isPrintOrderOk = true;
          }
          this.printOrder.printOrderList_in.push({
            orderMsg: item,
            eventPlanTable_in: item.eventPlanTable
          });
        });
      });

      const interval = setInterval(() => {
        if (this.isPrintOrderOk) {
          this.isPrintOrderOk = false;
          this.CetButton_print.disable_in = false;
          const newStr = document.getElementById("printContent11").innerHTML;
          var wind = window.open(
            "",
            "newwindow",
            "top=0, left=0, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no"
          );
          wind.document.body.innerHTML = newStr;
          wind.print();
          // wind.close();
          if (interval) {
            clearInterval(interval);
          }
        }
      }, 500);
    },
    //获取获取预案列表
    queryExpertEventPlan_out(val, callback) {
      if (!val.faultscenariosid) {
        val.eventPlanTable = [];
        callback && callback();
        return;
      }
      var data = {
        scenariosId: val.faultscenariosid,
        limit: 3
      };
      customApi.queryExpertEventPlan(data).then(res => {
        if (res.code === 0) {
          const resData = res.data || [];
          const tabData = [];
          resData.forEach((item, index) => {
            item.solution = item.solution || "";
            tabData.push({
              id: index,
              groupName: item.name,
              groupList: item.solution.split("\n")
            });
          });
          val.eventPlanTable = this._.cloneDeep(tabData);
          callback && callback();
        }
      });
    },
    toExamine_confirm_out(val) {
      this.reset();
    },
    createOrder_confirm_out() {
      this.reset();
    },
    showOrderDetail(row) {
      new Promise((res, err) => {
        this.getRepairWorkOrder_out(row, res);
      }).then(data => {
        if (!data) {
          return;
        }
        if ([1, 2].includes(row.worksheetstatus)) {
          this.showDetailConfirm = true;
        } else {
          this.showDetail = true;
        }
      });
    },
    //获取巡检工单信息
    showInspectOrderDetail() {
      new Promise((res, err) => {
        this.getInspectkOrder_out(res);
      }).then(data => {
        this.showDetailConfirm = false;
        this.showInspectDetail = false;
        this.showDetail = false;
        if (!data) {
          return;
        }
        this.showInspectDetail = true;
      });
    },
    goBack(val) {
      if (val && val === 3) {
        this.showInspectOrderDetail();
      } else if (val) {
        this.showDetailConfirm = false;
        this.showInspectDetail = false;
        this.showDetail = false;
        this.reset();
      } else {
        this.showDetailConfirm = false;
        this.showInspectDetail = false;
        this.showDetail = false;
        this.$nextTick(() => {
          this.$refs.CetTable.$refs.cetTable.doLayout();
        });
      }
    },
    // 获取维修工单详情信息
    getRepairWorkOrder_out(row, callback) {
      var _this = this;
      if (!row.code) {
        callback && callback(false);
        return;
      }
      var data = {
        code: row.code
      };
      customApi.getRepairWorkOrder(data).then(res => {
        if (res.code === 0) {
          const resData = _this._.get(res, "data", {}) || {};
          _this.orderMsg_in = this._.cloneDeep(resData);
          callback && callback(true);
        } else {
          callback && callback(false);
        }
      });
    },
    // 获取巡检工单详情信息
    getInspectkOrder_out(callback) {
      var _this = this;
      if (this.orderMsg_in.sourcetype !== 3) {
        callback && callback(false);
        return;
      }
      var data = {
        workOrderId: this.orderMsg_in.sourceid
      };
      customApi.queryWorkOrderById(data).then(res => {
        if (res.code === 0) {
          const resData = _this._.get(res, "data", {}) || {};
          _this.inspectOrderMsg_in = this._.cloneDeep(resData);
          callback && callback(true);
        } else {
          callback && callback(false);
        }
      });
    },
    //过滤巡检对象
    filDevicelist_out(row, column, cellValue, index) {
      if (
        row.deviceplanrelationship_model &&
        row.deviceplanrelationship_model.length
      ) {
        const list = row.deviceplanrelationship_model;
        let inspectObject = "";
        list.forEach((item, index) => {
          if (index) {
            inspectObject += " | ";
          }
          inspectObject += item.devicename;
        });
        row.inspectObject = inspectObject;
        return inspectObject;
      } else {
        return "--";
      }
    },
    //过滤异常原因
    filAbnormal_out(row, column, cellValue, index) {
      if (
        row.worksheetabnormalreason_model &&
        row.worksheetabnormalreason_model.length
      ) {
        const list = row.worksheetabnormalreason_model;
        let abnormal = "";
        list.forEach((item, index) => {
          if (index) {
            abnormal += " | ";
          }
          abnormal += item.typeName;
        });
        row.abnormal = abnormal;
        return abnormal;
      } else {
        return "--";
      }
    },
    //过滤耗时
    formTimeconsumeplan_out(row, column, cellValue, index) {
      if (cellValue) {
        let str = "--";
        const format = "hh h mm min";
        if (cellValue || cellValue === 0) {
          const hour = Math.floor(cellValue / 3600000);
          const minute = Math.floor((cellValue - hour * 3600000) / 60000);
          if (
            format.indexOf("hh") !== -1 &&
            format.indexOf("mm") !== -1 &&
            format.indexOf("ss") === -1
          ) {
            str = format.replace(
              /(.*)hh(.*)mm(.*)/,
              "$1" + hour + "$2" + minute + "$3"
            );
          }
        }
        return str;
      } else {
        return $T("查看");
      }
    },
    //过滤查看并确认操作列表
    formatModeBtn_out(row, column, cellValue, index) {
      const authorized = this._.get(row, "userTaskConfig.authorized", false);
      if (authorized) {
        return $T("查看并确认");
      } else {
        return $T("查看");
      }
    },
    //过滤查看并维修操作列表
    formatInputBtn_out(row, column, cellValue, index) {
      const authorized = this._.get(row, "userTaskConfig.authorized", false);
      if (authorized) {
        return $T("查看并维修");
      } else {
        return $T("查看");
      }
    },
    //过滤查看并审核操作列表
    formatExamineBtn_out(row, column, cellValue, index) {
      const authorized = this._.get(row, "userTaskConfig.authorized", false);
      if (authorized) {
        return $T("查看并审核");
      } else {
        return $T("查看");
      }
    },
    //过滤实际耗时
    timeconsumereal_out(row, column, cellValue, index) {
      if (row.finishtime && row.executetime) {
        const timeconsumereal = row.finishtime - row.executetime;
        let str = "--";
        const format = "hh h mm min";
        if (timeconsumereal) {
          const hour = Math.floor(timeconsumereal / 3600000);
          const minute = Math.floor((timeconsumereal - hour * 3600000) / 60000);
          if (
            format.indexOf("hh") !== -1 &&
            format.indexOf("mm") !== -1 &&
            format.indexOf("ss") === -1
          ) {
            str = format.replace(
              /(.*)hh(.*)mm(.*)/,
              "$1" + hour + "$2" + minute + "$3"
            );
          }
        }
        return str;
      } else {
        return "--";
      }
    },
    filNum(val) {
      if ([undefined, null, NaN].includes(val)) {
        return "--";
      } else {
        return val;
      }
    },
    filNumTotle(val, totle = 1) {
      if ([undefined, null, NaN].includes(val)) {
        return "--";
      } else {
        return Number((val / totle) * 100).toFixed2(2);
      }
    },
    // 工单状态标签样式格式化
    statusColorTypeFormatter(row, column) {
      const cellValue = row.worksheetstatus || 0;
      return WORKSHEET_STATUS_TAG_COLORS[cellValue];
    },
    statusFormatter(row, column) {
      const cellValue = row.worksheetstatus || 0;
      return WORKSHEET_STATUS_TAG_NAMES[cellValue];
    }
  },
  activated: function () {
    this.showDetail = false;
    this.showDetailConfirm = false;
    this.showInspectDetail = false;
    this.$nextTick(() => {
      this.init();
    });
  }
};
</script>
<style lang="scss" scoped>
.page {
  height: 100%;
}
.search-input {
  width: 188px;
}
.device-Input {
  display: inline-block;
}
.check-box {
  display: inline-block;
}
.clickformore {
  cursor: pointer;
  @include font_color(ZS);
}
.querytime-type {
  margin-top: 12px;
  width: 260px;
}
.echar-icon1 {
  display: inline-block;
  width: 12px;
  height: 12px;
  // background-color: #ec3b3b;
}
.time {
  width: 320px;
  display: flex;
}
.enTime {
  width: 360px;
}
.time-range {
  flex: 1;
}
.text-overflow {
  display: inline-block;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.examine-btn {
  position: absolute;
  top: -30px;
}
.tip {
  display: inline-block;
  width: 20px;
  height: 20px;
  font-size: 14px;
  line-height: 20px;
  border-radius: 50%;
  text-align: center;
  @include background_color(ZS);
}
.eem-table-custom :deep(.el-tag) {
  @include font_color(T5, !important);
  border: none !important;
}
.chartLine {
  border-bottom: 1px solid;
  @include border_color(B1);
}
.enSelect {
  width: 250px !important;
}
.el-radio-button--small :deep(.el-radio-button__inner) {
  font-size: 14px;
}
// .enInput {
//   width: 250px !important;
// }
</style>
<style>
.btn-custom-confirm-display {
  display: none;
}

.custom-date-picker-inspect .el-picker-panel__footer .el-button--text {
  display: none !important;
}
</style>
