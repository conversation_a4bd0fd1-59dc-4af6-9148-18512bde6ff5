<template>
  <div class="fullfilled eem-cont flex-column">
    <div class="flex-row">
      <div class="flex-auto text-ellipsis title">
        <el-tooltip effect="light" :content="projectInfo?.name">
          <span class="common-title-H3">{{ projectInfo?.name }}</span>
        </el-tooltip>
      </div>
      <ElInput
        class="mlJ3"
        v-model.trim="CetTable_1.dynamicInput.keyword_in"
        v-bind="ElInput_search"
        v-on="ElInput_search.event"
      ></ElInput>
      <CetButton
        class="mlJ3"
        v-bind="CetButton_add"
        v-on="CetButton_add.event"
      ></CetButton>
    </div>
    <CetTable
      class="mtJ3 text-right"
      :data.sync="CetTable_1.data"
      :dynamicInput.sync="CetTable_1.dynamicInput"
      v-bind="CetTable_1"
      v-on="CetTable_1.event"
    >
      <template v-for="item in Columns_1">
        <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
      </template>
      <ElTableColumn
        label="操作"
        width="160"
        header-align="left"
        align="left"
        fixed="right"
      >
        <template slot-scope="scope">
          <span
            class="eem-row-handle"
            @click.stop="associationHandle(scope.row)"
          >
            关联节点
          </span>
          <span class="eem-row-handle mlJ1" @click.stop="editHandle(scope.row)">
            编辑
          </span>
          <span
            :class="{
              'eem-row-delete': !!scope.row.allowDelete,
              mlJ1: true,
              'eem-row-no-handle': !scope.row.allowDelete
            }"
            @click.stop="deleteHandle(scope.row)"
          >
            删除
          </span>
        </template>
      </ElTableColumn>
    </CetTable>
    <AddAndEdit
      v-bind="addAndEdit"
      v-on="addAndEdit.event"
      :deviceTypeList_in="deviceTypeList"
      :aggregationType_in="aggregationType"
      :aggregationCycle_in="aggregationCycle"
    />
    <RelationNode v-bind="relationNode" v-on="relationNode.event" />
  </div>
</template>

<script>
import AddAndEdit from "./addAndEdit.vue";
import RelationNode from "./relationNode.vue";
import customApi from "@/api/custom";
import common from "eem-utils/common.js";
export default {
  components: {
    AddAndEdit,
    RelationNode
  },
  computed: {
    projectInfo() {
      return this.$store.state.projectInfo;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  data() {
    return {
      // 设备类型
      deviceTypeList: null,
      // 聚合方式
      aggregationType: [
        {
          id: 3,
          name: "最大值"
        },
        {
          id: 4,
          name: "最小值"
        },
        {
          id: 11,
          name: "平均值"
        }
      ],
      // 聚合周期
      aggregationCycle: [
        {
          id: 12,
          name: "日"
        },
        {
          id: 14,
          name: "月"
        },
        {
          id: 17,
          name: "年"
        }
      ],
      ElInput_search: {
        value: "",
        placeholder: "请输入节点名称",
        "suffix-icon": "el-icon-search",
        style: {
          width: "200px"
        },
        event: {
          change: this.queryTableData
        }
      },
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: "新建方案",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "calculateSchemeList",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "keyword_in", operator: "EQ", prop: "keyword" },
            { name: "projectId_in", operator: "EQ", prop: "projectId" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {
          keyword_in: null,
          projectId_in: null
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {
          pageSize: 20,
          layout: "total,sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        highlightCurrentRow: false,
        event: {}
      },
      Columns_1: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          fixed: "left",
          width: "50" //绝对宽度
        },
        {
          prop: "name", // 支持path a[0].b
          label: "方案名称", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "300",
          formatter: common.formatTextCol()
        },
        {
          prop: "objectlabel", // 支持path a[0].b
          label: "设备类型", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "200",
          formatter: (row, column, cellValue) => {
            if (!this.deviceTypeList || !this.deviceTypeList.length)
              return "--";
            const obj = this.deviceTypeList.find(
              i => i.monitoredlabel === cellValue
            );
            return obj ? obj.name : "--";
          }
        },
        {
          prop: "quantityaggregationsetting_model", // 支持path a[0].b
          label: "DataID", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "450",
          formatter: (row, column, cellValue) => {
            if (!cellValue || !cellValue.length) return "--";
            return cellValue.map(i => i.dataid).join("、");
          }
        },
        {
          prop: "quantityaggregationtype_model", // 支持path a[0].b
          label: "聚合方式", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "200",
          formatter: (row, column, cellValue) => {
            if (!cellValue || !cellValue.length) return "--";
            return cellValue
              .map(item => {
                const obj = this.aggregationType.find(
                  i => i.id === item.aggregationtype
                );
                return obj ? obj.name : "--";
              })
              .join("、");
          }
        },
        {
          prop: "quantityaggregationcycle_model", // 支持path a[0].b
          label: "聚合周期", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "120",
          formatter: (row, column, cellValue) => {
            if (!cellValue || !cellValue.length) return "--";
            return cellValue
              .map(item => {
                const obj = this.aggregationCycle.find(
                  i => i.id === item.aggregationcycle
                );
                return obj ? obj.name : "--";
              })
              .join("、");
          }
        }
      ],
      addAndEdit: {
        openTrigger_in: Date.now(),
        inputData_in: null,
        event: {
          reloadTable: this.queryTableData
        }
      },
      relationNode: {
        openTrigger_in: Date.now(),
        inputData_in: null,
        event: {
          reloadTable: this.queryTableData
        }
      }
    };
  },
  methods: {
    async init() {
      this.CetTable_1.dynamicInput.projectId_in = this.projectId;
      this.CetTable_1.dynamicInput.keyword_in = "";
      this.deviceTypeList = await this.getDeviceType();
      this.queryTableData();
    },
    async getDeviceType() {
      const res = await customApi.calculateMonitoredlabel();
      return this._.get(res, "data", []) || [];
    },
    queryTableData() {
      this.CetTable_1.queryTrigger_in = Date.now();
    },
    CetButton_add_statusTrigger_out() {
      this.addAndEdit.inputData_in = null;
      this.addAndEdit.openTrigger_in = Date.now();
    },
    editHandle(row) {
      this.addAndEdit.inputData_in = row;
      this.addAndEdit.openTrigger_in = Date.now();
    },
    deleteHandle(row) {
      if (!row.allowDelete) return;
      this.$confirm("确定将该方案吗？", "删除确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(async () => {
          const res = await customApi.deleteCalculateScheme([row.id]);
          if (res.code !== 0) return;
          this.$message({
            message: "删除成功",
            type: "success"
          });
          this.queryTableData();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消"
          });
        });
    },
    associationHandle(row) {
      this.relationNode.inputData_in = row;
      this.relationNode.openTrigger_in = Date.now();
    }
  },
  activated() {
    this.init();
  }
};
</script>

<style lang="scss" scoped>
.common-title-H3 {
  line-height: 32px;
}
</style>
