<template>
  <div class="page">
    <reportPage pageNum="03" :queryTime="queryTime">
      <div
        class="mbJ4"
        v-if="reportData.compressorEnergySavingVOList?.length > 8"
      >
        <stationTable
          :columnList="compressorColumnList"
          :tableData="
            reportData.compressorEnergySavingVOList?.slice(
              8,
              reportData.compressorEnergySavingVOList?.length
            ) || []
          "
        />
      </div>

      <div class="mbJ4">
        <div class="second-title mbJ2">
          {{ $T("2.5 天然气压缩机能效评估") }}
        </div>
        <energyEfficiencyAssessment :reportData="reportData" />
      </div>

      <div class="mbJ4">
        <div class="third-title mbJ2">
          {{ $T("天然气压缩机能效评估表") }}
        </div>
        <stationTable
          :tableData="reportData.stationsCompressorEffAssessmentList || []"
          :columnList="efficiencyAnalysisColumnList"
        />
      </div>

      <div class="mbJ4">
        <div class="third-title mbJ2">
          {{ $T("分设备型号天然气压缩机能效分析") }}
        </div>
        <efficiencyAssessmentTable
          :tableData="
            efficiencyAssessmentTableData.slice(
              0,
              efficiencyAssessmentTableMaxLength
            )
          "
          type="deviceType"
        />
      </div>
    </reportPage>
  </div>
</template>

<script>
import reportPage from "../components/reportPage.vue";
import stationTable from "../components/stationTable.vue";
import energyEfficiencyAssessment from "../components/energyEfficiencyAssessment.vue";
import efficiencyAssessmentTable from "../components/efficiencyAssessmentTable.vue";

import common from "eem-utils/common";
export default {
  name: "thirdPage",
  components: {
    reportPage,
    stationTable, // 天然气压缩机节能分析、天然气压缩机能效评估表
    energyEfficiencyAssessment, // 天然气压缩机能效评估
    efficiencyAssessmentTable // 分设备型号天然气压缩机能效分析
  },
  props: {
    reportData: {
      type: Object,
      default: () => {}
    },
    queryTime: {
      type: Number
    }
  },
  computed: {
    compressorColumnList() {
      const unit = this.reportData.compressorEnergyUnit;
      const list = [
        {
          prop: "order",
          label: $T("排名"),
          align: "left",
          showOverflowTooltip: true,
          width: 50
        },
        {
          prop: "name",
          label: $T("分析对象"),
          align: "left",
          showOverflowTooltip: true
        },
        {
          prop: "gasTransmissionVolume",
          label: $T("月实际输气量(10⁴m³)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 110
        },
        {
          prop: "energy",
          label: $T(`月实际能耗(${unit})`),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 100
        },
        {
          prop: "optimizationEnergy",
          label: $T(`优化后预计能耗(${unit})`),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 100
        },
        {
          prop: "energySaving",
          label: $T(`预计节能量(${unit})`),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 100
        },
        {
          prop: "savingRate",
          label: $T("预计节能率(%)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 80
        }
      ];
      return list;
    },
    efficiencyAssessmentTableData() {
      const list = this.reportData.typeCompressorEffAssessmentList || [];
      let result = [];
      list.forEach(item => {
        item.compressorEffAssessmentList?.forEach(v => {
          result.push({
            ...v,
            typeName: item.typeName
          });
        });
      });
      return result;
    },
    efficiencyAssessmentTableMaxLength() {
      const list = this.reportData.compressorEnergySavingVOList || [];
      const compressorList =
        this.reportData.stationsCompressorEffAssessmentList || [];
      const length = 15; // 节能分析表格和能效评估表表格都为空时，分设备型号天然气压缩机能效分析表格所能展示下的最大行数
      const num = compressorList.length + list.length - 8;
      return num > 0 ? length - num : length;
    }
  },
  data() {
    return {
      efficiencyAnalysisColumnList: [
        {
          prop: "name",
          label: $T("分析对象"),
          align: "left",
          showOverflowTooltip: true
        },
        {
          prop: "total",
          label: $T("总台数"),
          align: "left",
          formatter: common.formatNumberCol(0),
          showOverflowTooltip: true
        },
        {
          prop: "qualified",
          label: $T("合格台数"),
          align: "left",
          formatter: common.formatNumberCol(0),
          showOverflowTooltip: true
        },
        {
          prop: "saving",
          label: $T("节能台数"),
          align: "left",
          formatter: common.formatNumberCol(0),
          showOverflowTooltip: true
        },
        {
          prop: "qualifiedRate",
          label: $T("合格率(%)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true
        },
        {
          prop: "savingRate",
          label: $T("节能率(%)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true
        }
      ]
    };
  },
  watch: {},
  methods: {
    formatNumberWithPrecision(...args) {
      return common.formatNumberWithPrecision(...args);
    }
  }
};
</script>

<style lang="scss" scoped>
@media print {
  @page {
    margin: 0;
  }

  body {
    margin: 1.6cm;
    -webkit-print-color-adjust: exact !important;
    -moz-print-color-adjust: exact !important;
    -ms-print-color-adjust: exact !important;
  }
  button {
    display: none;
  }
}
.page {
  width: 100%;
  height: 100%;
  position: relative;
  background: #ffffff;
}
.title {
  color: #242424;
  font-weight: bold;
  line-height: 20px;
}
.second-title {
  color: #242424;
  line-height: 18px;
}
.third-title {
  color: #242424;
  font-size: 12px;
  line-height: 18px;
}
</style>
