<template>
  <div class="date-range">
    <el-button
      size="small"
      icon="el-icon-arrow-left"
      @click="queryPrv"
      class="mrJ"
    ></el-button>
    <CustomElDatePicker
      ref="datePicker"
      v-model="value"
      class="date-picker"
      :prefix_in="$T('时段')"
      :firstDayOfWeek="1"
      size="small"
      :clearable="false"
      value-format="timestamp"
      type="daterange"
      align="right"
      unlink-panels
      range-separator="-"
      :start-placeholder="$T('开始日期')"
      :end-placeholder="$T('结束日期')"
      :picker-options="pickerOptions"
    ></CustomElDatePicker>
    <el-button
      size="small"
      icon="el-icon-arrow-right"
      @click="queryNext"
      class="mlJ"
    ></el-button>
  </div>
</template>
<script>
import moment from "moment";
const SHORT_CUTS = [
  {
    text: $T("今天"),
    typeID: 1,
    unit: "d",
    number: 1,
    onClick(picker) {
      console.log("picker", picker);

      const end = moment().endOf("d").valueOf() + 1;
      const start = moment().startOf("d").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: $T("昨天"),
    typeID: 2,
    unit: "d",
    number: 1,
    onClick(picker) {
      const end = moment().startOf("d").valueOf();
      const start = moment().startOf("d").add(-1, "d").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: $T("最近3天"),
    typeID: 3,
    unit: "d",
    number: 3,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-2, "d").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: $T("最近一周"),
    typeID: 4,
    unit: "w",
    number: 1,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-6, "d").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: $T("最近一个月"),
    typeID: 5,
    unit: "M",
    number: 1,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-1, "M").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: $T("最近三个月"),
    typeID: 6,
    unit: "M",
    number: 3,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-3, "M").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: $T("最近半年"),
    typeID: 7,
    unit: "M",
    number: 6,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-6, "M").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: $T("最近1年"),
    typeID: 8,
    unit: "y",
    number: 1,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-1, "y").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  }
];
export default {
  name: "TimeRange",
  props: {
    val: Array,
    shortcuts: {
      type: Array,
      default: () => SHORT_CUTS
    },
    typeIds: {
      type: Array,
      default: () => [1, 2, 3, 4, 5, 6, 7, 8]
    },
    disabledDate: Function,
    onPick: Function
  },
  watch: {
    val: {
      deep: true,
      handler: function (val, oldVal) {
        this.value = val;
      }
    },
    value: {
      deep: true,
      handler: function (val, oldVal) {
        this.changDate(val);
      }
    }
  },
  computed: {},
  data() {
    const shortcuts = this.shortcuts.filter(item =>
      this.typeIds.includes(item.typeID)
    );
    return {
      pickerOptions: {
        firstDayOfWeek: 1,
        shortcuts: shortcuts,
        disabledDate: this.disabledDate,
        onPick: this.onPick
      },
      timeOptions: [
        {
          type: "today",
          text: $T("今天"),
          typeID: 1,
          number: 1,
          unit: "d"
        },
        {
          type: "yestoday",
          text: $T("昨天"),
          typeID: 2,
          number: -1,
          unit: "d"
        },
        {
          type: "lastThreeDay",
          text: $T("近3天"),
          typeID: 3,
          number: -3,
          unit: "d"
        },
        {
          type: "lastSevenDay",
          text: $T("近7天"),
          number: -7,
          typeID: 5,
          unit: "d"
        },
        {
          type: "theMonth",
          text: $T("当月"),
          number: 0,
          typeID: 5,
          unit: "M"
        }
      ],
      currentTimeOption: {
        type: "today",
        text: $T("今天"),
        typeID: 1,
        number: 0,
        unit: "d"
      },
      value: []
    };
  },
  methods: {
    queryPrv() {
      const date = moment(this.value[0]);
      let typeID;
      const timeDiff = moment(this.value[1]).diff(moment(this.value[0]), "d");
      try {
        typeID = this.$refs.datePicker.picker.typeID || 1;
      } catch (error) {
        typeID = 1;
      }
      const currentDate = [];
      const shortcuts = this._.find(SHORT_CUTS, ["typeID", typeID]);
      currentDate[0] = date.subtract(timeDiff, "d").valueOf();
      currentDate[1] = this.value[0];
      // this.changDate(currentDate);
      this.value = currentDate;
    },
    queryNext() {
      const date = moment(this.value[1]);
      let typeID;
      const timeDiff = moment(this.value[1]).diff(moment(this.value[0]), "d");
      try {
        typeID = this.$refs.datePicker.picker.typeID || 1;
      } catch (error) {
        typeID = 1;
      }
      const currentDate = [];
      const shortcuts = this._.find(SHORT_CUTS, ["typeID", typeID]);
      currentDate[0] = this.value[1];
      currentDate[1] = date.add(timeDiff, "d").valueOf();
      // this.changDate(currentDate);
      this.value = currentDate;
    },
    changDate(val) {
      this.value = val;
      this.$emit("update:val", val);
      this.$emit("change", val);
    }
  },
  created: function () {},
  mounted() {
    this.value = this.val;
  }
};
</script>
<style lang="scss" scoped>
.date-range {
  display: flex;
  align-items: center;
  .date-picker {
    flex: 1;
    width: auto;
  }
  .el-button {
    padding: 9px;
  }
}
</style>
