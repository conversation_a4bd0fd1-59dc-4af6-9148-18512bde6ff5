<template>
  <div class="page eem-common">
    <el-container class="fullheight flex-column">
      <div class="eem-container mbJ3">
        <div class="fr" style="display: flex; align-items: center">
          <customElSelect
            v-model="ElSelect_team.value"
            v-bind="ElSelect_team"
            v-on="ElSelect_team.event"
            class="mrJ1"
            :prefix_in="$T('责任班组')"
          >
            <ElOption
              v-for="item in ElOption_team.options_in"
              :key="item[ElOption_team.key]"
              :label="item[ElOption_team.label]"
              :value="item[ElOption_team.value]"
              :disabled="item[ElOption_team.disabled]"
            ></ElOption>
          </customElSelect>
          <time-tool
            :val.sync="startTime"
            @change="changeQueryTime"
          ></time-tool>
          <!-- 交班按钮组件 -->
          <CetButton
            class="mlJ1 mrJ1"
            v-bind="CetButton_exchange"
            v-on="CetButton_exchange.event"
          ></CetButton>
          <!-- 接班按钮组件 -->
          <CetButton
            v-bind="CetButton_takeover"
            v-on="CetButton_takeover.event"
          ></CetButton>
        </div>
      </div>
      <div class="flex-auto eem-container">
        <CetTable
          :data.sync="CetTable_list.data"
          :dynamicInput.sync="CetTable_list.dynamicInput"
          v-bind="CetTable_list"
          v-on="CetTable_list.event"
          class="eem-table-custom"
        >
          <template v-for="item in Columns_handover">
            <ElTableColumn
              :key="item.label"
              v-bind="item"
              headerAlign="left"
              align="left"
            ></ElTableColumn>
          </template>
          <ElTableColumn v-bind="ElTableColumn_operate">
            <template slot-scope="{ row }">
              <span @click="handleDetail(row)" class="eem-row-handle">
                {{ $T("详情") }}
              </span>
            </template>
          </ElTableColumn>
        </CetTable>
      </div>
    </el-container>

    <!-- 交班 -->
    <exchangeDialog
      v-bind="exchangeDialog"
      v-on="exchangeDialog.event"
    ></exchangeDialog>
    <!-- 接班 -->
    <takeoverDialog
      v-bind="takeoverDialog"
      v-on="takeoverDialog.event"
    ></takeoverDialog>
    <detailDialog v-bind="detailDialog"></detailDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import common from "eem-utils/common";
import TimeTool from "eem-components/TimeTool.vue";
import exchangeDialog from "./dialogs/exchangeDialog";
import takeoverDialog from "./dialogs/takeoverDialog";
import detailDialog from "./dialogs/detailDialog";

export default {
  name: "shifthandover",
  components: {
    TimeTool,
    exchangeDialog,
    takeoverDialog,
    detailDialog
  },
  computed: {
    projectTenantId() {
      return this.$store.state.projectTenantId;
    },
    userInfo() {
      return this.$store.state.userInfo;
    }
  },
  data() {
    return {
      // 责任班组组件
      ElSelect_team: {
        value: "",
        style: {
          width: "200px"
        },
        size: "small",
        event: {
          change: this.ElSelect_team_change_out
        }
      },
      ElOption_team: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      startTime: new Date().getTime(),
      // exchange组件
      CetButton_exchange: {
        visible_in: true,
        disable_in: false,
        title: $T("交班"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_exchange_statusTrigger_out
        }
      },
      // takeover组件
      CetButton_takeover: {
        visible_in: true,
        disable_in: false,
        title: $T("接班"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_takeover_statusTrigger_out
        }
      },
      // list表格组件
      CetTable_list: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "getHandoverList",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [
            { name: "startTime_in", operator: "GT", prop: "startTime" },
            { name: "endTime_in", operator: "LT", prop: "endTime" },
            { name: "teamId_in", operator: "EQ", prop: "teamId" },
            { name: "tenantId_in", operator: "EQ", prop: "tenantId" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {},
        exportFileName: "",
        event: {
          record_out: this.CetTable_list_record_out,
          outputData_out: this.CetTable_list_outputData_out
        }
      },
      Columns_handover: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          showOverflowTooltip: true,
          //minWidth: "200",  //该宽度会自适应
          width: "60" //绝对宽度
        },
        {
          prop: "starttime", // 支持path a[0].b
          label: $T("接班时间"), //列名
          showOverflowTooltip: true,
          formatter: (row, column, cellValue) => common.formatDate(cellValue)
          // width: "160" //绝对宽度
        },
        {
          prop: "endtime", // 支持path a[0].b
          label: $T("交班时间"), //列名
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: (row, column, cellValue) => common.formatDate(cellValue)
        },
        {
          prop: "teamName", // 支持path a[0].b
          label: $T("责任班组"), //列名
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        },
        {
          prop: "dutytime", // 支持path a[0].b
          label: $T("值班时长"), //列名
          showOverflowTooltip: true,
          formatter: (row, column, cellValue) =>
            this.secondsFormat(cellValue, "hh h mm min")
        },
        {
          prop: "dutyOfficerName", // 支持path a[0].b
          label: $T("值班长"), //列名
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        }
      ],
      ElTableColumn_operate: {
        prop: "", // 支持path a[0].b
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      // 交班弹窗页面组件
      exchangeDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        teamName: "", // 当前用户所属班组名称
        event: {
          saveData_out: this.handleExchange,
          finishTrigger_out: this.getHandoverList
        }
      },
      // 接班弹窗页面组件
      takeoverDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        event: {
          finishTrigger_out: this.getHandoverList,
          saveData_out: this.handleTakeover
        }
      },
      // 查看弹窗页面组件
      detailDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      }
    };
  },
  watch: {},
  methods: {
    // team输出,方法名要带_out后缀
    ElSelect_team_change_out() {
      this.getHandoverList();
    },
    changeQueryTime({ val, timeOption }) {
      let date = this.$moment(val);
      let startTime = date.startOf(timeOption.unit).valueOf();
      let endTime = date.endOf(timeOption.unit).valueOf() + 1;
      this.CetTable_list.dynamicInput.startTime_in = startTime;
      this.CetTable_list.dynamicInput.endTime_in = endTime;
      this.getHandoverList();
    },
    // 交班
    handleExchange(val) {
      customApi.handOverToNext(val).then(res => {
        if (res.code === 0) {
          this.$message.success($T("交班成功!"));
          this.exchangeDialog.closeTrigger_in = new Date().getTime();
          this.getHandoverList();
        }
      });
    },
    handleTakeover(val) {
      let param = {
        dutyStaffList: val.dutystaff
      };
      customApi.handoverSuccession(param).then(res => {
        if (res.code === 0) {
          this.$message.success($T("接班成功!"));
          this.takeoverDialog.closeTrigger_in = new Date().getTime();
          this.getHandoverList();
        }
      });
    },
    // exchange输出
    CetButton_exchange_statusTrigger_out() {
      this.exchangeDialog.openTrigger_in = new Date().getTime();
    },
    // takeover输出
    CetButton_takeover_statusTrigger_out() {
      this.getHandoverMatter();
      this.takeoverDialog.openTrigger_in = new Date().getTime();
    },
    // list表格输出
    CetTable_list_record_out() {},
    CetTable_list_outputData_out() {},
    handleDetail(row) {
      this.detailDialog.inputData_in = row;
      this.detailDialog.openTrigger_in = new Date().getTime();
    },
    formatter(row, column, cellValue) {
      return cellValue || "--";
    },
    // 获取班组列表
    getGroupList() {
      customApi.queryInspectorTeam({}).then(res => {
        if (res.code === 0) {
          this.ElOption_team.options_in = res.data;
          this.ElSelect_team.value = res.data[0].id;
          this.ElSelect_team_change_out();
          // 获取当前用户的班组信息
          let teamId = this.userInfo.relativeUserGroup[0];
          if (teamId) {
            let target = res.data.find(item => item.id === teamId);
            this.exchangeDialog.teamName = target?.name;
          }
        }
      });
    },
    // 获取交接班列表
    getHandoverList() {
      if (!this.ElSelect_team.value) return;
      this.CetTable_list.dynamicInput.tenantId_in = this.projectTenantId;
      this.CetTable_list.dynamicInput.teamId_in = this.ElSelect_team.value;
      this.CetTable_list.queryTrigger_in = new Date().getTime();
    },
    // 获取接班交接事项
    getHandoverMatter() {
      customApi.getHandoverMatter().then(res => {
        if (res.code === 0) {
          this.takeoverDialog.inputData_in = res.data;
        }
      });
    },
    // 毫秒数转化
    secondsFormat(sec, format) {
      let str = "--";
      if (sec || sec === 0) {
        let hour = Math.floor(sec / 3600000);
        let minute = Math.floor((sec - hour * 3600000) / 60000);
        if (
          format.indexOf("hh") != -1 &&
          format.indexOf("mm") != -1 &&
          format.indexOf("ss") == -1
        ) {
          str = format.replace(
            /(.*)hh(.*)mm(.*)/,
            "$1" + hour + "$2" + minute + "$3"
          );
        }
      }
      return str;
    }
  },
  activated() {
    this.getGroupList();
    // this.getHandoverList();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .header {
    line-height: 50px;
  }
}
</style>
