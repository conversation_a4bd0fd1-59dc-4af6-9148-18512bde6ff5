<template>
  <div class="w-full h-full">
    <div class="h-[32px] w-full">
      <div class="flex items-center">
        <div
          v-if="schemeName"
          class="text-T2 font-medium text-[16px] overflow-hidden text-ellipsis whitespace-nowrap"
          :title="schemeName"
          :class="[schemeName ? 'mr-[16px]' : '']"
        >
          {{ schemeName }}
        </div>
        <customElInput
          class="!w-[217px]"
          v-model.trim="sampleName"
          placeholder="请输入"
          @change="onSampleName"
          prefix_in="样本数据时段(日)"
        ></customElInput>
      </div>
    </div>
    <div class="w-full h-[calc(100%-32px-16px)] mt-[16px]">
      <CetChart v-bind="CetChart_line"></CetChart>
    </div>
  </div>
</template>

<script>
import customElInput from "@/components/customElInput";
import customApi from "@/api/custom.js";
import { onInputNumber } from ".././component/tableCol.jsx";
import common from "eem-utils/common.js";
export default {
  name: "lineChart",
  props: { selectNode: Object },
  components: { customElInput },
  data() {
    return {
      sampleName: 10,
      schemeName: null,
      CetChart_line: {
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "axis"
          },
          encode: {
            x: 0,
            y: 1
          },
          legend: { data: [$T("实际值"), $T("预测值")] },
          grid: {
            top: "16%",
            left: "2%",
            right: "2%",
            bottom: "12%",
            containLabel: true
          },
          xAxis: {
            type: "time",
            axisTick: { show: false },
            data: []
          },
          dataZoom: [
            {
              type: "inside",
              yAxisIndex: 0
            },
            {
              type: "slider",
              minValueSpan: 3600 * 1000 * 8,
              handleSize: 15,
              height: 15,
              bottom: 10
            }
          ],
          yAxis: {
            type: "value",
            name: "",
            nameTextStyle: {
              padding: [0, 0, 0, 60]
            },
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: {
              lineStyle: {
                type: "dashed"
              }
            }
          },
          series: [
            {
              name: $T("实际值"),
              type: "line",
              smooth: true,
              showSymbol: false,
              data: []
            },
            {
              name: $T("预测值"),
              type: "line",
              smooth: true,
              showSymbol: false,
              data: []
            }
          ]
        }
      }
    };
  },
  watch: {
    selectNode: {
      deep: true,
      handler(val) {
        if (!_.isObject(val)) return;
        this.getChartData();
      }
    }
  },
  methods: {
    onSampleName(val) {
      this.sampleName = onInputNumber(val);
      if (val > 30) {
        this.$message.warning("最大支持30天查询！");
      }
      this.getChartData();
    },
    async getChartData() {
      const params = {
        endTime: this.$moment().endOf("d").valueOf() + 1,
        objectId: this.selectNode?.id,
        objectLabel: this.selectNode?.modelLabel,
        startTime: this.$moment()
          .subtract(this.sampleName, "d")
          .startOf("d")
          .valueOf()
      };

      this.CetChart_line.options.series[0].data = [];
      this.CetChart_line.options.series[1].data = [];
      this.schemeName = null;
      if (params.objectLabel !== "waterinjectionstation") return;
      const res = await customApi.onlineFittingcurve(params);
      this.schemeName = res?.data?.schemeName;
      this.CetChart_line.options.series[0].data =
        res?.data?.dataVOList?.map(i => [
          i.logTime,
          common.formatNumberWithPrecision(i.productValue, 2)
        ]) || [];
      this.CetChart_line.options.series[1].data =
        res?.data?.dataVOList?.map(i => [
          i.logTime,
          common.formatNumberWithPrecision(i.forecastProductValue, 2)
        ]) || [];
      this.CetChart_line.options.yAxis.name = res?.data?.unit
        ? `单位(${res?.data?.unit})`
        : "";
    }
  },
  created() {},
  mounted() {
    this.getChartData();
  }
};
</script>

<style lang="scss" scoped></style>
