const routerNameArr = [
  "energyFlowAnalyse",
  "generatrixMonitor",
  "deviceMonitor",
  "videoMonitor",
  "energyConsumption",
  "balanceConsumption",
  "plantEnvironment",
  "energyPredictionAndAnalysis",
  "energyCostAnalysis",
  "energySavingPotentialMining",
  "energySavingBenefitEvaluation",
  "transformerEnergyEfficiency",
  "refrigerationEfficiency",
  "airCompressionEnergyEfficiency",
  "boilerEnergyEfficiency"
];
const graphRouter = routerNameArr.map(item => {
  return {
    path: `/${item}`,
    name: item,
    component: () => import("eem-pages/pages/project/deviceMonitor/index.vue"),
    meta: { keepAlive: true }
  };
});
const commonRouterConfig = [
    //平台层级--start
    {
      path: "/home",
      name: "home",
      component: () => import("eem-pages/pages/platform/home/<USER>"),
      meta: { keepAlive: true }
    },
    {
      path: "/projectnavigation",
      name: "projectnavigation",
      component: () =>
        import(
          "eem-pages/pages/platform/projectnavigation/ProjectNavigation.vue"
        ),
      meta: { keepAlive: true }
    },
    {
      path: "/platformusermanage",
      name: "platformusermanage",
      component: () =>
        import("eem-pages/pages/platform/platformusermanage/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/platformurolemanage",
      name: "platformurolemanage",
      component: () => import("eem-pages/pages/project/roleManage"),
      meta: { keepAlive: true }
    },
    //平台层级-end
    //能源管理--start
    ...graphRouter,
    {
      path: "/energydata",
      name: "energydata",
      component: () =>
        import("eem-pages/pages/project/energydata/EnergyData.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/dashboard",
      name: "dashboard",
      component: () =>
        import("eem-pages/pages/project/dashboard/Dashboard.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/realtimeMonitor",
      name: "realtimeMonitor",
      component: () => import("eem-pages/pages/project/realtimeMonitor"),
      meta: { keepAlive: true }
    },
    {
      path: "/allQueryReport",
      name: "allQueryReport",
      component: () => import("eem-pages/pages/project/allQueryReport"),
      meta: { keepAlive: true }
    },
    {
      path: "/energyreport",
      name: "energyreport",
      component: () => import("eem-pages/pages/project/energyreport"),
      meta: { keepAlive: true }
    },
    {
      path: "/fineReport",
      name: "fineReport",
      component: () => import("eem-pages/pages/project/fineReport"),
      meta: { keepAlive: true }
    },
    {
      path: "/operationreport",
      name: "operationreport",
      component: () => import("eem-pages/pages/project/operationreport"),
      meta: { keepAlive: true }
    },
    {
      path: "/reportDesignTool",
      name: "reportDesignTool",
      component: () => import("eem-pages/pages/project/reportDesignTool"),
      meta: { keepAlive: true }
    },
    {
      path: "/onlyReport",
      name: "onlyReport",
      component: () => import("eem-pages/pages/project/onlyReport"),
      meta: { keepAlive: true }
    },
    {
      path: "/knowledge",
      name: "knowledge",
      component: () => import("eem-pages/pages/project/knowledge"),
      meta: { keepAlive: true }
    },
    {
      path: "/energyQueryAndAnalysis",
      name: "energyQueryAndAnalysis",
      component: () =>
        import("eem-pages/pages/project/energyQueryAndAnalysis/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/energyFlowAnalysis",
      name: "energyFlowAnalysis",
      component: () =>
        import("eem-pages/pages/project/energyFlowAnalysis/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/energyEfficiencyIndexes",
      name: "energyEfficiencyIndexes",
      component: () =>
        import("eem-pages/pages/project/energyEfficiencyIndexes/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/trendcurve",
      name: "trendcurve",
      component: () => import("eem-pages/pages/project/trendcurve/TrendCurve"),
      meta: { keepAlive: true }
    },
    {
      path: "/networkTrendcurve",
      name: "networkTrendcurve",
      component: () =>
        import("eem-pages/pages/project/trendcurve/networkTrendcurve.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/networkEnergyAndTrendcurve",
      name: "networkEnergyAndTrendcurve",
      component: () =>
        import("eem-pages/pages/project/networkEnergyAndTrendcurve"),
      meta: { keepAlive: true }
    },
    {
      path: "/energyattributionanalysis",
      name: "energyattributionanalysis",
      component: () =>
        import(
          "eem-pages/pages/project/energyattributionanalysis/EnergyAttributionAnalysis.vue"
        ),
      meta: { keepAlive: true }
    },
    {
      path: "/energyConsumptionEvent",
      name: "energyConsumptionEvent",
      component: () => import("eem-pages/pages/project/eventCenter/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/energyEfficiencyEvent",
      name: "energyEfficiencyEvent",
      component: () =>
        import("eem-pages/pages/project/eventCenter/indexEfficiency.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/metrics",
      name: "metrics",
      component: () => import("eem-pages/pages/project/metrics/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/sandingBok",
      name: "sandingBok",
      component: () => import("eem-pages/pages/project/sandingBok/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/loadForecasting",
      name: "loadForecasting",
      component: () =>
        import("eem-pages/pages/project/loadForecasting/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/energyEntry",
      name: "energyEntry",
      component: () =>
        import("eem-pages/pages/project/cloudDataEntry/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/meterEntry",
      name: "meterEntry",
      component: () =>
        import("eem-pages/pages/project/meterDataEntry/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/productionPlan",
      name: "productionPlan",
      component: () =>
        import("eem-pages/pages/project/productionPlan/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/energyquery",
      name: "energyquery",
      component: () =>
        import("eem-pages/pages/project/energyquery/EnergyQuery.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/energyLossAnalysis",
      name: "energyLossAnalysis",
      component: () =>
        import("eem-pages/pages/project/energyLossAnalysis/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/linelossanalytical",
      name: "linelossanalytical",
      component: () =>
        import("eem-pages/pages/project/linelossanalytical/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/energyLossOverview",
      name: "energyLossOverview",
      component: () =>
        import(
          "eem-pages/pages/project/energyLossManagement/energyLossOverview/index.vue"
        ),
      meta: { keepAlive: true }
    },
    {
      path: "/energyLossWarning",
      name: "energyLossWarning",
      component: () =>
        import(
          "eem-pages/pages/project/energyLossManagement/energyLossWarning/index.vue"
        ),
      meta: { keepAlive: true }
    },
    {
      path: "/energyLossConfig",
      name: "energyLossConfig",
      component: () =>
        import(
          "eem-pages/pages/project/energyLossManagement/energyLossConfig/index.vue"
        ),
      meta: { keepAlive: true }
    },
    {
      path: "/dudianEnergyFee",
      name: "dudianEnergyFee",
      component: () =>
        import(
          "eem-pages/pages/project/energySavingImprove/dudianFeeIndex.vue"
        ),
      meta: { keepAlive: true }
    },
    {
      path: "/baseEnergyFee",
      name: "baseEnergyFee",
      component: () =>
        import("eem-pages/pages/project/energySavingImprove/baseFeeIndex.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/litiaoEnergyFee",
      name: "litiaoEnergyFee",
      component: () =>
        import(
          "eem-pages/pages/project/energySavingImprove/litiaoFeeIndex.vue"
        ),
      meta: { keepAlive: true }
    },
    {
      path: "/emmEnergyComparison",
      name: "emmEnergyComparison",
      component: () =>
        import("eem-pages/pages/project/eemEnergyComparison/index.vue"),
      meta: { keepAlive: true }
    },
    //能源管理--end
    //需量管理 --start
    {
      path: "/demandmonitor",
      name: "demandmonitor",
      component: () =>
        import("eem-pages/pages/project/demandmonitor/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/demandevent",
      name: "demandevent",
      component: () =>
        import("eem-pages/pages/project/demandevent/DemandEvent"),
      meta: { keepAlive: true }
    },
    {
      path: "/declareproposal",
      name: "declareproposal",
      component: () => import("eem-pages/pages/project/declareproposal/index"),
      meta: { keepAlive: true }
    },
    {
      path: "/demandanalysis",
      name: "demandanalysis",
      component: () =>
        import("eem-pages/pages/project/demandanalysis/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/inletwiremanage",
      name: "inletwiremanage",
      component: () => import("eem-pages/pages/project/inletwireManage"),
      meta: { keepAlive: true }
    },
    {
      path: "/demandearlywarning",
      name: "demandearlywarning",
      component: () =>
        import(
          "eem-pages/pages/project/eventCallPoliceConfigAuto/DemandEarlyWarningConfig"
        ),
      meta: { keepAlive: true }
    },
    {
      path: "/billingscheme",
      name: "billingscheme",
      component: () => import("eem-pages/pages/project/billingScheme"),
      meta: { keepAlive: true }
    },
    //需量管理--end
    //专家库--start
    {
      path: "/eventDiagnosis",
      name: "eventDiagnosis",
      component: () =>
        import("eem-pages/pages/project/eventDiagnosis/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/eventStatistics",
      name: "eventStatistics",
      component: () =>
        import("eem-pages/pages/project/eventStatistics/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/eventDiagByConvergence",
      name: "eventDiagByConvergence",
      component: () =>
        import("eem-pages/pages/project/eventDiagByConvergence/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/eventStatisConvergence",
      name: "eventStatisConvergence",
      component: () =>
        import(
          "eem-pages/pages/project/eventStatistics/eventStatisConvergence.vue"
        ),
      meta: { keepAlive: true }
    },
    {
      path: "/historyEventQuery",
      name: "historyEventQuery",
      component: () =>
        import(
          "eem-pages/pages/project/historyeventquery/historyEventQuery.vue"
        ),
      meta: { keepAlive: true }
    },
    {
      path: "/accidentHandleAdministration",
      name: "accidentHandleAdministration",
      component: () =>
        import(
          "eem-pages/pages/project/accidenthandleadministration/index.vue"
        ),
      meta: { keepAlive: true }
    },
    {
      path: "/eventattradministration",
      name: "eventattradministration",
      component: () =>
        import("eem-pages/pages/project/eventattradministration/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/transienteventanalysis",
      name: "transienteventanalysis",
      component: () =>
        import("eem-pages/pages/project/transienteventanalysis/index.vue"),
      meta: { keepAlive: true }
    },
    //专家库--end
    //成本分析--start
    {
      path: "/costanalysis",
      name: "costanalysis",
      component: () =>
        import("eem-pages/pages/project/costanalysis/Costanalysis.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/costaccounting",
      name: "costaccounting",
      component: () =>
        import("eem-pages/pages/project/costaccounting/Costaccounting.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/costbudget",
      name: "costbudget",
      component: () =>
        import("eem-pages/pages/project/costbudget/Costbudget.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/rateadministration",
      name: "rateadministration",
      component: () =>
        import("eem-pages/pages/project/rateadministration/RateManage.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/accountingscheme",
      name: "accountingscheme",
      component: () =>
        import("eem-pages/pages/project/accountingscheme/Accountingscheme.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/electricityCostAnalysis",
      name: "electricityCostAnalysis",
      component: () =>
        import("eem-pages/pages/project/electricityCostAnalysis/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/comprehensiveCostAnalysis",
      name: "comprehensiveCostAnalysis",
      component: () =>
        import("eem-pages/pages/project/comprehensiveCostAnalysis/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/unitCostAnalysis",
      name: "unitCostAnalysis",
      component: () =>
        import("eem-pages/pages/project/unitCostAnalysis/index.vue"),
      meta: { keepAlive: true }
    },
    //成本分析--end

    //电能质量部分--start
    {
      path: "/indicatorOverview",
      name: "indicatorOverview",
      component: () =>
        import(
          "eem-pages/pages/project/powerqualityanalyzer/IndicatorOverview.vue"
        ),
      meta: { keepAlive: true }
    },
    {
      path: "/singlePointAnalysis",
      name: "singlePointAnalysis",
      component: () =>
        import(
          "eem-pages/pages/project/powerqualityanalyzer/SinglePointAnalysis.vue"
        ),
      meta: { keepAlive: true }
    },
    {
      path: "/voltageVariation",
      name: "voltageVariation",
      component: () =>
        import(
          "eem-pages/pages/project/powerqualityanalyzer/VoltageVariation.vue"
        ),
      meta: { keepAlive: true }
    },
    {
      path: "/tolerance",
      name: "tolerance",
      component: () =>
        import("eem-pages/pages/project/powerqualityanalyzer/Tolerance.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/harmonicAnalysis",
      name: "harmonicAnalysis",
      component: () =>
        import(
          "eem-pages/pages/project/powerqualityanalyzer/HarmonicAnalysis.vue"
        ),
      meta: { keepAlive: true }
    },
    {
      path: "/pqReport",
      name: "pqReport",
      component: () =>
        import("eem-pages/pages/project/powerqualityanalyzer/PQReport.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/electricEquipment",
      name: "electricEquipment",
      component: () =>
        import("eem-pages/pages/project/electricEquipment/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/indicatorManage",
      name: "indicatorManage",
      component: () => import("eem-pages/pages/project/indicatorManage"),
      meta: { keepAlive: true }
    },
    //电能质量--end
    //运维管理--start
    {
      path: "/shifthandover",
      name: "shifthandover",
      component: () =>
        import("eem-pages/pages/project/shifthandover/index.vue"),
      meta: { keepAlive: true },
      props: true
    },
    {
      path: "/signinmanage",
      name: "signinmanage",
      component: () => import("eem-pages/pages/project/signinmanage/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/inspectplanmanage",
      name: "inspectplanmanage",
      component: () =>
        import("eem-pages/pages/project/inspectplanmanage/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/inspectplan",
      name: "inspectplan",
      component: () => import("eem-pages/pages/project/xjPlan/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/inspectorder",
      name: "inspectorder",
      component: () => import("eem-pages/pages/project/xjOrder/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/inspectperson",
      name: "inspectperson",
      component: () => import("eem-pages/pages/project/xjPerson/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/signinMonitor",
      name: "signinMonitor",
      component: () =>
        import("eem-pages/pages/project/signinMonitor/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/serviceOrder",
      name: "serviceOrder",
      component: () => import("eem-pages/pages/project/serviceOrder/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/servicePlan",
      name: "servicePlan",
      component: () => import("eem-pages/pages/project/servicePlan/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/serviceProject",
      name: "serviceProject",
      component: () =>
        import("eem-pages/pages/project/serviceProject/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/repairorder",
      name: "repairorder",
      component: () => import("eem-pages/pages/project/wxOrder/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/equipmanage",
      name: "equipmanage",
      component: () => import("eem-pages/pages/project/equipmanage/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/equiptemplate",
      name: "equiptemplate",
      component: () =>
        import("eem-pages/pages/project/equiptemplate/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/sparepartManage",
      name: "sparepartManage",
      component: () =>
        import("eem-pages/pages/project/sparepartManage/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/sparepartStatistic",
      name: "sparepartStatistic",
      component: () =>
        import("eem-pages/pages/project/sparepartStatistic/index.vue"),
      meta: { keepAlive: true }
    },
    //运维管理--end
    //系统配置管理--start
    {
      path: "/cloudProjectConfig",
      name: "cloudProjectConfig",
      component: () =>
        import("eem-pages/pages/project/cloudProjectConfig/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/networkCloudProjectConfig",
      name: "networkCloudProjectConfig",
      component: () =>
        import("eem-pages/pages/project/cloudProjectConfig/indexNetwork.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/customProjectConfig",
      name: "customProjectConfig",
      component: () =>
        import("eem-pages/pages/project/customProjectConfig/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/topologyConfig",
      name: "topologyConfig",
      component: () =>
        import("eem-pages/pages/project/topologyConfig/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/associatRelation",
      name: "associatRelation",
      component: () =>
        import("eem-pages/pages/project/associatRelation/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/connectedRelation",
      name: "connectedRelation",
      component: () =>
        import("eem-pages/pages/project/connectedRelation/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/unitTransition",
      name: "unitTransition",
      component: () =>
        import("eem-pages/pages/project/unitTransition/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/videoConfig",
      name: "videoConfig",
      component: () => import("eem-pages/pages/project/videoConfig/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/videoSurveillance",
      name: "videoSurveillance",
      component: () => import("eem-pages/pages/project/videoMonitor/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/virtualMeter",
      name: "virtualMeter",
      component: () => import("eem-pages/pages/project/virtualMeter/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/usermanage",
      name: "usermanage",
      component: () => import("eem-pages/pages/project/userManage"),
      meta: { keepAlive: true }
    },
    {
      path: "/rolemanage",
      name: "rolemanage",
      component: () => import("eem-pages/pages/project/roleManage"),
      meta: { keepAlive: true }
    },
    {
      path: "/usergroupmanage",
      name: "usergroupmanage",
      component: () => import("eem-pages/pages/project/usergroupManage"),
      meta: { keepAlive: true }
    },
    {
      path: "/videoAndDocPermission",
      name: "videoAndDocPermission",
      component: () => import("eem-pages/pages/project/videoAndDocPermission"),
      meta: { keepAlive: true }
    },
    {
      path: "/cycleconfig",
      name: "cycleconfig",
      component: () => import("eem-pages/pages/project/cycleConfig"),
      meta: { keepAlive: true }
    },
    {
      path: "/timesharingconfig",
      name: "timesharingconfig",
      component: () => import("eem-pages/pages/project/timesharingConfig"),
      meta: { keepAlive: true }
    },
    {
      path: "/shareschemeconfig",
      name: "shareschemeconfig",
      component: () => import("eem-pages/pages/project/shareschemeConfig"),
      meta: { keepAlive: true }
    },
    {
      path: "/enDimensional",
      name: "enDimensional",
      component: () => import("eem-pages/pages/project/enDimensional"),
      meta: { keepAlive: true }
    },
    {
      path: "/eemMultiDimensional",
      name: "eemMultiDimensional",
      component: () => import("eem-pages/pages/project/multiDimensional"),
      meta: { keepAlive: true }
    },
    {
      path: "/metricaldimension",
      name: "metricaldimension",
      component: () =>
        import("eem-pages/pages/project/metricaldimension/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/metricalnode",
      name: "metricalnode",
      component: () => import("eem-pages/pages/project/metricalnode/index.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/efficiencyBenchmarkingManage",
      name: "efficiencyBenchmarkingManage",
      component: () =>
        import("eem-pages/pages/project/efficiencyBenchmarkingManage"),
      meta: { keepAlive: true }
    },
    {
      path: "/eventCallPoliceConfig",
      name: "eventCallPoliceConfig",
      component: () =>
        import("eem-pages/pages/project/eventCallPoliceConfigAuto")
    },
    {
      path: "/energyBalance",
      name: "energyBalance",
      component: () => import("eem-pages/pages/project/energyBalance"),
      meta: { keepAlive: true }
    },
    {
      path: "/dataRecalculation",
      name: "dataRecalculation",
      component: () => import("eem-pages/pages/project/dataRecalculation")
    },
    {
      path: "/log",
      name: "log",
      component: () => import("eem-pages/pages/project/log/Log.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/operationlog",
      name: "operationlog",
      component: () => import("eem-pages/pages/project/log/operationlog.vue"),
      meta: { keepAlive: true }
    },
    {
      path: "/physicalQuantityConfig",
      name: "physicalQuantityConfig",
      component: () =>
        import("eem-pages/pages/project/physicalQuantityConfig/index.vue"),
      meta: { keepAlive: true }
    }
    //系统配置管理--end
  ],
  loginRouter = {
    path: "/login",
    component: () => import("eem-pages/pages/login/index.vue")
  },
  noPermissionRouter = {
    path: "/noPermission",
    component: () => import("eem-pages/pages/noPermission/NoPermission.vue")
  };

export { commonRouterConfig, loginRouter, noPermissionRouter };
