<template>
  <div class="energySavingTrend">
    <CetChart v-bind="CetChart_trend" :initOptions="rendererType"></CetChart>
  </div>
</template>

<script>
import common from "eem-utils/common";
const colorList = [
  "#29b061",
  "#00a2ff",
  "#ffc24c",
  "#e77fbc",
  "#00c1b2",
  "#8e43e7",
  "#ff7f43",
  "#0056ff",
  "#eb2f98",
  "#76daff",
  "#7fd0a0",
  "#1a914a",
  "#b38eca",
  "#585cbf",
  "#6bc1c3",
  "#12878d",
  "#579aff",
  "#2e63b1",
  "#ffc879",
  "#e68f11"
];
export default {
  name: "energySavingTrend",
  components: {},
  props: {
    chartData: {
      type: Array
    },
    type: {
      type: String,
      default: "energy"
    },
    reportData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      rendererType: { renderer: "svg" },
      // trend组件
      CetChart_trend: {
        //组件输入项
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "axis",
            confine: true
          },
          grid: {
            top: 40,
            bottom: 10,
            left: 20,
            right: 0,
            containLabel: true
          },
          legend: {
            show: true,
            textStyle: {
              color: "#798492"
            }
          },
          xAxis: {
            type: "category",
            axisTick: { show: false },
            axisLabel: {
              color: "#798492"
            }
          },
          yAxis: {
            name: $T("单位(kWh)"),
            type: "value",
            axisLabel: {
              color: "#798492"
            },
            axisLine: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: "#E0E4E8",
                type: "dashed"
              }
            },
            nameTextStyle: {
              color: "#798492",
              padding: [0, 0, 10, 0]
            }
          },
          color: colorList,
          series: []
        }
      }
    };
  },
  watch: {
    chartData(val) {
      const key =
        this.type === "cost" ? "dailySavingCostList" : "dailySavingEnergyList";
      this.CetChart_trend.options.series = val.map(item => {
        return {
          name: item.name,
          type: "line",
          stack: "Total",
          areaStyle: {},
          smooth: true,
          showSymbol: false,
          data: item[key]?.map(v => [
            this.$moment(v.time).format("DD"),
            common.formatNumberWithPrecision(v.value, 2)
          ])
        };
      });
      this.CetChart_trend.options.yAxis.name =
        this.type === "cost"
          ? "单位(万元)"
          : `单位(${this.reportData.stationTrendEnergyUnit})`;
    }
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
.energySavingTrend {
  width: 100%;
  height: 230px;
}
</style>
