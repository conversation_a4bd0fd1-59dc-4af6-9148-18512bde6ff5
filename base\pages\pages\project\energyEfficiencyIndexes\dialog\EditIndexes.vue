<template>
  <!-- 1弹窗组件 -->
  <div>
    <CetDialog
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
      :title="
        activeTab_in === 'ActiveEvent' ? '新增能效KPI指标' : '新增转换效率指标'
      "
    >
      <span slot="footer">
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </span>
      <div>
        <CetForm
          :data.sync="CetForm_1.data"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
        >
          <el-form-item label="指标名称" prop="name">
            <ElInput
              v-model="CetForm_1.data.name"
              v-bind="ElInput_1"
              v-on="ElInput_1.event"
            ></ElInput>
          </el-form-item>
          <template v-if="activeTab_in === 'ActiveEvent'">
            <el-form-item label="能源类型" prop="energytype">
              <ElSelect
                v-model="CetForm_1.data.energytype"
                v-bind="ElSelect_energytype"
                v-on="ElSelect_energytype.event"
              >
                <ElOption
                  v-for="item in ElOption_energytype.options_in"
                  :key="item[ElOption_energytype.key]"
                  :label="item[ElOption_energytype.label]"
                  :value="item[ElOption_energytype.value]"
                  :disabled="item[ElOption_energytype.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
            <el-form-item label="指标属性" prop="unittype">
              <ElSelect
                v-model="CetForm_1.data.unittype"
                v-bind="ElSelect_unittype"
                v-on="ElSelect_unittype.event"
              >
                <ElOption
                  v-for="item in ElOption_unittype.options_in"
                  :key="item[ElOption_unittype.key]"
                  :label="item[ElOption_unittype.label]"
                  :value="item[ElOption_unittype.value]"
                  :disabled="item[ElOption_unittype.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </template>
          <template v-else>
            <el-form-item label="消耗能源类型" prop="energytype">
              <ElSelect
                v-model="CetForm_1.data.energytype"
                v-bind="ElSelect_inenergytype"
                v-on="ElSelect_inenergytype.event"
              >
                <ElOption
                  v-for="item in ElOption_inenergytype.options_in"
                  :key="item[ElOption_inenergytype.key]"
                  :label="item[ElOption_inenergytype.label]"
                  :value="item[ElOption_inenergytype.value]"
                  :disabled="item[ElOption_inenergytype.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
            <el-form-item label="产出载能工质" prop="prodenergytype">
              <ElSelect
                v-model="CetForm_1.data.prodenergytype"
                v-bind="ElSelect_prodenergytype"
                v-on="ElSelect_prodenergytype.event"
              >
                <ElOption
                  v-for="item in ElOption_prodenergytype.options_in"
                  :key="item[ElOption_prodenergytype.key]"
                  :label="item[ElOption_prodenergytype.label]"
                  :value="item[ElOption_prodenergytype.value]"
                  :disabled="item[ElOption_prodenergytype.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </template>

          <el-form-item label="最小分析周期" prop="aggregationcycle">
            <ElSelect
              v-model="CetForm_1.data.aggregationcycle"
              v-bind="ElSelect_aggregationcycle"
              v-on="ElSelect_aggregationcycle.event"
            >
              <ElOption
                v-for="item in ElOption_aggregationcycle.options_in"
                :key="item[ElOption_aggregationcycle.key]"
                :label="item[ElOption_aggregationcycle.label]"
                :value="item[ElOption_aggregationcycle.value]"
                :disabled="item[ElOption_aggregationcycle.disabled]"
              ></ElOption>
            </ElSelect>
          </el-form-item>
          <el-form-item
            label="选择产品"
            prop="producttype"
            v-if="CetForm_1.data.unittype == 1"
          >
            <ElSelect
              v-model="CetForm_1.data.producttype"
              v-bind="ElSelect_producttype"
              v-on="ElSelect_producttype.event"
            >
              <ElOption
                v-for="item in ElOption_producttype.options_in"
                :key="item[ElOption_producttype.key]"
                :label="item[ElOption_producttype.label]"
                :value="item[ElOption_producttype.value]"
                :disabled="item[ElOption_producttype.disabled]"
              ></ElOption>
            </ElSelect>
          </el-form-item>
          <el-form-item
            label="系数"
            prop="coef"
            v-if="CetForm_1.data.unittype == 4"
          >
            <ElInputNumber
              v-model="CetForm_1.data.coef"
              v-bind="ElInputNumber_1"
              v-on="ElInputNumber_1.event"
            ></ElInputNumber>
          </el-form-item>
          <el-collapse v-model="activeNames" @change="handleChange">
            <el-collapse-item title="高级选项" name="1">
              <el-form-item
                label="维度选择"
                prop="dimension"
                v-if="activeNames.length > 0"
              >
                <ElSelect
                  v-model="CetForm_1.data.dimension"
                  v-bind="ElSelect_dimension"
                  v-on="ElSelect_dimension.event"
                >
                  <ElOption
                    v-for="item in ElOption_dimension.options_in"
                    :key="item[ElOption_dimension.key]"
                    :label="item[ElOption_dimension.label]"
                    :value="item[ElOption_dimension.value]"
                    :disabled="item[ElOption_dimension.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
              <div class="red fl">用能对象</div>
              <ElSelect
                v-model="ElSelect_energyUsers.value"
                v-bind="ElSelect_energyUsers"
                v-on="ElSelect_energyUsers.event"
                size="small"
                class="fl"
              >
                <!-- <ElSelect v-model="CetForm_1.data.energyUsers" v-bind="ElSelect_energyUsers" v-on="ElSelect_energyUsers.event"> -->
                <ElOption
                  v-for="item in ElOption_energyUsers.options_in"
                  :key="item[ElOption_energyUsers.key]"
                  :label="item[ElOption_energyUsers.label]"
                  :value="item[ElOption_energyUsers.value]"
                  :disabled="item[ElOption_energyUsers.disabled]"
                ></ElOption>
              </ElSelect>
            </el-collapse-item>
          </el-collapse>
        </CetForm>
      </div>
    </CetDialog>
    <CetInterface
      :data.sync="CetInterface_producttype.data"
      :dynamicInput.sync="CetInterface_producttype.dynamicInput"
      v-bind="CetInterface_producttype"
      v-on="CetInterface_producttype.event"
    ></CetInterface>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import common from "eem-utils/common";
import { httping } from "@omega/http";
export default {
  name: "EditIndexes",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    treeData_in: {
      type: Object
    },
    clickProjectNode_in: {
      type: Object
    },
    activeTab_in: {
      type: String
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.localStorage) {
          return false;
        } else {
          var storage = window.localStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    }
  },

  data() {
    return {
      activeNames: [],
      CustomDatePicker_1: {
        queryTime: {
          startTime: null,
          endTime: null,
          cycle: 12 //17年，14月，12日，20自定义
        },
        dataConfig: {
          time: null,
          cycle: 1,
          showPicker: true,
          showRange: true,
          type: [
            {
              id: 1,
              text: "日",
              type: "date"
            },
            {
              id: 3,
              text: "月",
              type: "month"
            },
            {
              id: 5,
              text: "年",
              type: "year"
            }
          ]
        }
      },
      ElInputNumber_1: {
        ...common.check_numberFloat,
        value: "",
        style: {
          width: "200px"
        },
        controls: false,
        event: {
          change: this.ElInputNumber_1_change_out
        }
      },
      CetDialog_1: {
        title: "新建",
        width: "420px",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "确认",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: ["energyUsers"], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {
          energyUsers: 1
        },
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        rules: {
          name: [
            {
              required: true,
              message: "请输入能效指标方案名称",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          energytype: [
            {
              required: true,
              message: "请选择能源类型",
              trigger: ["blur", "change"]
            }
          ],
          prodenergytype: [
            {
              required: true,
              message: "请选择产出载能工质",
              trigger: ["blur", "change"]
            }
          ],

          unittype: [
            {
              required: true,
              message: "请选择能效单位类型",
              trigger: ["blur", "change"]
            }
          ],
          aggregationcycle: [
            {
              required: true,
              message: "请选择分析周期",
              trigger: ["blur", "change"]
            }
          ],
          producttype: [
            {
              required: true,
              message: "请选择产品",
              trigger: ["blur", "change"]
            }
          ],
          coef: [
            {
              required: true,
              message: "清填写系数",
              trigger: ["blur", "change"]
            }
          ],
          dimension: [
            {
              required: true,
              message: "请选择维度",
              trigger: ["blur", "change"]
            }
          ]
          // energyUsers: [
          //   {
          //     required: true,
          //     message: "请选择用能对象",
          //     trigger: ["blur", "change"]
          //   }
          // ]
        },
        event: {
          currentData_out: this.CetForm_1_currentData_out,
          saveData_out: this.CetForm_1_saveData_out,
          finishData_out: this.CetForm_1_finishData_out,
          finishTrigger_out: this.CetForm_1_finishTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElInput_1_change_out,
          input: this.ElInput_1_input_out
        }
      },
      ElSelect_energytype: {
        value: null,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_energytype_change_out
        }
      },
      ElOption_energytype: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // 消耗能源类型下拉框
      ElSelect_inenergytype: {
        value: null,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_inenergytype_change_out
        }
      },
      ElOption_inenergytype: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // 产出能源类型下拉框
      ElSelect_prodenergytype: {
        value: null,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_prodenergytype_change_out
        }
      },
      ElOption_prodenergytype: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },

      ElSelect_unittype: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_unittype_change_out
        }
      },
      ElOption_unittype: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_aggregationcycle: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_aggregationcycle_change_out
        }
      },
      ElOption_aggregationcycle: {
        options_in: [
          {
            id: 12,
            text: "日",
            type: "date"
          },
          {
            id: 13,
            text: "周",
            type: "month"
          },
          {
            id: 14,
            text: "月",
            type: "month"
          },
          {
            id: 17,
            text: "年",
            type: "year"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_producttype: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_producttype_change_out
        }
      },
      ElOption_producttype: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_dimension: {
        value: 1,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_dimension_change_out
        }
      },
      ElOption_dimension: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_energyUsers: {
        value: null,
        style: {
          width: "200px"
        },
        event: {
          // change: this.ElSelect_energyUsers_change_out
        }
      },
      ElOption_energyUsers: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetInterface_producttype: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "queryEnum",
          modelLabel: "Producttype",
          dataIndex: [],
          modelList: [],
          filters: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {},
        page_in: null, // exp:{ index: 1, limit: 20 }
        defaultSort: null, // { prop: "code"  order: "descending" },
        event: {
          result_out: this.CetInterface_producttype_result_out,
          finishTrigger_out: this.CetInterface_producttype_finishTrigger_out,
          failTrigger_out: this.CetInterface_producttype_failTrigger_out,
          totalNum_out: this.CetInterface_producttype_totalNum_out
        }
      },
      projectList: []
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.grtEnergyefficiencyunittype();
      this.getProjectEnergy();
      this.getDimensionMsg();
      this.$nextTick(() => {
        this.CetInterface_producttype.queryTrigger_in = new Date().getTime();
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {
      var vm = this;
      // vm.CetDialog_1.queryId_in = val;
    },
    inputData_in(val) {
      // this.CetDialog_1.inputData_in = val;
      this.CetForm_1.data = val;
    },
    treeData_in(val) {
      // this.CetDialog_1.treeData_in = val;
    },
    clickProjectNode_in(val) {}
  },

  methods: {
    handleChange(val) {},
    grtEnergyefficiencyunittype() {
      this.ElOption_unittype.options_in = [];
      customApi.queryEnumerations("energyefficiencyunittype").then(res => {
        if (res.code === 0 && res.data.length > 0) {
          var data = [];
          res.data.forEach(item => {
            if (item.id <= 4) {
              data.push(item);
            }
          });
          this.ElOption_unittype.options_in = data;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    //获取维度列表信息
    getDimensionMsg: function () {
      var _this = this;
      var auth = _this.token; //身份验证
      // auth = _this.make_base_auth("kermit", "kermit"); //身份验证
      _this.loading = true;
      httping({
        url: "/eem-service/v1/dim/setting/list",
        method: "GET",
        timeout: 10000
      }).then(res => {
        if (res.code !== 0) return;
          let dimData = res.data || [];
          dimData = dimData.filter(item => {
            return item.fixdim;
          });
          this.ElOption_dimension.options_in = dimData;
      });
    },
    ElInputNumber_1_change_out(val) {},
    close() {
      this.ElOption_energyUsers.options_in = [];
      this.ElSelect_energyUsers.value = null;
      this.CetDialog_1.closeTrigger_in = new Date().getTime();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.close();
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetDialog_1_open_out(val) {},
    CetDialog_1_close_out(val) {},
    CetForm_1_currentData_out(val) {},
    CetForm_1_saveData_out(val) {
      if (!this.clickProjectNode_in || !this.treeData_in) {
        this.$message.warning("请选择节点！");
        return;
      }

      var _this = this;
      var auth = _this.token; //身份验证
      var params = [];
      var model_data = this._.cloneDeep(this.CetForm_1.data);
      // 选择高级选项时 写入用能对象id
      if (this.activeNames.length > 0) {
        if (!this.ElSelect_energyUsers.value) {
          this.$message("请选择用能对象");
          return;
        }
        model_data.energyefftype = 2;
        model_data.dimtagids = this.ElSelect_energyUsers.value;
      } else {
        model_data.energyefftype = 1;
      }
      // 转换效率统计 单位指标类型为7
      if (this.activeTab_in === "HistoricalEvent") {
        model_data.unittype = 7;
      }
      model_data.modelLabel = "energyefficiencyset";
      delete model_data.dimension;
      // model_data.objectlabel = this.treeData_in.modelLabel;
      var obj = {
        id: this.clickProjectNode_in.id,
        modelLabel: "project",
        energyefficiencyset_model: [model_data]
      };
      params.push(obj);

      // params[0] = model_data;
      this.Add_energy_efficiency(obj);
    },
    CetForm_1_finishData_out(val) {},
    CetForm_1_finishTrigger_out(val) {},
    ElInput_1_change_out(val) {},
    ElInput_1_input_out(val) {},
    ElSelect_energytype_change_out(val) {},
    ElSelect_inenergytype_change_out(val) {
      const list = this.ElOption_energytype.options_in.filter(item => {
        return item.id !== val;
      });
      this.ElOption_prodenergytype.options_in = list;
    },
    ElSelect_prodenergytype_change_out(val) {
      const list = this.ElOption_energytype.options_in.filter(item => {
        return item.id !== val;
      });
      this.ElOption_inenergytype.options_in = list;
    },
    ElSelect_unittype_change_out(val) {},
    ElSelect_aggregationcycle_change_out(val) {},
    ElSelect_producttype_change_out(val) {},
    ElSelect_dimension_change_out(val) {
      const item = this.ElOption_dimension.options_in.filter(item => {
        return item.id === val;
      })[0];

      const energyUsers = [];
      if (item.children) {
        item.children.forEach(level => {
          level.children &&
            level.children.forEach(tag => {
              energyUsers.push(tag);
            });
        });
        this.ElOption_energyUsers.options_in = this._.cloneDeep(energyUsers);
      }
      if (energyUsers.length > 0) {
        this.ElSelect_energyUsers.value = energyUsers[0].id;
      } else {
        this.ElSelect_energyUsers.value = null;
      }
    },
    ElSelect_energyUsers_change_out(val) {},
    CetInterface_producttype_finishTrigger_out(val) {},
    CetInterface_producttype_failTrigger_out(val) {},
    CetInterface_producttype_totalNum_out(val) {},
    CetInterface_producttype_result_out(val) {
      this.ElOption_producttype.options_in = this._.cloneDeep(val);
    },
    Add_energy_efficiency(params) {
      var _this = this;
      var auth = _this.token; //身份验证

      customApi.writeModel(params).then(res => {
        if (res.code === 0) {
          var modelId = _this._.get(
            res,
            ["data", "0", "energyefficiencyset_model", "0", "id"],
            0
          );
          _this.to_kpiset(modelId);
        } else {
          _this.$message.error(res.msg);
        }
      });
    },
    to_kpiset(id) {
      if (!id) {
        return;
      }
      if (!this.treeData_in) {
        this.$message.warning("请选择节点！");
        return;
      }
      var _this = this;
      var auth = _this.token; //身份验证
      var params = [];

      var obj = {
        energyefficiencyset_id: id,
        objectid: this.treeData_in.id,
        objectlabel: this.treeData_in.modelLabel,
        modelLabel: "kpiset"
      };
      params.push(obj);
      customApi.writeModel(obj).then(res => {
        if (res.code === 0) {
          _this.$emit("saveData_out", new Date().getTime());
          _this.close();
        } else {
          _this.$message.error(res.msg);
        }
      });
    },
    // 获取项目能源类型
    getProjectEnergy() {
      httping({
        url:
          "/eem-service/v1/project/projectEnergy?projectId=" + this.projectId,
        method: "GET"
      }).then(res => {
        if (res.code === 0 && res.data && res.data.length > 0) {
          var selectData = res.data.map(item => {
            return {
              id: item.energytype,
              text: item.name
            };
          });
          this.ElOption_energytype.options_in = this._.cloneDeep(selectData);
          this.ElOption_inenergytype.options_in = this._.cloneDeep(selectData);
          this.ElOption_prodenergytype.options_in =
            this._.cloneDeep(selectData);
          if (selectData.filter(item => item.id == 2).length > 0) {
            this.ElSelect_energytype.value = 2;
          } else {
            this.ElSelect_energytype.value = selectData[0].id;
          }
        } else {
          this.ElOption_energytype.options_in = [];
          this.ElSelect_energytype.value = null;
        }
      });
    }
  },
  created() {}
};
</script>
<style lang="scss" scoped>
:deep(.el-collapse) {
  border: none !important;
  :deep(.el-collapse-item__header) {
    border: none !important;
    text-align: right;
    display: block;
  }
  :deep(.el-collapse-item__wrap) {
    border: none !important;
  }
}
.red {
  width: 110px;
  text-align: right;
  margin-right: 10px;
  color: #606266;
}
.red::before {
  content: "*";
  color: #f56c6c;
  margin-right: 6px;
  margin-top: 6px;
}
:deep(.el-input),
.el-input__inner {
  height: 34px !important;
}
</style>
