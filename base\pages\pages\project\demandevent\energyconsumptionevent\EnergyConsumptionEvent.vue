<template>
  <div class="page eem-common">
    <el-container class="fullheight padding0 flex-column eem-min-width">
      <div class="mbJ3 eem-container">
        <div class="">
          <CetButton
            v-permission="'systemevent_confirm'"
            class="fr"
            v-bind="CetButton_3"
            v-on="CetButton_3.event"
          ></CetButton>
          <CetButton
            class="fr mrJ1"
            v-bind="CetButton_7"
            v-on="CetButton_7.event"
          ></CetButton>
          <div class="clearfix time fr mrJ1">
            <!-- <div class="time-label">时段:</div> -->
            <div class="time-range" style="width: 360px">
              <TimeRange
                @change="handleChange_out"
                :val="[TimeRange_1.starttime, TimeRange_1.endtime]"
                :disabledDate="TimeRangeDisabledDate"
              ></TimeRange>
            </div>
          </div>
          <div class="fr mrJ1 multipleSelect">
            <customElSelect
              v-model="ElSelect_type.value"
              v-bind="ElSelect_type"
              v-on="ElSelect_type.event"
              :prefix_in="$T('告警等级')"
            >
              <ElOption
                v-for="item in ElOption_type.options_in"
                :key="item[ElOption_type.key]"
                :label="item[ElOption_type.label]"
                :value="item[ElOption_type.value]"
                :disabled="item[ElOption_type.disabled]"
              ></ElOption>
            </customElSelect>
            <!-- <span class="basic-box-label">告警等级</span>
          <ElSelect
            v-model="ElSelect_type.value"
            v-bind="ElSelect_type"
            v-on="ElSelect_type.event"
          >
            <ElOption
              v-for="item in ElOption_type.options_in"
              :key="item[ElOption_type.key]"
              :label="item[ElOption_type.label]"
              :value="item[ElOption_type.value]"
              :disabled="item[ElOption_type.disabled]"
            ></ElOption>
          </ElSelect> -->
          </div>
          <div class="fr">
            <div class="check-box fl custom-check-box lh32">
              <ElRadioGroup
                v-model="ElRadioGroup_1.value"
                v-bind="ElRadioGroup_1"
                v-on="ElRadioGroup_1.event"
              >
                <ElRadio
                  v-for="item in ElRadioList_1.options_in"
                  :key="item[ElRadioList_1.key]"
                  :label="item[ElRadioList_1.label]"
                  :disabled="item[ElRadioList_1.disabled]"
                >
                  {{ item[ElRadioList_1.text] }}
                </ElRadio>
              </ElRadioGroup>
            </div>
          </div>
        </div>
      </div>
      <div class="flex-auto pJ3 eem-container flex-column">
        <div class="lh32 mbJ3" style="text-align: right">
          <el-tag
            :color="getEventGradeColor(1).bgColor"
            :style="`color: ${getEventGradeColor(1).color}`"
          >
            {{ $T("一级") }}
          </el-tag>
          <span>
            {{ $T("事件数") }}：
            <span>{{ labelCount.firstlevel }}</span>
          </span>
          <el-tag
            class="mlJ3"
            :color="getEventGradeColor(2).bgColor"
            :style="`color: ${getEventGradeColor(2).color}`"
          >
            {{ $T("二级") }}
          </el-tag>
          <span>
            {{ $T("事件数") }}：
            <span>{{ labelCount.secondlevel }}</span>
          </span>
          <el-tag
            class="mlJ3"
            :color="getEventGradeColor(3).bgColor"
            :style="`color: ${getEventGradeColor(3).color}`"
          >
            {{ $T("三级") }}
          </el-tag>
          <span>
            {{ $T("事件数") }}：
            <span>{{ labelCount.thirdlevel }}</span>
          </span>
        </div>

        <div class="flex-auto">
          <EnergyConsumptionEventTable
            :visibleTrigger_in="EnergyConsumptionEventTable.visibleTrigger_in"
            :closeTrigger_in="EnergyConsumptionEventTable.closeTrigger_in"
            :queryId_in="EnergyConsumptionEventTable.queryId_in"
            :queryNode_in="EnergyConsumptionEventTable.queryNode_in"
            :clickNode_in="clickNode"
            :page_in="page"
            :inputData_in="EnergyConsumptionEventTable.inputData_in"
            :confirmStatus_in="EnergyConsumptionEventTable.confirmStatus_in"
            :convergence_in="EnergyConsumptionEventTable.convergence_in"
            :exportTrigger_in="EnergyConsumptionEventTable.exportTrigger_in"
            :refreshTrigger_in="EnergyConsumptionEventTable.refreshTrigger_in"
            :searchKeyWords_in="EnergyConsumptionEventTable.searchKeyWords_in"
            :updateEventData_in="EnergyConsumptionEventTable.updateEventData_in"
            @finishTrigger_out="EnergyConsumptionEventTable_finishTrigger_out"
            @finishData_out="EnergyConsumptionEventTable_finishData_out"
            @saveData_out="EnergyConsumptionEventTable_saveData_out"
            @detailTrigger_out="EnergyConsumptionEventTable_detailTrigger_out"
            @currentData_out="EnergyConsumptionEventTable_currentData_out"
            @selectionChange_out="
              EnergyConsumptionEventTable_selectionChange_out
            "
            @record_out="EnergyConsumptionEventTable_record_out"
            @confirmTrigger_out="EnergyConsumptionEventTable_confirmTrigger_out"
            @waveview_out="EnergyConsumptionEventTable_waveview_out"
            @totalCount_out="EnergyConsumptionEventTable_totalCount_out"
            :levels_in="ElSelect_type.value"
            :date_in="TimeRange_1"
            @labelCount_out="EnergyConsumptionEventTable_labelCount_out"
            @handlerCurrentChange_out="
              EnergyConsumptionEventTable_handlerCurrentChange_out
            "
            @queryEvent_out="EnergyConsumptionEventTable_handlerQueryEvent_out"
          />
        </div>
        <div class="mtJ3">
          <el-row>
            <el-col :span="24" style="text-align: right">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.totalCount"
              ></el-pagination>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-container>
    <EnergyConsumptionEventConfirm
      :visibleTrigger_in="EnergyConsumptionEventConfirm.visibleTrigger_in"
      :closeTrigger_in="EnergyConsumptionEventConfirm.closeTrigger_in"
      :queryId_in="EnergyConsumptionEventConfirm.queryId_in"
      :inputData_in="EnergyConsumptionEventConfirm.inputData_in"
      @finishTrigger_out="EnergyConsumptionEventConfirm_finishTrigger_out"
      @finishData_out="EnergyConsumptionEventConfirm_finishData_out"
      @saveData_out="EnergyConsumptionEventConfirm_saveData_out"
      @currentData_out="EnergyConsumptionEventConfirm_currentData_out"
      @confirmedTrigger_out="EnergyConsumptionEventConfirm_confirmedTrigger_out"
    />
  </div>
</template>
<script>
import EnergyConsumptionEventTable from "./EnergyConsumptionEventTable.vue";
import EnergyConsumptionEventConfirm from "./EnergyConsumptionEventConfirm.vue";
import TimeRange from "eem-components/TimeRange";
import { getEventGradeColor } from "eem-utils/eventColor.js";
export default {
  name: "EnergyConsumptionEvent",
  components: {
    EnergyConsumptionEventTable,
    EnergyConsumptionEventConfirm,
    TimeRange
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    }
  },

  props: {
    refreshTrigger_in: {
      type: Number
    },
    clickNode: {
      type: Object
    }
  },

  data() {
    return {
      currentItem: null,
      labelCount: {
        firstlevel: 0,
        secondlevel: 0,
        thirdlevel: 0
      },
      TimeRange_1: {
        starttime: this.$moment().startOf("d").add(-1, "M").valueOf(),
        endtime: this.$moment().startOf("d").add(1, "d").valueOf()
      },
      ElRadioGroup_1: {
        value: true,
        style: {},
        event: {
          change: this.ElRadioGroup_1_change_out
        }
      },
      ElRadioList_1: {
        options_in: [
          {
            id: true,
            text: $T("待处理事件")
          },
          {
            id: false,
            text: $T("已处理事件")
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      ElSelect_type: {
        value: [1, 2, 3],
        style: { width: "256px" },
        multiple: true,
        collapseTags: true,
        size: "small",
        event: {
          change: this.ElSelect_type_change_out
        }
      },
      ElOption_type: {
        options_in: [
          {
            id: 1,
            text: $T("一级告警")
          },
          {
            id: 2,
            text: $T("二级告警")
          },
          {
            id: 3,
            text: $T("三级告警")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetButton_3: {
        visible_in: true,
        disable_in: true,
        title: $T("批量确认"),
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      eventType: {
        runningEvent: $T("运行事件"),
        EnergyConsumptionEvent: $T("电能质量事件"),
        energyManagementEvent: $T("能源管理事件")
      },
      filterText: "",
      EnergyConsumptionEventTable: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        queryNode_in: {},
        queryId_in: 0,
        inputData_in: null,
        confirmStatus_in: true,
        convergence_in: false,
        searchKeyWords_in: "",
        updateEventData_in: []
      },
      EnergyConsumptionEventConfirm: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      CetButton_7: {
        visible_in: true,
        disable_in: true,
        title: $T("查看进线"),
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_7_statusTrigger_out
        }
      },
      nodeName: "",
      page: {
        currentPage: 1,
        pageSize: 20,
        totalCount: 100
      }
    };
  },
  watch: {
    refreshTrigger_in(val) {
      this.EnergyConsumptionEventTable.refreshTrigger_in =
        this._.cloneDeep(val);
    },
    clickNode: {
      deep: true,
      handler: function (val, oldVal) {
        if (!(this._.get(val, "id") && this._.get(val, "modelLabel"))) return;
        if (
          val.id === this._.get(oldVal, "id") &&
          val.modelLabel === this._.get(oldVal, "modelLabel")
        ) {
          return;
        }
        this.nodeName = val.name;
      }
    },
    "page.totalCount": {
      deep: true,
      handler: function (val, oldVal) {
        this.page.currentPage = 1;
      }
    },
    currentItem: {
      deep: true,
      handler(val) {
        this.CetButton_7.disable_in = !val;
      }
    }
  },

  methods: {
    getEventGradeColor(val) {
      return getEventGradeColor(val);
    },
    CetButton_3_statusTrigger_out(val) {
      this.EnergyConsumptionEventConfirm.visibleTrigger_in =
        this._.cloneDeep(val);
    },
    CetButton_7_statusTrigger_out(val) {
      if (this.currentItem) {
        var params = this._.cloneDeep(this.currentItem);
        params.id = params.object_id;
        params.modelLabel = params.object_label;
        this.$router.push({
          name: "demandmonitor",
          params: params
        });
      }
    },
    ElRadioGroup_1_change_out(val) {
      this.EnergyConsumptionEventTable.confirmStatus_in = this._.cloneDeep(val);
    },
    ElSelect_type_change_out(val) {},
    EnergyConsumptionEventConfirm_currentData_out(val) {},
    EnergyConsumptionEventConfirm_finishData_out(val) {},
    EnergyConsumptionEventConfirm_finishTrigger_out(val) {},
    EnergyConsumptionEventConfirm_saveData_out(val) {},
    EnergyConsumptionEventConfirm_confirmedTrigger_out(eventList) {
      // this.EnergyConsumptionEventTable.updateEventData_in = this._.cloneDeep(eventList);
      this.EnergyConsumptionEventTable.refreshTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    EnergyConsumptionEventTable_confirmTrigger_out(val) {
      this.EnergyConsumptionEventConfirm.inputData_in = {
        eventlist: this._.cloneDeep(val)
      };
      this.EnergyConsumptionEventConfirm.visibleTrigger_in =
        new Date().getTime();
    },
    EnergyConsumptionEventTable_waveview_out(val) {
      this.$emit("waveview_out", this._.cloneDeep(val));
    },
    EnergyConsumptionEventTable_handlerCurrentChange_out(val) {
      this.currentItem = val;
      if (val) {
        this.CetButton_7.disable_in = false;
      } else {
        this.CetButton_7.disable_in = true;
      }
    },
    EnergyConsumptionEventTable_totalCount_out(val) {
      this.page.totalCount = val;
    },
    EnergyConsumptionEventTable_labelCount_out(val) {
      this.labelCount = val;
    },
    EnergyConsumptionEventTable_currentData_out(val) {},
    EnergyConsumptionEventTable_detailTrigger_out(val) {},
    EnergyConsumptionEventTable_finishData_out(val) {},
    EnergyConsumptionEventTable_finishTrigger_out(val) {},
    EnergyConsumptionEventTable_record_out(val) {
      this.$emit("record_out", this._.cloneDeep(val));
    },
    EnergyConsumptionEventTable_saveData_out(val) {},
    EnergyConsumptionEventTable_selectionChange_out(val) {
      this.EnergyConsumptionEventConfirm.inputData_in = {
        eventlist: this._.cloneDeep(val)
      };

      this.CetButton_3.disable_in = !val.length;
    },
    EnergyConsumptionEventTable_handlerQueryEvent_out() {
      this.currentItem = null;
    },
    //分页大小变化
    handleSizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
    },
    //分页当前页变化
    handleCurrentChange(val) {
      this.page.currentPage = val;
    },
    handleChange_out(val) {
      if (!val) {
        return;
      }
      if (val[0] === val[1]) {
        this.TimeRange_1.starttime = new Date(val[0]).getTime();
        this.TimeRange_1.endtime =
          new Date(val[0]).getTime() + 1000 * 3600 * 24;
        this.$message.warning($T("结束时间不能小于等于开始时间！"));
        return;
      }
      this.TimeRange_1.starttime = val[0] ? val[0] : new Date().getTime();
      this.TimeRange_1.endtime = val[1]
        ? val[1]
        : new Date().getTime() + 1000 * 3600 * 24;
    },
    TimeRangeDisabledDate(time) {
      return time.getTime() > this.$moment().add(1, "d").valueOf();
    }
  },
  created: function () {},
  mounted: function () {
    this.nodeName = this.clickNode ? this.clickNode.name : null;
  },
  activated: function () {
    this.ElSelect_type.value = [1, 2, 3];
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .el-tag {
    line-height: 25px;
  }
}
.head-label {
  line-height: 50px;
  font-size: 20px;
  color: #0152d9;
  float: left;
}
.head-label span {
  display: block;
  float: left;
  width: 32px;
  height: 32px;
  border-radius: 32px;
  margin: 9px 15px;
  background: url(./assets/trend.png) no-repeat center #0350da;
}
.event-title {
  display: inline-block;
  line-height: 40px;
  vertical-align: middle;
}
.event-title-icon {
  font-size: 40px;
}
.search-input {
  width: 200px;
}
.device-Input {
  display: inline-block;
}
.check-box {
  display: inline-block;
  :deep(.el-radio) {
    @include margin_right(J3);
  }
}
.time {
  display: flex;
  height: 32px;
  line-height: 32px;
}
.time-label {
  width: 50px;
  line-height: 32px;
}
.time-range {
  flex: 1;
}
// el-select多选以tag展示时，超过显示长度以...省略号显示
.multipleSelect {
  :deep(.el-select__tags-text) {
    display: inline-block;
    max-width: 75px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  :deep(.el-tag__close) {
    top: -5px;
  }
}
</style>
