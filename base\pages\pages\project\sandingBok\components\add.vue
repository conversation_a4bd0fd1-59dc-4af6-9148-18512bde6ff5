<template>
  <div>
    <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="small">
      <el-main class="eem-cont-c1 fullheight">
        <CetForm
          :data.sync="CetForm_1.data"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
          class="form"
        >
          <el-form-item
            :label="`${item.alias}`"
            :prop="item.name"
            v-for="(item, index) in templateArr"
            :key="index"
            :rules="judgeRules(item)"
          >
            <!-- 测量对象使用弹框 -->
            <div
              class="text img"
              :title="CetForm_1.data[item.name]"
              v-if="item.name == 'monitorname'"
            >
              <span class="text-ellipsis fl">
                {{ CetForm_1.data[item.name] || "--" }}
              </span>
              <CetButton
                class="fr"
                v-bind="CetButton_add"
                v-on="CetButton_add.event"
              ></CetButton>
            </div>
            <!-- 检定记录回填方式 -->
            <div
              class="text img"
              v-else-if="
                actionTitle_in === 2 && backfillArr.indexOf(item.name) !== -1
              "
              :title="CetForm_1.data[item.name]"
            >
              <span class="text-ellipsis fl">
                {{ CetForm_1.data[item.name] || "--" }}
              </span>
            </div>
            <!-- 检定记录仪表编号用输入框搜索模式 -->
            <el-autocomplete
              style="width: 250px"
              v-else-if="actionTitle_in === 2 && item.name == 'code'"
              v-model="CetForm_1.data[item.name]"
              :fetch-suggestions="querySearch"
              :placeholder="$T('请输入内容')"
              :trigger-on-focus="false"
              :disabled="editData_in && editData_in.id ? true : false"
              @select="handleSelect"
              @change="autocompleteChange"
            >
              <template slot-scope="{ item }">
                <div>{{ `${item.code}(${item.name || ""})` }}</div>
              </template>
            </el-autocomplete>
            <ElInput
              v-else-if="item.datatype == 'string'"
              v-model.trim="CetForm_1.data[item.name]"
              v-bind="ElInput_string"
              v-on="ElInput_string.event"
            ></ElInput>
            <ElInputNumber
              v-else-if="item.datatype == 'int4'"
              v-model="CetForm_1.data[item.name]"
              :min="item.name === 'plannedverificationcycle' ? 0 : -99999999"
              v-bind="ElInputNumber_num1"
              v-on="ElInputNumber_num1.event"
            ></ElInputNumber>
            <ElInputNumber
              v-else-if="item.datatype == 'int8'"
              v-model="CetForm_1.data[item.name]"
              :min="item.name === 'plannedverificationcycle' ? 0 : -99999999"
              v-bind="ElInputNumber_num1"
              v-on="ElInputNumber_num1.event"
            ></ElInputNumber>
            <ElInputNumber
              v-else-if="item.datatype == 'float'"
              v-model="CetForm_1.data[item.name]"
              :min="item.name === 'plannedverificationcycle' ? 0 : -99999999.99"
              v-bind="ElInputNumber_num2"
              v-on="ElInputNumber_num2.event"
            ></ElInputNumber>
            <el-date-picker
              style="width: 250px !important"
              v-else-if="item.datatype == 'date'"
              v-model="CetForm_1.data[item.name]"
              :placeholder="$T('选择日期')"
              value-format="timestamp"
            ></el-date-picker>
            <ElSelect
              v-else-if="item.datatype == 'boolean'"
              v-model="CetForm_1.data[item.name]"
              v-bind="ElSelect_boolean"
              v-on="ElSelect_boolean.event"
            >
              <ElOption
                v-for="item in ElOption_boolean.options_in"
                :key="item[ElOption_boolean.key]"
                :label="item[ElOption_boolean.label]"
                :value="item[ElOption_boolean.value]"
                :disabled="item[ElOption_boolean.disabled]"
              ></ElOption>
            </ElSelect>
            <ElSelect
              v-else-if="
                item.datatype == 'enum' ||
                (item.enumerationvalue && item.enumerationvalue.length)
              "
              v-model="CetForm_1.data[item.name]"
              v-bind="ElSelect_select"
              v-on="ElSelect_select.event"
            >
              <ElOption
                v-for="(item, index) in item.enumerationvalue"
                :key="index"
                :label="item.text"
                :value="item.id"
              ></ElOption>
            </ElSelect>
            <div
              class="text img"
              :title="CetForm_1.data[item.name]"
              v-else-if="item.datatype == 'img'"
            >
              <span class="text-ellipsis fl">
                {{ CetForm_1.data[item.name] || "--" }}
              </span>
              <CetButton
                class="fr"
                v-bind="CetButton_upload"
                @click="CetButton_upload_click_out(item.name)"
                v-on="CetButton_upload.event"
              ></CetButton>
            </div>
          </el-form-item>
        </CetForm>
      </el-main>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
    <el-upload
      v-show="false"
      ref="upload"
      accept=".png,.jpeg,.bmp,.gif,.jpg"
      :infoText_in="$T('支持 JPG,JPEG,BMP,GIF,PNG 文件')"
      action=""
      :auto-upload="true"
      :multiple="false"
      :limit="1"
      :http-request="httpRequest"
    >
      <el-button
        slot="trigger"
        v-show="false"
        size="small"
        type="primary"
        ref="uploadBtn"
      >
        {{ $T("选取文件") }}
      </el-button>
    </el-upload>
    <measureObj
      :visibleTrigger_in="measureObj.visibleTrigger_in"
      :closeTrigger_in="measureObj.closeTrigger_in"
      :inputData_in="measureObj.inputData_in"
      @CetButton_confirm_out="measureObj_CetButton_confirm_out"
    />
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import common from "eem-utils/common.js";
import measureObj from "./measureObj.vue";
export default {
  name: "templateAdmin",
  components: {
    measureObj
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    template_in: {
      type: Array,
      default() {
        return [];
      }
    },
    editData_in: {
      type: Object,
      default() {
        return {};
      }
    },
    actionTitle_in: {
      //1 仪表台账  2检定记录
      type: Number
    }
  },
  data(vm) {
    // 自定义验证仪表编号方法
    var checkEquipmentnumber = (rule, value, callback) => {
      if (!value) {
        return callback(new Error($T("仪表编号不能为空")));
      }
      if (
        this.actionTitle_in === 2 &&
        !this.devices.find(item => item.code === value)
      ) {
        return callback(new Error($T("仪表编号不存在")));
      }
      return callback();
    };
    return {
      backfillArr: ["name", "model", "location"], // 检定记录用回填方式字段
      devices: [], //所有仪表
      requireArr: ["name", "code"], //必填字段
      fieldsName: "", // 暂存上传成功后要存储的对应字段名称
      templateArr: [],
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "180px",
        labelPosition: "top",
        rules: {
          code: [
            {
              required: true,
              validator: checkEquipmentnumber,
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        },
        inline: true
      },
      // string组件
      ElInput_string: {
        value: "",
        placeholder: $T("请输入"),
        style: {
          width: "250px"
        },
        event: {}
      },
      ElSelect_select: {
        value: "",
        style: {
          width: "250px"
        },
        event: {}
      },
      ElSelect_boolean: {
        value: "",
        style: {
          width: "250px"
        },
        event: {}
      },
      ElOption_boolean: {
        options_in: [
          {
            value: true,
            label: $T("是")
          },
          {
            value: false,
            label: $T("否")
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      ElInputNumber_num1: {
        ...common.check_numberInt,
        value: "",
        controls: false,
        placeholder: $T("请输入"),
        style: {
          width: "250px"
        },
        event: {}
      },
      ElInputNumber_num2: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        placeholder: $T("请输入"),
        style: {
          width: "250px"
        },
        event: {}
      },
      CetButton_upload: {
        visible_in: true,
        disable_in: false,
        title: $T("上传"),
        type: "primary",
        plain: true,
        event: {}
      },
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("选择"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      measureObj: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      // 1弹窗组件
      CetDialog_1: {
        title: $T("新增"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    userId() {
      return this.$store.state.userInfo.id;
    }
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      vm.createCondition();
      if (vm.editData_in) {
        vm.CetForm_1.data = vm._.cloneDeep(vm.editData_in);
        vm.CetDialog_1.title =
          vm.actionTitle_in === 1 ? $T("编辑设备") : $T("编辑检定记录");
      } else {
        vm.CetForm_1.data = {};
        vm.CetDialog_1.title =
          vm.actionTitle_in === 1 ? $T("新增设备") : $T("新增检定记录");
      }
      vm.CetForm_1.resetTrigger_in = new Date().getTime();
      vm.$refs.upload.clearFiles();
      if (vm.actionTitle_in === 2) {
        vm.getDeviceTableDate();
      }
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },
  methods: {
    // 生成表单验证规则
    judgeRules(item) {
      var arr = [];
      if (this.requireArr.indexOf(item.name) !== -1) {
        arr.push({
          required: true,
          message: `${item.alias}${$T("不能为空")}`,
          trigger: ["blur", "change"]
        });
      }
      return arr;
    },
    // 构造查询表单 过滤出启用项
    createCondition() {
      this.templateArr = this.template_in.filter(item => {
        if (this.actionTitle_in === 1) {
          // 设备台账测量对象使用弹框交互
          if (item.name === "monitorname") {
            // 测量对象改名
            item.alias = $T("测量对象");
          }
          if (["monitorlabel", "monitorid"].indexOf(item.name) === -1) {
            return !item.allowdeactivation || item.active;
          } else {
            return false;
          }
        } else {
          // 检定记录仪表
          return !item.allowdeactivation || item.active;
        }
      });
    },
    // 获取所有仪表
    getDeviceTableDate() {
      var queryData = {
        rootCondition: {
          filter: {
            expressions: [
              { prop: "projectId", operator: "EQ", limit: this.projectId }
            ]
          }
        }
      };

      customApi.getDeviceTableDate(queryData).then(response => {
        if (response.code === 0) {
          this.devices = this._.get(response, "data", []);
          // 赋值已选的仪表
          if (this.editData_in && this.editData_in.code) {
            this.handleSelect(
              this.devices.find(item => item.code == this.editData_in.code)
            );
          }
        }
      });
    },
    // 输入框过滤
    querySearch(queryString, cb) {
      var devices = this.devices;
      var results = queryString
        ? devices.filter(item => {
            if (item.code === null) {
              console.error($T("ERROR:为啥会有code为空的表"), item);
            }
            return (
              !this._.isNil(item.code) && item.code.indexOf(queryString) !== -1
            );
          })
        : devices;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return restaurant => {
        return (
          restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
          0
        );
      };
    },
    handleSelect(item) {
      this.backfillArr.forEach(code => {
        this.$set(this.CetForm_1.data, code, item ? item[code] : "");
      });
      this.$set(this.CetForm_1.data, "equipmentid", item ? item.id : "");
      this.$set(this.CetForm_1.data, "code", item ? item.code : "");
      this.$set(
        this.CetForm_1.data,
        "verificationagency",
        item ? item.verificationagency : ""
      );
    },
    autocompleteChange() {
      this.$set(this.CetForm_1.data, "name", "");
    },
    addHandel(val) {
      var addFn, addData;
      val.projectid = this.projectId;
      if (this.actionTitle_in === 1) {
        addFn = "editDashboard";
        val.modelLabel = "meterextendinfo";
        addData = [val];
      } else if (this.actionTitle_in === 2) {
        addFn = "editRecord";
        val.modelLabel = "instrumentinspectrecord";
        addData = val;
        // 检定记录新增的时候加传userid
        if (!this.editData_in) {
          addData.userid = this.userId;
        }
      }
      customApi[addFn](addData).then(response => {
        if (response.code === 0) {
          this.$message({
            type: "success",
            message: $T("保存成功！")
          });
          this.CetDialog_1.closeTrigger_in = new Date().getTime();
          this.$emit("upTableData_out");
        }
      });
    },
    httpRequest(val) {
      var type = val.file.type.toUpperCase().split("/")[1];
      if (["JPG", "JPEG", "BMP", "GIF", "PNG"].indexOf(type) === -1) {
        this.$message.error($T("仅支持 JPG,JPEG,BMP,GIF,PNG 文件"));
        this.$refs.upload.clearFiles();
        return;
      }
      const formData = new FormData();
      formData.append("file", val.file);
      this.CetButton_confirm.disable_in = true;
      customApi.uploadFile(formData).then(
        response => {
          if (response.code === 0) {
            this.$message({
              type: "success",
              message: $T("上传成功！")
            });
            this.CetForm_1.data[this.fieldsName] = this._.get(response, "data");
          }
          this.$refs.upload.clearFiles();
          this.CetButton_confirm.disable_in = false;
        },
        () => {
          this.$refs.upload.clearFiles();
          this.CetButton_confirm.disable_in = false;
        }
      );
    },
    CetButton_add_statusTrigger_out(val) {
      if (this.CetForm_1.data.monitorid) {
        this.measureObj.inputData_in = {
          monitorid: this.CetForm_1.data.monitorid,
          monitorlabel: this.CetForm_1.data.monitorlabel,
          name: this.CetForm_1.data.monitorname
        };
      } else {
        this.measureObj.inputData_in = {};
      }
      this.measureObj.visibleTrigger_in = new Date().getTime();
    },
    measureObj_CetButton_confirm_out(val) {
      if (val) {
        this.$set(this.CetForm_1.data, "monitorname", val.name);
        this.$set(this.CetForm_1.data, "monitorid", val.id);
        this.$set(this.CetForm_1.data, "monitorlabel", val.modelLabel);
      } else {
        this.$set(this.CetForm_1.data, "monitorname", null);
        this.$set(this.CetForm_1.data, "monitorid", null);
        this.$set(this.CetForm_1.data, "monitorlabel", null);
      }
    },
    CetButton_upload_click_out(name) {
      this.fieldsName = name;
      this.$refs.uploadBtn.$el.click();
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = new Date().getTime();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = val;
    },
    CetForm_1_saveData_out(val) {
      this.addHandel(val);
    }
  },
  activated: function () {}
};
</script>
<style lang="scss" scoped>
.form {
  .el-form-item {
    width: calc(50% - 10px);
    .img {
      width: 250px;
      & > span {
        width: calc(100% - 50px);
        display: block;
      }
    }
  }
}
</style>
