﻿<template>
  <div class="page eem-common">
    <el-container class="fullheight">
      <el-aside width="315px" class="eem-aside">
        <div class="fullheight">
          <CetGiantTree
            v-bind="CetGiantTree_1"
            v-on="CetGiantTree_1.event"
          ></CetGiantTree>
        </div>
      </el-aside>
      <el-container class="padding0 fullheight mlJ3">
        <el-header
          class="trends__header mbJ3"
          height="32px"
          style="padding: 0px"
        >
          <div class="fontcolor common-title-H1">
            {{
              CetGiantTree_1.inputData_in.length >= 1
                ? CetGiantTree_1.inputData_in[0].name
                : "--"
            }}
          </div>
          <div style="margin-left: auto" class="header__item basic-box fr mrJ1">
            <customElSelect
              v-model="ElSelect_type.value"
              v-bind="ElSelect_type"
              v-on="ElSelect_type.event"
              :prefix_in="$T('能源类型')"
            >
              <ElOption
                v-for="item in ElOption_type.options_in"
                :key="item[ElOption_type.key]"
                :label="item[ElOption_type.label]"
                :value="item[ElOption_type.value]"
                :disabled="item[ElOption_type.disabled]"
              ></ElOption>
            </customElSelect>
          </div>
          <div class="header__item basic-box fr mrJ1">
            <customElSelect
              v-model="ElSelect_dimensionality.value"
              v-bind="ElSelect_dimensionality"
              v-on="ElSelect_dimensionality.event"
              :prefix_in="$T('统计维度')"
            >
              <ElOption
                v-for="item in ElOption_dimensionality.options_in"
                :key="item[ElOption_dimensionality.key]"
                :label="item[ElOption_dimensionality.label]"
                :value="item[ElOption_dimensionality.value]"
                :disabled="item[ElOption_dimensionality.disabled]"
              ></ElOption>
            </customElSelect>
          </div>
          <CetButton
            class="fr custom—square"
            v-bind="CetButton_prv"
            v-on="CetButton_prv.event"
          ></CetButton>
          <!-- <div class="header__item basic-box fr mr10">
            <el-date-picker
              v-model="CetDatePicker_time.val"
              v-bind="CetDatePicker_time.config"
            ></el-date-picker>
          </div> -->
          <CustomElDatePicker
            class="fr mlJ mrJ"
            :prefix_in="$T('选择年份')"
            v-bind="CetDatePicker_time.config"
            v-on="CetDatePicker_time.event"
            v-model="CetDatePicker_time.val"
          />
          <!-- 向后查询按钮 -->
          <CetButton
            class="fr custom—square"
            v-bind="CetButton_next"
            v-on="CetButton_next.event"
          ></CetButton>
        </el-header>

        <el-main style="padding: 0">
          <el-row
            class="mbJ3"
            type="flex"
            v-if="ElSelect_dimensionality.value === 0"
          >
            <el-col :span="6">
              <TrendsTotalCard
                :title="contentText.totalTitle"
                :total="nodeCardsData.total"
              />
            </el-col>
            <el-col :span="8" class="mrJ3 mlJ3">
              <TrendsCompareCard
                :yearOnYear="nodeCardsData.yearOnYearPercent"
                :chain="nodeCardsData.chainPercent"
              />
            </el-col>
            <el-col :span="10">
              <TrendsUnitCard :data="nodeCardsData.unitData" />
            </el-col>
          </el-row>

          <el-row
            type="flex"
            style="flex-wrap: wrap"
            class=""
            v-if="ElSelect_dimensionality.value === 2"
          >
            <div
              style="flex: 0 0 384px"
              v-for="costCard in dimCardsData"
              :key="costCard.title"
              class="mrJ3 mbJ3"
            >
              <TrendsTotalCard
                v-if="!costCard.title"
                :title="contentText.totalTitle"
                :total="costCard.value"
              />
              <TrendsCostCard v-else :data="costCard" />
            </div>
          </el-row>

          <el-row
            type="flex"
            class=""
            style="flex-wrap: wrap"
            v-if="ElSelect_dimensionality.value === 1"
          >
            <div
              style="flex: 0 0 300px"
              v-for="costCard in dimCardsData"
              :key="costCard.title"
              class="mrJ3 mbJ3"
            >
              <TrendsTotalCard
                style="height: 200px"
                v-if="!costCard.title"
                :title="contentText.totalTitle"
                :total="costCard.value"
              />
              <TrendSubentryCard v-else :data="costCard" />
            </div>
          </el-row>

          <div class="eem-container">
            <h3
              class="container__title highlight common-title-H2"
              v-if="chartsTitle"
            >
              {{ chartsTitle }}
            </h3>
            <el-row type="flex" justify="end" align="middle" class="mbJ3">
              <ElSelect
                v-model="ElSelect_items.value"
                v-bind="ElSelect_items"
                v-on="ElSelect_items.event"
                collapse-tags
                style="width: 250px; margin-right: 8px"
                class="custom-select-tag"
                v-if="ElSelect_dimensionality.value === 0"
              >
                <ElOption
                  v-for="item in ElOption_items.options_in"
                  :key="item[ElOption_items.key]"
                  :label="item[ElOption_items.label]"
                  :value="item[ElOption_items.value]"
                  :disabled="item[ElOption_items.disabled]"
                ></ElOption>
              </ElSelect>
              <el-checkbox
                class="mlJ1"
                v-model="chartChecks.maxAndMin"
                @change="checksChange"
              >
                {{ $T("最值") }}
              </el-checkbox>
              <el-checkbox
                class="mlJ1"
                v-model="chartChecks.average"
                @change="checksChange"
              >
                {{ $T("平均值") }}
              </el-checkbox>
              <el-checkbox
                class="mlJ1"
                v-model="chartChecks.showPoint"
                @change="checksChange"
              >
                {{ $T("打点显示") }}
              </el-checkbox>
              <el-checkbox
                class="mlJ1"
                v-model="chartChecks.chain"
                @change="checksChange"
              >
                {{ $T("同比") }}
              </el-checkbox>
            </el-row>
            <div style="height: 380px">
              <CetChart
                v-if="CetChart_1.config.options.series.length >= 1"
                :inputData_in="CetChart_1.inputData_in"
                v-bind="CetChart_1.config"
              />
              <div v-else class="container__empty">{{ $T("暂无数据") }}</div>
            </div>
          </div>
        </el-main>

        <!-- <el-footer height="50px" style="padding:0px;background-color:white; line-height:50px;">
          <div style="float:right;"></div>
        </el-footer> -->
      </el-container>
    </el-container>
  </div>
</template>
<script>
import common from "eem-utils/common";

import TrendsTotalCard from "./components/TrendsTotalCard";
import TrendsCompareCard from "./components/TrendsCompareCard";
import TrendsUnitCard from "./components/TrendsUnitCard";
import TrendsCostCard from "./components/TrendsCostCard";
import TrendSubentryCard from "./components/TrendSubentryCard";
import costanalysisAPI from "../api/costanalysisAPI.js";
import TREE_PARAMS from "@/store/treeParams.js";
import {
  setValue,
  setPercent,
  setStartAndEndTime,
  colors
} from "../utils/utils.js";

const initLineData = {
  color: colors,
  tooltip: {
    trigger: "axis",
    formatter: null
  },
  legend: {
    data: []
  },
  grid: {
    left: "3%",
    right: "4%",
    bottom: "50px",
    top: "50px",
    containLabel: true
  },
  toolbox: {
    top: 40,
    right: 30,
    feature: {
      dataZoom: {
        yAxisIndex: "none"
      },
      // restore: {},
      saveAsImage: {
        title: $T("保存为图片")
      }
    }
  },
  dataZoom: [
    {
      show: true,
      realtime: true,
      start: 0,
      end: 100
    },
    {
      type: "inside",
      realtime: true,
      start: 0,
      end: 100
    }
  ],
  xAxis: {
    type: "category",
    boundaryGap: false,
    axisLine: { onZero: false },
    data: []
  },
  yAxis: [
    {
      name: $T("成本（元）"),
      type: "value"
    }
  ],
  series: []
};

export default {
  name: "Trends",
  components: {
    TrendsTotalCard,
    TrendsCompareCard,
    TrendsUnitCard,
    TrendsCostCard,
    TrendSubentryCard
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    timeRange() {
      const dateType = this.CetDatePicker_time.config.type;
      return setStartAndEndTime(this.CetDatePicker_time.val, dateType);
    },
    projectId() {
      return this.$store.state.projectId;
    },
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },

  data() {
    return {
      unitName: $T("元"),
      chartsTitle: $T("区域维度变化趋势"),
      currentNode: null,
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out
        }
      },
      // 能源类型选择组件
      ElSelect_type: {
        value: 2,
        style: {},
        event: {
          change: this.ElSelect_type_change_out
        }
      },
      ElOption_type: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // 统计维度选择组件
      ElSelect_dimensionality: {
        value: 0,
        style: {},
        event: {
          change: this.ElSelect_dimensionality_change_out
        }
      },
      ElOption_dimensionality: {
        options_in: [
          { id: 0, name: $T("区域维度") },
          { id: 2, name: $T("分类用能") },
          { id: 1, name: $T("分项用能") }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetChart_1: {
        inputData_in: null,
        config: {
          options: initLineData
        }
      },
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      CetDatePicker_time: {
        disable_in: false, //禁用
        val: new Date(),
        config: {
          valueFormat: "timestamp",
          type: "year",
          rangeSeparator: "-",
          style: {
            display: "inline-block",
            width: "200px"
          },
          size: "small",
          clearable: false,
          pickerOptions: common.pickerOptions_earlierThanYesterd11
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: true,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },

      // 控制展示线的多少组件
      ElSelect_items: {
        value: "",
        style: {},
        multiple: true,
        multipleLimit: 6,
        event: {
          change: this.ElSelect_items_change_out
        }
      },
      ElOption_items: {
        options_in: [],
        key: "tree_id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },

      firstLoad: true,
      contentText: {
        totalTitle: $T("总用能成本（元）")
      },
      cardData: {},
      lineData: [],
      dimCardsData: [],
      nodeCardsData: {},
      chartChecks: {
        maxAndMin: false,
        average: false,
        showPoint: false,
        chain: false
      }
    };
  },
  watch: {
    "CetDatePicker_time.val"(value) {
      console.log("CetDatePicker_time.val", value);
      if (this.$moment(value).year() === this.$moment().year()) {
        this.CetButton_next.disable_in = true;
      } else {
        this.CetButton_next.disable_in = false;
      }
      if (!this.firstLoad) {
        this.getRightSectionData();
      }
    }
  },

  methods: {
    formatterTooltip(params) {
      let str = `<span>${params[0]?.axisValue}</span><br/>`;
      params.forEach(item => {
        str += `${item.marker}<span>${item.seriesName}(${this?.unitName})：</span><span>${item.value}</span><br/>`;
      });
      return str;
    },
    CetGiantTree_1_currentNode_out(val) {
      this.currentNode = this._.cloneDeep(val);
      if (val) {
        this.getRightSectionData();
      }
    },
    //region 能源类型选择组件out方法
    ElSelect_type_change_out(val) {
      this.changeContext();
      if (!this.firstLoad) {
        this.getRightSectionData();
      }
    },

    //region 统计维度out方法
    ElSelect_dimensionality_change_out(val) {
      if (!this.firstLoad) {
        this.getRightSectionData();
      }
      if (val === 0) {
        this.chartsTitle = $T("区域维度变化趋势");
      } else if (val === 1) {
        this.chartsTitle = $T("分项用能趋势分析");
      } else if (val === 2) {
        this.chartsTitle = $T("分类用能趋势分析");
      } else {
        this.chartsTitle = "--";
      }
    },
    CetButton_prv_statusTrigger_out(val) {
      let date = this.$moment(this.CetDatePicker_time.val);
      this.CetDatePicker_time.val = date.subtract(1, "year")._d;
    },
    CetButton_next_statusTrigger_out(val) {
      let date = this.$moment(this.CetDatePicker_time.val);
      this.CetDatePicker_time.val = date.add(1, "year")._d;
    },

    //region 选择线组件
    // items输出,方法名要带_out后缀
    ElSelect_items_change_out(val) {
      this.changLineChartData();
    },

    getNodeData() {
      const vm = this;
      const [startTime, endTime] = this.timeRange;
      const nodes = [
        {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          name: this.currentNode.name
        }
      ];
      const energyType = this.ElSelect_type.value;
      const data = {
        startTime,
        endTime,
        nodes,
        cycle: 17,
        energyType,
        queryType: 1,
        costKpiType: 2,
        projectId: this.projectId
      };
      costanalysisAPI.getCostByNode(data).then(result => {
        vm.cardData = result;
        this.unitName = this._.get(result, "[0].unitName", $T("元"));
        vm.setNodeCardsData();
      });
      if (this.currentNode.children) {
        data.nodes = this.currentNode.children.map(item => {
          return {
            id: item.id,
            modelLabel: item.modelLabel,
            name: item.name
          };
        });
      } else {
        data.nodes = [];
      }
      if (data.nodes.length === 0) {
        vm.lineData = [];
        this.ElOption_items.options_in = [];
        this.ElSelect_items.value = [];
        vm.CetChart_1.config.options.series = [];
        return;
      }
      costanalysisAPI.getChildCostByNode(data).then(result => {
        vm.lineData = result;
        vm.initOptions(result);
      });
    },
    getDimData(tags) {
      const vm = this;
      const [startTime, endTime] = this.timeRange;
      const node = {
        id: this.currentNode.id,
        modelLabel: this.currentNode.modelLabel,
        name: this.currentNode.name
      };
      const energyTypeNode = this.ElSelect_type.value;
      const data = {
        startTime,
        endTime,
        node,
        cycle: 17,
        energyType: energyTypeNode,
        queryType: 1,
        tags,
        queryTotal: true,
        projectId: this.projectId
      };

      costanalysisAPI.getCostByDimTagId(data).then(result => {
        vm.cardData = result;
        vm.setDimCardsData();
      });
      data.queryTotal = false;

      costanalysisAPI.getChildCostByDimTagId(data).then(result => {
        vm.lineData = result;
        vm.initOptions(result);
      });
    },
    getRightSectionData() {
      const dimNodeId = this.ElSelect_dimensionality.value;
      // 区域维度按照node查询数据
      if (dimNodeId === 0) {
        this.getNodeData();
      } else {
        costanalysisAPI.getPropertysByDimId(dimNodeId).then(result => {
          const tags = result.map(item => {
            return {
              name: item.name,
              tag: item.id
            };
          });
          if (tags.length === 0) {
            this.dimCardsData = [];
            this.lineData = [];
            this.CetChart_1.config.options.series = [];
            return;
          }
          this.getDimData(tags);
        });
      }
    },
    getTreeData() {
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_1.selectNode = null;
      this.currentNode = null;
      const data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.costanalysisTree,
        treeReturnEnable: true
      };
      costanalysisAPI.getNodeTree(data).then(res => {
        this.CetGiantTree_1.inputData_in = res;
        this.CetGiantTree_1.selectNode = this.CetGiantTree_1.inputData_in[0];
        this.firstLoad = false;
      });
    },
    changeContext() {
      const val = this.ElSelect_type.value;
      if (val === 2) {
        this.contentText = {
          totalTitle: `${$T("总用能成本")}（${this.unitName}）`
        };
      } else if (val === 3) {
        this.contentText = {
          totalTitle: `${$T("总用水成本")}（${this.unitName}）`
        };
      } else if (val === 15) {
        this.contentText = {
          totalTitle: `${$T("总用气成本")}（${this.unitName}）`
        };
      } else {
        const name = this.ElOption_type.options_in.find(
          item => item.id === this.ElSelect_type.value
        )?.name;
        this.contentText = {
          totalTitle: `${$T("总用{0}成本", name)}（${this.unitName}）`
        };
      }
    },
    setNodeCardsData() {
      this.changeContext();
      const cardData = this.cardData;
      const data = cardData[0].data[0];
      this.nodeCardsData = {
        total: data.value ? (data.value * 1).toFixed(2) + "" : "--",
        chainPercent: setPercent(data.chain),
        yearOnYearPercent: setPercent(data.yearOnYear),
        unitData: {
          value1: Array.isArray(data.costKpi)
            ? data.costKpi
                .sort((a, b) => b.key - a.key)
                .map(item => {
                  if (item.value) {
                    return item.value.toFixed2(2);
                  } else {
                    return "--";
                  }
                })
            : ["--", "--"],
          value2: ["--", "--"]
        }
      };
    },
    setDimCardsData() {
      const cardData = this.cardData;
      this.dimCardsData = cardData
        .map(item => {
          const data = item.data ? item.data[0] : {};
          return {
            value: setValue(data.value),
            chainPercent: setPercent(data.chain),
            yearOnYearPercent: setPercent(data.yearOnYear),
            title: item.dimTagName,
            id: item.dimTagId
          };
        })
        .sort((a, b) => {
          if (a.title) {
            return 1;
          } else {
            return -1;
          }
        });
    },
    checksChange() {
      this.changLineChartData();
    },
    getFullseries(data) {
      const myData = data.filter(item => item.objectName || item.dimTagName);
      let seriesData = myData.map(item => {
        return {
          name:
            this.ElSelect_dimensionality.value === 0
              ? item.objectName
              : item.dimTagName,
          type: "line",
          data: Array.isArray(item.data)
            ? item.data.map(subitem => setValue(subitem.value))
            : [],
          smooth: true
        };
      });
      const selectedLastYear = this.$moment(this.CetDatePicker_time.val)
        .subtract(1, "year")
        .year();
      seriesData = seriesData.concat(
        myData.map(item => {
          const name =
            this.ElSelect_dimensionality.value === 0
              ? item.objectName
              : item.dimTagName;
          return {
            name: name + `(${selectedLastYear})`,
            type: "line",
            data: Array.isArray(item.data)
              ? item.data.map(subitem => setValue(subitem.yearOnYearCost))
              : [],
            smooth: true
          };
        })
      );
      return seriesData;
    },

    initOptions(data) {
      let options = this._.clone(initLineData);
      let series = this.getFullseries(this.lineData);
      options.yAxis[0].name = `${$T("成本")}（${this._.get(
        this.lineData,
        "[0].unitName",
        $T("元")
      )}）`;
      const selectedLastYear = this.$moment(this.CetDatePicker_time.val)
        .subtract(1, "year")
        .year();
      if (!this.chartChecks.chain) {
        series = series.filter(
          item => item.name && !item.name.includes(selectedLastYear)
        );
      }
      if (this.chartChecks.maxAndMin) {
        series = series.map(item => {
          return {
            ...item,
            markPoint: {
              data: [
                { type: "max", name: $T("最大值") },
                { type: "min", name: $T("最小值") }
              ]
            }
          };
        });
      }
      if (this.chartChecks.average) {
        series = series.map(item => {
          return {
            ...item,
            markLine: {
              data: [{ type: "average", name: $T("平均值") }]
            }
          };
        });
      }

      series = series.map(item => {
        return {
          ...item,
          showSymbol: this.chartChecks.showPoint
        };
      });
      // 处理x轴坐标
      options.xAxis.data = this.lineData[0].data.map(item => {
        let xAxisName = this.$moment(item.time).format("MM");
        xAxisName = xAxisName + $T("月");
        return xAxisName;
      });

      options.legend.data = series.map(item => item.name);
      options.series = series;
      this.CetChart_1.config.options = options;
      this.setItemsSelectData(options.legend.data);
    },

    changLineChartData() {
      const selectedLastYear = this.$moment(this.CetDatePicker_time.val)
        .subtract(1, "year")
        .year();
      let series = this.getFullseries(this.lineData);

      if (!this.chartChecks.chain) {
        series = series.filter(item => !item.name.includes(selectedLastYear));
      }

      if (this.ElSelect_dimensionality.value === 0) {
        const selectedNodes = this.ElOption_items.options_in.filter(item =>
          this.ElSelect_items.value.includes(item.id)
        );
        series = series.filter(item => {
          return selectedNodes.some(snode => {
            return (
              snode.text === item.name ||
              snode.text + `(${selectedLastYear})` === item.name
            );
          });
        });
      }
      if (this.chartChecks.maxAndMin) {
        series = series.map(item => {
          return {
            ...item,
            markPoint: {
              data: [
                { type: "max", name: $T("最大值") },
                { type: "min", name: $T("最小值") }
              ]
            }
          };
        });
      }

      if (this.chartChecks.average) {
        series = series.map(item => {
          return {
            ...item,
            markLine: {
              data: [{ type: "average", name: $T("平均值") }]
            }
          };
        });
      }

      series = series.map(item => {
        return {
          ...item,
          showSymbol: this.chartChecks.showPoint
        };
      });

      let options = this._.clone(initLineData);
      options.yAxis[0].name = `成本（${this._.get(
        this.lineData,
        "[0].unitName",
        $T("元")
      )}）`;
      options.legend.data = series.map(item => item.name);
      options.series = series;
      this.CetChart_1.config.options = options;
    },

    setItemsSelectData(items) {
      const currentNode = this.currentNode;
      if (currentNode.children && Array.isArray(currentNode.children)) {
        this.ElOption_items.options_in = currentNode.children.map(item => ({
          id: item.id,
          tree_id: item.tree_id,
          text: item.name
        }));
        this.ElSelect_items.value = this.ElOption_items.options_in
          .slice(0, 6)
          .map(item => item.id);
        this.ElSelect_items_change_out(this.ElSelect_items.value);
      } else {
        this.ElOption_items.options_in = [];
        this.ElSelect_items.value = [];
      }
    },
    getProjectEnergy() {
      costanalysisAPI.getProjectEnergy(this.projectId).then(result => {
        let options = [];
        result.forEach(item => {
          if (![18, 22].includes(item.energytype)) {
            options.push({
              id: item.energytype,
              name: item.name
            });
          }
        });
        this.ElOption_type.options_in = options;
      });
    }
  },

  created: function () {
    this.CetChart_1.config.options.tooltip.formatter = this.formatterTooltip;
    this.getTreeData();
    this.getProjectEnergy();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  :deep(.el-checkbox) {
    @include margin_right(J1);
  }
  :deep(.el-checkbox .el-checkbox__label) {
    @include padding_left(J);
  }
}
.headcolor {
  @include background_color("BG1");
}
.footcolor {
  @include background_color("BG1");
}
.fontcolor {
  // @include font-color("Sta4");
}
.trends__header {
  display: flex;
  align-items: center;
}

.header__item {
  display: flex;
  align-items: center;
}

.container__title {
  // @include font_size("H2");
  // font-weight: 500;
  margin: 0;
  @include margin_bottom(J3);
}

.container__empty {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  @include font_size("H3");
}
.filter-tree {
  height: calc(100% - 10px);
  overflow: auto;
}
.custom-select-tag :deep(.el-select__tags-text) {
  float: left;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
