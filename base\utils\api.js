import { HttpBase } from "@omega/http";
import _ from "lodash";
// import msg from "@/base/components/msg";

class Base {
  // 带全屏longing
  httping = new HttpBase({
    loading: true,
    json: true
  });

  // 不带全屏loading
  http = new HttpBase({ loading: true });

  // 基础httpbase 仅仅是
  // 携带全局设置的 header 信息
  httpBase = new HttpBase();

  // 不带全屏loading,并且屏蔽错误信息
  httpNoNotice = new HttpBase({
    json: true,
    loading: true
  });

  _ = _;

  // msg = msg;
}

class Api extends Base {
  constructor(options) {
    super();

    Object.keys(options).forEach(key => {
      const value = options[key];
      if (_.isFunction(value)) {
        this[key] = value.bind(this);
      } else {
        this[key] = options[key];
      }
    });
  }
}

export default function api(options) {
  return new Api(options);
}
