import fetch from "eem-utils/fetch";
import { getCustomParameter } from "./common/common.js";
let version = "v1";

function processRequest(data) {
  // 对 data 进行任意转换处理
  return data;
}

function processResponse(response) {
  // 对 response 进行任意转换处理, response结构
  //   {
  //     // `data` 由服务器提供的响应
  //     data: {},

  //     // `status` 来自服务器响应的 HTTP 状态码
  //     status: 200,

  //     // `statusText` 来自服务器响应的 HTTP 状态信息
  //     statusText: 'OK',

  //     // `headers` 服务器响应的头
  //     headers: {},

  //     // `config` 是为请求提供的配置信息
  //     config: {}
  //   }
  return response;
}

export function getNodeTreeSimple(data, hideNotice, params) {
  return fetch({
    url: `/eem-service/${version}/node/nodeTree/simple`,
    method: "POST",
    headers: { hideNotice: hideNotice },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data),
    params: params
  });
}
export function getNodeTree(data, hideNotice) {
  return fetch({
    url: `/eem-service/${version}/node/nodeTree`,
    method: "POST",
    headers: { hideNotice: hideNotice },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
// pq的获取节点树
export function getPqNodeTree(data, projectId, showLeafNode) {
  return fetch({
    url: `/eem-service/${version}/pq/nodeTree?projectId=${projectId}&showLeafNode=${showLeafNode}`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
// 获取其他用户的节点树
export function getUserNodeTree(data, hideNotice) {
  var userId;
  var expressions = _.get(data, "rootCondition.filter.expressions");
  if (!_.isEmpty(expressions)) {
    userId = getCustomParameter(expressions, "userId");
  }
  return fetch({
    url: `/eem-service/${version}/node/nodeTree?userId=${userId}`,
    method: "POST",
    headers: { hideNotice: hideNotice },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
