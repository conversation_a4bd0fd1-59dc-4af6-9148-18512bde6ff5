<template>
  <div class="page fullfilled eem-common">
    <el-container class="fullheight">
      <el-aside width="315px" class="eem-aside flex-column">
        <customElSelect
          :prefix_in="$T('选择维度')"
          v-model="ElSelect_dimension.value"
          v-bind="ElSelect_dimension"
          v-on="ElSelect_dimension.event"
        >
          <ElOption
            v-for="item in ElOption_dimension.options_in"
            :key="item[ElOption_dimension.key]"
            :label="item[ElOption_dimension.label]"
            :value="item[ElOption_dimension.value]"
            :disabled="item[ElOption_dimension.disabled]"
          ></ElOption>
        </customElSelect>
        <customElSelect
          v-if="ElSelect_dimension.value === 2"
          class="mtJ1"
          :prefix_in="$T('关联对象')"
          v-model="ElSelect_relationObj.value"
          v-bind="ElSelect_relationObj"
          v-on="ElSelect_relationObj.event"
        >
          <ElOption
            v-for="item in ElOption_relationObj.options_in"
            :key="item[ElOption_relationObj.key]"
            :label="item[ElOption_relationObj.label]"
            :value="item[ElOption_relationObj.value]"
            :disabled="item[ElOption_relationObj.disabled]"
          ></ElOption>
        </customElSelect>
        <customElSelect
          multiple
          collapse-tags
          v-if="ElSelect_dimension.value === 3"
          class="mtJ1 custom-select-tag"
          :prefix_in="$T('设备类型选择')"
          v-model="ElSelect_deviceType.value"
          v-bind="ElSelect_deviceType"
          v-on="ElSelect_deviceType.event"
        >
          <ElOption
            v-for="item in ElOption_deviceType.options_in"
            :key="item[ElOption_deviceType.key]"
            :label="item[ElOption_deviceType.label]"
            :value="item[ElOption_deviceType.value]"
            :disabled="item[ElOption_deviceType.disabled]"
          ></ElOption>
        </customElSelect>
        <customElSelect
          class="mtJ1"
          :prefix_in="
            ElSelect_dimension.value === 2
              ? ElSelect_relationObj.value === 'manage'
                ? $T('关联管理层级状态')
                : $T('关联设备状态')
              : $T('关联管网状态')
          "
          v-model="ElSelect_status.value"
          v-bind="ElSelect_status"
          v-on="ElSelect_status.event"
        >
          <ElOption
            v-for="item in ElOption_status.options_in"
            :key="item[ElOption_status.key]"
            :label="item[ElOption_status.label]"
            :value="item[ElOption_status.value]"
            :disabled="item[ElOption_status.disabled]"
          ></ElOption>
        </customElSelect>
        <CetGiantTree
          class="mtJ1"
          style="height: 0; flex: 1"
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
        ></CetGiantTree>
        <CetButton
          class="fr mtJ2"
          v-bind="CetButton_batch"
          v-on="CetButton_batch.event"
        ></CetButton>
      </el-aside>
      <div class="flex-auto flex-column mlJ3">
        <div
          v-show="ElSelect_dimension.value === 1"
          class="header clearfix eem-container"
        >
          <customElSelect
            class="fl mrJ1"
            :prefix_in="$T('能源类型')"
            v-model="ElSelect_1.value"
            v-bind="ElSelect_1"
            v-on="ElSelect_1.event"
          >
            <ElOption
              v-for="item in ElOption_1.options_in"
              :key="item[ElOption_1.key]"
              :label="item[ElOption_1.label]"
              :value="item[ElOption_1.value]"
              :disabled="item[ElOption_1.disabled]"
            ></ElOption>
          </customElSelect>
          <customElSelect
            class="fl mrJ1"
            :prefix_in="$T('关联设备情况')"
            v-model="ElSelect_3.value"
            v-bind="ElSelect_3"
            v-on="ElSelect_3.event"
          >
            <ElOption
              v-for="item in ElOption_3.options_in"
              :key="item[ElOption_3.key]"
              :label="item[ElOption_3.label]"
              :value="item[ElOption_3.value]"
              :disabled="item[ElOption_3.disabled]"
            ></ElOption>
          </customElSelect>
          <CetButton
            class="fr mlJ2"
            v-bind="CetButton_import"
            v-on="CetButton_import.event"
          ></CetButton>
          <CetButton
            class="fr mlJ2"
            v-bind="CetButton_clear"
            v-on="CetButton_clear.event"
          ></CetButton>
          <el-tooltip
            effect="light"
            :content="
              $T(
                '无效关联：在一段关联关系中，其中一个关联项被删除了，但是关联关系仍然存在'
              )
            "
          >
            <i class="el-icon-question fr" style="line-height: 32px"></i>
          </el-tooltip>
        </div>
        <div
          class="content flex-auto"
          :class="{ mtJ3: ElSelect_dimension.value === 1 }"
        >
          <div class="flex-column eem-container">
            <div class="clearfix fullwidth">
              <div class="fl cardTitle common-title-H2">
                {{ getTitle }}
              </div>
              <CetButton
                class="fr mlJ1"
                v-bind="CetButton_add"
                v-on="CetButton_add.event"
              ></CetButton>
              <CetButton
                v-show="ElSelect_dimension.value === 1"
                class="fr mlJ1"
                :disable_in="CetTable_1.data.length === 0"
                v-bind="CetButton_shareRate"
                v-on="CetButton_shareRate.event"
              ></CetButton>
              <CetButton
                v-show="ElSelect_dimension.value === 1"
                class="fr mlJ1"
                v-bind="CetButton_importRelation"
                v-on="CetButton_importRelation.event"
              ></CetButton>
              <CetButton
                v-show="ElSelect_dimension.value !== 1"
                class="fr mlJ2"
                v-bind="CetButton_clear"
                v-on="CetButton_clear.event"
              ></CetButton>
              <el-tooltip
                v-show="ElSelect_dimension.value !== 1"
                effect="light"
                :content="
                  $T(
                    '无效关联：在一段关联关系中，其中一个关联项被删除了，但是关联关系仍然存在'
                  )
                "
              >
                <i class="el-icon-question fr" style="line-height: 32px"></i>
              </el-tooltip>
            </div>
            <CetTable
              class="mtJ3 flex-auto"
              :class="currentTheme"
              :data.sync="CetTable_1.data"
              :dynamicInput.sync="CetTable_1.dynamicInput"
              v-bind="CetTable_1"
              v-on="CetTable_1.event"
            >
              <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
              <ElTableColumn
                v-bind="ElTableColumn_childDevice"
                v-if="showChildDeviceCol()"
              ></ElTableColumn>
              <template v-for="item in Columns_1">
                <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
              </template>
              <ElTableColumn
                :label="$T('操作')"
                :width="setHandleWidth"
                header-align="left"
                align="left"
                fixed="right"
              >
                <template slot-scope="scope">
                  <div @click.stop v-if="ElSelect_dimension.value === 1">
                    <el-dropdown class="dropdown" @command="handleCommand">
                      <span class="el-icon-more"></span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item
                          class="delete"
                          v-if="scope.row.supplyTo && scope.row.supplyTo.id"
                          :command="{ type: 'deletePipe', scope, tableType: 1 }"
                        >
                          {{ $T("删除管网关联") }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="
                            scope.row.supplyTo && scope.row.supplyTo.objectid
                          "
                          :command="{ type: 'addGather', scope, tableType: 1 }"
                        >
                          {{ $T("关联设备") }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          class="delete"
                          v-if="
                            scope.row.measuredBy &&
                            scope.row.measuredBy.measuredby
                          "
                          :command="{
                            type: 'deleteGather',
                            scope,
                            tableType: 1
                          }"
                        >
                          {{ $T("删除设备关联") }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          :command="{ type: 'energydata', scope, tableType: 1 }"
                          v-if="checkEnergydataPermission"
                        >
                          {{ $T("能耗数据") }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          :command="{ type: 'trendcurve', scope, tableType: 1 }"
                          v-permission="'cloud_trendcurve'"
                        >
                          {{ $T("趋势曲线") }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                  <div v-else>
                    <span
                      class="delete"
                      v-show="
                        scope.row.supplyTo?.id &&
                        ElSelect_dimension.value === 2 &&
                        ElSelect_relationObj.value === 'manage'
                      "
                      @click="deleteManageRelation(scope)"
                    >
                      {{ $T("删除管理层级关联") }}
                    </span>
                    <span
                      class="delete"
                      v-show="
                        ElSelect_dimension.value === 2 &&
                        ElSelect_relationObj.value === 'device' &&
                        scope.row.measuredBy?.monitoredid
                      "
                      @click="
                        deleteGatherRelation1(
                          scope,
                          1,
                          scope.row.measuredBy?.name,
                          scope.row.measuredBy?.deviceName
                        )
                      "
                    >
                      {{ $T("删除设备关联") }}
                    </span>
                    <span
                      class="handle"
                      v-show="
                        ElSelect_dimension.value === 3 &&
                        scope.row.measuredBy?.monitoredid
                      "
                      @click="AddManageRelation(scope)"
                    >
                      {{ $T("关联管理层级") }}
                    </span>
                    <span
                      class="delete mlJ2"
                      v-show="
                        ElSelect_dimension.value === 3 &&
                        scope.row.measuredBy?.id &&
                        !scope.row.supplyTo?.id
                      "
                      @click="
                        deletePipeRelation1(
                          scope,
                          scope.row.measuredBy.modelLabel,
                          scope.row.measuredBy?.name,
                          scope.row.measuredBy?.deviceName
                        )
                      "
                    >
                      {{ $T("删除管网关联") }}
                    </span>
                    <span
                      class="delete mlJ2"
                      v-show="
                        ElSelect_dimension.value === 3 &&
                        scope.row.supplyTo?.id &&
                        !scope.row.measuredBy?.id
                      "
                      @click="deleteManageRelation(scope)"
                    >
                      {{ $T("删除管理层级关联") }}
                    </span>

                    <el-dropdown
                      :class="{ mlJ2: scope.row.measuredBy?.monitoredid }"
                      @command="handleDeleteCommand"
                      v-if="
                        ElSelect_dimension.value === 3 &&
                        scope.row.measuredBy?.id &&
                        scope.row.supplyTo?.id
                      "
                    >
                      <span class="el-dropdown-link delete">
                        {{ $T("删除关联关系") }}
                        <i class="el-icon-arrow-down el-icon--right"></i>
                      </span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item
                          :command="{ type: 'deletePipeline', scope }"
                          v-if="scope.row.measuredBy?.id"
                        >
                          {{ $T("管网关联") }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          :command="{ type: 'deleteManage', scope }"
                          v-if="scope.row.supplyTo?.id"
                        >
                          {{ $T("管理层级关联") }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                </template>
              </ElTableColumn>
            </CetTable>
            <div class="tableFooter" v-if="ElSelect_dimension.value === 2">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="currentPage"
                :page-sizes="pageSizes"
                :page-size.sync="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalCount"
              ></el-pagination>
            </div>
          </div>
          <div
            class="mtJ3 flex-column eem-container"
            v-show="
              currentNode &&
              currentNode.children &&
              currentNode.children.length &&
              ElSelect_dimension.value === 1
            "
          >
            <div class="clearfix fullwidth">
              <div class="fl cardTitle common-title-H3 mrJ3">
                {{ $T("子层级管网关联关系") }}
              </div>
            </div>
            <div class="clearfix fullwidth">
              <div class="basic-box mrJ1 fl" v-if="false">
                <span class="basic-box-label">{{ $T("层级选择") }}</span>
                <ElSelect
                  v-model="ElSelect_4.value"
                  v-bind="ElSelect_4"
                  v-on="ElSelect_4.event"
                >
                  <ElOption
                    v-for="item in ElOption_4.options_in"
                    :key="item[ElOption_4.key]"
                    :label="item[ElOption_4.label]"
                    :value="item[ElOption_4.value]"
                    :disabled="item[ElOption_4.disabled]"
                  ></ElOption>
                </ElSelect>
              </div>
              <CetButton
                class="fr mlJ1"
                :disable_in="CetTable_2.data.length === 0"
                v-bind="CetButton_export"
                v-on="CetButton_export.event"
              ></CetButton>
            </div>
            <CetTable
              class="mtJ3 flex-auto"
              :class="currentTheme"
              :data.sync="CetTable_2.data"
              :dynamicInput.sync="CetTable_2.dynamicInput"
              v-bind="CetTable_2"
              v-on="CetTable_2.event"
            >
              <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
              <ElTableColumn v-bind="ElTableColumn_1"></ElTableColumn>
              <template v-for="item in Columns_1">
                <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
              </template>
              <ElTableColumn
                :label="$T('操作')"
                :width="en ? 270 : 150"
                header-align="left"
                fixed="right"
                align="left"
              >
                <template slot-scope="scope">
                  <div @click.stop>
                    <span
                      class="handle fl mrJ4"
                      @click.stop="addCurrentPipe(scope)"
                    >
                      {{ $T("新增管网设备") }}
                    </span>
                    <el-dropdown class="dropdown fl" @command="handleCommand">
                      <span class="el-icon-more"></span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item
                          v-if="scope.row.supplyTo && scope.row.supplyTo.id"
                          class="delete"
                          :command="{ type: 'deletePipe', scope, tableType: 2 }"
                        >
                          {{ $T("删除管网关联") }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="
                            scope.row.supplyTo && scope.row.supplyTo.objectid
                          "
                          :command="{ type: 'addGather', scope, tableType: 2 }"
                        >
                          {{ $T("关联设备") }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          class="delete"
                          v-if="
                            scope.row.measuredBy &&
                            scope.row.measuredBy.measuredby
                          "
                          :command="{
                            type: 'deleteGather',
                            scope,
                            tableType: 2
                          }"
                        >
                          {{ $T("删除设备关联") }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          :command="{ type: 'energydata', scope, tableType: 2 }"
                          v-if="checkEnergydataPermission"
                        >
                          {{ $T("能耗数据") }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          :command="{ type: 'trendcurve', scope, tableType: 2 }"
                          v-permission="'cloud_trendcurve'"
                        >
                          {{ $T("趋势曲线") }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                </template>
              </ElTableColumn>
            </CetTable>
          </div>
        </div>
      </div>
    </el-container>
    <UploadDialog v-bind="uploadDialog" v-on="uploadDialog.event">
      <div slot="search" class="bg1 brC1 pJ3 plJ4 prJ4 mbJ1">
        <el-row :gutter="$J3">
          <el-col :span="8">
            <div class="mbJ1">{{ $T("能源类型") }}</div>
            <ElSelect
              v-model="ElSelect_5.value"
              v-bind="ElSelect_5"
              v-on="ElSelect_5.event"
            >
              <ElOption
                v-for="item in ElOption_5.options_in"
                :key="item[ElOption_5.key]"
                :label="item[ElOption_5.label]"
                :value="item[ElOption_5.value]"
                :disabled="item[ElOption_5.disabled]"
              ></ElOption>
            </ElSelect>
          </el-col>
        </el-row>
      </div>
    </UploadDialog>
    <UploadDialog
      v-bind="uploadDialogRelation"
      v-on="uploadDialogRelation.event"
    ></UploadDialog>
    <ShareRate
      :visibleTrigger_in="shareRate.visibleTrigger_in"
      :closeTrigger_in="shareRate.closeTrigger_in"
      :queryId_in="shareRate.queryId_in"
      :inputData_in="shareRate.inputData_in"
      :roomDeviceNodes="shareRate.energySupplyToDataAll"
      :roomDeviceList="roomDeviceList"
    />
    <AddPipe
      :energyType_in="ElSelect_1.value"
      :visibleTrigger_in="addPipe.visibleTrigger_in"
      :closeTrigger_in="addPipe.closeTrigger_in"
      :inputData_in="addPipe.inputData_in"
      :roomDeviceList="roomDeviceList"
      :currentNode="addPipe.currentNode"
      :deviceList="addPipe.energySupplyToDataAll"
      :showTagList="true"
      @updata_out="addPipe_updata_out"
    />
    <addPipelineDevice
      :visibleTrigger_in="addPipelineDevice.visibleTrigger_in"
      :closeTrigger_in="addPipelineDevice.closeTrigger_in"
      :inputData_in="addPipelineDevice.inputData_in"
      :roomDeviceList="roomDeviceList"
      :currentNode="addPipelineDevice.currentNode"
      :deviceList="addPipelineDevice.energySupplyToDataAll"
      @updata_out="addPipelineDevice_updata_out"
    />
    <AddGather
      :visibleTrigger_in="addGather.visibleTrigger_in"
      :closeTrigger_in="addGather.closeTrigger_in"
      :inputData_in="addGather.inputData_in"
      :showTagList="true"
      :roomDeviceList="roomDeviceList"
      :currentNode="addGather.currentNode"
      :deviceList="addGather.deviceList"
      @updata_out="addGather_updata_out"
    />
    <AddManage
      :visibleTrigger_in="AddManage.visibleTrigger_in"
      :closeTrigger_in="AddManage.closeTrigger_in"
      :inputData_in="AddManage.inputData_in"
      :currentNode="AddManage.currentNode"
      :energySupplyToDataAll="AddManage.energySupplyToDataAll"
      @updata_out="AddManage_updata_out"
    />
  </div>
</template>

<script>
import common from "eem-utils/common";
import TREE_PARAMS from "@/store/treeParams.js";
import customApi from "@/api/custom";
import UploadDialog from "eem-components/uploadDialog";
import ELECTRICAL_DEVICE from "@/store/electricaldevice.js";
import ShareRate from "../pipeRelation/shareRate.vue";
import AddPipe from "../pipeRelation/add.vue";
import addPipelineDevice from "./addPipelineDevice.vue";
import AddGather from "../gatherRelation/add.vue";
import AddManage from "./add.vue";
export default {
  components: {
    UploadDialog,
    ShareRate,
    AddPipe,
    addPipelineDevice,
    AddGather,
    AddManage
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    projectInfo() {
      var vm = this;
      return vm.$store.state.projectInfo;
    },
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    },
    checkEnergydataPermission() {
      return (
        this.$checkPermission("cloud_energydata") ||
        this.$checkPermission("cloud_energyQueryAndAnalysis")
      );
    },
    totalEnergyType() {
      return this.$store.state.systemCfg.totalEnergyType;
    },
    totalEnergyTypeCO2() {
      return this.$store.state.systemCfg.totalEnergyTypeCO2;
    },
    totalEnergyTypeC() {
      return this.$store.state.systemCfg.totalEnergyTypeC;
    },
    currentTheme() {
      return localStorage.getItem("omega_theme") || "light";
    },
    getTitle() {
      const title = {
        1: $T("管网关联关系"),
        2:
          this.ElSelect_relationObj.value === "manage"
            ? $T("管网层级与管理层级关联关系")
            : $T("管网层级与设备关联关系"),
        3: $T("设备关联关系")
      };
      return $T(title[this.ElSelect_dimension.value]);
    },
    setHandleWidth() {
      if (this.ElSelect_dimension.value === 1) {
        return this.en ? 80 : 50;
      } else if (this.ElSelect_dimension.value === 2) {
        return 150;
      } else {
        return 230;
      }
    }
  },
  data(vm) {
    const enLanguage = window.localStorage.getItem("omega_language") === "en";
    const roomDeviceList = [
      {
        filter: null,
        modelLabel: "linesegment",
        props: []
      }
    ];
    ELECTRICAL_DEVICE.forEach(item => {
      var obj = {
        filter: null,
        modelLabel: item.value,
        props: []
      };
      if (item.value === "linesegmentwithswitch") {
        obj.depth = 1;
      }
      roomDeviceList.push(obj);
    });
    return {
      totalCount: 0,
      currentPage: 1,
      pageSizes: [10, 20, 50, 100],
      pageSize: 10,
      // 配电室设备模型
      roomDeviceList: roomDeviceList,
      currentNode: null,
      checkedNodes: [],
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          view: {
            nodeClasses: (treeId, treeNode) => {
              return this.ElSelect_dimension.value !== 3 &&
                this.ElSelect_status.value !== 0 &&
                treeNode.childSelectState !== 1
                ? { add: ["forbiddenSelect"] }
                : "";
            }
          },
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              name: "name"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out, //选中单行输出
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out
        }
      },
      ElSelect_1: {
        value: null,
        style: {
          width: enLanguage ? "250px" : "200px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_dimension: {
        value: 1,
        style: {},
        event: {
          change: this.ElSelect_dimension_change_out
        }
      },
      ElOption_dimension: {
        options_in: [
          { id: 1, name: $T("管理层级") },
          { id: 2, name: $T("管网层级") },
          { id: 3, name: $T("厂站通道设备") }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_status: {
        value: 0,
        style: {},
        event: {
          change: this.ElSelect_status_change_out
        }
      },
      ElOption_status: {
        options_in: [
          { id: 0, name: $T("全部") },
          { id: 1, name: $T("已关联") },
          { id: 2, name: $T("未关联") }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_relationObj: {
        value: "manage",
        style: {},
        event: {
          change: this.ElSelect_relationObj_change_out
        }
      },
      ElOption_relationObj: {
        options_in: [
          { id: "manage", name: $T("管理层级") },
          { id: "device", name: $T("设备") }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_deviceType: {
        value: [],
        style: {},
        event: {
          change: this.ElSelect_deviceType_change_out
        }
      },
      ElOption_deviceType: {
        options_in: vm.$store.state.enumerations.metertype || [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_3: {
        value: 0,
        style: {
          width: enLanguage ? "300px" : "200px"
        },
        event: {
          change: this.ElSelect_3_change_out
        }
      },
      ElOption_3: {
        options_in: [
          {
            id: 0,
            name: $T("全部")
          },
          {
            id: 1,
            name: $T("已关联")
          },
          {
            id: 2,
            name: $T("未关联")
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_4: {
        value: 1,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_4_change_out
        }
      },
      ElOption_4: {
        options_in: [
          {
            id: 1,
            name: $T("仅子层级")
          },
          {
            id: 2,
            name: $T("查询两层")
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_5: {
        value: null,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_5: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 10,
          layout: "total,sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        highlightCurrentRow: false,
        "row-class-name": this.getInvalidRowClass,
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      },
      CetTable_2: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 10,
          layout: "total,sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        highlightCurrentRow: false,
        "row-class-name": this.getInvalidRowClass,
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        label: "#", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "41" //绝对宽度
      },
      ElTableColumn_1: {
        prop: "supplyTo.supplyToName", // 支持path a[0].b
        label: $T("管理层级"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "160", //该宽度会自适应
        formatter: common.formatTextCol()
      },
      ElTableColumn_childDevice: {
        prop: "measuredBy.deviceName", // 支持path a[0].b
        label: $T("子层级表计设备名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "160", //该宽度会自适应
        formatter: common.formatTextCol()
      },
      Columns_1: [
        {
          prop: "supplyTo.objectName", // 支持path a[0].b
          label: $T("管网名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "200", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "supplyTo.objectid", // 支持path a[0].b
          label: $T("管网ID"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "200", //该宽度会自适应
          formatter: row => {
            if (this.ElSelect_dimension.value === 1) {
              return row.supplyTo?.objectlabel && row.supplyTo?.objectid
                ? `${row.supplyTo?.objectlabel}-${row.supplyTo?.objectid}`
                : "--";
            } else if (this.ElSelect_dimension.value === 2) {
              if (this.ElSelect_relationObj.value === "manage") {
                return row.supplyTo?.objectlabel && row.supplyTo?.objectid
                  ? `${row.supplyTo?.objectlabel}-${row.supplyTo?.objectid}`
                  : "--";
              } else {
                return row.measuredBy?.monitoredlabel &&
                  row.measuredBy?.monitoredid
                  ? `${row.measuredBy?.monitoredlabel}-${row.measuredBy?.monitoredid}`
                  : "--";
              }
            } else if (this.ElSelect_dimension.value === 3) {
              return row.measuredBy?.monitoredlabel &&
                row.measuredBy?.monitoredid
                ? `${row.measuredBy?.monitoredlabel}-${row.measuredBy?.monitoredid}`
                : "--";
            }
          }
        },
        {
          prop: "energyType", // 支持path a[0].b
          label: $T("能源类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "120", //该宽度会自适应
          formatter: (row, column, cellValue) => {
            if (cellValue) {
              let obj = vm.ElOption_1.options_in.find(
                i => i.energytype === cellValue
              );
              return obj ? obj.name : "--";
            }
            return "--";
          }
        },
        {
          prop: "measuredBy.deviceName", // 支持path a[0].b
          label: $T("设备名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "200", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "measuredBy.measuredby", // 支持path a[0].b
          label: $T("设备ID"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "100", //该宽度会自适应
          formatter: (row, column, cellValue) => {
            if (this.ElSelect_dimension.value === 1) {
              if (["", undefined, null, NaN].includes(cellValue)) {
                return "--";
              } else {
                return cellValue;
              }
            } else if (this.ElSelect_dimension.value === 2) {
              if (this.ElSelect_relationObj.value === "manage") {
                return cellValue
                  ? `${row.supplyTo?.supplytolabel}-${row.supplyTo?.supplytoid}`
                  : "--";
              } else {
                return cellValue ? `${row.measuredBy.measuredby}` : "--";
              }
            } else {
              return cellValue
                ? `${row.supplyTo?.supplytolabel}-${row.supplyTo?.supplytoid}`
                : "--";
            }
          }
        }
      ],
      CetButton_batch: {
        visible_in: false,
        disable_in: false,
        title: $T("批量勾选通道节点"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_batch_statusTrigger_out
        }
      },
      CetButton_import: {
        visible_in: true,
        disable_in: true,
        title: $T("导入"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_import_statusTrigger_out
        }
      },
      CetButton_clear: {
        visible_in: true,
        disable_in: false,
        title: "清除无效关联",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_clear_statusTrigger_out
        }
      },
      CetButton_shareRate: {
        visible_in: true,
        // disable_in: true,
        title: $T("分摊系数"),
        plain: true,
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_shareRate_statusTrigger_out
        }
      },
      CetButton_add: {
        visible_in: true,
        disable_in: true,
        title: $T("新增管网设备"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      CetButton_importRelation: {
        visible_in: true,
        disable_in: true,
        title: $T("导入供能关系"),
        plain: true,
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_importRelation_statusTrigger_out
        }
      },
      CetButton_export: {
        visible_in: false,
        // disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      uploadDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        initTrigger_in: new Date().getTime(),
        extensionNameList_in: [".xlsx"],
        hideDownload: false,
        dialogTitle: $T("导入"),
        event: {
          download: this.uploadDialog_download,
          uploadFile: this.uploadDialog_uploadFile
        }
      },
      uploadDialogRelation: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        initTrigger_in: new Date().getTime(),
        extensionNameList_in: [".xls", ".xlsx"],
        hideDownload: false,
        dialogTitle: $T("导入供能关系"),
        event: {
          download: this.uploadDialogRelation_download,
          uploadFile: this.uploadDialogRelation_uploadFile
        }
      },
      shareRate: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        energySupplyToDataAll: [],
        inputData_in: null
      },
      addPipe: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        energySupplyToDataAll: [],
        currentNode: null,
        type: null
      },
      addPipelineDevice: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        energySupplyToDataAll: [],
        currentNode: null,
        type: null
      },
      AddManage: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        energySupplyToDataAll: [],
        currentNode: null,
        type: null
      },
      addGather: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        deviceList: [],
        currentNode: null,
        type: null
      }
    };
  },
  watch: {
    checkedNodes: {
      async handler(val) {
        if (!val?.length) {
          this.CetTable_1.data = [];
          return;
        }
        const queryData = val
            .map(item => item.children.map(it => it.nodeId))
            .flat(Infinity),
          params = {
            status: this.ElSelect_status.value,
            time: new Date().getTime()
          };

        const res = await customApi.getLineRelationReverse(queryData, params);
        this.CetTable_1.data = res?.data || [];
      }
    }
  },
  methods: {
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.getTableData();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getTableData();
    },
    // 设置表格中无效关联行的类名
    getInvalidRowClass({ row }) {
      if (this.ElSelect_dimension.value === 1) {
        if (
          (row.supplyTo?.id && !row.supplyTo?.objectName) ||
          (row.measuredBy?.id && !row.measuredBy?.deviceName)
        )
          return "invalidDataRow";
      } else if (this.ElSelect_dimension.value === 2) {
        if (this.ElSelect_relationObj.value === "manage") {
          if (
            row.supplyTo?.id &&
            !row.supplyTo?.supplyToName &&
            !row.measuredBy
          )
            return "invalidDataRow";
        } else {
          if (
            row.measuredBy?.id &&
            !row.measuredBy?.deviceName &&
            !row.supplyTo
          )
            return "invalidDataRow";
        }
      } else {
        if (
          (row.supplyTo?.id &&
            (!row.supplyTo.objectName || !row.supplyTo.supplyToName)) ||
          (row.measuredBy?.id && !row.measuredBy?.name)
        )
          return "invalidDataRow";
      }
    },
    showChildDeviceCol() {
      return (
        this.ElSelect_dimension.value === 3 &&
        (this.currentNode.nodeType === 269619456 ||
          this.CetButton_batch.title === $T("取消批量勾选通道节点"))
      );
    },
    async getEnergyType() {
      let res = await customApi.getProjectEnergy(this.projectId);
      if (res.code === 0 && res.data && res.data.length > 0) {
        const data = this._.get(res, "data", []).filter(
          item =>
            [
              this.totalEnergyType,
              this.totalEnergyTypeCO2,
              this.totalEnergyTypeC
            ].indexOf(item.energytype) === -1
        );
        this.ElOption_5.options_in = data;
        let options = this._.cloneDeep(data);
        options.unshift({
          energytype: 0,
          name: $T("全部")
        });
        this.ElOption_1.options_in = options;
        this.ElSelect_1.value = options[0].energytype;
      } else {
        this.ElOption_5.options_in = res.data;
        this.ElSelect_5.value = null;
        this.ElOption_1.options_in = [
          {
            energytype: 0,
            name: $T("全部")
          }
        ];
        this.ElSelect_1.value = 0;
      }
    },
    async getTreeData() {
      let apiStr,
        dimension = this.ElSelect_dimension.value;

      let data = {
        rootID: this.projectId,
        rootLabel: "project",
        treeReturnEnable: true
      };
      let params = {
        status: this.ElSelect_status.value,
        time: +new Date()
      };
      if (dimension === 1) {
        this.CetGiantTree_1.setting.data.key.name = "name";
        this.CetGiantTree_1.setting.data.simpleData.idKey = "tree_id";
        apiStr = "connectRelationManagementTree";
        data.subLayerConditions = TREE_PARAMS.pipeRelation;
      } else if (dimension === 2) {
        this.CetGiantTree_1.setting.data.key.name = "name";
        this.CetGiantTree_1.setting.data.simpleData.idKey = "tree_id";
        apiStr = "connectRelationNetworkTree";
        data.subLayerConditions = TREE_PARAMS.gatherRelation;
        params.isMeasureBy = this.ElSelect_relationObj.value === "device";
      } else if (dimension === 3) {
        this.CetGiantTree_1.setting.data.key.name = "text";
        this.CetGiantTree_1.setting.data.simpleData.idKey = "id";
        apiStr = "connectRelationPecCoreMeterTree";
        data = {
          async: false,
          loadDevice: true,
          status: this.ElSelect_status.value,
          tenantId: this.$store.state.userInfo.tenantId,
          type: this.ElSelect_deviceType.value
        };
        params = undefined;
      }

      const res = await customApi[apiStr](data, params);

      if (res.code === 0) {
        this.CetGiantTree_1.inputData_in = res.data;
        if (this.ElSelect_dimension.value === 3) {
          this.CetGiantTree_1.selectNode = this.getFirstStation(
            res?.data || []
          );
          this.currentNode = this._.cloneDeep(
            this.getFirstStation(res?.data || [])
          );
          const batchFlag = this.CetGiantTree_1.setting.check.enable;
          const treeData = this._.cloneDeep(this.CetGiantTree_1.inputData_in);
          batchFlag && this.setTreeLeaf_filterText1Change(treeData);
          this.CetGiantTree_1.inputData_in = treeData;
        } else {
          this.CetGiantTree_1.selectNode = res.data[0];
          this.currentNode = res.data[0];
        }
      }
    },
    // 获取第一个通道节点
    getFirstStation(treeData) {
      const node = treeData.find(
        item => item.children[0]?.nodeType === 269619456
      );
      return node.children[0];
    },

    CetGiantTree_1_currentNode_out(val) {
      if (!val) {
        this.CetButton_add.disable_in = true;
        this.CetButton_import.disable_in = true;
        this.CetButton_importRelation.disable_in = true;
        this.CetButton_clear.disable_in = true;
        return;
      }
      if (this.ElSelect_dimension.value === 3 && val.nodeType === 269615104) {
        this.$message.warning($T("请选择厂站以外的节点！"));
        this.CetButton_add.disable_in = true;
        this.CetButton_clear.disable_in = true;
        this.CetTable_1.data = [];
        return;
      }
      // if (this.ElSelect_dimension.value === 2) {
      //   this.currentNode = this._.cloneDeep(val);
      //   const flag =
      //     ["project", "sectionarea", "building", "floor"].includes(
      //       val.modelLabel
      //     ) ||
      //     (val.modelLabel === "room" && !val.roomtype);
      //   this.CetButton_add.disable_in = flag;
      // }
      if (
        this.ElSelect_dimension.value !== 3 &&
        this.ElSelect_status.value !== 0 &&
        val.childSelectState !== 1
      ) {
        this.CetButton_add.disable_in = true;
        this.CetButton_clear.disable_in = true;
        this.CetButton_import.disable_in = true;
        this.CetButton_importRelation.disable_in = true;
        this.CetTable_1.data = [];
        this.CetTable_2.data = [];
        return;
      }
      this.CetButton_import.disable_in = false;
      this.CetButton_importRelation.disable_in = false;
      this.CetButton_clear.disable_in = false;
      const flag =
        this.ElSelect_dimension.value === 2 &&
        this.ElSelect_relationObj.value === "device" &&
        (["project", "sectionarea", "building", "floor"].includes(
          val.modelLabel
        ) ||
          (val.modelLabel === "room" && !val.roomtype));
      this.CetButton_add.disable_in =
        flag ||
        (this.ElSelect_dimension.value === 3 &&
          (val.nodeType === 269619456 ||
            this.CetButton_batch.title === $T("取消批量勾选通道节点"))) ||
        (this.ElSelect_dimension.value === 2 &&
          this.ElSelect_relationObj.value === "manage" &&
          ["project", "room", "sectionarea", "building", "floor"].includes(
            val.modelLabel
          ));
      this.currentNode = this._.cloneDeep(val);
      if (this.CetGiantTree_1.setting.check.enable) return;
      this.getTableData();
    },
    CetGiantTree_1_checkedNodes_out(val) {
      const sum = val.reduce((prev, cur) => {
        return prev + cur.children?.length;
      }, 0);
      if (sum > 500) {
        this.$alert($T("最多选择500个设备"), "", {
          confirmButtonText: $T("确定")
        });
        this.CetGiantTree_1.checkedNodes = this.checkedNodes;
        return;
      }
      this.checkedNodes = val;
    },
    getTableData() {
      if (
        this.ElSelect_dimension.value !== 3 &&
        this.ElSelect_status.value !== 0 &&
        this.currentNode.childSelectState !== 1
      ) {
        this.CetButton_add.disable_in = true;
        this.CetButton_clear.disable_in = true;
        this.CetButton_import.disable_in = true;
        this.CetButton_importRelation.disable_in = true;
        this.CetTable_1.data = [];
        this.CetTable_2.data = [];
        return;
      }
      this.updateCurrentTable();
      this.updateChildrenTable();
    },
    async updateCurrentTable() {
      const vm = this;
      if (!vm.currentNode) {
        vm.CetTable_1.data = [];
        return;
      }
      let apiStr,
        queryData,
        params,
        dimension = this.ElSelect_dimension.value;
      if (dimension === 1) {
        if (!vm.currentNode.id || !vm.currentNode.modelLabel) {
          vm.CetTable_1.data = [];
          return;
        }
        apiStr = "relationshipGetLineRelation";
        queryData = [
          {
            id: vm.currentNode.id,
            modelLabel: vm.currentNode.modelLabel,
            name: vm.currentNode.name
          }
        ];
        params = {
          energyType: vm.ElSelect_1.value,
          status: vm.ElSelect_3.value,
          time: new Date().getTime()
        };
      } else if (dimension === 2) {
        if (!vm.currentNode.id || !vm.currentNode.modelLabel) {
          vm.CetTable_1.data = [];
          return;
        }
        apiStr =
          this.ElSelect_relationObj.value === "device"
            ? "getMeasureByByNodes"
            : "getLineRelationByNodes";
        params =
          this.ElSelect_relationObj.value === "manage"
            ? {
                time: new Date().getTime()
              }
            : undefined;

        queryData =
          this.ElSelect_relationObj.value === "device"
            ? [
                {
                  name: vm.currentNode.name,
                  id: vm.currentNode.id,
                  modelLabel: vm.currentNode.modelLabel
                }
              ]
            : this.getAllDevice(vm.currentNode);
        const flag =
          this.ElSelect_relationObj.value === "device" &&
          (["project", "sectionarea", "building", "floor"].includes(
            this.currentNode.modelLabel
          ) ||
            (this.currentNode.modelLabel === "room" &&
              !this.currentNode.roomtype));
        if (flag) {
          this.CetTable_1.data = [];
          return;
        }
      } else if (dimension === 3) {
        if (!vm.currentNode.nodeId) {
          vm.CetTable_1.data = [];
          return;
        }
        apiStr = "getLineRelationReverse";
        queryData =
          vm.currentNode.nodeType === 269619456
            ? (vm.currentNode.children || []).map(item => item.nodeId)
            : [vm.currentNode.nodeId];
        params = {
          status: vm.ElSelect_status.value,
          time: new Date().getTime()
        };
      }
      let res = [];
      if (dimension === 2) {
        const params = {
          nodes: queryData,
          page: {
            index: (this.currentPage - 1) * this.pageSize,
            limit: this.pageSize
          },
          time: Date.now()
        };
        res = await customApi[apiStr](params);
        this.totalCount = res.total;
      } else {
        res = await customApi[apiStr](queryData, params);
      }

      let tableData = vm._.get(res, "data", []) || [];
      vm.CetTable_1.data = tableData;
    },
    getAllDevice(node, deviceList = []) {
      if (
        !["project", "room", "sectionarea", "building", "floor"].includes(
          node.modelLabel
        )
      ) {
        deviceList.push({
          name: node.name,
          id: node.id,
          modelLabel: node.modelLabel
        });
      } else {
        (node?.children || []).forEach(item =>
          this.getAllDevice(item, deviceList)
        );
      }
      return deviceList;
    },
    async updateChildrenTable() {
      if (this.ElSelect_dimension.value !== 1) return;
      const vm = this;
      if (
        !vm.currentNode ||
        !vm.currentNode.children ||
        !vm.currentNode.children.length ||
        !vm.currentNode.children[0].modelLabel
      ) {
        vm.CetTable_2.data = [];
        return;
      }
      let queryData = [],
        params = {
          energyType: vm.ElSelect_1.value,
          status: vm.ElSelect_3.value,
          time: new Date().getTime()
        };
      vm.currentNode.children.forEach(item => {
        queryData.push({
          id: item.id,
          modelLabel: item.modelLabel,
          name: item.name
        });
      });
      let res = await customApi.relationshipGetLineRelation(queryData, params);
      let tableData = vm._.get(res, "data", []) || [];
      vm.CetTable_2.data = tableData;
    },
    addPipe_updata_out() {
      if (this.addPipe.type === 1) {
        this.updateCurrentTable();
      } else {
        this.updateChildrenTable();
      }
    },
    addGather_updata_out() {
      if (this.addGather.type === 1) {
        this.updateCurrentTable();
      } else {
        this.updateChildrenTable();
      }
    },
    AddManage_updata_out() {
      this.updateCurrentTable();
    },
    addPipelineDevice_updata_out() {
      this.updateCurrentTable();
    },
    AddManageRelation(scope) {
      if (scope.row.supplyTo?.supplyToName) {
        this.AddManage.energySupplyToDataAll = [
          {
            id: scope.row.supplyTo?.supplytoid,
            modelLabel: scope.row.supplyTo?.supplytolabel,
            name: scope.row.supplyTo?.supplyToName
          }
        ];
      } else {
        this.AddManage.energySupplyToDataAll = [];
      }

      this.AddManage.currentNode = {
        id: scope.row.measuredBy?.monitoredid,
        modelLabel: scope.row.measuredBy?.monitoredlabel
      };
      this.AddManage.visibleTrigger_in = new Date().getTime();
    },
    async handleCommand({ type, scope, tableType = 1 }) {
      switch (type) {
        case "deletePipe":
          this.deletePipeRelation(
            scope.row.supplyTo.id,
            scope.row.supplyTo.modelLabel,
            tableType,
            scope.row.supplyTo?.supplyToName,
            scope.row.supplyTo?.objectName
          );
          break;
        case "addGather":
          // 节点对象
          this.addGather.currentNode = {
            id: scope.row.supplyTo.objectid,
            modelLabel: scope.row.supplyTo.objectlabel
          };
          this.addGather.inputData_in = {
            id: scope.row.supplyTo.objectid,
            modelLabel: scope.row.supplyTo.objectlabel
          };
          // 已关联采集设备的列表
          this.addGather.deviceList = await this.connectMeasureBy({
            id: scope.row.supplyTo.objectid,
            modelLabel: scope.row.supplyTo.objectlabel
          });
          this.addGather.visibleTrigger_in = new Date().getTime();
          this.addGather.type = tableType;
          break;
        case "deleteGather":
          this.deleteGatherRelation(scope, tableType);
          break;
        case "energydata":
          if (this.$checkPermission("cloud_energydata")) {
            this.$router.push({
              name: "energydata"
            });
          } else if (this.$checkPermission("cloud_energyQueryAndAnalysis")) {
            this.$router.push({
              name: "energyQueryAndAnalysis"
            });
          }
          break;
        case "trendcurve":
          this.$router.push({
            name: "trendcurve"
          });
          break;
      }
    },
    async handleDeleteCommand({ type, scope }) {
      switch (type) {
        case "deletePipeline":
          this.deletePipeRelation1(
            scope,
            scope.row.measuredBy.modelLabel,
            scope.row.measuredBy?.name,
            scope.row.measuredBy?.deviceName
          );
          break;
        case "deleteManage":
          this.deleteManageRelation(scope);
          break;
      }
    },
    async connectMeasureBy(val) {
      let queryData = [
        {
          modelLabel: val.modelLabel,
          id: val.id
        }
      ];
      let res = await customApi.connectMeasureBy(queryData);
      let deviceList = [];
      this._.get(res, "data", []).forEach(item => {
        deviceList.push({
          name: item.deviceName,
          deviceName: item.deviceName,
          dataId: item.id,
          modelLabel: "269619472",
          id: item.measuredby,
          tree_id: "269619472_" + item.measuredby
        });
      });
      return deviceList;
    },
    // 删除管理层级关联关系
    deleteManageRelation(scope) {
      this.$confirm(
        $T(
          `{0}与{1}的管理层级关联关系，确认要删除吗？`,
          scope.row.supplyTo?.supplyToName || "--",
          scope.row.supplyTo?.objectName || "--"
        ),
        $T("提示"),
        {
          distinguishCancelAndClose: true,
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        }
      )
        .then(() => {
          const param = {
            idRange: [scope.row.supplyTo.id],
            modelLabel: "energysupplyto"
          };
          customApi.connectRelationship(param).then(res => {
            if (res.code === 0) {
              this.$message({
                message: $T("保存成功"),
                type: "success"
              });
              this.updateCurrentTable();
            }
          });
        })
        .catch(action => {
          if (action === "cancel") {
            this.$message({
              type: "info",
              message: $T("已取消")
            });
          }
        });
    },
    // 删除管网关联关系
    deletePipeRelation(id, modelLabel, tableType, name1, name2) {
      var me = this;
      me.$confirm(
        $T(`{0}与{1}的管网关联关系，确认要删除吗？`, name1, name2),
        $T("提示"),
        {
          distinguishCancelAndClose: true,
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        }
      )
        .then(() => {
          var param = {
            idRange: [id],
            modelLabel
          };
          customApi.connectRelationship(param).then(res => {
            if (res.code === 0) {
              me.$message({
                message: $T("保存成功"),
                type: "success"
              });
              if (tableType === 1) {
                me.updateCurrentTable();
              } else {
                me.updateChildrenTable();
              }
            }
          });
        })
        .catch(action => {
          if (action === "cancel") {
            me.$message({
              type: "info",
              message: $T("已取消")
            });
          }
        });
    },
    deletePipeRelation1(scope) {
      var me = this;
      me.$confirm(
        $T(
          `{0}与{1}的管网关联关系，确认要删除吗？`,
          scope.row.measuredBy?.name || "--",
          scope.row.measuredBy?.deviceName || "--"
        ),
        $T("提示"),
        {
          distinguishCancelAndClose: true,
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        }
      )
        .then(async () => {
          let params = [],
            deviceIds = [],
            deviceList = await this.connectMeasureBy({
              id: scope.row.measuredBy.monitoredid,
              modelLabel: scope.row.measuredBy.monitoredlabel
            });
          deviceList.forEach(item => {
            if (item.id !== scope.row.measuredBy.measuredby)
              deviceIds.push(item.id);
          });
          var obj = {
            modelLabel: scope.row.measuredBy.monitoredlabel,
            nodes: [
              {
                deviceIds: deviceIds,
                id: scope.row.measuredBy.monitoredid
              }
            ]
          };
          params.push(obj);
          // 数据源
          var dataSource = 6;
          if (deviceIds.length === 0) {
            dataSource = 6;
          } else {
            dataSource = 1;
          }
          customApi.quantityQuantityMap(params).then(res => {
            if (res.code === 0) {
              me.Edit_dataSource(dataSource, params, 1);
            }
          });
        })
        .catch(action => {
          if (action === "cancel") {
            me.$message({
              type: "info",
              message: $T("已取消")
            });
          }
        });
    },
    // 管理层级删除设备关联关系
    deleteGatherRelation(scope, tableType) {
      var me = this;
      me.$confirm(
        $T(
          `{0}与{1}的管网关联关系，确认要删除吗？`,
          scope.row.supplyTo?.objectName || "--",
          scope.row.measuredBy?.deviceName || "--"
        ),
        $T("提示"),
        {
          distinguishCancelAndClose: true,
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        }
      )
        .then(async () => {
          let params = [],
            deviceIds = [],
            deviceList = await this.connectMeasureBy({
              id: scope.row.supplyTo.objectid,
              modelLabel: scope.row.supplyTo.objectlabel
            });
          deviceList.forEach(item => {
            if (item.id !== scope.row.measuredBy.measuredby)
              deviceIds.push(item.id);
          });
          var obj = {
            modelLabel: scope.row.supplyTo.objectlabel,
            nodes: [
              {
                deviceIds: deviceIds,
                id: scope.row.supplyTo.objectid
              }
            ]
          };
          params.push(obj);
          // 数据源
          var dataSource = 6;
          if (deviceIds.length === 0) {
            dataSource = 6;
          } else {
            dataSource = 1;
          }
          customApi.quantityQuantityMap(params).then(res => {
            if (res.code === 0) {
              me.Edit_dataSource(dataSource, params, tableType);
            }
          });
        })
        .catch(action => {
          if (action === "cancel") {
            me.$message({
              type: "info",
              message: $T("已取消")
            });
          }
        });
    },
    // 管网层级删除设备关联关系
    deleteGatherRelation1(scope, tableType, name1, name2) {
      var me = this;
      me.$confirm(
        $T(`{0}与{1}的设备关联关系，确认要删除吗？`, name1, name2),
        $T("提示"),
        {
          distinguishCancelAndClose: true,
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        }
      )
        .then(async () => {
          let params = [],
            deviceIds = [],
            deviceList = await this.connectMeasureBy({
              id: scope.row.measuredBy.monitoredid,
              modelLabel: scope.row.measuredBy.monitoredlabel
            });
          deviceList.forEach(item => {
            if (item.id !== scope.row.measuredBy.measuredby)
              deviceIds.push(item.id);
          });
          var obj = {
            modelLabel: scope.row.measuredBy.monitoredlabel,
            nodes: [
              {
                deviceIds: deviceIds,
                id: scope.row.measuredBy.monitoredid
              }
            ]
          };
          params.push(obj);
          // 数据源
          var dataSource = 6;
          if (deviceIds.length === 0) {
            dataSource = 6;
          } else {
            dataSource = 1;
          }
          customApi.quantityQuantityMap(params).then(res => {
            if (res.code === 0) {
              me.Edit_dataSource(dataSource, params, tableType);
            }
          });
        })
        .catch(action => {
          if (action === "cancel") {
            me.$message({
              type: "info",
              message: $T("已取消")
            });
          }
        });
    },
    // 更新物理量数据源
    Edit_dataSource(dataSource, data, tableType) {
      let me = this;
      customApi.quantityQuantityObject(data, { dataSource }).then(res => {
        if (res.code === 0) {
          me.$message({
            message: $T("保存成功"),
            type: "success"
          });
          if (tableType === 1) {
            me.updateCurrentTable();
          } else {
            me.updateChildrenTable();
          }
        }
      });
    },
    setAddBtnTitle() {
      const dimension = this.ElSelect_dimension.value,
        relationObj = this.ElSelect_relationObj.value;
      if (dimension === 1) {
        this.CetButton_add.title = $T("新增管网设备");
      } else if (dimension === 2) {
        this.CetButton_add.title =
          relationObj === "manage"
            ? $T("新增管理层级关联")
            : $T("新增设备关联");
      } else {
        this.CetButton_add.title = $T("新增管网设备关联");
      }
    },
    async ElSelect_dimension_change_out(val) {
      this.CetButton_batch.visible_in = val === 3;
      this.setAddBtnTitle();
      this.ElSelect_status.value = 0;
      this.CetGiantTree_1.setting.check.enable = false;
      if (val === 1) {
        this.Columns_1[0].prop = "supplyTo.objectName";
        this.Columns_1[3].label = $T("设备名称");
        this.Columns_1[3].prop = "measuredBy.deviceName";
        this.Columns_1[4].label = $T("设备ID");
        this.Columns_1[4].prop = "measuredBy.measuredby";
        this.ElSelect_1.value = 0;
        this.ElSelect_3.value = 0;
      } else if (val === 2) {
        this.CetButton_add.disable_in = true;
        this.ElSelect_relationObj.value = "manage";
        this.Columns_1[0].prop = "supplyTo.objectName";
        this.Columns_1[3].label = $T("管理层级名称");
        this.Columns_1[3].prop = "supplyTo.supplyToName";
        this.Columns_1[4].label = $T("层级ID");
        this.Columns_1[4].prop = "supplyTo.supplytoid";
      } else if (val === 3) {
        this.Columns_1[0].prop = "measuredBy.name";
        this.Columns_1[3].label = $T("管理层级名称");
        this.Columns_1[3].prop = "supplyTo.supplyToName";
        this.Columns_1[4].label = $T("层级ID");
        this.Columns_1[4].prop = "supplyTo.supplytoid";
        this.ElSelect_deviceType.value =
          this.ElOption_deviceType.options_in.map(item => item.id);
      }
      await this.getTreeData();
      this.getTableData();
    },
    async ElSelect_status_change_out() {
      this.currentPage = 1;
      this.pageSize = 10;
      this.totalCount = 0;
      this.setAddBtnTitle();
      await this.getTreeData();
      if (
        this.ElSelect_dimension.value === 3 &&
        this.CetGiantTree_1.setting.check.enable
      ) {
        this.setTreeLeaf_filterText1Change(this.CetGiantTree_1.inputData_in);
      }
      if (
        (this.ElSelect_dimension.value === 2 &&
          this.ElSelect_relationObj.value === "manage") ||
        this.ElSelect_dimension.value === 3
      ) {
        this.getTableData();
      }
    },
    async ElSelect_deviceType_change_out() {
      this.setAddBtnTitle();
      await this.getTreeData();
      if (
        this.ElSelect_dimension.value === 3 &&
        this.CetGiantTree_1.setting.check.enable
      ) {
        this.setTreeLeaf_filterText1Change(this.CetGiantTree_1.inputData_in);
      }
      this.getTableData();
    },
    async ElSelect_relationObj_change_out(val) {
      this.currentPage = 1;
      this.pageSize = 10;
      this.totalCount = 0;
      this.setAddBtnTitle();
      if (val === "manage") {
        this.Columns_1[0].prop = "supplyTo.objectName";
        this.Columns_1[3].label = $T("管理层级名称");
        this.Columns_1[3].prop = "supplyTo.supplyToName";
        this.Columns_1[4].label = $T("层级ID");
        this.Columns_1[4].prop = "supplyTo.supplytoid";
      } else if (val === "device") {
        this.Columns_1[0].prop = "measuredBy.name";
        this.Columns_1[3].label = $T("设备名称");
        this.Columns_1[3].prop = "measuredBy.deviceName";
        this.Columns_1[4].label = $T("设备ID");
        this.Columns_1[4].prop = "measuredBy.measuredby";
      }
      await this.getTreeData();
      this.getTableData();
    },
    ElSelect_1_change_out() {
      this.getTableData();
    },
    ElSelect_3_change_out() {
      this.getTableData();
    },
    ElSelect_4_change_out() {
      this.updateChildrenTable();
    },
    CetButton_shareRate_statusTrigger_out() {
      // 已关联管网设备的列表
      const checkedNodes = [];
      this.CetTable_1.data.forEach(item => {
        if (item.supplyTo.objectid) {
          if (
            !checkedNodes.find(i => {
              return (
                i.id === item.supplyTo.objectid &&
                i.modelLabel === item.supplyTo.objectlabel
              );
            })
          )
            checkedNodes.push({
              id: item.supplyTo.objectid,
              name: item.supplyTo.objectName,
              modelLabel: item.supplyTo.objectlabel
            });
        }
      });
      this.shareRate.energySupplyToDataAll = checkedNodes;
      this.shareRate.inputData_in = this._.cloneDeep(this.currentNode);
      this.shareRate.visibleTrigger_in = new Date().getTime();
    },
    async CetButton_add_statusTrigger_out() {
      if (this.ElSelect_dimension.value === 1) {
        // 已关联管网设备的列表
        this.addPipe.energySupplyToDataAll = await this.getMeasuredBy(
          this.currentNode
        );
        this.addPipe.inputData_in = this._.cloneDeep(this.currentNode);
        this.addPipe.currentNode = this._.cloneDeep(this.currentNode);
        this.addPipe.visibleTrigger_in = new Date().getTime();
        this.addPipe.type = 1;
      } else if (this.ElSelect_dimension.value === 2) {
        if (this.ElSelect_relationObj.value === "manage") {
          this.AddManage.energySupplyToDataAll = this.CetTable_1.data
            .map(item => {
              if (item.supplyTo?.supplyToName)
                return {
                  id: item.supplyTo?.supplytoid,
                  modelLabel: item.supplyTo?.supplytolabel,
                  name: item.supplyTo?.supplyToName
                };
            })
            .filter(item => item);
          this.AddManage.currentNode = this._.cloneDeep(this.currentNode);
          this.AddManage.visibleTrigger_in = new Date().getTime();
        } else {
          // 节点对象
          this.addGather.currentNode = {
            id: this.currentNode.id,
            modelLabel: this.currentNode.modelLabel
          };
          this.addGather.inputData_in = {
            id: this.currentNode.id,
            modelLabel: this.currentNode.modelLabel
          };
          // 已关联采集设备的列表
          this.addGather.deviceList = await this.connectMeasureBy({
            id: this.currentNode.id,
            modelLabel: this.currentNode.modelLabel
          });
          this.addGather.visibleTrigger_in = new Date().getTime();
          this.addGather.type = 1;
        }
      } else {
        // 已关联管网设备的列表

        const data = this.CetTable_1.data
          .map(item => {
            if (item.measuredBy?.name)
              return {
                id: item.measuredBy?.monitoredid,
                modelLabel: item.measuredBy?.monitoredlabel,
                name: item.measuredBy?.name
              };
          })
          .filter(item => item);
        const arr = [];
        data.forEach(item => {
          const reItem = arr.find(
            it => it.modelLabel === item.modelLabel && it.id === item.id
          );
          !reItem && arr.push(item);
        });
        this.addPipelineDevice.energySupplyToDataAll = arr;
        this.addPipelineDevice.inputData_in = this._.cloneDeep(
          this.currentNode
        );
        this.addPipelineDevice.currentNode = this._.cloneDeep(this.currentNode);
        this.addPipelineDevice.visibleTrigger_in = new Date().getTime();
        this.addPipelineDevice.type = 1;
      }
    },
    async getMeasuredBy(val) {
      let param = [
        {
          modelLabel: val.modelLabel,
          id: val.id
        }
      ];
      let res = await customApi.energySupplyToMeasuredBy(param);
      let energySupplyToDataAll = [];
      var relationData = this._.get(res, "data", []);
      // 过滤出对应的关联关系
      relationData.forEach(item => {
        energySupplyToDataAll.push({
          dataId: item.id,
          modelLabel: item.objectlabel,
          name: item.objectName,
          id: item.objectid,
          tree_id: item.objectlabel + "_" + item.objectid,
          measuredBys: item.measuredBys || []
        });
      });
      return energySupplyToDataAll;
    },
    async addCurrentPipe(scope) {
      // 已关联管网设备的列表
      this.addPipe.energySupplyToDataAll = await this.getMeasuredBy({
        id: scope.row.supplyTo.supplytoid,
        modelLabel: scope.row.supplyTo.supplytolabel
      });
      this.addPipe.inputData_in = this._.cloneDeep(scope.row);
      this.addPipe.currentNode = {
        id: scope.row.supplyTo.supplytoid,
        modelLabel: scope.row.supplyTo.supplytolabel
      };
      this.addPipe.visibleTrigger_in = new Date().getTime();
      this.addPipe.type = 2;
    },
    CetButton_clear_statusTrigger_out() {
      const invalidData = this.getInvalidData();
      if (
        !invalidData.energySupplyToIds.length &&
        !invalidData.measuredByIds.length
      ) {
        let msg,
          dimension = this.ElSelect_dimension.value;
        if (dimension === 1) msg = $T("当前选中的节点及子节点没有无效关联数据");
        if (dimension === 2) msg = $T("当前选中的节点没有无效关联数据");
        if (dimension === 3) msg = $T("当前选中的节点及子节点没有无效关联数据");
        this.$message.info(msg);
        return;
      }
      this.$confirm($T("确定要清除无效关联吗？"), $T("提示"), {
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          customApi.deleteInvalidRelationship(invalidData).then(res => {
            if (res.code === 0) {
              this.$message.success($T("保存成功"));
              this.getTableData();
            }
          });
        })
        .catch(action => {
          if (action === "cancel") {
            this.$message.info($T("已取消"));
          }
        });
    },
    // 获取表格中无效关联的数据
    getInvalidData() {
      let energySupplyToIds = [],
        measuredByIds = [];
      if (this.ElSelect_dimension.value === 1) {
        energySupplyToIds = this.CetTable_1.data
          .concat(this.CetTable_2.data)
          .filter(item => item.supplyTo?.id && !item.supplyTo?.objectName)
          .map(item => item.supplyTo.id);
        measuredByIds = this.CetTable_1.data
          .concat(this.CetTable_2.data)
          .filter(item => item.measuredBy?.id && !item.measuredBy?.deviceName)
          .map(item => item.measuredBy.id);
      } else if (this.ElSelect_dimension.value === 2) {
        if (this.ElSelect_relationObj.value === "manage") {
          energySupplyToIds = this.CetTable_1.data
            .filter(item => {
              return (
                item.supplyTo?.id &&
                !item.supplyTo?.supplyToName &&
                !item.measuredBy
              );
            })
            .map(item => item.supplyTo.id);
        } else {
          measuredByIds = this.CetTable_1.data
            .filter(item => {
              return (
                item.measuredBy?.id &&
                !item.measuredBy?.deviceName &&
                !item.supplyTo
              );
            })
            .map(item => item.measuredBy.id);
        }
      } else {
        energySupplyToIds = this.CetTable_1.data
          .filter(item => {
            return (
              item.supplyTo?.id &&
              (!item.supplyTo.objectName || !item.supplyTo.supplyToName)
            );
          })
          .map(item => item.supplyTo.id);
        measuredByIds = this.CetTable_1.data
          .filter(item => item.measuredBy?.id && !item.measuredBy?.name)
          .map(item => item.measuredBy.id);
      }
      return { energySupplyToIds, measuredByIds };
    },

    CetButton_batch_statusTrigger_out() {
      this.CetTable_1.data = [];
      const batchFlag = !this.CetGiantTree_1.setting.check.enable;
      this.CetButton_add.disable_in = batchFlag;
      this.CetButton_batch.title = batchFlag
        ? $T("取消批量勾选通道节点")
        : $T("批量勾选通道节点");
      const treeData = this._.cloneDeep(this.CetGiantTree_1.inputData_in);
      this.CetGiantTree_1.setting.check.enable = batchFlag;
      if (batchFlag) this.setTreeLeaf_filterText1Change(treeData);
      this.CetGiantTree_1.inputData_in = treeData;
      if (!batchFlag) this.CetGiantTree_1_currentNode_out(this.currentNode);
    },
    setTreeLeaf_filterText1Change(nodes) {
      if (!nodes) {
        return;
      }
      nodes.forEach(item => {
        if (item.nodeType !== 269619456) {
          item.nocheck = true;
        }
        this.setTreeLeaf_filterText1Change(this._.get(item, "children", []));
      });
    },
    CetButton_import_statusTrigger_out() {
      if (this.ElSelect_1.value) {
        this.ElSelect_5.value = this.ElSelect_1.value;
      } else {
        if (this.ElOption_5.options_in.find(item => item.energytype === 2)) {
          this.ElSelect_5.value = 2;
        } else {
          this.ElSelect_5.value = this._.get(
            this.ElOption_5.options_in,
            "[0].energytype"
          );
        }
      }
      this.uploadDialog.openTrigger_in = new Date().getTime();
    },
    uploadDialog_download() {
      if (!this.ElSelect_5.value) {
        this.$message({
          type: "warning",
          message: $T("请先创建能源类型！")
        });
        return;
      }
      let energytypeText = this.ElOption_5.options_in.find(
        item => item.energytype === this.ElSelect_5.value
      ).name;
      this.$confirm(
        $T(
          "是否下载{0}，能源类型为{1}的配置模板？",
          this.projectInfo.name,
          energytypeText
        ),
        $T("提示"),
        {
          distinguishCancelAndClose: true,
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        }
      )
        .then(() => {
          const url = `/eem-service/v1/relationship/exportTemplate?energyType=${
            this.ElSelect_5.value
          }&fileName=${encodeURIComponent(
            `${this.projectInfo.name}_${this.ElSelect_5.value}_节点关系导入模板`
          )}`;
          common.downExcel(url, {}, this.token, this.projectId);
        })
        .catch(action => {
          if (action === "cancel") {
            this.$message({
              type: "info",
              message: $T("已取消")
            });
          }
        });
    },
    uploadDialog_uploadFile(val) {
      let fileName = this._.get(val, "file.name");
      let nameArr = fileName.split("_");
      if (nameArr.length < 3) {
        this.$message({
          type: "warning",
          message: $T("文件名称不规范！")
        });
        return;
      }
      let energytype = nameArr[nameArr.length - 2];
      let energytypeObj = this.ElOption_5.options_in.find(
        item => item.energytype === Number(energytype)
      );
      if (!energytypeObj) {
        this.$message({
          type: "warning",
          message: $T("项目不存在导入文件的能源类型！")
        });
        return;
      }
      this.$confirm(
        $T(
          "是否上传{0}，能源类型为{1}的配置数据？",
          this.projectInfo.name,
          energytypeObj.name
        ),
        $T("提示"),
        {
          distinguishCancelAndClose: true,
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        }
      )
        .then(() => {
          const formData = new FormData();
          formData.append("file", val.file);
          this.uploadDialog.initTrigger_in = Date.now();
          customApi.relationshipImport(formData).then(response => {
            if (response.code === 0) {
              this.$message({
                type: "warning",
                message: response.msg
              });
              this.uploadDialog.closeTrigger_in = new Date().getTime();
              this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
              // this.getTreeData();
              this.CetButton_import.disable_in = true;
              this.CetButton_import.title = "导入中";
              setTimeout(() => {
                this.callImportRatio();
              }, 2000);
            }
          });
        })
        .catch(action => {
          if (action === "cancel") {
            this.$message({
              type: "info",
              message: $T("已取消")
            });
          }
        });
    },
    CetButton_importRelation_statusTrigger_out() {
      this.uploadDialogRelation.openTrigger_in = new Date().getTime();
    },
    uploadDialogRelation_download() {
      const url = `/eem-service/v1/connect/energySupplyTo/export/${this.projectId}`;

      common.downExcel(url, {}, this.token, this.projectId);
    },
    uploadDialogRelation_uploadFile(val) {
      const formData = new FormData();
      formData.append("file", val.file);
      this.uploadDialogRelation.initTrigger_in = Date.now();
      customApi.energySupplyToBatchImport(formData).then(response => {
        if (response.code === 0) {
          this.$message({
            type: "success",
            message: $T("导入成功！")
          });
          this.uploadDialogRelation.closeTrigger_in = new Date().getTime();
          this.getTableData();
        }
      });
    },
    CetButton_export_statusTrigger_out() {},
    // 获取导入文件的进度
    getImportRatio() {
      customApi.getImportRatio().then(response => {
        if (response.code === 0) {
          if (response.data && !response.data.state) {
            let num = (response.data.ratio * 100).toFixed(1);
            this.CetButton_import.disable_in = true;
            this.CetButton_import.title = "导入中（" + num + "%）";
          } else {
            this.CetButton_import.disable_in = false;
            this.CetButton_import.title = "导入";
            if (response.data && response.data.ratio === 1) {
              this.getTreeData();
            }
            this.closeImportRatio();
          }
        }
      });
    },
    // 调用导入进度接口
    callImportRatio() {
      this.getImportRatio();
      this.setInterval = setInterval(() => {
        this.getImportRatio();
      }, 5000);
    },
    closeImportRatio() {
      if (this.setInterval) {
        clearInterval(this.setInterval);
      }
    }
  },
  async mounted() {
    await this.getEnergyType();
    this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
    this.getTreeData();
  },
  destroyed: function () {
    // 关闭定时器
    this.closeImportRatio();
  },
  deactivated: function () {
    // 关闭定时器
    this.closeImportRatio();
  }
};
</script>
<style lang="scss" scoped>
.page {
  .content {
    overflow: auto;
    & > div {
      height: 100%;
      box-sizing: border-box;
    }
    .cardTitle {
      @include line_height(Hm);
    }
  }
  :deep(.invalidDataRow) {
    // background-color: #feefef;
  }
  :deep(.dark .invalidDataRow) {
    // background-color: #261f46;
  }
}
.handle {
  cursor: pointer;
  @include font_color(ZS);
}
.delete {
  cursor: pointer;
  @include font_color(Sta3);
}

.custom-select-tag {
  :deep(.el-select__tags-text) {
    float: left;
    display: inline-block;
    max-width: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.page {
  :deep(.forbiddenSelect span.node_name) {
    @include font_color(T6);
    cursor: not-allowed;
  }
}

.page {
  :deep(.el-icon--right) {
    margin-left: 0;
  }
}

.tableFooter {
  text-align: right;
  @include padding_right(J2);
  @include padding_top(J2);
  @include padding_bottom(J2);
  height: 30px;
  @include background_color(BG1);
  border-bottom-left-radius: mh-get(C3);
  border-bottom-right-radius: mh-get(C3);
}
</style>
