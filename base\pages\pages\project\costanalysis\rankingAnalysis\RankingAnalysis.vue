﻿<template>
  <div class="page eem-common">
    <el-container class="fullheight">
      <el-aside width="315px" class="eem-aside">
        <CetGiantTree
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
        ></CetGiantTree>
      </el-aside>
      <el-main class="fullheight mlJ3 eem-container">
        <div class="common-title-H1 mbJ3">{{ title }}</div>
        <div class="mbJ1">
          <el-radio-group v-model="status" size="small" @change="handleClick">
            <el-radio-button label="0">{{ $T("成本排名") }}</el-radio-button>
            <el-radio-button label="1">
              {{ $T("单位指标成本排名") }}
            </el-radio-button>
          </el-radio-group>
        </div>
        <div class="box eem-card">
          <div class="top1">
            <CetButton
              class="fr custom—square"
              v-bind="CetButton_8"
              v-on="CetButton_8.event"
            ></CetButton>
            <div class="block fr mlJ mrJ">
              <CustomElDatePicker
                class="fr"
                :prefix_in="$T('选择月份')"
                v-model="value3"
                type="month"
                :placeholder="$T('选择月份')"
                v-if="ElSelect_4.value == 14"
                style="width: 200px"
                @change="monthChange"
                :picker-options="pickerOptions0"
                :clearable="false"
              />
              <CustomElDatePicker
                class="fr"
                :prefix_in="$T('选择年份')"
                v-model="value4"
                type="year"
                :placeholder="$T('选择年份')"
                v-if="ElSelect_4.value !== 14"
                style="width: 200px"
                @change="yearChange"
                :picker-options="pickerOptions0"
                :clearable="false"
              />
            </div>
            <CetButton
              class="fr custom—square"
              v-bind="CetButton_7"
              v-on="CetButton_7.event"
            ></CetButton>
            <customElSelect
              class="fr mrJ1"
              v-model="ElSelect_4.value"
              v-bind="ElSelect_4"
              v-on="ElSelect_4.event"
              :prefix_in="$T('查询时段')"
            >
              <ElOption
                v-for="item in ElOption_4.options_in"
                :key="item[ElOption_4.key]"
                :label="item[ElOption_4.label]"
                :value="item[ElOption_4.value]"
                :disabled="item[ElOption_4.disabled]"
              ></ElOption>
            </customElSelect>
            <customElSelect
              class="fr mrJ1"
              v-model="ElSelect_1.value"
              v-bind="ElSelect_1"
              v-on="ElSelect_1.event"
              :prefix_in="$T('能源类型')"
            >
              <ElOption
                v-for="item in ElOption_1.options_in"
                :key="item[ElOption_1.key]"
                :label="item[ElOption_1.label]"
                :value="item[ElOption_1.value]"
                :disabled="item[ElOption_1.disabled]"
              ></ElOption>
            </customElSelect>
            <customElSelect
              class="fr mrJ1"
              v-model="ElSelect_2.value"
              v-bind="ElSelect_2"
              v-on="ElSelect_2.event"
              :prefix_in="$T('排序')"
            >
              <ElOption
                v-for="item in ElOption_2.options_in"
                :key="item[ElOption_2.key]"
                :label="item[ElOption_2.label]"
                :value="item[ElOption_2.value]"
                :disabled="item[ElOption_2.disabled]"
              ></ElOption>
            </customElSelect>
            <customElSelect
              class="fr mrJ1"
              v-if="status == 0"
              v-model="ElSelect_3.value"
              v-bind="ElSelect_3"
              v-on="ElSelect_3.event"
              :prefix_in="$T('类型')"
            >
              <ElOption
                v-for="item in ElOption_3.options_in"
                :key="item[ElOption_3.key]"
                :label="item[ElOption_3.label]"
                :value="item[ElOption_3.value]"
                :disabled="item[ElOption_3.disabled]"
              ></ElOption>
            </customElSelect>
            <customElSelect
              class="fr mrJ1"
              v-if="status == 1"
              v-model="ElSelect_5.value"
              v-bind="ElSelect_5"
              v-on="ElSelect_5.event"
              :prefix_in="$T('指标')"
            >
              <ElOption
                v-for="item in ElOption_5.options_in"
                :key="item[ElOption_5.key]"
                :label="item[ElOption_5.label]"
                :value="item[ElOption_5.value]"
                :disabled="item[ElOption_5.disabled]"
              ></ElOption>
            </customElSelect>
          </div>
          <div class="top2 lh46">
            <div class="block mrJ1" v-if="status == 0" style="float: right">
              <el-radio
                v-model="radio"
                :label="item.id"
                v-for="item in radioList"
                :key="item.id"
                @change="changeRadio1"
              >
                {{ item.name }}
              </el-radio>
            </div>
            <div class="block mrJ1" v-if="status == 1" style="float: right">
              <el-radio v-model="radio1" label="0" @change="changeRadio2">
                {{ $T("单位面积") }}
              </el-radio>
              <el-radio v-model="radio1" label="1" @change="changeRadio2">
                {{ $T("单位人员") }}
              </el-radio>
            </div>
          </div>
          <div id="main">
            <CetChart v-bind="CetChart_1"></CetChart>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import TREE_PARAMS from "@/store/treeParams.js";
import { httping } from "@omega/http";

export default {
  name: "Rankinganalysis",
  components: {},

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data(vm) {
    return {
      unitName: $T("元"),
      flag: false,
      pickerOptions0: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6; //如果没有后面的-8.64e6就是不可以选择今天的
        }
      },
      title: "",
      projectId: 0, // 项目id
      nodes: [],
      status: 0, // 成本排名0和单位指标成本呢排名1
      radio: "", // 当前选中
      radio1: "0", // 单位面积和单位人员
      radioList: [], // 单选列表
      value3: vm.$moment().startOf("year").format("YYYY-M"), // 当前选中的年份
      value4: vm.$moment().startOf("year").format("YYYY"), // 当前选中的月份
      timeId: 1,
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out
        }
      },
      ElSelect_1: {
        value: 0,
        style: { width: "200px" },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: 2,
        style: { width: "200px" },
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [
          { id: 0, text: $T("由高到低") },
          { id: 1, text: $T("由低到高") },
          { id: 2, text: $T("无序") }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_3: {
        value: 0,
        style: { width: "200px" },
        event: {
          change: this.ElSelect_3_change_out
        }
      },
      ElOption_3: {
        options_in: [
          { id: 0, text: $T("按区域") },
          { id: 2, text: $T("按用能类型") },
          { id: 1, text: $T("按分项用能") }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_4: {
        value: 17,
        style: { width: "200px" },
        event: {
          change: this.ElSelect_4_change_out
        }
      },
      ElOption_4: {
        options_in: [
          { id: 14, text: $T("月") },
          { id: 17, text: $T("年") }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_5: {
        value: 0,
        style: {},
        event: {
          change: this.ElSelect_5_change_out
        }
      },
      ElOption_5: {
        options_in: [{ id: 0, text: $T("建筑") }],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetButton_7: {
        visible_in: true,
        disable_in: false,
        title: "",
        plain: true,
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_7_statusTrigger_out
        }
      },
      CetButton_8: {
        visible_in: true,
        disable_in: false,
        title: "",
        plain: true,
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_8_statusTrigger_out
        }
      },
      CetChart_1: {
        //组件输入项
        inputData_in: null,
        options: {}
      }
    };
  },
  watch: {},
  methods: {
    // 接口 获取节点树
    getTree() {
      let _this = this;
      _this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      let obj = {
        rootID: sessionStorage.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.costanalysisTree,
        treeReturnEnable: true
      };
      let auth = _this.token; //身份验证
      httping({
        url: "/eem-service/v1/node/nodeTree",
        data: obj,
        method: "POST"
      }).then(res => {
        if (res.code == 0) {
          let list = res.data;
          this.handleTree(list);
          this.projectId = list[0].id;
          this.nodes = list[0].children;
          this.CetGiantTree_1.inputData_in = list;
          this.title = list[0].name;
          this.projectEnergy(list[0].id); // 查询项目的能源类型
        }
      });
    },
    // 处理节点树
    handleTree(array) {
      const expanded = datas => {
        if (datas && datas.length > 0) {
          datas.forEach(e => {
            e.label = e.name;
            expanded(e.children);
          });
        }
      };
      expanded(array);
      return array;
    },
    // 接口 查询项目的能源类型
    projectEnergy(projectId) {
      httping({
        url: "eem-service/v1/project/projectEnergy?projectId=" + projectId,
        method: "GET"
      }).then(res => {
        if (res.code == 0) {
          if (res.data && res.data.length != 0) {
            let list = res.data;
            list.map(item => {
              item.text = item.name;
              item.id = item.energytype;
            });
            this.ElOption_1.options_in = list.filter(
              i => ![18, 22].includes(i.id)
            );
            this.ElSelect_1.value = list[0].energytype;
            // this.costByNode(projectId, this.nodes);
            this.CetGiantTree_1.selectNode =
              this.CetGiantTree_1.inputData_in[0];
          }
        }
      });
    },
    // 接口 通过维度id获取标签数据
    propertysByDimId(dimId) {
      httping({
        url: "/eem-service/v1/dim/setting/propertysByDimId?dimId=" + dimId,
        method: "GET"
      }).then(res => {
        if (res.code == 0) {
          if (res.data != null) {
            let list = res.data;
            this.radioList = list;
            this.radio = list[0].id;
            this.request();
          } else {
            this.radioList = [];
          }
        }
      });
    },
    // 接口 根据节点查询成本值
    costByNode(projectId, nodes) {
      let list = [];
      let time;
      let startTime;
      let endTime;
      if (this.ElSelect_4.value == 17) {
        // 按年
        time = this.value4 * 1;
        startTime = new Date(time + "-1-1").getTime();
        endTime = new Date(time + 1 + "-1-1").getTime();
      } else {
        time = this.value3;
        let year = time.split("-")[0];
        let month = time.split("-")[1];
        startTime = new Date(year + "-" + month + "-1").getTime();
        if (month == 12) {
          endTime = new Date(year * 1 + 1 + "-1-1").getTime();
        } else {
          endTime = new Date(year + "-" + (month * 1 + 1) + "-1").getTime();
        }
      }
      nodes.map(item => {
        let obj = {};
        obj.id = item.id;
        obj.modelLabel = item.modelLabel;
        obj.name = item.name;
        list.push(obj);
      });
      let obj = {
        startTime,
        endTime,
        nodes: list,
        cycle: this.ElSelect_4.value,
        energyType: this.ElSelect_1.value,
        queryType: 0, // 0:只查询本期成本值
        costKpiType: this.status == 0 ? 0 : 2, // 0 不查询该项
        projectId
      };
      httping({
        url: "eem-service/v1/costanalysis/costByNode",
        method: "POST",
        data: obj
      }).then(res => {
        if (res.code == 0) {
          let list = res.data;
          this.unitName = this._.get(list, "[0].unitName", "元");
          let nameList = [];
          let valueList = [];
          list.map(item => {
            nameList.push(item.objectName);
            if (this.status == 0) {
              valueList.push(item.data[0].value);
            } else if (this.radio1 == 0) {
              valueList.push(item.data[0].costKpi[0].value);
            } else {
              valueList.push(item.data[0].costKpi[1].value);
            }
          });

          this.echartData = [nameList, valueList];
          this.sort(nameList, valueList);
        }
      });
    },
    // 接口 根据维度信息查询成本值
    costByDimId(projectId, nodes) {
      let list = [];
      let radioList = this.radioList;
      let radioName;
      let time;
      let startTime;
      let endTime;
      if (this.ElSelect_4.value == 17) {
        // 按年
        time = this.value4 * 1;
        startTime = new Date(time + "-1-1").getTime();
        endTime = new Date(time + 1 + "-1-1").getTime();
      } else {
        time = this.value3;
        let year = time.split("-")[0];
        let month = time.split("-")[1];
        startTime = new Date(year + "-" + month + "-1").getTime();
        if (month == 12) {
          endTime = new Date(year * 1 + 1 + "-1-1").getTime();
        } else {
          endTime = new Date(year + "-" + (month * 1 + 1) + "-1").getTime();
        }
      }
      radioList.map(item => {
        if (item.id == this.radio) {
          radioName = item.name;
        }
      });
      nodes.map(item => {
        let obj = {};
        obj.id = item.id;
        obj.modelLabel = item.modelLabel;
        obj.name = item.name;
        list.push(obj);
      });
      let obj = {
        startTime,
        endTime,
        nodes: list,
        cycle: this.ElSelect_4.value,
        energyType: this.ElSelect_1.value,
        queryType: 0, // 0:只查询本期成本值
        tag: {
          name: radioName,
          tag: this.radio.toString()
        },
        queryTotal: true,
        projectId
      };
      httping({
        url: "eem-service/v1/costanalysis/cost/dimTag/nodes",
        method: "POST",
        data: obj
      }).then(res => {
        if (res.code == 0) {
          let list = res.data;
          let nameList = [];
          let valueList = [];
          list.map(item => {
            nameList.push(item.objectName);
            valueList.push(item.data[0].value);
          });
          this.echartData = [nameList, valueList];
          this.sort(nameList, valueList);
        }
      });
    },
    // 排序
    sort(nameList, valueList) {
      let newList = [];
      nameList.map((item, index) => {
        let obj = {};
        obj.name = item;
        if (valueList[index] === null) {
          obj.value = 0;
        } else {
          obj.value = valueList[index];
        }
        newList.push(obj);
      });
      setTimeout(() => {
        if (this.ElSelect_2.value === 0) {
          newList = this._.orderBy(newList, ["value"], ["desc"]);
        } else if (this.ElSelect_2.value === 1) {
          newList = this._.orderBy(newList, ["value"], ["asc"]);
        }
        let arr1 = [];
        let arr2 = [];
        newList.forEach(item => {
          arr1.push(item.name);
          arr2.push(item.value.toFixed(2));
        });
        this.createEchart(arr1, arr2);
      }, 0);
    },
    // 点击节点树
    CetGiantTree_1_currentNode_out(val) {
      // if (val.childSelectState == 2) {
      //   this.$message.warning("你没有此节点的全部权限！");
      //   this.nodes = null;
      //   return;
      // }
      if (val.children == null) {
        this.$alert("该节点下面没有子节点！", "", {
          confirmButtonText: "确定",
          callback: action => {}
        });
        return;
      }
      this.title = val.name;
      this.nodes = val.children;
      this.request();
    },
    // 创建柱状图
    createEchart(nameList, valueList) {
      let flag = false;
      if (nameList.length > 9) {
        flag = true;
      } else {
        flag = false;
      }

      this.CetChart_1.options = {};
      this.CetChart_1.options = {
        toolbox: {
          top: 40,
          right: 30,
          feature: {
            saveAsImage: {
              title: $T("保存为图片")
            }
          }
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          left: "2%",
          right: "1%",
          bottom: "1%",
          top: "8%",
          containLabel: true
        },
        xAxis: [
          {
            type: "category",
            data: nameList,

            axisLabel: {
              interval: 0,
              rotate: flag ? 40 : 0
            },
            axisTick: {
              alignWithLabel: true
            }
          }
        ],
        yAxis: [
          {
            type: "value",
            name:
              this.status == 0
                ? `成本(${this.unitName})`
                : `单位指标成本(${this.unitName})`
          }
        ],
        series: [
          {
            name:
              this.status == 0
                ? `成本(${this.unitName})`
                : `单位指标成本(${this.unitName})`,
            type: "bar",
            barWidth: 30,
            data: valueList
          }
        ]
      };
    },
    // tabs切换
    handleClick(val) {
      let projectId = this.projectId;
      let nodes = this.nodes;

      this.costByNode(projectId, nodes);
    },
    ElSelect_1_change_out(val) {
      this.request();
    },
    // 排序的下拉框
    ElSelect_2_change_out(val) {
      let projectId = this.projectId;
      if (projectId == 0) return false;
      let echartData = this.echartData;
      let nameList = echartData[0];
      let valueList = echartData[1];
      this.sort(nameList, valueList);
    },
    // 类型的下拉框
    ElSelect_3_change_out(val) {
      this.propertysByDimId(val);
    },
    // 查询时段的下拉框
    ElSelect_4_change_out(val) {
      if (val == 14) {
        let time = this.value4;
        this.value3 = time + "-1";
        if (
          this.$moment(this.value3).startOf("moth").valueOf() <
          this.$moment().startOf("moth").valueOf()
        ) {
          this.CetButton_8.disable_in = false;
        } else {
          this.CetButton_8.disable_in = true;
        }
      } else {
        if (
          this.$moment(this.value4).startOf("year").valueOf() <
          this.$moment().startOf("year").valueOf()
        ) {
          this.CetButton_8.disable_in = false;
        } else {
          this.CetButton_8.disable_in = true;
        }
      }
      this.request();
    },
    // 年切换
    yearChange(val) {
      let yearNow = new Date().getFullYear();
      let time = val.getTime();
      time = new Date(time * 1).getFullYear();
      if (yearNow == time) {
        this.CetButton_8.disable_in = true;
      } else {
        this.CetButton_8.disable_in = false;
      }
      this.value4 = time.toString();
      this.request();
    },
    // 月切换
    monthChange(val) {
      let yearNow = new Date().getFullYear();
      let monthNow = new Date().getMonth() * 1 + 1;

      let time = val.getTime();
      let year = new Date(time * 1).getFullYear();
      let month = new Date(time * 1).getMonth() + 1;
      if (yearNow + "-" + monthNow == year + "-" + month) {
        this.CetButton_8.disable_in = true;
      } else {
        this.CetButton_8.disable_in = false;
      }

      this.value3 = (year + "-" + month).toString();
      this.request();
    },
    ElSelect_5_change_out(val) {
      //   this.timeId = val;
    },
    // 向前按钮
    CetButton_7_statusTrigger_out(val) {
      this.CetButton_8.disable_in = false;
      if (this.ElSelect_4.value == 17) {
        let time = this.value4;
        time--;
        this.value4 = time.toString();
      } else {
        let time = this.value3;
        let year = time.split("-")[0];
        let month = time.split("-")[1];
        if (month == 1) {
          time = year - 1 + "-12";
        } else {
          time = year + "-" + (month * 1 - 1);
        }
        this.value3 = time.toString();
      }
      this.request();
    },
    // 向后按钮
    CetButton_8_statusTrigger_out(val) {
      let yearNow = new Date().getFullYear();
      let monthNow = new Date().getMonth() * 1 + 1;
      monthNow = monthNow > 9 ? monthNow : "0" + monthNow;
      if (this.ElSelect_4.value == 17) {
        let time = this.value4;
        time++;
        if (time == yearNow) {
          this.CetButton_8.disable_in = true;
        }
        this.value4 = time.toString();
      } else {
        let time = this.value3;
        let year = time.split("-")[0];
        let month = time.split("-")[1];
        if (month == 12) {
          time = year * 1 + 1 + "-1";
          // if()
        } else {
          time = year + "-" + (month * 1 + 1);
        }
        let selectMonth =
          time.split("-")[1] > 9
            ? time.split("-")[1]
            : "0" + time.split("-")[1];
        let selectTime = time.split("-")[0] + selectMonth;
        console.log(selectTime);
        if (selectTime == yearNow.toString() + monthNow) {
          this.CetButton_8.disable_in = true;
        }
        this.value3 = time.toString();
      }
      this.request();
    },
    changeRadio1(val) {
      this.request();
    },
    changeRadio2(val) {
      this.request();
    },
    // 每次切换之后的重新请求
    request() {
      let projectId = this.projectId;
      if (projectId === 0) return false;
      setTimeout(() => {
        let nodes = this.nodes;
        let type = this.ElSelect_3.value;
        if (type === 0 || this.status == 1) {
          // 按区域
          this.costByNode(projectId, nodes);
        } else {
          this.costByDimId(projectId, nodes);
        }
      }, 0);
    }
  },
  created: function () {
    this.getTree(); // 获取节点树
  },
  mounted() {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.filter-tree {
  height: calc(100% - 48px);
}
.box {
  position: relative;
  border: none !important;
  // @include padding(J3 J4 J3 J4);
  // overflow-x: scroll;
  height: 620px;
  #main {
    width: calc(100% - 0px);
    height: 560px;
  }
}
.unit {
  position: absolute;
  top: 20px;
  left: 70px;
  z-index: 11;
}
.top1 {
  height: 32px;
  // right: 0;
  // display: flex;
  // align-items: center;
  // white-space: wrap;
}
.top2 {
  align-items: center;
  overflow: hidden;
}
.silde {
  margin: 10% 0;
  height: 45% !important;
}
</style>
