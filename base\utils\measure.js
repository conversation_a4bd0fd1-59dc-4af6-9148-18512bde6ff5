const div = (() => {
  const div = document.createElement("div");
  div.innerHTML = "";
  div.style.position = "fixed";
  div.style.visibility = "hidden";
  div.style.top = "-9999px";
  div.style.left = "-9999px";

  document.body.appendChild(div);

  return div;
})();

export function measureTextWidth(text, fontStyle) {
  div.innerText = text;
  div.style.fontStyle = fontStyle;
  return div.clientWidth;
}

//处理cycle
export function getCyclePreLevel(iLevel) {
  if (iLevel === 7) {
    return 12;
  } else if (iLevel === 12) {
    return 14;
  } else if ([13, 14, 17].includes(iLevel)) {
    return 17;
  }
  console.error("等级错误", iLevel);
  return 0;
}
