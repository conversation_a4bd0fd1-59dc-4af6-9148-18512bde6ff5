<template>
  <div>
    <CetDialog v-bind="CetDialog_1" append-to-body v-on="CetDialog_1.event">
      <div class="fullheight eem-cont-custom">
        <CetForm :data.sync="CetForm_1.data" v-bind="CetForm_1" v-on="CetForm_1.event">
          <el-form-item :label="typeName" prop="type">
            <ElSelect v-model="ElSelect_energytype.value" v-bind="ElSelect_energytype" v-on="ElSelect_energytype.event" :disabled="editData_in ? true : false">
              <ElOption
                v-for="item in ElOption_energytype.options_in"
                :key="item[ElOption_energytype.key]"
                :label="item[ElOption_energytype.label]"
                :value="item[ElOption_energytype.value]"
                :disabled="item[ElOption_energytype.disabled]"
              ></ElOption>
            </ElSelect>
          </el-form-item>
          <el-form-item label="基础单位：" prop="basicUnitSymbolName">
            <div>{{ CetForm_1.data.basicUnitSymbolName }}</div>
          </el-form-item>
          <el-form-item label="目标单位(英文)：" prop="uniten">
            <ElInput placeholder="请输入如:mWh" v-model.trim="CetForm_1.data.uniten" v-bind="ElInput_unit" v-on="ElInput_unit.event" onKeypress="javascript:if(event.keyCode == 32)event.returnValue = false;"></ElInput>
          </el-form-item>
          <el-form-item label="目标单位(中文)：" prop="unitcn">
            <ElInput placeholder="请输入如:兆瓦时" v-model.trim="CetForm_1.data.unitcn" v-bind="ElInput_unit" v-on="ElInput_unit.event" onKeypress="javascript:if(event.keyCode == 32)event.returnValue = false;"></ElInput>
          </el-form-item>
          <el-form-item label="转换系数：" prop="coef">
            <ElInputNumber v-model="CetForm_1.data.coef" v-bind="ElInputNumber_coef" v-on="ElInputNumber_coef.event"></ElInputNumber>
            <div v-show="CetForm_1.data.uniten && CetForm_1.data.coef && CetForm_1.data.basicUnitSymbolName">
              <span>{{`1${CetForm_1.data.uniten || "--"}=${CetForm_1.data.coef || "--"}${CetForm_1.data.basicUnitSymbolName || "--"}`}}</span>
            </div>
          </el-form-item>
        </CetForm>
      </div>
      <span slot="footer">
        <CetButton v-bind="CetButton_cancel" v-on="CetButton_cancel.event"></CetButton>
        <CetButton v-bind="CetButton_confirm" v-on="CetButton_confirm.event"></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import commonApi from "@/api/custom.js";
export default {
  name: "add",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    editData_in: {
      type: Object
    },
    projectEnergytype: {
      type: Array
    },
    tableData_in: {
      type: Array
    },
    unitClass_in: {
      type: Number
    }
  },
  data(vm) {
    return {
      // 1弹窗组件
      CetDialog_1: {
        title: "新增单位转换配置",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        width: "352px",
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "确认",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "关闭",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // 1表单组件
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "140px",
        rules: {
          type: [
            {
              required: true,
              message: "请选择能源类型",
              trigger: ["blur", "change"]
            }
          ],
          uniten: [
            {
              required: true,
              message: "请输入英文单位，如：mWh",
              trigger: ["blur", "change"]
            }
          ],
          unitcn: [
            {
              required: true,
              message: "请输入中文单位，如：兆瓦时",
              trigger: ["blur", "change"]
            }
          ],
          coef: [
            {
              required: true,
              message: "请输入单位转换系数",
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElSelect_energytype: {
        value: null,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_energytype_change_out
        }
      },
      ElOption_energytype: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // unit组件
      ElInput_unit: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      // coef组件
      ElInputNumber_coef: {
        min: 0.001,
        max: 999999999,
        step: 2,
        precision: 3,
        controlsPosition: "",
        placeholder: "请输入内容",
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      }
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    typeName() {
      return `${this.unitClass_in === 1 ? '能耗' : '产品'}类型：`;
    }
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      this.ElOption_energytype.options_in = this.projectEnergytype;
      vm.CetDialog_1.openTrigger_in = val;
      if (vm.editData_in) {
        vm.CetDialog_1.title = "编辑单位转换配置";
        const list = this.ElOption_energytype.options_in || [];
        if (this.unitClass_in === 1) {
          const clickVal = list.find(item => item.energytype === this.editData_in.type);
          vm.ElSelect_energytype.value = clickVal && clickVal.id;
        } else if (this.unitClass_in === 2) {
          const clickVal = list.find(item => item.producttype === this.editData_in.type);
          vm.ElSelect_energytype.value = clickVal && clickVal.id;
        } else if (this.unitClass_in === 4) {
          const clickVal = list.find(item => item.id === this.editData_in.type);
          vm.ElSelect_energytype.value = clickVal && clickVal.id;
        }
        vm.CetForm_1.data = vm.editData_in;
      } else {
        vm.CetDialog_1.title = "新增单位转换配置";
        vm.reset();
      }
      vm.CetForm_1.resetTrigger_in = new Date().getTime();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },
  methods: {
    reset() {
      this.ElSelect_energytype.value = null;
      this.CetForm_1.data = {
        type: "",
        unitMultiplierName: "",
        unitmultiplier: "",
        transformCoef: ""
      };
    },
    ElSelect_energytype_change_out(val) {
      let type = null;
      const list = this.ElOption_energytype.options_in || [];
      const ClickType = list.find(item => item.id === val);
      if (ClickType && this.unitClass_in === 1) {
        type = ClickType.energytype;
      } else if (ClickType && this.unitClass_in === 2) {
        type = ClickType.producttype;
      } else if (ClickType && this.unitClass_in === 4) {
        type = ClickType.id;
      }
      if (!type) {
        return;
      }
      this.CetForm_1.data.type = type;
      var vm = this;
      const params = {
        projectUnitClassify: this.unitClass_in,
        types: [type]
      };
      commonApi.getDefaultUnitSetting(params).then(res => {
        if (res.code === 0) {
          // vm.CetForm_1.data.basicUnitSymbolName = vm._.get(res, "data[0].basicUnitSymbolName", "");
          const basicUnitSymbolName = vm._.get(res, "data[0].basicUnitSymbolName", "");
          this.$set(vm.CetForm_1.data, "basicUnitSymbolName", basicUnitSymbolName);
        }
      });
    },
    addUnitTransition(val) {
      var data = [];
      data = [
        {
          type: val.type,
          projectunitclassify: this.unitClass_in,
          id: val.id,
          projectid: this.projectId,
          uniten: val.uniten,
          unitcn: val.unitcn,
          coef: val.coef
        }
      ];
      commonApi.addUnitTransition(data).then(response => {
        if (response.code === 0) {
          this.$message({
            type: "success",
            message: "保存成功！"
          });
          this.CetDialog_1.closeTrigger_in = new Date().getTime();
          // 刷新
          this.$emit("updata_out");
        }
      });
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = val;
    },
    CetForm_1_saveData_out(val) {
      this.addUnitTransition(val);
    }
  },
  activated: function () {}
};
</script>
<style lang="scss" scoped>
.line {
  margin: 0 5px;
}
.handel {
  cursor: pointer;
}
.handel.display {
  color: #72809b;
  cursor: context-menu;
}
.add {
  border: 2px solid #7189a2;
  border-radius: 3px;
  cursor: pointer;
  height: 50px;
  width: 50px;
  text-align: center;
  line-height: 50px;
  font-size: 30px;
}
.delete {
  font-size: 20px;
  margin-left: 7px;
  cursor: pointer;
}
</style>
