<template>
  <div class="page">
    <div
      style="page-break-after: always"
      v-for="(order, index) in printOrderList_in"
      :key="index"
    >
      <h1 style="text-align: center">
        {{ order.orderMsg.teamName || $T("电气班") }}-{{ $T("维修工单") }}
      </h1>
      <div style="display: flex">
        <div style="flex: 1.5; margin-bottom: 10px">
          <span>{{ $T("工单号") }}:</span>
          <span>{{ order.orderMsg.code || "--" }}</span>
        </div>
        <div style="flex: 1.5">
          <span>{{ $T("开始时间") }}:</span>
          <span>{{ filStartTime(order.orderMsg.createtime) }}</span>
        </div>
        <div style="flex: 1">
          <span>{{ $T("预计耗时") }}:</span>
          <span>
            {{ formTimeconsumeplan_out(order.orderMsg.timeconsumeplan) }}
          </span>
        </div>
      </div>
      <div style="display: flex; margin-bottom: 10px">
        <div style="flex: 1.5">
          <span>{{ $T("维修对象") }}:</span>
          <span>
            {{ filDevicelist_out(order.orderMsg.deviceplanrelationship_model) }}
          </span>
        </div>
        <div style="flex: 1.5">
          <span>{{ $T("维修方式") }}:</span>
          <span>{{ order.orderMsg.repairTypeName || "--" }}</span>
        </div>
        <div style="flex: 1">
          <span>{{ $T("等级") }}:</span>
          <span>{{ order.orderMsg.taskLevelName || "--" }}</span>
        </div>
      </div>
      <div style="display: flex; margin-bottom: 10px">
        <div style="flex: 1.5" class="text-overflow">
          <span>{{ $T("责任班组") }}:</span>
          <span>{{ order.orderMsg.teamName || "--" }}</span>
        </div>
        <div style="flex: 1.5">
          <span>{{ $T("工单来源") }}:</span>
          <span>{{ order.orderMsg.sourceTypeName || "--" }}</span>
        </div>
        <div style="flex: 1">
          <span>&nbsp;&nbsp;</span>
        </div>
      </div>
      <div style="margin-bottom: 30px">
        <span>{{ $T("故障描述") }}:</span>
        <span>{{ order.orderMsg.faultdescription || "--" }}</span>
      </div>
      <!-- 维修项 -->
      <h2>{{ $T("维修结果") }}</h2>
      <div style="height: 30px; line-height: 40px">
        <div style="float: left" :style="{ width: en ? '170px' : '90px' }">
          <span>{{ $T("维修记录") }}：</span>
        </div>
        <div
          style="
            float: left;
            width: calc(100% - 90px);
            height: 30px;
            border-bottom: 1px solid #000;
          "
          :style="{ width: en ? 'calc(100% - 170px)' : 'calc(100% - 90px)' }"
        ></div>
      </div>
      <div
        style="height: 30px; height: 30px; border-bottom: 1px solid #000"
      ></div>
      <div
        style="height: 30px; height: 30px; border-bottom: 1px solid #000"
      ></div>
      <div style="height: 30px; line-height: 40px; margin-top: 5px">
        <div
          style="float: left; position: relative; z-index: 99; background: #fff"
        >
          <span>{{ $T("消耗备件") }}{{ order.orderMsg.sparePartsName }}：</span>
        </div>
        <div style="height: 30px; border-bottom: 1px solid #000"></div>
      </div>
      <div
        style="height: 30px; height: 30px; border-bottom: 1px solid #000"
      ></div>
      <h2 style="margin-top: 30px">
        {{ $T("预案：（请在使用的预案上打√，否则在下面填写处理方案）") }}
      </h2>
      <table
        border="1"
        style="width: 100%; border-collapse: collapse; border-spacing: 0"
      >
        <tbody>
          <tr>
            <th align="center" valign="middle" style="width: 50px"></th>
            <th align="center" valign="middle" style="width: 50px">
              {{ $T("序号") }}
            </th>
            <th align="center" valign="middle" style="width: 150px">
              {{ $T("名称") }}
            </th>
            <th align="center" valign="middle">{{ $T("详细操作步骤") }}</th>
          </tr>
          <tr
            style="align-items: center"
            v-for="(item, ind) in order.eventPlanTable_in"
            :key="item.id"
          >
            <td align="center" valign="middle" style="width: 50px" key="select">
              <span
                style="
                  width: 14px;
                  height: 14px;
                  display: inline-block;
                  position: relative;
                  border: 1px solid #000;
                  border-radius: 2px;
                  box-sizing: border-box;
                "
              ></span>
            </td>
            <td align="center" valign="middle" style="width: 50px" key="index">
              <span>{{ ind + 1 }}</span>
            </td>
            <td
              align="center"
              valign="middle"
              style="width: 150px"
              key="groupName"
            >
              {{ item["groupName"] || "--" }}
            </td>
            <td align="center" valign="middle" style="" key="content">
              <span
                style="margin-right: 5px"
                v-for="(grouItem, index) in item.groupList"
                :key="index"
              >
                {{ index + 1 }}.{{ grouItem }}
              </span>
            </td>
          </tr>
          <tr>
            <td
              align="center"
              valign="middle"
              style="width: 50px; height: 60px"
            >
              <span
                style="
                  width: 14px;
                  height: 14px;
                  display: inline-block;
                  position: relative;
                  border: 1px solid #000;
                  border-radius: 2px;
                  box-sizing: border-box;
                "
              ></span>
            </td>
            <td align="center" valign="middle" style="width: 50px">
              {{ order.eventPlanTable_in.length + 1 }}
            </td>
            <td align="center" valign="middle" style="width: 150px">
              {{ $T("处理方案") }}
            </td>
            <td align="center" valign="middle"></td>
          </tr>
        </tbody>
      </table>
      <h2 style="margin-top: 70px">{{ $T("签字确认") }}：</h2>
      <div style="height: 30px; line-height: 40px; display: flex">
        <div style="flex: 1">
          <div style="float: left; width: 100px">
            <span>{{ $T("执行人签字") }}：</span>
          </div>
          <div
            style="
              float: left;
              height: 30px;
              border-bottom: 1px solid #000;
              width: 115px;
            "
          ></div>
        </div>
        <div style="flex: 2">
          <span style="float: left">{{ $T("完成时间") }}</span>
          <span
            style="
              float: left;
              display: inline-block;
              height: 30px;
              border-bottom: 1px solid #000;
            "
            :style="{ width: yearWidth }"
          ></span>
          <span style="float: left">{{ $T("年") }}</span>
          <span
            style="
              float: left;
              display: inline-block;
              height: 30px;
              border-bottom: 1px solid #000;
            "
            :style="{ width: otherWidth }"
          ></span>
          <span style="float: left">{{ $T("月") }}</span>
          <span
            style="
              float: left;
              display: inline-block;
              height: 30px;
              border-bottom: 1px solid #000;
            "
            :style="{ width: otherWidth }"
          ></span>
          <span style="float: left">{{ $T("日") }}</span>
          <span
            style="
              float: left;
              display: inline-block;
              height: 30px;
              border-bottom: 1px solid #000;
            "
            :style="{ width: otherWidth }"
          ></span>
          <span style="float: left">{{ $T("时") }}</span>
          <span
            style="
              float: left;
              display: inline-block;
              height: 30px;
              border-bottom: 1px solid #000;
            "
            :style="{ width: otherWidth }"
          ></span>
          <span style="float: left">{{ $T("分") }}</span>
        </div>
      </div>
      <div style="height: 30px; line-height: 40px; display: flex">
        <div style="flex: 1">
          <div style="float: left" :style="{ width: en ? '135px' : '115px' }">
            <span>{{ $T("巡检班长签字") }}：</span>
          </div>
          <div
            style="
              float: left;
              width: 100px;
              height: 30px;
              border-bottom: 1px solid #000;
            "
            :style="{ width: en ? '80px' : '100px' }"
          ></div>
        </div>
        <div style="flex: 2">
          <span style="float: left">{{ $T("确认时间") }}</span>
          <span
            style="
              float: left;
              display: inline-block;
              height: 30px;
              border-bottom: 1px solid #000;
            "
            :style="{ width: yearWidth }"
          ></span>
          <span style="float: left">{{ $T("年") }}</span>
          <span
            style="
              float: left;
              display: inline-block;
              height: 30px;
              border-bottom: 1px solid #000;
            "
            :style="{ width: otherWidth }"
          ></span>
          <span style="float: left">{{ $T("月") }}</span>
          <span
            style="
              float: left;
              display: inline-block;
              height: 30px;
              border-bottom: 1px solid #000;
            "
            :style="{ width: otherWidth }"
          ></span>
          <span style="float: left">{{ $T("日") }}</span>
          <span
            style="
              float: left;
              display: inline-block;
              height: 30px;
              border-bottom: 1px solid #000;
            "
            :style="{ width: otherWidth }"
          ></span>
          <span style="float: left">{{ $T("时") }}</span>
          <span
            style="
              float: left;
              display: inline-block;
              height: 30px;
              border-bottom: 1px solid #000;
            "
            :style="{ width: otherWidth }"
          ></span>
          <span style="float: left">{{ $T("分") }}</span>
        </div>
      </div>
      <div style="height: 30px; line-height: 40px; display: flex">
        <div style="flex: 1">
          <div style="float: left; width: 115px">
            <span>{{ $T("运值班长签字") }}：</span>
          </div>
          <div
            style="
              float: left;
              width: 100px;
              height: 30px;
              border-bottom: 1px solid #000;
            "
          ></div>
        </div>
        <div style="flex: 2">
          <span style="float: left">{{ $T("确认时间") }}</span>
          <span
            style="
              float: left;
              display: inline-block;
              height: 30px;
              border-bottom: 1px solid #000;
            "
            :style="{ width: yearWidth }"
          ></span>
          <span style="float: left">{{ $T("年") }}</span>
          <span
            style="
              float: left;
              display: inline-block;
              height: 30px;
              border-bottom: 1px solid #000;
            "
            :style="{ width: otherWidth }"
          ></span>
          <span style="float: left">{{ $T("月") }}</span>
          <span
            style="
              float: left;
              display: inline-block;
              height: 30px;
              border-bottom: 1px solid #000;
            "
            :style="{ width: otherWidth }"
          ></span>
          <span style="float: left">{{ $T("日") }}</span>
          <span
            style="
              float: left;
              display: inline-block;
              height: 30px;
              border-bottom: 1px solid #000;
            "
            :style="{ width: otherWidth }"
          ></span>
          <span style="float: left">{{ $T("时") }}</span>
          <span
            style="
              float: left;
              display: inline-block;
              height: 30px;
              border-bottom: 1px solid #000;
            "
            :style="{ width: otherWidth }"
          ></span>
          <span style="float: left">{{ $T("分") }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";

export default {
  name: "printOrder",
  components: {},
  props: {
    inputData_in: {
      type: Object
    },
    printOrderList_in: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  computed: {
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },
  data() {
    const language = window.localStorage.getItem("omega_language");
    return {
      orderMsg: {},
      sparePartsName: "",
      tableData: [
        // {
        //   id: 1,
        //   groupName: "预案1",
        //   groupList: []
        // },
        // {
        //   id: 2,
        //   groupName: "预案2",
        //   groupList: []
        // },
        // {
        //   id: 3,
        //   groupName: "预案3",
        //   groupList: []
        // },
        // {
        //   id: 4,
        //   groupName: "处理方案",
        //   groupList: []
        // }
      ],
      yearWidth: language === "en" ? "45px" : "60px",
      otherWidth: language === "en" ? "25px" : "40px"
    };
  },
  watch: {
    inputData_in: {
      handler(val) {
        console.log(val);
        this.orderMsg = this._.cloneDeep(val);
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    //过滤消耗备件
    filSparePartsName(val) {
      let sparePartsName = "";
      const spareParts =
        this._.get(val, "maintenanceContentObj.sparePartsReplaceRecords", []) ||
        [];
      spareParts.forEach((item, index) => {
        if (index) {
          sparePartsName += " , ";
        }
        sparePartsName += item.name + $T("备件") + item.number + item.unit;
      });
      if (sparePartsName) {
        sparePartsName = "(" + sparePartsName + ")";
      }
      return sparePartsName || "";
    },
    //过滤开始时间
    filStartTime(val) {
      if ([null, undefined, NaN].includes(val)) {
        return "--";
      } else {
        return this.$moment(val).format("YYYY-MM-DD HH:mm:ss");
      }
    },
    //过滤耗时
    formTimeconsumeplan_out(cellValue) {
      if (cellValue) {
        let str = "--";
        const format = "hh h mm min";
        if (cellValue || cellValue === 0) {
          const hour = Math.floor(cellValue / 3600000);
          const minute = Math.floor((cellValue - hour * 3600000) / 60000);
          if (
            format.indexOf("hh") !== -1 &&
            format.indexOf("mm") !== -1 &&
            format.indexOf("ss") === -1
          ) {
            str = format.replace(
              /(.*)hh(.*)mm(.*)/,
              "$1" + hour + "$2" + minute + "$3"
            );
          }
        }
        return str;
      } else {
        return "--";
      }
    },
    //过滤维修对象
    filDevicelist_out(val) {
      if (val && val.length) {
        const list = val;
        let inspectObject = "";
        list.forEach((item, index) => {
          if (index) {
            inspectObject += " , ";
          }
          inspectObject += item.devicename;
        });
        return inspectObject;
      } else {
        return "--";
      }
    },
    filData(val, type) {
      if ([null, undefined, NaN].includes(val)) {
        return "";
      } else if (type === "number") {
        return Number(val).toFixed(2);
      } else if (type === "date") {
        return this.$moment(val).format($T("YYYY 年 MM 月 DD 日  HH 时 mm 分"));
      } else {
        return val;
      }
    },
    //获取获取预案列表
    queryExpertEventPlan_out() {
      if (!this.inputData_in.faultscenariosid) {
        return;
      }
      var data = {
        scenariosId: this.inputData_in.faultscenariosid,
        limit: 3
      };
      customApi.queryExpertEventPlan(data).then(res => {
        if (res.code === 0) {
          const resData = res.data || [];
          const tabData = [];
          resData.forEach((item, index) => {
            item.solution = item.solution || "";
            tabData.push({
              id: index,
              groupName: item.name,
              groupList: item.solution.split("\n")
            });
          });
          this.tableData = tabData;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 20px;
}
</style>
