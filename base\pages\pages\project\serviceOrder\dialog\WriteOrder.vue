<template>
  <div class="writeOrderPage eem-common flex-column">
    <el-header height="auto" class="header eem-cont mbJ3">
      <div class="goBack mrJ1 lhHm" @click="goBack">
        <i class="el-icon-arrow-left"></i>
        {{ $T("返回") }}
      </div>
      <span class="lhHm common-title-H2">{{ $T("工单录入") }}</span>
    </el-header>
    <div class="eem-container flex-column flex-auto">
      <CetForm
        ref="writeOrderForm"
        :data.sync="CetForm_writeOrder.data"
        v-bind="CetForm_writeOrder"
        v-on="CetForm_writeOrder.event"
        style="width: 100%"
        class="el_form flex-auto"
      >
        <el-row :gutter="$J3">
          <el-col :span="8">
            <el-form-item :label="$T('实际开始时间')" prop="executetime">
              <el-date-picker
                v-model="CetForm_writeOrder.data.executetime"
                type="datetime"
                :editable="true"
                :pickerOptions="pickerOptions"
                format="yyyy-MM-dd HH:mm"
                value-format="timestamp"
                :placeholder="$T('选择日期')"
                style="width: 200px"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$T('实际完成时间')" prop="finishtime">
              <el-date-picker
                v-model="CetForm_writeOrder.data.finishtime"
                type="datetime"
                :editable="true"
                :pickerOptions="pickerOptions"
                format="yyyy-MM-dd HH:mm"
                value-format="timestamp"
                :placeholder="$T('选择日期')"
                style="width: 200px"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="mbJ3 el-form_row">
          <div class="mbJ3">{{ $T("维保项目") }}:</div>
          <div>
            <CetTable
              :data.sync="CetTable_1.data"
              :dynamicInput.sync="CetTable_1.dynamicInput"
              v-bind="CetTable_1"
              v-on="CetTable_1.event"
            >
              <template v-for="item in Columns_order">
                <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
              </template>
              <el-table-column
                :label="$T('实际消耗数量')"
                align="left"
                :show-overflow-tooltip="true"
                width="120"
              >
                <template slot-scope="scope">
                  <ElInputNumber
                    v-if="scope.row.sparepartid"
                    v-model="scope.row.consumeAmount"
                    v-bind="ElInputNumber_2"
                    v-on="ElInputNumber_2.event"
                    @blur="ElInput_1_change_out(scope.row, scope.$index)"
                  ></ElInputNumber>
                  <span v-else>--</span>
                </template>
              </el-table-column>
              <el-table-column v-bind="column_unit"></el-table-column>
              <el-table-column
                :label="$T('执行人')"
                align="left"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="scope">
                  <ElSelect
                    v-model="scope.row.executor"
                    v-bind="ElSelect_1"
                    v-on="ElSelect_1.event"
                    @change="ElSelect_1_change_out(scope.row, scope.$index)"
                  >
                    <ElOption
                      v-for="item in ElOption_1.options_in"
                      :key="item[ElOption_1.key]"
                      :label="item[ElOption_1.label]"
                      :value="item[ElOption_1.value]"
                      :disabled="item[ElOption_1.disabled]"
                    ></ElOption>
                  </ElSelect>
                </template>
              </el-table-column>
              <el-table-column
                :label="$T('维保结果')"
                align="left"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="scope">
                  <ElInput
                    v-model="scope.row.maintenanceResult"
                    v-bind="ElInput_1"
                    v-on="ElInput_1.event"
                    @blur="ElInput_1_change_out(scope.row, scope.$index)"
                  ></ElInput>
                </template>
              </el-table-column>
            </CetTable>
          </div>
        </el-row>
        <el-row :gutter="$J3" style="height: 180px">
          <el-col :span="8" class="fullheight">
            <el-form-item
              :label="$T('额外消耗备件')"
              label-width="100px"
              class="overflowFormItem fullheight flex-column"
            >
              <div
                class="list_item mbJ1 flex-row"
                v-for="(item, index) in listData"
                :key="index"
              >
                <ElSelect
                  v-model="listData[index]"
                  v-bind="ElSelect_2"
                  v-on="ElSelect_2.event"
                  class="fl mrJ1"
                >
                  <ElOption
                    v-for="item in ElOption_2.options_in"
                    :key="item[ElOption_2.key]"
                    :label="item[ElOption_2.label]"
                    :value="item"
                    :disabled="item[ElOption_2.disabled]"
                  ></ElOption>
                </ElSelect>
                <div class="fl flex-auto countBox">
                  <ElInputNumber
                    v-model="item.number"
                    v-bind="ElInputNumber_1"
                    v-on="ElInputNumber_1.event"
                  ></ElInputNumber>
                  <span class="form-item-unit">
                    {{ item.unit || $T("件") }}
                  </span>
                </div>
                <i
                  class="el-icon-delete lhHm deleteIcon"
                  @click="handleDeleteSparepart(index)"
                  style="cursor: pointer"
                ></i>
              </div>
              <!-- 添加备件按钮组件 -->
              <CetButton
                v-bind="CetButton_add"
                v-on="CetButton_add.event"
              ></CetButton>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="fullheight">
            <el-form-item
              :label="$T('上传附件')"
              label-width="80px"
              class="overflowFormItem fullheight flex-column"
            >
              <el-upload
                ref="upload"
                class="avatar-uploader"
                action="/eem-service/v1/common/uploadFile"
                :headers="{ Authorization: this.token }"
                :file-list="fileList"
                :multiple="false"
                :on-remove="handleRemove"
                :on-success="handleUploadSuccess"
                :on-error="errorUpload"
              >
                <el-button size="small" type="primary">
                  {{ $T("点击上传") }}
                </el-button>
                <div slot="tip" class="el-upload__tip">
                  {{ $T("支持图片、视频、文档") }}
                </div>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <span>{{ $T("处理描述") }}</span>
            <div class="mtJ1">
              <ElInput
                v-model="CetForm_writeOrder.data.handledescription"
                v-bind="ElInput_describe"
                v-on="ElInput_describe.event"
              ></ElInput>
            </div>
          </el-col>
        </el-row>
      </CetForm>
      <div class="mtJ3 clearfix">
        <CetButton
          class="fr mlJ1"
          v-bind="CetButton_submit"
          v-on="CetButton_submit.event"
        ></CetButton>
        <CetButton
          class="fr mlJ1"
          v-bind="CetButton_save"
          v-on="CetButton_save.event"
        ></CetButton>
        <CetButton
          class="fr mlJ1"
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </div>
    </div>
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom.js";

export default {
  name: "writeorder",
  components: {},
  props: {
    inputData_in: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  data(vm) {
    const checkExecutetime = (rule, value, callback) => {
      if (!vm.CetForm_writeOrder.data.executetime) {
        return callback(new Error($T("请选择时间")));
      } else if (
        vm.CetForm_writeOrder.data.executetime <=
        vm.CetForm_writeOrder.data.createtime
      ) {
        return callback(new Error($T("开始时间不能小于创建时间")));
      } else if (
        vm.CetForm_writeOrder.data.executetime &&
        vm.CetForm_writeOrder.data.finishtime &&
        vm.CetForm_writeOrder.data.executetime >=
          vm.CetForm_writeOrder.data.finishtime
      ) {
        return callback(new Error($T("开始时间不能大于完成时间")));
      } else {
        if (vm.CetForm_writeOrder.data.finishtime) {
          vm.$refs.writeOrderForm.$refs.cetForm.clearValidate("finishtime");
        }
        callback();
      }
    };
    const checkFinishtime = (rule, value, callback) => {
      if (!vm.CetForm_writeOrder.data.finishtime) {
        return callback(new Error($T("请选择时间")));
      } else if (
        vm.CetForm_writeOrder.data.executetime &&
        vm.CetForm_writeOrder.data.finishtime &&
        vm.CetForm_writeOrder.data.executetime >=
          vm.CetForm_writeOrder.data.finishtime
      ) {
        return callback(new Error($T("完成时间不能小于开始时间")));
      } else if (vm.CetForm_writeOrder.data.finishtime > new Date().getTime()) {
        return callback(new Error($T("完成时间不能大于当前时间")));
      } else {
        if (vm.CetForm_writeOrder.data.executetime) {
          vm.$refs.writeOrderForm.$refs.cetForm.clearValidate("executetime");
        }
        callback();
      }
    };
    return {
      // writeOrder表单组件
      CetForm_writeOrder: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        "label-position": "top",
        rules: {
          executetime: [
            {
              type: "date",
              required: true,
              validator: checkExecutetime,
              trigger: ["blur", "change"]
            }
          ],
          finishtime: [
            {
              type: "date",
              required: true,
              validator: checkFinishtime,
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_writeOrder_saveData_out
        }
      },
      pickerOptions: {
        ...common.pickerOptions_earlierThanYesterd11
      },
      // 维保项表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        "highlight-current-row": false,
        "max-height": "400",
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      Columns_order: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          //minWidth: "200",  //该宽度会自适应
          width: "60" //绝对宽度
          //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
        },
        {
          type: "",
          prop: "groupName",
          minWidth: 60,
          width: "",
          label: $T("分组"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "maintenanceTypeName", // 支持path a[0].b
          label: $T("维保方式"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: common.formatTextCol()
        },
        {
          prop: "content", // 支持path a[0].b
          label: $T("维保内容"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: common.formatTextCol()
        },
        {
          prop: "sparePartName", // 支持path a[0].b
          label: $T("零部件名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: common.formatTextCol()
        },
        {
          prop: "number", // 支持path a[0].b
          label: $T("预计消耗数量"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "120", //绝对宽度
          formatter: common.formatTextCol()
        }
      ],
      column_unit: {
        prop: "unit", // 支持path a[0].b
        label: $T("单位"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "80", //绝对宽度
        formatter: common.formatTextCol()
      },
      // 执行人组件
      ElSelect_1: {
        value: "",
        style: {
          width: "90%"
        },
        size: "small",
        multiple: true,
        "collapse-tags": true,
        event: {
          // change: this.ElSelect_1_change_out
        }
      },
      // 1组件
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // 执行结果组件
      ElInput_1: {
        value: "",
        style: {
          width: "90%"
        },
        size: "small",
        placeholder: $T("请输入"),
        event: {}
      },
      ElInputNumber_2: {
        ...common.check_numberFloat,
        value: "",
        style: {
          width: "90%"
        },
        size: "small",
        controls: false,
        placeholder: $T("请输入"),
        event: {}
      },
      listData: [], // 备件列表
      copySparepartsList: [], //备件列表备份
      // 备件组件
      ElSelect_2: {
        value: "",
        style: {
          width: "200px"
        },
        "value-key": "id",
        event: {
          "visible-change": this.ElSelect_2_visibleChange_out
        }
      },
      ElOption_2: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // 备件数量组件
      ElInputNumber_1: {
        ...common.check_numberFloat,
        value: "",
        style: {
          width: "100%"
        },
        controls: false,
        min: 0,
        placeholder: $T("请输入"),
        event: {}
      },
      // add组件
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("添加备件"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      fileList: [], // 上传文件列表
      imageUrl: null,
      // describe组件
      ElInput_describe: {
        value: "",
        style: {
          width: "100%"
        },
        type: "textarea",
        resize: "none",
        rows: 5,
        event: {}
      },
      // cancel组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // save组件
      CetButton_save: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_save_statusTrigger_out
        }
      },
      // submit组件
      CetButton_submit: {
        visible_in: true,
        disable_in: false,
        title: $T("提交"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_submit_statusTrigger_out
        }
      },
      buttonStatus: "" // 是保存还是提交
    };
  },
  watch: {
    inputData_in: {
      immediate: true,
      handler(val) {
        this.CetTable_1.data = this._.get(val, "maintenanceItemExtendVos", []);
        this.CetTable_1.data.forEach(item => {
          if (item.consumeAmount == null) {
            this.$delete(item, "consumeAmount");
          }
        });
        this.listData = this._.get(val, "sparePartNumbers", []) || [];
        this.fileList = this._.get(val, "attachmentList", []) || [];
        this.CetForm_writeOrder.data = this._.cloneDeep(val);
      }
    }
  },

  methods: {
    goBack() {
      // this.$router.go(-1);
      this.$emit("goBackDetail");
    },
    formatter(row, column, cellValue) {
      return cellValue || "--";
    },
    CetForm_writeOrder_saveData_out() {
      let flag = false;
      this.listData.forEach(item => {
        if (item.name && !item.number) {
          flag = true;
        }
      });
      if (flag) {
        return this.$message.warning($T("请填写备件数量!"));
      }
      // 附件信息
      const attachment = this.fileList.map(item => {
        return {
          name: item.name,
          url: item.url ? item.url : item.response && item.response.data
        };
      });
      // 过滤备件
      const filterListData = this.listData.filter(
        item => item.name && item.number
      );
      const param = {
        id: this.inputData_in.id,
        code: this.inputData_in.code,
        description: this.CetForm_writeOrder.data.handledescription,
        executetime: this.CetForm_writeOrder.data.executetime,
        finishtime: this.CetForm_writeOrder.data.finishtime,
        itemExtends: this.CetTable_1.data,
        sparePartNumbers: filterListData,
        attachment
      };
      // 保存工单信息
      if (this.buttonStatus === $T("保存")) {
        customApi.saveMaintenanceOrder(param).then(res => {
          if (res.code === 0) {
            this.$message.success($T("保存成功!"));
            this.$emit("WriteOrderConfirm", true);
          }
        });
      } else if (this.buttonStatus === $T("提交")) {
        // 提交工单信息
        customApi.submitMaintenanceOrder(param).then(res => {
          if (res.code === 0) {
            this.$message.success($T("提交成功!"));
            this.$emit("WriteOrderConfirm", true);
          }
        });
      }
    },
    // 1输出,方法名要带_out后缀
    ElSelect_1_change_out(row, index) {
      this.CetTable_1.data[index] = row;
    },

    // 执行结果输出,方法名要带_out后缀
    ElInput_1_change_out(row, index) {
      this.CetTable_1.data[index] = row;
    },
    ElSelect_2_visibleChange_out(val) {
      if (val) {
        const sparepartsList =
          this.copySparepartsList.filter(item => {
            const idArr = this.listData.map(item => item.id) || [];
            return !idArr.includes(item.id);
          }) || [];
        this.ElOption_2.options_in = this._.cloneDeep(sparepartsList);
      } else {
        this.ElOption_2.options_in = this._.cloneDeep(this.copySparepartsList);
      }
    },
    // add输出
    CetButton_add_statusTrigger_out() {
      if (!this.ElOption_2.options_in.length) {
        return this.$message.warning($T("无备件!"));
      }
      const len = this.listData.length;
      if (len >= this.copySparepartsList.length) {
        return this.$message.warning($T("消耗备件已超出数量!"));
      }
      this.listData.push({});
    },
    handleDeleteSparepart(index) {
      this.listData.splice(index, 1);
    },
    handleRemove(file, fileList) {
      this.fileList = this._.cloneDeep(fileList);
    },
    handleUploadSuccess(response, file, fileList) {
      if (response.code === 0) {
        this.$message.success($T("上传成功"));
        this.fileList = this._.cloneDeep(fileList);
      } else if (response.code !== 0) {
        // 根据uid清除掉上传失败的文件显示
        const uid = file.uid;
        const idx = this.$refs.upload.uploadFiles.findIndex(
          item => item.uid === uid
        );
        this.$refs.upload.uploadFiles.splice(idx, 1);
        var tips = "";

        if (response.data) {
          for (var i = 0; i < response.data.length; i++) {
            tips = tips + response.data[i] + "<br/>";
          }
        } else {
          tips = response.msg;
        }
        this.$message({
          type: "error",
          message: tips
        });
      }
    },
    errorUpload() {},
    CetButton_cancel_statusTrigger_out() {
      this.$emit("WriteOrderConfirm", false);
    },
    CetButton_save_statusTrigger_out() {
      this.buttonStatus = $T("保存");
      this.CetForm_writeOrder.localSaveTrigger_in = new Date().getTime();
    },
    CetButton_submit_statusTrigger_out() {
      this.buttonStatus = $T("提交");
      this.CetForm_writeOrder.localSaveTrigger_in = new Date().getTime();
    }
  },
  created: function () {
    // 查班组下的人员
    customApi.queryTeamUser(this.inputData_in.teamid).then(res => {
      if (res.code === 0) {
        this.ElOption_1.options_in = res.data;
      }
    });
    // 查维保目标下的备件库
    const device = this.inputData_in.deviceplanrelationship_model[0];
    const param = {
      id: device.device_id,
      modelLabel: device.device_label
    };
    customApi.getSparePartsStorageByDevice(param).then(res => {
      if (res.code === 0) {
        const data = this._.get(res, "data", []);
        this.copySparepartsList = this._.cloneDeep(data);
        this.ElOption_2.options_in = this._.cloneDeep(data);
      }
    });
  },
  mounted: function () {}
};
</script>
<style lang="scss" scoped>
.writeOrderPage {
  width: 100%;
  height: 100%;
  position: relative;
  .countBox {
    position: relative;
    @include margin_right(J1);
  }
}
.avatar-uploader :deep(.el-upload-list) {
  width: 420px;
}
.header {
  .goBack {
    display: inline-block;
    @include font_color(ZS);
    cursor: pointer;
  }
}
.lhHm {
  @include line_height(Hm);
}
.el_form {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.el-form_row {
  flex: 1;
}
.deleteIcon {
  @include font_color(Sta3);
}
.overflowFormItem {
  :deep(.el-form-item__content) {
    flex: 1;
    min-height: 0;
    overflow: auto;
  }
}
</style>
