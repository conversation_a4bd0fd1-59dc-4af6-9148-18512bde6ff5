﻿<template>
  <div class="page">
    <el-container class="fullheight flex-column">
      <div class="mbJ3 text-ellipsis" v-if="isEnergyData">
        <el-tooltip effect="light" :content="nodeName" placement="top-start">
          <span
            class="common-title-H2 lh32 text-ellipsis"
            style="display: inline-block; width: 250px"
          >
            {{ nodeName || "--" }}
          </span>
        </el-tooltip>
      </div>
      <div class="eem-container flex-column flex-auto eem-min-width">
        <div class="mbJ3">
          <el-tooltip
            effect="light"
            :content="nodeName"
            placement="top-start"
            v-if="!isEnergyData"
          >
            <span
              class="common-title-H2 lh32 text-ellipsis"
              style="display: inline-block; width: 150px"
            >
              {{ nodeName || "--" }}
            </span>
          </el-tooltip>
          <div class="fullwidth inline-block">
            <div class="fr">
              <el-input
                class="search-input fl mrJ1"
                :placeholder="$T('输入关键字以检索')"
                suffix-icon="el-icon-search"
                v-model="filterText"
                size="small"
                @blur="serchByKeyword_out(filterText)"
              />
              <TimeRange
                class="fl mrJ1"
                style="width: 420px"
                @change="handleChange_out"
                :val="defaultTime"
              ></TimeRange>
              <ElRadioGroup
                class="eem-radio fl mrJ1 lh32"
                style="height: 32px"
                v-model="ElRadioGroup_1.value"
                v-bind="ElRadioGroup_1"
                v-on="ElRadioGroup_1.event"
              >
                <ElRadio
                  v-for="item in ElRadioList_1.options_in"
                  :key="item[ElRadioList_1.key]"
                  :label="item[ElRadioList_1.label]"
                  :disabled="item[ElRadioList_1.disabled]"
                >
                  {{ item[ElRadioList_1.text] }}
                </ElRadio>
              </ElRadioGroup>
              <ElCheckbox
                class="fl lh32"
                style="height: 32px"
                v-model="ElCheckbox_1.value"
                v-bind="ElCheckbox_1"
                v-on="ElCheckbox_1.event"
              >
                {{ ElCheckbox_1.text }}
              </ElCheckbox>
              <CetButton
                class="fl mlJ1"
                v-bind="CetButton_6"
                v-on="CetButton_6.event"
              ></CetButton>
              <CetButton
                class="fl mlJ1"
                v-bind="CetButton_1"
                v-on="CetButton_1.event"
              ></CetButton>
              <CetButton
                class="fl mlJ1"
                v-bind="CetButton_2"
                v-on="CetButton_2.event"
              ></CetButton>
              <CetButton
                v-permission="'systemevent_confirm'"
                class="fl mlJ1"
                v-bind="CetButton_3"
                v-on="CetButton_3.event"
              ></CetButton>
            </div>
          </div>
        </div>
        <div class="flex-auto">
          <EnergyConsumptionEventTable
            :visibleTrigger_in="EnergyConsumptionEventTable.visibleTrigger_in"
            :closeTrigger_in="EnergyConsumptionEventTable.closeTrigger_in"
            :queryId_in="EnergyConsumptionEventTable.queryId_in"
            :queryNode_in="EnergyConsumptionEventTable.queryNode_in"
            :clickNode_in="clickNode"
            :page_in="page"
            :inputData_in="EnergyConsumptionEventTable.inputData_in"
            :confirmStatus_in="EnergyConsumptionEventTable.confirmStatus_in"
            :convergence_in="EnergyConsumptionEventTable.convergence_in"
            :exportTrigger_in="EnergyConsumptionEventTable.exportTrigger_in"
            :refreshTrigger_in="EnergyConsumptionEventTable.refreshTrigger_in"
            :searchKeyWords_in="filterText"
            :dateQuery="defaultTime"
            :updateEventData_in="EnergyConsumptionEventTable.updateEventData_in"
            @finishTrigger_out="EnergyConsumptionEventTable_finishTrigger_out"
            @finishData_out="EnergyConsumptionEventTable_finishData_out"
            @saveData_out="EnergyConsumptionEventTable_saveData_out"
            @detailTrigger_out="EnergyConsumptionEventTable_detailTrigger_out"
            @currentData_out="EnergyConsumptionEventTable_currentData_out"
            @selectionChange_out="
              EnergyConsumptionEventTable_selectionChange_out
            "
            @record_out="EnergyConsumptionEventTable_record_out"
            @confirmTrigger_out="EnergyConsumptionEventTable_confirmTrigger_out"
            @waveview_out="EnergyConsumptionEventTable_waveview_out"
            @totalCount_out="EnergyConsumptionEventTable_totalCount_out"
            @outputData_out="EnergyConsumptionEventTable_outputData_out"
            :energyConsumption="ENERGY_CONSUMPTION"
            :energyEfficiecy="ENERGY_EFFICIECY"
            :energyAll="ENERGY_ALL"
          >
            <el-footer height="32px" style="padding: 0px" class="mtJ1">
              <el-pagination
                class="fr"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="prev, pager, next, jumper"
                :total="totalCount"
              ></el-pagination>
            </el-footer>
          </EnergyConsumptionEventTable>
        </div>
      </div>
    </el-container>
    <EnergyConsumptionEventDetail
      :visibleTrigger_in="EnergyConsumptionEventDetail.visibleTrigger_in"
      :closeTrigger_in="EnergyConsumptionEventDetail.closeTrigger_in"
      :queryId_in="EnergyConsumptionEventDetail.queryId_in"
      :inputData_in="EnergyConsumptionEventDetail.inputData_in"
      @finishTrigger_out="EnergyConsumptionEventDetail_finishTrigger_out"
      @finishData_out="EnergyConsumptionEventDetail_finishData_out"
      @saveData_out="EnergyConsumptionEventDetail_saveData_out"
      @currentData_out="EnergyConsumptionEventDetail_currentData_out"
    />
    <EnergyConsumptionEventAdvancedQuery
      :visibleTrigger_in="EnergyConsumptionEventAdvancedQuery.visibleTrigger_in"
      :closeTrigger_in="EnergyConsumptionEventAdvancedQuery.closeTrigger_in"
      :queryId_in="EnergyConsumptionEventAdvancedQuery.queryId_in"
      :inputData_in="EnergyConsumptionEventAdvancedQuery.inputData_in"
      :keyWord_in="EnergyConsumptionEventAdvancedQuery.keyWord_in"
      :clickNode_in="clickNode"
      :treeData_in="treeData_in"
      @finishTrigger_out="EnergyConsumptionEventAdvancedQuery_finishTrigger_out"
      @finishData_out="EnergyConsumptionEventAdvancedQuery_finishData_out"
      @saveData_out="EnergyConsumptionEventAdvancedQuery_saveData_out"
      @currentData_out="EnergyConsumptionEventAdvancedQuery_currentData_out"
      :energyConsumption="ENERGY_CONSUMPTION"
      :energyEfficiecy="ENERGY_EFFICIECY"
      :energyAll="ENERGY_ALL"
    />
    <EnergyConsumptionEventConfirm
      :visibleTrigger_in="EnergyConsumptionEventConfirm.visibleTrigger_in"
      :closeTrigger_in="EnergyConsumptionEventConfirm.closeTrigger_in"
      :queryId_in="EnergyConsumptionEventConfirm.queryId_in"
      :inputData_in="EnergyConsumptionEventConfirm.inputData_in"
      @finishTrigger_out="EnergyConsumptionEventConfirm_finishTrigger_out"
      @finishData_out="EnergyConsumptionEventConfirm_finishData_out"
      @saveData_out="EnergyConsumptionEventConfirm_saveData_out"
      @currentData_out="EnergyConsumptionEventConfirm_currentData_out"
      @confirmedTrigger_out="EnergyConsumptionEventConfirm_confirmedTrigger_out"
    />
  </div>
</template>
<script>
import EnergyConsumptionEventTable from "./EnergyConsumptionEventTable.vue";
import EnergyConsumptionEventDetail from "./EnergyConsumptionEventDetail.vue";
import EnergyConsumptionEventAdvancedQuery from "./EnergyConsumptionEventAdvancedQuery.vue";
import EnergyConsumptionEventConfirm from "./EnergyConsumptionEventConfirm.vue";
import TimeRange from "eem-components/TimeRange";
export default {
  name: "EnergyConsumptionEvent",
  components: {
    EnergyConsumptionEventTable,
    EnergyConsumptionEventDetail,
    EnergyConsumptionEventAdvancedQuery,
    EnergyConsumptionEventConfirm,
    TimeRange
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    isEnergyData() {
      return this.$route.name === "energydata";
    }
  },

  props: {
    refreshTrigger_in: {
      type: Number
    },
    clickNode: {
      type: Object
    },
    treeData_in: {
      type: Array
    }
  },

  data() {
    return {
      ENERGY_CONSUMPTION: [701, 702], // 能耗报警
      ENERGY_EFFICIECY: [708, 709], // 能效报警
      ENERGY_ALL: [701, 702, 708, 709],
      defaultTime: [
        this.$moment().startOf("date").valueOf(),
        this.$moment().endOf("date").valueOf() + 1
      ],
      ElRadioGroup_1: {
        value: true,
        style: {},
        event: {
          change: this.ElRadioGroup_1_change_out
        }
      },
      ElRadioList_1: {
        options_in: [
          {
            id: true,
            text: $T("待处理事件")
          },
          {
            id: false,
            text: $T("已处理事件")
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      ElCheckbox_1: {
        value: false,
        text: $T("收敛事件"),
        disabled: false,
        event: {
          change: this.ElCheckbox_1_change_out
        }
      },
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: $T("高级查询"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetButton_3: {
        visible_in: true,
        disable_in: true,
        title: $T("批量确认"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      eventType: {
        runningEvent: $T("运行事件"),
        EnergyConsumptionEvent: $T("电能质量事件"),
        energyManagementEvent: $T("能源管理事件")
      },
      filterText: "",
      EnergyConsumptionEventTable: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        queryNode_in: {},
        queryId_in: 0,
        inputData_in: null,
        confirmStatus_in: true,
        convergence_in: false,
        updateEventData_in: []
      },
      EnergyConsumptionEventDetail: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      EnergyConsumptionEventAdvancedQuery: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        keyWord_in: "",
        clickNode_in: {},
        queryId_in: 0,
        inputData_in: null
      },
      EnergyConsumptionEventConfirm: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      CetButton_6: {
        visible_in: true,
        disable_in: false,
        title: $T("刷新"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_6_statusTrigger_out
        }
      },
      nodeName: "",
      page: {
        currentPage: 1,
        pageSize: 100
      },
      totalCount: 0,
      flagTotal: 0,
      length: 0
    };
  },
  watch: {
    refreshTrigger_in(val) {
      this.EnergyConsumptionEventTable.refreshTrigger_in =
        this._.cloneDeep(val);
    },
    clickNode: {
      deep: true,
      handler: function (val, oldVal) {
        if (!(this._.get(val, "id") && this._.get(val, "modelLabel"))) return;
        if (
          val.id === this._.get(oldVal, "id") &&
          val.modelLabel === this._.get(oldVal, "modelLabel")
        ) {
          return;
        }
        this.initPage();
        this.nodeName = val.name;
        this.EnergyConsumptionEventTable.refreshTrigger_in = Date.now();
      }
    },
    totalCount: {
      deep: true,
      handler: function (val, oldVal) {
        // this.page.currentPage = 1;
      }
    },
    $route(to, from) {
      if (
        to.name == "energyConsumptionEvent" ||
        to.name == "energyEfficiencyEvent"
      ) {
        if (
          from.name == "energyConsumptionEvent" ||
          from.name == "energyEfficiencyEvent"
        ) {
          this.filterText = "";
          this.EnergyConsumptionEventAdvancedQuery.keyWord_in = "";
          this.ElRadioGroup_1.value = true;
          this.EnergyConsumptionEventTable.confirmStatus_in = true;
          this.ElCheckbox_1.value = false;
          this.EnergyConsumptionEventTable.convergence_in = false;
        }
      }
    }
  },

  methods: {
    initPage() {
      this.flagTotal = 0;
      this.totalCount = 0;
      this.page.currentPage = 1;
    },
    CetButton_1_statusTrigger_out(val) {
      this.EnergyConsumptionEventAdvancedQuery.visibleTrigger_in =
        this._.cloneDeep(val);
    },
    CetButton_2_statusTrigger_out(val) {
      this.EnergyConsumptionEventTable.exportTrigger_in = this._.cloneDeep(val);
    },
    CetButton_3_statusTrigger_out(val) {
      this.EnergyConsumptionEventConfirm.visibleTrigger_in =
        this._.cloneDeep(val);
    },
    CetButton_6_statusTrigger_out(val) {
      this.EnergyConsumptionEventTable.refreshTrigger_in =
        this._.cloneDeep(val);
    },
    ElRadioGroup_1_change_out(val) {
      this.initPage();
      this.EnergyConsumptionEventTable.confirmStatus_in = this._.cloneDeep(val);
      this.EnergyConsumptionEventTable.refreshTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },

    ElCheckbox_1_change_out(val) {
      this.initPage();
      this.EnergyConsumptionEventTable.convergence_in = this._.cloneDeep(val);
      this.EnergyConsumptionEventTable.refreshTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    EnergyConsumptionEventAdvancedQuery_currentData_out(val) {},
    EnergyConsumptionEventAdvancedQuery_finishData_out(val) {},
    EnergyConsumptionEventAdvancedQuery_finishTrigger_out(val) {},
    EnergyConsumptionEventAdvancedQuery_saveData_out(val) {
      val.starttime = this.defaultTime[0];
      val.endtime = this.defaultTime[1];
      this.initPage();
      this.EnergyConsumptionEventTable.queryNode_in = this._.cloneDeep(val);
      let keyWord = val.description;
      this.filterText = keyWord ? this._.cloneDeep(keyWord) : "";
      var obj = {
        id: val.id,
        modelLabel: val.modelLabel,
        tree_id: val.modelLabel + "_" + val.id,
        name: val.name,
        children: val.children
      };
      this.nodeName = val.name;
      this.$emit("setClickNode", obj);
      this.EnergyConsumptionEventTable.refreshTrigger_in = Date.now();
    },
    EnergyConsumptionEventConfirm_currentData_out(val) {},
    EnergyConsumptionEventConfirm_finishData_out(val) {},
    EnergyConsumptionEventConfirm_finishTrigger_out(val) {},
    EnergyConsumptionEventConfirm_saveData_out(val) {},
    EnergyConsumptionEventConfirm_confirmedTrigger_out(eventList) {
      // this.EnergyConsumptionEventTable.updateEventData_in = this._.cloneDeep(eventList);
      this.EnergyConsumptionEventTable.refreshTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    EnergyConsumptionEventDetail_currentData_out(val) {},
    EnergyConsumptionEventDetail_finishData_out(val) {},
    EnergyConsumptionEventDetail_finishTrigger_out(val) {
      this.EnergyConsumptionEventTable.refreshTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    EnergyConsumptionEventDetail_saveData_out(val) {},
    EnergyConsumptionEventTable_confirmTrigger_out(val) {
      this.EnergyConsumptionEventConfirm.inputData_in = {
        eventlist: this._.cloneDeep(val)
      };
      this.EnergyConsumptionEventConfirm.visibleTrigger_in =
        new Date().getTime();
    },
    EnergyConsumptionEventTable_waveview_out(val) {
      this.$emit("waveview_out", this._.cloneDeep(val));
    },
    EnergyConsumptionEventTable_outputData_out(val) {
      this.length = val ? val.length : 0;
    },
    EnergyConsumptionEventTable_totalCount_out(val) {
      if (val) {
        this.totalCount = val;
        this.flagTotal = this.totalCount;
      } else {
        if (this.length == 0) this.totalCount = 0;
        else this.totalCount = this.flagTotal;
      }
    },
    EnergyConsumptionEventTable_currentData_out(val) {},
    EnergyConsumptionEventTable_detailTrigger_out(val) {
      this.EnergyConsumptionEventDetail.visibleTrigger_in =
        this._.cloneDeep(val);
    },
    EnergyConsumptionEventTable_finishData_out(val) {},
    EnergyConsumptionEventTable_finishTrigger_out(val) {},
    EnergyConsumptionEventTable_record_out(val) {
      this.EnergyConsumptionEventDetail.inputData_in = this._.cloneDeep(val);
      this.$emit("record_out", this._.cloneDeep(val));
    },
    EnergyConsumptionEventTable_saveData_out(val) {},
    EnergyConsumptionEventTable_selectionChange_out(val) {
      this.EnergyConsumptionEventConfirm.inputData_in = {
        eventlist: this._.cloneDeep(val)
      };
      this.CetButton_3.disable_in = !val.length;
    },
    serchByKeyword_out(val) {
      this.initPage();
      this.EnergyConsumptionEventAdvancedQuery.keyWord_in = val || "";
      this.$nextTick(() => {
        this.EnergyConsumptionEventTable.refreshTrigger_in =
          new Date().getTime();
      });
    },
    handleChange_out(val) {
      this.defaultTime = val;
      this.page.currentPage = 1;
      if (this.clickNode) {
        this.EnergyConsumptionEventTable.refreshTrigger_in = Date.now();
      }
    },
    //分页大小变化
    handleSizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.EnergyConsumptionEventTable.refreshTrigger_in = Date.now();
    },
    //分页当前页变化
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.EnergyConsumptionEventTable.refreshTrigger_in = Date.now();
    }
  },
  created: function () {},
  mounted: function () {
    this.initPage();
    this.nodeName = this.clickNode ? this.clickNode.name : null;
    if (
      !["energyConsumptionEvent", "energyEfficiencyEvent"].includes(
        this.$route.name
      )
    ) {
      this.ElRadioGroup_1.value = true;
      this.ElCheckbox_1.value = false;
      this.$nextTick(() => {
        this.page = this._.cloneDeep(this.page);
      });
    }
  },
  activated: function () {
    this.initPage();
    this.ElRadioGroup_1.value = true;
    this.ElCheckbox_1.value = false;
    if (this.$route.params?.keepParams?.timeParam) {
      let [startTime, endTime] = this.$route.params.keepParams.timeParam;
      //如果查询时间是一年，就转为当年的第一月
      if (
        this.$moment(startTime).format("MMDD") === "0101" &&
        this.$moment(endTime - 1).format("MMDD") === "1231"
      ) {
        endTime = this.$moment(startTime).endOf("month").valueOf() + 1;
      }
      this.defaultTime = [startTime, endTime];
    }
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}

.event-title {
  display: inline-block;
  line-height: 40px;
  vertical-align: middle;
}
.event-title-icon {
  font-size: 40px;
}
.search-input {
  width: 220px;
}
.device-Input {
  display: inline-block;
}
.header {
  @include margin_top(J3);
  @include margin_bottom(J3);
}
.head-label {
  @include line_height(H2);
  @include font_size(H2);
  @include font_weight(MD);
}
.eem-radio {
  :deep(.el-radio) {
    @include margin_right(J1);
    vertical-align: middle;
  }
}
</style>
