<template>
  <div class="page">
    <el-container class="fullheight">
      <el-aside width="320px" class="bg1 pJ3 brC">
        <!-- left组件 -->
        <CetGiantTree
          v-bind="CetGiantTree_left"
          v-on="CetGiantTree_left.event"
        ></CetGiantTree>
      </el-aside>
      <el-main style="height: 100%" class="bg1 pJ4 brC mlJ3">
        <div class="head">
          <span class="common-title-H3 lh32">{{ currentNode.name }}</span>
          <ElInput
            class="flex-end"
            v-model="ElInput_search.value"
            v-bind="ElInput_search"
            v-on="ElInput_search.event"
          ></ElInput>
          <customElSelect
            class="mlJ1"
            v-model="ElSelect_deviceType.value"
            v-bind="ElSelect_deviceType"
            v-on="ElSelect_deviceType.event"
            :prefix_in="$T('设备类型')"
          >
            <ElOption
              v-for="item in ElOption_deviceType.options_in"
              :key="item[ElOption_deviceType.key]"
              :label="item[ElOption_deviceType.label]"
              :value="item[ElOption_deviceType.value]"
              :disabled="item[ElOption_deviceType.disabled]"
            ></ElOption>
          </customElSelect>
          <customElSelect
            class="mlJ1"
            v-model="ElSelect_driveType.value"
            v-bind="ElSelect_driveType"
            v-on="ElSelect_driveType.event"
            :prefix_in="$T('驱动类型')"
          >
            <ElOption
              v-for="item in ElOption_driveType.options_in"
              :key="item[ElOption_driveType.key]"
              :label="item[ElOption_driveType.label]"
              :value="item[ElOption_driveType.value]"
              :disabled="item[ElOption_driveType.disabled]"
            ></ElOption>
          </customElSelect>
        </div>
        <div class="content">
          <CetTable
            :data.sync="CetTable_scheme.data"
            :dynamicInput.sync="CetTable_scheme.dynamicInput"
            v-bind="CetTable_scheme"
            v-on="CetTable_scheme.event"
          >
            <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
            <template v-for="(item, index) in ColumnList">
              <ElTableColumn :key="index" v-bind="item"></ElTableColumn>
            </template>
            <ElTableColumn v-bind="ElTableColumn_operation">
              <template slot-scope="scope">
                <el-link
                  class="mrJ3"
                  :underLine="false"
                  type="primary"
                  @click="handleEditScheme(scope.row)"
                >
                  {{ $T("编辑运行方案") }}
                </el-link>
                <el-link
                  :underLine="false"
                  type="primary"
                  @click="handleSchemeDetails(scope.row)"
                >
                  {{ $T("详情") }}
                </el-link>
              </template>
            </ElTableColumn>
          </CetTable>
          <div class="foot">
            <el-pagination
              class="flex-end"
              :current-page="currentPage"
              :page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-main>
    </el-container>
    <editOperationScheme
      v-bind="editOperationScheme"
      v-on="editOperationScheme.event"
    />
    <operationSchemeDetails
      ref="detailsDialog"
      v-bind="operationSchemeDetails"
      v-on="operationSchemeDetails.event"
    />
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import editOperationScheme from "./dialogs/editOperationScheme.vue";
import operationSchemeDetails from "./dialogs/operationSchemeDetails.vue";
import common from "@/utils/common.js";
export default {
  name: "deviceOperationPlanConfig",
  components: { editOperationScheme, operationSchemeDetails },
  data() {
    return {
      currentNode: {},
      gascompressorIds: [],
      // left组件
      CetGiantTree_left: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_left_currentNode_out //选中单行输出
        }
      },
      // search组件
      ElInput_search: {
        value: "",
        placeholder: $T("请输入内容"),
        "prefix-icon": "el-icon-search",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElInput_search_change_out
        }
      },
      // deviceType组件
      ElSelect_deviceType: {
        value: null,
        style: {
          width: "250px"
        },
        event: {
          change: this.ElSelect_deviceType_change_out
        }
      },
      // deviceType组件
      ElOption_deviceType: {
        options_in: [
          {
            id: null,
            text: $T("全部")
          }
        ].concat(this.$store.state.enumerations.compressortype),
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // driveType组件
      ElSelect_driveType: {
        value: null,
        style: {
          width: "250px"
        },
        event: {
          change: this.ElSelect_driveType_change_out
        }
      },
      // driveType组件
      ElOption_driveType: {
        options_in: [
          {
            id: null,
            text: $T("全部")
          }
        ].concat(
          this.$store.state.enumerations.workingprincipletype?.filter(item =>
            [2, 3].includes(item.id)
          )
        ),
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      currentPage: 1,
      pageSize: 20,
      total: 0,
      // scheme表格组件
      CetTable_scheme: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        border: false,
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      },
      // index组件
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: $T("序号"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "60" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ColumnList: [
        {
          prop: "name", // 支持path a[0].b
          label: $T("设备名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true
        },
        {
          prop: "compresssorType", // 支持path a[0].b
          label: $T("设备类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "140", //绝对宽度
          formatter: this.formatterCompressorType
        },
        {
          prop: "principleType", // 支持path a[0].b
          label: $T("驱动类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "140", //绝对宽度
          formatter: this.formatterPrincipleType
        },
        {
          prop: "compressedSeries", // 支持path a[0].b
          label: $T("压缩级数"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "140", //绝对宽度
          formatter: this.formatterCompressedSeries
        },
        {
          prop: "adjustableMode", // 支持path a[0].b
          label: $T("可调节方式"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formatterAdjustableMode
        },
        {
          prop: "ratedGascapacity", // 支持path a[0].b
          label: $T("额定气量(万立方米/日)"), //列名
          headerAlign: "left",
          align: "left",
          width: "180",
          showOverflowTooltip: true,
          formatter: common.formatNumberColumn
        }
      ],
      // operation组件
      ElTableColumn_operation: {
        // type: "", // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: "180" //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      editOperationScheme: {
        openTrigger_in: Date.now(),
        inputData_in: {},
        event: {
          saveData_out: this.CetDialog_pagedialog_saveData_out
        }
      },
      operationSchemeDetails: {
        openTrigger_in: Date.now(),
        inputData_in: {},
        event: {
          handleEditScheme: this.handleEditScheme
        }
      }
    };
  },
  watch: {},
  methods: {
    /**
     * 获取节点树
     */
    getTreeData() {
      const params = {
        rootID: 0,
        rootLabel: "project",
        subLayerConditions: [
          {
            modelLabel: "gasgatheringstation"
          },
          {
            modelLabel: "dehydratingstation"
          },
          {
            modelLabel: "gascompressor",
            filter: {
              composemethod: true,
              expressions: [
                {
                  limit: 1,
                  operator: "EQ",
                  prop: "compressortype",
                  tagid: 1
                },
                {
                  limit: [2, 3],
                  operator: "IN",
                  prop: "principletype",
                  tagid: 1
                }
              ]
            }
          }
        ],
        treeReturnEnable: true
      };
      customApi.getNodeTree(params).then(res => {
        const data = res.data || [];
        this.CetGiantTree_left.inputData_in = data;
        const flatData = this.flatTreeData(data);
        const node = flatData?.find(item => item.modelLabel === "project");
        this.CetGiantTree_left.selectNode = node || {};
      });
    },

    /**
     * 拍平节点树数据
     */
    flatTreeData(treeData) {
      const cloneData = this._.cloneDeep(treeData);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },

    /**
     * 点击节点
     */
    CetGiantTree_left_currentNode_out(val) {
      this.currentNode = _.cloneDeep(val);
      let gascompressorIds = [];
      this.getChildrenIds(val, gascompressorIds);
      this.gascompressorIds = gascompressorIds;
      this.currentPage = 1;
      this.queryTableData();
    },

    /**
     * 获取压缩机id
     */
    getChildrenIds(val, list) {
      if (val.modelLabel === "gascompressor") {
        list.push(val.id);
      } else {
        val.children?.forEach(item => {
          if (item.modelLabel === "gascompressor") {
            list.push(item.id);
          } else {
            this.getChildrenIds(item, list);
          }
        });
      }
    },

    /**
     * 查询表格数据
     */
    queryTableData() {
      const params = {
        name: this.ElInput_search.value,
        compressorType: this.ElSelect_deviceType.value,
        principleType: this.ElSelect_driveType.value,
        gascompressorIds: this.gascompressorIds,
        page: {
          index: (this.currentPage - 1) * this.pageSize,
          limit: this.pageSize
        }
      };
      customApi.queryDeviceOperationScheme(params).then(res => {
        this.CetTable_scheme.data = res.data || [];
        this.total = res.total || 0;
      });
    },

    /**
     * 分页大小变化
     */
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.queryTableData();
    },

    /**
     * 分页当前页变化
     */
    handleCurrentChange(val) {
      this.currentPage = val;
      this.queryTableData();
    },

    /**
     * 关键字搜索
     */
    ElInput_search_change_out(val) {
      this.currentPage = 1;
      this.queryTableData();
    },

    /**
     * 切换设备类型
     */
    ElSelect_deviceType_change_out(val) {
      this.currentPage = 1;
      this.queryTableData();
    },

    /**
     * 切换驱动类型
     */
    ElSelect_driveType_change_out(val) {
      this.currentPage = 1;
      this.queryTableData();
    },

    /**
     * 编辑运行方案
     */
    handleEditScheme(val) {
      this.editOperationScheme.inputData_in = val;
      this.editOperationScheme.openTrigger_in = Date.now();
    },

    /**
     * 编辑成功-刷新表格
     */
    CetDialog_pagedialog_saveData_out() {
      this.queryTableData();
      if (this.$refs.detailsDialog?.$refs.drawer?.drawerShow) {
        this.operationSchemeDetails.openTrigger_in = Date.now();
      }
    },

    /**
     * 运行方案详情
     */
    handleSchemeDetails(val) {
      this.operationSchemeDetails.inputData_in = val;
      this.operationSchemeDetails.openTrigger_in = Date.now();
    },

    /**
     * 设备类型转换
     */
    formatterCompressorType(row, column, cellValue) {
      const list = this.$store.state.enumerations.compressortype;
      const obj = list.find(item => item.id === cellValue);
      return obj?.text || "--";
    },

    /**
     * 驱动类型转换
     */
    formatterPrincipleType(row, column, cellValue) {
      const list = this.$store.state.enumerations.workingprincipletype;
      const obj = list.find(item => item.id === cellValue);
      return obj?.text || "--";
    },

    /**
     * 压缩级数转换
     */
    formatterCompressedSeries(row, column, cellValue) {
      const list = [
        { id: 0, text: $T("单级") },
        { id: 1, text: $T("双级") }
      ];
      const obj = list.find(item => item.id === cellValue);
      return obj?.text || "--";
    },

    /**
     * 可调节类型转换
     */
    formatterAdjustableMode(row, column, cellValue) {
      const list = this.$store.state.enumerations.gascompressoradjustablemode;
      const cellValueList = cellValue
        ?.map(item => {
          const obj = list.find(v => v.id === item);
          return obj?.text;
        })
        ?.filter(item => item);
      return _.isEmpty(cellValueList) ? "--" : cellValueList.join("，");
    }
  },
  mounted() {
    this.getTreeData();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.head {
  display: flex;
  height: 32px;
  line-height: 32px;
  margin-bottom: 16px;
}
.content {
  height: calc(100% - 96px);
}
.foot {
  display: flex;
  height: 32px;
  line-height: 32px;
  margin-top: 16px;
}
.flex-end {
  margin-left: auto;
  justify-content: flex-end;
}
</style>
