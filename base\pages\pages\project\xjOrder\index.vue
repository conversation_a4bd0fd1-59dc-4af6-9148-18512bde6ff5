<template>
  <div class="page eem-common">
    <div
      class="fullheight"
      v-show="!showDetail && !showRepairDetail1 && !showRepairDetail2"
    >
      <div class="fullheight flex-row">
        <div class="eem-aside fullheight flex-column" style="width: 240px">
          <div class="mbJ3">
            <span class="common-title-H2">{{ $T("工单状态统计") }}</span>
          </div>
          <div style="height: 200px; position: relative">
            <CetChart v-bind="CetChart_order"></CetChart>
            <div
              style="
                text-align: center;
                position: absolute;
                width: 100%;
                top: 80px;
              "
            >
              <div>
                <span class="chartLine">
                  <span class="text-overflow" style="font-size: 20px">
                    {{ totleNum }}
                  </span>
                  {{ $T("单") }}
                </span>
              </div>
              <div>
                <span>{{ $T("工单总数") }}</span>
              </div>
            </div>
          </div>
          <div class="flex-auto">
            <el-row
              :gutter="10"
              style="margin: 0px"
              class="ptJ1 pbJ1"
              v-for="(item, index) in orderList"
              :key="index"
            >
              <el-col :span="10" class="text-overflow">
                <i
                  class="echar-icon1 mrJ"
                  :style="{ background: item.itemStyle.color }"
                ></i>
                <el-tooltip :content="item.name" effect="light" placement="top">
                  <span>{{ item.name }}</span>
                </el-tooltip>
              </el-col>
              <el-col :span="7">
                <span>{{ filNumTotle(item.value, item.totle) }}%</span>
              </el-col>
              <el-col :span="7" class="text-overflow">
                <el-tooltip
                  :content="filNum(item.value) + $T('个')"
                  effect="light"
                  placement="top"
                >
                  <span>{{ filNum(item.value) + $T("个") }}</span>
                </el-tooltip>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="flex-auto fullheight mlJ3 flex-column">
          <div class="mbJ3">
            <el-tabs
              v-model="selectedMenu"
              @tab-click="handleTabClick_out(selectedMenu)"
              class="eem-tabs-custom"
            >
              <el-tab-pane
                v-for="menu in menus"
                :label="menu"
                :name="menu"
                :key="menu"
              ></el-tab-pane>
            </el-tabs>
          </div>
          <div class="flex-auto" style="overflow: auto">
            <div
              class="fullheight flex-column eem-min-width eem-container"
              style="min-width: 1350px"
            >
              <div class="mbJ3">
                <div class="fr">
                  <ElInput
                    size="small"
                    class="search-input fl"
                    suffix-icon="el-icon-search"
                    :placeholder="$T('输入工单号以检索')"
                    v-model="filterText"
                    v-bind="ElInput_keyword"
                    v-on="ElInput_keyword.event"
                  ></ElInput>
                  <customElSelect
                    :class="en && 'enSelect'"
                    size="small"
                    default-first-option
                    v-model="ElSelect_team.value"
                    v-bind="ElSelect_team"
                    v-on="ElSelect_team.event"
                    class="fl mlJ1"
                    :prefix_in="$T('责任班组')"
                  >
                    <ElOption
                      v-for="item in ElOption_team.options_in"
                      :key="item[ElOption_team.key]"
                      :label="item[ElOption_team.label]"
                      :value="item[ElOption_team.value]"
                    ></ElOption>
                  </customElSelect>
                  <customElSelect
                    size="small"
                    default-first-option
                    v-model="ElSelect_signin.value"
                    v-bind="ElSelect_signin"
                    v-on="ElSelect_signin.event"
                    class="fl mlJ1"
                    :prefix_in="$T('签到点')"
                  >
                    <ElOption
                      v-for="item in ElOption_signin.options_in"
                      :key="item[ElOption_signin.key]"
                      :label="item[ElOption_signin.label]"
                      :value="item[ElOption_signin.value]"
                    ></ElOption>
                  </customElSelect>
                  <div class="clearfix time fl mlJ1" :class="en && 'enTime'">
                    <TimeRange
                      @change="handleTime_out"
                      :val.sync="queryTime"
                    ></TimeRange>
                  </div>
                  <!-- refreshOrder按钮组件 -->
                  <CetButton
                    class="fl mlJ1"
                    v-bind="CetButton_refresh"
                    v-on="CetButton_refresh.event"
                  ></CetButton>
                  <!-- addOrder按钮组件 -->
                  <CetButton
                    v-permission="'inspectorworkorder_create'"
                    class="fl mlJ1"
                    v-bind="CetButton_addOrder"
                    v-on="CetButton_addOrder.event"
                  ></CetButton>
                  <!-- 导出巡检记录按钮组件 -->
                  <!-- <CetButton v-if="systemCfg.exportInspectorBtn" class="fr ml10" v-bind="CetButton_exportInspector" v-on="CetButton_exportInspector.event"></CetButton> -->
                  <el-dropdown
                    v-if="systemCfg.exportInspectorBtn"
                    class="fl mlJ1"
                    trigger="click"
                    @command="handleExport"
                  >
                    <el-button size="small" type="primary" plain width="196px">
                      {{ $T("导出") }}
                      <i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="a">
                        {{ $T("导出工单") }}
                      </el-dropdown-item>
                      <el-dropdown-item command="b">
                        {{ $T("导出巡检记录") }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <!-- refreshOrder按钮组件 -->
                  <CetButton
                    v-if="!systemCfg.exportInspectorBtn"
                    class="fr mlJ1"
                    v-bind="CetButton_export"
                    v-on="CetButton_export.event"
                  ></CetButton>
                  <!-- examine按钮组件 -->
                  <CetButton
                    v-if="CetButton_examine.visible_in"
                    class="fr mlJ1"
                    v-bind="CetButton_examine"
                    v-on="CetButton_examine.event"
                  ></CetButton>
                </div>
              </div>
              <div class="flex-auto">
                <CetTable
                  ref="CetTable"
                  class="eem-table-custom"
                  :data.sync="CetTable_1.data"
                  :dynamicInput.sync="CetTable_1.dynamicInput"
                  v-bind="CetTable_1"
                  v-on="CetTable_1.event"
                  row-key="id"
                  @selection-change="handleSelectionChange"
                >
                  <el-table-column
                    v-if="selectedMenu == $T('待审核')"
                    type="selection"
                    width="40"
                    align="center"
                    :selectable="canConfirm"
                  ></el-table-column>
                  <template v-for="(column, index) in Columns_Order">
                    <el-table-column
                      v-if="column.custom && column.custom === 'tag'"
                      v-bind="column"
                      :key="index"
                      class-name="font0 hand"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <el-tag
                          size="small"
                          class="text-middle font14"
                          :effect="column.tagEffect"
                          :type="
                            column.tagTypeFormatter
                              ? column.tagTypeFormatter(scope.row, scope.column)
                              : 'primary'
                          "
                          :color="
                            column.colorTypeFormatter
                              ? column.colorTypeFormatter(
                                  scope.row,
                                  scope.column
                                )
                              : '#fff'
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : scope.row[column.prop]
                          }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else-if="column.custom && column.custom === 'button'"
                      v-bind="column"
                      :key="index"
                      class-name="font0"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <span
                          class="clickformore"
                          @click.stop="
                            column.onButtonClick
                              ? column.onButtonClick(scope.row, scope.$index)
                              : void 0
                          "
                        >
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : column.text
                          }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-else
                      v-bind="column"
                      :key="index"
                      class-name="hand"
                    ></el-table-column>
                  </template>
                </CetTable>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <orderDetail
      v-show="showDetail"
      @goBack="goBack"
      :inputData_in="orderMsg_in"
    ></orderDetail>
    <repairOrderDetail1
      v-show="showRepairDetail1"
      @goBack="goBack"
      :isShow_in="true"
      :inputData_in="repairOrderMsg_in"
    ></repairOrderDetail1>
    <repairOrderDetail2
      v-show="showRepairDetail2"
      @goBack="goBack"
      :isShow_in="true"
      :inputData_in="repairOrderMsg_in"
    ></repairOrderDetail2>
    <CreateOrder
      @confirm_out="createOrder_confirm_out"
      :visibleTrigger_in="createOrder.visibleTrigger_in"
      :closeTrigger_in="createOrder.closeTrigger_in"
      :inputData_in="createOrder.inputData_in"
    />
    <toExamine
      :visibleTrigger_in="toExamine.visibleTrigger_in"
      :closeTrigger_in="toExamine.closeTrigger_in"
      :inputData_in="toExamine.inputData_in"
      :codes_in="toExamine.codes_in"
      @confirm_out="toExamine_confirm_out"
    />
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import common from "eem-utils/common.js";
import CreateOrder from "./dialog/CreateOrder";
import orderDetail from "./dialog/Detail";
import toExamine from "./dialog/toExamine";
import TimeRange from "eem-components/TimeRange";
import repairOrderDetail1 from "../wxOrder/dialog/DetailConfirm";
import repairOrderDetail2 from "../wxOrder/dialog/Detail";
import { measureTextWidth } from "eem-utils/measure.js";
const WORKSHEET_STATUS_TAG_COLORS = [
  "#fff",
  "#ff5a5a",
  "#7CFFB2",
  "#1b89b7",
  "#ffa32b",
  "#FF8A45",
  "#16a66c",
  "#58D9F9",
  "#9e9e9e"
];
const WORKSHEET_STATUS_TAG_NAMES = [
  "--",
  $T("待巡检"),
  $T("已派单"),
  $T("待审核"),
  $T("被退回"),
  $T("已废弃"),
  $T("已完成"),
  $T("异常单"),
  $T("已超时")
];
export default {
  name: "metrics",
  components: {
    CreateOrder,
    orderDetail,
    toExamine,
    TimeRange,
    repairOrderDetail1,
    repairOrderDetail2
  },
  data(vm) {
    return {
      totleNum: "",
      orderList: [],
      menus: [
        $T("待巡检"),
        $T("待审核"),
        $T("被退回"),
        $T("已超时"),
        $T("已完成"),
        $T("全部工单")
      ],
      selectedMenu: "",
      // exportInspector组件
      CetButton_exportInspector: {
        visible_in: true,
        disable_in: false,
        title: $T("导出巡检记录"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_exportInspector_statusTrigger_out
        }
      },
      // addOrder组件
      CetButton_addOrder: {
        visible_in: true,
        disable_in: false,
        title: $T("新建"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_addOrder_statusTrigger_out
        }
      },
      // refresh组件
      CetButton_refresh: {
        visible_in: true,
        disable_in: false,
        title: $T("刷新"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_refresh_statusTrigger_out
        }
      },
      // refresh组件
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      // examine组件
      CetButton_examine: {
        visible_in: false,
        disable_in: true,
        title: $T("批量审核"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_examine_statusTrigger_out
        }
      },
      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryInspectorWorkOrder",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [
            { name: "code_in", operator: "EQ", prop: "code" },
            { name: "teamId_in", operator: "EQ", prop: "teamId" },
            { name: "signPointId_in", operator: "EQ", prop: "signPointId" },
            { name: "abnormalTypes_in", operator: "EQ", prop: "abnormalTypes" },
            { name: "tenantId_in", operator: "EQ", prop: "tenantId" },
            { name: "startTime_in", operator: "EQ", prop: "startTime" },
            { name: "endTime_in", operator: "EQ", prop: "endTime" },
            {
              name: "workSheetStatus_in",
              operator: "EQ",
              prop: "workSheetStatus"
            }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {
          teamId_in: 0,
          signPointId_in: 0,
          tenantId_in: 0,
          code_in: "",
          abnormalTypes_in: [0],
          endTime_in: 0,
          startTime_in: 0,
          workSheetStatus_in: 1
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {},
        exportFileName: "",
        event: {},
        highlightCurrentRow: false
      },
      Columns_Order: [],
      // order组件
      CetChart_order: {
        //组件输入项
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "item"
          },
          legend: {
            show: false
          },
          series: [
            {
              name: "",
              type: "pie",
              radius: ["70%", "85%"],
              label: {
                show: false
              },
              data: []
            }
          ]
        }
      },
      // 关键字组件
      ElInput_keyword: {
        value: "",
        event: {
          change: this.ElInput_keyword_change_out
        }
      },
      filterText: "",
      // 责任班组下拉框
      ElSelect_team: {
        value: null,
        style: {
          width: "180px"
        },
        event: {
          change: this.teamChange_out
        }
      },
      ElOption_team: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name"
      },
      // 签到点下拉框
      ElSelect_signin: {
        value: null,
        style: {
          width: "180px"
        },
        event: {
          change: this.signinChange_out
        }
      },
      ElOption_signin: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name"
      },
      queryTime: [
        this.$moment().startOf("month").valueOf(),
        this.$moment().endOf("month").valueOf() + 1
      ],
      createOrder: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      toExamine: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        codes_in: []
      },
      showDetail: false,
      showRepairDetail1: false,
      showRepairDetail2: false,
      orderMsg_in: {},
      repairOrderMsg_in: {},
      selecteOrders: [],
      signinId: null
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    projectTenantId() {
      return this.$store.state.projectTenantId;
    },
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },
  watch: {},
  methods: {
    //关键字搜索
    ElInput_keyword_change_out(val) {
      this.CetTable_1.dynamicInput.code_in = val ? this._.cloneDeep(val) : "";
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    //班组下拉框
    teamChange_out(val) {
      this.CetTable_1.dynamicInput.teamId_in = val ? this._.cloneDeep(val) : "";
      this.CetTable_1.queryTrigger_in = new Date().getTime();
      this.queryWorkOrderCount_out();
    },
    //签到点下拉框
    signinChange_out(val) {
      this.CetTable_1.dynamicInput.signPointId_in = val
        ? this._.cloneDeep(val)
        : 0;
      this.CetTable_1.queryTrigger_in = new Date().getTime();
      this.queryWorkOrderCount_out();
    },
    //时间控件
    handleTime_out(val) {
      this.CetTable_1.dynamicInput.startTime_in = val[0]
        ? this._.cloneDeep(val[0])
        : "";
      this.CetTable_1.dynamicInput.endTime_in = val[1]
        ? this._.cloneDeep(val[1])
        : "";
      if (
        [null, NaN, undefined].includes(
          this.CetTable_1.dynamicInput.teamId_in
        ) ||
        [null, NaN, undefined].includes(
          this.CetTable_1.dynamicInput.signPointId_in
        )
      ) {
        return;
      }
      this.CetTable_1.queryTrigger_in = new Date().getTime();
      this.queryWorkOrderCount_out();
    },
    //多选框显示
    canConfirm(row, column) {
      const isOk = this._.get(row, "userTaskConfig.authorized", false);
      return !!isOk;
    },
    //点击表格多选框
    handleSelectionChange(val) {
      this.selecteOrders = this._.cloneDeep(val);
      if (val && val.length > 0) {
        this.CetButton_examine.disable_in = false;
      } else {
        this.CetButton_examine.disable_in = true;
      }
    },
    //切换tabs列表
    handleTabClick_out(val) {
      const Columns_Order = [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 60,
          label: "#",
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "code",
          minWidth: 100,
          width: "",
          label: $T("工单号"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "executetimeplan",
          minWidth: 120,
          width: "",
          label: $T("预计开始时间"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatDateCol("YYYY-MM-DD HH:mm:ss")
        },
        {
          type: "",
          prop: "timeconsumeplan",
          minWidth: "",
          width: "",
          label: $T("预计耗时"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formTimeconsumeplan_out
        },
        {
          type: "",
          prop: "inspectionSchemeName",
          minWidth: "",
          width: "",
          label: $T("巡检方案"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "inspectObject",
          minWidth: 100,
          width: "",
          label: $T("巡检对象"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.filDevicelist_out
        },
        {
          type: "",
          prop: "teamName",
          minWidth: "",
          width: "",
          label: $T("责任班组"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "signPointName",
          minWidth: "",
          width: "",
          label: $T("签到点"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ];
      if (val === $T("待巡检")) {
        this.CetTable_1.dynamicInput.workSheetStatus_in = 1;
        this.Columns_Order = [
          // eslint-disable-next-line camelcase
          ...Columns_Order,
          {
            type: "",
            prop: "",
            width: "120",
            label: $T("操作"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: null,
            custom: "button",
            fixed: "right",
            text: $T("详情"),
            buttonFormatter: null,
            onButtonClick: this.showOrderDetail
          }
        ];
      } else if (val === $T("待审核")) {
        this.CetTable_1.dynamicInput.workSheetStatus_in = 3;
        this.Columns_Order = [
          // eslint-disable-next-line camelcase
          ...Columns_Order,
          {
            type: "",
            prop: "abnormalreason",
            minWidth: 100,
            width: "",
            label: $T("异常原因"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: this.filAbnormal_out
          },
          {
            type: "",
            prop: "executor",
            minWidth: "",
            width: "",
            label: $T("执行人"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: this.filExecutor_out
          },
          {
            type: "",
            prop: "",
            width: "120",
            label: $T("操作"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            fixed: "right",
            custom: "button",
            text: $T("查看并审核"),
            buttonFormatter: null,
            onButtonClick: this.showOrderDetail,
            formatter: this.formatExamineBtn_out
          }
        ];
      } else if (val === $T("被退回")) {
        this.CetTable_1.dynamicInput.workSheetStatus_in = 4;
        this.Columns_Order = [
          // eslint-disable-next-line camelcase
          ...Columns_Order,
          {
            type: "",
            prop: "abnormalreason",
            minWidth: 100,
            width: "",
            label: $T("异常原因"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: this.filAbnormal_out
          },
          {
            type: "",
            prop: "executor",
            minWidth: "",
            width: "",
            label: $T("执行人"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: this.filExecutor_out
          },
          {
            type: "",
            prop: "",
            width: "120",
            label: $T("操作"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: null,
            custom: "button",
            fixed: "right",
            text: $T("详情"),
            buttonFormatter: null,
            onButtonClick: this.showOrderDetail
          }
        ];
      } else if (val === $T("已超时")) {
        this.CetTable_1.dynamicInput.workSheetStatus_in = 8;
        this.Columns_Order = [
          // eslint-disable-next-line camelcase
          ...Columns_Order,
          {
            type: "",
            prop: "executor",
            minWidth: "",
            width: "",
            label: $T("执行人"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: this.filExecutor_out
          },
          {
            type: "",
            prop: "",
            width: "120",
            label: $T("操作"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: null,
            custom: "button",
            fixed: "right",
            text: $T("详情"),
            buttonFormatter: null,
            onButtonClick: this.showOrderDetail
          }
        ];
      } else if (val === $T("已完成")) {
        this.CetTable_1.dynamicInput.workSheetStatus_in = 6;
        this.Columns_Order = [
          // eslint-disable-next-line camelcase
          ...Columns_Order,
          {
            type: "",
            prop: "abnormalreason",
            minWidth: 100,
            width: "",
            label: $T("异常原因"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: this.filAbnormal_out
          },
          {
            type: "",
            prop: "executor",
            minWidth: "",
            width: "",
            label: $T("执行人"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: this.filExecutor_out
          },
          {
            type: "",
            prop: "inspectResult",
            minWidth: "",
            width: "",
            label: $T("巡检结果"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: common.formatTextCol()
          },
          {
            type: "",
            prop: "",
            width: "120",
            label: $T("操作"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: null,
            custom: "button",
            fixed: "right",
            text: $T("详情"),
            buttonFormatter: null,
            onButtonClick: this.showOrderDetail
          }
        ];
      } else if (val === $T("全部工单")) {
        this.CetTable_1.dynamicInput.workSheetStatus_in = 0;
        this.Columns_Order = [
          // eslint-disable-next-line camelcase
          ...Columns_Order,
          {
            type: "",
            prop: "workSheetStatusName",
            minWidth: 80,
            width: measureTextWidth($T("待巡检"), "14px") + 46,
            label: $T("工单状态"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            custom: "tag",
            tagEffect: "light",
            colorTypeFormatter: this.statusColorTypeFormatter,
            // eslint-disable-next-line no-dupe-keys
            formatter: this.statusFormatter
          },
          {
            type: "",
            prop: "abnormalreason",
            minWidth: 100,
            width: "",
            label: $T("异常原因"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: this.filAbnormal_out
          },
          {
            type: "",
            prop: "executor",
            minWidth: "",
            width: "",
            label: $T("执行人"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: this.filExecutor_out
          },
          {
            type: "",
            prop: "inspectResult",
            minWidth: "",
            width: "",
            label: $T("巡检结果"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: common.formatTextCol()
          },
          {
            type: "",
            prop: "",
            width: "120",
            label: $T("操作"),
            sortable: false,
            headerAlign: "left",
            align: "left",
            showOverflowTooltip: true,
            formatter: null,
            custom: "button",
            fixed: "right",
            text: $T("详情"),
            buttonFormatter: null,
            onButtonClick: this.showOrderDetail
          }
        ];
      }
      if (val === $T("待审核")) {
        this.CetButton_examine.visible_in = true;
        this.CetButton_examine.disable_in = true;
      } else {
        this.CetButton_examine.visible_in = false;
      }

      this.queryWorkOrderCount_out();
      this.CetTable_1.queryTrigger_in = new Date().getTime();
      this.$refs.CetTable.$refs.cetTable.doLayout();
    },
    //初始化入口
    init() {
      if (this.signinId) {
        this.selectedMenu = $T("全部工单");
      } else {
        this.selectedMenu = $T("待巡检");
      }

      // this.queryWorkOrderCount_out();
      // this.CetTable_1.dynamicInput = {
      //   enabled_in: true,
      //   hide_in: false,
      //   code_in: "",
      //   teamId_in: null
      //   signPointId_in: null
      // };

      Promise.all([
        new Promise((resolve, reject) => {
          this.getTeam_out(resolve);
        }),
        new Promise((resolve, reject) => {
          this.getSignin_out(resolve);
        })
      ]).then(data => {
        this.handleTabClick_out(this.selectedMenu);
      });
    },
    //重新刷新页面
    reset() {
      if (this.selectedMenu) {
        this.handleTabClick_out(this.selectedMenu);
      }
    },
    // 获取班组列表信息
    getTeam_out(callback) {
      const _this = this;
      const params = {};
      customApi.queryInspectorTeamWithOutUser(params).then(res => {
        if (res.code === 0) {
          callback && callback();
          let arr = [
            {
              id: 0,
              name: $T("全部")
            }
          ];
          const data = this._.get(res, "data", []);
          arr = arr.concat(data);
          const teamId = this._.get(arr, "[0].id", null);
          _this.ElOption_team.options_in = _this._.cloneDeep(arr);
          _this.ElSelect_team.value = teamId;
          _this.CetTable_1.dynamicInput.teamId_in = teamId;
          // _this.teamChange_out(teamId);
        }
      });
    },
    // 获取签到点列表信息
    getSignin_out(callback) {
      const _this = this;
      const params = {};
      customApi.getSignInPointByProjectXJ(params).then(res => {
        if (res.code === 0) {
          callback && callback();
          let arr = [
            {
              id: 0,
              name: $T("全部")
            }
          ];
          const data = this._.get(res, "data", []);
          arr = arr.concat(data);
          // data = this.unique(data);
          let signpointid = this._.get(arr, "[0].id", 0);
          _this.ElOption_signin.options_in = _this._.cloneDeep(arr);
          if (_this.signinId && arr.find(i => i.id === this.signinId)) {
            signpointid = this.signinId;
          }
          _this.ElSelect_signin.value = signpointid;
          _this.CetTable_1.dynamicInput.signPointId_in = signpointid;
          // _this.signinChange_out(signpointid);
        }
      });
    },
    //数组去重
    unique(arr) {
      //定义常量 res,值为一个Map对象实例
      const res = new Map();

      //返回arr数组过滤后的结果，结果为一个数组
      //过滤条件是，如果res中没有某个键，就设置这个键的值为1
      return arr.filter(a => !res.has(a.id) && res.set(a.id, 1));
    },
    //查询工单数量
    queryWorkOrderCount_out() {
      if (
        [null, NaN, undefined].includes(this.ElSelect_team.value) ||
        [null, NaN, undefined].includes(this.ElSelect_signin.value)
      ) {
        return;
      }
      const params = {
        endTime: this.$moment(this.queryTime[1]).valueOf(),
        startTime: this.$moment(this.queryTime[0]).valueOf(),
        workSheetStatuses: [1, 2, 3, 4, 5, 6, 7, 8],
        taskType: 2,
        teamId: this.ElSelect_team.value,
        signPointId: this.ElSelect_signin.value
      };
      customApi.queryWorkOrderCount(params).then(res => {
        if (res.code === 0) {
          const list = res.data || [];
          let num1 = null;
          let num2 = null;
          let num3 = null;
          let num4 = null;
          let num5 = null;
          let totle = null;
          list.forEach(item => {
            if ([1].includes(item.workOrderStatus)) {
              num1 = item.count;
            } else if ([3].includes(item.workOrderStatus)) {
              num2 = item.count;
            } else if ([8].includes(item.workOrderStatus)) {
              num3 = item.count;
            } else if ([6].includes(item.workOrderStatus)) {
              num4 = item.count;
            } else if ([4].includes(item.workOrderStatus)) {
              num5 = item.count;
            }
            if ([1, 3, 4, 8, 6].includes(item.workOrderStatus) && item.count) {
              totle += item.count;
            }
          });
          this.totleNum = totle;
          const dataList = [
            {
              value: num1,
              totle: totle,
              name: $T("待巡检"),
              itemStyle: { color: "#ff5a5a" }
            },
            {
              value: num2,
              totle: totle,
              name: $T("待审核"),
              itemStyle: { color: "#1b89b7" }
            },
            {
              value: num5,
              totle: totle,
              name: $T("被退回"),
              itemStyle: { color: "#ffa32b" }
            },
            {
              value: num3,
              totle: totle,
              name: $T("已超时"),
              itemStyle: { color: "#9e9e9e" }
            },
            {
              value: num4,
              totle: totle,
              name: $T("已完成"),
              itemStyle: { color: "#16a66c" }
            }
          ];
          this.orderList = this._.cloneDeep(dataList);
          this.CetChart_order.options.series[0].data =
            this._.cloneDeep(dataList);
        }
      });
    },
    // exportInspector输出
    CetButton_exportInspector_statusTrigger_out(val) {
      const url = "/eem-service/v1/inspector/export";
      const data = this.CetTable_1.dynamicInput;
      const currentPage = this.$refs.CetTable.currentPage || 1;
      const pageSize = this.$refs.CetTable.pageSize || 100;
      // eslint-disable-next-line no-unused-vars
      const page = {
        index: (currentPage - 1) * pageSize,
        limit: pageSize
      };
      const params = {
        teamId: data.teamId_in,
        signPointId: data.signPointId_in,
        code: data.code_in,
        endTime: data.endTime_in,
        startTime: data.startTime_in,
        workSheetStatus: 6,
        // page: page,
        tenantId: this.projectTenantId
      };

      common.downExcel(url, params, this.token, this.projectId);
    },
    // addOrder输出
    CetButton_addOrder_statusTrigger_out(val) {
      this.createOrder.inputData_in = {};
      this.createOrder.visibleTrigger_in = new Date().getTime();
    },
    // 点击刷新工单列表按钮
    CetButton_refresh_statusTrigger_out(val) {
      this.reset();
    },
    handleExport(command) {
      // 导出导出工单
      if (command === "a") {
        this.CetButton_export_statusTrigger_out();
      }
      // 导出巡检记录
      if (command === "b") {
        this.CetButton_exportInspector_statusTrigger_out();
      }
    },
    // 点击导出按钮
    CetButton_export_statusTrigger_out(val) {
      const url = "/eem-service/v1/workorder/inspector/export";
      const data = this.CetTable_1.dynamicInput;
      const currentPage = this.$refs.CetTable.currentPage || 1;
      const pageSize = this.$refs.CetTable.pageSize || 100;
      // eslint-disable-next-line no-unused-vars
      const page = {
        index: (currentPage - 1) * pageSize,
        limit: pageSize
      };
      const params = {
        teamId: data.teamId_in,
        signPointId: data.signPointId_in,
        code: data.code_in,
        endTime: data.endTime_in,
        startTime: data.startTime_in,
        workSheetStatus: data.workSheetStatus_in,
        // page: page,
        tenantId: this.projectTenantId
      };

      common.downExcel(url, params, this.token, this.projectId);
    },
    CetButton_examine_statusTrigger_out(val) {
      const list = this.selecteOrders || [];
      const codeArr = [];
      list.forEach(item => {
        codeArr.push(item.code);
      });

      this.toExamine.inputData_in = this._.cloneDeep({});
      this.toExamine.codes_in = this._.cloneDeep(codeArr);
      this.toExamine.visibleTrigger_in = new Date().getTime();
    },
    toExamine_confirm_out(val) {
      this.reset();
    },
    createOrder_confirm_out() {
      this.queryWorkOrderCount_out();
      this.reset();
    },
    // 事件等级标签样式格式化
    statusColorTypeFormatter(row, column) {
      const cellValue = row.worksheetstatus || 0;
      return WORKSHEET_STATUS_TAG_COLORS[cellValue];
    },
    statusFormatter(row, column) {
      const cellValue = row.worksheetstatus || 0;
      return WORKSHEET_STATUS_TAG_NAMES[cellValue];
    },
    //展示详情页面
    showOrderDetail(row) {
      // this.orderMsg_in = this._.cloneDeep(row);
      // this.showDetail = true;
      new Promise((resolve, reject) => {
        this.getInspectorWorkOrder_out(row, resolve);
      }).then(data => {
        if (!data) {
          return;
        }
        this.showDetail = true;
      });
    },
    //详情页面返回
    goBack(val) {
      if (val && val === 3) {
        this.showRepairOrderDetail();
      } else if (val) {
        this.showDetail = false;
        this.showRepairDetail1 = false;
        this.showRepairDetail2 = false;
        this.reset();
      } else {
        this.showDetail = false;
        this.showRepairDetail1 = false;
        this.showRepairDetail2 = false;
        this.$nextTick(() => {
          this.$refs.CetTable.$refs.cetTable.doLayout();
        });
      }
    },
    //过滤巡检对象
    filDevicelist_out(row, column, cellValue, index) {
      if (
        row.deviceplanrelationship_model &&
        row.deviceplanrelationship_model.length
      ) {
        const list = row.deviceplanrelationship_model || [];
        let inspectObject = "";
        list.forEach((item, index) => {
          if (index) {
            inspectObject += " , ";
          }
          inspectObject += item.devicename;
        });
        row.inspectObject = inspectObject;
        return inspectObject;
      } else {
        return "--";
      }
    },
    //过滤异常原因
    filAbnormal_out(row, column, cellValue, index) {
      if (
        row.worksheetabnormalreason_model &&
        row.worksheetabnormalreason_model.length
      ) {
        const list = row.worksheetabnormalreason_model || [];
        let abnormal = "";
        list.forEach((item, index) => {
          if (index) {
            abnormal += " , ";
          }
          abnormal += item.typeName;
        });
        row.abnormalreason = abnormal;
        return abnormal;
      } else {
        return "--";
      }
    },
    //过滤执行人
    filExecutor_out(row, column, cellValue, index) {
      if (row.maintenancecontent) {
        const maintenancecontent = JSON.parse(row.maintenancecontent);
        const users = maintenancecontent.users || [];
        let executor = "";
        users.forEach((item, index) => {
          if (index) {
            executor += " , ";
          }
          executor += item.userName;
        });
        if (!executor) {
          executor = "--";
        }
        row.executor = executor;
        return executor;
      }
      return "--";
    },
    //过滤耗时
    formTimeconsumeplan_out(row, column, cellValue, index) {
      if (cellValue) {
        let str = "--";
        const format = "hh h mm min";
        if (cellValue || cellValue === 0) {
          const hour = Math.floor(cellValue / 3600000);
          const minute = Math.floor((cellValue - hour * 3600000) / 60000);
          if (
            format.indexOf("hh") !== -1 &&
            format.indexOf("mm") !== -1 &&
            format.indexOf("ss") === -1
          ) {
            str = format.replace(
              /(.*)hh(.*)mm(.*)/,
              "$1" + hour + "$2" + minute + "$3"
            );
          }
        }
        return str;
      } else {
        return "--";
      }
    },
    //过滤查看并审核操作列表
    formatExamineBtn_out(row, column, cellValue, index) {
      const authorized = this._.get(row, "userTaskConfig.authorized", false);
      if (authorized) {
        return $T("查看并审核");
      } else {
        return $T("详情");
      }
    },
    //过滤数字
    filNum(val) {
      if ([undefined, null, NaN].includes(val)) {
        return "--";
      } else {
        return val;
      }
    },
    //过滤百分比
    filNumTotle(val, totle = 1) {
      if ([undefined, null, NaN].includes(val)) {
        return "--";
      } else {
        return Number((val / totle) * 100).toFixed2(2);
      }
    },
    //获取维修工单信息，弹出工单详情弹框
    showRepairOrderDetail() {
      new Promise((resolve, reject) => {
        this.getRepairWorkOrder_out(resolve);
      }).then(data => {
        this.showDetail = false;
        this.showRepairDetail1 = false;
        this.showRepairDetail2 = false;
        if (!data) {
          return;
        }
        if ([1, 2].includes(this.repairOrderMsg_in.worksheetstatus)) {
          this.showRepairDetail1 = true;
        } else {
          this.showRepairDetail2 = true;
        }
      });
    },
    // 获取巡检工单详情信息
    getInspectorWorkOrder_out(row, callback) {
      var _this = this;
      if (!row.code) {
        callback && callback(false);
        return;
      }
      var data = {
        code: row.code
      };
      customApi.queryWorkOrderByCode(data).then(res => {
        if (res.code === 0) {
          const resData = _this._.get(res, "data", {}) || {};
          _this.orderMsg_in = this._.cloneDeep(resData);
          callback && callback(true);
        } else {
          callback && callback(false);
        }
      });
    },
    // 获取维修工单详情信息
    getRepairWorkOrder_out(callback) {
      var _this = this;
      if (!this.orderMsg_in.relatedcode) {
        callback && callback(false);
        return;
      }
      var data = {
        code: this.orderMsg_in.relatedcode
      };
      customApi.getRepairWorkOrder(data).then(res => {
        if (res.code === 0) {
          const resData = _this._.get(res, "data", {}) || {};
          _this.repairOrderMsg_in = this._.cloneDeep(resData);
          callback && callback(true);
        } else {
          callback && callback(false);
        }
      });
    }
  },
  activated: function () {
    this.showDetail = false;
    this.showRepairDetail1 = false;
    this.showRepairDetail2 = false;
    this.signinId = null;
    this.filterText = "";
    this.CetTable_1.dynamicInput.code_in = "";
    this.queryTime = [
      this.$moment().startOf("month").valueOf(),
      this.$moment().endOf("month").valueOf() + 1
    ];
    this.CetTable_1.dynamicInput.startTime_in = this.queryTime[0];
    this.CetTable_1.dynamicInput.endTime_in = this.queryTime[1];
    if (!this._.isEmpty(this.$route.params)) {
      const params = this.$route.params;
      this.signinId = params.id || null;
    }
    if (this.signinId) {
      this.selectedMenu = $T("全部工单");
      this.CetTable_1.dynamicInput.workSheetStatus_in = 0;
    } else {
      this.selectedMenu = $T("待巡检");
      this.CetTable_1.dynamicInput.workSheetStatus_in = 1;
    }
    this.$nextTick(() => {
      this.init();
    });
  }
};
</script>
<style lang="scss" scoped>
.page {
  height: 100%;
}
.search-input {
  width: 180px;
}
.device-Input {
  display: inline-block;
}
.check-box {
  display: inline-block;
}
.clickformore {
  cursor: pointer;
  @include font_color(ZS);
}
.querytime-type {
  margin-top: 12px;
  width: 260px;
}
.echar-icon1 {
  display: inline-block;
  width: 12px;
  height: 12px;
}

.time {
  width: 340px;
  display: flex;
  height: 32px;
}
.time-label {
  width: 100px;
  line-height: 32px;
}
.time-range {
  flex: 1;
}
.text-overflow {
  display: inline-block;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.eem-table-custom :deep(.el-tag) {
  @include font_color(T5, !important);
  border: none !important;
}
.chartLine {
  border-bottom: 1px solid;
  @include border_color(B1);
}
.enSelect {
  width: 220px !important;
}
.enTime {
  width: 360px;
}
</style>
<style>
.btn-custom-confirm-display {
  display: none;
}

.custom-date-picker-inspect .el-picker-panel__footer .el-button--text {
  display: none !important;
}
</style>
