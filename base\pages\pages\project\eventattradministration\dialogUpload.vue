<template>
  <div>
    <CetDialog
      v-bind="CetDialog_add"
      v-on="CetDialog_add.event"
      class="CetDialog min"
    >
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
      <div class="eem-cont-c1">
        <el-main style="height: 100%; padding: 0px">
          <ElInput
            v-model="ElInput_1.value"
            v-bind="ElInput_1"
            v-on="ElInput_1.event"
          >
            <div slot="suffix" class="uploadBtnClick" @click="uploadBtnClick">
              <i class="el-icon-more"></i>
            </div>
          </ElInput>
          <div class="uploadTitle">
            <span @click="download">{{ download_text_in }}</span>
            <el-popover
              placement="bottom"
              width="400"
              trigger="click"
              v-if="tipsData_in && tipsData_in.length > 0"
            >
              <div>
                <div v-for="(item, index) in tipsData_in" :key="index">
                  {{ item }}
                </div>
              </div>
              <i class="el-icon-question" slot="reference"></i>
            </el-popover>
          </div>
        </el-main>
        <el-upload
          class="elUpload"
          ref="upload"
          action=""
          :file-list="fileList"
          :auto-upload="false"
          :multiple="false"
          :on-change="uploadChange"
          :limit="1"
          :http-request="httpRequest"
        >
          <el-button
            slot="trigger"
            style="display: none"
            size="small"
            type="primary"
            ref="uploadBtn"
          >
            选取文件
          </el-button>
        </el-upload>
      </div>
    </CetDialog>
  </div>
</template>

<script>
export default {
  name: "dialogUpload",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    title_in: {
      type: String,
      default: ""
    },
    download_text_in: {
      type: String,
      default: ""
    },
    tipsData_in: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  data(vm) {
    return {
      fileList: [],
      CetDialog_add: {
        title: vm.title_in,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_add_open_out,
          close_out: this.CetDialog_add_close_out
        },
        width: "500px",
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: true,
        title: "上传",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        style: {
          width: "200px"
        },
        disabled: true,
        event: {}
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.openTrigger_in = val;
      this.ElInput_1.value = "";
      this.CetButton_confirm.disable_in = true;
      this.$nextTick(() => {
        this.$refs.upload.clearFiles();
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    },
    inputData_in(val) {}
  },

  methods: {
    CetDialog_add_open_out(val) {},
    CetDialog_add_close_out(val) {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.$refs.upload.submit();
    },
    uploadBtnClick() {
      this.$refs.upload.clearFiles();
      this.ElInput_1.value = "";
      this.CetButton_confirm.disable_in = true;
      this.$refs.uploadBtn.$el.click();
    },
    uploadChange(file, fileList) {
      if (fileList && fileList.length > 0) {
        this.ElInput_1.value = fileList[0].name;
        this.CetButton_confirm.disable_in = false;
      } else {
        this.ElInput_1.value = "";
        this.CetButton_confirm.disable_in = true;
      }
    },
    httpRequest(val) {
      this.$emit("uploadFile", val);
    },
    download() {
      this.$emit("download");
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.elUpload {
  display: none;
}
.uploadBtnClick {
  height: 100%;
  width: 20px;
  line-height: 40px;
  cursor: pointer;
}
.uploadTitle {
  // color: #2e95ff;
  margin-top: 10px;
  cursor: pointer;
}
.CetDialog {
  :deep(.el-dialog__body) {
    @include padding(J1);
    @include background_color(BG);
  }
}
</style>
