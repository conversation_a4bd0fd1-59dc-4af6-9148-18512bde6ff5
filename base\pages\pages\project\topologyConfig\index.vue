<template>
  <div class="page eem-common">
    <div class="fullheight flex-column">
      <div class="mbJ3 eem-cont">
        <CetButton
          class="fr"
          v-bind="CetButton_3"
          v-on="CetButton_3.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_4"
          v-on="CetButton_4.event"
          class="mrJ1 fr"
        ></CetButton>
        <CetButton
          v-if="ElSelect_1.value === 2 || ElSelect_1.value === 26"
          v-bind="CetButton_1"
          v-on="CetButton_1.event"
          class="mrJ1 fr"
        ></CetButton>
        <CetButton
          v-bind="CetButton_2"
          v-on="CetButton_2.event"
          class="mrJ1 fr"
        ></CetButton>
        <customElSelect
          class="mrJ1 energyType fr"
          :prefix_in="$T('能源类型')"
          v-model="ElSelect_1.value"
          v-bind="ElSelect_1"
          v-on="ElSelect_1.event"
        >
          <ElOption
            v-for="item in ElOption_1.options_in"
            :key="item[ElOption_1.key]"
            :label="item[ElOption_1.label]"
            :value="item[ElOption_1.value]"
            :disabled="item[ElOption_1.disabled]"
          ></ElOption>
        </customElSelect>
      </div>
      <div class="flex-auto eem-container">
        <div v-if="showTopology" class="fullheight">
          <TopologyChart v-bind="TopologyChartConfig" />
        </div>
        <div v-else style="height: 100%; width: 100%">
          <div class="noTopology">
            <div class="fl text">
              <div>{{ $T("暂无拓扑图") }}</div>
              <div>{{ $T("请导入拓扑图或更新") }}</div>
              <div>
                <CetButton
                  class="fl"
                  v-bind="CetButton_1"
                  v-on="CetButton_1.event"
                  v-if="ElSelect_1.value === 2 || ElSelect_1.value === 26"
                ></CetButton>
              </div>
            </div>
            <img class="fr img" src="./assets/u1104.png" alt="" />
          </div>
        </div>
      </div>
    </div>
    <dialogUpload
      :visibleTrigger_in="dialogUpload.visibleTrigger_in"
      :closeTrigger_in="dialogUpload.closeTrigger_in"
      :inputData_in="dialogUpload.inputData_in"
      @uploadFile="dialogUpload_uploadFile"
      @download="dialogUpload_download"
      v-bind="dialogUpload"
    />
    <dialogUpload
      :visibleTrigger_in="dialogUpload2.visibleTrigger_in"
      :closeTrigger_in="dialogUpload2.closeTrigger_in"
      :inputData_in="dialogUpload2.inputData_in"
      @uploadFile="dialogUpload2_uploadFile"
      @download="dialogUpload2_download"
      v-bind="dialogUpload2"
    />
    <dialogUpload
      :visibleTrigger_in="dialogUpload3.visibleTrigger_in"
      :closeTrigger_in="dialogUpload3.closeTrigger_in"
      :inputData_in="dialogUpload3.inputData_in"
      @uploadFile="dialogUpload3_uploadFile"
      @download="dialogUpload3_download"
      v-bind="dialogUpload3"
    />
  </div>
</template>

<script>
import common from "eem-utils/common";
import dialogUpload from "./dialogUpload";
import TopologyChart from "./TopologyChart";
import commonApi from "@/api/custom";
export default {
  name: "communicationTopology",
  components: {
    dialogUpload,
    TopologyChart
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    totalEnergyType() {
      return this.$store.state.systemCfg.totalEnergyType;
    },
    totalEnergyTypeCO2() {
      return this.$store.state.systemCfg.totalEnergyTypeCO2;
    },
    totalEnergyTypeC() {
      return this.$store.state.systemCfg.totalEnergyTypeC;
    }
  },
  data(vm) {
    return {
      showTopology: false,
      CetButton_1: {
        visible_in: false,
        disable_in: false,
        title: $T("导入"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_3: {
        visible_in: false,
        disable_in: false,
        title: $T("导入节点"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      CetButton_4: {
        visible_in: true,
        disable_in: false,
        title: $T("导入关系"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_4_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: $T("更新"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      TopologyChartConfig: {
        inputData_in: null
      },
      dialogUpload: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        title_in: "",
        download_text_in: "",
        accept_in: ".xlsx,.xls"
        // tipsData_in: [
        //   "供电拓扑样例文件字段说明：",
        //   "TOPOLOGY_DATA_ID：拓扑数据ID，用于区分不同批次的拓扑",
        //   "PATH_ID：业务路径ID，指示拓扑中的一条下行业务链路",
        //   "PATH_LAYER：路径层级，表示网络模型的层次，用L1/L2/L3/LS表示",
        //   "PATH_ROLE：路径中网元的角色，起点/中间节点/终点分别为S/M/E",
        //   "PATH_HOP：路径中网元的跳数，从0开始",
        //   "NE_NAME：网元名称，网元的唯一标识，和告警接口的neIdList中的neID对应",
        //   "NE_TYPE：网元类型，如RTN950。",
        //   "注：请上传UTF-8格式文件。"
        // ]
      },
      dialogUpload2: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        title_in: "",
        download_text_in: "",
        accept_in: ".xlsx,.xls",
        tipsData_in: ["变压器节点信息橙色字段为变压器分析功能需要的配置"],
        nodeTypes: []
      },
      dialogUpload3: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        title_in: "",
        download_text_in: "",
        accept_in: ".xlsx,.xls"
      },
      ElSelect_1: {
        value: "",
        style: {
          width: "200px"
        },
        event: {}
      },
      ElOption_1: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  watch: {
    "ElSelect_1.value": function (val, old) {
      if (val) {
        var energytypeObj = this.ElOption_1.options_in.find(
          item => item.energytype === val
        );
        var energytypName = energytypeObj.name;
        this.dialogUpload.title_in = $T("上传{0}拓扑", energytypName);
        this.dialogUpload.download_text_in = $T(
          "{0}拓扑样例下载",
          energytypName
        );
        this.dialogUpload2.title_in = $T("上传{0}节点", energytypName);
        this.dialogUpload2.download_text_in = $T(
          "{0}节点样例下载",
          energytypName
        );
        this.dialogUpload3.title_in = $T("上传{0}关系", energytypName);
        this.dialogUpload3.download_text_in = $T(
          "{0}关系样例下载",
          energytypName
        );
        this.getTopolpgy_out();
      }
      this.CetButton_2.disable_in = !val;
      this.CetButton_3.disable_in = !val;
      this.CetButton_4.disable_in = !val;
    }
  },

  methods: {
    CetButton_1_statusTrigger_out(val) {
      this.dialogUpload.visibleTrigger_in = new Date().getTime();
    },
    async CetButton_3_statusTrigger_out(val) {
      const templateName = this.getTemplateName();
      const res = await commonApi.topologyNodeTypes({
        projectId: this.projectId,
        templateName
      });
      this.dialogUpload2.nodeTypes = this._.get(res, "data");
      this.dialogUpload2.visibleTrigger_in = new Date().getTime();
    },
    CetButton_4_statusTrigger_out(val) {
      this.dialogUpload3.visibleTrigger_in = new Date().getTime();
    },
    CetButton_2_statusTrigger_out(val) {
      this.getTopolpgy_out();
    },
    dialogUpload_uploadFile(val) {
      if (this.ElSelect_1.value === 2) {
        const formData = new FormData();
        formData.append("file", val.file);
        var queryData = {
          projectId: this.projectId,
          templateName: "importTemplateConfig",
          energyType: 2
        };
        commonApi
          .importNodeAndConnection(formData, queryData)
          .then(response => {
            if (response.code === 0) {
              this.$message({
                type: "success",
                message: $T("导入成功！")
              });
              this.getTopolpgy_out();
              this.dialogUpload.closeTrigger_in = new Date().getTime();
            }
          });
      } else {
        const formData = new FormData();
        formData.append("file", val.file);
        commonApi.importTopolpgy(formData, this.projectId).then(response => {
          if (response.code === 0) {
            this.$message({
              type: "success",
              message: $T("导入成功！")
            });
            this.getTopolpgy_out();
            this.dialogUpload.closeTrigger_in = new Date().getTime();
          }
        });
      }
    },
    dialogUpload_download() {
      if (this.ElSelect_1.value === 2) {
        common.downExcelGET(
          "/eem-service/v1/topology/exportNodeAndConnection",
          {
            projectId: this.projectId,
            templateName: "importTemplateConfig",
            energyType: 2
          },
          this.token,
          this.projectId
        );
      } else {
        common.downExcelGET(
          "/eem-service/v1/project/pipeNetworkConnectionModel/singleProject",
          { projectId: this.projectId },
          this.token,
          this.projectId
        );
      }
    },
    getTemplateName() {
      let templateName = "other-device";
      if (this.ElSelect_1.value === 2) {
        templateName = "power-device";
      } else if (this.ElSelect_1.value === 26) {
        templateName = "communication-device";
      } else if (this.ElSelect_1.value === 16) {
        // 压缩空气
        templateName = "aircompressor-device";
      } else if (this.ElSelect_1.value === 24) {
        // 冷量
        templateName = "refrigeration-device";
      } else if (this.ElSelect_1.value === 7 || this.ElSelect_1.value === 46) {
        // 热水/热量
        templateName = "boiler-device";
      }
      return templateName;
    },
    dialogUpload2_uploadFile(val) {
      const formData = new FormData();
      formData.append("file", val.file);
      var queryData = {
        projectId: this.projectId,
        templateName: this.getTemplateName(),
        energyType: this.ElSelect_1.value
      };
      commonApi.importNodeAndConnection(formData, queryData).then(response => {
        if (response.code === 0) {
          this.$message({
            type: "success",
            message: $T("导入成功！")
          });
          this.getTopolpgy_out();
          this.dialogUpload2.closeTrigger_in = new Date().getTime();
        }
      });
    },
    dialogUpload2_download(val) {
      var queryData = {
        templateName: this.getTemplateName(),
        energyType: this.ElSelect_1.value
      };
      if (val) {
        queryData.modelLables = val;
      }
      common.downExcel(
        "/eem-service/v1/topology/exportNodeAndConnection?projectId=" +
          this.projectId,
        queryData,
        this.token,
        this.projectId
      );
    },
    dialogUpload3_uploadFile(val) {
      const formData = new FormData();
      formData.append("file", val.file);
      var queryData = {
        projectId: this.projectId,
        templateName: "other-relations",
        energyType: this.ElSelect_1.value
      };
      if (this.ElSelect_1.value === 2) {
        queryData.templateName = "power-relations";
      } else if (this.ElSelect_1.value === 26) {
        queryData.templateName = "communication-relations";
      }
      commonApi.importNodeAndConnection(formData, queryData).then(response => {
        if (response.code === 0) {
          this.$message({
            type: "success",
            message: $T("导入成功！")
          });
          this.getTopolpgy_out();
          this.dialogUpload3.closeTrigger_in = new Date().getTime();
        }
      });
    },
    dialogUpload3_download() {
      var queryData = {
        projectId: this.projectId,
        templateName: "other-relations",
        energyType: this.ElSelect_1.value
      };
      if (this.ElSelect_1.value === 2) {
        queryData.templateName = "power-relations";
      } else if (this.ElSelect_1.value === 26) {
        queryData.templateName = "communication-relations";
      }
      common.downExcelGET(
        "/eem-service/v1/topology/exportNodeAndConnection",
        queryData,
        this.token,
        this.projectId
      );
    },
    formatEventColor(item) {
      return "#ADEFF7";
    },
    getProjectEnergy() {
      commonApi.getProjectEnergy(this.projectId).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []).filter(
            item =>
              [
                this.totalEnergyType,
                this.totalEnergyTypeCO2,
                this.totalEnergyTypeC
              ].indexOf(item.energytype) === -1
          );
          if (data.length) {
            this.ElOption_1.options_in = data;
            if (
              this.ElSelect_1.value === this.ElOption_1.options_in[0].energytype
            ) {
              this.getTopolpgy_out();
            }
            this.ElSelect_1.value = this.ElOption_1.options_in[0].energytype;
          } else {
            this.$message({
              type: "warning",
              message: $T("请先配置项目能源类型")
            });
            this.ElOption_1.options_in = [];
            this.ElSelect_1.value = null;
          }
        }
      });
    },
    getTopolpgy_out() {
      const params = {
        projectId: this.projectId,
        energyType: this.ElSelect_1.value
      };
      commonApi.getTopolpgy(params).then(res => {
        const dataInfoList = this._.get(res, "data.dataInfo", []) || [];
        const dataLink = this._.get(res, "data.dataLink", []) || [];
        if (res.code === 0 && dataInfoList.length > 0 && dataLink.length > 0) {
          this.showTopology = true;
          const dataInfo = dataInfoList.map(item => {
            return {
              id: item.name,
              label: item.nodeName,
              style: {
                fill: this.formatEventColor(item)
              },
              nodeLabel: item.nodeLabel
            };
          });
          this.TopologyChartConfig.inputData_in = {
            nodes: dataInfo,
            edges: dataLink
          };
        } else {
          this.showTopology = false;
        }
      });
    }
  },
  created: function () {},
  activated: function () {
    this.getProjectEnergy();
    this.showTopology = false;
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.noTopology {
  width: 725px;
  height: 315px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  .img {
    height: 315px;
  }
  .text {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    & > div:nth-child(1) {
      @include font_color(ZS);
      @include font_size(H);
      font-weight: 700;
    }
    & > div:nth-child(2) {
      @include font_color(T4);
      font-size: 14px;
      font-weight: 400;
      margin-bottom: 20px;
    }
  }
}
.energyType {
  display: inline-block;
}
</style>
