<template>
  <div class="page">
    <div class="menu_group" style="padding:0px;height:0px;position:relative;">
      <div class="RateadministrationTitle">
        <div>
          <i
            :class="{ 'el-icon-document': selectedMenu == '成本构成', CostComposition: selectedMenu == '费率管理' }"
          ></i>
        </div>
        {{ selectedMenu }}
      </div>
      <el-radio-group
        v-model="selectedMenu"
        @change="handleTabClick_out"
        style="position:absolute;z-index:1;top:14px;"
        v-if="$route.params.costComposition"
      >
        <el-radio-button v-for="menu in menus" :label="menu" :key="menu">
          {{ menu }}
        </el-radio-button>
      </el-radio-group>
    </div>
    <el-main style="padding:58px 16px 11px 12px">
      <RateManage
        v-if="selectedMenu == '费率管理'"
        :visibleTrigger_in="RateManage.visibleTrigger_in"
        :closeTrigger_in="RateManage.closeTrigger_in"
        :queryId_in="RateManage.queryId_in"
        :inputData_in="RateManage.inputData_in"
        @finishTrigger_out="RateManage_finishTrigger_out"
        @finishData_out="RateManage_finishData_out"
        @saveData_out="RateManage_saveData_out"
        @currentData_out="RateManage_currentData_out"
      />
      <CostComposition
        v-if="selectedMenu == '成本构成'"
        :visibleTrigger_in="CostComposition.visibleTrigger_in"
        :closeTrigger_in="CostComposition.closeTrigger_in"
        :queryId_in="CostComposition.queryId_in"
        :inputData_in="CostComposition.inputData_in"
        @finishTrigger_out="CostComposition_finishTrigger_out"
        @finishData_out="CostComposition_finishData_out"
        @saveData_out="CostComposition_saveData_out"
        @currentData_out="CostComposition_currentData_out"
      />
    </el-main>
  </div>
</template>
<script>
import CostComposition from "./CostComposition.vue";
import RateManage from "./RateManage.vue";
export default {
  name: "Rateadministration",
  components: {
    CostComposition,
    RateManage
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data() {
    return {
      menus: ["费率管理", "成本构成"],
      selectedMenu: "",
      CostComposition: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      RateManage: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      }
    };
  },
  watch: {},

  methods: {
    handleTabClick_out(val) {},
    CostComposition_currentData_out(val) {},
    CostComposition_finishData_out(val) {},
    CostComposition_finishTrigger_out(val) {},
    CostComposition_saveData_out(val) {},
    RateManage_currentData_out(val) {},
    RateManage_finishData_out(val) {},
    RateManage_finishTrigger_out(val) {},
    RateManage_saveData_out(val) {}
  },

  created: function() {},
  activated: function() {
    this.selectedMenu = null;
    this.$nextTick(() => {
      if (this.$route.params.costComposition) {
        this.selectedMenu = "成本构成";
      } else {
        this.selectedMenu = "费率管理";
      }
    });
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.RateadministrationTitle {
  font-weight: 400;
  font-style: normal;
  font-size: 20px;
  // color: #0152d9;
  line-height: 58px;
  left: 20px;
  position: absolute;
  padding-left: 10px;
  & > div {
    display: inline-block;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #0152d9;
    position: relative;
    top: 10px;
    & > i {
      color: #fff;
      position: relative;
      top: -13px;
    }
    & > .CostComposition {
      display: inline-block;
      width: 14px;
      height: 14px;
      color: #fff;
      position: relative;
      top: -13px;
      left: 8px;
      background: url("./assets/u12476.png") center center;
    }
  }
}
</style>
