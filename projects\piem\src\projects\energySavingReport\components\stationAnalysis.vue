<template>
  <div class="stationAnalysis">
    <CetChart
      v-bind="CetChart_stationAnalysis"
      :initOptions="rendererType"
    ></CetChart>
    <div class="legend-box">
      <div class="legend-item">
        <div
          class="legend-line"
          :style="{
            background: colorList[(stationAnalysisData?.length || 0) + 1]
          }"
        ></div>
        <div class="fs14">{{ overviewList[0].name }}</div>
      </div>
      <div
        v-for="(item, index) in stationAnalysisData"
        :key="index"
        class="legend-item"
      >
        <div class="legend-bar" :style="{ background: colorList[index] }"></div>
      </div>
      <div class="fs14">{{ overviewList[1].name }}</div>
      <div
        class="legend-bar"
        :style="{ background: colorList[stationAnalysisData?.length || 0] }"
      ></div>
      <div class="fs14">{{ overviewList[2].name + "量" }}</div>
    </div>
  </div>
</template>

<script>
import common from "eem-utils/common";
const colorList = [
  "#29b061",
  "#00a2ff",
  "#ffc24c",
  "#e77fbc",
  "#00c1b2",
  "#8e43e7",
  "#ff7f43",
  "#0056ff",
  "#eb2f98",
  "#76daff",
  "#7fd0a0",
  "#1a914a",
  "#b38eca",
  "#585cbf",
  "#6bc1c3",
  "#12878d",
  "#579aff",
  "#2e63b1",
  "#ffc879",
  "#e68f11"
];
export default {
  name: "stationAnalysis",
  components: {},
  props: {
    stationAnalysisData: {
      type: Array,
      default: () => []
    },
    overviewList: {
      type: Array
    }
  },
  data() {
    return {
      colorList: colorList,
      rendererType: { renderer: "svg" },
      // stationAnalysis组件
      CetChart_stationAnalysis: {
        //组件输入项
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            },
            confine: true
          },
          grid: {
            top: 40,
            bottom: 10,
            left: 20,
            right: 0,
            containLabel: true
          },
          legend: {
            show: false
          },
          xAxis: {
            type: "category",
            axisTick: { show: false },
            axisLabel: {
              show: true,
              fontSize: 12,
              interval: 0,
              color: "#798492"
            },
            data: []
          },
          yAxis: {
            name: $T("单位(kWh)"),
            type: "value",
            axisLabel: {
              color: "#798492"
            },
            axisLine: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: "#E0E4E8",
                type: "dashed"
              }
            },
            nameTextStyle: {
              color: "#798492",
              padding: [0, 0, 10, 0]
            }
          },
          series: [
            {
              name: $T("优化后预计能耗"),
              type: "bar",
              colorBy: "data",
              color: colorList,
              stack: "总量",
              barWidth: 18,
              smooth: false,
              showSymbol: true,
              data: []
            },
            {
              name: $T("节能量"),
              type: "bar",
              color: colorList[4],
              stack: "总量",
              barWidth: 18,
              label: {
                show: true,
                position: "inside",
                formatter: "节能{c}kWh",
                fontSize: 12,
                fontWeight: "bold",
                color: "#242424"
              },
              smooth: false,
              showSymbol: true,
              data: []
            },
            {
              name: $T("月实际能耗"),
              type: "line",
              color: colorList[5],
              barWidth: 18,
              smooth: false,
              showSymbol: true,
              data: []
            }
          ]
        }
      }
    };
  },
  watch: {
    stationAnalysisData(val) {
      const list = val;
      const overviewList = this.overviewList;
      let xAxis = [];
      let seriesData = [];
      let saveSeriesData = [];
      let optimizationSeriesData = [];

      list.forEach(item => {
        xAxis.push(item.name);
        seriesData.push({
          name: item.name,
          value: common.formatNumberWithPrecision(item[overviewList[0].key], 2)
        });
        saveSeriesData.push({
          name: item.name,
          value: common.formatNumberWithPrecision(item[overviewList[2].key], 2)
        });
        optimizationSeriesData.push({
          name: item.name,
          value: common.formatNumberWithPrecision(item[overviewList[1].key], 2)
        });
      });
      this.CetChart_stationAnalysis.options.xAxis.data = xAxis;
      this.CetChart_stationAnalysis.options.series[0].data =
        optimizationSeriesData;
      this.CetChart_stationAnalysis.options.series[1].data = saveSeriesData;
      this.CetChart_stationAnalysis.options.series[2].data = seriesData;

      this.CetChart_stationAnalysis.options.yAxis.name =
        "单位" + "(" + overviewList[0].unit + ")";
      this.CetChart_stationAnalysis.options.series[1].label.formatter =
        overviewList[2].name + "{c}" + overviewList[2].unit;

      this.CetChart_stationAnalysis.options.series[0].name =
        overviewList[1].name;
      this.CetChart_stationAnalysis.options.series[1].name =
        overviewList[2].name + "量";
      this.CetChart_stationAnalysis.options.series[2].name =
        overviewList[0].name;

      this.CetChart_stationAnalysis.options.series[1].color =
        colorList[list.length];
      this.CetChart_stationAnalysis.options.series[2].color =
        colorList[list.length + 1];
    }
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
.stationAnalysis {
  width: 100%;
  height: 230px;
  position: relative;
}
.legend-box {
  position: absolute;
  top: 0;

  width: 100%;
  padding: 0 40px;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  z-index: 11;
  gap: 8px;
  justify-content: center;
  align-items: center;
  .legend-item {
    display: flex;
    height: 16px;
    align-items: center;
  }
  .legend-bar {
    width: 14px;
    height: 14px;
    border-radius: 3px;
  }
  .legend-line {
    height: 3px;
    width: 25px;
    margin-right: 8px;
  }
}
</style>
