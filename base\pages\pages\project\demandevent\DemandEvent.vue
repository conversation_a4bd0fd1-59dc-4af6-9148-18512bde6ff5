<template>
  <div class="page eem-common">
    <el-container class="fullheight padding0">
      <el-aside width="315px" class="eem-aside bg1 brC3">
        <CetTree :selectNode.sync="CetTree_1.selectNode" :checkedNodes.sync="CetTree_1.checkedNodes" v-bind="CetTree_1" v-on="CetTree_1.event"></CetTree>
      </el-aside>
      <el-container class="fullheight padding0 marginLeftJ2">
        <EnergyConsumptionEvent
          :clickNode="clickNode"
          :visibleTrigger_in="EnergyConsumptionEvent.visibleTrigger_in"
          :closeTrigger_in="EnergyConsumptionEvent.closeTrigger_in"
          :queryId_in="EnergyConsumptionEvent.queryId_in"
          :inputData_in="EnergyConsumptionEvent.inputData_in"
          :refreshTrigger_in="EnergyConsumptionEvent.refreshTrigger_in"
          @finishTrigger_out="EnergyConsumptionEvent_finishTrigger_out"
          @finishData_out="EnergyConsumptionEvent_finishData_out"
          @saveData_out="EnergyConsumptionEvent_saveData_out"
          @currentData_out="EnergyConsumptionEvent_currentData_out"
          @waveview_out="EnergyConsumptionEvent_waveview_out"
          @record_out="EnergyConsumptionEvent_record_out"
        />
      </el-container>
    </el-container>
  </div>
</template>
<script>
import EnergyConsumptionEvent from "./energyconsumptionevent/EnergyConsumptionEvent";
import { httping } from "@omega/http";
export default {
  name: "DemandEvent",
  components: {
    EnergyConsumptionEvent
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    enumerations() {
      var vm = this;
      return vm.$store.state.enumerations;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },

  data() {
    return {
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out,
          parentList_out: this.CetTree_1_parentList_out,
          checkedNodes_out: this.CetTree_1_checkedNodes_out,
          halfCheckNodes_out: this.CetTree_1_halfCheckNodes_out,
          allCheckNodes_out: this.CetTree_1_allCheckNodes_out
        }
      },
      EnergyConsumptionEvent: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      clickNode: null
    };
  },
  watch: {},

  methods: {
    CetTree_1_currentNode_out(val) {
      this.clickNode = val;
    },
    CetTree_1_parentList_out(val) {},
    CetTree_1_checkedNodes_out(val) {},
    CetTree_1_halfCheckNodes_out(val) {},
    CetTree_1_allCheckNodes_out(val) {},
    EnergyConsumptionEvent_currentData_out(val) {},
    EnergyConsumptionEvent_finishData_out(val) {},
    EnergyConsumptionEvent_finishTrigger_out(val) {},
    EnergyConsumptionEvent_saveData_out(val) {},
    EnergyConsumptionEvent_record_out(val) {
      var wavedata = this._.cloneDeep(val);
      wavedata.deviceId = wavedata.srcdeviceid;
      wavedata.waveTime = wavedata.waveformlogtime;
      this.WaveView.inputData_in = wavedata;
    },
    EnergyConsumptionEvent_waveview_out(val) {
      this.WaveView.visibleTrigger_in = this._.cloneDeep(val);
    },
    getTreeData() {
      this.CetTree_1.inputData_in = [];
      this.copyTreeData = [];
      httping({
        url: "/eem-service/v1/demand/node/getDemandNodes",
        method: "POST",
        data: [this.projectId]
      }).then(response => {
        if (response.code == 0 && response.data && response.data.length) {
          if (response.data[0].children && response.data[0].children.length) {
            this.CetTree_1.selectNode = {
              id: response.data[0].children[0].id,
              modelLabel: response.data[0].children[0].modelLabel,
              tree_id: response.data[0].children[0].modelLabel + "_" + response.data[0].children[0].id
            };
          }
          this.CetTree_1.inputData_in = this._.cloneDeep(response.data);
        }
      });
    }
  },
  created: function () {},
  activated: function () {
    this.getTreeData();
    // 现在只保留能源事件查询
    // this.EnergyConsumptionEvent.refreshTrigger_in = new Date().getTime();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}

.device-Input {
  display: inline-block;
}

.check-box {
  display: inline-block;
}

.event-title {
  display: inline-block;
  line-height: 40px;
  vertical-align: middle;
}

.event-title-icon {
  font-size: 40px;
}

.search-input {
  width: 200px;
}

.menu_group {
  text-align: center;
  padding: 5px 0;
}
.pr10 {
  padding-right: 10px !important;
}
.content {
  height: 100%;
  box-sizing: border-box;
}
</style>
<style lang="scss">
.menu_group .el-radio-button__inner {
  padding: 7px 20px;
  // border: 1px solid #0066ff;
}
</style>
