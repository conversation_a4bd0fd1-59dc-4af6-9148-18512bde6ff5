<?xml version="1.0" encoding="UTF-8"?>
<svg width="62px" height="62px" viewBox="0 0 62 62" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 54备份</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="1638" height="332" rx="16"></rect>
        <linearGradient x1="20.0355775%" y1="48.3029667%" x2="89.05853%" y2="52.1013676%" id="linearGradient-3">
            <stop stop-color="#AACEFF" offset="0%"></stop>
            <stop stop-color="#BAD8FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="36.6824789%" y1="22.8474679%" x2="67.3593467%" y2="83.6218817%" id="linearGradient-4">
            <stop stop-color="#AACEFF" offset="0%"></stop>
            <stop stop-color="#BAD8FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="20.0355775%" y1="22.8474679%" x2="89.05853%" y2="83.6218817%" id="linearGradient-5">
            <stop stop-color="#AACEFF" offset="0%"></stop>
            <stop stop-color="#BAD8FF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="节能策略-电费优化-基本电费" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="用能策略-电费优化-基本电费诊断" transform="translate(-1152.000000, -252.000000)">
            <rect id="矩形" fill="#F6F6F7" x="-1" y="1" width="1935" height="1198"></rect>
            <g id="编组" transform="translate(280.000000, 196.000000)">
                <mask id="mask-2" fill="white">
                    <use xlink:href="#path-1"></use>
                </mask>
                <use id="蒙版" fill="#FFFFFF" xlink:href="#path-1"></use>
            </g>
            <rect id="矩形" fill="#F6F6F7" x="672" y="212" width="1231" height="142" rx="8"></rect>
            <g id="编组-54备份" transform="translate(1152.000000, 252.000000)">
                <rect id="矩形备份-7" fill-opacity="0.397863855" fill="#3E77FC" opacity="0.157976017" x="0" y="0" width="62" height="62" rx="8"></rect>
                <g id="编组-25" transform="translate(15.000000, 16.000000)">
                    <circle id="椭圆形" fill="#3E77FC" cx="10" cy="10" r="10"></circle>
                    <rect id="矩形" fill-opacity="0.4" fill="#C2E2FF" x="15" y="1" width="8" height="2"></rect>
                    <rect id="矩形备份-10" fill-opacity="0.4" fill="#C2E2FF" x="18" y="3" width="2" height="3"></rect>
                    <circle id="椭圆形备份" fill-opacity="0.4" fill="#C2E2FF" cx="19" cy="18" r="13"></circle>
                    <rect id="矩形" stroke="url(#linearGradient-3)" stroke-width="0.5" x="15.25" y="1.25" width="7.5" height="1.5"></rect>
                    <rect id="矩形备份-10" stroke="url(#linearGradient-4)" stroke-width="0.5" x="18.25" y="3.25" width="1.5" height="2.5"></rect>
                    <circle id="椭圆形备份" stroke="url(#linearGradient-5)" stroke-width="0.5" cx="19" cy="18" r="12.75"></circle>
                    <rect id="矩形" fill="#FFFFFF" x="18" y="12" width="2" height="8" rx="1"></rect>
                    <rect id="矩形备份-51" fill="#FFFFFF" transform="translate(21.855159, 20.660744) rotate(-240.000000) translate(-21.855159, -20.660744) " x="20.8551591" y="16.6607442" width="2" height="8" rx="1"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>