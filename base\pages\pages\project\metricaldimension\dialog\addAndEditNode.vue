<template>
  <div>
    <CetDialog
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
      class="CetDialog min"
    >
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
      <div>
        <CetForm
          class="eem-cont-c1"
          :data.sync="CetForm_1.data"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
        >
          <el-form-item :label="$T('名称')" prop="name">
            <ElInput
              v-model="CetForm_1.data.name"
              v-bind="ElInput_1"
              v-on="ElInput_1.event"
            ></ElInput>
          </el-form-item>
        </CetForm>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import common from "eem-utils/common";
import customApi from "@/api/custom";
const NODE_RELATION = {
  all: "dimension",
  dimension: "level",
  level: "tag"
};
const ADD_NODE_RELATION_TEXT = {
  all: $T("添加维度"),
  dimension: $T("添加层级"),
  level: $T("添加标签")
};
const EDIT_NODE_RELATION_TEXT = {
  all: $T("编辑维度"),
  dimension: $T("编辑层级"),
  level: $T("编辑标签")
};
export default {
  name: "EditEnergyefficiencyIndexes",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    treeData_in: {
      type: Object
    }
  },
  computed: {
    isEdit() {
      return this.inputData_in && this.inputData_in.id;
    },
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data() {
    return {
      formData: {},
      CetDialog_1: {
        title: "",
        openTrigger_in: 0,
        closeTrigger_in: Date.now(),
        event: {
          closeTrigger_out: this.closeDialog
        },
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        data: {},
        inputData_in: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name,
            common.check_space
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {
          width: "100%"
        },
        event: {}
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    },
    inputData_in(val) {
      this.CetForm_1.data = this._.cloneDeep(val);
      if (!val || !val.id) {
        this.CetDialog_1.title = `${this._.get(
          ADD_NODE_RELATION_TEXT,
          this.treeData_in.modelLabel,
          "添加节点"
        )}`;
      } else {
        this.CetDialog_1.title = `${this._.get(
          EDIT_NODE_RELATION_TEXT,
          this.treeData_in.modelLabel,
          "编辑节点"
        )}`;
      }
      this.CetForm_1.resetTrigger_in = new Date().getTime();
    }
  },

  methods: {
    close() {},

    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    },
    CetButton_confirm_statusTrigger_out() {
      this.CetForm_1.localSaveTrigger_in = Date.now();
    },
    closeDialog() {
      this.close();
    },
    CetForm_1_saveData_out() {
      let model_data = this._.cloneDeep(this.CetForm_1.data);
      if (!this.treeData_in || !this.treeData_in.id) {
        this.$message.warning($T("暂无父节点信息！"));
        return;
      }
      let params = {};
      params = {
        parentId: this.treeData_in.id,
        parentLabel: this.treeData_in.modelLabel,
        sonId: model_data.id,
        sonLabel: model_data.modelLabel,
        sonName: model_data.name
      };
      if (!this.isEdit) {
        params.sonId = 0;
        params.sonLabel = this._.get(
          NODE_RELATION,
          this.treeData_in.modelLabel,
          ""
        );
      }
      if (this.treeData_in.modelLabel === "all") {
        delete params.parentId;
        delete params.parentLabel;
      }

      this.Update_node(params);
    },
    Update_node(params) {
      let rqFunc = customApi.addDimNode;
      if (this.isEdit) {
        rqFunc = customApi.editDimNode;
      }
      rqFunc(params).then(response => {
        if (response.code === 0) {
          this.successUpdate();
        }
      });
    },

    successUpdate() {
      this.$emit("saveData_out", Date.now());
      this.CetDialog_1.closeTrigger_in = Date.now();
      this.$message({
        type: "success",
        message: $T("保存成功！")
      });
    }
  },
  created() {}
};
</script>
<style lang="scss" scoped></style>
