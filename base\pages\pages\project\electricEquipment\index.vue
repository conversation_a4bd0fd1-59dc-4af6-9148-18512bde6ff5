<template>
  <div class="page eem-common">
    <div class="fullheight flex-column">
      <div class="mbJ3 eem-cont">
        <div style="display: flex; justify-content: flex-end">
          <el-input
            class="mrJ1"
            style="width: 200px"
            size="small"
            :placeholder="$T('输入关键字以检索')"
            suffix-icon="el-icon-search"
            v-model="filterText"
          ></el-input>
          <customElSelect
            class="mrJ1"
            size="small"
            v-model="globeMeterType"
            @change="changeGlobeMeterType"
            :placeholder="$T('请选择')"
            :prefix_in="$T('批量设置')"
          >
            <el-option
              v-for="item in metertype"
              :key="item.id + item.text"
              :label="item.text"
              :value="item.id"
            ></el-option>
          </customElSelect>
          <CetButton
            v-bind="CetButton2_confirm"
            v-on="CetButton2_confirm.event"
          ></CetButton>
        </div>
      </div>
      <div class="flex-auto eem-container" style="overflow: auto">
        <el-tree
          :data="treeData"
          ref="meterTree"
          style="height: 100%; overflow: auto"
          :lazy="true"
          show-checkbox
          :filter-node-method="filterNode"
          :default-expanded-keys="defaultExpandedKeys"
          node-key="tree_id"
          :props="treeProps"
          :render-after-expand="true"
          :load="loadTree"
          :highlight-current="true"
          :expand-on-click-node="false"
          @check-change="checkChange"
        >
          <!-- :filter-node-method="filterNode" -->
          <!-- @node-click="handleNodeClick" -->
          <div class="custom-tree-node" slot-scope="{ node, data }">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-tooltip
                  :content="data.nodeName"
                  effect="light"
                  :open-delay="500"
                  placement="bottom-start"
                >
                  <div class="text-overflow" style="max-width: 50%">
                    {{ data.nodeName }}
                  </div>
                </el-tooltip>
              </el-col>
              <el-col :span="12">
                <el-tooltip
                  :content="data.typeName"
                  effect="light"
                  :open-delay="500"
                  placement="bottom-start"
                >
                  <div class="text-overflow" style="max-width: 50%">
                    {{ data.typeName }}
                  </div>
                </el-tooltip>
              </el-col>
              <el-col :span="4">
                <div v-show="data.leaf" style="line-height: 32px">
                  <el-select
                    class="fullWidth"
                    size="small"
                    v-model="data.metertype"
                    @change="val => changeMeterType(val, node, data)"
                    :placeholder="$T('请选择')"
                  >
                    <el-option
                      v-for="item in metertype"
                      :key="item.id + '_' + data.tree_id"
                      :label="item.text"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </div>
              </el-col>
            </el-row>
            <!-- {{data}} -->
          </div>
        </el-tree>
      </div>
    </div>
  </div>
</template>
<script>
import { httping } from "@omega/http";
export default {
  name: "ElectricEquipment",
  components: {},
  computed: {
    projectTenantId() {
      var vm = this;
      return vm.$store.state.projectTenantId;
    },
    userId() {
      return this.$store.state.userInfo.id;
    }
  },

  data() {
    return {
      activatedNum: 0,
      metertype: [],
      globeMeterType: undefined,
      checkedNodes: [],
      defaultExpandedKeys: [],
      filterText: "",
      treeProps: {
        children: "children",
        label: "nodeName",
        isLeaf: "leaf"
      },
      currentNode: null,
      treeData: [],
      // confirm组件
      // confirm组件
      CetButton2_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        style: {
          float: "right"
        },
        event: {
          statusTrigger_out: this.CetButton2_confirm_statusTrigger_out
        }
      }
    };
  },
  watch: {
    filterText(val) {
      this.$refs.meterTree.filter(val);
    },
    currentNode: {
      deep: true,
      handler: function (val, oldVal) {
        if (!val) return;
        const oldFirst = this._.get(oldVal, "childNodes[0]", null);
        const first = this._.get(val, "childNodes[0]", null);
        if (this._.get(first, "key") === this._.get(oldFirst, "key")) return;
        if (val.level === 0 && first) {
          // console.log(val, oldVal);
          first.expand();
          // first.loadData();
          // this.defaultExpandedKeys.push(val.tree_id)
        }
      }
    }
  },

  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.nodeName && data.nodeName.indexOf(value) !== -1;
    },
    getMeterType() {
      this.metertype = this.$store.state.enumerations.metertype || [];
    },
    setTreeId(arr, level, leaf = false, keyArr = ["nodeType", "nodeId"]) {
      return arr.map(item => {
        return {
          ...item,
          tree_id: item[keyArr[0]] + "_" + item[keyArr[1]], //keyArr.map(it=>item[it]).join('_'),
          nodeName: item.name || item.nodeName || item.devicename,
          typeName: item.typeName || item.protocalname,
          leaf,
          level
        };
      });
    },
    loadTree(node, resolve) {
      if (node.level === 0) {
        // 第一层显示厂站的内容
        httping({
          url: `/device-data/api/comm/v1/stations`,
          method: "GET"
        }).then(res => {
          const stations = this._.sortBy(this._.get(res, "data", []), [
            "nodeId"
          ]);
          const root = this.setTreeId(stations, 1);
          this.currentNode = node;
          resolve(root);
        });
      } else if (node.level === 1) {
        // 通道
        httping({
          url: `/eem-service/v2/peccore/channels`,
          method: "POST",
          data: {
            stationIds: [node.data.nodeId],
            tenantId: this.projectTenantId,
            userId: this.userId
          }
        }).then(res => {
          const channels = this._.sortBy(this._.get(res, "data", []), [
            "nodeId"
          ]);

          resolve(this.setTreeId(channels, 2));
        });
      } else if (node.level === 2) {
        // 设备
        httping({
          url: `/eem-service/v2/peccore/pecDeviceExtend?channelId=${node.data.nodeId}&tenantId=${this.projectTenantId}`,
          method: "GET"
        }).then(res => {
          const devices = this._.get(res, "data", []);
          resolve(this.setTreeId(devices, 3, true, ["modelLabel", "deviceid"]));
        });
      }
    },
    changeMeterType(val, node, data) {
      const params = {
        isOverride: true
        // channelId: 0,
        // deviceIds: [0],
        // metertype: 0,
        // stationId: 0
      };
      const config = {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      };
      if (node.level === 1) {
        params.stationId = data.nodeId;
        params.metertype = val;
        this.$confirm($T("确认修改此厂站下的所有设备吗?"), $T("提示"), config)
          .then(() => {
            this.setMeterType([params]);
          })
          .catch(() => {});
      } else if (node.level === 2) {
        params.stationId = node.parent.data.nodeId;
        params.channelId = data.nodeId;
        params.metertype = val;
        this.$confirm($T("确认修改此通道下的所有设备吗?"), $T("提示"), config)
          .then(() => {
            this.setMeterType([params]);
          })
          .catch(() => {});
      } else if (node.level === 3) {
        params.stationId = node.parent.parent.data.nodeId;
        params.channelId = node.parent.data.nodeId;
        params.metertype = val;
        params.deviceIds = [data.deviceid];
        this.setMeterType([params]);
      }
    },
    setMeterType(params) {
      return httping({
        url: `/eem-service/v2/peccore/meterTypes`,
        method: "PUT",
        data: params
      }).then(res => {
        if (res.code === 0) {
          this.$message({
            type: "success",
            message: $T("保存成功")
          });
          var data = this._.get(res, "data", []) || [];
          data.forEach(item => {
            const node = this.$refs.meterTree.getNode(
              item.modelLabel + "_" + item.deviceid
            );
            node &&
              node.data &&
              this.$set(node.data, "metertype", item.metertype);
          });
        }
      });
    },
    changeGlobeMeterType(meterType) {
      // if (!this.checkedNodes.length) return this.$message("请至少勾选一个节点");
      // this.CetButton_confirm_statusTrigger_out();
      // console.log(meterType);
    },
    checkChange() {
      this.globeMeterType = undefined;
      this.checkedNodes = this.$refs.meterTree.getCheckedNodes();
    },
    CetButton2_confirm_statusTrigger_out(val) {
      if (!this.globeMeterType) {
        this.$message({
          type: "warning",
          message: $T("请选择设备类型")
        });
        return;
      }
      if (!this.checkedNodes.length) {
        return this.$message({
          type: "warning",
          message: $T("请至少勾选一个节点")
        });
      }
      const treeIds = this.checkedNodes.map(item => item.tree_id);
      const params = [];
      this.checkedNodes.forEach(item => {
        const node = this.$refs.meterTree.getNode(item.tree_id);
        if (item.level === 1) {
          params.push({
            isOverride: true,
            stationId: item.nodeId,
            metertype: this.globeMeterType
          });
        } else if (
          item.level === 2 &&
          !treeIds.includes(node.parent.data.tree_id)
        ) {
          params.push({
            isOverride: true,
            stationId: node.parent.data.nodeId,
            channelId: item.nodeId,
            metertype: this.globeMeterType
          });
        } else if (
          item.level === 3 &&
          !treeIds.includes(node.parent.data.tree_id) &&
          !treeIds.includes(node.parent.parent.data.tree_id)
        ) {
          const param = this._.find(params, {
            stationId: node.parent.parent.data.nodeId,
            channelId: node.parent.data.nodeId
          });
          if (param && this._.isArray(param.deviceIds)) {
            param.deviceIds.push(item.deviceid);
          } else {
            params.push({
              isOverride: true,
              stationId: node.parent.parent.data.nodeId,
              channelId: node.parent.data.nodeId,
              deviceIds: [item.deviceid],
              metertype: this.globeMeterType
            });
          }
        }
      });
      this.setMeterType(params).then(() => {
        this.$refs.meterTree.setCheckedKeys([]);
      });
    },
    refreshNode(id) {
      httping({
        url: `/device-data/api/comm/v1/stations`,
        method: "GET"
      }).then(res => {
        const stations = this._.sortBy(this._.get(res, "data", []), ["nodeId"]);
        const root = this.setTreeId(stations, 1);
        this.treeData = root;
        this.$nextTick(() => {
          this.$refs.meterTree.getNode(root[0].tree_id).expand();
        });
      });
    }
  },

  created: function () {
    this.getMeterType();
  },
  activated() {
    this.activatedNum++;
    if (this.activatedNum > 1) {
      this.refreshNode();
    }
  },
  mounted() {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;

  :deep(.el-tree-node__content) {
    height: auto;
  }
}
.custom-tree-node {
  height: 40px;
  line-height: 40px;
  // flex: 1;
  // display: flex;
  // align-items: center;
  // justify-content: space-between;
  font-size: 14px;
  padding: 0 10px;
  width: 100%;
}
</style>
