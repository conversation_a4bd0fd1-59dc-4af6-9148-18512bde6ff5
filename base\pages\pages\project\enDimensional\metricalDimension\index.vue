<template>
  <div class="page metricalDimension eem-common">
    <div class="eem-metrical-dimension">
      <div class="metricalDimension-mianbox flex-row">
        <div class="page-aside eem-aside">
          <div class="eem-group-list">
            <el-tooltip
              v-for="(item, key) in treeData"
              :key="key"
              :content="item.node"
            >
              <div
                :class="[
                  'group-item',
                  {
                    active: (activeGroup && activeGroup.nodeId) === item.nodeId
                  }
                ]"
                @click="activeGroup = item"
              >
                {{ item.node }}
              </div>
            </el-tooltip>
          </div>
        </div>
        <div class="page-right mlJ3 flex-auto flex-column">
          <div class="mbJ3 lh32">
            <div class="common-title-H2">
              {{ activeGroup ? activeGroup.node : "--" }}
            </div>
          </div>
          <div class="flex-auto" style="overflow-y: auto">
            <div class="eem-metrical-dimension-items eem-min-width-mini">
              <template v-for="(dimension, index) in dimensionItems">
                <div class="eem-metrical-dimension-item" :key="index">
                  <div class="dimension-item dimension-item-count">
                    <span class="fl dimension-name">维度名称：</span>
                    <el-input
                      class="fl"
                      style="width: 180px"
                      size="mini"
                      :maxlength="inputMaxLen"
                      :disabled="!dimension.edit"
                      placeholder="请输入维度名称"
                      suffix-icon="el-icon-edit"
                      v-model="dimension.name"
                      @keyup.native="handleStr1(dimension)"
                    ></el-input>
                    <el-button
                      type="primary"
                      plain
                      class="fl mlJ1"
                      size="mini"
                      v-if="showDimensionBtn(dimension, 1)"
                      @click="saveDimension(dimension)"
                    >
                      保存
                    </el-button>
                    <el-button
                      type="primary"
                      plain
                      class="fl mlJ1"
                      size="mini"
                      v-if="showDimensionBtn(dimension, 1)"
                      @click="cancelDimension(dimension)"
                    >
                      取消
                    </el-button>
                    <el-button
                      type="primary"
                      class="fl mlJ1"
                      size="mini"
                      plain
                      v-if="showDimensionBtn(dimension, 2)"
                      @click="editDimension(dimension)"
                    >
                      编辑
                    </el-button>
                    <el-button
                      class="fl mlJ1"
                      size="mini"
                      :disabled="dimension.fixdim"
                      v-if="showDimensionBtn(dimension, 3)"
                      @click="deleteDimension(dimension, index)"
                      icon="el-icon-delete"
                    ></el-button>
                  </div>
                  <div
                    class="dimension-item pl"
                    v-for="(hierachy, index) in dimension.children"
                    :key="index"
                  >
                    <span class="dimension-item-icon fl">{{ index + 1 }}</span>
                    <el-input
                      class="fl mlJ1"
                      style="width: 180px"
                      size="mini"
                      :maxlength="inputMaxLen"
                      :disabled="!dimension.edit"
                      placeholder="请输入层级名称"
                      suffix-icon="el-icon-edit"
                      v-model="hierachy.name"
                      @keyup.native="handleStr2(hierachy)"
                    ></el-input>

                    <el-popover
                      placement="right-start"
                      width="240"
                      v-model="hierachy.visible"
                      popper-class="rightStartPopover"
                    >
                      <div class="dimension-hierachy-tag-items">
                        <div
                          class="dimension-hierachy-tag-item"
                          v-for="(tag, index) in hierachy.children"
                          :key="index"
                        >
                          <el-input
                            class="fl"
                            style="width: 160px"
                            size="mini"
                            :maxlength="inputMaxLen"
                            :disabled="!dimension.edit"
                            placeholder="请输入标签名称"
                            suffix-icon="el-icon-edit"
                            v-model="tag.name"
                            @keyup.native="handleStr3(tag)"
                          ></el-input>
                          <el-button
                            class="fl mlJ1 row-delete"
                            size="mini"
                            v-show="dimension.edit"
                            @click="deleteTag(hierachy, index)"
                            icon="el-icon-delete"
                          ></el-button>
                        </div>
                      </div>

                      <div
                        class="dimension-hierachy-tag-item"
                        v-show="dimension.edit"
                      >
                        <el-button
                          class="mr-15"
                          size="mini"
                          @click="addTag(hierachy)"
                          icon="el-add-tag-icon-plus"
                        ></el-button>

                        <span>新建标签</span>
                      </div>

                      <el-button
                        slot="reference"
                        class="fl mlJ1"
                        style="border: 0px; padding: 0px; float: left"
                        size="medium"
                        icon="el-icon-document-copy"
                        :disabled="
                          (!hierachy.children ||
                            hierachy.children.length === 0) &&
                          !dimension.edit
                        "
                        @click="relationHierachy(hierachy, dimension)"
                      ></el-button>
                    </el-popover>
                    <el-button
                      class="fl mlJ1"
                      size="mini"
                      :disabled="
                        hierachy.children &&
                        hierachy.children.length != 0 &&
                        dimension.edit
                      "
                      v-if="showHierachyBtn(dimension, hierachy, index)"
                      @click="deleteHierachy(dimension)"
                      icon="el-icon-delete"
                    ></el-button>
                  </div>
                  <div class="dimension-edit" v-show="dimension.edit">
                    <el-button
                      class="mr-15 dimension-edit-btn"
                      size="medium"
                      @click="addHierachy(dimension)"
                      icon="el-icon-circle-plus"
                    ></el-button>

                    <span>添加层级</span>
                  </div>
                </div>
              </template>
              <div class="eem-metrical-dimension-item avalar-add-icon-box">
                <i class="el-icon-plus avalar-add-icon" @click="addDimension">
                  <span>添加维度</span>
                </i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { httping } from "@omega/http";
export default {
  name: "MetricalDimension",
  components: {},
  props: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data() {
    return {
      activeGroup: null,
      isSlide: false,
      treeData: [], //左侧树的数据
      propsM: {
        children: "children",
        label: "node"
      },
      inputMaxLen: 50,
      dimensionItems: [], //维度信息
      selectNodeLabel: ""
    };
  },
  watch: {},
  methods: {
    // 节点点击事件
    handleNodeClickM: function (node) {
      this.selectNodeLabel = node.node;
      this.$nextTick(function () {
        this.$refs.tree.setCurrentKey(node.nodeId);
      });
    },
    //维度编辑、保存、删除按钮显示
    showDimensionBtn: function (dimension, index) {
      var isShow = false;
      var edit = dimension.edit;
      var children = dimension.children || [];
      var len = children.length;
      switch (index) {
        case 1:
          if (len !== 0 && edit) {
            isShow = true;
          }
          break;
        case 2:
          if (len !== 0 && !edit) {
            isShow = true;
          }
          break;
        case 3:
          if (len === 0) {
            isShow = true;
          }
          break;
      }
      return isShow;
    },
    //控制层级编辑、删除按钮显示
    showHierachyBtn: function (dimension, hierachy, index) {
      var isShow = false;
      var edit = dimension.edit;
      var len = dimension.children.length;
      index = index + 1;
      if (len === index && edit) {
        isShow = true;
      }
      return isShow;
    },
    //点击添加维度
    addDimension: function () {
      var _this = this;
      var list = _this.dimensionItems;
      var len = list.length;
      var isOk = true;
      var obj = {};
      if (len !== 0) {
        var lastDimension = _this.dimensionItems[len - 1];
        var name = lastDimension.name;
        var edit = lastDimension.edit;
        var leng = lastDimension.children.length;
        if (!name || leng === 0 || edit) {
          _this.$message.warning("请逐个维度进行编辑！");
          return;
        }
      }
      for (var i = 0; i < len; i++) {
        if (list[i].edit) {
          isOk = false;
        }
      }
      if (!isOk) {
        _this.$message.warning("请先保存维度，才能进行添加维度！");
        return;
      }
      obj = {
        // index: len,
        id: 0,
        name: "",
        edit: true,
        children: []
      };
      _this.dimensionItems.push(obj);
    },
    //点击保存维度
    saveDimension: function (dimension) {
      var _this = this;
      var isOk = _this.testDimensionMsg();
      if (!isOk) {
        return;
      }
      _this.saveDimensionMsg(dimension);
      // dimension.edit = false;
    },
    //取消维度编辑
    cancelDimension: function (dimension) {
      this.init();
    },
    // 点击编辑维度
    editDimension: function (dimension) {
      var _this = this;
      var dimensionItems = _this.dimensionItems;
      var len = dimensionItems.length;
      var num = 0;
      for (var i = 0; i < len; i++) {
        if (dimensionItems[i].edit == true) {
          num += 1;
        }
      }
      if (num === 0) {
        dimension.edit = true;
      } else {
        _this.$message.warning("只能单个维度进行编辑！");
      }
    },
    //点击删除维度
    deleteDimension: function (dimension, index) {
      var _this = this;
      // var dimensionItems = _this.dimensionItems;
      // var len = dimensionItems.length;
      // if (index != len - 1) {
      //   _this.$message.warning('只能从最后一个维度开始删除！');
      //   return
      // }
      if (dimension.fixdim) {
        this.$message.warning("固定维度，不允许删除！");
        return;
      }
      _this
        .$confirm("确认是否删除维度？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          cancelButtonClass: "btn-custom-cancel",
          type: "warning"
        })
        .then(function () {
          _this.dimensionItems.splice(index, 1);
          _this.saveDimensionMsg();
        })
        .catch(function () {
          _this.$message({
            type: "info",
            message: "取消！"
          });
        });
    },
    // 点击添加层级
    addHierachy: function (dimension) {
      var len = dimension.children.length;
      var obj = {};
      obj = {
        dimensionId: dimension.id,
        id: 0,
        index: len,
        name: "",
        children: [],
        visible: false
      };
      dimension.children.push(obj);
    },
    //关联维度
    relationHierachy: function (hierachy, dimension) {
      var list = hierachy.children || [];
      var edit = dimension.edit;
      var len = list.length;
      if (len === 0 && !edit) {
        this.$message.warning("没有绑定有标签！");
      } else {
        // hierachy.visible = true;
      }
    },
    //点击删除层级
    deleteHierachy: function (dimension) {
      var _this = this;
      _this
        .$confirm("确认是否删除层级？", "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          cancelButtonClass: "btn-custom-cancel",
          type: "warning"
        })
        .then(function () {
          dimension.children.pop();
        })
        .catch(function () {
          _this.$message({
            type: "info",
            message: "取消！"
          });
        });
    },
    // 点击添加标签
    addTag: function (hierachy) {
      var obj = {};
      obj = {
        levelId: 0,
        id: 0,
        name: ""
      };
      if (hierachy.children) {
        hierachy.children.push(obj);
      } else {
        this.$set(hierachy, "children", [obj]);
      }
    },
    //点击删除标签
    deleteTag: function (hierachy, index) {
      hierachy.children.splice(index, 1);
    },
    //点击确认或取消选择标签
    handleTagPopover: function (hierachy, index) {
      var _this = this;
      if (index === 1) {
        var list = hierachy.children;
        var len = list.length;
        var arr = [];
        for (var i = 0; i < len; i++) {
          var name = list[i].name;
          if (!name) {
            _this.$message.warning("请输入标签名称！");
            return;
          }
          if (arr.indexOf(name) !== -1) {
            _this.$message.warning("不能输入相同标签名称！");
            return;
          }
          arr.push(name);
        }
        hierachy.visible = false;
      } else {
        hierachy.children = [];
        hierachy.visible = false;
      }
    },
    saveDimensionMsg: function (dimension) {
      var _this = this;
      var params = _this.dimensionItems;
      var auth = _this.token; //身份验证
      httping({
        url: "/eem-service/v1/dim/setting/save/dim/info",
        data: params,
        method: "POST",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          _this.$message.success("保存成功！");
          // 修改节点，更改全局状态，刷新首页
          _this.$store.commit("refreIndex", new Date());
          _this.getDimensionMsg();
          if (dimension) {
            dimension.edit = false;
          }
          // store.editDimenStatus();
        } else {
          _this.$message.error(res.msg);
        }
      }).catch(res => {
        _this.$message.error("保存维度列表数据失败");
      });
    },
    // 初始化时间格式以及设备列表的数据
    init: function () {
      this.getDimensionMsg();
    },
    //获取维度列表信息
    getDimensionMsg: function () {
      var _this = this;
      var auth = _this.token; //身份验证
      httping({
        url: "/eem-service/v1/dim/setting/detailList",
        method: "GET",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          if (res.data) {
            res.data.forEach(item => (item.edit = false));
            _this.dimensionItems = res.data;
            _this.addTreeData();
          }
        }
      });
    },
    //更新左侧节点树列表
    addTreeData: function () {
      var _this = this;
      var list = _this.dimensionItems;
      var len = list.length;
      var arr = [];
      for (var i = 0; i < len; i++) {
        var obj = {};
        obj.node = list[i].name;
        obj.nodeId = list[i].id;
        obj.nodeType = list[i].id;
        obj.children = [];
        arr.push(obj);
      }

      _this.treeData = arr;
      this.activeGroup = arr[0];
    },
    //验证配置维度信息是否合理
    testDimensionMsg: function () {
      var _this = this;
      var items = _this.dimensionItems;
      var le = items.length;
      var dimensionArr = [];

      for (var z = 0; z < le; z++) {
        var name = items[z].name;
        var list = items[z].children;
        var len = list.length;
        var hierachyArr = [];
        if (name) {
          if (dimensionArr.indexOf(name) !== -1) {
            var index1 = z + 1;
            var dimMsg = "维度序号" + index1 + "维度名称:" + name + "输入重复";
            _this.$message.warning(dimMsg);
            return false;
          }
          dimensionArr.push(name);
          for (var i = 0; i < len; i++) {
            name = list[i].name;
            var li = list[i].children;
            var leng = li ? li.length : 0;
            var tagArr = [];
            if (name) {
              if (hierachyArr.indexOf(name) !== -1) {
                index1 = z + 1;
                var index2 = i + 1;
                var levelMsg =
                  "维度序号" +
                  index1 +
                  "层级序号" +
                  index2 +
                  "层级名称:" +
                  name +
                  "输入重复";
                _this.$message.warning(levelMsg);
                return false;
              }
              hierachyArr.push(name);
              for (var j = 0; j < leng; j++) {
                name = li[j].name;
                if (!name) {
                  _this.$message.warning("请输入标签名称！");
                  return false;
                }
                if (tagArr.indexOf(name) !== -1) {
                  index1 = z + 1;
                  index2 = i + 1;
                  var index3 = j + 1;
                  var tagMsg =
                    "维度序号" +
                    index1 +
                    "层级序号" +
                    index2 +
                    "标签序号" +
                    index3 +
                    "标签名称:" +
                    name +
                    "输入重复";
                  _this.$message.warning(tagMsg);
                  return false;
                }
                tagArr.push(name);
              }
            } else {
              _this.$message.warning("请输入层级名称！");
              return false;
            }
          }
        } else {
          _this.$message.warning("请输入维度名称！");
          return false;
        }
      }
      return true;
    },
    //控制维度输入名称规则
    handleStr1: function (dimension) {
      // 控制特殊字符
      var name = dimension.name;
      name = name.replace(/\ /g, "");
      dimension.name = name;
    },
    // 控制层级输入名称规则
    handleStr2: function (hierachy) {
      // 控制特殊字符
      var name = hierachy.name;
      name = name.replace(/\ /g, "");
      hierachy.name = name;
    },
    // 控制标签输入名称规则
    handleStr3: function (tag) {
      // 控制特殊字符
      var name = tag.name;
      // name = name.replace(/\￥|\$|\@|\#|\%|\&|\*|\<|\>|\=/g,'');
      name = name.replace(/\ /g, "");
      tag.name = name;
    }
  },
  created: function () {
    // 进入页面触发
    var _this = this;
    setTimeout(function () {
      _this.init();
    }, 100);
  },
  activated: function () {},
  deactivated: function () {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}

.metricalDimension {
  .eem-metrical-dimension {
    height: 100%;
    width: 100%;
  }
  .metricalDimension-mianbox {
    height: 100%;
    min-width: 1024px;
    .page-right {
      .dimension-name {
        margin-top: 4px;
      }
      .pl {
        padding-left: 67px;
      }
      .dimension-edit {
        clear: both;
        padding-left: 50px;
        .dimension-edit-btn {
          border: 0px;
          padding: 0px;
          float: left;
        }
      }
    }
  }
  .common-slide {
    width: 10px;
    height: 100%;
    position: absolute;
    cursor: col-resize;
    left: 305px;
    top: 0;
  }
}
/*common aside css*/
.page-aside {
  width: 315px;
  height: 100%;
  overflow: auto;
}
/*右侧css*/
.page-right {
  position: relative;
  height: 100%;
}

.eem-metrical-dimension {
  overflow: auto;
}

.eem-metrical-dimension .eem-table-head-title p {
  color: #5ba134;
  font-weight: 900;
  font-size: 20px;
  height: 40px;
  line-height: 40px;
  text-align: center;
}
.eem-metrical-dimension .eem-table-head-title span {
  display: block;
  text-align: center;
}

.eem-metrical-dimension .eem-metrical-dimension-items {
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 0px;
  box-sizing: border-box;
}

.eem-metrical-dimension .eem-metrical-dimension-item {
  display: block;
  width: calc(33% - 16px);
  height: 40%;
  float: left;
  overflow: auto;
  margin-bottom: 16px;
  margin-right: 16px;
  min-width: 400px;
  max-width: 480px;
  min-height: 250px;
  @include background_color(BG1);
  @include border_color(B1);
}

@media screen and (max-width: 1460px) {
  .eem-metrical-dimension .eem-metrical-dimension-item {
    display: block;
    width: calc(50% - 15px);
    height: 50%;
    float: left;
    overflow: auto;
    margin-bottom: 15px;
    margin-right: 10px;
    min-width: 420px;
    max-width: 480px;
    min-height: 250px;
  }
}
.eem-metrical-dimension .avalar-add-icon-box {
  text-align: center;
  background: none;
}
.eem-metrical-dimension .avalar-add-icon {
  font-size: 28px;
  width: 120px;
  height: 120px;
  text-align: center;
  padding-top: 35px;
  border-radius: 4px;
  margin-top: 65px;
  cursor: pointer;
  box-sizing: border-box;
  @include background_color(BG2);
  @include border_color(B2);
  @include font_color(T1);
}

.eem-metrical-dimension .avalar-add-icon span {
  display: block;
  font-size: 16px;
  margin-top: 10px;
}

.eem-metrical-dimension .dimension-item {
  width: calc(100% - 15px);
  height: 40px;
  /*line-height: 40px;*/

  box-sizing: border-box;
}

.dimension-item-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  text-align: center;
  border: 1px solid;
  @include border_color(ZS);
  @include font_color(T5);
  @include background_color(ZS);
  font-size: 12px;
  border-radius: 10px;
  margin-top: 5px;
}

.dimension-hierachy-tag-items {
  height: 200px;
  overflow-y: auto;
  clear: both;
  padding: 10px 10px 0px;
}

.dimension-hierachy-tag-item {
  height: 36px;
  clear: both;
  @include background_color(BG1);
}
.dimension-hierachy-tag-item .mr-15 {
  text-indent: 10px;
  padding: 0px 8px 0px 0px;
  background: none;
  border: 0px;
  float: left;
}
.dimension-hierachy-tag-item .row-delete {
  background: none;
  border: none;
  font-size: 16px;
  padding: 5px;
}

.eem-metrical-dimension .dimension-item-count {
  margin: 18px 0px 0px 15px;
}
.eem-metrical-dimension :deep(.el-icon-document-copy) {
  display: inline-block;
  width: 25px;
  height: 26px;
  line-height: 28px;
  @include font_color(T1);
}
</style>

<style>
.el-icon-circle-plus {
  display: inline-block;
  width: 20px;
  height: 20px;
  color: #5b9eff;
  font-size: 20px;
  margin-top: 2px;
  font-style: #fff;
}

.el-add-tag-icon-plus {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: no-repeat;
  background-image: url("./assets/el-add-tag-icon.png");
  background-size: cover;
}
</style>
