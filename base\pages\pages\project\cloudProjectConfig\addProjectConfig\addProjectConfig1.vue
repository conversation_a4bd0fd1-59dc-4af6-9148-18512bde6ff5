<template>
  <div>
    <!-- 1弹窗组件 -->
    <CetDialog
      class="CetDialog"
      ref="CetDialog"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
    >
      <CetForm
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
      >
        <div class="bg1 brC2 rowBox" :class="{ retract: retractCard }">
          <el-row :gutter="$J3">
            <el-col :span="8">
              <el-form-item :label="$T('所属区域')" prop="respectiveRegin">
                <el-cascader
                  v-model="CetForm_1.data.respectiveRegin"
                  :props="treeProps"
                  :options="areaOptions"
                  @change="handleChange"
                  style="width: 100%"
                ></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('节点名称')" prop="name">
                <ElInput
                  v-model="CetForm_1.data.name"
                  v-bind="ElInput_name"
                  v-on="ElInput_name.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('编号')" prop="code">
                <ElInput
                  :placeholder="$T('P-年后两位+月+日+序号')"
                  v-model="CetForm_1.data.code"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('联系人')" prop="contact">
                <ElInput
                  v-model="CetForm_1.data.contact"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('联系人电话')" prop="contactnumber">
                <ElInput
                  v-model="CetForm_1.data.contactnumber"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('所属公司')" prop="enterprisename">
                <ElInput
                  v-model="CetForm_1.data.enterprisename"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('联系人邮箱')" prop="contactemail">
                <ElInput
                  v-model="CetForm_1.data.contactemail"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                :label="$T('法人代表')"
                prop="cooperaterepresentative"
              >
                <ElInput
                  v-model="CetForm_1.data.cooperaterepresentative"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('联系地址')" prop="address">
                <ElInput
                  v-model="CetForm_1.data.address"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('负责人')" prop="director">
                <ElInput
                  v-model="CetForm_1.data.director"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('投用日期')" prop="commissiondate">
                <el-date-picker
                  v-model="CetForm_1.data.commissiondate"
                  type="date"
                  :editable="false"
                  :placeholder="$T('选择日期')"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('负责人电话')" prop="directornumber">
                <ElInput
                  v-model="CetForm_1.data.directornumber"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item :label="$T('负责人邮箱')" prop="directoremail">
                <ElInput
                  v-model="CetForm_1.data.directoremail"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('年产值')" prop="annualoutput">
                <ElInputNumber
                  v-model="CetForm_1.data.annualoutput"
                  v-bind="ElInputNumber_1"
                  v-on="ElInputNumber_1.event"
                ></ElInputNumber>
                <span class="form-item-unit">{{ $T("万元") }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('经度')" prop="longitude">
                <ElInputNumber
                  :disabled="true"
                  v-model="CetForm_1.data.longitude"
                  v-bind="ElInputNumber_3"
                  v-on="ElInputNumber_3.event"
                ></ElInputNumber>
                <i
                  class="el-icon-close el-input__icon eem-map-icon"
                  style="right: 35px"
                  @click="delteleMap"
                ></i>
                <i
                  class="el-icon-edit el-input__icon eem-map-icon"
                  @click="OpenBaiduMap"
                ></i>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('纬度')" prop="latitude">
                <ElInputNumber
                  :disabled="true"
                  v-model="CetForm_1.data.latitude"
                  v-bind="ElInputNumber_3"
                  v-on="ElInputNumber_3.event"
                ></ElInputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('面积')" prop="area">
                <ElInputNumber
                  v-model="CetForm_1.data.area"
                  v-bind="ElInputNumber_2"
                  v-on="ElInputNumber_2.event"
                ></ElInputNumber>
                <span class="form-item-unit">m²</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$T('人数')" prop="population">
                <ElInputNumber
                  v-model="CetForm_1.data.population"
                  v-bind="ElInputNumber_1"
                  v-on="ElInputNumber_1.event"
                ></ElInputNumber>
                <span class="form-item-unit">{{ $T("人") }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$T('项目简介')" prop="projectabstract">
                <ElInput
                  :rows="7"
                  v-model="CetForm_1.data.projectabstract"
                  v-bind="ElInput_2"
                  v-on="ElInput_2.event"
                ></ElInput>
              </el-form-item>
            </el-col>
          </el-row>
          <div
            class="handle fullwidth text-center fcZS mtJ3"
            @click="retractCard = !retractCard"
          >
            {{ retractCard ? $T("展开") : $T("收起") }}
            <i class="el-icon-d-arrow-left handleIcon"></i>
          </div>
        </div>
        <div class="bg1 brC2 rowBox mtJ1">
          <el-row :gutter="$J3">
            <el-col :span="24">
              <el-form-item>
                <div class="label" slot="label">
                  {{ $T("项目主图") }}
                  <div class="box_tip2">
                    {{
                      $T(
                        "只能上传jpg/png图片，且不超过{0}M，推荐80px*80px大小。",
                        systemCfg.uploadPicSize
                      )
                    }}
                  </div>
                </div>
                <div class="value">
                  <UploadImg
                    class="uploadImg"
                    :imgUrl.sync="CetForm_1.data.pic"
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </CetForm>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
    <MapLatOrLng
      :visibleTrigger_in="MapLatOrLng.visibleTrigger_in"
      :closeTrigger_in="MapLatOrLng.closeTrigger_in"
      :queryId_in="MapLatOrLng.queryId_in"
      :inputData_in="MapLatOrLng.inputData_in"
      :mapInfo="mapInfo"
      :showArea="true"
      @finishTrigger_out="MapLatOrLng_finishTrigger_out"
      @finishData_out="MapLatOrLng_finishData_out"
      @saveData_out="MapLatOrLng_saveData_out"
      @currentData_out="MapLatOrLng_currentData_out"
    />
  </div>
</template>
<script>
import common from "eem-utils/common";
import MapLatOrLng from "./MapLatOrLng.vue";
import UploadImg from "eem-components/uploadImg.vue";
import { httping } from "@omega/http";
export default {
  name: "addProjectConfig1",
  components: {
    MapLatOrLng,
    UploadImg
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    treeData_in: {
      type: Object
    },
    type_in: {
      type: Number
    },
    treeNameList_in: {
      type: Array
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    systemCfg() {
      return this.$store.state.systemCfg;
    }
  },

  data() {
    return {
      retractCard: true,
      input: "",
      mapInfo: {
        areaJson: null,
        point: null
      },
      CetDialog_1: {
        title: $T("添加项目节点"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        labelWidth: "120px",
        labelPosition: "top",
        rules: {
          respectiveRegin: [
            {
              required: true,
              message: $T("请选择区域"),
              trigger: ["blur", "change"]
            }
          ],
          nodeType: [
            {
              required: true,
              message: $T("请选择节点类型"),
              trigger: ["blur", "change"]
            }
          ],
          code: [
            {
              required: false,
              message: $T("P-年后两位+月+日+序号"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          name: [
            {
              required: true,
              message: $T("请输入名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          directornumber: [
            {
              required: false,
              message: $T("请输入负责人电话"),
              trigger: ["blur", "change"]
            },
            common.pattern_name,
            common.check_phoneandtelephone
          ],
          contact: [
            {
              required: false,
              message: $T("请输入联系人名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          contactnumber: [
            {
              required: false,
              message: $T("请输入联系人电话"),
              trigger: ["blur", "change"]
            },
            common.pattern_name,
            common.check_phoneandtelephone
          ],
          contactemail: [
            { required: false, message: $T("请输入邮箱地址"), trigger: "blur" },
            {
              type: "email",
              message: $T("请输入正确的邮箱地址"),
              trigger: ["blur", "change"]
            }
          ],
          enterprisename: [
            {
              required: false,
              message: $T("请输入所属公司名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          cooperaterepresentative: [
            {
              required: false,
              message: $T("请输入法人代表名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          address: [
            {
              required: false,
              message: $T("请输入联系地址"),
              trigger: ["blur", "change"]
            },
            common.check_stringLessThan255
          ],
          director: [
            {
              required: false,
              message: $T("请输入负责人名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          commissiondate: [
            {
              required: false,
              message: $T("请选择投用日期"),
              trigger: ["blur", "change"]
            }
          ],
          projectabstract: [
            {
              required: false,
              message: $T("请输入项目简介"),
              trigger: ["blur", "change"]
            },
            common.check_stringLessThan255
          ],
          directoremail: [
            { required: false, message: $T("请输入邮箱地址"), trigger: "blur" },
            {
              type: "email",
              message: $T("请输入正确的邮箱地址"),
              trigger: ["blur", "change"]
            }
          ],
          area: [
            {
              required: false,
              message: $T("请输入面积"),
              trigger: ["blur", "change"]
            }
          ],
          population: [
            {
              required: false,
              message: $T("请输入人数"),
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_1_currentData_out,
          saveData_out: this.CetForm_1_saveData_out,
          finishData_out: this.CetForm_1_finishData_out,
          finishTrigger_out: this.CetForm_1_finishTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        type: "text",
        style: {},
        placeholder: $T("请输入内容"),
        event: {
          change: this.ElInput_1_change_out,
          input: this.ElInput_1_input_out
        }
      },
      ElInput_2: {
        value: "",
        type: "textarea",
        maxlength: 255,
        showWordLimit: true,
        required: true,
        placeholder: $T("请输入项目简介"),
        rows: 2,
        style: {},
        event: {
          change: this.ElInput_2_change_out,
          input: this.ElInput_2_input_out
        }
      },
      ElInputNumber_1: {
        ...common.check_numberInt,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInputNumber_2: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInputNumber_3: {
        ...common.check_numberFloat11,
        value: "",
        style: {
          width: "100%"
        },
        placeholder: $T("请选择经纬度"),
        controls: false,
        event: {
          change: this.ElInputNumber_3_change_out
        }
      },
      pickerOptions: common.pickerOptions_laterThanYesterd11,
      ElInput_name: {
        value: "",
        style: {},
        placeholder: $T("请输入内容"),
        event: {
          change: this.ElInput_name_change_out,
          input: this.ElInput_name_input_out
        }
      },
      treeProps: {
        expandTrigger: "hover",
        children: "children",
        label: "name",
        value: "id"
      },
      areaOptions: [],
      districtId: null,
      MapLatOrLng: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      ismoveto: false
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.retractCard = true;
      vm.CetDialog_1.openTrigger_in = val;
      this.$nextTick(() => {
        $(this.$refs.CetDialog.$el).scrollTop(0);
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {
      var vm = this;
    },
    inputData_in(val) {
      if (!val) {
        return;
      }
      this.CetForm_1.data = this._.cloneDeep(val);
      if (val.province_id && val.city_id && val.district_id) {
        this.CetForm_1.data.respectiveRegin = [
          val.province_id,
          val.city_id,
          val.district_id
        ];
        this.districtId = val.district_id;
      }
      this.ismoveto = false;
      if (this.type_in === 3) {
        this.CetDialog_1.title = $T("编辑节点");
      } else {
        this.CetDialog_1.title = $T("添加节点");
      }
      // 处理经纬度
      if (
        !this.CetForm_1.data.longitude &&
        this.CetForm_1.data.longitude !== 0
      ) {
        this.CetForm_1.data.longitude = undefined;
      }
      if (!this.CetForm_1.data.latitude && this.CetForm_1.data.latitude !== 0) {
        this.CetForm_1.data.latitude = undefined;
      }
      this.CetForm_1.resetTrigger_in = new Date().getTime();
    },
    type_in(val) {
      // if (val === 3) {
      //   this.CetForm_1.data = this._.cloneDeep(this.inputData_in);
      // }
    }
  },

  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
      // this.$emit("confirm_out");
    },
    CetDialog_1_open_out() {},
    CetDialog_1_close_out() {},
    CetForm_1_currentData_out() {},
    CetForm_1_saveData_out() {
      var params = [],
        model_data = this._.cloneDeep(this.CetForm_1.data);
      if (this.ismoveto) {
        model_data = this._.cloneDeep(this.CetForm_1.data);
        params = [
          {
            following: {
              id: this.districtId,
              modelLabel: "district"
            },
            previous: {
              id: model_data.district_id,
              modelLabel: "district"
            },
            toBeOut: model_data
          }
        ];

        this.moveto_node(params);
      } else {
        if (this.type_in === 1) {
          if (!this.districtId) {
            this.$message.warning($T("请选择所属区域！"));
            return;
          }

          model_data = this._.cloneDeep(this.CetForm_1.data);
          model_data.modelLabel = "project";
          model_data.commissiondate = model_data.commissiondate
            ? new Date(model_data.commissiondate).getTime()
            : null;
          // model_data.children = [
          //   {
          //     id: this.districtId,
          //     modelLabel: "district"
          //   }
          // ];
          params[0] = model_data;
        } else if (this.type_in === 3) {
          params = [];
          model_data = this._.cloneDeep(this.CetForm_1.data);
          model_data.commissiondate = model_data.commissiondate
            ? new Date(model_data.commissiondate).getTime()
            : null;
          // model_data.children = [
          //   {
          //     id: this.districtId,
          //     modelLabel: "district"
          //   }
          // ];
          params[0] = model_data;
        }
        var name = this.CetForm_1.data.name;
        var list = this.treeNameList_in || [];
        for (var i = 0, len = list.length; i < len; i++) {
          if (list[i] === name) {
            if (this.type_in === 3 && this.inputData_in.name === name) {
              continue;
            }
            this.$message.warning($T($T("节点名称重复！")));
            return;
          }
        }
        this.Add_node(params);
      }
    },
    CetForm_1_finishData_out(val) {},
    CetForm_1_finishTrigger_out(val) {},

    ElInput_1_change_out(val) {},
    ElInput_1_input_out(val) {},
    ElInput_2_change_out(val) {},
    ElInput_2_input_out(val) {},
    ElInputNumber_3_change_out(val) {},
    // name输出,方法名要带_out后缀
    ElInput_name_change_out(val) {},
    ElInput_name_input_out(val) {},

    handleChange(val) {
      // console.log(val);
      if (!val) {
        return;
      }
      if (!val[2]) {
        this.$message.warning($T("请选择区域层级！"));
        return;
      }
      if (
        this.type_in === 3 &&
        this.CetForm_1.data.respectiveRegin !== val[2]
      ) {
        this.ismoveto = true;
      }
      this.districtId = val[2];
    },
    MapLatOrLng_currentData_out(val) {},
    MapLatOrLng_finishData_out(val) {
      if (!val) {
        return;
      }
      this.$set(this.CetForm_1.data, "longitude", val.lng);
      this.$set(this.CetForm_1.data, "latitude", val.lat);
      this.CetForm_1.data.locationrange = val.areaJson;
    },
    MapLatOrLng_finishTrigger_out(val) {},
    MapLatOrLng_saveData_out(val) {},
    OpenBaiduMap() {
      this.mapInfo.areaJson = this.CetForm_1.data.locationrange;
      this.mapInfo.point = {
        latitude: this.CetForm_1.data.latitude,
        longitude: this.CetForm_1.data.longitude
      };
      this.MapLatOrLng.visibleTrigger_in = this._.cloneDeep(
        new Date().getTime()
      );
    },
    delteleMap() {
      this.CetForm_1.data.longitude = undefined;
      this.CetForm_1.data.latitude = undefined;
      this.CetForm_1.data.locationrange = "";
    },
    Add_node(params) {
      params[0].province_id = params[0].respectiveRegin[0];
      params[0].city_id = params[0].respectiveRegin[1];
      params[0].district_id = params[0].respectiveRegin[2];
      delete params[0].respectiveRegin;
      params[0].city = "";
      params[0].district = "";
      params[0].province = "";
      params[0].hierarchy = "";
      // 经纬度如果是undefined 则转成null
      if (params[0].longitude === undefined) {
        params[0].longitude = null;
      }
      if (params[0].latitude === undefined) {
        params[0].latitude = null;
      }
      if (params[0].annualoutput === undefined) {
        params[0].annualoutput = null;
      }
      if (params[0].area === undefined) {
        params[0].area = null;
      }
      if (params[0].population === undefined) {
        params[0].population = null;
      }
      var _this = this;
      var auth = _this.token; //身份验证
      httping({
        url: `/eem-service/v1/project/rootNode`,
        data: params,
        method: "PUT",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          _this.CetDialog_1.closeTrigger_in = _this._.cloneDeep(
            new Date().getTime()
          );
          _this.$emit("saveData_out", res.data[0]);
          // 更新store
          let projectId = _this._.get(res, "data[0].id"),
            tenantid = _this._.get(res, "data[0].tenantid"),
            projectInfo = _this._.get(res, "data[0]");
          _this.$store.dispatch("initProject", {
            projectId,
            tenantid,
            projectInfo
          });
        }
      });
    },
    moveto_node(params) {
      var _this = this;
      var auth = _this.token; //身份验证
      httping({
        url: "/model/v1/moveto",
        data: params,
        method: "PUT",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          var model_data = _this._.cloneDeep(_this.CetForm_1.data);
          var param = [model_data];
          _this.Add_node(param);
        }
      });
    },
    getDistrictTree() {
      var _this = this;
      httping({
        url: "eem-service/v1/common/district",
        method: "GET"
      }).then(res => {
        if (res.code === 0) {
          _this.areaOptions = res.data;
        }
      });
    }
  },
  created: function () {},
  mounted: function () {
    this.getDistrictTree();
  }
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
    box-sizing: border-box;
  }
  .rowBox {
    @include padding(J3 J4);
    :deep(.el-row) {
      height: auto;
      overflow: initial;
    }
    .handleIcon {
      transform: rotate(90deg);
    }
    &.retract {
      :deep(.el-row) {
        height: 352px;
        overflow: hidden;
      }
      .handleIcon {
        transform: rotate(-90deg);
      }
    }
  }
}

.uploadImg {
  width: 100px;
  height: 100px;
}
.box_tip2 {
  display: inline-block;
  @include font_size(Ab);
  @include font_color(T3);
}
.eem-map-icon {
  position: absolute;
  right: 10px;
  z-index: 1;
  top: -3px;
  cursor: pointer;
}
.handle {
  cursor: pointer;
}
</style>
