<template>
  <div class="page">
    <el-container style="height: 100%">
      <el-container
        class="padding0 fullheight flex-column"
        style="min-width: 980px"
      >
        <div class="minWH mtJ3" style="flex: 1; overflow: auto">
          <div class="eem-container">
            <div class="clearfix mbJ3 lh32">
              <div class="fl" v-if="ElSelect_1.value">
                <span class="common-title-H2">
                  {{ $moment(CetDatePicker_1.val).year() }}{{ $T("年")
                  }}{{ $T("第")
                  }}{{
                    ElSelect_1.value == 1
                      ? $T("一季度（1月-3月）")
                      : ElSelect_1.value == 2
                      ? $T("二季度（4月-6月）")
                      : ElSelect_1.value == 3
                      ? $T("三季度（7月-9月）")
                      : ElSelect_1.value == 4
                      ? $T("四季度（10月-12月）")
                      : ""
                  }}{{ $T("需量报告") }}
                </span>
              </div>
              <!-- 向后查询按钮 -->
              <CetButton
                class="fr custom—square mlJ"
                v-bind="CetButton_next"
                v-on="CetButton_next.event"
              ></CetButton>
              <ElSelect
                class="fr"
                v-model="ElSelect_1.value"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in ElOption_1.options_in"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </ElSelect>
              <div class="basic-box fr mrJ">
                <!-- <div class="basic-box-label">季度</div>
                <el-date-picker
                  class="fr"
                  v-model="CetDatePicker_1.val"
                  v-bind="CetDatePicker_1.config"
                ></el-date-picker> -->
                <CustomElDatePicker
                  class="fr"
                  :prefix_in="$T('选择季度')"
                  v-bind="CetDatePicker_1.config"
                  v-model="CetDatePicker_1.val"
                />
              </div>
              <!-- 向前查询按钮 -->
              <CetButton
                class="fr custom—square mrJ"
                v-bind="CetButton_prv"
                v-on="CetButton_prv.event"
              ></CetButton>
            </div>
            <div class="chartsBox bg1">
              <CetChart
                :inputData_in="CetChart_1.inputData_in"
                v-bind="CetChart_1.config"
              />
            </div>
          </div>
          <div class="eem-container mtJ3">
            <div style="height: calc(100% - 360px)">
              <div class="clearfix mbJ3">
                <div class="fl" style="color: #e51b1d; font-size: 18px">
                  {{ $T("计费方式不合理的进线") }}
                  <div class="tag" style="background: #e51b1d">
                    {{ CetTable_1.data.length }}{{ $T("条") }}
                  </div>
                </div>
                <el-tooltip
                  :content="
                    `${$T('容需计费临界值')}：` +
                    `${$T('为容量单价')}` +
                    `/${$T('需量单价')}` +
                    `*${$T('容量。')}` +
                    `${$T('若最大需量')}` +
                    `<${$T('临界值')}` +
                    `，${$T('则需量计费更经济')}` +
                    `；${$T('若最大需量')}` +
                    `>${$T('临界值')}` +
                    `，${$T('则容量计费更经济。')}`
                  "
                >
                  <i class="el-icon-question fl mtJ1 mlJ1 fsH3"></i>
                </el-tooltip>
                <CetButton
                  class="fr"
                  v-bind="CetButton_1"
                  v-on="CetButton_1.event"
                ></CetButton>
              </div>
              <div class="" style="height: 300px">
                <CetTable
                  style="height: 100%"
                  :data.sync="CetTable_1.data"
                  :dynamicInput.sync="CetTable_1.dynamicInput"
                  v-bind="CetTable_1"
                  v-on="CetTable_1.event"
                >
                  <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
                  <ElTableColumn
                    v-bind="ElTableColumn_accountName"
                  ></ElTableColumn>
                  <ElTableColumn
                    v-bind="ElTableColumn_volumeText"
                  ></ElTableColumn>
                  <ElTableColumn
                    v-bind="ElTableColumn_rateVolumeText"
                  ></ElTableColumn>
                  <ElTableColumn
                    v-bind="ElTableColumn_chargingWay$Name"
                  ></ElTableColumn>
                  <ElTableColumn
                    v-bind="ElTableColumn_maxDemandText"
                  ></ElTableColumn>
                  <ElTableColumn
                    v-bind="ElTableColumn_shouldChargingWay$Name"
                  ></ElTableColumn>
                  <ElTableColumn
                    v-bind="ElTableColumn_savedValue"
                  ></ElTableColumn>
                </CetTable>
              </div>
              <div class="tooltipInfo mtJ3">
                {{
                  $T("对计费方式不合理的进线，建议修改计费方式，节省基本电量。")
                }}
              </div>
            </div>
          </div>
        </div>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import { httping } from "@omega/http";
export default {
  name: "DemandReportSeason",
  components: {},
  props: {
    currentNode: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },

  data() {
    const language = window.localStorage.getItem("omega_language") === "en";
    return {
      tableItem: null,
      CetDatePicker_1: {
        disable_in: false,
        val: this.$moment().add(0, "d").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "year",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          clearable: false,
          size: "small",
          style: {
            display: "inline-block",
            width: "200px"
          },
          pickerOptions: {
            disabledDate(time) {
              if (
                new Date().getMonth() < 3 &&
                time.getFullYear() == new Date().getFullYear()
              ) {
                return true;
              } else {
                return time.getTime() > Date.now();
              }
            }
          }
        }
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: true,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      CetChart_1: {
        inputData_in: {},
        config: {
          options: {
            tooltip: {
              trigger: "item",
              formatter: function (val) {
                var rete = val.percent || "--";
                if (val.data.rete || val.data.rete === 0) {
                  rete = val.data.rete;
                }
                if (val.name) {
                  return `${val.name}<br /> ${$T("进线数")}：${Number(
                    val.value
                  ).toFixed2()} <br /> ${$T("占比")}：${rete}%`;
                }
              }
            },
            legend: {
              show: false
            },
            series: [
              {
                name: "",
                type: "pie",
                selectedMode: "single",
                radius: [0, "40%"],
                label: {
                  fontSize: 14,
                  position: "inner"
                },
                labelLine: {
                  show: false
                },
                data: [
                  {
                    value: 100,
                    name: $T("容量"),
                    itemStyle: {
                      color: "#aaaaaa"
                    },
                    label: {
                      textBorderColor: "transparent"
                    }
                  },
                  {
                    value: 679,
                    name: $T("需量"),
                    itemStyle: {
                      color: "#1695F8"
                    },
                    label: {
                      textBorderColor: "transparent"
                    }
                  }
                ]
              },
              {
                name: "",
                type: "pie",
                radius: ["50%", "60%"],
                label: {
                  formatter: function (val) {
                    var rete = val.percent;
                    if (val.data.rete || val.data.rete === 0) {
                      rete = val.data.rete;
                    }
                    if (val.name) {
                      return `{b|${val.name}}\n {c|${$T(
                        "进线数"
                      )}：}{d|${Number(val.value).toFixed2()}条} \n {c|${$T(
                        "占比"
                      )}：}{d|${rete}%}`;
                    }
                  },
                  // formatter: "{b|{b}}\n {c|进线数：}{d|{c}条} \n {c|占比：}{d|{d}%}",
                  backgroundColor: "#1C2850",
                  borderColor: "#666666",
                  borderWidth: 0,
                  borderRadius: 4,
                  padding: [5, 5],
                  rich: {
                    b: {
                      fontSize: 14,
                      lineHeight: 24,
                      color: "#fff",
                      align: "center"
                    },
                    c: {
                      fontSize: 12,
                      lineHeight: 16,
                      color: "#fff",
                      align: "center"
                    },
                    d: {
                      color: "#fff",
                      align: "center"
                    }
                  }
                },
                data: [
                  {
                    value: 335,
                    name: $T("容量-计费方式不合理"),
                    itemStyle: {
                      color: "#EA4848"
                    },
                    rete: 0
                  },
                  {
                    value: 234,
                    name: "",
                    itemStyle: {
                      color: "transparent"
                    },
                    labelLine: {
                      show: false
                    },
                    emphasis: {
                      labelLine: {
                        show: false
                      }
                    }
                  },
                  {
                    value: 310,
                    name: $T("需量-计费方式不合理"),
                    itemStyle: {
                      color: "#F7B9B9"
                    },
                    rete: 0
                  },
                  {
                    value: 234,
                    name: "",
                    itemStyle: {
                      color: "transparent"
                    },
                    labelLine: {
                      show: false
                    },
                    emphasis: {
                      labelLine: {
                        show: false
                      }
                    }
                  }
                ]
              }
            ]
          }
        }
      },
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: $T("查看进线"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //  defaultSort:null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: "#", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "50" //绝对宽度
      },
      // accountName组件
      ElTableColumn_accountName: {
        prop: "accountName", // 支持path a[0].b
        label: $T("进线名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "190" : "150" //该宽度会自适应
      },
      // volumeText组件
      ElTableColumn_volumeText: {
        prop: "volumeText", // 支持path a[0].b
        label: $T("容量") + "（kVA）", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "150" : "150" //该宽度会自适应
      },
      // rateVolumeText组件
      ElTableColumn_rateVolumeText: {
        prop: "rateVolumeText", // 支持path a[0].b
        label: $T("容需计费临界值"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "210" : "150" //该宽度会自适应
      },
      // chargingWay$Name组件
      ElTableColumn_chargingWay$Name: {
        prop: "chargingWay$Name", // 支持path a[0].b
        label: $T("本季度计费方式"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "235" : "150" //该宽度会自适应
      },
      // maxDemandText组件
      ElTableColumn_maxDemandText: {
        prop: "maxDemandText", // 支持path a[0].b
        label: $T("本季度最大需量") + "（kW）", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "315" : "200" //该宽度会自适应
      },
      // shouldChargingWay$Name组件
      ElTableColumn_shouldChargingWay$Name: {
        prop: "shouldChargingWay$Name", // 支持path a[0].b
        label: $T("应选择计费方式"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: language ? "270" : "150" //该宽度会自适应
      },
      // savedValue组件
      ElTableColumn_savedValue: {
        prop: "savedValue", // 支持path a[0].b
        label: $T("修改可节省费用(元)"), //列名
        headerAlign: "right",
        align: "right",
        showOverflowTooltip: true,
        minWidth: language ? "300" : "150" //该宽度会自适应
      },
      ElSelect_1: {
        value: 1,
        style: {
          width: "150px"
        },
        event: {
          change: this.ElSelect_1_change_out
        },
        size: "small"
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      }
    };
  },
  watch: {
    "CetDatePicker_1.val": function (val) {
      this.deteChange(val);
      this.nextDisable();
      this.getData();
    },
    "ElSelect_1.value": function (val) {
      this.nextDisable();
    },
    currentNode: {
      handler: function (val) {
        if (val) {
          this.getData();
        }
      },
      deep: true
    }
  },

  methods: {
    // 获取季报信息
    getData() {
      if (!this.currentNode) {
        return;
      }
      var data = {
        node: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          name: this.currentNode.name
        },
        projectId: this.projectId
      };
      // 取季度时间
      if (this.ElSelect_1.value == 1) {
        data.startTime = this.$moment(this.CetDatePicker_1.val)
          .month(0)
          .startOf("month")
          .valueOf();
        data.endTime = this.$moment(this.CetDatePicker_1.val)
          .month(3)
          .startOf("month")
          .valueOf();
      } else if (this.ElSelect_1.value == 2) {
        data.startTime = this.$moment(this.CetDatePicker_1.val)
          .month(3)
          .startOf("month")
          .valueOf();
        data.endTime = this.$moment(this.CetDatePicker_1.val)
          .month(6)
          .startOf("month")
          .valueOf();
      } else if (this.ElSelect_1.value == 3) {
        data.startTime = this.$moment(this.CetDatePicker_1.val)
          .month(6)
          .startOf("month")
          .valueOf();
        data.endTime = this.$moment(this.CetDatePicker_1.val)
          .month(9)
          .startOf("month")
          .valueOf();
      } else if (this.ElSelect_1.value == 4) {
        data.startTime = this.$moment(this.CetDatePicker_1.val)
          .month(9)
          .startOf("month")
          .valueOf();
        data.endTime = this.$moment(this.CetDatePicker_1.val)
          .startOf("year")
          .add(1, "year")
          .startOf("month")
          .valueOf();
      }
      this.CetChart_1.config.options.series[0].data[0].value = 0;
      this.CetChart_1.config.options.series[0].data[1].value = 0;
      // 需量合理性分布统计
      this.CetChart_1.config.options.series[1].data[0].value = 0;
      this.CetChart_1.config.options.series[1].data[1].value = 0;
      this.CetChart_1.config.options.series[1].data[2].value = 0;
      this.CetChart_1.config.options.series[1].data[3].value = 0;
      // 列表数据
      this.CetTable_1.data = [];
      httping({
        url: `/eem-service/v1/demand/manage/quarterReport`,
        method: "POST",
        data
      }).then(response => {
        if (response.code == 0) {
          // 需量和容量数量分布
          if (response.data.basicFeeTypeCount) {
            this.CetChart_1.config.options.series[0].data[0].value =
              response.data.basicFeeTypeCount.volume &&
              response.data.basicFeeTypeCount.volume.toFixed(2);
            this.CetChart_1.config.options.series[0].data[0].name = $T("容量");
            this.CetChart_1.config.options.series[0].data[1].value =
              response.data.basicFeeTypeCount.demand &&
              response.data.basicFeeTypeCount.demand.toFixed(2);
            this.CetChart_1.config.options.series[0].data[1].name = $T("需量");
            if (
              this.CetChart_1.config.options.series[0].data[0].value &&
              !this.CetChart_1.config.options.series[0].data[1].value
            ) {
              this.CetChart_1.config.options.series[0].data[1].name = "";
            } else if (
              !this.CetChart_1.config.options.series[0].data[0].value &&
              this.CetChart_1.config.options.series[0].data[1].value
            ) {
              this.CetChart_1.config.options.series[0].data[0].name = "";
            }
          }
          // 需量合理性分布统计
          if (response.data.demandProperCountVo) {
            // 容量-计费方式不合理
            this.CetChart_1.config.options.series[1].data[0].value =
              response.data.demandProperCountVo.volumeNotProper &&
              response.data.demandProperCountVo.volumeNotProper.toFixed2(2);

            this.CetChart_1.config.options.series[1].data[0].rete = (
              (Number(this.CetChart_1.config.options.series[1].data[0].value) /
                Number(
                  this.CetChart_1.config.options.series[0].data[0].value
                )) *
              100
            ).toFixed2(2);
            if (
              String(this.CetChart_1.config.options.series[1].data[0].rete) ==
              "NaN"
            ) {
              this.CetChart_1.config.options.series[1].data[0].rete = 0;
            }
            // 容量合理
            this.CetChart_1.config.options.series[1].data[1].value = (
              response.data.basicFeeTypeCount.volume -
              response.data.demandProperCountVo.volumeNotProper
            ).toFixed2(2);
            //  需量-计费方式不合理
            this.CetChart_1.config.options.series[1].data[2].value =
              response.data.demandProperCountVo.demandNotProper &&
              response.data.demandProperCountVo.demandNotProper.toFixed2(2);
            this.CetChart_1.config.options.series[1].data[2].rete = (
              (Number(this.CetChart_1.config.options.series[1].data[2].value) /
                Number(
                  this.CetChart_1.config.options.series[0].data[1].value
                )) *
              100
            ).toFixed2(2);

            if (
              String(this.CetChart_1.config.options.series[1].data[2].rete) ===
              "NaN"
            ) {
              this.CetChart_1.config.options.series[1].data[2].rete = 0;
            }
            // 需量合理
            this.CetChart_1.config.options.series[1].data[3].value = (
              response.data.basicFeeTypeCount.demand -
              response.data.demandProperCountVo.demandNotProper
            ).toFixed2(2);
          }
          // 列表数据
          this.CetTable_1.data = response.data.quarterReportList;
          // 处理数据显示
          this.CetTable_1.data.forEach(item => {
            item.volumeText =
              item.volume &&
              item.volume
                .map(item => {
                  if (item) {
                    return item.toFixed(2);
                  } else {
                    return "--";
                  }
                })
                .join("/");
            item.rateVolumeText =
              item.rateVolume &&
              item.rateVolume
                .map(item => {
                  if (item) {
                    return item.toFixed(2);
                  } else {
                    return "--";
                  }
                })
                .join("/");
            item.maxDemandText =
              item.maxDemand &&
              item.maxDemand
                .map(item => {
                  if (item) {
                    return item.toFixed(2);
                  } else {
                    return "--";
                  }
                })
                .join("/");
            item.savedValue = item.savedValue && item.savedValue.toFixed(2);
          });
        }
      });
    },
    // 判断next按钮是否生效
    nextDisable() {
      var currentSeason;
      if (this.$moment().month() < 3) {
        currentSeason = 1;
      } else if (this.$moment().month() < 6) {
        currentSeason = 2;
      } else if (this.$moment().month() < 9) {
        currentSeason = 3;
      } else {
        currentSeason = 4;
      }
      const date = this.$moment(this.CetDatePicker_1.val);
      if (date.year() < this.$moment().year()) {
        this.CetButton_next.disable_in = false;
      } else if (date.year() == this.$moment().year()) {
        if (currentSeason == 1) {
          this.CetButton_next.disable_in = true;
        } else if (this.ElSelect_1.value < currentSeason - 1) {
          this.CetButton_next.disable_in = false;
        } else {
          this.CetButton_next.disable_in = true;
        }
      } else {
        this.CetButton_next.disable_in = true;
      }
    },
    // 判断季度是否可选
    deteChange(val) {
      if (
        this.$moment(val).startOf("year").valueOf() >=
        this.$moment().startOf("year").valueOf()
      ) {
        if (this.$moment().month() < 3) {
          this.ElOption_1.options_in = [];
          this.ElSelect_1.value = null;
        } else if (this.$moment().month() < 6) {
          this.ElOption_1.options_in = [
            {
              id: 1,
              text: $T("一季度")
            }
          ];
          this.ElSelect_1.value = 1;
        } else if (this.$moment().month() < 9) {
          this.ElOption_1.options_in = [
            {
              id: 1,
              text: $T("一季度")
            },
            {
              id: 2,
              text: $T("二季度")
            }
          ];
          this.ElSelect_1.value = 1;
        } else {
          this.ElOption_1.options_in = [
            {
              id: 1,
              text: $T("一季度")
            },
            {
              id: 2,
              text: $T("二季度")
            },
            {
              id: 3,
              text: $T("三季度")
            }
          ];
          this.ElSelect_1.value = 1;
        }
      } else {
        this.ElOption_1.options_in = [
          {
            id: 1,
            text: $T("一季度")
          },
          {
            id: 2,
            text: $T("二季度")
          },
          {
            id: 3,
            text: $T("三季度")
          },
          {
            id: 4,
            text: $T("四季度")
          }
        ];
      }
    },
    CetButton_1_statusTrigger_out(val) {
      this.$router.push({
        name: "demandmonitor",
        params: this.tableItem
      });
    },
    CetTable_1_outputData_out(val) {},
    CetTable_1_record_out(val) {
      this.tableItem = val;
      if (val.id != -1) {
        this.CetButton_1.disable_in = false;
      } else {
        this.CetButton_1.disable_in = true;
      }
    },
    CetButton_prv_statusTrigger_out(val) {
      if (!this.ElSelect_1.value || this.ElSelect_1.value == 1) {
        const date = this.$moment(this.CetDatePicker_1.val);
        this.CetDatePicker_1.val = date.subtract(1, "year").valueOf();
        this.ElSelect_1.value = 4;
      } else {
        this.ElSelect_1_change_out(this.ElSelect_1.value - 1);
      }
    },
    CetButton_next_statusTrigger_out(val) {
      if (this.ElSelect_1.value == 4) {
        const date = this.$moment(this.CetDatePicker_1.val);
        this.CetDatePicker_1.val = date.add(1, "year").valueOf();
        this.ElSelect_1.value = 1;
      } else {
        this.ElSelect_1_change_out(this.ElSelect_1.value + 1);
      }
    },
    ElSelect_1_change_out(val) {
      if (val) {
        this.ElSelect_1.value = val;
        this.getData();
      }
    }
  },
  created: function () {},
  mounted: function () {
    this.deteChange(this.$moment().add(0, "d").valueOf());
    this.nextDisable();
    this.getData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.chartsBox {
  height: 350px;
}
.tag {
  display: inline-block;
  text-align: center;
  line-height: 30px;
  border-width: 0px;
  width: 70px;
  height: 30px;
  background: inherit;
  border: none;
  border-radius: 5px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 14px;
  color: #ffffff;
}
.tooltipInfo {
  font-size: 13px;
}
.circle {
  display: inline-block;
  width: 16px;
  height: 16px;
  font-size: 14px;
  line-height: 16px;
  border-radius: 50%;
  text-align: center;
  @include background_color(ZS);
  color: #fff;
}
</style>
