<template>
  <div class="page eem-common">
    <el-container style="height: 100%">
      <el-aside width="315px" class="eem-aside">
        <div class="fullfilled flex-column">
          <div class="eem-group-list flex-auto">
            <el-tooltip
              v-for="(item, key) in teamList"
              :key="key"
              :content="item.name"
              effect="light"
              placement="top"
            >
              <div
                :class="['group-item', { active: key === teamIndex }]"
                @click="handleClick_team_out(item, key)"
              >
                {{ item.name }}
              </div>
            </el-tooltip>
          </div>
          <div class="mtJ3">
            <el-dropdown class="fr more mlJ3" @command="handleCommand">
              <span class="el-dropdown-link">
                {{ $T("更多") }}
                <i class="el-icon-arrow-up el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="edit">
                  {{ $T("编辑") }}
                </el-dropdown-item>
                <el-dropdown-item command="delete" class="delete">
                  {{ $T("删除") }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <!-- add按钮组件 -->
            <CetButton
              class="fr mlJ1"
              v-bind="CetButton_addTeam"
              v-on="CetButton_addTeam.event"
            ></CetButton>
            <!-- edit按钮组件 -->
            <!-- <CetButton
              class="fr mlJ1"
              v-bind="CetButton_edit"
              v-on="CetButton_edit.event"
            ></CetButton> -->
            <!-- delete按钮组件 -->
            <!-- <CetButton
              class="fr mlJ1"
              v-bind="CetButton_delete"
              v-on="CetButton_delete.event"
            ></CetButton> -->
            <CetButton
              v-bind="CetButton_import"
              v-on="CetButton_import.event"
              class="fr"
            ></CetButton>
          </div>
        </div>
      </el-aside>
      <el-main class="fullheight mlJ3 padding0">
        <div class="fullheight flex-column">
          <div>
            <el-tooltip :content="teamName" effect="light">
              <span
                class="common-title-H1 text-ellipsis"
                style="display: inline-block; max-width: 100%"
              >
                {{ teamName || "--" }}
              </span>
            </el-tooltip>
          </div>
          <div class="flex-auto eem-container mtJ3 flex-column">
            <div class="clearfix">
              <!-- add按钮组件 -->
              <CetButton
                class="fr"
                v-bind="CetButton_addPerson"
                v-on="CetButton_addPerson.event"
                :disable_in="!teamList?.length"
              ></CetButton>
            </div>
            <CetTable
              class="flex-auto mtJ3"
              :data.sync="CetTable_user.data"
              :dynamicInput.sync="CetTable_user.dynamicInput"
              v-bind="CetTable_user"
              v-on="CetTable_user.event"
            >
              <template v-for="item in Columns_inspectObj">
                <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
              </template>
              <ElTableColumn v-bind="ElTableColumn_operate">
                <template slot-scope="scope">
                  <span
                    class="eem-row-handle fl mrJ3"
                    @click="handleClick_edit_out(scope.row, scope.$index)"
                  >
                    {{ $T("编辑") }}
                  </span>
                  <span
                    class="row-delete fl"
                    @click="handleClick_delete_out(scope.row, scope.$index)"
                  >
                    {{ $T("删除") }}
                  </span>
                </template>
              </ElTableColumn>
            </CetTable>
          </div>
        </div>
      </el-main>
    </el-container>

    <!-- 编辑分组 -->
    <editTeamDialog
      v-bind="editTeamDialog"
      @finishTrigger_out="editTeamDialog_finishTrigger_out"
    ></editTeamDialog>
    <!-- 新建分组 -->
    <addTeamDialog
      v-bind="addTeamDialog"
      @finishTrigger_out="addTeamDialog_finishTrigger_out"
    ></addTeamDialog>
    <!-- 新建用户 -->
    <addUserDialog
      v-bind="addUserDialog"
      @finishTrigger_out="addUserDialog_finishTrigger_out"
    ></addUserDialog>
    <!-- 修改用户 -->
    <editUserDialog
      v-bind="editUserDialog"
      @finishTrigger_out="editUserDialog_finishTrigger_out"
    ></editUserDialog>
    <UploadDialog v-bind="uploadDialog" v-on="uploadDialog.event" />
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom.js";
import editTeamDialog from "./dialogs/editTeamDialog";
import addTeamDialog from "./dialogs/addTeamDialog";
import addUserDialog from "./dialogs/addUserDialog";
import editUserDialog from "./dialogs/editUserDialog";
import UploadDialog from "eem-components/uploadDialog";

export default {
  name: "signinmanage",
  components: {
    editTeamDialog,
    addTeamDialog,
    addUserDialog,
    editUserDialog,
    UploadDialog
  },
  data() {
    return {
      clickTeam: null,
      teamList: [],
      teamName: "",
      // delete组件
      CetButton_delete: {
        visible_in: true,
        disable_in: false,
        title: $T("删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_delete_statusTrigger_out
        }
      },
      // edit组件
      CetButton_edit: {
        visible_in: true,
        disable_in: false,
        title: $T("编辑"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_edit_statusTrigger_out
        }
      },
      // add组件
      CetButton_addTeam: {
        visible_in: true,
        disable_in: false,
        title: $T("新建"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_addTeam_statusTrigger_out
        }
      },
      // add组件
      CetButton_addPerson: {
        visible_in: true,
        // disable_in: false,
        title: $T("新建"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_addPerson_statusTrigger_out
        }
      },
      // 批量导入
      CetButton_import: {
        visible_in: true,
        disable_in: false,
        title: $T("批量导入"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_import_statusTrigger_out
        }
      },
      teamIndex: 0, // 当前点击的分组
      // inspectObj表格组件
      CetTable_user: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {
          record_out: this.CetTable_user_record_out,
          outputData_out: this.CetTable_user_outputData_out
        }
      },
      tableData: [], // 签到点表格数据
      Columns_inspectObj: [
        {
          prop: "name", // 支持path a[0].b
          label: $T("成员名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160", //绝对宽度
          formatter: this.formatter
        }
      ],
      ElTableColumn_operate: {
        //type: "",      // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        width: 120,
        flexd: "right",
        showOverflowTooltip: true
        // width: "60" //绝对宽度
      },
      // 编辑分组弹窗
      editTeamDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      // 新建分组弹窗
      addTeamDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      // 新建用户弹窗
      addUserDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      // 编辑密码弹窗
      editUserDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      uploadDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        extensionNameList_in: [".xlsx"],
        hideDownload: false,
        dialogTitle: $T("批量导入"),
        event: {
          download: this.uploadDialog_download,
          uploadFile: this.uploadDialog_uploadFile
        }
      }
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    projectTenantId() {
      return this.$store.state.projectTenantId;
    },
    userInfo() {
      var vm = this;
      return vm.$store.state.userInfo;
    },
    tenantId() {
      var vm = this;
      return vm.$store.state.tenantId;
    }
  },
  watch: {},
  methods: {
    // 删除班组
    CetButton_delete_statusTrigger_out(val) {
      var _this = this;
      if (!this.clickTeam) {
        return;
      }
      if (this.clickTeam.users.length > 0) {
        this.$message.warning($T("班组下面存在成员，无法进行删除操作"));
        return;
      }
      var params = {
        id: this.clickTeam.id,
        name: this.clickTeam.name,
        tenantId: this.clickTeam.tenantId
      };

      _this.$confirm($T("确定要删除所选班组吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        beforeClose: function (action, instance, done) {
          if (action === "confirm") {
            customApi.deleteInspectorTeam(params).then(res => {
              if (res.code === 0) {
                _this.$message.success($T("删除班组成功"));
                _this.init();
              }
            });
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action !== "confirm") {
            _this.$message({
              type: "info",
              message: $T("取消删除！")
            });
          }
        }
      });
    },
    // 编辑分组
    CetButton_edit_statusTrigger_out(val) {
      this.editTeamDialog.inputData_in = this._.cloneDeep(this.clickTeam);
      this.editTeamDialog.openTrigger_in = new Date().getTime();
    },
    // 新建分组
    CetButton_addTeam_statusTrigger_out(val) {
      this.addTeamDialog.openTrigger_in = new Date().getTime();
    },
    // 新建用户
    CetButton_addPerson_statusTrigger_out(val) {
      this.addUserDialog.openTrigger_in = new Date().getTime();
    },
    // position表格输出
    CetTable_user_record_out(val) {},
    CetTable_user_outputData_out(val) {},
    //编辑分组完之后刷新页面
    editTeamDialog_finishTrigger_out() {
      this.init();
    },
    //新建分组完之后刷新页面
    addTeamDialog_finishTrigger_out() {
      this.init();
    },
    //新建用户完之后刷新页面
    async addUserDialog_finishTrigger_out(data) {
      if (!this.clickTeam) {
        return;
      }
      //获取所有的用户组和用户
      let targetArr = [];
      const queryGroupParams = {
        loadChildren: true,
        removeRootUser: true,
        tenantId: this.projectTenantId
      };
      const res = await customApi.queryProjectUserAndGroup(queryGroupParams);
      if (res.code === 0 && res.data.length) {
        const data = res.data;
        const target = data.find(item => item.id === this.clickTeam.id);
        targetArr = target.children;
      }

      new Promise((res, err) => {
        const users = targetArr || [];
        let userIds = users.map(item => {
          return item.id;
        });
        userIds = userIds.concat([data.id]);
        const params = {
          id: this.clickTeam.id,
          name: this.clickTeam.name,
          userIds: userIds
        };
        this.editInspectorTeam_out(params, res);
      }).then(res => {
        this.init();
      });
    },
    //编辑用户完之后刷新页面
    editUserDialog_finishTrigger_out() {
      this.init();
    },
    //过滤表格数据
    formatter(row, column, cellValue) {
      return cellValue || "--";
    },
    //点击修改密码
    handleClick_edit_out(val) {
      this.editUserDialog.inputData_in = this._.cloneDeep(val);
      this.editUserDialog.openTrigger_in = new Date().getTime();
    },
    //点击删除成员按钮
    handleClick_delete_out(val) {
      var _this = this;
      var params = {
        id: val.id,
        name: val.name,
        tenantId: val.tenantId
      };
      _this.$confirm($T("确定要删除所选成员吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        beforeClose: function (action, instance, done) {
          if (action === "confirm") {
            customApi.deleteInspectorUser(params).then(res => {
              if (res.code === 0) {
                _this.$message.success($T("删除用户成功"));
                _this.init();
              }
            });
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action !== "confirm") {
            _this.$message({
              type: "info",
              message: $T("取消删除！")
            });
          }
        }
      });
    },
    //点击切换班组
    handleClick_team_out(team, index) {
      this.teamIndex = index;
      this.clickTeam = team;
      this.teamName = team.name;
      this.queryInspectorUser_out(team);
    },
    // 初始化
    init() {
      this.getTeam_queryInspectorTeamWithOutUser();
    },
    // 获取班组信息
    getTeam_queryInspectorTeamWithOutUser() {
      const _this = this;
      const params = {};
      customApi.queryInspectorTeamWithOutUser(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          _this.teamList = _this._.cloneDeep(data);
          const teamData = this._.get(res, "data[0]", {});
          if (_this.clickTeam) {
            let clickTeam = teamData;
            let teamIndex = 0;
            _this.teamList.forEach((item, index) => {
              if (_this.clickTeam.id === item.id) {
                clickTeam = item;
                teamIndex = index;
              }
            });
            _this.handleClick_team_out(clickTeam, teamIndex);
          } else {
            _this.handleClick_team_out(teamData, 0);
          }
        }
      });
    },
    // 获取选中班组下面成员列表信息
    queryInspectorUser_out(team) {
      const _this = this;
      if (!team || !team.id) {
        return;
      }
      const params = {
        id: team.id
      };
      customApi.queryInspectorUser(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          _this.CetTable_user.data = _this._.cloneDeep(data);
        }
      });
    },
    // 新增成员之后绑定选择班组里面
    editInspectorTeam_out(data, callback) {
      if (!data) {
        callback && callback();
        return;
      }
      const params = data;
      customApi.editInspectorTeam(params).then(res => {
        if (res.code === 0) {
          callback && callback();
        }
      });
    },
    // 批量导入
    CetButton_import_statusTrigger_out() {
      this.uploadDialog.openTrigger_in = new Date().getTime();
    },
    // 下载模板
    uploadDialog_download(val) {
      var url = "/eem-service/v1/inspector/download/Template";
      common.downExcel(url, {}, this.token);
    },
    uploadDialog_uploadFile(val) {
      const formData = new FormData();
      formData.append("file", val.file);
      customApi.queryImportItem(formData, this.tenantId).then(response => {
        if (response.code === 0) {
          this.$message({
            type: "success",
            message: $T("导入成功")
          });
          this.uploadDialog.closeTrigger_in = new Date().getTime();
          this.getTeam_queryInspectorTeamWithOutUser();
        }
      });
    },
    handleCommand(command) {
      if (command === "delete") {
        this.CetButton_delete_statusTrigger_out();
      } else if (command === "edit") {
        this.CetButton_edit_statusTrigger_out();
      }
    }
  },
  created() {},
  mounted() {},
  activated() {
    this.init();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .row-delete {
    @include font_color(Sta3);
    cursor: pointer;
  }
  .more {
    @include line_height(Hm);
  }
}
.delete {
  @include font_color(Sta3);
}
</style>
