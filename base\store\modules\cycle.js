// 当前属于非自然周期起始的起始时间
import moment from "moment";
export default {
  namespaced: true,
  state() {
    return {
      startHourTime: moment()
        .startOf("hour")
        .valueOf(),
      startDayTime: moment()
        .startOf("day")
        .valueOf(),
      startWeekTime: moment()
        .startOf("week")
        .valueOf(),
      startMonthTime: moment()
        .startOf("month")
        .valueOf(),
      startYearTime: moment()
        .startOf("year")
        .valueOf(),
      startHoursIndex: 0,
      startDayIndex: 1,
      startWeekIndex: 1,
      startMonthIndex: 0
    };
  },
  mutations: {
    setStartDayTime(state, val) {
      state.startDayTime = val;
    },
    setStartWeekTime(state, val) {
      state.startWeekTime = val;
    },
    setStartMonthTime(state, val) {
      state.startMonthTime = val;
    },
    setStartYearTime(state, val) {
      state.startYearTime = val;
    },
    setStartHoursIndex(state, val) {
      state.startHoursIndex = val;
    },
    setStartDayIndex(state, val) {
      state.startDayIndex = val;
    },
    setStartWeekIndex(state, val) {
      state.startWeekIndex = val;
    },
    setStartMonthIndex(state, val) {
      state.startMonthIndex = val;
    }
  },
  actions: {}
};
