<template>
  <el-container class="KnowledgeTable flex-column eem-cont">
    <div class="mbJ2 flex-row">
      <el-tooltip :content="title" effect="light" placement="top">
        <div class="text-ellipsis lh32">
          <span class="common-title-H2" style="display: inline">
            {{ title || "--" }}
          </span>
        </div>
      </el-tooltip>
      <div style="min-width: 480px" class="flex-auto">
        <div v-if="knowledgefilePermission" class="fr">
          <template v-permission="'knowledgedirectory_update'">
            <CetButton
              v-bind="CetButton_rename"
              v-on="CetButton_rename.event"
              class="fr"
            >
              {{ $T("重命名") }}
            </CetButton>
          </template>
          <template v-permission="'knowledgefile_update'">
            <CetButton
              v-bind="CetButton_moveTo"
              v-on="CetButton_moveTo.event"
              class="fr mrJ1"
            ></CetButton>
            <CetButton
              v-bind="CetButton_copyTo"
              v-on="CetButton_copyTo.event"
              class="fr mrJ1"
            ></CetButton>
          </template>
          <template v-permission="'knowledgefile_download'">
            <CetButton
              v-bind="CetButton_download"
              v-on="CetButton_download.event"
              class="fr mrJ1"
            ></CetButton>
          </template>
          <template v-permission="'knowledgefile_upload'">
            <el-upload
              class="fr mrJ1"
              :action="url"
              :data="data"
              :headers="headers"
              :before-upload="beforeUpload"
              :on-success="successUpload"
              :show-file-list="false"
            >
              <el-button size="small" type="primary">
                {{ $T("上传") }}
              </el-button>
            </el-upload>
          </template>
          <template
            v-permission="'knowledgedirectory_delete' || 'knowledgefile_delete'"
          >
            <CetButton
              :disable_in="deleteBtnDisabled"
              v-bind="CetButton_delete"
              v-on="CetButton_delete.event"
              class="fr mrJ1"
            >
              {{ $T("删除") }}
            </CetButton>
          </template>
        </div>
      </div>
    </div>
    <div class="flex-auto padding0">
      <CetTable
        :data.sync="CetTable_knowledge.data"
        :dynamicInput.sync="CetTable_knowledge.dynamicInput"
        v-bind="CetTable_knowledge"
        v-on="CetTable_knowledge.event"
        @select="select"
        @select-all="selectAll"
      >
        <el-table-column
          type="selection"
          width="40"
          header-align="center"
          align="center"
        ></el-table-column>
        <template v-for="(column, index) in columns">
          <template v-if="!column.disabled">
            <el-table-column
              header-align="left"
              align="left"
              show-overflow-tooltip
              :key="index"
              v-bind="column"
            ></el-table-column>
          </template>
        </template>
        <ElTableColumn
          :label="$T('操作')"
          width="180"
          headerAlign="left"
          align="left"
          fixed="right"
        >
          <template slot-scope="scope">
            <span
              class="eem-row-handle fl mrJ3"
              :class="{
                'eem-row-no-handle': !hasModifyFileAuth
              }"
              @click.stop="rename(scope.row)"
            >
              {{ $T("重命名") }}
            </span>
            <span
              class="eem-row-handle fl"
              :class="{
                'eem-row-no-handle': !hasDownloadFileAuth
              }"
              @click.stop="download(scope.row)"
            >
              {{ $T("下载") }}
            </span>
          </template>
        </ElTableColumn>
      </CetTable>
    </div>
  </el-container>
</template>
<script>
import common from "eem-utils/common";
import commonApi from "@/api/custom";
export default {
  name: "KnowledgeTable",
  components: {},
  props: {
    selectObj: Object,
    refresh: Number
  },
  data(vm) {
    return {
      url: "",
      flag: true,
      data: {
        groupName: null
      },
      headers: {
        Authorization: "Bearer " + vm.$store.state.token,
        projectId: vm.$store.state.projectId
      },
      selectList: [],
      fileList: [],
      maxSize: 0,
      fileTypes: [],
      title: "", // 当前选中tab的名称
      tabId: "", // 当前选中tab的id
      columns: [],
      // knowledge表格组件
      CetTable_knowledge: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "getFolder",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "filemodel",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "fathernode_in", operator: "EQ", prop: "fathernode_id" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: { fathernode_in: 22 },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showCheckBox: true,
        showPagination: false,
        paginationCfg: {
          small: true,
          background: true,
          layout: "total, prev, pager, next, jumper",
          pageSize: 50
        },
        exportFileName: "",
        defaultSort: {
          prop: "logtime",
          order: "descending"
        },
        event: {
          record_out: this.CetTable_knowledge_record_out,
          outputData_out: this.CetTable_knowledge_outputData_out
        }
      },
      // 重命名按钮
      CetButton_rename: {
        visible_in: true,
        disable_in: false,
        title: $T("重命名"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_rename_statusTrigger_out
        }
      },
      // 删除按钮
      CetButton_delete: {
        visible_in: true,
        title: $T("删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_delete_statusTrigger_out
        }
      },
      // 上传按钮
      CetButton_upload: {
        visible_in: true,
        disable_in: false,
        title: $T("上传"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_upload_statusTrigger_out
        }
      },
      // 下载按钮
      CetButton_download: {
        visible_in: true,
        disable_in: true,
        title: $T("下载"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_download_statusTrigger_out
        }
      },
      // 移动到按钮
      CetButton_moveTo: {
        visible_in: true,
        disable_in: true,
        title: $T("移动到"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_moveTo_statusTrigger_out
        }
      },
      // 上传按钮
      CetButton_copyTo: {
        visible_in: true,
        disable_in: true,
        title: $T("复制到"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_copyTo_statusTrigger_out
        }
      }
    };
  },
  computed: {
    systemCfg() {
      return this.$store.state.systemCfg;
    },
    userInfo() {
      return this.$store.state.userInfo;
    },
    hasModifyCatalogAuth() {
      return this.hasAuth(1025);
    },
    hasDeleteCatalogAuth() {
      return this.hasAuth(1026);
    },
    hasUploadFileAuth() {
      return this.hasAuth(1028);
    },
    hasDownloadFileAuth() {
      return this.hasAuth(1031);
    },
    hasModifyFileAuth() {
      return this.hasAuth(1029);
    },
    hasDeleteFileAuth() {
      return this.hasAuth(1030);
    },
    deleteBtnDisabled() {
      const selectedRecords = this.selectList || [];

      if (selectedRecords.length === 0 && this.hasDeleteCatalogAuth) {
        return false;
      }

      if (selectedRecords.length !== 0 && this.hasDeleteFileAuth) {
        return false;
      }

      return true;
    },
    knowledgefilePermission() {
      return (
        this.$checkPermission("knowledgefile_upload") ||
        this.$checkPermission("knowledgefile_update") ||
        this.$checkPermission("knowledgefile_download")
      );
    }
  },
  watch: {
    selectObj(val) {
      this.CetTable_knowledge.dynamicInput.fathernode_in = val.id;
      this.CetTable_knowledge.queryTrigger_in = new Date().getTime();
      const title = val.label;
      let flag = true;
      if (title.length > 20) {
        flag = false;
      }
      this.title = title;
      this.flag = flag;
      this.data.groupName = val.label;
      this.tabId = val.id;

      this.selectList = [];
      this.disable();
    },
    refresh(val) {
      this.CetTable_knowledge.queryTrigger_in = new Date().getTime();
    }
  },
  methods: {
    hasAuth(authId) {
      const userInfo = this.userInfo;
      // 超级管理员不用验证
      if (userInfo.name === "ROOT") {
        return true;
      }

      const role = userInfo.roles[0];
      const auths = role.auths || [];
      return auths.indexOf(authId) > -1;
    },
    beforeUpload(text) {
      const isLimit10M = text.size / 1024 / 1024 < this.maxSize;
      if (!isLimit10M) {
        this.$message.warning($T("附件不能大于{0}M", this.maxSize));
        return false;
      }
    },
    successUpload(res) {
      this.disable();
      if (res.code == 0) {
        this.$message({ type: "success", message: $T("上传文件成功!") });
        this.$emit("refresh", 2);
      } else {
        this.$message({ message: res.msg });
      }
    },
    // 选中的tab重命名
    CetButton_rename_statusTrigger_out(val) {
      if (this.selectList.length != 0) {
        this.$message({ message: $T("当前为目录重命名不可选择文件") });
        return;
      }
      this.$emit("rename");
    },
    // 删除选中的tab
    CetButton_delete_statusTrigger_out(val) {
      console.log(this.selectList, "1039431274124");
      if (this.selectList.length != 0) {
        const newList = [];
        this.selectList.forEach(item => {
          newList.push(item.id);
        });
        this.$confirm($T("确认要删除选中文件?"), $T("提示"), {
          cancelButtonText: $T("取消"),
          confirmButtonText: $T("确定"),
          type: "warning"
        })
          .then(() => {
            commonApi.deleteFilemodel(newList).then(res => {
              if (res.code == 0) {
                this.disable();
                this.selectList = [];
                this.$message({ type: "success", message: $T("删除成功!") });
                this.$emit("refresh", 2);
              }
            });
          })
          .catch(() => {
            this.$message({ type: "info", message: $T("已取消") });
          });
      } else {
        this.$confirm($T("确认要删除该目录?"), $T("提示"), {
          cancelButtonText: $T("取消"),
          confirmButtonText: $T("确定"),
          type: "warning"
        })
          .then(() => {
            commonApi.deleteFilemodel([this.tabId]).then(res => {
              if (res.code == 0) {
                this.$message({ type: "success", message: $T("删除成功!") });
                this.$emit("refresh");
              }
            });
          })
          .catch(() => {
            this.$message({ type: "info", message: $T("已取消") });
          });
      }
    },
    // 上传按钮
    CetButton_upload_statusTrigger_out() {
      console.log("上传按钮", this.tabId);
    },
    // 下载按钮
    CetButton_download_statusTrigger_out() {
      const newList = [];
      const selectList = this.selectList;
      selectList.forEach(item => {
        newList.push(item.id);
      });
      commonApi.knowladgeDownloadMany(newList);
    },
    // 移动到按钮
    CetButton_moveTo_statusTrigger_out() {
      this.$emit("move", this.selectList);
    },
    // 复制到按钮
    CetButton_copyTo_statusTrigger_out() {
      this.$emit("copy", this.selectList);
    },
    // 某一项重命名
    rename(val) {
      this.$emit("rename", val);
    },
    // 下载某一项
    download(val) {
      commonApi.knowladgeDownload({
        fileName: val.storagepath
      });
    },
    // knowledge表格输出
    CetTable_knowledge_record_out(val) {},
    CetTable_knowledge_outputData_out(val) {},
    select(selection, row) {
      if (selection.length === 0) {
        this.disable();
        this.selectList = selection;
      } else {
        this.CetButton_download.disable_in = false;
        this.CetButton_moveTo.disable_in = false;
        this.CetButton_copyTo.disable_in = false;
        this.selectList = selection;
      }
    },
    selectAll(selection) {
      if (selection.length === 0) {
        this.disable();
        this.selectList = selection;
      } else {
        this.CetButton_download.disable_in = false;
        this.CetButton_moveTo.disable_in = false;
        this.CetButton_copyTo.disable_in = false;
        this.selectList = selection;
      }
    },

    // 文件大小列格式化函数
    fileFormatter(row, column, val) {
      if (val) {
        if (val >= Math.pow(2, 10) && val < Math.pow(2, 20)) {
          val = (val / Math.pow(2, 10)).toFixed(2) + "KB";
        } else if (val >= Math.pow(2, 20)) {
          val = (val / Math.pow(2, 20)).toFixed(2) + "M";
        } else {
          val = val + "B";
        }
        return val;
      } else {
        return "--";
      }
    },
    disable() {
      this.CetButton_download.disable_in = true;
      this.CetButton_moveTo.disable_in = true;
      this.CetButton_copyTo.disable_in = true;
    }
  },
  created() {
    const columns = [
      {
        type: "",
        prop: "name",
        minWidth: 200,
        width: "",
        label: $T("标题"),
        sortable: false,
        showOverflowTooltip: true,
        formatter: null
      },
      {
        type: "",
        prop: "fileformat$text",
        minWidth: 100,
        width: "",
        label: $T("类型"),
        sortable: false,
        showOverflowTooltip: true,
        formatter: null
      },
      {
        type: "",
        prop: "size",
        minWidth: 100,
        width: "",
        label: $T("大小"),
        sortable: false,
        showOverflowTooltip: true,
        formatter: this.fileFormatter
      },
      {
        type: "",
        prop: "logtime",
        minWidth: "",
        width: 200,
        label: $T("发布时间"),
        sortable: false,
        showOverflowTooltip: true,
        formatter: common.formatDateColumn
      }
      // {
      //   type: "",
      //   prop: "rename",
      //   minWidth: "",
      //   width: 80,
      //   label: "重命名",
      //   sortable: false,
      //   headerAlign: "center",
      //   align: "center",
      //   showOverflowTooltip: true,
      //   formatter: null,
      //   custom: "button",
      //   buttonType: "primary",
      //   buttonIcon: "el-icon-edit-outline",
      //   onButtonClick: this.rename,
      //   disabled: !this.hasModifyFileAuth
      // },
      // {
      //   type: "",
      //   prop: "download",
      //   minWidth: "",
      //   width: 80,
      //   label: "下载",
      //   sortable: false,
      //   headerAlign: "center",
      //   align: "center",
      //   formatter: null,
      //   custom: "button",
      //   buttonType: "primary",
      //   buttonIcon: "el-icon-download",
      //   onButtonClick: this.download,
      //   disabled: !this.hasDownloadFileAuth
      // }
    ];
    this.title = this.selectObj.label;
    this.tabId = this.selectObj.id;
    this.columns = columns;

    commonApi
      .getFileTypes({
        rootLabel: "fileformat"
      })
      .then(res => {
        this.fileTypes = res.data || [];
      });
    commonApi.getMaxSize({}).then(res => {
      this.maxSize = res.data / Math.pow(2, 20) || 10;
    });

    this.url = `${window.location.origin}/eem-service/v1/knowledge/upload`;
  },
  mounted() {},
  activated: function () {}
};
</script>
<style lang="scss" scoped>
.KnowledgeTable {
  height: 100%;
  .sysBook {
    float: left;
    line-height: 48px;
    margin: 0px;
    @include margin_left(J1);
    font-weight: normal;
  }
}
</style>
