﻿<template>
  <div class="page eem-common">
    <el-container style="height: 100%; min-width: 1024px">
      <el-aside width="315px" class="eem-aside flex-column">
        <div class="mbJ1">
          <customElSelect
            v-model="ElSelect_1.value"
            v-bind="ElSelect_1"
            v-on="ElSelect_1.event"
            prefix_in="能耗类型"
          >
            <ElOption
              v-for="item in ElOption_1.options_in"
              :key="item[ElOption_1.key]"
              :label="item[ElOption_1.label]"
              :value="item[ElOption_1.value]"
              :disabled="item[ElOption_1.disabled]"
            ></ElOption>
          </customElSelect>
        </div>
        <CetGiantTree
          class="cetGiantTree"
          v-show="!dimTree.showCheckbox"
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
        ></CetGiantTree>
        <CetGiantTree
          v-show="dimTree.showCheckbox"
          class="cetGiantTree"
          v-bind="CetGiantTree_2"
          v-on="CetGiantTree_2.event"
        ></CetGiantTree>
      </el-aside>
      <el-container class="mlJ3 padding0 fullheight flex-auto">
        <el-header height="auto" class="lh32 padding0 mbJ3">
          <el-tooltip effect="light" :content="nodeName" placement="top-start">
            <div class="common-title-H2 text-ellipsis" style="width: 250px">
              {{ nodeName || "--" }}
            </div>
          </el-tooltip>
        </el-header>
        <el-container class="eem-container flex-auto eem-min-width">
          <el-header height="auto" class="padding0">
            <div class="clearfix fr">
              <div class="basic-box fl">
                <customElSelect
                  v-model="ElSelect_type.value"
                  v-bind="ElSelect_type"
                  v-on="ElSelect_type.event"
                  class="mrJ1 fl"
                  prefix_in="分析类型"
                >
                  <ElOption
                    v-for="item in ElOption_type.options_in"
                    :key="item[ElOption_type.key]"
                    :label="item[ElOption_type.label]"
                    :value="item[ElOption_type.value]"
                    :disabled="item[ElOption_type.disabled]"
                  ></ElOption>
                </customElSelect>
                <ElCheckboxGroup
                  v-show="ElSelect_type.value === 1"
                  class="eem-checkbox fl mrJ1 lh32"
                  style="height: 32px"
                  v-model="ElCheckboxGroup_1.value"
                  v-bind="ElCheckboxGroup_1"
                  v-on="ElCheckboxGroup_1.event"
                >
                  <ElCheckbox
                    v-for="item in ElCheckboxList_1.options_in"
                    :key="item[ElCheckboxList_1.key]"
                    :label="item[ElCheckboxList_1.label]"
                    :disabled="item[ElCheckboxList_1.disabled]"
                  >
                    {{ item[ElCheckboxList_1.text] }}
                  </ElCheckbox>
                </ElCheckboxGroup>
                <ElCheckboxGroup
                  v-show="ElSelect_type.value === 2"
                  class="eem-checkbox fl mrJ1 lh32"
                  style="height: 32px"
                  v-model="ElCheckboxGroup_2.value"
                  v-bind="ElCheckboxGroup_2"
                  v-on="ElCheckboxGroup_2.event"
                >
                  <ElCheckbox
                    v-for="item in ElCheckboxList_2.options_in"
                    :key="item[ElCheckboxList_2.key]"
                    :label="item[ElCheckboxList_2.label]"
                    :disabled="item[ElCheckboxList_2.disabled]"
                  >
                    {{ item[ElCheckboxList_2.text] }}
                  </ElCheckbox>
                </ElCheckboxGroup>
              </div>
              <CustomDatePicker
                ref="CustomDatePicker"
                class="fl mrJ1"
                @change="CustomDatePicker_1_change"
                :val="CustomDatePicker_1.queryTime"
                :dataConfig="CustomDatePicker_1.dataConfig"
                :customElSelect="false"
              ></CustomDatePicker>
              <!-- 请填写组件含义按钮组件 -->
              <CetButton
                class="fl mrJ1"
                v-bind="CetButton_search"
                v-on="CetButton_search.event"
              ></CetButton>
              <CetButton
                class="fl"
                v-bind="CetButton_export"
                v-on="CetButton_export.event"
              ></CetButton>
            </div>
          </el-header>
          <el-main class="contentBody mtJ3 padding0">
            <div style="height: 100%" v-show="ElSelect_type.value === 1">
              <CetChart
                :inputData_in="CetChart_1.inputData_in"
                v-bind="CetChart_1.config"
              />
            </div>
            <div style="height: 100%" v-show="ElSelect_type.value === 2">
              <CetChart
                @click="chartClick2"
                :inputData_in="CetChart_2.inputData_in"
                v-bind="CetChart_2.config"
              />
            </div>
            <div style="height: 100%" v-show="ElSelect_type.value === 3">
              <CetChart
                :inputData_in="CetChart_3.inputData_in"
                v-bind="CetChart_3.config"
              />
            </div>
          </el-main>
        </el-container>
      </el-container>
    </el-container>
    <Slide></Slide>
  </div>
</template>
<script>
import common from "eem-utils/common";
import Slide from "eem-components/slide/Slide.vue";
import CustomDatePicker from "../CustomDatePicker.vue";
import TREE_PARAMS from "@/store/treeParams.js";
import { httping } from "@omega/http";
export default {
  name: "EnergyQuery",
  components: {
    Slide,
    CustomDatePicker
  },
  props: {
    clickNode_in: {
      type: Object
    }
  },

  computed: {
    thbSelectList() {
      return this.ElCheckboxList_1.options_in.filter(
        item => !item.disabled && this.ElCheckboxGroup_1.value.includes(item.id)
      );
    },
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    // 选择框确认按钮状态
    btnIsDisabled: function () {
      return function (i) {
        return this.dropdownData[i].checkedArr.length === 0;
      };
    },
    // 维度选择框确认按钮状态
    dimenDis() {
      return this.dimension.check.length === 0;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },

  data() {
    return {
      initGiantTree: true,
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.nodeClick_out //选中单行输出
        }
      },
      checkedNodes: [],
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.nodeClick2_out, //选中单行输出
          checkedNodes_out: this.checkboxClick //勾选节点输出
        }
      },
      nodeName: "",
      expandkey: [], //初始化展开的节点树id
      filterText: "", //节点树过滤输入框
      dimension: {
        options: [],
        oldCheck: [],
        check: []
      },
      dimensionText: "", //维度文本
      dropdownData: [],
      filterData: [],
      allNum: 0,
      flag: true,
      dimTree: {
        treeData: [],
        showCheckbox: false,
        checkStrictly: true,
        treeProps: {
          children: "children",
          label: "name"
        }
      },
      ElSelect_1: {
        value: null,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElCheckboxGroup_1: {
        value: [],
        style: {},
        event: {
          change: this.ElCheckboxGroup_1_change_out
        }
      },
      ElCheckboxList_1: {
        options_in: [
          {
            id: 1,
            disabled: false,
            text: "同比"
          },
          {
            id: 2,
            disabled: false,
            text: "环比"
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      ElCheckboxGroup_2: {
        value: [0],
        style: {},
        event: {
          change: this.ElCheckboxGroup_2_change_out
        }
      },
      ElCheckboxList_2: {
        options_in: [
          {
            id: 0,
            text: "显示分时数据"
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      // type组件
      ElSelect_type: {
        value: 1,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_type_change_out
        }
      },
      // type组件
      ElOption_type: {
        options_in: [
          {
            id: 1,
            text: "同比环比"
          },
          {
            id: 2,
            text: "分时统计"
          },
          {
            id: 3,
            text: "节点对比"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },

      CustomDatePicker_1: {
        queryTime: {
          startTime: null,
          endTime: null,
          cycle: 12 //17年，14月，12日，20自定义
        },
        dataConfig: {
          time: null,
          cycle: 3,
          showPicker: true,
          showRange: true,
          type: [
            {
              id: 1,
              text: "日",
              type: "date"
            },
            {
              id: 3,
              text: "月",
              type: "month"
            },
            {
              id: 5,
              text: "年",
              type: "year"
            },
            {
              id: 11,
              text: "自定义",
              type: "date"
            }
          ]
        }
      },
      TimeRange_1: {
        queryTime: []
      },
      // 请填写组件含义组件
      CetButton_search: {
        visible_in: false,
        disable_in: true,
        title: "高级查询",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_search_statusTrigger_out
        }
      },
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: "导出",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      CetChart_1: {
        inputData_in: null,
        config: {
          options: {
            tooltip: {
              trigger: "axis",
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
              }
            },
            grid: {
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              containLabel: true
            },
            legend: {
              data: []
            },
            dataset: {
              source: []
            },
            xAxis: { type: "category" },
            yAxis: {},
            series: []
          }
        }
      },
      CetChart_2: {
        inputData_in: null,
        config: {
          options: {
            tooltip: {
              trigger: "axis",
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
              }
            },
            legend: {
              data: []
            },
            grid: {
              top: 32,
              left: 0,
              right: 0,
              bottom: 0,
              containLabel: true
            },
            dataset: {
              source: []
            },
            xAxis: { type: "category" },
            yAxis: {},
            series: []
          }
        }
      },
      CetChart_3: {
        inputData_in: null,
        config: {
          options: {
            legend: {
              data: []
            },
            tooltip: {},
            dataset: {
              source: []
            },
            xAxis: { type: "category" },
            yAxis: {},
            series: []
          }
        }
      },
      loading1: false,
      loading2: false,
      loading3: false,
      unit: ""
    };
  },
  watch: {
    // 维度切换
    "dimension.check": {
      handler: function (value) {
        console.log(value);
        var str = [];
        for (var i = 0; i < value.length; i++) {
          for (var j = 0; j < this.dimension.options.length; j++) {
            if (value[i] === this.dimension.options[j].propertyId) {
              str.push(this.dimension.options[j].propertyText);
            }
          }
        }
        this.dimensionText = str.join(" - ");
      },
      immediate: true
    },
    //本地存储监听
    storageDimAttribution: {
      handler: function (newStorage, oldStorage) {
        if (!window.sessionStorage) {
          console.log("浏览器不支持localStorage");
        } else {
          var storage = window.sessionStorage;
          var str = JSON.stringify(newStorage);
          storage.setItem("storageDimAttribution", str);
        }
      },
      deep: true
    },
    // 监听节点树输入框
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    "dimTree.showCheckbox": {
      handler: function (val, old) {
        if (val) {
          this.CetGiantTree_2.selectNode = this._.cloneDeep(
            this.CetGiantTree_1.selectNode
          );
        } else {
          this.CetGiantTree_1.selectNode = this._.cloneDeep(
            this.CetGiantTree_2.selectNode
          );
        }
      }
    }
  },

  methods: {
    // 节点树过滤触发函数
    filterFun: function (i) {
      this.filterData[i] = this.dropdownData[i].checkedArr;
      console.log(this.filterData);
      this.dropdownData[i].nofilterCheckedArr = this.dropdownData[i].checkedArr;
      this.$refs.tree.filter(this.filterData);
    },
    // 取消过滤
    notFilter: function () {
      for (var i = 0; i < this.dropdownData.length; i++) {
        this.dropdownData[i].nofilterCheckedArr = this.getAttrArr(
          this.dropdownData[i].dropDownList,
          "id"
        );
        this.dropdownData[i].checkedArr = this.getAttrArr(
          this.dropdownData[i].dropDownList,
          "id"
        );
      }
      this.$refs.tree.filter([]);
    },
    // 节点树过滤逻辑函数 value:过滤参数，data:当前节点，node:当前节点所有信息
    filterNode: function (value, data, node) {
      var startNum = 0;
      if (value.length === 0) {
        return true;
      }
      if (node.isLeaf) {
        // 搜索框过滤 和 维度过滤分离
        if (typeof value === "string") {
          return this.strJudgeParentNode(node, value);
        } else {
          startNum = this.judgeParentNode(node.parent, 0);
          return startNum === this.allNum;
        }
      }
    },
    // 维度过滤：判断子节点是否属于过滤节点，即判断该子节点的各个父节点是否和fileterData一一对应(选择过滤)
    judgeParentNode: function (node, startNum) {
      var index = node.level - 1;
      if (index >= 0) {
        for (var i = 0; i < this.filterData.length; i++) {
          var str = this.filterData[i];
          if (str.indexOf(node.data.nodeId) !== -1) {
            startNum++;
          }
        }
        return this.judgeParentNode(node.parent, startNum);
      } else {
        return startNum;
      }
    },
    // 搜索框过滤：判断关键字是否出现在父节点上(搜索过滤)
    strJudgeParentNode: function (node, val) {
      if (node.level === 0) {
        return false;
      } else {
        if (node.label.indexOf(val) !== -1) {
          return true;
        } else {
          return this.strJudgeParentNode(node.parent, val);
        }
      }
    },
    // 层级关键字过滤
    filterLv: function (i) {
      if (this.flag) {
        var val = this.dropdownData[i].input;
        if (val) {
          this.dropdownData[i].filterCheckboxArr = [];
          this.dropdownData[i].filterCheckedArr = [];

          var checkArr = this.dropdownData[i].dropDownList;
          for (var j = 0; j < checkArr.length; j++) {
            // var name = checkArr[j]["name"];
            if (checkArr[j]["name"].indexOf(val) !== -1) {
              this.dropdownData[i].filterCheckboxArr.push(checkArr[j]);
              this.dropdownData[i].filterCheckedArr.push(checkArr[j].id);
            }
          }
          if (this.dropdownData[i].filterCheckboxArr.length !== 0) {
            //找到了
            this.dropdownData[i].checkboxArr =
              this.dropdownData[i].filterCheckboxArr;
            this.dropdownData[i].checkedArr =
              this.dropdownData[i].filterCheckedArr;
            this.dropdownData[i].checkAll = true;
            this.dropdownData[i].isIndeterminate = false;
          } else {
            //没找到
            this.dropdownData[i].checkboxArr = [];
            this.dropdownData[i].checkedArr = [];
          }
        } else {
          this.dropdownData[i].checkboxArr =
            this.dropdownData[i].nofilterCheckboxArr;
          this.dropdownData[i].checkedArr =
            this.dropdownData[i].nofilterCheckedArr;
          this.dropdownData[i].checkAll =
            this.dropdownData[i].checkedArr.length ===
            this.dropdownData[i].checkboxArr.length;
          this.dropdownData[i].isIndeterminate =
            this.dropdownData[i].checkedArr.length !==
            this.dropdownData[i].checkboxArr.length;
        }
      }
    },
    // 从数组的对象里取出某个属性组成新数组,data-数组参数,attr-对象属性
    getAttrArr: function (data, attr) {
      if (Array.isArray(data) && data.length > 0) {
        var arr = data.map(function (item, i) {
          return item[attr];
        });
        return arr;
      } else {
        return [];
      }
    },


    getTreeData() {
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.management,
        treeReturnEnable: true
      };
      httping({
        url: "/eem-service/v1/node/nodeTree/simple",
        method: "POST",
        data
      }).then(res => {
        if (res.code === 0) {
          _this.CetGiantTree_1.inputData_in = res.data;
          _this.CetGiantTree_2.inputData_in = res.data;
          _this.CetGiantTree_1.selectNode = _this.clickNode_in;

          // _this.dimTree.treeData = res.data;
          // var key = _this.clickNode_in.tree_id;
          // var expandkey = _this.clickNode_in.tree_id; //默认展开的节点key

          // _this.$nextTick(function () {
          //   _this.$refs.tree.setCurrentKey(key);
          //   var node = _this.$refs.tree.getCurrentNode();
          //   _this.expandkey = [expandkey];
          //   _this.nodeClick_out(node);
          //   // _this.RealtimeMonitor1.currentNode_in = _this.$refs.tree.getCurrentNode();
          // });

          // _this.setTreeLeaf(_this.dimTree.treeData);
        } else {
          _this.$message.error(res.msg);
        }
      });
    },
    // 设置节点禁用
    setTreeLeaf(nodesAll) {
      if (nodesAll && nodesAll.length > 0) {
        nodesAll.forEach(item => {
          if (item.childSelectState == 2) {
            this.$set(item, "disabled", true);
          } else if (item.childSelectState == 1) {
            this.$set(item, "disabled", false);
          }
          this.setTreeLeaf(this._.get(item, "children", []));
        });
      }
    },
    // 获取第一个子节点数据
    childrenNodeKey(val, label) {
      if (!val) {
        return null;
      }
      for (let i = 0; i < val.length; i++) {
        if (
          !val[i].children ||
          val[i].children.length === 0 ||
          val[i].modelLabel === label
        ) {
          return val[i].tree_id;
        } else {
          return this.childrenNodeKey(val[i].children, label);
        }
      }
    },

    // 层级的全选
    handleCheckAll: function (val, i) {
      var allCheck = this.getAttrArr(this.dropdownData[i].dropDownList, "id");
      this.dropdownData[i].checkedArr = val ? allCheck : [];
      this.dropdownData[i].isIndeterminate = false;
    },
    // 层级的多选
    checkedChange: function (value, i) {
      console.log(value);
      var count = value.length;
      this.dropdownData[i].checkAll =
        count === this.dropdownData[i].checkboxArr.length;
      this.dropdownData[i].isIndeterminate =
        count > 0 && count < this.dropdownData[i].checkboxArr.length;
    },
    // 下拉框出现时，改变层级选择，即取消后选择框回复功能
    handleVisible: function (visible, i) {
      if (visible) {
        this.dropdownData[i].checkboxArr =
          this.dropdownData[i].nofilterCheckboxArr;
        this.dropdownData[i].checkedArr =
          this.dropdownData[i].nofilterCheckedArr;
        this.dropdownData[i].input = "";
        this.dropdownData[i].checkAll =
          this.dropdownData[i].checkedArr.length ===
          this.dropdownData[i].checkboxArr.length;
        this.dropdownData[i].isIndeterminate =
          this.dropdownData[i].checkedArr.length !==
          this.dropdownData[i].checkboxArr.length;
      }
    },
    // 维度下拉选择框出现时
    handleDimenVisible: function (visible) {
      if (!visible) {
        this.dimension.check = this.dimension.oldCheck;
      }
    },
    // 输入框防抖
    inputStart: function () {
      this.flag = false;
    },
    // 输入框防抖
    inputEnd: function () {
      this.flag = true;
    },
    // 获取localStorage的值
    getLocalStorage: function () {
      if (!window.sessionStorage) {
        return false;
      } else {
        var storage = window.sessionStorage;
        var json = storage.getItem("storageDimAttribution");
        if (json) {
          var jsonObj = JSON.parse(json);
          this.storageDimAttribution = jsonObj;
        }
      }
    },
    nodeClick_out(val) {
      // console.log(val);
      if (this.dimTree.showCheckbox) {
        return;
      }
      if (val) {
        // if (val.childSelectState == 2) {
        //   this.$message.warning("你没有此节点的全部权限！");
        //   return;
        // }
        this.nodeName = val.name;
        this.clickNode = val;
        if (this.ElSelect_type.value === 1) {
          this.getChartData1();
        } else if (this.ElSelect_type.value === 2) {
          this.getChartData2();
        } else if (this.ElSelect_type.value === 3) {
          // this.getChartData3();
        }
        this.$emit("setNode", val);
      } else {
        this.clickNode = {};
        this.$message.warning("请选择节点！");
      }
    },
    nodeClick2_out(val) {
      if (!this.dimTree.showCheckbox) {
        return;
      }
      if (val) {
        this.nodeName = val.name;
        this.clickNode = val;
        // if (!this.checkedNodes.length) {
        this.CetGiantTree_2.checkedNodes = [val];
        // }
      } else {
        this.clickNode = {};
        this.$message.warning("请选择节点！");
      }
    },
    checkboxClick(val) {
      this.checkedNodes = val;
      // console.log(val);
      this.getChartData3();
    },

    ElSelect_1_change_out(val) {
      if (!val) {
        return;
      }
      if (this.ElSelect_type.value === 1) {
        this.getChartData1();
      } else if (this.ElSelect_type.value === 2) {
        this.getChartData2();
      } else if (this.ElSelect_type.value === 3) {
        this.getChartData3();
      }
    },

    ElCheckboxGroup_1_change_out(val) {
      if (this.ElSelect_type.value === 1) {
        this.getChartData1();
      } else if (this.ElSelect_type.value === 2) {
        this.getChartData2();
      } else if (this.ElSelect_type.value === 3) {
        this.getChartData3();
      }
    },
    ElCheckboxGroup_2_change_out(val) {
      this.getChartData2();
    },
    ElSelect_type_change_out(val) {
      if (!val) {
        return;
      }
      this.dimTree.showCheckbox = this.ElSelect_type.value === 3;
      this.updateTHBGroup();
      this.CustomDatePicker_1.dataConfig.cycle = 3;
      this.CustomDatePicker_1.dataConfig.type = [
        {
          id: 1,
          text: "日",
          type: "date"
        },
        {
          id: 3,
          text: "月",
          type: "month"
        },
        {
          id: 5,
          text: "年",
          type: "year"
        },
        {
          id: 11,
          text: "自定义",
          type: "date"
        }
      ];
      if (val === 1) {
        // this.getChartData1();
        this.$refs.CustomDatePicker.init();
        if (this.clickNode) {
          setTimeout(() => {
            this.CetGiantTree_1.selectNode = this._.cloneDeep(this.clickNode);
          });
        }
      } else if (val === 2) {
        // this.getChartData2();
        this.CustomDatePicker_1.dataConfig.type = [
          {
            id: 3,
            text: "月",
            type: "month"
          },
          {
            id: 5,
            text: "年",
            type: "year"
          }
        ];
        this.$refs.CustomDatePicker.init();
        if (this.clickNode) {
          setTimeout(() => {
            this.CetGiantTree_1.selectNode = this._.cloneDeep(this.clickNode);
          });
        }
      } else if (val === 3) {
        if (this.clickNode) {
          let obj = this._.cloneDeep(this.clickNode);
          this.$refs.CustomDatePicker.init();
          setTimeout(() => {
            // this.$refs.tree.setCheckedKeys([this.clickNode.tree_id]);
            this.CetGiantTree_2.selectNode = obj;
            this.CetGiantTree_2.checkedNodes = [obj];
            // this.getChartData3();
          });
        }
      }
    },
    CetButton_search_statusTrigger_out(val) {},
    //导出
    CetButton_export_statusTrigger_out(val) {
      var urlStr = "",
        params = {};
      if (this.ElSelect_type.value === 1) {
        urlStr = "/eem-service/v1/energy/energydata/export/1";
        params = this.getParams(1);
      } else if (this.ElSelect_type.value === 2) {
        urlStr = "/eem-service/v1/energy/energydata/export/2";
        params = this.getParams(2);
      } else if (this.ElSelect_type.value === 3) {
        urlStr = "/eem-service/v1/energy/energydata/export/3";
        params = this.getParams(3);
      }
      if (!params) {
        return;
      }
      common.downExcel(urlStr, params, this.token);
    },

    getMarginType(iStartTime1, iEndTime) {
      let iMarginDay = this.$moment(iEndTime).subtract(2, "day");
      let iMarginMonth = this.$moment(iEndTime).subtract(3, "month");
      let iMarginYear = this.$moment(iEndTime).subtract(3, "year");
      if (iStartTime1 > iMarginDay) {
        return 1;
      } else if (iStartTime1 > iMarginMonth) {
        return 2;
      } else if (iStartTime1 > iMarginYear) {
        return 3;
      } else {
        return 4;
      }
    },

    //同比环比
    getChartData1() {
      var _this = this,
        auth = _this.token; //身份验证
      var queryBody = this.getParams(1);
      if (!queryBody) {
        return;
      }
      this.CetChart_1.config.options = {};

      queryBody.projectId = this.projectId;
      var queryOption = {
        url: `/eem-service/v1/energy/energydata/time/tbhb`,
        method: "POST",
        data: queryBody
      };

      httping(queryOption).then(function (response) {
        // this.loading1 = false;
        if (response.code === 0 && response.data) {
          //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
          console.log(response.data);
          // var data = _this._.get(response, ["data","1"], []);

          _this.filChartData1(response.data);
        }
      });
    },

    //1:同比；2：环比；3：同比环比
    getLegendTitle(dataType, time, customIndex) {
      let type = this.CustomDatePicker_1.queryTime.cycle;
      if (dataType === "base") {
        return this.getLegend(time, customIndex);
      } else if (dataType === "tb") {
        if (type === 12) {
          return this.getLegend(this.$moment(time).subtract(1, "month"));
        } else if (type === 14) {
          return this.getLegend(this.$moment(time).subtract(1, "year"));
        } else {
          return this.getLegend(this.$moment(time).subtract(1, "year"));
        }
      } else if (dataType === "hb") {
        if (type === 12) {
          return this.getLegend(this.$moment(time).subtract(1, "day"));
        } else if (type === 14) {
          return this.getLegend(this.$moment(time).subtract(1, "month"));
        } else {
          console.error("年度是没有环比数据的");
          return "--";
        }
      }
      return "--";
    },
    filChartData1(data) {
      let self = this;
      let customIndex = 1;
      this.CetChart_1.config.options = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          },
          formatter: function (val) {
            let list = val || [];
            let formatterStr = "";
            for (let i = 0, len = list.length; i < len; i++) {
              let showValue = self.getLegendTitle(
                val[i].data.dataInfo,
                val[i].data.time,
                customIndex
              );
              if (i === 0) {
                //TODO:可以做成标题不按照x轴来
                formatterStr += `${val[i].name}`;
              }
              formatterStr += `<br/>${val[i].marker}${showValue} : ${
                val[i].value !== null ? val[i].value : "--"
              }(${val[i].data.unit || "--"})`;
            }
            return formatterStr;
          }
        },
        grid: {
          top: 32,
          left: 0,
          right: 0,
          bottom: 0,
          containLabel: true
        },
        legend: {
          data: []
        },

        // dataset: {
        //   source: []
        // },
        xAxis: {
          type: "category",
          data: []
        },
        yAxis: {
          type: "value",
          nameTextStyle: {
            padding: [0, 0, 0, 50]
          }
        },
        series: []
      };
      var _this = this,
        currentdata = data.currentdata || [],
        tbdata = data.tbdata || [],
        hbdata = data.hbdata || [];
      if (currentdata.length === 0) {
        return;
      }
      currentdata.map((item, index) => {
        item.hbValue = hbdata.length > index && hbdata[index].value;
        item.tbValue = tbdata.length > index && tbdata[index].value;
      });
      let xAxisData = [],
        yAxisData = [[], [], []];
      customIndex = this.getMarginType(
        currentdata[0].time,
        currentdata[currentdata.length - 1].time
      );

      let source = [];
      let unit = data.symbol;
      unit = common.formatSymbolStr(unit);
      let typeLabel = ["base", "tb", "hb"];
      for (let i = 0, len = currentdata.length; i < len; i++) {
        let value = [];
        value[0] = common.formatNumberWithPrecision(currentdata[i].value, 2);
        value[1] = common.formatNumberWithPrecision(currentdata[i].tbValue, 2);
        value[2] = common.formatNumberWithPrecision(currentdata[i].hbValue, 2);
        let product = this.getAxixs(currentdata[i].time, customIndex);
        let sour = {
          time: currentdata[i].time,
          unit: unit,
          product: product,
          yAxis1: value[0],
          yAxis2: value[1],
          yAxis3: value[2]
        };
        source.push(sour);
        xAxisData.push(product);
        for (let index = 0; index < 3; index++) {
          yAxisData[index].push({
            time: currentdata[i].time,
            unit: unit,
            name: product,
            value: value[index],
            dataInfo: typeLabel[index]
          });
        }
      }

      let legend = [];
      let CheckedArray = this.thbSelectList.map(item => item.id),
        len = CheckedArray.length,
        queryType;
      let series = [
        {
          name: "本期",
          type: "bar",
          smooth: true,
          barWidth: "60%",
          data: yAxisData[0]
        }
      ];

      if (len === 0) {
        queryType = 0;
      } else if (len === 1) {
        queryType = CheckedArray[0];
      } else if (len === 2) {
        queryType = 3;
      }
      if (this.cycle === 17 && len !== 0) {
        queryType = 1;
      }

      //1:同比；2：环比；3：同比环比
      if (queryType === 1) {
        series.push({
          name: "同比",
          type: "line",
          smooth: true,
          data: yAxisData[1]
        });
      } else if (queryType === 2) {
        series.push({
          name: "环比",
          type: "line",
          smooth: true,
          data: yAxisData[2]
        });
      } else if (queryType === 3) {
        series = series.concat(
          {
            name: "同比",
            type: "line",
            smooth: true,
            data: yAxisData[1]
          },
          {
            name: "环比",
            type: "line",
            smooth: true,
            data: yAxisData[2]
          }
        );
      }
      var dataset = {
        source: source
      };
      series.forEach(item => {
        legend.push(item.name);
      });
      //2022-9-5 根据何秋平反馈，不对负值转为0处理
      // series[0].data.forEach(item => {
      //   if (Number(item.value) < 0) {
      //     item.value = 0;
      //   }
      // });
      _this.$nextTick(function () {
        _this.CetChart_1.config.options.dataset = dataset;
        var ElOption_1Text;
        if (
          _this.ElOption_1.options_in.filter(
            item => item.id == _this.ElSelect_1.value
          ).length > 0
        ) {
          ElOption_1Text = _this.ElOption_1.options_in.filter(
            item => item.id == _this.ElSelect_1.value
          )[0].text;
        }
        _this.CetChart_1.config.options.yAxis.name = ElOption_1Text
          ? `${ElOption_1Text}（${unit})`
          : "";
        _this.CetChart_1.config.options.xAxis.data = xAxisData;
        _this.CetChart_1.config.options.series = series;
        _this.CetChart_1.config.options.legend.data = legend;
      });
    },
    //分时统计
    getChartData2() {
      var _this = this,
        auth = _this.token; //身份验证
      var queryBody = this.getParams(2);
      if (!queryBody) {
        return;
      }
      var dataset = {
        source: []
      };

      this.CetChart_2.config.options = {};
      // this.loading2 = true;
      queryBody.projectId = this.projectId;
      var queryOption = {
        url: `/eem-service/v1/energy/energydata/time/timeshare`,
        method: "POST",
        data: queryBody
      };

      httping(queryOption).then(function (response) {
        // _this.loading2 = false;
        if (response.code === 0) {
          //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
          // console.log(response.data);
          var data = _this._.get(response, ["data", "0"], {});
          _this.filChartData2(data);
        }
      });
    },
    filChartData2(data) {
      this.CetChart_2.config.options = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          },
          formatter: function (val) {
            var list = val || [];
            var formatterStr = "";
            var formatVal = _this.getFormatVal();
            if (_this.ElCheckboxGroup_2.value.length > 0) {
              formatVal = false;
            }
            for (var i = 0, len = list.length; i < len; i++) {
              if (i === 0) {
                formatterStr += `${
                  val[i].name
                } <br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                  val[i].color
                };"></span>${
                  formatVal
                    ? _this.$moment(val[i].data.time).format(formatVal)
                    : val[i].seriesName
                } : ${val[i].value || "--"}(${val[i].data.unit || "--"})`;
              } else {
                formatterStr += `<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                  val[i].color
                };"></span>${
                  formatVal
                    ? _this.$moment(val[i].data.time).format(formatVal)
                    : val[i].seriesName
                } : ${val[i].value || "--"}(${val[i].data.unit || "--"})`;
              }
            }
            return formatterStr;
          }
        },
        grid: {
          top: 32,
          left: 0,
          right: 0,
          bottom: 0,
          containLabel: true
        },
        legend: {
          data: []
        },
        // dataset: {
        //   source: []
        // },
        xAxis: {
          type: "category",
          data: []
        },
        yAxis: {
          type: "value",
          nameTextStyle: {
            padding: [0, 0, 0, 50]
          }
        },
        series: []
      };
      var _this = this,
        energyDatas = data.energyDatas || [],
        touDatas = data.touDatas || [],
        legend = [],
        series = [];
      if (energyDatas.length === 0) {
        return;
      }
      let unit = data.symbol;
      unit = common.formatSymbolStr(unit);
      var showTimeSharing =
        this.ElCheckboxGroup_2.value.length > 0 ? true : false;
      if (showTimeSharing) {
        var leng = energyDatas.length;
        let customIndex = this.getMarginType(
          energyDatas[0].time,
          energyDatas[leng - 1].time
        );

        var source = [];

        energyDatas.forEach(item => {
          var obj = {};
          obj.time = item.time;
          obj.product = this.getAxixs(item.time, customIndex);
          touDatas.forEach(item1 => {
            var tou = item1.tou || [];
            for (var i = 0, len = tou.length; i < len; i++) {
              if (item.time == tou[i].time) {
                obj[`yAxis${item1.segId}`] = common.formatNumberWithPrecision(
                  tou[i].value,
                  2
                );
              }
            }
          });
          source.push(obj);
        });
        var dataset = {
          source: source
        };

        var xAxisData = [];
        source.forEach(item => {
          xAxisData.push(item.product);
        });
        for (var i = 0, len = touDatas.length; i < len; i++) {
          var yAxisData = [];
          source.forEach(item => {
            // yAxisData.push(item[`yAxis${touDatas[i].segId}`]);
            yAxisData.push({
              name: item["product"],
              time: item["time"],
              unit: unit,
              value: item[`yAxis${touDatas[i].segId}`]
            });
          });
          var obj = {
            // name: touDatas[i].segName,
            name: touDatas[i].segId,
            type: "bar",
            stack: "总量",
            // encode: { x: "product", y: `yAxis${touDatas[i].segId}` }
            data: yAxisData
          };
          // legend.push(touDatas[i].segName);
          legend.push(touDatas[i].segId);
          series.push(obj);
        }
        if (series && series.length > 0) {
          series[0].data.forEach(item => {
            if (Number(item.value) < 0) {
              item.value = 0;
            }
          });
        }
        // this.CetChart_2.config.options.dataset = dataset;
        var ElOption_1Text;
        if (
          _this.ElOption_1.options_in.filter(
            item => item.id == _this.ElSelect_1.value
          ).length > 0
        ) {
          ElOption_1Text = _this.ElOption_1.options_in.filter(
            item => item.id == _this.ElSelect_1.value
          )[0].text;
        }
        this.CetChart_2.config.options.yAxis.name = ElOption_1Text
          ? `${ElOption_1Text}（${unit})`
          : "";
        this.CetChart_2.config.options.xAxis.data = xAxisData;
        this.CetChart_2.config.options.series = series;
        this.CetChart_2.config.options.legend.data = legend;
      } else {
        var xAxisData = [],
          yAxisData1 = [],
          yAxisData2 = [],
          yAxisData3 = [];

        var leng = energyDatas.length,
          customIndex = 1,
          customTime = energyDatas[leng - 1].time - energyDatas[0].time;
        if (customTime < 1000 * 3600 * 24 * 3) {
          customIndex = 1;
        } else if (customTime < 1000 * 3600 * 24 * 30 * 3) {
          customIndex = 2;
        } else {
          customIndex = 3;
        }
        var source = [];
        energyDatas.forEach(item => {
          var product = this.getAxixs(item.time, customIndex);
          var value = common.formatNumberWithPrecision(item.value, 2);
          var obj = {};
          obj.time = item.time;
          obj.product = product;
          obj.yAxis = value;
          source.push(obj);
          xAxisData.push(product);
          // yAxisData1.push(value);
          yAxisData1.push({
            name: product,
            time: item.time,
            unit: unit,
            value: value
          });
        });
        var dataset = {
          source: source
        };
        var name = this.getLegend(this.CustomDatePicker_1.queryTime.startTime);
        var obj = {
          name: name,
          type: "bar",
          // encode: { x: "product", y: `yAxis` }
          data: yAxisData1
        };
        legend.push(name);
        series.push(obj);
        // this.CetChart_2.config.options.dataset = dataset;
        var ElOption_1Text;
        if (
          _this.ElOption_1.options_in.filter(
            item => item.id == _this.ElSelect_1.value
          ).length > 0
        ) {
          ElOption_1Text = _this.ElOption_1.options_in.filter(
            item => item.id == _this.ElSelect_1.value
          )[0].text;
        }
        this.CetChart_2.config.options.yAxis.name = ElOption_1Text
          ? `${ElOption_1Text}（${unit})`
          : "";
        this.CetChart_2.config.options.xAxis.data = xAxisData;
        this.CetChart_2.config.options.series = series;
        this.CetChart_2.config.options.legend.data = legend;
      }
    },
    //节点对比
    getChartData3() {
      var _this = this,
        auth = _this.token; //身份验证
      var queryBody = this.getParams(3);
      if (!queryBody) {
        return;
      }
      this.CetChart_3.config.options = {};
      // this.loading3 = true;
      queryBody.projectId = this.projectId;
      var queryOption = {
        url: `/eem-service/v1/energy/energydata/time/compare`,
        method: "POST",
        data: queryBody
      };

      httping(queryOption).then(function (response) {
        // _this.loading3 = false;
        if (response.code === 0) {
          //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
          console.log(response.data);
          var data = _this._.get(response, ["data"], []);

          _this.filChartData3(data);
        }
      });
    },
    filChartData3(pdata) {
      let customIndex = 1;
      this.CetChart_3.config.options = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          },
          formatter: function (val) {
            let list = val || [];
            let formatterStr = "";
            for (let i = 0, len = list.length; i < len; i++) {
              if (i === 0) {
                formatterStr += `${val[i].name}`;
              }
              formatterStr += `<br/>${val[i].marker}${val[i].seriesName} : ${
                val[i].value || "--"
              }(${val[i].data.unit || "--"})`;
            }
            return formatterStr;
          }
        },
        grid: {
          top: 32,
          left: 0,
          right: 0,
          bottom: 0,
          containLabel: true
        },
        legend: {
          data: []
        },
        // dataset: {
        //   source: []
        // },
        xAxis: {
          type: "category",
          data: []
        },
        yAxis: {
          type: "value",
          nameTextStyle: {
            padding: [0, 0, 0, 50]
          }
        },
        series: []
      };
      var _this = this,
        data = pdata || [],
        id = 0,
        modelLabel = "",
        legend = [],
        series = [];
      let unit = (data.length > 0 && data[0].symbol) || "--";
      unit = common.formatSymbolStr(unit);
      if (this.clickNode) {
        id = this.clickNode.id;
        modelLabel = this.clickNode.modelLabel;
      } else {
        this.$message.warning("请选择节点");
        return;
      }
      // var list = this.$refs.tree.getCheckedNodes() || [];
      var list = this.checkedNodes || [];
      var isHasNode = false;
      list.forEach(item => {
        if (
          item.id === this.clickNode.id &&
          item.modelLabel === this.clickNode.modelLabel
        ) {
          isHasNode = true;
        }
      });
      if (!isHasNode && list.length > 0) {
        id = list[0].id;
        modelLabel = list[0].modelLabel;
      }
      var source = [];
      data.forEach(item => {
        var serie = {};
        if (item.id === id && item.modelLabel === modelLabel) {
          var itemData1 = item.data || [];
          customIndex = this.getMarginType(
            itemData1[0].time,
            itemData1[itemData1.length - 1].time
          );
          itemData1.forEach(item1 => {
            var obj = {};
            obj.time = item1.time;
            obj.product = this.getAxixs(item1.time, customIndex);
            obj[`yAxis${item.modelLabel}${item.id}`] =
              common.formatNumberWithPrecision(item1.value, 2);
            source.push(obj);
          });
          serie = {
            name: item.name,
            type: "bar",
            smooth: true,
            encode: { x: "product", y: `yAxis${item.modelLabel}${item.id}` }
          };
        } else {
          serie = {
            name: item.name,
            type: "line",
            smooth: true,
            encode: { x: "product", y: `yAxis${item.modelLabel}${item.id}` }
          };
        }
        legend.push(item.name);
        series.push(serie);
      });
      data.forEach(item => {
        if (item.id != id || item.modelLabel !== modelLabel) {
          var itemData1 = item.data || [];
          itemData1.forEach(item1 => {
            source.forEach(item2 => {
              if (item2.time === item1.time) {
                item2[`yAxis${item.modelLabel}${item.id}`] =
                  common.formatNumberWithPrecision(item1.value, 2);
              }
            });
          });
        }
      });
      var dataset = {
        source: source
      };

      var xAxisData = [];
      var cloneLegend = [],
        cloneSeries = [];
      source.forEach(item => {
        xAxisData.push(item.product);
      });
      data.forEach(item => {
        var serie = {};
        var yAxisData = [];
        source.forEach(item11 => {
          // yAxisData.push(item11[`yAxis${item.modelLabel}${item.id}`]);
          yAxisData.push({
            name: item11.product,
            time: item11.time,
            unit: unit,
            value: item11[`yAxis${item.modelLabel}${item.id}`]
          });
        });
        if (item.id === id && item.modelLabel === modelLabel) {
          serie = {
            name: item.name,
            type: "bar",
            smooth: true,
            data: yAxisData
            // encode: { x: "product", y: `yAxis${item.modelLabel}${item.id}` }
          };
        } else {
          serie = {
            name: item.name,
            type: "line",
            smooth: true,
            data: yAxisData
            // encode: { x: "product", y: `yAxis${item.modelLabel}${item.id}` }
          };
        }
        cloneLegend.push(item.name);
        cloneSeries.push(serie);
      });
      if (cloneSeries.length > 0) {
        cloneSeries[0].data.forEach(item => {
          if (Number(item.value) < 0) {
            item.value = 0;
          }
        });
      }
      var ElOption_1Text;
      if (
        _this.ElOption_1.options_in.filter(
          item => item.id == _this.ElSelect_1.value
        ).length > 0
      ) {
        ElOption_1Text = _this.ElOption_1.options_in.filter(
          item => item.id == _this.ElSelect_1.value
        )[0].text;
      }
      _this.CetChart_3.config.options.yAxis.name = ElOption_1Text
        ? `${ElOption_1Text}（${unit})`
        : "";
      this.CetChart_3.config.options.xAxis.data = xAxisData;
      this.CetChart_3.config.options.series = cloneSeries;
      this.CetChart_3.config.options.legend.data = cloneLegend;
    },
    //获取图表参数
    getParams(type = 1) {
      if (type !== 3 && !this.clickNode) {
        return null;
      }
      var queryTime = this.CustomDatePicker_1.queryTime,
        startTime = queryTime.startTime,
        endTime = queryTime.endTime,
        cycle = queryTime.cycle;
      var energyType = this.ElSelect_1.value;
      if (type === 1) {
        var CheckedArray = this.ElCheckboxGroup_1.value || [],
          len = CheckedArray.length,
          queryType;
        if (len === 0) {
          queryType = 0;
        } else if (len === 1) {
          queryType = CheckedArray[0];
        } else if (len === 2) {
          queryType = 3;
        }
        if (cycle === 17) {
          queryType = 1;
        } else if (cycle === 20) {
          queryType = 0;
        }
        var params = {
          cycle: cycle,
          endTime: endTime,
          energyType: energyType,
          projectId: this.projectId,
          node: [
            {
              modelLabel: this.clickNode.modelLabel,
              nodes: [
                {
                  id: this.clickNode.id,
                  name: this.clickNode.name
                }
              ]
            }
          ],
          queryType: queryType,
          startTime: startTime
        };
        return params;
      } else if (type === 2) {
        var params = {
          cycle: cycle,
          endTime: endTime,
          energyType: energyType,
          projectId: this.projectId,
          node: [
            {
              modelLabel: this.clickNode.modelLabel,
              nodes: [
                {
                  id: this.clickNode.id,
                  modelLabel: this.clickNode.modelLabel,
                  name: this.clickNode.name
                }
              ]
            }
          ],
          startTime: startTime
        };
        return params;
      } else if (type === 3) {
        var params = {
          cycle: cycle,
          endTime: endTime,
          energyType: energyType,
          projectId: this.projectId,
          node: this.getSelectNode(),
          startTime: startTime
        };
        return params;
      }
      return {};
    },
    //节点对比中获取选中节点，进行分类
    getSelectNode() {
      // var list = this.$refs.tree.getCheckedNodes() || [];
      var list = this.checkedNodes || [];
      // console.log(list);
      var nodes = [];
      var objType = {}; //存放数组种类
      var resArr = []; //存放结果数组
      for (var i = 0, len = list.length; i < len; i++) {
        var obj = {
          id: list[i].id,
          modelLabel: list[i].modelLabel,
          name: list[i].name
        };
        if (objType[list[i].modelLabel]) {
          objType[list[i].modelLabel].push(obj);
        } else {
          objType[list[i].modelLabel] = [];
          objType[list[i].modelLabel].push(obj);
        }
      }
      // console.log(objType);
      for (var key in objType) {
        var resObj = {
          modelLabel: key,
          nodes: objType[key]
        };
        resArr.push(resObj);
      }

      return resArr;
    },
    //分时统计对应图表点击拽取
    chartClick2(val) {
      // console.log(val);
      //17年，14月，12日，20自定义
      var queryTime = this.CustomDatePicker_1.queryTime;
      if (queryTime.cycle == 17) {
        this.CustomDatePicker_1.dataConfig.time = val.data.time;
        this.CustomDatePicker_1.dataConfig.cycle = 3;
      } else if (queryTime.cycle == 14) {
        this.CustomDatePicker_1.dataConfig.time = val.data.time;
        this.CustomDatePicker_1.dataConfig.cycle = 1;
      }
    },
    //时间组件变化
    CustomDatePicker_1_change(val) {
      if (
        this.CustomDatePicker_1.queryTime.cycle !== val.cycle &&
        val.cycle === 20
      ) {
        this.ElCheckboxGroup_1.value = [];
      }
      this.CustomDatePicker_1.queryTime = val;

      this.updateTHBGroup();

      if (this.ElSelect_type.value === 1) {
        this.getChartData1();
      } else if (this.ElSelect_type.value === 2) {
        this.getChartData2();
      } else if (this.ElSelect_type.value === 3) {
        this.getChartData3();
      }
    },

    updateTHBGroup() {
      let cycle = this.CustomDatePicker_1.queryTime.cycle;

      this.ElCheckboxList_1.options_in = [
        {
          id: 1,
          disabled: cycle === 20,
          text: "同比"
        },
        {
          id: 2,
          disabled: cycle === 17 || cycle === 20,
          text: "环比"
        }
      ];
    },
    //获取图表图例
    getLegend(pDate, customIndex) {
      let sFormat = "";
      let cycle = this.CustomDatePicker_1.queryTime.cycle;
      if (cycle === 12) {
        sFormat = "YYYY-MM-DD";
      } else if (cycle === 14) {
        sFormat = "YYYY-MM";
      } else if (cycle === 17) {
        sFormat = "YYYY";
      } else if (cycle === 20) {
        if (customIndex === 1) {
          sFormat = "HH";
        } else if (customIndex === 2) {
          sFormat = "YYYY-MM";
        } else if (customIndex === 3) {
          sFormat = "YYYY";
        } else {
          sFormat = "YYYY";
        }
      }

      return this.$moment(pDate).format(sFormat);
    },
    //过滤获取图表x轴对应值
    getAxixs(pDate, customIndex) {
      let oDate = this.$moment(pDate);
      let type = this.CustomDatePicker_1.queryTime.cycle;
      if (type === 12) {
        if (oDate.format("HH:mm") == "00:00") {
          return oDate.format("DD");
        }
        return oDate.format("HH:mm");
      } else if (type === 14) {
        return oDate.format("DD");
      } else if (type === 17) {
        return oDate.format("MM");
      } else if (type === 20) {
        if (customIndex === 1) {
          if (oDate.format("HH:mm") == "00:00") {
            return oDate.format("DD");
          }
          return oDate.format("HH:mm");
        } else if (customIndex === 2) {
          return oDate.format("DD");
        } else if (customIndex === 3) {
          return oDate.format("MM");
        } else if (customIndex === 4) {
          return oDate.format("YYYY");
        }
      }
      return oDate.format("YYYY-MM-DD");
    },

    grtCetNoEnumSelect_1() {
      var _this = this;
      _this.ElOption_1.options_in = [];

      httping({
        url:
          "/eem-service/v1/project/projectEnergy?projectId=" + this.projectId,
        method: "GET"
      }).then(res => {
        if (res.code === 0 && res.data && res.data.length > 0) {
          var selectData = res.data.map(item => {
            return {
              id: item.energytype,
              text: item.name
            };
          });
          let obj = selectData.find(i => i.id === 2);

          _this.ElOption_1.options_in = _this._.cloneDeep(selectData);
          _this.ElSelect_1.value = obj ? obj.id : selectData[0].id;
          _this.ElSelect_1_change_out(selectData[0].id);
        }
      });
    },
    getFormatVal() {
      //17年，14月，12日，20自定义
      var cycle = this.CustomDatePicker_1.queryTime.cycle;
      switch (cycle) {
        case 17:
          return "YYYY";
        case 14:
          return "YYYY-MM";
        case 12:
          return "YYYY-MM-DD";
        default:
          return false;
      }
    },
    formatSeriesName(data) {
      var formatVal = this.getFormatVal(),
        tbArr = {
          17: "year",
          14: "month",
          12: "day"
        },
        hbArr = {
          14: "year",
          12: "month"
        };
      if (data.tb) {
        return this.$moment(data.time)
          .subtract(1, tbArr[this.CustomDatePicker_1.queryTime.cycle])
          .format(formatVal);
      } else if (data.hb) {
        return this.$moment(data.time)
          .subtract(1, hbArr[this.CustomDatePicker_1.queryTime.cycle])
          .format(formatVal);
      } else {
        return this.$moment(data.time).format(formatVal);
      }
    }
  },
  created: function () {
    // this.getLocalStorage();
    this.TimeRange_1.queryTime = common.initDateRange();
  },
  mounted: function () {
    this.grtCetNoEnumSelect_1();
    this.getTreeData();
  },
  activated: function () {
    this.getTreeData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}

.dropdownBox {
  width: 125px;
  padding: 0 6px;
  line-height: 30px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-right: 15px;
}
.dropdownBox i {
  display: block;
  line-height: 30px;
  width: 30px;
  margin-right: -6px;
  text-align: center;
  float: right;
}
.el-dropdown-menu .el-checkbox {
  display: block;
}
.el-dropdown-menu__item {
  float: left;
  padding: 0;
  line-height: 26px;
}
.el-dropdown-menu {
  padding: 10px;
}
.change-drop {
  width: 30px;
  height: 30px;
  position: absolute;
  top: 9px;
  background: url("./assets/select.png") no-repeat;
  cursor: pointer;
}

.page-main {
  position: relative;
  transition: all 500ms;
  width: 100%;
}
.collapse-aside {
  position: absolute;
  left: -13px;
  top: 50%;
  width: 13px;
  height: 80px;
  line-height: 80px;
  transform: translateY(-50%);
  cursor: pointer;
}

.basic-box :deep(.el-select) {
  flex: 1;
}
.treeBox {
  border-bottom-left-radius: mh-get(C3);
  border-bottom-right-radius: mh-get(C3);
  margin: 0 (0 - mh-get(C3));
  flex: 1;
  @include background-color(BG1);
  .elTree {
    overflow: auto;
  }
}
.cetGiantTree {
  flex: 1;
  min-height: 0;
}
.eem-checkbox {
  :deep(.el-checkbox) {
    @include margin_right(J1);
  }
}
</style>
