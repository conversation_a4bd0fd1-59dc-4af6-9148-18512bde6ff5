import { http, httping } from "@omega/http";
export function waterinjectionoptimization(params) {
  return httping({
    url: `/piem/v1/waterinjectionoptimization/scheme/list?name=${params.name}`,
    method: "GET"
  });
}

export function saveorupdate(data) {
  return httping({
    url: `/piem/v1/waterinjectionoptimization/scheme/saveorupdate`,
    method: "POST",
    data
  });
}
export function waterinjectionoptimizationDelete(id) {
  if (!id) return;
  return httping({
    url: `/piem/v1/waterinjectionoptimization/scheme/delete?id=${id}`,
    method: "DELETE"
  });
}

export function waterAssociatednode(id) {
  if (!id) return;
  return httping({
    url: `/piem/v1/waterinjectionoptimization/scheme/associatednode/querybyid?id=${id}`,
    method: "GET"
  });
}
export function waterQuerybymodelgoal(modelGoal) {
  if (!modelGoal) return;
  return http({
    url: `/piem/v1/waterinjectionoptimization/scheme/associatednode/querybymodelgoal?modelGoal=${modelGoal}`,
    method: "GET"
  });
}

export function waterTranSchemeAssociationNode(data) {
  return http({
    url: `/piem/v1/waterinjectionoptimization/tranSchemeAssociationNode`,
    method: "POST",
    data
  });
}

export function offlineTraining(data) {
  return httping({
    url: `/piem/v1/waterinjectionoptimization/offline/training`,
    method: "POST",
    data
  });
}

export function fittingcurve(data) {
  return httping({
    url: `/piem/v1/waterinjectionoptimization/offline/unitconsumption/fittingcurve`,
    method: "POST",
    data
  });
}

export function waterParamcorrelation(data) {
  return httping({
    url: `/piem/v1/waterinjectionoptimization/offline/waterinjectionstation/paramcorrelation`,
    method: "POST",
    data
  });
}

export function waterWellParamcorrelation(data) {
  return httping({
    url: `/piem/v1/waterinjectionoptimization/offline/waterinjectionwell/paramcorrelation`,
    method: "POST",
    data
  });
}

export function onlineFittingcurve(data) {
  return httping({
    url: `/piem/v1/waterinjectionoptimization/online/unitconsumption/fittingcurve`,
    method: "POST",
    data
  });
}

export function onlineSituation(data) {
  return httping({
    url: `/piem/v1/waterinjectionoptimization/online/waterinjection/situation`,
    method: "POST",
    data
  });
}
