<template>
  <!-- 1弹窗组件 -->
  <div>
    <CetDialog
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
      :title="inputData_in && inputData_in.id ? $T('编辑对标') : $T('新建对标')"
      class="CetDialog small"
    >
      <span slot="footer">
        <!-- 设置组件唯一识别字段按钮组件 -->
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
      <div class="eem-cont-c1">
        <CetForm
          :data.sync="CetForm_1.data"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
        >
          <el-row :gutter="$J3">
            <el-col :span="12">
              <el-form-item :label="$T('对标管理名称')" prop="name">
                <ElInput
                  v-model="CetForm_1.data.name"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('指标类型')" prop="indicatortype">
                <ElSelect
                  v-model="CetForm_1.data.indicatortype"
                  v-bind="ElSelect_indicatortype"
                  v-on="ElSelect_indicatortype.event"
                >
                  <ElOption
                    v-for="item in ElOption_indicatortype.options_in"
                    :key="item[ElOption_indicatortype.key]"
                    :label="item[ElOption_indicatortype.label]"
                    :value="item[ElOption_indicatortype.value]"
                    :disabled="item[ElOption_indicatortype.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('越限类型')" prop="limittype">
                <ElSelect
                  v-model="CetForm_1.data.limittype"
                  v-bind="ElSelect_limittype"
                  v-on="ElSelect_limittype.event"
                >
                  <ElOption
                    v-for="item in ElOption_limittype.options_in"
                    :key="item[ElOption_limittype.key]"
                    :label="item[ElOption_limittype.label]"
                    :value="item[ElOption_limittype.value]"
                    :disabled="item[ElOption_limittype.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item :label="$T('越限值')" prop="limitvalue">
                <ElInputNumber
                  v-model="CetForm_1.data.limitvalue"
                  v-bind="ElInputNumber_1"
                  v-on="ElInputNumber_1.event"
                ></ElInputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('能效周期')" prop="aggregationcycle">
                <ElSelect
                  v-model="CetForm_1.data.aggregationcycle"
                  v-bind="ElSelect_aggregationcycle"
                  v-on="ElSelect_aggregationcycle.event"
                >
                  <ElOption
                    v-for="item in ElOption_aggregationcycle.options_in"
                    :key="item[ElOption_aggregationcycle.key]"
                    :label="item[ElOption_aggregationcycle.label]"
                    :value="item[ElOption_aggregationcycle.value]"
                    :disabled="item[ElOption_aggregationcycle.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col> -->
          </el-row>
        </CetForm>
      </div>
      <div class="eem-cont-c1 mtJ1">
        <div class="mbJ1" v-if="!(inputData_in && inputData_in.id)">
          <el-radio-group v-model="radio" @change="changeRadio">
            <el-radio :label="3">按分析周期设定对标</el-radio>
            <el-radio :label="6">不按分析周期设定对标</el-radio>
          </el-radio-group>
        </div>
        <CetTable
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
          class="text-right"
        >
          <el-table-column
            prop="logtime"
            align="left"
            :label="$T('能效周期')"
            minWidth="40"
          ></el-table-column>
          <ElTableColumn prop="limitValue" :label="$T('越限值')">
            <template slot-scope="scope">
              <div v-if="true">
                <ElInputNumber
                  v-model="CetTable_1.data[scope.$index].limitValue"
                  v-bind="ElInputNumber_1"
                ></ElInputNumber>
              </div>
            </template>
          </ElTableColumn>
        </CetTable>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import commonApi from "@/api/custom.js";
import common from "eem-utils/common";
export default {
  name: "BenchmarkingManage",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    treeData_in: {
      type: Object
    },
    energyefficiencyset_in: {
      type: Object
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    }
  },

  data() {
    return {
      // 单选框
      radio: 3,
      CetDialog_1: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入对标管理名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          indicatortype: [
            {
              required: true,
              message: $T("请选择指标类型"),
              trigger: ["blur", "change"]
            }
          ],
          limittype: [
            {
              required: true,
              message: $T("请选择越限类型"),
              trigger: ["blur", "change"]
            }
          ],
          limitvalue: [
            {
              required: true,
              message: $T("请输入越限值"),
              trigger: ["blur", "change"]
            }
          ],
          aggregationcycle: [
            {
              required: true,
              message: $T("请选择能效周期"),
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElInput_1: {
        value: "",
        style: {},
        rows: 2,
        placeholder: $T("请输入内容"),
        event: {}
      },
      ElInputNumber_1: {
        ...common.check_numberFloat,
        value: "",
        style: {
          width: "100%"
        },
        controls: false,
        event: {}
      },
      ElSelect_indicatortype: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_indicatortype: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_limittype: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_limittype: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_aggregationcycle: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_aggregationcycle: {
        options_in: [
          {
            id: 7,
            text: $T("小时")
          },
          {
            id: 12,
            text: $T("日")
          },
          {
            id: 13,
            text: $T("周"),
            type: "week"
          },
          {
            id: 14,
            text: $T("月")
          },
          {
            id: 17,
            text: $T("年")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          //  record_out:this.CetTable_1_record_out,
          //  outputData_out: this.CetTable_1_outputData_out
        }
      },
      tableData: [
        {
          logtime: "小时",
          cycle: 7
        },
        {
          logtime: "日",
          cycle: 12
        },
        {
          logtime: "月",
          cycle: 14
        },
        {
          logtime: "周",
          cycle: 13
        },
        {
          logtime: "年",
          cycle: 17
        }
      ],
      tableData1: [],
      tableData2: [
        {
          logtime: "小时、日、月、周、年",
          limitValue: undefined
        }
      ]
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.ElOption_indicatortype.options_in =
        this.$store.state.enumerations.indicatortype;
      this.ElOption_limittype.options_in =
        this.$store.state.enumerations.indicatorlimittype;
      this.radio = 3;
      this.tableData1 = [];
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {
      var vm = this;
      vm.CetDialog_1.queryId_in = val;
    },
    inputData_in(val) {
      this.CetDialog_1.inputData_in = val;
      if (val && val.id) {
        this.radio = 6;
        let data = [];
        let str = "";
        if (val.mergeDisplayAdds && val.mergeDisplayAdds.length) {
          val.mergeDisplayAdds.forEach(item => {
            let res = this.tableData.filter(key => {
              return key.cycle === item.cycle;
            });
            item.logtime = res[0].logtime;
            str = str + res[0].logtime + "、";
            item.limitValue = item.limitValue ? item.limitValue : undefined;
            data.push(item);
          });
          str = str.slice(0, -1);
          data[0].logtime = str;
          this.tableData1 = [data[0]];
        }
        this.CetTable_1.data = this._.cloneDeep(this.tableData1);
      }
      this.CetForm_1.data = this._.cloneDeep(val);
      this.CetForm_1.resetTrigger_in = new Date().getTime();
    },
    treeData_in(val) {
      this.CetDialog_1.treeData_in = val;
    },
    energyefficiencyset_in(val) {
      let data = [];
      let str = "";
      if (!(this.inputData_in && this.inputData_in.id)) {
        if (val.mergeDisplayAdds && val.mergeDisplayAdds.length) {
          val.mergeDisplayAdds.forEach(item => {
            let res = this.tableData.filter(key => {
              return key.cycle === item.cycle;
            });
            item.logtime = res[0].logtime;
            str = str + res[0].logtime + "、";
            item.limitValue = item.limitValue ? item.limitValue : undefined;
            data.push(item);
          });
        }
        str = str.slice(0, -1);
        this.tableData2[0].logtime = str;
        this.tableData1 = this._.cloneDeep(data);
        this.CetTable_1.data = this._.cloneDeep(this.tableData1);
      }
    }
  },

  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_saveData_out() {
      var params = {};
      var model_data = this._.cloneDeep(this.CetForm_1.data);
      model_data.modelLabel = "benchmarkset";
      model_data.aggregationcycle = null;
      model_data.limitvalue = null;
      if (!model_data.energyefficiencyset_id) {
        model_data.energyefficiencyset_id = this.energyefficiencyset_in.id;
      }
      if (this.inputData_in && this.inputData_in.id) {
        model_data.mergeDisplayAdds = this._.cloneDeep(
          this.inputData_in.mergeDisplayAdds
        );
      } else {
        model_data.mergeDisplayAdds = this._.cloneDeep(
          this.energyefficiencyset_in.mergeDisplayAdds
        );
      }
      if (this.radio === 3) {
        const bool = this.CetTable_1.data.every(item => {
          return item.limitValue === undefined;
        });
        if (bool) {
          return this.$message.error("请填入越限值数据");
        }
        let res = this.CetTable_1.data.filter(item => {
          delete item.logtime;
          return item.limitValue !== undefined;
        });
        model_data.mergeDisplayAdds = this._.cloneDeep(res);
      } else {
        let value = this.CetTable_1.data[0].limitValue;
        if (!value) {
          return this.$message.error("请填入越限值数据");
        }
        model_data.mergeDisplayAdds.forEach(item => {
          delete item.logtime;
          item.limitValue = value;
        });
      }
      params = this._.cloneDeep(model_data);
      this.Add_benchmarkset(params);
    },
    Add_benchmarkset(params) {
      var _this = this;
      commonApi.writeBenchmarkset(params, _this.projectId).then(response => {
        if (response.code === 0) {
          _this.$message({
            type: "success",
            message: $T("保存成功！")
          });
          _this.$emit("saveData_out", new Date().getTime());
          _this.CetDialog_1.closeTrigger_in = new Date().getTime();
        }
      });
    },
    // 输入值校验
    changeRadio(val) {
      this.CetTable_1.data = this._.cloneDeep(
        val === 3 ? this.tableData1 : this.tableData2
      );
    }
  }
};
</script>
<style lang="scss" scoped></style>
