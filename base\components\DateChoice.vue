<template>
  <div class="date-range">
    <el-button
      size="small"
      icon="el-icon-arrow-left"
      @click="queryPrv"
      style="margin-right: 5px"
    ></el-button>
    <span class="date-range-label">{{ title }}</span>
    <el-date-picker
      ref="datePicker"
      v-model="value"
      class="date-picker"
      :type="type"
      size="small"
      placeholder="选择日期"
      :clearable="false"
      :picker-options="pickerOptions"
      v-bind="$attrs"
      v-on="$listeners"
      :format="type | typeFilter"
    ></el-date-picker>
    <el-button
      :disabled="nextDisabled"
      size="small"
      icon="el-icon-arrow-right"
      @click="queryNext"
      style="margin-left: 5px"
    ></el-button>
    <div
      v-show="isShowHours"
      class="hours"
      style="margin-left: 10px; display: flex; align-items: center"
    >
      <el-button
        size="small"
        icon="el-icon-arrow-left"
        @click="hoursQueryPrv"
        style="margin-right: 5px"
      ></el-button>
      <span class="date-range-label">选择时间</span>
      <span
        style="
          padding: 0 10px;
          border: 1px solid #dcdfe6;
          line-height: 30px;
          width: 40px;
          text-align: center;
        "
      >
        {{ hoursValue }}:00
      </span>
      <el-button
        size="small"
        icon="el-icon-arrow-right"
        @click="hoursQueryNext"
        style="margin-left: 5px"
      ></el-button>
    </div>
  </div>
</template>

<script>
import moment from "moment";
export default {
  name: "dateChoice",
  data() {
    return {
      title: this.label || "时间",
      isShowHours: this.showHours,
      value: this.val || new Date(),
      hoursValue: this.hours || 0,
      type: this.dateType || "date",
      nextDisabled: false
    };
  },
  props: ["val", "hours", "label", "showHours", "dateType", "pickerOptions"],
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler: function (val, oldVal) {
        this.$emit("update:val", val);
        if (this.isShowHours) {
          this.changDate(val, this.hoursValue);
        } else {
          this.changDate(val);
        }
        if (this.pickerOptions) {
          if (
            this.$moment(val).valueOf() >=
            this.$moment().startOf("day").valueOf()
          ) {
            this.nextDisabled = true;
          } else {
            this.nextDisabled = false;
          }
        }
      }
    },
    hoursValue: {
      deep: true,
      handler: function (val, oldVal) {
        if (this.isShowHours) {
          this.$emit("update:hours", val);
          this.changDate(this.value, val);
        }
      }
    },
    val: {
      deep: true,
      handler: function (val, oldVal) {
        this.value = val;
      }
    },
    hours: {
      deep: true,
      handler: function (val, oldVal) {
        this.hoursValue = val;
      }
    },
    label: {
      deep: true,
      handler: function (val, oldVal) {
        // this.value = title;
      }
    },
    showHours: {
      deep: true,
      handler: function (val, oldVal) {
        this.isShowHours = val;
      }
    },
    dateType: {
      deep: true,
      handler: function (val, oldVal) {
        this.type = val;
      }
    }
  },
  methods: {
    queryPrv() {
      let date;
      if (this.value) {
        date = moment(this.value);
      } else {
        date = moment(new Date().getTime());
      }
      switch (this.type) {
        case "date":
          this.value = date.subtract(1, "days").toString();
          break;
        case "month":
          this.value = date.subtract(1, "months").toString();
          break;
        case "year":
          this.value = date.subtract(1, "years").toString();
          break;
        default:
          break;
      }
    },
    queryNext() {
      let date;
      if (this.value) {
        date = moment(this.value);
      } else {
        date = moment(new Date().getTime());
      }
      switch (this.type) {
        case "date":
          this.value = date.add(1, "days").toString();
          break;
        case "month":
          this.value = date.add(1, "months").toString();
          break;
        case "year":
          this.value = date.add(1, "years").toString();
          break;
        default:
          break;
      }
    },
    hoursQueryPrv() {
      if (this.hoursValue > 0) {
        this.hoursValue--;
      }
    },
    hoursQueryNext() {
      if (this.hoursValue < 23) {
        this.hoursValue++;
      }
    },
    changDate(val, hours) {
      this.value = val;
      this.$emit("update:val", val);
      this.$emit("update:hours", hours);
      this.$emit("change", val, hours);
    }
  },
  filters: {
    typeFilter(type) {
      if (type == "date") {
        return "yyyy 年 MM 月 dd 日";
      } else if (type == "month") {
        return "yyyy 年 MM 月";
      } else if (type == "year") {
        return "yyyy 年";
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.date-range {
  display: flex;
  align-items: center;
  margin-right: 10px;
  .date-range-label {
    display: block;
    // width: 86px;
    padding: 0 10px;
    text-align: center;
    line-height: 32px;
    height: 32px;
    box-sizing: border-box;
    border: 1px solid;
    border-right: 0px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    @include border_color(B1);
    @include background_color(BG1);
    @include font_color(T1);
  }
  .date-picker {
    flex: 1;
    border-top-left-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
  }
  .el-button {
    padding: 9px;
  }
}
</style>
