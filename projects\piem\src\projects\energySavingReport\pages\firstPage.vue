<template>
  <div class="page">
    <reportPage pageNum="01" :queryTime="queryTime">
      <div class="mbJ4">
        <div class="title mbJ2">{{ $T("1.用能概览") }}</div>
        <usingEnergyOverview :reportData="reportData" :queryTime="queryTime" />
      </div>

      <div class="mbJ4">
        <div class="title mbJ4">{{ $T("2.集输增压系统节能分析") }}</div>
        <div class="second-title mbJ2">{{ $T("2.1 能耗概览") }}</div>
        <energyConsumptionOverview
          :reportData="reportData"
          :overviewList="energyConsumptionOverviewList"
        />
      </div>

      <div class="mbJ4">
        <div class="second-title mbJ2">{{ $T("2.2 站场分析") }}</div>
        <stationAnalysis
          class="mbJ4"
          :stationAnalysisData="reportData.stationsEnergySavingVOList || []"
          :overviewList="stationAnalysisOverviewList"
        />
        <stationTable
          :tableData="reportData.stationsEnergySavingVOList || []"
          :columnList="stationColumnList"
        />
      </div>
    </reportPage>
  </div>
</template>

<script>
import reportPage from "../components/reportPage.vue";
import usingEnergyOverview from "../components/usingEnergyOverview.vue";
import energyConsumptionOverview from "../components/energyConsumptionOverview.vue";
import stationAnalysis from "../components/stationAnalysis.vue";
import stationTable from "../components/stationTable.vue";
import common from "eem-utils/common";
export default {
  name: "firstPage",
  components: {
    reportPage,
    stationTable,
    usingEnergyOverview, // 用能概览
    energyConsumptionOverview, // 能耗概览
    stationAnalysis // 站场分析
  },
  props: {
    reportData: {
      type: Object,
      default: () => {}
    },
    queryTime: {
      type: Number
    }
  },
  computed: {
    energyConsumptionOverviewList() {
      const unit = this.reportData.overviewEnergyUnit;
      const list = [
        {
          name: $T("月实际能耗"),
          text: $T("月实际能耗"),
          key: "gatherSysEnergy",
          unit: unit
        },
        {
          name: $T("优化后预计能耗"),
          text: $T("优化后预计能耗"),
          key: "gatherSysOptimizationEnergy",
          unit: unit
        },
        {
          name: $T("预计节能量"),
          text: $T("节能量"),
          key: "gatherSysSavingEnergy",
          unit: unit
        },
        {
          name: $T("预计节能率"),
          text: $T("节能率"),
          key: "gatherSysSavingRate",
          unit: "%"
        }
      ];
      return list;
    },
    stationAnalysisOverviewList() {
      const unit = this.reportData.stationEnergyUnit;
      const list = [
        {
          name: $T("月实际能耗"),
          key: "energy",
          unit: unit
        },
        {
          name: $T("优化后预计能耗"),
          key: "optimizationEnergy",
          unit: unit
        },
        {
          name: $T("节能"),
          key: "savingEnergy",
          unit: unit
        }
      ];
      return list;
    },
    stationColumnList() {
      const unit = this.reportData.stationEnergyUnit;
      const list = [
        {
          prop: "name",
          label: $T("分析对象"),
          align: "left",
          showOverflowTooltip: true
        },
        {
          prop: "energy",
          label: $T("月实际能耗({0})", unit),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 140
        },
        {
          prop: "optimizationEnergy",
          label: $T("优化后预计能耗({0})", unit),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 170
        },
        {
          prop: "savingEnergy",
          label: $T("预计节能量({0})", unit),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 140
        },
        {
          prop: "savingRate",
          label: $T("预计节能率(%)"),
          align: "left",
          formatter: common.formatNumberColumn,
          showOverflowTooltip: true,
          width: 120
        }
      ];
      return list;
    }
  },
  data() {
    return {};
  },

  methods: {
    formatNumberWithPrecision(...args) {
      return common.formatNumberWithPrecision(...args);
    }
  }
};
</script>

<style lang="scss" scoped>
@media print {
  @page {
    margin: 0;
  }

  body {
    margin: 1.6cm;
    -webkit-print-color-adjust: exact !important;
    -moz-print-color-adjust: exact !important;
    -ms-print-color-adjust: exact !important;
  }
  button {
    display: none;
  }
}
.page {
  width: 100%;
  height: 100%;
  position: relative;
  background: #ffffff;
}
.title {
  color: #242424;
  font-weight: bold;
  line-height: 20px;
}
.second-title {
  color: #242424;
  line-height: 18px;
}
</style>
