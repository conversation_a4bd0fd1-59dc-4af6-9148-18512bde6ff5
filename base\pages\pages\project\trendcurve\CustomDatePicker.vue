<template>
  <div class="date-range">
    <customElSelect
      v-model="ElSelect_timetype.value"
      v-bind="ElSelect_timetype"
      :disabled="disable_in"
      v-on="ElSelect_timetype.event"
      class="fl mrJ1"
      :prefix_in="$T('分析周期')"
    >
      <ElOption
        v-for="item in ElOption_timetype.options_in"
        :key="item[ElOption_timetype.key]"
        :label="item[ElOption_timetype.label]"
        :value="item[ElOption_timetype.value]"
        :disabled="item[ElOption_timetype.disabled]"
      ></ElOption>
    </customElSelect>
    <div
      class="fl"
      style="display: inline-block; position: relative"
      v-show="![4, 11].includes(ElSelect_timetype.value)"
    >
      <!-- 向前查询按钮 -->
      <div class="fl">
        <el-button
          type="display: inline-block;"
          v-show="CetButton_prv.visible_in"
          :disabled="disable_in"
          v-bind="CetButton_prv.config"
          @click="CetButton_prv_statusTrigger_out"
        ></el-button>
      </div>
      <el-date-picker
        class="fl mlJ mrJ"
        :class="{
          datePickerComp: customElSelect,
          szwDatePicker: customElSelect,
          weekPicker: ElSelect_timetype.value === 2
        }"
        style="width: 200px"
        v-bind="CetDatePicker_time.config"
        v-model="CetDatePicker_time.val"
        :placeholder="$T('选择日期时间')"
        :picker-options="pickerOptions"
        :disabled="disable_in"
        @change="CetDatePicker_time_queryTime_out"
      ></el-date-picker>
      <div class="fl weekExplain mlJ mrJ" v-if="ElSelect_timetype.value === 2">
        {{ $moment(queryTime.startTime).format("yyyy-MM-DD") }}
        -
        {{ $moment(queryTime.endTime).add(-1, "d").format("yyyy-MM-DD") }}
      </div>
      <!-- 向后查询按钮 -->
      <div class="fl">
        <el-button
          type="display: inline-block;"
          v-show="CetButton_next.visible_in"
          :disabled="backToTimeBtnDis1 || disable_in"
          v-bind="CetButton_next.config"
          @click="CetButton_next_statusTrigger_out"
        ></el-button>
      </div>
    </div>
    <div
      class="fl"
      style="display: inline-block"
      v-show="ElSelect_timetype.value == 11"
    >
      <TimeRange
        style="width: 480px"
        :val.sync="TimeRange_1.queryTime"
        :disabledDate="TimeRange_1.disabledDate"
        :typeIds="TimeRange_1.typeIds"
        @change="TimeRange_1_change"
      ></TimeRange>
    </div>
    <div
      class="fl"
      style="display: inline-block"
      v-show="ElSelect_timetype.value == 4"
    >
      <DateSeason
        style="width: 420px"
        :val.sync="DateSeason_1.val"
        @change="DateSeason_1_change"
      ></DateSeason>
    </div>
  </div>
</template>
<script>
import common from "eem-utils/common";
import moment from "moment";
import TimeRange from "./TimeRange";
import DateSeason from "./DateSeason";
export default {
  name: "CustomDatePicker",
  components: {
    TimeRange,
    DateSeason
  },
  props: {
    //数据绑定配置
    dataConfig: {
      type: Object
    },
    val: {
      type: Object
    },
    customElSelect: {
      type: Boolean
    },
    disable_in: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    // 实际对标考核下一时段按钮禁点状态
    backToTimeBtnDis1() {
      var actTime = null, //控件时间
        maxTime = null, //当前时间
        cycle = this.ElSelect_timetype.value,
        time = this.CetDatePicker_time.val;

      if (cycle === 1) {
        actTime = moment(time).startOf("day").valueOf();
        maxTime = moment()
          .add(1, "d")
          .startOf("day")
          .subtract(0, "d")
          .valueOf();
      } else if (cycle === 2) {
        actTime = moment(time).startOf("week").valueOf();
        maxTime = moment().add(1, "W").startOf("week").valueOf();
      } else if (cycle === 3) {
        actTime = moment(time).startOf("month").valueOf();
        maxTime = moment().add(1, "M").startOf("month").valueOf();
      } else if (cycle === 5) {
        actTime = moment(time).startOf("year").valueOf();
        maxTime = moment().add(1, "y").startOf("year").valueOf();
      }

      if (this.dataConfig.dateLimitNext) {
        return this.dataConfig.dateLimitNext(actTime, maxTime);
      }
      return actTime >= maxTime;
    }
  },
  data(vm) {
    const language = window.localStorage.getItem("omega_language");
    return {
      // timetype组件
      ElSelect_timetype: {
        value: "",
        style: {
          width: `${language === "en" ? "230px" : "160px"}`
        },
        event: {
          change: this.ElSelect_timetype_change_out
        }
      },
      ElOption_timetype: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      TimeRange_1: {
        queryTime: [],
        typeIds: [1, 2, 4, 5, 6],
        disabledDate: function (time) {
          return time.getTime() > moment().add(1, "d").valueOf();
        }
      },
      DateSeason_1: {
        val: []
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        config: {
          title: "",
          size: "small",
          plain: true,
          icon: "el-icon-arrow-left"
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        config: {
          title: "",
          size: "small",
          plain: true,
          icon: "el-icon-arrow-right"
        }
      },
      CetDatePicker_time: {
        disable_in: false,
        val: this.$moment().add(0, "d").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "date",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          clearable: false,
          size: "small",
          style: {
            display: "inline-block"
          }
        }
      },
      pickerOptions: {
        firstDayOfWeek: 1,
        disabledDate(time) {
          const cycle = vm.ElSelect_timetype.value;
          if (cycle === 1) {
            return time.getTime() > moment().valueOf();
          } else if (cycle === 2) {
            return time.getTime() > moment().add(1, "W").valueOf();
          } else if (cycle === 3) {
            return time.getTime() > moment().valueOf();
          } else if (cycle === 5) {
            return time.getTime() > moment().valueOf();
          }
        }
      },
      timenum: {
        1: 12,
        2: 13,
        3: 14,
        4: 15,
        5: 17,
        11: 20
      },
      timenum1: {
        12: 1,
        13: 2,
        14: 3,
        15: 4,
        17: 5,
        20: 11
      },
      queryTime: {
        startTime: null,
        endTime: null,
        cycle: 12 //17年，15季度，14月，13周，12日，20自定义
      }
    };
  },
  watch: {
    val: {
      deep: true,
      handler: function (val) {
        if (!val) {
          return;
        }
        this.value = val;
      }
    },
    value: {
      deep: true,
      handler: function (val) {
        this.changDate(val);
      }
    },
    dataConfig: {
      deep: true,

      handler: function (val) {
        if (!val || !val.time || !val.cycle) {
          return;
        }
        this.CetDatePicker_time.config.valueFormat = "timestamp";
        this.CetDatePicker_time.config.format = "";
        this.ElSelect_timetype.value = val.cycle;
        if (val.cycle === 1) {
          this.CetDatePicker_time.config.type = "date";
          this.CetDatePicker_time.val = common.dateTypeChange(
            val.time,
            val.cycle
          );
        } else if (val.cycle === 2) {
          this.CetDatePicker_time.config.valueFormat = "";
          this.CetDatePicker_time.config.type = "week";
          this.CetDatePicker_time.val = common.dateTypeChange(
            val.time,
            val.cycle
          );
          this.CetDatePicker_time.config.format = $T("yyyy 第 WW 周");
        } else if (val.cycle === 3) {
          this.CetDatePicker_time.config.type = "month";
          this.CetDatePicker_time.val = common.dateTypeChange(
            val.time,
            val.cycle
          );
        } else if (val.cycle === 5) {
          this.CetDatePicker_time.config.type = "year";
          this.CetDatePicker_time.val = common.dateTypeChange(
            val.time,
            val.cycle
          );
        }
      }
    },
    queryTime: {
      deep: true,
      handler: function (val) {
        let dateType = this.ElOption_timetype.options_in.find(
          item => item.id === this.timenum1[val.cycle]
        );
        this.$emit("change", { ...val, type: dateType.type });
      }
    },
    "CetDatePicker_time.val": {
      deep: true,
      handler: function (val) {
        let date = this.$moment(val);
        // this.queryTime = [date.startOf("d").valueOf(), date.endOf("d").valueOf() + 1];
        var type = this.ElSelect_timetype.value;

        var value;
        if (type === 1) {
          value = {
            startTime: date.startOf("d").valueOf(),
            endTime: date.endOf("d").valueOf() + 1,
            cycle: this.timenum[this.ElSelect_timetype.value]
          };
        } else if (type === 2) {
          value = {
            startTime: date.startOf("W").valueOf(),
            endTime: date.endOf("W").valueOf() + 1,
            cycle: this.timenum[this.ElSelect_timetype.value]
          };
        } else if (type === 3) {
          value = {
            startTime: date.startOf("M").valueOf(),
            endTime: date.endOf("M").valueOf() + 1,
            cycle: this.timenum[this.ElSelect_timetype.value]
          };
        } else if (type === 5) {
          value = {
            startTime: date.startOf("Y").valueOf(),
            endTime: date.endOf("Y").valueOf() + 1,
            cycle: this.timenum[this.ElSelect_timetype.value]
          };
        }
        // this.$emit("change", value);
        this.queryTime = value;
      }
    }
  },
  methods: {
    ElSelect_timetype_change_out(val) {
      var time = this.CetDatePicker_time.val;
      if (new Date(time) > new Date()) {
        time = new Date().getTime();
      }
      this.CetDatePicker_time.config.valueFormat = "timestamp";
      this.CetDatePicker_time.config.format = "";
      if (val === 1) {
        this.CetDatePicker_time.config.type = "date";
        this.CetDatePicker_time.val = common.dateTypeChange(time, val);
      } else if (val === 2) {
        this.CetDatePicker_time.config.valueFormat = "";
        this.CetDatePicker_time.config.format = $T("yyyy 第 WW 周");
        this.CetDatePicker_time.config.type = "week";
        this.CetDatePicker_time.val =
          common.dateTypeChange(time, val) + 1000 * 3600 * 24;
      } else if (val === 3) {
        this.CetDatePicker_time.config.type = "month";
        this.CetDatePicker_time.val = common.dateTypeChange(time, val);
      } else if (val === 4) {
        this.DateSeason_1.val = common.initDateRange("quarter");
      } else if (val === 5) {
        this.CetDatePicker_time.config.type = "year";
        this.CetDatePicker_time.val = common.dateTypeChange(time, val);
      } else if (val === 11) {
        this.TimeRange_1.queryTime = common.initDateRange();
      }
      this.emitTime(val);
    },
    CetButton_prv_statusTrigger_out() {
      // let date = this.$moment(this.CetDatePicker_time.val);
      // this.CetDatePicker_time.val = date.subtract(1, "d")._d;
      var time = this.CetDatePicker_time.val;
      var type = this.ElSelect_timetype.value;
      this.CetDatePicker_time.val = common.goToTime(time, type);
    },
    CetButton_next_statusTrigger_out() {
      // let date = this.$moment(this.CetDatePicker_time.val);
      // this.CetDatePicker_time.val = date.add(1, "d")._d;
      var time = this.CetDatePicker_time.val;
      var type = this.ElSelect_timetype.value;
      this.CetDatePicker_time.val = common.backToTime(time, type);
    },
    CetDatePicker_time_queryTime_out() {},
    TimeRange_1_change(val) {
      if (this.ElSelect_timetype.value != 11) {
        return;
      }

      if (val[0] === val[1]) {
        const starttime = new Date(val[0]).getTime();
        const endtime = new Date(val[0]).getTime() + 1000 * 3600 * 24;
        this.TimeRange_1.queryTime = [starttime, endtime];
        this.$message.warning("结束时间不能小于等于开始时间！");
        return;
      }
      if (
        this.$moment(val[1]).add(-1, "d").valueOf() >
        this.$moment(val[0]).add(3, "M").valueOf()
      ) {
        const starttime = this.$moment().startOf("d").add(-1, "M").valueOf();
        const endtime = this.$moment().startOf("d").add(1, "d").valueOf();
        this.TimeRange_1.queryTime = [starttime, endtime];
        this.$message.warning("自定义查询时间不能超过三个月！");
        return;
      }
      var value = {
        startTime: val[0],
        endTime: val[1],
        cycle: this.timenum[this.ElSelect_timetype.value]
      };
      // this.$emit("change", value);
      this.queryTime = value;
    },
    DateSeason_1_change(val) {
      if (this.ElSelect_timetype.value != 4) {
        return;
      }
      if (!val[0] || !val[1]) {
        return;
      }
      if (val[0] >= val[1]) {
        this.$message.warning("结束时间不能小于等于开始时间！");
        return;
      }
      var value = {
        startTime: val[0],
        endTime: val[1],
        cycle: this.timenum[this.ElSelect_timetype.value]
      };
      // this.$emit("change", value);
      this.queryTime = value;
    },
    emitTime(type) {
      var time = this.CetDatePicker_time.val;
      let date = this.$moment(time);
      var value;
      if (type === 1) {
        value = {
          startTime: date.startOf("d").valueOf(),
          endTime: date.endOf("d").valueOf() + 1,
          cycle: this.timenum[type]
        };
      } else if (type === 2) {
        value = {
          startTime: date.startOf("W").valueOf(),
          endTime: date.endOf("W").valueOf() + 1,
          cycle: this.timenum[type]
        };
      } else if (type === 3) {
        value = {
          startTime: date.startOf("M").valueOf(),
          endTime: date.endOf("M").valueOf() + 1,
          cycle: this.timenum[type]
        };
      } else if (type === 4) {
        let val = this.DateSeason_1.val;
        value = {
          startTime: val[0],
          endTime: val[1],
          cycle: this.timenum[type]
        };
      } else if (type === 5) {
        value = {
          startTime: date.startOf("Y").valueOf(),
          endTime: date.endOf("Y").valueOf() + 1,
          cycle: this.timenum[type]
        };
      } else if (type === 11) {
        var val = this.TimeRange_1.queryTime;
        value = {
          startTime: val[0],
          endTime: val[1],
          cycle: this.timenum[type]
        };
      }
      // this.$emit("change", value);
      this.queryTime = value;
    },
    init() {
      this.ElOption_timetype.options_in = this.dataConfig.type;
      this.ElSelect_timetype.value = this.dataConfig.cycle; //TODO:此处应该把1.3.5限制在内部，外面用12,14,17
      this.ElSelect_timetype_change_out(this.ElSelect_timetype.value);
    }
  },
  created: function () {
    this.TimeRange_1.queryTime = common.initDateRange();
    this.DateSeason_1.val = common.initDateRange("quarter");
  },
  mounted() {
    this.ElOption_timetype.options_in = this.dataConfig.type;
    this.ElSelect_timetype.value = this.dataConfig.cycle; //TODO:此处应该把1.3.5限制在内部，外面用12,14,17
    this.ElSelect_timetype_change_out(this.ElSelect_timetype.value);
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-input) {
  input {
    font-size: 14px;
  }
}

.date-range {
  display: flex;
  align-items: center;
  .date-range-label {
    display: block;
    // width: 86px;
    padding: 0 10px;
    text-align: center;
    line-height: 32px;
    height: 32px;
    box-sizing: border-box;
    border: 1px solid;
    border-right: 0px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    @include border_color(B1);
    @include background_color(BG1);
    @include font_color(T1);
  }
  .date-picker {
    flex: 1;
    // border-top-left-radius: 0px !important;
    // border-bottom-left-radius: 0px !important;
  }
  // .el-button {
  //   padding: 9px;
  // }
}
.datePickerComp {
  box-sizing: border-box;
  &:before {
    content: "\9009\62e9\65f6\95f4";
    @include font_color(ZS);
    position: absolute;
    left: 12px;
    @include font_size("Ab");
  }
  :deep(.el-input__inner) {
    padding-left: 68px;
    @include font_size("Aa");
  }
}
.weekPicker {
  width: 280px !important;
}
.weekExplain {
  position: absolute;
  top: calc(50% - 8px);
  right: 50px;
  @include font_color(T2);
  @include font_size(Ab);
}
</style>
