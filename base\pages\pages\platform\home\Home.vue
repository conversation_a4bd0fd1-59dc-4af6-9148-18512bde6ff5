<template>
  <div ref="container" class="home">
    <!-- <BaiduMap
      :mapStyle="mapStyle"
      @datachange="getSubprojectData"
      :points="points"
      :searchSite="searchSite"
      :isOpenWin="isOpenWin"
      :poppoints="poppoints"
      :homeMap="homeMap"
    >
      <InfoBox
        slot="infobox"
        class="infoBox"
        :subprojectData="subprojectData"
        :transformData="transformData"
        :eventList="eventList"
        @dataChange="setOpenWinStatus"
      ></InfoBox>
    </BaiduMap> -->

    <!-- tab切换 -->
    <ProjectTab
      :queryTrigger_in="queryTrigger_in"
      class="ProjectTabCard"
      ref="ProjectTabCard"
      @setPoints="setPoints"
    ></ProjectTab>
    <!-- 天气详情 -->
    <!-- <WeatherCard
      :queryTrigger_in="queryTrigger_in"
      class="WeatherCard"
    ></WeatherCard> -->
    <!-- 活动事件详情 -->
    <EventCard :queryTrigger_in="queryTrigger_in" class="EventCard"></EventCard>
    <!-- 工单详情 -->
    <OrderCard :queryTrigger_in="queryTrigger_in" class="OrderCard"></OrderCard>
    <!-- 节能减排情况 -->
    <ConserveEnergyCard
      :queryTrigger_in="queryTrigger_in"
      class="ConserveEnergyCard"
    ></ConserveEnergyCard>
    <!-- 负荷预测 -->
    <LoadForecast
      :queryTrigger_in="queryTrigger_in"
      class="LoadForecast"
    ></LoadForecast>

    <!-- 项目数量概览 -->
    <ProjectStatistics
      ref="ProjectStatistics"
      :queryTrigger_in="queryTrigger_in"
      class="ProjectStatistics"
    ></ProjectStatistics>
    <!-- 发电量 -->
    <GenerationCapacity
      :queryTrigger_in="queryTrigger_in"
      class="GenerationCapacity"
    ></GenerationCapacity>
    <!-- 配电统计 -->
    <DistributionStatistics
      @exportProjectNum="exportProjectNum"
      :queryTrigger_in="queryTrigger_in"
      class="DistributionStatistics"
    ></DistributionStatistics>
    <!-- 用能量 -->
    <EnergyUse :queryTrigger_in="queryTrigger_in" class="EnergyUse"></EnergyUse>
    <div v-show="showBadge" class="statusBadge">
      <div>有报警事件</div>
      <div>运行中</div>
      <div>即将到期</div>
      <div>调试中/已到期</div>
    </div>
    <div v-show="showBadge" class="deviceBadge">
      <div>光伏电站</div>
      <div>厂房/楼栋</div>
      <div>配电室</div>
    </div>
  </div>
</template>

<script>
// import BaiduMap from "./cloud/BaiduMap.vue";
// import InfoBox from "./cloud/InfoBox";
import ProjectTab from "./cloud/ProjectTab";
// import WeatherCard from "./cloud/WeatherCard";
import EventCard from "./cloud/EventCard";
import OrderCard from "./cloud/OrderCard";
import ConserveEnergyCard from "./cloud/ConserveEnergyCard";
import LoadForecast from "./cloud/LoadForecast";
import ProjectStatistics from "./cloud/ProjectStatistics";
import GenerationCapacity from "./cloud/GenerationCapacity";
import DistributionStatistics from "./cloud/DistributionStatistics";
import EnergyUse from "./cloud/EnergyUse";
import { httping } from "@omega/http";

export default {
  name: "Home",
  components: {
    // BaiduMap,
    // InfoBox,
    ProjectTab,
    // WeatherCard,
    EventCard,
    OrderCard,
    ConserveEnergyCard,
    LoadForecast,
    ProjectStatistics,
    GenerationCapacity,
    DistributionStatistics,
    EnergyUse
  },

  computed: {
    token() {
      return this.$store.state.token;
    }
  },

  data() {
    return {
      showBadge: false, // 是否显示标点说明
      mapStyle: "darkBlue", //'darkBlue'深夜主题，"simple"默认主题
      isOpenWin: true,
      isOrderNumUpdate: false,
      subprojectData: {},
      transformData: [
        { name: "--", capacity: "--", load: "--", loadRate: "--" }
      ],
      eventList: [],
      events: [],
      points: [],
      poppoints: [],
      projectID: 0,
      standbyList: [],
      searchSite: {
        isNeed: false,
        position: "absolute",
        left: "300px",
        top: "300px",
        right: "auto",
        bottom: "auto",
        zIndex: 2
      },
      homeMap: false, //首页地图
      showPrjTree: true,
      nodeInfo: null,
      isGetPrjTree: false,
      queryTrigger_in: new Date().getTime()
    };
  },
  methods: {
    exportProjectNum(projectNum) {
      this.$refs.ProjectStatistics.filProjectNumData(projectNum);
    },
    getNode(node) {
      this.nodeInfo = node;
      this.isGetPrjTree = true;
    },
    orderStatusChange() {
      this.isOrderNumUpdate = !this.isOrderNumUpdate;
    },

    setOpenWinStatus() {
      this.isOpenWin = !this.isOpenWin;
    },

    setStandbyList(list) {
      this.standbyList = list;
    },

    setProjectID(id) {
      this.projectID = id;
    },
    //tab返回节点列表进行地图打点过滤
    setPoints(points) {
      if (this.$refs.ProjectTabCard.selectedMenu == "区域") {
        this.showBadge = true;
      } else {
        this.showBadge = false;
      }
      var newPoints = [];
      for (let i = 0; i < points.length; i++) {
        points[i].markerTitle = points[i].name;
        var lng = parseFloat(points[i].longitude),
          lat = parseFloat(points[i].latitude);
        //筛选有效的经纬度值才能创建标注，不然无效的会在经纬度(0,0)处创建标注
        if (lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90) {
          var imgUrl = "";
          var coopStatus = points[i].coopStatus || 5;
          if (
            points[i].modelLabel === "project" ||
            points[i].modelLabel === "powertransformer"
          ) {
            if (points[i].alarmLevel == 1) {
              imgUrl = "./static/assets/map/click4.png";
            } else if (points[i].alarmLevel == 2) {
              imgUrl = "./static/assets/map/click3.png";
            } else {
              imgUrl = "./static/assets/map/click5.png";
            }
            var customMarkerImg = {
              url: imgUrl,
              width: 20,
              height: 26
            };
            points[i].customMarkerImg = customMarkerImg;
          } else if (points[i].modelLabel === "building") {
            if (coopStatus == 2) {
              imgUrl = "./static/assets/map/click_building_1.png";
            } else if (coopStatus == 3) {
              imgUrl = "./static/assets/map/click_building_2.png";
            } else if (coopStatus == 1 || coopStatus == 4) {
              imgUrl = "./static/assets/map/click_building_3.png";
            } else {
              imgUrl = "./static/assets/map/click_building_4.png";
            }
            let customMarkerImg = {
              url: imgUrl,
              width: 20,
              height: 26
            };
            points[i].customMarkerImg = customMarkerImg;
          } else if (points[i].modelLabel === "room") {
            if (coopStatus == 2) {
              imgUrl = "./static/assets/map/click_room_1.png";
            } else if (coopStatus == 3) {
              imgUrl = "./static/assets/map/click_room_2.png";
            } else if (coopStatus == 1 || coopStatus == 4) {
              imgUrl = "./static/assets/map/click_room_3.png";
            } else {
              imgUrl = "./static/assets/map/click_room_4.png";
            }
            let customMarkerImg = {
              url: imgUrl,
              width: 20,
              height: 26
            };
            points[i].customMarkerImg = customMarkerImg;
          }
          if (
            ["project", "building", "room", "powertransformer"].indexOf(
              points[i].modelLabel
            ) != -1
          ) {
            newPoints.push(points[i]);
          }
        }
      }

      this.points = newPoints;
      this.homeMap = true;
    },

    setPointsForPrjTree(points) {
      for (let i = 0; i < points.length; i++) {
        points[i].markerTitle =
          points[i].projectName + "，" + points[i].nodeName;
      }
      this.points = points;
      this.homeMap = true;
    },

    //点击地图对应图标进行显示浮动框
    getSubprojectData(data) {
      this.subprojectData = data;
      // this.getTransformData(data.nodeID);
      // this.getEvents(data.nodeID);
      // this.transformData = { name: '--', capacity: '--', load: '--', loadRate: '--' };
      var nodeId = data.id;

      if (data.modelLabel === "project") {
        //查询项目节点浮动框信息
        let urlStr = `/eem-service/v1/overview/projectShowTips?id=${nodeId}`;
        this.getNodeFormData(urlStr, data.modelLabel);
      } else if (data.modelLabel === "building") {
        //查询厂房浮动框信息
        let urlStr = `/eem-service/v1/overview/buildingShowTips?id=${nodeId}`;
        this.getNodeFormData(urlStr, data.modelLabel);
      } else if (data.modelLabel === "room") {
        //查询配电室浮动框信息
        let urlStr = `/eem-service/v1/overview/powerRoomShowTips?id=${nodeId}`;
        this.getNodeFormData(urlStr, data.modelLabel);
      } else if (data.modelLabel === "powertransformer") {
        //查询变压器浮动框
        let urlStr = `/eem-service/v1/overview/powerTransformerShowTips?id=${nodeId}`;
        this.getNodeFormData(urlStr, data.modelLabel);
      } else {
        //点击其他接口
        this.$message.warning("对应节点无详情信息，请选择其他节点！");
      }
    },
    //查询项目节点浮动框信息
    getNodeFormData(urlStr) {
      var _this = this;
      if (!urlStr) {
        return;
      }
      httping({
        url: urlStr,
        method: "GET"
      }).then(function (res) {
        if (res.code === 0) {
          var data = res.data || {};
          _this.transformData = res.data;
        }
      });
    },

    //查询污水处理站下变压器的数据
    getTransformData(nodeID) {
      let me = this;
      httping({
        url:
          "custom/api/pc/cm/subproject/" +
          nodeID +
          "/customdata?token=" +
          me.token,
        method: "GET"
      }).then(function (res) {
        let data = res.data;
        if (data && data.length > 0) {
          for (let i = 0; i < data.length; i++) {
            data[i].loadRate = isNaN(data[i].loadRate)
              ? "--"
              : (data[i].loadRate * 100).toFixed(2);
            data[i].capacity = isNaN(data[i].capacity)
              ? "--"
              : data[i].capacity.toFixed(2);
            data[i].load = isNaN(data[i].load) ? "--" : data[i].load.toFixed(2);
          }
        } else {
          data = [{ name: "--", capacity: "--", load: "--", loadRate: "--" }];
        }
        me.transformData = data;
      });
    },

    //查询污水处理站的报警事件列表
    getEvents(nodeID) {
      let me = this,
        time = me.getTimestamp();
      let url = "";
      // url = 'powermonitor/api/pc/pm/subproject/' + nodeID + '/events?token=' + me.token + '&begintime=' + time.beginTime + '&endtime=' + time.endTime + '&startindex=0&maxnum=0'
      url =
        "powermonitor/api/pc/pm/subproject/fivestructure/events/" +
        nodeID +
        "?token=" +
        me.token +
        "&begintime=" +
        time.beginTime +
        "&endtime=" +
        time.endTime +
        "&startindex=0&maxnum=20";

      httping({
        url: url,
        method: "GET"
      }).then(function (res) {
        let data = res.data.items,
          //分别对应其他、事故、警报、一般、预警
          eventClassList = [
            "#919191",
            "#e61a16",
            "#ff961b",
            "#006cff",
            "#ffcc00"
          ];

        for (let i = 0; i < data.length; i++) {
          let obj = {};
          //页面显示设备名称=配电柜+设备
          obj = data[i];
          obj.parentDeviceName = data[i].parentDeviceName || "";
          if (obj.parentDeviceName) {
            obj.deviceName = data[i].parentDeviceName + " " + obj.deviceName;
          } else {
            obj.deviceName = data[i].deviceName;
          }
        }
        //console.log(data)
        if (data && data.length > 0) {
          for (let i = 0; i < data.length; i++) {
            data[i].timeDesc = me.getTimeDesc(data[i].time);
            me.setPropertyValue("eventClass", data[i], eventClassList); //事件等级
          }
        }
        me.eventList = data;
      });
    },

    //实时报警获取处理状态的时间参数
    getTimestamp() {
      let d = new Date(),
        beginTime = d.setHours(0, 0, 0, 0), //今天零点零分
        endTime = beginTime + 86399000; //23:59:59
      return { beginTime: beginTime, endTime: endTime };
    },

    //遍历数组，更改属性值——由接收到的数字改成对应的字符串
    setPropertyValue(property, obj, arr) {
      for (let i = 0; i < arr.length; i++) {
        if (obj[property] === i) {
          obj[property + "Desc"] = arr[i];
          return;
        }
      }
    },

    //时间戳转换成字符串
    getTimeDesc(timestamp) {
      let me = this,
        timeDesc,
        time = new Date(timestamp); //时间戳转换成字符串
      let hour = me.setTimeVal(time.getHours()),
        min = me.setTimeVal(time.getMinutes()),
        sec = me.setTimeVal(time.getSeconds()),
        ms = time.getMilliseconds();
      if (ms < 10) {
        ms = "00" + ms;
      } else if (10 < ms && ms < 100) {
        ms = "0" + ms;
      }

      timeDesc = hour + ":" + min + ":" + sec + "." + ms;
      return timeDesc;
    },

    //对时间的显示效果进行处理，如果数字小于10，前面加个零
    setTimeVal(val) {
      if (val < 10) {
        val = "0" + val;
      }
      return val;
    },

    //点击按钮使城市列表显示/隐藏
    gridToggleClick() {
      var me = this,
        btn = document.getElementById("projectListToggleBtn"),
        list = me.$refs.projectList;
      list.$el.style.display == "none"
        ? (list.$el.style.display = "block")
        : (list.$el.style.display = "none");
      btn.className == "projectListHidden"
        ? (btn.className = "projectListShow")
        : (btn.className = "projectListHidden");
    },
    togglePrjTree() {
      this.showPrjTree = !this.showPrjTree;
    }
  },
  activated() {
    this.queryTrigger_in = new Date().getTime();
  }
};
</script>
<style lang="scss" scoped>
.home {
  width: 100%;
  height: 100%;
  margin: 0;
  position: relative;
}

.projectList {
  width: 288px;
  position: absolute;
  z-index: 0;
  top: 135px;
  left: 0;
}

.projectListHidden {
  width: 66px;
  height: 71px;
  position: absolute;
  top: 70px;
  left: 0;
  z-index: 1;
  cursor: pointer;
  background: url(./assets/projectList/area-normal.png) no-repeat;
}

.projectListHidden:hover {
  width: 147px;
  background: url(./assets/projectList/area-hover.png) no-repeat;
}

.projectListShow {
  width: 147px;
  height: 71px;
  position: absolute;
  top: 70px;
  left: 0;
  z-index: 1;
  cursor: pointer;
  background: url(./assets/projectList/area-click.png) no-repeat;
}

.ProjectTabCard {
  position: absolute;
  top: 0px;
  width: 300px;
  height: 40px;
  left: calc(50% - 150px);
}
.WeatherCard {
  color: #fff;
  position: absolute;
  top: 15px;
  width: 323px;
  height: 50px;
  right: 15px;
  z-index: 2;
  background-color: rgba(15, 26, 71, 0.5);
  background-size: 100%;
  background-image: linear-gradient(
    to top,
    rgba(37, 115, 237, 0.1),
    rgba(50, 20, 220, 0.1)
  );
  border: 1px solid;
  border-image: linear-gradient(to right, #44e7f9, #001a4a, #50658a) 30 10;
}
.EventCard {
  color: #fff;
  width: 323px;
  height: 115px;
  position: absolute;
  top: 80px;
  right: 15px;
  background-color: rgba(15, 26, 71, 0.5);
  background-size: 100%;
  background-image: linear-gradient(
    to top,
    rgba(37, 115, 237, 0.1),
    rgba(50, 20, 220, 0.1)
  );
  border: 1px solid;
  border-image: linear-gradient(to right, #44e7f9, #001a4a, #50658a) 30 10;
}
.OrderCard {
  color: #fff;
  width: 323px;
  position: absolute;
  top: 210px;
  right: 15px;
  background-color: rgba(15, 26, 71, 0.5);
  background-image: linear-gradient(
    to top,
    rgba(37, 115, 237, 0.1),
    rgba(50, 20, 220, 0.1)
  );
  border: 1px solid;
  border-image: linear-gradient(to right, #44e7f9, #001a4a, #50658a) 30 10;
}
.ConserveEnergyCard {
  color: #fff;
  width: 323px;
  position: absolute;
  top: 425px;
  right: 15px;
  background-color: rgba(15, 26, 71, 0.5);
  background-image: linear-gradient(
    to top,
    rgba(37, 115, 237, 0.1),
    rgba(50, 20, 220, 0.1)
  );
  border: 1px solid;
  border-image: linear-gradient(to right, #44e7f9, #001a4a, #50658a) 30 10;
}
.LoadForecast {
  color: #fff;
  width: 323px;
  height: 200px;
  position: absolute;
  top: 640px;
  right: 15px;
  background-color: rgba(15, 26, 71, 0.5);
  background-image: linear-gradient(
    to top,
    rgba(37, 115, 237, 0.1),
    rgba(50, 20, 220, 0.1)
  );
  border: 1px solid;
  border-image: linear-gradient(to right, #44e7f9, #001a4a, #50658a) 30 10;
}
.ProjectStatistics {
  color: #fff;
  width: 323px;
  position: absolute;
  top: 80px;
  left: 15px;
  background-color: rgba(15, 26, 71, 0.5);
  background-image: linear-gradient(
    to top,
    rgba(37, 115, 237, 0.1),
    rgba(50, 20, 220, 0.1)
  );
  border: 1px solid;
  border-image: linear-gradient(to right, #44e7f9, #001a4a, #50658a) 30 10;
}
.GenerationCapacity {
  color: #fff;
  width: 323px;
  position: absolute;
  top: 210px;
  left: 15px;
  background-color: rgba(15, 26, 71, 0.5);
  background-image: linear-gradient(
    to top,
    rgba(37, 115, 237, 0.1),
    rgba(50, 20, 220, 0.1)
  );
  border: 1px solid;
  border-image: linear-gradient(to right, #44e7f9, #001a4a, #50658a) 30 10;
}
.DistributionStatistics {
  color: #fff;
  width: 323px;
  position: absolute;
  top: 425px;
  left: 15px;
  background-color: rgba(15, 26, 71, 0.5);
  background-image: linear-gradient(
    to top,
    rgba(37, 115, 237, 0.1),
    rgba(50, 20, 220, 0.1)
  );
  border: 1px solid;
  border-image: linear-gradient(to right, #44e7f9, #001a4a, #50658a) 30 10;
}
.EnergyUse {
  color: #fff;
  width: 323px;
  position: absolute;
  top: 640px;
  left: 15px;
  background-color: rgba(15, 26, 71, 0.5);
  background-image: linear-gradient(
    to top,
    rgba(37, 115, 237, 0.1),
    rgba(50, 20, 220, 0.1)
  );
  border: 1px solid;
  border-image: linear-gradient(to right, #44e7f9, #001a4a, #50658a) 30 10;
}
.statusBadge {
  position: absolute;
  bottom: 20px;
  right: 350px;
  height: 217px;
  width: 160px;
  color: #fff;
  font-size: 24px;
  line-height: 37px;
  display: flex;
  flex-direction: column;
  & > div {
    margin: 10px 0;
    padding-left: 40px;
  }

  & > div:nth-child(1) {
    background: url("./assets/icon/u12443.png") no-repeat;
  }
  & > div:nth-child(2) {
    background: url("./assets/icon/u12424.png") no-repeat;
  }
  & > div:nth-child(3) {
    background: url("./assets/icon/u12423.png") no-repeat;
  }
  & > div:nth-child(4) {
    background: url("./assets/icon/u12450.png") no-repeat;
  }
}
.deviceBadge {
  position: absolute;
  bottom: 20px;
  left: calc(50% - 175px);
  height: 37px;
  width: 350px;
  color: #fff;
  font-size: 24px;
  line-height: 37px;
  display: flex;
  & > div {
    margin: 0 10px;
    padding-left: 40px;
  }
  & > div:nth-child(1) {
    background: url("./assets/icon/u12424.png") no-repeat;
  }
  & > div:nth-child(2) {
    background: url("./assets/icon/u12422.png") no-repeat;
  }
  & > div:nth-child(3) {
    background: url("./assets/icon/u12427.png") no-repeat;
  }
}
</style>

<style>
.BMap_cpyCtrl {
  display: none;
}
.anchorBL {
  display: none;
}
.BMap_scaleCtrl {
  display: none;
}
.BMap_stdMpCtrl {
  display: none;
}
</style>
