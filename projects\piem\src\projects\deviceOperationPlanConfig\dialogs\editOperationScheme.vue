<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="CetDialog"
    >
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <CetForm
        :data.sync="CetForm_pagedialog.data"
        v-bind="CetForm_pagedialog"
        v-on="CetForm_pagedialog.event"
      >
        <!-- 螺杆式 -->
        <template v-if="inputData_in.principleType === 3">
          <div class="card-box">
            <div class="common-title-H3 mbJ3 lh32">
              {{ $T("气量调节方式") }}
            </div>
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item
                  prop="slidingValveMode"
                  :label="$T('滑阀调节方式')"
                >
                  <el-select
                    style="width: 100%"
                    v-model="CetForm_pagedialog.data.slidingValveMode"
                    :placeholder="$T('请选择')"
                  >
                    <el-option
                      v-for="item in slidingValveModeOption"
                      :key="item.id"
                      :value="item.id"
                      :label="item.text"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                :span="12"
                v-if="CetForm_pagedialog.data.slidingValveMode === 0"
              >
                <div class="flex row-center">
                  <el-form-item prop="slidingMin" :label="$T('可调范围')">
                    <div class="input-number">
                      <el-input-number
                        :placeholder="$T('请输入')"
                        :min="0"
                        :precision="2"
                        :controls="false"
                        v-model="CetForm_pagedialog.data.slidingMin"
                      ></el-input-number>
                      <div class="input-number-suffix">%</div>
                    </div>
                  </el-form-item>
                  <span class="mlJ1 mrJ1">-</span>
                  <el-form-item prop="slidingMax">
                    <div class="input-number mtJ4">
                      <el-input-number
                        :placeholder="$T('请输入')"
                        :min="0"
                        :precision="2"
                        :controls="false"
                        v-model="CetForm_pagedialog.data.slidingMax"
                      ></el-input-number>
                      <div class="input-number-suffix">%</div>
                    </div>
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
          </div>
        </template>

        <!-- 往复式 -->
        <template v-if="inputData_in.principleType === 2">
          <!-- 回流调节 -->
          <div class="card-box mbJ1">
            <div class="flex mbJ3 lh32">
              <div class="common-title-H3">{{ $T("回流调节") }}</div>
              <div
                class="mlJ3 title-tips"
                :class="_.isEmpty(refluxMode) ? 'tips-gray' : 'tips-green'"
              >
                <template v-if="!_.isEmpty(refluxMode)">
                  <omega-icon
                    class="text-Sta1 mrJ1"
                    symbolId="working-condition-lin"
                  ></omega-icon>
                  <span class="text-Sta1 mrJ3">{{ $T("当前回流工况") }}</span>
                  <span class="text-T3 mr4">{{ $T("基准工况") }}</span>
                  <span class="text-T1 mrJ3">
                    {{ $T("回流开度{0}%", refluxMode.adjustmentResult) }}
                  </span>
                  <span class="text-T3 mr4">{{ $T("工况生效时间") }}</span>
                  <span class="text-T1 mrJ3">
                    {{ formatterTime(refluxMode.effectiveTime) }}
                  </span>
                  <span class="text-T3 mr4">{{ $T("基准输气量") }}</span>
                  <span class="text-T1">
                    {{ formatNumberWithPrecision(refluxMode.referenceVolume, 2)
                    }}{{ $T("(万立方米/日)") }}
                  </span>
                </template>
                <template v-else>
                  <omega-icon
                    class="text-T3 mrJ1"
                    symbolId="working-condition-lin"
                  ></omega-icon>
                  <span class="text-T1 mrJ3">{{ $T("暂无回流工况") }}</span>
                  <span class="text-T3">
                    {{ $T("需要在工况调整记录里添加") }}
                  </span>
                </template>
              </div>
            </div>
            <el-row :gutter="16">
              <el-col :span="8">
                <div class="flex row-center">
                  <el-form-item
                    prop="refluxMinimum"
                    :label="$T('可调范围')"
                    style="width: calc(50% - 10px)"
                  >
                    <div class="input-number">
                      <el-input-number
                        :placeholder="$T('请输入')"
                        :min="0"
                        :precision="2"
                        :controls="false"
                        v-model="CetForm_pagedialog.data.refluxMinimum"
                      ></el-input-number>
                      <div class="input-number-suffix">%</div>
                    </div>
                  </el-form-item>
                  <span class="mlJ1 mrJ1">-</span>
                  <el-form-item
                    prop="refluxMaximum"
                    style="width: calc(50% - 10px)"
                  >
                    <div class="input-number mtJ4">
                      <el-input-number
                        :placeholder="$T('请输入')"
                        :min="0"
                        :precision="2"
                        :controls="false"
                        v-model="CetForm_pagedialog.data.refluxMaximum"
                      ></el-input-number>
                      <div class="input-number-suffix">%</div>
                    </div>
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  prop="refluxBaseDeviationTolerance"
                  :rules="CetForm_pagedialog.rules.baseDeviationTolerance"
                >
                  <div slot="label">
                    <span class="mrJ1">{{ $T("基准偏离容忍度") }}</span>
                    <el-tooltip
                      :content="
                        $T(
                          '日输气量偏离基准态百分比合理范围配置，偏离低于该百分比则认为日输气量未偏离'
                        )
                      "
                      placement="top"
                    >
                      <el-icon class="el-icon-info"></el-icon>
                    </el-tooltip>
                  </div>
                  <div class="flex">
                    <div
                      class="input-number mr4"
                      style="width: calc(100% - 80px)"
                    >
                      <el-input-number
                        style="width: 100%"
                        :placeholder="$T('请输入')"
                        :min="0"
                        :precision="2"
                        :controls="false"
                        v-model="
                          CetForm_pagedialog.data.refluxBaseDeviationTolerance
                        "
                      ></el-input-number>
                      <div class="input-number-suffix">%</div>
                    </div>
                    <span class="lh32">{{ $T("基准输气量") }}</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 余隙调节 -->
          <div class="card-box mbJ1">
            <div class="flex mbJ3 lh32">
              <div class="common-title-H3">{{ $T("余隙调节") }}</div>
              <div
                class="mlJ3 title-tips"
                :class="
                  _.isEmpty(clearanceRegulationMode)
                    ? 'tips-gray'
                    : 'tips-green'
                "
              >
                <template v-if="!_.isEmpty(clearanceRegulationMode)">
                  <omega-icon
                    class="text-Sta1 mrJ1"
                    symbolId="working-condition-lin"
                  ></omega-icon>
                  <span class="text-Sta1 mrJ3">{{ $T("当前余隙工况") }}</span>
                  <span class="text-T3 mr4">{{ $T("基准工况") }}</span>
                  <span class="text-T1 mrJ3">
                    {{
                      $T(
                        "余隙开度{0}%",
                        clearanceRegulationMode.adjustmentResult
                      )
                    }}
                  </span>
                  <span class="text-T3 mr4">{{ $T("工况生效时间") }}</span>
                  <span class="text-T1 mrJ3">
                    {{ formatterTime(clearanceRegulationMode.effectiveTime) }}
                  </span>
                  <span class="text-T3 mr4">{{ $T("基准输气量") }}</span>
                  <span class="text-T1">
                    {{
                      formatNumberWithPrecision(
                        clearanceRegulationMode.referenceVolume,
                        2
                      )
                    }}{{ $T("(万立方米/日)") }}
                  </span>
                </template>
                <template v-else>
                  <omega-icon
                    class="text-T3 mrJ1"
                    symbolId="working-condition-lin"
                  ></omega-icon>
                  <span class="text-T1 mrJ3">{{ $T("暂无余隙工况") }}</span>
                  <span class="text-T3 mrJ3">
                    {{ $T("需要在工况调整记录里添加") }}
                  </span>
                  <span class="text-T3">
                    {{ $T("基准输气量 --") }}
                  </span>
                </template>
              </div>
            </div>
            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item
                  :label="$T('调节方式')"
                  prop="clearanceRegulationMethod"
                >
                  <el-select
                    style="width: 100%"
                    v-model="CetForm_pagedialog.data.clearanceRegulationMethod"
                    :placeholder="$T('请选择')"
                  >
                    <el-option
                      v-for="item in clearanceRegulationMethodOption"
                      :key="item.id"
                      :value="item.id"
                      :label="item.text"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                :span="8"
                v-if="
                  CetForm_pagedialog.data.clearanceRegulationMethod === 1 ||
                  CetForm_pagedialog.data.clearanceRegulationMethod === 2
                "
              >
                <div class="flex row-center">
                  <el-form-item
                    prop="clearanceRegulationMin"
                    :label="$T('可调范围')"
                    style="width: calc(50% - 10px)"
                  >
                    <div class="input-number">
                      <el-input-number
                        :placeholder="$T('请输入')"
                        :min="0"
                        :precision="2"
                        :controls="false"
                        v-model="CetForm_pagedialog.data.clearanceRegulationMin"
                      ></el-input-number>
                      <div class="input-number-suffix">%</div>
                    </div>
                  </el-form-item>
                  <span class="mlJ1 mrJ1">-</span>
                  <el-form-item
                    prop="clearanceRegulationMax"
                    style="width: calc(50% - 10px)"
                  >
                    <div class="input-number mtJ4">
                      <el-input-number
                        :placeholder="$T('请输入')"
                        :min="0"
                        :precision="2"
                        :controls="false"
                        v-model="CetForm_pagedialog.data.clearanceRegulationMax"
                      ></el-input-number>
                      <div class="input-number-suffix">%</div>
                    </div>
                  </el-form-item>
                </div>
              </el-col>
              <el-col
                :span="8"
                v-if="
                  CetForm_pagedialog.data.clearanceRegulationMethod === 1 ||
                  CetForm_pagedialog.data.clearanceRegulationMethod === 2
                "
              >
                <el-form-item
                  prop="clearanceRegulationBaseDeviationTolerance"
                  :rules="CetForm_pagedialog.rules.baseDeviationTolerance"
                >
                  <div slot="label">
                    <span class="mrJ1">{{ $T("基准偏离容忍度") }}</span>
                    <el-tooltip
                      :content="
                        $T(
                          '日输气量偏离基准态百分比合理范围配置，偏离低于该百分比则认为日输气量未偏离'
                        )
                      "
                      placement="top"
                    >
                      <el-icon class="el-icon-info"></el-icon>
                    </el-tooltip>
                  </div>
                  <div class="flex">
                    <div
                      class="input-number mr4"
                      style="width: calc(100% - 80px)"
                    >
                      <el-input-number
                        style="width: 100%"
                        :placeholder="$T('请输入')"
                        :min="0"
                        :precision="2"
                        :controls="false"
                        v-model="
                          CetForm_pagedialog.data
                            .clearanceRegulationBaseDeviationTolerance
                        "
                      ></el-input-number>
                      <div class="input-number-suffix">%</div>
                    </div>
                    <span class="lh32">{{ $T("基准输气量") }}</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 气缸调节 -->
          <div class="card-box mbJ1">
            <div class="flex mbJ3 lh32">
              <div class="common-title-H3">{{ $T("气缸调节") }}</div>
              <div
                class="mlJ3 title-tips"
                :class="_.isEmpty(cylinderMode) ? 'tips-gray' : 'tips-green'"
              >
                <template v-if="!_.isEmpty(cylinderMode)">
                  <omega-icon
                    class="text-Sta1 mrJ1"
                    symbolId="working-condition-lin"
                  ></omega-icon>
                  <span class="text-Sta1 mrJ3">{{ $T("当前气缸工况") }}</span>
                  <span class="text-T3 mr4">{{ $T("基准工况") }}</span>
                  <span class="text-T1 mrJ3">
                    {{ formatterCylinderMode(cylinderMode.adjustmentResult) }}
                  </span>
                  <span class="text-T3 mr4">{{ $T("工况生效时间") }}</span>
                  <span class="text-T1 mrJ3">
                    {{ formatterTime(cylinderMode.effectiveTime) }}
                  </span>
                  <span class="text-T3 mr4">{{ $T("基准输气量") }}</span>
                  <span class="text-T1">
                    {{
                      formatNumberWithPrecision(
                        cylinderMode.referenceVolume,
                        2
                      )
                    }}{{ $T("(万立方米/日)") }}
                  </span>
                </template>
                <template v-else>
                  <omega-icon
                    class="text-T3 mrJ1"
                    symbolId="working-condition-lin"
                  ></omega-icon>
                  <span class="text-T1 mrJ3">{{ $T("暂无气缸工况") }}</span>
                  <span class="text-T3 mrJ3">
                    {{ $T("需要在工况调整记录里添加") }}
                  </span>
                  <span class="text-T3">
                    {{ $T("基准输气量 --") }}
                  </span>
                </template>
              </div>
            </div>
            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item
                  :label="$T('气缸组数')"
                  prop="cylinderBanksNumber"
                >
                  <el-select
                    style="width: 100%"
                    v-model="CetForm_pagedialog.data.cylinderBanksNumber"
                    :placeholder="$T('请选择')"
                    @change="changeCylinderBanksNumber"
                  >
                    <el-option
                      v-for="item in cylinderBanksNumberOption"
                      :key="item.id"
                      :value="item.id"
                      :label="item.text"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  :label="$T('气缸作用方式')"
                  prop="cylinderOperationMode"
                >
                  <el-select
                    style="width: 100%"
                    v-model="CetForm_pagedialog.data.cylinderOperationMode"
                    :placeholder="$T('请选择')"
                  >
                    <el-option
                      v-for="item in cylinderOperationModeOption"
                      :key="item.id"
                      :value="item.id"
                      :label="item.text"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  prop="cylinderBaseDeviationTolerance"
                  :rules="CetForm_pagedialog.rules.baseDeviationTolerance"
                >
                  <div slot="label">
                    <span class="mrJ1">{{ $T("基准偏离容忍度") }}</span>
                    <el-tooltip
                      :content="
                        $T(
                          '日输气量偏离基准态百分比合理范围配置，偏离低于该百分比则认为日输气量未偏离'
                        )
                      "
                      placement="top"
                    >
                      <el-icon class="el-icon-info"></el-icon>
                    </el-tooltip>
                  </div>
                  <div class="flex">
                    <div
                      class="input-number mr4"
                      style="width: calc(100% - 80px)"
                    >
                      <el-input-number
                        style="width: 100%"
                        :placeholder="$T('请输入')"
                        :min="0"
                        :precision="2"
                        :controls="false"
                        v-model="
                          CetForm_pagedialog.data.cylinderBaseDeviationTolerance
                        "
                      ></el-input-number>
                      <div class="input-number-suffix">%</div>
                    </div>
                    <span class="lh32">{{ $T("基准输气量") }}</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </template>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";
import common from "eem-utils/common";
export default {
  name: "editOperationScheme",
  components: {},
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("编辑设备运行方案"),
        width: "640px",
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      refluxMode: {},
      clearanceRegulationMode: {},
      cylinderMode: {},
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "static", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [
            "id",
            "gascompressorId",
            "slidingValveMode",
            "slidingMin",
            "slidingMax",
            "refluxMinimum",
            "refluxMaximum",
            "refluxBaseDeviationTolerance",
            "clearanceRegulationMethod",
            "clearanceRegulationMin",
            "clearanceRegulationMax",
            "clearanceRegulationBaseDeviationTolerance",
            "cylinderBanksNumber",
            "cylinderOperationMode",
            "cylinderBaseDeviationTolerance"
          ], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {
          id: null,
          gascompressorId: null,
          slidingValveMode: null,
          slidingMin: null,
          slidingMax: null,
          refluxMinimum: null,
          refluxMaximum: null,
          refluxBaseDeviationTolerance: null,
          clearanceRegulationMethod: null,
          clearanceRegulationMin: null,
          clearanceRegulationMax: null,
          clearanceRegulationBaseDeviationTolerance: null,
          cylinderBanksNumber: null,
          cylinderOperationMode: null,
          cylinderBaseDeviationTolerance: null
        },
        labelPosition: "top",
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        rules: {
          slidingValveMode: [
            {
              required: true,
              message: $T("请选择气量调节方式"),
              trigger: "blur"
            }
          ],
          slidingMin: [
            {
              required: true,
              trigger: "blur",
              validator: (rule, value, callback) =>
                this.minRule(
                  rule,
                  value,
                  callback,
                  this.CetForm_pagedialog.data.slidingMax
                )
            }
          ],
          slidingMax: [
            {
              required: true,
              trigger: "blur",
              validator: (rule, value, callback) =>
                this.maxRule(
                  rule,
                  value,
                  callback,
                  this.CetForm_pagedialog.data.slidingMin
                )
            }
          ],
          refluxMinimum: [
            {
              required: true,
              trigger: "blur",
              validator: (rule, value, callback) =>
                this.minRule(
                  rule,
                  value,
                  callback,
                  this.CetForm_pagedialog.data.refluxMaximum
                )
            }
          ],
          refluxMaximum: [
            {
              required: true,
              trigger: "blur",
              validator: (rule, value, callback) =>
                this.maxRule(
                  rule,
                  value,
                  callback,
                  this.CetForm_pagedialog.data.refluxMinimum
                )
            }
          ],
          baseDeviationTolerance: [
            {
              required: true,
              message: $T("请输入基准偏差容忍度"),
              trigger: "blur"
            }
          ],
          clearanceRegulationMethod: [
            {
              required: true,
              message: $T("请选择余隙调节方式"),
              trigger: "blur"
            }
          ],
          clearanceRegulationMin: [
            {
              required: true,
              trigger: ["blur", "change"],
              validator: (rule, value, callback) =>
                this.minRule(
                  rule,
                  value,
                  callback,
                  this.CetForm_pagedialog.data.clearanceRegulationMax
                )
            }
          ],
          clearanceRegulationMax: [
            {
              required: true,
              trigger: ["blur", "change"],
              validator: (rule, value, callback) =>
                this.maxRule(
                  rule,
                  value,
                  callback,
                  this.CetForm_pagedialog.data.clearanceRegulationMin
                )
            }
          ],
          cylinderBanksNumber: [
            {
              required: true,
              message: $T("请选择气缸组数"),
              trigger: "blur"
            }
          ],
          cylinderOperationMode: [
            {
              required: true,
              message: $T("请选择气缸作用方式"),
              trigger: "blur"
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_pagedialog_saveData_out
        }
      },
      slidingValveModeOption: [
        {
          id: 0,
          text: $T("手动")
        },
        {
          id: 1,
          text: $T("自动")
        }
      ],
      clearanceRegulationMethodOption: [
        {
          id: 0,
          text: $T("不可调节")
        },
        {
          id: 1,
          text: $T("手动调节")
        },
        {
          id: 2,
          text: $T("自动调节")
        }
      ],
      cylinderBanksNumberOption: [
        {
          id: 1,
          text: $T("1组"),
          children: [
            {
              id: 0,
              text: $T("单作用")
            },
            {
              id: 1,
              text: $T("双作用")
            }
          ]
        },
        {
          id: 2,
          text: $T("2组"),
          children: [
            {
              id: 2,
              text: $T("单双可调")
            },
            {
              id: 3,
              text: $T("单单作用")
            },
            {
              id: 4,
              text: $T("双双作用")
            }
          ]
        }
      ],
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  computed: {
    cylinderOperationModeOption() {
      const cylinderBanksNumber =
        this.CetForm_pagedialog.data.cylinderBanksNumber;
      const obj = this.cylinderBanksNumberOption.find(
        item => item.id === cylinderBanksNumber
      );
      return obj?.children || [];
    }
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.CetDialog_pagedialog.width =
        this.inputData_in.principleType === 2 ? "900px" : "640px";
      this.CetForm_pagedialog.data.gascompressorId =
        this.inputData_in?.gascompressorId;
      this.queryDeviceOperationSchemeDetails();
      this.queryWorkingConditionDetails();
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    }
  },
  methods: {
    /**
     * 获取方案详情
     */
    queryDeviceOperationSchemeDetails() {
      if (!this.inputData_in.id) return;
      customApi
        .queryDeviceOperationSchemeDetails([this.inputData_in.id])
        .then(res => {
          this.CetForm_pagedialog.data = res.data?.[0] || {};
        });
    },

    /**
     * 获取工况详情
     */
    queryWorkingConditionDetails() {
      const params = {
        gascompressorId: this.inputData_in.gascompressorId,
        adjustmentTypeList: []
      };
      customApi.queryWorkingConditionDetails(params).then(res => {
        const data = res.data || [];
        this.refluxMode = data.find(item => item.adjustmentType === 0) || {};
        this.clearanceRegulationMode =
          data.find(item => item.adjustmentType === 1) || {};
        this.cylinderMode = data.find(item => item.adjustmentType === 2) || {};
      });
    },

    /**
     * 切换气缸组数-清空气缸作用方式
     */
    changeCylinderBanksNumber(val) {
      this.CetForm_pagedialog.data.cylinderOperationMode = null;
    },

    /**
     * 保存数据
     */
    CetForm_pagedialog_saveData_out(val) {
      customApi
        .updateDeviceOperationScheme([this.CetForm_pagedialog.data])
        .then(res => {
          if (res.code === 0) {
            this.$message.success($T("保存成功"));
            this.CetDialog_pagedialog.closeTrigger_in = Date.now();
            this.$emit("saveData_out", this._.cloneDeep(val));
          }
        });
    },

    /**
     * 打开弹窗
     */
    CetDialog_pagedialog_openTrigger_out(val) {
      this.$emit("openTrigger_out", val);
    },

    /**
     * 关闭弹窗
     */
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.CetForm_pagedialog.data = {
        id: null,
        gascompressorId: null,
        slidingValveMode: null,
        slidingMin: null,
        slidingMax: null,
        refluxMinimum: null,
        refluxMaximum: null,
        refluxBaseDeviationTolerance: null,
        clearanceRegulationMethod: null,
        clearanceRegulationMin: null,
        clearanceRegulationMax: null,
        clearanceRegulationBaseDeviationTolerance: null,
        cylinderBanksNumber: null,
        cylinderOperationMode: null,
        cylinderBaseDeviationTolerance: null
      };
      this.refluxMode = {};
      this.clearanceRegulationMode = {};
      this.cylinderMode = {};
      this.$emit("closeTrigger_out", val);
    },

    /**
     * 确认
     */
    CetButton_preserve_statusTrigger_out(val) {
      this.CetForm_pagedialog.localSaveTrigger_in = this._.cloneDeep(val);
    },

    /**
     * 取消
     */
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },

    /**
     * 最小值规则
     */
    minRule(rule, value, callback, max) {
      if (!_.isNumber(value)) {
        callback(new Error($T("请输入可调范围最大值")));
        return;
      }

      if (max && value > max) {
        callback(new Error($T("最小值不能大于最大值")));
        return;
      }
      callback();
    },

    /**
     * 最大值规则
     */
    maxRule(rule, value, callback, min) {
      if (!_.isNumber(value)) {
        callback(new Error($T("请输入可调范围最大值")));
        return;
      }

      if (min && value < min) {
        callback(new Error($T("最大值不能小于最小值")));
        return;
      }
      callback();
    },

    /**
     * 时间转换
     */
    formatterTime(val) {
      if (val) {
        return this.$moment(val).format("YYYY-MM-DD HH:mm");
      } else {
        return "--";
      }
    },

    /**
     * 保留小数位
     */
    formatNumberWithPrecision(...args) {
      return common.formatNumberWithPrecision(...args);
    },

    /**
     * 气缸基准工况转换
     */
    formatterCylinderMode(val) {
      let list = [];
      this.cylinderBanksNumberOption.forEach(item => {
        list.push(...item.children);
      });
      const obj = list.find(item => item.id === val);
      return obj?.text || "--";
    }
  }
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
  }
}
.card-box {
  padding: 16px;
  border-radius: 8px;
  @include background_color(BG1);
}
.input-number {
  display: flex;
  position: relative;
  &-suffix {
    position: absolute;
    right: 1px;
    top: 1px;
    width: 40px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 0px 4px 4px 0px;
    @include background_color(BG12);
  }
  :deep(.el-input__inner) {
    padding-right: 48px;
  }
}
.row-center {
  align-items: center;
}
.flex-end {
  margin-left: auto;
  justify-content: flex-end;
}
.title-tips {
  height: 32px;
  line-height: 32px;
  padding: 0 16px;
}
.tips-green {
  @include background_color(BG_oil1);
}
.tips-gray {
  @include background_color(BG);
}
.mr4 {
  margin-right: 4px;
}
</style>
