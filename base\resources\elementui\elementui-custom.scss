/*一些全局性的对elementui的css扩展*/

.el-table--small {
  font-size: 14px;
}
.el-table thead {
  // color: #74788d;
  font-weight: 500;
}

.el-button--small.el-button--text {
  font-size: 14px;
}
// 正方形button
.custom—square {
  height: 32px;
  .el-button {
    @include border_color(B2);
    padding: 9px;
    & > span {
      padding-left: 0;
      margin-left: 0;
    }
  }
}
.el-tabs {
  &--border-card {
    .el-tabs__content {
      box-sizing: border-box;
      padding: 10px;
      .el-tab-pane {
        height: 100%;
      }
    }
  }
}

.el-input-number {
  vertical-align: top !important;
}

.el-dialog {
  margin: 0px auto;
}
.el-dialog__title {
  font-size: 16px;
}

// tree的样式修改为giantree一致
// 取消行高亮等颜色
.el-tree-node:focus > .el-tree-node__content {
  background-color: transparent !important;
}

.el-tree-node__content:hover {
  background-color: transparent;
}

.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: transparent;
}
// 添加文字高亮
.el-tree-node__content .el-tree-node__label {
  padding: 0 4px;
  border-radius: 2px;
}

.el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content
  > .el-tree-node__label {
  @include background_color("BG4");
  @include font_color("ZS");
}

.el-tree-node > .el-tree-node__children {
  overflow: visible !important;
}
//控制存在图标按钮高度为32px
.el-button--small > span > i {
  font-size: 12px;
}
//表格展示内容区域间距
.el-table .el-table__expanded-cell {
  @include padding_top(J3);
  @include padding_right(J5);
  @include padding_bottom(J3);
  @include padding_left(J5);
}
