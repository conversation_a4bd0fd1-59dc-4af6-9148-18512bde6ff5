<template>
  <div class="pagebox">
    <el-container class="contentBox">
      <div style="width: 316px" class="searchbox flex-column">
        <el-row>
          <el-col :span="24">
            <div class="basic-box mlJ2 mrJ2">
              <span class="basic-box-label">响应区域</span>
              <!-- 响应区域 -->
              <el-cascader
                class="cascader"
                clearable
                v-model="address"
                :props="treeProps"
                :options="areaOptions"
                @change="handelclick"
              ></el-cascader>
            </div>
          </el-col>
        </el-row>
        <el-aside width="316px" class="aside flex-auto mbJ2">
          <CetTree
            :selectNode.sync="CetTree_1.selectNode"
            :checkedNodes.sync="CetTree_1.checkedNodes"
            :searchText_in.sync="CetTree_1.searchText_in"
            v-bind="CetTree_1"
            v-on="CetTree_1.event"
            show-checkbox
            ref="tree"
            default-expand-all
            @node-click="treeclick"
          ></CetTree>
        </el-aside>
      </div>
      <el-main
        style="height: 100%; padding: 0px"
        class="marginLeftJ2 flex-column"
      >
        <el-row>
          <el-col class="box-col" :span="24">
            <div class="basic-box fl">
              <span class="basic-box-label">目标日期</span>
              <el-date-picker
                v-model="sameDay"
                value-format="yyyy-MM-dd 00:00:00"
                style="width: 200px"
                type="date"
                size="small"
                class="datePicker fl marginRightJ1"
                :picker-options="CetDatePicker_1.config.pickerOptions"
                placeholder="请筛选"
                :disabled="defaultdisabled"
                @change="defaultchange"
              ></el-date-picker>
            </div>
            <div class="basic-box fl">
              <span class="basic-box-label">基线参考日</span>
              <el-date-picker
                v-model="searchdate"
                value-format="yyyy-MM-dd 00:00:00"
                style="width: 200px"
                :picker-options="CetDatePicker_1.config.pickerOptions"
                type="dates"
                size="small"
                class="datePicker datePickertwo fl marginRightJ1"
                placeholder="请筛选"
                :disabled="basedisabled"
                @change="datechange"
              ></el-date-picker>
            </div>
            <!-- <div
              class="el-form-item__label clearfix"
              style="line-height: inherit; padding-right: 8px"
            >
              <div>
                <el-time-picker
                  class="fl datePickerrange"
                  style="width: 340px; height: 34px"
                  v-model="CetDatePicker_1.val"
                  value-format="HH:00:00"
                  format="HH:00:00"
                  is-range
                  size="small"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  @change="timechange"
                  popper-class="tpcrange"
                ></el-time-picker> -->
            <div class="basic-box fl">
              <span class="basic-box-label">起始时间</span>
              <el-time-select
                class="timeSelect marginRightJ1 fl"
                placeholder="起始时间"
                v-model="CetDatePicker_1.val[0]"
                :picker-options="{
                  start: '00:00',
                  step: '00:60',
                  end: '24:00'
                }"
              ></el-time-select>
            </div>
            <div class="basic-box fl">
              <span class="basic-box-label">结束时间</span>
              <el-time-select
                class="timeSelect fl"
                placeholder="结束时间"
                v-model="CetDatePicker_1.val[1]"
                :picker-options="{
                  start: '00:00',
                  step: '00:60',
                  end: '24:00',
                  minTime: CetDatePicker_1.val[0]
                }"
              ></el-time-select>
            </div>
            <!-- </div>
            </div> -->
            <el-button
              type="primary"
              class="marginLeftJ1 fl"
              @click="getchartData"
              :disabled="
                (!searchdate && !sameDay) ||
                !CetDatePicker_1.val[0] ||
                !CetDatePicker_1.val[1]
              "
            >
              计算
            </el-button>
          </el-col>
        </el-row>

        <div style="height: 180px" class="countbox">
          <div class="leftcount">
            <h5>
              {{ isBaseDate ? "基线参考日（自定义）" : "基线参考日（默认）" }}
              <el-tooltip placement="bottom-start">
                <div slot="content">
                  <p>
                    选择目标日期时，采用系统默认基线参考日选取和剔除规则进行计算；
                  </p>
                  <p>选择基线参考日时，基线负荷根据自定义所选参考日计算。</p>
                </div>
                <span class="el-icon-info" style="font-size: 18px"></span>
              </el-tooltip>
            </h5>
            <div class="datebox" v-if="!isBaseDate">
              <p v-for="(item, index) in defaultdate" :key="index">
                {{ item }}
              </p>
            </div>
            <div class="datebox" v-if="isBaseDate">
              <p v-for="(item, index) in datelist" :key="index">
                {{ item }}
              </p>
            </div>
          </div>
          <div class="rightcount">
            <h5>
              {{
                Number(avaeragedata)
                  ? Math.floor(avaeragedata * 100) / 100
                  : "--"
              }}
            </h5>

            <p>基线平均负荷(kW)</p>
          </div>
        </div>

        <div class="tableBody flex-auto">
          <div class="empty" v-if="chartdata.length === 0">
            <img src="/static/assets/empty.svg" class="imgbox" alt="" />
          </div>
          <CetChart
            v-if="chartdata.length !== 0"
            :inputData_in="CetChart_1.inputData_in"
            v-bind="CetChart_1.config"
          />
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import moment from "moment";
export default {
  props: {},
  computed: {},
  data() {
    return {
      avaeragedata: "",
      checked: false,
      isBaseDate: false,
      datelist: [],
      datedata: [],
      chartdata: [],
      defaultdate: [],
      // sameDay: (new Date()).getTime(),
      basedisabled: false,
      defaultdisabled: false,
      sameDay: "",
      address: "",
      treeProps: {
        expandTrigger: "hover",
        children: "children",
        label: "name",
        value: "name"
      },
      areaOptions: [],
      newareaOptions: [],
      searchdate: "",
      num: 2,
      isborderbottom: false,

      activeName: "负荷聚合商",
      sum: 0,
      showDetail: false,
      // 1树组件
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        // checkStrictly:true,
        nodeKey: "tree_id",
        props: {
          label: function (data, node) {
            let title = data.name ? data.name : "";
            let num = data.accountno ? data.accountno : " ";
            let meterno = data.meterno ? data.meterno : " ";
            if (data.modelLabel === "demandgroup") {
              return title + num;
            } else if (data.modelLabel === "demandaccount") {
              return title + meterno;
            } else {
              return data.name;
            }
          },
          children: "children"
        },
        highlightCurrent: true,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      ElInput_search: {
        value: "",

        style: {},
        event: {}
      },

      ElSelect_1: {
        value: "",
        style: {
          width: "200px"
        },
        event: {}
      },
      ElOption_1: {
        options_in: [
          {
            id: 1,
            name: "类型1"
          },
          {
            id: 2,
            name: "类型2"
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: "",
        style: {
          width: "200px"
        },
        event: {}
      },
      ElOption_2: {
        options_in: [
          {
            id: 1,
            name: "类型1"
          },
          {
            id: 2,
            name: "类型2"
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetDatePicker_1: {
        disable_in: false,
        val: ["00:00", "24:00"],
        config: {
          valueFormat: "timestamp",
          rangeSeparator: "至",
          clearable: false,
          size: "small",
          pickerOptions: {
            // disabledDate(time) {
            //   return time.getTime() > Date.now();
            // }
          }
        }
      },
      CetChart_1: {
        inputData_in: {},
        config: {
          options: {
            legend: {
              data: [],
              right: 30,
              top: 0,
              itemGap: 20,
              selected: {},
              // icon: "circle",
              textStyle: {
                color: "#9CA2B1"
              }
            },
            grid: {
              top: 80,
              left: 70,
              right: 70
            },
            tooltip: {
              extraCssText:
                'background: #0F2557; border-radius: 12px;color: #fff; opacity: 0.8; box-shadow: "0px 0px 4px 0px rgba(62, 66, 78, 0.22)";border:none;text-align:left;',
              trigger: "axis",
              axisPointer: {
                type: "shadow"
              },
              textStyle: {
                color: "#FFF",
                fontsize: "10px"
              },

              formatter: function (params) {
                let str = params[0].name + "<br>";
                let arr = [];
                params.forEach((item, index) => {
                  arr.push(item.seriesName);
                });
                arr
                  .sort()
                  .reverse()
                  .forEach((item, index) => {
                    params.forEach(code => {
                      if (code.seriesName === item) {
                        arr[index] = code;
                        str +=
                          code.marker +
                          code.seriesName +
                          "(kW):" +
                          code.value.toFixed(2) +
                          "<br>";
                      }
                    });
                  });
                return str;
              }
            },
            xAxis: {
              type: "category",
              // boundaryGap: false,
              name: "时间",
              axisLabel: {
                //---坐标轴 标签
                show: true, //---是否显示
                inside: false, //---是否朝内
                rotate: 0, //---旋转角度
                margin: 5, //---刻度标签与轴线之间的距离
                color: "#9CA2B1" //---默认取轴线的颜色
              },
              data: []
            },
            yAxis: {
              type: "value",
              name: "功率（kW）",
              scale: true,
              axisLabel: {
                //---坐标轴 标签
                show: true, //---是否显示
                inside: false, //---是否朝内
                rotate: 0, //---旋转角度
                margin: 5, //---刻度标签与轴线之间的距离
                color: "#9CA2B1" //---默认取轴线的颜色
              }
            },
            series: []
          }
        }
      }
    };
  },
  watch: {
    // checked: function (val) {
    //   this.CetTree_1.checkStrictly = !val;
    // }
    "CetTree_1.searchText_in": {
      handler(val) {
        if (val) {
          this.CetTree_1.checkedNodes = [];
        }
        console.log(val);
      },
      immediate: true
    }
  },
  methods: {
    init() {
      this.sum = 0;
      // this.CetTable_1.data = [];
      this.ElInput_search.value = "";
      this.ElSelect_1.value = "";
      // 清除列表勾选
      // this.$refs.CetTable.$refs.cetTable.clearSelection();
    },
    handelclick() {
      this.CetTree_1.checkedNodes = [];
      this.getTreeData();
    },
    getTreeData() {
      let address = this.address ? this.address.join("/") : [];

      let data = {
        address: address,
        powersellingcompanyId: this.$store.state.sellCompanyId
      };

      customApi.baselineCalculationqueryTreeNode(data).then(response => {
        if (response.code === 0) {
          let options = this._.get(response, "data", []);
          this.CetTree_1.inputData_in = options;
        }
      });
    },
    getaddressData() {
      let address = this.address ? this.address.join("/") : [];

      let data = {
        address: address,
        powersellingcompanyId: this.$store.state.sellCompanyId
      };

      customApi.baselineCalculationqueryTreeNode(data).then(response => {
        if (response.code === 0) {
          let options = this._.get(response, "data", []);
          this.CetTree_1.inputData_in = options;
          let arr = options.map(item => {
            return item.address;
          });
          this.newareaOptions = [];
          customApi.demandSideResponsedetailsdistrict().then(response => {
            this.areaOptions = this._.get(response, "data", []);
            let newareaOptions = [];
            let cityOptions = [];
            this.areaOptions.forEach(item => {
              arr.forEach(code => {
                let name = code.split("/")[0];
                if (name == item.name) {
                  newareaOptions.push(item);
                }
              });
            });

            Array.from(new Set(newareaOptions)).forEach(item => {
              let cityone = this._.cloneDeep(item);
              cityone.children = [];
              item.children.forEach(city => {
                arr.forEach(code => {
                  let name = code.split("/")[1];
                  if (name == city.name) {
                    cityone.children.push(city);
                    cityOptions.push(cityone);
                  }
                });
              });
            });
            let list = Array.from(new Set(cityOptions));
            let newcityoptions = [];
            list.forEach(item => {
              item.children = Array.from(new Set(item.children));
              newcityoptions.push(item);
            });

            this.newareaOptions = newcityoptions;
          });
        }
      });
    },
    treeclick(data, node) {
      console.log(data);
    },
    getchartData() {
      this.avaeragedata = "";
      this.defaultdate = [];
      this.clearchart();
      let checknode = this.$refs.tree.$refs.tree.getCheckedNodes(true);
      if (checknode.length <= 0) {
        this.$message({
          type: "warning",
          message: "请先勾选节点再计算"
        });
        return;
      }
      let ids = checknode.map(item => {
        return item.id;
      });
      let itemdate1 = "";
      let itemdate2 = "";
      let data = {
        cycle: 4,
        demandgroupIds: ids,
        powersellingcompanyid: this.$store.state.sellCompanyId
      };

      if (this.isBaseDate) {
        itemdate1 =
          moment(this.datedata[0]).format("yy-MM-DD") +
          " " +
          this.CetDatePicker_1.val[0];
        itemdate2 =
          moment(this.datedata[0]).format("yy-MM-DD") +
          " " +
          this.CetDatePicker_1.val[1];

        data.endtime = moment(itemdate2).valueOf();
        data.starttime = moment(itemdate1).valueOf();

        data.sameDay = new Date(
          moment(this.datedata[0]).format("yy-MM-DD 00:00:00")
        ).getTime();
        data.referencetime = this.datedata;
      } else {
        this.datelist = [];
        data.referencetime = [];
        // this.datedata = [];
        itemdate1 =
          moment(this.sameDay).format("yy-MM-DD") +
          " " +
          this.CetDatePicker_1.val[0];
        itemdate2 =
          moment(this.sameDay).format("yy-MM-DD") +
          " " +
          this.CetDatePicker_1.val[1];
        data.endtime = moment(itemdate2).valueOf();
        data.starttime = moment(itemdate1).valueOf();
        data.sameDay = new Date(
          moment(this.sameDay).format("yy-MM-DD 00:00:00")
        ).getTime();
      }
      data.demandgroupIds = ids;
      customApi.baselineCalculationqueryBaselineRes(data).then(response => {
        if (response.code === 0) {
          if (response.code === 0) {
            this.chartdata = this._.get(response, "data", []);
            this.avaeragedata = this.chartdata.基线平均负荷[0].value;
            this.CetChart_1.config.options.xAxis.data =
              this.chartdata.基线负荷.map(item => {
                return item.timeStr;
              });
            let arr = [];
            let num = 0;
            let colorList = [
              "#5160FF",
              "#FFA435",
              "#F65D68",
              "#08C673",
              "#9F74F0",
              "#7859FD",
              "#F6BD16",
              "#91C100",
              "#10C1E3",
              "#377DFC",
              "#CD2FE7",
              "#F17737"
            ];
            for (var i in this.chartdata) {
              this.CetChart_1.config.options.series.push({
                data: this.chartdata[i].map(item => {
                  return item.value;
                }),
                color: colorList[num],
                type: "line",
                smooth: true,
                showSymbol: false,
                name: i,
                itemStyle: {
                  // color: "#FFA435",
                  borderWidth: 3
                  // borderColor: "#FFA435",

                  // opacity: 0.5
                }
              });
              num++;
              this.CetChart_1.config.options.legend.data.push(i);
              if (i === "基线负荷" || i === "基线平均负荷") {
                console.log();
                this.CetChart_1.config.options.legend.selected[i] = true;
              } else {
                this.CetChart_1.config.options.legend.selected[i] = false;
                arr.push(moment(i).format("yy年MM月DD日"));
              }
            }
            this.CetChart_1.config.options.series.forEach(item => {
              if (item.name === "基线平均负荷") {
                item.data = [];
                this.CetChart_1.config.options.xAxis.data.forEach(time => {
                  item.data.push(this.avaeragedata);
                });
              }
            });

            this.isBaseDate
              ? (this.defaultdate = [])
              : (this.defaultdate = arr.sort());
            // this.CetChart_1.config.options.legend.data.forEach(item => {
            //   if (item.name === "基线负荷" || item.name === "基线平均负荷") {
            //     this.CetChart_1.config.options.legend.selected[
            //       item.name
            //     ] = true;
            //   } else {
            //     this.CetChart_1.config.options.legend.selected[
            //       item.name
            //     ] = false;
            //   }
            // });

            this.CetChart_1.inputData_in = this.chartdata;
            this.CetChart_1.config.options.legend.data.sort().reverse();
          }
        }
      });
    },
    clearchart() {
      this.CetChart_1 = {
        inputData_in: {},
        config: {
          options: {
            legend: {
              data: [],
              itemGap: 20,
              right: 30,
              top: 0,
              selected: {},
              // icon: "circle",
              textStyle: {
                color: "#9CA2B1"
              }
            },
            grid: {
              top: 80,
              left: 70,
              right: 70
            },
            tooltip: {
              extraCssText:
                'background: #0F2557; border-radius: 12px;color: #fff; opacity: 0.8; box-shadow: "0px 0px 4px 0px rgba(62, 66, 78, 0.22)";border:none;text-align:left;',
              trigger: "axis",
              axisPointer: {
                type: "shadow"
              },
              textStyle: {
                color: "#FFF",
                fontsize: "10px"
              },

              formatter: function (params) {
                let str = params[0].name + "<br>";
                let arr = [];
                params.forEach((item, index) => {
                  arr.push(item.seriesName);
                });
                arr
                  .sort()
                  .reverse()
                  .forEach((item, index) => {
                    params.forEach(code => {
                      if (code.seriesName === item) {
                        arr[index] = code;
                        str +=
                          code.marker +
                          code.seriesName +
                          "(kW):" +
                          code.value.toFixed(2) +
                          "<br>";
                      }
                    });
                  });
                return str;
              }
            },
            xAxis: {
              type: "category",
              // boundaryGap: false,
              name: "时间",
              axisLabel: {
                //---坐标轴 标签
                show: true, //---是否显示
                inside: false, //---是否朝内
                rotate: 0, //---旋转角度
                margin: 5, //---刻度标签与轴线之间的距离
                color: "#9CA2B1" //---默认取轴线的颜色
              },
              data: []
            },
            yAxis: {
              type: "value",
              name: "功率（kW）",
              scale: true,
              axisLabel: {
                //---坐标轴 标签
                show: true, //---是否显示
                inside: false, //---是否朝内
                rotate: 0, //---旋转角度
                margin: 5, //---刻度标签与轴线之间的距离
                color: "#9CA2B1" //---默认取轴线的颜色
              }
            },
            series: []
          }
        }
      };
    },
    defaultchange() {
      if (this.sameDay) {
        this.defaultdisabled = false;
        this.basedisabled = true;
        this.isBaseDate = false;
      } else {
        this.basedisabled = false;
      }
    },
    timechange() {
      if (
        this.CetDatePicker_1.val &&
        this.CetDatePicker_1.val[0] === this.CetDatePicker_1.val[1]
      ) {
        this.$message({
          type: "warning",
          message: "开始时间和结束时间不能相等"
        });
        this.CetDatePicker_1.val = "";
      }
    },
    datechange() {
      if (this.searchdate) {
        this.basedisabled = false;
        this.defaultdisabled = true;
        this.isBaseDate = true;
      } else {
        this.defaultdisabled = false;
      }
      if (this.searchdate.length > 12) {
        this.$message({
          type: "warning",
          message: "最多选择十二个参考日"
        });
        this.searchdate.splice(0, this.searchdate.length - 12);
      }

      this.datelist = this.searchdate.sort().map(item => {
        return moment(item).format("yy年MM月DD日");
      });
      this.datedata = this.searchdate.map(item => {
        return moment(item).valueOf();
      });
      // this.datedata.push(moment(this.searchdate).valueOf());
      //       if(this.datedata.length>5){
      //   this.datedata.splice(0,1)
      // }
    },
    CetTree_1_currentNode_out(val) {
      console.log(val);
    },
    handleSelectionChange(val) {
      this.sum = val.length;
    },
    getDistrictTree() {
      customApi.demandSideResponsedetailsdistrict().then(response => {
        this.areaOptions = this._.get(response, "data", []);
      });
    }
  },
  created() {
    this.init();
    // this.getaddressData();
    this.getTreeData();
    this.getDistrictTree();
  },
  activated() {}
};
</script>
<style lang="scss" scoped>
.pagebox {
  margin: 0 !important;
  height: 100%;
  width: 100% !important;
  .searchInput {
    width: 298px;
  }
  .contentBox {
    @include padding_top(J2);
    height: 100% !important;
    box-sizing: border-box;
    .searchbox {
      @include background_color(BG1);
      @include border_radius(C3);
      @include padding_top(J2);
    }
    .aside {
      @include background_color(BG1);
      border-top-left-radius: mh-get(C3);
      border-bottom-left-radius: mh-get(C3);
      // @include padding_top(J3);
      .tree {
        // height: calc(100% - 40px) !important;
        // @include margin_top(J2);
        @include padding_left(J2);
        @include padding_right(J2);
      }
      :deep(.el-tree) {
        @include background_color(BG1);
      }
    }
    .countbox {
      display: flex;
      width: 100%;
      justify-content: space-between;
      @include margin_top(J2);
      @include margin_bottom(J2);
      .leftcount {
        width: 68.8%;
        height: 180px;
        @include background_color(BG1);
        @include border_radius(C3);
        @include padding(J2);
        // padding-left: 30px;
        box-sizing: border-box;
        h5 {
          @include font_size("H2");
          font-weight: 400;
          line-height: 14px;
          margin: 0;
          @include margin_bottom(J2);
          // @include margin_top(J2);
        }
        .datebox {
          display: flex;
          max-width: 860px;
          height: 120px;
          // justify-content: space-around;
          flex-wrap: wrap;
          overflow: auto;
          p {
            width: 20%;
            margin: 10px 21px;
            // margin-right: 40px;
            @include font_size("Aa");
            @include background_color(BG2);
            text-align: center;
            line-height: 30px;
            height: 30px;
            border-radius: 4px;
          }
        }
      }
      .rightcount {
        width: 30%;
        height: 180px;
        @include background_color(BG1);
        @include border_radius(C3);
        text-align: center;
        h5 {
          height: 40px;
          @include font_size("H");
          margin: 40px 0 10px 0;
          font-weight: 500;
          line-height: 40px;
        }
        p {
          height: 20px;
          @include font_size("Aa");

          font-weight: 400;
          line-height: 20px;
        }
      }
    }

    .bgcolor {
      @include background_color(BG1);
      @include border_radius(C3);
    }
    .tableBody {
      width: 100%;
      margin: auto;
      text-align: center;
      @include background_color(BG1);
      @include padding_top(J2);
      position: relative;
      @include border_radius(C3);
      .empty::after {
        content: "\6682\65e0\6570\636e";
        display: inline-block;
        position: absolute;
        @include font_size("H3");
        line-height: 16px;
        top: calc(50% + 68px);
        margin: auto;
      }
      .imgbox {
        margin: auto;
        position: absolute;
        top: 30%;
        left: 45%;
      }
      .handel {
        cursor: pointer;
      }
    }
  }
  .el-icon-search {
    @include font_size("Aa");
    margin: 10px 7px 0 0;
  }
  .el-dropdown-link {
    cursor: pointer;
  }
}
.datePickerrange {
  width: 300px;
  text-align: left;
  border-radius: 8px !important;
  :deep(.el-range__icon) {
    width: 75px;
  }
  :deep(.el-range-input) {
    text-align: center;
  }
  :deep(.el-range__icon:before) {
    content: "\76ee\6807\65f6\6bb5";
    @include font_color(ZS);
  }
  :deep(.el-range__icon:after) {
    content: "\e71f";
    position: absolute;
    right: 24px;
  }
  :deep(.el-range__close-icon) {
    float: right;
    padding-right: 18px;
  }
  :deep(.el-input__icon.el-icon-circle-close:before) {
    content: "\e79d";
  }
}
.timeSelect {
  width: 100px;
}
.cascader {
  // width: 200px;
  // :deep(.el-input__inner) {
  //   padding-left: 76px;
  // }
  // :deep(.el-input::after) {
  //   content: "\54cd\5e94\533a\57df";
  //   position: absolute;
  //   left: 15px;
  //   @include font_color(ZS);
  // }
}
.el-icon-info {
  @include font_color(B1);
}
</style>
