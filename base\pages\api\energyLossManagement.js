import fetch from "eem-utils/fetch";
const version = "v1";

// 损耗概览
//获取损耗概览数据
export function lossConfigOverview(data, params) {
  return fetch({
    url: `/eem-service/${version}/loss/config/overview`,
    method: "POST",
    timeout: 120000,
    data,
    params
  });
}
//获取损耗分析数据
export function lossConfigDeviceLossAnalysis(data, params) {
  return fetch({
    url: `/eem-service/${version}/loss/config/deviceLossAnalysis`,
    method: "POST",
    data,
    params
  });
}
// 查询账单
export function powerManageQuerylinebill(data) {
  return fetch({
    url: `/eem-service/${version}/loss/config/querylinebill`,
    method: "POST",
    data
  });
}
// 损耗告警
// 获取损耗级别 弃用
export function lossConfigGetlosslevel(data) {
  return fetch({
    url: `/eem-service/${version}/loss/config/getlosslevel`,
    method: "GET",
    data
  });
}

// 获取损耗告警数据
export function lossConfigSystemevents(data) {
  const expressions = data.rootCondition.filter.expressions;
  const page = data.rootCondition.page;
  const queryData = expressions.find(i => i.prop === "queryData").limit;
  Object.assign(queryData, page);
  return fetch({
    url: `/eem-service/${version}/loss/config/systemevents`,
    method: "POST",
    data: queryData
  });
}
// 事件处理
export function powerManageUpdateeventdesc(data) {
  return fetch({
    url: `/eem-service/${version}/loss/config/updateeventdesc`,
    method: "PUT",
    data
  });
}

// 损耗配置
// 获取项目-设备节点树
export function powerManageConfigTree(data, params) {
  return fetch({
    url: `/power-manage/${version}/config/tree`,
    method: "POST",
    data,
    params
  });
}
export function lossConfigProjectTree(data, params) {
  return fetch({
    url: `/eem-service/${version}/loss/config/project/tree`,
    method: "POST",
    data,
    params
  });
}
// 获取选中项的配置信息
export function lossConfigNodes(data) {
  return fetch({
    url: `/eem-service/${version}/loss/config/nodes`,
    method: "POST",
    data
  });
}
// 批量设置线损阈值信息
export function powerManageConfigBatch(data) {
  return fetch({
    url: `/power-manage/${version}/config/batch`,
    method: "POST",
    data
  });
}
// 设置选中项的线损配置信息
export function powerManageConfigNode(data) {
  return fetch({
    url: `/power-manage/${version}/config/node`,
    method: "POST",
    data
  });
}
// 查询账单列表

// 保存账单
export function powerManageSaveBill(data) {
  return fetch({
    url: `/eem-service/${version}/bill/bill`,
    method: "PUT",
    data
  });
}
