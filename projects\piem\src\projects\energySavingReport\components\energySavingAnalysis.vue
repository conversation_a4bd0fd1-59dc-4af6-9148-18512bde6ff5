<template>
  <div class="energySavingAnalysis">
    <CetChart v-bind="CetChart_analysis" :initOptions="rendererType"></CetChart>
  </div>
</template>

<script>
import common from "eem-utils/common";
export default {
  name: "energySavingAnalysis",
  components: {},
  props: {
    chartData: {
      type: Array
    },
    type: {
      type: String,
      default: "energy"
    },
    reportData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      rendererType: { renderer: "svg" },
      // stationAnalysis组件
      CetChart_analysis: {
        //组件输入项
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            },
            confine: true
          },
          grid: {
            top: 40,
            bottom: 10,
            left: 10,
            right: 0,
            containLabel: true
          },
          legend: {
            show: false
          },
          xAxis: {
            type: "category",
            axisTick: { show: false },
            axisLabel: {
              show: true,
              fontSize: 12,
              color: "#798492",
              interval: 0,
              formatter: params => {
                const obj = this.chartData?.find(item => item.name === params);
                return obj?.order;
              }
            }
          },
          yAxis: {
            name: $T("单位(kWh)"),
            type: "value",
            axisLabel: {
              color: "#798492"
            },
            axisLine: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: "#E0E4E8",
                type: "dashed"
              }
            },
            nameTextStyle: {
              color: "#798492",
              padding: [0, 0, 10, 0]
            }
          },
          color: this.type === "cost" ? "#00a2ff" : "#29B061",
          series: [
            {
              name: $T("节能量"),
              type: "bar",
              barWidth: 18,
              data: []
            }
          ]
        }
      }
    };
  },
  watch: {
    chartData(val) {
      const key = this.type === "cost" ? "savingCost" : "energySaving";
      this.CetChart_analysis.options.series[0].data = val.map(item => [
        item.name,
        common.formatNumberWithPrecision(item[key], 2)
      ]);
      this.CetChart_analysis.options.series[0].name =
        this.type === "cost" ? $T("节费量") : $T("节能量");
      this.CetChart_analysis.options.yAxis.name =
        this.type === "cost"
          ? "单位(万元)"
          : `单位(${this.reportData.compressorEnergyUnit})`;
    }
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
.energySavingAnalysis {
  width: 100%;
  height: 230px;
}
</style>
