<template>
  <div class="page eem-common">
    <el-container class="fullheight">
      <el-aside width="315px" class="eem-aside flex-column">
        <div class="mbJ1 common-title-H2">{{ $T("选择预算对象") }}</div>
        <el-input
          class="mbJ3"
          :placeholder="$T('输入关键字以检索')"
          suffix-icon="el-icon-search"
          v-model="filterText"
        ></el-input>
        <div class="flex-auto">
          <el-tree
            style="height: 100%"
            :data="data"
            :props="defaultProps"
            default-expand-all
            :filter-node-method="filterNode"
            ref="tree"
            node-key="tree_id"
            @node-click="change"
            highlight-current
            :setCheckedNodes="[1]"
          ></el-tree>
        </div>
      </el-aside>
      <el-container class="fullheight padding0 mlJ3 flex-column">
        <div class="mbJ3 eem-cont">
          <div class="clearfix fr">
            <CetButton
              class="fr custom—square"
              v-bind="CetButton_8"
              v-on="CetButton_8.event"
            ></CetButton>
            <!-- <div class="basic-box-label" style="width: 50px">年份</div> -->
            <CustomElDatePicker
              class="fr mlJ mrJ"
              :prefix_in="$T('选择年份')"
              style="width: 200px"
              v-model="value3"
              type="year"
              :placeholder="$T('请选择年份')"
              @change="yearChange"
              :clearable="false"
            />
            <!-- <div class="block">
              <el-date-picker
                v-model="value3"
                type="year"
                placeholder="请选择"
                @change="yearChange"
                :clearable="false"
              ></el-date-picker>
            </div> -->
            <CetButton
              class="fr custom—square"
              v-bind="CetButton_7"
              v-on="CetButton_7.event"
            ></CetButton>
            <customElSelect
              class="fr mrJ1"
              v-model="ElSelect_1.value"
              v-bind="ElSelect_1"
              v-on="ElSelect_1.event"
              :prefix_in="$T('能源类型')"
            >
              <ElOption
                v-for="item in ElOption_1.options_in"
                :key="item[ElOption_1.key]"
                :label="item[ElOption_1.label]"
                :value="item[ElOption_1.value]"
                :disabled="item[ElOption_1.disabled]"
              ></ElOption>
            </customElSelect>
          </div>
        </div>
        <div class="flex-auto">
          <Implement
            :object="object"
            :projectId="projectId"
            :list="list"
            :source="source"
            :unitTransition="unitTransition"
            v-show="showImplement"
            :visibleTrigger_in="Implement.visibleTrigger_in"
            :closeTrigger_in="Implement.closeTrigger_in"
            :queryId_in="Implement.queryId_in"
            :inputData_in="Implement.inputData_in"
            @finishTrigger_out1="Implement_finishTrigger_out"
            @finishData_out="Implement_finishData_out"
            @saveData_out="Implement_saveData_out"
            @currentData_out="Implement_currentData_out"
          />
          <Entry
            :budgetList="budgetList"
            :year="value3"
            :object="object"
            :energyType="energyType"
            v-show="!showImplement"
            :visibleTrigger_in="Entry.visibleTrigger_in"
            :closeTrigger_in="Entry.closeTrigger_in"
            :queryId_in="Entry.queryId_in"
            :inputData_in="Entry.inputData_in"
            @finishTrigger_out2="Entry_finishTrigger_out"
            @finishData_out="Entry_finishData_out"
            @saveData_out="Entry_saveData_out"
            @currentData_out="Entry_currentData_out"
          />
        </div>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import Entry from "./Entry.vue";
import Implement from "./Implement.vue";
import customApi from "@/api/custom";
import { httping } from "@omega/http";
export default {
  name: "Costbudget",
  components: {
    Entry,
    Implement
  },

  computed: {
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },

  data(vm) {
    return {
      unitTransition: false, // 是否需要进行单位转换
      budgetList: [],
      list: [],
      source: {},
      energyType: 2,
      object: {},
      value3: vm.$moment().startOf("year").format("YYYY"),
      filterText: "",
      data: [],
      defaultProps: {
        children: "children",
        label: "label"
      },
      showImplement: true, // 是否展示执行页面
      ElSelect_1: {
        value: 2,
        style: { width: "200px" },
        event: {
          change: this.energyChange
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetButton_7: {
        visible_in: true,
        disable_in: false,
        title: "",
        plain: true,
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_7_statusTrigger_out
        }
      },
      CetButton_8: {
        visible_in: true,
        disable_in: false,
        title: "",
        plain: true,
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_8_statusTrigger_out
        }
      },
      Entry: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      Implement: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      }
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },

  methods: {
    yearChange(val) {
      const object = this.object;
      let time = val.getTime();
      time = new Date(time * 1).getFullYear();
      const time1 = time + "-1-1";
      const time2 = time * 1 + 1 + "-1-1";
      const startTime = new Date(time1).getTime();
      const endTime = new Date(time2).getTime();
      object.startTime = startTime;
      object.endTime = endTime;
      this.object = object;
      this.getList(object);
    },
    CetButton_7_statusTrigger_out() {
      const object = this.object;
      let time = this.value3;
      if (time > 1000000000) {
        time = new Date(time * 1).getFullYear();
      }
      time--;
      console.log(time);
      // this.value3 = time.toString();
      this.value3 = new Date(time.toString());
      const time1 = time + "-1-1";
      const time2 = time * 1 + 1 + "-1-1";
      const startTime = new Date(time1).getTime();
      const endTime = new Date(time2).getTime();
      object.startTime = startTime;
      object.endTime = endTime;
      console.log(startTime, endTime);
      this.object = object;
      this.getList(object);
    },
    CetButton_8_statusTrigger_out() {
      const object = this.object;
      let time = this.value3;
      if (time > 1000000000) {
        time = new Date(time * 1).getFullYear();
      }
      time++;
      // this.value3 = time.toString();
      this.value3 = new Date(time.toString());
      const time1 = time + "-1-1";
      const time2 = time * 1 + 1 + "-1-1";
      const startTime = new Date(time1).getTime();
      const endTime = new Date(time2).getTime();
      object.startTime = startTime;
      object.endTime = endTime;
      this.object = object;
      this.getList(object);
    },
    change(val) {
      const object = this.object;
      object.projectId = val.id;
      this.object = object;
      sessionStorage.projectId = val.id;
      this.getList(object);
    },
    energyChange(val) {
      let list = this.ElOption_1.options_in || [];
      let findObj = list.find(item => item.energytype === val);
      const object = this.object;
      object.energyType = val;
      if (findObj) {
        object.energyType$text = findObj.name;
      }
      this.object = object;
      this.energyType = val;
      sessionStorage.energyType = val;
      this.getList(object);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    Entry_currentData_out(val) {},
    Entry_finishData_out(val) {},
    Entry_finishTrigger_out(val) {
      console.log(val);
      const object = this.object;
      this.showImplement = true;
      this.getList(object);
    },
    Entry_saveData_out(val) {},
    Implement_currentData_out(val) {},
    Implement_finishData_out(val) {},
    Implement_finishTrigger_out(val) {
      this.showImplement = false;
    },
    Implement_saveData_out(val) {},
    // 接口 获取节点树
    getTree() {
      const params = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [],
        treeReturnEnable: true
      };
      customApi.getNodeTree(params).then(res => {
        if (res.code == 0) {
          const data = [];
          const obj1 = {
            id: res.data[0].id,
            label: res.data[0].name,
            modelLabel: res.data[0].modelLabel,
            tree_id: res.data[0].modelLabel + "_" + res.data[0].id
          };
          data.push(obj1);
          this.data = data;
          const startTime = this.$moment().startOf("year").valueOf();
          const endTime = this.$moment()
            .add("year", 1)
            .startOf("year")
            .valueOf();
          const obj = {
            cycle: 17,
            endTime,
            energyType: 2,
            energyType$text: $T("电"),
            projectId: res.data[0].id,
            startTime
          };
          this.object = obj;
          this.getList(obj);
          this.projectEnergy(res.data[0].id);
          this.$nextTick(() => {
            this.$refs.tree.setCurrentKey(obj1.tree_id);
          });
        }
      });
    },
    getList(obj) {
      this.unitTransition = false;
      httping({
        url: "eem-service/v1/costcaculating/deviation",
        method: "POST",
        data: obj
      }).then(res => {
        if (res.code == 0) {
          const list = this._.get(res, "data.datas", []) || [];
          const budgetList = [];
          const count = list.pop();
          const source = [["product", $T("实际成本"), $T("预算成本")]];
          // 先判断是否需要进行单位转换
          list.find(item => {
            if (item.budgetValue && item.budgetValue * 1 > 10000) {
              this.unitTransition = true;
              return true;
            }
            if (item.costValue && item.costValue * 1 > 10000) {
              this.unitTransition = true;
              return true;
            }
          });

          list.map((item, index) => {
            if (
              this.unitTransition &&
              (item.budgetValue || item.budgetValue === 0)
            ) {
              item.budgetValueEdit = item.budgetValue;
              item.budgetValue = item.budgetValue / 10000;
            }
            if (
              this.unitTransition &&
              (item.costValue || item.costValue === 0)
            ) {
              item.costValueEdit = item.costValue;
              item.costValue = item.costValue / 10000;
            }
            const obj = [];
            const month = new Date(item.logtime * 1).getMonth() + 1;
            const year = new Date(item.logtime * 1).getFullYear();
            let time = year + "年" + month + "月";
            if (this.language) {
              time = year + "-" + month;
            }
            item.logtime1 = time;
            let budgetValueEdit;
            if (item.budgetValue != null)
              item.budgetValue = item.budgetValue.toFixed(2);
            if (item.costValue != null)
              item.costValue = item.costValue.toFixed(2);
            if (item.budgetValueEdit || item.budgetValueEdit === 0) {
              budgetValueEdit = item.budgetValueEdit.toFixed(2);
            } else {
              budgetValueEdit = 0;
            }
            if (item.deviationRate != null)
              item.deviationRate = (item.deviationRate * 100).toFixed(2);
            obj[0] = month + "月";
            if (this.language) {
              obj[0] = month;
            }
            obj[1] = item.costValue;
            obj[2] = item.budgetValue;
            obj[3] = item.deviationRate;
            budgetList.push({
              time,
              budgetValueEdit,
              logtime: item.logtime
            });
            source.push(obj);
          });
          count.logtime1 = $T("合计");
          if (
            this.unitTransition &&
            (count.budgetValue || count.budgetValue === 0)
          ) {
            count.budgetValueEdit = count.budgetValue;
            count.budgetValue = count.budgetValue / 10000;
          }
          if (
            this.unitTransition &&
            (count.costValue || count.costValue === 0)
          ) {
            count.costValueEdit = count.costValue;
            count.costValue = count.costValue / 10000;
          }
          if (count.budgetValue != null)
            count.budgetValue = count.budgetValue.toFixed(2);
          if (count.costValue != null)
            count.costValue = count.costValue.toFixed(2);
          if (count.deviationRate != null)
            count.deviationRate = (count.deviationRate * 100).toFixed(2);
          list.push(count);
          this.list = list;
          this.source = source;
          this.budgetList = budgetList;
        }
      });
    },
    // 接口 查询项目的能源类型
    projectEnergy(projectId) {
      httping({
        url: "eem-service/v1/project/projectEnergy?projectId=" + projectId,
        method: "GET"
      }).then(res => {
        if (res.code == 0) {
          if (res.data && res.data.length != 0) {
            const list = res.data;
            list.map(item => {
              item.text = item.name;
              item.id = item.energytype;
            });
            this.ElOption_1.options_in = list.filter(
              i => ![18, 22].includes(i.id)
            );
            this.ElSelect_1.value = list[0].energytype;
            sessionStorage.energyType = list[0].energytype;
          }
        }
      });
    }
  },
  activated: function () {
    this.getTree();
  },
  destroyed() {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.logo {
  width: 34px;
  height: 34px;
  margin: 0 10px 12px 14px;
}
.change {
  width: 40px;
  height: 40px;
}
.silde {
  margin: 10% 0;
  height: 45% !important;
}
</style>
