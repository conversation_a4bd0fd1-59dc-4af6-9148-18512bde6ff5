<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="min"
    >
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <CetForm
        class="eem-cont-c1"
        :data.sync="CetForm_pagedialog.data"
        v-bind="CetForm_pagedialog"
        v-on="CetForm_pagedialog.event"
        ref="dialogForm"
      >
        <el-form-item :label="$T('参数名')" prop="inspectionparameterid">
          <ElSelect
            v-model="CetForm_pagedialog.data.inspectionparameterid"
            v-bind="ElSelect_param"
            v-on="ElSelect_param.event"
          >
            <ElOption
              v-for="item in ElOption_param.options_in"
              :key="item[ElOption_param.key]"
              :label="item[ElOption_param.label]"
              :value="item[ElOption_param.value]"
              :disabled="item[ElOption_param.disabled]"
            ></ElOption>
          </ElSelect>
        </el-form-item>
        <el-form-item :label="$T('上限')" prop="max">
          <ElInputNumber
            v-model="CetForm_pagedialog.data.max"
            v-bind="ElInputNumber_upperlimit"
          ></ElInputNumber>
        </el-form-item>
        <el-form-item :label="$T('下限')" prop="min">
          <ElInputNumber
            v-model="CetForm_pagedialog.data.min"
            v-bind="ElInputNumber_lowerlimit"
          ></ElInputNumber>
        </el-form-item>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
export default {
  name: "addOreditAnalogDialog",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    isAdd: {
      type: Boolean
    },
    analogList: {
      type: Array
    },
    analogTableList: {
      type: Array
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: "",
        showClose: true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "component", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        labelPosition: "top",
        rules: {
          inspectionparameterid: [
            {
              required: true,
              message: $T("请选择参数名"),
              trigger: ["blur", "change"]
            }
          ],
          max: [
            {
              required: true,
              message: $T("上限值不能为空"),
              trigger: "blur"
            }
          ],
          min: [
            {
              required: true,
              message: $T("下限值不能为空"),
              trigger: "blur"
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_pagedialog_currentData_out,
          saveData_out: this.CetForm_pagedialog_saveData_out,
          finishData_out: this.CetForm_pagedialog_finishData_out,
          finishTrigger_out: this.CetForm_pagedialog_finishTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // param组件
      ElSelect_param: {
        value: "",
        style: {
          width: "100%"
        },
        filterable: true,
        event: {
          change: this.ElSelect_param_change_out
        }
      },
      ElOption_param: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // upperlimit组件
      ElInputNumber_upperlimit: {
        value: "",
        style: {
          width: "100%"
        },
        placeholder: $T("请输入内容"),
        precision: 2,
        controls: false,
        event: {
          change: this.ElInputNumber_upperlimit_change_out
        }
      },
      // lowerlimit组件
      ElInputNumber_lowerlimit: {
        value: "",
        style: {
          width: "100%"
        },
        placeholder: $T("请输入内容"),
        precision: 2,
        controls: false,
        event: {
          change: this.ElInputNumber_lowerlimit_change_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      // 模拟量下拉框中，表格已经添加过的数据置为禁用
      const ids = [];
      this.analogTableList.forEach(item => {
        ids.push(item.inspectionparameterid);
      });
      this.analogList.forEach(item => {
        if (ids.includes(item.id)) {
          this.$set(item, "disabled", true);
        } else {
          this.$set(item, "disabled", false);
        }
      });
      this.ElOption_param.options_in = this.analogList;

      if (this.isAdd) {
        this.CetDialog_pagedialog.title = $T("新增模拟量");
        this.CetForm_pagedialog.data = {};
        this.ElSelect_param.disabled = false;
        this.ElOption_param.key = "id";
        this.CetForm_pagedialog.resetTrigger_in = new Date().getTime();
      } else {
        this.CetDialog_pagedialog.title = $T("编辑模拟量");
        this.ElSelect_param.disabled = true;
        this.ElOption_param.key = "inspectionparameterid";
        // this.CetForm_pagedialog.data = this._.cloneDeep(this.inputData_in);
      }
      this.CetForm_pagedialog.resetTrigger_in = new Date().getTime();
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {
      this.CetForm_pagedialog.queryId_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      this.CetForm_pagedialog.data = this._.cloneDeep(val);
    }
  },
  methods: {
    CetForm_pagedialog_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_saveData_out(val) {
      this.$emit("saveData_out", this._.cloneDeep(val));
      this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
    },
    CetForm_pagedialog_finishData_out(val) {
      this.$emit("finishData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishTrigger_out(val) {
      this.$emit("finishTrigger_out", val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetForm_pagedialog.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      if (
        this.CetForm_pagedialog.data.max <= this.CetForm_pagedialog.data.min
      ) {
        return this.$message.warning($T("上限值要高于下限值!"));
      }
      this.CetForm_pagedialog.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    no() {},
    // param输出,方法名要带_out后缀
    ElSelect_param_change_out(val) {},
    // upperlimit输出,方法名要带_out后缀
    ElInputNumber_upperlimit_change_out(val) {},
    // lowerlimit输出,方法名要带_out后缀
    ElInputNumber_lowerlimit_change_out(val) {}
  },
  created: function () {}
};
</script>
<style lang="scss" scoped></style>
