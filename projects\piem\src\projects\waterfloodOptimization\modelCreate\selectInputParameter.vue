<template>
  <div class="w-full h-full">
    <div class="text-T2 mb-[8px]">选择输入参数</div>
    <div class="w-full h-[calc(100%-32px-16px)] grid grid-cols-3 gap-[16px]">
      <div
        v-for="(item, index) in paramsTree"
        :key="index"
        class="box-border border rounded border-B2"
      >
        <div
          class="px-[16px] box-border flex items-center justify-between h-[32px] bg-BG2 border-b border-B2"
        >
          <div class="font-normal text-T2">{{ item.name }}</div>
          <div class="text-[12px] text-T4">
            {{ item.checkList.length }}/{{ item.dataList.length }}
          </div>
        </div>
        <div class="w-full h-[358px] p-[16px] pb-0 box-border">
          <el-checkbox-group v-model="item.checkList" class="flex flex-col">
            <el-checkbox
              class="mb-[16px]"
              v-for="it in item.dataList"
              :key="it.id"
              :label="it.id"
              :disabled="item.isDisabled"
            >
              {{ it.name }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "selectInputParameter",
  props: {
    inputParameter: {
      type: Array,
      default: () => []
    }
  },
  components: {},
  data() {
    return {
      paramsTree: [
        {
          name: "选择注水站管阀参数",
          checkList: [],
          dataList: [
            { id: 1, name: "出站汇管压力" },
            { id: 2, name: "出站汇管累计流量" }
          ]
        },
        {
          name: "选择注水泵参数",
          checkList: [],
          dataList: [
            { id: 3, name: "注水泵正向有功电能" },
            { id: 4, name: "注水泵额定功率" }
          ]
        },
        {
          name: "选择注水井参数",
          checkList: [],
          isDisabled: true,
          dataList: [
            { id: 5, name: "日额定配注量" },
            { id: 6, name: "累计流量" },
            { id: 7, name: "瞬时流量" },
            { id: 8, name: "干管压力" },
            { id: 9, name: "油管压力" },
            { id: 10, name: "阀开度" }
          ]
        }
      ]
    };
  },
  watch: {
    inputParameter: {
      immediate: true,
      deep: true,
      handler(val) {
        this.$nextTick(() => {
          this.paramsTree?.forEach(i => {
            i.checkList = val?.filter(it =>
              i.dataList?.map(i => i.id)?.includes(it)
            );
          });
        });
      }
    },
    paramsTree: {
      deep: true,
      handler(val) {
        let data = val?.map(i => i.checkList)?.flat();
        this.$emit("inputParameter", data);
      }
    }
  },
  methods: {},
  created() {},
  mounted() {}
};
</script>

<style lang="scss" scoped>
:deep(.el-tree) {
  overflow: auto;
}

.bx {
  @apply border border-B2 border-t-0 p-[16px] box-border rounded-b;
}
</style>
