<template>
  <div style="height: 200px">
    <div class="card-head-title">
      <span>节能减排（月）</span>
    </div>
    <div style="height: calc(100% - 30px)">
      <CetTable
        class="custom-table-style"
        :data.sync="CetTable_1.data"
        :dynamicInput.sync="CetTable_1.dynamicInput"
        v-bind="CetTable_1"
        v-on="CetTable_1.event"
        row-key="id"
      >
        <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
        <ElTableColumn v-bind="ElTableColumn_energyType_2"></ElTableColumn>
        <ElTableColumn v-bind="ElTableColumn_energyType_3"></ElTableColumn>
      </CetTable>
    </div>
  </div>
</template>
<script>
import common from "eem-utils/common";
import { httping } from "@omega/http";
export default {
  name: "ProjectTab",
  props: {
    queryTrigger_in: {
      type: Number
    }
  },
  components: {},

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data() {
    return {
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        // defaultSort: null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      // name组件
      ElTableColumn_name: {
        //type: "",      // selection 勾选 index 序号
        prop: "name", // 支持path a[0].b
        label: "", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        width: 70 //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_energyType_2: {
        //type: "",      // selection 勾选 index 序号
        prop: "energyType_2", // 支持path a[0].b
        label: "电", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        minWidth: 120 //该宽度会自适应
        // width: 70 //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_energyType_3: {
        //type: "",      // selection 勾选 index 序号
        prop: "energyType_3", // 支持path a[0].b
        label: "水", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        minWidth: 120 //该宽度会自适应
        // width: 70 //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      }
    };
  },
  watch: {
    //查询条件触发
    queryTrigger_in() {
      this.getConserveEnergyData();
    }
  },

  methods: {
    CetTable_1_record_out(val) {},
    CetTable_1_outputData_out(val) {},
    //统计节能减排情况
    getConserveEnergyData() {
      var _this = this; //身份验证
      var queryBody = [2, 3]; //2:用电，3:用水

      var queryOption = {
        url: `/eem-service/v1/overview/energyStatistics`,
        method: "POST",
        data: queryBody
      };

      httping(queryOption).then(function (response) {
        if (response.code === 0) {
          //判断是否需要展示合计行，如果需要的话将合计行添加到数据的最后
          // console.log(response.data);
          var data = _this._.get(response, ["data"], []);
          _this.filConserveEnergyData(data);
        }
      });
    },
    filConserveEnergyData(data) {
      data = data || [];
      var energy_2 = {},
        energy_3 = {};
      data.forEach(item => {
        if (item.energyType === 2) {
          energy_2 = item;
        } else if (item.energyType === 3) {
          energy_3 = item;
        }
      });
      var tableData = [];
      for (var i = 0; i < 4; i++) {
        if (i === 0) {
          let obj = {};
          obj["name"] = "用能量";
          obj["energyType_2"] = this.setCompany(energy_2.consumption, 1);
          obj["energyType_3"] = this.setCompany(energy_3.consumption, 2);
          tableData.push(obj);
        } else if (i === 1) {
          let obj = {};
          obj["name"] = "节能量";
          obj["energyType_2"] = this.setCompany(energy_2.savedConsumption, 1);
          obj["energyType_3"] = this.setCompany(energy_3.savedConsumption, 2);
          tableData.push(obj);
        } else if (i === 2) {
          let obj = {};
          obj["name"] = "节费用";
          obj["energyType_2"] = this.setCompany(energy_2.savedMoney, 3);
          obj["energyType_3"] = this.setCompany(energy_3.savedMoney, 3);
          tableData.push(obj);
        } else if (i === 3) {
          let obj = {};
          obj["name"] = "碳排放";
          obj["energyType_2"] = this.setCompany(energy_2.carbonEmission, 4);
          obj["energyType_3"] = this.setCompany(energy_3.carbonEmission, 4);
          tableData.push(obj);
        }
      }
      this.CetTable_1.data = tableData;
    },
    setCompany(val, key) {
      val = val || 0;
      if (typeof val == "string" || [NaN, undefined, null].includes(val)) {
        if (key === 1) {
          return "-- Wh";
        } else if (key === 2) {
          return "-- t";
        } else if (key === 3) {
          return "-- 元";
        } else if (key === 4) {
          return "-- t";
        }
      }
      if (key === 1) {
        if (val < -1000000) {
          return common.formatNumberWithPrecision(val / 1000000, 2) + " GWh";
        } else if (val < -1000) {
          return common.formatNumberWithPrecision(val / 1000, 2) + " MWh";
        }
        if (val < 1000) {
          return common.formatNumberWithPrecision(val, 2) + " kWh";
        } else if (val < 1000000) {
          return common.formatNumberWithPrecision(val / 1000, 2) + " MWh";
        } else {
          return common.formatNumberWithPrecision(val / 1000000, 2) + " GWh";
        }
      }
      if (key === 2) {
        if (val < -1000000000) {
          return common.formatNumberWithPrecision(val / 1000000000, 2) + " Gt";
        } else if (val < -1000000) {
          return common.formatNumberWithPrecision(val / 1000000, 2) + " Mt";
        } else if (val < -1000) {
          return common.formatNumberWithPrecision(val / 1000, 2) + " kt";
        }
        if (val < 1000) {
          return common.formatNumberWithPrecision(val, 2) + " t";
        } else if (val < 1000000) {
          return common.formatNumberWithPrecision(val / 1000, 2) + " kt";
        } else if (val < 1000000000) {
          return common.formatNumberWithPrecision(val / 1000000, 2) + " Mt";
        } else {
          return common.formatNumberWithPrecision(val / 1000000000, 2) + " Gt";
        }
      }
      if (key === 3) {
        if (val < -100000000) {
          return common.formatNumberWithPrecision(val / 100000000, 2) + " 亿元";
        } else if (val < -10000) {
          return common.formatNumberWithPrecision(val / 10000, 2) + " 万元";
        }
        if (val < 10000) {
          return common.formatNumberWithPrecision(val, 2) + "元";
        } else if (val < 100000000) {
          return common.formatNumberWithPrecision(val / 10000, 2) + "万元";
        } else {
          return common.formatNumberWithPrecision(val / 100000000, 2) + "亿元";
        }
      }
      if (key === 4) {
        if (val < -1000000000) {
          return common.formatNumberWithPrecision(val / 1000000000, 2) + " Gt";
        } else if (val < -1000000) {
          return common.formatNumberWithPrecision(val / 1000000, 2) + " Mt";
        } else if (val < -1000) {
          return common.formatNumberWithPrecision(val / 1000, 2) + " kt";
        }
        if (val < 1000) {
          return common.formatNumberWithPrecision(val, 2) + " t";
        } else if (val < 1000000) {
          return common.formatNumberWithPrecision(val / 1000, 2) + " kt";
        } else if (val < 1000000000) {
          return common.formatNumberWithPrecision(val / 1000000, 2) + " Mt";
        } else {
          return common.formatNumberWithPrecision(val / 1000000000, 2) + " Gt";
        }
      }
    }
  },

  created: function () {},
  mounted: function () {
    // this.getConserveEnergyData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.card-header {
  height: 34px;
  padding: 8px 0 0 10px;
  background: #fff;
  clear: both;
  text-align: center;
}

.card-title {
  display: inline-block;
  height: 34px;
  font-weight: bold;
  font-size: 13px;
  // color: #7a7a7a;
}
.card-head-title {
  height: 30px;
  line-height: 30px;
  text-align: center;
  span {
    font-size: 18px;
    color: rgba(128, 255, 255, 1);
  }
}
</style>
<style lang="scss">
.custom-table-style {
  .el-table {
    background-color: rgba(15, 26, 71, 0.5) !important;
  }
  .el-table--group,
  .el-table--border {
    border: 1px solid #0358c2;
  }
  .el-table th,
  .el-table tr {
    background-color: rgba(15, 26, 71, 0.5);
  }
  .el-table td,
  .el-table th.is-leaf {
    border-bottom: 1px solid #0358c2;
  }
  .el-table--border td,
  .el-table--border th,
  .el-table__body-wrapper
    .el-table--border.is-scrolling-left
    ~ .el-table__fixed {
    border-right: 1px solid #0358c2;
  }
  .el-table td,
  .el-table th.is-leaf {
    border-bottom: 1px solid #0358c2;
  }
  .el-table--border .el-table__body tr.current-row > td {
    border-right: 1px solid #0358c2 !important;
    background-color: rgba(15, 26, 71, 0.5) !important;
  }
  .el-table th,
  .el-table tr {
    background-color: rgba(15, 26, 71, 0.5);
  }
  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: rgba(15, 26, 71, 0.5) !important;
  }
  .el-table__body tr.current-row > td {
    background-color: rgba(15, 26, 71, 0.5) !important;
  }
  .el-table--group::after,
  .el-table--border::after,
  .el-table::before {
    background-color: rgba(15, 26, 71, 0.5);
  }
  div {
    color: #fff;
  }
  span {
    color: #fff;
  }
}
</style>
