<template>
  <div class="flex flex-col items-center justify-center w-full h-full">
    <CetChart v-bind="CetChart_bar" v-if="isData"></CetChart>
    <div class="flex flex-col items-center empty" v-else>
      <img v-if="isLight" src="../../../resources/assets/light.png" alt="" />
      <img v-else src="../../../resources/assets/dark.png" alt="" />
      <div class="text-[16px] text-T3 mt-[16px]">暂无数据</div>
    </div>
  </div>
</template>

<script>
import omegaTheme from "@omega/theme";
import customApi from "@/api/custom.js";
import common from "eem-utils/common.js";
export default {
  name: "injectionWell",
  props: { selectNode: Object, paramsTime: Array, activeName: String },
  components: {},
  data() {
    return {
      CetChart_bar: {
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "axis"
          },

          grid: {
            top: "10%",
            left: "2%",
            right: "2%",
            bottom: "1%",
            containLabel: true
          },
          xAxis: {
            type: "category",
            axisTick: { show: false },
            data: [
              "英2-4-#井累计流量",
              "Tue",
              "Wed",
              "Thu",
              "Fri",
              "Sat",
              "Sun"
            ],
            axisLine: {
              lineStyle: {
                type: "dashed"
              }
            },
            axisLabel: {
              show: true,
              interval: 0,
              rotate: 45,
              formatter: function (value) {
                if (value.length > 11) {
                  return `${value.slice(0, 11)}...`;
                }
                return value;
              }
            }
          },
          yAxis: {
            type: "value",
            name: "相关性系数",
            nameTextStyle: {
              padding: [0, 0, 0, 30]
            },
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: {
              lineStyle: {
                type: "dashed"
              }
            }
          },
          series: [
            {
              type: "bar",
              barWidth: 15,
              smooth: true,
              data: []
            }
          ]
        }
      }
    };
  },
  computed: {
    isLight() {
      return omegaTheme.theme === "light";
    },
    isData() {
      return this.CetChart_bar.options.series[0].data?.length > 0;
    }
  },
  watch: {
    selectNode: {
      deep: true,
      handler(val) {
        if (!_.isObject(val)) return;
        this.getData();
      }
    },
    paramsTime: {
      deep: true,
      handler() {
        this.getData();
      }
    }
  },
  methods: {
    async getData() {
      const params = {
        endTime: this.paramsTime[1],
        objectId: this.selectNode?.id,
        objectLabel: this.selectNode?.modelLabel,
        startTime: this.paramsTime[0]
      };
      this.CetChart_bar.options.xAxis.data = [];
      this.CetChart_bar.options.series[0].data = [];
      if (params.objectLabel !== "waterinjectionstation") return;
      const res = await customApi.waterWellParamcorrelation(params);

      this.CetChart_bar.options.series[0].data =
        res?.data?.map(i => {
          let isLight = omegaTheme.theme === "light";
          return {
            value: common.formatNumberWithPrecision(i.correlation, 2),
            itemStyle: {
              color:
                i.correlation < 0
                  ? isLight
                    ? "#f95e5a"
                    : "#ff3f3f"
                  : isLight
                  ? "#29b061"
                  : "#0d86ff"
            }
          };
        }) || [];
      this.CetChart_bar.options.xAxis.data =
        res?.data?.map(i => i.waterInjectionWellName) || [];
    }
  },
  created() {},
  mounted() {
    if (this.activeName === "injectionWell") this.getData();
  }
};
</script>

<style lang="scss" scoped>
.empty {
  img {
    width: 220px;
    height: 135px;
  }
}
</style>
