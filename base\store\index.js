import { getJsonFile } from "@omega/http/getJson.js";
import { httping, HttpBase } from "@omega/http";
import cycle from "./modules/cycle";
import customApi from "@/api/custom";
import moment from "moment";

const modules = {
  cycle
};

const state = {
  appTitle: "",
  token: "",
  currentModule: "pf", //pf代表平台级，fw代表项目级
  userInfo: {
    userId: 0,
    userName: "",
    roleId: 0,
    roleName: ""
  },
  TreeRefreshTrigger: "",
  initialInfo: {
    menuList: [], // 项目页面
    functionList: [],
    projectNodeList: []
  },
  // 平台、项目所有页面
  navmenu: {
    pfNavmenu: [],
    pjNavmenu: []
  },
  frameUI: {
    fullScreen: false
  },
  // 存储枚举类
  enumerations: {},
  frameCfg: {}, //框架配置
  systemCfg: {}, //自定义配置文件,
  projectId: null, // 选择的项目id
  projectTenantId: 0, //当前项目内部租户id
  tenantId: 0,
  projectInfo: null,
  realtime: {
    allGraphNode: []
  },
  costanalysis: {
    shareQuery: {}
  },
  currentMeasuringNode: {},
  effiencyTable: [],
  isSidebarCollapse: false,
  resetFrameNavBars: null, // 用于平台与项目间的切换清除面包屑
  unitInfo: {},
  authStringent: null,
  pecCoreTypes: [*********, *********, *********],
  mReportTypes: [*********, *********],
  pReportTypes: [*********, *********],
  graphTypes: [*********, *********],
  onlyReportTypes: [1, 2, 3], //onlyReport报表类型
  powerUserId: null, //当前项目内部批发用户id
  sellCompanyId: null //当前项目内部售电公司id
};

const mutations = {
  setNavmenu(state, navmenu) {
    state.navmenu = navmenu;
  },
  setAppTitle(state, appTitle) {
    state.appTitle = appTitle;
  },

  setUnitInfo(state, unitInfo) {
    state.unitInfo = unitInfo;
  },
  setProjectTenantId(state, projectTenantId) {
    if (!projectTenantId) {
      console.log("项目tenantId租户ID不存在");
      projectTenantId = 1;
    }
    state.projectTenantId = projectTenantId;
  },
  setProjectInfo(state, projectInfo) {
    if (projectInfo) {
      projectInfo.belongTenantId = projectInfo.tenantid;
    }
    state.projectInfo = projectInfo;
  },
  setTenantId(state, tenantId) {
    state.tenantId = tenantId;
  },
  setToken(state, token) {
    state.token = token;
  },
  setCurrentModule(state, currentModule) {
    state.currentModule = currentModule;
  },
  setUserInfo(state, userInfo) {
    userInfo.userName = userInfo.name;
    state.userInfo = userInfo;
  },
  setFullScreen(state, fullScreen) {
    state.frameUI.fullScreen = fullScreen;
  },
  setFrameCfg(state, frameCfg) {
    state.frameCfg = frameCfg;
  },
  setMenuList(state, menuList) {
    state.initialInfo.menuList = menuList;
  },
  setFunctionList(state, functionList) {
    state.initialInfo.functionList = functionList;
  },
  setProjectNodeList(state, projectNodeList) {
    state.initialInfo.projectNodeList = projectNodeList;
  },
  setSystemCfg(state, systemCfg) {
    state.systemCfg = systemCfg;
  },
  setEnumerations(state, val) {
    state.enumerations = val;
  },
  TreeRefreshTrigger(state, TreeRefreshTrigger) {
    state.TreeRefreshTrigger = TreeRefreshTrigger;
  },
  setProjectId(state, val) {
    state.projectId = val;
    window.sessionStorage.setItem("projectId", val);
    window.localStorage.setItem("projectId", val);
    HttpBase.setHeadersOperate({
      projectId: val
    });
  },
  setAllGraphNode(state, val) {
    state.realtime.allGraphNode = val;
  },
  setShareQuery(state, val) {
    state.costanalysis.shareQuery = val;
  },
  setCurrentMeasuringNode(state, val) {
    state.currentMeasuringNode = val;
  },
  setEffiencyTable(state, val) {
    state.effiencyTable = val;
  },
  setIsSidebarCollapse(state, val) {
    state.isSidebarCollapse = val;
  },
  setAuthStringent(state, val) {
    state.authStringent = val;
  },
  setPowerUserId(state, val) {
    state.powerUserId = val;
  },
  setSellCompanyId(state, val) {
    state.sellCompanyId = val;
  }
};

const actions = {
  async setAuthStringent(store) {
    // store.commit("setAuthStringent", true);
    // return;
    //判断是否存在超级管理员权限模式
    let res = await customApi.cloudRootExist();
    if (res.code === 0) {
      store.commit("setAuthStringent", res.data);
    }
  },
  async initUnitInfo(store) {
    customApi.queryEnergytype().then(res => {
      if (res.code === 0 && res.data) {
        let typeToData = {};
        res.data.map(item => {
          typeToData[item.energyType] = item;
        });
        store.commit("setUnitInfo", typeToData);
      }
    });
  },
  // 兼容原有项目，后期配置转至src/config
  async setSystemCfgJson({ commit }) {
    let SystemCfg;
    if (window.SystemCfg) {
      SystemCfg = window.SystemCfg;
    } else {
      SystemCfg = await getJsonFile(
        `/static/SystemCfg.json?t=${new Date().getTime()}`
      );
    }
    if (SystemCfg.internationalization) {
      // 获取当前浏览器语言当做系统默认语言
      let language = window.localStorage.getItem("omega_language");
      if (!language) {
        const browserLanguage = window.navigator && window.navigator.language;
        if (browserLanguage === "zh-CN") {
          window.localStorage.setItem("omega_language", "zh_cn");
        } else {
          window.localStorage.setItem("omega_language", "en");
        }
      }
    } else {
      window.localStorage.setItem("omega_language", "zh_cn");
    }
    const currentLanguage = window.localStorage.getItem("omega_language");
    if (currentLanguage === "en") {
      HttpBase.setHeadersOperate({
        "Accept-Language": "en-US,en;q=0.9"
      });
      commit("setAppTitle", SystemCfg.enProjectName);
      moment.locale("en-us");
    } else {
      HttpBase.setHeadersOperate({
        "Accept-Language": "zh-CN,zh;q=0.9"
      });
      commit("setAppTitle", SystemCfg.projectName);
      moment.locale("zh-cn");
    }
    if (SystemCfg[SystemCfg.key]) {
      Object.assign(SystemCfg, SystemCfg[SystemCfg.key]);
    }
    commit("setSystemCfg", SystemCfg);
    if (SystemCfg.powerUserId) {
      commit("setPowerUserId", SystemCfg.powerUserId);
    }
    if (SystemCfg.sellCompanyId) {
      commit("setSellCompanyId", SystemCfg.sellCompanyId);
    }
  },
  // 兼容原有项目，后期配置转至src/config
  initUserInfo({ commit }, { token, user }) {
    user.userId = user.id;
    user.roleInfo = user.roles[0];

    commit("setToken", token);
    commit("setUserInfo", user);
    commit("setTenantId", user.tenantId);
    var customConfig = JSON.parse(user.customConfig || "{}");
    let usertype = customConfig.usertype;
    if (!usertype) {
      usertype = customConfig.userType;
    }
    //判断是否是在平台项目环境下运行
    if (this.state.systemCfg.isPlatformModel) {
      if (customConfig.usertype === 4 || customConfig.usertype === 3) {
        commit("setCurrentModule", "fw");
      } else {
        commit("setCurrentModule", "pf");
      }
    } else {
      commit("setCurrentModule", "fw");
    }
  },
  // 兼容现项目
  initProject({ commit }, { projectId, tenantid, projectInfo }) {
    commit("setProjectId", +projectId);
    commit("setProjectTenantId", +tenantid);
    commit("setProjectInfo", projectInfo);
  },
  async getconvertTime({ commit }, { projectId }) {
    // 获取当前时刻所处非自然周期起始时间戳
    let response = await httping({
      url: `/eem-service/v1/costcaculating/convertTime`,
      method: "POST",
      hideNotice: true,
      data: {
        cycles: [12, 13, 14, 17],
        projectId: projectId,
        time: new Date().getTime()
      }
    });
    const cycleConfig = {
      startDayTime: moment().startOf("day").valueOf(),
      startWeekTime: moment().startOf("week").valueOf(),
      startMonthTime: moment().startOf("month").valueOf(),
      startYearTime: moment().startOf("year").valueOf()
    };
    var dayInfo, weekInfo, monthInfo, yearInfo;
    if (response.code === 0 && response.data && response.data.length > 0) {
      dayInfo = response.data.filter(item => item.cycle === 12)[0];
      weekInfo = response.data.filter(item => item.cycle === 13)[0];
      monthInfo = response.data.filter(item => item.cycle === 14)[0];
      yearInfo = response.data.filter(item => item.cycle === 17)[0];
      if (dayInfo) {
        cycleConfig.startDayTime = dayInfo.startTime;
      }
      if (weekInfo) {
        cycleConfig.startWeekTime = weekInfo.startTime;
      }
      if (monthInfo) {
        cycleConfig.startMonthTime = monthInfo.startTime;
      }
      if (yearInfo) {
        cycleConfig.startYearTime = yearInfo.startTime;
      }
    }
    commit("cycle/setStartDayTime", cycleConfig.startDayTime);
    commit("cycle/setStartWeekTime", cycleConfig.startWeekTime);
    commit("cycle/setStartMonthTime", cycleConfig.startMonthTime);
    commit("cycle/setStartYearTime", cycleConfig.startYearTime);
  }
};

export { modules, state, mutations, actions };
