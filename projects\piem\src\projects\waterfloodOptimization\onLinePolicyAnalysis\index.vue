<template>
  <div class="flex justify-between w-full h-full">
    <div class="w-[320px] h-full p-[16px] box-border border-r border-B2">
      <customTree
        :dataMode="CetTree_1.dataMode"
        :inputData_in="CetTree_1.inputData_in"
        :selectNode.sync="CetTree_1.selectNode"
        :searchText_in="CetTree_1.searchText_in"
        :filterNodes_in="CetTree_1.filterNodes_in"
        :checkedArray.sync="CetTree_1.checkedArray"
        v-bind="CetTree_1.config"
      />
    </div>

    <div
      class="w-[calc(100%-320px)] h-full flex"
      v-if="isWaterinjectionstation"
    >
      <div class="w-[calc(100%-517px)] h-full pxy flex flex-col">
        <div class="title">在线模型单耗预测值拟合曲线</div>
        <div class="flex-1 mt-[16px] flex flex-col">
          <LineChart
            class="w-full !h-[calc(100%-441px-16px)]"
            :selectNode="CetTree_1.selectNode"
          ></LineChart>
          <div class="w-full h-[441px] mt-[16px]">
            <div class="title">策略分析列表</div>

            <div class="mt-[16px] w-full h-[calc(100%-21px-16px)]">
              <PolicyAnalysislist
                :selectNode="CetTree_1.selectNode"
              ></PolicyAnalysislist>
            </div>
          </div>
        </div>
      </div>

      <div class="w-[517px] h-full pxy border-l border-B2">
        <div class="title">注水概况</div>
        <div class="h-[calc(100%-21px-16px)] w-full">
          <WaterfloodProfile
            :selectNode="CetTree_1.selectNode"
          ></WaterfloodProfile>
        </div>
      </div>
    </div>

    <div
      class="w-[calc(100%-320px)] h-full flex flex-col items-center justify-center text-[24px] font-bold"
      v-else
    >
      <img v-if="isLight" src="../../../resources/assets/light.png" alt="" />
      <img v-else src="../../../resources/assets/dark.png" alt="" />
      <div class="text-[16px] text-T3 mt-[16px]">暂无数据</div>
    </div>
  </div>
</template>

<script>
import omegaTheme from "@omega/theme";
import customTree from "@/components/customTree/index.vue";
import customApi from "@/api/custom.js";
import LineChart from "./lineChart.vue";
import PolicyAnalysislist from "./policyAnalysislist.vue";
import WaterfloodProfile from "./waterfloodProfile.vue";
import { flatTreeData } from "../component/tableCol.jsx";
export default {
  name: "onLinePolicyAnalysis",
  props: {},
  components: { customTree, LineChart, PolicyAnalysislist, WaterfloodProfile },
  data() {
    return {
      CetTree_1: {
        dataMode: "static",
        inputData_in: [],
        searchText_in: "",
        filterNodes_in: "",
        selectNode: null,
        checkedArray: [],
        config: {
          highlightCurrent: true,
          ShowRootNode: false,
          expandMode: true,
          screen: true,
          initialFlag: false,
          showCheckbox: false,
          checkStrictly: false,
          filterModelLabel: "waterinjectionstation"
        }
      },
      activeName: "injectionStation"
    };
  },
  computed: {
    isWaterinjectionstation() {
      return this.CetTree_1.selectNode?.modelLabel == "waterinjectionstation";
    },
    isLight() {
      return omegaTheme.theme === "light";
    }
  },
  watch: {},
  methods: {
    // 获取节点树
    async getTreeData() {
      const queryData = {
        rootID: 0,
        rootLabel: "oilcompany",
        subLayerConditions: [
          { modelLabel: "oilproductionplant" },
          { modelLabel: "operationarea" },
          { modelLabel: "oilproductioncrew" },
          { modelLabel: "waterinjectionstation" }
        ],
        treeReturnEnable: true
      };

      const res = await customApi.getNodeTree(queryData);
      this.CetTree_1.inputData_in = res?.data || [];

      if (!_.isEmpty(res?.data)) {
        const firstOilwell = _.find(flatTreeData(res?.data), [
          "modelLabel",
          "waterinjectionstation"
        ]);
        this.CetTree_1.selectNode = firstOilwell || {};
      }
    }
  },
  created() {},
  mounted() {
    this.getTreeData();
  }
};
</script>

<style lang="scss" scoped>
.pxy {
  @apply px-[16px] py-[20px] box-border;
  .title {
    @apply w-full text-T2 font-semibold text-[16px];
  }
}
</style>
