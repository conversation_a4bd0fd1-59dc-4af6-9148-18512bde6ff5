<template>
  <omega-dialog
    class="CetDialog"
    width="500px"
    :has-full-screen="false"
    :on-before-confirm="onBeforeConfirm"
  >
    <template #title>
      <div class="text-center">
        <omega-icon
          class="layout-user-reset-icon I5"
          icon-class="layout_unlock"
        />
      </div>
    </template>

    <el-form
      class="eem-cont-c1 fullfilled elForm"
      ref="form"
      :model="form"
      label-width="110px"
      :rules="rules"
    >
      <el-form-item
        class="custom-form-item gray-label"
        :label="$T('原密码')"
        prop="oldPassword"
      >
        <el-input
          size="small"
          clearable
          type="password"
          maxlength="32"
          :placeholder="$T('请输入原密码（当前账号密码）')"
          v-model.trim="form.oldPassword"
        />
      </el-form-item>
      <el-form-item
        class="custom-form-item gray-label"
        label="新密码"
        prop="newPassword"
      >
        <el-input
          clearable
          size="small"
          type="password"
          maxlength="18"
          :placeholder="$T('请输入新的密码')"
          v-model.trim="form.newPassword"
        />
      </el-form-item>
      <el-form-item
        class="custom-form-item gray-label"
        label="新密码确认"
        prop="_checkPassword"
      >
        <el-input
          clearable
          size="small"
          type="password"
          maxlength="18"
          :placeholder="$T('请输入确认密码')"
          v-model.trim="form._checkPassword"
        />
      </el-form-item>
    </el-form>
  </omega-dialog>
</template>
<script>
import common from "eem-utils/common";
import customApi from "@/api/custom";
import omegaAuth from "@omega/auth";

export default {
  name: "ResetUserPassword",
  data() {
    return {
      form: {
        oldPassword: "",
        newPassword: "",
        _checkPassword: ""
      },
      rules: {
        oldPassword: [
          { required: true, message: $T("原密码不能为空"), trigger: "blur" }
        ],
        newPassword: [
          common.check_strongPassword,
          {
            required: true,
            type: "string",
            trigger: "blur",
            validator: (rule, value, callback) => {
              if (this._.isNil(value) || value === "") {
                callback(new Error($T("新密码不能为空")));
                return;
              }

              callback();
            }
          }
        ],
        _checkPassword: [
          {
            required: true,
            type: "string",
            trigger: "blur",
            validator: (rule, value, callback) => {
              if (value !== this.form.newPassword) {
                callback(new Error($T("密码不一致")));
                return;
              }
              callback();
            }
          }
        ]
      }
    };
  },
  methods: {
    getData() {
      const form = this.form;
      return {
        id: omegaAuth.user.getUserId(),
        name: omegaAuth.user.getUserName(),
        newPassword: form.newPassword,
        oldPassword: form.oldPassword
      };
    },

    async onBeforeConfirm() {
      const { form } = this.$refs;

      await form.validate();

      const res = await customApi.updateUserPassword(this.getData());
      return res.code === 0;
    }
  }
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include padding(J1);
    @include background_color(BG);
  }
  .elForm {
    height: 300px;
  }
}
.layout-user-reset-icon {
  text-align: center;
  width: 80px;
  height: 80px;
  border-radius: 40px;
  padding: 10px;
  @include background_color(BG1);
  @include fill_color(T4);
}
.custom-form-item :deep(.el-input__inner) {
  vertical-align: top;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.gray-label :deep(.el-form-item__label) {
  @include background_color(BG1);
  @include border_color(B1);
  border: 1px solid;
  border-right: none;
  box-sizing: border-box;
  height: 32px;
  line-height: 32px;
  padding: 0 4px;
  box-sizing: border-box;
  border-radius: 4px 0 0 4px;
  text-align: center;
}
</style>
