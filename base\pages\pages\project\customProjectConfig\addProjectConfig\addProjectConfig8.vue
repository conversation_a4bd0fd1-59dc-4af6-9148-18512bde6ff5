<template>
  <div>
    <!-- 1弹窗组件 -->
    <CetDialog
      class="CetDialog"
      ref="CetDialog"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
    >
      <CetForm
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
      >
        <div class="bg1 brC2 rowBox">
          <el-row :gutter="$J3">
            <el-col :span="8">
              <el-form-item
                label="节点类型"
                prop="nodeType"
                v-if="type_in !== 3"
              >
                <ElSelect
                  v-model="CetForm_1.data.nodeType"
                  v-bind="ElSelect_nodeType"
                  v-on="ElSelect_nodeType.event"
                >
                  <ElOption
                    v-for="item in ElOption_nodeType.options_in"
                    :key="item[ElOption_nodeType.key]"
                    :label="item[ElOption_nodeType.label]"
                    :value="item[ElOption_nodeType.value]"
                    :disabled="item[ElOption_nodeType.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="节点名称" prop="name">
                <ElInput
                  v-model="CetForm_1.data.name"
                  v-bind="ElInput_name"
                  v-on="ElInput_name.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('code')">
              <el-form-item label="编号" prop="code">
                <ElInput
                  v-model="CetForm_1.data.code"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('location')">
              <el-form-item label="安装位置" prop="location">
                <ElInput
                  v-model="CetForm_1.data.location"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('commissiondate')">
              <el-form-item label="投用日期" prop="commissiondate">
                <el-date-picker
                  v-model="CetForm_1.data.commissiondate"
                  type="date"
                  value-format="timestamp"
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="filNodePermissions_out('maintenanceperiod')"
            >
              <el-form-item label="检修周期(天)" prop="maintenanceperiod">
                <ElInputNumber
                  v-model="CetForm_1.data.maintenanceperiod"
                  v-bind="ElInputNumber_2"
                  v-on="ElInputNumber_2.event"
                ></ElInputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('lastoverhauldate')">
              <el-form-item label="上次检修日期" prop="lastoverhauldate">
                <el-date-picker
                  v-model="CetForm_1.data.lastoverhauldate"
                  type="date"
                  :editable="false"
                  placeholder="选择日期"
                  value-format="timestamp"
                  format="yyyy-MM-dd"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('nextoverhauldate')">
              <el-form-item label="下次检修日期" prop="nextoverhauldate">
                <el-date-picker
                  v-model="CetForm_1.data.nextoverhauldate"
                  type="date"
                  :editable="false"
                  placeholder="选择日期"
                  value-format="timestamp"
                  format="yyyy-MM-dd"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('manufacturedate')">
              <el-form-item label="出厂日期" prop="manufacturedate">
                <el-date-picker
                  v-model="CetForm_1.data.manufacturedate"
                  type="date"
                  value-format="timestamp"
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('model')">
              <el-form-item label="型号" prop="model">
                <ElInput
                  v-model="CetForm_1.data.model"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('manufactor')">
              <el-form-item label="生产厂家" prop="manufactor">
                <ElInput
                  v-model="CetForm_1.data.manufactor"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('ownship')">
              <el-form-item label="资产归属" prop="ownship">
                <ElInput
                  v-model="CetForm_1.data.ownship"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="filNodePermissions_out('deviceclassification')"
            >
              <el-form-item label="设备归类:" prop="deviceclassification">
                <ElSelect
                  v-model="CetForm_1.data.deviceclassification"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                >
                  <ElOption
                    v-for="item in ElOption_deviceclassification.options_in"
                    :key="item[ElOption_deviceclassification.key]"
                    :label="item[ElOption_deviceclassification.label]"
                    :value="item[ElOption_deviceclassification.value]"
                    :disabled="item[ElOption_deviceclassification.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="filNodePermissions_out('airconditionsystemtype')"
            >
              <el-form-item label="空调系统类型" prop="airconditionsystemtype">
                <ElSelect
                  v-model="CetForm_1.data.airconditionsystemtype"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                >
                  <ElOption
                    v-for="item in airconditionsystemtypeList"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('devicesize')">
              <el-form-item label="机组尺寸" prop="devicesize">
                <ElInput
                  v-model="CetForm_1.data.devicesize"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('deviceweight')">
              <el-form-item label="机组重量（吨）" prop="deviceweight">
                <ElInputNumber
                  v-model="CetForm_1.data.deviceweight"
                  v-bind="ElInputNumber_1"
                  v-on="ElInputNumber_1.event"
                ></ElInputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('coolingmethod')">
              <el-form-item label="制冷方式" prop="coolingmethod">
                <ElSelect
                  v-model="CetForm_1.data.coolingmethod"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                >
                  <ElOption
                    v-for="item in coolingmethodList"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('engineattr')">
              <el-form-item label="机组属性" prop="engineattr">
                <ElSelect
                  v-model="CetForm_1.data.engineattr"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                >
                  <ElOption
                    v-for="item in coldwaterengineattrList"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('voltagelevel')">
              <el-form-item label="电压等级" prop="voltagelevel">
                <ElSelect
                  v-model="CetForm_1.data.voltagelevel"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                >
                  <ElOption
                    v-for="item in voltagelevelList"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('frequencytype')">
              <el-form-item label="是否变频" prop="frequencytype">
                <ElSelect
                  v-model="CetForm_1.data.frequencytype"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                >
                  <ElOption
                    v-for="item in frequencytypeList"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="
                filNodePermissions_out('minworkingfrequency') &&
                filNodePermissions_out('maxworkingfrequency')
              "
            >
              <el-form-item label="工作频率（Hz）" prop="minworkingfrequency">
                <el-row>
                  <el-col :span="11">
                    <ElInputNumber
                      v-model="CetForm_1.data.minworkingfrequency"
                      v-bind="ElInputNumber_1"
                      v-on="ElInputNumber_1.event"
                    ></ElInputNumber>
                  </el-col>
                  <el-col class="line" :span="2">~</el-col>
                  <el-col :span="11">
                    <ElInputNumber
                      v-model="CetForm_1.data.maxworkingfrequency"
                      v-bind="ElInputNumber_1"
                      v-on="ElInputNumber_1.event"
                    ></ElInputNumber>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="filNodePermissions_out('coolingcyclewater')"
            >
              <el-form-item
                label="冷却循环水量（m³/h）"
                prop="coolingcyclewater"
              >
                <ElInputNumber
                  v-model="CetForm_1.data.coolingcyclewater"
                  v-bind="ElInputNumber_1"
                  v-on="ElInputNumber_1.event"
                ></ElInputNumber>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="
                filNodePermissions_out('minconstantoutwatertemp') &&
                filNodePermissions_out('maxconstantoutwatertemp')
              "
            >
              <el-form-item
                label="恒定出水温度（℃）"
                prop="minconstantoutwatertemp"
              >
                <el-row>
                  <el-col :span="11">
                    <ElInputNumber
                      v-model="CetForm_1.data.minconstantoutwatertemp"
                      v-bind="ElInputNumber_1"
                      v-on="ElInputNumber_1.event"
                    ></ElInputNumber>
                  </el-col>
                  <el-col class="line" :span="2">~</el-col>
                  <el-col :span="11">
                    <ElInputNumber
                      v-model="CetForm_1.data.maxconstantoutwatertemp"
                      v-bind="ElInputNumber_1"
                      v-on="ElInputNumber_1.event"
                    ></ElInputNumber>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="
                filNodePermissions_out('mincoolingcycletemp') &&
                filNodePermissions_out('maxcoolingcycletemp')
              "
            >
              <el-form-item
                label="冷却循环水温度（℃）"
                prop="mincoolingcycletemp"
              >
                <el-row>
                  <el-col :span="11">
                    <ElInputNumber
                      v-model="CetForm_1.data.mincoolingcycletemp"
                      v-bind="ElInputNumber_1"
                      v-on="ElInputNumber_1.event"
                    ></ElInputNumber>
                  </el-col>
                  <el-col class="line" :span="2">~</el-col>
                  <el-col :span="11">
                    <ElInputNumber
                      v-model="CetForm_1.data.maxcoolingcycletemp"
                      v-bind="ElInputNumber_1"
                      v-on="ElInputNumber_1.event"
                    ></ElInputNumber>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="
                filNodePermissions_out('minrefrigeratingcycletemp') &&
                filNodePermissions_out('maxrefrigeratingcycletemp')
              "
            >
              <el-form-item
                label="冷冻循环水温度（℃）"
                prop="minrefrigeratingcycletemp"
              >
                <el-row>
                  <el-col :span="11">
                    <ElInputNumber
                      v-model="CetForm_1.data.minrefrigeratingcycletemp"
                      v-bind="ElInputNumber_1"
                      v-on="ElInputNumber_1.event"
                    ></ElInputNumber>
                  </el-col>
                  <el-col class="line" :span="2">~</el-col>
                  <el-col :span="11">
                    <ElInputNumber
                      v-model="CetForm_1.data.maxrefrigeratingcycletemp"
                      v-bind="ElInputNumber_1"
                      v-on="ElInputNumber_1.event"
                    ></ElInputNumber>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('ratedmotorpower')">
              <el-form-item label="额定功率（kW）" prop="ratedmotorpower">
                <ElInputNumber
                  v-model="CetForm_1.data.ratedmotorpower"
                  v-bind="ElInputNumber_1"
                  v-on="ElInputNumber_1.event"
                ></ElInputNumber>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="filNodePermissions_out('ratedrefrigeration')"
            >
              <el-form-item label="额定制冷量（kW）" prop="ratedrefrigeration">
                <ElInputNumber
                  v-model="CetForm_1.data.ratedrefrigeration"
                  v-bind="ElInputNumber_1"
                  v-on="ElInputNumber_1.event"
                ></ElInputNumber>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="filNodePermissions_out('refrigeratingcyclewater')"
            >
              <el-form-item
                label="冷冻循环水量（m³/h）"
                prop="refrigeratingcyclewater"
              >
                <ElInputNumber
                  v-model="CetForm_1.data.refrigeratingcyclewater"
                  v-bind="ElInputNumber_1"
                  v-on="ElInputNumber_1.event"
                ></ElInputNumber>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="filNodePermissions_out('refrigerationmediumtype')"
            >
              <el-form-item label="制冷介质" prop="refrigerationmediumtype">
                <ElSelect
                  v-model="CetForm_1.data.refrigerationmediumtype"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                >
                  <ElOption
                    v-for="item in refrigerationmediumtypeList"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="filNodePermissions_out('ratedheatexchange')"
            >
              <el-form-item label="额定热换量" prop="ratedheatexchange">
                <ElInputNumber
                  v-model="CetForm_1.data.ratedheatexchange"
                  v-bind="ElInputNumber_1"
                  v-on="ElInputNumber_1.event"
                ></ElInputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="filNodePermissions_out('valvetype')">
              <el-form-item label="阀门类型" prop="valvetype">
                <ElSelect
                  v-model="CetForm_1.data.valvetype"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                >
                  <ElOption
                    v-for="item in valvetypeList"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="相关文档" prop="test">
                <div class="mb10 upload" style="float: left">
                  <div
                    class="eem-pload-label"
                    :title="documentName"
                    v-if="type_in == 3"
                  >
                    {{ documentName || "--" }}
                    <el-button
                      class="ml-10 fr"
                      style="position: absolute; top: 0px; right: 0px"
                      size="mini"
                      type="danger"
                      icon="el-icon-delete"
                      circle
                      @click="handleDeleteDocument_out"
                    ></el-button>
                  </div>
                  <el-upload
                    class="elupload11"
                    ref="DocElupload"
                    action="/eem-service/v1/common/uploadFile"
                    :headers="{ Authorization: this.token }"
                    :before-upload="handleBeforeUpload2"
                    :on-success="uploadSuccess2"
                    :multiple="false"
                    :limit="1"
                    :before-remove="beforeRemove"
                    :on-exceed="handleExceed"
                  >
                    <el-button size="small" type="primary">
                      选择上传文件
                    </el-button>
                    <p>
                      只能上传xls/xlsx/docx/pdf格式文件,且不超过{{
                        systemCfg.uploadDocSize || "10"
                      }}M
                    </p>
                  </el-upload>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="bg1 brC2 rowBox mtJ1">
          <el-row :gutter="$J3">
            <el-col :span="24">
              <el-form-item>
                <div class="label" slot="label">
                  主图
                  <div class="box_tip2">
                    只能上传jpg/png图片，且不超过{{ systemCfg.uploadPicSize }}M
                  </div>
                </div>
                <div class="value">
                  <UploadImg
                    class="uploadImg"
                    :imgUrl.sync="CetForm_1.data.pic"
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </CetForm>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>

      <!-- 查设备归类 -->
      <CetInterface
        :data.sync="CetInterface_query.data"
        :dynamicInput.sync="CetInterface_query.dynamicInput"
        v-bind="CetInterface_query"
        v-on="CetInterface_query.event"
      ></CetInterface>
    </CetDialog>
  </div>
</template>
<script>
import common from "eem-utils/common";
import ELECTRICAL_DEVICE_NODE from "@/store/electricaldevicenode.js";
import Vue from "vue";
import UploadImg from "eem-components/uploadImg.vue";
import { httping } from "@omega/http";
export default {
  name: "addProjectConfig8",
  components: { UploadImg },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    treeData_in: {
      type: Object
    },
    currentTabItme_in: {
      type: Object
    },
    type_in: {
      type: Number
    },
    treeNameList_in: {
      type: Array
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    systemCfg() {
      return this.$store.state.systemCfg;
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: "添加空调机房设备",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "保存",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "关闭",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {
          nodeType: "coolingtower"
        },
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "160px",
        labelPosition: "top",
        rules: {
          nodeType: [
            {
              required: true,
              message: "请选择节点类型",
              trigger: ["blur", "change"]
            }
          ],
          name: [
            {
              required: true,
              message: "请输入名称",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          code: [
            {
              required: false,
              message: "请输入编号",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          location: [
            {
              required: false,
              message: "请输入安装位置",
              trigger: ["blur", "change"]
            },
            common.check_stringLessThan255,
            common.pattern_name
          ],
          model: [
            {
              required: false,
              message: "请输入型号",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          manufacturedate: [
            {
              required: false,
              message: "请选择出厂日期",
              trigger: ["blur", "change"]
            }
          ],
          commissiondate: [
            {
              required: false,
              message: "请选择投用日期",
              trigger: ["blur", "change"]
            }
          ],
          manufactor: [
            {
              required: false,
              message: "请输入厂家",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          minworkingfrequency: [
            {
              required: false,
              type: "string",
              message: "最小频率要小于最大频率",
              trigger: "blur",
              validator: (rule, value, callback) => {
                if (!value) {
                  callback();
                  return;
                }
                if (value > this.CetForm_1.data.maxworkingfrequency) {
                  callback(new Error());
                  return;
                }
                callback();
              }
            }
          ],
          minconstantoutwatertemp: [
            {
              required: false,
              type: "string",
              message: "最小恒定出水温度要小于最大恒定出水温度",
              trigger: "blur",
              validator: (rule, value, callback) => {
                if (!value) {
                  callback();
                  return;
                }
                if (value > this.CetForm_1.data.maxconstantoutwatertemp) {
                  callback(new Error());
                  return;
                }
                callback();
              }
            }
          ],
          mincoolingcycletemp: [
            {
              required: false,
              type: "string",
              message: "最小冷却循环水温度要小于最大冷却循环水温度",
              trigger: "blur",
              validator: (rule, value, callback) => {
                if (!value) {
                  callback();
                  return;
                }
                if (value > this.CetForm_1.data.maxcoolingcycletemp) {
                  callback(new Error());
                  return;
                }
                callback();
              }
            }
          ],
          minrefrigeratingcycletemp: [
            {
              required: false,
              type: "string",
              message: "最小冷冻循环水温度要小于最大冷冻循环水温度",
              trigger: "blur",
              validator: (rule, value, callback) => {
                if (!value) {
                  callback();
                  return;
                }
                if (value > this.CetForm_1.data.maxrefrigeratingcycletemp) {
                  callback(new Error());
                  return;
                }
                callback();
              }
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_1_currentData_out,
          saveData_out: this.CetForm_1_saveData_out,
          finishData_out: this.CetForm_1_finishData_out,
          finishTrigger_out: this.CetForm_1_finishTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: "请输入内容",
        style: {},
        event: {}
      },
      ElInputNumber_1: {
        ...common.check_numberFloat,
        value: "",
        style: {
          width: "100%"
        },
        controls: false,
        event: {}
      },
      ElInputNumber_2: {
        ...common.check_numberInt,
        value: "",
        style: {
          width: "100%"
        },
        controls: false,
        event: {}
      },
      ElInputNumber_3: {
        ...common.check_numberFloat11,
        value: "",
        style: {
          width: "100%"
        },
        placeholder: "请选择经纬度",
        controls: false,
        event: {}
      },
      ElSelect_1: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElOption_deviceclassification: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElInput_name: {
        value: "",
        placeholder: "请输入内容",
        style: {},
        event: {
          change: this.ElInput_name_change_out,
          input: this.ElInput_name_input_out
        }
      },
      // nodeType组件
      ElSelect_nodeType: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_nodeType_change_out
        }
      },
      // nodeType组件
      ElOption_nodeType: {
        options_in: [
          {
            value: "coolingtower",
            label: "冷却塔"
          },
          {
            value: "windset",
            label: "风柜"
          },
          {
            value: "coldwatermainengine",
            label: "冷水主机"
          },
          {
            value: "plateheatexchanger",
            label: "板式换热器"
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      uploadPath2: "",
      documentName: "",
      coolingmethodList: [],
      coldwaterengineattrList: [],
      airconditionsystemtypeList: [],
      voltagelevelList: [],
      frequencytypeList: [],
      refrigerationmediumtypeList: [],
      valvetypeList: [],
      CetInterface_query: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "getEventClassification",
          modelLabel: "deviceclassification",
          dataIndex: [],
          modelList: [],
          filters: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {},
        page_in: null, // exp:{ index: 1, limit: 20 }
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          result_out: this.CetInterface_query_result_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      vm.$nextTick(() => {
        vm.$refs.DocElupload.clearFiles();
        vm.coolingmethodList =
          this.$store.state.enumerations.coolingmethod || [];
        vm.coldwaterengineattrList =
          this.$store.state.enumerations.coldwaterengineattr || [];
        vm.airconditionsystemtypeList =
          this.$store.state.enumerations.airconditionsystemtype || [];
        vm.voltagelevelList = this.$store.state.enumerations.voltagelevel || [];
        vm.frequencytypeList =
          this.$store.state.enumerations.frequencytype || [];
        vm.refrigerationmediumtypeList =
          this.$store.state.enumerations.refrigerationmediumtype || [];
        vm.valvetypeList = this.$store.state.enumerations.valvetype || [];
        this.CetInterface_query.queryTrigger_in = new Date().getTime();
        $(this.$refs.CetDialog.$el).scrollTop(0);
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {},
    inputData_in(val) {
      // this.CetDialog_1.inputData_in = val;
      this.CetForm_1.data = this._.cloneDeep(val);
      this.uploadPath2 = "";
      this.CetForm_1.data.nodeType = "coolingtower";
      // 模型服务中字段返回是小写 删除大写的名称
      delete this.CetForm_1.data.busbarSegI;
      delete this.CetForm_1.data.busbarSegII;
      if (this.type_in === 3) {
        //删除设备通用字段信息，防止编辑保存报错；
        delete this.CetForm_1.data.devicecommoninfo_model;
        Vue.set(this.CetForm_1.data, "nodeType", val.modelLabel);
        // 文档
        var str = val.document || "";
        if (str) {
          this.uploadPath2 = str;
        }
        try {
          this.documentName =
            str.split("/")[3].split("_")[0] +
            "." +
            str.split("/")[3].split("_")[1].split(".")[1];
        } catch (err) {
          this.documentName = "";
        }
        this.CetDialog_1.title = "编辑节点";
      } else {
        this.CetDialog_1.title = "添加节点";
      }
      this.CetForm_1.resetTrigger_in = new Date().getTime();
    },
    type_in(val) {}
  },

  methods: {
    filNodePermissions_out(key) {
      const nodeModelLabel = this.CetForm_1.data.nodeType || "";
      const modelLabelList = ELECTRICAL_DEVICE_NODE[nodeModelLabel] || [];
      let isOk = false;
      modelLabelList.forEach(item => {
        if (item.propertyLabel == key) {
          isOk = true;
        }
      });
      return isOk;
    },
    // 获取设备归类
    CetInterface_query_result_out(val) {
      this.ElOption_deviceclassification.options_in = val;
    },
    beforeRemove(file, fileList) {
      var _this = this;
      _this.uploadPath2 = "";
    },
    //    上传前
    handleBeforeUpload2: function (file) {
      if (this.documentName) {
        this.$message.warning(`当前限制选择 1 个文件`);
        return false;
      }
      this.loading = true;
      if (
        file.name.indexOf(".xls") != -1 ||
        file.name.indexOf(".xlsx") != -1 ||
        file.name.indexOf(".docx") != -1 ||
        file.name.indexOf(".pdf") != -1
      ) {
        var uploadDocSize = this.systemCfg.uploadDocSize || 10;
        const isLimit100M = file.size / 1024 / 1024 < uploadDocSize;
        if (!isLimit100M) {
          this.$message.error("上传文件超过规定的最大上传大小");
        }
        return isLimit100M;
      } else {
        this.$message({
          type: "warning",
          message: "只能上传xls/xlsx/docx/pdf格式文件"
        });
        return false;
      }
    },
    //   上传成功
    uploadSuccess2: function (response) {
      if (response.code === 0) {
        this.uploadPath2 = response.data;
        this.$message({
          message: "上传成功",
          type: "success"
        });
      } else if (response.code !== 0) {
        var tips = "";
        if (response.data) {
          for (var i = 0; i < response.data.length; i++) {
            tips = tips + response.data[i] + "<br/>";
          }
        } else {
          tips = response.msg;
        }
        this.$message({
          type: "error",
          message: tips
        });
      }
    },
    handleDeleteDocument_out() {
      this.documentName = "";
      this.toDeleteDoc = true;
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件`);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
      this.$emit("confirm_out");
    },
    CetDialog_1_open_out(val) {},
    CetDialog_1_close_out(val) {},
    CetForm_1_saveData_out(val) {
      const lastoverhauldate = this.CetForm_1.data.lastoverhauldate;
      const nextoverhauldate = this.CetForm_1.data.nextoverhauldate;
      const manufacturedate = this.CetForm_1.data.manufacturedate;
      const commissiondate = this.CetForm_1.data.commissiondate;
      if (lastoverhauldate && nextoverhauldate) {
        if (lastoverhauldate >= nextoverhauldate) {
          this.$message.warning("下次检修时间不能小于等于上次检修日期！");
          return;
        }
      }
      if (manufacturedate && commissiondate) {
        if (manufacturedate >= commissiondate) {
          this.$message.warning("投运时间不能小于等于出厂时间！");
          return;
        }
      }
      var params = [];
      const modelData = this._.cloneDeep(this.CetForm_1.data);
      if (this.toDeleteDoc) {
        modelData.document = "";
      }
      if (this.uploadPath2) {
        modelData.document = this.uploadPath2;
      }
      if (this.type_in === 1) {
        if (!this.treeData_in) {
          return;
        }
        // var params = [];
        modelData.modelLabel = this.CetForm_1.data.nodeType;
        modelData.children = [
          {
            id: this.treeData_in.id,
            modelLabel: this.treeData_in.modelLabel
          }
        ];
        params[0] = modelData;
      } else if (this.type_in === 3) {
        if (!this.inputData_in) {
          return;
        }
        modelData.children = [
          {
            id: this.treeData_in.id,
            modelLabel: this.treeData_in.modelLabel
          }
        ];
        params[0] = modelData;
      }
      var name = this.CetForm_1.data.name;
      var list = this.treeNameList_in || [];
      for (var i = 0, len = list.length; i < len; i++) {
        if (list[i] === name) {
          if (this.type_in === 3 && this.inputData_in.name === name) {
            continue;
          }
          this.$message.warning("节点名称重复！");
          return;
        }
      }
      this.Add_node(params);
    },
    CetForm_1_currentData_out(val) {},
    CetForm_1_finishData_out(val) {},
    CetForm_1_finishTrigger_out(val) {},
    ElInput_name_change_out(val) {},
    ElInput_name_input_out(val) {},
    ElSelect_nodeType_change_out(val) {
      if (!val) {
        return;
      }
      this.CetForm_1.data = {
        nodeType: val
      };
    },
    Add_node(params) {
      var _this = this;
      httping({
        url: "/eem-service/v1/project/manageNode",
        data: params,
        method: "PUT"
      }).then(res => {
        if (res.code === 0) {
          _this.CetDialog_1.closeTrigger_in = _this._.cloneDeep(
            new Date().getTime()
          );
          _this.$emit("saveData_out", res.data[0]);
          if (_this.type_in !== 3) {
            _this.Edit_quantity(res.data);
          }
        }
      });
    },
    Edit_quantity(data) {
      var _this = this;
      var obj = this._.get(data, "[0]", {});
      var params = {};
      params = {
        dataSource: 1,
        energyType: null,
        nodes: [
          {
            id: obj.id,
            modelLabel: obj.modelLabel
          }
        ]
      };
      httping({
        url: "/eem-service/v1/quantity/quantityObjectSetting",
        data: params,
        method: "POST"
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
    box-sizing: border-box;
  }
  .rowBox {
    @include padding(J4 J4 J2 J4);
    padding-bottom: mh-get(J4) - 18px;
  }
}

.uploadImg {
  width: 80px;
  height: 80px;
}
.box_tip2 {
  display: inline-block;
  @include font_size(Ab);
  @include font_color(T3);
}
.upload,
.elupload11 {
  width: 100%;
  text-align: center;
  :deep(.el-upload) {
    width: 100%;
  }
}

.eem-pload-label {
  width: 100%;
  height: 30px;
  padding-right: 40px;
  box-sizing: border-box;
  position: relative;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}
.line {
  text-align: center;
}
</style>
