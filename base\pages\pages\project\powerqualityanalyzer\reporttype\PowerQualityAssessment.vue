<template>
  <el-container class="PowerQualityAssessment">
    <!-- 谐波 -->
    <template>
      <el-row class="mbJ3">
        <el-col :span="24" class="text-left common-title-H3">
          评估时间：{{
            queryParam.queryTime.startTime | formatDate("YYYY-MM-DD")
          }}
          至 {{ queryParam.queryTime.endTime | formatDate("YYYY-MM-DD") }}
        </el-col>
      </el-row>
      <div class="eem-cont mbJ3">
        <div>
          <headerSpot class="mbJ3">谐波</headerSpot>
        </div>
        <el-row>
          <el-col>
            <table class="table-terse" cellspacing="0" cellpadding="0">
              <thead>
                <tr>
                  <th>参数</th>
                  <th>最大值</th>
                  <th>最小值</th>
                  <th>C95最大值</th>
                  <th>国际限值</th>
                  <th>结论</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Va总谐波畸变率</td>
                  <td>
                    {{ findDataByKey(harmonicData, "va.max") | formatNum(2) }}
                  </td>
                  <td>
                    {{ findDataByKey(harmonicData, "va.min") | formatNum(2) }}
                  </td>
                  <td>
                    {{ findDataByKey(harmonicData, "va.c95") | formatNum(2) }}
                  </td>
                  <td rowspan="3">
                    {{ findDataByKey(harmonicData, "limit") }}
                  </td>
                  <td rowspan="3">
                    {{ findDataByKey(harmonicData, "conclusion") }}
                  </td>
                </tr>
                <tr>
                  <td>Vb总谐波畸变率</td>
                  <td>
                    {{ findDataByKey(harmonicData, "vb.max") | formatNum(2) }}
                  </td>
                  <td>
                    {{ findDataByKey(harmonicData, "vb.min") | formatNum(2) }}
                  </td>
                  <td>
                    {{ findDataByKey(harmonicData, "vb.c95") | formatNum(2) }}
                  </td>
                </tr>
                <tr>
                  <td>Vc总谐波畸变率</td>
                  <td>
                    {{ findDataByKey(harmonicData, "vc.max") | formatNum(2) }}
                  </td>
                  <td>
                    {{ findDataByKey(harmonicData, "vc.min") | formatNum(2) }}
                  </td>
                  <td>
                    {{ findDataByKey(harmonicData, "vc.c95") | formatNum(2) }}
                  </td>
                </tr>
              </tbody>
            </table>
          </el-col>
        </el-row>
        <el-row class="">
          <el-col style="height: 300px">
            <CetChart
              ref="harmonicChart"
              v-bind="CetChart2_harmonic"
            ></CetChart>
          </el-col>
        </el-row>
      </div>
    </template>
    <!-- 电压偏差 -->
    <template>
      <div class="eem-cont mbJ3">
        <div>
          <headerSpot class="mbJ3">电压偏差</headerSpot>
        </div>
        <el-row>
          <el-col>
            <table class="table-terse" cellspacing="0" cellpadding="0">
              <thead>
                <tr>
                  <th>参数</th>
                  <th>最大值</th>
                  <th>最小值</th>
                  <th>C95最大值</th>
                  <th>国际限值</th>
                  <th>结论</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>A相电压偏差</td>
                  <td>
                    {{ findDataByKey(deviationData, "va.max") | formatNum(2) }}
                  </td>
                  <td>
                    {{ findDataByKey(deviationData, "va.min") | formatNum(2) }}
                  </td>
                  <td>
                    {{ findDataByKey(deviationData, "va.c95") | formatNum(2) }}
                  </td>
                  <td rowspan="3">
                    {{ findDataByKey(deviationData, "limit") }}
                  </td>
                  <td rowspan="3">
                    {{ findDataByKey(deviationData, "conclusion") }}
                  </td>
                </tr>
                <tr>
                  <td>B相电压偏差</td>
                  <td>
                    {{ findDataByKey(deviationData, "vb.max") | formatNum(2) }}
                  </td>
                  <td>
                    {{ findDataByKey(deviationData, "vb.min") | formatNum(2) }}
                  </td>
                  <td>
                    {{ findDataByKey(deviationData, "vb.c95") | formatNum(2) }}
                  </td>
                </tr>
                <tr>
                  <td>C相电压偏差</td>
                  <td>
                    {{ findDataByKey(deviationData, "vc.max") | formatNum(2) }}
                  </td>
                  <td>
                    {{ findDataByKey(deviationData, "vc.min") | formatNum(2) }}
                  </td>
                  <td>
                    {{ findDataByKey(deviationData, "vc.c95") | formatNum(2) }}
                  </td>
                </tr>
              </tbody>
            </table>
          </el-col>
        </el-row>
        <el-row class="">
          <el-col style="height: 300px">
            <CetChart
              ref="deviationChart"
              v-bind="CetChart2_voltageDeviation"
            ></CetChart>
          </el-col>
        </el-row>
      </div>
    </template>
    <!-- 三相电压不平衡度 -->
    <template>
      <div class="eem-cont mbJ3">
        <div>
          <headerSpot class="mbJ3">三相电压不平衡度</headerSpot>
        </div>
        <el-row>
          <el-col>
            <table class="table-terse" cellspacing="0" cellpadding="0">
              <thead>
                <tr>
                  <th>参数</th>
                  <th>最大值</th>
                  <th>最小值</th>
                  <th>C95最大值</th>
                  <th>国际限值</th>
                  <th>结论</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>三相电压不平衡度</td>
                  <td>
                    {{
                      findDataByKey(unbalancerateData, "unbalancerate.max")
                        | formatNum(2)
                    }}
                  </td>
                  <td>
                    {{
                      findDataByKey(unbalancerateData, "unbalancerate.min")
                        | formatNum(2)
                    }}
                  </td>
                  <td>
                    {{
                      findDataByKey(unbalancerateData, "unbalancerate.c95")
                        | formatNum(2)
                    }}
                  </td>
                  <td>{{ findDataByKey(unbalancerateData, "limit") }}</td>
                  <td>{{ findDataByKey(unbalancerateData, "conclusion") }}</td>
                </tr>
              </tbody>
            </table>
          </el-col>
        </el-row>
        <el-row>
          <el-col style="height: 300px">
            <CetChart
              ref="unbalancerateChart"
              v-bind="CetChart2_phaseVoltage"
            ></CetChart>
          </el-col>
        </el-row>
      </div>
    </template>
    <!-- 闪变 -->
    <template>
      <div class="eem-cont mbJ3">
        <div>
          <headerSpot class="mbJ3">闪变</headerSpot>
        </div>
        <el-row>
          <el-col>
            <table class="table-terse" cellspacing="0" cellpadding="0">
              <thead>
                <tr>
                  <th>参数</th>
                  <th>最大值</th>
                  <th>国际限值</th>
                  <th>结论</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Va长时闪变</td>
                  <td>
                    {{ findDataByKey(flickerData, "va.max") | formatNum(2) }}
                  </td>
                  <td rowspan="3">{{ findDataByKey(flickerData, "limit") }}</td>
                  <td rowspan="3">
                    {{ findDataByKey(flickerData, "conclusion") }}
                  </td>
                </tr>
                <tr>
                  <td>Vb长时闪变</td>
                  <td>
                    {{ findDataByKey(flickerData, "vb.max") | formatNum(2) }}
                  </td>
                </tr>
                <tr>
                  <td>Vc长时闪变</td>
                  <td>
                    {{ findDataByKey(flickerData, "vc.max") | formatNum(2) }}
                  </td>
                </tr>
              </tbody>
            </table>
          </el-col>
        </el-row>
        <el-row>
          <el-col style="height: 300px">
            <CetChart
              ref="flickerChart"
              v-bind="CetChart2_flickering"
            ></CetChart>
          </el-col>
        </el-row>
      </div>
    </template>
    <!-- 频率 -->
    <template>
      <div class="eem-cont mbJ3">
        <div>
          <headerSpot class="mbJ3">频率</headerSpot>
        </div>
        <el-row>
          <el-col>
            <table class="table-terse" cellspacing="0" cellpadding="0">
              <thead>
                <tr>
                  <th>参数</th>
                  <th>最大值</th>
                  <th>最小值</th>
                  <th>C95最大值</th>
                  <th>国际限值</th>
                  <th>结论</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>频率偏差</td>
                  <td>
                    {{
                      findDataByKey(frequencyData, "frequencydeviation.max")
                        | formatNum(2)
                    }}
                  </td>
                  <td>
                    {{
                      findDataByKey(frequencyData, "frequencydeviation.min")
                        | formatNum(2)
                    }}
                  </td>
                  <td>
                    {{
                      findDataByKey(frequencyData, "frequencydeviation.c95")
                        | formatNum(2)
                    }}
                  </td>
                  <td>{{ findDataByKey(frequencyData, "limit") }}</td>
                  <td>{{ findDataByKey(frequencyData, "conclusion") }}</td>
                </tr>
              </tbody>
            </table>
          </el-col>
        </el-row>
        <el-row>
          <el-col style="height: 300px">
            <CetChart
              ref="frequencyChart"
              v-bind="CetChart2_frequency"
            ></CetChart>
          </el-col>
        </el-row>
      </div>
    </template>
    <template>
      <div v-show="conclusion" class="eem-cont">
        <p>评估结论：</p>
        <p>{{ conclusion }}</p>
      </div>
    </template>
  </el-container>
</template>
<script>
import common from "eem-utils/common";
import moment from "moment";

export default {
  name: "PowerQualityAssessment",
  props: {
    queryParam: {
      type: [Object],
      default: () => ({
        queryTime: {
          startTime: common.initDateRange("M")[0],
          endTime: common.initDateRange("M")[1]
        }
      })
    }
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    token() {
      return this.$store.state.token;
    }
  },
  watch: {
    queryParam: {
      deep: true,
      handler(val, oldVal) {
        this.queryReport();
      }
    }
  },
  data() {
    return {
      // 谐波柱形图
      CetChart2_harmonic: {
        //组件输入项
        inputData_in: null,
        options: {
          title: {
            show: false,
            left: "center",
            top: "40%",
            text: "暂无数据",
            textStyle: {
              color: "#909399",
              fontStyle: "normal",
              fontWeight: "normal",
              fontSize: "14",
              fontFamily: "Microsoft YaHei"
            }
          },
          xAxis: {
            type: "category"
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            }
          },
          yAxis: {
            type: "value"
          },
          series: [
            {
              type: "bar",
              barWidth: 40
            },
            {
              type: "bar",
              barWidth: 40
            },
            {
              type: "bar",
              barWidth: 40
            }
          ]
        }
      },
      // 电压偏差
      // 设置组件唯一识别字段组件
      CetChart2_voltageDeviation: {
        //组件输入项
        inputData_in: null,
        options: {
          title: {
            show: false,
            left: "center",
            top: "40%",
            text: "暂无数据",
            textStyle: {
              color: "#909399",
              fontStyle: "normal",
              fontWeight: "normal",
              fontSize: "14",
              fontFamily: "Microsoft YaHei"
            }
          },
          xAxis: {
            type: "category"
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            }
          },
          yAxis: {
            type: "value"
          },
          series: [
            {
              type: "bar",
              barWidth: 40
            },
            {
              type: "bar",
              barWidth: 40
            },
            {
              type: "bar",
              barWidth: 40
            }
          ]
        }
      },

      // 三相电压不平衡度
      CetChart2_phaseVoltage: {
        //组件输入项
        inputData_in: null,
        options: {
          title: {
            show: false,
            left: "center",
            top: "40%",
            text: "暂无数据",
            textStyle: {
              color: "#909399",
              fontStyle: "normal",
              fontWeight: "normal",
              fontSize: "14",
              fontFamily: "Microsoft YaHei"
            }
          },
          xAxis: {
            type: "category"
          },
          yAxis: {
            type: "value"
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            }
          },
          series: [
            {
              type: "bar",
              barWidth: 40
            }
          ]
        }
      },

      // 闪变组件
      CetChart2_flickering: {
        //组件输入项
        inputData_in: null,
        options: {
          title: {
            show: false,
            left: "center",
            top: "40%",
            text: "暂无数据",
            textStyle: {
              color: "#909399",
              fontStyle: "normal",
              fontWeight: "normal",
              fontSize: "14",
              fontFamily: "Microsoft YaHei"
            }
          },
          xAxis: {
            type: "category"
          },
          yAxis: {
            type: "value"
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            }
          },
          series: [
            {
              type: "bar",
              barWidth: 40
            }
          ]
        }
      },

      // 频率
      CetChart2_frequency: {
        //组件输入项
        inputData_in: null,
        options: {
          title: {
            show: false,
            left: "center",
            top: "40%",
            text: "暂无数据",
            textStyle: {
              color: "#909399",
              fontStyle: "normal",
              fontWeight: "normal",
              fontSize: "14",
              fontFamily: "Microsoft YaHei"
            }
          },
          xAxis: {
            type: "category"
          },
          yAxis: {
            type: "value"
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            }
          },
          series: [
            {
              type: "bar",
              barWidth: 40
            }
          ]
        }
      },
      harmonicData: {},
      deviationData: {},
      unbalancerateData: {},
      flickerData: {},
      frequencyData: {},
      conclusion: "",
      pageData: null
    };
  },
  methods: {
    getPageData() {},
    findDataByKey(...arg) {
      return common.findDataByKey(...arg);
    },
    queryReport() {
      const vm = this;
      if (!vm.queryParam || !vm.queryParam.params) {
        return;
      }
      const builddata = vm.queryParam.params;

      const initparam = {
        cycle: builddata.aggregationCycle,
        endTime: builddata.endTime,
        eventTypes: [0],
        lineFunctionTypes: [0],
        node: {
          childSelectState: 0,
          children: [null],
          deviceIds: [0],
          endPoint: true,
          id: builddata.id,
          modelLabel: builddata.modelLabel,
          name: "",
          startPoint: true
        },
        pictures: [],
        projectId: this.projectId,
        startTime: builddata.startTime
      };

      common.requestData(
        {
          url: "/eem-service/v1/pq/report/pq",
          data: initparam
        },
        res => {
          console.log(res);

          // 谐波
          vm.CetChart2_harmonic.inputData_in = [];
          vm.harmonicData = vm.handleHarmonicData(
            res,
            "harmonic",
            vm.CetChart2_harmonic.inputData_in
          );
          if (res.harmonic.reportDataList == "") {
            this.CetChart2_harmonic.options.title.show = true;
          } else {
            this.CetChart2_harmonic.options.title.show = false;
          }

          vm.CetChart2_voltageDeviation.inputData_in = [];
          vm.deviationData = vm.handleHarmonicData(
            res,
            "deviation",
            vm.CetChart2_voltageDeviation.inputData_in
          );
          if (res.deviation.reportDataList == "") {
            this.CetChart2_voltageDeviation.options.title.show = true;
          } else {
            this.CetChart2_voltageDeviation.options.title.show = false;
          }

          vm.CetChart2_flickering.inputData_in = [];
          vm.flickerData = vm.handleHarmonicData(
            res,
            "flicker",
            vm.CetChart2_flickering.inputData_in,
            ["va", "vb", "vc"],
            ["max"]
          );
          if (res.flicker.reportDataList == "") {
            this.CetChart2_flickering.options.title.show = true;
          } else {
            this.CetChart2_flickering.options.title.show = false;
          }

          vm.CetChart2_phaseVoltage.inputData_in = [];
          vm.unbalancerateData = vm.handleHarmonicData(
            res,
            "unbalancerate",
            vm.CetChart2_phaseVoltage.inputData_in,
            ["unbalancerate"],
            ["max", "min", "c95"]
          );

          if (res.unbalancerate.reportDataList == "") {
            this.CetChart2_phaseVoltage.options.title.show = true;
          } else {
            this.CetChart2_phaseVoltage.options.title.show = false;
          }

          vm.CetChart2_frequency.inputData_in = [];
          vm.frequencyData = vm.handleHarmonicData(
            res,
            "frequency",
            vm.CetChart2_frequency.inputData_in,
            ["frequencydeviation"],
            ["max", "min", "c95"]
          );

          if (res.frequency.reportDataList == "") {
            this.CetChart2_frequency.options.title.show = true;
          } else {
            this.CetChart2_frequency.options.title.show = false;
          }

          const conclusion = [];
          const noData = [];
          const maps = [
            {
              key: "harmonicData",
              label: "谐波对标管理"
            },
            {
              key: "deviationData",
              label: "电压偏差对标管理"
            },
            {
              key: "flickerData",
              label: "闪变对标管理"
            },
            {
              key: "unbalancerateData",
              label: "三项电压不平衡度对标管理"
            },
            {
              key: "frequencyData",
              label: "频率偏差对标管理"
            }
          ];

          maps.forEach(item => {
            if (vm[item.key].conclusion === "--") {
              noData.push(item.label);
            } else if (vm[item.key].conclusion === "不合格") {
              conclusion.push(item.label);
            }
          });

          if (conclusion.length > 0) {
            vm.conclusion = `评估期间内，${conclusion.join(
              "、"
            )}不合格，请予以关注！`;
          }
        }
      );
    },
    handleHarmonicData(
      allData,
      indexName,
      chartData,
      paramItem = ["va", "vb", "vc"],
      dataType = ["max", "min", "c95"]
    ) {
      const vm = this;
      // 无论data有没有数据，都经过这样的处理，这样将来展示的时候不会出现某项undefined的情况
      const res = vm.handleEmptyData(paramItem, dataType);
      const report = allData[indexName];
      const data = report.reportDataList;
      if (!data || data.length <= 0) {
        return res;
      }

      res.limit = report.limit ? report.limit : "--";
      res.conclusion = report.conclusion ? report.conclusion : "--";

      for (const item of data) {
        if (!item.data) continue;
        let obj = {};

        if (paramItem.length > 1) {
          obj = { name: vm.formatChartItemName(indexName, item.name) };
        }
        for (const item2 of item.data) {
          res[item.name][item2.name] = common.roundNumber(item2.value);

          if (paramItem.length == 1) {
            chartData.push({
              name: vm.formatChartItemName(indexName, item2.name),
              value: common.roundNumber(item2.value)
            });
          } else {
            obj[item2.name] = common.roundNumber(item2.value);
          }
        }
        if (paramItem.length > 1) {
          chartData.push(obj);
        }
      }

      return res;
    },
    formatChartItemName(item, name) {
      if (item === "harmonic") {
        switch (name) {
          case "va":
            return "Va总谐波畸变率";
          case "vb":
            return "Vb总谐波畸变率";
          case "vc":
            return "Vc总谐波畸变率";
        }
      } else if (item === "deviation") {
        switch (name) {
          case "va":
            return "A相电压偏差";
          case "vb":
            return "B相电压偏差";
          case "vc":
            return "C相电压偏差";
        }
      } else if (item === "flicker") {
        switch (name) {
          case "va":
            return "Va长时间闪变值";
          case "vb":
            return "Vb长时间闪变值";
          case "vc":
            return "Vc长时间闪变值";
        }
      } else if (item === "unbalancerate" || item === "frequency") {
        switch (name) {
          case "max":
            return "最大值";
          case "min":
            return "最小值";
          case "c95":
            return "C95最大值";
        }
      }
    },
    handleEmptyData(paramItem, dataType) {
      const res = {
        limit: "--",
        conclusion: "--"
      };
      for (const item1 of paramItem) {
        res[item1] = {};
        for (const item2 of dataType) {
          res[item1][item2] = "--";
        }
      }

      return res;
    },
    exportReport() {
      const vm = this;

      if (!vm.queryParam || !vm.queryParam.params) {
        return;
      }

      const params = this._.cloneDeep(vm.queryParam.params);
      params.pictures = [];
      params.pictures.push(this.$refs.harmonicChart.getDataURL());
      params.pictures.push(this.$refs.deviationChart.getDataURL());
      params.pictures.push(this.$refs.unbalancerateChart.getDataURL());
      params.pictures.push(this.$refs.flickerChart.getDataURL());
      params.pictures.push(this.$refs.frequencyChart.getDataURL());

      const initparam = {
        cycle: params.aggregationCycle,
        endTime: params.endTime,
        eventTypes: [0],
        lineFunctionTypes: [0],
        node: {
          childSelectState: 0,
          children: [null],
          deviceIds: [0],
          endPoint: true,
          id: params.id,
          modelLabel: params.modelLabel,
          name: "电能质量评估报表",
          startPoint: true
        },
        pictures: params.pictures,
        projectId: this.projectId,
        startTime: params.startTime
      };

      common.downExcel(
        "/eem-service/v1/pq/report/pq/export",
        initparam,
        this.token
      );
    }
  },
  filters: {
    //格式化日期
    formatDate(val, format = "YYYY-MM-DD HH:mm:ss") {
      if (!val) {
        return "--";
      }

      return moment(val).format(format);
    },
    formatNum(val, precision = 0, placeholder = "--") {
      if (!_.isNumber(val)) {
        //先转换成数字
        val = parseFloat(val);
      }
      if (isNaN(val)) {
        return placeholder;
      }

      return val.toFixed2(precision); //不为空的话就保留小数位
    }
  },
  created() {
    this.queryReport();
  }
};
</script>
<style lang="scss" scope>
.table-terse {
  th,
  td {
    @include padding(J);
  }
}
.PowerQualityAssessment {
  width: 100%;
  // padding: 10px;
  height: 100%;
  flex-direction: column;
  overflow: auto;
}
</style>
