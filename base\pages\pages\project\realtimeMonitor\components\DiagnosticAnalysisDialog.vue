<template>
  <div>
    <CetDialog
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
      class="CetDialog small"
    >
      <div class="eem-cont-c1" style="min-height: 100px; max-height: 320px">
        <div class="item" v-for="(item, i) in scenarios" :key="i">
          <div class="name" @click="showDetail(item, i + 1)">
            <span>{{ $T("推荐解决方案") }}{{ i + 1 }}:</span>
            <span class="ellipsis" style="width: 240px" :title="item.name">
              {{ item.name }}
            </span>
          </div>
          <div class="adoptionRate">
            {{ (item.adoptRate * 100).toFixed2(2) }}%
          </div>
        </div>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_lookAll"
          v-on="CetButton_lookAll.event"
        ></CetButton>
      </span>
    </CetDialog>
    <reservePlanDetail
      :visibleTrigger_in="ReservePlanDialog.show"
      :inputData_in="ReservePlanDialog.data"
    />
  </div>
</template>

<script>
import reservePlanDetail from "eem-components/expertComponents/order/ReservePlanDetail";
import customApi from "@/api/custom.js";
import { Message } from "element-ui";

export default {
  name: "DiagnosticAnalysisDialog",
  components: { reservePlanDetail },
  props: ["show_trigger_in", "condition"],
  data() {
    return {
      scenarios: [],
      eventInfo: {
        faultScenariosId_in: 0,
        eventClassified: 0,
        eventClassifiedName: ""
      },
      ReservePlanDialog: {
        show: new Date().getTime(),
        data: {}
      },
      // 1弹窗组件
      CetDialog_1: {
        title: $T("当前无预案"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        showClose: true
      },
      // lookAll组件
      CetButton_lookAll: {
        visible_in: true,
        disable_in: false,
        title: $T("查看所有预案"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_lookAll_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.close
        }
      }
    };
  },
  watch: {
    async show_trigger_in() {
      if (this.condition == null) {
        return;
      }

      const response = await customApi.getFaultScenariosByLastEvent(
        this.condition
      );

      if (response.code !== 0) {
        return;
      }

      const data = response.data;
      const scenarios = data?.eventplan_model;

      if (!scenarios?.length) {
        Message.error({
          message: $T("当前无预案"),
          duration: 5 * 1000
        });
        return;
      }

      this.CetDialog_1.title = data.name;
      this.scenarios = scenarios.map(scenario => {
        return {
          ...scenario,
          adoptRate: scenario.adoptRate === "NaN" ? 0 : scenario.adoptRate
        };
      });
      this.eventInfo.eventClassified = data.eventclassification;
      this.eventInfo.eventClassifiedName = data.eventClassificationName || "";
      this.eventInfo.faultScenariosId_in = data.id;
      this.open();
    }
  },
  methods: {
    open() {
      this.CetDialog_1.openTrigger_in = Date.now();
    },
    close() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    },
    CetButton_lookAll_statusTrigger_out() {
      this.close();
      this.$router.push({
        name: "accidentHandleAdministration",
        params: { ...this.eventInfo }
      });
    },
    showDetail(val, num) {
      this.ReservePlanDialog.data = { ...val, index: num };
      this.ReservePlanDialog.show = Date.now();
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  position: relative;
  .item {
    height: 60px;
    line-height: 60px;
    display: flex;
    @include font_color(ZS);
    justify-content: center;
    .name {
      display: flex;
      cursor: pointer;
    }
    .adoptionRate {
      width: 50px;
      height: 50px;
      line-height: 50px;
      font-size: 10px;
      text-align: center;
      @include background_color(ZS);
      border-radius: 50%;
      color: #fff;
      margin-top: 6px;
    }
  }
  .row {
    text-align: center;
    margin-top: 10px;
  }
}
.CetDialog {
  :deep(.el-dialog__body) {
    @include padding(J1);
    @include background_color(BG);
  }
}
</style>
