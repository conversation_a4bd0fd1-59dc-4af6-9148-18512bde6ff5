<template>
  <!-- 1弹窗组件 -->
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <el-container
      style="height: 100%; padding: 0px 16px 11px 12px; flex-direction: column"
    >
      <el-container style="height: 100%; padding: 0px 16px 11px 12px">
        <el-table
          :data="tableData"
          height="500"
          border
          style="width: 100%"
          stripe
        >
          <el-table-column
            label="序号"
            type="index"
            align="center"
            width="50"
          ></el-table-column>
          <el-table-column
            :prop="item.prop"
            :label="item.label"
            align="center"
            v-for="(item, index) in tableColumns"
            :key="index"
          >
            <template slot-scope="scope">
              <!-- 系数 -->
              <div v-if="item.prop == 'rate'">
                <ElInputNumber
                  v-model="scope.row[item.prop]"
                  v-bind="ElInputNumber_num"
                  v-on="ElInputNumber_num.event"
                ></ElInputNumber>
              </div>
              <span v-else>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-container>
    </el-container>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import { httping } from "@omega/http";
import common from "eem-utils/common";
export default {
  name: "shareRate",
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    // 配电室设备
    roomDeviceNodes: {
      type: Array
    },
    // 配电设备模型列表
    roomDeviceList: {
      type: Array
    }
  },

  data() {
    return {
      tableData: [],
      tableColumns: [
        {
          prop: "objectName",
          label: "配电室设备"
        },
        {
          prop: "supplytoName",
          label: "归属节点"
        },
        {
          prop: "rate",
          label: "供能系数"
        }
      ],
      CetDialog_1: {
        title: "设置供能系数",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "确认",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInputNumber_num: {
        value: "",
        style: {},
        controls: false,
        min: 0,
        max: 1,
        step: 0.01,
        precision: 2,
        controlsPosition: "", //right 可选值
        event: {}
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {
      this.CetDialog_1.queryId_in = val;
    },
    inputData_in(val) {
      this.CetDialog_1.inputData_in = val;
      this.getEnergysupplyto(val);
    }
  },

  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      let param = [];
      // 单个
      param = this.tableData.map(item => {
        return {
          id: item.id,
          rate: item.rate || 0,
          modelLabel: "energysupplyto"
        };
      });
      httping({
        url: "/eem-service/v1/common/write/hierachy",
        data: param,
        method: "POST"
      }).then(() => {
        this.$message.success("保存成功");
        this.CetDialog_1.closeTrigger_in = +new Date();
      });
    },
    // 获取当前节点关联的配电设备
    getEnergysupplyto(val) {
      if (!val) {
        return;
      }
      var param;
      var url;
      param = {
        rootCondition: {
          filter: {
            expressions: [
              {
                operator: "EQ", //数据库标识等于
                prop: "supplytolabel",
                limit: val.modelLabel
              },
              {
                operator: "EQ",
                prop: "supplytoid",
                limit: val.id
              },
              {
                operator: "IN",
                prop: "objectlabel",
                limit: this.roomDeviceList.map(item => item.modelLabel)
              }
            ]
          }
        },
        rootLabel: "energysupplyto",
        rootID: 0
      };
      url = `/eem-service/v1/common/query/oneLayer`;
      common.requestData(
        {
          url: url,
          data: param
        },
        (data, res) => {
          if (res.code === 0 && res.data && res.data.length > 0) {
            this.tableData = res.data;
            this.tableData.forEach(item => {
              item.supplytoName = val.name;
              item.objectName = this.findName(
                this.roomDeviceNodes,
                item.objectid,
                item.objectlabel
              );
              item.rate = item.rate || 1;
            });
          } else {
            this.tableData = [];
          }
        }
      );
    },
    // 找配电室设备名称
    findName(arr, id, modelLabel) {
      let name;
      arr.forEach(item => {
        if (item.id == id && item.modelLabel == modelLabel) {
          name = item.name;
        }
      });
      return name;
    }
  }
};
</script>
