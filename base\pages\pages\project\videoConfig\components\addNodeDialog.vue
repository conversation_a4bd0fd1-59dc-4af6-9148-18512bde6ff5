<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog v-bind="CetDialog_pagedialog" v-on="CetDialog_pagedialog.event">
      <template v-slot:footer>
        <span>
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <CetForm
        :data.sync="CetForm_pagedialog.data"
        v-bind="CetForm_pagedialog"
        v-on="CetForm_pagedialog.event"
      >
        <div class="bg1 brC2 rowBox">
          <el-form-item prop="name">
            <template slot="label">
              <span>{{ $T("视频分组节点名称") }}</span>
              <span class="required">*</span>
            </template>
            <ElInput
              v-model.trim="CetForm_pagedialog.data.name"
              v-bind="ElInput_name"
              v-on="ElInput_name.event"
            ></ElInput>
          </el-form-item>
        </div>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
import common from "eem-utils/common";

export default {
  name: "addNodeDialog",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    dialogTitle: {
      type: String
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: "",
        width: "320px",
        showClose: true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "component", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // this.$mess查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelPosition: "top",
        "hide-required-asterisk": true,
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入名称"),
              trigger: ["blur"]
            },
            common.check_name,
            common.pattern_name
          ]
        },
        event: {
          currentData_out: this.CetForm_pagedialog_currentData_out,
          saveData_out: this.CetForm_pagedialog_saveData_out,
          finishData_out: this.CetForm_pagedialog_finishData_out,
          finishTrigger_out: this.CetForm_pagedialog_finishTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // name组件
      ElInput_name: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_name_change_out,
          input: this.ElInput_name_input_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.CetDialog_pagedialog.title = this.dialogTitle;
      this.CetForm_pagedialog.data = this._.cloneDeep(this.inputData_in);
      this.CetForm_pagedialog.resetTrigger_in = new Date().getTime();
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {
      this.CetForm_pagedialog.queryId_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      this.CetForm_pagedialog.data = this._.cloneDeep(val);
    }
  },
  methods: {
    CetForm_pagedialog_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_saveData_out(val) {
      this.$emit("saveData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishData_out(val) {
      this.$emit("finishData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
      this.$emit("finishTrigger_out", val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetForm_pagedialog.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      this.CetForm_pagedialog.localSaveTrigger_in = new Date().getTime();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    no() {},
    // name输出,方法名要带_out后缀
    ElInput_name_change_out(val) {},
    ElInput_name_input_out(val) {}
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.el-dialog {
  .rowBox {
    @include padding(J3 J4);
  }
}
.required {
  @include font_color(Sta3);
}
</style>
