<template>
  <div class="card-info">
    <div class="info-left" style="width: 200px; float: left">
      <div class="info-left-img">
        <img
          style="width: 175px; height: 140px"
          v-if="imgSrc"
          :src="imgSrc"
          alt="暂无上传图片"
        />
        <img
          style="width: 175px; height: 140px"
          v-if="!imgSrc"
          src="../assets/cloud/u10806.png"
          alt="暂无上传图片"
        />
      </div>
      <div style="position: relative">
        <div class="info-left-safetyday">
          <span style="position: absolute; top: -35px; left: 30px; color: #fff">
            已安全运行
          </span>
          <span class="info-left-safetyday-num">
            {{ infoBoxData.safetyDay || "--" }}
          </span>
          天
        </div>
      </div>
    </div>
    <div class="info-cont" v-if="subprojectData.modelLabel === 'project'">
      <div style="height: 50px; line-height: 50px">
        <span
          class="info-cont-title fs-24 fl font-color"
          :title="infoBoxData.name"
        >
          {{ infoBoxData.name || "--" }}
        </span>
        <el-button
          class="ml10"
          type="primary"
          @click="handleDetail"
          size="mini"
        >
          详情
        </el-button>
      </div>
      <!-- <div style="height:50px;">
        <span style="display:block;">项目概览：</span>
        <span class="font-color">{{infoBoxData.projectabstract||"--"}}</span>
      </div> -->
      <div style="height: 50px; line-height: 50px">
        <span>合作状态：</span>
        <el-tag v-if="infoBoxData.status === 1">调试中</el-tag>
        <el-tag type="success" v-else-if="infoBoxData.status === 2">
          合作中
        </el-tag>
        <el-tag type="info" v-else-if="infoBoxData.status === 3">
          即将到期
        </el-tag>
        <el-tag type="danger" v-else-if="infoBoxData.status === 4">
          已到期
        </el-tag>
        <el-tag type="info" v-else>--</el-tag>
      </div>
      <div style="height: 50px; line-height: 50px">
        <span>截止时间：</span>
        <span>{{ infoBoxData.expiredDate$text || "--" }}</span>
      </div>
    </div>
    <div class="info-cont" v-else-if="subprojectData.modelLabel === 'building'">
      <div style="height: 50px; line-height: 50px">
        <span
          class="info-cont-title fs-24 fl font-color"
          :title="infoBoxData.name"
        >
          {{ infoBoxData.name || "--" }}
        </span>
        <el-button
          class="ml10"
          type="primary"
          @click="handleDetail"
          size="mini"
        >
          详情
        </el-button>
      </div>
      <div style="height: 50px; line-height: 50px">
        <span>合作状态：</span>
        <el-tag v-if="infoBoxData.status === 1">调试中</el-tag>
        <el-tag type="success" v-else-if="infoBoxData.status === 2">
          合作中
        </el-tag>
        <el-tag type="info" v-else-if="infoBoxData.status === 3">
          已将到期
        </el-tag>
        <el-tag type="danger" v-else-if="infoBoxData.status === 4">
          已到期
        </el-tag>
        <el-tag type="info" v-else>--</el-tag>
      </div>
      <div style="height: 50px; line-height: 50px">
        <span>截止时间：</span>
        <span>{{ infoBoxData.expiredDate$text || "--" }}</span>
      </div>
      <div style="height: 50px; line-height: 50px">
        <span>电压等级：</span>
        <span class="font-color">
          {{ infoBoxData.voltageLevel$Name || "--" }}
        </span>
      </div>
      <div style="height: 50px; line-height: 50px">
        <span>用能设施数量：</span>
        <span class="font-color">
          {{ infoBoxData.energyUsingDeviceNum || "--" }}
        </span>
      </div>
      <div style="height: 50px; line-height: 50px">
        <span>节能设置数量：</span>
        <span class="font-color">
          {{ infoBoxData.energySaveDeviceNum || "--" }}
        </span>
      </div>
      <div style="height: 50px; line-height: 50px">
        <span>今日用电量（kWh）：</span>
        <span class="font-color">
          {{
            infoBoxData.dayConsumption
              ? Number(infoBoxData.dayConsumption).toFixed(2)
              : "--"
          }}
        </span>
      </div>
      <div style="height: 50px; line-height: 50px">
        <span>本月用电量（kWh）：</span>
        <span class="font-color">
          {{
            infoBoxData.monthConsumption
              ? Number(infoBoxData.monthConsumption).toFixed(2)
              : "--"
          }}
        </span>
      </div>
    </div>
    <div class="info-cont" v-else-if="subprojectData.modelLabel === 'room'">
      <div style="height: 50px; line-height: 50px">
        <span
          class="info-cont-title fs-24 fl font-color"
          :title="infoBoxData.name"
        >
          {{ infoBoxData.name || "--" }}
        </span>
        <!-- <el-button class="ml10" type="primary"  @click="handleDetail" size="mini">详情</el-button> -->
      </div>
      <div style="height: 50px; line-height: 50px">
        <span>合作状态：</span>
        <el-tag v-if="infoBoxData.status === 1">调试中</el-tag>
        <el-tag type="success" v-else-if="infoBoxData.status === 2">
          合作中
        </el-tag>
        <el-tag type="info" v-else-if="infoBoxData.status === 3">
          已将到期
        </el-tag>
        <el-tag type="danger" v-else-if="infoBoxData.status === 4">
          已到期
        </el-tag>
        <el-tag type="info" v-else>--</el-tag>
      </div>
      <div style="height: 50px; line-height: 50px">
        <span>截止时间：</span>
        <span>{{ infoBoxData.expiredDate$text || "--" }}</span>
      </div>
      <div style="height: 50px; line-height: 50px">
        <span>电压等级：</span>
        <span class="font-color">
          {{ infoBoxData.voltageLevel$Name || "--" }}
        </span>
      </div>
      <div style="height: 50px; line-height: 50px">
        <span>配电设施数量：</span>
        <span class="font-color">
          {{ infoBoxData.powerRoomDeviceCount || "--" }}台
        </span>
      </div>
      <!-- <div style="height:50px;line-height:50px;">
        <span>节能设置数量：</span>
        <span class="font-color">{{infoBoxData.energySaveDeviceNum||"--"}}</span>
      </div> -->
      <div
        class="info-events"
        style="height: 80px"
        v-if="
          infoBoxData.daemonShowTips && infoBoxData.daemonShowTips.length > 0
        "
      >
        <table v-for="(list, index) in infoBoxData.daemonShowTips" :key="index">
          <tr>
            <td class="cet-theme-font">各进线需量：</td>
            <td class="cet-theme-font" colspan="2">{{ list.name || "--" }}</td>
          </tr>
          <tr>
            <td align="center">
              <span class="cet-theme-font">{{ list.current || "--" }}</span>
              <span class="cet-theme-font">实时需量（kW）</span>
            </td>
            <td align="center">
              <span class="cet-theme-font">{{ list.max || "--" }}</span>
              <span class="cet-theme-font">本月最大需量（kW）</span>
            </td>
            <td align="center">
              <span class="cet-theme-font">{{ list.maxTime || "--" }}</span>
              <span class="cet-theme-font">本月最大需量时间（kW）</span>
            </td>
          </tr>
        </table>
      </div>
      <div class="info-events" style="color: #999" v-else>无需量数据</div>
      <div
        class="info-events"
        style="height: 80px"
        v-if="
          infoBoxData.powerTransfomerShowTips &&
          infoBoxData.powerTransfomerShowTips.length > 0
        "
      >
        <table
          v-for="(list, index) in infoBoxData.powerTransfomerShowTips"
          :key="index"
        >
          <tr>
            <td class="cet-theme-font">各变压器负载率：</td>
            <td class="cet-theme-font" colspan="2">{{ list.name || "--" }}</td>
          </tr>
          <tr>
            <td align="center">
              <span class="cet-theme-font">{{ list.capacity || "--" }}</span>
              <span class="cet-theme-font">变压器容量（kVA）</span>
            </td>
            <td align="center">
              <span class="cet-theme-font">{{ list.load || "--" }}</span>
              <span class="cet-theme-font">实时负荷（kW）</span>
            </td>
            <td align="center">
              <span class="cet-theme-font">
                {{
                  list.loadRate
                    ? (Number(list.loadRate) * 100).toFixed(2)
                    : "--"
                }}
              </span>
              <span class="cet-theme-font">实时负荷率（%）</span>
            </td>
          </tr>
        </table>
      </div>
      <div class="info-events" style="color: #999" v-else>无变压器数据</div>
    </div>
    <div
      class="info-cont"
      v-else-if="
        subprojectData.modelLabel === 'powertransformer' ||
        subprojectData.modelLabel === 'linesegmentwithswitch'
      "
    >
      <div style="height: 50px; line-height: 50px">
        <span
          class="info-cont-title fs-24 fl font-color"
          :title="infoBoxData.name"
        >
          {{ infoBoxData.name || "--" }}
        </span>
        <el-button
          class="ml10"
          type="primary"
          @click="handleDetail"
          size="mini"
        >
          详情
        </el-button>
      </div>
      <div style="height: 50px; line-height: 50px">
        <span class="cont-label">电压等级：</span>
        <span class="cont-label-text font-color">
          {{ infoBoxData.voltageLevel$Name || "--" }}
        </span>
      </div>
      <div style="height: 50px; line-height: 50px">
        <span class="cont-label">生命电池：</span>
        <span class="cont-label-text font-color">
          {{ infoBoxData.remainLife || "--" }}
        </span>
      </div>
      <div style="height: 50px; line-height: 50px">
        <span class="cont-label">设备名称：</span>
        <span class="cont-label-text font-color" :title="infoBoxData.name">
          {{ infoBoxData.name || "--" }}
        </span>
      </div>
      <table
        width="100%"
        border="0"
        cellspacing="1"
        cellpadding="4"
        bgcolor="#cccccc"
        class="cont-tab"
      >
        <tr>
          <td class="cet-theme-font" align="center">编号</td>
          <td class="cet-theme-font font-color">
            {{ infoBoxData.code || "--" }}
          </td>
          <td class="cet-theme-font" align="center">PT变比</td>
          <td class="cet-theme-font font-color">
            {{ infoBoxData.ptratio || "--" }}
          </td>
        </tr>
        <tr>
          <td class="cet-theme-font" align="center">型号</td>
          <td class="cet-theme-font font-color">
            {{ infoBoxData.model || "--" }}
          </td>
          <td class="cet-theme-font" align="center">短路阻抗(Ω)</td>
          <td class="cet-theme-font font-color">
            {{ infoBoxData.shortcircuitimpedance || "--" }}
          </td>
        </tr>
        <tr>
          <td class="cet-theme-font" align="center">设备厂家</td>
          <td class="cet-theme-font font-color">
            {{ infoBoxData.vendor || "--" }}
          </td>
          <td class="cet-theme-font" align="center">空载电流(A)</td>
          <td class="cet-theme-font font-color">
            {{ infoBoxData.noloadcurrent || "--" }}
          </td>
        </tr>
        <tr>
          <td class="cet-theme-font" align="center">额定容量(kVA)</td>
          <td class="cet-theme-font font-color">
            {{ infoBoxData.ratedcapacity || "--" }}
          </td>
          <td class="cet-theme-font" align="center">空载损耗(kW)</td>
          <td class="cet-theme-font font-color">
            {{ infoBoxData.noloadloss || "--" }}
          </td>
        </tr>
        <tr>
          <td class="cet-theme-font" align="center">额定电压(kV)</td>
          <td class="cet-theme-font font-color">
            {{ infoBoxData.ratedvoltage || "--" }}
          </td>
          <td class="cet-theme-font" align="center">短路损耗(kW)</td>
          <td class="cet-theme-font font-color">
            {{ infoBoxData.shortcircuitloss || "--" }}
          </td>
        </tr>
        <tr>
          <td class="cet-theme-font" align="center">额定电流(A)</td>
          <td class="cet-theme-font font-color">
            {{ infoBoxData.ratedcurrent || "--" }}
          </td>
          <td class="cet-theme-font" align="center"></td>
          <td class="cet-theme-font font-color"></td>
        </tr>
      </table>
    </div>
    <div class="info-cont" v-else>
      <div style="height: 50px; line-height: 50px">
        <span
          class="info-cont-title fs-24 fl font-color"
          :title="infoBoxData.name"
        >
          {{ infoBoxData.name || "--" }}
        </span>
        <el-button
          class="ml10"
          type="primary"
          @click="handleDetail"
          size="mini"
        >
          详情
        </el-button>
      </div>
      <div style="height: 40px; line-height: 40px">
        <span>电压等级：</span>
        <span class="font-color">
          {{ infoBoxData.voltageLevel$Name || "--" }}
        </span>
      </div>
      <div style="height: 40px; line-height: 40px">
        <span>生命电池：</span>
        <span class="font-color">{{ infoBoxData.remainLife || "--" }}</span>
      </div>
      <div style="height: 40px; line-height: 40px">
        <span>设备名称：</span>
        <span class="font-color" :title="infoBoxData.name">
          {{ infoBoxData.name || "--" }}
        </span>
      </div>
      <table
        width="100%"
        border="0"
        cellspacing="1"
        cellpadding="4"
        bgcolor="#cccccc"
        class="cont-tab"
      >
        <tr>
          <td class="cet-theme-font">编号</td>
          <td class="cet-theme-font">{{ infoBoxData.code || "--" }}</td>
          <td class="cet-theme-font">电压比</td>
          <td class="cet-theme-font">{{ infoBoxData.ptratio || "--" }}</td>
        </tr>
        <tr>
          <td class="cet-theme-font">型号</td>
          <td class="cet-theme-font">{{ infoBoxData.model || "--" }}</td>
          <td class="cet-theme-font">短路电压</td>
          <td class="cet-theme-font">{{ infoBoxData.ratedvoltage || "--" }}</td>
        </tr>
        <tr>
          <td class="cet-theme-font">厂家</td>
          <td class="cet-theme-font">{{ infoBoxData.vendor || "--" }}</td>
          <td class="cet-theme-font">空载电流</td>
          <td class="cet-theme-font">
            {{ infoBoxData.noloadcurrent || "--" }}
          </td>
        </tr>
        <tr>
          <td class="cet-theme-font">容器</td>
          <td class="cet-theme-font">
            {{ infoBoxData.ratedcapacity || "--" }}
          </td>
          <td class="cet-theme-font">空载损耗</td>
          <td class="cet-theme-font">{{ infoBoxData.noloadloss || "--" }}</td>
        </tr>
        <tr>
          <td class="cet-theme-font">电压</td>
          <td class="cet-theme-font">{{ infoBoxData.ratedvoltage || "--" }}</td>
          <td class="cet-theme-font">短路损耗</td>
          <td class="cet-theme-font">
            {{ infoBoxData.shortcircuitloss || "--" }}
          </td>
        </tr>
        <tr>
          <td class="cet-theme-font">电流</td>
          <td class="cet-theme-font">{{ infoBoxData.ratedcurrent || "--" }}</td>
          <td class="cet-theme-font"></td>
          <td class="cet-theme-font"></td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
import { httping } from "@omega/http";
export default {
  name: "infobox",
  components: {},
  props: ["subprojectData", "transformData", "eventList"],
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  data() {
    return {
      infoBoxData: {},
      statusList: {
        1: "调试中",
        2: "合作中",
        3: "即将到期",
        4: "已到期"
      },
      imgSrc: null
    };
  },
  watch: {
    transformData: function (val) {
      var _this = this;
      _this.filInfoBoxData(val);
      _this.$nextTick(function () {
        _this.$emit("dataChange");
      });
    }
  },
  methods: {
    getRoundNum(minNum, maxNum) {
      return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10);
    },
    filInfoBoxData(data) {
      var _this = this;
      // if (this.subprojectData.modelLabel === "project") {
      if (data.expiredDate) {
        var dd = new Date(data.expiredDate),
          YY = dd.getFullYear(),
          MM = dd.getMonth() + 1,
          DD = dd.getDate();
        if (MM < 10) {
          MM = "0" + MM;
        }
        if (DD < 10) {
          DD = "0" + DD;
        }
        data.expiredDate$text = YY + "年" + MM + "月" + DD + "日";
      } else {
        data.expiredDate$text = "--";
      }
      var status = data.status;
      data.status$text = this.statusList[status];
      var remainLife = Number(data.remainLife) * 100;
      data.remainLife = remainLife.toFixed(2) + "%";
      // }

      _this.infoBoxData = data;
      _this.imgSrc = null;
      _this.getImgUrl(data);
    },
    getImgUrl: function (val) {
      var me = this;
      var uploadPath = val.pic || val.picture;
      if (!uploadPath) {
        return;
      }
      var url = "/eem-service/v1/common/downloadFile?path=" + uploadPath;
      if (!url) {
        return;
      }
      const xhr = new XMLHttpRequest();
      xhr.open("GET", url, true);
      xhr.responseType = "blob";
      xhr.setRequestHeader("Authorization", this.token);
      xhr.onload = () => {
        if (
          xhr.status === 200 &&
          xhr.response.type === "application/x-download"
        ) {
          //将图片信息放到Img中
          me.imgSrc = window.URL.createObjectURL(xhr.response);
        }
      };

      xhr.send();
    },
    //点击浮动框详情
    handleDetail() {
      // this.$router.push({path: '/energydata'});
      var params = this.subprojectData;
      if (params.modelLabel === "room" && params.roomtype === 1) {
        this.$message.warning("请选择配电室之外接口！");
        return;
      }
      var data = {
        rootID: params.id,
        rootLabel: params.modelLabel,
        subLayerConditions: [{ modelLabel: "project" }]
      };
      httping({
        url: "/eem-service/v1/node/nodeTree",
        method: "POST",
        data
      }).then(response => {
        if (response.code == 0 && response.data) {
          var id, tenantId, projectInfo;
          if (params.modelLabel == "project") {
            projectInfo = response.data[0];
            id = response.data[0].id;
            tenantId = response.data[0].tenantId;
          } else {
            projectInfo = response.data[0].project_model[0];
            id = response.data[0].project_model[0].id;
            tenantId = response.data[0].project_model[0].tenantId;
          }
          this.$router.push({ name: "energydata", params });
          this.$store.commit("setProjectId", Number(id));
          this.$store.commit("setProjectTenantId", Number(tenantId));
          this.$store.commit("setProjectInfo", projectInfo);
        }
      });
    }
  },
  created() {}
};
</script>

<style scoped>
* {
  font-size: 13px;
}

.card-info {
  background: #fff;
  padding: 15px 10px 10px;
  width: 500px;
}
.info-left {
  width: 200px;
  float: left;
}
.info-left-img {
  width: 175px;
  height: 140px;
  /* background: url(../assets/cloud/u10806.png) no-repeat center;   */
}
.info-left-safetyday {
  width: 124px;
  height: 128px;
  background: url(../assets/cloud/u10809.png) no-repeat center;
  position: absolute;
  left: 25px;
  top: -45px;
  line-height: 128px;
  text-align: center;
}
.info-left-safetyday-num {
  color: #0441f5;
  font-size: 24px;
}
.info-cont {
  width: calc(100% - 200px);
  float: left;
}
.info-cont-title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 200px;
}
.fs-24 {
  font-size: 24px;
}
.font-color {
  color: #0441f5;
}

.station {
  overflow: hidden;
  height: 20px;
  line-height: 20px;
}

.stationName {
  float: left;
  max-width: 270px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.eventNum {
  float: right;
  margin-right: 15px;
}

.address {
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.info-events {
  border-width: 1px 0;
  border-color: #b6b6b6;
  border-style: solid;
  margin: 8px 0;
  height: 44px;
  overflow-y: auto;
}

.info-events table {
  width: 100%;
}

.info-events table tr {
  height: 22px;
}

.eventClass {
  display: block;
  width: 13px;
  height: 13px;
}

.info-dataTable {
  width: 100%;
  max-height: 130px;
  overflow-y: auto;
  position: relative;
  top: -5px;
}
.info-dataTable td {
  text-align: center;
  padding: 1px;
}

.info-dataTable td div {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.transformName {
  width: 80px;
}

.transformCapacity {
  width: 120px;
}

.realtimeLoad {
  width: 100px;
}

.loadFactor {
  width: 80px;
}
.cont-tab td {
  background-color: #ffffff;
  height: 25px;
  line-height: 150%;
}
.cet-theme-font {
  overflow: hidden;
  max-width: 120px;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.cont-label {
  width: 70px;
  float: left;
}
.cont-label-text {
  width: calc(100% - 70px);
  float: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
