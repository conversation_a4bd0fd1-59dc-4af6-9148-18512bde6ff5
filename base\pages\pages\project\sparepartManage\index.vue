<template>
  <div class="page eem-common">
    <el-container class="fullheight">
      <el-aside width="350px" class="eem-aside flex-column">
        <div class="flex-auto">
          <CetTree
            :selectNode.sync="CetTree_1.selectNode"
            :checkedNodes.sync="CetTree_1.checkedNodes"
            v-bind="CetTree_1"
            v-on="CetTree_1.event"
          ></CetTree>
        </div>
        <div class="clearfix">
          <!-- edit按钮组件 -->
          <CetButton
            class="fr mlJ1 mtJ3"
            v-bind="CetButton_edit"
            v-on="CetButton_edit.event"
          ></CetButton>
          <!-- delete按钮组件 -->
          <CetButton
            class="fr mlJ1 mtJ3"
            v-bind="CetButton_delete"
            v-on="CetButton_delete.event"
          ></CetButton>
          <!-- add按钮组件 -->
          <CetButton
            class="fr mlJ1 mtJ3"
            v-bind="CetButton_addSparepart"
            v-on="CetButton_addSparepart.event"
          ></CetButton>
          <!-- addSystem按钮组件 -->
          <CetButton
            class="fr mtJ3"
            v-bind="CetButton_addSystem"
            v-on="CetButton_addSystem.event"
          ></CetButton>
        </div>
      </el-aside>
      <el-container class="fullheight padding0 mlJ3 flex-column">
        <div class="mbJ3">
          <div class="common-title-H1 fl">
            {{ CetTree_1.selectNode && CetTree_1.selectNode.name }}
          </div>
        </div>
        <div class="flex-auto eem-container flex-column">
          <div class="clearfix mbJ3">
            <!-- add按钮组件 -->
            <CetButton
              class="fr"
              v-bind="CetButton_add"
              v-on="CetButton_add.event"
            ></CetButton>
            <CetButton
              class="mrJ1 fr"
              v-bind="CetButton_mulDelete"
              v-on="CetButton_mulDelete.event"
            ></CetButton>
            <!-- 同步按钮组件 -->
            <el-tooltip
              effect="light"
              :content="
                $T(
                  '将选择节点的零件，导入到与该零件所属设备对应的备件分类的备件库中'
                )
              "
            >
              <CetButton
                class="mrJ1 fr"
                v-bind="CetButton_synchronize"
                v-on="CetButton_synchronize.event"
              ></CetButton>
            </el-tooltip>
            <ElInput
              class="mrJ1 fr"
              v-model="ElInput_search.value"
              v-bind="ElInput_search"
              v-on="ElInput_search.event"
            ></ElInput>
          </div>
          <CetTable
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
            class="text-right flex-auto"
            @selection-change="handleSelectionChange_out"
          >
            <ElTableColumn
              type="selection"
              width="50"
              align="center"
            ></ElTableColumn>
            <template v-for="item in Columns_sparepart">
              <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
            </template>
            <ElTableColumn v-bind="ElTableColumn_operate">
              <template slot-scope="{ row }">
                <span @click="handleEdit(row)" class="fl mrJ3 headle">
                  {{ $T("编辑") }}
                </span>
                <span @click="handleDelete(row)" class="fl headle delete">
                  {{ $T("删除") }}
                </span>
              </template>
            </ElTableColumn>
          </CetTable>
        </div>
      </el-container>
    </el-container>

    <!-- 新增系统 -->
    <addSystemDialog
      v-bind="addSystemDialog"
      v-on="addSystemDialog.event"
    ></addSystemDialog>
    <!-- 编辑系统 -->
    <editSystemDialog
      v-bind="editSystemDialog"
      v-on="editSystemDialog.event"
    ></editSystemDialog>
    <!-- 新增设备 -->
    <addDeviceDialog
      v-bind="addDeviceDialog"
      v-on="addDeviceDialog.event"
    ></addDeviceDialog>
    <!-- 编辑设备 -->
    <editDeviceDialog
      v-bind="editDeviceDialog"
      v-on="editDeviceDialog.event"
    ></editDeviceDialog>
    <!-- 新增备件 -->
    <addSparePartDialog
      v-bind="addSparePartDialog"
      v-on="addSparePartDialog.event"
    ></addSparePartDialog>
    <!-- 编辑备件 -->
    <editSparePartDialog
      v-bind="editSparePartDialog"
      v-on="editSparePartDialog.event"
    ></editSparePartDialog>
    <!-- 同步备件到设备 -->
    <synchronizeDialog
      v-bind="synchronizeDialog"
      v-on="synchronizeDialog.event"
    ></synchronizeDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import addSystemDialog from "./dialogs/addSystemDialog";
import editSystemDialog from "./dialogs/editSystemDialog";
import addDeviceDialog from "./dialogs/addDeviceDialog";
import editDeviceDialog from "./dialogs/editDeviceDialog";
import addSparePartDialog from "./dialogs/addSparepartDialog";
import editSparePartDialog from "./dialogs/editSparepartDialog";
import synchronizeDialog from "./dialogs/synchronizeDialog";

export default {
  name: "sparepartManage",
  components: {
    addSystemDialog,
    editSystemDialog,
    addDeviceDialog,
    editDeviceDialog,
    addSparePartDialog,
    editSparePartDialog,
    synchronizeDialog
  },
  data() {
    const en = window.localStorage.getItem("omega_language") === "en";
    return {
      // 1树组件
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "sparePartsDeviceVoList"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out,
          parentList_out: this.CetTree_1_parentList_out
        }
      },
      CetButton_delete: {
        visible_in: true,
        disable_in: false,
        title: $T("删除"),
        type: "danger",
        plain: true,
        size: "mini",
        event: {
          statusTrigger_out: this.CetButton_delete_statusTrigger_out
        }
      },
      CetButton_edit: {
        visible_in: true,
        disable_in: false,
        title: $T("编辑"),
        type: "primary",
        size: "mini",
        event: {
          statusTrigger_out: this.CetButton_edit_statusTrigger_out
        }
      },
      // addSystem组件
      CetButton_addSystem: {
        visible_in: true,
        disable_in: false,
        title: $T("新增同级"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_addSystem_statusTrigger_out
        }
      },
      CetButton_addSparepart: {
        visible_in: true,
        disable_in: true,
        title: $T("新增子级"),
        type: "primary",
        plain: true,
        size: "mini",
        event: {
          statusTrigger_out: this.CetButton_addSparepart_statusTrigger_out
        }
      },
      // search组件
      ElInput_search: {
        value: "",
        style: {
          width: "200px"
        },
        "suffix-icon": "el-icon-search",
        placeholder: $T("请输入备件名称"),
        size: "small",
        event: {
          change: this.ElInput_search_change_out
        }
      },
      // synchronize组件
      CetButton_synchronize: {
        visible_in: true,
        disable_in: false,
        title: $T("同步"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_synchronize_statusTrigger_out
        }
      },
      // add组件
      CetButton_add: {
        visible_in: true,
        disable_in: true,
        title: $T("新增"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      // 批量删除备件组件
      CetButton_mulDelete: {
        visible_in: true,
        disable_in: true,
        title: $T("批量删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_mulDelete_statusTrigger_out
        }
      },
      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      Columns_sparepart: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          //minWidth: "200",  //该宽度会自适应
          width: "60" //绝对宽度
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("备件名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: this.formatter
          // width: "160" //绝对宽度
        },
        {
          prop: "model", // 支持path a[0].b
          label: $T("规格型号"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        },
        {
          prop: "unit", // 支持path a[0].b
          label: $T("单位"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          // width: "160" //绝对宽度
          formatter: this.formatter
        }
      ],
      ElTableColumn_operate: {
        //type: "",      // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: en ? "150" : "100" //绝对宽度
      },
      addSystemDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: {},
        event: {
          saveData_out: this.saveAddDeviceSystem
        }
      },
      editSystemDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: {},
        event: {
          saveData_out: this.saveEditDeviceSystem
        }
      },
      addDeviceDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: {},
        event: {
          saveData_out: this.saveAddSparePartsDevice
        }
      },
      editDeviceDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: {},
        event: {
          saveData_out: this.saveEditSparePartsDevice
        }
      },
      addSparePartDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: {},
        event: {
          saveData_out: this.saveAddSpareParts
        }
      },
      editSparePartDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: {},
        event: {
          saveData_out: this.saveEditSpareParts
        }
      },
      synchronizeDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: {},
        event: {
          refresh: this.getSparePartsList
        }
      },
      systemId: null,
      selectedData: [] // 表格多选数据
    };
  },
  watch: {
    "CetTree_1.selectNode": {
      immediate: true,
      deep: true,
      handler(val) {
        if (this._.isEmpty(val)) {
          this.CetButton_edit.disable_in = true;
          this.CetButton_delete.disable_in = true;
          this.CetTable_1.data = [];
        } else {
          this.CetButton_edit.disable_in = false;
          this.CetButton_delete.disable_in = false;
        }
      }
    }
  },
  methods: {
    // 1 输出
    CetTree_1_currentNode_out(val) {
      this.ElInput_search.value = "";
      this.CetTable_1.data = [];
      if (val.modelLabel === "sparepartsdevice") {
        // 设备下不能再新增了
        this.CetButton_addSparepart.disable_in = true;
        this.getSparePartsList();
        this.CetButton_add.disable_in = false;
      } else {
        this.CetButton_addSparepart.disable_in = false;
        this.CetButton_add.disable_in = true;
      }
    },
    CetTree_1_parentList_out(val) {
      const target = val.find(item => item.modelLabel === "devicesystem");
      if (target) {
        this.systemId = target.id;
      }
    },
    CetButton_delete_statusTrigger_out() {
      this.$confirm($T("确定要删除所选项吗？"), $T("删除确认"), {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消")
      }).then(() => {
        if (this.CetTree_1.selectNode.modelLabel === "devicesystem") {
          // 删除系统
          customApi
            .deleteDeviceSystem([this.CetTree_1.selectNode.id])
            .then(response => {
              if (response.code === 0) {
                this.$message.success($T("删除成功!"));
                this.getDeviceSystem();
              }
            });
        } else if (
          this.CetTree_1.selectNode.modelLabel === "sparepartsdevice"
        ) {
          // 删除设备
          customApi
            .deleteSparePartsDevice(
              [this.CetTree_1.selectNode.id],
              this.systemId
            )
            .then(response => {
              if (response.code === 0) {
                this.$message.success($T("删除成功!"));
                this.getDeviceSystem();
              }
            });
        }
      });
    },
    CetButton_edit_statusTrigger_out() {
      if (this.CetTree_1.selectNode.modelLabel === "devicesystem") {
        // 编辑系统
        this.editSystemDialog.inputData_in = this.CetTree_1.selectNode;
        this.editSystemDialog.openTrigger_in = new Date().getTime();
      } else if (this.CetTree_1.selectNode.modelLabel === "sparepartsdevice") {
        // 编辑设备
        this.editDeviceDialog.inputData_in = this.CetTree_1.selectNode;
        this.editDeviceDialog.openTrigger_in = new Date().getTime();
      }
    },
    // 新增同级
    CetButton_addSystem_statusTrigger_out() {
      if (
        !this.CetTree_1.selectNode ||
        this.CetTree_1.selectNode.modelLabel === "devicesystem"
      ) {
        // 新增备件系统
        this.addSystemDialog.openTrigger_in = new Date().getTime();
      } else if (this.CetTree_1.selectNode.modelLabel === "sparepartsdevice") {
        // 新增备件分类
        this.addDeviceDialog.openTrigger_in = new Date().getTime();
      }
    },
    // 新增子级
    CetButton_addSparepart_statusTrigger_out() {
      this.addDeviceDialog.openTrigger_in = new Date().getTime();
    },
    // search输出,方法名要带_out后缀
    ElInput_search_change_out() {
      if (
        this.CetTree_1.selectNode &&
        this.CetTree_1.selectNode.modelLabel === "sparepartsdevice"
      ) {
        this.getSparePartsList();
      }
    },
    // synchronize输出
    CetButton_synchronize_statusTrigger_out() {
      this.synchronizeDialog.openTrigger_in = new Date().getTime();
    },
    // add输出
    CetButton_add_statusTrigger_out() {
      if (!this.CetTree_1.selectNode) {
        this.$message.warning($T("请选择备件"));
        return;
      }
      this.addSparePartDialog.openTrigger_in = new Date().getTime();
    },
    //表格点击多选框
    handleSelectionChange_out(val) {
      this.selectedData = val;
      this.CetButton_mulDelete.disable_in = Boolean(!(val && val.length));
    },
    // 批量删除
    CetButton_mulDelete_statusTrigger_out() {
      this.$confirm($T("确定要批量删除备件？"), $T("删除确认"), {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消")
      }).then(() => {
        const idsArr = this.selectedData.map(item => item.id);
        customApi
          .deleteSpareParts(idsArr, this.CetTree_1.selectNode.id)
          .then(response => {
            if (response.code === 0) {
              this.$message.success($T("删除成功!"));
              this.getSparePartsList();
            }
          });
      });
    },
    formatter(row, column, cellValue) {
      return cellValue || "--";
    },
    handleEdit(row) {
      this.editSparePartDialog.inputData_in = row;
      this.editSparePartDialog.openTrigger_in = new Date().getTime();
    },
    handleDelete(row) {
      this.$confirm($T("确定要删除所选项吗？"), $T("删除确认"), {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消")
      }).then(() => {
        customApi
          .deleteSpareParts([row.id], this.CetTree_1.selectNode.id)
          .then(response => {
            if (response.code === 0) {
              this.$message.success($T("删除成功!"));
              this.getSparePartsList();
            }
          });
      });
    },
    // 获取系统和设备节点树
    getDeviceSystem(flag) {
      customApi.queryDeviceSystem().then(res => {
        if (res.code === 0) {
          // 给每个节点加tree_id
          const arr = this._.cloneDeep(res.data);
          const loop = param =>
            param.forEach(item => {
              const str = item.modelLabel + "_" + item.id;
              this.$set(item, "tree_id", str);
              if (
                item.sparePartsDeviceVoList &&
                item.sparePartsDeviceVoList.length
              ) {
                loop(item.sparePartsDeviceVoList);
              }
            });
          loop(arr);
          this.CetTree_1.inputData_in = arr;
          // callback && this[callback]();
          this.setSelectNode(flag);
        }
      });
    },
    setSelectNode(flag) {
      const deviceArr = [];
      const loopDevice = param =>
        param.forEach(item => {
          deviceArr.push(item);
          if (
            item.sparePartsDeviceVoList &&
            item.sparePartsDeviceVoList.length
          ) {
            loopDevice(item.sparePartsDeviceVoList);
          }
        });
      loopDevice(this.CetTree_1.inputData_in);
      if (flag) {
        // 设置当前选中的
        this.CetTree_1.selectNode = deviceArr.find(
          item =>
            item.tree_id ===
            this.CetTree_1.selectNode.modelLabel +
              "_" +
              this.CetTree_1.selectNode.id
        );
      } else {
        // 选中第一个设备节点 modelLabel = sparepartsdevice
        this.CetTree_1.selectNode = deviceArr.find(
          item => item.modelLabel === "sparepartsdevice"
        );
      }
    },
    // 新增系统
    saveAddDeviceSystem(data) {
      customApi.addDeviceSystem(data).then(res => {
        if (res.code === 0) {
          this.$message.success($T("保存成功!"));
          this.addSystemDialog.closeTrigger_in = new Date().getTime();
          this.getDeviceSystem(true);
        }
      });
    },
    // 编辑系统
    saveEditDeviceSystem(data) {
      customApi.editDeviceSystem(data).then(res => {
        if (res.code === 0) {
          this.$message.success($T("保存成功!"));
          this.editSystemDialog.closeTrigger_in = new Date().getTime();
          this.getDeviceSystem(true);
        }
      });
    },
    // 新增设备
    saveAddSparePartsDevice(data) {
      customApi
        .addSparePartsDevice({ ...data, deviceSystemId: this.systemId })
        .then(res => {
          if (res.code === 0) {
            this.$message.success($T("保存成功!"));
            this.addDeviceDialog.closeTrigger_in = new Date().getTime();
            this.getDeviceSystem(true);
          }
        });
    },
    // 编辑设备
    saveEditSparePartsDevice(data) {
      customApi
        .editSparePartsDevice({ ...data, deviceSystemId: this.systemId })
        .then(res => {
          if (res.code === 0) {
            this.$message.success($T("保存成功!"));
            this.editDeviceDialog.closeTrigger_in = new Date().getTime();
            this.getDeviceSystem(true);
          }
        });
    },
    // 获取备件库列表
    getSparePartsList() {
      if (this.CetTree_1.selectNode.modelLabel !== "sparepartsdevice") return;
      const params = {
        id: this.CetTree_1.selectNode.id,
        keyWord: this.ElInput_search.value
      };
      customApi.querySparePartsList(params).then(res => {
        if (res.code === 0) {
          this.CetTable_1.data = res.data;
        }
      });
    },
    // 新增备件
    saveAddSpareParts(val) {
      customApi
        .addSpareParts({
          ...val,
          sparePartsDeviceId: this.CetTree_1.selectNode.id
        })
        .then(res => {
          if (res.code === 0) {
            this.$message.success($T("保存成功!"));
            this.addSparePartDialog.closeTrigger_in = new Date().getTime();
            this.getSparePartsList();
          }
        });
    },
    // 编辑备件
    saveEditSpareParts(val) {
      customApi
        .editSpareParts({
          ...val,
          sparePartsDeviceId: this.CetTree_1.selectNode.id
        })
        .then(res => {
          if (res.code === 0) {
            this.$message.success($T("保存成功!"));
            this.editSparePartDialog.closeTrigger_in = new Date().getTime();
            this.getSparePartsList();
          }
        });
    }
  },
  activated() {
    this.getDeviceSystem();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.headle {
  cursor: pointer;
  @include font_color(ZS);
  &.delete {
    @include font_color(Sta3);
  }
}
</style>
