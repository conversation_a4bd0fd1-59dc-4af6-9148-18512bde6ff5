import fetch from "eem-utils/fetch";
let version = "v1";

// 单压缩机调优-运行参数分析
export function queryParameterAnalysis(data) {
  return fetch({
    url: `/piem/${version}/compressoroptimization/singlecompressor/parameter/analysis`,
    method: `POST`,
    data
  });
}

// 单压缩机调优-单压缩机调优-参数相关性以及策略
export function queryParameterCorrelationAndStrategy(data) {
  return fetch({
    url: `/piem/${version}/compressoroptimization/singlecompressor/parameter/correlation/strategy`,
    method: `POST`,
    data
  });
}

// 多压缩机调优-气量区间
export function queryAirInterval(data) {
  return fetch({
    url: `/piem/${version}/compressoroptimization/compressorunit/interval/query`,
    method: `POST`,
    data
  });
}

// 多压缩机调优-气量区间录入
export function setAirInterval(data) {
  return fetch({
    url: `/piem/${version}/compressoroptimization/compressorunit/interval/input`,
    method: `POST`,
    data
  });
}

// 多压缩机调优-稳定性查询
export function queryStability(data) {
  return fetch({
    url: `/piem/${version}/compressoroptimization/compressorunit/stability/query`,
    method: `POST`,
    data
  });
}

// 多压缩机调优-稳定性录入
export function setStability(data) {
  return fetch({
    url: `/piem/${version}/compressoroptimization/compressorunit/stability/input`,
    method: `POST`,
    data
  });
}

// 多压缩机调优-运行参数分析
export function queryMultipleMachineParameterAnalysis(data) {
  return fetch({
    url: `/piem/${version}/compressoroptimization/compressorunit/parameter/analysis`,
    method: `POST`,
    data
  });
}

// 多压缩机调优-查询运行天数和策略
export function queryRunningDaysAndStrategy(data) {
  return fetch({
    url: `/piem/${version}/compressoroptimization/compressorunit/runningday/strategy`,
    method: `POST`,
    data
  });
}

// 处理气量调优-数据拟合
export function gasDataFitting(data) {
  return fetch({
    url: `/piem/${version}/compressoroptimization/gasoptimize/datafitting`,
    method: `POST`,
    data
  });
}

// 处理气量调优-能效趋势
export function queryGasVolumeEfficiencyTrend(data) {
  return fetch({
    url: `/piem/${version}/compressoroptimization/gasoptimize/efficiencytrend`,
    method: `POST`,
    data
  });
}

// 处理气量调优-获取拟合和优化数据
export function queryFitAndOptimizeData(data) {
  return fetch({
    url: `/piem/${version}/compressoroptimization/gasoptimize/fitandoptimizedata`,
    method: `POST`,
    data
  });
}
