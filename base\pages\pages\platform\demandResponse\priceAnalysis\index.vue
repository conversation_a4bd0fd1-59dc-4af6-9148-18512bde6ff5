<template>
  <div class="page flex-column">
    <el-tabs v-model="activeName" @tab-click="tabsClick" class="tabs SZWtabs">
      <el-tab-pane name="负荷聚合商" label="负荷聚合商"></el-tab-pane>
      <el-tab-pane name="园区虚拟电厂" label="园区虚拟电厂"></el-tab-pane>
    </el-tabs>
    <div class="content flex-auto flex-column">
      <div style="height: 250px" class="headerBox marginTopJ2">
        <div style="display: flex">
          <div class="num-box">
            <p>本年平均申报价格（元/MW）</p>
            <h5>
              {{ Number(tabledata.avgunitprice.toFixed(2)).toLocaleString() }}
            </h5>
            <h5>
              <!-- {{ tabledata.avgunitprice ? tabledata.avgunitprice : "--" }} -->
            </h5>
          </div>
          <div class="num-box marginLeftJ2 marginRightJ2">
            <p>本年平均中标价格（元/MW）</p>
            <h5>
              {{
                tabledata.avgclearingunitprice
                  ? Number(
                      tabledata.avgclearingunitprice.toFixed(2)
                    ).toLocaleString()
                  : "--"
              }}
            </h5>
          </div>
          <div class="num-box">
            <p>本年平均价差（元/MW）</p>
            <h5>
              {{
                tabledata.avgunitprice && tabledata.avgclearingunitprice
                  ? Number(
                      (
                        tabledata.avgclearingunitprice - tabledata.avgunitprice
                      ).toFixed(2)
                    ).toLocaleString()
                  : "--"
              }}
            </h5>
          </div>
        </div>
        <el-row class="center-num marginBottomJ2 marginTopJ2">
          <el-col class="center-box" :span="8" style="margin-right: 38px">
            <h5
              style="height: 30px; overflow: auto; font-size: 22px; margin: 0"
              class="marginBottomJ1"
            >
              {{ charttable.name ? charttable.name : "--" }}
            </h5>
            <p>响应时段(最近一次的需求响应情况)</p>
            <p style="font-size: 14px">
              {{
                charttable.starttime || charttable.endtime
                  ? $moment(charttable.starttime).format(
                      "YYYY/MM/DD HH:mm:ss"
                    ) +
                    " 至 " +
                    $moment(charttable.endtime).format("YYYY/MM/DD HH:mm:ss")
                  : "--"
              }}
            </p>
          </el-col>
          <el-col class="center-box" :span="4">
            <p>价格范围（元/MW）</p>
            <h5>
              {{
                charttable.maxunitprice || charttable.minunitprice
                  ? Number(
                      charttable.minunitprice.toFixed(2)
                    ).toLocaleString() +
                    "~" +
                    Number(charttable.maxunitprice.toFixed(2)).toLocaleString()
                  : "--"
              }}
            </h5>
          </el-col>
          <el-col class="center-box" :span="4">
            <p>申报价格（元/MW）</p>
            <h5>
              {{
                charttable.unitprice
                  ? Number(charttable.unitprice.toFixed(2)).toLocaleString()
                  : "--"
              }}
            </h5>
          </el-col>
          <el-col class="center-box" :span="4" style="padding-left: 24px">
            <p>中标价格（元/MW）</p>
            <h5>
              {{
                charttable.clearingunitprice
                  ? Number(
                      charttable.clearingunitprice.toFixed(2)
                    ).toLocaleString()
                  : "--"
              }}
            </h5>
          </el-col>
          <el-col class="center-box" :span="4">
            <p>价差（元/MW）</p>
            <h5>
              {{
                charttable.clearingunitprice && charttable.unitprice
                  ? Number(
                      (
                        charttable.clearingunitprice - charttable.unitprice
                      ).toFixed(2)
                    ).toLocaleString()
                  : "--"
              }}
            </h5>
          </el-col>
        </el-row>
      </div>
      <div class="chart-box flex-auto">
        <div class="radiobox">
          <div class="fl" style="display: inline-block">
            <!-- 向前查询按钮 -->
            <div class="fl custom—square">
              <el-button
                type="display: inline-block;"
                v-show="CetButton_prv.visible_in"
                :disabled="CetButton_prv.disable_in"
                v-bind="CetButton_prv.config"
                @click="CetButton_prv_statusTrigger_out"
              ></el-button>
            </div>
            <div class="basic-box fl ml5 mr5">
              <span class="basic-box-label">分析年份</span>
              <el-date-picker
                class="fl"
                style="width: 200px"
                v-bind="CetDatePicker_time.config"
                v-model="CetDatePicker_time.val"
                placeholder="选择日期时间"
                @change="getchartData"
              ></el-date-picker>
            </div>
            <!-- 向后查询按钮 -->
            <div class="fl custom—square">
              <el-button
                type="display: inline-block;"
                v-show="CetButton_next.visible_in"
                v-bind="CetButton_next.config"
                @click="CetButton_next_statusTrigger_out"
              ></el-button>
            </div>
          </div>
        </div>
        <CetChart
          :inputData_in="CetChart_1.inputData_in"
          v-bind="CetChart_1.config"
        />
      </div>
    </div>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import common from "eem-utils/common";
import moment from "moment";

export default {
  data() {
    return {
      tabledata: {},
      charttable: {},
      isSelfOperate: false,
      yeartype: "month",
      chartRadio: "年份",
      chartDate: "",
      activeName: "负荷聚合商",
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        config: {
          title: "",
          size: "small",
          icon: "el-icon-arrow-left"
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        config: {
          title: "",
          size: "small",
          icon: "el-icon-arrow-right"
        }
      },
      CetDatePicker_time: {
        disable_in: false,
        val: this.$moment().add(0, "d").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "year",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          clearable: false,
          pickerOptions: common.pickerOptions_earlierThanYesterd11,
          size: "small",
          style: {
            display: "inline-block"
          }
        }
      },
      CetChart_1: {
        inputData_in: {},
        config: {
          options: {
            title: {
              text: "价格分析",
              padding: [0, 0, 100, 24]
            },
            grid: {
              left: "100px",
              right: "70px"
            },
            tooltip: {
              extraCssText:
                'background: #0F2557; border-radius: 12px;color: #fff; opacity: 0.8; box-shadow: "0px 0px 4px 0px rgba(62, 66, 78, 0.22)";border:none;',
              trigger: "item",
              axisPointer: {
                type: "shadow"
              },
              textStyle: {
                color: "#FFF",
                fontsize: "10px"
              },

              formatter: function (params) {
                return (
                  params.name +
                  "<br>" +
                  params.marker +
                  "响应价格指数" +
                  "<br>" +
                  params.marker +
                  "申报价格（元/MW）:" +
                  params.value[1].toFixed(2) +
                  "<br>" +
                  params.marker +
                  "出清价格（元/MW）:" +
                  params.value[2].toFixed(2) +
                  "<br>" +
                  params.marker +
                  "邀约价格上限（元/MW）:" +
                  params.value[4].toFixed(2) +
                  "<br>" +
                  params.marker +
                  "邀约价格下限（元/MW）:" +
                  params.value[3].toFixed(2)
                );
              }
            },
            xAxis: {
              type: "category",
              // boundaryGap: false,
              // name: "时间",
              axisLabel: {
                //---坐标轴 标签
                show: true, //---是否显示
                inside: false, //---是否朝内
                rotate: 0, //---旋转角度
                margin: 5, //---刻度标签与轴线之间的距离
                color: "#9CA2B1" //---默认取轴线的颜色
              },
              data: []
            },
            yAxis: {
              // type: "value",
              name: "单位:元/MW",
              nameTextStyle: {
                //---主标题内容样式
                padding: [0, 70, 0, 0]
              },
              scale: true,
              axisLabel: {
                //---坐标轴 标签
                show: true, //---是否显示
                inside: false, //---是否朝内
                rotate: 0, //---旋转角度
                margin: 5, //---刻度标签与轴线之间的距离
                color: "#9CA2B1" //---默认取轴线的颜色
              }
            },
            dataZoom: [
              {
                id: "dataZoomX",
                type: "slider",
                showDetail: false,
                // xAxisIndex: [0],
                // filterMode: "filter", // 设定为 'filter' 从而 X 的窗口变化会影响 Y 的范围。
                start: 10,
                end: 90
              }
            ],
            series: [
              {
                data: [],
                type: "candlestick",
                barWidth: 12
                // itemStyle: {
                //   color: "#FFA435",
                //   borderWidth: 1,
                //   borderColor: "#FFA435",

                //   opacity: 0.5
                // },
              }
            ]
          }
        }
      }
    };
  },
  methods: {
    tabsClick() {
      if (this.activeName === "负荷聚合商") {
        this.isSelfOperate = false;
      } else {
        this.isSelfOperate = true;
      }
      this.gettableData();
      this.getchartData();
    },
    CetButton_prv_statusTrigger_out(val) {
      // let date = this.$moment(this.CetDatePicker_time.val);
      // this.CetDatePicker_time.val = date.subtract(1, "d")._d;
      var time = this.CetDatePicker_time.val;
      this.CetDatePicker_time.val = common.goToTime(time, 5);
      this.getchartData();
    },
    CetButton_next_statusTrigger_out(val) {
      // let date = this.$moment(this.CetDatePicker_time.val);
      // this.CetDatePicker_time.val = date.add(1, "d")._d;
      var time = this.CetDatePicker_time.val;
      this.CetDatePicker_time.val = common.backToTime(time, 5);
      this.getchartData();
    },

    gettableData() {
      let data = {
        isselfoperate: this.isSelfOperate,

        page: {
          index: 0,
          limit: 1
        }
      };
      customApi.priceAnalysislatelydata(data).then(response => {
        if (response.code == 0) {
          this.charttable = this._.get(response, "data[0]", []);
        }
      });
    },
    getchartData() {
      this.clearchart();
      let data = {
        isselfoperate: this.isSelfOperate,
        powersellingcompanyid: this.$store.state.sellCompanyId,
        starttime: moment(this.CetDatePicker_time.val)
          .startOf("year")
          .valueOf(),
        endtime: moment(this.CetDatePicker_time.val).endOf("year").valueOf()
      };
      customApi.priceAnalysisqueryindex(data).then(response => {
        if (response.code == 0) {
          this.tabledata = this._.get(response, "data", []);
          let date = response.data.datas.map(item => {
            return item.time;
          });
          response.data.datas.forEach(item => {
            this.CetChart_1.config.options.series[0].data.push([
              item.unitprice,
              item.clearingunitprice,
              item.minunitprice,
              item.maxunitprice
            ]);
          });
          console.log(date);
          this.CetChart_1.config.options.xAxis.data = date;
          console.log(this.CetChart_1.config.options.xAxis.data);
          console.log(this.CetChart_1.config.options.series[0].data);
        }
      });
    },
    typechange() {
      this.yeartype = this.chartRadio === "年份" ? "year" : "month";
    },
    clearchart() {
      this.CetChart_1.config.options = {
        title: {
          text: "价格分析",
          textStyle: {},
          padding: [0, 0, 100, 24]
        },
        grid: {
          left: "100px",
          right: "70px"
        },
        tooltip: {
          extraCssText:
            'background: #0F2557; border-radius: 12px;color: #fff; opacity: 0.8; box-shadow: "0px 0px 4px 0px rgba(62, 66, 78, 0.22)";border:none;',
          trigger: "item",
          axisPointer: {
            type: "shadow"
          },
          textStyle: {
            color: "#FFF",
            fontsize: "10px"
          },

          formatter: function (params) {
            return (
              params.name +
              "<br>" +
              params.marker +
              "响应价格指数" +
              "<br>" +
              params.marker +
              "申报价格（元/MW）:" +
              params.value[1].toFixed(2) +
              "<br>" +
              params.marker +
              "出清价格（元/MW）:" +
              params.value[2].toFixed(2) +
              "<br>" +
              params.marker +
              "邀约价格上限（元/MW）:" +
              params.value[4].toFixed(2) +
              "<br>" +
              params.marker +
              "邀约价格下限（元/MW）:" +
              params.value[3].toFixed(2)
            );
          }
        },
        xAxis: {
          type: "category",
          // boundaryGap: false,
          // name: "时间",
          axisLabel: {
            //---坐标轴 标签
            show: true, //---是否显示
            inside: false, //---是否朝内
            rotate: 0, //---旋转角度
            margin: 5, //---刻度标签与轴线之间的距离
            color: "#9CA2B1" //---默认取轴线的颜色
          },
          data: []
        },
        yAxis: {
          // type: "value",
          name: "单位:元/MW",
          nameTextStyle: {
            //---主标题内容样式
            padding: [0, 70, 0, 0]
          },
          scale: true,
          axisLabel: {
            //---坐标轴 标签
            show: true, //---是否显示
            inside: false, //---是否朝内
            rotate: 0, //---旋转角度
            margin: 5, //---刻度标签与轴线之间的距离
            color: "#9CA2B1" //---默认取轴线的颜色
          }
        },
        dataZoom: [
          {
            id: "dataZoomX",
            type: "slider",
            showDetail: false,
            // xAxisIndex: [0],
            // filterMode: "filter", // 设定为 'filter' 从而 X 的窗口变化会影响 Y 的范围。
            start: 10,
            end: 90
          }
        ],
        series: [
          {
            data: [],
            type: "candlestick",
            barWidth: 12
            // itemStyle: {
            //   color: "#FFA435",
            //   borderWidth: 1,
            //   borderColor: "#FFA435",

            //   opacity: 0.5
            // },
          }
        ]
      };
    }
  },
  created() {
    this.gettableData();
    this.getchartData();
  },
  mounted() {}
};
</script>
<style scoped lang="scss">
.page {
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
}
.content {
  width: 100%;
}
.tabs {
  padding: 0 19px;
  box-sizing: border-box;
  :deep(.el-tabs__header) {
    margin: 0;
  }
  @include background_color(BG1);
}
.num-box {
  width: 32.6%;
  height: 98px;
  @include background_color(BG1);
  @include border_radius(C3);
  @include padding_left(J3);

  p {
    @include font_size("Aa");
    font-weight: 400;
    @include font_color(T3);
    line-height: 14px;
    @include margin_top(J2);
    @include margin_bottom(J2);
  }
  h5 {
    @include font_size("H");
    font-weight: 600;
    line-height: 28px;
    margin: 0;
  }
}
.center-num {
  @include border_radius(C3);
  @include background_color(BG1);
  @include padding(J3);
  height: 118px;
  display: flex;
  align-items: center;
  .center-box {
    // padding-left: 10px;
    p {
      @include font_size("Aa");
      @include font_color(T3);
      font-weight: 400;
      line-height: 14px;
      margin: 0;
      // @include margin_top(J1);
      @include margin_bottom(J1);
    }
    h5 {
      @include font_size("J3");
      font-weight: 600;
      line-height: 24px;
      margin: 0;

      @include margin_top(J2);
    }
  }
}
.chart-box {
  @include background_color(BG1);
  @include border_radius(C3);
  position: relative;
  @include padding_top(J3);
  @include padding_bottom(J4);
}
.radiobox {
  height: 34px;
  position: absolute;
  top: 24px;
  right: 24px;
  z-index: 2;
  .year {
    :deep(.el-radio-button__inner) {
      @include border_radius(C1);
      border-top-right-radius: 0px !important;
      border-bottom-right-radius: 0px !important;
    }
  }
  .month {
    :deep(.el-radio-button__inner) {
      @include border_radius(C1);
      border-top-left-radius: 0px !important;
      border-bottom-left-radius: 0px !important;
    }
  }
}
</style>
