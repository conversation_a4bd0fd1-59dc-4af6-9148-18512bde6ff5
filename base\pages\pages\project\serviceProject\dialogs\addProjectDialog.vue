<template>
  <div>
    <CetDialog v-bind="CetDialog_add" v-on="CetDialog_add.event" class="small">
      <div>
        <CetForm
          :data.sync="CetForm_1.data"
          v-bind="CetForm_1"
          v-on="CetForm_1.event"
          class="eem-cont-c1"
          label-position="top"
        >
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="$T('维保方式')" prop="maintenancetype">
                <ElSelect
                  v-model="CetForm_1.data.maintenancetype"
                  v-bind="ElSelect_1"
                  v-on="ElSelect_1.event"
                >
                  <ElOption
                    v-for="item in ElOption_1.options_in"
                    :key="item[ElOption_1.key]"
                    :label="item[ElOption_1.label]"
                    :value="item[ElOption_1.value]"
                    :disabled="item[ElOption_1.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('维保内容')" prop="content">
                <ElInput
                  v-model.trim="CetForm_1.data.content"
                  v-bind="ElInput_content"
                  v-on="ElInput_content.event"
                ></ElInput>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="$T('分组')" prop="maintenanceGroupId">
                <ElSelect
                  v-model="CetForm_1.data.maintenanceGroupId"
                  v-bind="ElSelect_2"
                  v-on="ElSelect_2.event"
                >
                  <ElOption
                    v-for="item in ElOption_2.options_in"
                    :key="item[ElOption_2.key]"
                    :label="item[ElOption_2.label]"
                    :value="item[ElOption_2.value]"
                    :disabled="item[ElOption_2.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('需要零部件')" prop="isNeed">
                <ElSelect
                  v-model="CetForm_1.data.isNeed"
                  v-bind="ElSelect_3"
                  v-on="ElSelect_3.event"
                >
                  <ElOption
                    v-for="item in ElOption_3.options_in"
                    :key="item[ElOption_3.key]"
                    :label="item[ElOption_3.label]"
                    :value="item[ElOption_3.value]"
                    :disabled="item[ElOption_3.disabled]"
                  ></ElOption>
                </ElSelect>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12" v-if="CetForm_1.data.isNeed === 1">
              <el-form-item :label="$T('零部件类型')" prop="sparePartName">
                <div class="custom-btn" @click="OpenMaintenanceType">
                  <span>
                    {{ CetForm_1.data.sparePartName }}
                  </span>
                  <span class="toConnect">
                    <omega-icon symbolId="link-lin" />
                  </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="CetForm_1.data.isNeed === 1">
              <el-form-item :label="$T('零部件数量')" prop="number">
                <ElInputNumber
                  class="fr"
                  v-model="CetForm_1.data.number"
                  v-bind="ElInputNumber_count"
                  v-on="ElInputNumber_count.event"
                ></ElInputNumber>
                <span class="form-item-unit">
                  {{ CetForm_1.data.unit || $T("个") }}
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </CetForm>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>

    <!-- 零部件类型弹窗 -->
    <maintenanceTypeDialog
      v-bind="maintenanceTypeDialog"
      v-on="maintenanceTypeDialog.event"
    ></maintenanceTypeDialog>
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom.js";
import maintenanceTypeDialog from "./maintenanceTypeDialog";

export default {
  name: "addProjectDialog",
  components: {
    maintenanceTypeDialog
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    maintenancetype: {
      type: Array
    },
    groupList: {
      type: Array
    },
    maintenanceGroup: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.localStorage) {
          return false;
        } else {
          var storage = window.localStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },
  data() {
    return {
      CetDialog_add: {
        title: $T("新增维保项目"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        width: "550px",
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        rules: {
          maintenancetype: [
            {
              required: true,
              message: $T("请选择维保方式"),
              trigger: ["blur", "change"]
            }
          ],
          content: [
            {
              required: true,
              message: $T("请输入维保内容"),
              trigger: ["blur"]
            }
          ],
          maintenanceGroupId: [
            {
              required: true,
              message: $T("请选择分组"),
              trigger: ["blur", "change"]
            }
          ],
          isNeed: [
            {
              required: true,
              message: $T("请选择是否需要零部件"),
              trigger: ["blur", "change"]
            }
          ],
          sparePartName: [
            {
              required: true,
              message: $T("请选择零部件类型"),
              trigger: ["blur", "change"]
            }
          ],
          number: [
            {
              required: true,
              message: $T("请输入零件数量"),
              trigger: ["blur"]
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_1_currentData_out,
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      // 维保方式组件
      ElSelect_1: {
        value: "",
        style: {
          width: "100%"
        },
        clearable: true,
        // "allow-create": true,
        filterable: true,
        event: {}
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // content组件
      ElInput_content: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {
          width: "100%"
        },
        event: {}
      },
      // 2组件
      ElSelect_2: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      // 2组件
      ElOption_2: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // 是否需要零部件
      ElSelect_3: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_3: {
        options_in: [
          {
            value: 1,
            label: $T("是")
          },
          {
            value: 2,
            label: $T("否")
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      // 零部件类型组件
      ElInput_type: {
        value: "",
        style: {
          width: "100%"
        },
        readonly: true,
        event: {}
      },
      ElInputNumber_count: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      },
      maintenanceTypeDialog: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        tableData: [],
        event: {
          saveData_out: this.handleSparePart
        }
      },
      currentNode: null // 选择的零件类型节点
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.init();
      this.CetDialog_add.openTrigger_in = val;
      this.currentNode = null;
      // this.reset();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    }
  },

  methods: {
    init() {
      customApi.queryMaintenanceType().then(res => {
        if (res.code === 0) {
          this.ElOption_1.options_in = res.data;
        }
      });
      this.ElOption_2.options_in = this.groupList;
      this.CetForm_1.data = {
        maintenancetype: null,
        content: null,
        maintenanceGroupId: this.maintenanceGroup.id,
        isNeed: 2,
        sparePartName: null
      };
      this.maintenanceTypeDialog.tableData = [];
      this.CetForm_1.resetTrigger_in = new Date().getTime();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_currentData_out() {},
    CetForm_1_saveData_out(val) {
      const param = this._.cloneDeep(val);
      if (this.currentNode) {
        param.sparePartId = this.currentNode.id; // 需要零部件
      }
      this.$emit("saveData_out", param);
    },
    // 零部件类型
    OpenMaintenanceType() {
      this.maintenanceTypeDialog.inputData_in = {};
      this.maintenanceTypeDialog.openTrigger_in = new Date().getTime();
    },
    handleSparePart(val) {
      this.currentNode = val[0];
      this.CetForm_1.data.sparePartName = val[0].name;
      this.CetForm_1.data.unit = val[0].unit;
      this.maintenanceTypeDialog.tableData = this._.cloneDeep(val);
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.custom-btn {
  position: relative;
  padding-left: 15px;
  height: 32px;
  line-height: 32px;
  border-radius: 4px;
  border: 1px solid;
  @include border_color(B1);
  box-sizing: border-box;
  @include font_color(T2);
  cursor: pointer;
  @include background_color(BG4);
}
.toConnect {
  position: absolute;
  right: 2px;
  z-index: 999;
  display: inline-block;
  width: 30px;
  height: 30px;
  cursor: pointer;
  @include font_color(ZS);
  text-align: center;
}
</style>
