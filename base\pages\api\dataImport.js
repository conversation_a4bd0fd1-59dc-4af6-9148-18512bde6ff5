import fetch from "eem-utils/fetch";
let version = "v1";

function processRequest(data) {
  // 对 data 进行任意转换处理
  return data;
}

function processResponse(response) {
  // 对 response 进行任意转换处理, response结构
  //   {
  //     // `data` 由服务器提供的响应
  //     data: {},

  //     // `status` 来自服务器响应的 HTTP 状态码
  //     status: 200,

  //     // `statusText` 来自服务器响应的 HTTP 状态信息
  //     statusText: 'OK',

  //     // `headers` 服务器响应的头
  //     headers: {},

  //     // `config` 是为请求提供的配置信息
  //     config: {}
  //   }
  return response;
}

function postFile(url, fileObj, projectId) {
  let data = new FormData();
  data.append("multipartFile", fileObj);
  return fetch({
    url: url,
    method: "POST",
    headers: { projectId },
    data
  });
}

export function productImport(typeStr, id, label, fileObj, projectId) {
  let url = `/eem-service/${version}/system/data/import?dataEntryType=${typeStr}&objectId=${id}&objectLabel=${label}`;
  return postFile(url, fileObj, projectId);
}

export function powerUseImport(projectId, id, label, fileObj) {
  let url = `/eem-service/${version}/bill/bill/import/${id}/${label}`;
  return postFile(url, fileObj, projectId);
}

export function powerBillImport(projectId, fileObj, params) {
  let url = `/eem-service/${version}/bank/custom/input/bankdata/${params.objectId}/${params.modelLabel}/${params.overwrite}`;
  return postFile(url, fileObj, projectId);
}

export function planImport(label, fileObj, projectId) {
  let url = `/eem-service/${version}/energy/plan/import?modelLable=${label}`;
  let data = new FormData();
  data.append("file", fileObj);
  return fetch({
    url,
    method: "POST",
    headers: { projectId },
    data
  });
}

function downloadFile(url, projectId, data) {
  // return Axios.post(url, data, {
  //   responseType: 'arraybuffer',
  //   headers: {
  //     Authorization: store.state.token,
  //     projectId: projectId
  //   }
  // })
  return fetch({
    url: url,
    method: "POST",
    responseType: "arraybuffer",
    headers: {
      "User-ID": 1,
      projectId
    },
    data
  });
}

//产品导出+能耗导出+账单能耗
export function downloadProductExport(prjId, iptInfo, ndInfo, measureTypes) {
  let url = `/eem-service/${version}/system/data/export`;
  let params = {
    ...iptInfo,
    ...ndInfo,
    measureTypes
  };
  return downloadFile(url, prjId, params);
}

//计划产量导出
export function downloadPlanExport(prjId, iptInfo, ndInfo, measureTypes) {
  let url = `/eem-service/${version}/system/data/export/plan`;
  let params = {
    ...iptInfo,
    ...ndInfo,
    measureTypes
  };
  return downloadFile(url, prjId, params);
}

//账单能耗导出
export function downloadPowerExport(prjId, iptInfo, ndInfo, energyTypes) {
  let url = `/eem-service/${version}/bill/bill/export`;
  let params = {
    ...iptInfo,
    node: ndInfo,
    energyTypes
  };
  return downloadFile(url, prjId, params);
}

//获取产品展示数据
export function getProductData(iType, projectId, data) {
  let url = `/eem-service/${version}/system/data/input/query/new`;
  if (iType === "2") {
    url = `/eem-service/${version}/bill/bill`;
  } else if (iType === "3") {
    url = `/eem-service/${version}/bank/custom/query/data`;
  }
  return fetch({
    url,
    method: "POST",
    dataType: "json",
    headers: {
      projectId
    },
    data
  });
}

export function saveProduct(data) {
  return fetch({
    url: `/eem-service/${version}/system/data/input/new`,
    method: "POST",
    dataType: "json",
    headers: {},
    data
  });
}

export function saveBill(data) {
  return fetch({
    url: `/eem-service/${version}/bill/bill`,
    method: "PUT",
    dataType: "json",
    headers: {
      // "User-ID": 1
    },
    data
  });
}
//获取台账excel字段名称
export function getExcelHeader(projectId, data) {
  let url = `/eem-service/${version}/bank/custom/get/excelheader`;
  return fetch({
    url,
    method: "POST",
    dataType: "json",
    headers: {
      projectId
    },
    data
  });
}
// 批量导出录入数据
export function exportMultiNodes(projectId, data) {
  let url = `/eem-service/${version}/system/data/export/multiNodes`;
  return downloadFile(url, projectId, data);
}

// 批量导入录入数据
export function importMultiNodes(data, fileObj, projectId) {
  let url = `/eem-service/${version}/system/data/import/multiNodes?dataEntryType=${data.dataEntryType}`;
  return postFile(url, fileObj, projectId);
}

export function getDataEntryConfig(data) {
  return fetch({
    url: `/eem-service/${version}/system/data/dataEntry/config`,
    method: "POST",
    dataType: "json",
    data
  });
}
