import fetch from "eem-utils/fetch";
const version = "v1";

// 根据表计查询表计测点数据
export function getPoints(deviceId) {
  return fetch({
    url: `/eem-service/${version}/flink/datalog/points/${deviceId}`,
    method: "GET"
  });
}
// flink数据录入数据查询
export function getFlinkDatalogData(data) {
  return fetch({
    url: `/eem-service/${version}/flink/datalog/query`,
    method: "POST",
    data
  });
}

// flink数据录入新增和修改接口
export function getFlinkEdit(data) {
  return fetch({
    url: `/eem-service/${version}/flink/datalog/edit`,
    method: "POST",
    data
  });
}

// flink数据导出中查询可以导出的设备节点
export function getFlinkMaxNodes() {
  return fetch({
    url: `/eem-service/${version}/flink/datalog/getMaxNodes`,
    method: "GET"
  });
}

// flink数据导出中查询最大可以导出的数据数量
export function getFlinkMaxExportCount() {
  return fetch({
    url: `/eem-service/${version}/flink/datalog/getMaxExportCount`,
    method: "GET"
  });
}

// flink数据导入
export function flinkImport(data) {
  return fetch({
    url: `/eem-service/${version}/flink/datalog/import`,
    method: "POST",
    data: data
  });
}

//  获取设备的回路
export function getDeviceLogic(data) {
  return fetch({
    url: `/eem-service/${version}/flink/datalog/getDeviceLogic`,
    method: "POST",
    data: data
  });
}

//  flink执行能耗重算
export function getEnergyReCalc(data) {
  return fetch({
    url: `/eem-service/${version}/system/data/energy/re-calc`,
    method: "POST",
    timeout: 600000,
    data: data
  });
}

//  获取当前项目重算状态
export function getEnergyReCalcState(projectId) {
  return fetch({
    url: `/eem-service/${version}/system/data/energy/re-calc/state/${projectId}`,
    method: "POST"
  });
}
