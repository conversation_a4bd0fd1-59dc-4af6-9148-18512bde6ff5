<template>
  <div>
    <CetDialog
      v-bind="CetDialog_detail"
      v-on="CetDialog_detail.event"
      :class="!en && 'small'"
    >
      <div class="eem-cont-c1">
        <el-row :gutter="$J3" class="fullwidth">
          <el-col :span="12" v-for="(item, index) in items" :key="index">
            <div class="mbJ mtJ">
              <el-col :span="en ? 10 : 8">
                <el-tooltip :content="item.name" effect="light">
                  <span class="nowrap text-ellipsis fullwidth">
                    {{ item.name }}
                  </span>
                </el-tooltip>
              </el-col>
              <el-col :span="en ? 14 : 16" class="text-right">
                <span
                  class="fr clicktext"
                  v-if="item.type == 'button'"
                  @click="clickSee"
                >
                  {{ $T("查看") }}
                </span>
                <el-tooltip
                  v-else
                  :content="filText(data[item.key], item.type)"
                  effect="light"
                >
                  <div class="nowrap text-ellipsis fullwidth">
                    {{ filText(data[item.key], item.type) }}
                  </div>
                </el-tooltip>
              </el-col>
            </div>
          </el-col>
        </el-row>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
      </span>
    </CetDialog>
    <Scheme
      :visibleTrigger_in="scheme.visibleTrigger_in"
      :closeTrigger_in="scheme.closeTrigger_in"
      :inputData_in="scheme.inputData_in"
    />
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import Scheme from "./Scheme";

const ONE_MINUTE_MILLISECONDS = 60 * 1000;
const ONE_HOUR_MILLISECONDS = 60 * ONE_MINUTE_MILLISECONDS;

export default {
  name: "detail",
  components: { Scheme },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },
  data() {
    return {
      CetDialog_detail: {
        title: $T("维保计划详情"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      items: [
        {
          name: $T("名称"),
          key: "name"
        },
        {
          name: $T("预计耗时"),
          key: "timeconsumeplan"
        },
        {
          name: $T("等级"),
          key: "worksheetTaskLevelName"
        },
        {
          name: $T("工单生成时间"),
          key: "generate"
        },
        {
          name: $T("人员数量"),
          key: "population"
        },
        {
          name: $T("维保项目"),
          key: "",
          type: "button"
        },
        {
          name: $T("周期策略"),
          key: "executestrategy$text"
        },
        {
          name: $T("周期"),
          key: "cycle$text"
        },
        {
          name: $T("维保目标"),
          key: "deviceplanrelationship_model"
        },
        {
          name: $T("开始时间"),
          key: "executetime",
          type: "date"
        },
        {
          name: $T("责任班组"),
          key: "teamName"
        },
        {
          name: $T("结束时间"),
          key: "finishtime",
          type: "date"
        }
      ],
      data: {},
      scheme: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      options_in: [
        {
          value: 5,
          label: $T("执行前5分钟")
        },
        {
          value: 30,
          label: $T("执行前30分钟")
        },
        {
          value: 60,
          label: $T("执行前1小时")
        },
        {
          value: 2 * 60,
          label: $T("执行前2小时")
        },
        {
          value: 24 * 60,
          label: $T("执行前1天")
        },
        {
          value: 2 * 24 * 60,
          label: $T("执行前2天")
        }
      ],
      worksheetstatus: []
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.getWorksheetstatus();
      this.CetDialog_detail.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      this.CetDialog_detail.closeTrigger_in = val;
    },
    inputData_in(val) {
      // 工单生成时间
      const target = this.options_in.find(
        item => item.value === val.aheadduration
      );
      // 维保目标
      let inspectObject = "";
      val.deviceplanrelationship_model.forEach((item, index) => {
        if (index) {
          inspectObject += " 、 ";
        }
        inspectObject += item.devicename;
      });
      this.data = {
        ...val,
        timeconsumeplan: val.timeconsumeplan / ONE_HOUR_MILLISECONDS + "h",
        generate: target ? target.label : "--",
        deviceplanrelationship_model: inspectObject
      };
      this.scheme.inputData_in = this._.cloneDeep(val);
    }
  },

  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_detail.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_add.localSaveTrigger_in = this._.cloneDeep(val);
    },
    init() {},
    filText(val, type) {
      if ([undefined, null, NaN].includes(val)) {
        return "--";
      } else if (type === "date") {
        return this.$moment(val).format("YYYY-MM-DD HH:mm:ss");
      } else {
        return val + "";
      }
    },
    clickSee() {
      this.scheme.visibleTrigger_in = new Date().getTime();
    },
    formatter(row, column, cellValue) {
      return cellValue || "--";
    },
    formatterStatus(row, column, cellValue) {
      const target = this.worksheetstatus.find(item => item.id === cellValue);
      return target ? target.text : "--";
    },
    getWorksheetstatus() {
      customApi.queryEnumerations("worksheetstatus").then(res => {
        if (res.code === 0) {
          this.worksheetstatus = res.data;
        }
      });
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.clicktext {
  cursor: pointer;
  @include font_color(ZS);
}
</style>
