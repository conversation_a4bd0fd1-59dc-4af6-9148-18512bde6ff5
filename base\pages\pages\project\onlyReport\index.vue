<template>
  <div class="page EnergyReport eem-common">
    <el-container class="fullheight">
      <el-aside width="315px" class="eem-aside flex-column">
        <customElSelect
          v-model="ElSelect_1.value"
          v-bind="ElSelect_1"
          v-on="ElSelect_1.event"
          class="mbJ1"
          :prefix_in="$T('报表类型')"
        >
          <ElOption
            v-for="item in ElOption_1.options_in"
            :key="item[ElOption_1.key]"
            :label="item[ElOption_1.label]"
            :value="item[ElOption_1.value]"
            :disabled="item[ElOption_1.disabled]"
          ></ElOption>
        </customElSelect>
        <CetTree
          class="flex-auto"
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
        ></CetTree>
      </el-aside>
      <el-container class="eem-container fullheight mlJ3 eem-min-width">
        <div class="flex-column fullfilled">
          <div class="mbJ3 flex-row">
            <el-tooltip :content="nodeLabel" effect="light" placement="top">
              <div class="text-ellipsis lh28" style="width: 360px">
                <span class="fsH1">{{ nodeLabel }}</span>
              </div>
            </el-tooltip>
            <div style="flex: 1; min-width: 920px" class="minWH">
              <div class="fr">
                <el-button
                  class="fl mlJ1"
                  size="small"
                  @click="allexportReportFile"
                  :disabled="
                    (!isMreport && !reportHtml) ||
                    (isMreport && !iframeSrc && !showExprot) ||
                    !isReport
                  "
                >
                  {{ $T("导出") }}
                </el-button>

                <el-button
                  v-if="hasOnlyReportAush"
                  class="fl mlJ1"
                  @click="handlePush"
                >
                  {{ $T("报表设计") }}
                </el-button>
              </div>
              <div
                class="fr"
                v-show="currentNode && currentNode.nodeType === 3"
              >
                <customElSelect
                  v-show="showInterval"
                  v-model="ElSelect_interval.value"
                  v-bind="ElSelect_interval"
                  v-on="ElSelect_interval.event"
                  class="fl mrJ1"
                  :prefix_in="$T('分析周期')"
                >
                  <ElOption
                    v-for="item in ElOption_interval.options_in"
                    :key="item[ElOption_interval.key]"
                    :label="item[ElOption_interval.label]"
                    :value="item[ElOption_interval.value]"
                    :disabled="item[ElOption_interval.disabled]"
                  ></ElOption>
                </customElSelect>
                <CustomDatePicker
                  class="fl"
                  @change="CustomDatePicker_1_change"
                  :val="CustomDatePicker_1.queryTime"
                  :showElSelect="CustomDatePicker_1.showElSelect"
                  :dataConfig="CustomDatePicker_1.dataConfig"
                ></CustomDatePicker>
              </div>
              <div
                class="fr text-right"
                v-show="currentNode && currentNode.nodeType !== 3"
              >
                <el-select
                  v-model="currentTimeBucket"
                  class="data-row"
                  :disabled="
                    currentNode.pecCoreReport
                      ? currentNode.reportType !== 4
                      : currentNode.reportType !== 0
                  "
                  v-show="false"
                  @change="changeTimeBucket"
                  style="width: 90px"
                  size="small"
                >
                  <el-option
                    v-for="item in timeBucket"
                    :key="item.id + item.text"
                    :label="item.text"
                    :value="item.id"
                  ></el-option>
                </el-select>
                <el-button
                  class="fl mrJ1"
                  size="small"
                  icon="el-icon-arrow-left"
                  @click="queryPrv"
                ></el-button>
                <el-date-picker
                  class="data-row"
                  size="small"
                  :type="pickerType"
                  v-model="queryTime.startDate"
                  @change="changeDate"
                  :clearable="false"
                  :format="formatDate"
                  :picker-options="startPickerOptions"
                ></el-date-picker>

                <el-time-picker
                  v-show="pickerType === 'date'"
                  class="data-row"
                  :disabled="
                    currentNode.pecCoreReport
                      ? currentNode.reportType !== 4
                      : currentNode.reportType !== 30
                  "
                  size="small"
                  v-model="queryTime.startTime"
                  @change="changeDate"
                  :clearable="false"
                ></el-time-picker>
                <span class="fl lh32 mrJ1">{{ $T("至") }}</span>
                <el-date-picker
                  class="data-row"
                  :disabled="
                    currentNode.pecCoreReport
                      ? currentNode.reportType !== 4
                      : currentNode.reportType !== 30
                  "
                  size="small"
                  :type="pickerType"
                  v-model="queryTime.endDate"
                  @change="changeDate"
                  :clearable="false"
                  :format="formatDate"
                  :picker-options="endPickerOptions"
                ></el-date-picker>
                <el-time-picker
                  class="data-row"
                  v-show="pickerType === 'date'"
                  :disabled="
                    currentNode.pecCoreReport
                      ? currentNode.reportType !== 4
                      : currentNode.reportType !== 30
                  "
                  size="small"
                  v-model="queryTime.endTime"
                  @change="changeDate"
                  :clearable="false"
                ></el-time-picker>
                <el-button
                  class="fl"
                  size="small"
                  :disabled="isBtnOk"
                  icon="el-icon-arrow-right"
                  @click="queryNext"
                ></el-button>
              </div>
            </div>
          </div>
          <div
            v-if="!isReport"
            class="flex-auto text-center fsH1 horizontal-middle"
          >
            {{ $T("请选择报表模板") }}
          </div>
          <div v-else style="flex: 1" class="minWH reportHtml">
            <div style="overflow: auto; height: 100%" v-show="!isMreport">
              <div
                v-show="!reportHtml"
                class="fullheight text-center fsH1 horizontal-middle"
              >
                {{ $T("暂无报表数据") }}
              </div>
              <div v-show="reportHtml" v-html="reportHtml"></div>
            </div>
            <div style="overflow: auto; height: 100%" v-show="isMreport">
              <div
                v-show="!iframeSrc"
                class="fullheight text-center fsH1 horizontal-middle"
              >
                {{ $T("暂无报表数据") }}
              </div>
              <iframe
                v-show="iframeSrc"
                style="height: calc(100% - 4px)"
                ref="iframe"
                width="100%"
                frameborder="0"
                title=""
                :src="iframeSrc"
              ></iframe>
            </div>
          </div>
        </div>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import common from "eem-utils/common";
import customApi from "@/api/custom";
import { FullScreenLoading } from "@omega/http/loading.js";
import { httping } from "@omega/http";
import CustomDatePicker from "eem-components/CustomDatePicker.vue";

export default {
  name: "EnergyReport",
  components: {
    CustomDatePicker
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    systemCfg() {
      var vm = this;
      return vm.$store.state.systemCfg;
    },
    isReport() {
      let nodeType = this._.get(this.currentNode, "nodeType");
      return [272826368, 272760832, 2, 3].includes(nodeType);
    },
    userId() {
      return this.$store.state.userInfo.id;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    tenantId() {
      return this.$store.state.projectTenantId;
    }
  },

  data(vm) {
    return {
      hasOnlyReportAush: false, // 是否有only-report的页面权限
      iframeSrc: "",
      nodeLabel: "",
      showExprot: false,
      pecCoreReportList: [], // 更新日常运营报表
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "nodeName",
          children: "children"
        },
        lazy: false,
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out,
          parentList_out: this.CetTree_1_parentList_out,
          checkedNodes_out: this.CetTree_1_checkedNodes_out,
          halfCheckNodes_out: this.CetTree_1_halfCheckNodes_out,
          allCheckNodes_out: this.CetTree_1_allCheckNodes_out
        }
      },
      currentNode: {},
      queryTime: {
        startDate: vm.$moment().startOf("d").format("YYYY-MM-DD"),
        endDate: vm.$moment().add(1, "d").startOf("d").format("YYYY-MM-DD"),
        startTime: vm.$moment().startOf("d").valueOf(),
        endTime: vm.$moment().add(1, "d").startOf("d").valueOf()
      },
      disablePicker: true,
      pickerType: "date",
      formatDate: "yyyy-MM-dd",
      timeBucket: [
        {
          id: 1,
          text: $T("1天"),
          nodeType: 0,
          intervalType: 1,
          number: 1,
          unit: "d"
        },
        {
          id: 2,
          text: $T("1周"),
          nodeType: 2,
          intervalType: 2,
          number: 7,
          unit: "d"
        },
        {
          id: 3,
          text: $T("1月"),
          nodeType: 1,
          intervalType: 3,
          number: 1,
          unit: "M"
        },
        {
          id: 4,
          text: $T("1季度"),
          nodeType: 1,
          intervalType: 3,
          number: 3,
          unit: "M"
        },
        {
          id: 5,
          text: $T("1年"),
          nodeType: 3,
          intervalType: 5,
          number: 1,
          unit: "y"
        }
      ],
      startPickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        firstDayOfWeek: 1
      },
      endPickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() < vm.$moment(vm.queryTime.startDate).valueOf() ||
            time.getTime() > Date.now()
          );
        },
        firstDayOfWeek: 1
      },
      currentTimeBucket: 1,
      reportHtml: "",
      isBtnOk: false,
      isMreport: false,
      CustomDatePicker_1: {
        showElSelect: true,
        queryTime: {
          startTime: null,
          endTime: null,
          cycle: 12 //17年，14月，12日，20自定义
        },
        dataConfig: {
          time: null,
          cycle: 1,
          showPicker: true,
          showRange: true,
          type: [
            {
              id: 1,
              text: $T("日报"),
              type: "date"
            },
            {
              id: 3,
              text: $T("月报"),
              type: "month"
            },
            {
              id: 5,
              text: $T("年报"),
              type: "year"
            }
          ]
        }
      },
      ElSelect_1: {
        value: 0,
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [
          {
            id: 0,
            text: "全部"
          },
          {
            id: 1,
            text: "通用报表"
          },
          {
            id: 2,
            text: "固定模板报表"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      initTreeData: [],
      isLoading: false,
      showInterval: false,
      ElSelect_interval: {
        value: 1,
        event: {
          change: this.ElSelect_interval_change_out
        }
      },
      ElOption_interval: {
        options_in: [
          {
            id: 1,
            text: $T("三班倒月报")
          },
          {
            id: 2,
            text: $T("两班倒月报")
          },
          {
            id: 3,
            text: $T("月报")
          },
          {
            id: 4,
            text: $T("年报")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      }
    };
  },
  watch: {
    currentNode: {
      deep: true,
      handler(val, oldVal) {
        if (this._.isEmpty(val)) return;
        this.showInterval = false;
        this.CustomDatePicker_1.showElSelect = true;
        if (val.nodeType === 3 && val.reportType === 2) {
          this.showInterval = true;
          this.CustomDatePicker_1.showElSelect = false;
          this.ElSelect_interval.value = 1;
          let dataConfig = this.CustomDatePicker_1.dataConfig;
          dataConfig.time = this.$moment().startOf("M").valueOf();
          dataConfig.cycle = 3;
          this.CustomDatePicker_1.dataConfig = this._.cloneDeep(dataConfig);
          this.$nextTick(() => {
            this.queryCustomReportFile();
          });
        } else if (val.nodeType === 3 && val.reportType !== 2) {
          let dataConfig = this.CustomDatePicker_1.dataConfig;
          dataConfig.time = this.$moment().startOf("d").valueOf();
          dataConfig.cycle = 1;
          this.CustomDatePicker_1.dataConfig = this._.cloneDeep(dataConfig);
          this.$nextTick(() => {
            this.queryCustomReportFile();
          });
        } else {
          this.queryTime.startTime = this.$moment().startOf("d").valueOf();
          this.queryTime.endTime = this.$moment()
            .add(1, "d")
            .startOf("d")
            .valueOf();
          const queryTime = this.queryTime;
          if (val.pecCoreReport) {
            switch (val.reportType) {
              case 0:
                queryTime.startDate = this.$moment()
                  .startOf("d")
                  .format("YYYY-MM-DD");
                queryTime.endDate = this.$moment()
                  .startOf("d")
                  .add(1, "d")
                  .format("YYYY-MM-DD");
                this.pickerType = "date";
                this.formatDate = "yyyy-MM-dd";
                this.currentTimeBucket = 1;
                break;
              case 1:
                queryTime.startDate = this.$moment()
                  .startOf("M")
                  .format("YYYY-MM-DD");
                queryTime.endDate = this.$moment()
                  .startOf("M")
                  .add(1, "M")
                  .format("YYYY-MM-DD");
                this.pickerType = "month";
                this.formatDate = "yyyy-MM";
                this.currentTimeBucket = 3;
                break;
              case 2:
                queryTime.startDate = this.$moment()
                  .startOf("w")
                  .day(1)
                  .format("YYYY-MM-DD");
                queryTime.endDate = this.$moment()
                  .startOf("w")
                  .add(1, "w")
                  .day(1)
                  .format("YYYY-MM-DD");
                this.pickerType = "date";
                this.formatDate = "yyyy-MM-dd";
                this.currentTimeBucket = 2;
                break;
              case 3:
                queryTime.startDate = this.$moment()
                  .startOf("y")
                  .format("YYYY-MM-DD");
                queryTime.endDate = this.$moment()
                  .startOf("y")
                  .add(1, "y")
                  .format("YYYY-MM-DD");
                this.pickerType = "year";
                this.formatDate = "yyyy";
                this.currentTimeBucket = 5;
                break;
              default:
                queryTime.startDate = this.$moment()
                  .startOf("d")
                  .format("YYYY-MM-DD");
                queryTime.endDate = this.$moment()
                  .startOf("d")
                  .add(1, "d")
                  .format("YYYY-MM-DD");
                this.pickerType = "date";
                this.formatDate = "yyyy-MM-dd";
                this.currentTimeBucket = 1;
                break;
            }
          } else {
            switch (val.reportType) {
              case 0:
                queryTime.startDate = this.$moment()
                  .startOf("d")
                  .format("YYYY-MM-DD");
                queryTime.endDate = this.$moment()
                  .startOf("d")
                  .add(1, "d")
                  .format("YYYY-MM-DD");
                this.pickerType = "date";
                this.formatDate = "yyyy-MM-dd";
                this.currentTimeBucket = 1;
                break;
              case 1:
                queryTime.startDate = this.$moment()
                  .startOf("d")
                  .format("YYYY-MM-DD");
                queryTime.endDate = this.$moment()
                  .startOf("d")
                  .add(1, "d")
                  .format("YYYY-MM-DD");
                this.pickerType = "date";
                this.formatDate = "yyyy-MM-dd";
                this.currentTimeBucket = 1;
                break;
              case 2:
                queryTime.startDate = this.$moment()
                  .startOf("w")
                  .day(1)
                  .format("YYYY-MM-DD");
                queryTime.endDate = this.$moment()
                  .startOf("w")
                  .add(1, "w")
                  .day(1)
                  .format("YYYY-MM-DD");
                this.pickerType = "date";
                this.formatDate = "yyyy-MM-dd";
                this.currentTimeBucket = 2;
                break;
              case 3:
                queryTime.startDate = this.$moment()
                  .startOf("M")
                  .format("YYYY-MM-DD");
                queryTime.endDate = this.$moment()
                  .startOf("M")
                  .add(1, "M")
                  .format("YYYY-MM-DD");
                this.pickerType = "month";
                this.formatDate = "yyyy-MM";
                this.currentTimeBucket = 3;
                break;
              case 4:
                queryTime.startDate = this.$moment()
                  .startOf("Q")
                  .format("YYYY-MM-DD");
                queryTime.endDate = this.$moment()
                  .startOf("Q")
                  .add(1, "Q")
                  .format("YYYY-MM-DD");
                this.pickerType = "month";
                this.formatDate = "yyyy-MM";
                this.currentTimeBucket = 4;
                break;
              case 5:
                queryTime.startDate = this.$moment()
                  .startOf("y")
                  .format("YYYY-MM-DD");
                queryTime.endDate = this.$moment()
                  .startOf("y")
                  .add(1, "y")
                  .format("YYYY-MM-DD");
                this.pickerType = "year";
                this.formatDate = "yyyy";
                this.currentTimeBucket = 5;
                break;

              default:
                queryTime.startDate = this.$moment()
                  .startOf("d")
                  .format("YYYY-MM-DD");
                queryTime.endDate = this.$moment()
                  .startOf("d")
                  .add(1, "d")
                  .format("YYYY-MM-DD");
                this.pickerType = "date";
                this.formatDate = "yyyy-MM-dd";
                this.currentTimeBucket = 1;
                break;
            }
          }
          this.queryReportFile();
        }
      }
    },
    "queryTime.endDate": {
      handler: function (val, oldVal) {
        if (new Date(val).getTime() > new Date().getTime()) {
          this.isBtnOk = true;
        } else {
          this.isBtnOk = false;
        }
      },
      deep: true,
      immediate: true
    }
  },

  methods: {
    CetTree_1_currentNode_out(val) {
      this.reportHtml = "";
      this.iframeSrc = "";
      if (!val) {
        return;
      }
      this.currentNode = val;
      this.nodeLabel = val && val.nodeName;
    },
    CetTree_1_parentList_out(val) {},
    CetTree_1_checkedNodes_out(val) {},
    CetTree_1_halfCheckNodes_out(val) {},
    CetTree_1_allCheckNodes_out(val) {},
    setTreeKey: function (root) {
      //递归
      var res = root;
      function bfs(tree, idx) {
        if (!tree) return;
        tree.name = tree.nodeName;
        tree.id = tree.nodeType + tree.nodeId;
        tree.modelLabel = tree.nodeType + "";
        tree.tree_id = tree.nodeType + "_" + tree.nodeId;
        if (!Array.isArray(tree.children)) {
          return;
        }
        if (tree.children.length > 0) {
          for (var i = 0; i < tree.children.length; i++) {
            bfs(tree.children[i], idx + 1);
          }
        }
      }
      bfs(root, 0);
      return res;
    },
    queryReportFile() {
      const node = this.currentNode;
      if (node.pecCoreReport) {
        this.isMreport = false;
        this.queryPecCoreReportFile();
      } else {
        this.showExprot = false;
        this.isMreport = true;
        if (this._.get(node, "nodeType") !== 2) return;
        let params = {};
        if (node.reportType === 11 || node.reportType === 30) {
          let startTime =
            this.$moment(this.queryTime.startDate).format("YYYY-MM-DD") +
            this.$moment(this.queryTime.startTime).format(" HH:mm:ss");
          let endTime =
            this.$moment(this.queryTime.endDate).format("YYYY-MM-DD") +
            this.$moment(this.queryTime.endTime).format(" HH:mm:ss");
          // 时段报表
          params = {
            timePoints:
              this.$moment(startTime).valueOf() +
              "," +
              this.$moment(endTime).valueOf()
          };
        } else {
          // 日、周、月、季度、年、自定义报表
          params = {
            startTime: this.$moment(this.queryTime.startDate).valueOf()
          };
        }
        const loading = new FullScreenLoading();
        loading.showLoading();
        customApi.getOnlyReportTemplate(node.nodeId, params, false).then(
          res => {
            if (res.code === 0) {
              let urlStr = "/onlinePreviewHtml/" + res.data;
              this.$nextTick(() => {
                const origin =
                  this._.get(this.systemCfg, "reportsever", location.origin) ||
                  location.origin;
                this.iframeSrc = `${origin}${urlStr}`;
                const iframe = this.$refs.iframe;
                iframe.onload = () => {
                  loading.hideLoading();
                };
              });
            } else {
              loading.hideLoading();
            }
          },
          err => {
            this.showExprot = err.code === -1000001;
            loading.hideLoading();
          }
        );
      }
    },
    // 合并日常运营报表-查询html
    queryPecCoreReportFile() {
      const node = this.currentNode;
      this._.get(node, "nodeType");
      if (this._.get(node, "nodeType") !== 272760832) return;
      const params = {
        startTime:
          this.$moment(this.queryTime.startDate).format("YYYY-MM-DD") +
          this.$moment(this.queryTime.startTime).format(" HH:mm:ss"),
        exportType: 0,
        intervalType: 0,
        queryNodeID: 0,
        queryNodeType: 0,
        reportID: node.nodeId,
        reportName: node.nodeName,
        reportType: node.nodeType,
        endTime:
          this.$moment(this.queryTime.endDate).format("YYYY-MM-DD") +
          this.$moment(this.queryTime.endTime).format(" HH:mm:ss")
      };
      httping({
        url: `/device-data-service/api/report/v1/html`,
        method: "POST",
        data: params,
        responseType: "text"
      })
        .then(res => {
          if (res.status !== 200) {
            this.reportHtml = "";
            return;
          }
          this.reportHtml = res.data;
        })
        .catch(res => {
          this.reportHtml = "";
        });
    },
    // 合并能耗和运营报表导出
    allexportReportFile() {
      const node = this.currentNode;
      const vm = this;
      if (node.pecCoreReport) {
        vm.exportDateReportFile();
      } else if (node.nodeType === 3) {
        vm.customExportReportFile();
      } else {
        vm.exportReportFile();
      }
    },
    exportReportFile() {
      const node = this.currentNode;
      var startTime =
        this.$moment(this.queryTime.startDate).format("YYYY-MM-DD") +
        this.$moment(this.queryTime.startTime).format(" HH:mm:ss");
      var endTime =
        this.$moment(this.queryTime.endDate).format("YYYY-MM-DD") +
        this.$moment(this.queryTime.endTime).format(" HH:mm:ss");

      let urlStr;
      if (node.reportType === 11 || node.reportType === 30) {
        // 自定义报表
        let timePoints =
          this.$moment(startTime).valueOf() +
          "," +
          this.$moment(endTime).valueOf();
        urlStr = `/only-report/api/v1/template/query/download/${node.nodeId}?timePoints=${timePoints}`;
      } else {
        // 日、周、月、季度、年
        urlStr = `/only-report/api/v1/template/query/download/${
          node.nodeId
        }?startTime=${this.$moment(startTime).valueOf()}`;
      }
      common.downExcelGET(urlStr, {}, this.token, this.projectId);
    },
    // 合并日常运营报表导出
    exportDateReportFile() {
      const node = this.currentNode;
      const vm = this;
      const params = {
        startTime:
          this.$moment(this.queryTime.startDate).format("YYYY-MM-DD") +
          " " +
          vm.$moment(vm.queryTime.startTime).format("HH:mm:ss"),
        exportType: 1,
        intervalType: 0,
        queryNodeID: 0,
        queryNodeType: 0,
        reportID: node.nodeId,
        reportName: node.nodeName,
        reportType: node.nodeType,
        endTime:
          this.$moment(this.queryTime.endDate).format("YYYY-MM-DD") +
          " " +
          vm.$moment(vm.queryTime.endTime).format("HH:mm:ss")
      };
      common.downExcel(
        "/device-data-service/api/report/v1/excel",
        params,
        vm.token,
        this.projectId
      );
    },
    changeDate(val) {
      const node = this.currentNode;
      const queryTime = this._.cloneDeep(this.queryTime);
      if (node.pecCoreReport) {
        switch (node.reportType) {
          case 0:
            queryTime.startDate = this.$moment(val)
              .startOf("d")
              .format("YYYY-MM-DD");
            queryTime.endDate = this.$moment(val)
              .startOf("d")
              .add(1, "d")
              .format("YYYY-MM-DD");
            this.pickerType = "date";
            this.formatDate = "yyyy-MM-dd";
            break;
          case 1:
            queryTime.startDate = this.$moment(val)
              .startOf("M")
              .format("YYYY-MM-DD");
            queryTime.endDate = this.$moment(val)
              .startOf("M")
              .add(1, "M")
              .format("YYYY-MM-DD");
            this.pickerType = "month";
            this.formatDate = "yyyy-MM";
            break;
          case 2:
            queryTime.startDate = this.$moment(val)
              .startOf("w")
              .day(1)
              .format("YYYY-MM-DD");
            queryTime.endDate = this.$moment(val)
              .startOf("w")
              .add(1, "w")
              .day(1)
              .format("YYYY-MM-DD");
            this.pickerType = "date";
            this.formatDate = "yyyy-MM-dd";
            break;
          case 3:
            queryTime.startDate = this.$moment(val)
              .startOf("y")
              .format("YYYY-MM-DD");
            queryTime.endDate = this.$moment(val)
              .startOf("y")
              .add(1, "y")
              .format("YYYY-MM-DD");
            this.pickerType = "year";
            this.formatDate = "yyyy";
            break;
          default:
            break;
        }
      } else {
        switch (node.reportType) {
          case 1:
            queryTime.startDate = this.$moment(val)
              .startOf("d")
              .format("YYYY-MM-DD");
            queryTime.endDate = this.$moment(val)
              .startOf("d")
              .add(1, "d")
              .format("YYYY-MM-DD");
            this.pickerType = "date";
            this.formatDate = "yyyy-MM-dd";
            break;
          case 2:
            queryTime.startDate = this.$moment(val)
              .startOf("w")
              .day(1)
              .format("YYYY-MM-DD");
            queryTime.endDate = this.$moment(val)
              .startOf("w")
              .add(1, "w")
              .day(1)
              .format("YYYY-MM-DD");
            this.pickerType = "date";
            this.formatDate = "yyyy-MM-dd";
            break;
          case 3:
            queryTime.startDate = this.$moment(val)
              .startOf("M")
              .format("YYYY-MM-DD");
            queryTime.endDate = this.$moment(val)
              .startOf("M")
              .add(1, "M")
              .format("YYYY-MM-DD");
            this.pickerType = "month";
            this.formatDate = "yyyy-MM";
            break;
          case 4:
            queryTime.startDate = this.$moment(val)
              .startOf("Q")
              .format("YYYY-MM-DD");
            queryTime.endDate = this.$moment(val)
              .startOf("Q")
              .add(1, "Q")
              .format("YYYY-MM-DD");
            this.pickerType = "month";
            this.formatDate = "yyyy-MM";
            break;
          case 5:
            queryTime.startDate = this.$moment(val)
              .startOf("y")
              .format("YYYY-MM-DD");
            queryTime.endDate = this.$moment(val)
              .startOf("y")
              .add(1, "y")
              .format("YYYY-MM-DD");
            this.pickerType = "year";
            this.formatDate = "yyyy";
            break;
          default:
            break;
        }
      }
      this.queryTime = queryTime;
      this.queryReportFile();
    },
    changeTimeBucket(val) {
      const queryTime = this._.cloneDeep(this.queryTime);
      const startDate = this.$moment(queryTime.startDate);
      const currentTimeBucket = this._.find(this.timeBucket, { id: val });

      const endDate = startDate.add(
        currentTimeBucket.number,
        currentTimeBucket.unit
      );
      this.queryTime.endDate = endDate;
      this.queryReportFile();
    },
    queryPrv() {
      const queryTime = this.queryTime;
      const startDate = this.$moment(queryTime.startDate);
      const endDate = this.$moment(queryTime.endDate);
      const currentTimeBucket = this._.find(this.timeBucket, {
        id: this.currentTimeBucket
      });
      queryTime.startDate = startDate
        .subtract(currentTimeBucket.number, currentTimeBucket.unit)
        .format("YYYY-MM-DD");
      queryTime.endDate = this.$moment(queryTime.startDate)
        .add(currentTimeBucket.number, currentTimeBucket.unit)
        .format("YYYY-MM-DD");
      this.queryReportFile();
    },
    queryNext() {
      const queryTime = this.queryTime;
      const startDate = this.$moment(queryTime.startDate);
      const endDate = this.$moment(queryTime.endDate);
      const currentTimeBucket = this._.find(this.timeBucket, {
        id: this.currentTimeBucket
      });
      queryTime.startDate = startDate
        .add(currentTimeBucket.number, currentTimeBucket.unit)
        .format("YYYY-MM-DD");
      queryTime.endDate = this.$moment(queryTime.startDate)
        .add(currentTimeBucket.number, currentTimeBucket.unit)
        .format("YYYY-MM-DD");
      this.queryReportFile();
    },
    // 跳转only-report的web页面
    handlePush() {
      window.open(`/only-report/index.html?token=${this.token}`);
    },
    // 所有报表
    loadNodeTree() {
      // 控制权限
      const auth = true;
      const needPec = true; // 是否展示pecReport
      let params = {
        auth,
        id: -1,
        needPec,
        userId: this.userId
      };
      customApi.getOnlyReportTreeAll(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []) || [];
          this.initTreeData = this._.cloneDeep(data);
          this.ElSelect_1.value = 0;
          this.ElSelect_1_change_out(0);
        }
      });
    },
    init() {
      let isOk = false;
      let userInfo = this.$store.state.userInfo;
      let pageNodes = this._.get(userInfo, "roles[0].pageNodes") || [];
      // 是否有only-report的权限，有才显示报表设计按钮
      isOk = Boolean(this._.find(pageNodes, ["id", "only_report"]));
      //如果是ROOT用户，则直接显示报表设计按钮
      if (this.userId === 1) {
        isOk = true;
      }
      this.hasOnlyReportAush = isOk;
      this.CetTree_1.inputData_in = [];

      this.loadNodeTree();
    },
    //时间组件变化
    CustomDatePicker_1_change(val) {
      this.CustomDatePicker_1.queryTime = val;
      this.queryCustomReportFile();
    },
    //切换时间间隔下拉框
    ElSelect_interval_change_out(val) {
      let dataConfig = this.CustomDatePicker_1.dataConfig;
      let startTime = this.CustomDatePicker_1.queryTime.startTime;
      if ([1, 2, 3].includes(val)) {
        dataConfig.time = this.$moment(startTime).startOf("M").valueOf();
        dataConfig.cycle = 3;
      } else {
        dataConfig.time = this.$moment(startTime).startOf("Y").valueOf();
        dataConfig.cycle = 5;
      }
      this.CustomDatePicker_1.dataConfig = this._.cloneDeep(dataConfig);
      this.$nextTick(() => {
        this.queryCustomReportFile();
      });
    },
    //切换查询报表类型节点树
    ElSelect_1_change_out(val) {
      this.currentNode = {};
      this.CetTree_1.selectNode = {};
      let initTreeData = this._.cloneDeep(this.initTreeData);
      let inputData = [];
      if (val === 0) {
        inputData = initTreeData;
      } else if (val === 1) {
        inputData = this.deepFilterTree1(initTreeData);
      } else if (val === 2) {
        inputData = this.deepFilterTree2(initTreeData);
      }
      this.CetTree_1.inputData_in = this._.cloneDeep(inputData);
    },
    // 筛选节点树，接收一个数组
    deepFilterTree1(list) {
      return list.filter(item => {
        if (Array.isArray(item.children)) {
          item.children = this.deepFilterTree1(item.children);
        }

        if (item.nodeType === 272695296 || item.nodeType === 1) {
          return item.children && item.children.length > 0;
        } else {
          return [272826368, 272760832, 2].includes(item.nodeType);
        }
      });
    },
    deepFilterTree2(list) {
      return list.filter(item => {
        if (Array.isArray(item.children)) {
          item.children = this.deepFilterTree2(item.children);
        }

        if (item.nodeType === 272695296 || item.nodeType === 1) {
          return item.children && item.children.length > 0;
        } else {
          return item.nodeType === 3;
        }
      });
    },
    queryCustomReportFile() {
      this.showExprot = false;
      const node = this.currentNode;
      this.isMreport = true;
      if (this._.get(node, "nodeType") !== 3) return;

      let params = this.getCustomParams();
      if (!params) return;
      if (this.isLoading) {
        return;
      }
      this.isLoading = true;
      const loading = new FullScreenLoading();
      loading.showLoading();
      customApi.getOnlyReportTemplate(node.nodeId, params, false).then(
        res => {
          if (res.code === 0) {
            let urlStr = "/onlinePreviewHtml/" + res.data;
            this.$nextTick(() => {
              const origin =
                this._.get(this.systemCfg, "reportsever", location.origin) ||
                location.origin;
              this.iframeSrc = `${origin}${urlStr}`;
              const iframe = this.$refs.iframe;
              iframe.onload = () => {
                this.isLoading = false;
                loading.hideLoading();
              };
            });
          } else {
            this.isLoading = false;
            loading.hideLoading();
          }
        },
        err => {
          this.showExprot = err.code === -1000001;
          this.isLoading = false;
          loading.hideLoading();
        }
      );
    },
    customExportReportFile() {
      const node = this.currentNode;
      if (this._.get(node, "nodeType") !== 3) return;

      let params = this.getCustomParams();
      if (!params) return;
      let urlStr = `/only-report/api/v1/template/query/download/${node.nodeId}?startTime=${params.startTime}&interval=${params.interval}&offsetCount=${params.offsetCount}`;

      common.downExcelGET(urlStr, {}, this.token, this.projectId);
    },
    getCustomParams() {
      if (!this.CustomDatePicker_1.queryTime) {
        return null;
      }
      let reportParam = {};

      // 动态报表参数转换
      let customReport = {
        12: {
          interval: 7,
          offsetCount: 24
        },
        14: {
          interval: 12,
          offsetCount: 1000
        },
        17: {
          interval: 14,
          offsetCount: 12
        }
      };
      reportParam = customReport[this.CustomDatePicker_1.queryTime.cycle];
      if (this.showInterval) {
        let tableCode = {
          1: {
            interval: 80,
            offsetCount: 1000
          },
          2: {
            interval: 120,
            offsetCount: 1000
          },
          3: {
            interval: 12,
            offsetCount: 1000
          },
          4: {
            interval: 14,
            offsetCount: 12
          }
        };
        reportParam = tableCode[this.ElSelect_interval.value];
      }

      return {
        startTime: this.$moment(
          this.CustomDatePicker_1.queryTime.startTime
        ).valueOf(),
        ...reportParam
      };
    }
  },
  created: function () {},
  mounted() {
    if (this.systemCfg.cachePage) {
      this.init();
    }
  },
  activated() {
    if (!this.systemCfg.cachePage) {
      this.init();
    }
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.data-row {
  float: left;
  @include margin_right(J1);
  width: 150px !important;
}
.EnergyReport {
  :deep(.el-date-editor--daterange.el-input),
  .el-date-editor--daterange.el-input__inner,
  .el-date-editor--timerange.el-input,
  .el-date-editor--timerange.el-input__inner,
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 140px !important;
  }
  :deep(.el-header) {
    @include background_color(BG1);
    padding: 0px;
    height: "58px";
  }
  :deep(table tr td) {
    border: 1px solid;
    @include border_color(B1);
  }
}
.reportHtml {
  ::-webkit-scrollbar {
    width: 16px;
    height: 16px;
  }

  /*定义滚动条的轨道颜色、内阴影及圆角*/
  ::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #f1f1f1;
    border-radius: 0;
  }

  /*定义滑块颜色、内阴影及圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 0;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #c1c1c1;
    &:hover {
      background-color: #a8a8a8;
    }
    &:active {
      background-color: #787878;
    }
  }
}
</style>
