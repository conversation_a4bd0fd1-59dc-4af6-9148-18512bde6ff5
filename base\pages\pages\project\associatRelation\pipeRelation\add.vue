<template>
  <!-- 1弹窗组件 -->
  <CetDialog
    class="CetDialog small"
    v-bind="CetDialog_1"
    v-on="CetDialog_1.event"
  >
    <el-container class="fullheight flex-column bg1 brC1 pJ3 plJ4 prJ4">
      <div class="clearfix mbJ1">
        <!-- <el-checkbox class="fl lhHm" v-model="checkStrictly">
          默认选中子节点
        </el-checkbox> -->
        <customElSelect
          class="fr"
          :prefix_in="$T('能源类型')"
          v-model="ElSelect_energytype.value"
          v-bind="ElSelect_energytype"
          v-on="ElSelect_energytype.event"
        >
          <ElOption
            v-for="item in ElOption_energytype.options_in"
            :key="item[ElOption_energytype.key]"
            :label="item[ElOption_energytype.label]"
            :value="item[ElOption_energytype.value]"
            :disabled="item[ElOption_energytype.disabled]"
          ></ElOption>
        </customElSelect>
      </div>
      <!-- 父子关联 -->
      <CetGiantTree
        v-if="false"
        v-show="checkStrictly"
        class="switch-tree"
        v-bind="CetGiantTree_1"
        v-on="CetGiantTree_1.event"
      ></CetGiantTree>
      <!-- 父子不关联 -->
      <CetGiantTree
        v-show="!checkStrictly"
        ref="giantTree2"
        class="switch-tree separate"
        v-bind="CetGiantTree_2"
        v-on="CetGiantTree_2.event"
      ></CetGiantTree>
      <div class="tagBox" v-if="showTagList">
        <el-tag
          :key="index"
          v-for="(tag, index) in allTreeCheckedNodes"
          :closable="tag.closable"
          :disable-transitions="false"
          @close="handleClose(index)"
        >
          {{ tag.name }}
        </el-tag>
      </div>
    </el-container>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import commonApi from "@/api/custom.js";
import ELECTRICAL_DEVICE from "@/store/electricaldevice.js";
import { httping } from "@omega/http";
//能源类型-电
const ENERGY_TYPE_ELECTRIC = 2;
export default {
  name: "shareRate",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    // 配电设备模型列表
    roomDeviceList: {
      type: Array
    },
    currentNode: {
      type: Object
    },
    // 已关联管网设备列表
    deviceList: {
      type: Array,
      default() {
        return [];
      }
    },
    energyType_in: {
      type: Number
    },
    outlastNode: {
      type: Object
    },
    // 是否展示已关联列表
    showTagList: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    totalEnergyType() {
      return this.$store.state.systemCfg.totalEnergyType;
    },
    totalEnergyTypeCO2() {
      return this.$store.state.systemCfg.totalEnergyTypeCO2;
    },
    totalEnergyTypeC() {
      return this.$store.state.systemCfg.totalEnergyTypeC;
    }
  },

  data() {
    return {
      allTreeCheckedNodes: [],
      flag: false,
      checkStrictly: false,
      CetDialog_1: {
        title: $T("添加管网设备"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // energytype组件
      ElSelect_energytype: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_energytype_change_out
        }
      },
      // energytype组件
      ElOption_energytype: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      checkedNodes1: [],
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: this.setNodeClasses
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out //勾选节点输出
        }
      },
      sortNodes: [],
      checkedNodes2: [],
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: this.setNodeClasses
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_2_checkedNodes_out //勾选节点输出
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.flag = false;
      vm.checkStrictly = false;
      vm.CetDialog_1.openTrigger_in = val;
      this.allTreeCheckedNodes = this.deviceList.map(item => {
        return {
          ...item,
          closable: false
        };
      });
      this.sortNodes = [];
      this.queryProjectEnergyList_out();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    inputData_in(val) {
      this.CetDialog_1.inputData_in = val;
    },
    checkStrictly(val) {
      if (val) {
        this.CetGiantTree_1.checkedNodes = this._.cloneDeep(this.checkedNodes2);
      } else {
        this.CetGiantTree_2.checkedNodes = this._.cloneDeep(this.checkedNodes1);
      }
    }
  },

  methods: {
    handleClose(index) {
      this.allTreeCheckedNodes.splice(index, 1);
      const checkNodes = this.allTreeCheckedNodes.filter(item => item.closable);
      if (this.checkStrictly) {
        this.CetGiantTree_1.checkedNodes = checkNodes;
        if (!checkNodes.length) {
          this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
        }
      } else {
        this.CetGiantTree_2.checkedNodes = checkNodes;
        if (!checkNodes.length) {
          this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
        }
      }
    },
    setNodeClasses(treeId, treeNode) {
      return treeNode.disabledClass
        ? { add: ["disabledClass"] }
        : { remove: ["disabledClass"] };
    },
    CetGiantTree_1_checkedNodes_out(val) {
      this.allTreeCheckedNodes = (val || [])
        .map(item => {
          return {
            ...item,
            closable: true
          };
        })
        .concat(this.deviceList);
      this.checkedNodes1 = this._.cloneDeep(val);
    },
    CetGiantTree_2_checkedNodes_out(val) {
      this.allTreeCheckedNodes = (val || [])
        .map(item => {
          return {
            ...item,
            closable: true
          };
        })
        .concat(this.deviceList);
      if (val.length > this.sortNodes.length) {
        // 找出树节点中新勾选的节点
        let node = _.differenceBy(val, this.sortNodes, "tree_id");
        this.sortNodes.push(node[0]);
      } else {
        // 找出树中取消勾选的节点
        let node = _.differenceBy(this.sortNodes, val, "tree_id");
        _.pullAllBy(this.sortNodes, node);
      }
      this.checkedNodes2 = this._.cloneDeep(val);
    },
    getTreeData() {
      let data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [],
        treeReturnEnable: true
      };

      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.checkedNodes1 = [];
      this.CetGiantTree_1.checkedNodes = [];
      this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
      this.checkedNodes2 = [];
      this.CetGiantTree_2.checkedNodes = [];
      const energytype = this.ElSelect_energytype.value;
      //判断选择电能源类型，如果是则显示配电房和管道房，其他类型只显示管道房
      if (energytype === ENERGY_TYPE_ELECTRIC) {
        data.subLayerConditions = [
          {
            filter: {
              expressions: [
                { limit: [1, 6, 7], operator: "IN", prop: "roomtype" }
              ]
            },
            modelLabel: "room"
          },
          {
            filter: {
              expressions: [
                { limit: energytype, operator: "EQ", prop: "energytype" }
              ]
            },
            modelLabel: "pipeline"
          },
          {
            filter: null,
            modelLabel: "linesegment",
            props: []
          },
          {
            filter: null,
            modelLabel: "breaker",
            props: []
          }
        ];
        let roomDeviceList = [];
        ELECTRICAL_DEVICE.forEach(item => {
          var obj = {
            filter: null,
            modelLabel: item.value,
            props: []
          };
          roomDeviceList.push(obj);
        });

        data.subLayerConditions =
          data.subLayerConditions.concat(roomDeviceList);
      } else {
        data.subLayerConditions = [
          {
            filter: {
              expressions: [{ limit: [6], operator: "IN", prop: "roomtype" }]
            },
            modelLabel: "room"
          },
          {
            filter: {
              expressions: [
                { limit: energytype, operator: "EQ", prop: "energytype" }
              ]
            },
            modelLabel: "pipeline"
          }
        ];
      }
      httping({
        url: `/eem-service/v1/node/nodeTree/simple`,
        method: "POST",
        data
      }).then(res => {
        if (res.code === 0) {
          var data = this._.get(res, "data[0].children", []) || [];
          this.setTreeLeaf_filterText1Change(data);
          this.CetGiantTree_1.inputData_in = data;
          this.CetGiantTree_2.inputData_in = data;
          if (!_.isEmpty(this.outlastNode) && this.outlastNode.node) {
            this.CetGiantTree_2.selectNode = this._.cloneDeep(
              this.outlastNode.node
            );
            this.expandNode(
              [this.outlastNode.node],
              "tree_id",
              this.$refs.giantTree2.ztreeObj
            );
          }
        }
      });
    },
    // 展开节点
    expandNode(nodes, key, ztreeObj) {
      setTimeout(() => {
        nodes.forEach(item => {
          // 找到当前节点位置
          let node = ztreeObj.getNodeByParam(key, item[key]);
          let parentNodes = [],
            parentNode = node && node.getParentNode();
          // 获取当前节点的所有父节点
          while (parentNode) {
            parentNodes.push(parentNode);
            parentNode = parentNode.getParentNode();
          }
          // 将该节点全部展开
          parentNodes.forEach(i => {
            ztreeObj.expandNode(i, true);
          });
        });
      }, 0);
    },
    setTreeLeaf_filterText1Change(nodes) {
      if (!nodes) {
        return;
      }
      nodes.forEach(item => {
        if (
          !["project", "room", "powerdiscabinet", "arraycabinet"].includes(
            item.modelLabel
          )
        ) {
          item.leaf = true;
        }
        if (
          this.deviceList.filter(
            i => i.id === item.id && i.modelLabel === item.modelLabel
          ).length === 0
        ) {
          item.disabledClass = false;
        } else {
          item.disabledClass = true;
        }
        this.setTreeLeaf_filterText1Change(this._.get(item, "children", []));
      });
    },
    // 保存
    addSupplyRelation_out() {
      var me = this;
      let lastNode = {
        node: this.sortNodes.pop(),
        energyType: this.ElSelect_energytype.value
      };
      var param = [];
      var url = `/eem-service/v1/connect/relationship`;
      let checkedNodes = me.checkedNodes1 || [];
      if (!this.checkStrictly) {
        checkedNodes = me.checkedNodes2 || [];
      }
      if (!me.currentNode) {
        return;
      }
      checkedNodes.forEach(function (value) {
        if (value.modelLabel !== "room") {
          if (
            me.deviceList.filter(
              i => i.id === value.id && i.modelLabel === value.modelLabel
            ).length === 0
          ) {
            param.push({
              createtime: new Date().getTime(),
              objectlabel: value.modelLabel,
              objectid: value.id,
              supplytoid: me.currentNode.id,
              supplytolabel: me.currentNode.modelLabel,
              modelLabel: "energysupplyto",
              starttime: 0
              // starttime: new Date().getTime()
            });
          }
        }
      });
      if (param.length === 0) {
        this.$message({
          message: $T("请选择管网设备"),
          type: "warning"
        });
        return;
      }
      httping({
        url,
        data: param,
        method: "PUT"
      }).then(
        function (res) {
          if (res.code === 0) {
            me.$message({
              message: $T("保存成功"),
              type: "success"
            });
            me.$emit("updata_out", lastNode);
            me.CetDialog_1.closeTrigger_in = new Date().getTime();
          }
        },
        function () {}
      );
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      this.addSupplyRelation_out();
    },
    ElSelect_energytype_change_out() {
      this.getTreeData();
    },
    // 获取能耗类型
    queryProjectEnergyList_out() {
      var _this = this;
      const params = {
        projectId: this.projectId
      };
      commonApi.queryProjectEnergyList(params).then(res => {
        if (res.code === 0) {
          const energytypeList = (res.data || []).filter(
            item =>
              [
                this.totalEnergyType,
                this.totalEnergyTypeCO2,
                this.totalEnergyTypeC
              ].indexOf(item.energytype) === -1
          );
          _this.ElOption_energytype.options_in = energytypeList;
          let energyVal =
            _this._.get(energytypeList, "[0].energytype", null) || null;
          if (_this.ergyType_in) {
            energyVal = _this.ergyType_in;
          }
          if (!_.isEmpty(_this.outlastNode) && _this.outlastNode.energyType) {
            energyVal = _this.outlastNode.energyType;
          }
          _this.ElSelect_energytype.value = this._.cloneDeep(energyVal);
        } else {
          _this.ElOption_energytype.options_in = [];
          _this.ElOption_energytype.value = null;
        }
        this.flag = true;
        this.ElSelect_energytype_change_out();
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep() {
    .el-dialog__body {
      @include background_color(BG);
      @include padding(J1);
    }
  }
  .tagBox {
    min-height: 100px;
    border-top: 1px solid;
    border-bottom: 1px solid;
    @include border_color(B1);
    @include padding(J3 0);
    :deep(.el-tag) {
      @include margin_right(J1);
      @include margin_top(J1);
    }
  }
  .lhHm {
    @include line_height(Hm);
  }
}
.switch-tree {
  height: 500px;
  // :deep(& > .el-tree > .el-tree-node > .el-tree-node__content) {
  //   .el-tree-node__expand-icon + .el-checkbox {
  //     display: none;
  //   }
  // }
  // 模拟打钩
  :deep(.el-checkbox.is-disabled .el-checkbox__inner) {
    background: url("../assets/check.png") no-repeat center center;
  }
  :deep(.disabledClass) {
    position: relative;
    &::after {
      content: "";
      position: absolute;
      background: url("../assets/check.png") no-repeat center center;
      background-size: 100% 100%;
      height: 14px;
      left: -22px;
      top: 4px;
      width: 14px;
    }
    .node_name {
      position: relative;
      &::before {
        content: "";
        position: absolute;
        background: url("../assets/check.png") no-repeat center center;
        background-size: 100% 100%;
        height: 14px;
        left: -22px;
        top: 5px;
        width: 14px;
        cursor: no-drop;
      }
    }
  }
  &.separate {
    :deep(.ztree > div > li > span.button.chk) {
      display: none !important;
    }
  }
}
</style>
