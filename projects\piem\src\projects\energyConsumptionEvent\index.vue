﻿<template>
  <div class="page eem-common">
    <el-container class="flex-column fullheight">
      <div class="backPlatform" v-if="isJump" @click="jumpBack">
        <i class="el-icon-d-arrow-left"></i>
        {{ $T("返回") }}
      </div>
      <el-container class="fullheight">
        <el-aside width="315px" class="eem-aside">
          <CetGiantTree
            v-bind="CetGiantTree_1"
            v-on="CetGiantTree_1.event"
            class="treeStyle"
          ></CetGiantTree>
        </el-aside>
        <el-container class="mlJ3 fullheight">
          <EnergyWarning
            :clickNode="clickNode"
            :treeData_in="CetGiantTree_1.inputData_in"
            @setClickNode="setClickNode_out"
          ></EnergyWarning>
        </el-container>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import EnergyWarning from "./energyconsumptionevent/EnergyConsumptionEvent";
import TREE_PARAMS from "@/store/treeParams.js";
import { httping } from "@omega/http";
import piemCommon from "@/utils/common";
export default {
  name: "eventCenter",
  components: {
    EnergyWarning
  },

  props: { isJump1: Boolean, keepParams1: Object },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data() {
    return {
      isJump: false,
      keepParams: {},
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: this.setNodeClasses
          },
          callback: {
            beforeClick: this.beforeClick
          }
        },
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      currentNode: null,
      clickNode: null
    };
  },

  watch: {
    isJump1(val) {
      if (this.$route.name === "energyEfficiencyEvent") {
        this.isJump = val;
      }
    }
  },
  methods: {
    //返回查询页
    jumpBack() {
      if (this.$route.name === "energyConsumptionEvent") {
        this.$router.push({
          name: "eventNumberRank",
          params: {
            keepParams: this.keepParams
          }
        });
      } else if (this.$route.name === "energyEfficiencyEvent") {
        this.$router.push({
          name: "eventNumberRank",
          params: {
            keepParams: this.keepParams1
          }
        });
      }
    },
    CetTree_1_currentNode_out(val) {
      if (!val && val.childSelectState == 2) {
        return;
      }
      this.clickNode = this._.cloneDeep(val);
    },
    getTreeData() {
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "oilcompany",
        subLayerConditions: TREE_PARAMS.powerEquipment,
        treeReturnEnable: true
      };
      httping({
        url: "/eem-service/v1/node/nodeTree/simple",
        method: "POST",
        data
      }).then(res => {
        if (res.code === 0) {
          if (res.code === 0) {
            _this.handleDisablingTree(res.data);
            _this.CetGiantTree_1.inputData_in = res.data;
            const obj = _this._.find(
              piemCommon.findFirstChildSelectState(res.data),
              ["childSelectState", 1]
            );
            _this.CetGiantTree_1.selectNode = obj;
          } else {
            _this.$message.error(res.msg);
          }
        }
      });
    },
    setClickNode_out(val) {
      if (!val) {
        return;
      }
      this.CetGiantTree_1.selectNode = val;
      this.clickNode = val;
    },
    /**
     * 设置节点禁用
     */
    handleDisablingTree(nodes) {
      nodes.forEach(item => {
        if (item.childSelectState == 2) {
          item.disableNode = true;
        } else {
          item.disableNode = false;
        }
        if (item.children && item.children.length > 0) {
          this.handleDisablingTree(item.children);
        }
      });
    },
    setNodeClasses(treeId, treeNode) {
      return treeNode.disableNode
        ? { add: ["disableNode"] }
        : { remove: ["disableNode"] };
    },
    /**
     * 节点是否可选择
     */
    beforeClick(treeId, treeNode) {
      if (treeNode.childSelectState == 2) {
        return false;
      }
      return true;
    }
  },

  created: function () {},
  mounted() {},
  activated() {
    this.getTreeData();
  },
  deactivated() {
    this.isJump = false;
    this.clickNode = null;
  },
  beforeRouteEnter(to, from, next) {
    if (from.name === "eventNumberRank") {
      next(vm => {
        vm.isJump = true;
        vm.keepParams = vm.$route.params.keepParams;
      });
    } else {
      next();
    }
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.backPlatform {
  width: 50px;
  margin: -12px 0 8px 0;
  cursor: pointer;
  @include font_color(ZS);
}

.treeStyle {
  :deep(.disableNode) {
    @include font_color(T6 !important);
    cursor: not-allowed !important;
    opacity: 0.6;
  }
}
</style>
