﻿<template>
  <!-- 1弹窗组件 -->
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-show="showConfirm"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
    <div style="overflow: auto; max-height: calc(84vh - 156px)">
      <el-container class="eem-cont-c1" style="height: 100%">
        <el-header
          class="event-detail-header text-center"
          height="25px"
          style="padding: 0px; line-height: 25px"
        >
          <span>{{ $T("基本信息") }}</span>
        </el-header>
        <el-main class="event-detail-content" style="padding: 0px">
          <CetForm
            :data.sync="CetForm_1.data"
            v-bind="CetForm_1"
            v-on="CetForm_1.event"
          >
            <el-form-item :label="$T('时间') + ':'" prop="eventTime">
              <div class="label" style="width: 100%; text-align: right">
                {{ CetDialog_1.data.eventtime | formatterDate }}
              </div>
            </el-form-item>
            <el-form-item :label="$T('事件类型') + ':'" prop="eventtype">
              <div class="label" style="width: 100%; text-align: right">
                {{ CetDialog_1.data.eventtype$text }}
              </div>
            </el-form-item>
            <el-form-item :label="$T('设备名称') + ':'" prop="name">
              <div class="label" style="width: 100%; text-align: right">
                {{ CetDialog_1.data.name }}
              </div>
            </el-form-item>
            <el-form-item
              :label="$T('状态') + ':'"
              style="height: 43px"
              prop="confirmeventstatus"
            >
              <el-tag class="fr" :type="isState(CetDialog_1.data)">
                {{ CetDialog_1.data.confirmeventstatus$text }}
              </el-tag>
            </el-form-item>
            <el-form-item
              :label="$T('事件等级') + ':'"
              style="height: 43px"
              prop="eventtype"
            >
              <el-tag class="fr" :type="isLevel(CetDialog_1.data)">
                {{ CetDialog_1.data.level$text }}
              </el-tag>
            </el-form-item>
            <el-form-item :label="$T('收敛事件') + ':'" prop="convergence">
              <div class="label" style="width: 100%; text-align: right">
                {{ CetDialog_1.data.convergence }}
              </div>
            </el-form-item>
            <el-form-item :label="$T('描述') + ':'" prop="description">
              <el-tooltip
                effect="light"
                :content="CetDialog_1.data.description"
                placement="bottom"
              >
                <span class="label" style="width: 100%; text-align: right">
                  {{ CetDialog_1.data.description || "--" }}
                </span>
              </el-tooltip>
            </el-form-item>
          </CetForm>
        </el-main>
      </el-container>
      <el-container class="eem-cont-c1 mtJ3" style="height: 100%">
        <el-header
          class="event-detail-header text-center"
          height="25px"
          style="padding: 0px; line-height: 25px"
        >
          <span>{{ $T("层级对象") }}</span>
        </el-header>
        <el-main class="event-detail-content" style="padding: 0px">
          <div style="height: 40px; line-height: 40px; text-align: center">
            <span>{{ CetDialog_1.data.levelName || "--" }}</span>
          </div>
        </el-main>
      </el-container>
      <el-container
        class="eem-cont-c1 mtJ3"
        style="height: 100%"
        v-show="CetDialog_1.data.confirmeventstatus !== 1"
      >
        <el-header
          class="event-detail-header text-center"
          height="25px"
          style="padding: 0px; line-height: 25px"
        >
          <span>{{ $T("确认信息") }}</span>
        </el-header>
        <el-main class="event-detail-content" style="padding: 0px">
          <CetForm
            :data.sync="CetForm_2.data"
            v-bind="CetForm_2"
            v-on="CetForm_2.event"
          >
            <el-form-item :label="$T('确认人') + ':'" prop="operatorname">
              <div class="label" style="width: 100%; text-align: right">
                {{ CetDialog_1.data.operator }}
              </div>
            </el-form-item>
            <el-form-item :label="$T('联系方式') + ':'" prop="phone">
              <div class="label" style="width: 100%; text-align: right">
                {{ CetDialog_1.data.phone }}
              </div>
            </el-form-item>
            <el-form-item
              :label="$T('确认意见') + ':'"
              prop="name"
              style="width: 100%"
            >
              <el-tooltip placement="top" :content="CetDialog_1.data.remark">
                <div class="label" style="width: 100%; text-align: right">
                  {{ CetDialog_1.data.remark }}
                </div>
              </el-tooltip>
            </el-form-item>
          </CetForm>
        </el-main>
      </el-container>
      <el-container
        class="eem-cont-c1 mtJ3"
        style="height: 100%"
        v-show="showConfirm"
      >
        <el-header
          class="event-detail-header text-center"
          height="25px"
          style="padding: 0px; line-height: 25px"
        >
          <span>{{ $T("处理详情") }}</span>
        </el-header>
        <el-main class="event-detail-content" style="padding: 0px">
          <div class="confirm-opition clearfix">
            <span class="fl">{{ $T("确认意见") }}</span>
          </div>
          <ElInput
            class="confirm-input"
            v-model="ElInput_1.value"
            v-bind="ElInput_1"
            v-on="ElInput_1.event"
          ></ElInput>
          <!-- <div class="list clearfix">
            <span class="fl">确认时间：</span>
            <span class="fl value-color">{{ this.$moment().format("YYYY-MM-DD HH:mm:ss") }}</span>
          </div> -->
          <div class="list clearfix">
            <span class="fl">{{ $T("联系方式") }}：</span>
            <span class="fl value-color">{{ phone || "--" }}</span>
          </div>
          <div class="list clearfix">
            <span class="fl">{{ $T("操作人") }}：</span>
            <span class="fl value-color">{{ userInfo.nicName }}</span>
          </div>
        </el-main>
      </el-container>
    </div>
  </CetDialog>
</template>
<script>
import common from "eem-utils/common";
import moment from "moment";
import customApi from "@/api/custom";
export default {
  name: "EnergyConsumptionEventDetail",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userInfo() {
      return this.$store.state.userInfo;
    },
    showConfirm() {
      return (
        this.$checkPermission("systemevent_confirm") &&
        this.CetDialog_1.data.confirmeventstatus === 1
      );
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: $T("事件详情"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        },
        data: {
          channelId: "",
          code1: "",
          code2: "",
          description: "",
          deviceId: "",
          eveStr1: "",
          eveStr2: "",
          eventByte: "",
          eventClass: "",
          eventTime: "",
          eventType: "",
          hasWave: "",
          id: "",
          msec: "",
          pecName: "",
          stationFlag: "",
          stationId: "",
          confirmeventstatus: 0,
          confirmeventstatus$text: "",
          eventtype$text: "",
          level$text: "",
          energyType$text: "",
          convergence: 0,
          operator: "",
          updatetime: 0,
          remark: ""
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: true,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "90px",
        labelPosition: "left",
        rules: {},
        event: {
          currentData_out: this.CetForm_1_currentData_out,
          saveData_out: this.CetForm_1_saveData_out,
          finishData_out: this.CetForm_1_finishData_out,
          finishTrigger_out: this.CetForm_1_finishTrigger_out
        }
      },
      CetForm_2: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "90px",
        labelPosition: "left",
        rules: {},
        event: {
          currentData_out: this.CetForm_2_currentData_out,
          saveData_out: this.CetForm_2_saveData_out,
          finishData_out: this.CetForm_2_finishData_out,
          finishTrigger_out: this.CetForm_2_finishTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        config: {
          title: $T("升级"),
          type: "primary",
          plain: true
        }
      },
      ElInput_1: {
        value: "",
        style: {},
        rows: 8,
        type: "textarea",
        maxlength: 255,
        showWordLimit: true,
        required: true,
        placeholder: $T("请输入内容（必需）"),
        event: {
          change: this.ElInput_1_change_out,
          input: this.ElInput_1_input_out
        }
      },
      phone: ""
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {
      var vm = this;
      vm.CetDialog_1.queryId_in = val;
    },
    inputData_in(val) {
      this.CetDialog_1.inputData_in = this._.cloneDeep(val);
      this.CetDialog_1.data = this._.cloneDeep(val);
      // this.GetUpdateTime_out(null);
      if (val.confirmeventstatus === 1) {
        this.ElInput_1.value = "";
        let customConfig = this.userInfo.customConfig
          ? JSON.parse(this.userInfo.customConfig)
          : null;
        if (customConfig !== {} && customConfig !== null) {
          this.phone = customConfig.phone || this.userInfo.mobilePhone;
        } else {
          this.phone = "";
        }
      } else {
        this.GetUserName_out(null);
      }
    }
  },

  methods: {
    CetButton_2_statusTrigger_out(val) {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      var eventList = [this.inputData_in];
      this.eventConfirm_out(eventList);
    },
    CetDialog_1_open_out(val) {},
    CetDialog_1_close_out(val) {},
    CetForm_1_currentData_out(val) {},
    CetForm_1_saveData_out(val) {},
    CetForm_1_finishData_out(val) {},
    CetForm_1_finishTrigger_out(val) {},
    CetForm_2_currentData_out(val) {},
    CetForm_2_saveData_out(val) {},
    CetForm_2_finishData_out(val) {},
    CetForm_2_finishTrigger_out(val) {},
    ElInput_1_change_out(val) {},
    ElInput_1_input_out(val) {
      this.CetButton_confirm.disable_in = !val.length;
    },
    GetUserName_out(val) {
      let vm = this,
        user_id = vm.CetDialog_1.data.operator_id;

      if (!user_id) {
        vm.CetDialog_1.data.operatorname = "";
        return;
      }
      customApi.queryUserInfoById(user_id).then(response => {
        if (response.code === 0) {
          if (response.data.mobilePhone) {
            vm.$set(
              vm.CetDialog_1.data,
              "phone",
              response.data.mobilePhone || "--"
            );
            vm.$set(
              vm.CetDialog_1.data,
              "operatorname",
              vm._.get(response, ["data", "name"], "--")
            );
          } else {
            vm.$set(
              vm.CetDialog_1.data,
              "operatorname",
              vm._.get(response, ["data", "nodeName"], "--")
            );
            let customConfig = response.data.customConfig
              ? JSON.parse(response.data.customConfig)
              : null;
            if (customConfig !== {} && customConfig !== null) {
              vm.$set(vm.CetDialog_1.data, "phone", customConfig.phone);
            } else {
              vm.$set(vm.CetDialog_1.data, "phone", "");
            }
          }
        }
      });
    },
    GetUpdateTime_out(val) {
      let vm = this,
        user_id = vm.CetDialog_1.data.operator;

      // 修复没有确认人却有确认时间的问题
      if (!user_id) {
        vm.CetDialog_1.data.updatetime = null;
      }
    },
    getConfirmEventData_out(events) {
      var me = this;
      var list = [];
      if (!events || !events.length || events.length === 0) {
        return;
      }
      this._(events).forEach(function (item) {
        item.modelLabel = item.modelLabel ? item.modelLabel : "systemevent";
        item.confirmeventstatus = 3;
        item.operator = me.userInfo.nicName;
        item.operator_id = me.userInfo.id;
        item.remark = me.ElInput_1.value;
        item.updatetime = new Date().getTime();
      });
      return events;
    },

    eventConfirm_out(events) {
      var me = this,
        url,
        list;
      list = me.getConfirmEventData_out(events);
      if (!list || list.length === 0) {
        return;
      }
      url = `/eem-service/v1/alarmEvent/confirmEvents`;
      common.requestData(
        {
          url: url,
          data: list
        },
        res => {
          this.$message.success($T("操作成功"));
          me.CetDialog_1.closeTrigger_in = new Date().getTime();
          me.$emit("finishTrigger_out", new Date().getTime());
        }
      );
    },
    isLevel(val) {
      // 预警直接展示“预警”
      if (val.eventtype == 701) {
        return "level5";
      } else {
        if (val.level == 1) {
          return "level1";
        } else if (val.level == 2) {
          return "level2";
        } else if (val.level == 3) {
          return "level3";
        } else if (val.level == 4) {
          return "level4";
        }
      }
    },
    isState(val) {
      if (val.confirmeventstatus === 3) {
        return "success";
      } else {
        return "danger";
      }
    }
  },
  filters: {
    formatterDate(value) {
      if (value) {
        return moment(value).format("YYYY-MM-DD HH:mm:ss.SSS");
      } else {
        return "--";
      }
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.label {
  width: 100%;
  text-align: right;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.event-detail-header {
  width: 100%;
  border-radius: 11px;
  margin-bottom: 10px;
  @include background_color(B1);
}
.event-detail-content {
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
}
.el-form {
  width: 100%;
}

.el-form-item {
  margin: 0 !important;
  float: left;
  width: 50%;
  box-sizing: border-box;
  padding: 0 8px;
}
.confirm-opition {
  box-sizing: border-box;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  border-radius: 5px 5px 0 0;
}
.confirm-input {
  border: solid;
  border-width: 1px;
  border-radius: 5px;
  box-sizing: border-box;
  @include border_color(B1);
  :deep(.el-textarea__inner) {
    border-width: 0;
  }
}
.event-detail-content {
  :deep(.el-tag.el-tag--level1) {
    background-color: #ff0000;
    border-color: #ff0000;
    color: #fff;
  }
  :deep(.el-tag.el-tag--level2) {
    background-color: #ff9900;
    border-color: #ff9900;
    color: #fff;
  }
  :deep(.el-tag.el-tag--level3) {
    background-color: #0152d9;
    border-color: #0152d9;
    color: #fff;
  }
  :deep(.el-tag.el-tag--level4) {
    background-color: #18beb7;
    border-color: #18beb7;
    color: #fff;
  }
  :deep(.el-tag.el-tag--level5) {
    background-color: #28be44;
    border-color: #28be44;
    color: #fff;
  }
}
</style>
