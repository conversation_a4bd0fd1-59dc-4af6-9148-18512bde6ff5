<template>
  <div class="page eem-common">
    <el-container style="height: 100%">
      <el-aside width="360px" class="eem-aside">
        <statistics
          :resetTrigger_in="statistics.resetTrigger_in"
          @nextoverhauldate_out="statistics_nextoverhauldate_out"
        />
      </el-aside>
      <el-container class="fullheight mlJ3 flex-column eem-min-width-mini">
        <el-radio-group
          size="small"
          class="radiogroup"
          v-model="selectedMenu"
          @change="titleChange(selectedMenu)"
        >
          <el-radio-button
            v-for="menu in menus"
            :label="menu.label"
            :key="menu.id"
          >
            {{ menu.label }}
          </el-radio-button>
        </el-radio-group>
        <div class="eem-container mbJ3 mtJ3">
          <div class="fr">
            <ElInput
              class="fl mrJ1"
              v-model="queryCondition.name"
              v-bind="ElInput_name"
              v-on="ElInput_name.event"
            ></ElInput>
            <CetButton
              class="fl mrJ1"
              v-bind="CetButton_query"
              v-on="CetButton_query.event"
            ></CetButton>
            <CetButton
              class="fl mrJ1"
              v-bind="CetButton_add"
              v-on="CetButton_add.event"
            ></CetButton>
            <el-dropdown class="mlJ3 more" @command="handleCommand">
              <span class="el-dropdown-link">
                {{ $T("更多") }}
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="admin" v-show="actionTitle === 1">
                  {{ $T("模板管理") }}
                </el-dropdown-item>
                <el-dropdown-item command="import" :disabled="this.importBool">
                  {{ $T("导入") }}
                </el-dropdown-item>
                <el-dropdown-item command="export" :disabled="this.exportBool">
                  {{ $T("导出") }}
                </el-dropdown-item>
                <el-dropdown-item command="batchAdd" v-show="actionTitle === 1">
                  {{ $T("批量新增") }}
                </el-dropdown-item>
                <el-dropdown-item
                  command="batchDelete"
                  :class="{
                    delete: this.selectedIds && this.selectedIds.length
                  }"
                  :disabled="
                    this.selectedIds && this.selectedIds.length ? false : true
                  "
                >
                  {{ $T("批量删除") }}
                </el-dropdown-item>
                <el-dropdown-item
                  command="batchDelObject"
                  v-show="actionTitle === 1"
                >
                  {{ $T("批量清除") }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div class="flex-auto eem-container">
          <sandingBoxTable
            ref="sandTable"
            :actionTitle_in="actionTitle"
            v-bind="sandingBoxTable"
            v-on="sandingBoxTable.event"
            :queryCondition_in="queryCondition"
            @selectionAll="selectionAll"
          />
        </div>
      </el-container>
    </el-container>
    <add :actionTitle_in="actionTitle" v-bind="add" v-on="add.event" />
    <advancedQuery v-bind="advancedQuery" v-on="advancedQuery.event" />
    <templateAdmin v-bind="templateAdmin" v-on="templateAdmin.event" />
    <batchAddObj
      v-bind="batchAddObj"
      v-on="batchAddObj.event"
      @save_object="save_object"
    />
    <UploadDialog v-bind="uploadDialog" v-on="uploadDialog.event" />
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import common from "eem-utils/common.js";
import statistics from "./statistics.vue";
import add from "./components/add.vue";
import advancedQuery from "./components/advancedQuery.vue";
import sandingBoxTable from "./components/sandingBoxTable.vue";
import templateAdmin from "./components/templateAdmin/templateAdmin.vue";
import batchAddObj from "./components/batchAddObj.vue";
import UploadDialog from "eem-components/uploadDialog";
export default {
  name: "sandingBok",
  components: {
    statistics,
    add,
    advancedQuery,
    sandingBoxTable,
    templateAdmin,
    batchAddObj,
    UploadDialog
  },
  data(vm) {
    return {
      menus: [
        { id: 1, label: $T("设备台账") },
        { id: 2, label: $T("检定记录台账") }
      ],
      selectedMenu: $T("设备台账"),
      // 选中删除的行
      selectedIds: [],
      // 高级查询条件
      queryCondition: {},
      actionTitle: 1,
      statistics: {
        resetTrigger_in: new Date().getTime()
      },
      sandingBoxTable: {
        resetTrigger_in: new Date().getTime(),
        tableResetTrigger_in: new Date().getTime(),
        event: {
          columnArr_out: this.sandingBoxTable_columnArr_out,
          tableEdit_out: this.sandingBoxTable_tableEdit_out,
          exportDisableStatus_out: this.sandingBoxTable_exportDisableStatus_out,
          upStatistics_out: this.sandingBoxTable_upStatistics_out
        }
      },
      templateAdmin: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          updata_out: this.templateAdmin_updata_out
        }
      },
      advancedQuery: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        template_in: [],
        queryCondition_in: null,
        event: {
          queryCondition_out: this.CetButton_query_queryCondition_out
        }
      },
      add: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        template_in: [],
        editData_in: null,
        event: {
          upData_out: this.add_updata_out,
          upTableData_out: this.add_upTableData_out
        }
      },
      batchAddObj: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          // updata_out: this.templateAdmin_updata_out
        }
      },
      CetButton_query: {
        visible_in: true,
        disable_in: false,
        title: $T("高级查询"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_query_statusTrigger_out
        }
      },
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("新增"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      ElInput_name: {
        value: "",
        style: {
          width: "160px"
        },
        suffixIcon: "el-icon-search",
        placeholder: $T("请输入仪表名称"),
        size: "small",
        event: {
          change: this.ElInput_name_change_out
        }
      },
      uploadDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        extensionNameList_in: [".xlsx"],
        hideDownload: false,
        dialogTitle: $T("导入"),
        event: {
          download: this.uploadDialog_download,
          uploadFile: this.uploadDialog_uploadFile
        }
      },
      exportBool: false,
      importBool: false
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  watch: {},
  methods: {
    titleChange(val) {
      const id = this.menus.find(item => item.label === val)?.id;
      this.actionTitle = id;
      this.sandingBoxTable.resetTrigger_in = new Date().getTime();
      this.queryCondition = {};
    },
    uploadDialog_uploadFile(val) {
      const formData = new FormData();
      const checkFormData = new FormData();
      formData.append("file", val.file);
      checkFormData.append("xlsFile", val.file);
      if (this.actionTitle === 1) {
        // 导入检查
        customApi
          .importStandingBookCheck(checkFormData, this.projectId)
          .then(response => {
            if (response.code === 0) {
              if (response.data) {
                this.$confirm(
                  $T(
                    "检测到有设备编号与原来重复，继续导入会覆盖原来的信息，是否继续?"
                  ),
                  $T("提示"),
                  {
                    cancelButtonText: $T("取消"),
                    confirmButtonText: $T("继续")
                  }
                )
                  .then(() => {
                    customApi
                      .importDeviceStandingBook(formData, this.projectId)
                      .then(response => {
                        if (response.code === 0) {
                          this.$message({
                            type: "success",
                            message: $T("导入成功")
                          });
                          this.uploadDialog.closeTrigger_in =
                            new Date().getTime();
                          this.sandingBoxTable.resetTrigger_in =
                            new Date().getTime();
                          this.statistics.resetTrigger_in =
                            new Date().getTime();
                        }
                      });
                  })
                  .catch(() => {
                    this.$message($T("已取消"));
                  });
              } else {
                customApi
                  .importDeviceStandingBook(formData, this.projectId)
                  .then(response => {
                    if (response.code === 0) {
                      this.$message({
                        type: "success",
                        message: $T("导入成功")
                      });
                      this.uploadDialog.closeTrigger_in = new Date().getTime();
                      this.sandingBoxTable.resetTrigger_in =
                        new Date().getTime();
                      this.statistics.resetTrigger_in = new Date().getTime();
                    }
                  });
              }
            }
          });
      } else {
        customApi
          .importVerificationRecord(formData, this.projectId)
          .then(response => {
            if (response.code === 0) {
              this.$message({
                type: "success",
                message: $T("导入成功")
              });
              this.uploadDialog.closeTrigger_in = new Date().getTime();
              this.sandingBoxTable.resetTrigger_in = new Date().getTime();
              this.statistics.resetTrigger_in = new Date().getTime();
            }
          });
      }
    },
    CetButton_query_statusTrigger_out(val) {
      this.advancedQuery.queryCondition_in = this.queryCondition;
      this.advancedQuery.visibleTrigger_in = this._.cloneDeep(val);
    },
    CetButton_add_statusTrigger_out(val) {
      this.add.editData_in = null;
      this.add.visibleTrigger_in = this._.cloneDeep(val);
    },
    uploadDialog_download(val) {
      var url;
      if (this.actionTitle === 1) {
        url = "/eem-service/v1/dashboard/importtemplate/" + this.projectId;
      } else {
        url = "/eem-service/v1/dashboard/record/template/" + this.projectId;
      }
      common.downExcelGET(url, {}, this.token);
    },
    CetButton_export_statusTrigger_out(val) {
      var expressions = [];
      // 传入查询入参
      // 在表头中判断入参条件时间是区间，枚举是并且，文本是存在   赋值入参条件
      Object.keys(this.queryCondition).forEach(key => {
        if (this.queryCondition[key] || this.queryCondition[key] === false) {
          var obj = this.add.template_in.find(i => i.name === key);
          var expressionsItem = {};
          expressionsItem.limit = this.queryCondition[key];
          expressionsItem.prop = key;
          if (obj.datatype === "string") {
            expressionsItem.operator = "LIKE";
            expressions.push(expressionsItem);
          } else if (obj.datatype === "int4") {
            expressionsItem.operator = "EQ";
            expressions.push(expressionsItem);
          } else if (obj.datatype === "int8") {
            expressionsItem.operator = "EQ";
            expressions.push(expressionsItem);
          } else if (obj.datatype === "float") {
            expressionsItem.operator = "EQ";
            expressions.push(expressionsItem);
          } else if (
            obj.datatype === "enum" ||
            obj.datatype === "boolean" ||
            (obj.enumerationvalue && obj.enumerationvalue.length)
          ) {
            expressionsItem.operator = "EQ";
            expressions.push(expressionsItem);
          } else if (obj.datatype === "date") {
            expressions.push({
              limit: this.queryCondition[key][0],
              prop: key,
              operator: "GE"
            });
            expressions.push({
              limit:
                this.$moment(this.queryCondition[key][1])
                  .endOf("date")
                  .valueOf() + 1,
              prop: key,
              operator: "LT"
            });
          }
        }
      });
      if (this.actionTitle === 1) {
        common.downExcel(
          `/eem-service/v1/dashboard/export`,
          {
            expressions: expressions,
            projectid: this.projectId
          },
          this.token
        );
      } else {
        common.downExcel(
          `/eem-service/v1/dashboard/record/export`,
          {
            expressions: expressions,
            projectid: this.projectId
          },
          this.token
        );
      }
    },
    ElInput_name_change_out(val) {
      this.sandingBoxTable.resetTrigger_in = new Date().getTime();
    },
    templateAdmin_updata_out(val) {
      this.sandingBoxTable.resetTrigger_in = new Date().getTime();
    },
    sandingBoxTable_upStatistics_out(val) {
      this.statistics.resetTrigger_in = new Date().getTime();
    },
    add_updata_out(val) {
      this.sandingBoxTable.resetTrigger_in = new Date().getTime();
      this.statistics.resetTrigger_in = new Date().getTime();
    },
    add_upTableData_out(val) {
      this.sandingBoxTable.tableResetTrigger_in = new Date().getTime();
      this.statistics.resetTrigger_in = new Date().getTime();
    },
    // 表头输出
    sandingBoxTable_columnArr_out(val) {
      this.advancedQuery.template_in = val;
      this.add.template_in = val;
      this.CetButton_query.disable_in = false;
      this.CetButton_add.disable_in = false;
      this.importBool = false;
      if (!val || !val.length) {
        this.$message.warning(
          $T("获取不到设备台账模板，请在模板管理中导入预制字段！")
        );
        this.CetButton_query.disable_in = true;
        this.CetButton_add.disable_in = true;
        this.importBool = true;
      }
    },
    // 编辑
    sandingBoxTable_tableEdit_out(row) {
      this.add.editData_in = this._.cloneDeep(row);
      this.add.visibleTrigger_in = new Date().getTime();
    },
    sandingBoxTable_exportDisableStatus_out(val) {
      this.exportBool = val;
    },
    // 高级查询输出
    CetButton_query_queryCondition_out(val) {
      console.log(val);
      this.queryCondition = this._.cloneDeep(val);
      this.sandingBoxTable.resetTrigger_in = new Date().getTime();
    },
    // 图表点击
    statistics_nextoverhauldate_out(val) {
      if (this.actionTitle === 1) {
        this.queryCondition.nextoverhauldate = val;
        this.sandingBoxTable.resetTrigger_in = new Date().getTime();
      }
    },
    selectionAll(val) {
      this.selectedIds = val;
    },
    // 批量删除
    CetButton_batchDelete_statusTrigger_out() {
      var deleteFn;
      if (this.actionTitle === 1) {
        deleteFn = "deleteDashboard";
      } else if (this.actionTitle === 2) {
        deleteFn = "deleteRecord";
      }
      this.$confirm($T("是否删除此台账?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          customApi[deleteFn](this.selectedIds).then(response => {
            if (response.code === 0) {
              this.$message({
                type: "success",
                message: $T("删除成功！")
              });
              this.sandingBoxTable.tableResetTrigger_in = new Date().getTime();
              this.statistics.resetTrigger_in = new Date().getTime();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已取消删除")
          });
        });
    },
    // 批量删除测量对象
    CetButton_batchDelObject_statusTrigger_out() {
      this.$confirm($T("是否进行批量清除操作?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          customApi.deletePilpeline().then(res => {
            if (res.code === 0) {
              this.$message({
                type: "success",
                message: "批量清除成功"
              });
              this.sandingBoxTable.tableResetTrigger_in = new Date().getTime();
              this.statistics.resetTrigger_in = new Date().getTime();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已取消清除操作")
          });
        });
    },
    save_object() {
      this.sandingBoxTable.tableResetTrigger_in = new Date().getTime();
      this.statistics.resetTrigger_in = new Date().getTime();
    },
    handleCommand(command) {
      switch (command) {
        case "admin":
          this.templateAdmin.visibleTrigger_in = Date.now();
          break;
        case "import":
          this.uploadDialog.openTrigger_in = new Date().getTime();
          break;
        case "export":
          this.CetButton_export_statusTrigger_out();
          break;
        case "batchAdd":
          this.batchAddObj.visibleTrigger_in = Date.now();
          break;
        case "batchDelete":
          this.CetButton_batchDelete_statusTrigger_out();
          break;
        case "batchDelObject":
          this.CetButton_batchDelObject_statusTrigger_out();
          break;
        default:
          return;
      }
    }
  },
  activated: function () {
    this.selectedMenu = $T("设备台账");
    this.titleChange($T("设备台账"));
    this.statistics.resetTrigger_in = new Date().getTime();
  }
};
</script>
<style lang="scss" scoped>
.page {
  height: 100%;
}
.head-label {
  @include font_size(H2);
  @include font_weight(MD);
  line-height: 32px;
  position: relative;
  .radiogroup {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    z-index: 1;
  }
}
.left {
  font-size: 12px;
  cursor: pointer;
  .action {
    font-size: 18px;
    @include font_color(ZS);
  }
}
.more {
  @include line_height(Hm);
}
.delete {
  cursor: pointer;
  @include font_color(Sta3);
}
</style>
