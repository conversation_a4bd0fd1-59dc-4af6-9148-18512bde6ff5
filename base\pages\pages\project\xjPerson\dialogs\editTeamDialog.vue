<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="min"
    >
      <CetForm
        class="eem-cont-c1"
        :data.sync="CetForm_edit.inputData_in"
        v-bind="CetForm_edit"
        v-on="CetForm_edit.event"
      >
        <el-row :gutter="$J3">
          <el-col :span="24">
            <el-form-item :label="$T('班组名称')" prop="name">
              <ElInput
                maxlength="30"
                :placeholder="$T('请输入班组名称')"
                v-model.trim="CetForm_edit.inputData_in.name"
                v-bind="ElInput_name"
                v-on="ElInput_name.event"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <span>{{ $T("请选择班组成员") }}</span>
          </el-col>
          <el-col
            :span="24"
            class="checkedList"
            v-for="(role, index) in rolesList"
            :key="index"
          >
            <div class="mbJ3">
              <!-- <span class="isStar">*</span> -->
              {{ role.name }}
            </div>
            <div
              class="bg pJ3"
              style="height: 150px; overflow: auto"
              v-if="role.users && role.users.length"
            >
              <ElCheckboxGroup
                class="mp0"
                v-model="role.value"
                v-bind="ElCheckboxGroup_1"
                v-on="ElCheckboxGroup_1.event"
              >
                <ElCheckbox
                  class="mp0"
                  v-for="item in role.users"
                  :key="item[ElCheckboxList_1.key]"
                  :label="item[ElCheckboxList_1.label]"
                  :disabled="item[ElCheckboxList_1.disabled]"
                >
                  {{ item[ElCheckboxList_1.text] }}
                </ElCheckbox>
              </ElCheckboxGroup>
            </div>
          </el-col>
        </el-row>
      </CetForm>
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
    </CetDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";
export default {
  name: "editTeamDialog",
  computed: {
    projectTenantId() {
      return this.$store.state.projectTenantId;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("编辑班组"),
        showClose: true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // pagedialog表单组件
      CetForm_edit: {
        dataMode: "backendInterface", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "editInspectorTeam",
          modelLabel: "",
          dataIndex: ["id", "name"], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {
          id: 0,
          tenantId: 0,
          userIds: [],
          name: ""
        },
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入班组名称"),
              trigger: ["blur"]
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_edit_currentData_out,
          saveData_out: this.CetForm_edit_saveData_out,
          finishData_out: this.CetForm_edit_finishData_out,
          finishTrigger_out: this.CetForm_edit_finishTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // name组件
      ElInput_name: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_name_change_out,
          input: this.ElInput_name_input_out
        }
      },
      //
      ElCheckboxGroup_1: {
        value: [],
        style: {
          display: "block"
        },
        event: {
          change: this.ElCheckboxGroup_1_change_out
        }
      },
      ElCheckboxList_1: {
        options_in: [],
        key: "id",
        label: "id",
        text: "name",
        disabled: "disabled"
      },
      rolesList: []
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      new Promise((res, err) => {
        this.getRoles_out(res);
      }).then(res => {
        if (res.code === 0) {
          this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
        }
      });
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {
      this.CetForm_edit.queryId_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      if (val) {
        this.CetForm_edit.inputData_in.id = val.id;
        this.CetForm_edit.inputData_in.name = val.name;
      }
    }
  },
  methods: {
    CetForm_edit_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_edit_saveData_out(val) {
      this.$emit("saveData_out", this._.cloneDeep(val));
    },
    CetForm_edit_finishData_out(val) {
      this.$emit("finishData_out", this._.cloneDeep(val));
    },
    CetForm_edit_finishTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
      this.$emit("finishTrigger_out", val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetForm_edit.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      let userIds = [];
      this.rolesList.forEach(item => {
        if (item.value) {
          userIds = userIds.concat(item.value);
        }
      });
      // if(userIds.length === 0) {
      //   this.$message.warning("请选择用户");
      //   return;
      // }
      this.CetForm_edit.inputData_in.id = this.inputData_in.id;
      this.CetForm_edit.inputData_in.userIds = Array.from(new Set(userIds));
      new Promise((res, err) => {
        this.getTeam_out(res);
      }).then(res => {
        const list = res || [];
        let isOk = true;
        list.forEach(item => {
          if (
            item.name === this.CetForm_edit.inputData_in.name &&
            item.id !== this.CetForm_edit.inputData_in.id
          ) {
            isOk = false;
          }
        });
        if (isOk) {
          this.CetForm_edit.saveTrigger_in = new Date().getTime();
        } else {
          this.$message.warning($T("班组名称已存在，请输入其他名称"));
        }
      });
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    ElCheckboxGroup_1_change_out(val) {},
    // name输出,方法名要带_out后缀
    ElInput_name_change_out(val) {},
    ElInput_name_input_out(val) {},
    //获取巡检角色信息
    getRoles_out(callback) {
      const _this = this;
      const params = {
        tenantId: this.projectTenantId,
        id: this.inputData_in.id
      };

      customApi
        .queryInspectorRoles(params)
        .then(res => {
          if (res.code !== 0) {
            this.$message.warning($T("查询巡检角色信息失败"));
            return;
          }
          const selectUserIds =
            _this._.get(res, "data.selectUserIds", []) || [];
          const list = _this._.get(res, "data.userRoles", []) || [];
          list.forEach(item => {
            const usersList = item.users || [];
            const userIds = [];
            selectUserIds.forEach(item1 => {
              usersList.forEach(item2 => {
                if (item2.id === item1) {
                  userIds.push(item1);
                }
              });
            });
            item.value = userIds;
          });
          _this.rolesList = _this._.cloneDeep(list);
          callback && callback(res);
        })
        .catch(() => {});
    },
    // 获取班组信息
    getTeam_out(callback) {
      const params = {};
      if (!this.projectTenantId) {
        callback && callback([]);
        return;
      }
      customApi.queryInspectorTeamWithOutUser(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          callback && callback(data);
        }
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.checkedList {
  :deep(.el-checkbox) {
    width: 100% !important;
  }
  :deep(.mp0) {
    margin: 0px !important;
    padding: 0px !important;
  }
  :deep(.pd0) {
    padding: 0px !important;
  }
}
</style>
