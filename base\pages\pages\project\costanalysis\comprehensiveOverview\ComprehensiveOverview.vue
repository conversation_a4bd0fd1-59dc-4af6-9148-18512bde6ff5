﻿<template>
  <div class="page eem-common">
    <el-container class="fullheight">
      <el-aside width="315px" class="eem-aside">
        <CetGiantTree
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
        ></CetGiantTree>
      </el-aside>
      <el-main class="padding0 mlJ3">
        <el-container class="mainBox">
          <div class="itemBox oneBox">
            <div>
              <el-row>
                <el-col :span="8">
                  <img
                    class="image mtJ3"
                    :src="imgSrc"
                    style="max-height: 120px; max-width: 100%"
                    alt
                  />
                </el-col>
                <el-col :span="16" style="text-align: center">
                  <el-tooltip :content="currentNode && currentNode.name">
                    <div
                      class="mtJ4 common-title-H1 text-ellipsis"
                      style="display: block"
                    >
                      {{ currentNode && currentNode.name }}
                    </div>
                  </el-tooltip>
                  <el-tooltip :content="mainObj.value | formatNum2">
                    <div class="mtJ3 fsH text-ellipsis">
                      {{ mainObj.value | formatNum2 }}
                    </div>
                  </el-tooltip>
                  <div class="mtJ3 mbJ2">
                    {{ $T("总成本合计") }} ({{ unitName }})
                  </div>
                </el-col>
                <el-col :span="24">
                  <div class="mtJ1 mbJ1 datePicker">
                    <div class="fr w200">
                      <CustomElDatePicker
                        class="fr"
                        :prefix_in="$T('选择时间')"
                        v-bind="CetDatePicker_1.config"
                        v-on="CetDatePicker_1.event"
                        v-model="CetDatePicker_1.val"
                      />
                    </div>
                    <div class="mrJ1 fr">
                      <ElSelect
                        v-model="ElSelect_1.value"
                        v-bind="ElSelect_1"
                        v-on="ElSelect_1.event"
                      >
                        <ElOption
                          v-for="item in ElOption_1.options_in"
                          :key="item[ElOption_1.key]"
                          :label="item[ElOption_1.label]"
                          :value="item[ElOption_1.value]"
                          :disabled="item[ElOption_1.disabled]"
                        ></ElOption>
                      </ElSelect>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <div class="mainBox-bottom flex-row">
              <div class="flex-auto">
                <div class="text">{{ $T("同比") }}</div>
                <ArrwoNumber class="num" :value="mainObj.yearOnYear" />
              </div>
              <div class="flex-auto" v-show="ElSelect_1.value == 14">
                <div class="text">{{ $T("环比") }}</div>
                <ArrwoNumber class="num" :value="mainObj.chain" />
              </div>
            </div>
          </div>
          <div class="itemBox">
            <div class="common-title-H1 mtJ3">{{ $T("能源成本类型占比") }}</div>
            <CetChart
              style="height: 350px"
              @click="CetChartClick_out"
              :inputData_in="CetChart_1.inputData_in"
              v-bind="CetChart_1.config"
            />
          </div>
          <div class="itemBox" v-if="!hideCostanalysisRegionProportion">
            <div class="common-title-H1 mtJ3">{{ $T("能源成本区域占比") }}</div>
            <CetChart
              v-if="CetChart_2.config.options.series[0].data.length >= 1"
              style="height: 350px"
              :inputData_in="CetChart_2.inputData_in"
              v-bind="CetChart_2.config"
            />
            <div class="itemBox-empty" v-else>{{ $T("暂无数据") }}</div>
          </div>
          <div
            class="itemBox"
            v-for="(item, index) in ringGaugeList"
            :key="index"
          >
            <div class="common-title-H1 mtJ3">{{ item.name }}</div>
            <RingGauge style="margin-top: 70px" :inputData_in="item" />
          </div>
        </el-container>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import ArrwoNumber from "../components/ArrwoNumber.vue";
import RingGauge from "./RingGauge";
import common from "eem-utils/common";
import img from "../assets/u18999.png";
import costanalysisAPI from "../api/costanalysisAPI.js";
import TREE_PARAMS from "@/store/treeParams.js";
import {
  setValue,
  setPercent,
  setStartAndEndTime,
  pieItemStyle
} from "../utils/utils.js";
import { httping } from "@omega/http";

export default {
  name: "ComprehensiveOverview",
  props: { jumpParams: { type: Object } },
  components: {
    RingGauge,
    ArrwoNumber
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    timeRange() {
      const dateType = this.CetDatePicker_1.config.type;
      return setStartAndEndTime(this.CetDatePicker_1.val, dateType);
    },
    projectId() {
      return this.$store.state.projectId;
    },
    hideCostanalysisRegionProportion() {
      return this.$store.state.systemCfg.hideCostanalysisRegionProportion;
    }
  },

  data() {
    return {
      unitName: "元",
      currentNode: null,
      imgSrc: img,
      // 环形进度条
      ringGaugeList: [],
      mainObj: {
        value: "--",
        chain: "--",
        yearOnYear: "--"
      }, //第一个模块
      CetChart_1: {
        inputData_in: {},
        config: {
          options: {
            title: {
              text: "能源成本类型占比",
              show: false
            },
            legend: {
              type: "scroll",
              bottom: 10
            },
            tooltip: {
              trigger: "item",
              formatter: params => {
                return `${params.name}: ${params.percent}%`;
              }
            },
            series: [
              {
                name: "能源成本类型占比",
                type: "pie",
                radius: "55%",
                center: ["50%", "50%"],
                itemStyle: pieItemStyle,
                data: [],
                bottom: 30
              }
            ]
          }
        }
      },
      CetChart_2: {
        inputData_in: {},
        config: {
          options: {
            title: {
              text: "能源成本区域占比",
              show: false
            },
            legend: {
              type: "scroll",
              bottom: 10
            },
            tooltip: {
              trigger: "item",
              formatter: params => {
                return `${params.name}: ${params.percent}%`;
              }
            },
            series: [
              {
                name: "能源成本区域占比",
                type: "pie",
                radius: "55%",
                center: ["50%", "50%"],
                itemStyle: pieItemStyle,
                data: [],
                bottom: 30
              }
            ]
          }
        }
      },
      CetDatePicker_1: {
        disable_in: false,
        val: this.$moment().startOf("year").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "year",
          rangeSeparator: "-",
          style: {
            display: "inline-block"
          },
          size: "small",
          clearable: false,
          pickerOptions: common.pickerOptions_earlierThanYesterd11
        }
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "mini",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: true,
        title: "",
        size: "mini",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      ElSelect_1: {
        value: 17,
        style: {
          width: "100px"
        },
        size: "small",
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [
          {
            id: 17,
            text: $T("按年")
          },
          {
            id: 14,
            text: $T("按月")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out
        }
      },
      firstLoad: true
    };
  },
  watch: {
    "CetDatePicker_1.val": function (val) {
      if (this.ElSelect_1.value == 17) {
        const date = this.$moment(this.CetDatePicker_1.val);
        if (date.year() >= this.$moment().year()) {
          this.CetButton_next.disable_in = true;
        } else {
          this.CetButton_next.disable_in = false;
        }
      } else if (this.ElSelect_1.value == 14) {
        const date = this.$moment(this.CetDatePicker_1.val);
        if (
          date.startOf("month").valueOf() >=
          this.$moment().startOf("month").valueOf()
        ) {
          this.CetButton_next.disable_in = true;
        } else {
          this.CetButton_next.disable_in = false;
        }
      }
      this.getCurrentNodeValue();
      this.getAraeValue();
      this.getTypeValue();
    }
  },

  methods: {
    CetChartClick_out(val) {
      console.log("CetChartClick_out", val);
      const queryItem = {
        selectNode: this.currentNode,
        energyId: val.data.energyId,
        selectYear: this.CetDatePicker_1.val,
        dateType: this.ElSelect_1.value
      };
      this.$store.commit("setShareQuery", queryItem);
      this.$emit("changeMenu", "2");
    },
    getTreeData() {
      this.CetGiantTree_1.inputData_in = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      const data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.costanalysisTree,
        treeReturnEnable: true
      };
      costanalysisAPI.getNodeTree(data).then(result => {
        this.firstLoad = false;
        this.CetGiantTree_1.inputData_in = result;
        if (result && result.length > 0) {
          this.CetGiantTree_1.selectNode = result[0];
        }
      });
    },
    // 总成本合计区数据获取
    getCurrentNodeValue() {
      const [startTime, endTime] = this.timeRange;
      const nodes = [
        {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          name: this.currentNode.name
        }
      ];
      const data = {
        startTime,
        endTime,
        nodes,
        cycle: this.ElSelect_1.value,
        energyType: 13,
        queryType:
          this.ElSelect_1.value == 17 ? 1 : this.ElSelect_1.value == 14 ? 3 : 0,
        costKpiType: 0,
        projectId: this.projectId
      };
      costanalysisAPI.getCostByNode(data).then(result => {
        if (result[0]) {
          const mainData = result[0].data[0] || {};
          this.mainObj = {
            value: setValue(mainData.value),
            chain: setPercent(mainData.chain),
            yearOnYear: setPercent(mainData.yearOnYear)
          };
          this.unitName = this._.get(result, "[0].unitName", "元");
        }
        // this.mainObj.chain = result[0].chain && (result[0].chain * 100).toFixed2(2);
        // this.mainObj.yearOnYear = result[0].yearOnYear && (result[0].yearOnYear * 100).toFixed2(2);
      });
    },
    // 能源成本区域占比数据获取
    getAraeValue() {
      const [startTime, endTime] = this.timeRange;
      let nodes = [];
      if (
        this.currentNode.children &&
        Array.isArray(this.currentNode.children)
      ) {
        nodes = this.currentNode.children.map(item => {
          return {
            id: item.id,
            modelLabel: item.modelLabel,
            name: item.name
          };
        });
      }
      if (nodes.length === 0) {
        this.CetChart_2.config.options.series[0].data = [];
        return;
      }
      const data = {
        startTime,
        endTime,
        nodes,
        cycle: this.ElSelect_1.value,
        energyType: 13,
        queryType:
          this.ElSelect_1.value == 17 ? 1 : this.ElSelect_1.value == 14 ? 3 : 0,
        costKpiType: 0,
        projectId: this.projectId
      };
      costanalysisAPI.getCostByNode(data).then(result => {
        if (result.length > 0) {
          this.CetChart_2.config.options.series[0].data = result.map(item => {
            const itemData = item.data[0] || {};
            return {
              value: setValue(itemData.value, 0),
              name: item.objectName,
              unitName: item.unitName,
              label: {
                formatter: "{b}: {d}%",
                textStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                  fontSize: 14
                }
              }
            };
          });
        }
      });
    },
    // 能源成本类型占比以及分类用能的数据获取
    getTypeValue() {
      let energyTypeResult = [];
      costanalysisAPI
        .getProjectEnergy(this.projectId)
        .then(result => {
          energyTypeResult = result;
          const [startTime, endTime] = this.timeRange;
          const ids = [this.currentNode.id];
          const energyTypes = result.map(item => item.energytype);
          const data = {
            startTime,
            endTime,
            modelLabel: this.currentNode.modelLabel,
            ids,
            cycle: this.ElSelect_1.value,
            energyTypes,
            dimTagIds: null
          };
          return costanalysisAPI.getCostValue(data);
        })
        .then(result => {
          // 过滤调吨标煤和碳排放
          var newEnergyTypeResult = energyTypeResult.filter(item => {
            return ![13, 18, 22].includes(item.energytype);
          });
          var totalNum = 0;
          this.CetChart_1.config.options.series[0].data =
            newEnergyTypeResult.map(energyItem => {
              const item = result.find(
                item => item.energytype === energyItem.energytype
              ) || { value: 0 };
              totalNum += item.value;
              return {
                value: setValue(item.value, 0),
                name: energyItem.name,
                energyId: energyItem.energytype,
                unitName: item.unitName,
                label: {
                  formatter: "{b}: {d}%",
                  textStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: "rgba(0, 0, 0, 0.5)",
                    fontSize: 14
                  }
                }
              };
            });
          this.ringGaugeList = newEnergyTypeResult.map(energyItem => {
            const resultItem = result.find(
              item => item.energytype === energyItem.energytype
            ) || { value: null };
            return {
              name: energyItem.name,
              kpiName: `${$T("总")} ${energyItem.name} ${$T("成本")}（${
                resultItem.unitName || "--"
              }）`,
              passRate: (resultItem.value / totalNum) * 100,
              value:
                resultItem.value === null ? "--" : resultItem.value.toFixed2(2)
            };
          });
        });
    },
    CetGiantTree_1_currentNode_out(val) {
      if (val && !this.firstLoad) {
        // if (val.childSelectState == 2) {
        //   this.$message.warning("你没有此节点的全部权限！");
        //   return;
        // }
        this.currentNode = val;
        this.getCurrentNodeValue();
        this.getAraeValue();
        this.getTypeValue();
        // 获取图片
        this.getImg(val);
      }
    },
    getImg(data) {
      if (!data) {
        this.imgSrc = img;
      } else {
        httping({
          url: "/eem-service/v1/node/nodeTree",
          data: {
            rootID: data.id,
            rootLabel: data.modelLabel,
            treeReturnEnable: true
          },
          method: "POST"
        }).then(res => {
          if (
            res.code === 0 &&
            res.data &&
            res.data.length > 0 &&
            res.data[0].pic
          ) {
            this.getImgUrl(res.data[0].pic);
          } else {
            this.imgSrc = img;
          }
        });
      }
    },
    getImgUrl: function (uploadPath) {
      var me = this;
      if (!uploadPath) {
        return;
      }
      var url = "/eem-service/v1/common/downloadFile?path=" + uploadPath;
      if (!url) {
        return;
      }
      const xhr = new XMLHttpRequest();
      xhr.open("GET", url, true);
      xhr.responseType = "blob";
      xhr.setRequestHeader("Authorization", this.token);
      xhr.onload = () => {
        if (
          xhr.status === 200 &&
          xhr.response.type === "application/x-download"
        ) {
          //将图片信息放到Img中
          me.imgSrc = window.URL.createObjectURL(xhr.response);
        }
      };

      xhr.send();
    },
    ElSelect_1_change_out(val) {
      let date = this.$moment(this.CetDatePicker_1.val);
      if (val == 17) {
        this.CetDatePicker_1.config.type = "year";
        if (date.year() >= this.$moment().year()) {
          this.CetButton_next.disable_in = true;
        } else {
          this.CetButton_next.disable_in = false;
        }
      } else if (val == 14) {
        this.CetDatePicker_1.config.type = "month";
        if (date.month() >= this.$moment().month()) {
          this.CetButton_next.disable_in = true;
        } else {
          this.CetButton_next.disable_in = false;
        }
      }
      if (val && !this.firstLoad) {
        this.getCurrentNodeValue();
        this.getAraeValue();
        this.getTypeValue();
      }
    },

    CetButton_prv_statusTrigger_out(val) {
      if (this.ElSelect_1.value == 17) {
        let date = this.$moment(this.CetDatePicker_1.val);
        this.CetDatePicker_1.val = date.subtract(1, "year").valueOf();
      } else if (this.ElSelect_1.value == 14) {
        let date = this.$moment(this.CetDatePicker_1.val);
        this.CetDatePicker_1.val = date.subtract(1, "month").valueOf();
      }
    },
    CetButton_next_statusTrigger_out(val) {
      if (this.ElSelect_1.value == 17) {
        let date = this.$moment(this.CetDatePicker_1.val);
        this.CetDatePicker_1.val = date.add(1, "year").valueOf();
      } else if (this.ElSelect_1.value == 14) {
        let date = this.$moment(this.CetDatePicker_1.val);
        this.CetDatePicker_1.val = date.add(1, "month").valueOf();
      }
    }
  },
  created: function () {},
  mounted: function () {
    this.getTreeData();
    if (this.$route.params?.keepParams?.type === "jumpQueryParams") {
      this.$nextTick(() => {
        const { aggregationCycle, startTime } = this.$route.params.keepParams;
        this.ElSelect_1.value = aggregationCycle;
        this.ElSelect_1_change_out(this.ElSelect_1.value);
        this.CetDatePicker_1.val = startTime;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.mainBox {
  overflow: auto;
  flex-wrap: wrap;
  justify-content: space-between;
  & > .itemBox {
    width: 32.6%;
    margin: 0px;
    @include padding(0 J4 J3 J4);
    @include margin_bottom(J3);
    height: 400px;
    box-sizing: border-box;
    @include background_color(BG1);
    overflow: hidden;
    .itemBox-title {
      line-height: 50px;
      // @include padding_left(J3);
    }

    .itemBox-empty {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
    }
  }
  .mainBox-bottom {
    background: linear-gradient(
      180deg,
      rgba(52, 143, 218, 1) 0%,
      rgba(52, 143, 218, 1) 0%,
      rgba(27, 61, 199, 1) 100%,
      rgba(27, 61, 199, 1) 100%
    );
    display: flex;
    text-align: center;
    color: #fff;
    margin: 0 -24px -24px -24px;
    & > div {
      flex: 1;
    }
    .text {
      margin-top: 40px;
    }
    .num {
      margin-top: 40px;
      font-size: 36px;
    }
  }
}
.oneBox {
  display: flex;
  flex-direction: column;
  & > div {
    flex: 1;
  }
}
.miniBtn {
  :deep(.el-button) {
    padding: 7px 7px;
  }
}
.datePicker {
  :deep(.el-input--suffix .el-input__inner) {
    padding-right: 20px;
  }
}
</style>
