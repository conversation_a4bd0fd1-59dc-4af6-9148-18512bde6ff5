import fetch from "eem-utils/fetch";
import { getCustomParameter } from "./common/common.js";
const version = "v1";

function processRequest(data) {
  // 对 data 进行任意转换处理
  return data;
}

function processResponse(response) {
  // 对 response 进行任意转换处理, response结构
  //   {
  //     // `data` 由服务器提供的响应
  //     data: {},

  //     // `status` 来自服务器响应的 HTTP 状态码
  //     status: 200,

  //     // `statusText` 来自服务器响应的 HTTP 状态信息
  //     statusText: 'OK',

  //     // `headers` 服务器响应的头
  //     headers: {},

  //     // `config` 是为请求提供的配置信息
  //     config: {}
  //   }
  return response;
}
// 获取点检到期/超期统计
export function overdueCheck(projectId) {
  return fetch({
    url: `/eem-service/${version}/dashboard/overdueCheck/${projectId}`,
    method: "GET"
  });
}
// 设备台账
// 新增\修改设备台账
export function editDashboard(data) {
  return fetch({
    url: `/eem-service/${version}/dashboard/edit/${data[0].projectid}`,
    method: "POST",
    data: data
  });
}
// 删除设备台账
export function deleteDashboard(data) {
  return fetch({
    url: `/eem-service/${version}/dashboard/delete`,
    method: "DELETE",
    data: data
  });
}

// 获取设备台账表格数据
export function getDeviceTableDate(data) {
  var projectId, id, modellabel, monitorname;
  var expressions = _.get(data, "rootCondition.filter.expressions");
  if (!_.isEmpty(expressions)) {
    projectId = getCustomParameter(expressions, "projectId");
    id = getCustomParameter(expressions, "monitorid");
    modellabel = getCustomParameter(expressions, "monitorlabel");
    monitorname = getCustomParameter(expressions, "monitorname");
  }
  var queryData = {
    expressions: expressions,
    page: data.rootCondition.page,
    projectid: projectId
  };
  if (id && modellabel && monitorname) {
    queryData.id = id;
    queryData.modellabel = modellabel;
  }
  return fetch({
    url: `/eem-service/${version}/dashboard/query`,
    method: "POST",
    data: queryData
  });
}
// 台账导入前检查
export function importStandingBookCheck(data, projectId) {
  return fetch({
    url: `/eem-service/${version}/dashboard/importcheck/${projectId}`,
    method: "POST",
    data: data
  });
}
// 设备台账导入
export function importDeviceStandingBook(data, projectId) {
  return fetch({
    url: `/eem-service/${version}/dashboard/import/${projectId}/${true}`,
    method: "POST",
    data: data
  });
}

// 模板管理
// 获取设备台账模板
export function getDeviceTemplate(projectId) {
  return fetch({
    url: `/eem-service/${version}/dashboard/template/query/${projectId}`,
    method: "GET"
  });
}
// 编辑设备台账模板字段
export function editDeviceTemplate(data, projectId) {
  return fetch({
    url: `/eem-service/${version}/dashboard/template/edit/${projectId}`,
    method: "POST",
    data: data
  });
}
// 删除设备台账模板字段
export function deleteDeviceTemplate(data, bool) {
  return fetch({
    url: `/eem-service/${version}/dashboard/template/delete/${bool}`,
    headers: { hideNotice: bool },
    method: "DELETE",
    data: data
  });
}
// 导入系统预制模板字段
export function dashboardImportFixedTemplate(projectId) {
  return fetch({
    url: `/eem-service/${version}/dashboard/importFixedTemplate/${projectId}`,
    method: "POST"
  });
}

// 检定记录
// 检定记录导入
export function importVerificationRecord(data, projectId) {
  return fetch({
    url: `/eem-service/${version}/dashboard/record/import/${projectId}`,
    method: "POST",
    data: data
  });
}
// 获取检定记录台账
export function getRecordTableDate(data) {
  var projectId;
  var expressions = _.get(data, "rootCondition.filter.expressions");
  if (!_.isEmpty(expressions)) {
    projectId = getCustomParameter(expressions, "projectId");
  }
  var queryData = {
    expressions: expressions,
    page: data.rootCondition.page,
    projectid: projectId
  };

  return fetch({
    url: `/eem-service/${version}/dashboard/record/query`,
    method: "POST",
    data: queryData
  });
}
// 新增\修改检定记录
export function editRecord(data) {
  return fetch({
    url: `/eem-service/${version}/dashboard/record/edit`,
    method: "POST",
    data: data
  });
}
// 删除检定记录
export function deleteRecord(data) {
  return fetch({
    url: `/eem-service/${version}/dashboard/record/delete`,
    method: "DELETE",
    data: data
  });
}
// 根据管网设备新增仪表
export function createPilpeline(data) {
  return fetch({
    url: `/eem-service/${version}/dashboard/pipelineEquipment/create`,
    method: "POST",
    data: data
  });
}
// 删除管网没信息的仪表
export function deletePilpeline() {
  return fetch({
    url: `/eem-service/${version}/dashboard/pipelineEquipment/delete`,
    method: "DELETE"
  });
}
