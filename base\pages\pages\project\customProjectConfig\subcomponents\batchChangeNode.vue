<template>
  <!-- 1弹窗组件 -->
  <CetDialog class="CetDialog" v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <el-container class="fullheight flex-column bg1 brC2 pJ2">
      <!-- 父子关联 -->
      <CetGiantTree
        class="switch-tree"
        v-bind="CetGiantTree_1"
        v-on="CetGiantTree_1.event"
      ></CetGiantTree>
    </el-container>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import commonApi from "@/api/custom.js";
import ELECTRICAL_DEVICE_RELATION from "@/store/nodeRelation";
import { httping } from "@omega/http";
export default {
  name: "shareRate",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    //选中节点信息
    fatherNode_in: {
      type: Object
    },
    // 选中子节点列表
    selectedData_in: {
      type: Array,
      default() {
        return [];
      }
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: "请选择节点",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        width: "960px",
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "确认",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "关闭",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      checkedNode: {},
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: this.setNodeClasses
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out //勾选节点输出
        }
      },
      title: ""
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      if (!vm.fatherNode_in) {
        return;
      }
      vm.CetDialog_1.openTrigger_in = val;
      const list = this._.cloneDeep(ELECTRICAL_DEVICE_RELATION) || [];
      list.forEach(item => {
        if (this.fatherNode_in.modelLabel === "room") {
          if (
            item.modelLabel === "room" &&
            item.roomType === this.fatherNode_in.roomtype
          ) {
            this.CetDialog_1.title = `请选择${item.name}节点`;
          }
        } else if (this.fatherNode_in.modelLabel === item.modelLabel) {
          this.CetDialog_1.title = `请选择${item.name}节点`;
        }
      });

      vm.getTreeData();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    inputData_in(val) {
      this.CetDialog_1.inputData_in = val;
    }
  },

  methods: {
    setNodeClasses(treeId, treeNode) {
      return treeNode.disabledClass
        ? { add: ["disabledClass"] }
        : { remove: ["disabledClass"] };
    },
    CetGiantTree_1_currentNode_out(val) {
      this.checkedNode = this._.cloneDeep(val);
    },
    getTreeData() {
      let data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [
          { modelLabel: "sectionarea" },
          { modelLabel: "building" },
          { modelLabel: "floor" },
          { modelLabel: "room" },
          { modelLabel: "arraycabinet" },
          { modelLabel: "powerdiscabinet" }
        ],
        treeReturnEnable: true
      };

      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.checkedNode = {};
      this.CetGiantTree_1.checkedNodes = [];
      commonApi.getNodeTreeSimple(data).then(res => {
        if (res.code === 0) {
          var data = this._.get(res, "data", []) || [];
          this.CetGiantTree_1.inputData_in = data;
        }
      });
    },
    // 保存
    addSupplyRelation_out() {
      var me = this;
      var url = `/eem-service/v1/node/moveNode`;

      if (me.fatherNode_in.modelLabel !== me.checkedNode.modelLabel) {
        this.$message.warning(this.CetDialog_1.title);
        return;
      } else if (
        me.fatherNode_in.modelLabel === "room" &&
        me.fatherNode_in.roomtype !== me.checkedNode.roomtype
      ) {
        this.$message.warning(this.CetDialog_1.title);
        return;
      }
      let params = [
        {
          newParent: me.checkedNode,
          oldParent: me.fatherNode_in,
          toBeOutNodes: me.selectedData_in
        }
      ];

      httping(url, {
        data: params,
        method: "PUT"
      }).then(function (res) {
        if (res.code === 0) {
          me.$message({
            message: "保存成功",
            type: "success"
          });
          me.$emit("updata_out");
          me.CetDialog_1.closeTrigger_in = new Date().getTime();
        }
      });
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      this.addSupplyRelation_out();
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep() {
    .el-dialog__body {
      @include background_color(BG);
      @include padding(J1);
    }
  }
  .lhHm {
    @include line_height(Hm);
  }
}
.switch-tree {
  height: 500px;
}
</style>
