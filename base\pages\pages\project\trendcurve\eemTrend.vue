<template>
  <div style="width: 100%; height: 100%">
    <el-container style="height: 100%">
      <el-header
        class="trend-class"
        :height="isNoHeaderButton ? '0px' : '44px'"
      >
        <div style="padding-bottom: 5px" v-show="!showTable">
          <el-checkbox
            class="mrJ1"
            @change="limitHandler"
            v-if="showLimitButton"
          >
            {{ limitText.buttonText }}
          </el-checkbox>
          <el-checkbox
            class="mrJ1"
            v-model="showMaxAndMinStatus"
            @change="extremValueHandler"
            v-if="showExtremButton"
          >
            {{ $T("最值") }}
          </el-checkbox>
          <el-checkbox
            class="mrJ1"
            v-model="showDiffStatus"
            @change="diffHandler"
            v-if="showDiffButton"
          >
            {{ $T("差值") }}
          </el-checkbox>
          <el-checkbox
            class="mrJ1"
            v-model="showAverageStatus"
            @change="averageHandler"
            v-if="showAverageButton"
          >
            {{ $T("平均值") }}
          </el-checkbox>
          <el-checkbox
            class="mrJ1"
            v-model="showPointStatus"
            @change="pointHandler"
            v-if="showPointButton"
          >
            {{ $T("打点显示") }}
          </el-checkbox>
          <el-checkbox
            class="mrJ1"
            @change="rawMarkHandler"
            v-if="showRawMarkButton"
          >
            {{ $T("原始标记") }}
          </el-checkbox>
        </div>
        <customElSelect
          v-if="showTableButton"
          v-model="showTableSelect"
          v-bind="ElSelect_1"
          v-on="ElSelect_1.event"
          :prefix_in="$T('展示类型')"
        >
          <ElOption
            v-for="item in ElOption_1.options_in"
            :key="item[ElOption_1.key]"
            :label="item[ElOption_1.label]"
            :value="item[ElOption_1.value]"
            :disabled="item[ElOption_1.disabled]"
          ></ElOption>
        </customElSelect>
      </el-header>
      <el-main :style="mainStyle">
        <el-container style="height: 100%">
          <!-- <div class="trend-chart" v-show="!showTable"> -->
          <div
            :class="showTable ? 'hide-chart' : 'trend-chart'"
            class="trend-chart"
          >
            <CetChart
              ref="chart"
              :inputData_in="CetChart_trend.inputData_in"
              v-bind="CetChart_trend.config"
              @finished="chartFinish"
              @click="clickHandler"
            />
          </div>

          <div v-if="showTable" class="trend-table">
            <div
              v-for="(item, index) in tableData"
              :key="index"
              :style="tableDivStyle"
            >
              <el-table
                ref="cetTable"
                :data="item.data"
                tooltip-effect="light"
                border
                height="true"
                style="height: 100%; width: 100%"
              >
                <template v-for="(it, i) in item.header">
                  <el-table-column
                    :key="i"
                    :label="it.label"
                    :prop="it.prop"
                    header-align="center"
                    :width="it.width"
                    :show-overflow-tooltip="true"
                  ></el-table-column>
                </template>
              </el-table>
            </div>
          </div>
        </el-container>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import Common from "eem-utils/common";
import fetch from "eem-utils/fetch";
import _ from "lodash";

export default {
  components: {},
  name: "EemTrend",
  props: {
    //趋势曲线入参，每一条曲线包含一个入参对象，包含时间、nodeId，paramId
    params_in: {
      type: [Array, Object]
    },
    queryTime_in: {
      type: Object
    },
    //整数值, 0或者1不进行抽点, 2或者大于2的值按设置值进行抽点
    interval_in: {
      type: Number,
      default: 0
    },
    title_in: {
      type: String
    },
    scatter_in: {
      type: [Number, Array]
    },
    //查询模式 //查询按钮触发trigger，或者查询条件变化立即查询diff
    queryMode: {
      type: String
    },
    queryTrigger_in: {
      type: [Number, Array, Object, String]
    },
    clearTrigger_in: {
      type: Number
    },
    dataConfig: {
      type: Object
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    showExtremButton: {
      type: Boolean,
      default: true
    },
    showDiffButton: {
      type: Boolean,
      default: true
    },
    showLimitButton: {
      type: Boolean,
      default: false
    },
    showAverageButton: {
      type: Boolean,
      default: true
    },
    showPointButton: {
      type: Boolean,
      default: true
    },
    showRawMarkButton: {
      type: Boolean,
      default: false
    },
    showTableButton: {
      type: Boolean,
      default: true
    },
    precision: {
      type: Number
    },
    color: {
      type: Array
    },
    limitText: {
      type: Object,
      default: () => {
        return {
          buttonText: $T("限值"),
          upperLimitText: $T("上限"),
          lowerLimitText: $T("下限")
        };
      }
    },
    showSecond: {
      type: Boolean,
      default: false
    },
    setMergeOptions: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const vm = this;
    const language = window.localStorage.getItem("omega_language");
    return {
      trendData: [],
      originalData: [],
      originalTrendData: [],
      diffData: [],
      showMaxAndMinStatus: false,
      showLimitStatus: false,
      showAverageStatus: false,
      showPointStatus: false,
      showRawMarkStatus: false,
      showDiffStatus: false,
      showTableSelect: false,
      CetChart_trend: {
        inputData_in: {},
        config: {
          options: {
            // color: this.color || ["#c23531", "#2f4554", "#61a0a8", "#d48265", "#91c7ae", "#749f83", "#ca8622", "#bda29a", "#6e7074", "#546570", "#c4ccd3"],
            legend: {
              show: vm.showLegend
            },
            tooltip: {
              trigger: "axis",
              // axisPointer: {
              //   label: {
              //     formatter: function (params) {
              //       return vm.$moment(params.value).format("MM-DD HH:mm");
              //     }
              //   }
              // }
              formatter: function (params) {
                const trendData = vm.trendData;

                var showHtm =
                  vm.$moment(params[0].axisValue).format("MM-DD HH:mm:ss") +
                  "<br>";
                for (var i = 0; i < params.length; i++) {
                  //名称
                  var name = params[i].seriesName;
                  if (name === "trendScatter") {
                    continue;
                  }
                  const series = _.find(trendData, { name: name });
                  let unit = "";
                  if (series) {
                    unit = series.param.unit || "";
                  }

                  //值
                  var value = params[i].value[1];
                  value = _.isNumber(value) && !_.isNaN(value) ? value : "--";
                  showHtm += name + " ：" + value + unit + "<br>";
                }
                return showHtm;
              }
            },
            title: {
              left: "left",
              text: this.title_in
            },
            grid: {
              left: 0,
              right: 20,
              top: 100,
              containLabel: true
            },
            toolbox: {
              top: 40,
              right: 30,
              feature: {
                dataZoom: {
                  yAxisIndex: "none",
                  title: {
                    zoom: $T("区域缩放"),
                    back: $T("区域缩放还原")
                  }
                },
                saveAsImage: {
                  name: this.exportImgName_in,
                  title: $T("保存为图片")
                }
              }
            },
            xAxis: {
              type: "time",
              axisLine: {
                onZero: false
              }
            },
            yAxis: {
              boundaryGap: [0, "10%"],
              splitLine: {
                show: false
              }
            },
            dataZoom: [
              {
                type: "inside",
                start: 0,
                end: 100,
                minValueSpan: 60 * 1000
              },
              {
                start: 0,
                end: 100,
                handleIcon:
                  "M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z",
                handleSize: "80%",
                handleStyle: {
                  color: "#fff",
                  shadowBlur: 3,
                  shadowColor: "rgba(0, 0, 0, 0.6)",
                  shadowOffsetX: 2,
                  shadowOffsetY: 2
                },
                left: 150,
                right: 150
              }
            ],
            series: []
          },
          manualUpdate: true
        }
      },
      showTable: false,
      renderColumns: [],
      tableData: [],
      ElSelect_1: {
        value: false,
        style: {
          width: language === "en" ? "180px" : "160px"
        },
        event: {
          change(val) {
            vm.showTableSelect = val;
          }
        }
      },
      ElOption_1: {
        options_in: [
          {
            value: false,
            label: $T("曲线")
          },
          {
            value: true,
            label: $T("表格")
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      distributionChart: "",
      tableDivStyle: {
        width: "100%"
      },
      //保存各个单位的最值, 在自适应计算Y轴max和min值时, 要考虑最值点的位置; 避免最值点在Y轴范围外,格式:{max:100, min:2, name:'V'}
      extremValue: []
    };
  },
  watch: {
    params_in: {
      handler: function () {
        this.paramsChange();
      }
    },
    queryTime_in: {
      deep: true,
      handler: function (val) {
        this.paramsChange();
      }
    },
    title_in: {
      immediate: true,
      handler: function (val) {
        if (this.$refs.chart) {
          this.$refs.chart.mergeOptions({ title: { text: val } });
        }
      }
    },
    trendData: {
      deep: true,
      handler: function (val) {
        const vm = this;
        const dataLength = vm.trendData.length;
        if (!dataLength || dataLength === 1) {
          vm.tableDivStyle.width = "100%";
        } else {
          vm.tableDivStyle.width = `${100 / dataLength}%`;
        }
      }
    },
    queryTrigger_in() {
      this.getChartData();
    },
    clearTrigger_in() {
      this.showTableSelect = false;
      this.showMaxAndMinStatus = false;
      this.showDiffStatus = false;
      this.showAverageStatus = false;
      this.showPointStatus = false;
      this.trendData = [];
      this.updateChart();
      this.updateTable();
      this.setTrendStatus();
      this.$refs.chart.mergeOptions(this.CetChart_trend.config.options, true);
    },
    showTableSelect(val) {
      const vm = this;
      vm.showTable = val;

      if (val === false) {
        const timeout = setTimeout(() => {
          vm.updateChart();
          vm.setTrendStatus();
          clearTimeout(timeout);
        }, 0);
      }
    },
    scatter_in(val) {
      const vm = this;
      if (val && _.isArray(val)) {
        const data = val.map(item => [item.x, 0, item]);
        const series = [
          {
            name: "trendScatter",
            data: data,
            type: "scatter"
          }
        ];
        vm.$refs.chart.mergeOptions({ series: series });
      }
    }
  },
  updated() {},
  computed: {
    isNoHeaderButton() {
      return (
        !this.showExtremButton &&
        !this.showPointButton &&
        !this.showAverageButton &&
        !this.showTableButton &&
        !this.showLimitButton &&
        !this.showRawMarkButton &&
        !this.showDiffButton
      );
    },
    mainStyle() {
      return this.isNoHeaderButton
        ? {
            padding: "0px 12px",
            overflowX: "auto",
            whiteSpace: "nowrap",
            height: "100%"
          }
        : {
            padding: "0px 0px 0px 12px",
            overflowX: "auto",
            whiteSpace: "nowrap",
            height: "calc(100% - 44px)"
          };
    }
  },
  methods: {
    //参数变化查询曲线数据
    paramsChange() {
      this.getChartData();
    },
    //组织查询时间入参
    getQueryTime() {
      const vm = this;
      let queryTime = {};
      if (_.isEmpty(vm.queryTime_in) || !vm.queryTime_in) {
        return null;
      }

      queryTime = {
        timeType: vm.queryTime_in.timeType ? vm.queryTime_in.timeType : 1,
        startTime: null,
        endTime: null
      };

      if (_.isArray(vm.queryTime_in.time)) {
        if (_.isDate(vm.queryTime_in.time[0])) {
          queryTime.startTime = this.$moment(vm.queryTime_in.time[0]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
        }
        if (_.isDate(vm.queryTime_in.time[1])) {
          queryTime.endTime = this.$moment(vm.queryTime_in.time[1]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
        }
      }
      // else {
      //   if (_.isDate(vm.queryTime_in.time)) {
      //     queryTime.startTime = vm.queryTime_in.time.getTime();
      //   }
      // }
      return queryTime;
    },
    //组织参数入参
    getParams() {
      const vm = this;
      return _.map(vm.params_in, function (n) {
        return {
          dataId: n.dataId,
          dataTypeId: n.dataTypeId,
          deviceId: n.deviceId,
          logicalId: n.logicalId
        };
      });
    },

    //打点显示
    pointHandler(val) {
      const vm = this;
      vm.showPointStatus = !!val;

      vm.setPoint(vm.showPointStatus);
    },

    setPoint(status) {
      const vm = this;
      const trendData = vm.trendData;
      const options = {
        series: []
      };

      for (let i = 0, len = trendData.length; i < len; i++) {
        const single = {
          showSymbol: status,
          name: trendData[i].name
        };
        options.series.push(single);
      }
      vm.$refs.chart.mergeOptions(options);
    },
    //原始标记
    rawMarkHandler(val) {
      const vm = this;
      vm.showRawMarkStatus = !!val;
      vm.setRawMark(vm.showRawMarkStatus);
    },
    //切换原始标记展示状态
    setRawMark(status) {
      const vm = this;
      const trendData = vm.trendData;
      const options = {
        series: []
      };

      for (let i = 0, len = trendData.length; i < len; i++) {
        if (status) {
          const data = vm.getSingleRawMark(trendData[i]);
          const single = {
            markArea: {
              silent: true,
              data: data
            },
            name: trendData[i].name
          };
          options.series.push(single);
        } else {
          const single = {
            markArea: {
              silent: true,
              itemStyle: {
                color: "rgb(230, 230, 230)",
                opacity: 0.5
              },
              data: []
            },
            name: trendData[i].name
          };
          options.series.push(single);
        }
      }
      vm.$refs.chart.mergeOptions(options);
    },
    //计算单条曲线的原始标记数据
    getSingleRawMark(trendData) {
      const vm = this;
      let data = [];
      const markAreaData = [];
      let startIndex = 0;
      let endIndex = 0;
      data = vm.getOriginalData(trendData);

      //找到原始标记的status为3的序列
      while (startIndex !== -1 && endIndex !== data.length - 1) {
        startIndex = _.findIndex(data, { status: 3 }, endIndex);
        if (startIndex !== -1) {
          endIndex = _.findIndex(data, { status: 0 }, startIndex);
          if (endIndex === -1) {
            endIndex = data.length - 1;
          }
          markAreaData.push([]);
          markAreaData[markAreaData.length - 1].push({
            xAxis: data[startIndex].time
          });
          markAreaData[markAreaData.length - 1].push({
            xAxis: data[endIndex].time
          });
        }
      }
      return markAreaData;
    },
    //获取接口返回的原始数据, 原始数据中才包含status,用于判断是否为原始标记的数据点
    getOriginalData(trendData) {
      const vm = this;
      const data = vm.originalData;
      const param = trendData.param;

      for (let i = 0, len = data.length; i < len; i++) {
        if (
          data[i].deviceId === param.deviceId &&
          data[i].logicalId === param.logicalId &&
          data[i].dataTypeId === param.dataTypeId &&
          data[i].dataId === param.dataId
        ) {
          return data[i].dataList;
        }
      }
    },
    //限值显示隐藏的处理
    limitHandler(val) {
      const vm = this;
      vm.showLimitStatus = !!val;
      vm.setMarkline();
    },

    //处理平均值展示和隐藏逻辑
    averageHandler(val) {
      const vm = this;
      vm.showAverageStatus = !!val;
      vm.setMarkline();
    },

    //差值处理逻辑
    diffHandler(val) {
      const vm = this;
      vm.showDiffStatus = !!val;
      //差值和原始值是不同数据, 清空最值保存值, 重新计算
      vm.extremValue = [];
      if (val) {
        vm.showDiff();
      } else {
        vm.hideDiff();
      }
      vm.updateChart();
      vm.updateTable();
      vm.setTrendStatus();
    },
    //显示差值
    showDiff() {
      const vm = this;
      vm.trendData = vm.diffData;
    },
    //隐藏差值
    hideDiff() {
      const vm = this;
      vm.trendData = vm.originalTrendData;
    },
    //计算趋势曲线差值数据
    calcDiffData() {
      const vm = this;
      const trendData = vm.trendData;
      const diffData = trendData.map(trendItem => {
        const diffItem = _.cloneDeep(trendItem);
        diffItem.data = trendItem.data.map((item, index, array) => {
          const orginalValue = item[1];
          let value;
          if (index === array.length - 1) {
            value = 0;
          } else {
            let nextValue = array[index + 1][1];
            if (_.isNil(orginalValue) || !_.isNumber(orginalValue)) {
              value = NaN;
            } else if (_.isNil(nextValue) || !_.isNumber(nextValue)) {
              value = NaN;
            } else {
              value = parseFloat(
                Common.formatNumberWithPrecision(
                  nextValue - orginalValue,
                  vm.precision || 2
                )
              );
            }
          }

          return [item[0], value];
        });
        return diffItem;
      });
      vm.originalTrendData = trendData;
      vm.diffData = diffData;
    },
    /**
     * @description: 初始化差值状态
     * @param {*}
     * @return {*}
     */
    initDiffData() {
      const vm = this;
      vm.calcDiffData();
      if (vm.showDiffStatus) {
        vm.trendData = vm.diffData;
      }

      //重新获取数据后, 清空最值保存值, 重新计算
      vm.extremValue = [];
      vm.updateChart();
      vm.updateTable();
      vm.setTrendStatus();
    },

    //最值展示和隐藏逻辑
    extremValueHandler(val) {
      const vm = this;
      if (val) {
        vm.showMaxAndMinStatus = true;
        vm.showMaxAndMin();
      } else {
        vm.showMaxAndMinStatus = false;
        vm.removeMaxAndMin();
      }
    },
    showMaxAndMin() {
      const vm = this;
      const trendData = vm.trendData;
      const options = {
        series: []
      };

      for (let i = 0, len = trendData.length; i < len; i++) {
        const single = vm.calcMaxAndMin(trendData[i]);
        if (!_.isNil(single)) {
          options.series.push(single);
        }
      }

      vm.$refs.chart.mergeOptions(options);
    },
    removeMaxAndMin() {
      const vm = this;
      const trendData = vm.trendData;
      const options = {
        series: []
      };

      for (let i = 0, len = trendData.length; i < len; i++) {
        const single = {
          markPoint: { data: [] },
          name: trendData[i].name
        };
        options.series.push(single);
      }

      vm.$refs.chart.mergeOptions(options);
    },
    //计算单条曲线的最大最小值，返回echarts配置项
    calcMaxAndMin(singleTrend) {
      const vm = this;
      const data = singleTrend.data;
      let maxValue;
      let minValue;
      let single;
      if (data.length < 1) {
        return null;
      }
      maxValue = _.maxBy(data, function (a) {
        return a[1];
      });
      minValue = _.minBy(data, function (a) {
        return a[1];
      });

      if (_.isNil(maxValue) || _.isNil(minValue)) {
        return null;
      }
      vm.saveExtremValue(singleTrend.param.unit, maxValue[1], minValue[1]);

      single = {
        markPoint: {
          data: [
            {
              value: maxValue[1],
              name: $T("最大值"),
              coord: maxValue
            },
            {
              value: minValue[1],
              name: $T("最小值"),
              coord: minValue
            }
          ],
          label: {
            formatter: "{@value}({b})"
          }
        },
        name: singleTrend.name
      };

      return single;
    },
    saveExtremValue(name, max, min) {
      const vm = this;
      const extremValue = vm.extremValue;
      const singleExtrem = _.find(extremValue, { name: name });
      if (singleExtrem) {
        singleExtrem.max = max > singleExtrem.max ? max : singleExtrem.max;
        singleExtrem.min = min < singleExtrem.min ? min : singleExtrem.min;
      } else {
        extremValue.push({
          name,
          max,
          min
        });
      }
    },

    //设置平均线, 上下限值线等Markline的显示
    setMarkline() {
      const vm = this;
      const trendData = vm.trendData;
      const options = {
        series: []
      };

      //遍历每一条曲线进行处理
      for (let i = 0, len = trendData.length; i < len; i++) {
        const single = vm.setSingleMarkline(trendData[i]);
        options.series.push(single);
      }

      vm.$refs.chart.mergeOptions(options);
    },
    //设置每一条曲线的markline
    setSingleMarkline(singleData) {
      const vm = this;
      let single;
      //如果不显示限值, 不显示平均线, 则清空markline显示
      if (!vm.showAverageStatus && !vm.showLimitStatus) {
        return {
          markLine: { data: [] },
          name: singleData.name
        };
      }

      single = {
        markLine: {
          data: [],
          symbol: "none",
          label: {
            formatter: "{@value}({b})",
            position: "insideStartTop"
          }
        },
        name: singleData.name
      };

      //如果显示平均线, 且数据长度大于0则计算平均值, 并增加平均值markline
      if (vm.showAverageStatus && singleData.data.length > 0) {
        const data = singleData.data;
        let averageValue;
        const dataWithoutInvaid = data.filter(item => {
          return _.isNumber(item[1]) && !_.isNaN(item[1]);
        });
        averageValue = _.meanBy(dataWithoutInvaid, function (a) {
          return a[1];
        });
        single.markLine.data.push({
          yAxis: averageValue,
          name: $T("平均值")
        });
      }

      //如果显示限值, 则增加限值的markline
      if (vm.showLimitStatus) {
        const param = singleData.param;
        if (param.upperLimit) {
          single.markLine.data.push({
            yAxis: param.upperLimit,
            name: vm.limitText.upperLimitText
          });
        }

        if (param.lowerLimit) {
          single.markLine.data.push({
            yAxis: param.lowerLimit,
            name: vm.limitText.lowerLimitText
          });
        }
      }
      return single;
    },

    //查询曲线数据并处理
    getChartData() {
      const vm = this;
      const queryBody = {
        endTime: vm.getQueryTime().endTime,
        interval: vm.interval_in,
        meterConfigs: vm.getParams(),
        startTime: vm.getQueryTime().startTime
      };

      if (queryBody.meterConfigs.length < 1) {
        vm.clearLines();
        return;
      }

      let queryOption = {
        url: vm.dataConfig.queryUrl,
        method: "POST",
        data: queryBody
      };
      if (this.showSecond) {
        const queryBody = {
          endTime: new Date(vm.getQueryTime().endTime).getTime(),
          interval: vm.interval_in,
          meterConfigs: vm.getParams(),
          startTime: new Date(vm.getQueryTime().startTime).getTime()
        };
        queryOption = {
          url: "/device-data/api/v1/second/datalog",
          method: "POST",
          data: queryBody,
          headers: {
            hideNotice: true
          }
        };
        fetch(queryOption).then(function (response) {
          if (response.code === 0) {
            const resData = vm._.get(response, "data", null) || null;
            if (_.isArray(resData)) {
              vm.originalData = resData;
              vm.generateTendData(resData);
              vm.initDiffData();
            } else {
              vm.clearLines();
            }
          }
        });
      } else {
        fetch(queryOption).then(function (response) {
          if (response.code === 0) {
            if (_.isArray(response.data)) {
              vm.originalData = response.data;
              vm.generateTendData(response.data);
              vm.initDiffData();
            } else {
              vm.clearLines();
            }
          }
        });
      }
    },
    /**清空数据 */
    clearLines() {
      const vm = this;
      vm.originalData = [];
      vm.trendData = [];
      vm.originalTrendData = [];
      vm.diffData = [];
      vm.updateChart();
      vm.updateTable();
      vm.setTrendStatus();
    },
    //生成趋势曲线数据
    generateTendData(data) {
      const vm = this;
      vm.trendData = [];

      vm.params_in.forEach(item => {
        const single = vm.getSingelTrendData(item, data);
        vm.trendData.push(single);
      });
    },
    //获取单条曲线的数据
    getSingelTrendData(param, data) {
      const vm = this;
      const single = {
        name: "",
        param: {},
        data: []
      };
      single.param = param;
      single.name = `${single.param.deviceName}-${single.param.dataName}-${single.param.dataTypeName}`;
      if (this.showSecond) {
        single.name = single.name + "-秒级";
      }
      for (let i = 0, len = data.length; i < len; i++) {
        if (
          data[i].deviceId === param.deviceId &&
          data[i].logicalId === param.logicalId &&
          data[i].dataTypeId === param.dataTypeId &&
          data[i].dataId === param.dataId
        ) {
          single.data = _.map(data[i].dataList, function (n) {
            return [
              n.time,
              parseFloat(
                Common.formatNumberWithPrecision(n.value, vm.precision || 2)
              )
            ];
          });
          break;
        }
      }

      return single;
    },

    //将trendData的数据展示到曲线
    updateChart() {
      const vm = this;
      const trendData = vm.trendData;
      const series = [];
      let yIndexs = [];
      const legendData = [];
      //生成Y轴序列
      yIndexs = vm.getYIndexs();

      for (let i = 0, len = trendData.length; i < len; i++) {
        const param = trendData[i].param;
        const yAxisIndex = param.yIndex ? param.yIndex : 0;

        const single = {
          name: trendData[i].name,
          data: trendData[i].data,
          type: "line",
          showSymbol: false,
          smooth: true,
          sampling: "average",
          yAxisIndex: yAxisIndex
        };

        series.push(single);
        legendData.push(trendData[i].name);
      }

      const scatter = vm.scatter_in;
      let scatterData = [];
      if (scatter && _.isArray(scatter)) {
        scatterData = scatter.map(item => [item.x, 0, item]);
      }

      series.push({
        name: "trendScatter",
        data: scatterData,
        type: "scatter",
        symbol: "pin",
        symbolSize: 25
      });

      vm.$refs.chart.mergeOptions(
        vm.CetChart_trend.config.options,
        !vm.setMergeOptions
      );
      if (yIndexs.length < 1) {
        vm.$refs.chart.mergeOptions({
          series: series,
          legend: { data: legendData }
        });
      } else {
        //根据坐标轴的数量设置grid的左右间距值
        const leftIndexNum =
          parseInt(yIndexs.length / 2) + (yIndexs.length % 2);
        const rightIndexNum = yIndexs.length - leftIndexNum;
        const grid = {
          left: 50 * (leftIndexNum - 1) + 20,
          right: 20 + 50 * rightIndexNum
        };
        vm.$refs.chart.mergeOptions({
          series: series,
          yAxis: yIndexs,
          grid: grid,
          legend: { data: legendData }
        });
      }
    },
    getYIndexs() {
      const vm = this;
      const trendData = vm.trendData;
      let yIndexs = [];
      trendData.forEach(item => {
        if (!_.isNil(item.param.unit)) {
          const index = yIndexs.indexOf(item.param.unit);
          if (index === -1) {
            yIndexs.push(item.param.unit);
            item.param.yIndex = yIndexs.length - 1;
          } else {
            item.param.yIndex = index;
          }
        }
      });
      yIndexs = yIndexs.map((item, index) => ({
        splitLine: {
          show: false
        },
        position: index % 2 === 0 ? "left" : "right",
        offset: 60 * parseInt(index / 2),
        name: item,
        min(value) {
          const extrem = _.find(vm.extremValue, { name: item });
          let max;
          let min;
          if (extrem) {
            const dataMax = _.get(extrem, "max");
            const dataMin = _.get(extrem, "min");
            max = _.max([dataMax, value.max]);
            min = _.min([dataMin, value.min]);
          } else {
            max = value.max;
            min = value.min;
          }

          return Math.floor(min - (max - min) / 10);
        },
        max(value) {
          const extrem = _.find(vm.extremValue, { name: item });
          let max;
          let min;
          if (extrem) {
            const dataMax = _.get(extrem, "max");
            const dataMin = _.get(extrem, "min");
            max = _.max([dataMax, value.max]);
            min = _.min([dataMin, value.min]);
          } else {
            max = value.max;
            min = value.min;
          }
          return Math.ceil(max + (max - min) / 10);
        }
      }));
      return yIndexs;
    },
    //将数据更新到表格中
    updateTable() {
      const vm = this;
      const trendData = vm.trendData;
      const tableList = [];

      for (let i = 0, len = trendData.length; i < len; i++) {
        const obj = {
          name: trendData[i].name,
          data: [],
          header: [
            { label: $T("时间"), prop: "d0", width: "160" },
            { label: trendData[i].name, prop: "d1" }
          ]
        };
        for (var j = 0; j < trendData[i].data.length; j++) {
          const value = Common.formatNumberWithPrecision(
            _.cloneDeep(trendData[i].data[j][1]),
            vm.precision || 2
          );
          const res = {
            d0: _.cloneDeep(
              vm.$moment(trendData[i].data[j][0]).format("YYYY-MM-DD HH:mm:ss")
            ),
            d1: _.isNil(value) ? "--" : value
          };
          obj.data.push(res);
        }
        tableList.push(obj);
      }
      vm.tableData = tableList;
    },
    //根据最值、平均值等勾选状态进行展示
    setTrendStatus() {
      const vm = this;
      vm.extremValueHandler(vm.showMaxAndMinStatus);
      vm.pointHandler(vm.showPointStatus);
      vm.rawMarkHandler(vm.showRawMarkStatus);
      vm.setMarkline();
    },
    chartFinish() {},
    clickHandler(params) {
      if (
        params.seriesType === "scatter" &&
        params.seriesName === "trendScatter"
      ) {
        this.$emit("scatterClick_out", params.data[2]);
      }
    }
  },
  mounted: function () {},
  activated() {}
};
</script>
<style lang="scss" scoped>
.el-main {
  overflow-y: hidden;
}
.trend-class {
  text-align: right;
  @include padding_left(J2);
  @include padding_right(J2);
  > div,
  > label {
    display: inline-block;
  }
  .el-select {
    display: inline-block;
  }
}
.trend-table {
  display: inline-block;
  height: 100%;
  width: 100%;
  p {
    text-align: center;
  }
  > div {
    display: inline-block;
    height: 100%;
    padding-right: 12px;
    min-width: 312px;
    box-sizing: border-box;
  }
}
.trend-chart {
  width: 100%;
  height: 100%;
}

.hide-chart {
  position: absolute;
  left: -10000px;
  top: -10000px;
  width: calc(100% - 80px);
  height: 100%;
}
</style>
