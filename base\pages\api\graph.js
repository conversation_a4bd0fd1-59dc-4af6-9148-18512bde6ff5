import fetch from "eem-utils/fetch";
const version = "v2";

function processRequest(data) {
  // 对 data 进行任意转换处理
  return data;
}

function processResponse(response) {
  // 对 response 进行任意转换处理, response结构
  //   {
  //     // `data` 由服务器提供的响应
  //     data: {},

  //     // `status` 来自服务器响应的 HTTP 状态码
  //     status: 200,

  //     // `statusText` 来自服务器响应的 HTTP 状态信息
  //     statusText: 'OK',

  //     // `headers` 服务器响应的头
  //     headers: {},

  //     // `config` 是为请求提供的配置信息
  //     config: {}
  //   }
  return response;
}

export function getGraphTree(data) {
  return fetch({
    url: `/eem-service/${version}/peccore/graphTree`,
    method: "GET",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    params: data
  });
}
