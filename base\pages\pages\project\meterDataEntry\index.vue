<template>
  <div class="page eem-common">
    <el-container class="page-body">
      <el-aside width="315px" class="eem-aside">
        <ProjectTree
          @currentNode_out="updateTreeNode"
          @project_node="updateProject"
          :refreshTrigger="refreshProjectTree"
        />
      </el-aside>
      <el-container class="fullheight mlJ3 flex-column eem-min-width-mini">
        <div
          v-if="!showRight"
          class="text-center w100"
          style="margin-top: 80px"
        >
          <span class="fs22">{{ showLeftName }}</span>
        </div>
        <div class="eem-container mbJ1" v-if="showRight">
          <CetButton
            class="fr mlJ1"
            v-bind="CetButton_recalculation"
            v-on="CetButton_recalculation.event"
          ></CetButton>
          <CetButton
            class="fr mlJ1"
            v-bind="CetButton_export"
            v-on="CetButton_export.event"
          ></CetButton>
          <CetButton
            class="fr mlJ1"
            v-show="showUpload"
            v-bind="CetButton_import"
            v-on="CetButton_import.event"
          ></CetButton>
          <customElSelect
            v-model="ElSelect_1.value"
            v-bind="ElSelect_1"
            v-on="ElSelect_1.event"
            class="fr mlJ1 groupSelect"
            prefix_in="录入类型"
          >
            <el-option-group
              v-for="group in ElOption_1.options_in"
              :key="group[ElOption_1.key]"
              :label="group[ElOption_1.label]"
              :value="group[ElOption_1.value]"
              :disabled="group[ElOption_1.disabled]"
            >
              <el-option
                v-for="item in group.options"
                :key="item[ElOption_1.key]"
                :label="item[ElOption_1.label]"
                :value="item[ElOption_1.value]"
                :disabled="item[ElOption_1.disabled]"
              ></el-option>
            </el-option-group>
          </customElSelect>
          <customElSelect
            v-model="ElSelect_2.value"
            v-bind="ElSelect_2"
            v-on="ElSelect_2.event"
            class="fr"
            prefix_in="数据类型"
          >
            <ElOption
              v-for="item in ElOption_2.options_in"
              :key="item[ElOption_2.key]"
              :label="item[ElOption_2.label]"
              :value="item[ElOption_2.value]"
              :disabled="item[ElOption_2.disabled]"
            ></ElOption>
          </customElSelect>
        </div>
        <div class="flex-auto eem-container flex-column" v-if="showRight">
          <div class="clearfix mbJ3">
            <div
              v-if="this.ElSelect_1.value === 2"
              style="display: inline-block"
            >
              <span class="asterisk">*</span>
              <span class="lh32">
                累计差值录入不支持可以采集数据的表计，请勿针对此类表计录入！
              </span>
            </div>
            <CetButton
              class="fr"
              v-bind="CetButton_save"
              v-on="CetButton_save.event"
            ></CetButton>
            <CetButton
              class="fr mrJ1"
              v-bind="CetButton_cancel"
              v-on="CetButton_cancel.event"
            ></CetButton>
            <CetButton
              class="fr mrJ1"
              v-bind="CetButton_add"
              v-on="CetButton_add.event"
            ></CetButton>
            <div class="year-range fr mrJ1">
              <el-date-picker
                ref="dayPicker"
                v-model="dayInfo"
                type="daterange"
                v-if="ElSelect_3.value === 12"
                @change="timeChanged"
                :clearable="false"
                :pickerOptions="CetDatePicker_2.pickerOptions"
              ></el-date-picker>
            </div>
            <el-date-picker
              ref="seasonPicker"
              class="fr mrJ1"
              v-if="ElSelect_3.value === 14"
              v-model="CetDatePicker_2.val"
              v-bind="CetDatePicker_2.config"
              @change="timeChanged"
              :pickerOptions="CetDatePicker_2.pickerOptions"
            ></el-date-picker>
            <customElSelect
              v-model="ElSelect_3.value"
              v-bind="ElSelect_3"
              v-on="ElSelect_3.event"
              v-if="ElSelect_1.value !== 1"
              class="fr mrJ1"
              prefix_in="查询时段"
            >
              <ElOption
                v-for="item in ElOption_3.options_in"
                v-show="item.visible"
                :key="item[ElOption_3.key]"
                :label="item[ElOption_3.label]"
                :value="item[ElOption_3.value]"
                :disabled="item[ElOption_3.disabled]"
              ></ElOption>
            </customElSelect>
          </div>
          <div class="flex-auto clearfix">
            <CetTable
              :data.sync="CetTable_1.data"
              :dynamicInput.sync="CetTable_1.dynamicInput"
              v-bind="CetTable_1"
              v-on="CetTable_1.event"
              style="height: calc(100% - 32px)"
            >
              <el-table-column
                type="index"
                :label="$T('序号')"
                :fixed="true"
                align="left"
                :width="language ? 90 : 50"
              ></el-table-column>
              <el-table-column
                prop="logtime"
                :fixed="true"
                align="left"
                :label="$T('时间')"
                minWidth="120"
                :formatter="timeFormat"
              ></el-table-column>
              <ElTableColumn
                v-for="(item, index) in columnArr"
                :key="index"
                v-bind="item"
                align="left"
              >
                <template slot-scope="scope">
                  <div v-if="true">
                    <el-input
                      v-model="scope.row[item.prop]"
                      :disabled="!scope.row.edit"
                      placeholder="请输入"
                      @keyup.native="handleNum(scope.row, item.prop, 2)"
                    ></el-input>
                  </div>
                </template>
              </ElTableColumn>
            </CetTable>
            <div class="text-right mtJ" style="height: 32px">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalCount"
              ></el-pagination>
            </div>
          </div>
        </div>
      </el-container>
    </el-container>
    <exportWin
      :visibleTrigger_in="exportWin.visibleTrigger_in"
      :closeTrigger_in="exportWin.closeTrigger_in"
      :inputData_in="exportWin.inputData_in"
    />
    <addMeter
      :visibleTrigger_in="addMeter.visibleTrigger_in"
      :closeTrigger_in="addMeter.closeTrigger_in"
      :inputData_in="addMeter.inputData_in"
      :columnArr="addMeter.meterColumnArr"
      @updata_out="updata_out"
    ></addMeter>
    <!-- 导入数据 -->
    <ImportDataDialog
      v-bind="ImportDataDialog"
      v-on="ImportDataDialog.event"
    ></ImportDataDialog>
    <recalculation
      v-bind="recalculation"
      @updataReCalcState_out="updataReCalcState_out"
    ></recalculation>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import ProjectTree from "./subcomponents/ProjectTree.vue";
import moment from "moment";
import exportWin from "./subcomponents/exportWin.vue";
import addMeter from "./subcomponents/add.vue";
import ImportDataDialog from "./subcomponents/ImportDataDialog.vue";
import recalculation from "./subcomponents/recalculation.vue";
export default {
  name: "meterConsumptionDataEntry",
  components: {
    ProjectTree,
    exportWin,
    addMeter,
    ImportDataDialog,
    recalculation
  },
  computed: {
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  data() {
    return {
      // 树节点刷新
      refreshProjectTree: Date.now(),
      // 左侧树节点获取节点数据
      currentNode: null,
      // 按照天数选择信息
      dayInfo: [],
      // 控制右侧表格显示
      showRight: false,
      // 判断表单数据是否进行更改
      editFlag: false,
      // 数据不存在时标题展示
      showLeftName: "请选择表计设备",
      // 是否展示导入
      showUpload: true,
      // 表头数据
      columnArr: [],
      CetTable_1: {
        //组件模式设置项
        queryMode: "", //查询按钮触发  trigger  ,或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          //   record_out: this.CetTable_1_record_out
        }
      },
      ElSelect_1: {
        value: 2,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_3: {
        value: 12,
        style: {
          width: "160px"
        },
        event: {
          change: this.ElSelect_3_change_out
        }
      },
      ElOption_3: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      CetButton_import: {
        visible_in: true,
        disable_in: false,
        title: $T("导入"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_import_statusTrigger_out
        }
      },
      CetButton_recalculation: {
        visible_in: true,
        disable_in: false,
        title: "重算",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_recalculation_statusTrigger_out
        }
      },
      CetDatePicker_2: {
        disable_in: false,
        val: [],
        config: {
          valueFormat: "timestamp",
          type: "monthrange",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          clearable: false,
          style: {
            width: "300px"
          }
        },
        pickerOptions: {
          // 限制预约时间
          disabledDate(time) {
            let timeStr = moment(new Date()).startOf("day").valueOf();
            return time.getTime() > timeStr;
          }
        }
      },
      CetButton_save: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_save_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_add: {
        visible_in: false,
        disable_in: false,
        title: "换表",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      // 导出按钮的弹窗
      exportWin: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      // 录入类型固定数据
      typeofEntryData: [
        {
          text: "手动设值",
          options: [
            {
              id: 2,
              text: "累计差值"
            }
            // 由于后端在表码值上有点问题，根据曹雪莹那边要求先对表码值进行隐藏
            // {
            //   id: 1,
            //   text: "表码值"
            // }
          ]
        },
        {
          text: "手动换表",
          options: [
            {
              id: 31,
              text: "手动换表"
            }
          ]
        }
      ],
      // 查询时段数据
      inquiryPeriodData: [
        {
          id: 12,
          visible: true,
          text: $T("日")
        },
        {
          id: 14,
          visible: true,
          text: $T("月")
        }
      ],
      // 表格数据录入
      tableData: [],
      // 数据查询
      params: {
        deviceid: null,
        dataid: null,
        startTime: null,
        endTime: null,
        status: 30,
        meterType: 2,
        aggregationcycle: 12,
        page: {
          index: 0,
          limit: 100
        }
      },
      // 换表
      addMeter: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        meterColumnArr: null
      },
      // 重算
      recalculation: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      // 项目节点信息
      projectNode: {
        objectId: 1,
        objectLabel: "project"
      },
      // 分页标签
      currentPage: 1,
      pageSize: 100,
      totalCount: 0,
      // 导入弹窗组件
      ImportDataDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        event: {
          refresh: this.refresh
        }
      }
    };
  },
  methods: {
    init() {
      this.ElOption_1.options_in = this._.cloneDeep(this.typeofEntryData);
      this.ElOption_3.options_in = this._.cloneDeep(this.inquiryPeriodData);
      this.ElSelect_1.value = this._.get(
        this.typeofEntryData,
        "[0].options[0].id",
        2
      );
      this.ElSelect_3.value = this._.get(this.inquiryPeriodData, "[0].id", 12);
      this.timeInit();
    },
    timeInit() {
      this.dayInfo = [
        this.$moment().startOf("day").valueOf(),
        this.$moment().endOf("day").valueOf()
      ];
      this.CetDatePicker_2.val = [
        this.$moment().startOf("month").valueOf(),
        this.$moment().endOf("month").valueOf()
      ];
    },
    // 时间值校验
    timeFormat(row, column, cellValue, index) {
      let timeString;
      if (this.ElSelect_1.value === 2) {
        timeString = this.ElSelect_3.value === 12 ? "YYYY-MM-DD" : "YYYY-MM";
      } else {
        timeString = "YYYY-MM-DD HH:mm:ss";
      }
      return this.$moment(cellValue).format(timeString);
    },
    CetButton_export_statusTrigger_out(val) {
      let param = {
        startTime: this.params.startTime,
        endTime: this.params.endTime,
        status: this.params.status,
        meterType: this.params.meterType,
        aggregationcycle: this.params.aggregationcycle
      };
      this.exportWin.inputData_in = this._.cloneDeep(param);
      this.exportWin.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 时间控件选择时间触发
    timeChanged() {
      if (!this.judgeTimeSection()) {
        return;
      }
      this.currentPage = 1;
      this.params.page.index = 0;
      this.isEditFlag();
    },
    // 表格中输入数字控制
    handleNum(row, key, num) {
      this.editFlag = true;
      var value;
      if (typeof row[key] === "object") {
        value = row[key].val;
      } else {
        value = row[key];
      }
      var reg = new RegExp("^(\\-)*(\\d+)\\.(\\d{0," + num + "}).*$");
      var reg2 = new RegExp("^(\\d{0,9})(\\d+)\\.(\\d{0," + num + "}).*$");
      var reg3 = new RegExp("^(\\d{0,9})(\\d+)$");
      var val = String(value);
      if (val) {
        val = val.replace(/[^\d.]/g, ""); //清除数字和'.'以外的字符
        val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", "."); //只保留顺位第一的'.'
        val = val.replace(reg, "$1$2.$3"); //只能输入两位位小数
      }
      if (val.length >= 2 && val.indexOf(".") !== 1 && val[0] == 0) {
        //在非小数的时候清除前导0
        val = val.replace(/0/, "");
      }
      if (val.length >= 2 && val.indexOf(".") === 0) {
        // 在先输入小数点时补0
        val = Number(val);
      }
      if (typeof row[key] === "object") {
        if (String(val).indexOf(".") !== -1 && Number(val) > 999999999.99) {
          val = val.replace(reg2, "$1.$3");
        } else if (String(val).indexOf(".") === -1 && String(val).length > 9) {
          val = val.replace(reg3, "$1");
        }
        row[key].val = val;
      } else {
        if (String(val).indexOf(".") !== -1 && Number(val) > 999999999.99) {
          val = val.replace(reg2, "$1.$3");
        } else if (String(val).indexOf(".") === -1 && String(val).length > 9) {
          val = val.replace(reg3, "$1");
        }
        row[key] = val;
      }
      this.CetTable_1.data.find(item => item.logtime == row.logtime)[key] =
        row[key];
    },
    // 树节点选中之后更新
    updateTreeNode(val) {
      const callback = () => {
        this.currentNode = this._.cloneDeep(val);
        if (val.nodeType === 269619472) {
          this.params.deviceid = [val.nodeId];
          this.getDataType(val.nodeId);
          this.init();
        } else {
          this.showRight = false;
          this.showLeftName = "请选择表计设备";
        }
      };
      this.isEditFlag(callback);
      // this.currentNode = this._.cloneDeep(val);
      //   if (val.nodeType === 269619472) {
      //     this.params.deviceid = [val.nodeId];
      //     this.getDataType(val.nodeId);
      //     this.init();
      //   } else {
      //     this.showRight = false;
      //     this.showLeftName = "请选择表计设备";
      //   }
    },
    // 获取数据类型的数据
    getDataType(deviceId) {
      customApi.getPoints(deviceId).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          this.ElOption_2.options_in = this._.cloneDeep(data);
          if (data.length == 0) {
            this.showRight = false;
            this.showLeftName =
              "当前设备无正向有功电能、反向有功电能或者能源累计值定时记录测点，请在PecConfig中配置定时记录映射方案！";
          } else {
            this.ElSelect_2.value = data[0].id;
            this.getTableData();
            this.showRight = true;
          }
        }
      });
    },
    // 查询时段数据切换
    ElSelect_3_change_out(val) {
      this.ElSelect_3.value = val;
      if (val === 12) {
        this.CetDatePicker_2.val = [
          this.$moment().startOf("month").valueOf(),
          this.$moment().endOf("month").valueOf()
        ];
      } else if (val === 14) {
        this.dayInfo = [
          this.$moment().startOf("day").valueOf(),
          this.$moment().endOf("day").valueOf()
        ];
      }
      this.currentPage = 1;
      this.params.page.index = 0;
      this.isEditFlag();
    },
    // 对时间控制选择的时间进行判断是否合规
    judgeTimeSection() {
      let flag = true;
      switch (this.ElSelect_3.value) {
        case 12:
          if (this.ElSelect_1.value === 1) {
            if (
              this.$moment(this.dayInfo[0]).add(31, "day").valueOf() <
              this.$moment(this.dayInfo[1]).valueOf()
            ) {
              this.$message({
                type: "warning",
                message: $T("起止时间不能相差超过31天")
              });

              this.dayInfo[0] = this.$moment(this.dayInfo[1])
                .subtract(30, "day")
                .valueOf();
            }
          } else {
            if (
              this.$moment(this.dayInfo[0]).add(365, "day").valueOf() <
              this.$moment(this.dayInfo[1]).valueOf()
            ) {
              this.$message({
                type: "warning",
                message: $T("起止时间不能相差超过365天")
              });

              this.dayInfo[0] = this.$moment(this.dayInfo[1])
                .subtract(365, "day")
                .valueOf();
            }
          }
          break;
        case 14:
          if (
            this.$moment(this.CetDatePicker_2.val[0])
              .add(11, "month")
              .startOf("month")
              .valueOf() <
            this.$moment(this.CetDatePicker_2.val[1]).startOf("month").valueOf()
          ) {
            this.$message({
              type: "warning",
              message: $T("起止时间不能相差超过12个月")
            });
            this.CetDatePicker_2.val[0] = this.$moment(
              this.CetDatePicker_2.val[1]
            )
              .startOf("month")
              .subtract(11, "month")
              .valueOf();
          }
          break;
        default:
          break;
      }
      return flag;
    },
    // 录入类型数据切换
    ElSelect_1_change_out(val) {
      this.CetButton_add.visible_in = false;
      if (val === 1) {
        this.ElSelect_3.value = 12;
      } else if (val === 31) {
        this.CetButton_add.visible_in = true;
      }
      this.timeInit();
      this.currentPage = 1;
      this.params.page.index = 0;
      this.isEditFlag();
    },
    // 数据类型切换
    ElSelect_2_change_out(val) {
      this.currentPage = 1;
      this.params.page.index = 0;
      this.isEditFlag();
    },
    // 获取表格查询数据
    getTableData() {
      this.params.dataid = this.ElSelect_2.value;
      this.params.meterType = this.ElSelect_1.value;
      this.params.status = this.ElSelect_1.value === 31 ? 31 : 30;
      this.params.aggregationcycle = this.ElSelect_3.value;
      switch (this.ElSelect_3.value) {
        case 12:
          this.params.startTime = this.$moment(this.dayInfo[0])
            .startOf("day")
            .valueOf();
          this.params.endTime =
            this.$moment(this.dayInfo[1]).endOf("day").valueOf() + 1;
          break;
        case 14:
          this.params.startTime = this.$moment(this.CetDatePicker_2.val[0])
            .startOf("month")
            .valueOf();
          this.params.endTime =
            this.$moment(this.CetDatePicker_2.val[1]).endOf("month").valueOf() +
            1;
          break;
        default:
          break;
      }
      let param = this.params;
      customApi.getFlinkDatalogData(param).then(response => {
        if (response.code === 0) {
          var data = this._.get(response, "data", []);
          data.forEach(item => {
            item.logicalValues.forEach(i => {
              item["value_" + i.logicalid] =
                i.value === null ? null : i.value.toFixed(2);
            });
          });
          let columnArr = [];
          if (data && data.length) {
            data[0].logicalValues.forEach(i => {
              columnArr.push({
                prop: "value_" + i.logicalid,
                label: this.getHeadText(i.logicalid),
                headerAlign: "left",
                align: "left",
                showOverflowTooltip: true,
                minWidth: 160
              });
            });
          }
          this.columnArr = columnArr;
          this.totalCount = response.total;
          this.CetTable_1.data = this._.cloneDeep(data);
        }
        this.editFlag = false;
      });
    },
    // 表格头部名称
    getHeadText(iText) {
      return this.ElSelect_1.value === 2
        ? "回路" + iText + "累计值"
        : "回路" + iText + "表码值";
    },
    // 换表数据
    CetButton_add_statusTrigger_out(val) {
      this.addMeter.visibleTrigger_in = this._.cloneDeep(val);
      this.addMeter.meterColumnArr = this._.cloneDeep(this.columnArr);
      let param = {
        deviceid: this.params.deviceid[0],
        dataid: this.params.dataid,
        status: this.params.status,
        meterType: this.params.meterType
      };
      this.addMeter.inputData_in = this._.cloneDeep(param);
    },
    // 保存按钮
    CetButton_save_statusTrigger_out() {
      let oldData = this._.cloneDeep(this.CetTable_1.data);
      const callback = () => {
        this.CetTable_1.data.forEach(item => {
          item.logicalValues.forEach(i => {
            i.value = item["value_" + i.logicalid];
            item.delete = this.isEmpty(item["value_" + i.logicalid], 1);
            delete item["value_" + i.logicalid];
          });
        });
        let data = this._.cloneDeep(this.CetTable_1.data);
        this.editFlag = false;
        customApi.getFlinkEdit(data).then(response => {
          if (response.code === 0) {
            this.$message({
              type: "success",
              message: $T("保存成功")
            });
            this.getTableData();
          }
        });
      };
      if (this.ElSelect_1.value === 1) {
        this.disOrdering(oldData, callback);
        return;
      } else {
        callback();
      }
      // this.CetTable_1.data.forEach(item => {
      //   item.logicalValues.forEach(i => {
      //     i.value = item["value_" + i.logicalid];
      //     delete item["value_" + i.logicalid];
      //   });
      // });
      // let data = this._.cloneDeep(this.CetTable_1.data);
      // this.editFlag = false;
      // customApi.getFlinkEdit(data).then(response => {
      //   if (response.code === 0) {
      //     this.$message({
      //       type: "success",
      //       message: $T("保存成功")
      //     });
      //     this.getTableData();
      //   }
      // });
    },
    // 判断数值是否为空
    isEmpty(val, id) {
      if (typeof val === "string" && val.length === 0 && id === 1) {
        return true;
      }
      return false;
    },
    // 取消按钮
    CetButton_cancel_statusTrigger_out() {
      if (this.editFlag) {
        this.$confirm("是否取消数据保存?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "关闭",
          type: "warning"
        })
          .then(() => {
            this.editFlag = false;
            this.isEditFlag();
            this.$message({
              type: "success",
              message: "取消成功!"
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已关闭取消"
            });
          });
      } else {
        this.$message({
          type: "info",
          message: "暂未有数据进行了修改"
        });
      }
    },
    // 检查表格数据是否存在修改
    isEditFlag(callback) {
      const vm = this;

      if (vm.editFlag) {
        vm.$confirm($T("数据有修改,是否保存？"), $T("提示"), {
          distinguishCancelAndClose: true,
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        })
          .then(() => {
            this.CetButton_save_statusTrigger_out(); //TODO保存和读取有冲突
            if (typeof callback === "function") {
              callback();
            } else {
              vm.getTableData();
            }
            return;
          })
          .catch(action => {
            if (action === "cancel") {
              vm.editFlag = false;
              if (typeof callback === "function") {
                callback();
              } else {
                vm.getTableData();
              }
            }
          });
      } else {
        if (typeof callback === "function") {
          callback();
        } else {
          vm.getTableData();
        }
      }
    },
    updata_out() {
      this.getTableData();
    },
    // 分段操作
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.params.page.index = 0;
      this.params.page.limit = val;
      this.getTableData();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.params.page.index = (val - 1) * this.pageSize;
      this.getTableData();
    },
    // 是否导入判断
    CetButton_import_statusTrigger_out() {
      this.ImportDataDialog.openTrigger_in = new Date().getTime();
    },
    // 导入成功后重新请求
    refresh() {
      this.getTableData();
    },
    updateProject(val) {
      this.projectNode.objectId = this.projectId;
    },
    CetButton_recalculation_statusTrigger_out(val) {
      this.recalculation.visibleTrigger_in = this._.cloneDeep(val);
      this.recalculation.inputData_in = this._.cloneDeep(this.projectNode);
    },
    // 获取当前项目的重算状态并展示进度值
    getReCalcState() {
      customApi.getEnergyReCalcState(this.projectId).then(response => {
        if (response.code === 0) {
          if (response.data && response.data.state === 2) {
            let num = response.data.ratio.toFixed(1);
            this.CetButton_recalculation.disable_in = true;
            this.CetButton_recalculation.title = "重算中（" + num + "%）";
          } else {
            this.CetButton_recalculation.disable_in = false;
            this.CetButton_recalculation.title = "重算";
          }
        }
      });
    },
    // 更新项目的重算状态
    updataReCalcState_out() {
      setTimeout(() => {
        this.getReCalcState();
      }, 2500);
    },
    // 进行判断是否存在错序的问题
    disOrdering(num, callback) {
      let arr = [];
      let bool = true;
      if (num && num[0].logicalValues && num[0].logicalValues.length) {
        num[0].logicalValues.forEach(key => {
          num.forEach(item => {
            if (item["value_" + key.logicalid]) {
              arr.push(item["value_" + key.logicalid]);
            }
          });
          if (!this.isSomeArray(arr)) {
            bool = false;
            return;
          }
        });
      }
      if (!bool) {
        this.$confirm(
          "存在表码值时间越后的值比前面时间的值更大的问题，是否继续保存?",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "关闭",
            type: "warning"
          }
        )
          .then(() => {
            typeof callback === "function" && callback();
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消保存"
            });
          });
      } else {
        typeof callback === "function" && callback();
      }
    },
    // 判断两个数组是否相同
    isSomeArray(arr) {
      let b = this._.cloneDeep(arr);
      arr.sort((a, b) => a - b);
      return b.join("") === arr.join("") ? true : false;
    }
  },

  activated: function () {
    this.editFlag = false;
    this.showRight = false;
    this.showLeftName = "请选择表计设备";
    this.CetButton_add.visible_in = false;
    this.refreshProjectTree = Date.now();
    this.init();
    // 定时器
    this.getReCalcState();
    this.setInterval = setInterval(() => {
      this.getReCalcState();
    }, 60000);
  },
  destroyed: function () {
    // 关闭定时器
    clearInterval(this.setInterval);
  },
  deactivated: function () {
    // 关闭定时器
    clearInterval(this.setInterval);
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}

.page-body {
  padding: 0;
  height: 100%;
}

.upload {
  :deep .el-upload-list {
    display: none;
    font-size: 0;
  }
}

.year-range {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 300px;
  padding: 0px;
  @include border_radius(J);
  box-sizing: border-box;
  :deep .el-input__inner {
    text-align: center;
  }
  :deep input {
    border: none;
    height: 32px;
  }
  & > div:last-child {
    :deep .el-input__prefix {
      display: none;
    }
  }
}

.CetDateSelect {
  :deep .el-card.box-card.is-always-shadow {
    right: 0 !important;
  }
  :deep .el-select {
    width: 100px !important;
  }
}
.title-width {
  max-width: 50%;
}
</style>
