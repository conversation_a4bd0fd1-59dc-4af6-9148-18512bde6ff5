<template>
  <customDrawer v-bind="strategyRank">
    <div class="eem-cont-c1" style="height: 100%">
      <div v-if="defaultInterval === 0">
        <div class="title mbJ3">{{ $T("默认区间优化策略") }}</div>
        <div
          v-for="(
            item, index
          ) in optimizationStrategyData.optimizationListRanking"
          :key="index"
          class="mbJ1 rank-row"
        >
          <img
            :src="
              require(`../../../assets/rank-${index > 2 ? 3 : index + 1}.png`)
            "
            alt=""
            class="mrJ2"
          />
          <span>{{ item.compressorUnitName }}</span>
        </div>
      </div>
      <div v-if="defaultInterval === 1">
        <template v-for="(item, index) in rankList">
          <div :key="index" class="mbJ3" v-if="machineLength > index">
            <div>
              <div class="title mbJ3">{{ item.name }}</div>
              <template v-if="!_.isEmpty(optimizationStrategyData[item.key])">
                <div
                  v-for="(v, i) in optimizationStrategyData[item.key]"
                  :key="i"
                  class="mbJ1 rank-row"
                >
                  <img
                    :src="
                      require(`../../../assets/rank-${i > 2 ? 3 : i + 1}.png`)
                    "
                    alt=""
                    class="mrJ2"
                  />
                  <span>{{ v.compressorUnitName }}</span>
                </div>
              </template>
              <div v-else class="rank-row">--</div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </customDrawer>
</template>

<script>
import common from "eem-utils/common.js";
import customDrawer from "@/components/customElDrawer";
export default {
  name: "optimizationStrategyDetails",
  components: { customDrawer },
  props: {
    openTrigger_in: {
      type: Number
    },
    optimizationStrategyData: {
      type: Object
    },
    defaultInterval: {
      type: Number
    },
    machineLength: {
      type: Number
    }
  },
  data() {
    return {
      strategyRank: {
        title: "机组区间策略排名",
        size: "480px",
        openTrigger_in: +new Date(),
        closeTrigger_in: +new Date()
      },
      rankList: [
        {
          name: $T("单机优化策略"),
          key: "singleOptimizationRanking"
        },
        {
          name: $T("双机优化策略"),
          key: "twoOptimizationRanking"
        },
        {
          name: $T("三机优化策略"),
          key: "threeOptimizationRanking"
        }
      ]
    };
  },
  watch: {
    openTrigger_in() {
      this.strategyRank.openTrigger_in = +new Date();
    }
  },
  methods: {}
};
</script>
<style lang="scss" scoped>
.title {
  font-weight: bold;
}
.rank-row {
  height: 32px;
  line-height: 32px;
  display: flex;
  align-items: center;
}
</style>
