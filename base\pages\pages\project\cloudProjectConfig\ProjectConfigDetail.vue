<template>
  <!-- 1弹窗组件 -->
  <CetDialog
    ref="CetDialog"
    v-bind="CetDialog_1"
    v-on="CetDialog_1.event"
    class="CetDialog"
  >
    <div style="max-height: 800px; overflow-y: auto" ref="detailWrap">
      <div class="cont-title">{{ $T("基本信息") }}</div>
      <div style="overflow: hidden" class="bg1 brC1 mtJ1 rowBox">
        <el-row :gutter="$J3">
          <el-col
            :span="8"
            v-for="(item, index) in items"
            :key="index"
            class="mtJ3"
          >
            <div class="detail-label mbJ1">{{ item.label }}</div>
            <div class="value">{{ findDataByKey(dialogMsg1, item.key) }}</div>
          </el-col>
          <el-col :span="8" v-if="showDocument" class="mtJ3">
            <div class="detail-label mbJ1">{{ $T("关联文档") }}</div>
            <div class="value">
              <span class="dcm-btn-class-label" :title="documentName">
                {{ documentName || "--" }}
              </span>
              <el-button
                size="mini"
                style="position: absolute"
                :disabled="!documentBtnAble"
                type="primary"
                @click="importDocument()"
              >
                {{ $T("导出文档") }}
              </el-button>
            </div>
          </el-col>
          <el-col
            :span="24"
            v-if="dialogMsg1.modelLabel == 'project'"
            class="mtJ3"
          >
            <div class="detail-label mbJ1">{{ $T("项目简介") }}</div>
            <div class="value">
              {{ projectabstract || "--" }}
            </div>
          </el-col>
          <el-col
            :span="24"
            class="mtJ3"
            v-if="
              (filNodePermissions_out('pic') ||
                dialogMsg1.modelLabel == 'project') &&
              ![
                'meteorologicalmonitor',
                'airconditioner',
                'civicpipe'
              ].includes(dialogMsg1.modelLabel)
            "
          >
            <div class="detail-label mbJ1">{{ $T("主图") }}</div>
            <div class="value">
              <UploadImg class="img" :static_in="true" :imgUrl.sync="imgSrc" />
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="cont-title mtJ1" v-show="showHierarchy">
        {{ $T("层级信息") }}
      </div>
      <div
        style="overflow: hidden"
        v-show="showHierarchy"
        class="bg1 brC1 mtJ1 rowBox ptJ3"
      >
        <el-row :gutter="$J3">
          <el-col :span="8">
            <div class="detail-label mbJ1">{{ $T("层级对象") }}</div>
            <div class="value">
              {{ `${project || "--"} > ${building || "--"}` }}
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="cont-title mtJ1" v-show="showDevice">
        {{ $T("关联的设备") }}
      </div>
      <div style="overflow: hidden" v-show="showDevice">
        <div class="ptJ3 pbJ3 plJ4 prJ4 bg1 brC1 mtJ1" style="height: 300px">
          <CetTable
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
          >
            <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_code"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_location"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_brand"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_model"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_voltagelevel"></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_commissiondate"
            ></ElTableColumn>
          </CetTable>
        </div>
      </div>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
    </span>
    <CetInterface
      :data.sync="CetInterface_query.data"
      :dynamicInput.sync="CetInterface_query.dynamicInput"
      v-bind="CetInterface_query"
      v-on="CetInterface_query.event"
    ></CetInterface>
  </CetDialog>
</template>
<script>
import common from "eem-utils/common";
import ELECTRICAL_DEVICE from "@/store/electricaldevice.js";
import ELECTRICAL_DEVICE_NODE from "@/store/electricaldevicenode.js";
import UploadImg from "eem-components/uploadImg.vue";
import { httping } from "@omega/http";
export default {
  components: {
    UploadImg
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    dialogWidth() {
      if (
        this.filNodePermissions_out("pic") ||
        this.dialogMsg1.modelLabel == "project"
      ) {
        return "960px";
      } else {
        return "480px";
      }
    }
  },

  data(vm) {
    return {
      CetDialog_1: {
        title: $T("详情"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        // defaultSort: null, // { prop: "code"  order: "descending" },
        event: {},
        style: "text-align: center;height:100%;"
      },
      ElTableColumn_name: {
        prop: "name", // 支持path a[0].b
        label: $T("设备名称"), //列名
        headerAlign: "left",
        align: "left",
        minWidth: "120", //该宽度会自适应
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.name || val.name === 0) {
            return val.name;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_code: {
        prop: "code", // 支持path a[0].b
        label: $T("编号"), //列名
        headerAlign: "left",
        align: "left",
        minWidth: "120", //该宽度会自适应
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.code || val.code === 0) {
            return val.code;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_location: {
        prop: "location", // 支持path a[0].b
        label: $T("安装位置"), //列名
        headerAlign: "left",
        align: "left",
        minWidth: "180", //该宽度会自适应
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.location || val.location === 0) {
            return val.location;
          } else {
            return "--";
          }
        }
      },
      ElTableColumn_brand: {
        //type: "",      // selection 勾选 index 序号
        prop: "brand", // 支持path a[0].b
        label: $T("品牌"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "120", //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (val) {
          if (val.brand || val.brand === 0) {
            return val.brand;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_model: {
        //type: "",      // selection 勾选 index 序号
        prop: "model", // 支持path a[0].b
        label: $T("型号"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "120", //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (val) {
          if (val.model || val.model === 0) {
            return val.model;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_commissiondate: {
        //type: "",      // selection 勾选 index 序号
        prop: "commissiondate", // 支持path a[0].b
        label: $T("投运时间"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "150", //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (val) {
          if (val.commissiondate || val.commissiondate === 0) {
            return vm.$moment(val.commissiondate).format("YYYY-MM-DD");
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_voltagelevel: {
        //type: "",      // selection 勾选 index 序号
        prop: "voltagelevel$text", // 支持path a[0].b
        label: $T("电压等级"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "120", //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (val) {
          if (val.voltagelevel$text || val.voltagelevel$text === 0) {
            return val.voltagelevel$text;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      CetInterface_query: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "getEventClassification",
          modelLabel: "deviceclassification",
          dataIndex: [],
          modelList: [],
          filters: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {},
        page_in: null, // exp:{ index: 1, limit: 20 }
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          result_out: this.CetInterface_query_result_out
        }
      },
      dialogMsg1: {},
      hierarchy: "",
      project: "",
      building: "",
      items: [],
      showHierarchy: false,
      showDevice: false,
      imgSrc: null,
      showDocument: false,
      documentSrc: "",
      projectabstract: "",
      documentName: "",
      documentBtnAble: false,
      deviceclassificationArr: [],
      basicList: [
        {
          label: $T("设备名称"),
          key: "name",
          required: true
        },
        {
          label: $T("厂家"),
          key: "manufactor"
        },
        {
          label: $T("设备编号"),
          key: "code"
        },
        {
          label: $T("型号"),
          key: "model"
        },
        {
          label: $T("安装位置"),
          key: "location"
        },
        {
          label: $T("出厂日期"),
          key: "manufacturedate$text"
        },
        {
          label: $T("设备归类"),
          key: "deviceclassification$text"
        },
        {
          label: $T("投运时间"),
          key: "commissiondate$text"
        },
        {
          label: $T("检修周期(天)"),
          key: "maintenanceperiod"
        },
        {
          label: $T("上次检修日期"),
          key: "lastoverhauldate$text"
        },
        {
          label: $T("下次检修日期"),
          key: "nextoverhauldate$text"
        },
        {
          label: $T("资产归属"),
          key: "ownship"
        }
      ]
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      vm.show_dialog_out(this.inputData_in);
      vm.$nextTick(() => {
        $(this.$refs.detailWrap).scrollTop(0);
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {
      var vm = this;
      vm.CetDialog_1.queryId_in = val;
    },
    inputData_in(val) {}
  },

  methods: {
    findDataByKey(...arg) {
      return common.findDataByKey(...arg);
    },
    filNodePermissions_out(key) {
      const nodeModelLabel = this.dialogMsg1.modelLabel || "";
      const modelLabelList = ELECTRICAL_DEVICE_NODE[nodeModelLabel] || [];
      let isOk = false;
      modelLabelList.forEach(item => {
        if (item.propertyLabel == key) {
          isOk = true;
        }
      });
      return isOk;
    },
    show_dialog_out(val) {
      this.CetDialog_1.inputData_in = val;
      this.items = [];
      this.imgSrc = null;
      this.documentName = "";
      val.commissiondate$text = val.commissiondate
        ? this.$moment(val.commissiondate).format("YYYY-MM-DD")
        : null;
      val.cooperativedeadline$text = val.cooperativedeadline
        ? this.$moment(val.cooperativedeadline).format("YYYY-MM-DD")
        : null;
      val.lastoverhauldate$text = val.lastoverhauldate
        ? this.$moment(val.lastoverhauldate).format("YYYY-MM-DD")
        : null;
      val.nextoverhauldate$text = val.nextoverhauldate
        ? this.$moment(val.nextoverhauldate).format("YYYY-MM-DD")
        : null;
      val.warrantydate$text = val.warrantydate
        ? this.$moment(val.warrantydate).format("YYYY-MM-DD")
        : null;
      val.productiondate$text = val.productiondate
        ? this.$moment(val.productiondate).format("YYYY-MM-DD")
        : null;
      val.manufacturedate$text = val.manufacturedate
        ? this.$moment(val.manufacturedate).format("YYYY-MM-DD")
        : null;
      val.conversiontime$text = val.conversiontime
        ? this.$moment(val.conversiontime).format("YYYY-MM-DD")
        : null;
      val.lastpretestdate$text = val.lastpretestdate
        ? this.$moment(val.lastpretestdate).format("YYYY-MM-DD")
        : null;
      val.changedate$text = val.changedate
        ? this.$moment(val.changedate).format("YYYY-MM-DD")
        : null;
      val.scrapdate$text = val.scrapdate
        ? this.$moment(val.scrapdate).format("YYYY-MM-DD")
        : null;
      val.busbarsegi$text = val.busbarSegI;
      val.busbarsegii$text = val.busbarSegII;
      if (val.isscrap === true) {
        val.isscrap$text = $T("是");
      } else if (val.isscrap === false) {
        val.isscrap$text = $T("否");
      } else {
        val.isscrap$text = null;
      }

      if (!val.manufactor) {
        val.manufactor = val.vendor;
      }
      this.dialogMsg1 = val;
      this.projectabstract = val.projectabstract;
      var deviceIdArr = [
        "manuequipment",
        "linesegment",
        "pipeline",
        "photoeleconverter"
      ];
      ELECTRICAL_DEVICE.forEach(item => {
        deviceIdArr.push(item.value);
      });

      if (val.modelLabel === "project") {
        this.items = [
          {
            label: $T("名称"),
            key: "name",
            required: true
          },
          {
            label: $T("项目编号"),
            key: "code"
          },
          {
            label: $T("所属区域"),
            key: "hierarchy"
          },
          {
            label: $T("所属公司"),
            key: "enterprisename"
          },
          {
            label: $T("法人代表"),
            key: "cooperaterepresentative"
          },
          {
            label: $T("负责人"),
            key: "director"
          },
          {
            label: $T("负责人电话"),
            key: "directornumber"
          },
          {
            label: $T("负责人邮箱"),
            key: "directoremail"
          },
          {
            label: $T("联系人"),
            key: "contact"
          },
          {
            label: $T("联系人邮箱"),
            key: "contactemail"
          },
          {
            label: $T("联系地址"),
            key: "address"
          },
          {
            label: $T("年产值(万元)"),
            key: "annualoutput"
          },
          {
            label: $T("投运时间"),
            key: "commissiondate$text"
          },
          {
            label: $T("经度"),
            key: "longitude"
          },
          {
            label: $T("纬度"),
            key: "latitude"
          },
          {
            label: $T("面积(m²)"),
            key: "area"
          },
          {
            label: $T("人数(人)"),
            key: "population"
          }
          // {
          //   label: "合作截止时间",
          //   key: "cooperativedeadline$text"
          // }
        ];
      } else if (val.modelLabel === "sectionarea") {
        const nodeList = [];
        const list = ELECTRICAL_DEVICE_NODE[val.modelLabel] || [];
        const dataList = this._.cloneDeep(list);

        dataList.forEach(item => {
          if (item.unit) {
            item.text = `${item.text}（${item.unit}）`;
          }
          if (item.type === "pic" || item.type === "document") {
            //
          } else if (["date", "enum", "boolean"].includes(item.type)) {
            nodeList.push({
              label: item.text,
              key: `${item.propertyLabel}$text`
            });
          } else if (item.propertyLabel === "name" && item.type === "string") {
            nodeList.push({
              label: item.text,
              key: item.propertyLabel,
              required: true
            });
          } else {
            nodeList.push({
              label: item.text,
              key: item.propertyLabel
            });
          }
        });
        this.items = nodeList;
      } else if (val.modelLabel === "building") {
        this.items = [
          {
            label: $T("名称"),
            key: "name",
            required: true
          },
          {
            label: $T("编号"),
            key: "code"
          },
          {
            label: $T("地址"),
            key: "address"
          },
          {
            label: $T("经度"),
            key: "longitude"
          },
          {
            label: $T("纬度"),
            key: "latitude"
          },
          {
            label: $T("电压等级"),
            key: "voltagelevel$text"
          },
          {
            label: $T("面积(m²)"),
            key: "floorarea"
          },
          {
            label: $T("人数(人)"),
            key: "population"
          },
          {
            label: $T("合作截止日期"),
            key: "cooperativedeadline$text"
          },
          {
            label: $T("投运时间"),
            key: "commissiondate$text"
          }
        ];
      } else if (val.modelLabel === "floor") {
        this.items = [
          {
            label: $T("名称"),
            key: "name",
            required: true
          },
          {
            label: $T("编号"),
            key: "code"
          },
          {
            label: $T("地址"),
            key: "address"
          },
          {
            label: $T("面积(m²)"),
            key: "area"
          },
          {
            label: $T("人数(人)"),
            key: "population"
          }
        ];
      } else if (val.modelLabel === "room") {
        if (val.roomtype === 1) {
          this.items = [
            {
              label: $T("名称"),
              key: "name",
              required: true
            },
            {
              label: $T("编号"),
              key: "code"
            },

            {
              label: $T("地址"),
              key: "address"
            },
            {
              label: $T("经度"),
              key: "longitude"
            },
            {
              label: $T("纬度"),
              key: "latitude"
            },
            {
              label: $T("电压等级"),
              key: "voltagelevel$text"
            },
            {
              label: $T("面积(m²)"),
              key: "area"
            },
            {
              label: $T("人数(人)"),
              key: "population"
            },
            {
              label: $T("合作截止日期"),
              key: "cooperativedeadline$text"
            },
            {
              label: $T("配电室类型"),
              key: "powerroomtype$text"
            },
            {
              label: $T("投运时间"),
              key: "commissiondate$text"
            },
            {
              label: $T("房间类型"),
              key: "roomtype$text"
            }
          ];
        } else {
          this.items = [
            {
              label: $T("名称"),
              key: "name",
              required: true
            },
            {
              label: $T("编号"),
              key: "code"
            },
            {
              label: $T("地址"),
              key: "address"
            },
            {
              label: $T("经度"),
              key: "longitude"
            },
            {
              label: $T("纬度"),
              key: "latitude"
            },
            {
              label: $T("电压等级"),
              key: "voltagelevel$text"
            },
            {
              label: $T("面积(m²)"),
              key: "area"
            },
            {
              label: $T("人数(人)"),
              key: "population"
            },
            {
              label: $T("合作截止日期"),
              key: "cooperativedeadline$text"
            },
            {
              label: $T("投运时间"),
              key: "commissiondate$text"
            },
            {
              label: $T("房间类型"),
              key: "roomtype$text"
            }
          ];
        }
      } else if (val.modelLabel === "manuequipment") {
        this.items = [
          {
            label: $T("名称"),
            key: "name",
            required: true
          },
          {
            label: $T("编号"),
            key: "code"
          },
          {
            label: $T("安装位置"),
            key: "location"
          },
          {
            label: $T("经度"),
            key: "longitude"
          },
          {
            label: $T("纬度"),
            key: "latitude"
          },
          {
            label: $T("厂家"),
            key: "manufactor"
          },
          {
            label: $T("品牌"),
            key: "brand"
          },
          {
            label: $T("型号"),
            key: "model"
          },
          {
            label: $T("出厂日期"),
            key: "manufacturedate$text"
          },
          {
            label: $T("投运时间"),
            key: "commissiondate$text"
          },
          {
            label: $T("电压等级"),
            key: "voltagelevel$text"
          }
        ];
      } else if (
        ["meteorologicalmonitor", "airconditioner"].includes(val.modelLabel)
      ) {
        this.items = [
          {
            label: $T("名称"),
            key: "name",
            required: true
          }
        ];
      } else if (val.modelLabel === "pipeline") {
        this.items = [
          ...this.basicList,
          {
            label: $T("能源类型"),
            key: "energytype$text",
            required: true
          },
          {
            label: $T("管道功能类型"),
            key: "pipefunctiontype$text"
          },
          {
            label: $T("管道位置类型"),
            key: "pipepositiontype$text"
          }
        ];
      } else if (val.modelLabel === "civicpipe") {
        this.items = [
          {
            label: $T("名称"),
            key: "name",
            required: true
          }
        ];
      } else if (val.modelLabel === "aircompressor") {
        this.items = [
          ...this.basicList,
          {
            label: $T("机组尺寸"),
            key: "devicesize"
          },
          {
            label: $T("机组重量（吨）"),
            key: "deviceweight"
          },
          {
            label: $T("空压机组属性"),
            key: "aircompressorattr$text"
          },
          {
            label: $T("生产厂家"),
            key: "manufactor"
          },
          {
            label: $T("电压等级"),
            key: "voltagelevel$text"
          }
        ];
      } else if (val.modelLabel === "colddryingmachine") {
        this.items = [
          ...this.basicList,
          {
            label: $T("机组尺寸"),
            key: "devicesize"
          },
          {
            label: $T("机组重量（吨）"),
            key: "deviceweight"
          },
          {
            label: $T("冷干机属性"),
            key: "colddrymachineattr$text"
          }
        ];
      } else if (val.modelLabel === "dryingmachine") {
        this.items = [
          ...this.basicList,
          {
            label: $T("机组尺寸"),
            key: "devicesize"
          },
          {
            label: $T("机组重量（吨）"),
            key: "deviceweight"
          },
          {
            label: $T("干燥剂"),
            key: "dryingagent"
          },
          {
            label: $T("干燥机属性"),
            key: "dryingmachineattr$text"
          }
        ];
      } else if (val.modelLabel === "boiler") {
        this.items = [
          ...this.basicList,
          {
            label: $T("锅炉类型"),
            key: "boilertype$text"
          },
          {
            label: $T("机组尺寸"),
            key: "devicesize"
          },
          {
            label: $T("机组重量（吨）"),
            key: "deviceweight"
          },
          {
            label: $T("燃料类型"),
            key: "fueltype$text"
          }
        ];
      } else if (ELECTRICAL_DEVICE_NODE.roomtype1.includes(val.modelLabel)) {
        //处理配电室下面设备详情信息
        const nodeList = [];
        const list = ELECTRICAL_DEVICE_NODE[val.modelLabel] || [];
        const common = ELECTRICAL_DEVICE_NODE.common || [];
        let dataList = this._.cloneDeep(list);
        const commonList = this._.cloneDeep(common);
        const otherList = [];
        //对配电设备添加公共字段显示
        commonList.forEach(item => {
          let isHas = false;
          dataList.forEach(item2 => {
            if (item.propertyLabel === item2.propertyLabel) {
              isHas = true;
            }
          });
          if (!isHas) {
            otherList.push(item);
          }
        });

        dataList = dataList.concat(otherList);
        dataList.forEach(item => {
          if (item.unit) {
            item.text = `${item.text}（${item.unit}）`;
          }
          if (item.type === "pic" || item.type === "document") {
            //
          } else if (["date", "enum", "boolean"].includes(item.type)) {
            nodeList.push({
              label: item.text,
              key: `${item.propertyLabel}$text`
            });
          } else if (item.propertyLabel === "name" && item.type === "string") {
            nodeList.push({
              label: item.text,
              key: item.propertyLabel,
              required: true
            });
          } else {
            nodeList.push({
              label: item.text,
              key: item.propertyLabel
            });
          }
        });
        this.items = nodeList;
      } else if (ELECTRICAL_DEVICE_NODE.roomtype2.includes(val.modelLabel)) {
        //泵设备、一段线
        const nodeList = [];
        const list = ELECTRICAL_DEVICE_NODE[val.modelLabel] || [];
        const dataList = this._.cloneDeep(list);

        dataList.forEach(item => {
          if (item.unit) {
            item.text = `${item.text}（${item.unit}）`;
          }
          if (item.type === "pic" || item.type === "document") {
            //
          } else if (["date", "enum", "boolean"].includes(item.type)) {
            nodeList.push({
              label: item.text,
              key: `${item.propertyLabel}$text`
            });
          } else if (item.propertyLabel === "name" && item.type === "string") {
            nodeList.push({
              label: item.text,
              key: item.propertyLabel,
              required: true
            });
          } else {
            nodeList.push({
              label: item.text,
              key: item.propertyLabel
            });
          }
        });
        this.items = nodeList;
      } else if (ELECTRICAL_DEVICE_NODE.deviceList.includes(val.modelLabel)) {
        //其他设备模型
        //处理配电室下面设备详情信息
        const nodeList = [];
        const list = ELECTRICAL_DEVICE_NODE[val.modelLabel] || [];
        let dataList = this._.cloneDeep(list);
        dataList.forEach(item => {
          if (item.unit) {
            item.text = `${item.text}（${item.unit}）`;
          }
          if (item.type === "pic" || item.type === "document") {
            //
          } else if (["date", "enum", "boolean"].includes(item.type)) {
            nodeList.push({
              label: item.text,
              key: `${item.propertyLabel}$text`
            });
          } else if (item.propertyLabel === "name" && item.type === "string") {
            nodeList.push({
              label: item.text,
              key: item.propertyLabel,
              required: true
            });
          } else {
            nodeList.push({
              label: item.text,
              key: item.propertyLabel
            });
          }
        });
        this.items = nodeList;
      } else {
        this.items = [
          {
            label: $T("编号"),
            key: "code"
          },
          {
            label: $T("名称"),
            key: "name",
            required: true
          },
          {
            label: $T("安装位置"),
            key: "location"
          },
          {
            label: $T("经度"),
            key: "longitude"
          },
          {
            label: $T("纬度"),
            key: "latitude"
          },
          {
            label: $T("厂家"),
            key: "manufactor"
          },
          {
            label: $T("品牌"),
            key: "brand"
          },
          {
            label: $T("型号"),
            key: "model"
          },
          {
            label: $T("出厂日期"),
            key: "manufacturedate$text"
          },
          {
            label: $T("投运时间"),
            key: "commissiondate$text"
          },
          {
            label: $T("电压等级"),
            key: "voltagelevel$text"
          }
        ];
      }
      this.items.push({
        label: $T("模型ID"),
        key: "id"
      });
      this.items.push({
        label: $T("模型名称"),
        key: "modelLabel"
      });
      if (val.modelLabel === "building") {
        this.hierarchy = val.hierarchy;
        this.project = val.project || val.sectionarea;
        this.building = val.name;
        this.showHierarchy = true;
        this.showDevice = true;
        this.getDeviceList();
      } else {
        this.showHierarchy = false;
        this.showDevice = false;
      }
      if (deviceIdArr.includes(val.modelLabel)) {
        this.showDocument = true;
        this.documentSrc = val.document;
        var documentPath = val.document || "";
        if (documentPath) {
          this.documentBtnAble = true;
        } else {
          this.documentBtnAble = false;
        }
        try {
          this.documentName =
            documentPath.split("/")[3].split("_")[0] +
            "." +
            documentPath.split("/")[3].split("_")[1].split(".")[1];
        } catch (err) {
          this.documentName = "";
        }
      } else {
        this.showDocument = false;
        this.documentBtnAble = false;
      }

      this.getImgUrl(val);
      // 解析设备归类
      this.$nextTick(() => {
        if (val.deviceclassification) {
          this.CetInterface_query.queryTrigger_in = new Date().getTime();
        }
      });
    },
    CetInterface_query_result_out(val) {
      if (val && val.length > 0) {
        this.dialogMsg1.deviceclassification$text = val.find(
          item => item.id === this.dialogMsg1.deviceclassification
        ).name;
      }
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    getImgUrl: function (val) {
      var uploadPath = val.pic || val.picture || "";
      this.imgSrc = this._.cloneDeep(uploadPath);
    },
    importDocument: function () {
      var uploadPath = this.documentSrc;
      if (!uploadPath) {
        return;
      }
      var url = "/eem-service/v1/common/downloadFile?path=" + uploadPath;
      common.downExcelGET(url, {}, this.token, this.projectId);
    },
    //获取关联用能设备列表
    getDeviceList() {
      var _this = this;
      var auth = _this.token; //身份验证
      var params = {
        subLayerConditions: [
          {
            modelLabel: "manuequipment"
          }
        ],
        rootLabel: "building",
        rootID: this.inputData_in.id,
        treeReturnEnable: true
      };
      httping({
        url: "/eem-service/v1/node/nodeTree",
        data: params,
        method: "POST",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          _this.CetTable_1.data = _this._.get(
            res,
            ["data", "0", "children"],
            []
          );
        }
      });
    }
  },
  created: function () {},
  atcivated: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
  }
  .detail-label {
    @include font_color(T3);
    line-height: 1;
  }
  .value {
    line-height: 1.5;
  }
  .img {
    height: 80px;
    width: 80px;
  }
  .rowBox {
    @include padding_left(J4);
    @include padding_right(J4);
    @include padding_bottom(J3);
  }
}
.cont-title {
  font-weight: bold;
  @include margin_left(J3);
}
.cont-cont-label {
  height: 30px;
  line-height: 30px;
}
.cont-cont-label-span {
  float: left;
  width: 160px;
  margin-right: 10px;
  text-align: right;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
// 冷水主机详情
.coldwater-label-span {
  width: 200px !important;
}
.coldwater-label-cont {
  width: calc(100% - 210px) !important;
}
.cont-cont-label-cont {
  float: left;
  width: calc(100% - 170px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.projectabstract-label {
  width: 85px;
  text-align: right;
}
.projectabstract-cont {
  flex: 1;
  height: 130px;
  overflow-y: auto;
  // border: 1px solid;
  border-radius: 4px;
  // @include border_color(B1);
  @include background_color(BG1);
  @include padding(J1);
}
.dcm-btn-class-label {
  padding-right: 0px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 150px;
  display: inline-block;
}
</style>
