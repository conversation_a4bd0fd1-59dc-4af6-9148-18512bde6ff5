<template>
  <div class="page eem-common">
    <el-container style="height: 100%">
      <el-aside width="315px" class="eem-aside flex-column">
        <el-tooltip :content="getDimension" placement="top-end" effect="light">
          <customElSelect
            v-model="ElSelect_1.value"
            v-bind="ElSelect_1"
            v-on="ElSelect_1.event"
            class="mbJ1"
            :prefix_in="$T('选择维度')"
          >
            <ElOption
              v-for="item in ElOption_1.options_in"
              :key="item[ElOption_1.key]"
              :label="item[ElOption_1.label]"
              :value="item[ElOption_1.value]"
              :disabled="item[ElOption_1.disabled]"
            ></ElOption>
          </customElSelect>
        </el-tooltip>
        <ProjectTree
          v-show="ElSelect_1.value !== 3"
          class="flex-auto"
          @currentNode_out="CetTree_1_currentNode_out"
          @clearChart_out="CetButton_empty_statusTrigger_out"
          :dimId_in="ElSelect_1.value"
          :refreshTrigger="refreshProjectTree"
          :dimIdChangeTrigger="dimIdChange"
          :energyOptions_in="energyOptions"
        />
        <TopologyTree
          v-show="ElSelect_1.value === 3"
          class="flex-auto"
          @currentNode_out="CetTree_1_currentNode_out"
          @clearChart_out="CetButton_empty_statusTrigger_out"
          :dimId_in="ElSelect_1.value"
          :refreshTrigger="refreshProjectTree"
          :dimIdChangeTrigger="dimIdChange"
          :energyOptions_in="energyOptions"
        />
      </el-aside>
      <el-container class="mlJ3 fullheight eem-min-width">
        <el-header height="auto" class="mbJ3 flex-row eem-container">
          <div class="flex-auto text-ellipsis mrJ3">
            <el-tooltip
              effect="light"
              :content="
                (currentNode && (currentNode.name || currentNode.text)) || '--'
              "
              placement="top-start"
            >
              <span class="common-title-H2 lh32" style="display: inline">
                {{
                  `${
                    (currentNode && (currentNode.name || currentNode.text)) ||
                    "--"
                  }`
                }}
              </span>
            </el-tooltip>
          </div>
          <div class="fr" style="display: flex; align-items: center">
            <div class="mrJ2" v-if="hidenSecondTrendBtn">
              <!-- 秒级曲线: -->
              <el-select
                v-model="showSecond"
                style="width: 140px"
                @change="showSecond_out"
              >
                <el-option
                  v-for="item in secondOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
            <time-tool
              v-show="showSecond"
              v-bind="timeTool"
              :typeID.sync="timeTool.typeID"
              :val.sync="timeTool.val"
              @change="changeQueryTime"
            ></time-tool>
            <CustomDatePicker
              v-show="!showSecond"
              ref="CustomDatePicker"
              class="fl"
              @change="CustomDatePicker_1_change"
              :val="CustomDatePicker_1.queryTime"
              :disable_in="CustomDatePicker_1.disable_in"
              :dataConfig="CustomDatePicker_1.dataConfig"
            ></CustomDatePicker>
            <CetButton
              class="mlJ1"
              v-bind="CetButton_empty"
              v-on="CetButton_empty.event"
            ></CetButton>
            <CetButton
              class="mlJ1"
              v-bind="CetButton_export"
              v-on="CetButton_export.event"
            ></CetButton>
          </div>
        </el-header>
        <el-container
          v-show="!showTrend"
          class="padding0 fullheight text-center"
        >
          <span class="info">{{ $T("请选择设备") }}。</span>
        </el-container>
        <el-container
          v-show="showTrend"
          class="fullheight"
          style="margin: 0 0 0 -15px; padding: 0 0 0 15px; overflow: auto"
        >
          <el-container
            class="fullheight eem-min-width"
            style="overflow: hidden"
          >
            <el-aside
              style="width: 315px"
              id="pecDeviceID"
              class="sub-aside mrJ3 eem-aside"
              :class="collapseAside ? 'show-aside' : 'hide-aside'"
            >
              <div class="fullheight" style="overflow: auto">
                <el-collapse
                  v-model="activeDevices"
                  accordion
                  @change="changeDevices"
                >
                  <el-collapse-item
                    class="aside-collapse"
                    v-for="item in pecDeviceList[tree_id]"
                    :name="item.measuredby"
                    :key="item.modelLabel + '_' + item.id"
                  >
                    <template slot="title">
                      <el-tooltip :content="item.deviceName" effect="light">
                        <div class="text-ellipsis">{{ item.deviceName }}</div>
                      </el-tooltip>
                    </template>
                    <template v-if="activeDevices === item.measuredby">
                      <StationList
                        :deviceID="item.measuredby"
                        :checkedStations.sync="checkedStations[item.measuredby]"
                        @changeStation="
                          val =>
                            changeStation(val, item.deviceName, item.measuredby)
                        "
                        :stations="stations[item.measuredby]"
                        :minHeight="getMinHeight(pecDeviceList[tree_id])"
                      ></StationList>
                    </template>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </el-aside>
            <div
              class="page-main flex-auto"
              :style="{
                marginLeft: collapseAside ? '0' : '-315px',
                width: collapseAside ? 'calc(100% - 315px)' : '100%'
              }"
            >
              <div
                class="collapse-aside"
                @click="collapseAside = !collapseAside"
              >
                <i
                  :class="
                    collapseAside ? 'el-icon-arrow-left' : 'el-icon-arrow-right'
                  "
                ></i>
              </div>
              <div
                class="fullheight brC1 bg1 ptJ3 pbJ3"
                style="box-sizing: border-box"
              >
                <EemTrend
                  ref="trend"
                  :queryMode="CetTable_realTime.queryMode"
                  :dataConfig="CetTable_realTime.dataConfig"
                  :queryTime_in="CetTable_realTime.queryTime_in"
                  :params_in="CetTable_realTime.params_in"
                  :interval_in="CetTable_realTime.interval_in"
                  :queryTrigger_in="CetTable_realTime.queryTrigger_in"
                  :clearTrigger_in="CetTable_realTime.clearTrigger_in"
                  :showSecond="showSecond"
                  :setMergeOptions="setMergeOptions"
                  v-bind="CetTable_realTime.config"
                ></EemTrend>
              </div>
            </div>
          </el-container>
        </el-container>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import StationList from "./StationList";
import common from "eem-utils/common";
import { PEC_DATA_TYPE } from "@/store/constQuantity";
import ProjectTree from "./ProjectTree.vue";
import TopologyTree from "./TopologyTree.vue";
import customApi from "@/api/custom";
import TimeTool from "./TimeTool.vue";
import EemTrend from "./eemTrend";
import CustomDatePicker from "./CustomDatePicker.vue";
import { httping } from "@omega/http";
export default {
  name: "TrendCurve",
  components: {
    ProjectTree,
    TopologyTree,
    StationList,
    TimeTool,
    EemTrend,
    CustomDatePicker
  },
  props: {},
  computed: {
    getDimension() {
      let id = this.ElSelect_1.value;
      let resObj = this.ElOption_1.options_in.find(item => item.id === id);

      return resObj?.name || "--";
    },
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    asideClass() {
      return "hide-aside";
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    systemCfg() {
      return this.$store.state.systemCfg;
    },
    hidenSecondTrendBtn() {
      return !this.$store.state.systemCfg.hidenSecondTrendBtn;
    }
  },

  data() {
    return {
      energyOptions: [],
      ElSelect_1: {
        value: 1,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [
          {
            id: 1,
            name: $T("管理层级")
          },
          {
            id: 2,
            name: $T("管网层级")
          },
          // {
          //   id: 3,
          //   name: $T("拓扑结构")
          // },
          {
            id: 4,
            name: $T("厂站通道设备")
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      refreshProjectTree: Date.now(),
      dimIdChange: Date.now(),
      //导出按钮
      CetButton_export: {
        visible_in: true,
        disable_in: true,
        title: $T("导出"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      // 清空按钮
      CetButton_empty: {
        visible_in: true,
        disable_in: true,
        title: $T("清空"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_empty_statusTrigger_out
        }
      },
      CetTable_realTime: {
        queryMode: "diff",
        dataConfig: {
          queryUrl: "/device-data-service/api/v1/batch/datalog/span/group",
          type: "POST"
        },
        queryTrigger_in: new Date().getTime(),
        clearTrigger_in: new Date().getTime(),
        queryTime_in: {
          timeType: 1,
          time: common.initDateRange().map(item => new Date(item))
        },
        title_in: $T("趋势曲线"),
        params_in: [],
        config: {
          showTableButton: true,
          showLegend: true,
          showExtremButton: true,
          showAverageButton: true,
          showPointButton: true,
          color: []
        }
      },
      pecDeviceList: {
        project_0: []
      },
      activeDevices: "",
      checkedStations: {},
      // oldCheckedStations: {},
      stations: {},
      collapseAside: false,
      queryTime: common.initDateRange(),
      currentNode: null,
      showTrend: false,
      tree_id: "project_0",
      showSecond: false,
      secondOptions: [
        {
          value: false,
          label: $T("普通定时记录")
        },
        {
          value: true,
          label: $T("秒级定时记录")
        }
      ],
      setMergeOptions: false,
      customed: false, // 是否是自定义的曲线查询时长
      timeOption: {},
      timeTool: {
        val: new Date().getTime(),
        typeID: 12,
        showOption: true,
        disable_in: true,
        isNextDisabled: true,
        timeType_in: [
          // {
          //   type: "date",
          //   text: "近5分钟",
          //   typeID: 4,
          //   number: 5,
          //   interval: 1, // 曲线打点间隔
          //   refreshTime: 3000, // 自动刷新间隔
          //   unit: "m",
          //   custom: true // 自定义的曲线查询时长
          // },
          {
            type: "date",
            text: $T("近10分钟"),
            typeID: 5,
            number: 10,
            interval: 0,
            refreshTime: 3000,
            unit: "m",
            custom: true
          },
          // {
          //   type: "date",
          //   text: "近15分钟",
          //   refreshTime: 3000,
          //   typeID: 18,
          //   number: 15,
          //   interval: 3,
          //   unit: "m",
          //   custom: true
          // },
          {
            type: "date",
            text: $T("近30分钟"),
            typeID: 6,
            number: 30,
            interval: 0,
            refreshTime: 5000,
            unit: "m",
            custom: true
          },
          {
            type: "date",
            text: $T("近1小时"),
            typeID: 7,
            number: 1,
            interval: 0,
            refreshTime: 10000,
            unit: "h",
            custom: true
          },
          // {
          //   type: "date",
          //   text: "近2小时",
          //   typeID: 8,
          //   number: 2,
          //   interval: 0,
          //   refreshTime: 20000,
          //   unit: "h",
          //   custom: true
          // },
          // {
          //   type: "date",
          //   text: "近4小时",
          //   typeID: 19,
          //   number: 4,
          //   interval: 0,
          //   refreshTime: 30000,
          //   unit: "h",
          //   custom: true
          // },
          // {
          //   type: "date",
          //   text: "近12小时",
          //   typeID: 11,
          //   number: 12,
          //   interval: 60,
          //   refreshTime: 60000,
          //   unit: "h",
          //   custom: true
          // },
          // {
          //   type: "date",
          //   text: "近24小时",
          //   typeID: 20,
          //   number: 24,
          //   interval: 60,
          //   refreshTime: 60000,
          //   unit: "h",
          //   custom: true
          // },
          {
            type: "date",
            text: $T("时"),
            typeID: 21,
            number: 1,
            interval: 0,
            unit: "h",
            showTimeSelect: true
          },
          {
            type: "date",
            text: $T("日"),
            typeID: 12,
            number: 1,
            unit: "d"
          }
          // {
          //   type: "month",
          //   text: "月",
          //   typeID: 14,
          //   number: 1,
          //   unit: "M"
          // },
        ]
      },
      timter: null, // 自定义曲线时长查询的定时器
      CustomDatePicker_1: {
        disable_in: false,
        queryTime: {
          startTime: null,
          endTime: null,
          cycle: 12 //17年，14月，12日，20自定义
        },
        dataConfig: {
          time: null,
          cycle: 1,
          showPicker: true,
          showRange: true,
          type: [
            {
              id: 1,
              text: $T("日"),
              type: "date"
            },
            {
              id: 2,
              text: $T("周"),
              type: "date"
            },
            {
              id: 3,
              text: $T("月"),
              type: "month"
            },
            {
              id: 4,
              text: $T("季度"),
              type: "month"
            },
            {
              id: 11,
              text: $T("自定义"),
              type: "date"
            }
          ]
        }
      }
    };
  },
  watch: {
    queryTime: {
      deep: true,
      handler: function (val, oldVal) {
        if (this._.isEmpty(this.CetTable_realTime.params_in)) return;
        this.CetTable_realTime.queryTime_in.time = val.map(
          item => new Date(item)
        );
      }
    },
    customed: {
      handler(val) {
        this.timeTool.disable_in = val;
      }
    }
  },

  methods: {
    getMinHeight(list) {
      let len = list?.length;
      let maxHeight = document.getElementById("pecDeviceID").clientHeight;
      let h = 144;
      if (len === 1) {
        h += 36 * 1;
      } else if (len === 2) {
        h += 36 * 2;
      } else {
        h += 36 * 3;
      }
      let minHeight = maxHeight - h;
      return minHeight < 400 ? "400px" : `${minHeight}px`;
    },
    ElSelect_1_change_out() {
      this.dimIdChange = Date.now();
    },
    getAllCheckLength(val) {
      let len = 0;
      this._.keys(val).forEach(key => (len += val[key].length));
      return len;
    },
    setParamsIn(val) {
      const _this = this;
      const points = [];
      this._.keys(val).forEach(key => {
        val[key].forEach(item => {
          let device = {};
          _this._.keys(_this.pecDeviceList).forEach(key1 => {
            const findDevice = _this._.find(_this.pecDeviceList[key1], {
              measuredby: item.deviceId
            });
            if (findDevice) {
              device = findDevice;
            }
          });
          points.push({
            dataId: item.dataId,
            dataTypeId: item.dataTypeId,
            deviceId: item.deviceId,
            logicalId: item.logicalDeviceIndex,
            dataName: item.paraName,
            dataTypeName: PEC_DATA_TYPE[item.dataTypeId],
            deviceName: device.deviceName,
            unit: ""
          });
        });
      });
      this.CetTable_realTime.params_in = points;
    },
    CetTree_1_currentNode_out(val) {
      if (this._.isEmpty(val)) {
        this.collapseAside = false;
        this.currentNode = null;
        this.showTrend = false;
      } else {
        if (this.ElSelect_1.value === 4) {
          if ([269615104, 269619456].includes(val.nodeType)) {
            this.collapseAside = false;
            this.currentNode = null;
            this.showTrend = false;
          } else {
            this.tree_id = val.id;
            this.currentNode = val;
            this.pecCoreTreeClick(val);
            this.showTrend = true;
          }
        } else {
          this.tree_id = val.tree_id;
          this.currentNode = val;
          this.getPecDevice(val);
          // this.collapseAside = true;
          this.showTrend = true;
        }
      }
    },
    pecCoreTreeClick(val) {
      const data = [
        {
          deviceName: val.text,
          measuredby: val.nodeId
        }
      ];
      this.pecDeviceList[this.tree_id] = [];
      this.initStations(data);
      this.activeDevices = data[0].measuredby;
      this.getPoints(data[0].measuredby);
      this.pecDeviceList[this.tree_id] = data;
      this.collapseAside = true;
    },
    CetTree_1_parentList_out(val) {},
    CetTree_1_checkedNodes_out(val) {},
    CetTree_1_halfCheckNodes_out(val) {},
    CetTree_1_allCheckNodes_out(val) {},
    //导出
    CetButton_export_statusTrigger_out(val) {
      const dataIdNameList = [];
      const meterConfigs = [];
      this._.each(this.CetTable_realTime.params_in, item => {
        dataIdNameList.push({
          dataId: item.dataId,
          dataName: item.dataName
        });
        meterConfigs.push({
          dataId: item.dataId,
          dataTypeId: item.dataTypeId,
          deviceId: item.deviceId,
          logicalId: item.logicalId,
          name: `${item.deviceName}-${item.dataName}-${item.dataTypeName}`
        });
      });
      let params = {
        // dataIdNameList,
        //需要组件支持
        // pictures: [this.$refs.trend.getDataURL()],
        // pictures: [this.$refs.trend.$refs.chart.getDataURL()],
        // trendSearchListVo: {
        endTime: this.$moment(this.queryTime[1]).format("YYYY-MM-DD HH:mm:ss"), // this.CetTable_realTime.queryTime_in.time[1],
        interval: 0,
        meterConfigs,
        startTime: this.$moment(this.queryTime[0]).format("YYYY-MM-DD HH:mm:ss") //this.CetTable_realTime.queryTime_in.time[0]
        // }
      };
      if (!this.$refs.trend.showTableSelect) {
        params.pictures = [this.$refs.trend.$refs.chart.getDataURL()];
      }
      let url = "";
      if (this._.get(this.$store.state, "systemCfg.trendcurveBuffer")) {
        url =
          "/eem-service/v2/peccore/datalog/export?cacheInterval=0&cacheMonthNumber=3&dbInterval=0";
      } else {
        url =
          "/eem-service/v2/peccore/datalog/export?cacheInterval=0&cacheMonthNumber=0&dbInterval=0";
      }
      // 秒级曲线修改导出接口
      if (this.showSecond) {
        url = "/eem-service/v2/peccore/second/datalog/export";
        params = {
          endTime: this.$moment(this.queryTime[1]).valueOf(),
          interval: 0,
          meterConfigs: meterConfigs,
          startTime: this.$moment(this.queryTime[0]).valueOf()
        };
      }
      common.downExcel(url, params, this.token);
    },
    CetButton_empty_statusTrigger_out() {
      this.checkedStations = {};
      this.CetTable_realTime.params_in = [];
      this.CetTable_realTime.clearTrigger_in = new Date().getTime();
      this.CetButton_export.disable_in = true;
      this.CetButton_empty.disable_in = true;
      this.timeTool.disable_in = true;
      this.timeTool.selectDisable = true;
      this.CustomDatePicker_1.disable_in = true;
    },
    changeStation(checkArr, devname, measuredby) {
      // console.log("changeStation", checkArr, devname, measuredby);
      const len = this.getAllCheckLength(this.checkedStations);
      if (len === 0) {
        this.timeTool.disable_in = true;
        this.timeTool.selectDisable = true;
        this.CustomDatePicker_1.disable_in = true;
        this.CetButton_export.disable_in = true;
        this.CetButton_empty.disable_in = true;
      } else {
        this.timeTool.disable_in = false;
        this.timeTool.selectDisable = false;
        this.CustomDatePicker_1.disable_in = false;
        this.CetButton_export.disable_in = false;
        this.CetButton_empty.disable_in = false;
      }
      if (len > 6) {
        this.$message($T("最多支持6个测点查询"));
        const arr = this.checkedStations[measuredby];
        this.checkedStations[measuredby] = arr.slice(0, arr.length - 1);
      } else {
        this.setParamsIn(this.checkedStations);
      }
    },
    getPecDevice(node) {
      const params = [
        {
          id: node.id,
          modelLabel: node.modelLabel,
          name: node.name
        }
      ];
      httping({
        url: `/eem-service/v1/connect/deviceInfo`,
        method: "POST",
        data: params
      }).then(res => {
        const data = this._.get(res, "data", []);
        this.pecDeviceList[this.tree_id] = [];
        if (this._.isEmpty(data))
          return (this.pecDeviceList[this.tree_id] = []);
        this.initStations(data);
        this.activeDevices = data[0].measuredby;
        this.getPoints(data[0].measuredby);
        this.pecDeviceList[this.tree_id] = data;
        this.pecDeviceList = this._.cloneDeep(this.pecDeviceList);
        this.collapseAside = true;
      });
    },
    initStations(devs) {
      if (!this.stations) {
        this.stations = {};
      }
      if (!this.checkedStations) {
        this.checkedStations = {};
      }

      // this.oldCheckedStations = {};

      devs.forEach(item => {
        if (!this.stations[item.measuredby]) {
          this.$set(this.stations, item.measuredby, []);
        }
        if (!this.checkedStations[item.measuredby]) {
          this.$set(this.checkedStations, item.measuredby, []);
        }
        // this.$set(this.oldCheckedStations, item.measuredby, []);
      });
    },
    getPoints(id) {
      const _this = this;
      httping({
        url: `/device-data-service/api/comm/v1/device/datalog/points?paraTypeId=0`,
        method: "POST",
        data: [id]
      }).then(res => {
        let resData = _this._.get(res, `data[${id}]`, []) || [];
        if (_this.showSecond) {
          //1：普通定时记录；5：高速定时记录；6：秒级定时记录
          resData = resData.filter(item => {
            return item.paraTypeId === 6;
          });
        } else {
          resData = resData.filter(item => {
            return item.paraTypeId !== 6;
          });
        }
        _this.stations[id] = resData;
      });
    },
    changeDevices(val) {
      if (!val) return;
      this.getPoints(val);
    },
    getTreeData() {
      this.refreshProjectTree = Date.now();
    },
    showSecond_out(val) {
      this.showSecond = val;
      this.timeTool.typeID = this._.cloneDeep(12);
      this.timeTool.showOption = this._.cloneDeep(val);
      this.CetButton_empty_statusTrigger_out();
      this.getPoints(this.activeDevices);
    },
    // 自定义查询时长定时器
    setTimer() {
      if (this.customed) {
        this.timter = setInterval(() => {
          this.setMergeOptions = true;
          this.queryTime = [
            this.$moment().subtract(
              this.timeOption.number,
              this.timeOption.unit
            ),
            new Date().getTime()
          ];
        }, this.timeOption.refreshTime);
      }
    },
    changeQueryTime({ val, timeOption }) {
      this.setMergeOptions = false;
      clearInterval(this.timter);
      const date = this.$moment(val);
      this.timeTool.typeID = timeOption.typeID;
      this.customed = timeOption.custom;
      this.timeOption = this._.cloneDeep(timeOption);
      if (timeOption.custom) {
        // 自定义的曲线查询时长
        this.CetTable_realTime.interval_in = timeOption.interval;
        this.queryTime = [
          this.$moment().subtract(timeOption.number, timeOption.unit),
          new Date().getTime()
        ];
        this.setTimer();
      } else {
        clearInterval(this.timter);
        if (this.showSecond) {
          this.CetTable_realTime.interval_in = this._.isNumber(
            timeOption.interval
          )
            ? timeOption.interval
            : timeOption.typeID === 12
            ? 60
            : 900;
        } else {
          this.CetTable_realTime.interval_in = 0;
        }
        this.queryTime = [
          date.startOf(timeOption.unit).valueOf(),
          date.endOf(timeOption.unit).valueOf() + 1
        ];
      }
    },

    async init() {
      if (this._.get(this.$store.state, "systemCfg.trendcurveBuffer")) {
        this.CetTable_realTime.dataConfig.queryUrl =
          "/eem-service/v2/peccore/datalog?cacheInterval=0&cacheMonthNumber=3&dbInterval=0";
      } else {
        this.CetTable_realTime.dataConfig.queryUrl =
          "/eem-service/v2/peccore/datalog?cacheInterval=0&cacheMonthNumber=0&dbInterval=0";
      }
      await this.getProjectEnergy();
      this.ElSelect_1.value = 1;
      this.showSecond = false;
      this.checkedStations = {};
      this.stations = {};
      this.CetTable_realTime.clearTrigger_in = new Date().getTime();
      this.getTreeData();
      this.timeTool.disable_in = true;
      this.timeTool.selectDisable = true;
      this.CustomDatePicker_1.disable_in = true;
      this.timeTool.typeID = 12;
      this.timeTool.showOption = false;
      this.CetButton_export.disable_in = true;
      this.CetButton_empty.disable_in = true;
    },
    async getProjectEnergy() {
      const res = await customApi.getProjectEnergy(this.projectId);
      if (res.code !== 0) {
        return;
      }
      const options = res.data || [];
      this.energyOptions = this._.cloneDeep(options);
    },
    //时间组件变化
    CustomDatePicker_1_change(val) {
      this.CustomDatePicker_1.queryTime = val;
      let interval = 0;
      if (val.cycle === 12) {
        interval = 0;
      } else if (val.cycle === 13) {
        interval = 5;
      } else if (val.cycle === 14) {
        interval = 30;
      } else if (val.cycle === 15) {
        interval = 60;
      } else if (val.cycle === 20) {
        let time = val.endTime - val.startTime;
        let timeDay = time / (1000 * 60 * 60 * 24);
        if (timeDay < 3) {
          interval = 0;
        } else if (timeDay < 7) {
          interval = 5;
        } else if (timeDay < 30) {
          interval = 30;
        } else if (timeDay < 90) {
          interval = 60;
        } else {
          interval = 60;
        }
      }
      this.CetTable_realTime.interval_in = interval;
      this.queryTime = [val.startTime, val.endTime];
    }
  },
  mounted() {
    if (this.systemCfg.cachePage) {
      this.init();
    }
  },
  activated() {
    if (!this.systemCfg.cachePage) {
      this.init();
    } else if (
      this.CetTable_realTime.params_in &&
      this.CetTable_realTime.params_in.length
    ) {
      this.CetTable_realTime.params_in = this._.cloneDeep(
        this.CetTable_realTime.params_in
      );
    }
  },
  disactivated() {
    clearInterval(this.timter);
  },
  destroyed() {
    clearInterval(this.timter);
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.sub-aside {
  position: relative;
  transition: all 500ms;
  &.show-aside {
    transform: translateX(0);
  }
  &.hide-aside {
    transform: translateX(-100%);
  }
  :deep(.el-collapse-item__wrap) {
    border-bottom: none;
  }
  :deep(.el-collapse-item__content) {
    padding-bottom: 10px;
  }
}
.page-main {
  position: relative;
  transition: all 500ms;
}
.collapse-aside {
  position: absolute;
  left: -15px;
  top: 50%;
  width: 13px;
  height: 80px;
  line-height: 80px;
  transform: translateY(-50%);
  cursor: pointer;
  @include background_color(ZS);
  border-radius: 4px 0px 0px 4px;
  color: #fff;
}
.text-center {
  text-align: center;
  width: 100%;
  height: 100%;
  position: relative;
  .info {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    height: 50px;
    line-height: 50px;
    margin: auto;
    @include font_size(H1);
  }
}
</style>
<style lang="scss">
.aside-collapse {
  .el-collapse-item__header {
    height: 35px;
    line-height: 35px;
    padding: 0 10px;
  }
  .el-collapse-item__header.is-active {
    @include background_color(BG4);
  }
}
</style>
