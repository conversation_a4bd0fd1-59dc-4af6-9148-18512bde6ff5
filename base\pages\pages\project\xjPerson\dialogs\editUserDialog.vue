<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="min"
    >
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_password"
            v-on="CetButton_password.event"
          ></CetButton>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <CetForm
        class="eem-cont-c1"
        :data.sync="CetForm_User.inputData_in"
        v-bind="CetForm_User"
        v-on="CetForm_User.event"
      >
        <el-form-item
          class="custom-form-item"
          :label="$T('成员名称')"
          prop="name"
        >
          <ElInput
            maxlength="30"
            :placeholder="$T('请输入成员名称')"
            v-model.trim="CetForm_User.inputData_in.name"
          ></ElInput>
        </el-form-item>
        <el-form-item :label="$T('角色')" prop="roleId">
          <ElSelect
            v-model="CetForm_User.inputData_in.roleId"
            v-bind="ElSelect_Role"
            v-on="ElSelect_Role.event"
          >
            <ElOption
              v-for="item in ElOption_Roles.options_in"
              :key="item[ElOption_Roles.key]"
              :label="item[ElOption_Roles.label]"
              :value="item[ElOption_Roles.value]"
              :disabled="item[ElOption_Roles.disabled]"
            ></ElOption>
          </ElSelect>
        </el-form-item>
      </CetForm>
    </CetDialog>
    <!-- 修改用户 -->
    <editPasswordDialog
      v-bind="editPasswordDialog"
      @finishTrigger_out="editPasswordDialog_finishTrigger_out"
    ></editPasswordDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";
import common from "eem-utils/common.js";
import editPasswordDialog from "./editPasswordDialog";
export default {
  name: "editUserDialog",
  components: { editPasswordDialog },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("修改成员"),
        showClose: true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // ResetUserPassword表单组件
      CetForm_User: {
        dataMode: "backendInterface", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "editInspectorUser",
          modelLabel: "",
          dataIndex: ["id", "name", "roleId", "role"], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {
          id: "",
          name: "",
          roleId: null,
          role: {}
        },
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "110px",
        labelPosition: "top",
        rules: {
          name: [
            common.pattern_name,
            { required: true, message: $T("用户名不能为空"), trigger: "blur" }
          ],
          roleId: [
            { required: true, message: $T("角色不能为空"), trigger: "blur" }
          ]
        },
        event: {
          currentData_out: this.CetForm_User_currentData_out,
          saveData_out: this.CetForm_User_saveData_out,
          finishData_out: this.CetForm_User_finishData_out,
          finishTrigger_out: this.CetForm_User_finishTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_password: {
        visible_in: true,
        disable_in: false,
        title: $T("修改密码"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_password_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // name组件
      ElInput_name: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_name_change_out,
          input: this.ElInput_name_input_out
        }
      },
      ElSelect_Role: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_Role_change_out
        }
      },
      ElOption_Roles: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      // 编辑密码弹窗
      editPasswordDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      const _this = this;
      // this.CetForm_User.inputData_in = {
      //   name: "",
      //   roleId: null,
      // };

      // eslint-disable-next-line promise/param-names
      new Promise((res, err) => {
        _this.getRoles_out(res);
      }).then(data => {
        if (data) {
          _this.CetDialog_pagedialog.openTrigger_in = _this._.cloneDeep(val);
          _this.CetForm_User.inputData_in = _this._.cloneDeep(
            _this.inputData_in
          );
          const roleId = _this._.get(_this.inputData_in, "roles[0].id", null);
          _this.$set(_this.CetForm_User.inputData_in, "roleId", roleId);
          _this.CetForm_User.inputData_in.role = _this._.get(
            _this.inputData_in,
            "roles[0]",
            {}
          );
        }
      });
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {
      this.CetForm_User.queryId_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      const user = {
        name: val.name,
        id: val.id
      };
      this.CetForm_User.inputData_in = this._.cloneDeep(user);
    }
  },
  methods: {
    CetForm_User_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_User_saveData_out(val) {
      this.$emit("saveData_out", this._.cloneDeep(val));
    },
    CetForm_User_finishData_out(val) {
      this.$emit("finishData_out", this._.cloneDeep(val));
    },
    CetForm_User_finishTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
      this.$emit("finishTrigger_out", val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetForm_User.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      // eslint-disable-next-line promise/param-names
      new Promise((res, err) => {
        this.getUsers_out(res);
      }).then(res => {
        const list = res || [];
        let isOk = true;
        list.forEach(item => {
          if (
            item.name === this.CetForm_User.inputData_in.name &&
            item.id !== this.CetForm_User.inputData_in.id
          ) {
            isOk = false;
          }
        });
        if (isOk) {
          this.CetForm_User.saveTrigger_in = new Date().getTime();
        } else {
          this.$message.warning($T("成员名称已存在，请输入其他名称"));
        }
      });
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_password_statusTrigger_out(val) {
      this.editPasswordDialog.inputData_in = this._.cloneDeep(
        this.inputData_in
      );
      this.editPasswordDialog.openTrigger_in = new Date().getTime();
    },
    no() {},
    // name输出,方法名要带_out后缀
    ElInput_name_change_out(val) {},
    ElInput_name_input_out(val) {},
    ElSelect_Role_change_out(val) {
      const _this = this;
      if (_this._.isNil(val)) {
        _this.CetForm_User.inputData_in.role = {};
        return;
      }

      const role = _this._.find(_this.ElOption_Roles.options_in, { id: val });
      if (!role) {
        _this.CetForm_User.inputData_in.role = {};
        return;
      }

      _this.CetForm_User.inputData_in.role = _this._.cloneDeep(role);
    },
    //获取角色列表信息
    getRoles_out(callback) {
      const _this = this;
      customApi
        .getInspectorRoles()
        .then(response => {
          if (response.code !== 0) {
            // eslint-disable-next-line standard/no-callback-literal
            callback && callback(false);
            return;
          }
          const list = response.data || [];
          _this.ElOption_Roles.options_in = list;
          // eslint-disable-next-line standard/no-callback-literal
          callback && callback(true);
        })
        .catch(() => {});
    },
    // 获取所有用户信息
    getUsers_out(callback) {
      const params = {};
      customApi.getAllUsers(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []);
          callback && callback(data);
        }
      });
    },
    //修改密码完成之后
    editPasswordDialog_finishTrigger_out(val) {}
  },
  created: function () {}
};
</script>
<style lang="scss" scoped></style>
