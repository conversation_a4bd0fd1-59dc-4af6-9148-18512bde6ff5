<template>
  <el-container class="knowledge eem-common">
    <el-aside width="315px" class="eem-aside">
      <div class="fullheight flex-column">
        <div class="flex-auto">
          <KnowledgeTab
            @tabChange="tabChange"
            :tabs="tabs"
            :selectObj="selectObj"
          />
        </div>
        <div
          v-permission="'knowledgedirectory_create'"
          class="center-text mtJ3"
        >
          <!-- 新建目录按钮组件 -->
          <CetButton
            v-bind="CetButton_createCatalog"
            v-on="CetButton_createCatalog.event"
            class="create"
          ></CetButton>
        </div>
      </div>
    </el-aside>
    <el-main class="fullheight padding0 mlJ3 eem-container">
      <KnowledgeTable
        :selectObj="selectObj"
        @rename="rename"
        @move="move"
        @copy="copy"
        @refresh="refresh"
        :refresh="refresh1"
      />
    </el-main>
    <CreateAndRenameDialog
      :openTrigger_in="createAndRenameDialog.openTrigger_in"
      :order="createAndRenameDialog.order"
      :folderId="createAndRenameDialog.folderId"
      :txt="createAndRenameDialog.txt"
      @refresh="refresh"
    />
    <MoveAndCopyDialog
      :openTrigger_in="moveAndCopyDialog.openTrigger_in"
      :order="moveAndCopyDialog.order"
      :selectList="moveAndCopyDialog.selectList"
      :tabs="tabs"
      @refresh="refresh"
    />
  </el-container>
</template>
<script>
import KnowledgeTab from "./subcomponents/KnowledgeTab";
import KnowledgeTable from "./subcomponents/KnowledgeTable";
import CreateAndRenameDialog from "./subcomponents/CreateAndRenameDialog";
import MoveAndCopyDialog from "./subcomponents/MoveAndCopyDialog";
import commonApi from "@/api/custom";

export default {
  name: "Knowledge",
  components: {
    KnowledgeTab,
    KnowledgeTable,
    CreateAndRenameDialog,
    MoveAndCopyDialog
  },
  computed: {
    hasCreateCatalogAuth() {
      const userInfo = this.$store.state.userInfo;
      // 超级管理员不用验证
      if (userInfo.name === "ROOT") {
        return true;
      }

      const role = userInfo.roles[0];
      const auths = role.auths || [];
      return auths.indexOf(1024) > -1;
    },
    userId() {
      var vm = this;
      return vm.$store.state.userInfo.id;
    },
    projectTenantId() {
      var vm = this;
      return vm.$store.state.projectTenantId;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.localStorage) {
          return false;
        } else {
          var storage = window.localStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },
  data() {
    return {
      activatedNum: 0,
      flag: true,
      refresh1: new Date().getTime(),
      // 新建文件夹和重命名弹窗的信息
      createAndRenameDialog: {
        openTrigger_in: new Date().getTime(),
        order: "",
        folderId: null,
        txt: {}
      },
      // 移动到和复制到弹窗的信息
      moveAndCopyDialog: {
        openTrigger_in: new Date().getTime(),
        order: "",
        selectList: []
      },
      tabs: [], // tab 列表
      selectObj: { id: 1, label: "" }, // 选中的tab
      // 新建目录组件
      CetButton_createCatalog: {
        visible_in: true,
        disable_in: false,
        title: $T("新建目录"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_createCatalog_statusTrigger_out
        }
      },
      // 1弹窗组件
      CetDialog_1: {
        title: $T("请输入名称"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        width: "350px",
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        }
      },
      // name组件
      ElInput_name: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElInput_name_change_out,
          input: this.ElInput_name_input_out
        }
      },
      // 确定按钮
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      // 取消按钮
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {},
  methods: {
    // tab切换
    tabChange(val) {
      this.selectObj = val;
    },
    // 新建目录
    CetButton_createCatalog_statusTrigger_out() {
      if (!this.$checkPermission("knowledgedirectory_create")) {
        this.$message.warning($T("无权限"));
      } else {
        this.createAndRenameDialog.order = $T("新建目录");
        this.createAndRenameDialog.openTrigger_in = new Date().getTime();
      }
    },
    // 重命名
    rename(val) {
      this.createAndRenameDialog.order = $T("重命名");
      if (val) {
        this.createAndRenameDialog.txt = {
          name: val.name,
          type: 2,
          fathernode_id: val.fathernode_id
        };
        this.createAndRenameDialog.folderId = val.id;
      } else {
        this.createAndRenameDialog.txt = {
          name: this.selectObj.label,
          type: 1,
          fathernode_id: 0
        };
        this.createAndRenameDialog.folderId = this.selectObj.id;
      }
      this.createAndRenameDialog.openTrigger_in = new Date().getTime();
    },
    // 复制到
    copy(val) {
      this.moveAndCopyDialog.openTrigger_in = new Date().getTime();
      this.moveAndCopyDialog.order = $T("复制到");
      this.moveAndCopyDialog.selectList = val;
    },
    // 移动到
    move(val) {
      this.moveAndCopyDialog.openTrigger_in = new Date().getTime();
      this.moveAndCopyDialog.order = $T("移动到");
      this.moveAndCopyDialog.selectList = val;
    },
    CetInterface_getFolder_result_out(val) {
      const tabs = val || [];
      let selectObj = {};
      tabs.forEach(item => {
        item.label = item.name;
      });
      this.tabs = tabs;
      if (tabs.length === 0) return;
      if (this.flag) {
        selectObj = {
          id: tabs[0].id,
          label: tabs[0].label
        };
      } else {
        selectObj = this.NewSelect;
      }

      this.selectObj = selectObj;
    },
    refresh(val) {
      if (!val) {
        this.flag = true;
        const timeout = setTimeout(() => {
          this.getVideoPermission_out();
          clearTimeout(timeout);
        }, 300);
      } else if (val == 2) {
        this.refresh1 = new Date().getTime();
      } else {
        this.flag = false;
        this.NewSelect = val;
        this.getVideoPermission_out();
      }
    },
    getVideoPermission_out() {
      if (!this.userId) {
        return;
      }
      // if (!this.flag) {
      //   return;
      // }
      // this.flag = false;
      let params = {
        userId: this.userId,
        tenantId: this.projectTenantId,
        projectId: this.projectId
      };
      commonApi
        .getVideoPermission(params)
        .then(res => {
          if (res.code == 0) {
            this.CetInterface_getFolder_result_out(res.data);
          }
        })
        .finally(() => {
          // this.flag = true;
        });
    }
  },
  created: function () {},
  activated: function () {
    this.getVideoPermission_out();
  }
};
</script>
<style lang="scss" scoped>
.knowledge {
  width: 100%;
  height: 100%;
  .create {
    width: 100%;
    :deep(.el-button) {
      width: 100% !important;
    }
  }
}
</style>
