<template>
  <!-- 1弹窗组件 -->
  <CetDialog class="CetDialog" v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <el-container
      style="height: 100%; flex-direction: column"
      class="pJ3 plJ4 prJ4 bg1 brC1"
    >
      <el-container style="height: 100%">
        <el-table
          :data="tableData"
          height="500"
          border
          style="width: 100%"
          @selection-change="handleSelectionChange"
          stripe
        >
          <el-table-column
            label="#"
            type="index"
            align="left"
            width="41"
          ></el-table-column>
          <el-table-column
            :prop="item.prop"
            :label="item.label"
            align="left"
            v-for="(item, index) in tableColumns"
            :key="index"
          >
            <template slot-scope="scope">
              <!-- 系数 -->
              <div v-if="item.prop == 'rate'">
                <ElInputNumber
                  v-model="scope.row[item.prop]"
                  v-bind="ElInputNumber_num"
                  v-on="ElInputNumber_num.event"
                ></ElInputNumber>
              </div>
              <span v-else>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-container>
    </el-container>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import common from "eem-utils/common";
import { httping } from "@omega/http";
export default {
  name: "shareRate",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    // 配电室设备
    roomDeviceNodes: {
      type: Array
    },
    // 配电设备模型列表
    roomDeviceList: {
      type: Array
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data(vm) {
    return {
      tableData: [
        // {
        //   objectName: "配电室设备",
        //   supplytoName: "归属节点",
        //   starttime: 1594599064676,
        //   endtime: 1595299564676,
        //   rate: 0.5
        // }
      ],
      tableColumns: [
        {
          prop: "objectName",
          label: $T("配电室设备")
        },
        {
          prop: "supplytoName",
          label: $T("归属节点")
        },
        // {
        //   prop: "starttime",
        //   label: "生效时间"
        // },
        // {
        //   prop: "endtime",
        //   label: "失效时间"
        // },
        {
          prop: "rate",
          label: $T("供能系数")
        }
      ],
      // 多选数组
      checkedArray: [],
      CetDialog_1: {
        title: $T("分摊系数"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInputNumber_num: {
        value: "",
        style: {},
        controls: false,
        min: 0,
        max: 1,
        step: 0.00001,
        precision: 5,
        controlsPosition: "", //right 可选值
        event: {
          change: this.ElInputNumber_num_change_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      this.checkedArray = [];
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {
      var vm = this;
      vm.CetDialog_1.queryId_in = val;
    },
    inputData_in(val) {
      this.CetDialog_1.inputData_in = val;
      this.getEnergysupplyto(val);
    }
  },

  methods: {
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.addSupplyRelation_out();
    },
    CetDialog_1_open_out(val) {},
    CetDialog_1_close_out(val) {},
    // num输出,方法名要带_out后缀
    ElInputNumber_num_change_out(val) {},
    // 获取当前节点关联的配电设备
    getEnergysupplyto(val) {
      if (!val) {
        return;
      }
      var me = this;
      var param;
      var url;
      param = {
        rootCondition: {
          filter: {
            expressions: [
              {
                operator: "EQ", //数据库标识等于
                prop: "supplytolabel",
                limit: val.modelLabel
              },
              {
                operator: "EQ",
                prop: "supplytoid",
                limit: val.id
              }
            ]
          }
        },
        rootLabel: "energysupplyto",
        rootID: 0
      };
      url = `/eem-service/v1/common/query/oneLayer`;
      common.requestData(
        {
          url: url,
          data: param
        },
        (data, res) => {
          if (res.code === 0 && res.data && res.data.length > 0) {
            let data = res.data || [],
              roomDeviceNodes = this.roomDeviceNodes || [],
              tableData = [];
            let ids = roomDeviceNodes.map(i => {
              return `${i.modelLabel}_${i.id}`;
            });
            data.forEach(item => {
              if (ids.includes(`${item.objectlabel}_${item.objectid}`)) {
                item.supplytoName = val.name;
                item.objectName = this.findName(
                  this.roomDeviceNodes,
                  item.objectid,
                  item.objectlabel
                );
                item.rate = this._.isNumber(item.rate) ? item.rate : 1;
                tableData.push(item);
              }
            });
            this.tableData = tableData;
          } else {
            this.tableData = [];
          }
        }
      );
    },
    // 找配电室设备名称
    findName(arr, id, modelLabel) {
      var name;
      arr.forEach(item => {
        if (item.id == id && item.modelLabel == modelLabel) {
          name = item.name;
        }
      });
      return name;
    },
    // tab多选
    handleSelectionChange(val) {
      this.checkedArray = val;
    },
    // 保存
    addSupplyRelation_out() {
      var param = [];
      // 单个
      param = this.tableData.map(item => {
        return {
          id: item.id,
          rate: item.rate || 0,
          modelLabel: "energysupplyto"
        };
      });
      httping({
        url: `/eem-service/v1/common/write/hierachy`,
        data: param,
        method: "POST"
      }).then(
        res => {
          this.$message({
            message: $T("保存成功"),
            type: "success"
          });
          this.CetDialog_1.closeTrigger_in = this._.cloneDeep(
            new Date().getTime()
          );
        },
        function () {}
      );
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep() {
    .el-dialog__body {
      @include background_color(BG);
      @include padding(J1);
    }
  }
}
</style>
