<template>
  <div>
    <CetDialog v-bind="CetDialog_add" v-on="CetDialog_add.event">
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
      <el-container style="height: 100%; padding: 0px">
        <el-aside width="200px">
          <el-main style="height: 100%" class="eem-cont-c1">
            <div
              v-for="(item, index) in listData"
              :key="index"
              :class="{
                clearfix: true,
                listItem: true,
                action: itemAction == index
              }"
              @click="listDataClick(index)"
            >
              <div v-show="!item.showInput">
                <div
                  class="fl ellipsis"
                  style="width: calc(100% - 50px)"
                  :title="`${item.name}`"
                >
                  {{ item.name }}
                </div>
                <div class="fr">
                  <span
                    class="el-icon-edit"
                    @click="handleEdit(item, index)"
                  ></span>
                  <span
                    class="el-icon-delete"
                    @click="handleDelete(item, index)"
                  ></span>
                  <!-- <span class="list-tips" v-show="item.num != 0" @click="handleTips()"></span> -->
                </div>
              </div>
              <ElInput
                :ref="`addInput${index}`"
                v-show="item.showInput"
                class="mt5"
                v-model.trim="item.name"
                v-bind="ElInput_1"
                v-on="ElInput_1.event"
                @blur="elInputBlur(item, index)"
              ></ElInput>
            </div>
            <div class="addBtn" @click="handleAdd">
              <span class="el-icon-circle-plus"></span>
              点击添加标准
            </div>
          </el-main>
        </el-aside>
        <el-container style="height: 100%" class="mlJ2">
          <el-row :gutter="24" style="width: 100%; margin: 0px">
            <el-col :span="10" class="eem-cont-c1" style="height: 100%">
              <div class="mbJ1 fs16">下限曲线</div>
              <CetTable
                :data.sync="CetTable_1.data"
                :dynamicInput.sync="CetTable_1.dynamicInput"
                v-bind="CetTable_1"
                v-on="CetTable_1.event"
              >
                <ElTableColumn v-bind="ElTableColumn_duration">
                  <template slot-scope="scope">
                    <span v-show="!CetTable_1.showInput">
                      {{
                        CetTable_1.data[scope.$index][
                          ElTableColumn_duration.prop
                        ] || 0
                      }}
                    </span>
                    <ElInputNumber
                      :precision="2"
                      v-show="CetTable_1.showInput"
                      v-model="
                        CetTable_1.data[scope.$index][
                          ElTableColumn_duration.prop
                        ]
                      "
                      v-bind="ElInputNumber_1"
                      v-on="ElInputNumber_1.event"
                    ></ElInputNumber>
                  </template>
                </ElTableColumn>
                <ElTableColumn v-bind="ElTableColumn_magnitude">
                  <template slot-scope="scope">
                    <span v-show="!CetTable_1.showInput">
                      {{
                        CetTable_1.data[scope.$index][
                          ElTableColumn_magnitude.prop
                        ] ||
                        CetTable_1.data[scope.$index][
                          ElTableColumn_magnitude.prop
                        ] === 0
                          ? Number(
                              CetTable_1.data[scope.$index][
                                ElTableColumn_magnitude.prop
                              ]
                            ).toFixed2(2) + "%"
                          : "0.00%"
                      }}
                    </span>
                    <ElInputNumber
                      :precision="2"
                      v-show="CetTable_1.showInput"
                      v-model="
                        CetTable_1.data[scope.$index][
                          ElTableColumn_magnitude.prop
                        ]
                      "
                      v-bind="ElInputNumber_1"
                      v-on="ElInputNumber_1.event"
                    ></ElInputNumber>
                  </template>
                </ElTableColumn>
              </CetTable>
              <div class="mtJ1">
                <CetButton
                  class="fr mlJ"
                  v-bind="CetButton_add"
                  v-on="CetButton_add.event"
                ></CetButton>
                <CetButton
                  class="fr"
                  v-bind="CetButton_edit"
                  v-on="CetButton_edit.event"
                ></CetButton>
                <CetButton
                  class="fr"
                  v-bind="CetButton_tab_confirm"
                  v-on="CetButton_tab_confirm.event"
                ></CetButton>
                <CetButton
                  class="fr mrJ"
                  v-bind="CetButton_del"
                  v-on="CetButton_del.event"
                ></CetButton>
              </div>
            </el-col>
            <el-col :span="14" style="padding: 0px">
              <div class="eem-cont-c1 mlJ2">
                <div class="mbJ1 fs16">曲线预览</div>
                <CetChart v-bind="CetChart_1" class="mbJ1"></CetChart>
              </div>
            </el-col>
          </el-row>
        </el-container>
      </el-container>
    </CetDialog>
    <CetDialog v-bind="CetDialog_edit" v-on="CetDialog_edit.event">
      <div class="eem-cont-c1">
        <div class="mbJ1">类型名称</div>
        <ElInput
          v-model="ElInput_edit.value"
          v-bind="ElInput_edit"
          v-on="ElInput_edit.event"
        ></ElInput>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_edit_cancel"
          v-on="CetButton_edit_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_edit_confirm"
          v-on="CetButton_edit_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom";
export default {
  name: "customCurve",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    // 所有曲线列表
    chartCurveAll_in: {
      type: Array
    },
    // 曲线类型
    sarficurvetype_in: {
      type: Number
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.localStorage) {
          return false;
        } else {
          var storage = window.localStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },
  data(vm) {
    return {
      deleteData: [],
      listData: [
        // {
        //   name: "电能质量类",
        //   showInput: false,
        //   sarfichartvalue_model: [
        //     {
        //       criticaldirection: 1,
        //       duration: 0.4,
        //       magnitude: 5
        //     },
        //     {
        //       criticaldirection: 1,
        //       duration: 1.0,
        //       magnitude: 2
        //     },
        //     {
        //       criticaldirection: 1,
        //       duration: 3.0,
        //       magnitude: 1.4
        //     },
        //     {
        //       criticaldirection: 1,
        //       duration: 19.0,
        //       magnitude: 1.2
        //     },
        //     {
        //       criticaldirection: 1,
        //       duration: 500,
        //       magnitude: 1
        //     },
        //     {
        //       criticaldirection: 1,
        //       duration: 1000,
        //       magnitude: 1
        //     }
        //   ]
        // },
        // {
        //   name: "工艺母线类",
        //   showInput: false,
        //   sarfichartvalue_model: [
        //     {
        //       criticaldirection: 1,
        //       duration: 1,
        //       magnitude: 0.56
        //     }
        //   ]
        // }
      ],
      itemAction: 0,
      // add弹窗组件
      CetDialog_add: {
        title: "编辑类型",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_add_open_out,
          close_out: this.CetDialog_add_close_out
        },
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "保存",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetDialog_edit: {
        title: "编辑标准名称",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        width: "500px",
        showClose: true
      },
      ElInput_edit: {
        value: "",
        style: {
          width: "200px"
        },
        maxlength: 20,
        event: {}
      },
      CetButton_edit_confirm: {
        visible_in: true,
        disable_in: false,
        title: "完成",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_edit_confirm_statusTrigger_out
        }
      },
      CetButton_edit_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_edit_cancel_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        },
        style: {
          height: "320px"
        },
        showInput: false
      },
      ElTableColumn_duration: {
        //type: "",      // selection 勾选 index 序号
        prop: "duration", // 支持path a[0].b
        label: "持续时间（ms）", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      ElTableColumn_magnitude: {
        //type: "",      // selection 勾选 index 序号
        prop: "magnitude", // 支持path a[0].b
        label: "电压增幅", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        formatter: function (row, column, cellValue, index) {
          if (cellValue || cellValue === 0) {
            return cellValue;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: "新增",
        type: "primary",
        size: "small",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      CetButton_del: {
        visible_in: true,
        disable_in: true,
        title: "删除",
        type: "danger",
        size: "small",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_del_statusTrigger_out
        }
      },
      CetButton_edit: {
        visible_in: true,
        disable_in: true,
        title: "编辑",
        type: "primary",
        size: "small",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_edit_statusTrigger_out
        }
      },
      CetButton_tab_confirm: {
        visible_in: false,
        disable_in: false,
        title: "完成",
        type: "primary",
        size: "small",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_tab_confirm_statusTrigger_out
        }
      },
      CetChart_1: {
        //组件输入项
        inputData_in: null,
        style: {
          height: "350px"
        },
        options: {
          tooltip: {
            trigger: "item"
          },
          grid: {
            top: 23,
            right: 20,
            bottom: 20,
            left: 50
          },
          xAxis: {
            type: "log",
            logBase: 10,
            axisLabel: {
              formatter: "{value} ms"
            }
          },
          yAxis: {
            type: "value",
            axisLabel: {
              formatter: "{value} %"
            }
          },
          series: [
            {
              data: [],
              type: "line",
              symbol: "circle"
            }
          ]
        }
      },
      ElInput_1: {
        value: "",
        size: "small",
        style: {
          width: "100%"
        },
        maxlength: 20,
        event: {}
      },
      ElInputNumber_1: {
        value: "",
        size: "small",
        controls: false,
        min: 0,
        style: {
          width: "100%"
        },
        event: {}
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.openTrigger_in = val;
      vm.deleteData = [];
      vm.CetTable_1.showInput = false;
      vm.CetButton_edit.visible_in = true;
      vm.CetButton_tab_confirm.visible_in = false;
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_add.closeTrigger_in = val;
    },
    inputData_in(val) {},
    "CetTable_1.data": {
      handler: function (val) {
        if (val && val.length > 0) {
          this.CetChart_1.options.series[0].data = val.map((item, index) => {
            this.$set(item, "index", index);
            if (!item.duration && item.duration !== 0) {
              this.$set(item, "duration", "0");
            }
            if (!item.magnitude && item.magnitude !== 0) {
              this.$set(item, "magnitude", "0");
            }
            if (Number(item.duration)) {
              return [
                Number(item.duration),
                Number(item.magnitude).toFixed2(2)
              ];
            }
          });
        } else {
          this.CetChart_1.options.series[0].data = [];
        }
      },
      deep: true
    },
    chartCurveAll_in: {
      handler: function (val) {
        var data = this._.cloneDeep(val);
        if (data && data.length > 0) {
          var listData = data.filter(item => !item.isdefault);
          listData.forEach(item => {
            this.$set(item, "showInput", false);
            if (!item.sarfichartvalue_model) {
              this.$set(item, "sarfichartvalue_model", []);
            }
          });
          this.listData = listData;
        } else {
          this.listData = [];
          this.CetButton_add.disable_in = true;
        }
        this.listDataClick(0);
      },
      deep: true
    },
    listData: {
      handler: function (val) {
        if (val.length > 0) {
          this.CetButton_add.disable_in = false;
        } else {
          this.CetButton_add.disable_in = true;
          this.CetTable_1.data = [];
          this.CetButton_tab_confirm_statusTrigger_out();
        }
      },
      deep: true
    }
  },

  methods: {
    elInputBlur(item, index) {
      if (!item.name) {
        this.$message({
          message: "名称不能为空",
          type: "warning"
        });
        this.listData.splice(index, 1);
        return;
      } else if (item.name.length > 20) {
        this.$message({
          message: "名称支持20个字符",
          type: "warning"
        });
        this.listData.splice(index, 1);
        return;
      } else if (
        this.listData.filter(listDataItem => listDataItem.name == item.name)
          .length > 1
      ) {
        this.$message({
          message: "名称重复",
          type: "warning"
        });
        this.listData.splice(index, 1);
        return;
      }
      // 如果是第一次新增需要输出左侧列表点击事件
      if (this.listData.length == 1) {
        this.listDataClick(0);
      }
      item.showInput = false;
    },
    CetDialog_add_open_out(val) {},
    CetDialog_add_close_out(val) {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      var addData = [];
      var deleteData = [];
      console.log(this.listData);
      console.log(this.deleteData);
      // 获取边界枚举
      customApi.queryEnum({ rootLabel: "criticaldirection" }).then(response => {
        var criticaldirectionId = response.data.filter(
          item => item.text == "下限"
        )[0].id;
        var noName = false;
        var nameRepeat = false;
        if (this.listData && this.listData.length > 0) {
          this.listData.forEach(item => {
            if (!item.name) {
              noName = true;
            }

            if (addData.filter(i => i.name == item.name).length > 0) {
              nameRepeat = true;
            }
            if (
              item.sarfichartvalue_model &&
              item.sarfichartvalue_model.length > 0
            ) {
              item.sarfichartvalue_model.forEach(i => {
                i.criticaldirection = criticaldirectionId;
              });
            }
            addData.push({
              id: item.id,
              isdefault: false, //是否是默认的曲线，界面上一律传false
              name: item.name,
              sarficurvetype: item.sarficurvetype, //曲线类型，如SEMI47、ITIC等，枚举取SARFICurveType
              sarfichartvalue_model: item.sarfichartvalue_model,
              projectid: this.projectId
            });
          });
        }
        if (noName) {
          this.$message({
            message: "标准名称不能为空",
            type: "warning"
          });
          return;
        }
        if (nameRepeat) {
          this.$message({
            message: "标准名称不能重复",
            type: "warning"
          });
          return;
        }
        this.deleteData.forEach(item => {
          if (item.id) {
            deleteData.push(item.id);
          }
        });
        Promise.all([
          new Promise((resolve, reject) => {
            if (addData && addData.length > 0) {
              customApi
                .transienteventanalysisAddChartCurve(addData)
                .then(res => {
                  if (res.code == 0) {
                    resolve();
                  }
                });
            } else {
              resolve();
            }
          }),
          new Promise((resolve, reject) => {
            if (deleteData && deleteData.length > 0) {
              customApi
                .transienteventanalysisDelChartCurve(deleteData)
                .then(res => {
                  if (res.code == 0) {
                    resolve();
                  }
                });
            } else {
              resolve();
            }
          })
        ])
          .then(res => {
            this.$message({
              message: "保存成功！",
              type: "success"
            });
            this.$emit("confirm_out");
            this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
          })
          .catch(err => {});
      });
    },
    CetButton_edit_cancel_statusTrigger_out(val) {
      this.ElInput_edit.value = "";
      this.CetDialog_edit.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_edit_confirm_statusTrigger_out(val) {
      if (!this.ElInput_edit.value) {
        this.$message({
          message: "类型名称不能为空",
          type: "warning"
        });
        return;
      } else if (
        this.listData.filter((item, index) => {
          if (index != this.itemAction) {
            return item.name == this.ElInput_edit.value;
          } else {
            return false;
          }
        }).length
      ) {
        this.$message({
          message: "名称重复",
          type: "warning"
        });
        return;
      }
      this.listData[this.itemAction].name = this.ElInput_edit.value;
      this.ElInput_edit.value = "";
      this.CetDialog_edit.closeTrigger_in = this._.cloneDeep(val);
    },
    CetTable_1_record_out(val) {
      this.currentTabItem = val;
      if (val.id === -1) {
        this.CetButton_del.disable_in = true;
        this.CetButton_edit.disable_in = true;
      } else {
        this.CetButton_del.disable_in = false;
        this.CetButton_edit.disable_in = false;
      }
    },
    CetTable_1_outputData_out(val) {},
    CetButton_add_statusTrigger_out(val) {
      this.CetTable_1.data.push({
        criticaldirection: 1,
        duration: 0,
        magnitude: 0
      });
    },
    CetButton_edit_statusTrigger_out(val) {
      this.CetTable_1.showInput = true;

      this.CetButton_edit.visible_in = false;
      this.CetButton_tab_confirm.visible_in = true;
    },
    CetButton_del_statusTrigger_out(val) {
      this.CetTable_1.data.splice(this.currentTabItem.index, 1);
    },
    CetButton_tab_confirm_statusTrigger_out(val) {
      this.CetTable_1.showInput = false;
      this.CetButton_edit.visible_in = true;
      this.CetButton_tab_confirm.visible_in = false;
    },
    handleEdit(item, index) {
      this.ElInput_edit.value = item.name;
      this.CetDialog_edit.openTrigger_in = new Date().getTime();
    },
    handleDelete(item, index) {
      this.$confirm("此操作将会永久删除此标准，是否继续？", "删除", {
        confirmButtonText: "确定删除",
        cancelButtonText: "我再想想",
        type: "warning",
        closeOnClickModal: false,
        showClose: true,
        beforeClose: (action, instance, done) => {
          if (action == "confirm") {
            this.deleteData.push(this.listData.splice(index, 1)[0]);
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action != "confirm") {
            this.$message({
              type: "info",
              message: "取消删除！"
            });
          }
        }
      });
    },
    handleTips() {
      this.$alert(
        "此类型下依然有场景，暂时不能进行删除操作，请将场景清空后重试。",
        "提示",
        {
          confirmButtonText: "我知道了",
          callback: action => {}
        }
      );
    },
    handleAdd() {
      this.listData.push({
        name: "新增标准",
        showInput: true,
        isdefault: false,
        sarfichartvalue_model: [],
        sarficurvetype: this.sarficurvetype_in
      });
      this.$nextTick(() => {
        this.$refs[`addInput${this.listData.length - 1}`][0].focus();
      });
    },
    // 左侧列表选择
    listDataClick(index) {
      this.itemAction = index;
      if (this.listData && this.listData.length > 0) {
        this.CetTable_1.data = this.listData[index].sarfichartvalue_model || [];
      } else {
        this.CetTable_1.data = [];
      }
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.list-edit {
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: inline-block;
  background: url("../assets/21_u2310.png") no-repeat center 2px;
  background-size: 20px 20px;
}
.list-delete {
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: inline-block;
  background: url("../assets/22_u2309.png") no-repeat center 2px;
  background-size: 20px 20px;
}
.list-tips {
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: inline-block;
  background: url("../assets/u2319.png") no-repeat center 4px;
  background-size: 16px 16px;
}
.addBtn {
  cursor: pointer;
  height: 30px;
  @include font_color(ZS);
  display: inline-block;
  @include margin_top(J1);
  @include margin_left(J2);
}
.listItem {
  margin-top: 5px;
  width: 100%;
  box-sizing: border-box;
  padding: 0 10px 0 15px;
  cursor: pointer;
  line-height: 25px;
  border-radius: 5px;
  &.action {
    @include background_color(BG4);
    @include font_color(T1);
  }
}
</style>
