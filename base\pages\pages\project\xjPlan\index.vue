<template>
  <div class="page eem-common">
    <div class="fullheight flex-column eem-min-width">
      <div class="eem-container mbJ3">
        <div class="fr flex-row">
          <ElInput
            size="small"
            class="search-input fl"
            suffix-icon="el-icon-search"
            :placeholder="$T('输入关键字以检索')"
            v-model="filterText"
            v-bind="ElInput_keyword"
            v-on="ElInput_keyword.event"
          ></ElInput>
          <div class="eem-radio fl mlJ1 lh32">
            <ElRadioGroup
              v-model="ElRadioGroup_1.value"
              v-bind="ElRadioGroup_1"
              v-on="ElRadioGroup_1.event"
            >
              <ElRadio
                v-for="item in ElRadioList_1.options_in"
                :key="item[ElRadioList_1.key]"
                :label="item[ElRadioList_1.label]"
                :disabled="item[ElRadioList_1.disabled]"
              >
                {{ item[ElRadioList_1.text] }}
              </ElRadio>
            </ElRadioGroup>
          </div>
          <div class="eem-checkbox fl mlJ1 lh32">
            <ElCheckbox
              v-model="ElCheckbox_1.value"
              v-bind="ElCheckbox_1"
              v-on="ElCheckbox_1.event"
            >
              {{ ElCheckbox_1.text }}
            </ElCheckbox>
          </div>
          <customElSelect
            size="small"
            default-first-option
            v-model="ElSelect_team.value"
            v-bind="ElSelect_team"
            v-on="ElSelect_team.event"
            class="fl mlJ1"
            :prefix_in="$T('责任班组')"
          >
            <ElOption
              v-for="item in ElOption_team.options_in"
              :key="item[ElOption_team.key]"
              :label="item[ElOption_team.label]"
              :value="item[ElOption_team.value]"
            ></ElOption>
          </customElSelect>
          <!-- refreshOrder按钮组件 -->
          <CetButton
            class="fl mlJ1"
            v-bind="CetButton_refresh"
            v-on="CetButton_refresh.event"
          ></CetButton>
          <!-- addOrder按钮组件 -->
          <CetButton
            class="fl mlJ1"
            v-bind="CetButton_add"
            v-on="CetButton_add.event"
          ></CetButton>
          <!-- 批量删除按钮组件 -->
          <CetButton
            class="fl mlJ1"
            v-if="CetButton_delete.visible_in"
            v-bind="CetButton_delete"
            v-on="CetButton_delete.event"
          ></CetButton>
          <!-- 批量禁用按钮组件 -->
          <CetButton
            class="fl mlJ1"
            v-if="CetButton_disable.visible_in"
            v-bind="CetButton_disable"
            v-on="CetButton_disable.event"
          ></CetButton>
          <!-- 批量启动按钮组件 -->
          <CetButton
            v-if="CetButton_start.visible_in"
            class="fl mlJ1"
            v-bind="CetButton_start"
            v-on="CetButton_start.event"
          ></CetButton>
          <!-- 禁用巡检对象按钮组件 -->
          <CetButton
            v-if="CetButton_inspectObj.visible_in"
            class="fl mlJ1"
            v-bind="CetButton_inspectObj"
            v-on="CetButton_inspectObj.event"
          ></CetButton>
        </div>
      </div>
      <div class="flex-auto eem-container">
        <CetTable
          style="height: 100%; text-align: right"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
          @selection-change="handleSelectionChange_out"
        >
          <ElTableColumn
            type="selection"
            width="45"
            align="center"
            header-align="center"
          ></ElTableColumn>
          <template v-for="(column, index) in Columns_Plan">
            <el-table-column
              v-if="column.custom && column.custom === 'tag'"
              v-bind="column"
              :key="index"
              class-name="font0 hand"
              label-class-name="font14"
              headerAlign="left"
              align="left"
            >
              <template slot-scope="scope">
                <el-tag
                  size="small"
                  class="text-middle font14"
                  :effect="column.tagEffect"
                  :type="
                    column.tagTypeFormatter
                      ? column.tagTypeFormatter(scope.row, scope.column)
                      : 'primary'
                  "
                >
                  {{
                    column.formatter
                      ? column.formatter(
                          scope.row,
                          scope.column,
                          scope.row[column.prop],
                          scope.$index
                        )
                      : scope.row[column.prop]
                  }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="column.custom && column.custom === 'button'"
              v-bind="column"
              :key="index"
              class-name="font0"
              label-class-name="font14"
              headerAlign="left"
              align="left"
            >
              <template slot-scope="scope">
                <span
                  class="eem-row-handle"
                  @click.stop="
                    column.onButtonClick
                      ? column.onButtonClick(scope.row, scope.$index)
                      : void 0
                  "
                >
                  {{
                    column.formatter
                      ? column.formatter(
                          scope.row,
                          scope.column,
                          scope.row[column.prop],
                          scope.$index
                        )
                      : column.text
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              v-else
              v-bind="column"
              :key="index"
              class-name="hand"
              headerAlign="left"
              align="left"
            ></el-table-column>
          </template>
          <el-table-column
            headerAlign="left"
            align="left"
            fixed="right"
            :label="$T('操作')"
            width="150"
          >
            <template slot-scope="scope">
              <span
                class="eem-row-handle fl mrJ3"
                @click="handleClick_see_out(scope.row, scope.$index)"
              >
                {{ $T("查看") }}
              </span>
              <span
                :class="{ 'eem-row-no-handle': !!scope.row.enabled }"
                class="eem-row-handle fl mrJ3"
                @click="handleClick_edit_out(scope.row, scope.$index)"
              >
                {{ $T("编辑") }}
              </span>
              <span
                :class="{
                  'eem-row-no-handle': !!scope.row.enabled,
                  rowDelete: !scope.row.enabled
                }"
                class="eem-row-delete fl"
                @click="handleClick_delete_out(scope.row, scope.$index)"
              >
                {{ $T("删除") }}
              </span>
            </template>
          </el-table-column>
        </CetTable>
      </div>
    </div>
    <CreatePlan
      @confirm_out="createPlan_confirm_out"
      :visibleTrigger_in="createPlan.visibleTrigger_in"
      :closeTrigger_in="createPlan.closeTrigger_in"
      :inputData_in="createPlan.inputData_in"
    />
    <EditPlan
      @confirm_out="editPlan_confirm_out"
      :visibleTrigger_in="editPlan.visibleTrigger_in"
      :closeTrigger_in="editPlan.closeTrigger_in"
      :inputData_in="editPlan.inputData_in"
    />
    <Detail
      @confirm_out="detail_confirm_out"
      :visibleTrigger_in="detail.visibleTrigger_in"
      :closeTrigger_in="detail.closeTrigger_in"
      :inputData_in="detail.inputData_in"
    />
    <inspectObjDialog
      v-bind="inspectObjDialog"
      v-on="inspectObjDialog.event"
    ></inspectObjDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
import common from "eem-utils/common.js";
import Detail from "./dialog/Detail";
import CreatePlan from "./dialog/CreatePlan";
import EditPlan from "./dialog/EditPlan";
import inspectObjDialog from "./dialog/inspectObjDialog";
const WORKSHEET_LEVEL_TAG_NAMES = [
  "primary",
  "red",
  "orange",
  "green",
  "yellow",
  "gray"
];
export default {
  name: "metrics",
  components: { CreatePlan, Detail, EditPlan, inspectObjDialog },
  data(vm) {
    return {
      defaultProps: {
        children: "children",
        label: "name"
      },
      // addOrder组件
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: $T("新增"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_statusTrigger_out
        }
      },
      // refresh组件
      CetButton_refresh: {
        visible_in: true,
        disable_in: false,
        title: $T("刷新"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_refresh_statusTrigger_out
        }
      },
      // 批量删除组件
      CetButton_delete: {
        visible_in: false,
        disable_in: true,
        title: $T("批量删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_delete_statusTrigger_out
        }
      },
      // 批量禁用组件
      CetButton_disable: {
        visible_in: true,
        disable_in: true,
        title: $T("批量禁用"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_disable_statusTrigger_out
        }
      },
      // 批量启动组件inspectObj
      CetButton_start: {
        visible_in: false,
        disable_in: true,
        title: $T("批量启用"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_start_statusTrigger_out
        }
      },
      // 禁用巡检对象
      CetButton_inspectObj: {
        visible_in: false,
        disable_in: false,
        title: $T("禁用巡检对象"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_inspectObj_statusTrigger_out
        }
      },
      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryInspectPlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [
            { name: "enabled_in", operator: "EQ", prop: "enabled" },
            { name: "hide_in", operator: "EQ", prop: "hide" },
            { name: "name_in", operator: "EQ", prop: "name" },
            { name: "teamId_in", operator: "EQ", prop: "teamId" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: "合计"
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {
          enabled_in: true,
          hide_in: false,
          name_in: "",
          teamId_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {},
        exportFileName: "",
        event: {},
        highlightCurrentRow: false
      },
      Columns_Plan: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 60,
          label: "#",
          sortable: false,
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "name",
          minWidth: 100,
          width: "",
          label: $T("计划名称"),
          sortable: false,
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "cycle",
          minWidth: 120,
          width: "",
          label: $T("周期"),
          sortable: false,
          showOverflowTooltip: true,
          formatter: this.filCycle_out
        },
        {
          type: "",
          prop: "inspectionSchemeName",
          minWidth: 120,
          width: "",
          label: $T("巡检方案"),
          sortable: false,
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "signInGroupName",
          minWidth: 120,
          width: "",
          label: $T("巡检路线"),
          sortable: false,
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "teamName",
          minWidth: 100,
          width: "",
          label: $T("责任班组"),
          sortable: false,
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "inspectObject",
          minWidth: 100,
          width: "",
          label: $T("巡检对象"),
          sortable: false,
          showOverflowTooltip: true,
          formatter: this.filDevicelist_out
        },
        {
          type: "",
          prop: "enabled",
          minWidth: 80,
          width: "",
          label: $T("状态"),
          sortable: false,
          showOverflowTooltip: true,
          formatter: this.filEnabled_out
        },
        {
          type: "",
          prop: "nextFireTime",
          minWidth: 120,
          width: "",
          label: $T("下次巡检时间"),
          sortable: false,
          showOverflowTooltip: true,
          formatter: this.filNextFireTime_out
        }
      ],
      ElRadioGroup_1: {
        value: 1,
        style: {},
        event: {
          change: this.ElRadioGroup_1_change_out
        }
      },
      ElRadioList_1: {
        options_in: [
          {
            id: 1,
            text: $T("启用")
          },
          {
            id: 2,
            text: $T("禁用")
          },
          {
            id: 0,
            text: $T("全部")
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      ElCheckbox_1: {
        value: false,
        text: $T("隐藏已结束的计划"),
        disabled: false,
        event: {
          change: this.ElCheckbox_1_change_out
        }
      },
      // 责任班组下拉框
      ElSelect_team: {
        value: 0,
        style: {
          width: "200px"
        },
        event: {
          change: this.teamChange_out
        }
      },
      ElOption_team: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name"
      },
      showTopology: false,
      detail: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      createPlan: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      editPlan: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      // 巡检对象弹窗
      inspectObjDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        event: {
          saveData_out: this.insepectObjDialog_saveData_out
        }
      },
      // 关键字组件
      ElInput_keyword: {
        value: "",
        event: {
          change: this.ElInput_keyword_change_out
        }
      },
      filterText: ""
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    energyTypeObj() {
      return null;
    }
  },
  watch: {},
  methods: {
    //关键字输入框查询
    ElInput_keyword_change_out(val) {
      this.CetTable_1.dynamicInput.name_in = val ? this._.cloneDeep(val) : "";
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    //启动、禁用、全部切换
    ElRadioGroup_1_change_out(val) {
      if (val === 1) {
        this.CetButton_delete.visible_in = false;
        this.CetTable_1.dynamicInput.enabled_in = true;
        this.CetButton_disable.visible_in = true;
        this.CetButton_start.visible_in = false;
      } else if (val === 2) {
        this.CetButton_delete.visible_in = true;
        this.CetTable_1.dynamicInput.enabled_in = false;
        this.CetButton_disable.visible_in = false;
        this.CetButton_start.visible_in = true;
      } else {
        this.CetButton_delete.visible_in = true;
        this.CetTable_1.dynamicInput.enabled_in = 0;
        this.CetButton_disable.visible_in = true;
        this.CetButton_start.visible_in = true;
      }
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    //点击隐藏已结束计划
    ElCheckbox_1_change_out(val) {
      this.CetTable_1.dynamicInput.hide_in = this._.cloneDeep(val);
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    // 班组下拉框切换
    teamChange_out(val) {
      this.CetTable_1.dynamicInput.teamId_in = this._.cloneDeep(val);
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    //表格点击多选框
    handleSelectionChange_out(val) {
      this.selectedData = val;
      if (val && val.length > 0) {
        this.CetButton_delete.disable_in = false;
        this.CetButton_disable.disable_in = false;
        this.CetButton_start.disable_in = false;
      } else {
        this.CetButton_delete.disable_in = true;
        this.CetButton_disable.disable_in = true;
        this.CetButton_start.disable_in = true;
      }
    },
    // 点击查看按钮
    handleClick_see_out(val) {
      this.detail.inputData_in = this._.cloneDeep(val);
      this.detail.visibleTrigger_in = new Date().getTime();
    },
    // 点击编辑按钮
    handleClick_edit_out(val) {
      if (val && val.enabled) {
        this.$message.warning($T("计划已启动，不可编辑"));
        return;
      }
      this.editPlan.inputData_in = this._.cloneDeep(val);
      this.editPlan.visibleTrigger_in = new Date().getTime();
    },
    // 点击删除按钮
    handleClick_delete_out(val) {
      var _this = this;
      if (val && val.enabled) {
        this.$message.warning($T("计划已启动，不可删除"));
        return;
      }
      this.$confirm($T("是否删除该条计划？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: function (action, instance, done) {
          if (action === "confirm") {
            _this.deleteInspectPlan_out([val.id]);
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action !== "confirm") {
            _this.$message({
              type: "info",
              message: $T("取消删除!")
            });
          }
        }
      });
    },
    // 点击新建计划按钮
    CetButton_add_statusTrigger_out(val) {
      this.createPlan.inputData_in = {};
      this.createPlan.visibleTrigger_in = new Date().getTime();
    },
    // 点击新建计划按钮
    CetButton_refresh_statusTrigger_out(val) {
      this.reset();
    },
    // 点击批量删除按钮
    CetButton_delete_statusTrigger_out(val) {
      var _this = this;
      let isOk = false;
      _this.selectedData.forEach(item => {
        if (item.enabled) {
          isOk = true;
        }
      });
      if (isOk) {
        this.$message.warning($T("计划已启动，不可批量删除"));
        return;
      }
      this.$confirm($T("是否批量删除计划？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: function (action, instance, done) {
          if (action === "confirm") {
            const idArr = [];
            _this.selectedData.forEach(item => {
              idArr.push(item.id);
            });
            _this.deleteInspectPlan_out(idArr);
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action !== "confirm") {
            _this.$message({
              type: "info",
              message: $T("取消删除!")
            });
          }
        }
      });
    },
    // 点击批量禁用按钮
    CetButton_disable_statusTrigger_out(val) {
      var _this = this;
      this.$confirm($T("是否批量禁用计划？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: function (action, instance, done) {
          if (action === "confirm") {
            _this.setPlanDisable_out();
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action !== "confirm") {
            _this.$message({
              type: "info",
              message: $T("取消禁用!")
            });
          }
        }
      });
    },
    // 点击批量启动按钮
    CetButton_start_statusTrigger_out(val) {
      var _this = this;
      if (!_this.selectedData || _this.selectedData.length === 0) {
        return;
      }
      let isOk = true;
      _this.selectedData.forEach(item => {
        if (item.finishtime && new Date(item.finishtime) < new Date()) {
          isOk = false;
        }
      });
      if (!isOk) {
        // this.$message.warning("部分计划由于已达到结束时间无法启动，请修改结束时间后重试。");
        this.$confirm(
          $T("部分计划由于已达到结束时间无法启动，请修改结束时间后重试。"),
          $T("提示"),
          {
            confirmButtonText: $T("确定"),
            cancelButtonText: $T("关闭"),
            type: "warning",
            closeOnClickModal: false,
            showClose: false,
            confirmButtonClass: "btn-custom-confirm-display",
            beforeClose: function (action, instance, done) {
              done();
            },
            callback: function (action) {}
          }
        );
        return;
      }
      this.$confirm($T("是否批量启动计划？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: function (action, instance, done) {
          if (action === "confirm") {
            _this.setPlanEnable_out();
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action !== "confirm") {
            _this.$message({
              type: "info",
              message: $T("取消启动!")
            });
          }
        }
      });
    },
    //点击禁用巡检对象
    CetButton_inspectObj_statusTrigger_out(val) {
      this.inspectObjDialog.inputData_in = {};
      this.inspectObjDialog.openTrigger_in = new Date().getTime();
    },
    //初始化
    init() {
      this.CetTable_1.dynamicInput = {
        enabled_in: true,
        hide_in: false,
        name_in: "",
        teamId_in: null
      };
      this.ElRadioGroup_1.value = 1;
      this.filterText = "";
      this.ElRadioGroup_1_change_out(1);
      this.getTeam_out();
    },
    //刷新页面数据,责任班组不刷新
    reset() {
      if (![undefined, NaN, null].includes(this.ElSelect_team.value)) {
        this.teamChange_out(this.ElSelect_team.value);
      }
    },
    //详情弹框执行
    detail_confirm_out() {
      this.reset();
    },
    //新建巡检计划弹框关闭之后执行
    createPlan_confirm_out() {
      this.reset();
    },
    //编辑巡检计划弹框结束执行
    editPlan_confirm_out() {
      this.reset();
    },
    // 事件等级标签样式格式化
    levelTagTypeFormatter(row, column) {
      const cellValue = row.status || 0;
      return WORKSHEET_LEVEL_TAG_NAMES[cellValue];
    },
    // 获取班组列表信息
    getTeam_out() {
      const _this = this;
      const params = {};
      customApi.queryInspectorTeamWithOutUser(params).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []) || [];
          let resData = [
            {
              id: 0,
              name: "全部"
            }
          ];
          resData = resData.concat(data);
          const teamId = 0;
          _this.ElOption_team.options_in = _this._.cloneDeep(resData);
          _this.ElSelect_team.value = teamId;
          _this.teamChange_out(teamId);
        }
      });
    },
    //过滤表格周期显示
    filCycle_out(row, column, cellValue, index) {
      if (row.aggregationcycle === 0 && cellValue) {
        const cycle = cellValue || "";
        let month = 0;
        let day = 0;
        let hour = 0;

        const cycleAry = cycle.split("T") || [];
        cycleAry.forEach(cycleItem => {
          let subTimeStrAry = [];
          if (cycleItem.indexOf("P") > -1) {
            subTimeStrAry = cycleItem.match(/\d+[YMD]/g) || [];
            subTimeStrAry.forEach(subTimeStr => {
              const num = parseInt(subTimeStr.slice(0, -1));
              const unit = subTimeStr.slice(-1);
              switch (unit) {
                case "Y":
                  month += 12 * num;
                  break;
                case "M":
                  month += num;
                  break;
                case "D":
                  day = num;
                  break;
              }
            });
          } else {
            subTimeStrAry = cycleItem.match(/\d+H/g) || [];
            subTimeStrAry.forEach(subTimeStr => {
              hour = parseInt(subTimeStr.slice(0, -1));
            });
          }
        });
        row.cycle$text = `${month}月${day}天${hour}小时`;
        return `${month}月${day}天${hour}小时`;
      } else {
        const list = [
          {
            value: 18,
            label: $T("只执行一次")
          },
          {
            value: 12,
            label: $T("1天")
          },
          {
            value: 13,
            label: $T("1周")
          },
          {
            value: 14,
            label: $T("1个月")
          },
          {
            value: 16,
            label: $T("半年")
          },
          {
            value: 17,
            label: $T("1年")
          }
        ];
        for (var i = 0, len = list.length; i < len; i++) {
          if (list[i].value === row.aggregationcycle) {
            row.cycle$text = list[i].label;
            return list[i].label;
          }
        }
        return "--";
      }
    },
    //过滤班组显示
    filSigngroup_out(row, column, cellValue, index) {
      console.log(cellValue);
      if (cellValue) {
        const list = this.ElOption_team.options_in || [];
        for (var i = 0, len = list.length; i < len; i++) {
          if (list[i].id === cellValue) {
            row.teamId$text = list[i].name;
            return list[i].name;
          }
        }
        return "--";
      } else {
        return "--";
      }
    },
    //过滤表格状态
    filEnabled_out(row, column, cellValue, index) {
      if (cellValue === true) {
        return $T("启用");
      } else if (cellValue === false) {
        return $T("禁用");
      } else {
        return "--";
      }
    },
    //过滤下次巡检时间
    filNextFireTime_out(row, column, cellValue, index) {
      if (cellValue) {
        return this.$moment(cellValue).format("YYYY-MM-DD HH:mm:ss");
      } else {
        return $T("已结束");
      }
    },
    //过滤巡检对象
    filDevicelist_out(row, column, cellValue, index) {
      if (
        row.deviceplanrelationship_model &&
        row.deviceplanrelationship_model.length
      ) {
        const list = row.deviceplanrelationship_model || [];
        let inspectObject = "";
        list.forEach((item, index) => {
          if (index) {
            inspectObject += " , ";
          }
          inspectObject += item.devicename;
        });
        row.inspectObject = inspectObject;
        return inspectObject;
      } else {
        return "--";
      }
    },
    // 批量删除计划
    deleteInspectPlan_out(params) {
      if (!params) {
        return;
      }
      customApi.deleteInspectPlan(params).then(res => {
        if (res.code === 0) {
          this.$message.success($T("删除成功!"));
          this.reset();
        }
      });
    },
    // 批量禁用巡检计划
    setPlanDisable_out() {
      const _this = this;
      const idArr = [];
      if (!_this.selectedData || _this.selectedData.length === 0) {
        return;
      }
      _this.selectedData.forEach(item => {
        idArr.push(item.id);
      });
      customApi.setPlanDisable(idArr).then(res => {
        if (res.code === 0) {
          this.reset();
        }
      });
    },
    // 批量启用巡检计划
    setPlanEnable_out() {
      const _this = this;
      if (!_this.selectedData || _this.selectedData.length === 0) {
        return;
      }
      const idArr = [];
      _this.selectedData.forEach(item => {
        idArr.push(item.id);
      });
      customApi.setPlanEnable(idArr).then(res => {
        if (res.code === 0) {
          this.reset();
        }
      });
    },
    // 巡检对象保存
    insepectObjDialog_saveData_out(val) {}
  },
  activated: function () {
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.page {
  height: 100%;
}
.search-input {
  width: 200px;
}
.device-Input {
  display: inline-block;
}
.check-box {
  display: inline-block;
}
.rowDelete {
  @include font_color(Sta3);
}
</style>
<style>
.btn-custom-confirm-display {
  display: none;
}

.custom-date-picker-inspect .el-picker-panel__footer .el-button--text {
  display: none !important;
}
</style>
