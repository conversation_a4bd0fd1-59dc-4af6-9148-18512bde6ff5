<template>
  <div class="page">
    <div
      v-for="(item, index) in dateArr"
      :key="index"
      :style="{ width: (1 / num) * 100 + '%' }"
      class="timeBox"
      @click="timeBoxClick(item)"
      @mousedown="timeBoxDown(item, index)"
      @mouseup="timeBoxUp(item, index)"
    >
      <div :title="item.time[0] + '-' + item.time[1]" :class="item.color"></div>
    </div>
    <div class="mark mark1">
      <div>00:00</div>
      <div>03:00</div>
      <div>06:00</div>
      <div>09:00</div>
      <div>12:00</div>
      <div>15:00</div>
      <div>18:00</div>
      <div>21:00</div>
    </div>
    <div class="mark mark2">
      <div>03:00</div>
      <div>06:00</div>
      <div>09:00</div>
      <div>12:00</div>
      <div>15:00</div>
      <div>18:00</div>
      <div>21:00</div>
      <div>24:00</div>
    </div>
  </div>
</template>
<script>
export default {
  name: "AddDaySchemeTimePicker",
  components: {},
  props: {
    visibleColor: {
      type: String,
      default: ""
    },
    inputData_in: {
      type: Array
    }
  },

  computed: {},

  data() {
    var dateArr = [];
    for (let index = 0; index < 96; index++) {
      if (index == 95) {
        dateArr.push({
          time: [
            this.$moment()
              .startOf("day")
              .minutes(index * 15)
              .format("HH:mm"),
            "24:00"
          ],
          visible: false
        });
      } else {
        dateArr.push({
          time: [
            this.$moment()
              .startOf("day")
              .minutes(index * 15)
              .format("HH:mm"),
            this.$moment()
              .startOf("day")
              .minutes((index + 1) * 15)
              .format("HH:mm")
          ],
          visible: false
        });
      }
    }
    return {
      dateArr: dateArr,
      // 一列多少格
      num: 12,
      timeBoxDownIndex: null
    };
  },
  watch: {
    inputData_in(val) {
      this.timeBoxDownIndex = null;
      var visibleArr = [];
      if (val.length > 0) {
        val.forEach(item => {
          if (item.data && item.data.length > 0) {
            item.data.forEach(ite => {
              var num;
              if (ite.split("~")[1] == "00:00") {
                // 结束时间是24:00
                num = 96;
              } else {
                num =
                  (ite.split("~")[1].split(":")[0] * 60 +
                    Number(ite.split("~")[1].split(":")[1])) /
                  15;
              }
              for (
                var i =
                  (ite.split("~")[0].split(":")[0] * 60 +
                    Number(ite.split("~")[0].split(":")[1])) /
                  15;
                i < num;
                i++
              ) {
                visibleArr.push({
                  num: i,
                  color: item.color
                });
              }
            });
          }
        });
      }
      var dateArr = [];
      for (let index = 0; index < 96; index++) {
        var objArr = visibleArr.filter(item => item.num == index);
        if (index == 95) {
          dateArr.push({
            time: [
              this.$moment()
                .startOf("day")
                .minutes(index * 15)
                .format("HH:mm"),
              "24:00"
            ],
            visible: objArr.length > 0 ? true : false,
            color: objArr.length > 0 ? objArr[0].color : null
            // visible: visibleArr.indexOf(index) != -1 ? true : false
          });
        } else {
          dateArr.push({
            time: [
              this.$moment()
                .startOf("day")
                .minutes(index * 15)
                .format("HH:mm"),
              this.$moment()
                .startOf("day")
                .minutes((index + 1) * 15)
                .format("HH:mm")
            ],
            visible: objArr.length > 0 ? true : false,
            color: objArr.length > 0 ? objArr[0].color : null
            // visible: visibleArr.indexOf(index) != -1 ? true : false
          });
        }
      }
      this.dateArr = dateArr;
    }
  },

  methods: {
    timeBoxClick(val) {
      if (val.color == this.visibleColor) {
        val.color = "";
      } else {
        val.color = this.visibleColor;
      }
      val.visible = !val.visible;
      this.currentNode = val;
      this.exportData();
    },
    timeBoxDown(val, index) {
      this.timeBoxDownFalg = true;
      this.timeBoxDownIndex = index;
    },
    timeBoxUp(val, index) {
      if (this.timeBoxDownFalg) {
        if (this.timeBoxDownIndex == index) {
          return;
        }
        for (let i = 0; i < this.dateArr.length; i++) {
          if (this.timeBoxDownIndex < index) {
            if (i >= this.timeBoxDownIndex && i <= index) {
              if (this.dateArr[i].color == this.visibleColor) {
                this.dateArr[i].color = "";
              } else {
                this.dateArr[i].color = this.visibleColor;
              }
            }
          } else {
            if (i <= this.timeBoxDownIndex && i >= index) {
              if (this.dateArr[i].color == this.visibleColor) {
                this.dateArr[i].color = "";
              } else {
                this.dateArr[i].color = this.visibleColor;
              }
            }
          }
        }
        this.exportData();
        this.timeBoxDownIndex = null;
        this.timeBoxDownFalg = false;
      }
    },
    resetNode() {
      this.currentNode.visible = false;
      this.exportData();
    },
    // 计算出选择的周期
    exportData() {
      var outArr = [];
      for (let i = 0; i < 5; i++) {
        var arr = [],
          str = "",
          str2 = "";
        this.dateArr.forEach((item, index) => {
          if (item.color == "color" + i) {
            if (item.color && !str) {
              str = item.time[0];
              str2 = item.time[1];
            } else if (item.color && str) {
              if (item.time[0] == str2) {
                str2 = item.time[1];
              } else {
                arr.push(str + "~" + str2);
                str = item.time[0];
                str2 = item.time[1];
              }
            }
          }
          if (index == 95 && str) {
            arr.push(str + "~" + str2);
          }
        });
        outArr.push(arr);
      }
      this.$emit("confirm_out", outArr);
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 200px;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  box-sizing: border-box;
  padding: 0 40px 0 40px;
}
.timeBox {
  box-sizing: border-box;
  padding: 1px;
  height: 24px;
  cursor: pointer;
  & > div {
    width: 100%;
    height: 100%;
    background: #ccc;
    user-select: none;
  }
  .color0 {
    background: rgb(0, 151, 86);
  }
  .color1 {
    background: rgb(253, 202, 1);
  }
  .color2 {
    background: rgb(0, 152, 254);
  }
  .color3 {
    background: rgb(253, 51, 254);
  }
  .color4 {
    background: rgb(1, 82, 217);
  }
}
.mark {
  & > div {
    margin-bottom: 1px;
  }
  position: absolute;
  height: 24px;
  line-height: 24px;
}
.mark1 {
  left: 0;
}
.mark2 {
  right: 0;
}
</style>
