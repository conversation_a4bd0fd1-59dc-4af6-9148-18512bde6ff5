import fetch from "eem-utils/fetch";
import { getCustomParameter } from "./common/common.js";
import store from "@/store";
import { encryptAttributes, decryptAttributes } from "eem-utils/crypto.js";
const version = "v1";

function processRequest(data) {
  return data;
}

function processResponse(response) {
  // 对 response 进行任意转换处理, response结构
  //   {
  //     // `data` 由服务器提供的响应
  //     data: {},

  //     // `status` 来自服务器响应的 HTTP 状态码
  //     status: 200,

  //     // `statusText` 来自服务器响应的 HTTP 状态信息
  //     statusText: 'OK',

  //     // `headers` 服务器响应的头
  //     headers: {},

  //     // `config` 是为请求提供的配置信息
  //     config: {}
  //   }
  return response;
}
/**
 *
 * @param {*巡检人员管理} data
 */
//巡检人员密码修改
export function editInspectorPassword(data) {
  delete data.modelLabel;
  delete data.roleId;
  delete data._checkPassword;
  delete data.checkPassword;
  return fetch({
    url: `/eem-service/${version}/inspector/password`,
    method: "PATCH",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
//巡检人员巡检确认密码修改,弃用
export function editInspectorCheckPassword(data) {
  delete data.modelLabel;
  delete data.roleId;
  delete data._checkPassword;
  data.password = data.checkPassword;
  delete data.checkPassword;
  return fetch({
    url: `/eem-service/${version}/inspector/checkPassword`,
    method: "PATCH",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
//修改巡检人员密码，密码需要进行加密
export function editInspectorCheckPasswordSecurity(data) {
  delete data.modelLabel;
  delete data.roleId;
  delete data._checkPassword;
  return fetch({
    url: `/eem-service/${version}/inspector/checkPassword/security`,
    method: "PATCH",
    // headers: { "User-ID": 1 },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
//巡检人员注册,弃用
export function addInspectorRegister(data) {
  delete data.modelLabel;
  delete data.roleId;
  delete data._checkPassword;
  return fetch({
    url: `/eem-service/${version}/inspector/register`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
//巡检人员注册，密码进行加密
export function addInspectorRegisterSecurity(data) {
  delete data.modelLabel;
  delete data.roleId;
  delete data._checkPassword;
  // 需要进行加密
  data = encryptAttributes(data, ["name", "checkPassword", "password"]);
  return fetch({
    url: `/eem-service/${version}/inspector/register/security`,
    method: "POST",
    // headers: { "User-ID": 1 },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
//查询巡检角色信息
export function getInspectorRoles(data) {
  return fetch({
    url: `/eem-service/${version}/inspector/roles?tenantId=${store.state.tenantId}`,
    method: "GET",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
//查询巡检角色信息
export function queryInspectorRoles(data) {
  return fetch({
    url: `/eem-service/${version}/inspector/roles`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
//查询班组
export function queryInspectorTeam(data) {
  return fetch({
    url: `/eem-service/${version}/inspector/team?tenantId=${store.state.tenantId}`,
    method: "GET",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}
//新增班组
export function addInspectorTeam(data) {
  delete data.modelLabel;
  return fetch({
    url: `/eem-service/${version}/inspector/team`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
//编辑班组
export function editInspectorTeam(data) {
  delete data.modelLabel;
  return fetch({
    url: `/eem-service/${version}/inspector/team`,
    method: "PATCH",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
//查询班组列表
export function queryInspectorTeamWithOutUser(data) {
  return fetch({
    url: `/eem-service/${version}/inspector/teamWithOutUser?tenantId=${store.state.tenantId}`,
    method: "GET",
    // headers: { "User-ID": 1 },
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}
//查询班组，不限制班组
export function queryInspectorTeamWithOutUserNoAuth(data) {
  return fetch({
    url: `/eem-service/${version}/inspector/teamWithOutUser/noAuth?tenantId=${store.state.tenantId}`,
    method: "GET",
    // headers: { "User-ID": 1 },
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

function parseAttributes(data) {
  if (typeof data === "string") {
    data = JSON.parse(data);
  }
  const response = _.get(data, "data", []) || [];
  let newData = response.map(item => {
    // 需要进行解密
    return decryptAttributes(item, ["name", "nicName", "email", "mobilePhone"]);
  });
  return {
    ...data,
    data: newData
  };
}

//巡检人员查询
export function queryInspectorUser(data) {
  return fetch({
    url: `/eem-service/${version}/inspector/user`,
    method: "POST",
    transformResponse: [parseAttributes], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
//巡检人员编辑
export function editInspectorUser(data) {
  delete data.modelLabel;
  delete data.roleId;
  // 需要进行加密
  data = encryptAttributes(data, [
    "name",
    "rootName",
    "rootPassword",
    "userName",
    "newPassword"
  ]);
  return fetch({
    url: `/eem-service/${version}/inspector/user`,
    method: "PATCH",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
// 删除用户
export function deleteInspectorUser(data) {
  const userName = encodeURIComponent(data.name);
  return fetch({
    url: `/eem-service/${version}/inspector/user?id=${data.id}&name=${userName}&tenantId=${store.state.tenantId}`,
    method: "DELETE",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}
// 删除班组
export function deleteInspectorTeam(data) {
  const userName = encodeURIComponent(data.name);
  return fetch({
    url: `/eem-service/${version}/inspector/team?id=${data.id}&name=${userName}&tenantId=${store.state.tenantId}`,
    method: "DELETE",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

//巡检人员查询
export function queryImportItem(data, tenantId) {
  return fetch({
    url: `/eem-service/${version}/inspector/importItem?tenantId=${tenantId}`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

/**
 *
 * @param {*巡检工单管理} data
 */

//查询巡检工单
export function queryInspectorWorkOrder(data) {
  var newData = {
    teamId: 0,
    signPointId: 0,
    code: "",
    abnormalTypes: [0],
    endTime: 0,
    startTime: 0,
    workSheetStatus: 0,
    page: data.rootCondition.page,
    tenantId: store.state.tenantId
  };

  var expressions = _.get(data, "rootCondition.filter.expressions");
  if (!_.isEmpty(expressions)) {
    newData.code = getCustomParameter(expressions, "code") || "";
    newData.teamId = getCustomParameter(expressions, "teamId");
    newData.signPointId = getCustomParameter(expressions, "signPointId");
    newData.abnormalTypes = getCustomParameter(expressions, "abnormalTypes");
    newData.endTime = getCustomParameter(expressions, "endTime");
    newData.startTime = getCustomParameter(expressions, "startTime");
    newData.workSheetStatus = getCustomParameter(
      expressions,
      "workSheetStatus"
    );
  }
  if (newData.workSheetStatus !== 7) {
    delete newData.abnormalTypes;
  }
  return fetch({
    url: `/eem-service/${version}/workorder/inspector/workOrder`,
    method: "POST",
    // headers: { "User-ID": 1 },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(newData)
  });
}
//创建或者修改巡检工单
export function createInspectorWorkOrder(data) {
  delete data.deviceListName;
  delete data.modelLabel;
  data.executetimeplan = new Date(data.executetimeplan).getTime();
  if (data.timeconsumeplan) {
    data.timeconsumeplan = data.timeconsumeplan * 1000 * 60 * 60;
  }
  return fetch({
    url: `/eem-service/${version}/workorder/inspector/workOrder`,
    method: "PUT",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

//查询指定巡检工单
export function queryWorkOrderByCode(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/inspector/workOrder/${data.code}`,
    method: "GET",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}
//根据工单id查询指定巡检工单
export function queryWorkOrderById(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/inspector/${data.workOrderId}`,
    method: "GET",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}
//审核工单
export function toWorkOrderCheck(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/inspector/workOrder/check`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
//批量审核工单
export function toWorkOrderCheckBatch(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/inspector/workOrder/check/batch`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
//暂存审核信息
export function toWorkOrderCheckStash(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/inspector/workOrder/check/stash`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
//查询暂存审核信息
export function getWorkOrderCheckStash(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/inspector/workOrder/check/stash/${data.code}`,
    method: "GET",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}
//统计工单数量
export function queryWorkOrderCount(data) {
  return fetch({
    url: `/eem-service/${version}/workorder/inspector/workOrder/count`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

/**
 *
 * @param {*巡检工单管理} data
 */

//新增巡检计划
export function addInspectPlan(data) {
  delete data.deviceListName;
  delete data.modelLabel;
  data.executetime = new Date(data.executetime).getTime();
  if (data.finishtime) {
    data.finishtime = new Date(data.finishtime).getTime();
  }
  if (data.timeconsumeplan) {
    data.timeconsumeplan = data.timeconsumeplan * 1000 * 60 * 60;
  }
  return fetch({
    url: `/eem-service/${version}/inspect/plan`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}

//删除巡检计划
export function deleteInspectPlan(data) {
  return fetch({
    url: `/eem-service/${version}/inspect/plan`,
    method: "DELETE",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
//批量禁用巡检计划
export function setPlanDisable(data) {
  return fetch({
    url: `/eem-service/${version}/inspect/plan/disable`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
//批量启用巡检计划
export function setPlanEnable(data) {
  return fetch({
    url: `/eem-service/${version}/inspect/plan/enable`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
//更新巡检计划
export function editInspectPlan(data) {
  delete data.deviceListName;
  delete data.modelLabel;
  data.executetime = new Date(data.executetime).getTime();
  if (data.finishtime) {
    data.finishtime = new Date(data.finishtime).getTime();
  }
  if (data.timeconsumeplan) {
    data.timeconsumeplan = data.timeconsumeplan * 1000 * 60 * 60;
  }
  return fetch({
    url: `/eem-service/${version}/inspect/plan`,
    method: "PATCH",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: processRequest(data)
  });
}
//查询巡检计划
export function queryInspectPlan(data) {
  var newData = {
    enabled: true,
    hide: false,
    name: "",
    page: data.rootCondition.page,
    teamId: 0,
    tenantId: store.state.tenantId
  };

  var expressions = _.get(data, "rootCondition.filter.expressions");
  if (!_.isEmpty(expressions)) {
    const enabled = getCustomParameter(expressions, "enabled");
    if (enabled === 0) {
      newData.enabled = null;
    } else {
      newData.enabled = enabled;
    }
    newData.hide = getCustomParameter(expressions, "hide");
    newData.name = getCustomParameter(expressions, "name") || "";
    newData.teamId = getCustomParameter(expressions, "teamId");
  }
  if (!newData.teamId) {
    delete newData.teamId;
  }
  return fetch({
    url: `/eem-service/${version}/inspect/plan/query`,
    method: "POST",
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data: newData
  });
}

//其他页面接口引用

// 查询巡检方案
export function queryInspectSchemeXJ(data) {
  return fetch({
    url: `/eem-service/${version}/inspect/scheme/query`,
    method: "POST",
    data: processRequest(data)
  });
}

// 签到点分组
export function groupManageXJ(data) {
  return fetch({
    url: `/eem-service/${version}/signin/group`,
    method: "GET",
    data
  });
}

//查询指定巡检对象
export function querySigninEquipmentXJ(data, signInGroup) {
  return fetch({
    url: `/eem-service/${version}/signin/equipment?signInGroup=${signInGroup}`,
    method: "POST",
    // headers: { "User-ID": 1 },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    data
  });
}

//查询巡检方案参数
export function querySchemeDetailXJ(data) {
  return fetch({
    url: `/eem-service/${version}/inspect/scheme/detail?inspectionSchemeId=${data.inspectionschemeid}`,
    method: "GET",
    transformResponse: [processResponse] //对接口返回的数据结构进行处理
  });
}

// 根据项目查询签到点信息
export function getSignInPointByProjectXJ(data) {
  return fetch({
    url: `/eem-service/${version}/signin/signInPointByProject`,
    method: "GET",
    // headers: { "User-ID": 1 },
    data
  });
}
