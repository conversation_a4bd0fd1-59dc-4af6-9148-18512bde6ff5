<template>
  <div class="page eem-common">
    <el-container style="height: 100%; padding: 0px">
      <el-aside
        width="315px"
        class="eem-aside"
        style="height: 100%; position: relative; z-index: 100"
      >
        <CetTree
          :selectNode.sync="CetTree_leftTree.selectNode"
          :checkedNodes.sync="CetTree_leftTree.checkedNodes"
          :searchText_in.sync="CetTree_leftTree.searchText_in"
          v-bind="CetTree_leftTree"
          v-on="CetTree_leftTree.event"
          style="height: 100%"
        ></CetTree>
      </el-aside>
      <el-container class="fullheight mlJ3 flex-column" style="overflow: auto">
        <el-header
          height="auto"
          class="eem-container mbJ3 eem-min-width"
          style="display: flex; justify-content: flex-end; align-items: center"
        >
          <customElSelect
            class="fr mrJ1"
            v-model="ElSelect_productType.value"
            v-bind="ElSelect_productType"
            v-on="ElSelect_productType.event"
            :prefix_in="$T('产品类型')"
          >
            <ElOption
              v-for="item in ElOption_productType.options_in"
              :key="item[ElOption_productType.key]"
              :label="item[ElOption_productType.label]"
              :value="item[ElOption_productType.value]"
              :disabled="item[ElOption_productType.disabled]"
            ></ElOption>
          </customElSelect>
          <customElSelect
            class="fr mrJ1"
            v-model="ElSelect_energyType.value"
            v-bind="ElSelect_energyType"
            v-on="ElSelect_energyType.event"
            :prefix_in="$T('能源类型')"
          >
            <ElOption
              v-for="item in ElOption_energyType.options_in"
              :key="item[ElOption_energyType.key]"
              :label="item[ElOption_energyType.label]"
              :value="item[ElOption_energyType.value]"
              :disabled="item[ElOption_energyType.disabled]"
            ></ElOption>
          </customElSelect>
          <time-tool
            class="fr"
            :typeID="14"
            :val.sync="startTime"
            @change="changeQueryTime"
            :timeType_in="timeType"
          ></time-tool>
        </el-header>
        <el-container class="flex-auto flex-column eem-min-width">
          <div style="flex: 5" class="eem-container flex-column">
            <!-- 单位成本趋势 -->
            <headerSpot>
              {{ $T("单位成本趋势") }}
            </headerSpot>
            <CetChart
              class="mtJ3 flex-auto"
              v-bind="CetChart_electrictyCost"
            ></CetChart>
          </div>
          <div style="flex: 4" class="proportion mtJ3">
            <div class="flex-row fullheight" style="min-height: 230px">
              <div class="flex-auto fullheight eem-container flex-column">
                <headerSpot class="mbJ3">
                  {{ $T("单位成本概览") }}
                </headerSpot>
                <div class="proportion-item flex-auto avg-cost flex-column">
                  <div class="cost-item mbJ1">
                    <span>{{ $T("时间") }}:</span>
                    &emsp;
                    <span>{{ selectTime }}</span>
                  </div>
                  <div class="cost-item mbJ1">
                    <span>{{ $T("单位成本") }}:</span>
                    &emsp;
                    <span>
                      {{ formatNumberWithPrecision(unitCostView, 2) }}
                    </span>
                    <span>{{ tbLabel.unit }}</span>
                  </div>
                  <div class="compare flex-auto">
                    <el-row
                      :gutter="20"
                      class="fullheight"
                      style="
                        display: flex;
                        justify-content: center;
                        position: relative;
                      "
                    >
                      <span class="fs16 preText tip">{{ $T("同比") }}</span>
                      <el-col
                        :span="12"
                        class="fullheight"
                        style="position: relative"
                      >
                        <CetChart
                          v-bind="CetChart_avgcost1"
                          style="position: absolute; top: 0; left: 0"
                        ></CetChart>
                        <div class="label">
                          <el-tooltip
                            :content="
                              _.isEmpty(tbLabel)
                                ? ''
                                : tbLabel.price + tbLabel.unit
                            "
                            effect="light"
                          >
                            <div class="price text-ellipsis">
                              <span class="fs20">{{ tbLabel.price }}</span>
                            </div>
                          </el-tooltip>
                          <div class="percent">
                            <el-tooltip
                              :content="tbLabel.percent"
                              effect="light"
                            >
                              <span class="text-ellipsis">
                                {{ tbLabel.percent }}
                              </span>
                            </el-tooltip>
                            <img :src="tbLabel.src" v-if="tbLabel.src" alt="" />
                          </div>
                        </div>
                      </el-col>
                      <el-col
                        :span="12"
                        class="fullheight"
                        style="position: relative"
                        v-if="queryTime.aggregationCycle === 14"
                      >
                        <span class="fs16 lastText tip">{{ $T("环比") }}</span>
                        <CetChart
                          v-bind="CetChart_avgcost2"
                          style="position: absolute; top: 0; left: 0"
                        ></CetChart>
                        <div class="label">
                          <el-tooltip
                            :content="
                              _.isEmpty(hbLabel)
                                ? ''
                                : hbLabel.price + hbLabel.unit
                            "
                            effect="light"
                          >
                            <div class="price text-ellipsis">
                              <span class="fs20">{{ hbLabel.price }}</span>
                            </div>
                          </el-tooltip>
                          <div class="percent">
                            <el-tooltip
                              :content="hbLabel.percent"
                              effect="light"
                            >
                              <span class="text-ellipsis">
                                {{ hbLabel.percent }}
                              </span>
                            </el-tooltip>
                            <img :src="hbLabel.src" v-if="hbLabel.src" alt="" />
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
              <div
                class="flex-auto fullheight eem-container flex-column mlJ3 mrJ3"
              >
                <headerSpot class="mbJ3">
                  {{ $T("单位成本占比") }}
                </headerSpot>
                <div class="proportion-item flex-auto">
                  <div class="cost-item mbJ1">
                    <span>{{ $T("时间") }}:</span>
                    &emsp;
                    <span>{{ selectTime }}</span>
                  </div>
                  <CetChart
                    v-if="CetChart_energyCost.options.series[0].data.length"
                    v-bind="CetChart_energyCost"
                    style="height: calc(100% - 34px)"
                  ></CetChart>
                  <div class="itemBox-empty" v-else>{{ $T("暂无数据") }}</div>
                </div>
              </div>
              <div class="flex-auto fullheight eem-container flex-column">
                <headerSpot class="mbJ3">{{ $T("单位成本") }} TOP5</headerSpot>
                <div class="proportion-item flex-auto">
                  <div class="cost-item mbJ1">
                    <span>{{ $T("时间") }}:</span>
                    &emsp;
                    <span>{{ selectTime }}</span>
                  </div>
                  <template v-if="costList.length">
                    <div class="top-unit">
                      <span class="fr">
                        <span>{{ $T("单位") }}:</span>
                        &emsp;
                        <span>{{ costList[0].unit }}</span>
                      </span>
                    </div>
                    <div class="top">
                      <div
                        class="top-item"
                        v-for="(item, index) in costList"
                        :key="index"
                      >
                        <div class="label fl mr10 text-ellipsis fullheight">
                          {{ item.objectName }}
                        </div>
                        <el-tooltip
                          :content="
                            item.objectName +
                            ':' +
                            formatNumberWithPrecision(item.value, 2) +
                            item.unit
                          "
                          effect="light"
                        >
                          <div class="costVal fl fullheight">
                            <div
                              class="item fullheight"
                              :style="{
                                width: (item.value / costMax) * 100 + '%',
                                background: colors[index]
                              }"
                            ></div>
                            <span
                              class="text-ellipsis ml5"
                              :style="{
                                left: (item.value / costMax) * 100 + '%'
                              }"
                            >
                              {{ formatNumberWithPrecision(item.value, 2) }}
                            </span>
                          </div>
                        </el-tooltip>
                      </div>
                    </div>
                  </template>
                  <div class="itemBox-empty" v-else>{{ $T("暂无数据") }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-container>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import common from "eem-utils/common";
import customApi from "@/api/custom";
import TimeTool from "eem-components/TimeTool.vue";
import TREE_PARAMS from "@/store/treeParams.js";
const currentTheme = localStorage.getItem("omega_theme");
const isLightTheme = currentTheme === "light";
let colors = ["#0d86ff", "#19e9e9", "#26ec86", "#2485c0", "#8680f6"];
if (isLightTheme) {
  colors = ["#29b061", "#9D6630", "#FFC24C", "#2B71C3", "#9B2222"];
}

export default {
  name: "electricityCostAnalysis",
  components: {
    TimeTool
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    costMax() {
      return this._.max(this.costList.map(item => item.value)) * 1.2;
    },
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },
  data(vm) {
    return {
      colors,
      ElSelect_productType: {
        value: "",
        style: {
          width: "200px"
        },
        size: "small",
        event: {
          change: this.ElSelect_productType_change_out
        }
      },
      ElOption_productType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_energyType: {
        value: "",
        style: {
          width: "200px"
        },
        size: "small",
        event: {
          change: this.ElSelect_energyType_change_out
        }
      },
      ElOption_energyType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // leftTree树组件
      CetTree_leftTree: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_leftTree_currentNode_out,
          parentList_out: this.CetTree_leftTree_parentList_out,
          checkedNodes_out: this.CetTree_leftTree_checkedNodes_out,
          halfCheckNodes_out: this.CetTree_leftTree_halfCheckNodes_out,
          allCheckNodes_out: this.CetTree_leftTree_allCheckNodes_out
        }
      },
      currentNode: null, // 当前点击树节点
      startTime: new Date().getTime(),
      queryTime: {},
      timeType: [
        {
          type: "month",
          text: $T("月"),
          typeID: 14,
          number: 1,
          unit: "M"
        },
        {
          type: "year",
          text: $T("年"),
          typeID: 17,
          number: 1,
          unit: "y"
        }
      ],
      // electrictyCost组件
      CetChart_electrictyCost: {
        //组件输入项
        inputData_in: null,
        options: {}
      },
      // 单位成本占比
      CetChart_energyCost: {
        //组件输入项
        inputData_in: null,
        options: {
          legend: {
            type: "scroll",
            left: 10,
            bottom: -5
          },
          tooltip: {
            trigger: "item",
            formatter(params) {
              return (
                params.name +
                ": " +
                Number(params.value).toFixed(2) +
                (params.data.unit || "") +
                "(" +
                Number(params.percent).toFixed(2) +
                "%)"
              );
            }
          },
          series: [
            {
              name: "",
              type: "pie",
              radius: "65%",
              center: ["50%", "48%"],
              label: {
                show: true,
                textStyle: {
                  fontSize: 12
                },
                formatter(params) {
                  return (
                    params.name + ": " + Number(params.percent).toFixed(2) + "%"
                  );
                }
              },
              data: []
            }
          ]
        }
      },
      unitCostView: null, // 单位成本
      tbLabel: {}, // 同比数据
      hbLabel: {}, // 环比数据
      // 同比
      CetChart_avgcost1: {
        //组件输入项
        inputData_in: null,
        options: {
          series: [
            {
              name: $T("同比"),
              type: "pie",
              radius: ["65%", "85%"],
              avoidLabelOverlap: false,
              emphasis: {
                scale: false
              },
              label: {
                position: "center",
                rich: {
                  a: {
                    color: "#00fff9",
                    fontSize: 24
                  },
                  b: {
                    color: "#00fff9"
                  },
                  c: {
                    fontSize: 16,
                    color: "#fff"
                  },
                  d: {
                    width: 14,
                    backgroundColor: {
                      image: "/static/assets/icons/arrow_up.png"
                    }
                  }
                }
              },
              labelLine: {
                show: false
              },
              data: []
            }
          ]
        }
      },
      // 环比
      CetChart_avgcost2: {
        //组件输入项
        inputData_in: null,
        options: {
          series: [
            {
              name: $T("同比"),
              type: "pie",
              radius: ["65%", "85%"],
              avoidLabelOverlap: false,
              emphasis: {
                scale: false
              },
              label: {
                position: "center",
                rich: {
                  a: {
                    color: "#00fff9",
                    fontSize: 24
                  },
                  b: {
                    color: "#00fff9"
                  },
                  c: {
                    fontSize: 16,
                    color: "#fff"
                  },
                  d: {
                    width: 14,
                    backgroundColor: {
                      image: "/static/assets/icons/arrow_up.png"
                    }
                  }
                }
              },
              labelLine: {
                show: false
              },
              data: []
            }
          ]
        }
      },
      selectTime: "",
      costList: [] // 单位成本top5列表
    };
  },
  methods: {
    // product输出,方法名要带_out后缀
    ElSelect_productType_change_out(val) {
      this.getAllData();
    },
    // energyType输出,方法名要带_out后缀
    ElSelect_energyType_change_out(val) {
      this.getAllData();
    },

    // leftTree 输出
    CetTree_leftTree_currentNode_out: _.debounce(function (val) {
      this.currentNode = this._.cloneDeep(val);
      this.getAllData();
    }),
    CetTree_leftTree_parentList_out(val) {},
    CetTree_leftTree_checkedNodes_out(val) {},
    CetTree_leftTree_halfCheckNodes_out(val) {},
    CetTree_leftTree_allCheckNodes_out(val) {},
    changeQueryTime({ val, timeOption }) {
      const date = this.$moment(val);
      this.queryTime = {
        aggregationCycle: timeOption.typeID,
        startTime: date.startOf(timeOption.unit).valueOf(),
        endTime: date.endOf(timeOption.unit).valueOf() + 1
      };
      const mothStr = this.language ? "YYYY-MM" : "YYYY年MM月";
      const yearStr = this.language ? "YYYY" : "YYYY年";
      this.selectTime =
        timeOption.typeID === 14
          ? date.format(mothStr)
          : timeOption.typeID === 17
          ? date.format(yearStr)
          : "--";
      this.getAllData();
    },
    getTreeData() {
      this.CetTree_leftTree.inputData_in = [];
      const data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.autoManagement,
        treeReturnEnable: true
      };
      customApi.getNodeTree(data).then(res => {
        if (res.code === 0) {
          this.CetTree_leftTree.inputData_in = res.data;
          if (res.data && res.data.length) {
            this.CetTree_leftTree.selectNode = res.data[0];
          }
        }
      });
    },
    formatNumberWithPrecision: common.formatNumberWithPrecision,
    formatterDate(cellValue, formatStr = "YYYY-MM-DD HH:mm:ss") {
      if (cellValue) {
        return this.$moment(cellValue).format(formatStr);
      } else if (cellValue === 0 || cellValue === "") {
        return cellValue;
      } else {
        return "--";
      }
    },
    // 获取项目产品类型
    async getProductType() {
      this.ElOption_productType.options_in = [];
      await customApi
        .queryProductList({ projectId: this.projectId })
        .then(res => {
          if (res.code === 0 && res.data && res.data.length > 0) {
            const selectData = res.data.map(item => {
              return {
                id: item.producttype,
                text: item.name
              };
            });
            this.ElOption_productType.options_in = selectData;
            this.ElSelect_productType.value = selectData[0].id;
          }
        });
    },
    // 获取项目能源类型
    async getProjectEnergy() {
      this.ElOption_energyType.options_in = [];
      await customApi.getProjectEnergy(this.projectId).then(res => {
        if (res.code === 0 && res.data && res.data.length > 0) {
          let selectData = [];
          // 过滤碳排放（CO2）
          res.data.forEach(item => {
            if (![18, 22].includes(item.energytype)) {
              selectData.push({
                id: item.energytype,
                text: item.name
              });
            }
          });
          this.ElOption_energyType.options_in = selectData;
          this.ElSelect_energyType.value = selectData[0].id;
        }
      });
    },
    getAllData() {
      this.CetChart_electrictyCost.options = {};
      this.CetChart_energyCost.options.series[0].data = [];
      this.unitCostView = null;
      this.tbLabel = {};
      this.hbLabel = {};
      this.costList = [];
      if (
        !this.currentNode ||
        !this.ElSelect_productType.value ||
        !this.ElSelect_energyType.value
      )
        return;
      const params = {
        objectId: this.currentNode.id,
        objectLabel: this.currentNode.modelLabel,
        productType: this.ElSelect_productType.value,
        energyType: this.ElSelect_energyType.value,
        ...this.queryTime
      };
      this.getUnitObjectCosttrend(params);
      this.getUnitObjectCostView(params);
      this.getUnitObjectCostPercent(params);
    },
    //过滤获取图表x轴对应值
    getAxixs(date, type) {
      if (type === 14) {
        return this.$moment(date).format("DD");
      } else if (type === 17) {
        return this.$moment(date).format("YYYY/MM");
      }
    },
    // 单位成本图例综合成本、同比、环比增加悬浮提示
    formatLegend(name, startTime, endTime) {
      const cycle = this.queryTime.aggregationCycle;
      let str = name + ":";
      const mothStr = this.language ? "YYYY-MM" : "YYYY年MM月";
      const dayStr = this.language ? "YYYY-MM-DD" : "YYYY年MM月DD日";
      const formatStr = cycle === 17 ? mothStr : dayStr;
      const unit = cycle === 17 ? "M" : "d";
      if (name === $T("单位成本")) {
        str +=
          this.formatterDate(startTime, formatStr) +
          "-" +
          this.formatterDate(this.$moment(endTime).add(1, unit), formatStr);
      }
      if (name === $T("同比")) {
        str +=
          this.formatterDate(
            this.$moment(startTime).subtract(1, "Y"),
            formatStr
          ) +
          "-" +
          this.formatterDate(
            this.$moment(endTime).add(1, unit).subtract(1, "Y"),
            formatStr
          );
      }
      if (name === $T("环比")) {
        str +=
          this.formatterDate(
            this.$moment(startTime).subtract(1, "M"),
            formatStr
          ) +
          "-" +
          this.formatterDate(startTime, formatStr);
      }
      return str;
    },
    // 查单位成本趋势图
    getUnitObjectCosttrend(params) {
      const _this = this;
      customApi.queryUnitObjectCostTrend(params).then(res => {
        if (res.code === 0 && res.data.data && res.data.data.length) {
          // 处理x轴
          const xAxisData = [];
          // 自然周期和非自然周期返回的开始时间和结束时间
          const startTime = res.data.data[0].time;
          const endTime = res.data.data[res.data.data.length - 1].time;
          res.data.data.forEach(item => {
            xAxisData.push(
              this.getAxixs(item.time, this.queryTime.aggregationCycle)
            );
          });
          if (this.queryTime.aggregationCycle === 14) {
            this.CetChart_electrictyCost.options = {
              toolbox: {
                top: 30,
                right: 30,
                feature: {
                  saveAsImage: {}
                }
              },
              tooltip: {
                trigger: "axis",
                formatter(params) {
                  return _this.formatTooltip(params, res.data.unit);
                }
              },
              legend: {
                top: 10,
                tooltip: {
                  show: true,
                  formatter(params) {
                    return _this.formatLegend(params.name, startTime, endTime);
                  }
                }
              },
              grid: {
                left: "3%",
                right: "4%",
                bottom: "3%",
                containLabel: true
              },
              xAxis: {
                type: "category",
                name: $T("天数"),
                nameLocation: "end",
                data: xAxisData,
                axisPointer: {
                  type: "shadow"
                }
              },
              yAxis: {
                type: "value",
                name: res.data.unit,
                nameTextStyle: {
                  align: "right"
                }
              },
              series: [
                {
                  name: $T("单位成本"),
                  type: "bar",
                  barMaxWidth: 30,
                  data: res.data.data
                },
                {
                  name: $T("同比"),
                  type: "line",
                  data: res.data.tbData,
                  smooth: true
                },
                {
                  name: $T("环比"),
                  type: "line",
                  data: res.data.hbData,
                  smooth: true
                }
              ]
            };
          } else {
            this.CetChart_electrictyCost.options = {
              toolbox: {
                top: 30,
                right: 30,
                feature: {
                  saveAsImage: {}
                }
              },
              tooltip: {
                trigger: "axis",
                formatter(params) {
                  return _this.formatTooltip(params, res.data.unit);
                }
              },
              legend: {
                top: 10,
                tooltip: {
                  show: true,
                  formatter(params) {
                    return _this.formatLegend(params.name, startTime, endTime);
                  }
                }
              },
              grid: {
                left: "3%",
                right: "4%",
                bottom: "3%",
                containLabel: true
              },
              xAxis: {
                type: "category",
                name: $T("月份"),
                nameLocation: "end",
                data: xAxisData,
                axisPointer: {
                  type: "shadow"
                }
              },
              yAxis: {
                type: "value",
                name: res.data.unit,
                nameTextStyle: {
                  align: "right"
                }
              },
              series: [
                {
                  name: $T("单位成本"),
                  type: "bar",
                  barMaxWidth: 30,
                  data: res.data.data
                },
                {
                  name: $T("同比"),
                  type: "line",
                  data: res.data.tbData,
                  smooth: true
                }
              ]
            };
          }
        }
      });
    },
    // 单位成本趋势tooltip格式化
    formatTooltip(params, unit) {
      if (!params[0].data.time) return;
      const cycle = this.queryTime.aggregationCycle;
      const formatStr =
        cycle === 14 ? "YYYY-MM-DD" : cycle === 17 ? "YYYY-MM" : "";
      let str = this.$moment(params[0].data.time).format(formatStr) + "<br />";
      params.forEach(item => {
        str +=
          item.marker +
          item.seriesName +
          ": " +
          (item.data.value || item.data.value === 0
            ? Number(item.data.value).toFixed(2)
            : "--") +
          "(" +
          unit +
          ")" +
          "<br />";
      });
      return str;
    },
    // 单位成本概览
    getUnitObjectCostView(params) {
      customApi.queryUnitObjectCostView(params).then(res => {
        if (res.code === 0) {
          this.unitCostView = res.data.unitCostValue;
          // 同比
          const tbRate = Math.abs(res.data.tb);
          if (tbRate > 1) {
            this.CetChart_avgcost1.options.series[0].data = [1];
          } else {
            this.CetChart_avgcost1.options.series[0].data = [
              tbRate,
              1 - tbRate
            ];
          }
          this.tbLabel = {
            price:
              this.formatNumberWithPrecision(res.data.unitCostValueTb, 2) ||
              "--",
            percent:
              res.data.tb || res.data.tb === 0
                ? this.formatNumberWithPrecision(
                    Math.abs(res.data.tb) * 100,
                    2
                  ) + "%"
                : "--",
            src:
              res.data.tb > 0
                ? require("./assets/arrow_up.png")
                : res.data.tb < 0
                ? require("./assets/arrow_down.png")
                : "",
            unit: res.data.unit
          };
          // 环比
          const hbRate = Math.abs(res.data.hb);
          if (hbRate > 1) {
            this.CetChart_avgcost2.options.series[0].data = [1];
          } else {
            this.CetChart_avgcost2.options.series[0].data = [
              hbRate,
              1 - hbRate
            ];
          }
          this.hbLabel = {
            price:
              this.formatNumberWithPrecision(res.data.unitCostValueHb, 2) ||
              "--",
            percent:
              res.data.hb || res.data.hb === 0
                ? this.formatNumberWithPrecision(
                    Math.abs(res.data.hb) * 100,
                    2
                  ) + "%"
                : "--",
            src:
              res.data.hb > 0
                ? require("./assets/arrow_up.png")
                : res.data.hb < 0
                ? require("./assets/arrow_down.png")
                : "",
            unit: res.data.unit
          };
        }
      });
    },
    // 单位成本占比
    getUnitObjectCostPercent(params) {
      let nodes = [];
      if (this.currentNode.children && this.currentNode.children.length) {
        nodes = this.currentNode.children.map(item => {
          return {
            id: item.id,
            modelLabel: item.modelLabel,
            name: item.name
          };
        });
      }
      customApi.queryUnitObjectCostPercent({ ...params, nodes }).then(res => {
        if (res.code === 0) {
          this.CetChart_energyCost.options.series[0].data = [];
          res.data.forEach(item => {
            this.CetChart_energyCost.options.series[0].data.push({
              value: item.value,
              name: item.objectName,
              unit: item.unit
            });
          });
          this.costList = res.data.slice(0, 5);
        }
      });
    },
    // 初始化
    async init() {
      await this.getProductType();
      await this.getProjectEnergy();
      this.getTreeData();
    }
  },
  activated() {
    this.init();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .proportion {
    .proportion-item {
      box-sizing: border-box;
      position: relative;
      .cost-item {
        text-align: center;
        height: 28px;
        line-height: 28px;
        @include font_color(T8);
        @include background(BG6);
        span {
          font-size: 16px;
        }
      }
      .top-unit {
        height: 28px;
        line-height: 28px;
      }
      .top {
        box-sizing: border-box;
        height: calc(100% - 100px);
        position: absolute;
        top: 96px;
        left: 24px;
        width: calc(100% - 48px);
        overflow-y: auto;
        .top-item {
          height: 20px;
          line-height: 20px;
          margin-bottom: 20px;
          .label {
            width: 80px;
            text-align: right;
          }
          .costVal {
            width: calc(100% - 90px);
            display: flex;
            align-items: center;
            span {
              flex: 1;
            }
          }
        }
      }
      .itemBox-empty {
        height: calc(100% - 45px);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
      }
    }
    .avg-cost {
      padding-bottom: 0;
      margin-right: 20px;
      .compare {
        .tip {
          position: absolute;
          top: 10px;
          left: 10px;
          z-index: 99;
          padding: 2px 20px 2px 2px;
        }
        .preText {
          @include font_color(T8);
          @include background(BG6);
        }
        .lastText {
          @include font_color(T8);
          @include background(BG6);
        }
        .label {
          width: 50%;
          height: 50px;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;
          .percent {
            span {
              display: inline-block;
              max-width: calc(100% - 20px);
            }
            img {
              width: 14px;
            }
          }
        }
      }
    }
  }
}
</style>
