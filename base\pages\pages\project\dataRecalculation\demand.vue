<template>
  <div class="page">
    <el-main style="height: 100%; width: 100%; padding: 10px">
      <div class="card">
        <CetButton v-bind="CetButton_1" v-on="CetButton_1.event"></CetButton>
      </div>
      <div class="card">
        <el-row class="title">重算时段</el-row>
        <el-row>
          <el-col :span="8" class="pl10">
            起始时间：
            <el-date-picker
              class="datePicker"
              :disabled="status"
              :clearable="false"
              v-model="startTime"
              type="date"
              value-format="timestamp"
              placeholder="选择日期"
            ></el-date-picker>
          </el-col>
          <el-col :span="8" class="pl10">
            结束时间：
            <el-date-picker
              class="datePicker"
              :disabled="status"
              :clearable="false"
              v-model="endTime"
              type="date"
              value-format="timestamp"
              placeholder="选择日期"
            ></el-date-picker>
          </el-col>
        </el-row>
      </div>
      <div class="card">
        <el-row class="title">重算数据类型</el-row>
        <ElRadioGroup
          v-model="ElRadioGroup_1.value"
          v-bind="ElRadioGroup_1"
          v-on="ElRadioGroup_1.event"
        >
          <ElRadio
            v-for="item in ElRadioList_1.options_in"
            :key="item[ElRadioList_1.key]"
            :label="item[ElRadioList_1.label]"
            :disabled="item[ElRadioList_1.disabled]"
          >
            {{ item[ElRadioList_1.text] }}
          </ElRadio>
        </ElRadioGroup>
      </div>
      <div class="card">
        <el-row class="title">关联的重算步骤</el-row>
        <ElCheckboxGroup
          v-model="ElCheckboxGroup_1.value"
          v-bind="ElCheckboxGroup_1"
          v-on="ElCheckboxGroup_1.event"
        >
          <ElCheckbox
            v-for="item in ElCheckboxList_1.options_in"
            :key="item[ElCheckboxList_1.key]"
            :label="item[ElCheckboxList_1.label]"
            :disabled="item[ElCheckboxList_1.disabled]"
          >
            {{ item[ElCheckboxList_1.text] }}
          </ElCheckbox>
          <span v-show="ElCheckboxList_1.options_in.length === 0">无</span>
        </ElCheckboxGroup>
      </div>
      <el-row class="title">选择节点</el-row>
      <el-row class="selectNode" :gutter="20">
        <el-col :span="12">
          <CetTree
            class="card"
            :selectNode.sync="CetTree_1.selectNode"
            :checkedNodes.sync="CetTree_1.checkedNodes"
            :searchText_in.sync="CetTree_1.searchText_in"
            v-bind="CetTree_1"
            v-on="CetTree_1.event"
          ></CetTree>
        </el-col>
        <el-col :span="12">
          <CetTable
            class="CetTable card"
            v-show="!status"
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
          >
            <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_id"></ElTableColumn>
          </CetTable>
        </el-col>
      </el-row>
      <el-row class="mt10">
        <CetButton
          class="fr mr10"
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="fr mr20"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </el-row>
    </el-main>
  </div>
</template>
<script>
import TREE_PARAMS from "@/store/treeParams.js";
import { httping } from "@omega/http";
export default {
  name: "dataRecalculation",
  components: {},

  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    }
  },

  data(vm) {
    return {
      startTime: vm.$moment().subtract(1, "d").valueOf(),
      endTime: vm.$moment().valueOf(),
      status: false,
      CetButton_1: {
        visible_in: true,
        disable_in: true,
        title: "全局重算",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      ElRadioGroup_1: {
        value: [],
        style: {},
        event: {
          change: this.ElRadioGroup_1_change_out
        }
      },
      ElRadioList_1: {
        options_in: [],
        key: "id",
        label: "id",
        text: "value",
        disabled: "disabled"
      },
      ElCheckboxGroup_1: {
        value: [],
        style: {},
        event: {}
      },
      ElCheckboxList_1: {
        options_in: [],
        key: "id",
        label: "id",
        text: "value",
        disabled: "disabled"
      },
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: false,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: true,
        checkStrictly: true,
        event: {
          checkedNodes_out: this.CetTree_1_checkedNodes_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      ElTableColumn_name: {
        prop: "name", // 支持path a[0].b
        label: "节点名称", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true
      },
      ElTableColumn_id: {
        prop: "id", // 支持path a[0].b
        label: "节点ID", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "确定",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {},

  methods: {
    init() {
      this.startTime = this.$moment().subtract(1, "d").valueOf();
      this.endTime = this.$moment().valueOf();
      this.ElRadioGroup_1.value = this._.get(
        this.ElRadioList_1,
        "options_in[0].id"
      );
      this.CetTree_1.checkedNodes = [];
      this.status = false;
      this.CetButton_1_statusTrigger_out();
      this.getNodeTree();
    },
    // 获取节点树
    getNodeTree() {
      const obj = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: TREE_PARAMS.management,
        treeReturnEnable: true
      };
      const auth = this.token; //身份验证
      httping({
        url: "/eem-service/v1/node/nodeTree",
        data: obj,
        method: "POST"
      }).then(res => {
        if (res.code === 0) {
          var data = this._.get(res, "data", []);
          this.CetTree_1.inputData_in = data;
        }
      });
    },
    // 获取重算数据类型
    getDataType() {
      var queryData = {};
      
      this.ElRadioList_1.options_in = [
        {
          id: 3,
          value: "最大需量重算",
          children: [
            {
              id: 1,
              value: "需量预测"
            }
          ]
        },
        {
          id: 5,
          value: "需量预测",
          children: []
        }
      ];
      this.ElRadioGroup_1.value = this._.get(
        this.ElRadioList_1,
        "options_in[0].id"
      );
      this.ElRadioGroup_1_change_out(this.ElRadioGroup_1.value);
      
    },
    ElRadioGroup_1_change_out(val) {
      this.ElCheckboxList_1.options_in =
        this.ElRadioList_1.options_in.find(item => item.id === val).children ||
        [];
      this.ElCheckboxGroup_1.value = this.ElCheckboxList_1.options_in.map(
        item => item.id
      );
    },
    CetTable_1_outputData_out(val) {
      // 有部分重算的时候再打开
      // this.CetButton_confirm.disable_in = !val.length ? true : false;
    },
    CetTree_1_checkedNodes_out(val) {
      this.CetTable_1.data = val;
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.init();
    },
    CetButton_confirm_statusTrigger_out(val) {
      var vm = this;
      vm.$confirm("重算操作会覆盖原来的数据，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        beforeClose: function (action, instance, done) {
          if (action == "confirm") {
            vm.confirmServer();
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action != "confirm") {
            vm.$message({
              type: "info",
              message: "取消删除！"
            });
          }
        }
      });
    },
    confirmServer() {
      var queryData = {
        type: this.ElRadioGroup_1.value,
        relative: this.ElCheckboxGroup_1.value.length > 0 ? 1 : null
      };
      httping({
        url: "/eem-service/v1/kettle/recount",
        data: queryData,
        method: "POST"
      }).then(res => {
        if (res.code === 0) {
          this.$message({
            type: "success",
            message: "操作成功"
          });
        }
      });
    },
    CetButton_1_statusTrigger_out(val) {
      this.status = !this.status;
      if (this.status) {
        this.CetButton_1.title = "部分重算";
        this.CetTree_1.showCheckbox = false;
        this.CetButton_confirm.disable_in = false;
      } else {
        this.CetButton_1.title = "全局重算";
        this.CetTree_1.showCheckbox = true;
        this.CetButton_confirm.disable_in = !this.CetTable_1.data.length;
      }
    }
  },
  created: function () {},
  activated: function () {},
  mounted: function () {
    this.init();
    this.getDataType();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.selectNode {
  height: 300px;
  :deep(.el-col) {
    height: 100%;
  }
}
.card {
  border: 1px solid;
  @include border_color(B1);
  @include padding(J1);
  box-sizing: border-box;
  border-radius: 4px;
  margin-bottom: 10px;
}
:deep(.el-radio-group) {
  width: 100%;
  padding: 0 10px;
  box-sizing: border-box;
  :deep(.el-radio) {
    margin-bottom: 5px;
  }
}
.title {
  font-size: 18px;
  line-height: 24px;
  margin-bottom: 10px;
}
.datePicker {
  display: inline-block;
  width: 250px;
}
</style>
