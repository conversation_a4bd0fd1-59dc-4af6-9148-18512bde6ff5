<template>
  <div class="page flex-column">
    <div class="mbJ1">
      <customElSelect
        v-model="ElSelect_energyType.value"
        v-bind="ElSelect_energyType"
        v-on="ElSelect_energyType.event"
        :prefix_in="$T('能源类型')"
      >
        <ElOption
          v-for="item in ElOption_energyType.options_in"
          :key="item[ElOption_energyType.key]"
          :label="item[ElOption_energyType.label]"
          :value="item[ElOption_energyType.value]"
          :disabled="item[ElOption_energyType.disabled]"
        ></ElOption>
      </customElSelect>
    </div>
    <div class="flex-auto">
      <CetGiantTree
        v-bind="CetGiantTree_1"
        v-on="CetGiantTree_1.event"
      ></CetGiantTree>
    </div>
  </div>
</template>

<script>
import ELECTRICAL_DEVICE from "@/store/electricaldevice.js";
import customApi from "@/api/custom";

export default {
  name: "LeftTree",
  props: {
    selectNodeModel: {
      type: String,
      default: ""
    }
  },
  components: {},
  computed: {
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    },
    totalEnergyType() {
      return this.$store.state.systemCfg.totalEnergyType;
    },
    totalEnergyTypeCO2() {
      return this.$store.state.systemCfg.totalEnergyTypeCO2;
    },
    totalEnergyTypeC() {
      return this.$store.state.systemCfg.totalEnergyTypeC;
    }
  },
  data() {
    return {
      ElSelect_energyType: {
        value: "",
        style: {},
        event: {
          change: this.ElSelect_energyType_change_out
        }
      },
      ElOption_energyType: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out //选中单行输出
        }
      },
      deviceclass: [] // 配电室和管道房下的设备
    };
  },
  watch: {},
  methods: {
    ElSelect_energyType_change_out(val) {
      this.$emit("handleEnergyChange", val);
      this.queryTree();
    },
    getProjectEnergy(callback) {
      customApi.getProjectEnergy(this.projectId).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []).filter(
            item =>
              [
                this.totalEnergyType,
                this.totalEnergyTypeCO2,
                this.totalEnergyTypeC
              ].indexOf(item.energytype) === -1
          );
          this.ElOption_energyType.options_in = data;
          this.ElSelect_energyType.value = data[0] && data[0].energytype;
          this.$emit("handleEnergyChange", this.ElSelect_energyType.value);
          callback && callback();
        }
      });
    },
    // 获取房间下的设备
    queryDevice() {
      const deviceclassData = ELECTRICAL_DEVICE.map(item => {
        return {
          text: item.label,
          propertyLabel: item.value
        };
      });
      // 去除交换机和光电转换器、母线、母联
      const roomDeviceList = deviceclassData.filter(item => {
        return (
          [
            "interchanger",
            "photoeleconverter",
            "busbarsection",
            "busbarconnector"
          ].indexOf(item.propertyLabel) === -1
        );
      });
      roomDeviceList.push(
        ...[
          {
            propertyLabel: "pipeline",
            text: $T("管道")
          },
          {
            propertyLabel: "linesegment",
            text: $T("一段线")
          }
        ]
      );
      this.deviceclass = roomDeviceList;
    },
    queryTree() {
      let roomType = [];
      const energyType = this.ElSelect_energyType.value;
      if (energyType === 2) {
        // 电
        roomType = [1];
      } else if (energyType === 16) {
        // 压缩空气
        roomType = [4, 6];
      } else if (energyType === 24) {
        // 冷量
        roomType = [3, 6];
      } else if ([7, 46].includes(energyType)) {
        // 热水、热量
        roomType = [5, 6];
      } else {
        roomType = [6];
      }
      const params = {
        rootID: this.projectId,
        rootLabel: "project",
        treeReturnEnable: true,
        hideIfNoChildLabels: ["project", "room"],
        subLayerConditions: [
          {
            filter: {
              expressions: [
                {
                  limit: roomType,
                  prop: "roomtype",
                  operator: "IN"
                }
              ]
            },
            modelLabel: "room",
            props: []
          }
        ]
      };

      this.deviceclass.forEach(item => {
        if (item.propertyLabel === "pipeline") {
          params.subLayerConditions.push({
            modelLabel: item.propertyLabel,
            filter: {
              // 此处的过滤room没有子节点时仍然会显示
              expressions: [
                {
                  limit: this.ElSelect_energyType.value,
                  prop: "energytype",
                  operator: "EQ"
                }
              ]
            }
          });
        } else if (item.propertyLabel === "linesegment") {
          params.subLayerConditions.push({ modelLabel: item.propertyLabel });
        } else if (item.propertyLabel === "linesegmentwithswitch") {
          params.subLayerConditions.push({
            modelLabel: item.propertyLabel,
            depth: 2
          });
        } else {
          params.subLayerConditions.push({
            modelLabel: item.propertyLabel
          });
        }
      });
      customApi.getNodeTree(params).then(res => {
        if (res.code === 0) {
          const val = res.data || [];
          // this.CetGiantTree_1.inputData_in = this.filterData(val);
          this.CetGiantTree_1.inputData_in = this.filterData(val);
          // 电加载配电室下设备，其它能源类型加载相应能源类型的管道，根据节点树返回的energytype过滤
          // let filterNodes = this.filterData(val);
          let selectNode = {}; // 选中节点
          if (this.selectNodeModel) {
            // 损耗分析默认选中第一个设备节点
            // if (!this._.find(filterNodes, ["modelLabel", "room"])) {
            //   this.CetGiantTree_1.inputData_in = [];
            // }
            // this.CetGiantTree_1.filterNodes_in = filterNodes;
            let flatData = this.dataTransform(res.data);
            selectNode = flatData.find(item =>
              this._.find(this.deviceclass, ["propertyLabel", item.modelLabel])
            );
          } else {
            // 损耗事件默认选中第一个节点
            selectNode = val[0];
          }
          this.CetGiantTree_1.selectNode = this._.cloneDeep(selectNode);
        }
      });
    },
    // 节点树平铺，用于选中第一个设备
    dataTransform(array) {
      const cloneData = this._.cloneDeep(array);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },
    // 房间、管道房下没有设备时不展示
    filterData(data, arr = []) {
      data.forEach(item => {
        if (
          (["project", "room"].includes(item.modelLabel) &&
            item.children &&
            item.children.length) ||
          this._.find(this.deviceclass, ["propertyLabel", item.modelLabel])
        ) {
          arr.push(item);
        }
        if (item.children && item.children.length) {
          item.children = this.filterData(item.children);
        }
      });
      return arr;
    },
    CetGiantTree_1_currentNode_out(val) {
      this.$emit("handleNodeClick", val);
    }
  },
  activated() {
    this.queryDevice();
    this.getProjectEnergy(() => {
      this.queryTree();
    });
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  .leftTree {
    flex: 1;
  }
}
</style>
