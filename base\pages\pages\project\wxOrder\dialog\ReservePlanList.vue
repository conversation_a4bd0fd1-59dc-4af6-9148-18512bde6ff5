<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="small el_dialog"
    >
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <!-- <CetButton v-bind="CetButton_preserve" v-on="CetButton_preserve.event"></CetButton> -->
        </span>
      </template>
      <div class="dialog eem-cont-c1" style="height: 400px">
        <el-row :gutter="20" style="height: 100%">
          <el-col :span="12" style="height: 100%; padding-right: 0px">
            <CetTable
              style="height: 100%; text-align: right"
              :data.sync="CetTable_1.data"
              :dynamicInput.sync="CetTable_1.dynamicInput"
              v-bind="CetTable_1"
              v-on="CetTable_1.event"
              row-key="id"
            >
              <template v-for="(column, index) in Columns_Order">
                <el-table-column
                  v-if="column.custom && column.custom === 'tag'"
                  v-bind="column"
                  :key="index"
                  class-name="font0 hand"
                  label-class-name="font14"
                >
                  <template slot-scope="scope">
                    <el-tag
                      size="small"
                      class="text-middle font14"
                      :effect="column.tagEffect"
                      :type="
                        column.tagTypeFormatter
                          ? column.tagTypeFormatter(scope.row, scope.column)
                          : 'primary'
                      "
                    >
                      {{
                        column.formatter
                          ? column.formatter(
                              scope.row,
                              scope.column,
                              scope.row[column.prop],
                              scope.$index
                            )
                          : scope.row[column.prop]
                      }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  v-else-if="column.custom && column.custom === 'button'"
                  v-bind="column"
                  :key="index"
                  class-name="font0"
                  label-class-name="font14"
                >
                  <template slot-scope="scope">
                    <span
                      class="clickformore"
                      @click.stop="
                        column.onButtonClick
                          ? column.onButtonClick(scope.row, scope.$index)
                          : void 0
                      "
                    >
                      {{
                        column.formatter
                          ? column.formatter(
                              scope.row,
                              scope.column,
                              scope.row[column.prop],
                              scope.$index
                            )
                          : column.text
                      }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-else
                  v-bind="column"
                  :key="index"
                  class-name="hand"
                ></el-table-column>
              </template>
            </CetTable>
          </el-col>
          <el-col :span="12" style="height: 100%">
            <div style="padding: 0px 10px; height: 100%; border-radius: 4px">
              <div class="lh40 fsH2">{{ $T("操作步骤") }}：</div>
              <div style="height: calc(100% - 40px); overflow-y: auto">
                <div
                  class="lh30"
                  v-for="(item, index) in solution.split('\n')"
                  :key="index"
                >
                  {{ item }}
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </CetDialog>
    <ReservePlanMsg
      v-bind="ReservePlanMsg"
      v-on="ReservePlanMsg.event"
    ></ReservePlanMsg>
  </div>
</template>
<script>
import common from "eem-utils/common.js";
import ReservePlanMsg from "./ReservePlanMsg";

export default {
  name: "ReservePlanList",
  components: { ReservePlanMsg },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("预案列表"),
        "append-to-body": true,
        showClose: true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "static", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        rules: {},
        event: {
          currentData_out: this.CetForm_pagedialog_currentData_out,
          saveData_out: this.CetForm_pagedialog_saveData_out,
          finishData_out: this.CetForm_pagedialog_finishData_out,
          finishTrigger_out: this.CetForm_pagedialog_finishTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "queryEventPlan",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [
            { name: "scenariosId_in", operator: "EQ", prop: "scenariosId" },
            { name: "limit_in", operator: "EQ", prop: "limit" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: $T("合计")
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {
          scenariosId_in: 0,
          limit_in: 0
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {
          record_out: this.CetTable_1_record_out
        },
        highlightCurrentRow: true
      },
      Columns_Order: [
        {
          type: "index",
          prop: "index",
          minWidth: "",
          width: 60,
          label: "#",
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "name",
          minWidth: 100,
          width: "",
          label: $T("预案名称"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          type: "",
          prop: "adoptRate",
          minWidth: 80,
          label: $T("采纳率"),
          sortable: false,
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: function (row, column, cellValue, index) {
            if (typeof cellValue === "number") {
              return (cellValue * 100).toFixed2(2) + "%";
            } else {
              return "0.00%";
            }
          }
        }
      ],
      // 预案详情弹窗
      ReservePlanMsg: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        event: {
          saveData_out: this.reservePlanMsg_saveData_out
        }
      },
      checkedNodes: [], // 选中节点
      solution: ""
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
      this.$nextTick(() => {
        this.CetTable_1.dynamicInput.scenariosId_in =
          this.inputData_in.scenariosId;
        this.CetTable_1.queryTrigger_in = new Date().getTime();
      });
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {
      this.CetForm_pagedialog.queryId_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      this.CetForm_pagedialog.inputData_in = this._.cloneDeep(val);
    }
  },
  methods: {
    CetForm_pagedialog_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_saveData_out(val) {
      this.$emit("saveData_out", {});
      this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
    },
    CetForm_pagedialog_finishData_out(val) {
      this.$emit("finishData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishTrigger_out(val) {
      this.$emit("finishTrigger_out", val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetForm_pagedialog.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      this.CetForm_pagedialog.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    CetTable_1_record_out(val) {
      if (val) {
        this.solution = val.solution || "";
      }
    },
    no() {},
    showReservePlanDetail(row, index) {
      row.index = index + 1;
      this.ReservePlanMsg.inputData_in = this._.cloneDeep(row);
      this.ReservePlanMsg.visibleTrigger_in = new Date().getTime();
    },
    reservePlanMsg_saveData_out() {}
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.dialog :deep(.el-table__row) {
  cursor: pointer;
}
.clickformore {
  cursor: pointer;
  text-decoration: underline;
}
.el_dialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
    @include border-radius(C1);
  }
}
</style>
