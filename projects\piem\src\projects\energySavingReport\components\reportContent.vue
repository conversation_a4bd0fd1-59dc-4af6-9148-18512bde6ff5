<template>
  <div class="reportContent">
    <!-- 封面 -->
    <Cover
      :queryTime="queryTime"
      :generationDate="reportData.generationDate"
    ></Cover>
    <FirstPage :reportData="reportData" :queryTime="queryTime" />
    <SecondPage :reportData="reportData" :queryTime="queryTime" />
    <ThirdPage :reportData="reportData" :queryTime="queryTime" />
    <FourthPage :reportData="reportData" :queryTime="queryTime" />
    <FifthPage
      :reportData="reportData"
      :queryTime="queryTime"
      @showfithPahe="showfithPahe"
      v-if="fivePage"
    />
    <SixthPage
      :reportData="reportData"
      :queryTime="queryTime"
      :pagesNumber="fivePage ? '06' : '05'"
    />
    <SeventhPage
      :reportData="reportData"
      :queryTime="queryTime"
      :pagesNumber="fivePage ? '07' : '06'"
    />
    <EighthPage
      :reportData="reportData"
      :queryTime="queryTime"
      v-if="reportData.pumpCostVOList?.length > 8"
    />
  </div>
</template>

<script>
import Cover from "../pages/cover";
import FirstPage from "../pages/firstPage";
import SecondPage from "../pages/secondPage";
import ThirdPage from "../pages/thirdPage";
import FourthPage from "../pages/fourthPage";
import FifthPage from "../pages/fifthPage";
import SixthPage from "../pages/sixthPage";
import SeventhPage from "../pages/seventhPage";
import EighthPage from "../pages/eighthPage";
export default {
  name: "reportContent",
  components: {
    Cover,
    FirstPage,
    SecondPage,
    ThirdPage,
    FourthPage,
    FifthPage,
    SixthPage,
    SeventhPage,
    EighthPage
  },
  props: {
    reportData: {
      type: Object,
      default: () => {}
    },
    queryTime: {
      type: Number
    }
  },
  data() {
    return {
      fivePage: true
    };
  },
  watch: {},
  methods: {
    showfithPahe(e) {
      this.fivePage = e;
    }
  }
};
</script>

<style lang="scss" scoped>
.reportContent {
  width: 100%;
  position: relative;
}
:deep(.el-table) {
  background-color: #ffffff !important;
}
</style>
