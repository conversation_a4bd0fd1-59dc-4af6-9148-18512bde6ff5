<template>
  <el-container class="eem-common fullheight eem-min-width-mini">
    <el-header height="auto" class="flex-row mbJ3 eem-cont">
      <div class="text-ellipsis lh32" style="flex: 1">
        <el-tooltip effect="light" :content="headerlabel">
          <span class="common-title-H2" style="display: inline">
            {{ headerlabel }}
          </span>
        </el-tooltip>
      </div>
      <div class="mlJ2 lh32">
        <div class="fl">刷新时间：</div>
        {{ refreshTime }}
      </div>
      <div class="mlJ2 lh32 eem-radio">
        <div class="fl">谐波电压：</div>
        <ElRadioGroup
          v-model="ElRadioGroup_harmonicVoltage.value"
          v-bind="ElRadioGroup_harmonicVoltage"
          v-on="ElRadioGroup_harmonicVoltage.event"
        >
          <ElRadio
            class="fl"
            v-for="item in ElRadioList_harmonicVoltage.options_in"
            :key="item[ElRadioList_harmonicVoltage.key]"
            :label="item[ElRadioList_harmonicVoltage.label]"
            :disabled="item[ElRadioList_harmonicVoltage.disabled]"
          >
            {{ item[ElRadioList_harmonicVoltage.text] }}
          </ElRadio>
        </ElRadioGroup>
      </div>
      <div class="mlJ2 lh32 eem-radio">
        <div class="fl">谐波电流：</div>
        <ElRadioGroup
          v-model="ElRadioGroup_harmonicCurrent.value"
          v-bind="ElRadioGroup_harmonicCurrent"
          v-on="ElRadioGroup_harmonicCurrent.event"
        >
          <ElRadio
            class="fl"
            v-for="item in ElRadioList_harmonicCurrent.options_in"
            :key="item[ElRadioList_harmonicCurrent.key]"
            :label="item[ElRadioList_harmonicCurrent.label]"
            :disabled="item[ElRadioList_harmonicCurrent.disabled]"
          >
            {{ item[ElRadioList_harmonicCurrent.text] }}
          </ElRadio>
        </ElRadioGroup>
      </div>
      <div class="mlJ2">
        <!-- 谐波次数按钮组件 -->
        <CetButton
          class="fl mrJ1"
          v-bind="CetButton2_harmTimes"
          v-on="CetButton2_harmTimes.event"
        ></CetButton>
        <CetButton
          class="fl"
          v-bind="CetButton2_export"
          v-on="CetButton2_export.event"
        ></CetButton>
      </div>
      <!-- </div> -->
    </el-header>
    <el-main class="fullheight padding0 flex-column">
      <div class="eem-container flex-auto flex-column">
        <headerSpot class="mbJ3">电压谐波</headerSpot>
        <div class="flex-auto">
          <CetChart ref="UTHDChart" v-bind="CetChart2_UTHD"></CetChart>
        </div>
      </div>
      <div class="eem-container flex-auto flex-column mtJ3">
        <headerSpot class="mbJ3">电流谐波</headerSpot>
        <div class="flex-auto">
          <CetChart ref="ATHDChart" v-bind="CetChart2_ATHD"></CetChart>
        </div>
      </div>
    </el-main>
    <HarmTimesGroup
      :visible.sync="queryDialogVisble"
      :last-checked.sync="lastChecked"
      @confirm="queryDialogConfirm"
    />
  </el-container>
</template>
<script>
import HarmTimesGroup from "./HarmTimesGroup";
import common from "eem-utils/common";

export default {
  name: "RealTimeHarmonic",
  components: {
    HarmTimesGroup
  },
  props: {
    currentNode: Object,
    refreshTrigger_in: {
      type: [Number]
    },
    selectedMenu: {
      type: String
    },
    token: {
      type: String
    }
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    }
  },
  watch: {
    currentNode: {
      deep: true,
      handler: function (val, oldVal) {
        this.clearInterval("interVal");
        if (
          this._.get(val, "data.id", -1) === this._.get(oldVal, "data.id", -2)
        )
          return;
        if (this._.get(val, "data.modelLabel"))
          this.CetButton_query_statusTrigger_out();
        this.headerlabel = val.data?.name;
      }
    },
    refreshTrigger_in: {
      deep: true,
      handler: function (val, oldVal) {
        this.CetButton_query_statusTrigger_out();
      }
    }
  },
  data() {
    return {
      headerlabel: "",
      // 谐波电压
      ElRadioGroup_harmonicVoltage: {
        value: 1,
        event: {
          change: this.ElRadioGroup_harmonicVoltage_change_out
        }
      },
      ElRadioList_harmonicVoltage: {
        options_in: [
          {
            id: 1,
            text: "含有率"
          },
          {
            id: 2,
            text: "相角"
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },

      // 谐波电流
      ElRadioGroup_harmonicCurrent: {
        value: 3,
        event: {
          change: this.ElRadioGroup_harmonicCurrent_change_out
        }
      },
      // harmonicCurrent组件
      ElRadioList_harmonicCurrent: {
        options_in: [
          {
            id: 3,
            text: "含有率"
          },
          {
            id: 4,
            text: "有效值"
          },
          {
            id: 5,
            text: "相角"
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },

      //相别
      CetCheckboxGroup_Phase: {
        dataConfig: {
          edition: "v1",
          queryUrl: "",
          type: "",
          modelLabel: "accountstatus",
          dropItemId: "id", //指定显示文字、输出参数
          dropItemText: "text"
        },
        dataMode: "static",
        inputData_in: [
          {
            id: 1,
            text: "A"
          },
          {
            id: 2,
            text: "B"
          },
          {
            id: 3,
            text: "C"
          }
        ], //显示列表
        CheckedArray: [1, 2, 3], //选中的数组
        config: {
          showCheckAll: false //是否显示全选
        }
      },
      // 谐波次数组件
      CetButton2_harmTimes: {
        visible_in: true,
        disable_in: false,
        title: "设 置",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton2_harmTimes_statusTrigger_out
        }
      },

      // 查询按钮组件
      CetButton_query: {
        visible_in: true,
        disable_in: false,
        config: {
          title: "查询",
          type: "primary",
          plain: true
        }
      },
      // 导出
      CetButton2_export: {
        visible_in: true,
        disable_in: false,
        title: "导出",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton2_export_statusTrigger_out
        }
      },

      // 电压谐波畸变率
      CetChart2_UTHD: {
        //组件输入项
        inputData_in: null,
        options: {
          color: ["orange", "green", "red"],
          title: {
            text: ""
          },
          grid: {
            top: 35,
            right: 25,
            bottom: 35,
            left: 50
          },
          legend: {
            x: "right",
            y: "top"
          },
          tooltip: {
            trigger: "axis",

            appendToBody: true,
            formatter: params => {
              const arr = [];
              for (let i = 0; i < params.length; i++) {
                arr.push(
                  "<br>" +
                    params[i].marker +
                    params[i].seriesName +
                    "：" +
                    common.setNumFixed(params[i].value[i + 1])
                );
              }
              arr.unshift("谐波次数: " + params[0].name);
              return arr.join("");
            },
            // formatter: "谐波次数：{b}<br />{a0}： {c0}<br />{a1}： {c1}<br />{a2}： {c2}",
            axisPointer: {
              type: "shadow"
            }
          },

          xAxis: {
            type: "category"
          },
          yAxis: {},
          series: [{ type: "bar" }, { type: "bar" }, { type: "bar" }]
        }
      },

      // 电流谐波畸变率
      CetChart2_ATHD: {
        //组件输入项
        inputData_in: null,
        options: {
          color: ["orange", "green", "red"],
          title: {
            text: "",
            x: "center",
            textStyle: {
              fontSize: 16,
              fontWeight: "normal",
              color: "#999"
            }
          },
          grid: {
            top: 35,
            right: 25,
            bottom: 35,
            left: 50
          },
          legend: {
            x: "right",
            y: "top"
          },
          tooltip: {
            trigger: "axis",
            appendToBody: true,
            formatter: params => {
              const arr = [];
              for (let i = 0; i < params.length; i++) {
                arr.push(
                  "<br>" +
                    params[i].marker +
                    params[i].seriesName +
                    "：" +
                    common.setNumFixed(params[i].value[i + 1])
                );
              }
              arr.unshift("谐波次数: " + params[0].name);
              return arr.join("");
            },
            // formatter: "谐波次数：{b}<br />A相： {c}<br />B相： {c1}<br />C相： {c2}",
            axisPointer: {
              type: "shadow"
            }
          },

          xAxis: {
            type: "category"
          },
          yAxis: {},
          series: [{ type: "bar" }, { type: "bar" }, { type: "bar" }]
        }
      },

      refreshTime: common.formatDate(new Date()),
      queryDialogVisble: false,
      // 用于记录自定义谐波次数中确定勾选的选项
      lastChecked: common.fileNumArr(2, 25),
      // 用于保存定时器
      interVal: null
    };
  },
  methods: {
    //谐波电压
    ElRadioGroup_harmonicVoltage_change_out(val) {
      if (this._.isEmpty(this.currentNode)) return;
      this.CetButton_query_statusTrigger_out();
    },

    ElRadioGroup_harmonicCurrent_change_out(val) {
      if (this._.isEmpty(this.currentNode)) return;
      this.CetButton_query_statusTrigger_out();
    },

    //相别
    CetCheckboxGroup_Phase_resulttext_out(val) {},
    CetButton_query_statusTrigger_out(val) {
      this.getData();
      if (this.interVal === null) this.setInterval();
    },
    // 自定义谐波次数
    CetButton2_harmTimes_statusTrigger_out(val) {
      this.queryDialogVisble = true;
    },

    CetButton2_export_statusTrigger_out(val) {
      const node = this.currentNode.data;
      const data = {
        harmTimes: this.lastChecked,
        harmTypeCurrent: this.ElRadioGroup_harmonicCurrent.value,
        harmTypeVoltage: this.ElRadioGroup_harmonicVoltage.value,
        node: {
          childSelectState: 0,
          children: [null],
          deviceIds: [0],
          endPoint: true,
          id: node.id,
          modelLabel: node.modelLabel,
          name: "实时谐波",
          startPoint: true
        },
        pictures: [
          this.$refs.UTHDChart.getDataURL(),
          this.$refs.ATHDChart.getDataURL()
        ],
        projectId: this.projectId
      };
      common.downExcel(
        "/eem-service/v1/pq/harmonic/realData/export",
        data,
        this.token
      );
    },

    queryDialogConfirm() {
      this.CetButton_query_statusTrigger_out();
    },

    formatChartData(data) {
      const temp = [["谐波次数", "A相", "B相", "C相"]];
      data.forEach(item => {
        const data = [item.harmonicOrder];
        item.realData.forEach(item => {
          if (item.value === -2147483648) return;
          data.push(this._.round(item.dataValue, 2));
        });
        temp.push(data);
      });
      return temp;
    },
    // 查询数据
    getData() {
      if (!this.currentNode) return;
      const node = this.currentNode.data;

      const data = {
        harmTimes: this.lastChecked,
        harmTypeCurrent: this.ElRadioGroup_harmonicCurrent.value,
        harmTypeVoltage: this.ElRadioGroup_harmonicVoltage.value,
        node: {
          childSelectState: 0,
          children: [null],
          deviceIds: [0],
          endPoint: true,
          id: node?.id,
          modelLabel: node?.modelLabel,
          name: "",
          startPoint: true
        },
        pictures: [],
        projectId: this.projectId
      };
      common.requestData(
        {
          url: "/eem-service/v1/pq/harmonic/realData",
          data,
          hideNotice: true
        },
        res => {
          const unit = {
            1: "%",
            2: "°",
            3: "%",
            4: "A",
            5: "°"
          };
          const UTHDunit = unit[this.ElRadioGroup_harmonicVoltage.value];
          const ATHDunit = unit[this.ElRadioGroup_harmonicCurrent.value];
          this.CetChart2_UTHD.inputData_in = this.formatChartData(
            this._.get(res, "voltage", [])
          );
          this.CetChart2_UTHD.options.yAxis.name = UTHDunit;
          this.CetChart2_UTHD.options.tooltip.formatter = params => {
            const arr = [];
            for (let i = 0; i < params.length; i++) {
              arr.push(
                "<br>" +
                  params[i].marker +
                  params[i].seriesName +
                  "：" +
                  common.setNumFixed(params[i].value[i + 1]) +
                  "(" +
                  UTHDunit +
                  ")"
              );
            }
            arr.unshift("谐波次数: " + params[0].name);
            return arr.join("");
          };
          this.CetChart2_ATHD.inputData_in = this.formatChartData(
            this._.get(res, "current", [])
          );
          this.CetChart2_ATHD.options.yAxis.name = ATHDunit;
          this.CetChart2_ATHD.options.tooltip.formatter = params => {
            const arr = [];
            for (let i = 0; i < params.length; i++) {
              arr.push(
                "<br>" +
                  params[i].marker +
                  params[i].seriesName +
                  "：" +
                  common.setNumFixed(params[i].value[i + 1]) +
                  "(" +
                  ATHDunit +
                  ")"
              );
            }
            arr.unshift("谐波次数: " + params[0].name);
            return arr.join("");
          };
          this.refreshTime = common.formatDate(new Date());
          this.$refs.UTHDChart.mergeOptions(this.CetChart2_UTHD.options);
          this.$refs.ATHDChart.mergeOptions(this.CetChart2_ATHD.options);
        }
      );
    },
    // 设置定时器 定时查询实时数据
    setInterval() {
      this.interVal = setInterval(() => {
        if (this._.isEmpty(this.currentNode)) return;
        this.getData();
      }, 5000);
    },
    // 清除定时器
    clearInterval(interVal) {
      // 终止请求
      if (!this[interVal]) return;
      clearInterval(this[interVal]);
      this[interVal] = null;
    }
  },
  created() {
    this.headerlabel =
      this.currentNode && this.currentNode.data && this.currentNode.data.name;
    this.CetButton_query_statusTrigger_out();
  },

  beforeDestroy() {
    this.clearInterval("interVal");
  },
  activated: function () {
    // 进入页面触发
    this.CetButton_query_statusTrigger_out();
  },
  deactivated: function () {
    //离开页面触发
    this.clearInterval("interVal");
  }
};
</script>
<style lang="scss" scoped></style>
