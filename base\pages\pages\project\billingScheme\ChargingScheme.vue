<template>
  <div class="page eem-common">
    <el-container style="height: 100%">
      <el-container
        class="mrJ3 eem-container"
        style="height: 100%; width: 420px; flex: none"
      >
        <div class="common-title-H2 mbJ3" style="padding: 0px">
          {{ projectName }}
        </div>
        <CetTable
          style="height: calc(100% - 120px)"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <ElTableColumn v-bind="ElTableColumn_name1"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_chargyWayText"></ElTableColumn>
        </CetTable>
        <el-footer height="aotu" style="padding: 0px" class="mtJ3">
          <CetButton
            class="createScheme"
            v-bind="CetButton_1"
            v-on="CetButton_1.event"
          ></CetButton>
        </el-footer>
      </el-container>
      <el-container class="eem-container" style="height: 100%">
        <div class="mbJ3">
          <div class="common-title-H2">{{ effectName }}</div>
          <CetButton
            class="fr"
            v-bind="CetButton_3"
            v-on="CetButton_3.event"
          ></CetButton>
          <CetButton
            class="fr mrJ1"
            v-bind="CetButton_2"
            v-on="CetButton_2.event"
          ></CetButton>
        </div>
        <div class="currentRate">
          <div class="common-title-H2">当前生效费率:</div>
          <span>{{ currentFee }}</span>
          {{ currentFeeText }}
          <img src="./assets/rate_bg.png" alt="" />
        </div>
        <el-header height="auto" class="p0 mtJ3 mbJ3">
          <div class="common-title-H3">调整历史</div>
          <el-popover placement="top-start" trigger="hover">
            <div>
              <div>
                偏差费用：偏差费用=（实际最大需量-申报需量*上限比例）*需量电价*惩罚系数，不计算负偏差时，偏差费用为0。
              </div>
              <div
                v-text="
                  '计算负偏差：实际最大需量<申报需量*上限比例时，是否计算负偏差，若不计算负偏差费用为0.'
                "
              ></div>
            </div>
            <div slot="reference" class="el-icon-question fsH3"></div>
          </el-popover>
          <CetButton
            class="fr"
            v-bind="CetButton_4"
            v-on="CetButton_4.event"
          ></CetButton>
        </el-header>
        <!-- 如设置为100%，flex=1计算后会出现纵向滚动条，所以设高为0 -->
        <CetTable
          style="height: 0"
          :data.sync="CetTable_2.data"
          :dynamicInput.sync="CetTable_2.dynamicInput"
          v-bind="CetTable_2"
          v-on="CetTable_2.event"
        >
          <template v-if="currentTabelItem">
            <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_name2"></ElTableColumn>
            <ElTableColumn
              v-bind="ElTableColumn_effectivedateText"
            ></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_chargyWayText"></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_feerate"></ElTableColumn>
            <ElTableColumn
              v-if="currentTabelItem.chargyWay == 2"
              v-bind="ElTableColumn_highlimit"
            ></ElTableColumn>
            <ElTableColumn
              v-if="currentTabelItem.chargyWay == 2"
              v-bind="ElTableColumn_punishrate"
            ></ElTableColumn>
            <ElTableColumn
              v-if="currentTabelItem.chargyWay == 2"
              v-bind="ElTableColumn_calculatedeviationText"
            ></ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_statusText">
              <template slot-scope="scope">
                <span :style="colorFormet">
                  {{ scope.row[ElTableColumn_statusText.prop] }}
                </span>
              </template>
            </ElTableColumn>
            <ElTableColumn v-bind="ElTableColumn_delete">
              <template slot-scope="scope">
                <span
                  v-if="scope.row.statusText == '未生效'"
                  class="eem-row-handle"
                  @click="handleDelete(scope.$index, scope.row)"
                >
                  删除
                </span>
                <span
                  v-if="scope.row.statusText != '未生效'"
                  class="eem-row-no-handle"
                >
                  删除
                </span>
              </template>
            </ElTableColumn>
          </template>
        </CetTable>
      </el-container>
    </el-container>
    <AddScheme
      :visibleTrigger_in="AddScheme.visibleTrigger_in"
      :closeTrigger_in="AddScheme.closeTrigger_in"
      :queryId_in="AddScheme.queryId_in"
      :inputData_in="AddScheme.inputData_in"
      @finishTrigger_out="AddScheme_finishTrigger_out"
    />
    <adjustmentScheme1
      :visibleTrigger_in="adjustmentScheme1.visibleTrigger_in"
      :closeTrigger_in="adjustmentScheme1.closeTrigger_in"
      :queryId_in="adjustmentScheme1.queryId_in"
      :inputData_in="adjustmentScheme1.inputData_in"
      @finishTrigger_out="adjustmentScheme1_finishTrigger_out"
      :data_list_in="CetTable_2.data"
      :schemeName="adjustmentScheme1.schemeName"
    />
    <adjustmentScheme2
      :visibleTrigger_in="adjustmentScheme2.visibleTrigger_in"
      :closeTrigger_in="adjustmentScheme2.closeTrigger_in"
      :queryId_in="adjustmentScheme2.queryId_in"
      :inputData_in="adjustmentScheme2.inputData_in"
      @finishTrigger_out="adjustmentScheme2_finishTrigger_out"
      :data_list_in="CetTable_2.data"
      :schemeName="adjustmentScheme2.schemeName"
    />
    <SchemeRename
      :visibleTrigger_in="SchemeRename.visibleTrigger_in"
      :closeTrigger_in="SchemeRename.closeTrigger_in"
      :queryId_in="SchemeRename.queryId_in"
      :inputData_in="SchemeRename.inputData_in"
      @finishTrigger_out="SchemeRename_finishTrigger_out"
      :schemeName="SchemeRename.schemeName"
    />
  </div>
</template>
<script>
import AddScheme from "./AddScheme";
import adjustmentScheme1 from "./adjustmentScheme1";
import adjustmentScheme2 from "./adjustmentScheme2";
import SchemeRename from "./SchemeRename";
import { httping } from "@omega/http";
export default {
  name: "ChargingScheme",
  components: {
    AddScheme,
    SchemeRename,
    adjustmentScheme1,
    adjustmentScheme2
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    },
    // 状态字体颜色
    deleteFormat(val) {
      if (val.statusText == "未生效") {
        return {
          "eem-row-handle": true
        };
      } else {
        return {
          "eem-row-no-handle": true
        };
      }
    }
  },

  data() {
    return {
      currentFeeText: "元/kVA", //费率单位
      currentFee: "--", //生效费率
      projectName: "",
      currentTabelItem: null,
      effectName: "",
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        // defaultSort: null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out
        }
      },
      // name组件
      ElTableColumn_name1: {
        prop: "name", // 支持path a[0].b
        label: "费率方案", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      ElTableColumn_chargyWayText: {
        prop: "chargyWayText", // 支持path a[0].b
        label: "费率类型", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      CetTable_2: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        // defaultSort: null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_2_record_out
        }
      },
      // index组件
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: "#", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "65" //绝对宽度
      },
      // name组件
      ElTableColumn_name2: {
        prop: "name", // 支持path a[0].b
        label: "方案名称", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      // effectivedateText组件
      ElTableColumn_effectivedateText: {
        prop: "effectivedateText", // 支持path a[0].b
        label: "生效时间", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      // feerate组件
      ElTableColumn_feerate: {
        prop: "feerate", // 支持path a[0].b
        label: "费率（元/kW）", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "130"
      },
      // highlimit组件
      ElTableColumn_highlimit: {
        prop: "highlimit", // 支持path a[0].b
        label: "上限比例（%）", //列名
        headerAlign: "left",
        align: "left",
        width: "130",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.highlimit || val.highlimit === 0) {
            return val.highlimit;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      // punishrate组件
      ElTableColumn_punishrate: {
        prop: "punishrate", // 支持path a[0].b
        label: "惩罚系数", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.punishrate || val.punishrate === 0) {
            return val.punishrate;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      },
      // calculatedeviationText组件
      ElTableColumn_calculatedeviationText: {
        prop: "calculatedeviationText", // 支持path a[0].b
        label: "计算负偏差", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: 120
      },
      // statusText组件
      ElTableColumn_statusText: {
        prop: "statusText", // 支持path a[0].b
        label: "当前状态", //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      //
      ElTableColumn_delete: {
        prop: "", // 支持path a[0].b
        label: "操作", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        width: "65" //绝对宽度
      },
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: "新建费率方案",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: "重命名",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      CetButton_3: {
        visible_in: true,
        disable_in: false,
        title: "删除",
        type: "danger",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_3_statusTrigger_out
        }
      },
      CetButton_4: {
        visible_in: true,
        disable_in: false,
        title: "调整计费方案",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_4_statusTrigger_out
        }
      },
      AddScheme: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      adjustmentScheme1: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        schemeName: null
      },
      adjustmentScheme2: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        schemeName: null
      },
      SchemeRename: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        schemeName: null
      }
    };
  },
  watch: {},

  methods: {
    // 获取项目名称
    getProject() {
      this.projectName = "";
      httping({
        url: "/eem-service/v1/node/nodeTree",
        method: "POST",
        data: {
          rootID: this.projectId,
          rootLabel: "project",
          treeReturnEnable: true
        }
      }).then(response => {
        if (response.code == 0 && response.data && response.data.length > 0) {
          this.projectName = response.data[0].name;
        }
      });
    },
    // 获取计费方案
    getDemandConfig() {
      // this.CetTable_1.data = [];
      this.CetTable_2.data = [];
      this.currentFee = "--";
      httping({
        url: "/eem-service/v1/common/query/children",
        method: "POST",
        data: {
          rootID: this.projectId,
          rootLabel: "project",
          subLayerConditions: [
            {
              modelLabel: "feescheme",
              filter: {
                expressions: [
                  {
                    limit: 2,
                    operator: "EQ",
                    prop: "energytype"
                  },
                  {
                    limit: 1,
                    operator: "EQ",
                    prop: "feeratetype"
                  }
                ]
              }
            }
          ],
          treeReturnEnable: true
        }
      }).then(response => {
        if (
          response.code == 0 &&
          response.data[0].children &&
          response.data[0].children.length > 0
        ) {
          this.CetTable_1.data = response.data[0].children;
          this.CetTable_1.data.forEach(item => {
            item.chargyWay = item.feeratesubtype;
            item.chargyWayText =
              item.chargyWay == 1
                ? "容量计费"
                : item.chargyWay == 2
                ? "需量计费"
                : "";
          });
        } else {
          this.CetTable_1.data = [];
          this.CetButton_1.disable_in = false;
        }
      });
    },
    // 获取调整历史
    getHistoryDemendConfig() {
      this.CetTable_2.data = [];
      this.currentFee = "--";
      this.effectName = "--";
      httping({
        url:
          "/eem-service/v1/demand/maintain/getFeeSchemeItem?id=" +
          this.currentTabelItem.id,
        method: "GET"
      }).then(response => {
        if (response.code == 0 && response.data) {
          if (this.currentTabelItem.chargyWay == 1) {
            // 容量计费
            if (!response.data.volumefeerecord_model) {
              return;
            }
            this.CetTable_2.data = response.data.volumefeerecord_model;
          } else {
            // 需量计费
            if (!response.data.demandfeerecord_model) {
              return;
            }
            this.CetTable_2.data = response.data.demandfeerecord_model;
          }
          // 列表生效时间按倒序排序
          var wrap;
          for (var i = 0; i < this.CetTable_2.data.length; i++) {
            for (var k = i + 1; k < this.CetTable_2.data.length; k++) {
              if (
                this.CetTable_2.data[i].effectivedate <
                this.CetTable_2.data[k].effectivedate
              ) {
                wrap = this.CetTable_2.data[k];
                this.CetTable_2.data[k] = this.CetTable_2.data[i];
                this.CetTable_2.data[i] = wrap;
              }
            }
          }
          var action = false;
          this.CetTable_2.data.forEach(item => {
            item.chargyWayText =
              response.data.feeratesubtype == 1
                ? "容量计费"
                : response.data.feeratesubtype == 2
                ? "需量计费"
                : "";
            item.effectivedateText =
              item.effectivedate &&
              this.$moment(item.effectivedate).format("YYYY/MM");
            item.feerate = item.feerate && item.feerate.toFixed(5);
            item.calculatedeviationText = item.calculatedeviation ? "是" : "否";
            item.name = response.data.name;
            if (item.effectivedate <= this.$moment().endOf("month").valueOf()) {
              if (!action) {
                item.statusText = "生效中";
                this.currentFee = item.feerate;
                this.effectName = item.name;
                action = true;
              } else {
                item.statusText = "已过期";
              }
            } else {
              item.statusText = "未生效";
            }
          });
        }
      });
    },
    CetButton_1_statusTrigger_out(val) {
      if (this.CetTable_1.data.length >= 2) {
        this.$message({
          message: "一个项目只能存在一个容量计费和一个需量计费",
          type: "warning"
        });
        return;
      }
      if (this.CetTable_1.data.length == 0) {
        this.AddScheme.inputData_in = {};
      } else {
        this.AddScheme.inputData_in = this._.cloneDeep(
          Object.assign(this.currentTabelItem, this.CetTable_2.data[0])
        );
      }
      this.AddScheme.visibleTrigger_in = this._.cloneDeep(val);
    },
    CetButton_2_statusTrigger_out(val) {
      // 传入另一个方案名称验证重名
      if (
        this.CetTable_1.data.filter(item => item.id != this.currentTabelItem.id)
          .length > 0
      ) {
        this.SchemeRename.schemeName = this.CetTable_1.data.filter(
          item => item.id != this.currentTabelItem.id
        )[0].name;
      }
      this.SchemeRename.inputData_in = this._.cloneDeep(this.currentTabelItem);
      this.SchemeRename.visibleTrigger_in = this._.cloneDeep(val);
    },
    // 删除
    CetButton_3_statusTrigger_out() {
      if (this.CetTable_2.data.length > 0) {
        this.$message({
          message: "存在调整历史的方案不允许被删除",
          type: "warning"
        });
        return;
      }
      this.$confirm("确定要删除所选项吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: "btn-custom-cancel",
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: (action, instance, done) => {
          if (action == "confirm") {
            httping({
              url: `/eem-service/v1/common/delete?projectId=${this.projectId}`,
              method: "DELETE",
              data: {
                modelLabel: "feescheme",
                idRange: [this.currentTabelItem.id]
              }
            }).then(response => {
              if (response.code == 0) {
                this.$message({
                  message: "删除成功",
                  type: "success"
                });
                this.getDemandConfig();
              }
            });
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action != "confirm") {
            this.$message({
              type: "info",
              message: "取消删除！"
            });
          }
        }
      });
    },
    CetButton_4_statusTrigger_out(val) {
      if (this.currentTabelItem.chargyWay == 1) {
        // 传入另一个方案名称验证重名
        if (
          this.CetTable_1.data.filter(
            item => item.id != this.currentTabelItem.id
          ).length > 0
        ) {
          this.adjustmentScheme1.schemeName = this.CetTable_1.data.filter(
            item => item.id != this.currentTabelItem.id
          )[0].name;
        }
        this.adjustmentScheme1.inputData_in = this._.cloneDeep({
          chargeway: 1,
          effectivedate:
            this.CetTable_2.data[0] &&
            this.CetTable_2.data[0].effectivedate &&
            this.$moment(this.CetTable_2.data[0].effectivedate)
              .startOf("month")
              .valueOf(),
          feerate: this.CetTable_2.data[0] && this.CetTable_2.data[0].feerate,
          name: this.currentTabelItem.name,
          recordId: this.CetTable_2.data[0] && this.CetTable_2.data[0].id,
          schemeId: this.currentTabelItem.id,
          modelId: this.projectId,
          modelLabel: "project"
        });
        this.adjustmentScheme1.visibleTrigger_in = this._.cloneDeep(val);
      } else if (this.currentTabelItem.chargyWay == 2) {
        // 传入另一个方案名称验证重名
        if (
          this.CetTable_1.data.filter(
            item => item.id != this.currentTabelItem.id
          ).length > 0
        ) {
          this.adjustmentScheme2.schemeName = this.CetTable_1.data.filter(
            item => item.id != this.currentTabelItem.id
          )[0].name;
        }
        this.adjustmentScheme2.inputData_in = this._.cloneDeep({
          calculatedeviation:
            this.CetTable_2.data[0] &&
            this.CetTable_2.data[0].calculatedeviation,
          chargeway: 2,
          effectivedate:
            this.CetTable_2.data[0] &&
            this.CetTable_2.data[0].effectivedate &&
            this.$moment(this.CetTable_2.data[0].effectivedate)
              .startOf("month")
              .valueOf(),
          feerate: this.CetTable_2.data[0] && this.CetTable_2.data[0].feerate,
          highLimit:
            this.CetTable_2.data[0] && this.CetTable_2.data[0].highlimit,
          name: this.currentTabelItem.name,
          punishRate:
            this.CetTable_2.data[0] && this.CetTable_2.data[0].punishrate,
          recordId: this.CetTable_2.data[0] && this.CetTable_2.data[0].id,
          schemeId: this.currentTabelItem.id,
          modelId: this.projectId,
          modelLabel: "project"
        });
        this.adjustmentScheme2.visibleTrigger_in = this._.cloneDeep(val);
      }
    },
    CetTable_1_record_out(val) {
      if (val.id == -1) {
        this.currentTabelItem = null;
        this.CetButton_2.disable_in = true;
        this.CetButton_4.disable_in = true;
        return;
      } else {
        this.currentTabelItem = val;
        this.getHistoryDemendConfig();
        this.CetButton_2.disable_in = false;
        this.CetButton_4.disable_in = false;
      }
      // 根据选中行调整右侧展示内容
      if (val.chargyWay == 1) {
        this.ElTableColumn_feerate.label = "费率（元/kVA）";
        this.currentFeeText = "元/kVA";
      } else if (val.chargyWay == 2) {
        this.currentFeeText = "元/kW";
        this.ElTableColumn_feerate.label = "费率（元/kW）";
      }
    },
    CetTable_2_record_out() {
      if (this.currentTabelItem) {
        this.CetButton_3.disable_in = false;
      } else {
        this.CetButton_3.disable_in = true;
      }
    },
    // 删除方案
    handleDelete(index, row) {
      var vm = this;
      vm.$confirm("确定要删除所选项吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: function (action, instance, done) {
          if (action == "confirm") {
            httping({
              url: `/eem-service/v1/demand/maintain/feeRecord?projectId=${vm.projectId}`,
              method: "DELETE",
              data: {
                modelLabel: row.modelLabel,
                idRange: [row.id]
              }
            }).then(response => {
              if (response.code == 0) {
                vm.$message({
                  message: "删除成功",
                  type: "success"
                });
                vm.getHistoryDemendConfig();
              }
            });
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action != "confirm") {
            vm.$message({
              type: "info",
              message: "取消删除！"
            });
          }
        }
      });
    },
    AddScheme_finishTrigger_out() {
      this.getDemandConfig();
    },
    adjustmentScheme1_finishTrigger_out() {
      this.getDemandConfig();
    },
    adjustmentScheme2_finishTrigger_out() {
      this.getDemandConfig();
    },
    SchemeRename_finishTrigger_out() {
      this.getDemandConfig();
    },
    // 状态字体颜色
    colorFormet(val) {
      if (val.statusText == "已过期") {
        return "color:#999999";
      } else if (val.statusText == "生效中") {
        return "color:#0033FF";
      } else if (val.statusText == "未生效") {
        return "color:#FF0000";
      }
    }
  },
  created: function () {},
  activated: function () {
    this.getProject();
    this.getDemandConfig();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.currentRate {
  height: 240px;
  // background: #1e2b5f url("./assets/rate_bg.png") no-repeat top right;
  box-shadow: none;
  // font-size: 16px;
  @include font_size(H3);
  border-width: initial;
  border-style: none;
  border-color: initial;
  border-image: initial;
  border-radius: 4px;
  position: relative;
  line-height: 240px;
  & > div {
    @include font_size(H2);
    line-height: 20px;
    position: absolute;
    left: 30px;
    top: 50px;
    display: inline-block;
    padding: 5px;
  }
  & > span {
    font-size: 48px;
    display: inline-block;
    margin-left: 30px;
  }
  img {
    position: absolute;
    right: 100px;
    bottom: 0;
    opacity: 0.73;
  }
}
.createScheme {
  width: 100%;
  :deep(.el-button) {
    width: 100%;
  }
}
</style>
