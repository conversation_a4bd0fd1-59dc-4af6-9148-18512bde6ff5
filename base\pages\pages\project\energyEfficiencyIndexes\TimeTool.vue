<template>
  <div class="clearfix">
    <el-select
      v-show="showOption"
      class="fl"
      value-key="typeID"
      v-model="currentTimeOption"
      :disabled="selectDisable"
      @change="changTimeType"
      size="small"
      style="width: 80px"
    >
      <el-option
        v-for="(item, index) in timeOptions"
        :key="`${index}_${item.typeID}`"
        :label="item.text"
        :value="item"
      ></el-option>
    </el-select>

    <el-button
      class="fl ml5 mr5"
      plain
      size="small"
      icon="el-icon-arrow-left"
      @click="queryPrv"
    ></el-button>
    <el-date-picker
      v-if="typeID === 13"
      size="small"
      :clearable="false"
      class="fl"
      v-model="value"
      type="week"
      style="width: 180px !important"
      :format="formatDate"
      :picker-options="pickerOptionsByWeek"
      placeholder="选择周"
      @change="changDate"
    ></el-date-picker>
    <el-date-picker
      v-if="typeID !== 13"
      size="small"
      value-format="timestamp"
      :clearable="false"
      class="fl"
      style="width: 150px !important"
      v-model="value"
      :picker-options="pickerOptions"
      :type="currentTimeOption.type"
      placeholder="选择日期"
      :format="formatDate"
      @change="changDate"
    ></el-date-picker>
    <el-button
      class="fl ml5"
      plain
      size="small"
      icon="el-icon-arrow-right"
      @click="queryNext"
    ></el-button>
  </div>
</template>
<script>
import { TIME_TYPE } from "@/store/constQuantity";
export default {
  name: "TimeTool",
  props: {
    time: {
      type: [Number, Object],
      default: +new Date()
    },
    val: {
      type: [Number, Object, String]
    },
    typeID: {
      type: Number,
      default: 14
    },
    showOption: {
      type: Boolean,
      default: true
    },
    selectDisable: {
      type: Boolean,
      default: false
    },
    timeType_in: {
      type: Array,
      default: function () {
        return [];
      }
    },
    isNextDisabled: {
      type: Boolean,
      default: false
    }
  },

  watch: {
    val: {
      deep: true,
      handler: function (val, oldVal) {
        this.value = val;
      }
    },
    value: {
      deep: true,
      handler: function (val, oldVal) {
        if (val) this.changDate(val);
      }
    },
    typeID: {
      deep: true,
      handler: function (val, oldVal) {
        if (val === oldVal) return;
        this.$set(
          this,
          "currentTimeOption",
          this._.find(this.timeType_in, { typeID: val })
        );
        if (this.value > new Date().getTime()) {
          this.value = new Date().getTime();
        }
        this.$emit("change", {
          val: this.value,
          timeOption: this._.find(this.timeType_in, {
            typeID: this.currentTimeOption.typeID
          })
        });
        this.changeTimeFormat();
      }
    },
    currentTimeOption: {
      deep: true,
      handler: function (val, oldVal) {
        if (this._.get(val.typeID) === this._.get(oldVal.typeID)) return;
        this.$emit("change", { val: this.value, timeOption: val });
      }
    }
  },
  computed: {
    nextDisabled() {
      return this.isNextDisabled
        ? this.$moment(this.value)
            .startOf(this.currentTimeOption.unit)
            .valueOf() >=
            this.$moment().startOf(this.currentTimeOption.unit).valueOf()
        : false;
    }
  },
  data(vm) {
    return {
      pickerOptions: {},
      pickerOptionsByWeek: {
        firstDayOfWeek: 1
      },
      timeOptions: vm.timeType_in,
      value: +new Date(),
      currentTimeOption: this._.find(vm.timeType_in, { typeID: this.typeID }),
      formatDate: null
      // currentDate: +this.time
    };
  },
  methods: {
    // 通过匹配key与value返回timeType对应对象
    findTimeType(key, value) {
      return this.timeOptions.find(item => {
        return item[key] === value;
      });
    },
    queryPrv() {
      var date = this.$moment(this.value);
      this.value = date.subtract(1, this.currentTimeOption.unit).valueOf();
    },
    queryNext() {
      var date = this.$moment(this.value);
      this.value = date.add(1, this.currentTimeOption.unit).valueOf();
    },
    changDate(val) {
      const typeID = this.currentTimeOption.typeID;
      if (typeID === 13) {
        val = new Date(val).getTime();
      }
      this.value = val;
      this.$emit("update:val", val);
      this.$emit("change", {
        val,
        timeOption: this._.find(this.timeType_in, {
          typeID: this.currentTimeOption.typeID
        })
      });
      this.changeTimeFormat();
    },
    changTimeType(val) {
      if (this.value > new Date().getTime()) {
        this.value = new Date().getTime();
      }
      this.$emit("change", { val: this.value, timeOption: val });
      this.changeTimeFormat();
    },
    // 改变时间时，相应时间显示
    changeTimeFormat() {
      const typeID = this.currentTimeOption.typeID;
      switch (typeID) {
        case 7:
          this.formatDate = this.$moment(this.value).format("YYYY-MM-DD");
          break;
        case 12:
          this.formatDate = this.$moment(this.value).format("YYYY-MM");
          break;
        case 13:
          this.formatDate =
            this.$moment(this.value).subtract(9, "w").format("yyyy第ww周-") +
            this.$moment(this.value).format("ww周");
          break;
        case 14:
          this.formatDate = this.$moment(this.value).format("YYYY");
          break;
        case 17:
          this.formatDate =
            this.$moment(this.value).subtract(9, "y").format("YYYY-") +
            this.$moment(this.value).format("YYYY");
          break;
      }
    }
  },
  created: function () {},
  mounted() {
    this.value = this.val;
    this.timeOptions =
      this.timeType_in.length > 0 ? this.timeType_in : TIME_TYPE;
  }
};
</script>
