<template>
  <div>
    <CetDialog v-bind="CetDialog_add" v-on="CetDialog_add.event" class="small">
      <div class="eem-cont-c1">
        <div class="mbJ3">
          <span>{{$T("处理描述")}}：</span>
        </div>
        <div style="height: 120px">
          <el-input
            type="textarea"
            rows="5"
            resize="none"
            :placeholder="$T('请输入内容')"
            v-model="description"
            onKeypress="javascript:if(event.keyCode == 32)event.returnValue = false;"
          ></el-input>
        </div>
      </div>
      <span slot="footer">
        <CetButton
          v-if="CetButton_cancel.visible_in"
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-if="CetButton_confirm.visible_in"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
        <CetButton
          v-if="CetButton_submit.visible_in"
          v-bind="CetButton_submit"
          v-on="CetButton_submit.event"
        ></CetButton>
        <CetButton
          v-if="CetButton_noSubmit.visible_in"
          v-bind="CetButton_noSubmit"
          v-on="CetButton_noSubmit.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>

<script>
import customApi from "@/api/custom.js";
export default {
  name: "toExamine",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object,
      default() {
        return {};
      }
    },
    codes_in: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    }
  },
  data(vm) {
    return {
      isOk: true,
      CetDialog_add: {
        title: $T("审核"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_add_open_out,
          close_out: this.CetDialog_add_close_out
        },
        showClose: true
      },
      CetButton_confirm: {
        visible_in: false,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_submit: {
        visible_in: true,
        disable_in: false,
        title: $T("通过"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_submit_statusTrigger_out
        }
      },
      CetButton_noSubmit: {
        visible_in: true,
        disable_in: false,
        title: $T("不通过"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_noSubmit_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      description: ""
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;

      if (!this.inputData_in || !this.inputData_in.code) {
        this.CetButton_confirm.visible_in = false;
        vm.CetDialog_add.openTrigger_in = val;
        this.description = "";
      } else {
        this.CetButton_confirm.visible_in = true;
        new Promise((res, err) => {
          this.GetRepairCheckStashCode_out(res);
        }).then(data => {
          vm.CetDialog_add.openTrigger_in = val;
          if (data && data.remark) {
            this.description = data.remark;
          } else {
            this.description = "";
          }
        });
      }

      // this.reset();
    },
    closeTrigger_in(val) {
      this.CetDialog_add.closeTrigger_in = val;
    }
  },

  methods: {
    CetDialog_add_open_out(val) {},
    CetDialog_add_close_out(val) {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_add.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      const params = {
        code: this.inputData_in.code,
        params: {
          remark: this.description
        }
      };
      this.queryRepairCheckStash_out(params);
    },
    CetButton_submit_statusTrigger_out(val) {
      // let params = {
      //   codes: this.codes_in,
      //   params: {
      //     remark: this.description,
      //     taskResult: true
      //   }
      // };
      this.codes_in = this.codes_in || [];
      const codes = this.codes_in.map(item => {
        return item.code;
      });
      const dataList = this.codes_in.map(item => {
        return {
          id: item.id,
          modelLabel: "pmworksheet"
        };
      });
      const params = {
        codes: codes,
        params: {
          formDataList: dataList,
          remark: this.description,
          taskResult: true
        }
      };
      this.queryRepairCommonSubmitBatch_out(params);
    },
    //taskobject不传则为审核不通过
    CetButton_noSubmit_statusTrigger_out(val) {
      this.codes_in = this.codes_in || [];
      const codes = this.codes_in.map(item => {
        return item.code;
      });
      const dataList = this.codes_in.map(item => {
        return {
          id: item.id,
          modelLabel: "pmworksheet"
        };
      });
      const params = {
        codes: codes,
        params: {
          formDataList: dataList,
          remark: this.description,
          taskResult: false
        }
      };
      this.queryRepairCommonSubmitBatch_out(params);
    },
    init() {},
    //提交表单数据公共方法，写入内容由调用端自己决定，推动流程进入下一步
    queryRepairCommonSubmitBatch_out(params) {
      const _this = this;
      if (!params || !params.codes) {
        return;
      }

      customApi
        .queryRepairCommonSubmitBatch(params)
        .then(res => {
          if (res.code === 0) {
            _this.CetDialog_add.closeTrigger_in = this._.cloneDeep(
              new Date().getTime()
            );
            _this.$emit("confirm_out", {});
            _this.$message.success($T("审核工单成功"));
          }
        })
        .catch(() => {});
    },
    //批量审核工单
    queryRepairCheckBatch_out(params) {
      const _this = this;
      if (!params || !params.codes) {
        return;
      }

      customApi
        .queryRepairCheckBatch(params)
        .then(res => {
          if (res.code === 0) {
            _this.CetDialog_add.closeTrigger_in = this._.cloneDeep(
              new Date().getTime()
            );
            _this.$emit("confirm_out", {});
            _this.$message.success($T("审核工单成功"));
          }
        })
        .catch(() => {});
    },
    //暂存审核信息
    queryRepairCheckStash_out(params) {
      const _this = this;
      if (!params || !params.code) {
        return;
      }

      customApi.queryRepairCheckStash(params).then(res => {
        if (res.code === 0) {
          _this.CetDialog_add.closeTrigger_in = this._.cloneDeep(
            new Date().getTime()
          );
          _this.$emit("confirm_out", {});
          _this.$message.success($T("暂存审核信息成功"));
        }
      });
    },
    //查询暂存审核信息
    GetRepairCheckStashCode_out(callback) {
      if (!this.inputData_in || !this.inputData_in.code) {
        callback && callback();
        return;
      }
      const params = {
        code: this.inputData_in.code
      };

      customApi
        .GetRepairCheckStashCode(params)
        .then(res => {
          if (res.code === 0) {
            callback && callback(res.data);
          } else {
            callback && callback();
          }
        })
        .catch(() => {});
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped></style>
