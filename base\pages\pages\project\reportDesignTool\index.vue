<template>
  <div id="report">
    <!-- <iframe src="http://***********:8070/ureport/designer" frameborder="0" width="100%" height="100%"></iframe> -->
  </div>
</template>

<script>
import { FullScreenLoading } from "@omega/http/loading.js";
const loading = new FullScreenLoading();
export default {
  name: "reportTool",
  components: {},
  computed: {
    systemCfg() {
      return this.$store.state.systemCfg;
    }
  },
  data() {
    return {};
  },
  watch: {},
  methods: {},
  mounted() {
    loading.showLoading();
    const iframe = document.createElement("iframe");

    // const hostname = this._.get(this.systemCfg, "reportsever", location.hostname) || location.hostname;
    // iframe.src = "http://************:8070/ureport/designer";
    // iframe.src = "http://" + hostname + ":8070/ureport/designer";
    iframe.src = `${location.origin}/ureport/designer`;
    iframe.width = "100%";
    iframe.height = "100%";
    iframe.frameBorder = "0";
    iframe.onload = () => {
      loading.hideLoading();
    };
    document.getElementById("report").appendChild(iframe);
  }
};
</script>

<style lang="scss" scoped>
#report {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
