<template>
  <div class="page">
    <el-container style="height: 100%">
      <el-aside style="width: 200px" class="flex-column">
        <div class="common-title-H2 mbJ3">事件类型</div>
        <el-main class="flex-auto" style="padding: 0px">
          <div
            v-for="(item, index) in listData"
            :key="index"
            :class="{
              clearfix: true,
              listItem: true,
              action: itemAction == index
            }"
            @click="listDataClick(index)"
          >
            <div
              class="fl ellipsis"
              style="width: calc(100% - 20px)"
              :title="`${item.eventClassifiedName}(${
                item.faultScenariosList ? item.faultScenariosList.length : 0
              })`"
            >
              {{ item.eventClassifiedName }}
            </div>
            <div class="fl" style="width: 20px">
              ({{
                item.faultScenariosList ? item.faultScenariosList.length : 0
              }})
            </div>
          </div>
        </el-main>
      </el-aside>
      <el-container style="padding: 0px 10px; height: 100%">
        <el-header height="58px" style="padding: 0px; line-height: 58px">
          <ElSelect
            v-model="ElSelect_1.value"
            v-bind="ElSelect_1"
            v-on="ElSelect_1.event"
            class="remoteElSelect"
          >
            <ElOptionGroup
              v-for="(group, index) in ElOptionGroup_1.options_in"
              :key="index"
              :label="ElOptionGroup_1.title"
            >
              <div class="remoteElSelectTitle">{{ group.title }}</div>
              <ElOption
                v-for="item in group.options_in"
                :key="item[group.key]"
                :label="item[group.label]"
                :value="item[group.value]"
                :disabled="item[group.disabled]"
              >
                <div class="remoteElSelectText">{{ item[group.label] }}</div>
                <div class="remoteElSelectFrom">
                  {{
                    group.title == "故障场景"
                      ? `来自：${item.eventClassificationName}`
                      : `来自：${item.eventClassificationName} > ${item.faultScenariosName}`
                  }}
                </div>
              </ElOption>
            </ElOptionGroup>
          </ElSelect>
          <CetButton
            class="fr mlJ1"
            v-bind="CetButton_1"
            v-on="CetButton_1.event"
          ></CetButton>
          <CetButton
            class="fr mlJ1"
            v-bind="CetButton_2"
            v-on="CetButton_2.event"
          ></CetButton>
        </el-header>
        <el-main
          style="height: calc(100% - 58px); padding: 0px; position: relative"
          class="accidentHandleMain"
        >
          <div
            v-for="(item, index) in listData"
            :key="index"
            class="content-box"
            :id="`accidentHandleListData${index}`"
          >
            <div class="content-box-title">
              {{ item.eventClassifiedName }} ({{
                item.faultScenariosList ? item.faultScenariosList.length : 0
              }})
            </div>
            <div class="content-box-list">
              <div
                class="content-box-list-item ellipsis"
                v-for="(childrenItem, childrenIndex) in item.faultScenariosList"
                :key="childrenIndex"
              >
                <span
                  v-if="childrenIndex < 11"
                  :title="childrenItem.name"
                  @click="toClassDetail(item)"
                >
                  {{ childrenItem.name }}
                </span>
                <span v-else class="fcZS" @click="toClassDetail(item)">
                  查看更多
                </span>
              </div>
            </div>
          </div>
          <div style="height: 100%"></div>
        </el-main>
      </el-container>
    </el-container>
    <dialogUpload
      :visibleTrigger_in="dialogUpload.visibleTrigger_in"
      :closeTrigger_in="dialogUpload.closeTrigger_in"
      :inputData_in="dialogUpload.inputData_in"
      @uploadFile="dialogUpload_uploadFile"
      @download="dialogUpload_download"
      v-bind="dialogUpload"
    />
    <addExpertKnowledge
      :visibleTrigger_in="addExpertKnowledge.visibleTrigger_in"
      :closeTrigger_in="addExpertKnowledge.closeTrigger_in"
      :eventType_in="addExpertKnowledge.eventType_in"
      :inputData_in="addExpertKnowledge.inputData_in"
    />
    <CetInterface
      :data.sync="CetInterface_listData.data"
      :dynamicInput.sync="CetInterface_listData.dynamicInput"
      v-bind="CetInterface_listData"
      v-on="CetInterface_listData.event"
    ></CetInterface>
  </div>
</template>

<script>
import common from "eem-utils/common";
import dialogUpload from "./dialogUpload";
import addExpertKnowledge from "./addExpertKnowledge";
import custom from "@/api/custom";
export default {
  name: "accidentHandle",
  components: {
    dialogUpload,
    addExpertKnowledge
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.localStorage) {
          return false;
        } else {
          var storage = window.localStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    }
  },
  data() {
    return {
      listData: [
        // {
        //   eventClassifiedName: "电能质量类",
        //   num: 20,
        //   eventClassified: 1,
        //   faultScenariosList: [
        //     {
        //       alarmKeywordName: "故障场景2",
        //       alarmkeyword: 104,
        //       children: null,
        //       deviceClassificationName: "低压母线",
        //       deviceclassification: 8,
        //       eventClassificationName: "低压开关类",
        //       eventclassification: 8,
        //       eventplan_model: null,
        //       id: 4,
        //       modelLabel: "faultscenarios",
        //       name: "场景名称2",
        //       projectid: 2
        //     }
        //   ]
        // }
      ],
      itemAction: 0,
      ElSelect_1: {
        value: "",
        size: "small",
        style: {
          width: "200px"
        },
        placeholder: "请输入关键字以检索",
        filterable: true,
        remote: true,
        loading: false,
        "remote-method": this.ElSelect_1_remoteMethod,
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElOptionGroup_1: {
        options_in: [
          {
            title: "故障场景",
            options_in: [],
            key: "id",
            value: "id",
            label: "name",
            disabled: "disabled"
          },
          {
            title: "处理预案",
            options_in: [],
            key: "id",
            value: "id",
            label: "name",
            disabled: "disabled"
          }
        ]
      },
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: "新增专家知识",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: "导入专家知识",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      dialogUpload: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        title_in: "上传专家知识",
        download_text_in: "专家知识样例下载",
        tipsData_in: null
      },
      addExpertKnowledge: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        eventType_in: null
      },
      CetInterface_listData: {
        queryMode: "diff", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "getEventReservePlan",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {},
        page_in: null, // exp:{ index: 1, limit: 20 }
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          result_out: this.CetInterface_listData_result_out,
          finishTrigger_out: this.CetInterface_listData_finishTrigger_out,
          failTrigger_out: this.CetInterface_listData_failTrigger_out,
          totalNum_out: this.CetInterface_listData_totalNum_out
        }
      }
    };
  },
  watch: {},

  methods: {
    CetInterface_listData_finishTrigger_out(val) {},
    CetInterface_listData_failTrigger_out(val) {},
    CetInterface_listData_totalNum_out(val) {},
    CetInterface_listData_result_out(val) {
      this.listData = val || [];
    },
    ElSelect_1_change_out(val) {
      var eventclassification,
        faultScenariosId_in,
        eventPlanId_in,
        ElSelectData;
      if (val.split("_")[0] == "faultScenarios") {
        // 场景
        ElSelectData = this.ElOptionGroup_1.options_in[0].options_in.filter(
          item => item.id == val
        )[0];
        faultScenariosId_in = ElSelectData.faultScenariosId;
      } else if (val.split("_")[0] == "eventPlan") {
        // 预案
        ElSelectData = this.ElOptionGroup_1.options_in[1].options_in.filter(
          item => item.id == val
        )[0];
        faultScenariosId_in = ElSelectData.faultScenariosId;
        eventPlanId_in = ElSelectData.eventPlanId;
      }
      eventclassification = ElSelectData.eventclassification;
      var info = this.listData.filter(
        item => item.eventClassified === eventclassification
      )[0];
      var data = this._.cloneDeep(info);
      data.faultScenariosId_in = faultScenariosId_in;
      data.eventPlanId_in = eventPlanId_in;
      this.toClassDetail(data);
    },
    CetButton_1_statusTrigger_out(val) {
      this.addExpertKnowledge.inputData_in = this._.cloneDeep(this.listData);
      this.addExpertKnowledge.visibleTrigger_in = new Date().getTime();
    },
    CetButton_2_statusTrigger_out(val) {
      this.dialogUpload.visibleTrigger_in = new Date().getTime();
    },
    listDataClick(index) {
      this.itemAction = index;
      $(".accidentHandleMain").animate({
        scrollTop: $(`#accidentHandleListData${index}`)[0].offsetTop + "px"
      });
    },
    toClassDetail(item) {
      var params = {
        eventClassified: item.eventClassified,
        eventClassifiedName: item.eventClassifiedName,
        faultScenariosId_in: item.faultScenariosId_in,
        eventPlanId_in: item.eventPlanId_in
      };
      this.$emit("toclassDetail", params);
    },
    dialogUpload_uploadFile(val) {
      const formData = new FormData();
      formData.append("file", val.file);
      custom.importEventPlan(formData, this.projectId).then(response => {
        if (response.code === 0) {
          this.$message({
            type: "success",
            message: "导入成功！"
          });
          this.dialogUpload.closeTrigger_in = new Date().getTime();
          this.CetInterface_listData.queryTrigger_in = new Date().getTime();
        }
      });
    },
    dialogUpload_download() {
      common.downExcel(
        `/eem-service/v1/expert/exportEventPlan?projectId=` + this.projectId,
        null,
        this.token
      );
    },
    // 远程搜索方法
    ElSelect_1_remoteMethod(query) {
      this.ElSelect_1.loading = true;
      custom.getKnowledgeKeyword(query).then(response => {
        this.ElSelect_1.loading = false;
        this.ElOptionGroup_1.options_in[0].options_in = [];
        this.ElOptionGroup_1.options_in[1].options_in = [];
        if (response.code === 0 && response.data) {
          if (
            response.data.faultScenariosList &&
            response.data.faultScenariosList.length > 0
          ) {
            response.data.faultScenariosList.forEach((item, index) => {
              item.faultScenariosName = item.name; // 场景
              item.faultScenariosId = item.id; // 场景
              item.id = "faultScenarios_" + item.id;
            });
            this.ElOptionGroup_1.options_in[0].options_in =
              response.data.faultScenariosList;
          }
          if (
            response.data.eventPlanList &&
            response.data.eventPlanList.length > 0
          ) {
            response.data.eventPlanList.forEach(item => {
              if (item.faultscenarios_model) {
                item.faultScenariosName = item.faultscenarios_model[0].name; // 场景
                item.faultScenariosId = item.faultscenarios_model[0].id; // 场景
                item.eventclassification =
                  item.faultscenarios_model[0].eventclassification; // 事件归类
                item.eventClassificationName =
                  item.faultscenarios_model[0].eventClassificationName; // 事件归类
              }
              item.eventPlanId = item.id;
              item.id = "eventPlan_" + item.id;
            });
            this.ElOptionGroup_1.options_in[1].options_in =
              response.data.eventPlanList;
          }
        }
      });
    }
  },

  created: function () {},
  activated: function () {
    this.CetInterface_listData.queryTrigger_in = new Date().getTime();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.eventTitle {
  line-height: 20px;
  font-size: 12px;
  // color: rgb(153, 153, 153);
}
.listItem {
  margin-top: 10px;
  box-sizing: border-box;
  padding: 0 10px 0 15px;
  cursor: pointer;
  line-height: 30px;
  border-radius: 5px;
  &.action {
    @include background_color(BG4);
    // @include font_color(T5);
  }
}
.content-box {
  margin: 0 10px 10px 10px;
  .content-box-title {
    font-size: 20px;
    // color: rgb(51, 51, 51);
    font-weight: 700;
    line-height: 50px;
  }
  .content-box-list {
    display: flex;
    flex-wrap: wrap;
    max-height: 100px;
    overflow: hidden;
    .content-box-list-item {
      font-size: 18px;
      // color: rgb(51, 51, 51);
      font-weight: 400;
      width: 16.6%;
      height: 50px;
      line-height: 50px;
      & > span {
        cursor: pointer;
      }
    }
  }
}
:deep(.el-select-dropdown__item) {
  height: auto;
}
.remoteElSelectTitle {
  color: #999999;
  font-size: 12px;
  padding-left: 20px;
}
.remoteElSelectText {
  line-height: 16px;
}
.remoteElSelectFrom {
  color: #999999;
  font-size: 12px;
  line-height: 16px;
}
</style>
