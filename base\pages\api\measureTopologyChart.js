import fetch from "eem-utils/fetch";
import { getCustomParameter } from "./common/common.js";
const version = "v1";

function processRequest(data) {
  // 对 data 进行任意转换处理
  return data;
}

function processResponse(response) {
  // 对 response 进行任意转换处理, response结构
  //   {
  //     // `data` 由服务器提供的响应
  //     data: {},

  //     // `status` 来自服务器响应的 HTTP 状态码
  //     status: 200,

  //     // `statusText` 来自服务器响应的 HTTP 状态信息
  //     statusText: 'OK',

  //     // `headers` 服务器响应的头
  //     headers: {},

  //     // `config` 是为请求提供的配置信息
  //     config: {}
  //   }
  return response;
}
// 获取拓扑图
export function getMeasureTopology(data) {
  return fetch({
    url: `/eem-service/${version}/topology/network/${data.projectId}/${data.energytype}/${data.n}`,
    method: "POST",
    data: data.nodes
  });
}
// 获取表格
export function getMeasureTable(data) {
  var expressions = _.get(data, "rootCondition.filter.expressions");
  var energytype;
  var hierarchy;
  var projectId;
  if (!_.isEmpty(expressions)) {
    energytype = getCustomParameter(expressions, "energytype");
    hierarchy = getCustomParameter(expressions, "hierarchy");
    projectId = getCustomParameter(expressions, "projectId");
  }
  return fetch({
    url: `/eem-service/${version}/topology/network/form/${projectId}/${energytype}/${hierarchy}`,
    method: "POST",
    data: []
  });
}
// 导入
export function networkImportNodeAndConnection(formData, queryData) {
  return fetch({
    url: `/eem-service/${version}/topology/network/importNodeAndConnection/${queryData.projectId}`,
    method: "POST",
    data: formData
  });
}
