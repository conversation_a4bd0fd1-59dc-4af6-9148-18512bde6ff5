<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="min"
    >
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <CetForm
        class="eem-cont-c1"
        :data.sync="CetForm_User.data"
        v-bind="CetForm_User"
        v-on="CetForm_User.event"
      >
        <el-form-item
          class="custom-form-item"
          :label="$T('原密码')"
          prop="oldPassword"
        >
          <ElInput
            clearable
            type="password"
            maxlength="18"
            :placeholder="$T('请输入巡检确认密码')"
            v-model.trim="CetForm_User.data.oldPassword"
          ></ElInput>
        </el-form-item>
        <el-form-item
          class="custom-form-item"
          :label="$T('新密码')"
          prop="newPassword"
        >
          <ElInput
            clearable
            type="password"
            maxlength="18"
            :placeholder="$T('请输入新的密码')"
            v-model.trim="CetForm_User.data.newPassword"
          ></ElInput>
        </el-form-item>
        <el-form-item
          class="custom-form-item"
          :label="$T('密码确认')"
          prop="_checkPassword"
        >
          <ElInput
            clearable
            type="password"
            maxlength="18"
            :placeholder="$T('请输入确认密码')"
            v-model.trim="CetForm_User.data._checkPassword"
          ></ElInput>
        </el-form-item>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";
import AES from "crypto-js/aes";
import Hex from "crypto-js/enc-hex";

export default {
  name: "editUserDialog",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("修改密码"),
        showClose: true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // ResetUserPassword表单组件
      CetForm_User: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "editInspectorCheckPasswordSecurity",
          modelLabel: "",
          dataIndex: ["id", "newPassword", "_checkPassword", "oldPassword"], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "110px",
        labelPosition: "top",
        rules: {
          oldPassword: [
            {
              min: 1,
              max: 18,
              message: $T("长度在 1 到 {0} 个字符", 18),
              trigger: ["blur", "change"]
            },
            {
              required: true,
              type: "string",
              message: $T("原密码不能为空"),
              trigger: "blur",
              validator: (rule, value, callback) => {
                if (this._.isNil(value) || value === "") {
                  callback(new Error());
                  return;
                }

                callback();
              }
            }
          ],
          newPassword: [
            {
              min: 1,
              max: 18,
              message: $T("长度在 1 到 {0} 个字符", 18),
              trigger: ["blur", "change"]
            },
            {
              required: true,
              type: "string",
              message: $T("新密码不能为空"),
              trigger: "blur",
              validator: (rule, value, callback) => {
                if (this._.isNil(value) || value === "") {
                  callback(new Error());
                  return;
                }

                callback();
              }
            }
          ],
          _checkPassword: [
            {
              required: true,
              type: "string",
              message: $T("密码不一致"),
              trigger: "blur",
              validator: (rule, value, callback) => {
                if (value !== this.CetForm_User.data.newPassword) {
                  callback(new Error());
                  return;
                }

                callback();
              }
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_User_currentData_out,
          saveData_out: this.CetForm_User_saveData_out,
          finishData_out: this.CetForm_User_finishData_out,
          finishTrigger_out: this.CetForm_User_finishTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
      this.CetForm_User.data = {
        newPassword: "",
        _checkPassword: "",
        oldPassword: ""
      };
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {
      this.CetForm_User.queryId_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      this.CetForm_User.inputData_in = this._.cloneDeep(val);
    }
  },
  methods: {
    CetForm_User_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_User_saveData_out(val) {
      // this.$emit("saveData_out", this._.cloneDeep(val));

      // eslint-disable-next-line promise/param-names
      new Promise((res, err) => {
        this.editInspectorCheckPassword_out(res);
      }).then(data => {
        if (data) {
          this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
          this.$emit("saveData_out", val);
        }
      });
    },
    CetForm_User_finishData_out(val) {
      this.$emit("finishData_out", this._.cloneDeep(val));
    },
    CetForm_User_finishTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
      this.$emit("finishTrigger_out", val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetForm_User.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      this.CetForm_User.localSaveTrigger_in = new Date().getTime();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    editInspectorCheckPassword_out(callback) {
      const params = this._.cloneDeep(this.CetForm_User.data);

      // params.name = this.encryptUsers(params.name);
      params.newPassword = this.encryptUsers(params.newPassword);
      params.oldPassword = this.encryptUsers(params.oldPassword);

      customApi.editInspectorCheckPasswordSecurity(params).then(res => {
        if (res.code === 0) {
          // eslint-disable-next-line standard/no-callback-literal
          callback && callback(true);
        }
      });
    },
    // 加密处理方法
    encryptUsers: function (value) {
      var vm = this;
      var str = vm.stringToHex("8924534290ABCDEF1264147890ACAB56");
      // eslint-disable-next-line no-undef
      var key = Hex.parse(str);
      // eslint-disable-next-line no-undef
      var iv = Hex.parse("2934577290ABCDEF1264147890ACAE75");
      // eslint-disable-next-line no-undef
      var AESValue = AES.encrypt(value, key, { iv: iv }).toString();

      return AESValue;
    },
    stringToHex: function (str) {
      var val = "";
      for (var i = 0; i < str.length; i++) {
        if (val === "") {
          val = str.charCodeAt(i).toString(16);
        } else {
          val += str.charCodeAt(i).toString(16);
        }
      }
      return val;
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped></style>
