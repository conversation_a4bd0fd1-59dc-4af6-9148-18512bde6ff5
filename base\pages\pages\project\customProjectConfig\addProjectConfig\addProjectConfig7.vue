<template>
  <!-- 1弹窗组件 -->
  <CetDialog
    class="CetDialog"
    ref="CetDialog"
    v-bind="CetDialog_1"
    v-on="CetDialog_1.event"
  >
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
    <CetForm
      class="addProjectConfig8"
      :data.sync="CetForm_1.data"
      v-bind="CetForm_1"
      v-on="CetForm_1.event"
    >
      <div class="bg1 brC2 rowBox">
        <el-row :gutter="$J3">
          <el-col :span="8">
            <el-form-item label="节点名称" prop="name">
              <ElInput
                v-model="CetForm_1.data.name"
                v-bind="ElInput_1"
                v-on="ElInput_1.event"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="资产编码" prop="asset">
              <ElInputNumber
                v-model="CetForm_1.data.asset"
                v-bind="ElInputNumber_8"
                v-on="ElInputNumber_8.event"
              ></ElInputNumber>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="编号" prop="code">
              <ElInput
                v-model="CetForm_1.data.code"
                v-bind="ElInput_10"
                v-on="ElInput_10.event"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="安装位置" prop="location">
              <ElInput
                v-model="CetForm_1.data.location"
                v-bind="ElInput_1"
                v-on="ElInput_1.event"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="更换日期" prop="changedate">
              <el-date-picker
                v-model="CetForm_1.data.changedate"
                v-bind="CetDatePicker_5.config"
                placeholder="选择日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="功能类型" prop="functiontype">
              <ElSelect
                v-model="CetForm_1.data.functiontype"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in pumpfunctiontypeList"
                  :key="item[ElOption_5.key]"
                  :label="item[ElOption_5.label]"
                  :value="item[ElOption_5.value]"
                  :disabled="item[ElOption_5.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="额定冲次（次/min）" prop="ratedblankingtimes">
              <ElInputNumber
                v-model="CetForm_1.data.ratedblankingtimes"
                v-bind="ElInputNumber_int"
                v-on="ElInputNumber_int.event"
              ></ElInputNumber>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="额定冲程（mm）" prop="ratedstroke">
              <ElInputNumber
                v-model="CetForm_1.data.ratedstroke"
                v-bind="ElInputNumber_10"
                v-on="ElInputNumber_10.event"
              ></ElInputNumber>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="泵类型" prop="physicaltype">
              <ElSelect
                v-model="CetForm_1.data.physicaltype"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in pumptypeList"
                  :key="item[ElOption_7.key]"
                  :label="item[ElOption_7.label]"
                  :value="item[ElOption_7.value]"
                  :disabled="item[ElOption_7.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="电机厂家"
              prop="electricalmachinerymanufacturer"
            >
              <ElInput
                v-model="CetForm_1.data.electricalmachinerymanufacturer"
                v-bind="ElInput_10"
                v-on="ElInput_10.event"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工作介质类型" prop="operationmediumtype">
              <ElSelect
                v-model="CetForm_1.data.operationmediumtype"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in operationmediumtypeList"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="电机型号" prop="motortype">
              <ElInput
                v-model="CetForm_1.data.motortype"
                v-bind="ElInput_11"
                v-on="ElInput_11.event"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="型号" prop="model">
              <ElInput
                v-model="CetForm_1.data.model"
                v-bind="ElInput_12"
                v-on="ElInput_12.event"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检修周期(天)" prop="maintenanceperiod">
              <ElInputNumber
                v-model="CetForm_1.data.maintenanceperiod"
                v-bind="ElInputNumber_1"
                v-on="ElInputNumber_1.event"
              ></ElInputNumber>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="上次检修日期" prop="lastoverhauldate">
              <el-date-picker
                v-model="CetForm_1.data.lastoverhauldate"
                type="date"
                :editable="false"
                placeholder="选择日期"
                value-format="timestamp"
                format="yyyy-MM-dd"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="下次检修日期" prop="nextoverhauldate">
              <el-date-picker
                v-model="CetForm_1.data.nextoverhauldate"
                type="date"
                :editable="false"
                placeholder="选择日期"
                value-format="timestamp"
                format="yyyy-MM-dd"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="资产归属" prop="ownship">
              <ElInput
                v-model="CetForm_1.data.ownship"
                v-bind="ElInput_1"
                v-on="ElInput_1.event"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备归类:" prop="deviceclassification">
              <ElSelect
                v-model="CetForm_1.data.deviceclassification"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in ElOption_deviceclassification.options_in"
                  :key="item[ElOption_deviceclassification.key]"
                  :label="item[ElOption_deviceclassification.label]"
                  :value="item[ElOption_deviceclassification.value]"
                  :disabled="item[ElOption_deviceclassification.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="电机额定功率（kW）" prop="ratedmotorpower">
              <ElInputNumber
                v-model="CetForm_1.data.ratedmotorpower"
                v-bind="ElInputNumber_11"
                v-on="ElInputNumber_11.event"
              ></ElInputNumber>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出厂日期" prop="manufacturedate">
              <el-date-picker
                v-model="CetForm_1.data.manufacturedate"
                v-bind="CetDatePicker_6.config"
                placeholder="选择日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="电机额定电流（A）" prop="ratedmotorcurrent">
              <ElInputNumber
                v-model="CetForm_1.data.ratedmotorcurrent"
                v-bind="ElInputNumber_12"
                v-on="ElInputNumber_12.event"
              ></ElInputNumber>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="生产厂家" prop="manufactor">
              <ElInput
                v-model="CetForm_1.data.manufactor"
                v-bind="ElInput_13"
                v-on="ElInput_13.event"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="电机额定功率因数" prop="ratedmotorpowerfactor">
              <ElInputNumber
                v-model="CetForm_1.data.ratedmotorpowerfactor"
                v-bind="ElInputNumber_13"
                v-on="ElInputNumber_13.event"
              ></ElInputNumber>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="投用日期" prop="commissiondate">
              <el-date-picker
                v-model="CetForm_1.data.commissiondate"
                v-bind="CetDatePicker_7.config"
                placeholder="选择日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="电机额定效率（%）" prop="ratedmotorefficiency">
              <ElInputNumber
                v-model="CetForm_1.data.ratedmotorefficiency"
                v-bind="ElInputNumber_14"
                v-on="ElInputNumber_14.event"
              ></ElInputNumber>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="额定扬程（m）" prop="ratedlift">
              <ElInputNumber
                v-model="CetForm_1.data.ratedlift"
                v-bind="ElInputNumber_15"
                v-on="ElInputNumber_15.event"
              ></ElInputNumber>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="冷却方式" prop="coolingmode">
              <ElInput
                v-model="CetForm_1.data.coolingmode"
                v-bind="ElInput_14"
                v-on="ElInput_14.event"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="额定流量（m³/h）" prop="rateddischarge">
              <ElInputNumber
                v-model="CetForm_1.data.rateddischarge"
                v-bind="ElInputNumber_16"
                v-on="ElInputNumber_16.event"
              ></ElInputNumber>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="润滑方式" prop="lubricationmode">
              <ElInput
                v-model="CetForm_1.data.lubricationmode"
                v-bind="ElInput_15"
                v-on="ElInput_15.event"
              ></ElInput>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="额定转速（r/min）" prop="ratedspeed">
              <ElInputNumber
                v-model="CetForm_1.data.ratedspeed"
                v-bind="ElInputNumber_17"
                v-on="ElInputNumber_17.event"
              ></ElInputNumber>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="使用状态" prop="usagestate">
              <ElSelect
                v-model="CetForm_1.data.usagestate"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in usagestateList"
                  :key="item[ElOption_9.key]"
                  :label="item[ElOption_9.label]"
                  :value="item[ElOption_9.value]"
                  :disabled="item[ElOption_9.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="额定工作压力（Mpa）"
              prop="ratedworkingpressure"
            >
              <ElInputNumber
                v-model="CetForm_1.data.ratedworkingpressure"
                v-bind="ElInputNumber_18"
                v-on="ElInputNumber_18.event"
              ></ElInputNumber>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否报废" prop="isscrap">
              <ElSelect
                v-model="CetForm_1.data.isscrap"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in ElOption_10.options_in"
                  :key="item[ElOption_10.key]"
                  :label="item[ElOption_10.label]"
                  :value="item[ElOption_10.value]"
                  :disabled="item[ElOption_10.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="效率节能评价值" prop="effevaluation">
              <ElInputNumber
                v-model="CetForm_1.data.effevaluation"
                v-bind="ElInputNumber_21"
                v-on="ElInputNumber_21.event"
              ></ElInputNumber>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="效率限定值" prop="efflimit">
              <ElInputNumber
                v-model="CetForm_1.data.efflimit"
                v-bind="ElInputNumber_22"
                v-on="ElInputNumber_22.event"
              ></ElInputNumber>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="空调系统类型" prop="airconditionsystemtype">
              <ElSelect
                v-model="CetForm_1.data.airconditionsystemtype"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in airconditionsystemtypeList"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="频率类型" prop="frequencytype">
              <ElSelect
                v-model="CetForm_1.data.frequencytype"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in frequencytypeList"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="一次泵或二次泵" prop="pumpcycletype">
              <ElSelect
                v-model="CetForm_1.data.pumpcycletype"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in pumpcycletypeList"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </ElSelect>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="报废日期" prop="scrapdate">
              <el-date-picker
                v-model="CetForm_1.data.scrapdate"
                v-bind="CetDatePicker_4.config"
                placeholder="选择日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工作频率（Hz）" prop="minworkingfrequency">
              <el-row>
                <el-col :span="11">
                  <ElInputNumber
                    v-model="CetForm_1.data.minworkingfrequency"
                    v-bind="ElInputNumber_16"
                    v-on="ElInputNumber_16.event"
                  ></ElInputNumber>
                </el-col>
                <el-col class="line" :span="2">~</el-col>
                <el-col :span="11">
                  <ElInputNumber
                    v-model="CetForm_1.data.maxworkingfrequency"
                    v-bind="ElInputNumber_16"
                    v-on="ElInputNumber_16.event"
                  ></ElInputNumber>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="相关文档" prop="test">
              <div class="mb10 upload" style="float: left">
                <div
                  class="eem-pload-label"
                  :title="documentName"
                  v-if="type_in == 3"
                >
                  {{ documentName || "--" }}
                  <el-button
                    class="ml-10 fr"
                    style="position: absolute; top: 0px; right: 0px"
                    size="mini"
                    type="danger"
                    icon="el-icon-delete"
                    circle
                    @click="handleDeleteDocument_out"
                  ></el-button>
                </div>
                <el-upload
                  class="elupload11"
                  ref="DocElupload"
                  action="/eem-service/v1/common/uploadFile"
                  :headers="{ Authorization: this.token }"
                  :before-upload="handleBeforeUpload2"
                  :on-success="uploadSuccess2"
                  :multiple="false"
                  :limit="1"
                  :before-remove="beforeRemove"
                  :on-exceed="handleExceed"
                >
                  <el-button size="small" type="primary">
                    选择上传文件
                  </el-button>
                  <p>
                    只能上传xls/xlsx/docx/pdf格式文件,且不超过{{
                      systemCfg.uploadDocSize || "10"
                    }}M
                  </p>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="bg1 brC2 rowBox mtJ1">
        <el-row :gutter="$J3">
          <el-col :span="24">
            <el-form-item>
              <div class="label" slot="label">
                主图
                <div class="box_tip2">
                  只能上传jpg/png图片，且不超过{{ systemCfg.uploadPicSize }}M
                </div>
              </div>
              <div class="value">
                <UploadImg
                  class="uploadImg"
                  :imgUrl.sync="CetForm_1.data.pic"
                />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <!-- 查设备归类 -->
      <CetInterface
        :data.sync="CetInterface_query.data"
        :dynamicInput.sync="CetInterface_query.dynamicInput"
        v-bind="CetInterface_query"
        v-on="CetInterface_query.event"
      ></CetInterface>
    </CetForm>
  </CetDialog>
</template>
<script>
import common from "eem-utils/common";
import UploadImg from "eem-components/uploadImg.vue";
import { httping } from "@omega/http";
export default {
  name: "addProjectConfig7",
  components: {
    UploadImg
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    treeData_in: {
      type: Object
    },
    currentTabItme_in: {
      type: Object
    },
    type_in: {
      type: Number
    },
    treeNameList_in: {
      type: Array
    }
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    systemCfg() {
      return this.$store.state.systemCfg;
    }
  },

  data() {
    return {
      uploadPath2: "",
      documentName: "",
      CetDialog_1: {
        title: "添加泵类",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {
          open_out: this.CetDialog_1_open_out,
          close_out: this.CetDialog_1_close_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "保存",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "关闭",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "180px",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: "请输入节点名称",
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.pattern_name
          ],
          electricalmachinerymanufacturer: [common.check_stringLessThan255],
          manufacturer: [common.check_stringLessThan255],
          motortype: [common.check_name],
          equipmenttype: [common.check_name],
          coolingmode: [common.check_name],
          lubricationmode: [common.check_name],
          minworkingfrequency: [
            {
              required: false,
              type: "string",
              message: "最小频率要小于最大频率",
              trigger: "blur",
              validator: (rule, value, callback) => {
                if (!value) {
                  callback();
                  return;
                }
                if (value > this.CetForm_1.data.maxworkingfrequency) {
                  callback(new Error());
                  return;
                }
                callback();
              }
            }
          ]
        },
        event: {
          currentData_out: this.CetForm_1_currentData_out,
          saveData_out: this.CetForm_1_saveData_out,
          finishData_out: this.CetForm_1_finishData_out,
          finishTrigger_out: this.CetForm_1_finishTrigger_out
        }
      },
      ElInputNumber_1: {
        ...common.check_numberInt,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_1_change_out
        }
      },
      ElInputNumber_2: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_2_change_out
        }
      },
      ElInputNumber_3: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_3_change_out
        }
      },
      ElInputNumber_4: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_4_change_out
        }
      },
      ElInputNumber_5: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_5_change_out
        }
      },
      ElInputNumber_6: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_6_change_out
        }
      },
      ElInputNumber_7: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_7_change_out
        }
      },
      ElInputNumber_8: {
        ...common.check_numberInt,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_8_change_out
        }
      },
      ElInputNumber_int: {
        ...common.check_numberInt,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_int_change_out
        }
      },
      ElInputNumber_9: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_9_change_out
        }
      },
      ElInputNumber_10: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_10_change_out
        }
      },
      ElInputNumber_11: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_11_change_out
        }
      },
      ElInputNumber_12: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_12_change_out
        }
      },
      ElInputNumber_13: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_13_change_out
        }
      },
      ElInputNumber_14: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_14_change_out
        }
      },
      ElInputNumber_15: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_15_change_out
        }
      },
      ElInputNumber_16: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_16_change_out
        }
      },
      ElInputNumber_17: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_17_change_out
        }
      },
      ElInputNumber_19: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_19_change_out
        }
      },
      ElInputNumber_20: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_20_change_out
        }
      },
      ElInputNumber_21: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_21_change_out
        }
      },
      ElInputNumber_22: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_22_change_out
        }
      },
      ElInputNumber_18: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInputNumber_18_change_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_1_change_out,
          input: this.ElInput_1_input_out
        }
      },
      ElInput_2: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_2_change_out,
          input: this.ElInput_2_input_out
        }
      },
      ElInput_4: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_4_change_out,
          input: this.ElInput_4_input_out
        }
      },
      ElInput_5: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_5_change_out,
          input: this.ElInput_5_input_out
        }
      },
      ElInput_6: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_6_change_out,
          input: this.ElInput_6_input_out
        }
      },
      ElInput_7: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_7_change_out,
          input: this.ElInput_7_input_out
        }
      },
      ElInput_8: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_8_change_out,
          input: this.ElInput_8_input_out
        }
      },
      ElInput_9: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_9_change_out,
          input: this.ElInput_9_input_out
        }
      },
      ElInput_10: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_10_change_out,
          input: this.ElInput_10_input_out
        }
      },
      ElInput_11: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_11_change_out,
          input: this.ElInput_11_input_out
        }
      },
      ElInput_12: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_12_change_out,
          input: this.ElInput_12_input_out
        }
      },
      ElInput_13: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_13_change_out,
          input: this.ElInput_13_input_out
        }
      },
      ElInput_14: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_14_change_out,
          input: this.ElInput_14_input_out
        }
      },
      ElInput_15: {
        value: "",
        placeholder: "请输入内容",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_15_change_out,
          input: this.ElInput_15_input_out
        }
      },
      CetDatePicker_1: {
        disable_in: false,
        val: this.$moment().add(1, "day").startOf("day").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "date",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          style: {
            display: "block"
          }
        }
      },
      ElSelect_1: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_type: {
        value: "pump",
        style: {
          flex: 1
        },
        event: {
          change: this.ElSelect_type_change_out
        }
      },
      ElOption_type: {
        options_in: [
          // {
          //   id: "heatingfurnace",
          //   text: "加热炉"
          // },
          {
            id: "pump",
            text: "泵类设备"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElOption_5: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElOption_7: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElOption_9: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElOption_10: {
        options_in: [
          {
            id: false,
            text: "否"
          },
          {
            id: true,
            text: "是"
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetDatePicker_2: {
        disable_in: false,
        val: this.$moment().add(1, "day").startOf("day").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "date",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          style: {
            display: "block"
          }
        }
      },
      CetDatePicker_3: {
        disable_in: false,
        val: this.$moment().add(1, "day").startOf("day").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "date",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          style: {
            display: "block"
          }
        }
      },
      CetDatePicker_4: {
        disable_in: false,
        val: this.$moment().add(1, "day").startOf("day").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "date",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          style: {
            display: "block"
          }
        }
      },
      CetDatePicker_5: {
        disable_in: false,
        val: this.$moment().add(1, "day").startOf("day").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "date",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          style: {
            display: "block"
          }
        }
      },
      CetDatePicker_6: {
        disable_in: false,
        val: this.$moment().add(1, "day").startOf("day").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "date",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          style: {
            display: "block"
          }
        }
      },
      CetDatePicker_7: {
        disable_in: false,
        val: this.$moment().add(1, "day").startOf("day").valueOf(),
        config: {
          valueFormat: "timestamp",
          type: "date",
          // format: "yyyy-MM-dd",
          rangeSeparator: "-",
          style: {
            display: "block"
          }
        }
      },
      airconditionsystemtypeList: [],
      frequencytypeList: [],
      operationmediumtypeList: [],
      pumpcycletypeList: [],
      pumpfunctiontypeList: [],
      pumptypeList: [],
      usagestateList: [],
      ElOption_deviceclassification: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetInterface_query: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "getEventClassification",
          modelLabel: "deviceclassification",
          dataIndex: [],
          modelList: [],
          filters: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {},
        page_in: null, // exp:{ index: 1, limit: 20 }
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          result_out: this.CetInterface_query_result_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
      vm.$nextTick(() => {
        vm.$refs.DocElupload.clearFiles();
        vm.airconditionsystemtypeList =
          this.$store.state.enumerations.airconditionsystemtype || [];
        vm.frequencytypeList =
          this.$store.state.enumerations.frequencytype || [];
        vm.operationmediumtypeList =
          this.$store.state.enumerations.pumpoperationmediumtype || [];
        vm.pumpcycletypeList =
          this.$store.state.enumerations.pumpcycletype || [];
        vm.pumpfunctiontypeList =
          this.$store.state.enumerations.pumpfunctiontype || [];
        vm.pumptypeList = this.$store.state.enumerations.pumptype || [];
        vm.usagestateList = this.$store.state.enumerations.usagestate || [];
        this.CetInterface_query.queryTrigger_in = new Date().getTime();
        $(this.$refs.CetDialog.$el).scrollTop(0);
      });
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    queryId_in(val) {},
    inputData_in: {
      handler: function (data) {
        // operationmediumtype转成数字
        this.documentName = "";
        this.uploadPath2 = "";
        if (data.operationmediumtype) {
          data.operationmediumtype = Number(data.operationmediumtype);
        }
        if (data.physicaltype) {
          data.physicaltype = Number(data.physicaltype);
        }
        if (this.type_in === 3) {
          this.ElSelect_type.disabled = true;
          this.ElSelect_type.value = "pump";
          this.CetForm_1.data = this._.cloneDeep(data);
          //删除设备通用字段信息，防止编辑保存报错；
          delete this.CetForm_1.data.devicecommoninfo_model;

          this.CetDialog_1.title = "编辑节点";
          var str = data.document || "";
          if (str) {
            this.uploadPath2 = str;
          }
          try {
            this.documentName =
              str.split("/")[3].split("_")[0] +
              "." +
              str.split("/")[3].split("_")[1].split(".")[1];
          } catch (err) {
            this.documentName = "";
          }
        } else {
          this.CetForm_1.data = {
            functiontype: 1,
            physicaltype: 1,
            operationmediumtype: 1,
            usagestate: 1,
            isscrap: false
          };

          this.CetDialog_1.title = "添加节点";
        }
        this.CetForm_1.resetTrigger_in = new Date().getTime();
      },
      deep: true
    }
  },

  methods: {
    // 获取设备归类
    CetInterface_query_result_out(val) {
      this.ElOption_deviceclassification.options_in = val;
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件`);
    },
    beforeRemove(file, fileList) {
      var _this = this;
      _this.uploadPath2 = "";
    },
    //    上传前
    handleBeforeUpload2: function (file) {
      if (this.documentName) {
        this.$message.warning(`当前限制选择 1 个文件`);
        return false;
      }
      this.loading = true;
      if (
        file.name.indexOf(".xls") != -1 ||
        file.name.indexOf(".xlsx") != -1 ||
        file.name.indexOf(".docx") != -1 ||
        file.name.indexOf(".pdf") != -1
      ) {
        var uploadDocSize = this.systemCfg.uploadDocSize || 10;
        const isLimit100M = file.size / 1024 / 1024 < uploadDocSize;
        if (!isLimit100M) {
          this.$message.error("上传文件超过规定的最大上传大小");
        }
        return isLimit100M;
      } else {
        this.$message({
          type: "warning",
          message: "只能上传xls/xlsx/docx/pdf格式文件"
        });
        return false;
      }
    },
    //   上传成功
    uploadSuccess2: function (response) {
      if (response.code === 0) {
        this.uploadPath2 = response.data;
        this.$message({
          message: "上传成功",
          type: "success"
        });
      } else if (response.code !== 0) {
        var tips = "";
        if (response.data) {
          for (var i = 0; i < response.data.length; i++) {
            tips = tips + response.data[i] + "<br/>";
          }
        } else {
          tips = response.msg;
        }
        this.$message({
          type: "error",
          message: tips
        });
      }
    },
    handleDeleteDocument_out() {
      this.documentName = "";
      this.toDeleteDoc = true;
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetDialog_1_open_out(val) {},
    CetDialog_1_close_out(val) {},
    CetForm_1_currentData_out(val) {},
    CetForm_1_saveData_out(val) {
      this.CetForm_1.data.modelLabel = "pump";

      if (this.type_in === 1) {
        this.CetForm_1.data.children = [
          {
            id: this.treeData_in.id,
            modelLabel: this.treeData_in.modelLabel
          }
        ];
      }
      var params = [];
      var modelData = this._.cloneDeep(this.CetForm_1.data);
      var manufacturedate = modelData.manufacturedate
        ? new Date(modelData.manufacturedate).getTime()
        : null;
      var commissiondate = modelData.commissiondate
        ? new Date(modelData.commissiondate).getTime()
        : null;

      if (manufacturedate && commissiondate) {
        if (manufacturedate >= commissiondate) {
          this.$message.warning("投运时间不能小于等于生产时间！");
          return;
        }
      }
      modelData.manufacturedate = manufacturedate;
      modelData.commissiondate = commissiondate;
      modelData.modelLabel = "pump";
      if (this.toDeleteDoc) {
        modelData.document = "";
      }
      if (this.uploadPath2) {
        modelData.document = this.uploadPath2;
      }

      if (this.type_in === 1) {
        if (!this.treeData_in) {
          return;
        }

        modelData.children = [
          {
            id: this.treeData_in.id,
            modelLabel: this.treeData_in.modelLabel
          }
        ];
        params[0] = modelData;
        // this.Add_node(params);
      } else if (this.type_in === 2) {
        if (!this.currentTabItme_in) {
          return;
        }

        modelData.children = [
          {
            id: this.currentTabItme_in.id,
            modelLabel: this.currentTabItme_in.modelLabel
          }
        ];
        params[0] = modelData;
      } else if (this.type_in === 3) {
        if (!this.inputData_in) {
          return;
        }

        modelData.children = [
          {
            id: this.treeData_in.id,
            modelLabel: this.treeData_in.modelLabel
          }
        ];
        params[0] = modelData;
        // this.Add_node(params);
      }
      var name = this.CetForm_1.data.name;
      var list = this.treeNameList_in || [];
      for (var i = 0, len = list.length; i < len; i++) {
        if (list[i] === name) {
          if (this.type_in === 3 && this.inputData_in.name === name) {
            continue;
          }
          this.$message.warning("节点名称重复！");
          return;
        }
      }

      this.addNode(params, data => {
        this.$emit("saveData_out", data);
        if (!this.CetForm_1.data.id) {
          this.addQuantityObject(data.id, data.modelLabel);
        } else {
          this.CetDialog_1.closeTrigger_in = new Date().getTime();
          this.$emit("finishTrigger_out", new Date().getTime());
        }
      });
    },
    CetForm_1_finishData_out(val) {},
    CetForm_1_finishTrigger_out(val) {},
    ElInput_1_change_out(val) {},
    ElInput_1_input_out(val) {},
    ElInput_10_change_out(val) {},
    ElInput_10_input_out(val) {},
    ElInput_11_change_out(val) {},
    ElInput_11_input_out(val) {},
    ElInput_12_change_out(val) {},
    ElInput_12_input_out(val) {},
    ElInput_13_change_out(val) {},
    ElInput_13_input_out(val) {},
    ElInput_14_change_out(val) {},
    ElInput_14_input_out(val) {},
    ElInput_15_change_out(val) {},
    ElInput_15_input_out(val) {},
    ElInput_2_change_out(val) {},
    ElInput_2_input_out(val) {},
    ElInput_4_change_out(val) {},
    ElInput_4_input_out(val) {},
    ElInput_5_change_out(val) {},
    ElInput_5_input_out(val) {},
    ElInput_6_change_out(val) {},
    ElInput_6_input_out(val) {},
    ElInput_7_change_out(val) {},
    ElInput_7_input_out(val) {},
    ElInput_8_change_out(val) {},
    ElInput_8_input_out(val) {},
    ElInput_9_change_out(val) {},
    ElInput_9_input_out(val) {},
    ElInputNumber_1_change_out(val) {},
    ElInputNumber_10_change_out(val) {},
    ElInputNumber_11_change_out(val) {},
    ElInputNumber_12_change_out(val) {},
    ElInputNumber_13_change_out(val) {},
    ElInputNumber_14_change_out(val) {},
    ElInputNumber_15_change_out(val) {},
    ElInputNumber_16_change_out(val) {},
    ElInputNumber_17_change_out(val) {},
    ElInputNumber_18_change_out(val) {},
    ElInputNumber_19_change_out(val) {},
    ElInputNumber_20_change_out(val) {},
    ElInputNumber_21_change_out(val) {},
    ElInputNumber_22_change_out(val) {},
    ElInputNumber_2_change_out(val) {},
    ElInputNumber_3_change_out(val) {},
    ElInputNumber_4_change_out(val) {},
    ElInputNumber_5_change_out(val) {},
    ElInputNumber_6_change_out(val) {},
    ElInputNumber_7_change_out(val) {},
    ElInputNumber_int_change_out(val) {},
    ElInputNumber_8_change_out(val) {},
    ElInputNumber_9_change_out(val) {},
    ElSelect_type_change_out(val) {},
    addQuantityObject(id, modelLabel) {
      var data = [
        {
          modelLabel: modelLabel,
          nodes: [
            {
              id: id
            }
          ]
        }
      ];
      httping({
        url: "/eem-service/v1/quantity/quantityObject?dataSource=" + 6,
        method: "PUT",
        data
      }).then(response => {
        if (response.code === 0) {
          this.CetDialog_1.closeTrigger_in = new Date().getTime();
          this.$emit("finishTrigger_out", new Date().getTime());
        }
      });
    },
    addNode(data, fn) {
      httping({
        url: "/eem-service/v1/project/manageNode",
        method: "PUT",
        data
      }).then(response => {
        if (response.code === 0) {
          this.$message({
            message: "保存成功",
            type: "success"
          });
          fn && fn(response.data[0]);
        }
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
    box-sizing: border-box;
  }
  .rowBox {
    @include padding(J4 J4 J2 J4);
    padding-bottom: mh-get(J4) - 18px;
  }
}

.uploadImg {
  width: 80px;
  height: 80px;
}
.box_tip2 {
  display: inline-block;
  @include font_size(Ab);
  @include font_color(T3);
}
.upload,
.elupload11 {
  width: 100%;
  text-align: center;
  :deep(.el-upload) {
    width: 100%;
  }
}
.eem-pload-label {
  width: 100%;
  height: 30px;
  padding-right: 40px;
  box-sizing: border-box;
  position: relative;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}
.line {
  text-align: center;
}
</style>
