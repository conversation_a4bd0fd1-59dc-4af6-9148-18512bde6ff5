<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="small"
    >
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <CetForm
        class="eem-cont-c1"
        :data.sync="CetForm_pagedialog.data"
        v-bind="CetForm_pagedialog"
        v-on="CetForm_pagedialog.event"
      >
        <CetTree
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
          style="height: 500px"
          ref="cetTree"
          class="nodeTree"
        ></CetTree>
        <div class="selected">
          <span>
            已选择图形路径:
            <el-tag class="mrJ mbJ" @close="handleClose(item)" closable>
              {{ selectNode && selectNode.nodeName }}
            </el-tag>
          </span>
        </div>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
import customApi from "@/api/custom.js";

export default {
  name: "graphTreeDialog",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    userInfo() {
      var vm = this;
      return vm.$store.state.userInfo;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    tableData: {
      type: Array
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: "巡检对象",
        "append-to-body": true,
        showClose: true,
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "static", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        rules: {},
        event: {
          currentData_out: this.CetForm_pagedialog_currentData_out,
          saveData_out: this.CetForm_pagedialog_saveData_out,
          finishData_out: this.CetForm_pagedialog_finishData_out,
          finishTrigger_out: this.CetForm_pagedialog_finishTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: "保存",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "text",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out,
          checkedNodes_out: this.CetTree_1_checkedNodes_out,
          parentList_out: this.CetTree_1_parentList_out
        }
      },
      checkedNodes: [], // 选中节点
      selectNode: {}
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      new Promise((res, err) => {
        this.getTreeData_out(res);
      }).then(res => {
        if (res) {
          this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
        } else {
          this.$message.warning("获取图形节点的树失败");
        }
      });
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {
      this.CetForm_pagedialog.queryId_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      this.CetForm_pagedialog.inputData_in = this._.cloneDeep(val);
    }
  },
  methods: {
    CetForm_pagedialog_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_saveData_out(val) {
      this.querySigninRelateGraph_out();
    },
    CetForm_pagedialog_finishData_out(val) {
      this.$emit("finishData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishTrigger_out(val) {
      this.$emit("finishTrigger_out", val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetForm_pagedialog.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      this.CetForm_pagedialog.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    no() {},
    CetTree_1_currentNode_out(val) {
      if (val.nodeType != 271712256) {
        this.$message.warning("请选择图形节点");
        return;
      }
      this.selectNode = this._.cloneDeep(val);
    },
    CetTree_1_checkedNodes_out(val) {},
    //输出选中节点父节点列表
    outputParentList(treenode) {
      const arr = [];
      const vm = this;

      while (treenode != null && treenode.parent != null) {
        //循环res.parent获取所有父节点

        const obj = {};
        //判断key是否是数组，如果是数组则新增对象不添加该属性值
        for (var key in treenode.data) {
          if (!vm._.isArray(treenode.data[key])) {
            obj[key] = treenode.data[key];
          }
        }
        arr.unshift(obj);
        treenode = treenode.parent;
      }
      return arr;
    },
    CetTree_1_parentList_out(val) {},
    getTreeData_out(callback) {
      var me = this;
      var queryData = {
        tenantId: this.userInfo.tenantId,
        userId: me.userInfo.id
      };
      customApi.getGraphTree(queryData).then(res => {
        if (res.code === 0) {
          callback && callback(true);
          var treeData = me.formatGraphNodeTree(res.data || []);
          console.log("nodeTree --> treeData ", treeData);
          me.CetTree_1.inputData_in = treeData;
          if (!me.CetTree_1.selectNode.tree_id && treeData.length > 0) {
            me.CetTree_1.selectNode = me.findChildren(treeData[0]);
          }
        }
      });
    },
    formatGraphNodeTree(rawNodeAry) {
      var me = this;
      rawNodeAry = rawNodeAry || [];

      me._(rawNodeAry).forEach(function (rawNode) {
        rawNode.id = rawNode.nodeId;

        if (rawNode.nodeType === 271712256) {
          rawNode.modelLabel = "graphnode"; // 自定义一个modelLabel
          rawNode.leaf = true;
        } else {
          rawNode.modelLabel = "graphfolder"; // 自定义一个modelLabel
          rawNode.leaf = false;
          rawNode.children = me.formatGraphNodeTree(rawNode.children);
        }

        rawNode.tree_id = `${rawNode.modelLabel}_${rawNode.id}`;
        // rawNode.tree_id = rawNode.tree_id;
        // 设置主画面
        if (me.CetTree_1.MainGraphPath) {
          if (me.CetTree_1.MainGraphPath === rawNode.nodeName) {
            me.CetTree_1.selectNode = rawNode;
          }
        }
      });

      return rawNodeAry;
    },
    findChildren(obj) {
      if (obj.children && obj.children.length > 0) {
        return this.findChildren(obj.children[0]);
      } else {
        return obj;
      }
    },
    querySigninRelateGraph_out(callback) {
      var me = this;
      var queryData = {
        data: this.selectNode,
        signGroupId: this.inputData_in.id
      };

      customApi.querySigninRelateGraph(queryData).then(res => {
        if (res.code === 0) {
          this.$emit("saveData_out", this.selectNode);
          this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
        }
      });
    },
    // 点击删除tag标签
    handleClose(tag) {
      this.selectNode = {};
      this.CetTree_1.selectNode = this._.cloneDeep(this.selectNode);
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.selected {
  height: 70px;
  border-top: 1px solid;
  @include border_color(B1);
  @include padding_top(J3);
  @include margin_top(J3);
  overflow: auto;
}
.nodeTree {
  :deep(.el-checkbox.is-disabled) {
    display: none !important;
  }
}
</style>
