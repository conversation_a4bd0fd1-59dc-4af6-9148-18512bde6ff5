<template>
  <div class="flex w-full h-full">
    <div class="flex-1 pxy">
      <div class="ha">
        <div class="title">模型训练方案</div>
        <div class="flex">
          <el-input
            class="mr-[8px]"
            v-model="keyName"
            @blur="getTableData"
            placeholder="请输入关键字"
            prefix-icon="el-icon-search"
          ></el-input>
          <el-button @click="onAdd" type="primary">新增方案</el-button>
        </div>
      </div>
      <div class="mt-[16px] h-[calc(100%-32px-16px)] w-full">
        <CetTable
          ref="table"
          class="mtJ3 piemTable1"
          style="height: calc(100% - 8px)"
          :data.sync="CetTable_1.data"
          v-bind="CetTable_1"
          :border="false"
          v-on="CetTable_1.event"
        >
          <el-table-column
            v-for="(item, index) in tableCol"
            :key="index"
            v-bind="item"
          />
          <el-table-column label="是否启用方案" width="120">
            <template slot-scope="{ $index, row }">
              <el-switch
                v-model="CetTable_1.data[$index].status"
                @change="onSwitch(row, $index)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160">
            <template slot-scope="{ row }">
              <el-button
                @click.stop="onEdit(row)"
                type="text"
                class="handleBtn"
              >
                编辑
              </el-button>
              <el-button
                @click.stop="onDetail(row)"
                type="text"
                class="handleBtn"
              >
                查看
              </el-button>
              <el-button
                type="text"
                class="handleBtn delete"
                @click.stop="onDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </CetTable>
      </div>
    </div>
    <div class="w-[418px] h-full border-l border-B2 pxy">
      <div class="ha">
        <div class="title">关联注水站</div>
        <el-button @click="onSave" type="primary">保存</el-button>
      </div>

      <div class="h-[calc(100%-48px)] w-full mt-[16px]">
        <customTree
          :dataMode="CetTree_1.dataMode"
          :inputData_in="CetTree_1.inputData_in"
          :selectNode.sync="CetTree_1.selectNode"
          :searchText_in="CetTree_1.searchText_in"
          :filterNodes_in="CetTree_1.filterNodes_in"
          :checkedArray.sync="CetTree_1.checkedArray"
          v-bind="CetTree_1.config"
        />
      </div>
    </div>
    <AddOrEditModelCreate
      v-bind="addOrEditModelCreate"
      @updateTable="getTableData"
    />
    <ModelCreateDetail v-bind="modelCreateDetail" />
    <CustomDelete v-bind="deteleConfirm" @delete="onCustomDelete">
      {{ deleteName }}
    </CustomDelete>
  </div>
</template>

<script>
import customTree from "@/components/customTree/index.vue";
import customApi from "@/api/custom.js";
import { getTableCol } from "../component/tableCol.jsx";
import AddOrEditModelCreate from "./addOrEditModelCreate.vue";
import ModelCreateDetail from "./modelCreateDetail.vue";
import CustomDelete from "@/components/customDelete";

export default {
  name: "modelCreate",
  props: {},
  components: {
    customTree,
    AddOrEditModelCreate,
    ModelCreateDetail,
    CustomDelete
  },
  computed: {
    tableCol() {
      return getTableCol();
    },
    deleteName() {
      return `确定要删除方案${this.deteleConfirm.inputData_in?.name || ""}吗`;
    }
  },
  data() {
    return {
      keyName: "",
      CetTable_1: {
        "empty-text": "暂无数据",
        queryMode: "",
        dataMode: "component",
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [],
          hasQueryNode: true
        },
        data: [],
        event: {
          record_out: this.onRowClick
        }
      },
      addOrEditModelCreate: {
        openTrigger_in: +new Date(),
        inputData_in: {}
      },
      modelCreateDetail: {
        openTrigger_in: +new Date(),
        inputData_in: {}
      },
      deteleConfirm: {
        openTrigger_in: +new Date(),
        inputData_in: {}
      },
      CetTree_1: {
        dataMode: "static",
        inputData_in: [],
        searchText_in: "",
        filterNodes_in: "",
        selectNode: null,
        checkedArray: [],
        config: {
          highlightCurrent: true,
          ShowRootNode: false,
          expandMode: true,
          screen: true,
          initialFlag: false,
          showCheckbox: true,
          checkStrictly: false,
          filterModelLabel: "waterinjectionstation"
        }
      },
      currentRow: {},
      associatednode: []
    };
  },
  watch: {},
  methods: {
    onAdd() {
      this.addOrEditModelCreate.openTrigger_in = +new Date();
      this.addOrEditModelCreate.inputData_in = {};
    },

    async getTableData() {
      const res = await customApi.waterinjectionoptimization({
        name: this.keyName
      });
      this.CetTable_1.data = res?.data || [];
    },

    async onSwitch(row) {
      row.status = !row.status;
      const res = await customApi.saveorupdate(row);
      if (res?.code === 0) {
        this.$message.success("保存成功");
        this.getTableData();
      }
    },
    onEdit(row) {
      this.addOrEditModelCreate.openTrigger_in = +new Date();
      this.addOrEditModelCreate.inputData_in = _.cloneDeep(row);
    },
    onDetail(row) {
      this.modelCreateDetail.openTrigger_in = +new Date();
      this.modelCreateDetail.inputData_in = _.cloneDeep(row);
    },
    onDelete(row) {
      this.deteleConfirm.openTrigger_in = +new Date();
      this.deteleConfirm.inputData_in = _.cloneDeep(row);
    },
    async onCustomDelete(row) {
      const res = await customApi.waterinjectionoptimizationDelete(row.id);
      if (res?.code === 0) {
        this.$message.success("删除成功");
        this.getTableData();
      }
    },
    async onRowClick(row) {
      const res = await customApi.waterAssociatednode(row?.id),
        res1 = await customApi.waterQuerybymodelgoal(row?.modelGoal);
      const allData = _.cloneDeep(res1?.data) || [];
      this.associatednode = allData
        ?.filter(i => !(res?.data || []).includes(i))
        ?.map(i => `waterinjectionstation_${i}`);

      this.CetTree_1.checkedArray =
        res?.data?.length > 0
          ? res?.data?.map(i => {
              return { modelLabel: "waterinjectionstation", id: i };
            })
          : [];
      this.setTreeCheckStatus(this.CetTree_1.inputData_in);
      this.currentRow = _.cloneDeep(row);
    },

    // 设置节点树节点禁选
    setTreeCheckStatus(treeData) {
      if (treeData.length === 0) {
        return;
      }
      treeData.forEach(node => {
        const flag = this.associatednode?.includes(node.tree_id);
        this.$set(node, "disabled", flag);
        this.setTreeCheckStatus(
          this._.get(node, "children", []),
          this.associatednode
        );
      });
    },
    async onSave() {
      const params = {
        modelSchemeId: this.currentRow.id,
        objectLabel: "waterinjectionstation",
        objectId: this.CetTree_1.checkedArray
          ?.filter(i => i.modelLabel === "waterinjectionstation")
          ?.map(i => i.id)
      };

      const res = await customApi.waterTranSchemeAssociationNode(params);
      if (res.code === 0) {
        this.$message.success("保存成功");
        this.getTableData();
      }
    },
    // 获取节点树
    async getTreeData() {
      const queryData = {
        rootID: 0,
        rootLabel: "oilcompany",
        subLayerConditions: [
          { modelLabel: "oilproductionplant" },
          { modelLabel: "operationarea" },
          { modelLabel: "oilproductioncrew" },
          { modelLabel: "waterinjectionstation" }
        ],
        treeReturnEnable: true
      };
      const res = await customApi.getNodeTree(queryData);
      this.CetTree_1.inputData_in = res?.data || [];
    }
  },
  created() {},
  mounted() {
    this.getTableData();
    this.getTreeData();
  }
};
</script>

<style lang="scss" scoped>
.pxy {
  @apply px-[24px] py-[16px] box-border;
}

.ha {
  @apply h-[32px] w-full flex items-center justify-between;

  .title {
    @apply text-T2 text-[16px] font-semibold;
  }
}

:deep(.el-tree) {
  overflow: auto;
}

.handleBtn {
  padding: 0;

  &:not(:first-of-type) {
    @include margin_left(J4);
  }

  &.delete {
    @include font-color(Sta3);
  }

  :deep(span) {
    @include font_size(Aa);
  }
}
</style>
