<template>
  <div class="page flex-column">
    <!-- <div class="Toback" v-if="isdetail">
      <i class="el-icon-arrow-left icon"></i>
      <span class="title" @click="back">返回</span>
    </div> -->
    <el-tabs v-model="activeName" @tab-click="tabsClick" class="tabs">
      <el-tab-pane name="负荷聚合商" label="负荷聚合商"></el-tab-pane>
      <el-tab-pane name="园区虚拟电厂" label="园区虚拟电厂"></el-tab-pane>
    </el-tabs>
    <el-container class="content flex-auto flex-column">
      <el-header
        height="34px"
        style="line-height: 34px; padding: 0"
        class="headerBox marginBottomJ2 marginTopJ2"
        v-if="!isSelfOperate"
      >
        <el-row>
          <el-col :span="20" class="box-col" style="height: 40px">
            <!-- 关键字： -->
            <ElInput
              v-model="ElInput_1.value"
              v-bind="ElInput_1"
              v-on="ElInput_1.event"
              style="width: 298px"
              class="searchInput fl marginRightJ1"
            >
              <i slot="suffix" class="el-icon-search"></i>
            </ElInput>

            <div class="basic-box fl">
              <span class="basic-box-label">响应区域</span>
              <!-- 地址选择 -->
              <el-cascader
                class="cascader"
                clearable
                v-model="address"
                :props="treeProps"
                :options="areaOptions"
                @change="gettableData"
              ></el-cascader>
            </div>
          </el-col>
        </el-row>
      </el-header>
      <el-header class="numbox" v-if="!isSelfOperate">
        <div class="numone radiusBox marginRightJ2">
          <img src="./assets/user.png" alt="" />
          <div>
            <div style="font-size: 14px" class="label">用户数量（个）</div>
            <P>
              {{ totalCount ? totalCount : "--" }}
            </P>
          </div>
        </div>

        <div class="numone radiusBox">
          <img src="./assets/baozhuang.png" alt="" />
          <div>
            <div style="font-size: 14px" class="label">报装容量（kVA）</div>
            <P>
              {{
                numdata && numdata.declaredCapacity
                  ? numdata.declaredCapacity.toFixed(2)
                  : "--"
              }}
            </P>
          </div>
        </div>
        <div class="numone radiusBox marginLeftJ2">
          <img src="./assets/max.png" alt="" />
          <div>
            <div style="font-size: 14px" class="label">最大响应量（kW）</div>
            <P>
              {{
                numdata && numdata.maxResponse
                  ? numdata.maxResponse.toFixed(2)
                  : "--"
              }}
            </P>
          </div>
        </div>
      </el-header>
      <el-container class="container flex-auto flex-row">
        <el-aside
          width="316px"
          class="aside bg1 brC3 flex-column mrJ2"
          v-if="isdetail"
        >
          <span class="treetitle">管理层级</span>
          <CetTree
            class="flex-auto mJ2"
            :selectNode.sync="CetTree_1.selectNode"
            :checkedNodes.sync="CetTree_1.checkedNodes"
            :searchText_in.sync="CetTree_1.searchText_in"
            v-bind="CetTree_1"
            default-expand-all
            v-on="CetTree_1.event"
            @node-click="treeclick"
            :filter-node-method="filterNode"
          ></CetTree>
        </el-aside>
        <el-main class="tabBox radiusBox flex-column">
          <el-header class="numbox1 paddingAll-16" v-if="isSelfOperate">
            <div class="numone1 radiusBox marginRightJ2">
              <div class="marginJ3">
                <div style="font-size: 14px" class="label">
                  {{ labelname }}（个）
                </div>
                <P>
                  {{ totalCount ? totalCount : "--" }}
                </P>
              </div>
            </div>

            <div
              class="numone1 radiusBox marginRightJ2"
              v-if="labelname === '户号数量' || !isdetail"
            >
              <div class="marginJ3">
                <div style="font-size: 14px" class="label">报装容量（kVA）</div>
                <P>
                  {{
                    numdata && numdata.declaredCapacity
                      ? numdata.declaredCapacity.toFixed(2)
                      : "--"
                  }}
                </P>
              </div>
            </div>
            <div class="numone1 radiusBox marginRightJ2">
              <div class="marginJ3">
                <div style="font-size: 14px" class="label">
                  最大响应量（kW）
                </div>
                <P>
                  {{
                    numdata && numdata.maxResponse
                      ? numdata.maxResponse.toFixed(2)
                      : "--"
                  }}
                </P>
              </div>
            </div>
          </el-header>
          <!-- :style="tablecontent" -->
          <div class="flex-auto">
            <div
              class="headercolor radiusBox paddingAll-16 flex-column"
              style="height: 100%; width: 100%; box-sizing: border-box"
            >
              <div v-if="isSelfOperate">
                <!-- 关键字： -->
                <ElInput
                  v-model="ElInput_1.value"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                  style="width: 298px"
                  class="searchInput"
                >
                  <i
                    slot="suffix"
                    class="el-icon-search"
                    style="line-height: 34px"
                  ></i>
                </ElInput>
              </div>

              <div class="marginTopJ2 mbJ2">
                共
                <span class="totalCount">{{ totalCount }}</span>
                条
              </div>
              <CetTable
                class="flex-auto"
                :data.sync="CetTable_1.data"
                :dynamicInput.sync="CetTable_1.dynamicInput"
                v-bind="CetTable_1"
                v-on="CetTable_1.event"
              >
                <el-table-column
                  label="#"
                  width="42"
                  type="index"
                ></el-table-column>
                <el-table-column
                  label="名称"
                  prop="name"
                  max-width="296px"
                  show-overflow-tooltip
                  :formatter="
                    val => (val.name || val.name == 0 ? val.name : '--')
                  "
                  v-if="isSelfOperate"
                ></el-table-column>
                <el-table-column
                  label="用电户号"
                  prop="accountno"
                  show-overflow-tooltip
                  :formatter="
                    val =>
                      val.accountno || val.accountno == 0 ? val.accountno : '--'
                  "
                  v-if="isSelfOperate && nodelevel === 1"
                ></el-table-column>
                <!-- 根据模型数据绑定表格列 -->
                <template v-for="item in columnsArr">
                  <template>
                    <el-table-column
                      :key="item.key"
                      :show-overflow-tooltip="true"
                      :label="item.title"
                      :min-width="item.width"
                      :width="item.fixedwidth"
                      :prop="item.key"
                      :formatter="item.formatter"
                      header-align="left"
                      align="left"
                    ></el-table-column>
                  </template>
                </template>
                <el-table-column
                  label="操作"
                  width="52"
                  v-if="!isdetail"
                  align="center"
                >
                  <template slot-scope="scope" v-if="!isdetail">
                    <span @click="Todetail(scope.row)" class="highLight">
                      详情
                    </span>
                  </template>
                </el-table-column>
              </CetTable>
              <div class="tableFooter">
                <el-pagination
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page.sync="currentPage"
                  :page-sizes="pageSizes"
                  :page-size.sync="pageSize"
                  layout=" sizes, prev, pager, next, jumper"
                  :total="totalCount"
                ></el-pagination>
              </div>
            </div>
          </div>
        </el-main>
      </el-container>
    </el-container>
    <Drawerdialog v-bind="Drawerdialog" />
  </div>
</template>
<script>
import Drawerdialog from "./DrawerDialog.vue";

import customApi from "@/api/custom";
export default {
  components: { Drawerdialog },
  props: {},
  computed: {},
  data(vm) {
    return {
      nodelevel: 1,
      clicklabel: "园区名称",
      labelname: "户号数量",
      checkeddata: {},
      detailId: "",
      tablecontent: "height:calc(100% - 94px);width:100%;",
      isSelfOperate: false,
      isdetail: false,
      address: [],
      treeProps: {
        expandTrigger: "hover",
        children: "children",
        label: "name",
        value: "name"
      },
      areaOptions: [],
      drawer: false,
      activeName: "负荷聚合商",
      showDetail: true,
      totalCount: 0,
      currentPage: 1,
      pageSizes: [10, 20, 50, 100],
      pageSize: 10,
      pageTotal: 0,
      numdata: {},
      Drawerdialog: {
        openTrigger_in: new Date().getTime(),
        inputData_in: null,
        event: {
          edit_out: this.Detail_edit_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: "请输入",
        style: {
          width: "298px"
        },
        event: {
          change: this.ElInput_1_change_out
          // input: this.ElInput_1_input_out
        }
      },
      ElSelect_type: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_type_change_out
        }
      },
      ElOption_type: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项

        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        // paginationCfg: {
        //   pageSize: 10,
        //   textAlign: "center",
        //   layout: "sizes, prev, pager, next, jumper"
        // },
        "highlight-current-row": false,
        exportFileName: "",
        defaultSort: { prop: "operationtime", order: "descending" },
        event: {
          "cell-click": this.tableRow_Click
          // totalCount_out: this.CetTable_1_totalCount_out
        }
      },
      columnsArr: [],
      columnsArrProxyuser: [
        {
          title: "名称",
          key: "enterprisename",
          // type: "date",
          // sortable: "custom",
          width: 100,
          formatter: val =>
            val.enterprisename || val.enterprisename == 0
              ? val.enterprisename
              : "--"
        },
        {
          title: "响应区域",
          key: "responseArea",
          headerAlign: "left",
          align: "left",
          width: 100,
          formatter: function (val) {
            if (val.address) {
              return val.address.split("-")[0];
            } else {
              return "--";
            }
          }
        },
        {
          title: "报装容量(kVA)",
          key: "declaredCapacity",
          width: 120,
          formatter: val =>
            val.declaredCapacity || val.declaredCapacity == 0
              ? val.declaredCapacity
              : "--"
        },
        {
          title: "最大响应量(kW)",
          key: "maxResponse",
          width: 120,
          formatter: val =>
            val.maxResponse || val.maxResponse == 0 ? val.maxResponse : "--"
        },
        {
          title: "电压等级",
          key: "voltageLevel",
          width: 100,
          formatter(val) {
            if (val.voltageLevel) {
              let data = vm.$store.state.enumerations.voltagelevel || [];
              let voltageLevel = data.filter(item => {
                return item.id == val.voltageLevel ? item.text : "";
              });
              return voltageLevel[0].text;
            } else {
              return "--";
            }
          } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
        },
        {
          title: "用户地址",
          key: "address",
          width: 100,
          formatter: function (val) {
            if (val.address) {
              return val.address.split("-")[1];
            } else {
              return "--";
            }
          }
        },
        {
          title: "联系人",
          key: "contact",
          width: 100,
          formatter: val =>
            val.contact || val.contact == 0 ? val.contact : "--"
        },
        {
          title: "联系方式",
          key: "contactNumber",
          width: 100,
          formatter: val =>
            val.contactNumber || val.contactNumber == 0
              ? val.contactNumber
              : "--"
        }
      ],
      columnsArrdetail: [
        {
          title: "最大响应量(kW)",
          key: "maxresponse",
          width: 120,
          formatter: val =>
            val.maxresponse || val.maxresponse == 0 ? val.maxresponse : "--"
        },
        {
          title: "电压等级",
          key: "voltagelevel",
          width: 100,
          formatter(val) {
            if (val.voltagelevel) {
              let data = vm.$store.state.enumerations.voltagelevel || [];
              let voltagelevel = data.filter(item => {
                return item.id == val.voltagelevel ? item.text : "";
              });
              return voltagelevel[0].text;
            } else {
              return "--";
            }
          } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
        }
      ],
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        nodeKey: "tree_id",
        props: {
          label: function (data, node) {
            let title = data.name ? data.name : "";
            let num = data.accountno ? data.accountno : " ";
            let meterno = data.meterno ? data.meterno : " ";
            if (data.modelLabel === "demandgroup") {
              return title + num;
            } else if (data.modelLabel === "demandaccount") {
              return title + meterno;
            } else {
              return data.name;
            }
          },
          children: "children"
        },
        filterNode_in: (value, data, node) => {
          const label = "name";
          const nodeLabel = vm._.get(data, label) || "";
          const nodeLabel1 = vm._.get(data, "meterno") || "";
          const nodeLabel2 = vm._.get(data, "accountno") || "";
          const nodeKey = "tree_id";
          const filterNode = {};
          filterNode[nodeKey] = data[nodeKey];
          let filterText = vm.CetTree_1.searchText_in;
          if (
            filterText &&
            nodeLabel.toLowerCase().indexOf(filterText.toLowerCase()) === -1 &&
            nodeLabel1.toLowerCase().indexOf(filterText.toLowerCase()) === -1 &&
            nodeLabel2.toLowerCase().indexOf(filterText.toLowerCase()) === -1
          ) {
            return false;
          }
          return true;
        },
        highlightCurrent: true,
        event: {
          // currentNode_out: this.CetTree_1_currentNode_out
        }
      }
    };
  },
  watch: {
    isdetail: {
      handler(val) {
        {
          if (val) {
            this.columnsArr = this._.cloneDeep(this.columnsArrdetail);
            this.tablecontent = "height:calc(100% - 242px)";
          } else {
            this.tablecontent = "height:100%";
            this.columnsArr = this._.cloneDeep(this.columnsArrProxyuser);
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.isSelfOperate ? this.getdetailData() : this.gettableData();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.isSelfOperate ? this.getdetailData() : this.gettableData();
    },
    tableRow_Click(row) {
      if (!this.isdetail) {
        this.Todetail(row);
      }
    },
    treeclick(draggingNode, dropNode, ev) {
      console.log(draggingNode);
      this.checkeddata = draggingNode;
      this.nodelevel = dropNode.level;

      if (dropNode.level === 1) {
        this.labelname = "户号数量";
        this.checkeddata.id = this.checkeddata.poweruserId;
      } else if (dropNode.level === 2) {
        this.labelname = "进线数量";
      } else if (dropNode.level === 3) {
        this.labelname = "资源数量";
      }
      this.getdetailData();
    },
    Detail_edit_out() {
      this.EditContract.openTrigger_in = new Date().getTime();
      this.EditContract.type_in = 1;
    },
    getDistrictTree() {
      customApi.demandSideResponsedetailsdistrict().then(response => {
        this.areaOptions = this._.get(response, "data", []);
        console.log(this.areaOptions);
      });
    },
    gettableData() {
      let data = {
        // powersellingcompanyid:"",
        isselfoperate: this.isSelfOperate,
        keyword: this.ElInput_1.value,
        address: this.address.join("/"),
        // province: this.address[0],
        // city: this.address[1],
        page: {
          // index: (this.currentPage - 1) * this.pageSize,
          index: this.currentPage - 1,
          limit: this.pageSize
        }
      };
      customApi.invitationDisplayquery(data).then(response => {
        if (response.code == 0) {
          this.numdata = response.data;
          this.CetTable_1.data = this._.get(response, "data.data", []);
          this.totalCount = response.total;
          // console.log(response.data);
        }
      });
    },

    ElInput_1_change_out() {
      this.isSelfOperate ? this.getdetailData() : this.gettableData();
    },
    init() {
      this.totalCount = 0;
      this.CetTable_1.data = [];
      this.numdata = {};
      this.ElInput_1.value = "";
      // this.ElSelect_1.value = "";
      // 清除列表勾选
      // this.$refs.CetTable.$refs.cetTable.clearSelection();
    },
    tabsClick() {
      this.currentPage = 1;
      this.pageSize = 10;
      if (this.activeName === "负荷聚合商") {
        this.isSelfOperate = false;
        this.isdetail = false;
        this.init();
        this.gettableData();
      } else {
        this.isSelfOperate = true;
        this.isdetail = true;
        this.init();
        this.getTreeData();
      }
    },
    Todetail(row) {
      // if (this.isSelfOperate) {

      //   this.isdetail = true;
      //   this.detailId = row.id;
      //   this.getTreeData();
      // } else {

      this.Drawerdialog.openTrigger_in = new Date().getTime();
      this.Drawerdialog.inputData_in = { id: row.id };
      // }
    },
    getTreeData() {
      let data = {
        objectlabel: "",
        powersellingcompanyId: this.$store.state.sellCompanyId
      };
      console.log(data);
      customApi.baselineCalculationqueryTreeNodePark(data).then(response => {
        console.log(response);
        if (response.code === 0) {
          let options = this._.get(response, "data", []);
          this.CetTree_1.inputData_in = options;
          this.CetTree_1.selectNode = this._.get(options, "[0]");
          this.treeclick(this.CetTree_1.selectNode, {
            level: 1
          });
        }
      });
    },
    getdetailData() {
      let data = {
        // id: this.checkeddata.id,
        // label: this.checkeddata.modelLabel,
        keyword: this.ElInput_1.value,
        // powerid: this.detailId,
        id: this.checkeddata.id,
        modelLabel: this.checkeddata.modelLabel,
        page: {
          index: this.currentPage - 1,
          limit: this.pageSize
        }
      };
      customApi.invitationDisplayoverviewpark(data).then(response => {
        if (response.code == 0) {
          this.numdata = response.data;
          console.log(this.numdata);
          this.CetTable_1.data = this._.get(response, "data.data", []);
          this.totalCount = response.total;
        }
      });
    }
    //     formatTable(row, column, cellValue, index) {
    //   if (!cellValue) {
    //     if (cellValue === 0 || cellValue === "") {
    //       return cellValue;
    //     } else {
    //       return "--";
    //     }
    //   }
    //   return cellValue;
    // },
  },
  created() {
    this.getDistrictTree();
    this.gettableData();
  },
  mounted() {
    // this.gettableData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100% !important;
  // height: calc(100% - 46px);
  height: 100% !important;
  margin: 0 !important;
  box-sizing: border-box;
}
.tabs {
  padding: 0 19px;
  box-sizing: border-box;
  :deep(.el-tabs__header) {
    margin: 0;
  }
  @include background_color(BG1);
}
.container {
  @include padding_top(J2);
}
.content {
  width: 100%;
  overflow: auto;
  .numbox {
    height: 98px !important;
    // display: flex;
    // justify-content: space-between;
    align-items: center;
    padding: 0;
    // @include margin_bottom(J2);
    .numone {
      float: left;
      height: 98px;
      width: 372px;
      display: flex;
      // justify-content:space-evenly;
      align-items: center;
      @include background_color("BG1");
      .label {
        @include font_color(T3);
      }
      p {
        height: 40px;
        @include font_size("H");
        margin: 0;
        font-weight: 600;
      }
      img {
        height: 62px;
        width: 62px;
        @include margin_right(J3);
        @include margin_left(J3);
      }
    }
  }
  .numbox1 {
    height: 98px !important;
    // display: flex;
    // justify-content: space-between;
    align-items: center;
    @include margin_bottom(J2);
    .numone1 {
      float: left;
      height: 98px;

      width: 264px;
      display: flex;
      // justify-content: space-around;
      align-items: center;

      @include background_color("BG2");
      .marginJ3 {
        @include margin_left(J3);
      }
      .label {
        @include font_color(T3);
      }
      p {
        height: 40px;
        @include font_size("H");
        font-weight: 600;
        margin: 0;
      }
    }
  }
}
.aside {
  height: 100%;
  @include padding_top(J3);
  .treetitle {
    @include font_size("H2");
    @include margin_left(J2);
    font-weight: 600;
    line-height: 18px;
  }
}
.headerBox {
  padding: 0;
  @include padding_left(J1);
  @include padding_right(J1);
}
.headercolor {
  @include background_color(BG1);
  box-sizing: border-box;
}
.tabBox {
  width: 100%;
  @include background_color(BG1);

  height: 100%;
  padding: 0;
}
.radiusBox {
  @include border_radius(C3);
}
.contentBox {
  @include border_radius(C3);
  height: calc(100% - 58px);
}
.totalCount {
  @include font_color(ZS);
}
:deep(.el-footer) {
  text-align: right;
}

.cascader {
  width: 200px;
}
.tableFooter {
  text-align: right;
  @include padding_top(J2);
  height: 30px;
  @include background_color(BG1);
  border-bottom-left-radius: mh-get(C3);
  border-bottom-right-radius: mh-get(C3);
}
.searchInput {
  :deep(.el-input__inner) {
    padding-right: 50px;
  }
  :deep(.el-icon-search:before) {
    @include margin_right(J1);
  }
}
.el-form-item__label {
  @include padding_right(J1);
}
</style>
