<template>
  <div :class="['notice-item', language ? 'en' : '']">
    <div class="notice-item-left">
      <el-tag
        class="notice-item-tag"
        :color="tag.bgColor"
        :style="`color: ${tag.color}`"
      >
        {{ tag.label }}
      </el-tag>
    </div>
    <div class="notice-item-main">
      <div class="notice-item-main-head">
        <div class="notice-item-time">
          {{ time }}
        </div>
        <div class="notice-item-operate">
          <el-link
            class="notice-item-operate-btn"
            type="primary"
            @click="evGotoClick(item)"
          >
            {{ $T("转到") }}
          </el-link>
          <el-link
            class="notice-item-operate-btn"
            :underline="false"
            type="primary"
            @click="evReadClick(item)"
          >
            {{ $T("已读") }}
          </el-link>
        </div>
      </div>
      <div class="notice-item-main-content">
        <el-tooltip :content="content" placement="bottom-start">
          <div class="text-ellipsis">
            {{ content }}
          </div>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import { getEventTypeColor } from "eem-utils/eventColor.js";
const LEVEL_MAP = [
  [1, $T("事故")],
  [2, $T("报警")],
  [3, $T("一般")],
  [4, $T("预警")],
  [5, $T("其他")]
];
export default {
  name: "NoticeItem",
  props: {
    item: Object
  },
  computed: {
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },
  data() {
    const time = moment(this.item.time).format("YYYY-MM-DD HH:mm");
    const content = this.item.content;

    const [, label] = LEVEL_MAP.find(([level]) => {
      return level === this.item.level;
    });
    const { bgColor, color } = getEventTypeColor(this.item.level);
    return {
      tag: {
        label: label,
        bgColor: bgColor,
        color: color
      },
      time,
      content
    };
  },
  methods: {
    evReadClick(item) {
      this.removeItem(item);
    },
    evGotoClick(item) {
      // 业务侧处理
      // ...
      // TODO

      // 处于平台页面则不进行跳转
      if (this.$store.state.currentModule === "pf") {
        this.$message("请先进入项目之后再跳转");
        return;
      } else {
        this.goRouter(item);
      }
      this.removeItem(item);
    },
    goRouter(val) {
      const hash = window.location.hash;
      let isOk = true;
      if ([704, 705].includes(val.logType)) {
        //需量报警事件
        if (hash.includes("demandevent")) {
          isOk = false;
        }
        this.$router.push({
          name: "demandevent",
          params: val
        });
      } else if ([708, 709].includes(val.logType)) {
        //能效超标报警
        if (hash.includes("energyEfficiencyEvent")) {
          isOk = false;
        }
        this.$router.push({
          name: "energyEfficiencyEvent",
          params: val
        });
      } else if ([701, 702, 703, 710].includes(val.logType)) {
        //能耗超标报警
        if (hash.includes("energyConsumptionEvent")) {
          isOk = false;
        }
        this.$router.push({
          name: "energyConsumptionEvent",
          params: val
        });
      } else if ([711, 713].includes(val.logType)) {
        //巡检工单事件
        if (hash.includes("inspectorder")) {
          isOk = false;
        }
        this.$router.push({
          name: "inspectorder",
          params: val
        });
      } else if ([714].includes(val.logType)) {
        //维保工单事件
        if (hash.includes("serviceOrder")) {
          isOk = false;
        }
        this.$router.push({
          name: "serviceOrder",
          params: val
        });
      } else if ([715].includes(val.logType)) {
        //维修工单事件
        if (hash.includes("repairorder")) {
          isOk = false;
        }
        this.$router.push({
          name: "repairorder",
          params: val
        });
      } else if ([706, 707].includes(val.logType)) {
        //损耗告警
        if (hash.includes("energyLossWarning")) {
          isOk = false;
        }
        this.$router.push({
          name: "energyLossWarning",
          params: val
        });
      } else {
        //通用页面调整到告警实时诊断页面，华星光电跳转到告警实时诊断收敛页面eventDiagByConvergence
        if (hash.includes("eventDiagnosis")) {
          isOk = false;
        }
        this.$router.push({
          name: "eventDiagnosis",
          params: val
        });
      }
      if (!isOk) {
        this.$message.info("已在当前页面，可点击刷新按钮查看最新事件!");
      }
    },
    removeItem(item) {
      this.$emit("removeItem", item);
    }
  }
};
</script>
<style lang="scss" scoped>
.notice-item {
  height: 50px;
  margin-right: 4px;
  position: relative;
  &-left {
    position: absolute;
    width: 58px;
    height: 100%;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
  }
  &-main {
    margin-left: 58px + 8px;
    &-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 30px;
    }
    &-content {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      height: 20px;
      line-height: 20px;
      @include font_color(T2);
      @include font_size(Ab);
    }
  }
  &-time {
    @include font_color(T3);
    @include font_size(Ab);
  }
  &-operate {
    &-btn {
      width: 40px;
      height: 30px;
      @include font_size(Ab);
      &:hover {
        @include background_color(BG2);
      }
      &:active {
        @include background_color(BG3);
      }
    }
  }
  &-tag {
    width: 58px;
    height: 30px;
    line-height: 28px;
    text-align: center;
  }
  :deep(.el-tag--commonly) {
    border-color: rgb(24, 190, 183);
    color: rgb(24, 190, 183);
  }
}
.notice-item.en {
  .notice-item-left {
    width: 100px;
  }
  .notice-item-main {
    margin-left: 100px + 8px;
  }
  .notice-item-tag {
    width: 100px;
  }
}
</style>
