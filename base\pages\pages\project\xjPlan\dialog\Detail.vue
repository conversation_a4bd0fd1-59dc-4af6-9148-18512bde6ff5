<template>
  <div>
    <CetDialog
      v-bind="CetDialog_detail"
      v-on="CetDialog_detail.event"
      class="small"
    >
      <el-container class="eem-cont-c1">
        <el-row :gutter="$J3">
          <el-col
            :span="en ? 24 : 12"
            v-for="(item, index) in items"
            :key="index"
          >
            <div style="line-height: 2">
              <span class="fl text-left" :class="{ 'en-text-left': en }">
                {{ item.name }}
              </span>
              <span
                class="fr clicktext"
                v-if="item.type == 'button'"
                @click="clickSee"
              >
                {{ $T("查看") }}
              </span>
              <span
                class="fr text-right"
                :class="{ 'en-text-right': en }"
                v-else
              >
                <el-tooltip
                  :content="filText(planMsg[item.key], item.type, item.unit)"
                  effect="light"
                >
                  <span>
                    {{ filText(planMsg[item.key], item.type, item.unit) }}
                  </span>
                </el-tooltip>
              </span>
            </div>
          </el-col>
        </el-row>
      </el-container>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <!-- <CetButton v-bind="CetButton_confirm" v-on="CetButton_confirm.event"></CetButton> -->
      </span>
    </CetDialog>
    <Scheme
      :visibleTrigger_in="scheme.visibleTrigger_in"
      :closeTrigger_in="scheme.closeTrigger_in"
      :inputData_in="scheme.inputData_in"
    />
  </div>
</template>

<script>
import Scheme from "./Scheme";
export default {
  name: "detail",
  components: { Scheme },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    en() {
      return window.localStorage.getItem("omega_language") === "en";
    }
  },
  data() {
    return {
      CetDialog_detail: {
        title: $T("巡检计划详情"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {
          open_out: this.CetDialog_detail_open_out,
          close_out: this.CetDialog_detail_close_out
        },
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      items: [
        {
          name: $T("名称"),
          key: "name"
        },
        {
          name: $T("预计耗时"),
          key: "timeconsumeplan",
          unit: "h",
          type: "ms"
        },
        {
          name: $T("创建人"),
          key: "creatorname"
        },
        {
          name: $T("工单生成时间"),
          key: "aheadduration",
          type: "minute"
        },
        {
          name: $T("创建时间"),
          key: "createtime",
          type: "date"
        },
        {
          name: $T("巡检方案"),
          key: "inspectionschemeid",
          type: "button"
        },
        {
          name: $T("周期"),
          key: "cycle$text"
        },
        {
          name: $T("巡检路线"),
          key: "signInGroupName"
        },
        {
          name: $T("首次执行时间"),
          key: "executetime",
          type: "date"
        },
        {
          name: $T("巡检目标"),
          key: "inspectObject"
        },
        {
          name: $T("结束时间"),
          key: "finishtime",
          type: "date"
        },
        {
          name: $T("责任班组"),
          key: "teamName"
        },
        {
          name: $T("生效日"),
          key: "enableddays",
          type: "list"
        }
      ],
      planMsg: {},
      scheme: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_detail.openTrigger_in = val;
      // this.reset();
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_detail.closeTrigger_in = val;
    },
    inputData_in(val) {
      this.planMsg = this._.cloneDeep(val);
    }
  },

  methods: {
    CetDialog_detail_open_out() {},
    CetDialog_detail_close_out() {},
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_detail.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_add.localSaveTrigger_in = this._.cloneDeep(val);
    },
    init() {},
    filText(val, type, unit = "") {
      if ([undefined, null, NaN].includes(val)) {
        return "--" + unit;
      } else if (type === "date") {
        return this.$moment(val).format("YYYY-MM-DD HH:mm:ss");
      } else if (type === "minute") {
        if (val === 5) {
          return $T("执行前5分钟");
        } else if (val === 30) {
          return $T("执行前30分钟");
        } else if (val === 60) {
          return $T("执行前1小时");
        } else if (val === 2 * 60) {
          return $T("执行前2小时");
        } else if (val === 24 * 60) {
          return $T("执行前1天");
        } else if (val === 2 * 24 * 60) {
          return $T("执行前2天");
        } else {
          return "--";
        }
      } else if (type === "ms") {
        return (val / (1000 * 60 * 60)).toFixed(2) + unit;
      } else if (type === "list") {
        let name = "";
        const list = this._.get(val, "weekDays", []) || [];
        const obj = {
          1: $T("周一"),
          2: $T("周二"),
          3: $T("周三"),
          4: $T("周四"),
          5: $T("周五"),
          6: $T("周六"),
          7: $T("周日")
        };
        list.forEach((item, index) => {
          if (index === 0) {
            name = obj[item];
          } else {
            name += " , " + obj[item];
          }
        });
        if (!name) {
          name = "--";
        }
        return name + unit;
      } else {
        return val + unit;
      }
    },
    clickSee() {
      this.scheme.inputData_in = this._.cloneDeep(this.inputData_in);
      this.scheme.visibleTrigger_in = new Date().getTime();
    }
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.clicktext {
  cursor: pointer;
  @include font_color(ZS);
}
.text-left {
  display: inline-block;
  width: 100px;
}
.en-text-left {
  width: 190px;
}
.text-right {
  display: inline-block;
  width: calc(100% - 100px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.en-text-right {
  width: calc(100% - 190px);
}
</style>
