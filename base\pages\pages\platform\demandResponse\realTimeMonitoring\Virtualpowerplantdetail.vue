<template>
  <div class="page">
    <el-container class="contentBox">
      <el-aside width="316px" class="aside bg1 brC3">
        <span class="treetitle">管理层级</span>
        <CetTree
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          :searchText_in.sync="CetTree_1.searchText_in"
          v-bind="CetTree_1"
          default-expand-all
          v-on="CetTree_1.event"
          @node-click="treeclick"
        >
          <span slot-scope="{ node, data }" class="el-tree-node__label">
            <i
              class="el-icon-warning"
              style="color: #ffa435; float: right"
              v-if="data.color === 1"
            ></i>

            <el-tooltip
              class="treeTooltip"
              effect="light"
              :content="node.label"
              placement="bottom-start"
            >
              <span>
                {{ node.label }}
              </span>
            </el-tooltip>
          </span>
        </CetTree>
      </el-aside>
      <el-main
        style="height: 100%; padding: 0px; width: 100%"
        class="asidecolor mlJ2"
      >
        <div class="bgcolor" style="height: 100%; width: 100%">
          <div style="height: 40%" class="chartbox">
            <CetChart
              :inputData_in="CetChart_1.inputData_in"
              v-bind="CetChart_1.config"
            />
          </div>
          <div class="tableTop clearfix marginBottom12">
            <div class="fl marginLeftJ2">
              共
              <span class="sum">{{ totalCount }}</span>
              条
            </div>
          </div>
          <div class="tableBody">
            <CetTable
              ref="CetTable"
              :data.sync="CetTable_1.data"
              :dynamicInput.sync="CetTable_1.dynamicInput"
              v-bind="CetTable_1"
              v-on="CetTable_1.event"
            >
              <template v-for="item in Columns_1">
                <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
              </template>
            </CetTable>
          </div>
          <div class="tableFooter">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              :page-sizes="pageSizes"
              :page-size.sync="pageSize"
              layout=" sizes, prev, pager, next, jumper"
              :total="totalCount"
            ></el-pagination>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import customApi from "@/api/custom";
import common from "eem-utils/common";
export default {
  props: { rowdata: { type: Object } },
  computed: {},
  data() {
    return {
      activeName: "负荷聚合商",
      totalCount: 0,
      currentPage: 1,
      pageSizes: [10, 20, 50, 100],
      pageSize: 10,
      pageTotal: 0,
      sum: 0,
      showDetail: false,
      baseAverageLoad: "",
      // 1树组件
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        nodeKey: "tree_id",
        props: {
          label: function (data, node) {
            let title = data.name ? data.name : "";
            let num = data.accountno ? data.accountno : " ";
            let meterno = data.meterno ? data.meterno : " ";
            if (data.modelLabel === "demandgroup") {
              return title + num;
            } else if (data.modelLabel === "demandaccount") {
              return title + meterno;
            } else {
              return data.name;
            }
          },
          children: "children"
        },
        highlightCurrent: true,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      ElInput_search: {
        value: "",

        style: {},
        event: {}
      },

      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        "highlight-current-row": false,
        paginationCfg: {
          pageSize: 10,
          textAlign: "center",
          layout: "sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          "selection-change": this.handleSelectionChange
        }
      },
      Columns_1: [],
      Columns_inletWire: [
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          width: "42" //绝对宽度
        },
        {
          prop: "eventtime", // 支持path a[0].b
          label: "时间", //列名
          headerAlign: "left",
          align: "left",
          width: "220",
          showOverflowTooltip: true,
          formatter: common.formatDateCol("YYYY-MM-DD HH:mm:ss.SSS")
        },
        {
          prop: "calcbaseline", // 支持path a[0].b
          label: "基线平均负荷(kW)", //列名
          headerAlign: "left",
          align: "left",
          width: "200",
          showOverflowTooltip: true,
          formatter: val =>
            val.calcbaseline || val.calcbaseline == 0
              ? (val.calcbaseline * 1000).toFixed(3)
              : "--"
        },

        {
          prop: "remark", // 支持path a[0].b
          label: "实际平均负荷(kW)", //列名
          headerAlign: "left",
          align: "left",
          width: "200",
          showOverflowTooltip: true,
          formatter: val =>
            val.remark || val.remark == 0
              ? (val.remark * 1000).toFixed(3)
              : "--"
          // formatter: function (val) {
          //   // return val.description
          //   //   ? val.description.split("平均响应量为:")[1].split("，")[0]
          //   //   : "--";
          //   if (val.description && val.description.split("平均响应量为:")[1]) {
          //     return (
          //       (val.description.split("平均响应量为:")[1].split("，")[0] * 1 +
          //         val.calcbaseline) *
          //       1000
          //     ).toFixed(3);
          //   } else {
          //     return "--";
          //   }
          // }
        },
        {
          prop: "description", // 支持path a[0].b
          label: "事件描述", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: val => (val.description ? val.description : "--")
        }
      ],
      Columns_resource: [
        {
          type: "selection", // selection 勾选 index 序号
          headerAlign: "center",
          align: "center",
          width: "41" //绝对宽度
        },
        {
          type: "index", // selection 勾选 index 序号
          label: "#", //列名
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          width: "42" //绝对宽度
        },
        {
          prop: "demo1", // 支持path a[0].b
          label: "时间", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true
        },
        {
          prop: "demo2", // 支持path a[0].b
          label: "基线平均负荷", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true
        },
        {
          prop: "demo3", // 支持path a[0].b
          label: "实际平均负荷", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true
        },
        {
          prop: "demo4", // 支持path a[0].b
          label: "事件描述", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true
        },
        {
          prop: "demo5", // 支持path a[0].b
          label: "电压等级", //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true
        }
      ],
      ElSelect_1: {
        value: "",
        style: {
          width: "200px"
        },
        event: {}
      },
      ElOption_1: {
        options_in: [
          {
            id: 1,
            name: "类型1"
          },
          {
            id: 2,
            name: "类型2"
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetChart_1: {
        inputData_in: {},
        config: {
          options: {
            legend: {
              // data: ["基线负荷", "实际负荷", "基线平均负荷", "实际平均负荷"],
              // selectedMode: false,
              right: "4%",
              itemGap: 30,
              // icon: "circle",
              textStyle: {
                color: "#9CA2B1"
              }
            },
            grid: {
              left: "6%",
              top: "12%",
              bottom: "12%",
              right: "6%"
            },
            tooltip: {
              extraCssText:
                'background: #0F2557; border-radius: 12px;color: #fff; opacity: 0.8; box-shadow: "0px 0px 4px 0px rgba(62, 66, 78, 0.22)";border:none;',
              trigger: "axis",
              axisPointer: {
                type: "shadow"
              },
              textStyle: {
                color: "#FFF",
                fontsize: "10px"
              },

              formatter: function (params) {
                let str = params[0].name + "<br>";
                params.forEach((item, index) => {
                  str +=
                    params[index].marker +
                    params[index].seriesName +
                    "(kW):" +
                    params[index].value.toFixed(2) +
                    "<br>";
                });
                return str;
              }
            },
            xAxis: {
              type: "category",
              // boundaryGap: false,
              name: "时间",
              axisLabel: {
                //---坐标轴 标签
                show: true, //---是否显示
                inside: false, //---是否朝内
                rotate: 0, //---旋转角度
                margin: 5, //---刻度标签与轴线之间的距离
                color: "#9CA2B1" //---默认取轴线的颜色
              },
              axisLine: {
                //---坐标轴 标签
                show: true, //---是否显示
                lineStyle: {
                  color: "#D7DBE5" //---默认取轴线的颜色
                }
              },
              data: []
            },
            yAxis: {
              type: "value",
              name: "功率（kW）",
              scale: true,
              axisLine: {
                //---坐标轴 标签
                show: false, //---是否显示
                lineStyle: {
                  color: "#D7DBE5" //---默认取轴线的颜色
                }
              },
              axisLabel: {
                //---坐标轴 标签
                show: true, //---是否显示

                color: "#9CA2B1" //---默认取轴线的颜色
              }
            },
            series: [
              {
                name: "基线负荷",
                data: [],
                type: "line",
                smooth: true,
                showSymbol: false,
                itemStyle: {
                  color: "#FFA435",
                  borderWidth: 3,
                  borderColor: "#FFA435"
                }
              },
              {
                name: "实际负荷",
                showSymbol: false,
                data: [],
                markArea: {
                  itemStyle: {
                    color: "#3E77FC",
                    opacity: "0.1"
                  },
                  data: [
                    [
                      {
                        name: "响应开始时间",
                        label: {
                          show: true,
                          position: "bottom", // markArea中文字（name）位置
                          offset: [0, 14], // markArea中文字（name）显示在位置基础上x、y轴偏移
                          color: "#3E77FC" // markArea中文字（name）颜色
                        },
                        xAxis: this.$moment(this.rowdata.starttime).format(
                          "HH:00"
                        )
                        // yAxis: 0
                      },
                      {
                        xAxis: this.$moment(this.rowdata.starttime).format(
                          "HH:00"
                        )
                        // yAxis: 0
                      }
                    ],
                    [
                      {
                        xAxis: this.$moment(this.rowdata.starttime).format(
                          "HH:00"
                        )
                        // yAxis: 0
                      },
                      {
                        xAxis: this.$moment(this.rowdata.endtime).format(
                          "HH:00"
                        )
                        // yAxis:40000000,
                      }
                    ],
                    [
                      {
                        name: "响应结束时间",
                        label: {
                          show: true,
                          position: "bottom", // markArea中文字（name）位置
                          offset: [0, 14], // markArea中文字（name）显示在位置基础上x、y轴偏移
                          color: "#3E77FC" // markArea中文字（name）颜色
                        },
                        xAxis: this.$moment(this.rowdata.endtime).format(
                          "HH:00"
                        )
                        // yAxis: 0
                      },
                      {
                        xAxis: this.$moment(this.rowdata.endtime).format(
                          "HH:00"
                        )
                        // yAxis: 0
                      }
                    ]
                  ]
                },
                type: "line",
                smooth: true,
                itemStyle: {
                  color: "#3E77FC",
                  borderWidth: 3,
                  borderColor: "#3E77FC"
                },
                markLine: {
                  silent: true,
                  symbol: "none",
                  data: [
                    {
                      xAxis: this.$moment(this.rowdata.starttime).format(
                        "HH:mm"
                      ),
                      name: "申报响应量：",
                      label: {
                        formatter: "申报响应量：",
                        color: "#9CA2B1",
                        fontSize: "14"
                      },
                      lineStyle: { color: "#D7DBE5", width: 1 }
                    },
                    {
                      xAxis: this.$moment(this.rowdata.endtime).format("HH:mm"),
                      label: {
                        show: false
                      },
                      lineStyle: { color: "#D7DBE5", width: 1 }
                    }
                  ]
                  // data: [
                  //   {
                  //     type: "average",
                  //     name: "平均值",
                  //     lineStyle: { color: "#3EC7FC", type: "solid", width: 2 }
                  //   }
                  // ]
                }
              },
              {
                name: "基线平均负荷",
                data: [],
                type: "line",
                smooth: true,
                showSymbol: false,
                itemStyle: {
                  color: "#08C673",
                  borderWidth: 3,
                  borderColor: "#08C673"
                }
              },
              {
                name: "实际平均负荷",
                data: [],
                type: "line",
                showSymbol: false,
                smooth: true,
                itemStyle: {
                  color: "#3EC7FC",
                  borderWidth: 3,
                  borderColor: "#3EC7FC"
                }
              }
            ]
          }
        }
      },
      editInletWire: {
        openTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      editResource: {
        openTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      relevanceDetail: {
        openTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      checkdata: {}
    };
  },
  watch: {
    showDetail: {
      handler(val) {
        {
          if (val) {
            this.Columns_1 = this._.cloneDeep(this.Columns_resource);
          } else {
            this.Columns_1 = this._.cloneDeep(this.Columns_inletWire);
          }
        }
      },
      immediate: true
    },
    rowdata: {
      handler(oldval, newval) {
        this.rowdata.starttime = Number(this.rowdata.starttime);
        this.rowdata.endtime = Number(this.rowdata.endtime);
        if (this.$route.query.isquery) {
          let node = { level: 2 };
          let data = {
            id: this.rowdata.objectid,
            modelLabel: this.rowdata.objectlabel
          };
          this.treeclick(data, node);
        }
        this.getTreeData();
      },
      immediate: true
    }
  },
  methods: {
    init() {
      this.sum = 0;
      // this.CetTable_1.data = [];
      this.ElInput_search = "";
      this.ElSelect_1.value = "";
      // 清除列表勾选
      this.$refs.CetTable.$refs.cetTable.clearSelection();
    },
    tabsClick() {
      this.showDetail = false;
      this.init();
      this.getTreeData();
    },
    treeclick(data, node) {
      this.CetTable_1.data = [];
      this.checkdata = data;
      this.getTabledata();
      if (this.rowdata.isselfoperate) {
        if (node.level === 1) {
          return;
        }
        this.clearchart();
        let treedata = {
          cycle: 4,
          objectLabels: [
            {
              objectid: data.id,
              modelLabel: data.modelLabel
            }
          ],
          planid: this.rowdata.isquery ? this.rowdata.planid : this.rowdata.id,
          endtime: this.rowdata.endtime,
          starttime: this.rowdata.starttime,
          powersellingcompanyid: this.$store.state.sellCompanyId
        };
        // if (node.level === 3) {
        //   treedata.objectLabels[0].objectlabel = data.objectlabel;
        //   treedata.objectLabels[0].objectid = data.objectid;
        // }
        customApi.loadDetectionqueryParkDetail(treedata).then(response => {
          console.log(response);
          if (response.code === 0) {
            this.CetChart_1.config.options.series[1].markLine.data[0].label.formatter =
              "申报响应量:" + response.data.planvalue * 1000 + "kW";
            let options = this._.get(response, "data.datas", []);
            // this.CetChart_1.inputData_in=options
            let arr1 = [];
            let arr2 = [];
            let valuearr1 = [];
            let valuearr2 = [];
            options.forEach((item, num) => {
              if (item.jxfh && item.jxfh.length > 0) {
                arr1.push(...item.jxfh);

                let list1 = item.jxfh.map(item => {
                  return item.timeStr;
                });
                list1.forEach(time => {
                  valuearr1.push(item.jxpjfh[0].value);
                });

                // this.CetChart_1.config.options.series[0].markLine.data[
                //   num
                // ][0].xAxis = item.jxfh[0].timeStr;
                // this.CetChart_1.config.options.series[0].markLine.data[
                //   num
                // ][1].xAxis = item.jxfh[item.jxfh.length - 1].timeStr;

                // this.CetChart_1.config.options.series[0].markLine.data[
                //   num
                // ][0].yAxis = item.jxpjfh[0].value;
                // this.CetChart_1.config.options.series[0].markLine.data[
                //   num
                // ][1].yAxis = item.jxpjfh[0].value;
              } else if (item.sjfh && item.sjfh.length > 0) {
                arr2.push(...item.sjfh);
                let list2 = item.sjfh.map(item => {
                  return item.timeStr;
                });
                list2.forEach(time => {
                  valuearr2.push(item.sjpjfh[0].value);
                });
                // this.CetChart_1.config.options.series[0].markLine.data[
                //   num
                // ][0].xAxis = item.sjfh[0].timeStr;
                // this.CetChart_1.config.options.series[0].markLine.data[
                //   num
                // ][1].xAxis = item.sjfh[item.sjfh.length - 1].timeStr;
                // console.log(
                //   this.CetChart_1.config.options.series[0].markLine.data
                // );
                // this.CetChart_1.config.options.series[0].markLine.data[
                //   num
                // ][0].yAxis = item.sjpjfh[0].value;
                // this.CetChart_1.config.options.series[0].markLine.data[
                //   num
                // ][1].yAxis = item.sjpjfh[0].value;
              }
            });
            let time1 = arr1.map(item => {
              return item.timeStr;
            });
            let time2 = arr2.map(item => {
              return item.timeStr;
            });
            this.CetChart_1.config.options.xAxis.data =
              time1.length > 0 ? time1 : time2;
            // this.CetChart_1.config.options.xAxis.data = arr1.map(item => {
            //   return item.timeStr;
            // });
            this.CetChart_1.config.options.series[0].data = arr1.map(item => {
              return item.value;
            });
            this.CetChart_1.config.options.series[1].data = arr2.map(item => {
              return item.value;
            });
            this.CetChart_1.config.options.series[2].data = valuearr1;
            this.CetChart_1.config.options.series[3].data = valuearr2;
          }
        });
      } else {
        this.clearchart();
        let treedata = {
          demandgroupIds: [],
          cycle: 4,
          endtime: this.rowdata.endtime,
          starttime: this.rowdata.starttime,
          planid: this.rowdata.isquery ? this.rowdata.planid : this.rowdata.id,
          powersellingcompanyid: this.$store.state.sellCompanyId
        };
        if (node.level === 1) {
          let accountnolist = data.children;
          let accountno =
            accountnolist &&
            accountnolist.map(item => {
              return item.id;
            });
          treedata.demandgroupIds = accountno;
        } else {
          treedata.demandgroupIds.push(data.id);
        }
        customApi.loadDetectionqueryLoadDetail(treedata).then(response => {
          console.log(response);
          if (response.code === 0) {
            this.CetChart_1.config.options.series[1].markLine.data[0].label.formatter =
              "申报响应量:" + response.data.planvalue * 1000 + "kW";
            let options = this._.get(response, "data.datas", []);
            let arr1 = [];
            let arr2 = [];
            let valuearr1 = [];
            let valuearr2 = [];
            options.forEach((item, num) => {
              if (item.jxfh && item.jxfh.length > 0) {
                arr1.push(...item.jxfh);
                let list1 = item.jxfh.map(item => {
                  return item.timeStr;
                });
                list1.forEach(time => {
                  valuearr1.push(item.jxpjfh[0].value);
                });
                // this.CetChart_1.config.options.series[0].markLine.data[
                //   num
                // ][0].xAxis = item.jxfh[0].timeStr;
                // this.CetChart_1.config.options.series[0].markLine.data[
                //   num
                // ][1].xAxis = item.jxfh[item.jxfh.length - 1].timeStr;

                // this.CetChart_1.config.options.series[0].markLine.data[
                //   num
                // ][0].yAxis = item.jxpjfh[0].value;
                // this.CetChart_1.config.options.series[0].markLine.data[
                //   num
                // ][1].yAxis = item.jxpjfh[0].value;
              } else if (item.sjfh && item.sjfh.length > 0) {
                arr2.push(...item.sjfh);
                let list2 = item.sjfh.map(item => {
                  return item.timeStr;
                });
                list2.forEach(time => {
                  valuearr2.push(item.sjpjfh[0].value);
                });
                // this.CetChart_1.config.options.series[0].markLine.data[
                //   num
                // ][0].xAxis = item.sjfh[0].timeStr;
                // this.CetChart_1.config.options.series[0].markLine.data[
                //   num
                // ][1].xAxis = item.sjfh[item.sjfh.length - 1].timeStr;

                // this.CetChart_1.config.options.series[0].markLine.data[
                //   num
                // ][0].yAxis = item.sjpjfh[0].value;
                // this.CetChart_1.config.options.series[0].markLine.data[
                //   num
                // ][1].yAxis = item.sjpjfh[0].value;
              }
            });
            let time1 = arr1.map(item => {
              return item.timeStr;
            });
            let time2 = arr2.map(item => {
              return item.timeStr;
            });
            this.CetChart_1.config.options.xAxis.data =
              time1.length > 0 ? time1 : time2;
            this.CetChart_1.config.options.series[0].data = arr1.map(item => {
              return item.value;
            });
            this.CetChart_1.config.options.series[1].data = arr2.map(item => {
              return item.value;
            });
            this.CetChart_1.config.options.series[2].data = valuearr1;
            this.CetChart_1.config.options.series[3].data = valuearr2;
          }
        });
      }
    },
    getTreeData() {
      console.log(this.rowdata);
      let data = "";
      this.rowdata.isquery
        ? (data = this.rowdata.planid)
        : (data = this.rowdata.id);
      if (this.rowdata.isselfoperate) {
        customApi.loadDetectiontreeNodePark(data).then(response => {
          if (response.code === 0) {
            let options = this._.get(response, "data", []);
            this.CetTree_1.inputData_in = options;
            if (this.rowdata.isquery) {
              this.CetTree_1.selectNode = {
                tree_id: this.rowdata.tree_id ? this.rowdata.tree_id : ""
              };
            }
          }
        });
      } else {
        customApi.loadDetectionqueryLoadTree(data).then(response => {
          if (response.code === 0) {
            let options = this._.get(response, "data", []);
            this.CetTree_1.inputData_in = options;
            if (this.rowdata.isquery) {
              this.CetTree_1.selectNode = {
                tree_id: this.rowdata.tree_id ? this.rowdata.tree_id : ""
              };
            }
          }
        });
      }
    },
    clearchart() {
      this.CetChart_1.config.options = {
        legend: {
          // data: ["基线负荷", "实际负荷", "基线平均负荷", "实际平均负荷"],
          // icon: "circle",
          right: "4%",
          itemGap: 30,
          // selectedMode: false,
          textStyle: {
            color: "#9CA2B1"
          }
        },
        grid: {
          left: "6%",
          top: "14%",
          bottom: "14%",
          right: "6%"
        },
        tooltip: {
          extraCssText:
            'background: #0F2557; border-radius: 12px;color: #fff; opacity: 0.8; box-shadow: "0px 0px 4px 0px rgba(62, 66, 78, 0.22)";border:none;',
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          },
          textStyle: {
            color: "#FFF",
            fontsize: "10px"
          },

          formatter: function (params) {
            // console.log(params);
            let str = params[0].name + "<br>";
            params.forEach((item, index) => {
              str +=
                params[index].marker +
                params[index].seriesName +
                "(kW):" +
                params[index].value.toFixed(2) +
                "<br>";
            });
            return str;
          }
        },
        xAxis: {
          type: "category",
          // boundaryGap: false,
          name: "时间",
          axisLabel: {
            //---坐标轴 标签
            show: true, //---是否显示
            inside: false, //---是否朝内
            rotate: 0, //---旋转角度
            margin: 5, //---刻度标签与轴线之间的距离
            color: "#9CA2B1" //---默认取轴线的颜色
          },
          axisLine: {
            //---坐标轴 标签
            show: true, //---是否显示
            lineStyle: {
              color: "#D7DBE5" //---默认取轴线的颜色
            }
          },
          data: []
        },
        yAxis: {
          type: "value",
          name: "功率（kW）",
          scale: true,
          axisLine: {
            //---坐标轴 标签
            show: false, //---是否显示
            lineStyle: {
              color: "#D7DBE5" //---默认取轴线的颜色
            }
          },
          axisLabel: {
            //---坐标轴 标签
            show: true, //---是否显示

            color: "#9CA2B1" //---默认取轴线的颜色
          }
        },
        series: [
          {
            name: "基线负荷",
            data: [],
            type: "line",
            smooth: true,
            showSymbol: false,
            itemStyle: {
              color: "#FFA435",
              borderWidth: 3,
              borderColor: "#FFA435"
            }

            // markLine: {
            //   silent: true,
            //   symbol: "none",
            //   data: [
            //     [
            //       {
            //         xAxis: "",
            //         yAxis: "",
            //         lineStyle: { color: "#08C673", type: "solid", width: 2 }
            //       },
            //       {
            //         xAxis: "",
            //         yAxis: ""
            //       }
            //     ],
            //     [
            //       {
            //         xAxis: "",
            //         yAxis: "",
            //         lineStyle: { color: "#08C673", type: "solid", width: 2 }
            //       },
            //       {
            //         xAxis: "",
            //         yAxis: ""
            //       }
            //     ],
            //     [
            //       {
            //         xAxis: "",
            //         yAxis: "",
            //         lineStyle: { color: "#08C673", type: "solid", width: 2 }
            //       },
            //       {
            //         xAxis: "",
            //         yAxis: ""
            //       }
            //     ],
            //     [
            //       {
            //         xAxis: "",
            //         yAxis: "",
            //         lineStyle: { color: "#3EC7FC", type: "solid", width: 2 }
            //       },
            //       {
            //         xAxis: "",
            //         yAxis: ""
            //       }
            //     ],
            //     [
            //       {
            //         xAxis: "",
            //         yAxis: "",
            //         lineStyle: { color: "#3EC7FC", type: "solid", width: 2 }
            //       },
            //       {
            //         xAxis: "",
            //         yAxis: ""
            //       }
            //     ],
            //     [
            //       {
            //         xAxis: "",
            //         yAxis: "",
            //         lineStyle: { color: "#3EC7FC", type: "solid", width: 2 }
            //       },
            //       {
            //         xAxis: "",
            //         yAxis: ""
            //       }
            //     ]
            //   ]
            // }
          },
          {
            name: "实际负荷",
            data: [],
            smooth: true,
            showSymbol: false,
            markArea: {
              itemStyle: {
                color: "#3E77FC",
                opacity: "0.1"
              },
              data: [
                [
                  {
                    name: "响应开始时间",
                    label: {
                      show: true,
                      position: "bottom", // markArea中文字（name）位置
                      offset: [0, 14], // markArea中文字（name）显示在位置基础上x、y轴偏移
                      color: "#3E77FC" // markArea中文字（name）颜色
                    },
                    xAxis: this.$moment(this.rowdata.starttime).format("HH:mm")
                    // yAxis: 0
                  },
                  {
                    xAxis: this.$moment(this.rowdata.starttime).format("HH:mm")
                    // yAxis: 0
                  }
                ],
                [
                  {
                    xAxis: this.$moment(this.rowdata.starttime).format("HH:mm")
                    // yAxis: 0
                  },
                  {
                    xAxis: this.$moment(this.rowdata.endtime).format("HH:mm")
                    // yAxis:4mmmmmm0,
                  }
                ],
                [
                  {
                    name: "响应结束时间",
                    label: {
                      show: true,
                      position: "bottom", // markArea中文字（name）位置
                      offset: [0, 14], // markArea中文字（name）显示在位置基础上x、y轴偏移
                      color: "#3E77FC" // markArea中文字（name）颜色
                    },
                    xAxis: this.$moment(this.rowdata.endtime).format("HH:mm")
                    // yAxis: 0
                  },
                  {
                    xAxis: this.$moment(this.rowdata.endtime).format("HH:mm")
                    // yAxis: 0
                  }
                ]
              ]
            },
            type: "line",
            itemStyle: {
              color: "#3E77FC",
              borderWidth: 3,
              borderColor: "#3E77FC"
            },
            markLine: {
              silent: true,
              symbol: "none",
              data: [
                {
                  xAxis: this.$moment(this.rowdata.starttime).format("HH:mm"),
                  name: "申报响应量：",
                  label: {
                    formatter: "申报响应量：",
                    color: "#9CA2B1",
                    fontSize: "14"
                  },
                  lineStyle: { color: "#D7DBE5", width: 1 }
                },
                {
                  xAxis: this.$moment(this.rowdata.endtime).format("HH:mm"),
                  label: {
                    show: false
                  },
                  lineStyle: { color: "#D7DBE5", width: 1 }
                }
              ]
              // data: [
              //   {
              //     type: "average",
              //     name: "平均值",
              //     lineStyle: { color: "#3EC7FC", type: "solid", width: 2 }
              //   }
              // ]
            }
          },
          {
            name: "基线平均负荷",
            data: [],
            type: "line",
            showSymbol: false,
            smooth: true,
            itemStyle: {
              color: "#08C673",
              borderWidth: 3,
              borderColor: "#08C673"
            }
          },
          {
            name: "实际平均负荷",
            data: [],
            type: "line",
            showSymbol: false,
            smooth: true,
            itemStyle: {
              color: "#3EC7FC",
              borderWidth: 3,
              borderColor: "#3EC7FC"
            }
          }
        ]
      };
    },
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.getTabledata();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getTabledata();
    },
    getTabledata() {
      console.log(this.rowdata);
      let data = {
        endtime: this.rowdata.endtime + 60000,
        starttime: this.rowdata.starttime,
        planid: this.rowdata.isquery ? this.rowdata.planid : this.rowdata.id,
        id: this.checkdata.id,
        label: this.checkdata.modelLabel,

        page: {
          index: (this.currentPage - 1) * this.pageSize,
          limit: this.pageSize
        }
      };

      customApi.loadDetectioneventDatas(data).then(response => {
        console.log(response);
        if (response.code == 0) {
          this.CetTable_1.data = this._.get(response, "data", []);
          this.totalCount = response.total;
          // console.log(response.data);
        }
      });
    },
    CetTree_1_currentNode_out(val) {
      console.log(this.CetTree_1.selectNode);
      this.showDetail = false;
    },
    handleSelectionChange(val) {
      this.sum = val.length;
    },
    CetButton_delete_statusTrigger_out(val) {
      if (!this.showDetail) {
        this.$confirm(
          "是否删除所选的进线，删除会导致进线下资源会全部删除？",
          "资源删除",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }
        )
          .then(() => {})
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消删除"
            });
          });
      } else {
        this.$confirm("是否删除选择的资源？", "进线删除", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {})
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消删除"
            });
          });
      }
    },
    CetButton_add_statusTrigger_out(val) {
      if (this.showDetail) {
        this.editResource.openTrigger_in = val;
        this.editResource.inputData_in = null;
      } else {
        this.editInletWire.openTrigger_in = val;
        this.editInletWire.inputData_in = null;
      }
    },
    handleCommand(val) {
      if (!this.showDetail) {
        if (val === "edit") {
          this.editInletWire.openTrigger_in = new Date().getTime();
          this.editInletWire.inputData_in = {
            id: 11
          };
        } else if (val === "detail") {
          this.showDetail = true;
          this.init();
        }
      } else {
        if (val === "edit") {
          this.editResource.openTrigger_in = new Date().getTime();
          this.editResource.inputData_in = {
            id: 11
          };
        } else if (val === "detail") {
          this.relevanceDetail.openTrigger_in = new Date().getTime();
        }
      }
    }
  },
  created() {},
  mounted() {
    this.init();
    // this.getTreeData();
  },
  activated() {
    console.log(this.rowdata);

    // this.showDetail = false;
    // this.getTreeData();
  }
};
</script>
<style lang="scss" scoped>
.page {
  margin: 0 !important;
  height: 100% !important;
  width: 100% !important;

  .searchInput {
    width: 298px;
  }
  .customElSelect {
    margin-left: 8px;
  }
  .contentBox {
    margin: 0;
    height: 100%;
    box-sizing: border-box;
    .tableTop {
      @include background_color(BG1);
      border-top-left-radius: mh-get(C3);
      border-top-right-radius: mh-get(C3);
      line-height: 34px;
      .sum {
        @include font_color(ZS);
      }
    }
    .bgcolor {
      @include background_color(BG1);
      @include border_radius(C3);
    }
    .tableBody {
      @include background_color(BG1);
      @include padding_left(J2);
      @include padding_right(J2);
      position: relative;
      height: 43%;
      :deep(.el-footer) {
        position: absolute;
        right: 36px;
        bottom: -48px;
      }
      .handel {
        cursor: pointer;
      }
    }
    .tableFooter {
      text-align: right;
      @include padding_right(J2);
      @include padding_top(J2);
      @include padding_bottom(J2);
      height: 30px;
      @include background_color(BG1);
      border-bottom-left-radius: mh-get(C3);
      border-bottom-right-radius: mh-get(C3);
    }
  }
  .el-icon-search {
    @include font_size("Aa");
    margin: 10px 7px 0 0;
  }
  .el-dropdown-link {
    cursor: pointer;
  }
}
.aside {
  height: 100%;
  @include padding_top(J3);
  box-sizing: border-box;
  .treetitle {
    @include font_size("H2");
    @include margin_left(J2);
    font-weight: 600;
    line-height: 18px;
  }
  .tree {
    height: calc(100% - 40px) !important;
    @include margin_top(J2);
    @include padding_left(J2);
    @include padding_right(J2);
    @include font_size("Ab");
  }
  :deep(.el-tree) {
    .el-tree-node__label {
      max-width: calc(100% - 50px);
    }
  }
}
.chartbox {
  @include padding_top(J2);
}
</style>
