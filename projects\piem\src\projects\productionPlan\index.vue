<template>
  <div class="page projectOverview eem-common" style="min-width: 1350px">
    <el-container style="height: 100%">
      <el-aside
        class="eem-aside leftTree"
        :class="{ 'aside-collapsed': collapsed }"
        :width="asideWidth"
      >
        <CetGiantTree
          v-show="!collapsed"
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
          class="treeStyle"
        ></CetGiantTree>
        <div class="collapse-btn" @click="onCollapseBtnClick">
          {{ collapseText }}
        </div>
      </el-aside>
      <el-main class="fullheight padding0 mlJ3 flex-column">
        <div class="eem-container">
          <div class="fr">
            <customElSelect
              v-model="ElSelect_producttype.value"
              v-bind="ElSelect_producttype"
              v-on="ElSelect_producttype.event"
              class="fl customElSelect"
              :prefix_in="$T('产品类型')"
            >
              <ElOption
                v-for="item in ElOption_producttype.options_in"
                :key="item[ElOption_producttype.key]"
                :label="item[ElOption_producttype.label]"
                :value="item[ElOption_producttype.value]"
                :disabled="item[ElOption_producttype.disabled]"
              ></ElOption>
            </customElSelect>
            <customElSelect
              v-model="ElSelect_period.value"
              v-bind="ElSelect_period"
              v-on="ElSelect_period.event"
              class="fl mlJ4 customElSelect"
              :prefix_in="$T('时段')"
            >
              <ElOption
                v-for="item in ElOption_period.options_in"
                :key="item[ElOption_period.key]"
                :label="item[ElOption_period.label]"
                :value="item[ElOption_period.value]"
                :disabled="item[ElOption_period.disabled]"
              ></ElOption>
            </customElSelect>
            <div class="fl mlJ1" style="display: inline-block">
              <CetButton
                class="fl mrJ timeBtn"
                v-bind="CetButton_prv"
                v-on="CetButton_prv.event"
              ></CetButton>
              <div class="basic-box fl">
                <el-date-picker
                  v-model="CetDatePicker_1.val"
                  v-bind="CetDatePicker_1.config"
                  :clearable="false"
                  :placeholder="$T('选择日期')"
                ></el-date-picker>
              </div>
              <CetButton
                class="fl mlJ timeBtn"
                v-bind="CetButton_next"
                v-on="CetButton_next.event"
              ></CetButton>
            </div>
          </div>
        </div>
        <div class="flex-auto flex-column" style="overflow: auto">
          <div class="eem-container mtJ3" v-if="showChart">
            <div class="mbJ3">
              <span class="title">
                {{ $T("生产计划与跟踪") }}
              </span>
              <el-checkbox-group
                class="eem-checkbox fr mlJ1 lh32"
                style="height: 32px"
                v-model="chartTypeRadio"
                @change="chartTypeRadioChange_out"
              >
                <el-checkbox
                  v-for="(item, index) in chartTypeList"
                  :key="index"
                  :label="item.label"
                >
                  {{ item.name }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
            <div style="height: 300px" v-if="!singleFlag">
              <CetChart
                ref="CetChart_1"
                :inputData_in="CetChart_1.inputData_in"
                v-bind="CetChart_1.config"
              />
            </div>
            <div style="height: 300px" v-if="singleFlag">
              <CetChart
                ref="CetChart_2"
                :inputData_in="CetChart_2.inputData_in"
                v-bind="CetChart_2.config"
              />
            </div>
          </div>
          <div
            class="eem-container mtJ3 flex-auto flex-column"
            style="min-height: 300px"
          >
            <div class="mbJ3 flex-row">
              <div class="flex-auto text-ellipsis">
                <span class="lh32 title" style="display: inline">
                  {{ $T("生产计划与实际产量统计") }}
                </span>
              </div>
              <div style="display: flex">
                <CetButton
                  class="mrJ1"
                  v-show="productionPlan.tableType == 1"
                  v-bind="CetButton_exportTemplate"
                  v-on="CetButton_exportTemplate.event"
                ></CetButton>
                <CetButton
                  class="mrJ1"
                  v-show="productionPlan.tableType == 1"
                  v-bind="CetButton_exportData"
                  v-on="CetButton_exportData.event"
                ></CetButton>
                <CetButton
                  class="mrJ1"
                  v-show="productionPlan.tableType == 1"
                  v-bind="CetButton_import1"
                  v-on="CetButton_import1.event"
                ></CetButton>
                <CetButton
                  class="mrJ1"
                  v-show="productionPlan.tableType == 1"
                  v-bind="CetButton_import2"
                  v-on="CetButton_import2.event"
                ></CetButton>
                <CetButton
                  v-permission="'productionplan_inspect'"
                  v-show="
                    productionPlan.tableType == 0 &&
                    [totalProductType, totalProductTypeC].includes(
                      ElSelect_producttype.value
                    )
                  "
                  class="fr mrJ1"
                  v-bind="CetButton_inputPlan"
                  v-on="CetButton_inputPlan.event"
                ></CetButton>
                <CetButton
                  class="fr mrJ1"
                  v-show="productionPlan.tableType == 0"
                  v-bind="CetButton_export"
                  v-on="CetButton_export.event"
                ></CetButton>
                <el-button
                  size="small"
                  class="mrJ1"
                  style="margin-left: 0px"
                  v-show="productionPlan.tableType == 1"
                  plain
                  @click="backAddProductionPlan_out"
                >
                  {{ $T("返回") }}
                </el-button>
                <el-button
                  class="ml0 mrJ1"
                  size="small"
                  v-show="productionPlan.tableType == 1"
                  type="primary"
                  @click="saveProductPlanData"
                >
                  {{ $T("保存") }}
                </el-button>
                <el-upload
                  style="display: none"
                  action="/eem-service/v1/productPlan/ImportProductPlanWithFile"
                  name="userFile"
                  :headers="{
                    Authorization: this.token,
                    projectId: this.projectId
                  }"
                  :before-upload="handleBeforeUpload"
                  :on-success="uploadSuccess"
                  :on-error="uploadError"
                  :multiple="false"
                >
                  <button ref="uploadBtn"></button>
                </el-upload>

                <omega-icon
                  v-if="ElSelect_producttype.value !== totalProductTypeC"
                  class="fullBtn"
                  :symbolId="fullScreen ? 'lessen-lin' : 'frame-fullscreen-lin'"
                  @click="fullScreenBtnClick"
                />
              </div>
            </div>
            <div class="flex-auto">
              <!-- 单个产品 -->
              <div class="fullheight" v-if="singleFlag">
                <CetTable
                  ref="elTab3"
                  tooltip-effect="light"
                  height="100%"
                  :data.sync="CetTable_single.data"
                  :dynamicInput.sync="CetTable_single.dynamicInput"
                  v-bind="CetTable_single"
                  v-on="CetTable_single.event"
                >
                  <ElTableColumn
                    headerAlign="center"
                    align="center"
                    :show-overflow-tooltip="true"
                    :index="table_index"
                    v-bind="ElTableColumn_index"
                  ></ElTableColumn>
                  <template v-for="(column, index) in Columns_single">
                    <el-table-column
                      v-bind="column"
                      :key="index"
                      :show-overflow-tooltip="true"
                      class-name="font0 hand"
                      label-class-name="font14"
                    >
                      <template slot-scope="scope">
                        <span v-if="scope.$index === 0">
                          <span v-if="column.prop === 'logtime'">
                            <omega-icon
                              class="icon"
                              :symbolId="
                                ElSelect_period.value === 14 ? 'month' : 'year'
                              "
                            />
                            {{
                              `${
                                ElSelect_period.value === 14
                                  ? $moment(scope.row[column.prop]).format(
                                      "YYYY年MM月"
                                    )
                                  : $moment(scope.row[column.prop]).format(
                                      "YYYY年"
                                    )
                              }共计`
                            }}
                          </span>
                          <span v-else>{{ scope.row[column.prop] }}</span>
                        </span>
                        <span v-else size="small" class="text-middle font14">
                          {{
                            column.formatter
                              ? column.formatter(
                                  scope.row,
                                  scope.column,
                                  scope.row[column.prop],
                                  scope.$index
                                )
                              : scope.row[column.prop]
                          }}
                        </span>
                      </template>
                    </el-table-column>
                  </template>
                </CetTable>
              </div>
              <!-- 综合概览/产品当量查询表格 -->
              <div
                class="fullheight"
                v-show="!singleFlag && productionPlan.tableType == 0"
              >
                <el-table
                  ref="elTab1"
                  tooltip-effect="light"
                  :data="productionPlan.table.data"
                  :span-method="productionPlantableSpanMethod"
                  :header-cell-style="tabHeaderStyle"
                  border
                  height="100%"
                >
                  <el-table-column
                    v-for="(item, index) in productionPlan.table.config.columns"
                    :header-align="[0, 1].includes(index) ? 'left' : 'right'"
                    :width="[0, 1].includes(index) ? '130px' : '100px'"
                    :fixed="[0, 1, 2].includes(index)"
                    :minWidth="100"
                    :key="index"
                    :show-overflow-tooltip="true"
                    align="center"
                    :prop="item.id"
                    :label="item.name"
                  >
                    <template slot-scope="scope">
                      <span>{{ getTableCellData(scope, item.id) }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <!-- 综合概览/产品当量录入表格 -->
              <div
                class="fullheight"
                v-show="!singleFlag && productionPlan.tableType == 1"
              >
                <el-table
                  ref="elTab2"
                  tooltip-effect="light"
                  :data="productionPlan.inputProductionTable.data"
                  :span-method="productionPlantableSpanMethod"
                  :header-cell-style="tabHeaderStyle"
                  border
                  height="100%"
                >
                  <el-table-column
                    v-for="(item, index) in productionPlan.table.config.columns"
                    :width="
                      [0, 1].includes(index)
                        ? '130px'
                        : index === 2
                        ? '200px'
                        : '85px'
                    "
                    :fixed="[0, 1, 2].includes(index)"
                    :minWidth="150"
                    :key="index"
                    :show-overflow-tooltip="true"
                    align="center"
                    :prop="item.id"
                    :label="item.name"
                  >
                    <template slot-scope="scope">
                      <span v-show="!tableShowInput(index, scope.row)">
                        {{ getTableCellData(scope, item.id) }}
                      </span>
                      <el-input
                        size="small"
                        v-if="tableShowInput(index, scope.row)"
                        :class="{
                          projectOverview_tabModifyInput: true,
                          addProductionPlaTableInput: index == 2
                        }"
                        v-model="scope.row[item.id].val"
                        @keyup.native="handleNum(scope.row, item.id, 2)"
                      ></el-input>
                      <el-tooltip
                        v-if="showDealBtn(index, scope.row)"
                        :content="`分解到${
                          ElSelect_period.value === 14 ? '日' : '月'
                        }`"
                      >
                        <omega-icon
                          class="tabTextBlue"
                          symbolId="decompose"
                          @click="
                            tableRowDecompose(
                              productionPlan.inputProductionTable.data[
                                scope.$index
                              ]
                            )
                          "
                        />
                      </el-tooltip>
                      <el-tooltip
                        v-if="showDealBtn(index, scope.row)"
                        :content="`汇总到${
                          ElSelect_period.value === 14 ? '月' : '年'
                        }`"
                      >
                        <omega-icon
                          class="tabTextBlue"
                          symbolId="join"
                          @click="
                            tableRowJoin(
                              productionPlan.inputProductionTable.data[
                                scope.$index
                              ]
                            )
                          "
                        />
                      </el-tooltip>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import common from "eem-utils/common";
import customApi from "@/api/custom";
import { FullScreenLoading } from "@omega/http/loading.js";
import TREE_PARAMS from "@/store/treeParams.js";
import piemCommon from "@/utils/common";
export default {
  name: "index",
  computed: {
    token() {
      return this.$store.state.token;
    },
    systemCfg() {
      return this.$store.state.systemCfg;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    language() {
      return window.localStorage.getItem("omega_language") === "en";
    },
    asideWidth() {
      return this.collapsed ? "15px" : "320px";
    },
    collapseText() {
      return this.collapsed ? ">" : "<";
    }
  },

  data() {
    const language = window.localStorage.getItem("omega_language") === "en";
    return {
      collapsed: false,
      fullScreen: false, // 表格是否处于全屏状态
      treeNode: {},
      // 图表类型选择
      chartTypeRadio: [1, 2],
      chartTypeList: [
        {
          name: $T("预测值"),
          label: 1
        },
        {
          name: $T("计划值"),
          label: 2
        }
      ],
      showChart: true, // 是否显示chart图表
      // 图表配置
      CetChart_1: {
        inputData_in: {},
        config: {
          options: {
            toolbox: {
              top: 0,
              right: 30,
              feature: {
                saveAsImage: {
                  title: $T("保存为图片")
                }
              }
            },
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "shadow"
              },
              formatter: params => {
                let uniten = this.totalChartData?.unitEn || "--";
                let str = params[0].name + "<br/>";
                params.forEach(item => {
                  str +=
                    item.marker +
                    `<span style="
                        width: 100%;
                        display: inline-block;">
                        ${item.seriesName}(${uniten || "--"})：
                        <span style="
                          float: right;
                          margin-right: 15px;">
                          ${
                            common.isEffectiveValue(item.value)
                              ? item.value
                              : "--"
                          }
                        </span>
                      </span><br/>`;
                });
                return str;
              }
            },
            grid: {
              top: "40",
              left: "40",
              right: "30",
              bottom: "10",
              containLabel: true
            },
            legend: {
              data: []
            },
            xAxis: [
              {
                type: "category",
                axisTick: {
                  alignWithLabel: true
                },
                name: "",
                data: []
              }
            ],
            yAxis: [
              {
                type: "value",
                name: "",
                position: "left",
                nameLocation: "end"
              }
            ],
            series: []
          }
        }
      },
      // 图表配置
      CetChart_2: {
        inputData_in: {},
        config: {
          options: {
            toolbox: {
              top: 0,
              right: 120,
              feature: {
                saveAsImage: {
                  title: $T("保存为图片")
                }
              }
            },
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "shadow"
              },
              formatter: params => {
                let uniten = this.singleChartData?.unitEn || "--";
                let str = params[0].name + "<br/>";
                params.forEach((item, index) => {
                  if ([3, 4].includes(index)) {
                    uniten = "%";
                  }
                  str +=
                    item.marker +
                    `<span style="
                        width: 100%;
                        display: inline-block;">
                        ${item.seriesName}(${uniten || "--"})：
                        <span style="
                          float: right;
                          margin-right: 15px;">
                          ${
                            common.isEffectiveValue(item.value)
                              ? item.value
                              : "--"
                          }
                        </span>
                      </span><br/>`;
                });
                return str;
              }
            },
            grid: {
              top: "40",
              left: "40",
              right: "40",
              bottom: "10",
              containLabel: true
            },
            legend: {
              data: []
            },
            xAxis: [
              {
                type: "category",
                axisTick: {
                  alignWithLabel: true
                },
                name: "",
                data: []
              }
            ],
            yAxis: [
              {
                type: "value",
                name: "",
                position: "left",
                nameLocation: "end"
              },
              {
                type: "value",
                name: $T("偏差（%）"),
                position: "right",
                nameLocation: "end"
              }
            ],
            series: []
          }
        }
      },
      // 生产计划与实绩跟踪
      productionPlan: {
        tableType: 0, // 表格类型，0为展示，1为录入
        table: {
          config: {
            columns: [
              {
                name: $T("统计项目"),
                id: "key1"
              },
              {
                name: $T("统计项目"),
                id: "key2"
              },
              {
                name: $T("全年"),
                id: "key3"
              },
              {
                name: "1月",
                id: "key4"
              },
              {
                name: "2月",
                id: "key5"
              },
              {
                name: "3月",
                id: "key6"
              },
              {
                name: "4月",
                id: "key7"
              },
              {
                name: "5月",
                id: "key8"
              },
              {
                name: "6月",
                id: "key9"
              },
              {
                name: "7月",
                id: "key10"
              },
              {
                name: "8月",
                id: "key11"
              },
              {
                name: "9月",
                id: "key12"
              },
              {
                name: "10月",
                id: "key13"
              },
              {
                name: "11月",
                id: "key14"
              },
              {
                name: "12月",
                id: "key15"
              }
            ]
          },
          data: []
        },
        inputProductionTable: {
          data: []
        }
      },
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: this.setNodeClasses
          },
          callback: {
            beforeClick: this.beforeClick
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out
        }
      },
      CetButton_export: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_export_statusTrigger_out
        }
      },
      CetButton_exportTemplate: {
        visible_in: true,
        disable_in: false,
        title: $T("导出模板"),
        type: "",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_exportTemplate_statusTrigger_out
        }
      },
      CetButton_exportData: {
        visible_in: true,
        disable_in: false,
        title: $T("导出数据"),
        type: "",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_exportData_statusTrigger_out
        }
      },
      CetButton_import1: {
        visible_in: true,
        disable_in: false,
        title: $T("从Excel导入"),
        type: "",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_import1_statusTrigger_out
        }
      },
      CetButton_import2: {
        visible_in: true,
        disable_in: false,
        title: $T("从预测值导入"),
        type: "",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_import2_statusTrigger_out
        }
      },
      CetButton_inputPlan: {
        visible_in: true,
        disable_in: false,
        title: $T("录入计划"),
        type: "",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_inputPlan_statusTrigger_out
        }
      },
      ElSelect_producttype: {
        value: 0,
        style: {
          width: language ? "320px" : "200px"
        },
        event: {
          change: this.ElSelect_producttype_change_out
        }
      },
      ElOption_producttype: {
        options_in: [],
        key: "producttype",
        value: "producttype",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_period: {
        value: 17,
        style: {
          width: "102px"
        },
        event: {
          change: this.ElSelect_period_change_out
        }
      },
      ElOption_period: {
        options_in: [
          { id: 14, text: $T("月") },
          { id: 17, text: $T("年") }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetDatePicker_1: {
        disable_in: false,
        val: new Date(),
        config: {
          type: "year",
          format: language ? "yyyy" : "yyyy年",
          rangeSeparator: "~",
          size: "small",
          style: {
            display: "inline-block",
            width: "150px"
          },
          pickerOptions: {}
        }
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      // single表格组件
      CetTable_single: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        "cell-class-name": this.singleTabCellClass,
        highlightCurrentRow: false,
        event: {}
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        label: "#", //列名
        headerAlign: "left",
        align: "lftt",
        showOverflowTooltip: true,
        width: language ? "100px" : "50px" //绝对宽度
      },
      Columns_single: [],
      singleFlag: false, // 选中产品是否为单一产品
      singleChartData: {}, // 单一产品的图表数据
      totalChartData: {}, // 产品当量的图表数据
      totalProductType: 50000, // 产品当量枚举值
      totalProductTypeC: 0, // 综合概览枚举值
      totalEnergyNum: 0,
      isHasEdit: false
    };
  },
  watch: {
    "CetDatePicker_1.val": {
      handler(val) {
        this.init();
        this.productionPlan.tableType = 0;
        const timeStr = this.ElSelect_period.value === 14 ? "month" : "year";
        this.CetButton_inputPlan.visible_in =
          this.$moment().startOf(timeStr).valueOf() <=
          this.$moment(val).startOf(timeStr).valueOf();
      },
      deep: true
    }
  },
  methods: {
    getTableCellData(scope, id) {
      return scope.row[id]?.val || "--";
    },
    onCollapseBtnClick() {
      this.collapsed = !this.collapsed;
    },
    table_index(index) {
      if (index === 0) return null;
      else if (index < 10) return `0${index}`;
      else return index;
    },
    // 点击表格全屏按钮
    fullScreenBtnClick() {
      this.fullScreen = !this.fullScreen;
      this.showChart = !this.fullScreen;
    },
    // 返回接口查询参数
    getParams() {
      const timeType = this.ElSelect_period.value;
      let startTime = null;
      let endTime = null;
      if (timeType === 17) {
        startTime = this.$moment(this.CetDatePicker_1.val)
          .startOf("year")
          .valueOf();
        endTime =
          this.$moment(this.CetDatePicker_1.val).endOf("year").valueOf() + 1;
      } else {
        startTime = this.$moment(this.CetDatePicker_1.val)
          .startOf("month")
          .valueOf();
        endTime =
          this.$moment(this.CetDatePicker_1.val).endOf("month").valueOf() + 1;
      }
      const currentProduct = this.ElSelect_producttype.value;
      let productType = [currentProduct];
      if (
        [this.totalProductType, this.totalProductTypeC].includes(currentProduct)
      ) {
        productType = this.ElOption_producttype.options_in
          .map(item => item.producttype)
          .filter(item => item !== 0);
      }
      return {
        objectLabel: this.treeNode.modelLabel,
        objectId: this.treeNode.id,
        aggregationCycle: this.ElSelect_period.value,
        productType,
        startTime,
        endTime
      };
    },
    // 表格是否显示输入框
    tableShowInput(index, row) {
      if (
        ![0, 1].includes(index) &&
        [$T("当年生产计划"), $T("当月生产计划")].includes(row?.key1?.val)
      ) {
        if (row?.productType === this.totalProductType) {
          return false;
        }
        const timeStr = this.ElSelect_period.value === 14 ? "month" : "year";
        const isNew = this.$moment(this.CetDatePicker_1.val).isSame(
          this.$moment(),
          timeStr
        );
        if (index !== 2 && isNew) {
          if (this.ElSelect_period.value === 17) {
            const month = this.$moment().month();
            if (index < month + 3) {
              return false;
            }
          } else if (this.ElSelect_period.value === 14) {
            const date = this.$moment().date();
            if (index < date + 2) {
              return false;
            }
          }
        }
        return true;
      }
      return false;
    },
    // 是否显示分摊或合并图标
    showDealBtn(index, row) {
      return (
        index === 2 &&
        [$T("当年生产计划"), $T("当月生产计划")].includes(row?.key1?.val) &&
        ![this.totalProductType, this.totalProductTypeC].includes(
          row?.productType
        )
      );
    },
    // 获取节点树
    async getTree() {
      var me = this;
      me.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      const params = {
        rootID: this.projectId,
        rootLabel: "oilcompany",
        subLayerConditions: TREE_PARAMS.energyQuery,
        treeReturnEnable: true
      };
      await customApi.getNodeTree(params).then(res => {
        if (res.code === 0) {
          const resData = res.data || [];
          this.handleDisablingTree(resData);
          me.CetGiantTree_1.inputData_in = resData;
          const obj = me._.find(piemCommon.findFirstChildSelectState(resData), [
            "childSelectState",
            1
          ]);
          me.CetGiantTree_1.selectNode = obj;
        } else {
          me.CetGiantTree_1.inputData_in = [];
          me.CetGiantTree_1.selectNode = null;
        }
      });
    },
    /**
     * 设置节点禁用
     */
    handleDisablingTree(nodes) {
      nodes.forEach(item => {
        if (item.childSelectState == 2) {
          item.disableNode = true;
        } else {
          item.disableNode = false;
        }
        if (item.children && item.children.length > 0) {
          this.handleDisablingTree(item.children);
        }
      });
    },
    setNodeClasses(treeId, treeNode) {
      return treeNode.disableNode
        ? { add: ["disableNode"] }
        : { remove: ["disableNode"] };
    },
    /**
     * 节点是否可选择
     */
    beforeClick(treeId, treeNode) {
      if (treeNode.childSelectState == 2) {
        return false;
      }
      return true;
    },
    // 获取产品类型
    async queryProduct() {
      const queryData = {
        rootLabel: "project",
        rootCondition: {
          filter: {
            expressions: [
              {
                prop: "id",
                operator: "EQ",
                limit: this.projectId
              }
            ]
          },
          include_submodel: true
        },
        subLayerConditions: [
          {
            filter: null,
            include_relations: [],
            modelLabel: "product",
            props: []
          }
        ]
      };
      const res = await customApi.queryModel(queryData);
      this.ElOption_producttype.options_in = res?.data[0]?.product_model || [];
      const item = this.ElOption_producttype.options_in.find(
        item => item.producttype === this.totalProductType
      );
      if (!item) {
        this.ElOption_producttype.options_in.unshift({
          producttype: 0,
          name: "综合概览"
        });
      }
      // this.ElOption_producttype.options_in.unshift({
      //   producttype: 50000,
      //   name: "产品当量"
      // });
      if (this.ElOption_producttype.options_in.length) {
        this.ElSelect_producttype.value =
          this.ElOption_producttype.options_in[0].producttype;
      }
      this.showChart =
        this.ElSelect_producttype.value !== 0 && !this.fullScreen;
    },
    //初始化
    init() {
      if (!this.treeNode?.id) {
        return;
      }
      this.productionPlan.tableType = 0;
      if (
        [this.totalProductType, this.totalProductTypeC].includes(
          this.ElSelect_producttype.value
        )
      ) {
        this.singleFlag = false;
        this.$nextTick(() => {
          this.getTotalProductData();
        });
      } else {
        this.singleFlag = true;
        this.getSingleProductData();
      }
      this.showChart =
        this.ElSelect_producttype.value !== 0 && !this.fullScreen;
      const timeStr = this.ElSelect_period.value === 14 ? "month" : "year";
      this.CetButton_inputPlan.visible_in =
        [this.totalProductType, this.totalProductTypeC].includes(
          this.ElSelect_producttype.value
        ) &&
        this.$moment().startOf(timeStr).valueOf() <=
          this.$moment(this.CetDatePicker_1.val).startOf(timeStr).valueOf();
    },
    // 图表类型选择
    chartTypeRadioChange_out() {
      if (
        [this.totalProductType, this.totalProductTypeC].includes(
          this.ElSelect_producttype.value
        )
      ) {
        this.setTotalChart(this.totalChartData);
      } else {
        this.setSingleChart(this.singleChartData);
      }
    },
    /**
     * 查询综合概览/产品当量数据。
     * adaptiveUnit:单位是否自适应。true表示会适配单位，false表示基础单位
     */
    async getTotalProductData(adaptiveUnit = true) {
      const queryData = this.getParams();
      const tableRes = await customApi.queryProductEquivalentReportByNode({
        ...queryData,
        adaptiveUnit
      });
      const tableResData = tableRes?.data || {};
      this.setTotalTable(tableResData);
      if (this.ElSelect_producttype.value === this.totalProductType) {
        queryData.productType = [this.totalProductType];
        const chartRes =
          await customApi.getProductPlanWithProductEquivalentCurve(queryData);
        const chartData = chartRes?.data || {};
        this.totalChartData = this._.cloneDeep(chartData);
        this.setTotalChart(chartData);
      }
    },
    // 设置综合概览/产品当量表格数据
    setTotalTable(data) {
      const columnList = data?.actualCurrentProduction[0]?.valueList || [];
      const periodName = this.ElSelect_period.value === 14 ? "月" : "年";
      const periodName1 = this.ElSelect_period.value === 14 ? "日" : "月";
      const formatStr1 = this.ElSelect_period.value === 14 ? "M" : "YYYY";
      const formatStr2 = this.ElSelect_period.value === 14 ? "DD" : "MM";

      const columns = [
        {
          name: $T("统计项目"),
          id: "key1"
        },
        {
          name: $T("统计项目"),
          id: "key2"
        },
        {
          name: `${this.$moment(this.CetDatePicker_1.val).format(
            formatStr1
          )}${periodName}`,
          id: "key3"
        }
      ];
      for (let i = 0; i < columnList.length; i++) {
        columns.push({
          name: `${this.$moment(columnList[i].logtime).format(
            formatStr2
          )}${periodName1}`,
          id: `key${4 + i}`,
          dateName: this.$moment(columnList[i].logtime).format(formatStr2)
        });
      }

      this.productionPlan.table.config.columns = columns;

      // 当期实际产量
      const actualCurrentProduction = this.dealTableData(
        data.actualCurrentProduction,
        `当${periodName}实际产量`
      );
      // 当期生产计划
      const currentProductionPlan = this.dealTableData(
        data.currentProductionPlan,
        `当${periodName}生产计划`
      );
      // 当期产量预测
      const currentProductionForecast = this.dealTableData(
        data.currentProductionForecast,
        `当${periodName}产量预测`
      );
      // 上期同期产量
      const lastYearOutput = this.dealTableData(
        data.lastYearOutput,
        `上${periodName}同期产量`
      );
      const tableData = [
        ...actualCurrentProduction,
        ...currentProductionPlan,
        ...currentProductionForecast,
        ...lastYearOutput
      ];
      if (this.productionPlan.tableType == 0) {
        this.productionPlan.table.data = tableData;
      } else {
        this.productionPlan.inputProductionTable.data = tableData;
      }
      this.$nextTick(() => {
        this.$refs.elTab1?.doLayout();
        this.$refs.elTab2?.doLayout();
      });
    },
    // 处理表格数据
    dealTableData(data, title) {
      return data.map(item => {
        const productName =
          this.ElOption_producttype.options_in.find(
            it => it.producttype === item.productType
          )?.name || "--";
        const obj = {
          productType: item.productType,
          coef: item.productUnitCoef,
          key1: { val: title },
          key2: { val: `${productName}(${item.productUnit || "--"})` },
          key3: { val: this.filNumValue(item.totalProductValue) }
        };
        item.valueList.forEach(({ value, logtime }, i) => {
          const startStr = this.ElSelect_period.value === 14 ? "day" : "month";
          obj["key" + (i + 4)] = { val: this.filNumValue(value) };
          if (
            this.$moment().startOf(startStr).valueOf() <= logtime &&
            item.productType !== this.totalProductType
          ) {
            obj["key" + (i + 4)].logtime = logtime;
          }
        });
        return obj;
      });
    },
    // 设置产品当量图表数据
    setTotalChart(data) {
      const productData = this.ElOption_producttype.options_in.find(
        item => item.producttype === this.ElSelect_producttype.value
      );
      const unit = data.unitEn || "--";
      this.CetChart_1.config.options.yAxis[0].name = `${productData.name}(${unit})`;

      const periodName = this.ElSelect_period.value === 14 ? "上月" : "去年";
      const legendData = [
        $T("实际值"),
        $T(`${periodName}同期`),
        $T("预测值"),
        $T("计划值")
      ];

      this.CetChart_1.config.options.legend.data = legendData;

      const xAxisData = (data.productEquivalentCurveVoList || []).map(item => {
        const formatStr = this.ElSelect_period.value === 17 ? "MM" : "DD";
        return this.$moment(item.logtime).format(formatStr);
      });
      this.CetChart_1.config.options.xAxis[0].data = xAxisData;

      const seriesData = data.productEquivalentCurveVoList || [];
      this.CetChart_1.config.options.series = [
        {
          name: legendData[0],
          type: "bar",
          data: seriesData.map(item =>
            common.formatNumberWithPrecision(item.productDataValue, 2)
          ),
          barWidth: "20%"
        },
        {
          name: legendData[1],
          type: "bar",
          data: seriesData.map(item =>
            common.formatNumberWithPrecision(item.lastproductDataValue, 2)
          ),
          barWidth: "20%"
        },
        {
          name: legendData[2],
          type: "line",
          data: seriesData.map(item =>
            common.formatNumberWithPrecision(item.productForecastValue, 2)
          )
        },
        {
          name: legendData[3],
          type: "line",
          yAxisIndex: 0,
          data: seriesData.map(item =>
            common.formatNumberWithPrecision(item.productPlanValue, 2)
          )
        }
      ];
      if (this.chartTypeRadio.length === 0) {
        this.CetChart_1.config.options.series.splice(2, 2);
      } else if (this.chartTypeRadio.length === 1) {
        this.chartTypeRadio.forEach(item => {
          if (Number(item) === 1) {
            this.CetChart_1.config.options.series.splice(3, 1);
          } else if (Number(item) === 2) {
            this.CetChart_1.config.options.series.splice(2, 1);
          }
        });
      }
      this.$nextTick(() => {
        this.$refs.CetChart_1?.mergeOptions(
          this.CetChart_1.config.options,
          true
        );
      });
    },
    // 查询单一产品趋势图和表格数据
    async getSingleProductData() {
      if (this._.isNil(this.ElSelect_producttype.value)) {
        this.CetChart_2.config.options.series.forEach(item => {
          item.data = [];
        });
        return;
      }

      const chartRes = await customApi.getProductPlanWithOneProductCurve({
        ...this.getParams(),
        adaptiveUnit: true
      });
      const chartResData = chartRes?.data || {};
      this.singleChartData = this._.cloneDeep(chartResData);
      this.setSingleChart(chartResData);
      const tableRes = await customApi.queryProductReportByProductType(
        this.getParams()
      );
      this.setSingleTable(tableRes?.data || {});
    },
    // 设置单一产品图表数据
    setSingleChart(data) {
      const timeType = this.ElSelect_period.value;
      const xAxisData = [];
      const seriesData1 = [];
      const seriesData2 = [];
      const seriesData3 = [];
      const seriesData4 = [];
      const seriesData5 = [];
      (data.oneProductCurveVoList || []).forEach(item => {
        if (timeType === 17) {
          xAxisData.push(this.$moment(item.logtime).format("MM"));
        } else {
          xAxisData.push(this.$moment(item.logtime).format("DD"));
        }
        seriesData1.push(
          common.formatNumberWithPrecision(item.productDataValue, 2)
        );
        seriesData2.push(
          common.formatNumberWithPrecision(item.productForecastvalue, 2)
        );
        seriesData3.push(
          common.formatNumberWithPrecision(item.productPlanValue, 2)
        );
        seriesData4.push(
          common.formatNumberWithPrecision(item.productPredictionDeviation, 2)
        );
        seriesData5.push(
          common.formatNumberWithPrecision(item.productPlanDeviation, 2)
        );
      });

      this.CetChart_2.config.options.legend.data = [
        $T("实际值"),
        $T("预测值"),
        $T("计划值"),
        $T("预测偏差"),
        $T("计划偏差")
      ];
      const productName =
        this.ElOption_producttype.options_in.find(
          item => item.producttype === this.ElSelect_producttype.value
        )?.name || "--";
      this.CetChart_2.config.options.yAxis[0].name = `${productName}(${
        data.unitEn || "--"
      })`;
      this.CetChart_2.config.options.xAxis[0].data = xAxisData;
      this.CetChart_2.config.options.series = [
        {
          name: $T("实际值"),
          type: "bar",
          yAxisIndex: 0,
          data: seriesData1
        },
        {
          name: $T("预测值"),
          type: "bar",
          yAxisIndex: 0,
          data: seriesData2
        },
        {
          name: $T("计划值"),
          type: "line",
          yAxisIndex: 0,
          data: seriesData3
        },
        {
          name: $T("预测偏差"),
          type: "line",
          yAxisIndex: 1,
          data: seriesData4
        },
        {
          name: $T("计划偏差"),
          type: "line",
          yAxisIndex: 1,
          data: seriesData5
        }
      ];
      if (this.chartTypeRadio.length === 0) {
        this.CetChart_2.config.options.series.splice(1);
        this.CetChart_2.config.options.legend.data.splice(1);
      } else if (this.chartTypeRadio.length === 1) {
        this.chartTypeRadio.forEach(item => {
          if (Number(item) === 1) {
            this.CetChart_2.config.options.series.splice(4, 1);
            this.CetChart_2.config.options.legend.data.splice(4, 1);
            this.CetChart_2.config.options.series.splice(2, 1);
            this.CetChart_2.config.options.legend.data.splice(2, 1);
          } else if (Number(item) === 2) {
            this.CetChart_2.config.options.series.splice(3, 1);
            this.CetChart_2.config.options.legend.data.splice(3, 1);
            this.CetChart_2.config.options.series.splice(1, 1);
            this.CetChart_2.config.options.legend.data.splice(1, 1);
          }
        });
      }

      this.$nextTick(() => {
        this.$refs.CetChart_2?.mergeOptions(
          this.CetChart_2.config.options,
          true
        );
      });
    },
    // 设置单一产品表格
    setSingleTable(data) {
      this.Columns_single = [
        {
          label: $T("时间"),
          prop: "logtime",
          minWidth: 100,
          showOverflowTooltip: true,
          headerAlign: "center",
          align: "center",
          formatter:
            this.ElSelect_period.value === 17
              ? common.formatDateCol("YYYY-MM")
              : common.formatDateCol("YYYY-MM-DD")
        },
        {
          label: `${$T("产量统计值")}（${data.productDataValueUnit || "--"}）`,
          prop: "productDataValue",
          minWidth: this.language ? "300px" : "100px",
          showOverflowTooltip: true,
          headerAlign: "center",
          align: "center",
          formatter: common.formatNumberCol()
        },
        {
          label: `${$T("产量计划值")}（${data.productPlanValueUnit || "--"}）`,
          prop: "productPlanValue",
          minWidth: this.language ? "340px" : "100px",
          showOverflowTooltip: true,
          headerAlign: "center",
          align: "center",
          formatter: common.formatNumberCol()
        },
        {
          label: `${$T("计划偏差")}（${data.planningDeviationUnit || "--"}）`,
          prop: "planningDeviation",
          minWidth: this.language ? "200px" : "100px",
          showOverflowTooltip: true,
          headerAlign: "center",
          align: "center",
          formatter: common.formatNumberCol()
        },
        {
          label: `${$T("产量预测值")}（${
            data.productionForecastUnit || "--"
          }）`,
          prop: "productionForecast",
          minWidth: this.language ? "340px" : "100px",
          showOverflowTooltip: true,
          headerAlign: "center",
          align: "center",
          formatter: common.formatNumberCol()
        },
        {
          label: `${$T("预测偏差")}（${data.predictionDeviationUnit || "--"}）`,
          prop: "predictionDeviation",
          minWidth: this.language ? "200px" : "100px",
          showOverflowTooltip: true,
          headerAlign: "center",
          align: "center",
          formatter: common.formatNumberCol()
        }
      ];

      this.CetTable_single.data = data.productPlanDataByTypeVoList || [];
      this.CetTable_single.data.unshift({
        logtime: data.totalProductPlanDataByTypeVo?.logtime,
        productDataValue: common.formatNumberWithPrecision(
          data.totalProductPlanDataByTypeVo?.productDataValue,
          2
        ),
        productPlanValue: common.formatNumberWithPrecision(
          data.totalProductPlanDataByTypeVo?.productPlanValue,
          2
        ),
        planningDeviation: common.formatNumberWithPrecision(
          data.totalProductPlanDataByTypeVo?.planningDeviation,
          2
        ),
        productionForecast: common.formatNumberWithPrecision(
          data.totalProductPlanDataByTypeVo?.productionForecast,
          2
        ),
        predictionDeviation: common.formatNumberWithPrecision(
          data.totalProductPlanDataByTypeVo?.predictionDeviation,
          2
        )
      });
      this.$nextTick(() => {
        this.$refs.elTab3.$refs.cetTable.doLayout();
      });
    },
    //返回计划录入
    backAddProductionPlan_out() {
      this.init();
      this.productionPlan.tableType = 0;
      this.CetButton_inputPlan.visible_in = true;
    },
    // 计划录入
    async saveProductPlanData() {
      const len = this.ElOption_producttype.options_in.length;
      const sliceStart =
        this.ElSelect_producttype.value === this.totalProductTypeC
          ? len - 1
          : len;
      const sliceEnd =
        this.ElSelect_producttype.value === this.totalProductTypeC
          ? (len - 1) * 2
          : len * 2 + 1;
      const data = this.productionPlan.inputProductionTable.data.slice(
        sliceStart,
        sliceEnd
      );
      const params = data.map(item => {
        const totalCycle = this.ElSelect_period.value,
          singleCycle = totalCycle === 17 ? 14 : 12,
          startStr = totalCycle === 17 ? "year" : "month";
        const allItemData = Object.entries(item)
          .filter(
            item =>
              !["productType", "coef", "key1", "key2", "key3"].includes(item[0])
          )
          .map(item => item[1]);
        let productPlanDataList = [
          {
            aggregationCycle: totalCycle,
            logtime: this.$moment(this.CetDatePicker_1.val)
              .startOf(startStr)
              .valueOf(),
            productPlanValue: common.isEffectiveValue(item.key3?.val)
              ? +item.key3.val
              : null,
            productType: item.productType
          }
        ];
        allItemData.forEach(({ logtime, val }) => {
          logtime &&
            productPlanDataList.push({
              aggregationCycle: singleCycle,
              logtime,
              productPlanValue: common.isEffectiveValue(val)
                ? +val * item.coef
                : null,
              productType: item.productType
            });
        });
        return {
          objectId: this.treeNode.id,
          objectLabel: this.treeNode.modelLabel,
          productPlanDataList
        };
      });

      const res = await customApi.saveProductPlanData(params);

      if (res.code === 0) {
        this.$message.success($T("保存成功"));
        this.productionPlan.tableType = 0;
        this.init();
      }
    },
    //导出按钮点击
    CetButton_export_statusTrigger_out() {
      common.downExcel(
        "/eem-service/v1/productPlan/exportProductReportByNode",
        this.getParams(),
        this.token,
        this.projectId
      );
    },
    //导出模板
    CetButton_exportTemplate_statusTrigger_out() {
      const params = this.getParams();
      common.downExcel(
        "/eem-service/v1/productPlan/exportProductPlanInputTemplate",
        {
          aggregationCycle: params.aggregationCycle,
          startTime: params.startTime,
          endTime: params.endTime
        },
        this.token,
        this.projectId
      );
    },
    //导出数据
    CetButton_exportData_statusTrigger_out() {
      common.downExcel(
        "/eem-service/v1/productPlan/exportProductReportByNode",
        this.getParams(),
        this.token,
        this.projectId
      );
    },
    //从Excel导入
    CetButton_import1_statusTrigger_out() {
      this.$refs.uploadBtn.click();
    },
    //从预测值导入
    CetButton_import2_statusTrigger_out() {
      const optionLen = this.ElOption_producttype.options_in.length;
      const len =
        this.ElSelect_producttype.value === this.totalProductTypeC
          ? optionLen - 1
          : optionLen;

      for (let index = 0; index < len; index++) {
        const rowData =
          this.productionPlan.inputProductionTable.data[len + index];
        // 需要填值的列的key
        const keys = Object.entries(rowData)
          .filter(item => item[0] === "key3" || item[1].logtime)
          .map(item => item[0]);
        keys.forEach(key => {
          this.productionPlan.inputProductionTable.data[len + index][key].val =
            this.productionPlan.inputProductionTable.data[len * 2 + index][
              key
            ].val;
        });
      }
    },
    // 导入前
    handleBeforeUpload(file) {
      const loading = new FullScreenLoading();
      loading.showLoading();
      if (
        file.name.indexOf(".xls") !== -1 ||
        file.name.indexOf(".xlsx") !== -1
      ) {
        var uploadDocSize = this.systemCfg.uploadDocSize || 0.5;
        const isLimit100M = file.size / 1024 / 1024 < uploadDocSize;
        if (!isLimit100M) {
          this.$message.error(
            `${$T("上传文件超过规定的最大上传大小$0M", uploadDocSize)}`
          );
        }
        return isLimit100M;
      } else {
        loading.hideLoading();
        this.$message({
          type: "warning",
          message: $T("只能上传xls/xlsx格式文件")
        });
        return false;
      }
    },
    // 导入成功
    uploadSuccess(response) {
      const loading = new FullScreenLoading();
      loading.hideLoading();
      if (response.code === 0) {
        this.$message({
          message: $T("导入成功"),
          type: "success"
        });
        this.init();
      } else if (response.code !== 0) {
        this.$message({
          type: "error",
          message: response.msg
        });
      }
    },
    uploadError() {
      const loading = new FullScreenLoading();
      loading.hideLoading();
      this.$message({
        message: $T("导入失败"),
        type: "error"
      });
    },
    //点击录入计划
    CetButton_inputPlan_statusTrigger_out() {
      this.getTotalProductData(false);
      this.isHasEdit = false;
      this.CetButton_inputPlan.visible_in = false;
      this.productionPlan.table.copyData = JSON.parse(
        JSON.stringify(this.productionPlan.table.data)
      );
      this.productionPlan.tableType = 1;
    },
    //切换产品类型
    ElSelect_producttype_change_out() {
      this.init();
    },
    // 切换查询时段
    ElSelect_period_change_out(val) {
      if (val === 14) {
        this.CetDatePicker_1.config.type = "month";
        let formatStr = this.language ? "yyyy-MM" : "yyyy年MM月";
        this.CetDatePicker_1.config.format = formatStr;
      } else {
        this.CetDatePicker_1.config.type = "year";
        let formatStr = this.language ? "yyyy" : "yyyy年";
        this.CetDatePicker_1.config.format = formatStr;
      }
      this.init();
    },
    //点击节点树
    CetGiantTree_1_currentNode_out(val) {
      if (val.childSelectState == 2) return;
      this.treeNode = this._.cloneDeep(val);
      this.init();
    },
    //点击上一时段按钮
    CetButton_prv_statusTrigger_out() {
      if (this.CetDatePicker_1.val) {
        const date = this.$moment(this.CetDatePicker_1.val);
        if (this.ElSelect_period.value === 17) {
          this.CetDatePicker_1.val = date.subtract(1, "y")._d;
        } else if (this.ElSelect_period.value === 14) {
          this.CetDatePicker_1.val = date.subtract(1, "M")._d;
        }
      } else {
        this.CetDatePicker_1.val = new Date();
      }
    },
    //点击下一段时间按钮
    CetButton_next_statusTrigger_out() {
      if (this.CetDatePicker_1.val) {
        const date = this.$moment(this.CetDatePicker_1.val);
        if (this.ElSelect_period.value === 17) {
          this.CetDatePicker_1.val = date.add(1, "y")._d;
        } else if (this.ElSelect_period.value === 14) {
          this.CetDatePicker_1.val = date.add(1, "M")._d;
        }
      } else {
        this.CetDatePicker_1.val = new Date();
      }
    },
    // 分摊
    tableRowDecompose(data) {
      // 将key3.val的值减去历史数据，再平均分摊到未来每个周期
      const totalVal = data.key3.val;
      if (!common.isEffectiveValue(totalVal)) {
        this.$message.warning("汇总数据为非法数据，请检查数据");
        return;
      }
      const keyArrs = Object.entries(data)
        .filter(item => item[1].logtime)
        .map(item => item[0]);
      const otherTotalVal = Object.entries(data)
        .filter(item => !["coef", "key3", ...keyArrs].includes(item[0]))
        .map(item => item[1].val)
        .reduce((pre, cur) => {
          return common.isEffectiveValue(cur) ? pre + Number(cur) : pre;
        }, 0);
      if (totalVal < otherTotalVal) {
        this.$message.warning("汇总数据小于历史数据，请检查数据");
        return;
      }
      keyArrs.forEach(item => {
        data[item].val = this.filNumValue(
          (totalVal - otherTotalVal) / keyArrs.length
        );
      });
    },
    // 合并
    tableRowJoin(data) {
      // 合并的值为key3.val,需要合并的值为key3后边的每一个val
      const valueArr = Object.entries(data)
        .filter(item => !["coef", "key1", "key2", "key3"].includes(item[0]))
        .map(item => item[1]?.val);
      data.key3.val = valueArr.reduce((pre, cur) => {
        return common.isEffectiveValue(cur) ? pre + Number(cur) : pre;
      }, 0);
      data.key3.val = this.filNumValue(data.key3.val);
    },
    // 表格合并规则
    productionPlantableSpanMethod({ rowIndex, columnIndex }) {
      const len = this.ElOption_producttype.options_in.filter(
        item => item.producttype
      ).length;
      if (!len) {
        return {
          rowspan: 1,
          colspan: 1
        };
      }
      if (columnIndex === 0) {
        if (rowIndex % len === 0) {
          return {
            rowspan: len,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },
    // 表头合并规则
    tabHeaderStyle({ columnIndex }) {
      if (columnIndex === 0) {
        return { "padding-left": "14px" };
      } else if (columnIndex === 1) {
        return { display: "none" };
      } else {
        return { "padding-right": "14px" };
      }
    },
    // 单个产品表格样式
    singleTabCellClass({ row, rowIndex, columnIndex }) {
      if (rowIndex === 0) {
        return this.ElSelect_period.value === 14 ? "ZS" : "bg4";
      }
      if (columnIndex === 4 && common.isEffectiveValue(row.planningDeviation)) {
        if (row.planningDeviation < 0) return "warnText";
        if (row.planningDeviation > 0) return "successText";
      }
      if (
        columnIndex === 6 &&
        common.isEffectiveValue(row.predictionDeviation)
      ) {
        if (row.predictionDeviation < 0) return "warnText";
        if (row.predictionDeviation > 0) return "successText";
      }
    },
    // 输入控制
    handleNum(row, key, num) {
      var value;
      if (typeof row[key] === "object" && row[key]) {
        value = row[key].val;
      } else {
        value = row[key];
      }
      var reg = new RegExp("^(\\-)*(\\d+)\\.(\\d{0," + num + "}).*$");
      var val = String(value);
      if (val) {
        val = val.replace(/[^\d.]/g, ""); //清除数字和'.'以外的字符
        val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", "."); //只保留顺位第一的'.'
        val = val.replace(reg, "$1$2.$3"); //只能输入两位位小数
      }
      if (val.length >= 2 && val.indexOf(".") !== 1 && val[0] === "0") {
        //在非小数的时候清除前导0
        val = val.replace(/0/, "");
      }
      if (val.length >= 2 && val.indexOf(".") === 0) {
        // 在先输入小数点时补0
        val = Number(val);
      }
      if (typeof row[key] === "object" && row[key]) {
        row[key].val = val;
      } else {
        row[key] = val;
      }
      this.isHasEdit = true;
    },
    // 格式化数据
    filNumValue(val) {
      if (["", "NaN", null, NaN, undefined].includes(val)) {
        return null;
      } else if (Number(val) === 0) {
        return "0.00";
      } else {
        return Number(val).toFixed2(2);
      }
    }
  },
  mounted() {
    // tab表头合并
    this.$nextTick(() => {
      const dom = document
        .getElementsByClassName("projectOverview")[0]
        .getElementsByClassName("el-table__header");
      for (let index = 0; index < dom.length; index++) {
        dom[index].rows[0].cells[0].colSpan = 2;
      }
    });
  },
  async activated() {
    await this.queryProduct();
    this.getTree();
  }
};
</script>
<style lang="scss" scoped>
.page :deep(.v-modal) {
  display: none;
}
.title {
  font-weight: bold;
  @include font_size(H2);
}
.customElSelect :deep(.prefix) {
  @include font_size(Ab);
}
.icon {
  vertical-align: middle;
  @include font_size(H4);
}
.fullBtn {
  cursor: pointer;
  font-size: 32px;
}
.page :deep(.warnText) {
  @include font_color(Sta3);
}
.page :deep(.successText) {
  @include font_color(Sta1);
}
.page :deep(.ZS) {
  border: none;
  @include themeify {
    background-color: rgba(themed(ZS), 10%);
  }
}
.page :deep(.bg4) {
  border: none;
  @include background_color(BG4);
}
.page :deep(td) {
  padding: 4px 0;
  div {
    line-height: 32px;
  }
}
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .customElSelect :deep(.prefix) {
    @include font_size(Ab);
  }
  .timeBtn :deep() {
    button {
      width: 32px;
      display: flex;
      justify-content: center;
    }
    span {
      display: none;
    }
  }
}
.projectOverview {
  :deep(.table-cell-red1 span) {
    color: red;
  }
}
.tabTextBlue {
  cursor: pointer;
  vertical-align: middle;
  @include font_size(I1);
  @include margin(0 J 0);
}
.aside-collapsed {
  padding: 0 !important;
}
.leftTree {
  position: relative;
  transition: width 0.3s;
}
.collapse-btn {
  cursor: pointer;
  position: absolute;
  top: 0;
  right: 0px;
  bottom: 0;
  margin: auto;
  width: 14px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  vertical-align: middle;
  @include background_color(ZS);
  @include font_color(T5);
  border-radius: 3px;
}
.projectOverview_tabModifyInput.addProductionPlaTableInput {
  width: calc(100% - 65px);
}
.projectOverview_tabModifyInput > input {
  text-align: center;
}

.projectOverview_tabModifyInput.addProductionPlaTableInput.minWifth {
  width: calc(100% - 80px);
}
.projectOverviewTabNoHeader > .el-table__header-wrapper {
  display: none;
}
.treeStyle {
  :deep(.disableNode) {
    @include font_color(T6 !important);
    cursor: not-allowed !important;
    opacity: 0.6;
  }
}
</style>
