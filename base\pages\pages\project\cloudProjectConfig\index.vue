<template>
  <div class="page eem-common">
    <div class="fullfilled flex-column">
      <div class="backPlatform" v-if="isJump" @click="jumpBack">
        <i class="el-icon-d-arrow-left"></i>
        {{ $T("返回") }}
      </div>
      <div class="projectInfo pJ3 bg1 brC flex-row">
        <UploadImg
          class="loadImg mrJ3"
          :static_in="true"
          :imgUrl.sync="projectInfo.pic"
        />
        <div class="flex-auto flex-column">
          <div class="common-title-H2">
            {{ projectInfo.name | formatStr }}
          </div>
          <div class="flex-auto flex-row mtJ1 projectInfoList" height="auto">
            <div class="flex-auto flex-row">
              <div class="projectInfoItem">
                <div class="label">{{ $T("项目编号") }}</div>
                <el-tooltip
                  effect="light"
                  :content="projectInfo.code"
                  placement="bottom"
                >
                  <div class="value text-ellipsis fl">
                    {{ projectInfo.code | formatStr }}
                  </div>
                </el-tooltip>
              </div>
              <div class="projectInfoItem">
                <div class="label">{{ $T("所属公司") }}</div>
                <el-tooltip
                  effect="light"
                  :content="projectInfo.enterprisename"
                  placement="bottom"
                >
                  <div class="value text-ellipsis fl">
                    {{ projectInfo.enterprisename | formatStr }}
                  </div>
                </el-tooltip>
              </div>
              <div class="projectInfoItem">
                <div class="label">{{ $T("所属区域") }}</div>
                <el-tooltip
                  effect="light"
                  :content="projectInfo.hierarchy"
                  placement="bottom"
                >
                  <div class="value text-ellipsis fl">
                    {{ projectInfo.hierarchy | formatStr }}
                  </div>
                </el-tooltip>
              </div>
            </div>
            <div class="rightBtn">
              <span
                class="btn mrJ3"
                @click="CetButton_projectCfg_statusTrigger_out"
              >
                {{ $T("项目设置") }}
              </span>
              <span class="btn mrJ3" @click="edditProject">
                {{ $T("编辑") }}
              </span>
              <span class="btn" @click="detailProject">{{ $T("详情") }}</span>
            </div>
          </div>
        </div>
      </div>
      <el-container class="mtJ3 flex-auto">
        <el-aside class="eem-aside bg1 brC" width="315px">
          <el-container style="height: 100%" class="flex-column">
            <!-- <div class="common-title-H2 mbJ1">
              {{ netWork ? "管网层级" : "管理层级" }}
            </div> -->
            <CetGiantTree
              ref="cetGiantTree"
              class="bg1 flex-auto"
              v-bind="CetGiantTree_1"
              v-on="CetGiantTree_1.event"
            ></CetGiantTree>
            <div class="treeFoot">
              <CetButton
                class="fr mlJ1 mtJ3"
                v-bind="CetButton_7"
                v-if="CetButton_7.visible_in"
                v-on="CetButton_7.event"
              ></CetButton>
              <CetButton
                class="fr mlJ1 mtJ3"
                v-bind="CetButton_6"
                v-if="CetButton_6.visible_in"
                v-on="CetButton_6.event"
              ></CetButton>
            </div>
          </el-container>
        </el-aside>
        <el-container class="mlJ3 fullheight flex-column eem-container">
          <div class="minWH flex-column" style="flex: 1; position: relative">
            <div class="clearfix flex-row">
              <div class="mbJ3 flex-auto text-ellipsis">
                <el-tooltip
                  effect="light"
                  :content="currentNode && currentNode.name"
                  placement="bottom-start"
                >
                  <span
                    class="common-title-H2 nodeName"
                    style="display: inline"
                  >
                    {{ currentNode && currentNode.name }}
                  </span>
                </el-tooltip>
              </div>
              <div class="clearfix mlJ2">
                <el-dropdown
                  class="fr mbJ3"
                  split-button
                  type="primary"
                  @command="dropdownClick"
                >
                  {{ $T("更多") }}
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      command="copy"
                      v-show="CetButton_12.visible_in"
                      :disabled="CetButton_12.disable_in"
                    >
                      {{ $T("复制") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      command="paste"
                      v-show="CetButton_13.visible_in"
                      :disabled="CetButton_13.disable_in"
                    >
                      {{ $T("粘贴") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      command="batchMove"
                      v-show="CetButton_21.visible_in"
                      :disabled="CetButton_21.disable_in"
                    >
                      {{ $T("批量移动") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      command="association"
                      v-show="CetButton_8.visible_in"
                      :disabled="CetButton_8.disable_in"
                    >
                      {{ $T("关联设备") }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <CetButton
                  class="fr mbJ3 mrJ1"
                  v-bind="CetButton_5"
                  v-show="CetButton_5.visible_in"
                  v-on="CetButton_5.event"
                ></CetButton>
                <CetButton
                  v-if="netWork"
                  class="fr mrJ1 mbJ3"
                  v-bind="CetButton_batchNode"
                  v-on="CetButton_batchNode.event"
                ></CetButton>
                <customElSelect
                  class="fr mrJ1 mbJ3"
                  v-show="showTab"
                  :prefix_in="$T('节点类型')"
                  v-model="selectedMenu"
                  v-bind="ElSelect_1"
                  @change="handleTabClick_out"
                >
                  <ElOption
                    v-for="item in menus"
                    :key="item"
                    :label="item"
                    :value="item"
                  ></ElOption>
                </customElSelect>
                <ElInput
                  class="fr mrJ1 mbJ3"
                  suffix-icon="el-icon-search"
                  v-model="CetTable_1.dynamicInput.name"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                ></ElInput>
              </div>
            </div>
            <div class="flex-auto">
              <CetTable
                style="height: 100%"
                :data.sync="CetTable_1.data"
                :dynamicInput.sync="CetTable_1.dynamicInput"
                v-bind="CetTable_1"
                v-on="CetTable_1.event"
                @selection-change="handleSelectionChange"
              >
                <ElTableColumn
                  type="selection"
                  width="39"
                  headerAlign="left"
                  align="left"
                ></ElTableColumn>
                <ElTableColumn
                  label="#"
                  width="55"
                  type="index"
                  headerAlign="left"
                  align="left"
                ></ElTableColumn>
                <ElTableColumn
                  v-for="(item, index) in ElTableColumnArr"
                  headerAlign="left"
                  align="left"
                  showOverflowTooltip
                  minWidth="left"
                  :key="index"
                  v-bind="item"
                ></ElTableColumn>
                <ElTableColumn
                  :label="$T('操作')"
                  width="110"
                  headerAlign="left"
                  align="left"
                  fixed="right"
                >
                  <template slot-scope="scope">
                    <span
                      class="row-handel fl mrJ3"
                      @click.stop="CetButton_3_statusTrigger_out(scope.row)"
                    >
                      {{ $T("编辑") }}
                    </span>
                    <span
                      class="row-handel fl"
                      @click.stop="CetTable_1_detailTrigger_out(scope.row)"
                    >
                      {{ $T("详情") }}
                    </span>
                  </template>
                </ElTableColumn>
              </CetTable>
            </div>
          </div>
        </el-container>
      </el-container>
    </div>
    <AddAndEditNode
      :netWork="netWork"
      :visibleTrigger_in="addAndEditNode.visibleTrigger_in"
      :closeTrigger_in="addAndEditNode.closeTrigger_in"
      :inputData_in="addAndEditNode.inputData_in"
      :fatherNode_in="fatherNode"
      :treeNameList_in="treeNameList"
      @saveData_out="addAndEditNode_saveData_out"
    />
    <addProjectConfig1
      :visibleTrigger_in="addProjectConfig1.visibleTrigger_in"
      :closeTrigger_in="addProjectConfig1.closeTrigger_in"
      :queryId_in="addProjectConfig1.queryId_in"
      :inputData_in="addProjectConfig1.inputData_in"
      :treeData_in="fatherNode"
      :type_in="type_in"
      :treeNameList_in="treeNameList"
      @saveData_out="addProjectConfig1_saveData_out"
    />
    <ProjectConfigDetail
      :visibleTrigger_in="ProjectConfigDetail.visibleTrigger_in"
      :closeTrigger_in="ProjectConfigDetail.closeTrigger_in"
      :queryId_in="ProjectConfigDetail.queryId_in"
      :inputData_in="ProjectConfigDetail.inputData_in"
      :dataInfo_in="currentTabItme"
    />
    <Detail
      :visibleTrigger_in="detail.visibleTrigger_in"
      :closeTrigger_in="detail.closeTrigger_in"
      :inputData_in="detail.inputData_in"
    />
    <ProjectCfg
      :visibleTrigger_in="projectCfg.visibleTrigger_in"
      :closeTrigger_in="projectCfg.closeTrigger_in"
      :queryId_in="projectCfg.queryId_in"
      :inputData_in="projectCfg.inputData_in"
    ></ProjectCfg>
    <UploadDialog v-bind="uploadDialog" v-on="uploadDialog.event">
      <div slot="search" class="flex-row">
        <customElSelect
          class="flex-auto mbJ1"
          :prefix_in="$T('能源类型')"
          v-model="ElSelect_energyType.value"
          v-bind="ElSelect_energyType"
          v-on="ElSelect_energyType.event"
        >
          <ElOption
            v-for="item in ElOption_energyType.options_in"
            :key="item[ElOption_energyType.key]"
            :label="item[ElOption_energyType.label]"
            :value="item[ElOption_energyType.value]"
            :disabled="item[ElOption_energyType.disabled]"
          ></ElOption>
        </customElSelect>
        <customElSelect
          class="flex-auto mlJ1 mbJ1"
          :prefix_in="$T('节点类型')"
          :popover_in="$T('变压器节点信息橙色字段为变压器分析功能需要的配置')"
          v-model="ElSelect_nodeTypes.value"
          v-bind="ElSelect_nodeTypes"
          v-on="ElSelect_nodeTypes.event"
        >
          <ElOption
            v-for="item in ElOption_nodeTypes.options_in"
            :key="item[ElOption_nodeTypes.key]"
            :label="item[ElOption_nodeTypes.label]"
            :value="item[ElOption_nodeTypes.value]"
            :disabled="item[ElOption_nodeTypes.disabled]"
          ></ElOption>
        </customElSelect>
      </div>
    </UploadDialog>
    <batchChangeNode
      :netWork="netWork"
      :visibleTrigger_in="batchChangeNode.visibleTrigger_in"
      :closeTrigger_in="batchChangeNode.closeTrigger_in"
      :fatherNode_in="fatherNode"
      :selectedData_in="selectedData"
      @updata_out="updataOut"
    />
  </div>
</template>
<script>
import common from "eem-utils/common";
import addProjectConfig1 from "./addProjectConfig/addProjectConfig1.vue";
import ProjectConfigDetail from "./ProjectConfigDetail.vue";
import ProjectCfg from "./ProjectCfg/ProjectCfg";
import batchChangeNode from "./subcomponents/batchChangeNode";
import UploadImg from "eem-components/uploadImg.vue";
import customApi from "@/api/custom.js";
import UploadDialog from "eem-components/uploadDialog";
import { getTreeParams, findNode } from "eem-utils/analysisServiceConfig.js";
import AddAndEditNode from "./addProjectConfig/addAndEditNode.vue";
import Detail from "./detail.vue";
import { httping } from "@omega/http";
export default {
  props: {
    netWork: Boolean,
    isJump: Boolean,
    keepParams: Object
  },
  components: {
    ProjectConfigDetail,
    addProjectConfig1,
    ProjectCfg,
    UploadImg,
    UploadDialog,
    batchChangeNode,
    AddAndEditNode,
    Detail
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    systemCfg() {
      return this.$store.state.systemCfg;
    },
    userInfo() {
      return this.$store.state.userInfo;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    stateProjectInfo() {
      return this.$store.state.projectInfo;
    },
    totalEnergyType() {
      return this.$store.state.systemCfg.totalEnergyType;
    },
    totalEnergyTypeCO2() {
      return this.$store.state.systemCfg.totalEnergyTypeCO2;
    },
    totalEnergyTypeC() {
      return this.$store.state.systemCfg.totalEnergyTypeC;
    }
  },

  data() {
    const language = window.localStorage.getItem("omega_language") === "en";
    return {
      projectInfo: {},
      ajaxFlag: true,
      currentNode: null,
      fatherNode: null,
      fatherCurrentNode: null,
      // 拷贝的节点信息
      copyNode: null,
      showTab: false,
      menus: [$T("车间/楼栋"), $T("用能设备")],
      selectedMenu: $T("车间/楼栋"),
      type_in: null,
      modelLabel: null,
      currentTabItme: {},
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        unCheckTrigger_in: new Date().getTime(),
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out //选中单行输出
        }
      },
      ElInput_1: {
        value: "",
        style: {
          width: "200px"
        },
        placeholder: $T("请输入内容"),
        event: {
          input: this.ElInput_1_input_out
        }
      },
      CetButton_12: {
        visible_in: true,
        disable_in: false,
        title: $T("复制"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_12_statusTrigger_out
        }
      },
      CetButton_13: {
        visible_in: true,
        disable_in: false,
        title: $T("粘贴"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_13_statusTrigger_out
        }
      },
      CetButton_batchNode: {
        visible_in: true,
        disable_in: false,
        title: "导入节点",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_batchNode_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "cloudProjectConfigQueryTable",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "nodes", operator: "EQ", prop: "nodes" },
            { name: "name", operator: "LIKE", prop: "name" },
            { name: "selected", operator: "EQ", prop: "selected" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
        paginationCfg: {
          pageSize: 20
        },
        style: {
          textAlign: "right"
        },
        exportFileName: "",
        tableKey: "tree_id",
        // defaultSort: null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out,
          outputData_out: this.CetTable_1_outputData_out
        }
      },
      ElTableColumnArr: [],
      CetButton_5: {
        visible_in: true,
        disable_in: true,
        title: $T("批量删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_5_statusTrigger_out
        }
      },
      CetButton_6: {
        visible_in: false,
        disable_in: false,
        title: $T("添加同级"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_6_statusTrigger_out
        }
      },
      CetButton_7: {
        visible_in: false,
        disable_in: false,
        title: $T("添加子层级"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_7_statusTrigger_out
        }
      },
      CetButton_8: {
        visible_in: false,
        disable_in: false,
        title: $T("关联设备"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_8_statusTrigger_out
        }
      },
      CetButton_21: {
        visible_in: true,
        disable_in: true,
        title: $T("批量移动"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_21_statusTrigger_out
        }
      },
      addProjectConfig1: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      ProjectConfigDetail: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      selectedData: [],
      treeNameList: [],
      isRefreshTable: false,
      projectCfg: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      ElSelect_1: {
        value: "",
        style: {
          width: language ? "260px" : "200px"
        }
      },
      uploadDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        extensionNameList_in: [".xlsx", ".xls"],
        hideDownload: false,
        dialogTitle: $T("导入节点"),
        event: {
          download: this.uploadDialog_download,
          uploadFile: this.uploadDialog_uploadFile
        }
      },
      //批量转移节点弹框
      batchChangeNode: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      addAndEditNode: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      detail: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      ElSelect_energyType: {
        value: "",
        style: {},
        event: {
          change: this.ElSelect_energyType_change_out
        }
      },
      ElOption_energyType: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_nodeTypes: {
        value: [],
        filterable: true,
        clearable: true,
        multiple: true,
        "collapse-tags": true,
        style: {},
        event: {}
      },
      ElOption_nodeTypes: {
        options_in: [],
        key: "modelLabel",
        value: "modelLabel",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  watch: {
    currentNode: {
      handler: function (val) {
        // 判断当前节点是否是复制节点的父节点
        if (
          this.copyNode &&
          val.modelLabel === this.copyNode.fatherNodeModelLabel
        ) {
          this.CetButton_13.disable_in = false;
        } else if (
          this.copyNode &&
          val.modelLabel === "project" &&
          this.copyNode.fatherNodeModelLabel === "district"
        ) {
          this.CetButton_13.disable_in = false;
        } else {
          this.CetButton_13.disable_in = true;
        }
      },
      deep: true
    },
    copyNode: {
      handler: function (val) {
        // 判断当前节点是否是复制节点的父节点
        if (val && this.currentNode.modelLabel === val.fatherNodeModelLabel) {
          this.CetButton_13.disable_in = false;
        } else if (
          val &&
          this.currentNode.modelLabel === "project" &&
          val.fatherNodeModelLabel === "district"
        ) {
          this.CetButton_13.disable_in = false;
        } else {
          this.CetButton_13.disable_in = true;
        }
      },
      deep: true
    }
  },

  methods: {
    async getProjectEnergy() {
      const res = await customApi.getProjectEnergy(this.projectId);
      if (res.code !== 0) return;
      const data = this._.get(res, "data", []).filter(
        item =>
          [
            this.totalEnergyType,
            this.totalEnergyTypeCO2,
            this.totalEnergyTypeC
          ].indexOf(item.energytype) === -1
      );
      if (data.length) {
        this.ElOption_energyType.options_in = data;
        this.ElSelect_energyType.value =
          this.ElOption_energyType.options_in[0].energytype;
        this.ElSelect_energyType_change_out();
      } else {
        this.ElOption_energyType.options_in = [];
        this.ElSelect_energyType.value = null;
      }
    },
    async ElSelect_energyType_change_out() {
      const templateName = this.getTemplateName();
      const res = await customApi.topologyNodeTypes({
        projectId: this.projectId,
        templateName
      });
      this.ElSelect_nodeTypes.value = [];
      this.ElOption_nodeTypes.options_in = this._.get(res, "data");
    },
    getTemplateName() {
      let templateName = "other-device";
      const energyType = this.ElSelect_energyType.value;
      if (energyType === 2) {
        templateName = "power-device";
      } else if (energyType === 26) {
        templateName = "communication-device";
      } else if (energyType === 16) {
        // 压缩空气
        templateName = "aircompressor-device";
      } else if (energyType === 24) {
        // 冷量
        templateName = "refrigeration-device";
      } else if (energyType === 7 || energyType === 46) {
        // 热水/热量
        templateName = "boiler-device";
      }
      return templateName;
    },
    dropdownClick(val) {
      switch (val) {
        case "copy":
          this.CetButton_12_statusTrigger_out();
          break;
        case "":
          this._statusTrigger_out();
          break;
        case "paste":
          this.CetButton_13_statusTrigger_out();
          break;
        case "batchMove":
          this.CetButton_21_statusTrigger_out();
          break;
        case "association":
          this.CetButton_8_statusTrigger_out();
          break;
        default:
          break;
      }
    },
    //返回查询页
    jumpBack() {
      this.$router.push({
        name: "fixedParametersRank",
        params: {
          keepParams: this.keepParams
        }
      });
    },
    // 打开项目配置
    CetButton_projectCfg_statusTrigger_out() {
      this.projectCfg.visibleTrigger_in = new Date().getTime();
    },
    // 复制节点
    CetButton_12_statusTrigger_out() {
      if (this.currentNode.modelLabel === "project") {
        new Promise(resolve => {
          this.getDistrictNode(this.currentNode, resolve);
        }).then(node => {
          this.copyNode = {
            fatherNodeModelLabel: "district",
            fatherNode: node,
            node: this.currentNode
          };
        });
      } else {
        if (!this.fatherCurrentNode) {
          return;
        }
        this.copyNode = {
          fatherNodeModelLabel: this.fatherCurrentNode.modelLabel,
          node: this.currentNode
        };
      }
    },
    // 粘贴
    CetButton_13_statusTrigger_out() {
      // console.log(this.copyNode);
      var vm = this;

      // 油气田项目中，《油气井》下只能新建一个机采设备
      if (this.oilCheckData(vm.currentNode)) return;

      // 换行会导致国际化失败
      var confirmMsg = $T(
        "是否将【{0}】复制到【{1}】",
        vm.copyNode.node.name,
        vm.currentNode.name
      );
      if (
        vm.copyNode &&
        vm.copyNode.fatherNode &&
        vm.currentNode.modelLabel === "project"
      ) {
        confirmMsg = $T("是否复制【{0}】", vm.copyNode.node.name);
      }
      vm.$confirm(confirmMsg, $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        cancelButtonClass: "btn-custom-cancel",
        type: "info",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: function (action, instance, done) {
          var data = {
            cmodelLabel: vm.copyNode.node.modelLabel,
            cmoldelId: vm.copyNode.node.id,
            pmodelId: vm.currentNode.id,
            pmodelLabel: vm.currentNode.modelLabel,
            system: "cloudsp"
            // version: 2
          };
          if (
            vm.copyNode &&
            vm.copyNode.fatherNode &&
            vm.currentNode.modelLabel === "project"
          ) {
            data = {
              cmodelLabel: vm.copyNode.node.modelLabel,
              cmoldelId: vm.copyNode.node.id,
              pmodelId: vm.copyNode.fatherNode.id || 0,
              pmodelLabel: vm.copyNode.fatherNode.modelLabel || "",
              system: "cloud"
              // version: 2
            };
          }
          if (action === "confirm") {
            httping({
              url: "/eem-service/v1/node/copyNodes",
              method: "POST",
              data
            }).then(function (response) {
              if (response.code === 0) {
                vm.$message.success($T("操作成功"));
                vm.getTreeData();
              }
            });
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action !== "confirm") {
            vm.$message({
              type: "info",
              message: $T("已取消！")
            });
          }
        }
      });
    },
    //点击批量导出节点关系
    async CetButton_batchNode_statusTrigger_out() {
      await this.getProjectEnergy();
      this.uploadDialog.openTrigger_in = new Date().getTime();
    },
    uploadDialog_download() {
      var queryData = {
        templateName: this.getTemplateName(),
        energyType: this.ElSelect_energyType.value
      };
      if (
        this.ElSelect_nodeTypes.value &&
        this.ElSelect_nodeTypes.value.length
      ) {
        queryData.modelLables = this.ElSelect_nodeTypes.value;
      }
      common.downExcel(
        "/eem-service/v1/topology/exportNodeAndConnection?projectId=" +
          this.projectId,
        queryData,
        this.token,
        this.projectId
      );
    },
    uploadDialog_uploadFile(val) {
      const formData = new FormData();
      formData.append("file", val.file);
      var queryData = {
        projectId: this.projectId,
        templateName: this.getTemplateName(),
        energyType: this.ElSelect_energyType.value
      };
      customApi.importNodeAndConnection(formData, queryData).then(response => {
        if (response.code === 0) {
          this.$message({
            type: "success",
            message: $T("导入成功！")
          });
          this.uploadDialog.closeTrigger_in = Date.now();
          this.getTreeData();
        }
      });
    },
    edditProject() {
      this.type_in = 3;
      this.addProjectConfig1.inputData_in = this._.cloneDeep(this.projectInfo);
      this.addProjectConfig1.visibleTrigger_in = new Date().getTime();
    },
    //点击弹出项目详情弹框
    detailProject() {
      this.CetTable_1_detailTrigger_out(this.projectInfo);
    },
    //编辑节点
    CetButton_3_statusTrigger_out(val) {
      this.currentTabItme = val;
      if (!this.currentTabItme.modelLabel) {
        return;
      }
      this.type_in = 3;
      // 找父级节点
      if (this.currentNode.modelLabel === val.modelLabel) {
        this.fatherNode = this._.cloneDeep(this.fatherCurrentNode);
      } else {
        this.fatherNode = this._.cloneDeep(this.currentNode);
      }
      // 找同级别所有name
      let query = {
        modelLabel: this.currentNode.modelLabel
      };
      if (this.currentNode.modelLabel === "room") {
        query.roomType = this.currentNode.roomtype || null;
      }
      const node = findNode(query);
      const leafNode = !node.children;
      if (!leafNode) {
        this.treeNameList = this.currentNode.children.map(i => i.name);
      } else {
        this.treeNameList = this.fatherCurrentNode.children.map(i => i.name);
      }
      this.addAndEditNode.inputData_in = this._.cloneDeep(this.currentTabItme);
      this.addAndEditNode.visibleTrigger_in = new Date().getTime();
    },
    // 删除
    CetButton_5_statusTrigger_out() {
      this.$confirm($T("是否删除此节点?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          new Promise(resolve => {
            this.isExistChildren_out(resolve);
          }).then(data => {
            if (data.data === 0) {
              this.toDeleteNode_out();
            } else {
              this.$message({
                message: $T("所选节点底下包含有子节点，不允许被删除"),
                type: "warning"
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: $T("已取消删除")
          });
        });
    },
    isExistChildren_out(callback) {
      var _this = this;
      var auth = _this.token; //身份验证
      let nodes = this.selectedData || [];
      if (nodes.length === 0) {
        return;
      }
      var data = nodes.map(item => {
        return {
          id: item.id,
          modelLabel: item.modelLabel
        };
      });
      httping({
        url: "/eem-service/v1/node/nodeChild",
        data,
        method: "POST",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          callback(res);
        }
      });
    },
    toDeleteNode_out() {
      var _this = this;
      var dataObj = {};
      _this.selectedData.forEach(item => {
        if (dataObj[item.modelLabel]) {
          dataObj[item.modelLabel].push(item.id);
        } else {
          dataObj[item.modelLabel] = [item.id];
        }
      });
      var promiseArr = [];
      Object.keys(dataObj).forEach(item => {
        promiseArr.push(
          new Promise(resolve => {
            var queryOption = {
              url: "/eem-service/v1/project/manageNode",
              method: "DELETE",
              data: {
                modelLabel: item,
                idRange: dataObj[item]
              }
            };
            httping(queryOption).then(function (response) {
              if (response.code === 0) {
                resolve();
              }
            });
          })
        );
      });
      Promise.all(promiseArr).then(() => {
        if (
          _this.selectedData.length === 1 &&
          _this.currentNode.modelLabel === _this.selectedData[0].modelLabel &&
          _this.currentNode.id === _this.selectedData[0].id
        ) {
          _this.currentNode = null;
          _this.CetGiantTree_1.selectNode = null;
        }
        _this.$message.success($T("删除成功"));
        _this.deleteQuantityObject();
        _this.deleteWorkSections();
        _this.deleteEfficiencyCurve();
        _this.isRefreshTable = true;
        _this.getTreeData();
        _this.CetGiantTree_1.selectNode = _this._.cloneDeep(_this.currentNode);
      });
    },
    // 删除冷水主机时删除工作区间
    deleteWorkSections() {
      let list = this.selectedData || [];
      let params = [];
      list.forEach(item => {
        if (item.modelLabel === "coldwatermainengine") {
          params.push({
            id: item.id,
            modelLabel: item.modelLabel
          });
        }
      });
      if (params.length === 0) {
        return;
      }
      customApi.deleteWorkSections(params).then(() => {});
    },
    // 删除冷水主机时删除运行效率曲线
    deleteEfficiencyCurve() {
      let list = this.selectedData || [];
      let params = [];
      list.forEach(item => {
        if (item.modelLabel === "coldwatermainengine") {
          params.push({
            id: item.id,
            modelLabel: item.modelLabel
          });
        }
      });
      if (!params.length) {
        return;
      }
      customApi.deleteOperatingEfficiencyCurve(params).then(() => {});
    },
    //添加同级节点
    CetButton_6_statusTrigger_out(val) {
      if (!this.currentNode.modelLabel) {
        return;
      }

      // 油气田项目中，《油气井》下只能新建一个机采设备
      if (this.oilCheckData(this.currentNode, "sibling")) return;

      this.type_in = 1;
      this.fatherNode = this._.cloneDeep(this.fatherCurrentNode);
      let treeData = this.fatherNode.children || [],
        treeNameList = [];
      treeData.forEach(item => {
        treeNameList.push(item.name);
      });
      this.treeNameList = treeNameList;
      this.addAndEditNode.inputData_in = null;
      this.addAndEditNode.visibleTrigger_in = new Date().getTime();
    },
    //添加子节点
    CetButton_7_statusTrigger_out(val) {
      if (!this.currentNode.modelLabel) {
        return;
      }
      this.type_in = 1;
      this.fatherNode = this._.cloneDeep(this.currentNode);
      var treeData = this.fatherNode.children || [];

      // 油气田项目中，《油气井》下只能新建一个机采设备
      if (this.oilCheckData(this.currentNode)) return;

      var treeNameList = [];
      treeData.forEach(item => {
        treeNameList.push(item.name);
      });
      this.treeNameList = treeNameList;
      this.addAndEditNode.inputData_in = null;
      this.addAndEditNode.visibleTrigger_in = new Date().getTime();
    },
    /**
     * 油气田项目中，《油气井》下只能新建一个机采设备
     * @currentNode 当前选中节点
     * @type 添加的类型。child：添加子层级；sibling：添加同级
     */
    oilCheckData(currentNode, type = "child") {
      const mes = `一个井只能有一个机采设备，该井目前已有机采设备！`;
      // 机采设备不允许创建同级节点
      if (
        currentNode?.modelLabel === "mechanicalminingmachine" &&
        type === "sibling"
      ) {
        this.$message.warning(mes);
        return true;
      }
      // 油气井下有机采设备时不允许创建子层级
      const hasChildrenFlag =
        currentNode?.modelLabel === "oilwell" &&
        type === "child" &&
        !!currentNode?.children?.find(
          item => item.modelLabel === "mechanicalminingmachine"
        );
      hasChildrenFlag && this.$message.warning(mes);
      return hasChildrenFlag;
    },
    // 关联设备
    CetButton_8_statusTrigger_out() {
      this.$router.push({
        name: "associatRelation",
        params: {
          projectId: this.projectId,
          deviceId: this.currentTabItme.id,
          deviceModelLabel: this.currentTabItme.modelLabel
        }
      });
    },
    ElInput_1_input_out() {
      this.CetTable_1.queryTrigger_in = new Date().getTime();
    },
    CetTable_1_detailTrigger_out(val) {
      if (!val) {
        return;
      }
      this.currentTabItme = val;
      if (this.currentTabItme.modelLabel === "project") {
        this.ProjectConfigDetail.inputData_in = this._.cloneDeep(
          this.currentTabItme
        );
        this.ProjectConfigDetail.visibleTrigger_in = new Date().getTime();
      } else {
        this.detail.inputData_in = this._.cloneDeep(this.currentTabItme);
        this.detail.visibleTrigger_in = new Date().getTime();
      }
    },

    CetTable_1_outputData_out() {
      this.ajaxFlag = true;
    },
    CetTable_1_record_out(val) {
      this.isRefreshTable = false;
      if (!val || !val.modelLabel) {
        return;
      }
      this.currentTabItme = val;
    },
    //选中节点触发方法
    CetGiantTree_1_currentNode_out(val) {
      this.currentNode = val;
      if (val && val.modelLabel === "project") {
        this.fatherCurrentNode = null;
      } else {
        this.fatherCurrentNode = val.getParentNode() || null;
      }

      if (!this.ajaxFlag) {
        return;
      }
      if (this.isRefreshTable && this.selectedMenu === $T("用能设备")) {
        this.getTableData();
        return;
      }
      this.isRefreshTable = false;
      // ????????
      if (val.children && val.children.length > 0) {
        this.modelLabel = val.children[0].modelLabel;
      }
      // 重置筛选条件
      this.CetTable_1.dynamicInput.name = "";

      //重置tab 现在只有保留building和floor有两个tab了
      this.selectedMenu = null;
      if (val.modelLabel === "building") {
        this.showTab = true;
        this.menus = [$T("车间/楼栋"), $T("用能设备")];
        this.selectedMenu = $T("车间/楼栋");
      } else if (val.modelLabel === "floor") {
        this.showTab = true;
        this.menus = [$T("产线/房间"), $T("用能设备")];
        this.selectedMenu = $T("产线/房间");
      } else {
        this.showTab = false;
      }

      if (val.modelLabel === "district") {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属区域"),
            prop: "hierarchy",
            formatter: common.formatTextCol()
          },
          {
            label: $T("项目编号"),
            prop: "code",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属公司"),
            prop: "enterprisename",
            formatter: common.formatTextCol()
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "project") {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "project",
            formatter: common.formatTextCol()
          },
          {
            label: $T("编号"),
            prop: "code",
            formatter: common.formatTextCol()
          },
          {
            label: $T("面积（㎡）"),
            prop: "area",
            formatter: function (val, cell, cellVal) {
              if (val.floorarea && val.floorarea !== 0) {
                return val.floorarea;
              } else if (val.area && val.area !== 0) {
                return val.area;
              } else if (cellVal === 0) {
                return cellVal;
              } else {
                return "--";
              }
            }
          },
          {
            label: $T("人数（人）"),
            prop: "population",
            formatter: common.formatTextCol()
          },
          {
            label: $T("合作截止时间"),
            prop: "cooperativedeadline",
            formatter: common.formatDateCol("YYYY-MM-DD")
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "sectionarea") {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "sectionarea",
            formatter: common.formatTextCol()
          },
          {
            label: $T("编号"),
            prop: "code",
            formatter: common.formatTextCol()
          },
          {
            label: $T("面积（㎡）"),
            prop: "area",
            formatter: function (val, cell, cellVal) {
              if (val.floorarea && val.floorarea !== 0) {
                return val.floorarea;
              } else if (val.area && val.area !== 0) {
                return val.area;
              } else if (cellVal === 0) {
                return cellVal;
              } else {
                return "--";
              }
            }
          },
          {
            label: $T("人数（人）"),
            prop: "population",
            formatter: common.formatTextCol()
          },
          {
            label: $T("合作截止时间"),
            prop: "cooperativedeadline",
            formatter: common.formatDateCol("YYYY-MM-DD")
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "building") {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "building",
            formatter: common.formatTextCol()
          },
          {
            label: $T("地址"),
            prop: "address",
            formatter: common.formatTextCol()
          },
          {
            label: $T("面积（㎡）"),
            prop: "area",
            formatter: common.formatTextCol()
          },
          {
            label: $T("人数（人）"),
            prop: "population",
            formatter: common.formatTextCol()
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "floor") {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "floor",
            formatter: common.formatTextCol()
          },
          {
            label: $T("地址"),
            prop: "address",
            formatter: common.formatTextCol()
          },
          {
            label: $T("面积（㎡）"),
            prop: "area",
            formatter: common.formatTextCol()
          },
          {
            label: $T("人数（人）"),
            prop: "population",
            formatter: common.formatTextCol()
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "room") {
        if ([1, 2, 3, 4, 5].includes(val.roomtype)) {
          // 配电设备
          this.ElTableColumnArr = [
            {
              label: $T("节点名称"),
              prop: "name",
              formatter: common.formatTextCol()
            },
            {
              label: $T("编号"),
              prop: "code",
              formatter: common.formatTextCol()
            },
            {
              label: $T("型号"),
              prop: "model",
              formatter: common.formatTextCol()
            },
            {
              label: $T("投运时间"),
              prop: "commissiondate",
              formatter: common.formatDateCol("YYYY-MM-DD")
            }
          ];
        } else if (val.roomtype === 6) {
          this.ElTableColumnArr = [
            {
              label: $T("节点名称"),
              prop: "name",
              formatter: common.formatTextCol()
            },
            {
              label: $T("管道类型"),
              prop: "pipetype$text",
              formatter: common.formatTextCol()
            },
            {
              label: $T("物料类型"),
              prop: "energytype$text",
              formatter: common.formatTextCol()
            }
          ];
        } else {
          // 用能设备
          this.ElTableColumnArr = [
            {
              label: $T("节点名称"),
              prop: "name",
              formatter: common.formatTextCol()
            },
            {
              label: $T("所属层级"),
              prop: "room",
              formatter: common.formatTextCol()
            },
            {
              label: $T("安装位置"),
              prop: "location",
              formatter: common.formatTextCol()
            },
            {
              label: $T("厂家"),
              prop: "manufactor",
              formatter: common.formatTextCol()
            },
            {
              label: $T("型号"),
              prop: "model",
              formatter: common.formatTextCol()
            }
          ];
        }
        this.getTableData();
      } else if (["powerdiscabinet", "arraycabinet"].includes(val.modelLabel)) {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("编号"),
            prop: "code",
            formatter: common.formatTextCol()
          },
          {
            label: $T("品牌"),
            prop: "brand",
            formatter: common.formatTextCol()
          },
          {
            label: $T("型号"),
            prop: "model",
            formatter: common.formatTextCol()
          },
          {
            label: $T("投运时间"),
            prop: "commissiondate",
            formatter: common.formatDateCol("YYYY-MM-DD")
          }
        ];
        this.getTableData();
      } else if (
        [
          "oilcompany",
          "oilproductionplant",
          "operationarea",
          "oilproductioncrew",
          "gasmine"
        ].includes(val.modelLabel)
      ) {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "floor",
            formatter: val =>
              val.oilcompany ||
              val.oilproductionplant ||
              val.operationarea ||
              val.oilproductioncrew ||
              val.gasmine ||
              val.gasoperationarea ||
              "--"
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "platform") {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "platform",
            formatter: common.formatTextCol()
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "gasoperationarea") {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "gasoperationarea",
            formatter: common.formatTextCol()
          },
          {
            label: $T("地理位置"),
            prop: "geographicalposition",
            formatter: common.formatTextCol()
          },
          {
            label: $T("投运日期"),
            prop: "usagedate",
            formatter: val => {
              const time = val.usagedate || val.operationdate;
              return time ? this.$moment(time).format("YYYY-MM-DD") : "--";
            }
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "purificationcompany") {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "purificationcompany",
            formatter: common.formatTextCol()
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "waterinjectionstation") {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "waterinjectionstation",
            formatter: common.formatTextCol()
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "gasgatheringstation") {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "gasgatheringstation",
            formatter: common.formatTextCol()
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "oiltransferstation") {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "oiltransferstation",
            formatter: common.formatTextCol()
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "dehydratingstation") {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "dehydratingstation",
            formatter: common.formatTextCol()
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "purificationplant") {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "purificationplant",
            formatter: common.formatTextCol()
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "combinedstation") {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "combinedstation",
            formatter: common.formatTextCol()
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "gasplatform") {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "gasplatform",
            formatter: common.formatTextCol()
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "oilwell") {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "oilwell",
            formatter: common.formatTextCol()
          },
          {
            label: $T("电机型号"),
            prop: "motortype",
            formatter: common.formatTextCol()
          },
          {
            label: $T("投运日期"),
            prop: "operationdate",
            formatter: common.formatDateCol("YYYY-MM-DD")
          },
          {
            label: $T("使用状态"),
            prop: "usagestate$text",
            formatter: val => val.usagestate$text || "--"
          }
        ];
        this.getTableData();
      } else if (val.modelLabel === "waterinjectionplatform") {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "waterinjectionplatform",
            formatter: common.formatTextCol()
          }
        ];
        this.getTableData();
      } else {
        if (val.modelLabel === "manuequipment") {
          // 用能设备
          this.ElTableColumnArr = [
            {
              label: $T("节点名称"),
              prop: "name",
              formatter: common.formatTextCol()
            },
            {
              label: $T("所属层级"),
              prop: "room",
              formatter: common.formatTextCol()
            },
            {
              label: $T("安装位置"),
              prop: "location",
              formatter: common.formatTextCol()
            },
            {
              label: $T("厂家"),
              prop: "manufactor",
              formatter: common.formatTextCol()
            },
            {
              label: $T("型号"),
              prop: "model",
              formatter: common.formatTextCol()
            }
          ];
        } else if (
          ["meteorologicalmonitor", "airconditioner"].includes(val.modelLabel)
        ) {
          this.ElTableColumnArr = [
            {
              label: $T("节点名称"),
              prop: "name",
              formatter: common.formatTextCol()
            }
          ];
        } else if (val.modelLabel === "civicpipe") {
          this.ElTableColumnArr = [
            {
              label: $T("节点名称"),
              prop: "name",
              formatter: common.formatTextCol()
            },
            {
              label: $T("所属层级"),
              prop: "project",
              formatter: common.formatTextCol()
            }
          ];
        } else if (val.modelLabel === "pipeline") {
          this.ElTableColumnArr = [
            {
              label: $T("节点名称"),
              prop: "name",
              formatter: common.formatTextCol()
            },
            {
              label: $T("管道类型"),
              prop: "pipetype$text",
              formatter: common.formatTextCol()
            },
            {
              label: $T("物料类型"),
              prop: "energytype$text",
              formatter: common.formatTextCol()
            }
          ];
        } else if (val.modelLabel === "mechanicalminingmachine") {
          this.ElTableColumnArr = [
            {
              label: $T("节点名称"),
              prop: "name",
              formatter: common.formatTextCol()
            },
            {
              label: $T("电机型号"),
              prop: "motortype",
              formatter: common.formatTextCol()
            },
            {
              label: $T("投运日期"),
              prop: "operationdate",
              formatter: common.formatDateCol("YYYY-MM-DD")
            },
            {
              label: $T("使用状态"),
              prop: "usagestate$text",
              formatter: val => val.usagestate$text || "--"
            }
          ];
        } else if (val.modelLabel === "heatingfurnace") {
          this.ElTableColumnArr = [
            {
              label: $T("节点名称"),
              prop: "name",
              formatter: common.formatTextCol()
            },
            {
              label: $T("投运日期"),
              prop: "operationdate",
              formatter: common.formatDateCol("YYYY-MM-DD")
            },
            {
              label: $T("功能类型"),
              prop: "functiontype$text",
              formatter: val => val.functiontype$text || "--"
            },
            {
              label: $T("使用状态"),
              prop: "usagestate$text",
              formatter: val => val.usagestate$text || "--"
            }
          ];
        } else if (val.modelLabel === "gascompressor") {
          this.ElTableColumnArr = [
            {
              label: $T("节点名称"),
              prop: "name",
              formatter: common.formatTextCol()
            },
            {
              label: $T("投运日期"),
              prop: "operationdate",
              formatter: common.formatDateCol("YYYY-MM-DD")
            },
            {
              label: $T("功能类型"),
              prop: "functiontype$text",
              formatter: val => val.functiontype$text || "--"
            },
            {
              label: $T("使用状态"),
              prop: "usagestate$text",
              formatter: val => val.usagestate$text || "--"
            }
          ];
        } else if (val.modelLabel === "oilcommonequipment") {
          this.ElTableColumnArr = [
            {
              label: $T("节点名称"),
              prop: "name",
              formatter: common.formatTextCol()
            },
            {
              label: $T("投运日期"),
              prop: "operationdate",
              formatter: common.formatDateCol("YYYY-MM-DD")
            },
            {
              label: $T("使用状态"),
              prop: "usagestate$text",
              formatter: val => val.usagestate$text || "--"
            }
          ];
        } else if (val.modelLabel === "waterinjectionwell") {
          this.ElTableColumnArr = [
            {
              label: $T("节点名称"),
              prop: "name",
              formatter: common.formatTextCol()
            },
            {
              label: $T("水井编号"),
              prop: "number",
              formatter: common.formatTextCol()
            }
          ];
        } else {
          // 配电设备
          this.ElTableColumnArr = [
            {
              label: $T("节点名称"),
              prop: "name",
              formatter: common.formatTextCol()
            },
            {
              label: $T("编号"),
              prop: "code",
              formatter: common.formatTextCol()
            },
            {
              label: $T("型号"),
              prop: "model",
              formatter: common.formatTextCol()
            },
            {
              label: $T("投运时间"),
              prop: "commissiondate",
              formatter: common.formatDateCol("YYYY-MM-DD")
            },
            {
              label: $T("厂家"),
              prop: "manufactor",
              formatter: common.formatTextCol()
            }
          ];
        }
        var obj = {
          id: val.id,
          modelLabel: val.modelLabel
        };
        this.CetTable_1.dynamicInput.selected = {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          name: this.currentNode.name
        };
        this.CetTable_1.dynamicInput.nodes = [obj];
        this.CetTable_1.queryTrigger_in = new Date().getTime();
      }

      const isSelectProject = val.modelLabel === "project";
      // 关联设备按钮
      if (
        ["project", "sectionarea", "building", "floor", "district"].includes(
          val.modelLabel
        )
      ) {
        this.CetButton_8.visible_in = false;
      } else {
        this.CetButton_8.visible_in = true;
      }
      // 复制按钮
      this.CetButton_12.disable_in = isSelectProject;

      // 判断添加同级或子级
      let query = {
        modelLabel: val.modelLabel
      };
      if (val.modelLabel === "room") {
        query.roomType = val.roomtype || null;
      }
      const node = findNode(query);
      this.CetButton_6.visible_in = !isSelectProject;
      this.CetButton_7.visible_in = node.children ? true : false;
    },
    addProjectConfig1_saveData_out(val) {
      if (this.type_in === 3) {
        this.getTreeData(true);
        // this.CetTable_1.queryTrigger_in = new Date().getTime();
      } else {
        // 新增需要关联项目和租户
        this.relateTenantAndProject(val);
        this.addAuthUser_out(val);
        // this.getTreeData();
        this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
      }
    },
    relateTenantAndProject(data) {
      httping({
        url: `/eem-service/v1/auth/cloud/relateTenantAndProject?projectId=${data.id}&tenantId=${this.userInfo.tenantId}`,
        method: "PUT"
      });
    },
    addAndEditNode_saveData_out(val) {
      if (this.showTab) {
        this.isRefreshTable = true;
      }
      if (
        this._.isEmpty(this.addAndEditNode.inputData_in) ||
        !this._.get(this.addAndEditNode.inputData_in, "id")
      ) {
        this.addAuthUser_out(val);
      } else {
        this.getTreeData();
        this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
      }
    },
    getTreeData(initProject) {
      var _this = this;
      var auth = _this.token; //身份验证
      let params;
      if (this.netWork) {
        params = {
          rootID: this.projectId,
          rootLabel: "project",
          subLayerConditions: getTreeParams(2),
          treeReturnEnable: true
        };
      } else {
        params = {
          rootID: this.projectId,
          rootLabel: "project",
          subLayerConditions: getTreeParams(1),
          treeReturnEnable: true
        };
      }
      httping({
        url: "/eem-service/v1/node/nodeTree/simple",
        method: "POST",
        data: params
      }).then(res => {
        if (res.code === 0) {
          const resData = res.data || [];

          _this.CetGiantTree_1.inputData_in = resData;
          if (!_this.currentNode) {
            _this.CetGiantTree_1.selectNode = resData[0];
          } else {
            _this.CetGiantTree_1.selectNode = _this._.cloneDeep(
              _this.currentNode
            );
            setTimeout(() => {
              let node = _this.$refs.cetGiantTree.ztreeObj.getNodeByParam(
                "tree_id",
                _this.currentNode.tree_id
              );
              _this.CetGiantTree_1_currentNode_out(node);
            }, 0);
          }
          if (initProject) {
            _this.getProject();
          }
        }
      });
    },
    getTableData() {
      this.$nextTick(() => {
        var nodes = this.getChildrenNodes();
        this.CetTable_1.dynamicInput.selected = {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          name: this.currentNode.name
        };
        this.CetTable_1.dynamicInput.nodes = nodes;

        if (this.ajaxFlag) {
          this.ajaxFlag = false;
          this.CetTable_1.queryTrigger_in = new Date().getTime();
        }
      });
    },
    // 获取节点字列表
    getChildrenNodes() {
      let currentNode = this.currentNode;
      // 判断是否是叶子节点
      let query = {
        modelLabel: currentNode.modelLabel
      };
      if (currentNode.modelLabel === "room") {
        query.roomType = currentNode.roomtype || null;
      }
      const node = findNode(query);
      const leafNode = !node.children;
      if (leafNode) {
        return [
          {
            modelLabel: currentNode.modelLabel,
            id: currentNode.id,
            hasChildren: false
          }
        ];
      } else {
        const childrenNodes = currentNode.children || [];
        let filterList = ["floor", "room"];
        let nodes = [];
        childrenNodes.forEach(item => {
          let node = {
            modelLabel: item.modelLabel,
            id: item.id,
            hasChildren: item.children && item.children.length ? true : false
          };
          if (!this.showTab) {
            nodes.push(node);
          } else {
            if (
              this.selectedMenu === $T("用能设备") &&
              !filterList.includes(item.modelLabel)
            ) {
              nodes.push(node);
            } else if (
              this.selectedMenu !== $T("用能设备") &&
              filterList.includes(item.modelLabel)
            ) {
              nodes.push(node);
            }
          }
        });
        return nodes;
      }
    },
    //表格点击多选框
    handleSelectionChange(val) {
      this.selectedData = val;
      if (val && val.length > 0) {
        this.CetButton_5.disable_in = false;
        this.CetButton_21.disable_in = false;
      } else {
        this.CetButton_5.disable_in = true;
        this.CetButton_21.disable_in = true;
      }
    },
    // 删除物理量
    deleteQuantityObject() {
      var _this = this;
      var list = _this.selectedData || [];
      var params = [];
      for (var i = 0, len = list.length; i < len; i++) {
        if (
          list[i].modelLabel === "project" ||
          list[i].modelLabel === "building" ||
          list[i].modelLabel === "sectionarea" ||
          list[i].modelLabel === "floor" ||
          list[i].modelLabel === "room"
        ) {
          continue;
        }
        params.push({
          modelLabel: list[i].modelLabel,
          nodes: [
            {
              id: list[i].id
            }
          ]
        });
      }
      if (params.length === 0) {
        return;
      }
      httping({
        url: "/eem-service/v1/quantity/quantityObject",
        method: "DELETE",
        data: params
      }).then(() => {});
    },
    //切换tab
    handleTabClick_out(tab) {
      this.selectedMenu = tab;
      if (tab === $T("车间/楼栋")) {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "building",
            formatter: common.formatTextCol()
          },
          {
            label: $T("地址"),
            prop: "address",
            formatter: common.formatTextCol()
          },
          {
            label: $T("面积（㎡）"),
            prop: "area",
            formatter: common.formatTextCol()
          },
          {
            label: $T("人数（人）"),
            prop: "population",
            formatter: common.formatTextCol()
          }
        ];
        this.getTableData();
      } else if (tab === $T("产线/房间")) {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("所属层级"),
            prop: "floor",
            formatter: common.formatTextCol()
          },
          {
            label: $T("地址"),
            prop: "address",
            formatter: common.formatTextCol()
          },
          {
            label: $T("面积（㎡）"),
            prop: "area",
            formatter: common.formatTextCol()
          },
          {
            label: $T("人数（人）"),
            prop: "population",
            formatter: common.formatTextCol()
          }
        ];
        this.getTableData();
      } else if (tab === $T("用能设备")) {
        this.ElTableColumnArr = [
          {
            label: $T("节点名称"),
            prop: "name",
            formatter: common.formatTextCol()
          },
          {
            label: $T("编号"),
            prop: "code",
            formatter: common.formatTextCol()
          },
          {
            label: $T("厂家"),
            prop: "manufactor",
            formatter: common.formatTextCol()
          },
          {
            label: $T("型号"),
            prop: "model",
            formatter: common.formatTextCol()
          },
          {
            label: $T("投运时间"),
            prop: "commissiondate",
            formatter: common.formatDateCol("YYYY-MM-DD")
          }
        ];
        this.getTableData();
      }
    },
    // 添加权限
    addAuthUser_out(data) {
      // 获取权限
      var _this = this;
      // console.log(this.userInfo);
      if (this.userInfo && this.userInfo.name === "ROOT") {
        _this.getTreeData();
        return;
      }
      var params = [
        {
          id: data.id,
          modelLabel: data.modelLabel
        }
      ];
      httping({
        url: `/eem-service/v1/node/modelNode`,
        method: "PUT",
        data: params
      }).then(response => {
        if (response.code === 0) {
          _this.getTreeData();
        }
      });
    },
    getDistrictNode(node, callback) {
      var _this = this;
      var auth = _this.token; //身份验证
      var params = {
        rootID: node.id,
        rootLabel: "project",
        subLayerConditions: [
          {
            modelLabel: "district"
          }
        ]
      };
      httping({
        url: "/eem-service/v1/common/query/nodes", //反查
        data: params,
        method: "POST",
        timeout: 10000
      }).then(res => {
        if (res.code === 0) {
          var districtNode = _this._.get(res, ["data", "0", "children"], []);
          if (districtNode && districtNode.length > 0) {
            callback(districtNode[0]);
            // callback && callback(districtNode[0]);
          } else {
            const backMsg = {
              id: 0,
              modelLabel: "string"
            };
            callback(backMsg);
          }
        }
      });
    },
    //获取项目信息
    getProject() {
      var _this = this;
      var treeData = _this.CetGiantTree_1.inputData_in || [];
      var nodes = [];
      var selected = {};
      treeData.forEach(item => {
        if (item.modelLabel === "project") {
          var hasChildren = false;
          if (item.children && item.children.length > 0) {
            hasChildren = true;
          }
          var obj = {
            modelLabel: item.modelLabel,
            id: item.id,
            hasChildren: hasChildren
          };
          nodes.push(obj);
          selected = obj;
        }
      });
      let queryData = {
        rootCondition: {
          page: {
            index: 0,
            limit: 20
          },
          filter: {
            expressions: [
              { prop: "nodes", operator: "EQ", limit: nodes },
              { prop: "selected", operator: "EQ", limit: selected }
            ]
          }
        }
      };
      customApi.cloudProjectConfigQueryTable(queryData).then(res => {
        _this.projectInfo = _this._.get(res, "data[0]");
      });
    },
    // 批量转移节点
    CetButton_21_statusTrigger_out() {
      let nodes = this.selectedData || [];
      if (nodes.length === 0) {
        return;
      }
      var arr = [
        "project",
        "sectionarea",
        "building",
        "floor",
        "room",
        "arraycabinet",
        "powerdiscabinet"
      ];
      if (arr.indexOf(this.currentNode.modelLabel) === -1) {
        // 属于叶子节点
        this.fatherNode = this._.cloneDeep(this.fatherCurrentNode);
      } else {
        this.fatherNode = this._.cloneDeep(this.currentNode);
      }
      this.batchChangeNode.visibleTrigger_in = new Date().getTime();
    },
    // 批量转移节点之后，刷新页面
    updataOut() {
      this.getTreeData();
      this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
    }
  },
  created: function () {},
  mounted: function () {},
  activated: function () {
    this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
    this.currentNode = null;
    this.copyNode = null;
    this.getTreeData(true);
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .nodeName {
    max-width: 420px;
    @include line_height(Hm);
  }
}

.row-handel {
  cursor: pointer;
  @include font_color(ZS);
}
.fcB1 {
  @include font_color(B1);
}
.projectInfo {
  .loadImg {
    width: 100px;
    height: 100px;
  }
  .projectInfoList {
    overflow: auto;
    position: relative;
    .projectInfoItem {
      width: 160px;
      @include padding(J3);
      @include margin_right(J3);
      @include background_color(BG);
      @include border_radius(C);
      .label {
        @include font_color(T3);
      }
      .value {
        max-width: 100%;
        @include font_weight(MD);
      }
    }
  }
  .rightBtn {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    .btn {
      cursor: pointer;
      @include font_color(ZS);
    }
  }
}
.backPlatform {
  width: 50px;
  margin: -12px 0 8px 0;
  cursor: pointer;
  @include font_color(ZS);
}
</style>
