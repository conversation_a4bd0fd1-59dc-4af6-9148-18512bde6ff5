<template>
  <div class="page connectedRelation eem-common">
    <el-container class="connectedRelation_box">
      <el-aside width="315px" class="eem-aside flex-column">
        <customElSelect
          :prefix_in="$T('能源类型')"
          class="mbJ1 fullwidth"
          v-model="ElSelect_energyType.value"
          v-bind="ElSelect_energyType"
          v-on="ElSelect_energyType.event"
        >
          <ElOption
            v-for="item in ElOption_energyType.options_in"
            :key="item[ElOption_energyType.key]"
            :label="item[ElOption_energyType.label]"
            :value="item[ElOption_energyType.value]"
            :disabled="item[ElOption_energyType.disabled]"
          ></ElOption>
        </customElSelect>
        <div class="flex-auto">
          <CetTree
            :selectNode.sync="CetTree_1.selectNode"
            :checkedNodes.sync="CetTree_1.checkedNodes"
            :searchText_in.sync="CetTree_1.searchText_in"
            v-bind="CetTree_1"
            v-on="CetTree_1.event"
          ></CetTree>
        </div>
      </el-aside>
      <el-container
        v-show="_.get(outerNode, 'modelLabel') !== 'room'"
        class="lefttree_sbox"
      >
        <div class="p30 text-center fs20 info w100">
          {{ $T("请选择房间节点") }}
        </div>
      </el-container>
      <el-container
        v-show="_.get(outerNode, 'modelLabel') === 'room'"
        class="nodeListbox mlJ3"
      >
        <el-container class="nodeListbox_asidebox">
          <el-aside
            width="315px"
            class="snodeListbox eem-aside brC flex-column"
          >
            <el-tooltip
              effect="light"
              :content="_.get(outerNode, 'name')"
              placement="bottom"
            >
              <div class="text-ellipsis mbJ1 common-title-H2">
                {{ _.get(outerNode, "name") }}
              </div>
            </el-tooltip>
            <div style="flex: 1" class="minWH">
              <CetTree
                :selectNode.sync="CetTree_device.selectNode"
                :checkedNodes.sync="CetTree_device.checkedNodes"
                v-bind="CetTree_device"
                v-on="CetTree_device.event"
              ></CetTree>
            </div>
          </el-aside>
          <el-main class="threeMainbox mlJ3 flex-column">
            <el-header
              height="auto"
              v-show="!_.isEmpty(insideNode)"
              class="threeMainbox_header clearfix mbJ3 eem-cont"
            >
              <div class="fr threeMainbox_headerbox">
                <div class="headerbox_select clearfix">
                  <el-radio-group
                    v-model="relatedPosition"
                    @change="handTabsClick_out"
                    size="small"
                    class="fl mrJ1"
                  >
                    <el-radio-button label="upper">
                      {{ $T("上端") }}
                    </el-radio-button>
                    <el-radio-button label="lower">
                      {{ $T("下端") }}
                    </el-radio-button>
                  </el-radio-group>
                  <customElSelect
                    :prefix_in="$T('设备类型')"
                    class="fl"
                    v-model="ElSelect_electrical.value"
                    v-bind="ElSelect_electrical"
                    v-on="ElSelect_electrical.event"
                    v-show="ElSelect_energyType.value === 2"
                  >
                    <ElOption
                      v-for="item in ElOption_electrical.options_in"
                      :key="item[ElOption_electrical.key]"
                      :label="item[ElOption_electrical.label]"
                      :value="item[ElOption_electrical.value]"
                      :disabled="item[ElOption_electrical.disabled]"
                    ></ElOption>
                  </customElSelect>
                  <CetButton
                    class="fr"
                    :visible_in="CetButton_1.visible_in"
                    :disable_in="CetButton_1.disable_in"
                    v-bind="CetButton_1.config"
                    @statusTrigger_out="CetButton_1_statusTrigger_out"
                  />
                </div>
              </div>
            </el-header>
            <div
              v-show="_.isEmpty(insideNode)"
              class="p30 text-center fs20 info"
            >
              {{ $T("请选择设备节点") }}
            </div>
            <div
              v-show="!_.isEmpty(insideNode)"
              class="allListbox flex-row flex-auto"
            >
              <div
                class="allListbox_main bg1 plJ3 prJ3 pbJ4 ptJ4 brC flex-auto flex-column"
              >
                <div class="allListbox_main_box mbJ1">
                  {{ $T("共") }}
                  <span class="totalNum fcZS">{{ totalNum }}</span>
                  {{ $T("条") }}
                </div>
                <div class="min_box_content flex-column flex-auto">
                  <ElInput
                    suffix-icon="el-icon-search"
                    class="searchbox fullWidth"
                    v-model="ElInput_filterWait.value"
                    v-bind="ElInput_filterWait"
                    v-on="ElInput_filterWait.event"
                  ></ElInput>
                  <el-checkbox-group
                    v-model="checkWaitModel"
                    class="block-label min_box_contentList flex-auto mtJ1"
                    @change="CetCheckboxGroup_waitList_resulttext_out"
                  >
                    <el-checkbox
                      v-for="item in ElInput_filterWait_input_out(
                        ElInput_filterWait.value
                      )"
                      :key="`wait_${item.tree_id}`"
                      :label="item.tree_id"
                    >
                      <el-tooltip
                        :content="item.name"
                        effect="light"
                        placement="top"
                      >
                        <span>{{ item.name }}</span>
                      </el-tooltip>
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
              <div
                class="checkedListbox_main bg1 plJ3 prJ3 pbJ4 ptJ4 brC flex-auto mlJ3 flex-column"
              >
                <div class="checkedListbox_main_header mbJ1">
                  {{ $T("已选") }}
                  <span class="hasNum fcZS">{{ hasNum }}</span>
                  {{ $T("条") }}
                </div>
                <div class="checkedListbox_main_content flex-auto flex-column">
                  <ElInput
                    suffix-icon="el-icon-search"
                    class="searchbar fullWidth"
                    v-model="ElInput_filterAlready.value"
                    v-bind="ElInput_filterAlready"
                    v-on="ElInput_filterAlready.event"
                  ></ElInput>
                  <ul class="already-check-list block-label flex-auto mtJ1">
                    <li
                      v-for="item in filterAlreadyCheckList(
                        ElInput_filterAlready.value
                      )"
                      :key="`already_${item.tree_id}`"
                      class="clearfix plJ prJ"
                    >
                      <el-tooltip
                        :content="item.name"
                        effect="light"
                        placement="top"
                      >
                        <span class="block-label-span text-ellipsis">
                          {{ item.name }}
                        </span>
                      </el-tooltip>
                      <i
                        @click="removeAlreadyItem(item)"
                        class="fr el-icon-delete"
                      ></i>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </el-main>
        </el-container>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import ELECTRICAL_DEVICE from "@/store/electricaldevice.js";
import commonApi from "@/api/custom.js";
import { httping } from "@omega/http";

export default {
  name: "ConnectedRelation",
  components: {},

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    },
    electricalDevice() {
      // 设备类型
      return this.deviceclass;
    },
    projectId() {
      var vm = this;
      return vm.$store.state.projectId;
    }
  },

  data() {
    return {
      totalNum: 0, // 总共的项
      hasNum: 0, //已勾选的项
      deviceclass: [],
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        defaultExpandAll: true,
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        config: {
          title: $T("保存"),
          type: "primary",
          plain: false
        }
      },
      ElInput_filterWait: {
        value: "",
        style: {},
        placeholder: $T("输入关键字以检索"),
        event: {
          change: this.ElInput_filterWait_change_out,
          input: this.ElInput_filterWait_input_out
        }
      },
      ElInput_filterAlready: {
        value: "",
        style: {},
        placeholder: $T("输入关键字以检索"),
        event: {}
      },
      // device树组件
      CetTree_device: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        event: {
          currentNode_out: this.CetTree_device_currentNode_out
        }
      },
      ElSelect_electrical: {
        value: "",
        style: {
          width: "300px"
        },
        event: {
          change: this.ElSelect_electrical_change_out
        }
      },
      ElOption_electrical: {
        options_in: [],
        key: "propertyLabel",
        value: "propertyLabel",
        label: "text",
        disabled: "disabled"
      },
      // 节点树节点（园区至房间）
      outerNode: null,
      // 设备节点
      insideNode: null,
      relatedPosition: "upper",
      // checkGroup绑定的Model值
      checkWaitModel: [],
      // 待勾选列表
      waitCheckList: [],
      // 所有待勾选 映射
      waitListMap: {},
      // 已有连接关系列表
      alreadyCheckList: [],
      // 连接关系映射表
      connectedList: [],
      // 链接关系数据
      connectedData: [],
      // 正在获取连接关系数据
      getConnectDataisPending: false,
      ElSelect_energyType: {
        value: "",
        event: {
          change: this.getLeftTree
        }
      },
      ElOption_energyType: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  watch: {
    outerNode: {
      deep: true,
      handler(val, oldVal) {
        if (this._.get(val, "modelLabel") !== "room") {
          return;
        }
        this.CetTree_device.inputData_in = [];
        this.getDeviceNode(val);
      }
    }
  },

  methods: {
    CetTree_1_currentNode_out(val) {
      this.ElInput_filterWait.value = "";
      this.ElInput_filterAlready.value = "";
      this.outerNode = val;
      this.CetTree_device.selectNode = {};
      this.insideNode = null;
    },
    async CetButton_1_statusTrigger_out(val) {
      if (this._.isEmpty(this.outerNode)) return;
      this.CetButton_1.disable_in = true;
      await this.addConnect();

      this.CetButton_1.disable_in = false;
      this.getConnectData();
    },
    ElInput_filterWait_change_out(val) {},
    ElInput_filterWait_input_out(val) {
      if (!val) return this.waitCheckList;
      else {
        let res = this.waitCheckList.filter(item =>
          this._.includes(item.name.toLowerCase(), val.toLowerCase())
        );
        return res;
      }
    },
    filterAlreadyCheckList(val) {
      if (!val) return this.alreadyCheckList;
      else
        return this.alreadyCheckList.filter(item =>
          this._.includes(item.name.toLowerCase(), val.toLowerCase())
        );
    },
    CetTree_device_currentNode_out(val) {
      if (!val) return;
      this.insideNode = {
        modelLabel: val.modelLabel,
        id: val.id
      };
      // 获取待勾选数据类型
      this.getSelectOptions();
      // 获取待勾选列表
      if (this.ElSelect_energyType.value === 2) {
        this.getWaitCheckList(this.ElSelect_electrical.value);
      } else {
        this.getWaitCheckList("pipeline");
      }
      // 获取连接关系数据
      this.getConnectData();
    },
    CetCheckboxGroup_waitList_resulttext_out(val) {
      let currentElectrical;
      if (this.ElSelect_energyType.value === 2) {
        currentElectrical = [this.ElSelect_electrical.value];
      } else if (this.ElSelect_energyType.value === 16) {
        // 压缩空气
        currentElectrical = [
          "pipeline",
          "aircompressor",
          "colddryingmachine",
          "dryingmachine"
        ];
      } else if (this.ElSelect_energyType.value === 24) {
        // 冷量
        currentElectrical = [
          "pipeline",
          "coldwatermainengine",
          "windset",
          "coolingtower"
        ];
      } else if ([7, 46].includes(this.ElSelect_energyType.value)) {
        // 热水、热量
        currentElectrical = ["pipeline", "boiler"];
      } else if ([26].includes(this.ElSelect_energyType.value)) {
        //网络信号
        currentElectrical = [
          "computer",
          "gateway",
          "meter",
          "interchanger",
          "photoeleconverter"
        ];
      } else {
        // 其他类型
        currentElectrical = ["pipeline"];
      }
      // 浅拷贝 避免重复刷新dom 节约性能
      let alreadyCheckList = [...this.alreadyCheckList];
      // 先把所有当前设备类型过滤掉
      alreadyCheckList = alreadyCheckList.filter(
        item => !currentElectrical.includes(item.modelLabel)
      );
      this.connectedList = this.connectedList.filter(
        item => !currentElectrical.includes(item.split("_")[0])
      );
      val.forEach(item => {
        // 把当前勾选的结果放入已勾选列表
        this.connectedList.push(item);
        // 保存 所有已勾选
        if (this.waitListMap[item])
          alreadyCheckList.push(this.waitListMap[item]);
      });
      this.alreadyCheckList = _.sortBy(alreadyCheckList, ["modelLabel", "id"]);
      this.hasNum = this.alreadyCheckList.length;
    },
    handTabsClick_out(val) {
      var me = this;
      // 获取连接关系数据
      this.getConnectData();
    },
    ElSelect_electrical_change_out(val) {
      if (!val) return;
      this.getWaitCheckList(val);
    },
    // 获取待勾选节点列表queryData的参数
    getQueryDeviceNodeParams() {},
    // 获取设备列表下拉参数
    // 根据选择的节点类型，过滤掉对应的下拉选项
    // 目前规则是  如果当前设备是变压器或者发电机，则去除变压器或者发电机
    getSelectOptions() {
      const node = this.insideNode;
      if (!node) return;
      const noRepeat = ["powertransformer", "generator"];
      if (noRepeat.includes(node.modelLabel)) {
        const filterNode = this.electricalDevice.filter(
          item => item.propertyLabel !== node.modelLabel
        );
        this.ElOption_electrical.options_in = filterNode;
        this.ElSelect_electrical.value = filterNode[0].propertyLabel;
      } else {
        this.ElOption_electrical.options_in = this.electricalDevice;
        if (this._.isEmpty(this.ElSelect_electrical.value)) {
          this.ElSelect_electrical.value =
            this.electricalDevice[0].propertyLabel;
        }
      }
    },
    // 获取待勾选列表
    getWaitCheckList(modelLabel) {
      var subLayerConditions = [{ filter: null, modelLabel }];
      if (modelLabel === "pipeline") {
        subLayerConditions[0].filter = {
          expressions: [
            {
              limit: this.ElSelect_energyType.value,
              prop: "energytype",
              operator: "EQ"
            }
          ]
        };
        // 管道关联设备和管道，设备只能关联管道
        if (this._.get(this.outerNode, "roomtype") === 6) {
          if (this.ElSelect_energyType.value === 16) {
            // 压缩空气
            subLayerConditions = [
              ...subLayerConditions,
              { modelLabel: "aircompressor" },
              { modelLabel: "colddryingmachine" },
              { modelLabel: "dryingmachine" }
            ];
          } else if (this.ElSelect_energyType.value === 24) {
            // 冷量
            subLayerConditions = [
              ...subLayerConditions,
              { modelLabel: "coldwatermainengine" },
              { modelLabel: "windset" },
              { modelLabel: "coolingtower" }
            ];
          } else if ([7, 46].includes(this.ElSelect_energyType.value)) {
            // 热水、热量
            subLayerConditions = [
              ...subLayerConditions,
              { modelLabel: "boiler" }
            ];
          } else {
            //其他
            subLayerConditions = [...subLayerConditions];
          }
        }
      }
      //网络信号类型，下下端查询IT机房下五种模型
      if (this.ElSelect_energyType.value === 26) {
        subLayerConditions = [
          { modelLabel: "computer" },
          { modelLabel: "gateway" },
          {
            filter: {
              expressions: [
                {
                  limit: this.ElSelect_energyType.value,
                  prop: "energytype",
                  operator: "EQ"
                }
              ]
            },
            modelLabel: "meter"
          },
          { modelLabel: "interchanger" },
          { modelLabel: "photoeleconverter" }
        ];
      }
      const params = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: subLayerConditions,
        treeReturnEnable: true
      };
      httping({
        url: `eem-service/v1/node/nodeTree/simple`,
        method: "POST",
        data: params
      }).then(res => {
        if (res.code === 0) {
          let resData = this._.get(res, "data[0].children", []) || [];
          const result = resData.filter(
            item =>
              item.id !== this.insideNode.id ||
              item.modelLabel !== this.insideNode.modelLabel
          );
          this.waitListMap = this.setWaitListMap(result);
          this.waitCheckList = result;
          this.totalNum = result.length;
          var currentElectrical;
          if (this.ElSelect_energyType.value === 2) {
            currentElectrical = [this.ElSelect_electrical.value];
          } else if (this.ElSelect_energyType.value === 16) {
            // 压缩空气
            currentElectrical = [
              "pipeline",
              "aircompressor",
              "colddryingmachine",
              "dryingmachine"
            ];
          } else if (this.ElSelect_energyType.value === 24) {
            // 冷量
            currentElectrical = [
              "pipeline",
              "coldwatermainengine",
              "windset",
              "coolingtower"
            ];
          } else if ([7, 46].includes(this.ElSelect_energyType.value)) {
            // 热水、热量
            currentElectrical = ["pipeline", "boiler"];
          } else if ([26].includes(this.ElSelect_energyType.value)) {
            //网络信号
            currentElectrical = [
              "computer",
              "gateway",
              "meter",
              "interchanger",
              "photoeleconverter"
            ];
          } else {
            // 其他类型
            currentElectrical = ["pipeline"];
          }
          this.checkWaitModel = this.connectedList.filter(item =>
            currentElectrical.includes(item.split("_")[0])
          );
          this.ElInput_filterWait_input_out(this.ElInput_filterWait.val);
          this.CetCheckboxGroup_waitList_resulttext_out(this.checkWaitModel);
        }
      });
    },
    // 拿到所有映射表
    setWaitListMap(arr) {
      const obj = {};
      arr.forEach(item => {
        obj[item.tree_id] = item;
      });
      return obj;
    },
    // 查询到当前设备的连接关系
    getConnectData() {
      const vm = this;
      const dcbaseId = vm._.get(vm.CetTree_1, "inputData_in[0].id");
      if (!vm.insideNode || !dcbaseId) return;
      var queryData = {
        energyType: this.ElSelect_energyType.value,
        inflowNode: true, //查询流入端或者流出端，true：查询流入端，false：查询流出端，null：流入流出端均进行查询
        nodes: [vm.insideNode]
      };
      if (vm.relatedPosition === "upper") {
        queryData.inflowNode = true;
      } else {
        queryData.inflowNode = false;
      }
      httping({
        url: `/eem-service/v1/project/connection`,
        method: "POST",
        data: queryData
      }).then(
        res => {
          const data = vm._.get(res, "data", []);
          vm.connectedData = data;
          const connectedList = [];
          const alreadyCheckList = [];
          if (vm.relatedPosition === "upper") {
            data.forEach(item => {
              connectedList.push(item.inflowlabel + "_" + item.inflowid);
              alreadyCheckList.push({
                id: item.inflowid,
                modelLabel: item.inflowlabel,
                tree_id: item.inflowlabel + "_" + item.inflowid,
                name: item.inflowName
              });
            });
          } else {
            data.forEach(item => {
              connectedList.push(item.outflowlabel + "_" + item.outflowid);
              alreadyCheckList.push({
                id: item.outflowid,
                modelLabel: item.outflowlabel,
                tree_id: item.outflowlabel + "_" + item.outflowid,
                name: item.outflowName
              });
            });
          }
          vm.connectedList = connectedList;
          vm.alreadyCheckList = alreadyCheckList;
          var currentElectrical;
          if (this.ElSelect_energyType.value === 2) {
            currentElectrical = [this.ElSelect_electrical.value];
          } else if (this.ElSelect_energyType.value === 16) {
            // 压缩空气
            currentElectrical = [
              "pipeline",
              "aircompressor",
              "colddryingmachine",
              "dryingmachine"
            ];
          } else if (this.ElSelect_energyType.value === 24) {
            // 冷量
            currentElectrical = [
              "pipeline",
              "coldwatermainengine",
              "windset",
              "coolingtower"
            ];
          } else if ([7, 46].includes(this.ElSelect_energyType.value)) {
            // 热水、热量
            currentElectrical = ["pipeline", "boiler"];
          } else if ([26].includes(this.ElSelect_energyType.value)) {
            //网络信号
            currentElectrical = [
              "computer",
              "gateway",
              "meter",
              "interchanger",
              "photoeleconverter"
            ];
          } else {
            // 其他类型
            currentElectrical = ["pipeline"];
          }
          this.checkWaitModel = [];
          this.connectedList.forEach(item => {
            currentElectrical.forEach(current => {
              if (item.includes(current)) {
                this.checkWaitModel.push(item);
              }
            });
          });
          vm.CetCheckboxGroup_waitList_resulttext_out(vm.checkWaitModel);
        },
        () => {
          vm.getConnectDataisPending = false;
          vm.CetButton_1.disable_in = false;
        }
      );
    },
    // 获取当前选中房间下的设备节点
    getDeviceNode(node) {
      const params = {
        subLayerConditions: [],
        rootID: node.id,
        rootLabel: "room",
        treeReturnEnable: true
      };
      if (node.roomtype === 6 && node.modelLabel === "room") {
        params.subLayerConditions = [
          {
            filter: {
              expressions: [
                {
                  limit: this.ElSelect_energyType.value,
                  prop: "energytype",
                  operator: "EQ"
                }
              ]
            },
            modelLabel: "pipeline"
          }
        ];
      } else if (node.modelLabel === "room" && node.roomtype === 3) {
        params.subLayerConditions = [
          { modelLabel: "coldwatermainengine" },
          { modelLabel: "windset" },
          { modelLabel: "coolingtower" }
        ];
      } else if (node.modelLabel === "room" && node.roomtype === 4) {
        params.subLayerConditions = [
          { modelLabel: "aircompressor" },
          { modelLabel: "colddryingmachine" },
          { modelLabel: "dryingmachine" }
        ];
      } else if (node.modelLabel === "room" && node.roomtype === 5) {
        params.subLayerConditions = [{ modelLabel: "boiler" }];
      } else if (node.modelLabel === "room" && node.roomtype === 2) {
        params.subLayerConditions = [
          { modelLabel: "computer" },
          { modelLabel: "gateway" },
          {
            filter: {
              expressions: [
                {
                  limit: this.ElSelect_energyType.value,
                  prop: "energytype",
                  operator: "EQ"
                }
              ]
            },
            modelLabel: "meter"
          },
          { modelLabel: "interchanger" },
          { modelLabel: "photoeleconverter" }
        ];
      } else {
        params.subLayerConditions = this.deviceclass.map(item => {
          return {
            modelLabel: item.propertyLabel
          };
        });
      }
      httping({
        url: `eem-service/v1/node/nodeTree/simple`,
        method: "POST",
        data: params
      }).then(res => {
        const data = this._.get(res, "data[0].children", []) || [];
        if (data.length === 0) {
          this.insideNode = null;
        }
        this.CetTree_device.inputData_in = this._.cloneDeep(data);
      });
    },

    // 增加连接关系
    addConnect() {
      const oldConnect = [];

      if (this.relatedPosition === "upper") {
        this.connectedData.forEach(item => {
          oldConnect.push(item.inflowlabel + "_" + item.inflowid);
        });
      } else {
        this.connectedData.forEach(item => {
          oldConnect.push(item.outflowlabel + "_" + item.outflowid);
        });
      }

      const newConnect = this.alreadyCheckList.map(item => item.tree_id);
      // 如果原本树中没有的则为添加的
      const addNodeList = this._.differenceBy(newConnect, oldConnect);
      // 如果原本树中有减少则为删除
      const delNodeList = this._.differenceBy(oldConnect, newConnect);
      if (this._.isEmpty(addNodeList) && this._.isEmpty(delNodeList)) return;
      let params = [];
      if (this.relatedPosition === "upper") {
        params = addNodeList.map(item => {
          const arr = item.split("_");
          const node = {
            modelLabel: arr[0],
            id: parseInt(arr[1])
          };
          return {
            endpoint: false,
            inflowid: node.id,
            inflowlabel: node.modelLabel,
            outflowid: this.insideNode.id,
            outflowlabel: this.insideNode.modelLabel,
            remark: "",
            transmissionsubstance: this.ElSelect_energyType.value
          };
        });
      } else {
        params = addNodeList.map(item => {
          const arr = item.split("_");
          const node = {
            modelLabel: arr[0],
            id: parseInt(arr[1])
          };
          return {
            endpoint: false,
            inflowid: this.insideNode.id,
            inflowlabel: this.insideNode.modelLabel,
            outflowid: node.id,
            outflowlabel: node.modelLabel,
            remark: "",
            transmissionsubstance: this.ElSelect_energyType.value
          };
        });
      }
      var data = {
        energyType: this.ElSelect_energyType.value,
        projectId: this.projectId,
        connections: params,
        deleteIds: this.deleteConnect()
      };
      return new Promise((resolve, reject) => {
        httping({
          url: `/eem-service/v1/project/connection`,
          data,
          method: "PUT"
        }).then(
          res => {
            if (res.code !== 0) {
              this.CetButton_1.disable_in = false;
              return;
            }
            this.$message({
              message: $T("保存成功"),
              type: "success"
            });
            resolve();
          },
          () => {
            reject();
            this.CetButton_1.disable_in = false;
          }
        );
      });
    },
    // 删除链接关系
    deleteConnect() {
      const oldConnect = [];

      if (this.relatedPosition === "upper") {
        this.connectedData.forEach(item => {
          oldConnect.push(item.inflowlabel + "_" + item.inflowid);
        });
      } else {
        this.connectedData.forEach(item => {
          oldConnect.push(item.outflowlabel + "_" + item.outflowid);
        });
      }
      const newConnect = this.alreadyCheckList.map(item => item.tree_id);
      // 如果原本树中没有的则为添加的
      const delNodeList = this._.differenceBy(oldConnect, newConnect);

      if (this._.isEmpty(delNodeList)) return;
      const idRange = [];

      if (this.relatedPosition === "upper") {
        delNodeList.forEach(item => {
          const arr = item.split("_");
          const node = {
            modelLabel: arr[0],
            id: parseInt(arr[1])
          };
          const connect = this._.find(this.connectedData, {
            inflowlabel: node.modelLabel,
            inflowid: node.id
          });
          if (connect) {
            idRange.push(connect.id);
          }
        });
      } else {
        delNodeList.forEach(item => {
          const arr = item.split("_");
          const node = {
            modelLabel: arr[0],
            id: parseInt(arr[1])
          };
          const connect = this._.find(this.connectedData, {
            outflowlabel: node.modelLabel,
            outflowid: node.id
          });

          if (connect) {
            idRange.push(connect.id);
          }
        });
      }
      return idRange;
    },
    getLeftTree() {
      const params = {
        subLayerConditions: [],
        rootID: this.projectId,
        rootLabel: "project",
        treeReturnEnable: true
      };
      if (this.ElSelect_energyType.value === 2) {
        //电能
        params.subLayerConditions = [
          {
            filter: {
              expressions: [
                {
                  limit: 1,
                  prop: "roomtype",
                  operator: "EQ"
                }
              ]
            },
            modelLabel: "room",
            props: []
          }
        ];
      } else if (this.ElSelect_energyType.value === 16) {
        //压缩空气
        params.subLayerConditions = [
          {
            filter: {
              expressions: [
                {
                  limit: [4, 6],
                  prop: "roomtype",
                  operator: "IN"
                }
              ]
            },
            modelLabel: "room",
            props: []
          }
        ];
      } else if (this.ElSelect_energyType.value === 24) {
        //冷量
        params.subLayerConditions = [
          {
            filter: {
              expressions: [
                {
                  limit: [3, 6],
                  prop: "roomtype",
                  operator: "IN"
                }
              ]
            },
            modelLabel: "room",
            props: []
          }
        ];
      } else if ([7, 46].includes(this.ElSelect_energyType.value)) {
        //热水、热量
        params.subLayerConditions = [
          {
            filter: {
              expressions: [
                {
                  limit: [5, 6],
                  prop: "roomtype",
                  operator: "IN"
                }
              ]
            },
            modelLabel: "room",
            props: []
          }
        ];
      } else if ([26].includes(this.ElSelect_energyType.value)) {
        //网络信号
        params.subLayerConditions = [
          {
            filter: {
              expressions: [
                {
                  limit: [2],
                  prop: "roomtype",
                  operator: "IN"
                }
              ]
            },
            modelLabel: "room",
            props: []
          }
        ];
      } else {
        params.subLayerConditions = [
          {
            filter: {
              expressions: [
                {
                  limit: 6,
                  prop: "roomtype",
                  operator: "EQ"
                }
              ]
            },
            modelLabel: "room",
            props: []
          }
        ];
      }
      httping({
        url: `eem-service/v1/node/nodeTree/simple`,
        method: "POST",
        data: params
      }).then(res => {
        const data = this._.get(res, "data", []) || [];
        this.CetTree_1.inputData_in = data;
        var selectNode = this._.get(res, "data[0].children[0]", null);
        this.CetTree_1.selectNode = selectNode;
        this.outerNode = selectNode;
      });
    },
    removeAlreadyItem(connectObj) {
      this.alreadyCheckList = this.alreadyCheckList.filter(
        item => connectObj.tree_id !== item.tree_id
      );
      this.connectedList = this.connectedList.filter(
        item => connectObj.tree_id !== item
      );
      this.checkWaitModel = this.checkWaitModel.filter(
        item => connectObj.tree_id !== item
      );
      this.CetCheckboxGroup_waitList_resulttext_out(this.checkWaitModel);
    },
    getProjectEnergy(callback) {
      commonApi.getProjectEnergy(this.projectId).then(res => {
        if (res.code === 0) {
          const data = this._.get(res, "data", []).filter(
            item => ![13, 18, 22].includes(item.energytype)
          );
          this.ElOption_energyType.options_in = data;
          this.ElSelect_energyType.value =
            this.ElOption_energyType.options_in[0].energytype;
          callback && callback();
        }
      });
    }
  },

  created: function () {},
  activated() {
    // 现在设备类型先固定层级配置里面的类型，后续要取模型枚举把注释打开即可
    const deviceclassData = ELECTRICAL_DEVICE.map(item => {
      return {
        text: item.label,
        propertyLabel: item.value
      };
    });
    // 去除交换机和光电转换器
    const arr = deviceclassData.filter(item => {
      return (
        ["interchanger", "photoeleconverter"].indexOf(item.propertyLabel) == -1
      );
    });
    arr.push({
      propertyLabel: "pipeline",
      text: $T("管道")
    });
    arr.push({
      propertyLabel: "linesegment",
      text: $T("一段线")
    });

    this.deviceclass = arr;
    this.getProjectEnergy(() => {
      this.getLeftTree();
    });
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
.aside_bg {
  @include background_color(BG1);
  @include border_radius(C);
  @include padding_left(J1);
  @include padding_right(J1);
}
.connectedRelation {
  .connectedRelation_box {
    height: 100%;
    .lefttree_sbox {
      height: 100%;
      padding: 0px 16px 11px 12px;
    }
    .nodeListbox {
      height: 100%;
      .nodeListbox_asidebox {
        height: 100%;
        .snodeListbox {
          height: calc(100%);
          position: relative;
        }
        .threeMainbox {
          padding: 0px;
          .threeMainbox_header {
            @include line_height(Hm);
            .threeMainbox_headerbox {
              width: 100%;
              .headerbox_select {
                box-sizing: border-box;
                min-width: 422px;
                .headerbox_select_type {
                  font-size: 14px;
                  padding: 0px 0px 0px 10px;
                }
              }
            }
          }
          .allListbox {
            height: calc(100% - 52px);
            .tree-container {
              height: 100%;
              .allListbox_main {
                height: 100%;
                margin: 0px;
                @include margin_left(J3);
                @include margin_right(J3);
                .allListbox_main_box {
                  line-height: 32px;
                  .totalNum {
                    float: right;
                    font-size: 14px;
                  }
                  .min_box_content {
                    height: calc(100% - 50px);
                    padding: 0px;
                    .searchbox {
                      padding-top: 8px;
                      position: absolute;
                      top: 4px;
                      left: 10px;
                      color: #9ab8ca;
                    }

                    .block-label {
                      height: calc(100% - 50px);
                      overflow: auto;
                    }
                  }
                }
              }
              .checkedListbox_main {
                height: 100%;
                @include background_color(BG1);
                .checkedListbox_main_header {
                  line-height: 32px;
                  .hasNum {
                    float: right;
                  }
                }
                .checkedListbox_main_content {
                  height: calc(100% - 50px);
                  .already-check-list {
                    height: calc(100% - 52px);
                    overflow: auto;
                    margin-top: 8px;
                  }
                }
              }
            }
          }
        }
      }
    }

    .block-label {
      border: none;
    }
    .block-label :deep(.el-radio:hover) {
      @include background_color(B2);
    }
    .nodeListbox :deep(.el-select .el-input) {
      padding: 0px;
    }
    :deep(.el-input .el-input__inner) {
      height: 32px;
      line-height: 32px;
    }
    .min_box_content {
      height: calc(100% - 50px);
    }
    .min_box_contentList {
      height: calc(100% - 52px);
      overflow: auto;
      line-height: 26px;
    }
    .min_box_contentList :deep(.el-checkbox) {
      margin: 0px;
      position: relative;
      @include padding(0 J 0 J);
    }
    .min_box_contentList :deep(.el-checkbox .el-checkbox__label) {
      display: inline-block;
      width: calc(100% - 30px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      top: 3px;
      position: absolute;
    }
  }
}
.block-label {
  :deep(.el-radio-group) {
    display: block;
  }
  :deep(.el-radio) {
    display: block;
    margin-right: 0;
    line-height: 26px;
    &.is-checked {
      @include background_color(BG4);
    }
    &:hover {
      @include background_color(BG2);
    }
    .el-radio__input {
      display: none;
    }
  }
  :deep(.el-checkbox) {
    display: block;
    line-height: 26px;
    &:hover {
      @include background_color(BG2);
    }
  }
}
.connected-tab {
  :deep(.el-tabs__content) {
    height: calc(100% - 30px) !important;
  }
}
.already-check-list {
  overflow: auto;
  padding: 0;
  line-height: 26px;
  list-style: none;
  li {
    width: 100%;
    box-sizing: border-box;
    &:hover {
      @include background_color(BG2);
    }
    .block-label-span {
      max-width: calc(100% - 30px);
      float: left;
    }
    i {
      line-height: 26px;
      cursor: pointer;
    }
  }
}
.com:hover {
  @include background_color(BG2);
}
.active {
  @include background_color(BG3);
  cursor: pointer;
}
</style>
