<template>
  <!-- 1弹窗组件 -->
  <div>
    <CetDialog
      class="CetDialog small"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
    >
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
      <CetForm
        class="eem-cont-c1"
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
      >
        <el-row :gutter="$J3">
          <el-col :span="12">
            <el-form-item :label="$T('用户组名称')" prop="name">
              <ElInput
                v-model.trim="CetForm_1.data.name"
                v-bind="ElInput_1"
                v-on="ElInput_1.event"
              ></ElInput>
            </el-form-item>
          </el-col>
        </el-row>
      </CetForm>
    </CetDialog>
  </div>
</template>
<script>
import common from "eem-utils/common";
import customApi from "@/api/custom.js";
export default {
  name: "EditUserGroup",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    currentNode_in: {
      type: Object
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {},
  data() {
    return {
      detailInfo: null,
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        type: "primary",
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: ["name", "tenantId", "parentId"], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入用户组名称")
            },
            common.pattern_name,
            common.check_stringLessThan50
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入"),
        style: {},
        event: {}
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      this.init();
      vm.CetDialog_1.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    init() {
      let formData = {
        name: ""
      };
      if (this.inputData_in) {
        this.CetDialog_1.title = $T("编辑用户组");
        formData.name = this.inputData_in.name;
      } else {
        this.CetDialog_1.title = $T("新增用户组");
      }
      this.CetForm_1.data = formData;
      this.CetForm_1.resetTrigger_in = new Date().getTime();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetForm_1_saveData_out(val) {
      const vm = this;
      let param = {
          id: 0,
          name: val.name,
          parentId: 0,
          tenantId: 0
        },
        type = "";
      if (vm.inputData_in) {
        param.id = vm.inputData_in.id;
        param.parentId = vm.inputData_in.parentId || 0;
        param.tenantId = vm.inputData_in.tenantId;
        type = "editAuthCommonUserGroup";
      } else {
        if (vm.currentNode_in.modelLabel === "usergroup") {
          // 用户组下新建
          param.parentId = vm.currentNode_in.id;
          param.tenantId = vm.currentNode_in.tenantId;
        } else {
          param.tenantId = vm.currentNode_in.id;
        }
        type = "addAuthCommonUserGroup";
      }
      customApi[type](param).then(res => {
        if (res.code == 0) {
          vm.CetDialog_1.closeTrigger_in = new Date().getTime();
          vm.$message.success($T("保存成功！"));
          vm.$emit("finishTrigger_out", val);
        }
      });
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG, !important);
    @include padding(J1);
  }
}
</style>
